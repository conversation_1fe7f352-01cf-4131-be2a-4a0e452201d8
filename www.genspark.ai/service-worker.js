const CACHE_NAME = 'browser_extension-v2'
const URL_TO_CACHES = [
    '/agents?type=super_agent&from=browser_extension',
    '/agents?type=super_agent&from=browser_extension&action=ask_by_image',
    '/agents?type=super_agent&from=browser_extension&action=compare_price',
    '/agents?type=super_agent&from=browser_extension&action=chat_now',
]

let is_refresh_page = false
self.addEventListener('activate', event => {
    event.waitUntil(
        caches.keys().then(keys => {
            return Promise.all(
                keys.filter(key => key !== CACHE_NAME).map(key => caches.delete(key))
            )
        })
    )
})

self.addEventListener('message', event => {
    if (event.data.type === 'GET_IS_REFRESH_PAGE') {
        event.source.postMessage({
            type: 'REFRESH_PAGE',
            payload: {
                is_refresh_page: is_refresh_page,
            },
        })
    }
})

async function getResponseText(response) {
    if (!response) return null
    try {
        const text = await response.text()

        // Extract window.genspark_page_id value from script tags
        const scriptMatch = text.match(
            /window\.genspark_page_id\s*=\s*['"]([^'"]+)['"]/
        )

        if (scriptMatch) {
            return scriptMatch[1]
        }

        return null
    } catch (err) {
        return null
    }
}

async function compareResponses(cachedResponse, networkResponse) {
    if (!cachedResponse || !networkResponse) {
        return false
    }

    const cachedText = await getResponseText(cachedResponse)
    const networkText = await getResponseText(networkResponse)

    if (cachedText === null || networkText === null) {
        return false
    }

    const isConsistent = cachedText === networkText

    return isConsistent
}

self.addEventListener('fetch', event => {
    if (URL_TO_CACHES.some(url => event.request.url.includes(url))) {
        event.respondWith(
            caches.open(CACHE_NAME).then(cache => {
                return cache.match(event.request).then(cachedResponse => {
                    const cachedResponseClone = cachedResponse ? .clone()
                    const fetchPromise = fetch(event.request)
                        .then(networkResponse => {
                            if (networkResponse && networkResponse.status === 200) {
                                cache.put(event.request, networkResponse.clone())

                                const networkResponseClone = networkResponse.clone()
                                compareResponses(
                                    cachedResponseClone,
                                    networkResponseClone
                                ).then(isConsistent => {
                                    console.log('isConsistent', isConsistent)
                                    if (!isConsistent) {
                                        is_refresh_page = true
                                    } else {
                                        is_refresh_page = false
                                    }
                                    self.clients.matchAll().then(clients => {
                                        clients.forEach(client => {
                                            client.postMessage({
                                                type: 'REFRESH_PAGE',
                                                payload: {
                                                    is_refresh_page: is_refresh_page,
                                                },
                                            })
                                        })
                                    })
                                })
                            }

                            return networkResponse
                        })
                        .catch(err => {
                            console.log('serverworkerfetcherr', err)
                        })

                    return cachedResponse || fetchPromise
                })
            })
        )
    }
})