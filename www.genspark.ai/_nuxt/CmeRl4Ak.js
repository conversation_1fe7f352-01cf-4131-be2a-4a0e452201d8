import {
    J as e,
    X as o,
    Y as n,
    r,
    c as a,
    K as c,
    aR as l,
    aT as i,
    ak as d,
    al as t,
    aX as s,
    aY as b,
    am as u,
    aO as h,
    aQ as k,
    aS as v,
    b9 as f,
    i as x,
    Z as p,
    ba as m,
    aV as g,
    ap as y,
    aq as C,
    bb as w
} from "./Cf0SOiw0.js";
import {
    u as z
} from "./MpDLC7up.js";
import {
    u as R
} from "./BuhfKjCJ.js";
import {
    c as S,
    r as T
} from "./B7VeW_-d.js";
import {
    o as D
} from "./Jr9eiJio.js";
const $ = i("n-checkbox-group"),
    A = e({
        name: "CheckboxGroup",
        props: {
            min: Number,
            max: Number,
            size: String,
            value: Array,
            defaultValue: {
                type: Array,
                default: null
            },
            disabled: {
                type: Boolean,
                default: void 0
            },
            "onUpdate:value": [Function, Array],
            onUpdateValue: [Function, Array],
            onChange: [Function, Array]
        },
        setup(e) {
            const {
                mergedClsPrefixRef: o
            } = n(e), i = z(e), {
                mergedSizeRef: d,
                mergedDisabledRef: t
            } = i, s = r(e.defaultValue), b = a((() => e.value)), u = R(b, s), h = a((() => {
                var e;
                return (null === (e = u.value) || void 0 === e ? void 0 : e.length) || 0
            })), k = a((() => Array.isArray(u.value) ? new Set(u.value) : new Set));
            return c($, {
                checkedCountRef: h,
                maxRef: l(e, "max"),
                minRef: l(e, "min"),
                valueSetRef: k,
                disabledRef: t,
                mergedSizeRef: d,
                toggleCheckbox: function(o, n) {
                    const {
                        nTriggerFormInput: r,
                        nTriggerFormChange: a
                    } = i, {
                        onChange: c,
                        "onUpdate:value": l,
                        onUpdateValue: d
                    } = e;
                    if (Array.isArray(u.value)) {
                        const e = Array.from(u.value),
                            i = e.findIndex((e => e === n));
                        o ? ~i || (e.push(n), d && S(d, e, {
                            actionType: "check",
                            value: n
                        }), l && S(l, e, {
                            actionType: "check",
                            value: n
                        }), r(), a(), s.value = e, c && S(c, e)) : ~i && (e.splice(i, 1), d && S(d, e, {
                            actionType: "uncheck",
                            value: n
                        }), l && S(l, e, {
                            actionType: "uncheck",
                            value: n
                        }), c && S(c, e), s.value = e, r(), a())
                    } else o ? (d && S(d, [n], {
                        actionType: "check",
                        value: n
                    }), l && S(l, [n], {
                        actionType: "check",
                        value: n
                    }), c && S(c, [n]), s.value = [n], r(), a()) : (d && S(d, [], {
                        actionType: "uncheck",
                        value: n
                    }), l && S(l, [], {
                        actionType: "uncheck",
                        value: n
                    }), c && S(c, []), s.value = [], r(), a())
                }
            }), {
                mergedClsPrefix: o
            }
        },
        render() {
            return o("div", {
                class: `${this.mergedClsPrefix}-checkbox-group`,
                role: "group"
            }, this.$slots)
        }
    }),
    F = d([t("checkbox", "\n font-size: var(--n-font-size);\n outline: none;\n cursor: pointer;\n display: inline-flex;\n flex-wrap: nowrap;\n align-items: flex-start;\n word-break: break-word;\n line-height: var(--n-size);\n --n-merged-color-table: var(--n-color-table);\n ", [u("show-label", "line-height: var(--n-label-line-height);"), d("&:hover", [t("checkbox-box", [h("border", "border: var(--n-border-checked);")])]), d("&:focus:not(:active)", [t("checkbox-box", [h("border", "\n border: var(--n-border-focus);\n box-shadow: var(--n-box-shadow-focus);\n ")])]), u("inside-table", [t("checkbox-box", "\n background-color: var(--n-merged-color-table);\n ")]), u("checked", [t("checkbox-box", "\n background-color: var(--n-color-checked);\n ", [t("checkbox-icon", [d(".check-icon", "\n opacity: 1;\n transform: scale(1);\n ")])])]), u("indeterminate", [t("checkbox-box", [t("checkbox-icon", [d(".check-icon", "\n opacity: 0;\n transform: scale(.5);\n "), d(".line-icon", "\n opacity: 1;\n transform: scale(1);\n ")])])]), u("checked, indeterminate", [d("&:focus:not(:active)", [t("checkbox-box", [h("border", "\n border: var(--n-border-checked);\n box-shadow: var(--n-box-shadow-focus);\n ")])]), t("checkbox-box", "\n background-color: var(--n-color-checked);\n border-left: 0;\n border-top: 0;\n ", [h("border", {
        border: "var(--n-border-checked)"
    })])]), u("disabled", {
        cursor: "not-allowed"
    }, [u("checked", [t("checkbox-box", "\n background-color: var(--n-color-disabled-checked);\n ", [h("border", {
        border: "var(--n-border-disabled-checked)"
    }), t("checkbox-icon", [d(".check-icon, .line-icon", {
        fill: "var(--n-check-mark-color-disabled-checked)"
    })])])]), t("checkbox-box", "\n background-color: var(--n-color-disabled);\n ", [h("border", "\n border: var(--n-border-disabled);\n "), t("checkbox-icon", [d(".check-icon, .line-icon", "\n fill: var(--n-check-mark-color-disabled);\n ")])]), h("label", "\n color: var(--n-text-color-disabled);\n ")]), t("checkbox-box-wrapper", "\n position: relative;\n width: var(--n-size);\n flex-shrink: 0;\n flex-grow: 0;\n user-select: none;\n -webkit-user-select: none;\n "), t("checkbox-box", "\n position: absolute;\n left: 0;\n top: 50%;\n transform: translateY(-50%);\n height: var(--n-size);\n width: var(--n-size);\n display: inline-block;\n box-sizing: border-box;\n border-radius: var(--n-border-radius);\n background-color: var(--n-color);\n transition: background-color 0.3s var(--n-bezier);\n ", [h("border", "\n transition:\n border-color .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier);\n border-radius: inherit;\n position: absolute;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n border: var(--n-border);\n "), t("checkbox-icon", "\n display: flex;\n align-items: center;\n justify-content: center;\n position: absolute;\n left: 1px;\n right: 1px;\n top: 1px;\n bottom: 1px;\n ", [d(".check-icon, .line-icon", "\n width: 100%;\n fill: var(--n-check-mark-color);\n opacity: 0;\n transform: scale(0.5);\n transform-origin: center;\n transition:\n fill 0.3s var(--n-bezier),\n transform 0.3s var(--n-bezier),\n opacity 0.3s var(--n-bezier),\n border-color 0.3s var(--n-bezier);\n "), k({
        left: "1px",
        top: "1px"
    })])]), h("label", "\n color: var(--n-text-color);\n transition: color .3s var(--n-bezier);\n user-select: none;\n -webkit-user-select: none;\n padding: var(--n-label-padding);\n font-weight: var(--n-label-font-weight);\n ", [d("&:empty", {
        display: "none"
    })])]), s(t("checkbox", "\n --n-merged-color-table: var(--n-color-table-modal);\n ")), b(t("checkbox", "\n --n-merged-color-table: var(--n-color-table-popover);\n "))]),
    B = e({
        name: "Checkbox",
        props: Object.assign(Object.assign({}, p.props), {
            size: String,
            checked: {
                type: [Boolean, String, Number],
                default: void 0
            },
            defaultChecked: {
                type: [Boolean, String, Number],
                default: !1
            },
            value: [String, Number],
            disabled: {
                type: Boolean,
                default: void 0
            },
            indeterminate: Boolean,
            label: String,
            focusable: {
                type: Boolean,
                default: !0
            },
            checkedValue: {
                type: [Boolean, String, Number],
                default: !0
            },
            uncheckedValue: {
                type: [Boolean, String, Number],
                default: !1
            },
            "onUpdate:checked": [Function, Array],
            onUpdateChecked: [Function, Array],
            privateInsideTable: Boolean,
            onChange: [Function, Array]
        }),
        setup(e) {
            const o = x($, null),
                c = r(null),
                {
                    mergedClsPrefixRef: i,
                    inlineThemeDisabled: d,
                    mergedRtlRef: t
                } = n(e),
                s = r(e.defaultChecked),
                b = l(e, "checked"),
                u = R(b, s),
                h = f((() => {
                    if (o) {
                        const n = o.valueSetRef.value;
                        return !(!n || void 0 === e.value) && n.has(e.value)
                    }
                    return u.value === e.checkedValue
                })),
                k = z(e, {
                    mergedSize(n) {
                        const {
                            size: r
                        } = e;
                        if (void 0 !== r) return r;
                        if (o) {
                            const {
                                value: e
                            } = o.mergedSizeRef;
                            if (void 0 !== e) return e
                        }
                        if (n) {
                            const {
                                mergedSize: e
                            } = n;
                            if (void 0 !== e) return e.value
                        }
                        return "medium"
                    },
                    mergedDisabled(n) {
                        const {
                            disabled: r
                        } = e;
                        if (void 0 !== r) return r;
                        if (o) {
                            if (o.disabledRef.value) return !0;
                            const {
                                maxRef: {
                                    value: e
                                },
                                checkedCountRef: n
                            } = o;
                            if (void 0 !== e && n.value >= e && !h.value) return !0;
                            const {
                                minRef: {
                                    value: r
                                }
                            } = o;
                            if (void 0 !== r && n.value <= r && h.value) return !0
                        }
                        return !!n && n.disabled.value
                    }
                }),
                {
                    mergedDisabledRef: v,
                    mergedSizeRef: T
                } = k,
                D = p("Checkbox", "-checkbox", F, m, e, i);

            function A(n) {
                if (o && void 0 !== e.value) o.toggleCheckbox(!h.value, e.value);
                else {
                    const {
                        onChange: o,
                        "onUpdate:checked": r,
                        onUpdateChecked: a
                    } = e, {
                        nTriggerFormInput: c,
                        nTriggerFormChange: l
                    } = k, i = h.value ? e.uncheckedValue : e.checkedValue;
                    r && S(r, i, n), a && S(a, i, n), o && S(o, i, n), c(), l(), s.value = i
                }
            }
            const B = {
                    focus: () => {
                        var e;
                        null === (e = c.value) || void 0 === e || e.focus()
                    },
                    blur: () => {
                        var e;
                        null === (e = c.value) || void 0 === e || e.blur()
                    }
                },
                V = g("Checkbox", t, i),
                U = a((() => {
                    const {
                        value: e
                    } = T, {
                        common: {
                            cubicBezierEaseInOut: o
                        },
                        self: {
                            borderRadius: n,
                            color: r,
                            colorChecked: a,
                            colorDisabled: c,
                            colorTableHeader: l,
                            colorTableHeaderModal: i,
                            colorTableHeaderPopover: d,
                            checkMarkColor: t,
                            checkMarkColorDisabled: s,
                            border: b,
                            borderFocus: u,
                            borderDisabled: h,
                            borderChecked: k,
                            boxShadowFocus: v,
                            textColor: f,
                            textColorDisabled: x,
                            checkMarkColorDisabledChecked: p,
                            colorDisabledChecked: m,
                            borderDisabledChecked: g,
                            labelPadding: C,
                            labelLineHeight: w,
                            labelFontWeight: z,
                            [y("fontSize", e)]: R,
                            [y("size", e)]: S
                        }
                    } = D.value;
                    return {
                        "--n-label-line-height": w,
                        "--n-label-font-weight": z,
                        "--n-size": S,
                        "--n-bezier": o,
                        "--n-border-radius": n,
                        "--n-border": b,
                        "--n-border-checked": k,
                        "--n-border-focus": u,
                        "--n-border-disabled": h,
                        "--n-border-disabled-checked": g,
                        "--n-box-shadow-focus": v,
                        "--n-color": r,
                        "--n-color-checked": a,
                        "--n-color-table": l,
                        "--n-color-table-modal": i,
                        "--n-color-table-popover": d,
                        "--n-color-disabled": c,
                        "--n-color-disabled-checked": m,
                        "--n-text-color": f,
                        "--n-text-color-disabled": x,
                        "--n-check-mark-color": t,
                        "--n-check-mark-color-disabled": s,
                        "--n-check-mark-color-disabled-checked": p,
                        "--n-font-size": R,
                        "--n-label-padding": C
                    }
                })),
                j = d ? C("checkbox", a((() => T.value[0])), U, e) : void 0;
            return Object.assign(k, B, {
                rtlEnabled: V,
                selfRef: c,
                mergedClsPrefix: i,
                mergedDisabled: v,
                renderedChecked: h,
                mergedTheme: D,
                labelId: w(),
                handleClick: function(e) {
                    v.value || A(e)
                },
                handleKeyUp: function(e) {
                    if (!v.value) switch (e.key) {
                        case " ":
                        case "Enter":
                            A(e)
                    }
                },
                handleKeyDown: function(e) {
                    if (" " === e.key) e.preventDefault()
                },
                cssVars: d ? void 0 : U,
                themeClass: null == j ? void 0 : j.themeClass,
                onRender: null == j ? void 0 : j.onRender
            })
        },
        render() {
            var e;
            const {
                $slots: n,
                renderedChecked: r,
                mergedDisabled: a,
                indeterminate: c,
                privateInsideTable: l,
                cssVars: i,
                labelId: d,
                label: t,
                mergedClsPrefix: s,
                focusable: b,
                handleKeyUp: u,
                handleKeyDown: h,
                handleClick: k
            } = this;
            null === (e = this.onRender) || void 0 === e || e.call(this);
            const f = T(n.default, (e => t || e ? o("span", {
                class: `${s}-checkbox__label`,
                id: d
            }, t || e) : null));
            return o("div", {
                ref: "selfRef",
                class: [`${s}-checkbox`, this.themeClass, this.rtlEnabled && `${s}-checkbox--rtl`, r && `${s}-checkbox--checked`, a && `${s}-checkbox--disabled`, c && `${s}-checkbox--indeterminate`, l && `${s}-checkbox--inside-table`, f && `${s}-checkbox--show-label`],
                tabindex: a || !b ? void 0 : 0,
                role: "checkbox",
                "aria-checked": c ? "mixed" : r,
                "aria-labelledby": d,
                style: i,
                onKeyup: u,
                onKeydown: h,
                onClick: k,
                onMousedown: () => {
                    D("selectstart", window, (e => {
                        e.preventDefault()
                    }), {
                        once: !0
                    })
                }
            }, o("div", {
                class: `${s}-checkbox-box-wrapper`
            }, " ", o("div", {
                class: `${s}-checkbox-box`
            }, o(v, null, {
                default: () => this.indeterminate ? o("div", {
                    key: "indeterminate",
                    class: `${s}-checkbox-icon`
                }, o("svg", {
                    viewBox: "0 0 100 100",
                    class: "line-icon"
                }, o("path", {
                    d: "M80.2,55.5H21.4c-2.8,0-5.1-2.5-5.1-5.5l0,0c0-3,2.3-5.5,5.1-5.5h58.7c2.8,0,5.1,2.5,5.1,5.5l0,0C85.2,53.1,82.9,55.5,80.2,55.5z"
                }))) : o("div", {
                    key: "check",
                    class: `${s}-checkbox-icon`
                }, o("svg", {
                    viewBox: "0 0 64 64",
                    class: "check-icon"
                }, o("path", {
                    d: "M50.42,16.76L22.34,39.45l-8.1-11.46c-1.12-1.58-3.3-1.96-4.88-0.84c-1.58,1.12-1.95,3.3-0.84,4.88l10.26,14.51  c0.56,0.79,1.42,1.31,2.38,1.45c0.16,0.02,0.32,0.03,0.48,0.03c0.8,0,1.57-0.27,2.2-0.78l30.99-25.03c1.5-1.21,1.74-3.42,0.52-4.92  C54.13,15.78,51.93,15.55,50.42,16.76z"
                })))
            }), o("div", {
                class: `${s}-checkbox-box__border`
            }))), f)
        }
    });
export {
    B as N, A as a
};