import {
    d as C,
    o as t,
    D as h
} from "./Cf0SOiw0.js";
const a = {
    width: "107",
    height: "24",
    viewBox: "0 0 107 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const l = {
    render: function(l, p) {
        return t(), C("svg", a, p[0] || (p[0] = [h('<path d="M48.2099 10.4609C49.4982 10.4609 50.4878 10.8572 51.1763 11.6497C51.8649 12.4422 52.2092 13.2542 52.2092 14.6313V15.4541H46.4105C46.3542 15.9781 46.456 16.7143 47.0731 17.1863C47.491 17.5046 47.9977 17.6605 48.8118 17.6605C49.626 17.6605 50.1543 17.4678 50.6523 17.1625L51.7523 17.9961C51.5271 18.4054 50.4141 19.3494 48.7642 19.3494C47.2983 19.3494 46.1788 18.9424 45.4102 18.1282C44.6393 17.3141 44.2539 16.2401 44.2539 14.9063C44.2539 14.0185 44.438 13.2368 44.8082 12.5613C45.1785 11.8879 45.6635 11.3704 46.2633 11.0066C46.8631 10.6428 47.5105 10.4631 48.2077 10.4631L48.2099 10.4609ZM46.456 13.9644H50.1565C50.1565 13.9644 50.0699 12.1629 48.3052 12.1629C46.5405 12.1629 46.4538 13.9644 46.4538 13.9644H46.456Z" fill="white"></path><path d="M55.8065 11.2162C56.8133 10.7269 57.4651 10.4844 58.4286 10.4844C59.3316 10.4844 60.0093 10.6836 60.4618 11.0842C60.9144 11.4847 61.2024 11.9503 61.3279 12.4829C61.4535 13.0156 61.5163 13.6154 61.5163 14.2823V19.126H59.4052V14.016C59.4052 13.5721 59.429 13.152 59.2081 12.7969C58.9851 12.4418 58.4459 12.1863 57.9198 12.2144C57.3936 12.2426 56.7505 12.2599 55.8065 13.0632V19.1239H53.6953V10.5926H55.8065V11.2141V11.2162Z" fill="white"></path><path d="M73.1801 11.1495C73.7603 10.7208 74.4316 10.4609 75.2912 10.4609C75.9126 10.4609 76.5362 10.6017 77.1577 10.8832C77.7791 11.1647 78.3009 11.6345 78.7232 12.2928C79.1454 12.951 79.3554 13.815 79.3554 14.8803C79.3554 16.2574 78.9743 17.3422 78.2122 18.1347C77.45 18.9272 76.3803 19.3234 75.0011 19.3234C74.6611 19.3234 74.3536 19.2866 74.0786 19.213C73.8037 19.1394 73.4962 19.029 73.1562 18.8796V23.6995H71.0234V10.593H73.1779V11.1473L73.1801 11.1495ZM74.7131 12.1499C74.1652 12.1499 73.6542 12.2538 73.1801 12.4617V17.2383C73.6088 17.5198 74.0981 17.6605 74.646 17.6605C75.4016 17.6605 76.0122 17.4505 76.4778 17.0282C76.9455 16.606 77.1772 15.9283 77.1772 14.995C77.1772 13.1004 76.3543 12.152 74.7109 12.152L74.7131 12.1499Z" fill="white"></path><path d="M84.421 10.4614C85.8566 10.4614 86.8613 10.758 87.4307 11.3491C88.0002 11.9424 88.286 12.7869 88.286 13.8825V19.1247H86.1316L86.1532 18.5249C85.757 18.8518 84.8086 19.2762 83.8169 19.2762C82.8988 19.2762 82.2124 19.0402 81.6646 18.566C81.1168 18.0918 80.8418 17.3708 80.8418 16.6303C80.8418 15.801 81.1493 15.1535 81.7642 14.6858C82.3792 14.2203 83.2561 14.1185 84.3972 14.0579L86.0645 13.9691C86.0645 13.2286 85.9302 12.7241 85.6639 12.4578C85.3976 12.1914 84.8714 12.0572 84.0854 12.0572C83.4185 12.0572 82.4008 12.2975 81.617 12.5942L81.5758 11.0352C82.7321 10.6497 83.6783 10.457 84.4188 10.457L84.421 10.4614ZM86.1316 15.4155L84.644 15.526C83.5332 15.5844 82.9768 15.9547 82.9768 16.6367C82.9768 16.9767 83.1023 17.2452 83.3535 17.4357C83.6047 17.6263 83.9533 17.7237 84.3972 17.7237C84.6938 17.7237 84.945 17.7021 85.1529 17.6566C85.3607 17.6111 85.5599 17.5418 85.7526 17.4466C85.9454 17.3513 86.0558 17.2798 86.1294 17.2365V15.4155H86.1316Z" fill="white"></path><path d="M95.446 12.9474C94.9848 12.6205 94.3288 12.4018 93.6186 12.4018C93.1292 12.4018 92.3475 12.5533 91.9318 12.8435V19.125H89.7773V10.5938H91.9318V11.2152C92.6009 10.8536 93.0837 10.6955 93.7181 10.6674C94.3526 10.6392 94.922 10.7172 95.4677 10.9489L95.446 12.9474Z" fill="white"></path><path d="M99.1115 19.1252H96.957V6.64453H99.1115V19.1252ZM105.109 10.4814L101.82 14.4352L105.263 19.1231H102.686L99.3086 14.4352L102.53 10.4814H105.107H105.109Z" fill="white"></path><path d="M66.455 19.4125C66.0998 19.4125 65.7123 19.3821 65.29 19.2934C63.9974 19.0249 63.1594 18.4814 63.1096 18.4381V16.4049C63.1096 16.4049 64.346 17.2407 65.6365 17.4897C66.8859 17.73 67.5528 17.4377 67.661 17.0415C67.739 16.7557 67.7065 16.3031 67.1717 16.1407C66.9465 16.0736 66.678 15.987 66.3878 15.9069C65.2013 15.5821 63.4106 15.0927 63.0533 13.328C62.8389 12.267 63.3673 11.245 64.3092 10.7795C66.0133 9.93933 68.5293 10.7838 69.0771 11.2342V12.9577C67.8234 12.2042 65.6148 11.6759 65.0497 12.2887C64.8981 12.4532 64.8029 12.7152 64.8527 12.9642C64.9761 13.5748 65.7318 13.8239 66.8729 14.1357C67.1977 14.2244 67.4943 14.337 67.7931 14.4128C69.3305 14.8047 69.7635 16.3313 69.4301 17.5265C69.1702 18.4619 68.2456 19.4103 66.4528 19.4103L66.455 19.4125Z" fill="white"></path><path d="M42.7659 19.1252H38.7168C34.9903 19.1252 32.0391 16.3255 32.0391 12.8849C32.0391 9.44424 35.0705 6.64453 38.7969 6.64453H42.7659V8.71887H38.7969C36.2159 8.71887 34.1156 10.5875 34.1156 12.8849C34.1156 15.1822 36.1358 17.0509 38.719 17.0509H40.6136V14.4244H38.641V12.3501H42.768V19.1274L42.7659 19.1252Z" fill="white"></path><path d="M19.4421 0H4.55792C2.04065 0 0 2.04065 0 4.55792V19.4421C0 21.9594 2.04065 24 4.55792 24H19.4421C21.9594 24 24 21.9594 24 19.4421V4.55792C24 2.04065 21.9594 0 19.4421 0Z" fill="white"></path><path d="M20.127 18.3438H3.93071C3.59468 18.3438 3.32227 18.6162 3.32227 18.9522V20.2297C3.32227 20.5657 3.59468 20.8382 3.93071 20.8382H20.127C20.4631 20.8382 20.7355 20.5657 20.7355 20.2297V18.9522C20.7355 18.6162 20.4631 18.3438 20.127 18.3438Z" fill="black"></path><path d="M8.72989 15.7361C8.63245 15.7361 8.55018 15.6646 8.53502 15.5672C7.90059 11.373 7.44154 11.0028 3.2777 10.3748C3.14995 10.3553 3.05469 10.2449 3.05469 10.115C3.05469 9.98509 3.14995 9.87466 3.2777 9.85517C7.41989 9.2294 7.788 8.85914 8.41376 4.71912C8.43325 4.59137 8.54367 4.49609 8.67359 4.49609C8.8035 4.49609 8.91394 4.59137 8.93342 4.71912C9.55919 8.85914 9.92946 9.2294 14.0695 9.85517C14.1972 9.87466 14.2925 9.98509 14.2925 10.115C14.2925 10.2449 14.1972 10.3553 14.0695 10.3748C9.90997 11.0028 9.55486 11.373 8.92477 15.5672C8.90961 15.6625 8.82732 15.7361 8.72989 15.7361Z" fill="black"></path><path d="M16.6327 10.1882C16.572 10.1882 16.5201 10.1428 16.5114 10.0821C16.1152 7.46215 15.8272 7.23046 13.2267 6.83855C13.1466 6.82556 13.0859 6.75843 13.0859 6.67615C13.0859 6.59604 13.1444 6.52675 13.2267 6.51376C15.8142 6.12401 16.0459 5.89232 16.4356 3.30481C16.4486 3.22469 16.5158 3.16406 16.598 3.16406C16.6781 3.16406 16.7474 3.22253 16.7604 3.30481C17.1502 5.89232 17.3819 6.12401 19.9694 6.51376C20.0495 6.52675 20.1101 6.59387 20.1101 6.67615C20.1101 6.75627 20.0517 6.82556 19.9694 6.83855C17.371 7.23046 17.148 7.46215 16.7539 10.0821C16.7453 10.1428 16.6933 10.1882 16.6327 10.1882Z" fill="black"></path><path d="M16.6117 16.727C16.5727 16.727 16.5403 16.6988 16.5338 16.6598C16.2826 14.9926 16.0985 14.8453 14.4443 14.5963C14.3923 14.5877 14.3555 14.5444 14.3555 14.4924C14.3555 14.4404 14.3923 14.3971 14.4443 14.3885C16.0899 14.1394 16.2371 13.9922 16.4861 12.3466C16.4948 12.2946 16.5381 12.2578 16.59 12.2578C16.642 12.2578 16.6853 12.2946 16.694 12.3466C16.943 13.9922 17.0902 14.1394 18.7358 14.3885C18.7878 14.3971 18.8246 14.4404 18.8246 14.4924C18.8246 14.5444 18.7878 14.5877 18.7358 14.5963C17.0816 14.8453 16.9408 14.9926 16.6897 16.6598C16.6832 16.6988 16.6507 16.727 16.6117 16.727Z" fill="black"></path>', 13)]))
    }
};
export {
    l as L
};