import {
    aW as t,
    aA as r
} from "./Cf0SOiw0.js";
var e = /\s/;

function a(t) {
    for (var r = t.length; r-- && e.test(t.charAt(r)););
    return r
}
var n = /^\s+/;

function f(t) {
    return t ? t.slice(0, a(t) + 1).replace(n, "") : t
}
var s = /^[-+]0x[0-9a-f]+$/i,
    i = /^0b[01]+$/i,
    u = /^0o[0-7]+$/i,
    o = parseInt;

function c(e) {
    if ("number" == typeof e) return e;
    if (t(e)) return NaN;
    if (r(e)) {
        var a = "function" == typeof e.valueOf ? e.valueOf() : e;
        e = r(a) ? a + "" : a
    }
    if ("string" != typeof e) return 0 === e ? e : +e;
    e = f(e);
    var n = i.test(e);
    return n || u.test(e) ? o(e.slice(2), n ? 2 : 8) : s.test(e) ? NaN : +e
}
export {
    a,
    f as b,
    c as t
};