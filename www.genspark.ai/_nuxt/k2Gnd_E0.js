import {
    _ as o,
    r as n,
    s as e,
    P as s,
    x as a,
    h as c,
    d as t,
    b as i,
    f as l,
    e as r,
    t as u,
    i as p,
    A as d,
    g as m,
    E as v,
    o as g
} from "./Cf0SOiw0.js";
import {
    M as k
} from "./BMP7zQoC.js";
const f = {
        class: "menu"
    },
    h = {
        key: 0,
        class: "dropdown"
    },
    M = {
        class: "item"
    };
const _ = o({
    name: "AutopilotagentMenu",
    components: {
        MenuIcon: k
    },
    props: {},
    async setup(o, {
        emit: t,
        slots: i
    }) {
        n(o.not_login), p("currentUser"), d(), m();
        const l = n(!1),
            {
                t: r,
                locale: u,
                locales: v
            } = e(),
            {
                $i18n: g
            } = s(),
            k = n(u.value),
            f = () => {
                l.value = !1
            };
        return a((() => {
            document.removeEventListener("click", f)
        })), c((async () => {
            document.addEventListener("click", f)
        })), {
            currentLocale: k,
            locales: v,
            login: () => {
                location.href = "/api/login?redirect_url=" + encodeURIComponent(location.pathname + location.search)
            },
            goto: o => {
                location.href = o
            },
            clickMenuicon: o => {
                o.stopPropagation(), o.preventDefault(), l.value = !l.value
            },
            showDropdown: l
        }
    }
}, [
    ["render", function(o, n, e, s, a, c) {
        const p = v("MenuIcon");
        return g(), t("div", f, [i("div", {
            class: "icon",
            onClick: n[0] || (n[0] = (...o) => s.clickMenuicon && s.clickMenuicon(...o))
        }, [r(p)]), s.showDropdown ? (g(), t("div", h, [i("div", M, [i("div", {
            onClick: n[1] || (n[1] = o => s.goto("/"))
        }, u(o.$t("components.autopilotagent-menu.go-to-genspark")), 1)])])) : l("", !0)])
    }],
    ["__scopeId", "data-v-95b7c9cb"]
]);
export {
    _
};