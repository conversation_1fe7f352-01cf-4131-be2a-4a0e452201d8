import {
    a2 as e
} from "./Cf0SOiw0.js";

function n(e) {
    function n(e, n) {
        let t;
        const s = [],
            r = n.length;
        for (let i = 0; i < r; i++) {
            const r = n[i];
            if (43 !== r.marker) continue;
            if (-1 === r.end) continue;
            const a = n[r.end];
            t = e.tokens[r.token], t.type = "ins_open", t.tag = "ins", t.nesting = 1, t.markup = "++", t.content = "", t = e.tokens[a.token], t.type = "ins_close", t.tag = "ins", t.nesting = -1, t.markup = "++", t.content = "", "text" === e.tokens[a.token - 1].type && "+" === e.tokens[a.token - 1].content && s.push(a.token - 1)
        }
        for (; s.length;) {
            const n = s.pop();
            let r = n + 1;
            for (; r < e.tokens.length && "ins_close" === e.tokens[r].type;) r++;
            r--, n !== r && (t = e.tokens[r], e.tokens[r] = e.tokens[n], e.tokens[n] = t)
        }
    }
    e.inline.ruler.before("emphasis", "ins", (function(e, n) {
        const t = e.pos,
            s = e.src.charCodeAt(t);
        if (n) return !1;
        if (43 !== s) return !1;
        const r = e.scanDelims(e.pos, !0);
        let i = r.length;
        const a = String.fromCharCode(s);
        if (i < 2) return !1;
        if (i % 2) {
            e.push("text", "", 0).content = a, i--
        }
        for (let o = 0; o < i; o += 2) {
            e.push("text", "", 0).content = a + a, (r.can_open || r.can_close) && e.delimiters.push({
                marker: s,
                length: 0,
                jump: o / 2,
                token: e.tokens.length - 1,
                end: -1,
                open: r.can_open,
                close: r.can_close
            })
        }
        return e.pos += r.length, !0
    })), e.inline.ruler2.before("emphasis", "ins", (function(e) {
        const t = e.tokens_meta,
            s = (e.tokens_meta || []).length;
        n(e, e.delimiters);
        for (let r = 0; r < s; r++) t[r] && t[r].delimiters && n(e, t[r].delimiters)
    }))
}

function t(e) {
    function n(e, n) {
        const t = [],
            s = n.length;
        for (let r = 0; r < s; r++) {
            const s = n[r];
            if (61 !== s.marker) continue;
            if (-1 === s.end) continue;
            const i = n[s.end],
                a = e.tokens[s.token];
            a.type = "mark_open", a.tag = "mark", a.nesting = 1, a.markup = "==", a.content = "";
            const o = e.tokens[i.token];
            o.type = "mark_close", o.tag = "mark", o.nesting = -1, o.markup = "==", o.content = "", "text" === e.tokens[i.token - 1].type && "=" === e.tokens[i.token - 1].content && t.push(i.token - 1)
        }
        for (; t.length;) {
            const n = t.pop();
            let s = n + 1;
            for (; s < e.tokens.length && "mark_close" === e.tokens[s].type;) s++;
            if (s--, n !== s) {
                const t = e.tokens[s];
                e.tokens[s] = e.tokens[n], e.tokens[n] = t
            }
        }
    }
    e.inline.ruler.before("emphasis", "mark", (function(e, n) {
        const t = e.pos,
            s = e.src.charCodeAt(t);
        if (n) return !1;
        if (61 !== s) return !1;
        const r = e.scanDelims(e.pos, !0);
        let i = r.length;
        const a = String.fromCharCode(s);
        if (i < 2) return !1;
        if (i % 2) {
            e.push("text", "", 0).content = a, i--
        }
        for (let o = 0; o < i; o += 2) {
            e.push("text", "", 0).content = a + a, (r.can_open || r.can_close) && e.delimiters.push({
                marker: s,
                length: 0,
                jump: o / 2,
                token: e.tokens.length - 1,
                end: -1,
                open: r.can_open,
                close: r.can_close
            })
        }
        return e.pos += r.length, !0
    })), e.inline.ruler2.before("emphasis", "mark", (function(e) {
        let t;
        const s = e.tokens_meta,
            r = (e.tokens_meta || []).length;
        for (n(e, e.delimiters), t = 0; t < r; t++) s[t] && s[t].delimiters && n(e, s[t].delimiters)
    }))
}
const s = /\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;

function r(e, n) {
    const t = e.posMax,
        r = e.pos;
    if (126 !== e.src.charCodeAt(r)) return !1;
    if (n) return !1;
    if (r + 2 >= t) return !1;
    e.pos = r + 1;
    let i = !1;
    for (; e.pos < t;) {
        if (126 === e.src.charCodeAt(e.pos)) {
            i = !0;
            break
        }
        e.md.inline.skipToken(e)
    }
    if (!i || r + 1 === e.pos) return e.pos = r, !1;
    const a = e.src.slice(r + 1, e.pos);
    if (a.match(/(^|[^\\])(\\\\)*\s/)) return e.pos = r, !1;
    e.posMax = e.pos, e.pos = r + 1;
    e.push("sub_open", "sub", 1).markup = "~";
    e.push("text", "", 0).content = a.replace(s, "$1");
    return e.push("sub_close", "sub", -1).markup = "~", e.pos = e.posMax + 1, e.posMax = t, !0
}

function i(e) {
    e.inline.ruler.after("emphasis", "sub", r)
}
const a = /\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;

function o(e, n) {
    const t = e.posMax,
        s = e.pos;
    if (94 !== e.src.charCodeAt(s)) return !1;
    if (n) return !1;
    if (s + 2 >= t) return !1;
    e.pos = s + 1;
    let r = !1;
    for (; e.pos < t;) {
        if (94 === e.src.charCodeAt(e.pos)) {
            r = !0;
            break
        }
        e.md.inline.skipToken(e)
    }
    if (!r || s + 1 === e.pos) return e.pos = s, !1;
    const i = e.src.slice(s + 1, e.pos);
    if (i.match(/(^|[^\\])(\\\\)*\s/)) return e.pos = s, !1;
    e.posMax = e.pos, e.pos = s + 1;
    e.push("sup_open", "sup", 1).markup = "^";
    e.push("text", "", 0).content = i.replace(a, "$1");
    return e.push("sup_close", "sup", -1).markup = "^", e.pos = e.posMax + 1, e.posMax = t, !0
}

function l(e) {
    e.inline.ruler.after("emphasis", "sup", o)
}
var c = !1,
    p = {
        false: "push",
        true: "unshift",
        after: "push",
        before: "unshift"
    },
    u = {
        isPermalinkSymbol: !0
    };

function f(e, n, t, s) {
    var r;
    if (!c) {
        "object" == typeof process && process && process.emitWarning && process.emitWarning("Using deprecated markdown-it-anchor permalink option, see https://github.com/valeriangalliat/markdown-it-anchor#permalinks"), c = !0
    }
    var i = [Object.assign(new t.Token("link_open", "a", 1), {
        attrs: [].concat(n.permalinkClass ? [
            ["class", n.permalinkClass]
        ] : [], [
            ["href", n.permalinkHref(e, t)]
        ], Object.entries(n.permalinkAttrs(e, t)))
    }), Object.assign(new t.Token("html_block", "", 0), {
        content: n.permalinkSymbol,
        meta: u
    }), new t.Token("link_close", "a", -1)];
    n.permalinkSpace && t.tokens[s + 1].children[p[n.permalinkBefore]](Object.assign(new t.Token("text", "", 0), {
        content: " "
    })), (r = t.tokens[s + 1].children)[p[n.permalinkBefore]].apply(r, i)
}

function k(e) {
    return "#" + e
}

function d(e) {
    return {}
}
var h = {
    class: "header-anchor",
    symbol: "#",
    renderHref: k,
    renderAttrs: d
};

function m(e) {
    function n(t) {
        return t = Object.assign({}, n.defaults, t),
            function(n, s, r, i) {
                return e(n, t, s, r, i)
            }
    }
    return n.defaults = Object.assign({}, h), n.renderPermalinkImpl = e, n
}

function b(e) {
    var n = [],
        t = e.filter((function(e) {
            if ("class" !== e[0]) return !0;
            n.push(e[1])
        }));
    return n.length > 0 && t.unshift(["class", n.join(" ")]), t
}
var g = m((function(e, n, t, s, r) {
    var i, a = [Object.assign(new s.Token("link_open", "a", 1), {
        attrs: b([].concat(n.class ? [
            ["class", n.class]
        ] : [], [
            ["href", n.renderHref(e, s)]
        ], n.ariaHidden ? [
            ["aria-hidden", "true"]
        ] : [], Object.entries(n.renderAttrs(e, s))))
    }), Object.assign(new s.Token("html_inline", "", 0), {
        content: n.symbol,
        meta: u
    }), new s.Token("link_close", "a", -1)];
    if (n.space) {
        var o = "string" == typeof n.space ? n.space : " ";
        s.tokens[r + 1].children[p[n.placement]](Object.assign(new s.Token("string" == typeof n.space ? "html_inline" : "text", "", 0), {
            content: o
        }))
    }(i = s.tokens[r + 1].children)[p[n.placement]].apply(i, a)
}));
Object.assign(g.defaults, {
    space: !0,
    placement: "after",
    ariaHidden: !1
});
var y = m(g.renderPermalinkImpl);
y.defaults = Object.assign({}, g.defaults, {
    ariaHidden: !0
});
var _ = m((function(e, n, t, s, r) {
    var i = [Object.assign(new s.Token("link_open", "a", 1), {
        attrs: b([].concat(n.class ? [
            ["class", n.class]
        ] : [], [
            ["href", n.renderHref(e, s)]
        ], Object.entries(n.renderAttrs(e, s))))
    })].concat(n.safariReaderFix ? [new s.Token("span_open", "span", 1)] : [], s.tokens[r + 1].children, n.safariReaderFix ? [new s.Token("span_close", "span", -1)] : [], [new s.Token("link_close", "a", -1)]);
    s.tokens[r + 1] = Object.assign(new s.Token("inline", "", 0), {
        children: i
    })
}));
Object.assign(_.defaults, {
    safariReaderFix: !1
});
var x, w, v = m((function(e, n, t, s, r) {
    var i;
    if (!["visually-hidden", "aria-label", "aria-describedby", "aria-labelledby"].includes(n.style)) throw new Error("`permalink.linkAfterHeader` called with unknown style option `" + n.style + "`");
    if (!["aria-describedby", "aria-labelledby"].includes(n.style) && !n.assistiveText) throw new Error("`permalink.linkAfterHeader` called without the `assistiveText` option in `" + n.style + "` style");
    if ("visually-hidden" === n.style && !n.visuallyHiddenClass) throw new Error("`permalink.linkAfterHeader` called without the `visuallyHiddenClass` option in `visually-hidden` style");
    var a = s.tokens[r + 1].children.filter((function(e) {
            return "text" === e.type || "code_inline" === e.type
        })).reduce((function(e, n) {
            return e + n.content
        }), ""),
        o = [],
        l = [];
    if (n.class && l.push(["class", n.class]), l.push(["href", n.renderHref(e, s)]), l.push.apply(l, Object.entries(n.renderAttrs(e, s))), "visually-hidden" === n.style) {
        if (o.push(Object.assign(new s.Token("span_open", "span", 1), {
                attrs: [
                    ["class", n.visuallyHiddenClass]
                ]
            }), Object.assign(new s.Token("text", "", 0), {
                content: n.assistiveText(a)
            }), new s.Token("span_close", "span", -1)), n.space) {
            var c = "string" == typeof n.space ? n.space : " ";
            o[p[n.placement]](Object.assign(new s.Token("string" == typeof n.space ? "html_inline" : "text", "", 0), {
                content: c
            }))
        }
        o[p[n.placement]](Object.assign(new s.Token("span_open", "span", 1), {
            attrs: [
                ["aria-hidden", "true"]
            ]
        }), Object.assign(new s.Token("html_inline", "", 0), {
            content: n.symbol,
            meta: u
        }), new s.Token("span_close", "span", -1))
    } else o.push(Object.assign(new s.Token("html_inline", "", 0), {
        content: n.symbol,
        meta: u
    }));
    "aria-label" === n.style ? l.push(["aria-label", n.assistiveText(a)]) : ["aria-describedby", "aria-labelledby"].includes(n.style) && l.push([n.style, e]);
    var f = [Object.assign(new s.Token("link_open", "a", 1), {
        attrs: b(l)
    })].concat(o, [new s.Token("link_close", "a", -1)]);
    (i = s.tokens).splice.apply(i, [r + 3, 0].concat(f)), n.wrapper && (s.tokens.splice(r, 0, Object.assign(new s.Token("html_block", "", 0), {
        content: n.wrapper[0] + "\n"
    })), s.tokens.splice(r + 3 + f.length + 1, 0, Object.assign(new s.Token("html_block", "", 0), {
        content: n.wrapper[1] + "\n"
    })))
}));

function O(e, n, t, s) {
    var r = e,
        i = s;
    if (t && Object.prototype.hasOwnProperty.call(n, r)) throw new Error("User defined `id` attribute `" + e + "` is not unique. Please fix it in your Markdown to continue.");
    for (; Object.prototype.hasOwnProperty.call(n, r);) r = e + "-" + i, i += 1;
    return n[r] = !0, r
}

function j(e, n) {
    n = Object.assign({}, j.defaults, n), e.core.ruler.push("anchor", (function(e) {
        for (var t, s = {}, r = e.tokens, i = Array.isArray(n.level) ? (t = n.level, function(e) {
                return t.includes(e)
            }) : function(e) {
                return function(n) {
                    return n >= e
                }
            }(n.level), a = 0; a < r.length; a++) {
            var o = r[a];
            if ("heading_open" === o.type && i(Number(o.tag.substr(1)))) {
                var l = n.getTokensText(r[a + 1].children),
                    c = o.attrGet("id");
                c = null == c ? O(c = n.slugifyWithState ? n.slugifyWithState(l, e) : n.slugify(l), s, !1, n.uniqueSlugStartIndex) : O(c, s, !0, n.uniqueSlugStartIndex), o.attrSet("id", c), !1 !== n.tabIndex && o.attrSet("tabindex", "" + n.tabIndex), "function" == typeof n.permalink ? n.permalink(c, n, e, a) : (n.permalink || n.renderPermalink && n.renderPermalink !== f) && n.renderPermalink(c, n, e, a), a = r.indexOf(o), n.callback && n.callback(o, {
                    slug: c,
                    title: l
                })
            }
        }
    }))
}
Object.assign(v.defaults, {
    style: "visually-hidden",
    space: !0,
    placement: "after",
    wrapper: null
}), j.permalink = {
    __proto__: null,
    legacy: f,
    renderHref: k,
    renderAttrs: d,
    makePermalink: m,
    linkInsideHeader: g,
    ariaHidden: y,
    headerLink: _,
    linkAfterHeader: v
}, j.defaults = {
    level: 1,
    slugify: function(e) {
        return encodeURIComponent(String(e).trim().toLowerCase().replace(/\s+/g, "-"))
    },
    uniqueSlugStartIndex: 1,
    tabIndex: "-1",
    getTokensText: function(e) {
        return e.filter((function(e) {
            return ["text", "code_inline"].includes(e.type)
        })).map((function(e) {
            return e.content
        })).join("")
    },
    permalink: !1,
    renderPermalink: f,
    permalinkClass: y.defaults.class,
    permalinkSpace: y.defaults.space,
    permalinkSymbol: "¶",
    permalinkBefore: "before" === y.defaults.placement,
    permalinkHref: y.defaults.renderHref,
    permalinkAttrs: y.defaults.renderAttrs
}, j.default = j;
const T = e(function() {
    if (w) return x;
    w = 1;
    var e = !0,
        n = !1,
        t = !1;

    function s(e, n, t) {
        var s = e.attrIndex(n),
            r = [n, t];
        s < 0 ? e.attrPush(r) : e.attrs[s] = r
    }

    function r(e, n) {
        for (var t = e[n].level - 1, s = n - 1; s >= 0; s--)
            if (e[s].level === t) return s;
        return -1
    }

    function i(e, n) {
        return "inline" === e[n].type && function(e) {
            return "paragraph_open" === e.type
        }(e[n - 1]) && function(e) {
            return "list_item_open" === e.type
        }(e[n - 2]) && function(e) {
            return 0 === e.content.indexOf("[ ] ") || 0 === e.content.indexOf("[x] ") || 0 === e.content.indexOf("[X] ")
        }(e[n])
    }

    function a(s, r) {
        if (s.children.unshift(function(n, t) {
                var s = new t("html_inline", "", 0),
                    r = e ? ' disabled="" ' : "";
                0 === n.content.indexOf("[ ] ") ? s.content = '<input class="task-list-item-checkbox"' + r + 'type="checkbox">' : 0 !== n.content.indexOf("[x] ") && 0 !== n.content.indexOf("[X] ") || (s.content = '<input class="task-list-item-checkbox" checked=""' + r + 'type="checkbox">');
                return s
            }(s, r)), s.children[1].content = s.children[1].content.slice(3), s.content = s.content.slice(3), n)
            if (t) {
                s.children.pop();
                var i = "task-item-" + Math.ceil(1e7 * Math.random() - 1e3);
                s.children[0].content = s.children[0].content.slice(0, -1) + ' id="' + i + '">', s.children.push(function(e, n, t) {
                    var s = new t("html_inline", "", 0);
                    return s.content = '<label class="task-list-item-label" for="' + n + '">' + e + "</label>", s.attrs = [{
                        for: n
                    }], s
                }(s.content, i, r))
            } else s.children.unshift(function(e) {
                var n = new e("html_inline", "", 0);
                return n.content = "<label>", n
            }(r)), s.children.push(function(e) {
                var n = new e("html_inline", "", 0);
                return n.content = "</label>", n
            }(r))
    }
    return x = function(o, l) {
        l && (e = !l.enabled, n = !!l.label, t = !!l.labelAfter), o.core.ruler.after("inline", "github-task-lists", (function(n) {
            for (var t = n.tokens, o = 2; o < t.length; o++) i(t, o) && (a(t[o], n.Token), s(t[o - 2], "class", "task-list-item" + (e ? "" : " enabled")), s(t[r(t, o - 2)], "class", "contains-task-list"))
        }))
    }
}());
export {
    t as a, j as b, l as c, n as i, i as s, T as t
};