import {
    W as e,
    r as t,
    s as a
} from "./Cf0SOiw0.js";
const E = {
        INIT_AGENT: "init-agent",
        GENERAL_CHAT: "AI Chat",
        DEEP_RESEARCH: "Deep Research",
        GENERATE_IMAGE: "Generate Image",
        GENERATE_VIDEO: "Generate Video",
        GENERATE_AUDIO: "Generate Audio",
        ALL_AGENTS: "All Agents",
        GENERATE_SLIDE: "AI Slide",
        CALL_FOR_ME: "Call for me",
        DOWNLOAD_FOR_ME: "Download for me",
        SHEETS_AGENT: "Sheets Agent",
        DOCS_AGENT: "Docs Agent",
        FACT_CHECK: "Fact Check",
        AI_PODS: "AI Pods",
        AI_INBOX: "AI Inbox",
        AI_PODCASTS: "AI Podcasts",
        AIDRIVE: "AI Drive",
        TRANSLATION: "Translation",
        MEETING_NOTES: "Meeting Notes",
        AI_DEVELOPER: "AI Developer"
    },
    _ = e("chat-input", (() => {
        const e = t("gpt-4.1"),
            _ = t(!1),
            s = t([]),
            n = t([]),
            o = t(!1),
            {
                t: A
            } = a(),
            l = t(E.INIT_AGENT),
            g = t(null),
            r = {
                SEARCH_AGENT: A("pages.index.ask_anything_create_anything"),
                GENERAL_CHAT: A("components.agents.agent-select.general-chat"),
                GENERATE_IMAGE: A("components.generate_image_content.create_image_message"),
                GENERATE_IMAGE_ON_IMAGE: A("components.generate_image_content.remix_image_message"),
                GENERATE_VIDEO: A("components.generate_image_content.what-video-do-you-want-to-create"),
                GENERATE_AUDIO: A("pages.agents.generate_audio_prompt_placeholder"),
                GENERATE_VIDEO_ON_IMAGE: A("components.generate_image_content.video-description-based-on-this-image"),
                DEEP_RESEARCH: A("pages.chat_agent.moa_deep_research_input_placeholder"),
                GENERATE_SLIDE: A("pages.agents.generate_slide_prompt_placeholder"),
                ENTER_YOUR_SLIDES: A("pages.agents.ai_slide_prompt_placeholder"),
                ASK_GENSPARK_TO_EDIT: A("components.slides.ask_genspark_to_edit"),
                AI_PODS: A("pages.agents.ai_podcasts_prompt_placeholder"),
                ENTER_YOUR_SHEETS: A("pages.agents.ai_sheets_prompt_placeholder")
            },
            p = t(r.SEARCH_AGENT),
            i = t(""),
            c = t({});
        return {
            modelsSelected: e,
            setModelsSelected: t => {
                e.value = t
            },
            selectAgent: l,
            setSelectAgent: e => {
                l.value = e
            },
            agentSelectedModel: g,
            setAgentSelectedModel: e => {
                g.value = e
            },
            placeholder: p,
            setPlaceholder: e => {
                p.value = e
            },
            prompt: i,
            setPrompt: e => {
                i.value = e
            },
            requestWebKnowledge: _,
            setRequestWebKnowledge: e => {
                _.value = e
            },
            images: s,
            setImages: e => {
                s.value = e
            },
            files: n,
            setFiles: e => {
                n.value = e
            },
            loadingNext: o,
            setLoadingNext: e => {
                o.value = e
            },
            PLACEHOLDER_MAP: r,
            browserExtensionData: c,
            setBrowserExtensionData: e => {
                c.value = e
            }
        }
    }));
export {
    E as S, _ as u
};