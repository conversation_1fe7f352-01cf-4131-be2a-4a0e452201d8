function t(t, n) {
    let o;
    if (void 0 === n)
        for (const e of t) null != e && (o < e || void 0 === o && e >= e) && (o = e);
    else {
        let e = -1;
        for (let r of t) null != (r = n(r, ++e, t)) && (o < r || void 0 === o && r >= r) && (o = r)
    }
    return o
}

function n(t, n) {
    let o;
    if (void 0 === n)
        for (const e of t) null != e && (o > e || void 0 === o && e >= e) && (o = e);
    else {
        let e = -1;
        for (let r of t) null != (r = n(r, ++e, t)) && (o > r || void 0 === o && r >= r) && (o = r)
    }
    return o
}

function o(t, n) {
    let o = 0;
    if (void 0 === n)
        for (let e of t)(e = +e) && (o += e);
    else {
        let e = -1;
        for (let r of t)(r = +n(r, ++e, t)) && (o += r)
    }
    return o
}

function e(t) {
    return t.target.depth
}

function r(t) {
    return t.depth
}

function i(t, n) {
    return n - 1 - t.height
}

function s(t, n) {
    return t.sourceLinks.length ? t.depth : n - 1
}

function u(t) {
    return t.targetLinks.length ? t.depth : t.sourceLinks.length ? n(t.sourceLinks, e) - 1 : 0
}

function f(t) {
    return function() {
        return t
    }
}

function c(t, n) {
    return l(t.source, n.source) || t.index - n.index
}

function h(t, n) {
    return l(t.target, n.target) || t.index - n.index
}

function l(t, n) {
    return t.y0 - n.y0
}

function a(t) {
    return t.value
}

function y(t) {
    return t.index
}

function g(t) {
    return t.nodes
}

function d(t) {
    return t.links
}

function _(t, n) {
    const o = t.get(n);
    if (!o) throw new Error("missing: " + n);
    return o
}

function p({
    nodes: t
}) {
    for (const n of t) {
        let t = n.y0,
            o = t;
        for (const e of n.sourceLinks) e.y0 = t + e.width / 2, t += e.width;
        for (const e of n.targetLinks) e.y1 = o + e.width / 2, o += e.width
    }
}

function k() {
    let e, r, i, u = 0,
        k = 0,
        x = 1,
        L = 1,
        v = 24,
        w = 8,
        M = y,
        b = s,
        S = g,
        m = d,
        A = 6;

    function T() {
        const s = {
            nodes: S.apply(null, arguments),
            links: m.apply(null, arguments)
        };
        return function({
                nodes: t,
                links: n
            }) {
                for (const [e, r] of t.entries()) r.index = e, r.sourceLinks = [], r.targetLinks = [];
                const o = new Map(t.map(((n, o) => [M(n, o, t), n])));
                for (const [e, r] of n.entries()) {
                    r.index = e;
                    let {
                        source: t,
                        target: n
                    } = r;
                    "object" != typeof t && (t = r.source = _(o, t)), "object" != typeof n && (n = r.target = _(o, n)), t.sourceLinks.push(r), n.targetLinks.push(r)
                }
                if (null != i)
                    for (const {
                            sourceLinks: e,
                            targetLinks: r
                        } of t) e.sort(i), r.sort(i)
            }(s),
            function({
                nodes: t
            }) {
                for (const n of t) n.value = void 0 === n.fixedValue ? Math.max(o(n.sourceLinks, a), o(n.targetLinks, a)) : n.fixedValue
            }(s),
            function({
                nodes: t
            }) {
                const n = t.length;
                let o = new Set(t),
                    e = new Set,
                    r = 0;
                for (; o.size;) {
                    for (const t of o) {
                        t.depth = r;
                        for (const {
                                target: n
                            } of t.sourceLinks) e.add(n)
                    }
                    if (++r > n) throw new Error("circular link");
                    o = e, e = new Set
                }
            }(s),
            function({
                nodes: t
            }) {
                const n = t.length;
                let o = new Set(t),
                    e = new Set,
                    r = 0;
                for (; o.size;) {
                    for (const t of o) {
                        t.height = r;
                        for (const {
                                source: n
                            } of t.targetLinks) e.add(n)
                    }
                    if (++r > n) throw new Error("circular link");
                    o = e, e = new Set
                }
            }(s),
            function(i) {
                const s = function({
                    nodes: n
                }) {
                    const o = t(n, (t => t.depth)) + 1,
                        e = (x - u - v) / (o - 1),
                        i = new Array(o);
                    for (const t of n) {
                        const n = Math.max(0, Math.min(o - 1, Math.floor(b.call(null, t, o))));
                        t.layer = n, t.x0 = u + n * e, t.x1 = t.x0 + v, i[n] ? i[n].push(t) : i[n] = [t]
                    }
                    if (r)
                        for (const t of i) t.sort(r);
                    return i
                }(i);
                e = Math.min(w, (L - k) / (t(s, (t => t.length)) - 1)),
                    function(t) {
                        const r = n(t, (t => (L - k - (t.length - 1) * e) / o(t, a)));
                        for (const n of t) {
                            let t = k;
                            for (const o of n) {
                                o.y0 = t, o.y1 = t + o.value * r, t = o.y1 + e;
                                for (const t of o.sourceLinks) t.width = t.value * r
                            }
                            t = (L - t + e) / (n.length + 1);
                            for (let o = 0; o < n.length; ++o) {
                                const e = n[o];
                                e.y0 += t * (o + 1), e.y1 += t * (o + 1)
                            }
                            I(n)
                        }
                    }(s);
                for (let t = 0; t < A; ++t) {
                    const n = Math.pow(.99, t),
                        o = Math.max(1 - n, (t + 1) / A);
                    E(s, n, o), z(s, n, o)
                }
            }(s), p(s), s
    }

    function z(t, n, o) {
        for (let e = 1, i = t.length; e < i; ++e) {
            const i = t[e];
            for (const t of i) {
                let o = 0,
                    e = 0;
                for (const {
                        source: n,
                        value: i
                    } of t.targetLinks) {
                    let r = i * (t.layer - n.layer);
                    o += V(n, t) * r, e += r
                }
                if (!(e > 0)) continue;
                let r = (o / e - t.y0) * n;
                t.y0 += r, t.y1 += r, P(t)
            }
            void 0 === r && i.sort(l), C(i, o)
        }
    }

    function E(t, n, o) {
        for (let e = t.length - 2; e >= 0; --e) {
            const i = t[e];
            for (const t of i) {
                let o = 0,
                    e = 0;
                for (const {
                        target: n,
                        value: i
                    } of t.sourceLinks) {
                    let r = i * (n.layer - t.layer);
                    o += Z(t, n) * r, e += r
                }
                if (!(e > 0)) continue;
                let r = (o / e - t.y0) * n;
                t.y0 += r, t.y1 += r, P(t)
            }
            void 0 === r && i.sort(l), C(i, o)
        }
    }

    function C(t, n) {
        const o = t.length >> 1,
            r = t[o];
        q(t, r.y0 - e, o - 1, n), j(t, r.y1 + e, o + 1, n), q(t, L, t.length - 1, n), j(t, k, 0, n)
    }

    function j(t, n, o, r) {
        for (; o < t.length; ++o) {
            const i = t[o],
                s = (n - i.y0) * r;
            s > 1e-6 && (i.y0 += s, i.y1 += s), n = i.y1 + e
        }
    }

    function q(t, n, o, r) {
        for (; o >= 0; --o) {
            const i = t[o],
                s = (i.y1 - n) * r;
            s > 1e-6 && (i.y0 -= s, i.y1 -= s), n = i.y0 - e
        }
    }

    function P({
        sourceLinks: t,
        targetLinks: n
    }) {
        if (void 0 === i) {
            for (const {
                    source: {
                        sourceLinks: t
                    }
                } of n) t.sort(h);
            for (const {
                    target: {
                        targetLinks: n
                    }
                } of t) n.sort(c)
        }
    }

    function I(t) {
        if (void 0 === i)
            for (const {
                    sourceLinks: n,
                    targetLinks: o
                } of t) n.sort(h), o.sort(c)
    }

    function V(t, n) {
        let o = t.y0 - (t.sourceLinks.length - 1) * e / 2;
        for (const {
                target: r,
                width: i
            } of t.sourceLinks) {
            if (r === n) break;
            o += i + e
        }
        for (const {
                source: e,
                width: r
            } of n.targetLinks) {
            if (e === t) break;
            o -= r
        }
        return o
    }

    function Z(t, n) {
        let o = n.y0 - (n.targetLinks.length - 1) * e / 2;
        for (const {
                source: r,
                width: i
            } of n.targetLinks) {
            if (r === t) break;
            o += i + e
        }
        for (const {
                target: e,
                width: r
            } of t.sourceLinks) {
            if (e === n) break;
            o -= r
        }
        return o
    }
    return T.update = function(t) {
        return p(t), t
    }, T.nodeId = function(t) {
        return arguments.length ? (M = "function" == typeof t ? t : f(t), T) : M
    }, T.nodeAlign = function(t) {
        return arguments.length ? (b = "function" == typeof t ? t : f(t), T) : b
    }, T.nodeSort = function(t) {
        return arguments.length ? (r = t, T) : r
    }, T.nodeWidth = function(t) {
        return arguments.length ? (v = +t, T) : v
    }, T.nodePadding = function(t) {
        return arguments.length ? (w = e = +t, T) : w
    }, T.nodes = function(t) {
        return arguments.length ? (S = "function" == typeof t ? t : f(t), T) : S
    }, T.links = function(t) {
        return arguments.length ? (m = "function" == typeof t ? t : f(t), T) : m
    }, T.linkSort = function(t) {
        return arguments.length ? (i = t, T) : i
    }, T.size = function(t) {
        return arguments.length ? (u = k = 0, x = +t[0], L = +t[1], T) : [x - u, L - k]
    }, T.extent = function(t) {
        return arguments.length ? (u = +t[0][0], x = +t[1][0], k = +t[0][1], L = +t[1][1], T) : [
            [u, k],
            [x, L]
        ]
    }, T.iterations = function(t) {
        return arguments.length ? (A = +t, T) : A
    }, T
}
var x = Math.PI,
    L = 2 * x,
    v = 1e-6,
    w = L - v;

function M() {
    this._x0 = this._y0 = this._x1 = this._y1 = null, this._ = ""
}

function b() {
    return new M
}

function S(t) {
    return function() {
        return t
    }
}

function m(t) {
    return t[0]
}

function A(t) {
    return t[1]
}
M.prototype = b.prototype = {
    constructor: M,
    moveTo: function(t, n) {
        this._ += "M" + (this._x0 = this._x1 = +t) + "," + (this._y0 = this._y1 = +n)
    },
    closePath: function() {
        null !== this._x1 && (this._x1 = this._x0, this._y1 = this._y0, this._ += "Z")
    },
    lineTo: function(t, n) {
        this._ += "L" + (this._x1 = +t) + "," + (this._y1 = +n)
    },
    quadraticCurveTo: function(t, n, o, e) {
        this._ += "Q" + +t + "," + +n + "," + (this._x1 = +o) + "," + (this._y1 = +e)
    },
    bezierCurveTo: function(t, n, o, e, r, i) {
        this._ += "C" + +t + "," + +n + "," + +o + "," + +e + "," + (this._x1 = +r) + "," + (this._y1 = +i)
    },
    arcTo: function(t, n, o, e, r) {
        t = +t, n = +n, o = +o, e = +e, r = +r;
        var i = this._x1,
            s = this._y1,
            u = o - t,
            f = e - n,
            c = i - t,
            h = s - n,
            l = c * c + h * h;
        if (r < 0) throw new Error("negative radius: " + r);
        if (null === this._x1) this._ += "M" + (this._x1 = t) + "," + (this._y1 = n);
        else if (l > v)
            if (Math.abs(h * u - f * c) > v && r) {
                var a = o - i,
                    y = e - s,
                    g = u * u + f * f,
                    d = a * a + y * y,
                    _ = Math.sqrt(g),
                    p = Math.sqrt(l),
                    k = r * Math.tan((x - Math.acos((g + l - d) / (2 * _ * p))) / 2),
                    L = k / p,
                    w = k / _;
                Math.abs(L - 1) > v && (this._ += "L" + (t + L * c) + "," + (n + L * h)), this._ += "A" + r + "," + r + ",0,0," + +(h * a > c * y) + "," + (this._x1 = t + w * u) + "," + (this._y1 = n + w * f)
            } else this._ += "L" + (this._x1 = t) + "," + (this._y1 = n);
        else;
    },
    arc: function(t, n, o, e, r, i) {
        t = +t, n = +n, i = !!i;
        var s = (o = +o) * Math.cos(e),
            u = o * Math.sin(e),
            f = t + s,
            c = n + u,
            h = 1 ^ i,
            l = i ? e - r : r - e;
        if (o < 0) throw new Error("negative radius: " + o);
        null === this._x1 ? this._ += "M" + f + "," + c : (Math.abs(this._x1 - f) > v || Math.abs(this._y1 - c) > v) && (this._ += "L" + f + "," + c), o && (l < 0 && (l = l % L + L), l > w ? this._ += "A" + o + "," + o + ",0,1," + h + "," + (t - s) + "," + (n - u) + "A" + o + "," + o + ",0,1," + h + "," + (this._x1 = f) + "," + (this._y1 = c) : l > v && (this._ += "A" + o + "," + o + ",0," + +(l >= x) + "," + h + "," + (this._x1 = t + o * Math.cos(r)) + "," + (this._y1 = n + o * Math.sin(r))))
    },
    rect: function(t, n, o, e) {
        this._ += "M" + (this._x0 = this._x1 = +t) + "," + (this._y0 = this._y1 = +n) + "h" + +o + "v" + +e + "h" + -o + "Z"
    },
    toString: function() {
        return this._
    }
};
var T = Array.prototype.slice;

function z(t) {
    return t.source
}

function E(t) {
    return t.target
}

function C(t, n, o, e, r) {
    t.moveTo(n, o), t.bezierCurveTo(n = (n + e) / 2, o, n, r, e, r)
}

function j() {
    return function(t) {
        var n = z,
            o = E,
            e = m,
            r = A,
            i = null;

        function s() {
            var s, u = T.call(arguments),
                f = n.apply(this, u),
                c = o.apply(this, u);
            if (i || (i = s = b()), t(i, +e.apply(this, (u[0] = f, u)), +r.apply(this, u), +e.apply(this, (u[0] = c, u)), +r.apply(this, u)), s) return i = null, s + "" || null
        }
        return s.source = function(t) {
            return arguments.length ? (n = t, s) : n
        }, s.target = function(t) {
            return arguments.length ? (o = t, s) : o
        }, s.x = function(t) {
            return arguments.length ? (e = "function" == typeof t ? t : S(+t), s) : e
        }, s.y = function(t) {
            return arguments.length ? (r = "function" == typeof t ? t : S(+t), s) : r
        }, s.context = function(t) {
            return arguments.length ? (i = null == t ? null : t, s) : i
        }, s
    }(C)
}

function q(t) {
    return [t.source.x1, t.y0]
}

function P(t) {
    return [t.target.x0, t.y1]
}

function I() {
    return j().source(q).target(P)
}
export {
    k as S, u as c, I as d, s as j, r as l, i as r
};