try {
    self["workbox:window:7.0.0"] && _()
} catch (l) {}

function n(n, t) {
    return new Promise((function(e) {
        var r = new MessageChannel;
        r.port1.onmessage = function(n) {
            e(n.data)
        }, n.postMessage(t, [r.port2])
    }))
}

function t(n) {
    var t = function(n) {
        if ("object" != typeof n || !n) return n;
        var t = n[Symbol.toPrimitive];
        if (void 0 !== t) {
            var e = t.call(n, "string");
            if ("object" != typeof e) return e;
            throw new TypeError("@@toPrimitive must return a primitive value.")
        }
        return String(n)
    }(n);
    return "symbol" == typeof t ? t : t + ""
}

function e(n, t) {
    return (e = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(n, t) {
        return n.__proto__ = t, n
    })(n, t)
}

function r(n, t) {
    (null == t || t > n.length) && (t = n.length);
    for (var e = 0, r = new Array(t); e < t; e++) r[e] = n[e];
    return r
}
try {
    self["workbox:core:7.0.0"] && _()
} catch (l) {}
var o = function() {
    var n = this;
    this.promise = new Promise((function(t, e) {
        n.resolve = t, n.reject = e
    }))
};

function i(n, t) {
    var e = location.href;
    return new URL(n, e).href === new URL(t, e).href
}
var a = function(n, t) {
    this.type = n, Object.assign(this, t)
};

function s(n, t, e) {
    return e ? t ? t(n) : n : (n && n.then || (n = Promise.resolve(n)), t ? n.then(t) : n)
}

function c() {}
var u = {
    type: "SKIP_WAITING"
};

function f(n, t) {
    return n && n.then ? n.then(c) : Promise.resolve()
}
var v = function(r) {
    function c(n, t) {
        var e, c;
        return void 0 === t && (t = {}), (e = r.call(this) || this).nn = {}, e.tn = 0, e.rn = new o, e.en = new o, e.on = new o, e.un = 0, e.an = new Set, e.cn = function() {
            var n = e.fn,
                t = n.installing;
            e.tn > 0 || !i(t.scriptURL, e.sn.toString()) || performance.now() > e.un + 6e4 ? (e.vn = t, n.removeEventListener("updatefound", e.cn)) : (e.hn = t, e.an.add(t), e.rn.resolve(t)), ++e.tn, t.addEventListener("statechange", e.ln)
        }, e.ln = function(n) {
            var t = e.fn,
                r = n.target,
                o = r.state,
                i = r === e.vn,
                s = {
                    sw: r,
                    isExternal: i,
                    originalEvent: n
                };
            !i && e.mn && (s.isUpdate = !0), e.dispatchEvent(new a(o, s)), "installed" === o ? e.wn = self.setTimeout((function() {
                "installed" === o && t.waiting === r && e.dispatchEvent(new a("waiting", s))
            }), 200) : "activating" === o && (clearTimeout(e.wn), i || e.en.resolve(r))
        }, e.yn = function(n) {
            var t = e.hn,
                r = t !== navigator.serviceWorker.controller;
            e.dispatchEvent(new a("controlling", {
                isExternal: r,
                originalEvent: n,
                sw: t,
                isUpdate: e.mn
            })), r || e.on.resolve(t)
        }, e.gn = (c = function(n) {
            var t = n.data,
                r = n.ports,
                o = n.source;
            return s(e.getSW(), (function() {
                e.an.has(o) && e.dispatchEvent(new a("message", {
                    data: t,
                    originalEvent: n,
                    ports: r,
                    sw: o
                }))
            }))
        }, function() {
            for (var n = [], t = 0; t < arguments.length; t++) n[t] = arguments[t];
            try {
                return Promise.resolve(c.apply(this, n))
            } catch (e) {
                return Promise.reject(e)
            }
        }), e.sn = n, e.nn = t, navigator.serviceWorker.addEventListener("message", e.gn), e
    }
    var v, h;
    h = r, (v = c).prototype = Object.create(h.prototype), v.prototype.constructor = v, e(v, h);
    var d, p, g = c.prototype;
    return g.register = function(n) {
        var t, e, r = (void 0 === n ? {} : n).immediate,
            o = void 0 !== r && r;
        try {
            var c = this;
            return s((t = function() {
                return c.mn = Boolean(navigator.serviceWorker.controller), c.dn = c.pn(), s(c.bn(), (function(n) {
                    c.fn = n, c.dn && (c.hn = c.dn, c.en.resolve(c.dn), c.on.resolve(c.dn), c.dn.addEventListener("statechange", c.ln, {
                        once: !0
                    }));
                    var t = c.fn.waiting;
                    return t && i(t.scriptURL, c.sn.toString()) && (c.hn = t, Promise.resolve().then((function() {
                        c.dispatchEvent(new a("waiting", {
                            sw: t,
                            wasWaitingBeforeRegister: !0
                        }))
                    })).then((function() {}))), c.hn && (c.rn.resolve(c.hn), c.an.add(c.hn)), c.fn.addEventListener("updatefound", c.cn), navigator.serviceWorker.addEventListener("controllerchange", c.yn), c.fn
                }))
            }, (e = function() {
                if (!o && "complete" !== document.readyState) return f(new Promise((function(n) {
                    return window.addEventListener("load", n)
                })))
            }()) && e.then ? e.then(t) : t()))
        } catch (u) {
            return Promise.reject(u)
        }
    }, g.update = function() {
        try {
            return this.fn ? s(f(this.fn.update())) : s()
        } catch (l) {
            return Promise.reject(l)
        }
    }, g.getSW = function() {
        return void 0 !== this.hn ? Promise.resolve(this.hn) : this.rn.promise
    }, g.messageSW = function(t) {
        try {
            return s(this.getSW(), (function(e) {
                return n(e, t)
            }))
        } catch (l) {
            return Promise.reject(l)
        }
    }, g.messageSkipWaiting = function() {
        this.fn && this.fn.waiting && n(this.fn.waiting, u)
    }, g.pn = function() {
        var n = navigator.serviceWorker.controller;
        return n && i(n.scriptURL, this.sn.toString()) ? n : void 0
    }, g.bn = function() {
        try {
            var n = this;
            return s(function(t, e) {
                try {
                    var r = s(navigator.serviceWorker.register(n.sn, n.nn), (function(t) {
                        return n.un = performance.now(), t
                    }))
                } catch (o) {
                    return e(o)
                }
                return r && r.then ? r.then(void 0, e) : r
            }(0, (function(n) {
                throw n
            })))
        } catch (t) {
            return Promise.reject(t)
        }
    }, d = c, (p = [{
        key: "active",
        get: function() {
            return this.en.promise
        }
    }, {
        key: "controlling",
        get: function() {
            return this.on.promise
        }
    }]) && function(n, e) {
        for (var r = 0; r < e.length; r++) {
            var o = e[r];
            o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(n, t(o.key), o)
        }
    }(d.prototype, p), Object.defineProperty(d, "prototype", {
        writable: !1
    }), d
}(function() {
    function n() {
        this.Pn = new Map
    }
    var t = n.prototype;
    return t.addEventListener = function(n, t) {
        this.jn(n).add(t)
    }, t.removeEventListener = function(n, t) {
        this.jn(n).delete(t)
    }, t.dispatchEvent = function(n) {
        n.target = this;
        for (var t, e = function(n, t) {
                var e = "undefined" != typeof Symbol && n[Symbol.iterator] || n["@@iterator"];
                if (e) return (e = e.call(n)).next.bind(e);
                if (Array.isArray(n) || (e = function(n, t) {
                        if (n) {
                            if ("string" == typeof n) return r(n, t);
                            var e = Object.prototype.toString.call(n).slice(8, -1);
                            return "Object" === e && n.constructor && (e = n.constructor.name), "Map" === e || "Set" === e ? Array.from(n) : "Arguments" === e || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e) ? r(n, t) : void 0
                        }
                    }(n)) || t) {
                    e && (n = e);
                    var o = 0;
                    return function() {
                        return o >= n.length ? {
                            done: !0
                        } : {
                            done: !1,
                            value: n[o++]
                        }
                    }
                }
                throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
            }(this.jn(n.type)); !(t = e()).done;)(0, t.value)(n)
    }, t.jn = function(n) {
        return this.Pn.has(n) || this.Pn.set(n, new Set), this.Pn.get(n)
    }, n
}());
export {
    v as Workbox, a as WorkboxEvent, n as messageSW
};