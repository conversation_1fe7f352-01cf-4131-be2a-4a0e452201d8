import {
    n as t,
    e
} from "./DbJ6Dt9m.js";
import {
    f as a
} from "./Bl-gMEVt.js";
const n = "/api/aidrive",
    o = "files",
    r = {
        async fetch(a, r = "all") {
            try {
                const s = t(a, !0),
                    i = await $fetch(`${n}/ls/${o}/${s}?filter_type=${r}&sort_by=modified_desc`);
                if (!i || !i.items) throw new Error("Invalid response from server");
                return i.items.map(((t, a) => ({
                    id: t.id || "",
                    parent_id: t.parent_id || "",
                    name: t.name,
                    isDir: "directory" === t.type,
                    path: e(t.path),
                    modified_time: t.modified_time || 0,
                    size: t.size || 0,
                    type: t.type,
                    mime_type: t.mime_type || ("directory" === t.type ? "application/directory" : "application/octet-stream"),
                    index: a
                })))
            } catch (s) {
                throw s
            }
        },
        async getPrivateStorageUrl(e) {
            try {
                const a = t(e, !1),
                    r = await $fetch(`${n}/private_url/${o}/${a}`);
                if (200 !== r.status_code || !r.private_url) throw new Error("Failed to get private storage URL");
                return r.private_url
            } catch (a) {
                throw a
            }
        },
        download(t) {
            return this.downloadFile(t)
        },
        downloadFile(e) {
            const a = t(e, !1);
            return `${n}/download/${o}/${a}`
        },
        streamMedia(e) {
            const a = t(e, !1);
            return `${n}/download/${o}/${a}?stream=true`
        },
        async uploadFile(t, e) {
            let a;
            return e instanceof FormData ? a = e : (a = new FormData, a.append("file", e)), await $fetch(`${n}/upload/${o}/${t}`, {
                method: "POST",
                body: a
            })
        },
        async createDirectory(e) {
            const a = t(e, !0);
            return await $fetch(`${n}/mkdir/${o}/${a}`, {
                method: "POST"
            })
        },
        async createDirectoryWithParents(e) {
            const a = t(e, !0);
            return await $fetch(`${n}/mkdir_parents/${o}/${a}`, {
                method: "POST"
            })
        },
        async detectUrl(t) {
            const e = await fetch(`${n}/detect`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    url: t
                })
            });
            return await e.json()
        },
        async deleteItem(e, a = !1) {
            const r = t(e, a);
            return await $fetch(`${n}/delete/${o}/${r}`, {
                method: "DELETE"
            })
        },
        async moveItem(e, a) {
            const o = decodeURIComponent(t(e, !1)),
                r = decodeURIComponent(t(a, !1));
            return await $fetch(`${n}/move`, {
                method: "POST",
                body: {
                    src_path: o,
                    dst_path: r
                }
            })
        },
        async startAsyncDownload(t) {
            const e = await fetch(`${n}/async_download`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(t)
            });
            return await e.json()
        },
        async startAsyncArchive(t) {
            const e = await fetch(`${n}/async_archive`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(t)
            });
            return await e.json()
        },
        async getInitialTasks() {
            const t = await fetch(`${n}/async_task/initial_tasks`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                }
            });
            return await t.json()
        },
        async clearCompletedTasks(t) {
            const e = await fetch(`${n}/async_task/clear_tasks`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    task_ids: t
                })
            });
            return await e.json()
        },
        async cancelTask(t, e) {
            const a = await fetch(`${n}/async_task/cancel`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    task_id: t,
                    task_type: e
                })
            });
            return await a.json()
        },
        streamTasks: (t, e, o) => a(`${n}/async_task/stream`, {
            tasks: t
        }, e, o),
        async getUploadUrl(e) {
            const a = t(e, !1);
            return await $fetch(`${n}/get_upload_url/${o}/${a}`)
        },
        async confirmUpload(e, a, r = "application/octet-stream") {
            const s = t(e, !1);
            return await $fetch(`${n}/confirm_upload/${o}/${s}`, {
                method: "POST",
                body: {
                    token: a,
                    mime_type: r
                }
            })
        },
        getStorageUsage: async () => await $fetch(`${n}/storage_usage`)
    };
export {
    r as a
};