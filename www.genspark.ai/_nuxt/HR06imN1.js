var e, t, n, s, r = Object.defineProperty,
    i = e => {
        throw TypeError(e)
    },
    l = (e, t, n) => ((e, t, n) => t in e ? r(e, t, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: n
    }) : e[t] = n)(e, "symbol" != typeof t ? t + "" : t, n),
    o = (e, t, n) => (((e, t, n) => {
        t.has(e) || i("Cannot " + n)
    })(e, t, "access private method"), n);

function a() {
    return {
        async: !1,
        breaks: !1,
        extensions: null,
        gfm: !0,
        hooks: null,
        pedantic: !1,
        renderer: null,
        silent: !1,
        tokenizer: null,
        walkTokens: null
    }
}
let c = {
    async: !1,
    breaks: !1,
    extensions: null,
    gfm: !0,
    hooks: null,
    pedantic: !1,
    renderer: null,
    silent: !1,
    tokenizer: null,
    walkTokens: null
};

function h(e) {
    c = e
}
const p = /[&<>"']/,
    u = new RegExp(p.source, "g"),
    k = /[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,
    g = new RegExp(k.source, "g"),
    f = {
        "&": "&amp;",
        "<": "&lt;",
        ">": "&gt;",
        '"': "&quot;",
        "'": "&#39;"
    },
    d = e => f[e];

function x(e, t) {
    if (t) {
        if (p.test(e)) return e.replace(u, d)
    } else if (k.test(e)) return e.replace(g, d);
    return e
}
const b = /&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;
const w = /(^|[^\[])\^/g;

function m(e, t) {
    let n = "string" == typeof e ? e : e.source;
    t = t || "";
    const s = {
        replace: (e, t) => {
            let r = "string" == typeof t ? t : t.source;
            return r = r.replace(w, "$1"), n = n.replace(e, r), s
        },
        getRegex: () => new RegExp(n, t)
    };
    return s
}

function y(e) {
    try {
        e = encodeURI(e).replace(/%25/g, "%")
    } catch {
        return null
    }
    return e
}
const $ = {
    exec: () => null
};

function z(e, t) {
    const n = e.replace(/\|/g, ((e, t, n) => {
        let s = !1,
            r = t;
        for (; --r >= 0 && "\\" === n[r];) s = !s;
        return s ? "|" : " |"
    })).split(/ \|/);
    let s = 0;
    if (n[0].trim() || n.shift(), n.length > 0 && !n[n.length - 1].trim() && n.pop(), t)
        if (n.length > t) n.splice(t);
        else
            for (; n.length < t;) n.push("");
    for (; s < n.length; s++) n[s] = n[s].trim().replace(/\\\|/g, "|");
    return n
}

function R(e, t, n) {
    const s = e.length;
    if (0 === s) return "";
    let r = 0;
    for (; r < s;) {
        if (e.charAt(s - r - 1) !== t) break;
        r++
    }
    return e.slice(0, s - r)
}

function T(e, t, n, s) {
    const r = t.href,
        i = t.title ? x(t.title) : null,
        l = e[1].replace(/\\([\[\]])/g, "$1");
    if ("!" !== e[0].charAt(0)) {
        s.state.inLink = !0;
        const e = {
            type: "link",
            raw: n,
            href: r,
            title: i,
            text: l,
            tokens: s.inlineTokens(l)
        };
        return s.state.inLink = !1, e
    }
    return {
        type: "image",
        raw: n,
        href: r,
        title: i,
        text: x(l)
    }
}
class _ {
    constructor(e) {
        l(this, "options"), l(this, "rules"), l(this, "lexer"), this.options = e || c
    }
    space(e) {
        const t = this.rules.block.newline.exec(e);
        if (t && t[0].length > 0) return {
            type: "space",
            raw: t[0]
        }
    }
    code(e) {
        const t = this.rules.block.code.exec(e);
        if (t) {
            const e = t[0].replace(/^ {1,4}/gm, "");
            return {
                type: "code",
                raw: t[0],
                codeBlockStyle: "indented",
                text: this.options.pedantic ? e : R(e, "\n")
            }
        }
    }
    fences(e) {
        const t = this.rules.block.fences.exec(e);
        if (t) {
            const e = t[0],
                n = function(e, t) {
                    const n = e.match(/^(\s+)(?:```)/);
                    if (null === n) return t;
                    const s = n[1];
                    return t.split("\n").map((e => {
                        const t = e.match(/^\s+/);
                        if (null === t) return e;
                        const [n] = t;
                        return n.length >= s.length ? e.slice(s.length) : e
                    })).join("\n")
                }(e, t[3] || "");
            return {
                type: "code",
                raw: e,
                lang: t[2] ? t[2].trim().replace(this.rules.inline.anyPunctuation, "$1") : t[2],
                text: n
            }
        }
    }
    heading(e) {
        const t = this.rules.block.heading.exec(e);
        if (t) {
            let e = t[2].trim();
            if (/#$/.test(e)) {
                const t = R(e, "#");
                this.options.pedantic ? e = t.trim() : t && !/ $/.test(t) || (e = t.trim())
            }
            return {
                type: "heading",
                raw: t[0],
                depth: t[1].length,
                text: e,
                tokens: this.lexer.inline(e)
            }
        }
    }
    hr(e) {
        const t = this.rules.block.hr.exec(e);
        if (t) return {
            type: "hr",
            raw: R(t[0], "\n")
        }
    }
    blockquote(e) {
        const t = this.rules.block.blockquote.exec(e);
        if (t) {
            let e = R(t[0], "\n").split("\n"),
                n = "",
                s = "";
            const r = [];
            for (; e.length > 0;) {
                let t = !1;
                const i = [];
                let l;
                for (l = 0; l < e.length; l++)
                    if (/^ {0,3}>/.test(e[l])) i.push(e[l]), t = !0;
                    else {
                        if (t) break;
                        i.push(e[l])
                    }
                e = e.slice(l);
                const o = i.join("\n"),
                    a = o.replace(/\n {0,3}((?:=+|-+) *)(?=\n|$)/g, "\n    $1").replace(/^ {0,3}>[ \t]?/gm, "");
                n = n ? `${n}\n${o}` : o, s = s ? `${s}\n${a}` : a;
                const c = this.lexer.state.top;
                if (this.lexer.state.top = !0, this.lexer.blockTokens(a, r, !0), this.lexer.state.top = c, 0 === e.length) break;
                const h = r[r.length - 1];
                if ("code" === (null == h ? void 0 : h.type)) break;
                if ("blockquote" === (null == h ? void 0 : h.type)) {
                    const t = h,
                        i = t.raw + "\n" + e.join("\n"),
                        l = this.blockquote(i);
                    r[r.length - 1] = l, n = n.substring(0, n.length - t.raw.length) + l.raw, s = s.substring(0, s.length - t.text.length) + l.text;
                    break
                }
                if ("list" !== (null == h ? void 0 : h.type));
                else {
                    const t = h,
                        i = t.raw + "\n" + e.join("\n"),
                        l = this.list(i);
                    r[r.length - 1] = l, n = n.substring(0, n.length - h.raw.length) + l.raw, s = s.substring(0, s.length - t.raw.length) + l.raw, e = i.substring(r[r.length - 1].raw.length).split("\n")
                }
            }
            return {
                type: "blockquote",
                raw: n,
                tokens: r,
                text: s
            }
        }
    }
    list(e) {
        let t = this.rules.block.list.exec(e);
        if (t) {
            let n = t[1].trim();
            const s = n.length > 1,
                r = {
                    type: "list",
                    raw: "",
                    ordered: s,
                    start: s ? +n.slice(0, -1) : "",
                    loose: !1,
                    items: []
                };
            n = s ? `\\d{1,9}\\${n.slice(-1)}` : `\\${n}`, this.options.pedantic && (n = s ? n : "[*+-]");
            const i = new RegExp(`^( {0,3}${n})((?:[\t ][^\\n]*)?(?:\\n|$))`);
            let l = !1;
            for (; e;) {
                let n = !1,
                    s = "",
                    o = "";
                if (!(t = i.exec(e))) break;
                if (this.rules.block.hr.test(e)) break;
                s = t[0], e = e.substring(s.length);
                let a = t[2].split("\n", 1)[0].replace(/^\t+/, (e => " ".repeat(3 * e.length))),
                    c = e.split("\n", 1)[0],
                    h = !a.trim(),
                    p = 0;
                if (this.options.pedantic ? (p = 2, o = a.trimStart()) : h ? p = t[1].length + 1 : (p = t[2].search(/[^ ]/), p = p > 4 ? 1 : p, o = a.slice(p), p += t[1].length), h && /^ *$/.test(c) && (s += c + "\n", e = e.substring(c.length + 1), n = !0), !n) {
                    const t = new RegExp(`^ {0,${Math.min(3,p-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))`),
                        n = new RegExp(`^ {0,${Math.min(3,p-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),
                        r = new RegExp(`^ {0,${Math.min(3,p-1)}}(?:\`\`\`|~~~)`),
                        i = new RegExp(`^ {0,${Math.min(3,p-1)}}#`);
                    for (; e;) {
                        const l = e.split("\n", 1)[0];
                        if (c = l, this.options.pedantic && (c = c.replace(/^ {1,4}(?=( {4})*[^ ])/g, "  ")), r.test(c)) break;
                        if (i.test(c)) break;
                        if (t.test(c)) break;
                        if (n.test(e)) break;
                        if (c.search(/[^ ]/) >= p || !c.trim()) o += "\n" + c.slice(p);
                        else {
                            if (h) break;
                            if (a.search(/[^ ]/) >= 4) break;
                            if (r.test(a)) break;
                            if (i.test(a)) break;
                            if (n.test(a)) break;
                            o += "\n" + c
                        }
                        h || c.trim() || (h = !0), s += l + "\n", e = e.substring(l.length + 1), a = c.slice(p)
                    }
                }
                r.loose || (l ? r.loose = !0 : /\n *\n *$/.test(s) && (l = !0));
                let u, k = null;
                this.options.gfm && (k = /^\[[ xX]\] /.exec(o), k && (u = "[ ] " !== k[0], o = o.replace(/^\[[ xX]\] +/, ""))), r.items.push({
                    type: "list_item",
                    raw: s,
                    task: !!k,
                    checked: u,
                    loose: !1,
                    text: o,
                    tokens: []
                }), r.raw += s
            }
            r.items[r.items.length - 1].raw = r.items[r.items.length - 1].raw.trimEnd(), r.items[r.items.length - 1].text = r.items[r.items.length - 1].text.trimEnd(), r.raw = r.raw.trimEnd();
            for (let e = 0; e < r.items.length; e++)
                if (this.lexer.state.top = !1, r.items[e].tokens = this.lexer.blockTokens(r.items[e].text, []), !r.loose) {
                    const t = r.items[e].tokens.filter((e => "space" === e.type)),
                        n = t.length > 0 && t.some((e => /\n.*\n/.test(e.raw)));
                    r.loose = n
                }
            if (r.loose)
                for (let e = 0; e < r.items.length; e++) r.items[e].loose = !0;
            return r
        }
    }
    html(e) {
        const t = this.rules.block.html.exec(e);
        if (t) {
            return {
                type: "html",
                block: !0,
                raw: t[0],
                pre: "pre" === t[1] || "script" === t[1] || "style" === t[1],
                text: t[0]
            }
        }
    }
    def(e) {
        const t = this.rules.block.def.exec(e);
        if (t) {
            const e = t[1].toLowerCase().replace(/\s+/g, " "),
                n = t[2] ? t[2].replace(/^<(.*)>$/, "$1").replace(this.rules.inline.anyPunctuation, "$1") : "",
                s = t[3] ? t[3].substring(1, t[3].length - 1).replace(this.rules.inline.anyPunctuation, "$1") : t[3];
            return {
                type: "def",
                tag: e,
                raw: t[0],
                href: n,
                title: s
            }
        }
    }
    table(e) {
        const t = this.rules.block.table.exec(e);
        if (!t) return;
        if (!/[:|]/.test(t[2])) return;
        const n = z(t[1]),
            s = t[2].replace(/^\||\| *$/g, "").split("|"),
            r = t[3] && t[3].trim() ? t[3].replace(/\n[ \t]*$/, "").split("\n") : [],
            i = {
                type: "table",
                raw: t[0],
                header: [],
                align: [],
                rows: []
            };
        if (n.length === s.length) {
            for (const e of s) /^ *-+: *$/.test(e) ? i.align.push("right") : /^ *:-+: *$/.test(e) ? i.align.push("center") : /^ *:-+ *$/.test(e) ? i.align.push("left") : i.align.push(null);
            for (let e = 0; e < n.length; e++) i.header.push({
                text: n[e],
                tokens: this.lexer.inline(n[e]),
                header: !0,
                align: i.align[e]
            });
            for (const e of r) i.rows.push(z(e, i.header.length).map(((e, t) => ({
                text: e,
                tokens: this.lexer.inline(e),
                header: !1,
                align: i.align[t]
            }))));
            return i
        }
    }
    lheading(e) {
        const t = this.rules.block.lheading.exec(e);
        if (t) return {
            type: "heading",
            raw: t[0],
            depth: "=" === t[2].charAt(0) ? 1 : 2,
            text: t[1],
            tokens: this.lexer.inline(t[1])
        }
    }
    paragraph(e) {
        const t = this.rules.block.paragraph.exec(e);
        if (t) {
            const e = "\n" === t[1].charAt(t[1].length - 1) ? t[1].slice(0, -1) : t[1];
            return {
                type: "paragraph",
                raw: t[0],
                text: e,
                tokens: this.lexer.inline(e)
            }
        }
    }
    text(e) {
        const t = this.rules.block.text.exec(e);
        if (t) return {
            type: "text",
            raw: t[0],
            text: t[0],
            tokens: this.lexer.inline(t[0])
        }
    }
    escape(e) {
        const t = this.rules.inline.escape.exec(e);
        if (t) return {
            type: "escape",
            raw: t[0],
            text: x(t[1])
        }
    }
    tag(e) {
        const t = this.rules.inline.tag.exec(e);
        if (t) return !this.lexer.state.inLink && /^<a /i.test(t[0]) ? this.lexer.state.inLink = !0 : this.lexer.state.inLink && /^<\/a>/i.test(t[0]) && (this.lexer.state.inLink = !1), !this.lexer.state.inRawBlock && /^<(pre|code|kbd|script)(\s|>)/i.test(t[0]) ? this.lexer.state.inRawBlock = !0 : this.lexer.state.inRawBlock && /^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0]) && (this.lexer.state.inRawBlock = !1), {
            type: "html",
            raw: t[0],
            inLink: this.lexer.state.inLink,
            inRawBlock: this.lexer.state.inRawBlock,
            block: !1,
            text: t[0]
        }
    }
    link(e) {
        const t = this.rules.inline.link.exec(e);
        if (t) {
            const e = t[2].trim();
            if (!this.options.pedantic && /^</.test(e)) {
                if (!/>$/.test(e)) return;
                const t = R(e.slice(0, -1), "\\");
                if ((e.length - t.length) % 2 == 0) return
            } else {
                const e = function(e, t) {
                    if (-1 === e.indexOf(t[1])) return -1;
                    let n = 0;
                    for (let s = 0; s < e.length; s++)
                        if ("\\" === e[s]) s++;
                        else if (e[s] === t[0]) n++;
                    else if (e[s] === t[1] && (n--, n < 0)) return s;
                    return -1
                }(t[2], "()");
                if (e > -1) {
                    const n = (0 === t[0].indexOf("!") ? 5 : 4) + t[1].length + e;
                    t[2] = t[2].substring(0, e), t[0] = t[0].substring(0, n).trim(), t[3] = ""
                }
            }
            let n = t[2],
                s = "";
            if (this.options.pedantic) {
                const e = /^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);
                e && (n = e[1], s = e[3])
            } else s = t[3] ? t[3].slice(1, -1) : "";
            return n = n.trim(), /^</.test(n) && (n = this.options.pedantic && !/>$/.test(e) ? n.slice(1) : n.slice(1, -1)), T(t, {
                href: n ? n.replace(this.rules.inline.anyPunctuation, "$1") : n,
                title: s ? s.replace(this.rules.inline.anyPunctuation, "$1") : s
            }, t[0], this.lexer)
        }
    }
    reflink(e, t) {
        let n;
        if ((n = this.rules.inline.reflink.exec(e)) || (n = this.rules.inline.nolink.exec(e))) {
            const e = t[(n[2] || n[1]).replace(/\s+/g, " ").toLowerCase()];
            if (!e) {
                const e = n[0].charAt(0);
                return {
                    type: "text",
                    raw: e,
                    text: e
                }
            }
            return T(n, e, n[0], this.lexer)
        }
    }
    emStrong(e, t, n = "") {
        let s = this.rules.inline.emStrongLDelim.exec(e);
        if (!s) return;
        if (s[3] && n.match(/[\p{L}\p{N}]/u)) return;
        if (!(s[1] || s[2] || "") || !n || this.rules.inline.punctuation.exec(n)) {
            const n = [...s[0]].length - 1;
            let r, i, l = n,
                o = 0;
            const a = "*" === s[0][0] ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;
            for (a.lastIndex = 0, t = t.slice(-1 * e.length + n); null != (s = a.exec(t));) {
                if (r = s[1] || s[2] || s[3] || s[4] || s[5] || s[6], !r) continue;
                if (i = [...r].length, s[3] || s[4]) {
                    l += i;
                    continue
                }
                if ((s[5] || s[6]) && n % 3 && !((n + i) % 3)) {
                    o += i;
                    continue
                }
                if (l -= i, l > 0) continue;
                i = Math.min(i, i + l + o);
                const t = [...s[0]][0].length,
                    a = e.slice(0, n + s.index + t + i);
                if (Math.min(n, i) % 2) {
                    const e = a.slice(1, -1);
                    return {
                        type: "em",
                        raw: a,
                        text: e,
                        tokens: this.lexer.inlineTokens(e)
                    }
                }
                const c = a.slice(2, -2);
                return {
                    type: "strong",
                    raw: a,
                    text: c,
                    tokens: this.lexer.inlineTokens(c)
                }
            }
        }
    }
    codespan(e) {
        const t = this.rules.inline.code.exec(e);
        if (t) {
            let e = t[2].replace(/\n/g, " ");
            const n = /[^ ]/.test(e),
                s = /^ /.test(e) && / $/.test(e);
            return n && s && (e = e.substring(1, e.length - 1)), e = x(e, !0), {
                type: "codespan",
                raw: t[0],
                text: e
            }
        }
    }
    br(e) {
        const t = this.rules.inline.br.exec(e);
        if (t) return {
            type: "br",
            raw: t[0]
        }
    }
    del(e) {
        const t = this.rules.inline.del.exec(e);
        if (t) return {
            type: "del",
            raw: t[0],
            text: t[2],
            tokens: this.lexer.inlineTokens(t[2])
        }
    }
    autolink(e) {
        const t = this.rules.inline.autolink.exec(e);
        if (t) {
            let e, n;
            return "@" === t[2] ? (e = x(t[1]), n = "mailto:" + e) : (e = x(t[1]), n = e), {
                type: "link",
                raw: t[0],
                text: e,
                href: n,
                tokens: [{
                    type: "text",
                    raw: e,
                    text: e
                }]
            }
        }
    }
    url(e) {
        var t;
        let n;
        if (n = this.rules.inline.url.exec(e)) {
            let e, s;
            if ("@" === n[2]) e = x(n[0]), s = "mailto:" + e;
            else {
                let r;
                do {
                    r = n[0], n[0] = (null == (t = this.rules.inline._backpedal.exec(n[0])) ? void 0 : t[0]) ? ? ""
                } while (r !== n[0]);
                e = x(n[0]), s = "www." === n[1] ? "http://" + n[0] : n[0]
            }
            return {
                type: "link",
                raw: n[0],
                text: e,
                href: s,
                tokens: [{
                    type: "text",
                    raw: e,
                    text: e
                }]
            }
        }
    }
    inlineText(e) {
        const t = this.rules.inline.text.exec(e);
        if (t) {
            let e;
            return e = this.lexer.state.inRawBlock ? t[0] : x(t[0]), {
                type: "text",
                raw: t[0],
                text: e
            }
        }
    }
}
const A = /^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,
    S = /(?:[*+-]|\d{1,9}[.)])/,
    I = m(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g, S).replace(/blockCode/g, / {4}/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\n>]+>\n/).getRegex(),
    v = /^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,
    E = /(?!\s*\])(?:\\.|[^\[\]\\])+/,
    q = m(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label", E).replace("title", /(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),
    Z = m(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g, S).getRegex(),
    L = "address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",
    P = /<!--(?:-?>|[\s\S]*?(?:-->|$))/,
    C = m("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))", "i").replace("comment", P).replace("tag", L).replace("attribute", / +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),
    Q = m(v).replace("hr", A).replace("heading", " {0,3}#{1,6}(?:\\s|$)").replace("|lheading", "").replace("|table", "").replace("blockquote", " {0,3}>").replace("fences", " {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list", " {0,3}(?:[*+-]|1[.)]) ").replace("html", "</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag", L).getRegex(),
    B = {
        blockquote: m(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph", Q).getRegex(),
        code: /^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,
        def: q,
        fences: /^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,
        heading: /^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,
        hr: A,
        html: C,
        lheading: I,
        list: Z,
        newline: /^(?: *(?:\n|$))+/,
        paragraph: Q,
        table: $,
        text: /^[^\n]+/
    },
    O = m("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr", A).replace("heading", " {0,3}#{1,6}(?:\\s|$)").replace("blockquote", " {0,3}>").replace("code", " {4}[^\\n]").replace("fences", " {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list", " {0,3}(?:[*+-]|1[.)]) ").replace("html", "</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag", L).getRegex(),
    j = { ...B,
        table: O,
        paragraph: m(v).replace("hr", A).replace("heading", " {0,3}#{1,6}(?:\\s|$)").replace("|lheading", "").replace("table", O).replace("blockquote", " {0,3}>").replace("fences", " {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list", " {0,3}(?:[*+-]|1[.)]) ").replace("html", "</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag", L).getRegex()
    },
    D = { ...B,
        html: m("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment", P).replace(/tag/g, "(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),
        def: /^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,
        heading: /^(#{1,6})(.*)(?:\n+|$)/,
        fences: $,
        lheading: /^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,
        paragraph: m(v).replace("hr", A).replace("heading", " *#{1,6} *[^\n]").replace("lheading", I).replace("|table", "").replace("blockquote", " {0,3}>").replace("|fences", "").replace("|list", "").replace("|html", "").replace("|tag", "").getRegex()
    },
    M = /^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,
    H = /^( {2,}|\\)\n(?!\s*$)/,
    U = "\\p{P}\\p{S}",
    X = m(/^((?![*_])[\spunctuation])/, "u").replace(/punctuation/g, U).getRegex(),
    F = m(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/, "u").replace(/punct/g, U).getRegex(),
    N = m("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])", "gu").replace(/punct/g, U).getRegex(),
    W = m("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])", "gu").replace(/punct/g, U).getRegex(),
    G = m(/\\([punct])/, "gu").replace(/punct/g, U).getRegex(),
    J = m(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme", /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email", /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),
    K = m(P).replace("(?:--\x3e|$)", "--\x3e").getRegex(),
    V = m("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment", K).replace("attribute", /\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),
    Y = /(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,
    ee = m(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label", Y).replace("href", /<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title", /"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),
    te = m(/^!?\[(label)\]\[(ref)\]/).replace("label", Y).replace("ref", E).getRegex(),
    ne = m(/^!?\[(ref)\](?:\[\])?/).replace("ref", E).getRegex(),
    se = {
        _backpedal: $,
        anyPunctuation: G,
        autolink: J,
        blockSkip: /\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,
        br: H,
        code: /^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,
        del: $,
        emStrongLDelim: F,
        emStrongRDelimAst: N,
        emStrongRDelimUnd: W,
        escape: M,
        link: ee,
        nolink: ne,
        punctuation: X,
        reflink: te,
        reflinkSearch: m("reflink|nolink(?!\\()", "g").replace("reflink", te).replace("nolink", ne).getRegex(),
        tag: V,
        text: /^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,
        url: $
    },
    re = { ...se,
        link: m(/^!?\[(label)\]\((.*?)\)/).replace("label", Y).getRegex(),
        reflink: m(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label", Y).getRegex()
    },
    ie = { ...se,
        escape: m(M).replace("])", "~|])").getRegex(),
        url: m(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/, "i").replace("email", /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),
        _backpedal: /(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,
        del: /^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,
        text: /^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/
    },
    le = { ...ie,
        br: m(H).replace("{2,}", "*").getRegex(),
        text: m(ie.text).replace("\\b_", "\\b_| {2,}\\n").replace(/\{2,\}/g, "*").getRegex()
    },
    oe = {
        normal: B,
        gfm: j,
        pedantic: D
    },
    ae = {
        normal: se,
        gfm: ie,
        breaks: le,
        pedantic: re
    };
class ce {
    constructor(e) {
        l(this, "tokens"), l(this, "options"), l(this, "state"), l(this, "tokenizer"), l(this, "inlineQueue"), this.tokens = [], this.tokens.links = Object.create(null), this.options = e || c, this.options.tokenizer = this.options.tokenizer || new _, this.tokenizer = this.options.tokenizer, this.tokenizer.options = this.options, this.tokenizer.lexer = this, this.inlineQueue = [], this.state = {
            inLink: !1,
            inRawBlock: !1,
            top: !0
        };
        const t = {
            block: oe.normal,
            inline: ae.normal
        };
        this.options.pedantic ? (t.block = oe.pedantic, t.inline = ae.pedantic) : this.options.gfm && (t.block = oe.gfm, this.options.breaks ? t.inline = ae.breaks : t.inline = ae.gfm), this.tokenizer.rules = t
    }
    static get rules() {
        return {
            block: oe,
            inline: ae
        }
    }
    static lex(e, t) {
        return new ce(t).lex(e)
    }
    static lexInline(e, t) {
        return new ce(t).inlineTokens(e)
    }
    lex(e) {
        e = e.replace(/\r\n|\r/g, "\n"), this.blockTokens(e, this.tokens);
        for (let t = 0; t < this.inlineQueue.length; t++) {
            const e = this.inlineQueue[t];
            this.inlineTokens(e.src, e.tokens)
        }
        return this.inlineQueue = [], this.tokens
    }
    blockTokens(e, t = [], n = !1) {
        let s, r, i;
        for (e = this.options.pedantic ? e.replace(/\t/g, "    ").replace(/^ +$/gm, "") : e.replace(/^( *)(\t+)/gm, ((e, t, n) => t + "    ".repeat(n.length))); e;)
            if (!(this.options.extensions && this.options.extensions.block && this.options.extensions.block.some((n => !!(s = n.call({
                    lexer: this
                }, e, t)) && (e = e.substring(s.raw.length), t.push(s), !0)))))
                if (s = this.tokenizer.space(e)) e = e.substring(s.raw.length), 1 === s.raw.length && t.length > 0 ? t[t.length - 1].raw += "\n" : t.push(s);
                else if (s = this.tokenizer.code(e)) e = e.substring(s.raw.length), r = t[t.length - 1], !r || "paragraph" !== r.type && "text" !== r.type ? t.push(s) : (r.raw += "\n" + s.raw, r.text += "\n" + s.text, this.inlineQueue[this.inlineQueue.length - 1].src = r.text);
        else if (s = this.tokenizer.fences(e)) e = e.substring(s.raw.length), t.push(s);
        else if (s = this.tokenizer.heading(e)) e = e.substring(s.raw.length), t.push(s);
        else if (s = this.tokenizer.hr(e)) e = e.substring(s.raw.length), t.push(s);
        else if (s = this.tokenizer.blockquote(e)) e = e.substring(s.raw.length), t.push(s);
        else if (s = this.tokenizer.list(e)) e = e.substring(s.raw.length), t.push(s);
        else if (s = this.tokenizer.html(e)) e = e.substring(s.raw.length), t.push(s);
        else if (s = this.tokenizer.def(e)) e = e.substring(s.raw.length), r = t[t.length - 1], !r || "paragraph" !== r.type && "text" !== r.type ? this.tokens.links[s.tag] || (this.tokens.links[s.tag] = {
            href: s.href,
            title: s.title
        }) : (r.raw += "\n" + s.raw, r.text += "\n" + s.raw, this.inlineQueue[this.inlineQueue.length - 1].src = r.text);
        else if (s = this.tokenizer.table(e)) e = e.substring(s.raw.length), t.push(s);
        else if (s = this.tokenizer.lheading(e)) e = e.substring(s.raw.length), t.push(s);
        else {
            if (i = e, this.options.extensions && this.options.extensions.startBlock) {
                let t = 1 / 0;
                const n = e.slice(1);
                let s;
                this.options.extensions.startBlock.forEach((e => {
                    s = e.call({
                        lexer: this
                    }, n), "number" == typeof s && s >= 0 && (t = Math.min(t, s))
                })), t < 1 / 0 && t >= 0 && (i = e.substring(0, t + 1))
            }
            if (this.state.top && (s = this.tokenizer.paragraph(i))) r = t[t.length - 1], n && "paragraph" === (null == r ? void 0 : r.type) ? (r.raw += "\n" + s.raw, r.text += "\n" + s.text, this.inlineQueue.pop(), this.inlineQueue[this.inlineQueue.length - 1].src = r.text) : t.push(s), n = i.length !== e.length, e = e.substring(s.raw.length);
            else if (s = this.tokenizer.text(e)) e = e.substring(s.raw.length), r = t[t.length - 1], r && "text" === r.type ? (r.raw += "\n" + s.raw, r.text += "\n" + s.text, this.inlineQueue.pop(), this.inlineQueue[this.inlineQueue.length - 1].src = r.text) : t.push(s);
            else if (e) {
                const t = "Infinite loop on byte: " + e.charCodeAt(0);
                if (this.options.silent) break;
                throw new Error(t)
            }
        }
        return this.state.top = !0, t
    }
    inline(e, t = []) {
        return this.inlineQueue.push({
            src: e,
            tokens: t
        }), t
    }
    inlineTokens(e, t = []) {
        let n, s, r, i, l, o, a = e;
        if (this.tokens.links) {
            const e = Object.keys(this.tokens.links);
            if (e.length > 0)
                for (; null != (i = this.tokenizer.rules.inline.reflinkSearch.exec(a));) e.includes(i[0].slice(i[0].lastIndexOf("[") + 1, -1)) && (a = a.slice(0, i.index) + "[" + "a".repeat(i[0].length - 2) + "]" + a.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))
        }
        for (; null != (i = this.tokenizer.rules.inline.blockSkip.exec(a));) a = a.slice(0, i.index) + "[" + "a".repeat(i[0].length - 2) + "]" + a.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);
        for (; null != (i = this.tokenizer.rules.inline.anyPunctuation.exec(a));) a = a.slice(0, i.index) + "++" + a.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);
        for (; e;)
            if (l || (o = ""), l = !1, !(this.options.extensions && this.options.extensions.inline && this.options.extensions.inline.some((s => !!(n = s.call({
                    lexer: this
                }, e, t)) && (e = e.substring(n.raw.length), t.push(n), !0)))))
                if (n = this.tokenizer.escape(e)) e = e.substring(n.raw.length), t.push(n);
                else if (n = this.tokenizer.tag(e)) e = e.substring(n.raw.length), s = t[t.length - 1], s && "text" === n.type && "text" === s.type ? (s.raw += n.raw, s.text += n.text) : t.push(n);
        else if (n = this.tokenizer.link(e)) e = e.substring(n.raw.length), t.push(n);
        else if (n = this.tokenizer.reflink(e, this.tokens.links)) e = e.substring(n.raw.length), s = t[t.length - 1], s && "text" === n.type && "text" === s.type ? (s.raw += n.raw, s.text += n.text) : t.push(n);
        else if (n = this.tokenizer.emStrong(e, a, o)) e = e.substring(n.raw.length), t.push(n);
        else if (n = this.tokenizer.codespan(e)) e = e.substring(n.raw.length), t.push(n);
        else if (n = this.tokenizer.br(e)) e = e.substring(n.raw.length), t.push(n);
        else if (n = this.tokenizer.del(e)) e = e.substring(n.raw.length), t.push(n);
        else if (n = this.tokenizer.autolink(e)) e = e.substring(n.raw.length), t.push(n);
        else if (this.state.inLink || !(n = this.tokenizer.url(e))) {
            if (r = e, this.options.extensions && this.options.extensions.startInline) {
                let t = 1 / 0;
                const n = e.slice(1);
                let s;
                this.options.extensions.startInline.forEach((e => {
                    s = e.call({
                        lexer: this
                    }, n), "number" == typeof s && s >= 0 && (t = Math.min(t, s))
                })), t < 1 / 0 && t >= 0 && (r = e.substring(0, t + 1))
            }
            if (n = this.tokenizer.inlineText(r)) e = e.substring(n.raw.length), "_" !== n.raw.slice(-1) && (o = n.raw.slice(-1)), l = !0, s = t[t.length - 1], s && "text" === s.type ? (s.raw += n.raw, s.text += n.text) : t.push(n);
            else if (e) {
                const t = "Infinite loop on byte: " + e.charCodeAt(0);
                if (this.options.silent) break;
                throw new Error(t)
            }
        } else e = e.substring(n.raw.length), t.push(n);
        return t
    }
}
class he {
    constructor(e) {
        l(this, "options"), l(this, "parser"), this.options = e || c
    }
    space(e) {
        return ""
    }
    code({
        text: e,
        lang: t,
        escaped: n
    }) {
        var s;
        const r = null == (s = (t || "").match(/^\S*/)) ? void 0 : s[0],
            i = e.replace(/\n$/, "") + "\n";
        return r ? '<pre><code class="language-' + x(r) + '">' + (n ? i : x(i, !0)) + "</code></pre>\n" : "<pre><code>" + (n ? i : x(i, !0)) + "</code></pre>\n"
    }
    blockquote({
        tokens: e
    }) {
        return `<blockquote>\n${this.parser.parse(e)}</blockquote>\n`
    }
    html({
        text: e
    }) {
        return e
    }
    heading({
        tokens: e,
        depth: t
    }) {
        return `<h${t}>${this.parser.parseInline(e)}</h${t}>\n`
    }
    hr(e) {
        return "<hr>\n"
    }
    list(e) {
        const t = e.ordered,
            n = e.start;
        let s = "";
        for (let i = 0; i < e.items.length; i++) {
            const t = e.items[i];
            s += this.listitem(t)
        }
        const r = t ? "ol" : "ul";
        return "<" + r + (t && 1 !== n ? ' start="' + n + '"' : "") + ">\n" + s + "</" + r + ">\n"
    }
    listitem(e) {
        let t = "";
        if (e.task) {
            const n = this.checkbox({
                checked: !!e.checked
            });
            e.loose ? e.tokens.length > 0 && "paragraph" === e.tokens[0].type ? (e.tokens[0].text = n + " " + e.tokens[0].text, e.tokens[0].tokens && e.tokens[0].tokens.length > 0 && "text" === e.tokens[0].tokens[0].type && (e.tokens[0].tokens[0].text = n + " " + e.tokens[0].tokens[0].text)) : e.tokens.unshift({
                type: "text",
                raw: n + " ",
                text: n + " "
            }) : t += n + " "
        }
        return t += this.parser.parse(e.tokens, !!e.loose), `<li>${t}</li>\n`
    }
    checkbox({
        checked: e
    }) {
        return "<input " + (e ? 'checked="" ' : "") + 'disabled="" type="checkbox">'
    }
    paragraph({
        tokens: e
    }) {
        return `<p>${this.parser.parseInline(e)}</p>\n`
    }
    table(e) {
        let t = "",
            n = "";
        for (let r = 0; r < e.header.length; r++) n += this.tablecell(e.header[r]);
        t += this.tablerow({
            text: n
        });
        let s = "";
        for (let r = 0; r < e.rows.length; r++) {
            const t = e.rows[r];
            n = "";
            for (let e = 0; e < t.length; e++) n += this.tablecell(t[e]);
            s += this.tablerow({
                text: n
            })
        }
        return s && (s = `<tbody>${s}</tbody>`), "<table>\n<thead>\n" + t + "</thead>\n" + s + "</table>\n"
    }
    tablerow({
        text: e
    }) {
        return `<tr>\n${e}</tr>\n`
    }
    tablecell(e) {
        const t = this.parser.parseInline(e.tokens),
            n = e.header ? "th" : "td";
        return (e.align ? `<${n} align="${e.align}">` : `<${n}>`) + t + `</${n}>\n`
    }
    strong({
        tokens: e
    }) {
        return `<strong>${this.parser.parseInline(e)}</strong>`
    }
    em({
        tokens: e
    }) {
        return `<em>${this.parser.parseInline(e)}</em>`
    }
    codespan({
        text: e
    }) {
        return `<code>${e}</code>`
    }
    br(e) {
        return "<br>"
    }
    del({
        tokens: e
    }) {
        return `<del>${this.parser.parseInline(e)}</del>`
    }
    link({
        href: e,
        title: t,
        tokens: n
    }) {
        const s = this.parser.parseInline(n),
            r = y(e);
        if (null === r) return s;
        let i = '<a href="' + (e = r) + '"';
        return t && (i += ' title="' + t + '"'), i += ">" + s + "</a>", i
    }
    image({
        href: e,
        title: t,
        text: n
    }) {
        const s = y(e);
        if (null === s) return n;
        let r = `<img src="${e=s}" alt="${n}"`;
        return t && (r += ` title="${t}"`), r += ">", r
    }
    text(e) {
        return "tokens" in e && e.tokens ? this.parser.parseInline(e.tokens) : e.text
    }
}
class pe {
    strong({
        text: e
    }) {
        return e
    }
    em({
        text: e
    }) {
        return e
    }
    codespan({
        text: e
    }) {
        return e
    }
    del({
        text: e
    }) {
        return e
    }
    html({
        text: e
    }) {
        return e
    }
    text({
        text: e
    }) {
        return e
    }
    link({
        text: e
    }) {
        return "" + e
    }
    image({
        text: e
    }) {
        return "" + e
    }
    br() {
        return ""
    }
}
class ue {
    constructor(e) {
        l(this, "options"), l(this, "renderer"), l(this, "textRenderer"), this.options = e || c, this.options.renderer = this.options.renderer || new he, this.renderer = this.options.renderer, this.renderer.options = this.options, this.renderer.parser = this, this.textRenderer = new pe
    }
    static parse(e, t) {
        return new ue(t).parse(e)
    }
    static parseInline(e, t) {
        return new ue(t).parseInline(e)
    }
    parse(e, t = !0) {
        let n = "";
        for (let s = 0; s < e.length; s++) {
            const r = e[s];
            if (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[r.type]) {
                const e = r,
                    t = this.options.extensions.renderers[e.type].call({
                        parser: this
                    }, e);
                if (!1 !== t || !["space", "hr", "heading", "code", "table", "blockquote", "list", "html", "paragraph", "text"].includes(e.type)) {
                    n += t || "";
                    continue
                }
            }
            const i = r;
            switch (i.type) {
                case "space":
                    n += this.renderer.space(i);
                    continue;
                case "hr":
                    n += this.renderer.hr(i);
                    continue;
                case "heading":
                    n += this.renderer.heading(i);
                    continue;
                case "code":
                    n += this.renderer.code(i);
                    continue;
                case "table":
                    n += this.renderer.table(i);
                    continue;
                case "blockquote":
                    n += this.renderer.blockquote(i);
                    continue;
                case "list":
                    n += this.renderer.list(i);
                    continue;
                case "html":
                    n += this.renderer.html(i);
                    continue;
                case "paragraph":
                    n += this.renderer.paragraph(i);
                    continue;
                case "text":
                    {
                        let r = i,
                            l = this.renderer.text(r);
                        for (; s + 1 < e.length && "text" === e[s + 1].type;) r = e[++s],
                        l += "\n" + this.renderer.text(r);n += t ? this.renderer.paragraph({
                            type: "paragraph",
                            raw: l,
                            text: l,
                            tokens: [{
                                type: "text",
                                raw: l,
                                text: l
                            }]
                        }) : l;
                        continue
                    }
                default:
                    {
                        const e = 'Token with "' + i.type + '" type was not found.';
                        if (this.options.silent) return "";
                        throw new Error(e)
                    }
            }
        }
        return n
    }
    parseInline(e, t) {
        t = t || this.renderer;
        let n = "";
        for (let s = 0; s < e.length; s++) {
            const r = e[s];
            if (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[r.type]) {
                const e = this.options.extensions.renderers[r.type].call({
                    parser: this
                }, r);
                if (!1 !== e || !["escape", "html", "link", "image", "strong", "em", "codespan", "br", "del", "text"].includes(r.type)) {
                    n += e || "";
                    continue
                }
            }
            const i = r;
            switch (i.type) {
                case "escape":
                case "text":
                    n += t.text(i);
                    break;
                case "html":
                    n += t.html(i);
                    break;
                case "link":
                    n += t.link(i);
                    break;
                case "image":
                    n += t.image(i);
                    break;
                case "strong":
                    n += t.strong(i);
                    break;
                case "em":
                    n += t.em(i);
                    break;
                case "codespan":
                    n += t.codespan(i);
                    break;
                case "br":
                    n += t.br(i);
                    break;
                case "del":
                    n += t.del(i);
                    break;
                default:
                    {
                        const e = 'Token with "' + i.type + '" type was not found.';
                        if (this.options.silent) return "";
                        throw new Error(e)
                    }
            }
        }
        return n
    }
}
class ke {
    constructor(e) {
        l(this, "options"), this.options = e || c
    }
    preprocess(e) {
        return e
    }
    postprocess(e) {
        return e
    }
    processAllTokens(e) {
        return e
    }
}
l(ke, "passThroughHooks", new Set(["preprocess", "postprocess", "processAllTokens"]));
e = new WeakSet, t = function(e, t, n) {
    switch (t) {
        case "heading":
            return function(s) {
                return s.type && s.type === t ? e.call(this, n.parser.parseInline(s.tokens), s.depth, n.parser.parseInline(s.tokens, n.parser.textRenderer).replace(b, ((e, t) => "colon" === (t = t.toLowerCase()) ? ":" : "#" === t.charAt(0) ? "x" === t.charAt(1) ? String.fromCharCode(parseInt(t.substring(2), 16)) : String.fromCharCode(+t.substring(1)) : ""))) : e.apply(this, arguments)
            };
        case "code":
            return function(n) {
                return n.type && n.type === t ? e.call(this, n.text, n.lang, !!n.escaped) : e.apply(this, arguments)
            };
        case "table":
            return function(n) {
                if (!n.type || n.type !== t) return e.apply(this, arguments);
                let s = "",
                    r = "";
                for (let e = 0; e < n.header.length; e++) r += this.tablecell({
                    text: n.header[e].text,
                    tokens: n.header[e].tokens,
                    header: !0,
                    align: n.align[e]
                });
                s += this.tablerow({
                    text: r
                });
                let i = "";
                for (let e = 0; e < n.rows.length; e++) {
                    const t = n.rows[e];
                    r = "";
                    for (let e = 0; e < t.length; e++) r += this.tablecell({
                        text: t[e].text,
                        tokens: t[e].tokens,
                        header: !1,
                        align: n.align[e]
                    });
                    i += this.tablerow({
                        text: r
                    })
                }
                return e.call(this, s, i)
            };
        case "blockquote":
            return function(n) {
                if (!n.type || n.type !== t) return e.apply(this, arguments);
                const s = this.parser.parse(n.tokens);
                return e.call(this, s)
            };
        case "list":
            return function(n) {
                if (!n.type || n.type !== t) return e.apply(this, arguments);
                const s = n.ordered,
                    r = n.start,
                    i = n.loose;
                let l = "";
                for (let e = 0; e < n.items.length; e++) {
                    const t = n.items[e],
                        s = t.checked,
                        r = t.task;
                    let o = "";
                    if (t.task) {
                        const e = this.checkbox({
                            checked: !!s
                        });
                        i ? t.tokens.length > 0 && "paragraph" === t.tokens[0].type ? (t.tokens[0].text = e + " " + t.tokens[0].text, t.tokens[0].tokens && t.tokens[0].tokens.length > 0 && "text" === t.tokens[0].tokens[0].type && (t.tokens[0].tokens[0].text = e + " " + t.tokens[0].tokens[0].text)) : t.tokens.unshift({
                            type: "text",
                            text: e + " "
                        }) : o += e + " "
                    }
                    o += this.parser.parse(t.tokens, i), l += this.listitem({
                        type: "list_item",
                        raw: o,
                        text: o,
                        task: r,
                        checked: !!s,
                        loose: i,
                        tokens: t.tokens
                    })
                }
                return e.call(this, l, s, r)
            };
        case "html":
            return function(n) {
                return n.type && n.type === t ? e.call(this, n.text, n.block) : e.apply(this, arguments)
            };
        case "paragraph":
        case "strong":
        case "em":
        case "del":
            return function(n) {
                return n.type && n.type === t ? e.call(this, this.parser.parseInline(n.tokens)) : e.apply(this, arguments)
            };
        case "escape":
        case "codespan":
        case "text":
            return function(n) {
                return n.type && n.type === t ? e.call(this, n.text) : e.apply(this, arguments)
            };
        case "link":
            return function(n) {
                return n.type && n.type === t ? e.call(this, n.href, n.title, this.parser.parseInline(n.tokens)) : e.apply(this, arguments)
            };
        case "image":
            return function(n) {
                return n.type && n.type === t ? e.call(this, n.href, n.title, n.text) : e.apply(this, arguments)
            }
    }
    return e
}, n = function(t, n) {
    return (r, i) => {
        const l = { ...i
            },
            a = { ...this.defaults,
                ...l
            };
        !0 === this.defaults.async && !1 === l.async && (a.silent, a.async = !0);
        const c = o(this, e, s).call(this, !!a.silent, !!a.async);
        if (null == r) return c(new Error("marked(): input parameter is undefined or null"));
        if ("string" != typeof r) return c(new Error("marked(): input parameter is of type " + Object.prototype.toString.call(r) + ", string expected"));
        if (a.hooks && (a.hooks.options = a), a.async) return Promise.resolve(a.hooks ? a.hooks.preprocess(r) : r).then((e => t(e, a))).then((e => a.hooks ? a.hooks.processAllTokens(e) : e)).then((e => a.walkTokens ? Promise.all(this.walkTokens(e, a.walkTokens)).then((() => e)) : e)).then((e => n(e, a))).then((e => a.hooks ? a.hooks.postprocess(e) : e)).catch(c);
        try {
            a.hooks && (r = a.hooks.preprocess(r));
            let e = t(r, a);
            a.hooks && (e = a.hooks.processAllTokens(e)), a.walkTokens && this.walkTokens(e, a.walkTokens);
            let s = n(e, a);
            return a.hooks && (s = a.hooks.postprocess(s)), s
        } catch (h) {
            return c(h)
        }
    }
}, s = function(e, t) {
    return n => {
        if (n.message += "\nPlease report this to https://github.com/markedjs/marked.", e) {
            const e = "<p>An error occurred:</p><pre>" + x(n.message + "", !0) + "</pre>";
            return t ? Promise.resolve(e) : e
        }
        if (t) return Promise.reject(n);
        throw n
    }
};
const ge = new class {
    constructor(...t) {
        var s, r, a;
        s = this, (r = e).has(s) ? i("Cannot add the same private member more than once") : r instanceof WeakSet ? r.add(s) : r.set(s, a), l(this, "defaults", {
            async: !1,
            breaks: !1,
            extensions: null,
            gfm: !0,
            hooks: null,
            pedantic: !1,
            renderer: null,
            silent: !1,
            tokenizer: null,
            walkTokens: null
        }), l(this, "options", this.setOptions), l(this, "parse", o(this, e, n).call(this, ce.lex, ue.parse)), l(this, "parseInline", o(this, e, n).call(this, ce.lexInline, ue.parseInline)), l(this, "Parser", ue), l(this, "Renderer", he), l(this, "TextRenderer", pe), l(this, "Lexer", ce), l(this, "Tokenizer", _), l(this, "Hooks", ke), this.use(...t)
    }
    walkTokens(e, t) {
        var n, s;
        let r = [];
        for (const i of e) switch (r = r.concat(t.call(this, i)), i.type) {
            case "table":
                {
                    const e = i;
                    for (const n of e.header) r = r.concat(this.walkTokens(n.tokens, t));
                    for (const n of e.rows)
                        for (const e of n) r = r.concat(this.walkTokens(e.tokens, t));
                    break
                }
            case "list":
                {
                    const e = i;r = r.concat(this.walkTokens(e.items, t));
                    break
                }
            default:
                {
                    const e = i;
                    (null == (s = null == (n = this.defaults.extensions) ? void 0 : n.childTokens) ? void 0 : s[e.type]) ? this.defaults.extensions.childTokens[e.type].forEach((n => {
                        const s = e[n].flat(1 / 0);
                        r = r.concat(this.walkTokens(s, t))
                    })) : e.tokens && (r = r.concat(this.walkTokens(e.tokens, t)))
                }
        }
        return r
    }
    use(...n) {
        const s = this.defaults.extensions || {
            renderers: {},
            childTokens: {}
        };
        return n.forEach((n => {
            const r = { ...n
            };
            if (r.async = this.defaults.async || r.async || !1, n.extensions && (n.extensions.forEach((e => {
                    if (!e.name) throw new Error("extension name required");
                    if ("renderer" in e) {
                        const t = s.renderers[e.name];
                        s.renderers[e.name] = t ? function(...n) {
                            let s = e.renderer.apply(this, n);
                            return !1 === s && (s = t.apply(this, n)), s
                        } : e.renderer
                    }
                    if ("tokenizer" in e) {
                        if (!e.level || "block" !== e.level && "inline" !== e.level) throw new Error("extension level must be 'block' or 'inline'");
                        const t = s[e.level];
                        t ? t.unshift(e.tokenizer) : s[e.level] = [e.tokenizer], e.start && ("block" === e.level ? s.startBlock ? s.startBlock.push(e.start) : s.startBlock = [e.start] : "inline" === e.level && (s.startInline ? s.startInline.push(e.start) : s.startInline = [e.start]))
                    }
                    "childTokens" in e && e.childTokens && (s.childTokens[e.name] = e.childTokens)
                })), r.extensions = s), n.renderer) {
                const s = this.defaults.renderer || new he(this.defaults);
                for (const r in n.renderer) {
                    if (!(r in s)) throw new Error(`renderer '${r}' does not exist`);
                    if (["options", "parser"].includes(r)) continue;
                    const i = r;
                    let l = n.renderer[i];
                    n.useNewRenderer || (l = o(this, e, t).call(this, l, i, s));
                    const a = s[i];
                    s[i] = (...e) => {
                        let t = l.apply(s, e);
                        return !1 === t && (t = a.apply(s, e)), t || ""
                    }
                }
                r.renderer = s
            }
            if (n.tokenizer) {
                const e = this.defaults.tokenizer || new _(this.defaults);
                for (const t in n.tokenizer) {
                    if (!(t in e)) throw new Error(`tokenizer '${t}' does not exist`);
                    if (["options", "rules", "lexer"].includes(t)) continue;
                    const s = t,
                        r = n.tokenizer[s],
                        i = e[s];
                    e[s] = (...t) => {
                        let n = r.apply(e, t);
                        return !1 === n && (n = i.apply(e, t)), n
                    }
                }
                r.tokenizer = e
            }
            if (n.hooks) {
                const e = this.defaults.hooks || new ke;
                for (const t in n.hooks) {
                    if (!(t in e)) throw new Error(`hook '${t}' does not exist`);
                    if ("options" === t) continue;
                    const s = t,
                        r = n.hooks[s],
                        i = e[s];
                    ke.passThroughHooks.has(t) ? e[s] = t => {
                        if (this.defaults.async) return Promise.resolve(r.call(e, t)).then((t => i.call(e, t)));
                        const n = r.call(e, t);
                        return i.call(e, n)
                    } : e[s] = (...t) => {
                        let n = r.apply(e, t);
                        return !1 === n && (n = i.apply(e, t)), n
                    }
                }
                r.hooks = e
            }
            if (n.walkTokens) {
                const e = this.defaults.walkTokens,
                    t = n.walkTokens;
                r.walkTokens = function(n) {
                    let s = [];
                    return s.push(t.call(this, n)), e && (s = s.concat(e.call(this, n))), s
                }
            }
            this.defaults = { ...this.defaults,
                ...r
            }
        })), this
    }
    setOptions(e) {
        return this.defaults = { ...this.defaults,
            ...e
        }, this
    }
    lexer(e, t) {
        return ce.lex(e, t ? ? this.defaults)
    }
    parser(e, t) {
        return ue.parse(e, t ? ? this.defaults)
    }
};

function fe(e, t) {
    return ge.parse(e, t)
}
fe.options = fe.setOptions = function(e) {
    return ge.setOptions(e), fe.defaults = ge.defaults, h(fe.defaults), fe
}, fe.getDefaults = a, fe.defaults = c, fe.use = function(...e) {
    return ge.use(...e), fe.defaults = ge.defaults, h(fe.defaults), fe
}, fe.walkTokens = function(e, t) {
    return ge.walkTokens(e, t)
}, fe.parseInline = ge.parseInline, fe.Parser = ue, fe.parser = ue.parse, fe.Renderer = he, fe.TextRenderer = pe, fe.Lexer = ce, fe.lexer = ce.lex, fe.Tokenizer = _, fe.Hooks = ke, fe.parse = fe, fe.options, fe.setOptions, fe.use, fe.walkTokens, fe.parseInline, ue.parse, ce.lex;
export {
    fe as m
};