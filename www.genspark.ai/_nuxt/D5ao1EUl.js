import {
    d as l,
    b as e,
    o as r
} from "./Cf0SOiw0.js";
const t = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const i = {
    render: function(i, o) {
        return r(), l("svg", t, o[0] || (o[0] = [e("g", {
            "clip-path": "url(#clip0_739_13340)"
        }, [e("path", {
            "fill-rule": "evenodd",
            "clip-rule": "evenodd",
            d: "M20.0001 5C20.0001 4.44772 19.5524 4 19.0001 4C18.4478 4 18.0001 4.44772 18.0001 5V10C18.0001 11.6569 16.6569 13 15.0001 13H7.0006L8.83774 11.0593C9.21742 10.6582 9.21742 10.0079 8.83774 9.60687C8.45806 9.20579 7.84249 9.20579 7.46281 9.60687L4.30047 12.9475C3.54112 13.7496 3.54112 15.0502 4.30047 15.8523L7.46281 19.1929C7.84249 19.594 8.45806 19.594 8.83774 19.1929C9.21742 18.7919 9.21742 18.1416 8.83774 17.7405L6.24348 15H15.0001C17.7615 15 20.0001 12.7614 20.0001 10V5Z",
            fill: "currentColor"
        })], -1), e("defs", null, [e("clipPath", {
            id: "clip0_739_13340"
        }, [e("rect", {
            width: "24",
            height: "24",
            fill: "currentColor"
        })])], -1)]))
    }
};
export {
    i as E
};