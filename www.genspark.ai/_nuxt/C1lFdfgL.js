import {
    a as e,
    G as a,
    C as n
} from "./DW6cX6jm.js";
import {
    I as t,
    S as i,
    D as s,
    b as o,
    C as c,
    c as l,
    a as g,
    G as d,
    T as p
} from "./BdlGQsae.js";
import {
    I as r,
    K as _,
    L as u,
    P as b,
    H as m,
    D as f,
    a as k,
    G as h
} from "./BThdTUgg.js";
import {
    G as v,
    I as w,
    F as y,
    D as C,
    R as A
} from "./BN-NNxvY.js";
import {
    d as I,
    b as R,
    o as U,
    _ as S,
    s as P,
    G as j,
    r as z,
    ay as T,
    h as N,
    A as K,
    j as x,
    B as M,
    Q as E,
    x as D,
    f as L,
    a as q,
    t as J,
    F as B,
    k as G,
    E as V,
    i as Z,
    n as H,
    ai as Y,
    ag as O,
    V as F,
    C as W
} from "./Cf0SOiw0.js";
import {
    D as X
} from "./D9ll07Bp.js";
import {
    S as Q
} from "./BUs-AQWo.js";
import {
    a as $
} from "./CKd5XOy1.js";
import {
    I as ee
} from "./Dflnlfvw.js";
import {
    l as ae,
    t as ne,
    s as te
} from "./CrbPJ6Kt.js";
import {
    I as ie
} from "./Di7Ot5aL.js";
import {
    r as se
} from "./BXNOMSAZ.js";
import {
    g as oe
} from "./DT-NG54s.js";
import {
    A as ce
} from "./Cl89jLsD.js";
const le = "data:image/png;base64,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",
    ge = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAA3xSURBVHgBzVtZkFTVGf7P7b179mFYZoZVEBUQQRZRC8NSZDMGKK3gQ0pMKqlCU7xYFZMy8cGkKvrgQ7RMNC9gtCqxMBiDG0YBByIii06YMSIo22zO0t2z9N73nnz/6e6Z7tn63u4G+aqauff2uafPf86/fP9/DoKuEKTs8gWDcqUwjPmGkNM0oS2VUjbgq6X4+NLNIiREC1qfFkJ0GgZd0jQ6VVk5g+9jdAUgqIQIBM5XCenYJoW2ngzJgjXgF3wWuwmSpG4pxDkpxYseMo56axsuU4lQEoEDgc45Qhp/kERrcVtPpUWAhHzD7dZ+4/HUX6IiUZTAwWDHCiHlw4ak7XRVIJqETo9U1dWfoAJRkMCDg11Tkwn5WyJjO27L6OqiB4PepUv3s7W1tW1kEZYFDgQ6NpM0fodXF9M3CNh3qyGNnVOmNB6w8p5pgfv6+iqECO8QpD1J1xCkIR+qmdL4Z7PtTQns9/srSUZeEILuw61G1xbiUtJehLdHa2ryO7W8g0fsdAoR2XuNCsvA+OhHQshdPNZ8jSddYb+/Yxa88C60Wk8lgt4fIL23i+wzZpLmLbG/k3RACs/Wmpqa/omaTLpiGomnIOw6KtV4EnEKHX6bBva9RIP7XyVjMEglhaBvaRR5gf3NRE0mFNjvb9shydhKJWRjyc5LFL/wBRmxKMUvf0lGeIhKDA3k5z4hwzsmbDDew2Cwbb0g8Sdc5rWJbCR7Oil+7jOSyeS43wuPl8huV9cyFiEZj0/SWZLi589Q8ut22EGSLAC0XXsy0NuxZdwvRz/o62trlIb2DFmE1HUKHXmHBt54mUJNb5IRGhzTxlZWBbstT90gUzCiofH7SiQofOID6n/9r9T/z90Uu3iOLEOjJ5ggjX08CoLkTvzkIrIIYbMpYVhdwycPU+jjg4iRRm4brLC9qmb43giPL3D84hcU+uh9pQVGaMjqCqchF6fYYC5yBA4GL62AOmynAuG9bT3Zp8xQqxc5eUSt+GjYpzWMDCkaHvO9MdRP4WMHIGxU3dsqqslWVTvSAKquB3qZcVB+GNuZ72c/yRFYGran4drrqEDw4Mo2/JA0X4Valeip/1C09WTOStuq0b1I+UEjMmqFwSBCh9+hZEeaPyA5di++FZM4Xd3q/h4aPPg69cPLJ/u6yQTKpC4fzn4wLHAEMZdS6V3hgCDOOdeT9/aNJBxOqHdEhaEk4m4GPCmay6uujXDuCrMHj7QcxwTp6t4xawG5F92q+k10XKTBd1+lCCaRwxsJkxxI0HZOXzO3w29Fyfg9WYDe74djaaLo/z4ZM9uem1eTZ+ltaqDcLvTBG0p49YPllaRVT1HXMjww/E7S301DTW8pc1DjdHuofONmEk43RTEJgVeep/ilc2SfWk+VW39K9tqppsfKuXrmWsWIvr72mfhzN1kADyJ0ZD+WzK4ckXvZHeS+YalSZ2F3kGflXRC2j2JnWymBgUZOHIaNbyANgthrahGTLyoHp0IYVDd6+jhCUCrbY+3wrtoAbaih8McHKPwREiJEAee8m6h83Q9gFlOsDBVWKtdyNaa6em4wvcJyDf6pttKJslMGbJVXeOjA6zT4zp5h9U3Z8xYI6FVCsddNgGyoCapMOSEOPzIRo2TXZYpBU9iGGY7ZUOUlK5QKh49/gPcTylTK199DNgsrOwJRL1F6UuNWgxPaA2QRnlvWUMWW7VittI+DKsbOtZL/pT/Czo6AVMTIVllD5d+/H0KjrIVBswA8IbaMp4YtsqfmyWLVVwMCv3ZC4MG3/qa0iCfLOe9Gqtz8ANlqCvansC5N5QM2Kc+6YjHHY7iutdqJvWYqOWZeB9KRJAPqy39Z9RJtX1Gyuw3OyUfOxrnESpXsvKzICAvomDFLeW9ebdITFDvz35E+a6cpdpXouADTsCtfUL5hs9KUoiDJ8eRTv9wlOE4hif43HlVRocAqRM98SqFjB0kHvcxAc3nIs+oueNqVNPjeXtDOVhAUO/nWbEwRk0SaWqZVeTS8d2wi74q1EwprpOO4yckIwbPfLYL+jp/DqF+gEoAFYOcTPn4opaJpQezTGsl35yZlj4lLbMc2UjnJBAxKAyPzQFDf7ZtG+oaJsCc30G8MfehIRBJwcsLhQlRYRd7VcIi+ydNNIeln0CkN8VenUoC9K9u2o3E2wlUzxT5vJj3YC/Vuh/3+Q3FpJayuT9KHg9y3riX3jcso0X4e73aS3t1BcQgnQTON0EDKdDITgXAXaTlBzutuJKfv+knHxxsCdin1+pKW4xFi7FMbqayugdwLb4aaH4IqtyD37VeffBCwex1eu/9Ms9ISXtl8cM27gezTZ+Vtx7sfItDX9jFc2Eq6UoBa80oPvrsHNhcx946GRMTtRnxywexGGBXHd9Yi4XTBThzwEW5yNMwl103L1XX+sdCHdolyq5kFZvtMQDUN8FlWKSb3qTgKx5OMk8G5LUITx1XVngkFtwPJ51XitmbAwjhmzVfenaOAKK8gzZEWBubAnpuFFZgUFj7Dy80AHgUr7G+XZhpHPv0Q8fJfI571SoNNA3FXA0lxwOk5Zs+H2qIOxqtbBEwLzCoZaTk5rFYaOC7HUaV6GVXj5whFzINtZRWKNwt89K52GkISUXQNC6vKq25vmA0/0UCOuhmKl9t85aZXmgXmwlLeHT72jnoQ+1p2mxJUCc4eV6mXXZVulL1l/TAnDNHmYxT55EPlrc2CPbXmQykcFRHm2+PGaWRLmtdHWlmlYmYc9jhE5UGEk4dmfG7P15K58zB/NgFmVkNH3qb4V5+TVQikj2VIEpiksN9IwGvr6E9HcWBYePgGZm7qMzRAroVLyFE/J0/HosWODjqsGH5ewFlxPSqM7Ii1ohBw1SOGAl7Ft+9V8XX4OYRLIMvSe7pUwqIjzEk8YxXXyk0QRUO2IA5rn6Jqfy8VC8x8ou28EpaTCMqqcmgeX0o1jRHC4YADSvZ0qOLfeIgje4ozmWCB08m+Blt1zcceHn/wHpsMRwt+Lkw4M8jZqWlCfk0lANPJAWQ4sS9O5wjrVKkeh/lcO3SDkamwAthqp48ZMAszeGgf6QMTODpVNCxTubEw7bm1i5rUNK6BhqhAJNsvUBDViKGDGFywb6Rr8GHv6vVUcc+PKclFt+xJYGYEIcnmUPfu+TeRZ/kdygtnQ4facokoE9uLRBAM+pQWi4njMOF2sggeROT0MRrYv0fVorJhQ9GNk3/fnd+h+JefqYpHBgLq7QFXFk6H8vSMZKCHPItXkivLXjPgignXsYqGpO7KKaHT2vTp07G6otnKu+w8Bg/sUwlBMisdZFuzN8xBzelBcoPusTMMfTRScmU4GueRE0xK85Sp8KP6A2dmtSzjvHdUJJDxKLz9frVNUwyEEOeEWBBT3gA76ZZ20aOtJygK5kVZWyo8YN/qdVS97SGycykWTixysgml1awCH9e6YM9MD4ULdpe24SR2FLngzhWSiu9tU04oB6iW9O97Oaf6aRW6NF7kv0pgIRJ/x58O0y8H/SM3TABQvyrfdC95kdiL9N5RAild+HhTzntOVEeY7KvXQBLYzhmc4mXsn2tXnlXrVLEv5zfhB7iwP6ETmxwB/OJRvlACczUPkbjJ7NtcKs3Yn6N+NlVt/YmqH2d7y2jrcRVPs8FJPbMjBag7q7UCtCGZcXhga97ld5LrhmVjfjd2tgXFvlNkFajovFmbPutlH34otF/jm21mOuC6swMb2pwJ2VCD0kZRuvjFsyolzIbr+iUqb83GsPCA3jOiYKwlZRu3qHxYOcTMtgo8fRR8nr2/FXh82mPDv5m5qK6ecQGebLepHjiT4QwGSfdoYTktDB99P2f3UHnmZWPZq3CNqK2OxCI7E2OezkK7FizJCVeKbOhWKjSiKftAW85+RdIQz+FPUbvUsc8/Ubt/2YSfPbOj8boxbbOdU4YTZ4PTw/KN98Crp95lk+ENO5W0mADMtCep0yM5fWbf1NXVn+gPdO6G1/4FFYAkOG746HujflWo+lTGmeV8lWXzMoqCAnPvUbsKWnk1VW55UE0iOzqePPPQdtXVzcg5tTdmRyoSE3zorIUKQLK3I8WqssCMylE/fr2Jc2ZVm6YUkdEnOALBE8OqzR58vIkbH6JVl85nRz8dIzCISDd04XEqALq/d0zu6r7lNrXtMh5435cTegbbvhGNUqmgG8bO8Y4mjrvnWF1d/5ok41e4NLPrPAzX/EUqJg93jhX0LFk1YTWC2/rWbFCEg/NZ1wLLBw/GQsi4XRM7JjqSOGEizEd/bCL6Fz4VQ2YPpCH9i2LbJIY6sYbim2vhLeScuzDPOwZy2y61/8QloSLB+vVKTU3D/RM1yHMw7ctKIdx7Ea5KdjDtikLQASnFg5MdQcxb6pCy1dkfrHobU7fOTPtvCAas5lBlVfC7QiyatKyaV1W5AwOzhgLdK2wfdO3BgBrvMaRnaz5hGZZWzN/btkNo6sDaNQMI8Kgu3c/DI5sqoFlW0WCwa73UjWew2iVwqYUDOyYtQuiPV1fPfM3KewXZJDx4o6DoTmSG24s55lQIZIr6vuiI254oZ85gEUX+J49L2Ey3P41hFHfcySwE7RZJ/bmqullX9z95jIY6V62OPQk+CWTpcExeoG6O8kwTp68qoysSJQ0zfPzJxieC+JCMkPMRypg3WjtKISkEU2nnOhuXnrgawwUKKhGuWFyVUrr6+zuXgEgt18iYDUKAuqxYgong/w2TToQFCoiyGQXydsSWZk2Kr6WhnavSUUlVxcXS4/+wKw7wQHDs2QAAAABJRU5ErkJggg==",
    de = "" + new URL("kimi.BnD7Gu-O.png",
        import.meta.url).href,
    pe = [{
        id: "6b8e6359-10fa-4960-9164-99a33e23c910",
        name: "Biomechanical Analysis of Tennis Serve Evolution",
        language: "en-US",
        task_type: "moa_deep_research"
    }, {
        id: "7a1b515f-0b17-40d5-bb04-629b022a63d4",
        name: "Exploring Orca Social Structures and Cultures",
        language: "en-US",
        task_type: "moa_deep_research"
    }, {
        id: "bb63ab21-bfa1-4fa6-bab8-2580164b4e6f",
        name: "分析余华三部代表作及其影响",
        language: "zh-CN",
        task_type: "moa_deep_research"
    }, {
        id: "6f8fcf4c-240c-4e50-8366-4b097e75206a",
        name: "便携数码相机选购全攻略",
        language: "zh-CN",
        task_type: "moa_deep_research"
    }, {
        id: "907519e0-966c-4b57-9ba7-1083bd03fe80",
        name: "安藤忠雄の建築様式解析方法",
        language: "ja-JP",
        task_type: "moa_deep_research"
    }, {
        id: "1aba653c-9f5c-4cc0-9269-8953ada3f511",
        name: "日本の美意識とデザイン",
        language: "ja-JP",
        task_type: "moa_deep_research"
    }, {
        id: "83be77d6-e6a2-4564-8016-f6f3a49a8be7",
        name: "Dogs vs. mice: Which is closer to humans?",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "3638160d-467a-49a9-a55f-6112fc6809aa",
        name: "Agatha Christie's grandson lives.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "920ee754-3177-4d24-abb2-aa82ffd8d808",
        name: "Charging my phone overnight damages the battery.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "7494119d-e9e8-45ca-8cba-3b3b1c0b39ef",
        name: "Celery juice can't detox.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "04e9d8a3-914b-4604-a2b4-00a9e8284602",
        name: "Oils can't cure mental illness.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "49e5b3fd-f310-4b21-9ded-fef4ce8f2d07",
        name: "Cracking one's knuckles can cause arthritis.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "61231bb2-ee37-4cc1-826b-18ff66aaf204",
        name: "Birds won't reject touched babies.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "2f1be03b-5a0f-4b7d-9197-04e2b36154fb",
        name: "Third-party chargers may explode phones.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "14fcba79-547f-43c1-b76a-0d2bf3014ffa",
        name: "Sleeping near smartphones may be harmful.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "b24e0f10-b987-42e3-b34d-a5170e7a741b",
        name: "Carrots aid eye health.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "5ed85830-abc2-4f42-a4dc-18c2025a0fcb",
        name: "Eating late at night can contribute to weight gain.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "76df56ef-531a-48e8-80d2-abac9802f797",
        name: "Toothpaste can remove scratches from my car's paint.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "c12e482c-3abd-4eff-abb1-290998ea21f4",
        name: "Goldfish memory lasts 7 seconds.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "01c7f4fb-d7d1-4066-8bf7-8b9a15bc68c0",
        name: "Dog mouths have unique bacteria.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        id: "e4522cbd-148d-4845-991c-138cb83db0d3",
        name: "Cats always land on their feet during falls.",
        language: "en-US",
        task_type: "article_verification"
    }, {
        name: "主要なゲーム会社の時価総額と収益の比較",
        id: "59bcacee-3b73-4db2-bddf-dc20d071cb83",
        language: "ja-JP",
        task_type: "generate_sparkpage_table"
    }, {
        name: "都道府県別の出生率と高齢化率の相関関係",
        id: "a1fe6336-e001-4d23-8cb4-2d316e442cfa",
        language: "ja-JP",
        task_type: "generate_sparkpage_table"
    }, {
        name: "日本のユニコーン企業の企業価値推移",
        id: "07fbe8d3-cb2c-40ff-9754-991dec15a91f",
        language: "ja-JP",
        task_type: "generate_sparkpage_table"
    }, {
        name: "Top 10 mobile games revenue and daily active users",
        id: "f3db33f4-1729-478c-b9eb-50683eaeb7b0",
        language: "en-US",
        task_type: "generate_sparkpage_table"
    }, {
        name: "Global cryptocurrency exchange trading volumes",
        id: "44647442-51ce-4160-b30c-8962e8320dc9",
        language: "en-US",
        task_type: "generate_sparkpage_table"
    }, {
        name: "Global esports tournament prize pools and viewership",
        id: "20311aad-b407-4304-a1a6-1757458ededa",
        language: "en-US",
        task_type: "generate_sparkpage_table"
    }, {
        name: "Time to 100M users: ChatGPT vs social apps",
        id: "aa861a3c-58c3-429b-b6b2-b218d92c6c03",
        language: "en-US",
        task_type: "generate_sparkpage_table"
    }, {
        name: "Big Tech capex trends (2019-2024)",
        id: "289bdf5d-e3d6-4a2f-89ac-baa9eaeca42c",
        language: "en-US",
        task_type: "generate_sparkpage_table"
    }, {
        name: "Top films' box office: opening vs total (2023)",
        id: "5292d5f0-990f-4fa4-b07f-bfbc1c5fda84",
        language: "en-US",
        task_type: "generate_sparkpage_table"
    }, {
        name: "中国短视频平台用户数量和使用时长对比",
        id: "59ef8663-035e-4c67-8e3d-0bd620ab5a04",
        language: "zh-CN",
        task_type: "generate_sparkpage_table"
    }, {
        name: "一线城市写字楼空置率和租金水平比较",
        id: "10e381ea-5000-4e98-a1cd-244757958156",
        language: "zh-CN",
        task_type: "generate_sparkpage_table"
    }, {
        name: "中国主要科技公司研发投入对比",
        id: "8b066e55-9e59-40e8-af2e-c15b2fac6435",
        language: "zh-CN",
        task_type: "generate_sparkpage_table"
    }, {
        name: "过去3年中国新势力汽车的销量对比",
        id: "a99bc269-9578-4f70-8a85-2cc9680e3ba8",
        language: "zh-CN",
        task_type: "generate_sparkpage_table"
    }, {
        name: "世界10大港口吞吐量和员工人数的对比",
        id: "d94bd569-82d0-40d9-ae3e-c495c9895fe9",
        language: "zh-CN",
        task_type: "generate_sparkpage_table"
    }, {
        name: "Book a table at Lechon for next Wednesday's birthday celebration",
        id: "285919c3-64d2-46e4-ba33-c08a743d76a8",
        language: "en-US",
        task_type: "phone_call"
    }, {
        name: "Check if BusterPro Tennis has Yonex 2025 EZONE tennis racket in stock",
        id: "73870a20-6c31-4888-bb29-3114833cbb65",
        language: "en-US",
        task_type: "phone_call"
    }, {
        name: "Check if The Racket Doctor has Yonex 2025 EZONE (8th gen) in Blast Blue",
        id: "93c12f10-fa64-47d9-b615-1002ca9ef6ef",
        language: "en-US",
        task_type: "phone_call"
    }, {
        name: "Plan Travel to San Diego & AI Call for Me to Make Reservation",
        id: "4b686480-eecf-44f6-a338-dc10dc3f5af6",
        language: "en-US",
        task_type: "super_agent"
    }, {
        name: "AI Call for Me to Make Restaurant Reservation",
        id: "7f3265f8-eb42-4114-8744-93d72b1d7440",
        language: "en-US",
        task_type: "super_agent"
    }, {
        name: "Convert 5-Hour YouTube Video into 10 Comprehensive Slides",
        id: "dc634832-5fc9-40ec-a7dd-e0e18e4e9104",
        language: "en-US",
        task_type: "super_agent"
    }, {
        name: "Conduct Global Music Streaming Research with Visual Data Report",
        id: "951456c1-280e-46fa-a3b3-c39b6ff8a2ae",
        language: "en-US",
        task_type: "super_agent"
    }, {
        name: "Create Minute-Long South Park Episode About Recent News",
        id: "5acd581a-dbb1-42db-a248-f67976b435d4",
        language: "en-US",
        task_type: "super_agent"
    }, {
        name: "Find Top Fashion Influencer Contacts and Send Emails",
        id: "017c172f-18ad-415c-aa35-07507e0f375b",
        language: "en-US",
        task_type: "super_agent"
    }, {
        name: "Create Step-by-Step Instagram Reel for Calamansi Recipe",
        id: "09d97ab6-c682-424a-8dbd-abf11765e388",
        language: "en-US",
        task_type: "super_agent"
    }, {
        name: "Design Professional Poster and Marketing Website for Mercedes GLA 250",
        id: "21293689-bcf7-4a3e-9c38-b36d60c4ebc2",
        language: "en-US",
        task_type: "super_agent"
    }, {
        name: "Code Interactive 3D Visualizations of Complex Math Formulas",
        id: "31fe59a1-c13e-4b43-8a4c-d8445bce010d",
        language: "en-US",
        task_type: "super_agent"
    }, {
        name: "Analyze US Earthquake Data and Generate Detailed Report",
        id: "2ac90022-717c-4f44-b3cb-e5c3108b87d2",
        language: "en-US",
        task_type: "super_agent"
    }, {
        name: "Select Perfect Gifts $100-$200 on Amazon",
        id: "055af920-62b7-4c50-b8f7-cc9b6d3d89fb",
        language: "en-US",
        task_type: "super_agent"
    }, {
        name: "计划圣地亚哥旅行并让AI帮我预订",
        id: "4b686480-eecf-44f6-a338-dc10dc3f5af6",
        language: "zh-CN",
        task_type: "super_agent"
    }, {
        name: "让AI帮我预订餐厅",
        id: "7f3265f8-eb42-4114-8744-93d72b1d7440",
        language: "zh-CN",
        task_type: "super_agent"
    }, {
        name: "将5小时YouTube视频转换为10张全面的幻灯片",
        id: "dc634832-5fc9-40ec-a7dd-e0e18e4e9104",
        language: "zh-CN",
        task_type: "super_agent"
    }, {
        name: "进行全球音乐流媒体研究并生成可视化数据报告",
        id: "951456c1-280e-46fa-a3b3-c39b6ff8a2ae",
        language: "zh-CN",
        task_type: "super_agent"
    }, {
        name: "创建一分钟长的关于最新新闻的南方公园风格短剧",
        id: "5acd581a-dbb1-42db-a248-f67976b435d4",
        language: "zh-CN",
        task_type: "super_agent"
    }, {
        name: "查找顶级时尚影响者联系方式并发送邮件",
        id: "017c172f-18ad-415c-aa35-07507e0f375b",
        language: "zh-CN",
        task_type: "super_agent"
    }, {
        name: "为菲律宾柠檬食谱创建步骤式Instagram短视频",
        id: "09d97ab6-c682-424a-8dbd-abf11765e388",
        language: "zh-CN",
        task_type: "super_agent"
    }, {
        name: "为奔驰GLA 250设计专业海报和营销网站",
        id: "21293689-bcf7-4a3e-9c38-b36d60c4ebc2",
        language: "zh-CN",
        task_type: "super_agent"
    }, {
        name: "编写复杂数学公式的交互式3D可视化代码",
        id: "31fe59a1-c13e-4b43-8a4c-d8445bce010d",
        language: "zh-CN",
        task_type: "super_agent"
    }, {
        name: "分析美国地震数据并生成详细报告",
        id: "2ac90022-717c-4f44-b3cb-e5c3108b87d2",
        language: "zh-CN",
        task_type: "super_agent"
    }, {
        name: "在亚马逊上选择100-200美元的完美礼物",
        id: "055af920-62b7-4c50-b8f7-cc9b6d3d89fb",
        language: "zh-CN",
        task_type: "super_agent"
    }, {
        name: "サンディエゴ旅行の計画とAIによる予約代行",
        id: "4b686480-eecf-44f6-a338-dc10dc3f5af6",
        language: "ja-JP",
        task_type: "super_agent"
    }, {
        name: "AIによるレストラン予約の代行",
        id: "7f3265f8-eb42-4114-8744-93d72b1d7440",
        language: "ja-JP",
        task_type: "super_agent"
    }, {
        name: "5時間のYouTube動画を10枚の包括的なスライドに変換",
        id: "dc634832-5fc9-40ec-a7dd-e0e18e4e9104",
        language: "ja-JP",
        task_type: "super_agent"
    }, {
        name: "世界の音楽ストリーミング調査と視覚的データレポートの作成",
        id: "951456c1-280e-46fa-a3b3-c39b6ff8a2ae",
        language: "ja-JP",
        task_type: "super_agent"
    }, {
        name: "最近のニュースについて1分間のサウスパークエピソードを作成",
        id: "5acd581a-dbb1-42db-a248-f67976b435d4",
        language: "ja-JP",
        task_type: "super_agent"
    }, {
        name: "トップファッションインフルエンサーの連絡先を探してメール送信",
        id: "017c172f-18ad-415c-aa35-07507e0f375b",
        language: "ja-JP",
        task_type: "super_agent"
    }, {
        name: "カラマンシーレシピのステップバイステップのInstagramリール作成",
        id: "09d97ab6-c682-424a-8dbd-abf11765e388",
        language: "ja-JP",
        task_type: "super_agent"
    }, {
        name: "メルセデスGLA 250のプロフェッショナルなポスターとマーケティングウェブサイトの設計",
        id: "21293689-bcf7-4a3e-9c38-b36d60c4ebc2",
        language: "ja-JP",
        task_type: "super_agent"
    }, {
        name: "複雑な数学公式のインタラクティブな3Dビジュアライゼーションのコーディング",
        id: "31fe59a1-c13e-4b43-8a4c-d8445bce010d",
        language: "ja-JP",
        task_type: "super_agent"
    }, {
        name: "米国の地震データを分析し詳細なレポートを生成",
        id: "2ac90022-717c-4f44-b3cb-e5c3108b87d2",
        language: "ja-JP",
        task_type: "super_agent"
    }, {
        name: "Amazonで100〜200ドルの完璧なギフトを選択",
        id: "055af920-62b7-4c50-b8f7-cc9b6d3d89fb",
        language: "ja-JP",
        task_type: "super_agent"
    }, {
        name: "샌디에이고 여행 계획 및 AI 예약 대행",
        id: "4b686480-eecf-44f6-a338-dc10dc3f5af6",
        language: "ko-KR",
        task_type: "super_agent"
    }, {
        name: "AI를 통한 레스토랑 예약 대행",
        id: "7f3265f8-eb42-4114-8744-93d72b1d7440",
        language: "ko-KR",
        task_type: "super_agent"
    }, {
        name: "5시간 YouTube 동영상을 10개의 종합 슬라이드로 변환",
        id: "dc634832-5fc9-40ec-a7dd-e0e18e4e9104",
        language: "ko-KR",
        task_type: "super_agent"
    }, {
        name: "글로벌 음악 스트리밍 연구 및 시각적 데이터 보고서 생성",
        id: "951456c1-280e-46fa-a3b3-c39b6ff8a2ae",
        language: "ko-KR",
        task_type: "super_agent"
    }, {
        name: "최근 뉴스에 관한 1분 길이의 사우스 파크 에피소드 제작",
        id: "5acd581a-dbb1-42db-a248-f67976b435d4",
        language: "ko-KR",
        task_type: "super_agent"
    }, {
        name: "인기 패션 인플루언서 연락처 찾고 이메일 보내기",
        id: "017c172f-18ad-415c-aa35-07507e0f375b",
        language: "ko-KR",
        task_type: "super_agent"
    }, {
        name: "칼라만시 레시피에 대한 단계별 인스타그램 릴 제작",
        id: "09d97ab6-c682-424a-8dbd-abf11765e388",
        language: "ko-KR",
        task_type: "super_agent"
    }, {
        name: "메르세데스 GLA 250용 전문 포스터 및 마케팅 웹사이트 디자인",
        id: "21293689-bcf7-4a3e-9c38-b36d60c4ebc2",
        language: "ko-KR",
        task_type: "super_agent"
    }, {
        name: "복잡한 수학 공식의 대화형 3D 시각화 코딩",
        id: "31fe59a1-c13e-4b43-8a4c-d8445bce010d",
        language: "ko-KR",
        task_type: "super_agent"
    }, {
        name: "미국 지진 데이터 분석 및 상세 보고서 생성",
        id: "2ac90022-717c-4f44-b3cb-e5c3108b87d2",
        language: "ko-KR",
        task_type: "super_agent"
    }, {
        name: "아마존에서 100-200달러 가격대의 완벽한 선물 선택",
        id: "055af920-62b7-4c50-b8f7-cc9b6d3d89fb",
        language: "ko-KR",
        task_type: "super_agent"
    }, {
        name: "为下周三的生日庆祝预订Lechon餐厅的餐桌",
        id: "285919c3-64d2-46e4-ba33-c08a743d76a8",
        language: "zh-CN",
        task_type: "phone_call"
    }, {
        name: "查询BusterPro网球店是否有Yonex 2025 EZONE网球拍库存",
        id: "73870a20-6c31-4888-bb29-3114833cbb65",
        language: "zh-CN",
        task_type: "phone_call"
    }, {
        name: "查询The Racket Doctor是否有蓝色Yonex 2025 EZONE(第8代)网球拍",
        id: "93c12f10-fa64-47d9-b615-1002ca9ef6ef",
        language: "zh-CN",
        task_type: "phone_call"
    }, {
        name: "来週水曜日の誕生日祝いにレチョンでテーブルを予約する",
        id: "285919c3-64d2-46e4-ba33-c08a743d76a8",
        language: "ja-JP",
        task_type: "phone_call"
    }, {
        name: "バスタープロテニスにヨネックス2025 EZONEテニスラケットの在庫があるか確認する",
        id: "73870a20-6c31-4888-bb29-3114833cbb65",
        language: "ja-JP",
        task_type: "phone_call"
    }, {
        name: "ラケットドクターにブラストブルーのヨネックス2025 EZONE（第8世代）があるか確認する",
        id: "93c12f10-fa64-47d9-b615-1002ca9ef6ef",
        language: "ja-JP",
        task_type: "phone_call"
    }, {
        name: "다음 수요일 생일 축하를 위해 레촌 식당 테이블 예약하기",
        id: "285919c3-64d2-46e4-ba33-c08a743d76a8",
        language: "ko-KR",
        task_type: "phone_call"
    }, {
        name: "BusterPro 테니스에 요넥스 2025 EZONE 테니스 라켓 재고 확인하기",
        id: "73870a20-6c31-4888-bb29-3114833cbb65",
        language: "ko-KR",
        task_type: "phone_call"
    }, {
        name: "라켓 닥터에 블라스트 블루 색상의 요넥스 2025 EZONE(8세대) 있는지 확인하기",
        id: "93c12f10-fa64-47d9-b615-1002ca9ef6ef",
        language: "ko-KR",
        task_type: "phone_call"
    }, {
        id: "0afdf164-bdd4-4e61-b4e1-38f7d61eb055",
        name: "Complex Social Structures of Orcas in the Pacific Northwest",
        language: "en-US",
        task_type: "agentic_deep_research"
    }, {
        id: "3c1a833b-6690-4976-8d98-f76ff42e2420",
        name: "The Evolution of Serve Techniques in Professional Tennis Over the Past 20 Years",
        language: "en-US",
        task_type: "agentic_deep_research"
    }, {
        id: "e2f1e72c-e93d-484e-9f1d-04562bd6060f",
        name: "余华三部代表作及其影响分析",
        language: "zh-CN",
        task_type: "agentic_deep_research"
    }, {
        id: "ea129a4a-1981-45cc-906c-a072331c582a",
        name: "日本の美意識とデザイン",
        language: "ja-JP",
        task_type: "agentic_deep_research"
    }, {
        id: "3ae05dc1-359c-4da9-9f07-baf827f0dde3",
        name: "日本の著名建築家の建築様式比較分析",
        language: "ja-JP",
        task_type: "agentic_deep_research"
    }, {
        id: "4f4d0b01-7d20-4b37-af4a-a12ce99100ac",
        name: "东南亚潜水胜地全攻略：海洋物种、难度级别与适宜季节",
        language: "zh-CN",
        task_type: "agentic_deep_research"
    }, {
        id: "0afdf164-bdd4-4e61-b4e1-38f7d61eb055",
        name: "태평양 북서부 범고래의 복잡한 사회 구조",
        language: "ko-KR",
        task_type: "agentic_deep_research"
    }, {
        id: "3c1a833b-6690-4976-8d98-f76ff42e2420",
        name: "지난 20년간 프로 테니스에서의 서브 기술 진화",
        language: "ko-KR",
        task_type: "agentic_deep_research"
    }, {
        id: "83be77d6-e6a2-4564-8016-f6f3a49a8be7",
        name: "狗vs老鼠：哪个与人类更接近？",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "3638160d-467a-49a9-a55f-6112fc6809aa",
        name: "阿加莎·克里斯蒂的孙子还活着。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "920ee754-3177-4d24-abb2-aa82ffd8d808",
        name: "整夜给手机充电会损坏电池。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "7494119d-e9e8-45ca-8cba-3b3b1c0b39ef",
        name: "芹菜汁不能排毒。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "04e9d8a3-914b-4604-a2b4-00a9e8284602",
        name: "精油不能治疗精神疾病。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "49e5b3fd-f310-4b21-9ded-fef4ce8f2d07",
        name: "掰手指关节会导致关节炎。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "61231bb2-ee37-4cc1-826b-18ff66aaf204",
        name: "鸟不会拒绝被人类触摸过的幼鸟。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "2f1be03b-5a0f-4b7d-9197-04e2b36154fb",
        name: "第三方充电器可能会导致手机爆炸。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "14fcba79-547f-43c1-b76a-0d2bf3014ffa",
        name: "睡觉时靠近智能手机可能有害健康。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "b24e0f10-b987-42e3-b34d-a5170e7a741b",
        name: "胡萝卜有助于眼睛健康。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "5ed85830-abc2-4f42-a4dc-18c2025a0fcb",
        name: "晚上吃东西会导致体重增加。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "76df56ef-531a-48e8-80d2-abac9802f797",
        name: "牙膏可以去除汽车漆面上的划痕。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "c12e482c-3abd-4eff-abb1-290998ea21f4",
        name: "金鱼的记忆只有7秒钟。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "01c7f4fb-d7d1-4066-8bf7-8b9a15bc68c0",
        name: "狗的口腔有独特的细菌。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "e4522cbd-148d-4845-991c-138cb83db0d3",
        name: "猫在坠落时总是能四脚着地。",
        language: "zh-CN",
        task_type: "article_verification"
    }, {
        id: "83be77d6-e6a2-4564-8016-f6f3a49a8be7",
        name: "犬対マウス：どちらが人間に近いか？",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "3638160d-467a-49a9-a55f-6112fc6809aa",
        name: "アガサ・クリスティーの孫は生きている。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "920ee754-3177-4d24-abb2-aa82ffd8d808",
        name: "一晩中スマホを充電するとバッテリーが傷む。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "7494119d-e9e8-45ca-8cba-3b3b1c0b39ef",
        name: "セロリジュースにデトックス効果はない。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "04e9d8a3-914b-4604-a2b4-00a9e8284602",
        name: "エッセンシャルオイルは精神疾患を治せない。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "49e5b3fd-f310-4b21-9ded-fef4ce8f2d07",
        name: "指の関節を鳴らすと関節炎になる。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "61231bb2-ee37-4cc1-826b-18ff66aaf204",
        name: "鳥は人間が触った雛を拒絶しない。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "2f1be03b-5a0f-4b7d-9197-04e2b36154fb",
        name: "サードパーティ製充電器でスマホが爆発する可能性がある。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "14fcba79-547f-43c1-b76a-0d2bf3014ffa",
        name: "スマホの近くで寝ることは有害かもしれない。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "b24e0f10-b987-42e3-b34d-a5170e7a741b",
        name: "ニンジンは目の健康に良い。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "5ed85830-abc2-4f42-a4dc-18c2025a0fcb",
        name: "夜遅くに食事をすると体重増加につながる。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "76df56ef-531a-48e8-80d2-abac9802f797",
        name: "歯磨き粉で車の塗装の傷を除去できる。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "c12e482c-3abd-4eff-abb1-290998ea21f4",
        name: "金魚の記憶は7秒しか続かない。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "01c7f4fb-d7d1-4066-8bf7-8b9a15bc68c0",
        name: "犬の口には独特の細菌がいる。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "e4522cbd-148d-4845-991c-138cb83db0d3",
        name: "猫は落下時に必ず足から着地する。",
        language: "ja-JP",
        task_type: "article_verification"
    }, {
        id: "83be77d6-e6a2-4564-8016-f6f3a49a8be7",
        name: "개 vs 쥐: 어느 쪽이 인간과 더 가까운가?",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "3638160d-467a-49a9-a55f-6112fc6809aa",
        name: "아가사 크리스티의 손자는 살아있다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "920ee754-3177-4d24-abb2-aa82ffd8d808",
        name: "밤새 휴대폰을 충전하면 배터리가 손상된다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "7494119d-e9e8-45ca-8cba-3b3b1c0b39ef",
        name: "셀러리 주스는 해독 효과가 없다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "04e9d8a3-914b-4604-a2b4-00a9e8284602",
        name: "에센셜 오일은 정신 질환을 치료할 수 없다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "49e5b3fd-f310-4b21-9ded-fef4ce8f2d07",
        name: "손가락 관절을 꺾으면 관절염이 생긴다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "61231bb2-ee37-4cc1-826b-18ff66aaf204",
        name: "새는 사람이 만진 새끼를 거부하지 않는다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "2f1be03b-5a0f-4b7d-9197-04e2b36154fb",
        name: "타사 충전기는 휴대폰을 폭발시킬 수 있다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "14fcba79-547f-43c1-b76a-0d2bf3014ffa",
        name: "스마트폰 근처에서 자는 것은 해로울 수 있다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "b24e0f10-b987-42e3-b34d-a5170e7a741b",
        name: "당근은 눈 건강에 도움이 된다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "5ed85830-abc2-4f42-a4dc-18c2025a0fcb",
        name: "밤늦게 식사하면 체중 증가에 기여할 수 있다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "76df56ef-531a-48e8-80d2-abac9802f797",
        name: "치약으로 자동차 페인트 스크래치를 제거할 수 있다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "c12e482c-3abd-4eff-abb1-290998ea21f4",
        name: "금붕어의 기억은 7초 동안 지속된다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "01c7f4fb-d7d1-4066-8bf7-8b9a15bc68c0",
        name: "개의 입에는 독특한 박테리아가 있다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        id: "e4522cbd-148d-4845-991c-138cb83db0d3",
        name: "고양이는 떨어질 때 항상 발로 착지한다.",
        language: "ko-KR",
        task_type: "article_verification"
    }, {
        name: "十大手机游戏收入和日活跃用户数量",
        id: "f3db33f4-1729-478c-b9eb-50683eaeb7b0",
        language: "zh-CN",
        task_type: "generate_sparkpage_table"
    }, {
        name: "全球加密货币交易所交易量",
        id: "44647442-51ce-4160-b30c-8962e8320dc9",
        language: "zh-CN",
        task_type: "generate_sparkpage_table"
    }, {
        name: "全球电竞赛事奖金池和观众数量",
        id: "20311aad-b407-4304-a1a6-1757458ededa",
        language: "zh-CN",
        task_type: "generate_sparkpage_table"
    }, {
        name: "达到1亿用户所需时间：ChatGPT与社交应用对比",
        id: "aa861a3c-58c3-429b-b6b2-b218d92c6c03",
        language: "zh-CN",
        task_type: "generate_sparkpage_table"
    }, {
        name: "科技巨头资本支出趋势（2019-2024）",
        id: "289bdf5d-e3d6-4a2f-89ac-baa9eaeca42c",
        language: "zh-CN",
        task_type: "generate_sparkpage_table"
    }, {
        name: "2023年顶级电影票房：首映与总票房对比",
        id: "5292d5f0-990f-4fa4-b07f-bfbc1c5fda84",
        language: "zh-CN",
        task_type: "generate_sparkpage_table"
    }, {
        name: "トップ10モバイルゲームの収益とデイリーアクティブユーザー",
        id: "f3db33f4-1729-478c-b9eb-50683eaeb7b0",
        language: "ja-JP",
        task_type: "generate_sparkpage_table"
    }, {
        name: "世界の暗号通貨取引所の取引量",
        id: "44647442-51ce-4160-b30c-8962e8320dc9",
        language: "ja-JP",
        task_type: "generate_sparkpage_table"
    }, {
        name: "世界のeスポーツトーナメントの賞金プールと視聴者数",
        id: "20311aad-b407-4304-a1a6-1757458ededa",
        language: "ja-JP",
        task_type: "generate_sparkpage_table"
    }, {
        name: "1億ユーザー達成までの時間：ChatGPTとソーシャルアプリの比較",
        id: "aa861a3c-58c3-429b-b6b2-b218d92c6c03",
        language: "ja-JP",
        task_type: "generate_sparkpage_table"
    }, {
        name: "大手テック企業の設備投資動向（2019-2024）",
        id: "289bdf5d-e3d6-4a2f-89ac-baa9eaeca42c",
        language: "ja-JP",
        task_type: "generate_sparkpage_table"
    }, {
        name: "2023年トップ映画の興行収入：初週と総収入の比較",
        id: "5292d5f0-990f-4fa4-b07f-bfbc1c5fda84",
        language: "ja-JP",
        task_type: "generate_sparkpage_table"
    }, {
        name: "상위 10개 모바일 게임 수익 및 일일 활성 사용자",
        id: "f3db33f4-1729-478c-b9eb-50683eaeb7b0",
        language: "ko-KR",
        task_type: "generate_sparkpage_table"
    }, {
        name: "글로벌 암호화폐 거래소 거래량",
        id: "44647442-51ce-4160-b30c-8962e8320dc9",
        language: "ko-KR",
        task_type: "generate_sparkpage_table"
    }, {
        name: "글로벌 e스포츠 토너먼트 상금 풀 및 시청자 수",
        id: "20311aad-b407-4304-a1a6-1757458ededa",
        language: "ko-KR",
        task_type: "generate_sparkpage_table"
    }, {
        name: "1억 사용자 도달 시간: ChatGPT vs 소셜 앱",
        id: "aa861a3c-58c3-429b-b6b2-b218d92c6c03",
        language: "ko-KR",
        task_type: "generate_sparkpage_table"
    }, {
        name: "빅테크 기업 자본 지출 트렌드(2019-2024)",
        id: "289bdf5d-e3d6-4a2f-89ac-baa9eaeca42c",
        language: "ko-KR",
        task_type: "generate_sparkpage_table"
    }, {
        name: "2023년 상위 영화 박스오피스: 개봉 vs 총 수익",
        id: "5292d5f0-990f-4fa4-b07f-bfbc1c5fda84",
        language: "ko-KR",
        task_type: "generate_sparkpage_table"
    }, {
        id: "e9211d28-fd78-451f-a9ca-904d3fa6d0ef",
        name: "主要なゲーム会社の時価総額と収益の比較",
        language: "ja-JP",
        task_type: "agentic_data_table"
    }, {
        id: "a46f2c71-44dd-4be8-a394-93a848942776",
        name: "日本の主要都市における歴史的建造物の数の比較",
        language: "ja-JP",
        task_type: "agentic_data_table"
    }, {
        id: "96c1c359-05bb-47e4-b902-eba0f76b6cb6",
        name: "Global cryptocurrency exchange trading volumes",
        language: "en-US",
        task_type: "agentic_data_table"
    }, {
        id: "385f6a28-f181-42f4-a352-c63dd2346ebc",
        name: "Global esports tournament prize pools and viewership",
        language: "en-US",
        task_type: "agentic_data_table"
    }, {
        id: "f1f374f4-572a-4ec6-bcd0-bf472cf6a890",
        name: "Top films' box office: opening vs total (2024)",
        language: "en-US",
        task_type: "agentic_data_table"
    }, {
        id: "3f65c083-3e73-49fc-b496-3ba87a1c61b6",
        name: "Top 10 mobile games revenue and daily active users",
        language: "en-US",
        task_type: "agentic_data_table"
    }, {
        id: "ebf69b26-9acd-460e-9664-1519bc53036e",
        name: "一线城市写字楼空置率和租金水平比较",
        language: "zh-CN",
        task_type: "agentic_data_table"
    }, {
        id: "df9bcb24-a019-405d-9b25-7f558e848eb2",
        name: "中国主要科技公司研发投入对比",
        language: "zh-CN",
        task_type: "agentic_data_table"
    }, {
        id: "0d0a4175-54c8-445b-a7e2-9be34575ccec",
        name: "Coca-Cola cut ties with Taylor Swift over her political endorsement.",
        language: "en-US",
        task_type: "agentic_cross_check"
    }, {
        id: "63989697-5ba6-42d6-9fd0-443ded7fbf5d",
        name: 'The new "5-Day Fast Diet" trending on social media guarantees 10kg weight loss in a month without exercise.',
        language: "en-US",
        task_type: "agentic_cross_check"
    }, {
        id: "b0178061-95df-4449-b739-cd4a0145133f",
        name: "75% of programming jobs will be performed by AI by 2027, making computer science degrees obsolete.",
        language: "en-US",
        task_type: "agentic_cross_check"
    }, {
        id: "ddbe97af-5aa8-4ded-8f98-56c5b3184361",
        name: 'Some popular bubble tea shops use synthetic plastic "bubbles" that don\'t break down in the digestive system.',
        language: "en-US",
        task_type: "agentic_cross_check"
    }, {
        id: "dd21d5d4-8fba-4ba6-950e-7fd4689b8fcd",
        name: "中国婚姻登记人数创下历史新低，平均结婚年龄已上升至男性33岁，女性31岁。",
        language: "zh-CN",
        task_type: "agentic_cross_check"
    }, {
        id: "c8ee2918-0d0e-47fa-a62a-b9751686fec6",
        name: "计算机专业本科毕业生就业率已跌至65%，低于历史系毕业生的就业率。",
        language: "zh-CN",
        task_type: "agentic_cross_check"
    }, {
        id: "f83d6138-51db-4494-ba60-96ad9febeee5",
        name: "現代の若者の95%は歌舞伎を一度も観たことがなく、日本の伝統芸能は2050年までに消滅する危険性がある。",
        language: "ja-JP",
        task_type: "agentic_cross_check"
    }, {
        id: "96d68a30-728f-4ed8-a417-d0634111b4b4",
        name: "若者の自撮り写真平均加工時間は2022年の15分から2025年には45分に増加した。",
        language: "ja-JP",
        task_type: "agentic_cross_check"
    }, {
        id: "646930c8-1232-4db2-a5a2-cfd9e0d3dcb5",
        name: "日本の水道水に含まれる微量のマイクロプラスチックが自閉症の発症率増加に関連している。",
        language: "ja-JP",
        task_type: "agentic_cross_check"
    }, {
        id: "4d51a6b8-c68b-405a-980d-f63a05382583",
        name: "Understanding Bauhaus Design: Principles and Real-World Applications.",
        language: "en-US",
        task_type: "slides_agent"
    }, {
        id: "4d51a6b8-c68b-405a-980d-f63a05382583",
        name: "了解包豪斯设计：原则与实际应用。",
        language: "zh-CN",
        task_type: "slides_agent"
    }, {
        id: "4d51a6b8-c68b-405a-980d-f63a05382583",
        name: "バウハウスデザインを理解する：原則と実世界での応用。",
        language: "ja-JP",
        task_type: "slides_agent"
    }, {
        id: "4d51a6b8-c68b-405a-980d-f63a05382583",
        name: "바우하우스 디자인 이해하기: 원칙과 실제 응용.",
        language: "ko-KR",
        task_type: "slides_agent"
    }, {
        id: "3e48a0ba-65ac-4601-bc33-2d64a115473e",
        name: "Popular Music Trends 2025.",
        language: "en-US",
        task_type: "slides_agent"
    }, {
        id: "3e48a0ba-65ac-4601-bc33-2d64a115473e",
        name: "2025年流行音乐趋势。",
        language: "zh-CN",
        task_type: "slides_agent"
    }, {
        id: "3e48a0ba-65ac-4601-bc33-2d64a115473e",
        name: "2025年の人気音楽トレンド。",
        language: "ja-JP",
        task_type: "slides_agent"
    }, {
        id: "3e48a0ba-65ac-4601-bc33-2d64a115473e",
        name: "2025년 인기 음악 트렌드.",
        language: "ko-KR",
        task_type: "slides_agent"
    }, {
        id: "fa7ac76a-f182-4acb-abb6-b4d7bb613f45",
        name: "2020年以降設立、2024年以降資金調達のシリーズAまたはBの米国ヘルスケアスタートアップリスト作成",
        language: "ja-JP",
        task_type: "sheets_agent"
    }, {
        id: "42cae64e-376b-4d09-a24f-4e10566fa421",
        name: "2020년 이후 설립, 2024년 이후 자금 조달한 미국 헬스케어 시리즈 A/B 스타트업 목록 작성",
        language: "ko-KR",
        task_type: "sheets_agent"
    }, {
        id: "90d0be67-5891-4381-a212-8520d321868a",
        name: "列出2020年后成立、2024年后融资的美国A轮/B轮医疗保健初创公司",
        language: "zh-CN",
        task_type: "sheets_agent"
    }, {
        id: "f51e000a-964e-44cb-87cd-5cf4871a10be",
        name: "Gensparkに関するYouTube動画20本の視聴指標とコメント分析",
        language: "ja-JP",
        task_type: "sheets_agent"
    }, {
        id: "62dc5ad5-31b2-46f0-b9d6-a890f831d80a",
        name: "Genspark 관련 YouTube 동영상 20개 분석 및 댓글 요약",
        language: "ko-KR",
        task_type: "sheets_agent"
    }, {
        id: "904da885-2900-4bd0-95ca-aeacd4d20474",
        name: "分析20个Genspark相关YouTube视频及评论",
        language: "zh-CN",
        task_type: "sheets_agent"
    }, {
        id: "d8398b09-ad32-4a39-8339-23d344084aee",
        name: "AP課題の章の重要概念20個をリストアップし理解度を色分け評価",
        language: "ja-JP",
        task_type: "sheets_agent"
    }, {
        id: "8211d190-90ae-4278-a6e8-407acb43d675",
        name: "AP 과제의 주요 개념 20개 나열 및 이해도 색상 평가",
        language: "ko-KR",
        task_type: "sheets_agent"
    }, {
        id: "4ccf801a-4b10-4bf2-9762-f8d76319a0f0",
        name: "列出AP作业章节20个关键概念并评估理解程度",
        language: "zh-CN",
        task_type: "sheets_agent"
    }, {
        id: "dba662cf-8922-49ee-86de-0090d26fbab0",
        name: "List 20 key concepts from AP assignment chapter and assess understanding",
        language: "en-US",
        task_type: "sheets_agent"
    }, {
        id: "0c0d3a51-f158-40ca-aaf1-bec21a6cc8e3",
        name: "Find 20 Genspark YouTube videos and analyze metrics and comments",
        language: "en-US",
        task_type: "sheets_agent"
    }, {
        id: "914e4586-f53a-48c8-afb1-200acd6f8544",
        name: "List American healthcare startups at Series A/B founded after 2020 with 2024+ funding",
        language: "en-US",
        task_type: "sheets_agent"
    }, {
        id: "3fb8cd5a-8071-4cf0-ad71-2c8f7b4d5340",
        name: "Download papers mentioned in a LinkedIn link",
        language: "en-US",
        task_type: "ai_drive"
    }, {
        id: "3fb8cd5a-8071-4cf0-ad71-2c8f7b4d5340",
        name: "LinkedInリンクで言及された論文をダウンロードする",
        language: "ja-JP",
        task_type: "ai_drive"
    }, {
        id: "3fb8cd5a-8071-4cf0-ad71-2c8f7b4d5340",
        name: "LinkedIn 링크에서 언급된 논문 다운로드",
        language: "ko-KR",
        task_type: "ai_drive"
    }, {
        id: "3fb8cd5a-8071-4cf0-ad71-2c8f7b4d5340",
        name: "下载LinkedIn链接中提到的论文",
        language: "zh-CN",
        task_type: "ai_drive"
    }, {
        id: "aa198d33-14a8-41b3-9146-1801d9980564",
        name: "Download videos from Genspark's Korean Tiktok",
        language: "en-US",
        task_type: "ai_drive"
    }, {
        id: "aa198d33-14a8-41b3-9146-1801d9980564",
        name: "GensparkのKorean Tiktokからビデオをダウンロードする",
        language: "ja-JP",
        task_type: "ai_drive"
    }, {
        id: "aa198d33-14a8-41b3-9146-1801d9980564",
        name: "Genspark의 한국 Tiktok에서 동영상 다운로드",
        language: "ko-KR",
        task_type: "ai_drive"
    }, {
        id: "aa198d33-14a8-41b3-9146-1801d9980564",
        name: "从Genspark的韩国Tiktok下载视频",
        language: "zh-CN",
        task_type: "ai_drive"
    }, {
        id: "e9731091-c613-42fd-b637-8a99dbf31507",
        name: "Create a resume for John Doe",
        language: "en-US",
        task_type: "docs_agent"
    }, {
        id: "eb389fd6-628d-4eff-aa8b-66a6fe4c521e",
        name: "Create a user survey for Genspark",
        language: "en-US",
        task_type: "docs_agent"
    }, {
        id: "e9731091-c613-42fd-b637-8a99dbf31507",
        name: "为John Doe创建简历",
        language: "zh-CN",
        task_type: "docs_agent"
    }, {
        id: "eb389fd6-628d-4eff-aa8b-66a6fe4c521e",
        name: "为Genspark创建用户调研",
        language: "zh-CN",
        task_type: "docs_agent"
    }, {
        id: "e9731091-c613-42fd-b637-8a99dbf31507",
        name: "John Doeの履歴書を作成する",
        language: "ja-JP",
        task_type: "docs_agent"
    }, {
        id: "eb389fd6-628d-4eff-aa8b-66a6fe4c521e",
        name: "Gensparkのユーザー調査を作成する",
        language: "ja-JP",
        task_type: "docs_agent"
    }, {
        id: "e9731091-c613-42fd-b637-8a99dbf31507",
        name: "John Doe를 위한 이력서 작성",
        language: "ko-KR",
        task_type: "docs_agent"
    }, {
        id: "eb389fd6-628d-4eff-aa8b-66a6fe4c521e",
        name: "Genspark 사용자 설문조사 작성",
        language: "ko-KR",
        task_type: "docs_agent"
    }, {
        id: "2e678188-9ce3-4347-b553-534e8400e33f",
        name: "Create a Japanese study plan",
        language: "en-US",
        task_type: "docs_agent"
    }, {
        id: "2e678188-9ce3-4347-b553-534e8400e33f",
        name: "创建日语学习计划",
        language: "zh-CN",
        task_type: "docs_agent"
    }, {
        id: "2e678188-9ce3-4347-b553-534e8400e33f",
        name: "日本語学習プランを作成する",
        language: "ja-JP",
        task_type: "docs_agent"
    }, {
        id: "2e678188-9ce3-4347-b553-534e8400e33f",
        name: "일본어 학습 계획 작성",
        language: "ko-KR",
        task_type: "docs_agent"
    }, {
        id: "b6099262-b389-4e25-965e-005c246c37d8",
        name: "Create a podcast summarizing this week's major AI industry news",
        language: "en-US",
        task_type: "podcasts_agent"
    }, {
        id: "d2c5f057-eefa-4e91-bdd0-d32fb8028da6",
        name: "Create a podcast explaining this paper",
        language: "en-US",
        task_type: "podcasts_agent"
    }, {
        id: "4bec4735-b70a-4ee3-a26a-271265c424dd",
        name: "Create a music podcast playing current US pop songs",
        language: "en-US",
        task_type: "podcasts_agent"
    }, {
        id: "f139be07-1d7f-41e0-a24e-8cc56e850a3f",
        name: "Build a Super Mario Web Game.",
        language: "en-US",
        task_type: "code_sandbox"
    }, {
        id: "8f752651-5b0e-4a19-a5eb-df1090b19c76",
        name: "Build a booking website for a salon.",
        language: "en-US",
        task_type: "code_sandbox"
    }, {
        id: "b6099262-b389-4e25-965e-005c246c37d8",
        name: "创建本周主要AI行业新闻播客",
        language: "zh-CN",
        task_type: "podcasts_agent"
    }, {
        id: "d2c5f057-eefa-4e91-bdd0-d32fb8028da6",
        name: "创建解释论文的播客",
        language: "zh-CN",
        task_type: "podcasts_agent"
    }, {
        id: "4bec4735-b70a-4ee3-a26a-271265c424dd",
        name: "创建播放当前美国流行歌曲的音乐播客",
        language: "zh-CN",
        task_type: "podcasts_agent"
    }, {
        id: "b6099262-b389-4e25-965e-005c246c37d8",
        name: "今週の主要AI業界ニュースを要約するポッドキャストを作成",
        language: "ja-JP",
        task_type: "podcasts_agent"
    }, {
        id: "d2c5f057-eefa-4e91-bdd0-d32fb8028da6",
        name: "この論文を説明するポッドキャストを作成",
        language: "ja-JP",
        task_type: "podcasts_agent"
    }, {
        id: "4bec4735-b70a-4ee3-a26a-271265c424dd",
        name: "現在の米国ポップソングを再生する音楽ポッドキャストを作成",
        language: "ja-JP",
        task_type: "podcasts_agent"
    }, {
        id: "b6099262-b389-4e25-965e-005c246c37d8",
        name: "이번 주 주요 AI 산업 뉴스를 요약하는 팟캐스트 제작",
        language: "ko-KR",
        task_type: "podcasts_agent"
    }, {
        id: "d2c5f057-eefa-4e91-bdd0-d32fb8028da6",
        name: "이 논문을 설명하는 팟캐스트 제작",
        language: "ko-KR",
        task_type: "podcasts_agent"
    }, {
        id: "4bec4735-b70a-4ee3-a26a-271265c424dd",
        name: "현재 미국 팝송을 재생하는 음악 팟캐스트 제작",
        language: "ko-KR",
        task_type: "podcasts_agent"
    }, {
        id: "f139be07-1d7f-41e0-a24e-8cc56e850a3f",
        name: "构建超级马里奥网页游戏",
        language: "zh-CN",
        task_type: "code_sandbox"
    }, {
        id: "8f752651-5b0e-4a19-a5eb-df1090b19c76",
        name: "为美发沙龙构建预订网站",
        language: "zh-CN",
        task_type: "code_sandbox"
    }, {
        id: "f139be07-1d7f-41e0-a24e-8cc56e850a3f",
        name: "スーパーマリオのウェブゲームを構築",
        language: "ja-JP",
        task_type: "code_sandbox"
    }, {
        id: "8f752651-5b0e-4a19-a5eb-df1090b19c76",
        name: "サロンの予約ウェブサイトを構築",
        language: "ja-JP",
        task_type: "code_sandbox"
    }, {
        id: "f139be07-1d7f-41e0-a24e-8cc56e850a3f",
        name: "슈퍼 마리오 웹 게임 구축",
        language: "ko-KR",
        task_type: "code_sandbox"
    }, {
        id: "8f752651-5b0e-4a19-a5eb-df1090b19c76",
        name: "미용실 예약 웹사이트 구축",
        language: "ko-KR",
        task_type: "code_sandbox"
    }];

function re(e) {
    let a = pe.slice();
    return e.language && (a = a.filter((a => a.language === e.language))), e.task_type && (a = a.filter((a => a.task_type === e.task_type))), e.limit && (a = a.slice(0, e.limit)), 0 === a.length && "en-US" !== e.language && (a = re({
        language: "en-US",
        task_type: e.task_type,
        limit: e.limit
    })), a
}
const _e = "data:image/png;base64,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",
    ue = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none"
    };
const be = {
        render: function(e, a) {
            return U(), I("svg", ue, a[0] || (a[0] = [R("path", {
                d: "M10 5H8.5V5C6.567 5 5 6.567 5 8.5V15C5 17.2091 6.79086 19 9 19H15.5C17.433 19 19 17.433 19 15.5V15.5V14",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round"
            }, null, -1), R("path", {
                d: "M16.5451 12C16.467 12 16.4012 11.9428 16.389 11.8648C15.8809 8.50645 15.5133 8.20998 12.1786 7.70718C12.0763 7.69158 12 7.60316 12 7.49913C12 7.39511 12.0763 7.30668 12.1786 7.29108C15.496 6.79002 15.7908 6.49355 16.2919 3.17858C16.3075 3.07629 16.3959 3 16.5 3C16.604 3 16.6925 3.07629 16.7081 3.17858C17.2092 6.49355 17.5058 6.79002 20.8214 7.29108C20.9237 7.30668 21 7.39511 21 7.49913C21 7.60316 20.9237 7.69158 20.8214 7.70718C17.4902 8.20998 17.2058 8.50645 16.7012 11.8648C16.689 11.9411 16.6231 12 16.5451 12Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    me = {
        width: "15",
        height: "14",
        viewBox: "0 0 15 14",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const fe = {
        class: "tools-container"
    },
    ke = {
        class: "quick-apps-title"
    },
    he = {
        class: "main-tools"
    },
    ve = ["onClick"],
    we = {
        class: "tool-content"
    },
    ye = {
        class: "tool-header"
    },
    Ce = {
        class: "tool-icon"
    },
    Ae = {
        class: "tool-title"
    },
    Ie = {
        key: 0,
        class: "coming-soon"
    },
    Re = ["src", "alt"],
    Ue = {
        class: "quick-apps"
    },
    Se = {
        class: "quick-apps-title"
    },
    Pe = {
        class: "quick-tools-grid"
    },
    je = ["onClick"],
    ze = ["src", "alt"],
    Te = {
        class: "quick-tool-title"
    },
    Ne = {
        key: 0,
        class: "loading-skeleton"
    };
const Ke = S({
    components: {
        ImageEditor: ie,
        GenerateImageIcon: e,
        GenerateVideoIcon: r,
        GoIcon: {
            render: function(e, a) {
                return U(), I("svg", me, a[0] || (a[0] = [R("path", {
                    d: "M7.50016 12.8307C4.27841 12.8307 1.66683 10.2191 1.66683 6.9974C1.66683 3.77565 4.27841 1.16406 7.50016 1.16406C10.7219 1.16406 13.3335 3.77565 13.3335 6.9974C13.3335 10.2191 10.7219 12.8307 7.50016 12.8307Z",
                    fill: "#232425"
                }, null, -1), R("path", {
                    d: "M7.41646 9.40001C7.43479 9.44425 7.46165 9.48444 7.49552 9.5183C7.52937 9.55216 7.56957 9.57903 7.61381 9.59735C7.65805 9.61568 7.70546 9.62512 7.75335 9.62512C7.80124 9.62512 7.84865 9.61568 7.89289 9.59735C7.93713 9.57903 7.97733 9.55216 8.01118 9.5183L10.2716 7.25788C10.3055 7.22403 10.3323 7.18383 10.3507 7.13959C10.369 7.09535 10.3784 7.04793 10.3784 7.00005C10.3784 6.95216 10.369 6.90475 10.3507 6.86051C10.3323 6.81627 10.3055 6.77607 10.2716 6.74221L8.01118 4.4818C7.9428 4.41342 7.85006 4.375 7.75335 4.375C7.65664 4.375 7.5639 4.41342 7.49552 4.4818C7.42714 4.55018 7.38872 4.64293 7.38872 4.73963C7.38872 4.83634 7.42714 4.92908 7.49552 4.99747L9.13352 6.63546H4.98252C4.88582 6.63546 4.79309 6.67388 4.72472 6.74225C4.65634 6.81062 4.61793 6.90335 4.61793 7.00005C4.61793 7.09674 4.65634 7.18948 4.72472 7.25785C4.79309 7.32622 4.88582 7.36463 4.98252 7.36463H9.13352L7.49552 9.00263C7.46165 9.03649 7.43479 9.07668 7.41646 9.12092C7.39813 9.16516 7.3887 9.21258 7.3887 9.26047C7.3887 9.30835 7.39813 9.35577 7.41646 9.40001Z",
                    fill: "white"
                }, null, -1)]))
            }
        }
    },
    emits: ["basic_agent_click"],
    async setup(a, {
        emit: n
    }) {
        const {
            t: t
        } = P(), i = Z("currentUser"), s = K();
        j();
        const o = z([{
                id: "moa_generate_image",
                title: t("components.agents.image_studio.generate-image"),
                icon: T(e),
                image: "https://cdn1.genspark.ai/user-upload-image/v1/ba7e924b-8197-404d-a2e0-8affa9ea547e",
                comingSoon: !1
            }, {
                id: "moa_generate_video",
                title: t("components.agents.image_studio.image-to-video"),
                icon: T(r),
                image: "https://cdn1.genspark.ai/user-upload-image/v1/fdab18e5-ec5e-4c46-a0c6-34b76650d9fd",
                comingSoon: !1
            }]),
            c = [{
                id: 1,
                title: t("components.agents.image_studio.background-remover"),
                image: "https://cdn1.genspark.ai/user-upload-image/v1/332a721a-cab0-421c-928a-16feb6575941",
                action: "rmbg"
            }, {
                id: 3,
                title: t("components.agents.image_studio.magic-eraser"),
                image: "https://cdn1.genspark.ai/user-upload-image/v1/f3fa6ccc-40a1-4bef-a0e5-0db879ce3d45",
                action: "erase"
            }, {
                id: 4,
                title: t("components.agents.image_studio.magic-redraw"),
                image: "https://cdn1.genspark.ai/user-upload-image/v1/61777290-d87e-4d3e-93ab-ffa61c6bf80e",
                action: "inpaint"
            }, {
                id: 5,
                title: t("components.agents.image_studio.image-unblur"),
                image: "https://cdn1.genspark.ai/user-upload-image/v1/b1ed922b-2d3f-474e-9625-01e72cabcda4",
                action: "upscale"
            }, {
                id: 6,
                title: t("components.agents.image_studio.image-expand"),
                image: "https://cdn1.genspark.ai/user-upload-image/v1/bca3b382-8739-455e-8436-3606919abc7a",
                action: "outpaint"
            }, {
                id: 7,
                title: t("components.agents.image_studio.image-tryon"),
                image: "https://cdn1.genspark.ai/user-upload-image/v1/5249bea3-9d3e-440e-bd91-6e28422b6e6e",
                action: "url:/fashion/uploadCustom?from=image_studio"
            }],
            l = z(!1),
            g = z(null),
            d = z(null),
            p = z(null),
            _ = z(null),
            u = z(null),
            b = z(!1),
            m = z(!1),
            f = () => !!i.value || (location.href = "/api/login?redirect_url=" + encodeURIComponent(location.pathname + location.search), !1),
            k = z(!1),
            h = async (e, a) => {
                try {
                    const n = c.find((a => a.action === e));
                    if (!n) return;
                    p.value = n, u.value = n.action, _.value = a;
                    const t = await ae("currentUploadI2VImage", !1);
                    t && (g.value = t, l.value = !0)
                } finally {
                    b.value = !1
                }
            },
            v = async e => {
                if (e.data.type === E.ChromeExtensionPostImageStudioUrl) {
                    const a = e.data.payload;
                    if (u.value = a.type, b.value = !0, a.url.includes("base64")) l.value = !0, g.value = decodeURIComponent(a.url);
                    else {
                        const e = await oe(decodeURIComponent(a.url));
                        g.value = e, l.value = !0
                    }
                    b.value = !1
                }
            };
        return N((async () => {
            if ("local" === s.query.from) {
                const e = s.query.action,
                    a = s.query.path || "/";
                if (e) {
                    h(e, a);
                    const n = new URLSearchParams(window.location.search);
                    n.delete("from"), n.delete("action"), n.delete("path");
                    const t = window.location.pathname + (n.toString() ? "?" + n.toString() : "");
                    history.replaceState(null, "", t)
                }
            } else if (s.query.from === x && s.query.action === M.ImageStudio) window.addEventListener("message", v), window.postMessage({
                type: E.ChromeExtensionGetImageStudioUrl
            }, "*");
            else if (s.query.from === x) {
                const e = new URLSearchParams(window.location.search);
                g.value = await se(x, "Image"), l.value = !0, u.value = e.get("action"), b.value = !1
            }
        })), D((() => {
            window.removeEventListener("message", v)
        })), {
            enableVideoFeature: k,
            handleMainToolClick: async e => {
                if (!e.comingSoon) {
                    if ("moa_generate_video" === e.id) {
                        if (!f()) return;
                        return m.value = !0, void d.value.click()
                    }
                    n("basic_agent_click", {
                        id: e.id,
                        name: e.title
                    })
                }
            },
            mainTools: o,
            quickApps: c,
            showEditor: l,
            selectedFile: g,
            fileInput: d,
            currentApp: p,
            currentTool: u,
            handleQuickAppClick: e => {
                f() && (e.action.startsWith("url:") ? window.location.href = e.action.slice(4) : (p.value = e, u.value = e.action, d.value.click()))
            },
            handleFileSelect: async e => {
                const a = e.target.files[0];
                if (a) {
                    b.value = !0;
                    try {
                        if (g.value = await ne(a), m.value) return await te(g.value, "currentUploadI2VImage"), void(window.location.href = "/agents?type=moa_generate_video&from=local");
                        l.value = !0
                    } finally {
                        b.value = !1
                    }
                }
                e.target.value = ""
            },
            closeEditor: () => {
                l.value = !1, g.value = null
            },
            isLoading: b,
            handleEditFromIndexedDB: h,
            imageOriginPath: _
        }
    }
}, [
    ["render", function(e, a, n, t, i, s) {
        const o = V("GoIcon"),
            c = V("ImageEditor");
        return U(), I("div", fe, [R("div", ke, J(e.$t("components.agents.image_studio.image-generation")), 1), R("div", he, [(U(!0), I(B, null, G(t.mainTools, (a => (U(), I("div", {
            key: a.id,
            class: H(["tool-card", {
                "coming-soon-card": a.comingSoon
            }]),
            onClick: () => t.handleMainToolClick(a)
        }, [R("div", we, [R("div", ye, [R("div", Ce, [(U(), q(Y(a.icon)))]), R("div", Ae, J(a.title), 1), a.comingSoon ? L("", !0) : (U(), q(o, {
            key: 0
        }))]), a.comingSoon ? (U(), I("div", Ie, " (" + J(e.$t("components.agents.image_studio.coming-soon")) + ") ", 1)) : L("", !0), R("img", {
            src: a.image,
            alt: a.title,
            class: "tool-image"
        }, null, 8, Re)])], 10, ve)))), 128))]), R("div", Ue, [R("div", Se, J(e.$t("components.agents.image_studio.quick-apps")), 1), R("div", Pe, [R("input", {
            ref: "fileInput",
            type: "file",
            class: "hidden-input",
            onChange: a[0] || (a[0] = (...e) => t.handleFileSelect && t.handleFileSelect(...e)),
            accept: "image/*"
        }, null, 544), (U(!0), I(B, null, G(t.quickApps, (e => (U(), I("div", {
            key: e.id,
            class: "quick-tool-card",
            onClick: () => t.handleQuickAppClick(e)
        }, [R("img", {
            src: e.image,
            alt: e.title
        }, null, 8, ze), R("div", Te, J(e.title), 1)], 8, je)))), 128))])]), t.isLoading ? (U(), I("div", Ne, a[1] || (a[1] = [R("div", {
            class: "skeleton-content"
        }, null, -1)]))) : L("", !0), t.showEditor ? (U(), q(c, {
            key: 1,
            file: t.selectedFile,
            tool: t.currentTool,
            aiDriveFilePath: t.imageOriginPath,
            class: "fullscreen-editor",
            onClose: t.closeEditor
        }, null, 8, ["file", "tool", "aiDriveFilePath", "onClose"])) : L("", !0)])
    }],
    ["__scopeId", "data-v-fdd87f59"]
]);
const xe = "super_data_set1",
    Me = "sheets_agent";

function Ee(e, a) {
    const n = z(void 0),
        t = z(null);
    if (e.value.type === Me) return O((() => {
        var a, i, s, o;
        const c = e.value.session_state;
        if (!c) return;
        const l = function(e) {
            if ("manager" !== e.role) return;
            const {
                list: a,
                current: n
            } = e.chat_history;
            if (!a) return;
            const t = n ? a.find((e => e.project_id === n)) : a[0];
            return t.session_id ? {
                id: t.session_id,
                project_id: t.session_id,
                messages: []
            } : void 0
        }(c);
        if ((null == l ? void 0 : l.id) != (null == (a = n.value) ? void 0 : a.id)) {
            if (null == l || l.id, null == (i = n.value) || i.id, !l) return;
            !n.value && (null == (o = null == (s = t.value) ? void 0 : s.beforeMessages) ? void 0 : o.length) && (l.messages = t.value.beforeMessages), n.value = l, t.value && (t.value = null)
        }
    })), {
        handleAgentAskStart: function(e) {
            n.value || (t.value = {
                event: e,
                beforeMessages: a.value.getMessagesRef().value.slice()
            }, t.value)
        }
    }
}
const De = {
    __name: "agents_configs",
    emits: ["basic_agent_click", "phone_call_click", "phone_call_setting_click", "ai_drive_click"],
    setup(r, {
        expose: I,
        emit: R
    }) {
        const {
            t: U,
            locale: S
        } = P(), z = F();
        j();
        const N = R,
            K = e => {
                N("basic_agent_click", e)
            },
            x = e => {
                N("open_create_task_form", e)
            },
            M = {
                agents: [{
                    id: "super_agent",
                    name: U("pages.agents.genspark_super_agent"),
                    icon: T(a),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !0,
                    agent_type: "super_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !1,
                    is_new: !1,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "super_agent",
                        limit: 2
                    }),
                    viewer: {
                        new_label: U("pages.agents.genspark_super_agent"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                        icon: T(a),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "slide_agent",
                    name: "Genspark AI Slides",
                    icon: T(Q),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !0,
                    agent_type: "slide_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !0,
                    is_new: !1,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/slides_agent_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "slide_agent",
                        limit: 2
                    }),
                    is_slide_agent: !0,
                    viewer: {
                        new_label: "Genspark AI Slides",
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/slides_agent_icon.svg",
                        icon: T(a),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "slides_agent",
                    name: U("pages.agents.ai_slides"),
                    icon: T(Q),
                    onClick: K,
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "slides_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !1,
                    is_new: !1,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/slides_agent_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "slides_agent",
                        limit: 2
                    }),
                    is_slide_agent: !0,
                    viewer: {
                        new_label: U("pages.agents.ai_slides"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/slides_agent_icon.svg",
                        icon: T(Q),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "sheets_agent",
                    name: U("pages.agents.ai_sheets"),
                    icon: T(i),
                    onClick: K,
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "sheets_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !1,
                    is_new: !1,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/sheets_agent_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "sheets_agent",
                        limit: 2
                    }),
                    is_slide_agent: !0,
                    viewer: {
                        new_label: U("pages.agents.ai_sheets"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/slides_agent_icon.svg",
                        icon: T(ee),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "ai_inbox",
                    name: U("pages.agents.ai_inbox"),
                    icon: t,
                    onClick: K,
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "ai_inbox",
                    is_agent: !0,
                    show_title: !0,
                    hide: !0,
                    is_new: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/inbox_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "ai_inbox",
                        limit: 2
                    }),
                    viewer: {
                        new_label: U("pages.agents.ai_inbox"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/inbox_icon.svg",
                        icon: t,
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "offline_task",
                    name: "Offline Task",
                    icon: T(be),
                    onClick: K,
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "offline_task",
                    is_agent: !0,
                    show_title: !0,
                    hide: !0,
                    is_new: !1,
                    viewer: {
                        new_label: "Offline Task",
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        icon: T(be),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "docs_agent",
                    name: U("pages.agents.ai_docs"),
                    icon: T(s),
                    onClick: K,
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "docs_agent",
                    is_agent: !0,
                    show_title: !0,
                    is_new: !1,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/slides_agent_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "docs_agent",
                        limit: 2
                    }),
                    is_slide_agent: !0,
                    viewer: {
                        new_label: U("pages.agents.ai_docs"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                        icon: T(s),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "code_sandbox",
                    alias_id: "ai_developer",
                    name: "AI Developer",
                    icon: T(f),
                    onClick: K,
                    modelIcons: [ge],
                    is_advanced: !0,
                    agent_type: "code_sandbox",
                    is_agent: !0,
                    is_new: !0,
                    show_title: !0,
                    hide: !1,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/document_text_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "code_sandbox",
                        limit: 2
                    }),
                    viewer: {
                        new_label: "AI Developer",
                        buttonLabel: "Continue Coding",
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        input_placeholder: "Describe what you want to build or develop...",
                        icon: T(f),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/document_text_icon.svg",
                        popularTasks: re({
                            language: S.value,
                            task_type: "code_sandbox",
                            limit: 3
                        }),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "podcasts_agent",
                    name: U("pages.agents.ai_podcasts"),
                    icon: T($),
                    onClick: K,
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "podcasts_agent",
                    is_agent: !0,
                    show_title: !0,
                    is_new: !1,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/ai-pods.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "podcasts_agent",
                        limit: 2
                    }),
                    is_slide_agent: !0,
                    viewer: {
                        new_label: U("pages.agents.ai_podcasts"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/ai-pods.svg",
                        icon: T($),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "agentic_deep_research",
                    name: U("pages.agents.agentic_deep_research"),
                    icon: T(o),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !0,
                    agent_type: "agentic_deep_research",
                    is_agent: !0,
                    show_title: !0,
                    hide: !1,
                    is_new: !1,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "agentic_deep_research",
                        limit: 2
                    }),
                    viewer: {
                        new_label: U("pages.agents.agentic_deep_research"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                        icon: T(a),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "call_for_me",
                    name: "Call For Me Agent",
                    icon: T(c),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !0,
                    agent_type: "call_for_me",
                    is_agent: !0,
                    show_title: !0,
                    hide: "1" !== z.query.show_generate_sparkpage_gan,
                    is_new: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "call_for_me",
                        limit: 2
                    }),
                    viewer: {
                        new_label: "Call For Me Agent",
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                        icon: T(c),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "agentic_cross_check",
                    name: U("pages.agents.agentic_cross_check"),
                    icon: T(n),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !0,
                    agent_type: "agentic_cross_check",
                    is_agent: !0,
                    show_title: !0,
                    hide: !1,
                    popularTasks: re({
                        language: S.value,
                        task_type: "agentic_cross_check",
                        limit: 2
                    }),
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                    viewer: {
                        new_label: U("pages.agents.agentic_cross_check"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                        icon: T(a),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "phone_call",
                    name: U("pages.call_assistant.call_for_me"),
                    new_label: U("pages.call_assistant.call_for_me"),
                    task_label: U("pages.article_verification.task-phone-call"),
                    button_label: U("pages.call_assistant.place_call"),
                    input_placeholder: U("pages.article_verification.phone-call-input-placeholder"),
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "phone_call",
                        limit: 2
                    }),
                    icon: T(c),
                    onClick: e => {
                        N("phone_call_click", e)
                    },
                    onSetting: () => {
                        N("phone_call_setting_click")
                    },
                    is_new: !1,
                    is_advanced: !0,
                    hide: !1,
                    agent_type: "phone_call",
                    viewer: {
                        new_label: U("pages.agents.phone_call"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function() {
                            W.windowopen("/agents")
                        },
                        input_placeholder: U("pages.agents.phone_call_input_placeholder"),
                        icon: T(n),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                        popularTasks: re({
                            language: S.value,
                            task_type: "phone_call",
                            limit: 2
                        })
                    }
                }, {
                    id: "ai_drive",
                    name: U("components.aidrive.download_for_me"),
                    icon: T(l),
                    onClick: () => {
                        N("ai_drive_click")
                    },
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "ai_drive",
                    is_agent: !1,
                    show_title: !0,
                    hide: !1,
                    is_new: !1,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/AIDriveIcon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "ai_drive",
                        limit: 2
                    }),
                    viewer: {
                        new_label: U("components.aidrive.download_for_me"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/AIDriveIcon.svg",
                        icon: T(ce)
                    }
                }, {
                    id: "poster_agent",
                    name: "Poster Agent",
                    icon: T(Q),
                    onClick: K,
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "poster_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !0,
                    is_new: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/sheets_agent_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "poster_agent",
                        limit: 2
                    }),
                    is_slide_agent: !0,
                    viewer: {
                        new_label: "Poster Agent",
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/poster_agent_icon.svg",
                        icon: T(Q),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "image_agent",
                    name: "AI Designer",
                    icon: T(Q),
                    onClick: K,
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "image_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !0,
                    is_new: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/image_agent_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "image_agent",
                        limit: 2
                    }),
                    is_slide_agent: !0,
                    viewer: {
                        new_label: "AI Designer",
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/image_agent_icon.svg",
                        icon: T(Q),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "test_cooperation_agent",
                    name: "Cooperation Agent",
                    icon: T(Q),
                    onClick: K,
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "test_cooperation_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !0,
                    is_new: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/image_agent_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "test_cooperation_agent",
                        limit: 2
                    }),
                    is_slide_agent: !1,
                    viewer: {
                        new_label: "Cooperation Agent",
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/image_agent_icon.svg",
                        icon: T(Q),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "markdown_agent",
                    name: "pages.agents.markdown_agent",
                    icon: T(s),
                    onClick: K,
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "markdown_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !z.query.show_generate_sparkpage_gan,
                    is_new: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/slides_agent_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "markdown_agent",
                        limit: 2
                    }),
                    is_slide_agent: !0,
                    viewer: {
                        new_label: "Genspark AI Docs",
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                        icon: T(ee),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "html_agent",
                    name: U("pages.agents.html_agent1"),
                    icon: T(s),
                    onClick: K,
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "html_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !z.query.show_generate_sparkpage_gan,
                    is_new: !0,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/slides_agent_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "html_agent",
                        limit: 2
                    }),
                    is_slide_agent: !0,
                    viewer: {
                        new_label: "Genspark AI Docs",
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                        icon: T(s),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "moa_deep_research",
                    name: U("pages.agents.deep_research"),
                    icon: T(o),
                    onClick: e => {
                        K(e)
                    },
                    runningTips: [U("pages.agents.running_tips_deep_research1"), U("pages.agents.running_tips_deep_research2_1")],
                    agent_type: "moa_deep_research",
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "moa_deep_research",
                        limit: 2
                    }),
                    is_deep_research: !0,
                    is_advanced: !0,
                    is_new: !0,
                    show_title: !0,
                    no_run_project: !0,
                    hide: !0,
                    viewer: {
                        new_label: U("pages.agents.deep_research"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function() {
                            W.windowopen("/agents")
                        },
                        input_placeholder: U("pages.agents.deep_research_input_placeholder"),
                        icon: T(n),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                        popularTasks: re({
                            language: S.value,
                            task_type: "moa_deep_research",
                            limit: 3
                        })
                    }
                }, {
                    id: "generate_sparkpage_table",
                    name: U("pages.article_verification.data-search"),
                    new_label: U("pages.article_verification.data-search"),
                    task_label: U("pages.article_verification.task-data-search"),
                    button_label: U("pages.article_verification.data-search"),
                    input_placeholder: U("pages.article_verification.data-search-input-placeholder"),
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/data_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "generate_sparkpage_table",
                        limit: 2
                    }),
                    icon: T(X),
                    agent_type: "generate_sparkpage_table",
                    blog_url: "https://mainfunc.ai/blog/genspark_autopilot_agent_data_search",
                    blog_title: "World's First <b>Data Search Autopilot Agent</b>",
                    is_new: !1,
                    onClick: e => {
                        x({
                            type: e.id
                        })
                    },
                    runningTips: [U("pages.agents.running_tips_data_search1"), U("pages.agents.running_tips_data_search2_1")],
                    is_advanced: !0,
                    hide: !0,
                    viewer: {
                        new_label: U("pages.article_verification.data-search"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function() {
                            W.windowopen("/agents")
                        },
                        input_placeholder: U("pages.article_verification.data-search-input-placeholder"),
                        icon: T(X),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/data_icon.svg",
                        popularTasks: re({
                            language: S.value,
                            task_type: "generate_sparkpage_table",
                            limit: 3
                        })
                    }
                }, {
                    id: "article_verification",
                    name: U("pages.article_verification.cross-check"),
                    new_label: U("pages.article_verification.cross-check"),
                    task_label: U("pages.article_verification.task-cross-check"),
                    button_label: U("pages.article_verification.cross-check"),
                    input_placeholder: U("pages.article_verification.cross-check-input-placeholder"),
                    agent_type: "article_verification",
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "article_verification",
                        limit: 2
                    }),
                    icon: T(n),
                    blog_url: "https://mainfunc.ai/blog/genspark_autopilot_agent",
                    blog_title: "World's First <b>Cross Check Autopilot Agent</b>",
                    onClick: e => {
                        x({
                            type: e.id
                        })
                    },
                    runningTips: [U("pages.agents.running_tips_cross_check1"), U("pages.agents.running_tips_cross_check2_1")],
                    is_advanced: !0,
                    hide: !0,
                    viewer: {
                        new_label: U("pages.article_verification.cross-check"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function() {
                            W.windowopen("/agents")
                        },
                        input_placeholder: U("pages.article_verification.cross-check-input-placeholder"),
                        icon: T(n),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                        popularTasks: re({
                            language: S.value,
                            task_type: "article_verification",
                            limit: 3
                        })
                    }
                }, {
                    id: "sheets_to_site_agent",
                    name: U("pages.agents.sheets_to_site_agent"),
                    icon: T(f),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !0,
                    agent_type: "sheets_to_site_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !z.query.show_generate_sparkpage_gan,
                    is_new: !1,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "sheets_to_site_agent",
                        limit: 2
                    }),
                    viewer: {
                        new_label: U("pages.agents.sheets_to_site_agent"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                        icon: T(a),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "sheets_agent_new",
                    name: U("pages.agents.sheets_agent_new"),
                    icon: T(i),
                    onClick: K,
                    modelIcons: [],
                    is_advanced: !0,
                    agent_type: "sheets_agent_new",
                    is_agent: !0,
                    show_title: !0,
                    hide: !z.query.show_generate_sparkpage_gan,
                    is_new: !1,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/sheets_agent_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "sheets_agent_new",
                        limit: 2
                    }),
                    is_slide_agent: !0,
                    viewer: {
                        new_label: U("pages.agents.ai_sheets"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/sheets_agent_icon.svg",
                        icon: T(a),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "clip_genius_agent",
                    name: "Clip Genius Agent",
                    icon: T(f),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !0,
                    agent_type: "clip_genius_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !z.query.show_generate_sparkpage_gan,
                    is_new: !1,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "clip_genius_agent",
                        limit: 2
                    }),
                    viewer: {
                        new_label: "Clip Genius Agent",
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                        icon: T(a),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "agent_chat",
                    name: "Agent Chat",
                    icon: T(a),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !1,
                    agent_type: "super_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                    viewer: {
                        new_label: "Agent Chat",
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                        icon: T(a),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "super_chat",
                    name: U("pages.agents.super_chat"),
                    icon: T(a),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !1,
                    agent_type: "super_agent",
                    is_agent: !0,
                    show_title: !0,
                    hide: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                    viewer: {
                        new_label: "Super Chat",
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                        icon: T(a),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "super_data_set",
                    name: U("pages.agents.super_data_set"),
                    icon: T(ee),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !1,
                    agent_type: "super_data_set",
                    is_agent: !0,
                    show_title: !0,
                    hide: !z.query.show_generate_sparkpage_gan,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                    viewer: {
                        new_label: U("pages.agents.super_data_set"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                        icon: T(a),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: xe,
                    name: U("pages.agents.ai_sheets"),
                    icon: T(ee),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !1,
                    agent_type: xe,
                    is_agent: !0,
                    show_title: !0,
                    hide: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                    viewer: {
                        new_label: U("pages.agents.super_data_set"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: () => {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(project.value.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                        icon: T(a),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: Me,
                    name: U("pages.agents.ai_sheets"),
                    icon: T(ee),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !1,
                    agent_type: Me,
                    is_agent: !0,
                    show_title: !0,
                    hide: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg",
                    viewer: {
                        new_label: U("pages.agents.ai_sheets"),
                        buttonLabel: U("pages.autopilotagent_viewer.continue_conversation"),
                        buttonClicked: () => {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(project.value.id))
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/slides_agent_icon.svg",
                        icon: T(ee),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }, {
                    id: "agent_deep_research",
                    name: U("pages.agents.agentic_deep_research"),
                    icon: T(o),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !1,
                    agent_type: "agentic_deep_research",
                    is_agent: !0,
                    show_title: !0,
                    hide: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/general_chat.svg"
                }, {
                    id: "moa_chat",
                    name: U("pages.agents.ai_chat"),
                    icon: T(g),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    is_advanced: !1,
                    agent_type: "moa_chat",
                    show_title: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/ai_chat.svg",
                    viewer: {
                        new_label: U("pages.agents.ai_chat"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function() {
                            W.windowopen("/agents")
                        },
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/ai_chat.svg",
                        icon: T(g)
                    }
                }, {
                    id: "generate_presentation",
                    name: U("pages.agents.generate_presentation"),
                    icon: T(e),
                    onClick: K,
                    modelIcons: [le, ge, v],
                    hide: !0,
                    agent_type: "generate_presentation",
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate-image.svg"
                }, {
                    id: "moa_generate_image",
                    name: U("pages.agents.generate_image"),
                    onClick: K,
                    icon: T(e),
                    modelIcons: [w, y, C, A],
                    is_advanced: !1,
                    hide: !0,
                    agent_type: "image_studio",
                    show_title: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate-image.svg",
                    viewer: {
                        new_label: U("pages.agents.generate_image"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        icon: T(e),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate-image.svg"
                    }
                }, {
                    id: "image_studio",
                    name: U("pages.agents.image_studio"),
                    onClick: K,
                    icon: T(e),
                    modelIcons: [w, y, C, A],
                    component: Ke,
                    is_advanced: !1,
                    hide: !1,
                    agent_type: "image_studio",
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate-image.svg"
                }, {
                    id: "moa_generate_video",
                    name: U("pages.agents.generate_video"),
                    onClick: K,
                    icon: T(d),
                    modelIcons: [_, u, b, m],
                    is_advanced: !1,
                    hide: !1,
                    agent_type: "moa_generate_video",
                    show_title: !0,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate_video_icon.svg",
                    viewer: {
                        new_label: U("pages.agents.generate_video"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        icon: T(d),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate_video_icon.svg"
                    }
                }, {
                    id: "image_generation_agent",
                    name: U("pages.agents.generate_image"),
                    onClick: K,
                    icon: T(e),
                    modelIcons: [w, y, C, A],
                    is_advanced: !1,
                    is_agent: !0,
                    hide: !0,
                    agent_type: "image_generation_agent",
                    show_title: !0,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate-image.svg",
                    viewer: {
                        new_label: U("pages.agents.generate_image"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        icon: T(e),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate-image.svg"
                    }
                }, {
                    id: "dynamic_agent",
                    name: U("pages.agents.dynamic_agent"),
                    onClick: K,
                    icon: T(k),
                    modelIcons: [],
                    is_advanced: !1,
                    is_agent: !0,
                    hide: !0,
                    agent_type: "dynamic_agent",
                    show_title: !0,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate-image.svg",
                    viewer: {
                        new_label: U("pages.agents.dynamic_agent"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        icon: T(e),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate-image.svg"
                    }
                }, {
                    id: "audio_generation_agent",
                    name: U("pages.agents.generate_audio"),
                    onClick: K,
                    icon: T(h),
                    modelIcons: [le, v],
                    is_advanced: !1,
                    is_agent: !0,
                    hide: !0,
                    agent_type: "audio_generation_agent",
                    show_title: !0,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate-image.svg",
                    viewer: {
                        new_label: U("pages.agents.generate_audio"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        icon: T(h),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate-image.svg"
                    }
                }, {
                    id: "video_generation_agent",
                    name: U("pages.agents.generate_video"),
                    onClick: K,
                    icon: T(d),
                    modelIcons: [_, u, b, m],
                    is_advanced: !1,
                    hide: !0,
                    is_agent: !0,
                    agent_type: "video_generation_agent",
                    show_title: !0,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate_video_icon.svg",
                    viewer: {
                        new_label: U("pages.agents.generate_video"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        icon: T(d),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate_video_icon.svg"
                    }
                }, {
                    id: "video_studio",
                    name: "Video Studio",
                    onClick: K,
                    icon: T(d),
                    modelIcons: [_, u, b, m],
                    is_advanced: !1,
                    is_agent: !0,
                    hide: !0,
                    agent_type: "video_studio",
                    show_title: !0,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate_video_icon.svg",
                    viewer: {
                        new_label: "Video Studio Agent",
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        icon: T(d),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/generate_video_icon.svg"
                    }
                }, {
                    id: "moa_translator",
                    name: U("pages.agents.translation"),
                    icon: T(p),
                    onClick: K,
                    modelIcons: [_e, le, v, ge],
                    is_advanced: !1,
                    agent_type: "moa_translator",
                    show_title: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/translation.svg",
                    viewer: {
                        new_label: U("pages.agents.translation"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        icon: T(p),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/translation.svg"
                    }
                }, {
                    id: "generate_sparkpage_gan",
                    name: U("pages.article_verification.generate-sparkpage"),
                    new_label: U("pages.article_verification.new-generate-sparkpage"),
                    task_label: U("pages.article_verification.task-generate-sparkpage-gan"),
                    button_label: U("pages.article_verification.generate-sparkpage"),
                    input_placeholder: U("pages.article_verification.generate-sparkpage-input-placeholder"),
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "generate_sparkpage_gan",
                        limit: 3
                    }),
                    icon: T(f),
                    onClick: e => {
                        x({
                            type: e.id
                        })
                    },
                    hide: !z.query.show_generate_sparkpage_gan,
                    is_advanced: !0,
                    agent_type: "generate_sparkpage_gan",
                    viewer: {
                        new_label: U("pages.article_verification.generate-sparkpage"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function() {
                            W.windowopen("/agents")
                        },
                        input_placeholder: U("pages.article_verification.generate-sparkpage-input-placeholder"),
                        icon: T(n),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                        popularTasks: re({
                            language: S.value,
                            task_type: "generate_sparkpage_gan",
                            limit: 3
                        })
                    }
                }, {
                    id: "generate_info_collection",
                    name: U("pages.article_verification.generate-info-collection"),
                    new_label: U("pages.article_verification.new-generate-info-collection"),
                    task_label: U("pages.article_verification.task-generate-info-collection"),
                    button_label: U("pages.article_verification.generate-info-collection"),
                    input_placeholder: U("pages.article_verification.generate-info-collection-input-placeholder"),
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "generate_info_collection",
                        limit: 3
                    }),
                    icon: T(f),
                    onClick: e => {
                        x({
                            type: e.id
                        })
                    },
                    hide: !z.query.show_generate_sparkpage_gan,
                    is_advanced: !0,
                    agent_type: "generate_info_collection"
                }, {
                    id: "moa_deep_research_v2",
                    name: "Deep Research",
                    icon: T(o),
                    onClick: e => {
                        K(e)
                    },
                    runningTips: [U("pages.agents.running_tips_deep_research1"), U("pages.agents.running_tips_deep_research2_1")],
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "moa_deep_research_v2",
                        limit: 2
                    }),
                    is_deep_research: !0,
                    is_advanced: !0,
                    hide: !0,
                    agent_type: "moa_deep_research",
                    show_title: !0,
                    no_run_project: !0,
                    viewer: {
                        new_label: U("pages.agents.deep_research"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function() {
                            W.windowopen("/agents")
                        },
                        input_placeholder: U("pages.agents.deep_research_input_placeholder"),
                        icon: T(n),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                        popularTasks: re({
                            language: S.value,
                            task_type: "moa_deep_research_v2",
                            limit: 3
                        })
                    }
                }, {
                    id: "moa_deep_research_v3",
                    name: "Deep Research",
                    icon: T(f),
                    onClick: e => {
                        K(e)
                    },
                    runningTips: [U("pages.agents.running_tips_deep_research1"), U("pages.agents.running_tips_deep_research2_1")],
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "moa_deep_research_v3",
                        limit: 2
                    }),
                    is_deep_research: !0,
                    is_advanced: !0,
                    is_new: !0,
                    hide: !z.query.show_generate_sparkpage_gan,
                    agent_type: "moa_deep_research",
                    show_title: !0,
                    no_run_project: !0,
                    viewer: {
                        new_label: U("pages.agents.deep_research"),
                        buttonLabel: U("pages.autopilotagent_viewer.goto_genspark_agent"),
                        buttonClicked: function() {
                            W.windowopen("/agents")
                        },
                        input_placeholder: U("pages.agents.deep_research_input_placeholder"),
                        icon: T(n),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/checks_circle_icon.svg",
                        popularTasks: re({
                            language: S.value,
                            task_type: "moa_deep_research_v3",
                            limit: 3
                        })
                    }
                }, {
                    id: "code_sandbox_light",
                    alias_id: "ai_developer_light",
                    name: "AI Developer",
                    icon: T(f),
                    onClick: K,
                    modelIcons: [ge],
                    is_advanced: !0,
                    agent_type: "code_sandbox_light",
                    is_agent: !0,
                    show_title: !0,
                    hide: !0,
                    support_chat_session: !0,
                    favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/document_text_icon.svg",
                    popularTasks: re({
                        language: S.value,
                        task_type: "code_sandbox_light",
                        limit: 2
                    }),
                    viewer: {
                        new_label: "AI Developer",
                        buttonLabel: "Continue Coding",
                        buttonClicked: function(e) {
                            W.windowopen("/api/continue_conversation?id=" + encodeURIComponent(null == e ? void 0 : e.id))
                        },
                        input_placeholder: "Describe what you want to build or develop...",
                        icon: T(f),
                        favicon_url: "https://cdn1.genspark.ai/user-upload-image/manual/document_text_icon.svg",
                        popularTasks: re({
                            language: S.value,
                            task_type: "code_sandbox_light",
                            limit: 3
                        }),
                        use_chat_agent: !0,
                        show_replay: !0
                    }
                }]
            },
            E = e => ["code_sandbox", "code_sandbox_light"].includes(e);
        return I({
            agentsPageConfig: M,
            isProjectTypeSupportChatSessions: e => {
                const a = M.agents.find((a => a.agent_type === e));
                return !!a && !0 === a.support_chat_session
            },
            isProjectTypeSupportModelSelection: E,
            getAvailableModelsForProjectType: e => E(e) ? [{
                name: "claude-sonnet-4",
                icon: ge,
                label: "Claude Sonnet 4",
                full_label: "Anthropic Claude Sonnet 4",
                description: "Efficient, balancing speed, intelligence"
            }, {
                name: "claude-opus-4-1-20250805",
                icon: ge,
                label: "Claude Opus 4.1",
                full_label: "Anthropic Claude Opus 4.1",
                description: "Most powerful, expensive for complex tasks"
            }, {
                name: "gpt-5",
                icon: le,
                label: "GPT-5",
                full_label: "Open AI GPT-5",
                description: "Cheaper and strongest coding model from OpenAI"
            }, {
                name: "groq-kimi-k2-instruct",
                icon: de,
                label: "Kimi K2 + Groq",
                full_label: "Kimi K2 Instruct",
                description: "Fastest, cheapest with good coding quality",
                not_support_image: !0
            }] : []
        }), (e, a) => null
    }
};
export {
    ge as C, _e as D, Ke as I, de as K, xe as M, be as N, le as O, Me as S, De as _, re as g, Ee as u
};