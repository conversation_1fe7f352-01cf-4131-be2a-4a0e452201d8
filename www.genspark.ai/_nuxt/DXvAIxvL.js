import {
    al as n,
    am as e,
    ak as o,
    J as i,
    au as t,
    X as r,
    av as s,
    Y as a,
    Z as c,
    aw as l,
    c as d,
    aq as m
} from "./Cf0SOiw0.js";
import {
    f as p
} from "./CW991W2w.js";
const h = n("icon", "\n height: 1em;\n width: 1em;\n line-height: 1em;\n text-align: center;\n display: inline-block;\n position: relative;\n fill: currentColor;\n transform: translateZ(0);\n", [e("color-transition", {
        transition: "color .3s var(--n-bezier)"
    }), e("depth", {
        color: "var(--n-color)"
    }, [o("svg", {
        opacity: "var(--n-opacity)",
        transition: "opacity .3s var(--n-bezier)"
    })]), o("svg", {
        height: "1em",
        width: "1em"
    })]),
    u = i({
        _n_icon__: !0,
        name: "I<PERSON>",
        inheritAttrs: !1,
        props: Object.assign(Object.assign({}, c.props), {
            depth: [String, Number],
            size: [Number, String],
            color: String,
            component: [Object, Function]
        }),
        setup(n) {
            const {
                mergedClsPrefixRef: e,
                inlineThemeDisabled: o
            } = a(n), i = c("Icon", "-icon", h, l, n, e), t = d((() => {
                const {
                    depth: e
                } = n, {
                    common: {
                        cubicBezierEaseInOut: o
                    },
                    self: t
                } = i.value;
                if (void 0 !== e) {
                    const {
                        color: n,
                        [`opacity${e}Depth`]: i
                    } = t;
                    return {
                        "--n-bezier": o,
                        "--n-color": n,
                        "--n-opacity": i
                    }
                }
                return {
                    "--n-bezier": o,
                    "--n-color": "",
                    "--n-opacity": ""
                }
            })), r = o ? m("icon", d((() => `${n.depth||"d"}`)), t, n) : void 0;
            return {
                mergedClsPrefix: e,
                mergedStyle: d((() => {
                    const {
                        size: e,
                        color: o
                    } = n;
                    return {
                        fontSize: p(e),
                        color: o
                    }
                })),
                cssVars: o ? void 0 : t,
                themeClass: null == r ? void 0 : r.themeClass,
                onRender: null == r ? void 0 : r.onRender
            }
        },
        render() {
            var n;
            const {
                $parent: e,
                depth: o,
                mergedClsPrefix: i,
                component: a,
                onRender: c,
                themeClass: l
            } = this;
            return (null === (n = null == e ? void 0 : e.$options) || void 0 === n ? void 0 : n._n_icon__) && t("icon", "don't wrap `n-icon` inside `n-icon`"), null == c || c(), r("i", s(this.$attrs, {
                role: "img",
                class: [`${i}-icon`, l, {
                    [`${i}-icon--depth`]: o,
                    [`${i}-icon--color-transition`]: void 0 !== o
                }],
                style: [this.cssVars, this.mergedStyle]
            }), a ? r(a) : this.$slots)
        }
    });
export {
    u as N
};