import {
    d as t,
    b as r,
    o as l
} from "./Cf0SOiw0.js";
const e = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const o = {
    render: function(o, i) {
        return l(), t("svg", e, i[0] || (i[0] = [r("rect", {
            x: "3",
            y: "5",
            width: "18",
            height: "1",
            fill: "currentColor"
        }, null, -1), r("rect", {
            x: "3",
            y: "11",
            width: "18",
            height: "1",
            fill: "currentColor"
        }, null, -1), r("rect", {
            x: "3",
            y: "17",
            width: "18",
            height: "1",
            fill: "currentColor"
        }, null, -1)]))
    }
};
export {
    o as S
};