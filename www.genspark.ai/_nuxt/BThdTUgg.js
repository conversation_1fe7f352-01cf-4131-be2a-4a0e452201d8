import {
    d as r,
    o as n,
    b as o
} from "./Cf0SOiw0.js";
const t = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const e = {
        render: function(e, l) {
            return n(), r("svg", t, l[0] || (l[0] = [o("path", {
                d: "M20.9957 11C20.998 11.4701 20.998 11.9693 20.998 12.5C20.998 16.9783 20.998 19.2175 19.6068 20.6088C18.2155 22 15.9763 22 11.498 22C7.01971 22 4.78054 22 3.38929 20.6088C1.99805 19.2175 1.99805 16.9783 1.99805 12.5C1.99805 8.02166 1.99805 5.78249 3.38929 4.39124C4.78054 3 7.01971 3 11.498 3C12.0287 3 12.5279 3 12.998 3.00231",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round"
            }, null, -1), o("path", {
                d: "M15 8L13 8.79711L13 15.5",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M10.5 18C11.8807 18 13 16.8807 13 15.5C13 14.1193 11.8807 13 10.5 13C9.11929 13 8 14.1193 8 15.5C8 16.8807 9.11929 18 10.5 18Z",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M18.5 2L18.7579 2.69703C19.0961 3.61102 19.2652 4.06802 19.5986 4.40139C19.932 4.73477 20.389 4.90387 21.303 5.24208L22 5.5L21.303 5.75792C20.389 6.09613 19.932 6.26524 19.5986 6.59861C19.2652 6.93198 19.0961 7.38898 18.7579 8.30297L18.5 9L18.2421 8.30297C17.9039 7.38898 17.7348 6.93198 17.4014 6.59861C17.068 6.26524 16.611 6.09613 15.697 5.75792L15 5.5L15.697 5.24208C16.611 4.90387 17.068 4.73477 17.4014 4.40139C17.7348 4.06802 17.9039 3.61102 18.2421 2.69703L18.5 2Z",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    l = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 24 24"
    };
const s = {
        render: function(t, e) {
            return n(), r("svg", l, e[0] || (e[0] = [o("path", {
                d: "M8 8H6v7c0 1.1.9 2 2 2h9v-2H8V8z",
                fill: "currentColor"
            }, null, -1), o("path", {
                d: "M20 3h-8c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 8h-8V7h8v4zM4 12H2v7c0 1.1.9 2 2 2h9v-2H4v-7z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    C = "" + new URL("kling_icon.B9Oegjgb.png",
        import.meta.url).href,
    i = "" + new URL("pixverse_icon.dA9T4Slk.png",
        import.meta.url).href,
    h = "" + new URL("luma_icon.DbkLlrXJ.png",
        import.meta.url).href,
    a = "" + new URL("hunyuan_icon.BXIfEhw_.png",
        import.meta.url).href,
    u = {
        width: "28",
        height: "28",
        viewBox: "0 0 28 28",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const w = {
        render: function(t, e) {
            return n(), r("svg", u, e[0] || (e[0] = [o("path", {
                d: "M16.8623 15.0439C16.6855 15.7751 15.8503 16.2918 14.1799 17.3252C12.565 18.3241 11.7576 18.8237 11.1069 18.6229C10.8379 18.5398 10.5928 18.3822 10.3951 18.165C9.91699 17.6398 9.91699 16.6209 9.91699 14.5833C9.91699 12.5457 9.91699 11.5269 10.3951 11.0016C10.5928 10.7844 10.8379 10.6268 11.1069 10.5438C11.7576 10.343 12.565 10.8425 14.1799 11.8414C15.8503 12.8749 16.6855 13.3916 16.8623 14.1227C16.9352 14.4245 16.9352 14.7421 16.8623 15.0439Z",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M24.497 12.8333C24.4997 13.3818 24.4997 13.9642 24.4997 14.5833C24.4997 19.808 24.4997 22.4204 22.8766 24.0436C21.2534 25.6667 18.641 25.6667 13.4163 25.6667C8.19161 25.6667 5.57925 25.6667 3.95612 24.0436C2.33301 22.4204 2.33301 19.808 2.33301 14.5833C2.33301 9.3586 2.33301 6.74624 3.95612 5.12311C5.57925 3.5 8.19161 3.5 13.4163 3.5C14.0355 3.5 14.6179 3.5 15.1663 3.5027",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round"
            }, null, -1), o("path", {
                d: "M21.5833 2.33594L21.8842 3.14914C22.2788 4.21546 22.4761 4.74863 22.865 5.13756C23.254 5.5265 23.7872 5.72379 24.8535 6.11836L25.6667 6.41927L24.8535 6.72018C23.7872 7.11476 23.254 7.31205 22.865 7.70098C22.4761 8.08991 22.2788 8.62308 21.8842 9.6894L21.5833 10.5026L21.2824 9.6894C20.8879 8.62308 20.6906 8.08991 20.3016 7.70098C19.9127 7.31205 19.3795 7.11476 18.3132 6.72018L17.5 6.41927L18.3132 6.11836C19.3795 5.72379 19.9127 5.5265 20.3016 5.13756C20.6906 4.74863 20.8879 4.21546 21.2824 3.14914L21.5833 2.33594Z",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    c = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 24 24"
    };
const d = {
    render: function(t, e) {
        return n(), r("svg", c, e[0] || (e[0] = [o("g", {
            fill: "none"
        }, [o("path", {
            d: "M8.75 11.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5zm0 2.75a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5zm0 2.75a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5zm4.835-14.414l5.829 5.828A2 2 0 0 1 20 9.828V20a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h6.172c.028 0 .055.004.082.007c.02.003.04.006.059.007c.215.015.427.056.624.138c.057.024.112.056.166.087l.05.029l.047.024a.652.652 0 0 1 .081.044c.078.053.148.116.219.18a.63.63 0 0 0 .036.03a.491.491 0 0 1 .049.04zM18 20.5a.5.5 0 0 0 .5-.5V10H14a2 2 0 0 1-2-2V3.5H6a.5.5 0 0 0-.5.5v16a.5.5 0 0 0 .5.5h12zm-.622-12L13.5 4.621V8a.5.5 0 0 0 .5.5h3.378z",
            fill: "currentColor"
        })], -1)]))
    }
};
export {
    d as D, e as G, a as H, w as I, C as K, h as L, i as P, s as a
};