function e(e) {
    return new Promise(((t, n) => {
        const r = indexedDB.open(e, 1);
        r.onupgradeneeded = function(e) {
            const t = e.target.result;
            t.objectStoreNames.contains("texts") || t.createObjectStore("texts")
        }, r.onsuccess = function(e) {
            t(e.target.result)
        }, r.onerror = function(e) {
            n("IndexedDB open error: " + e.target.errorCode)
        }
    }))
}
async function t(t, n, r) {
    const o = (await e(t)).transaction("texts", "readwrite");
    return o.objectStore("texts").put(r, n), new Promise(((e, t) => {
        o.oncomplete = () => e(), o.onerror = () => t("Transaction failed")
    }))
}
async function n(t, n) {
    const r = (await e(t)).transaction("texts", "readonly").objectStore("texts");
    return new Promise(((e, t) => {
        const o = r.get(n);
        o.onsuccess = () => e(o.result), o.onerror = () => t("Read failed")
    }))
}
export {
    n as r, t as s
};