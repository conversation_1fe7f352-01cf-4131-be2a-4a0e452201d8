const {
    entries: e,
    setPrototypeOf: t,
    isFrozen: n,
    getPrototypeOf: o,
    getOwnPropertyDescriptor: r
} = Object;
let {
    freeze: i,
    seal: a,
    create: l
} = Object, {
    apply: c,
    construct: s
} = "undefined" != typeof Reflect && Reflect;
i || (i = function(e) {
    return e
}), a || (a = function(e) {
    return e
}), c || (c = function(e, t, n) {
    return e.apply(t, n)
}), s || (s = function(e, t) {
    return new e(...t)
});
const u = R(Array.prototype.forEach),
    m = R(Array.prototype.lastIndexOf),
    p = R(Array.prototype.pop),
    f = R(Array.prototype.push),
    d = R(Array.prototype.splice),
    h = R(String.prototype.toLowerCase),
    g = R(String.prototype.toString),
    T = R(String.prototype.match),
    y = R(String.prototype.replace),
    E = R(String.prototype.indexOf),
    A = R(String.prototype.trim),
    _ = R(Object.prototype.hasOwnProperty),
    S = R(RegExp.prototype.test),
    N = (b = TypeError, function() {
        for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
        return s(b, t)
    });
var b;

function R(e) {
    return function(t) {
        t instanceof RegExp && (t.lastIndex = 0);
        for (var n = arguments.length, o = new Array(n > 1 ? n - 1 : 0), r = 1; r < n; r++) o[r - 1] = arguments[r];
        return c(e, t, o)
    }
}

function w(e, o) {
    let r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : h;
    t && t(e, null);
    let i = o.length;
    for (; i--;) {
        let t = o[i];
        if ("string" == typeof t) {
            const e = r(t);
            e !== t && (n(o) || (o[i] = e), t = e)
        }
        e[t] = !0
    }
    return e
}

function O(e) {
    for (let t = 0; t < e.length; t++) {
        _(e, t) || (e[t] = null)
    }
    return e
}

function v(t) {
    const n = l(null);
    for (const [o, r] of e(t)) {
        _(t, o) && (Array.isArray(r) ? n[o] = O(r) : r && "object" == typeof r && r.constructor === Object ? n[o] = v(r) : n[o] = r)
    }
    return n
}

function D(e, t) {
    for (; null !== e;) {
        const n = r(e, t);
        if (n) {
            if (n.get) return R(n.get);
            if ("function" == typeof n.value) return R(n.value)
        }
        e = o(e)
    }
    return function() {
        return null
    }
}
const L = i(["a", "abbr", "acronym", "address", "area", "article", "aside", "audio", "b", "bdi", "bdo", "big", "blink", "blockquote", "body", "br", "button", "canvas", "caption", "center", "cite", "code", "col", "colgroup", "content", "data", "datalist", "dd", "decorator", "del", "details", "dfn", "dialog", "dir", "div", "dl", "dt", "element", "em", "fieldset", "figcaption", "figure", "font", "footer", "form", "h1", "h2", "h3", "h4", "h5", "h6", "head", "header", "hgroup", "hr", "html", "i", "img", "input", "ins", "kbd", "label", "legend", "li", "main", "map", "mark", "marquee", "menu", "menuitem", "meter", "nav", "nobr", "ol", "optgroup", "option", "output", "p", "picture", "pre", "progress", "q", "rp", "rt", "ruby", "s", "samp", "section", "select", "shadow", "small", "source", "spacer", "span", "strike", "strong", "style", "sub", "summary", "sup", "table", "tbody", "td", "template", "textarea", "tfoot", "th", "thead", "time", "tr", "track", "tt", "u", "ul", "var", "video", "wbr"]),
    C = i(["svg", "a", "altglyph", "altglyphdef", "altglyphitem", "animatecolor", "animatemotion", "animatetransform", "circle", "clippath", "defs", "desc", "ellipse", "filter", "font", "g", "glyph", "glyphref", "hkern", "image", "line", "lineargradient", "marker", "mask", "metadata", "mpath", "path", "pattern", "polygon", "polyline", "radialgradient", "rect", "stop", "style", "switch", "symbol", "text", "textpath", "title", "tref", "tspan", "view", "vkern"]),
    x = i(["feBlend", "feColorMatrix", "feComponentTransfer", "feComposite", "feConvolveMatrix", "feDiffuseLighting", "feDisplacementMap", "feDistantLight", "feDropShadow", "feFlood", "feFuncA", "feFuncB", "feFuncG", "feFuncR", "feGaussianBlur", "feImage", "feMerge", "feMergeNode", "feMorphology", "feOffset", "fePointLight", "feSpecularLighting", "feSpotLight", "feTile", "feTurbulence"]),
    I = i(["animate", "color-profile", "cursor", "discard", "font-face", "font-face-format", "font-face-name", "font-face-src", "font-face-uri", "foreignobject", "hatch", "hatchpath", "mesh", "meshgradient", "meshpatch", "meshrow", "missing-glyph", "script", "set", "solidcolor", "unknown", "use"]),
    k = i(["math", "menclose", "merror", "mfenced", "mfrac", "mglyph", "mi", "mlabeledtr", "mmultiscripts", "mn", "mo", "mover", "mpadded", "mphantom", "mroot", "mrow", "ms", "mspace", "msqrt", "mstyle", "msub", "msup", "msubsup", "mtable", "mtd", "mtext", "mtr", "munder", "munderover", "mprescripts"]),
    M = i(["maction", "maligngroup", "malignmark", "mlongdiv", "mscarries", "mscarry", "msgroup", "mstack", "msline", "msrow", "semantics", "annotation", "annotation-xml", "mprescripts", "none"]),
    U = i(["#text"]),
    z = i(["accept", "action", "align", "alt", "autocapitalize", "autocomplete", "autopictureinpicture", "autoplay", "background", "bgcolor", "border", "capture", "cellpadding", "cellspacing", "checked", "cite", "class", "clear", "color", "cols", "colspan", "controls", "controlslist", "coords", "crossorigin", "datetime", "decoding", "default", "dir", "disabled", "disablepictureinpicture", "disableremoteplayback", "download", "draggable", "enctype", "enterkeyhint", "face", "for", "headers", "height", "hidden", "high", "href", "hreflang", "id", "inputmode", "integrity", "ismap", "kind", "label", "lang", "list", "loading", "loop", "low", "max", "maxlength", "media", "method", "min", "minlength", "multiple", "muted", "name", "nonce", "noshade", "novalidate", "nowrap", "open", "optimum", "pattern", "placeholder", "playsinline", "popover", "popovertarget", "popovertargetaction", "poster", "preload", "pubdate", "radiogroup", "readonly", "rel", "required", "rev", "reversed", "role", "rows", "rowspan", "spellcheck", "scope", "selected", "shape", "size", "sizes", "span", "srclang", "start", "src", "srcset", "step", "style", "summary", "tabindex", "title", "translate", "type", "usemap", "valign", "value", "width", "wrap", "xmlns", "slot"]),
    P = i(["accent-height", "accumulate", "additive", "alignment-baseline", "amplitude", "ascent", "attributename", "attributetype", "azimuth", "basefrequency", "baseline-shift", "begin", "bias", "by", "class", "clip", "clippathunits", "clip-path", "clip-rule", "color", "color-interpolation", "color-interpolation-filters", "color-profile", "color-rendering", "cx", "cy", "d", "dx", "dy", "diffuseconstant", "direction", "display", "divisor", "dur", "edgemode", "elevation", "end", "exponent", "fill", "fill-opacity", "fill-rule", "filter", "filterunits", "flood-color", "flood-opacity", "font-family", "font-size", "font-size-adjust", "font-stretch", "font-style", "font-variant", "font-weight", "fx", "fy", "g1", "g2", "glyph-name", "glyphref", "gradientunits", "gradienttransform", "height", "href", "id", "image-rendering", "in", "in2", "intercept", "k", "k1", "k2", "k3", "k4", "kerning", "keypoints", "keysplines", "keytimes", "lang", "lengthadjust", "letter-spacing", "kernelmatrix", "kernelunitlength", "lighting-color", "local", "marker-end", "marker-mid", "marker-start", "markerheight", "markerunits", "markerwidth", "maskcontentunits", "maskunits", "max", "mask", "media", "method", "mode", "min", "name", "numoctaves", "offset", "operator", "opacity", "order", "orient", "orientation", "origin", "overflow", "paint-order", "path", "pathlength", "patterncontentunits", "patterntransform", "patternunits", "points", "preservealpha", "preserveaspectratio", "primitiveunits", "r", "rx", "ry", "radius", "refx", "refy", "repeatcount", "repeatdur", "restart", "result", "rotate", "scale", "seed", "shape-rendering", "slope", "specularconstant", "specularexponent", "spreadmethod", "startoffset", "stddeviation", "stitchtiles", "stop-color", "stop-opacity", "stroke-dasharray", "stroke-dashoffset", "stroke-linecap", "stroke-linejoin", "stroke-miterlimit", "stroke-opacity", "stroke", "stroke-width", "style", "surfacescale", "systemlanguage", "tabindex", "tablevalues", "targetx", "targety", "transform", "transform-origin", "text-anchor", "text-decoration", "text-rendering", "textlength", "type", "u1", "u2", "unicode", "values", "viewbox", "visibility", "version", "vert-adv-y", "vert-origin-x", "vert-origin-y", "width", "word-spacing", "wrap", "writing-mode", "xchannelselector", "ychannelselector", "x", "x1", "x2", "xmlns", "y", "y1", "y2", "z", "zoomandpan"]),
    H = i(["accent", "accentunder", "align", "bevelled", "close", "columnsalign", "columnlines", "columnspan", "denomalign", "depth", "dir", "display", "displaystyle", "encoding", "fence", "frame", "height", "href", "id", "largeop", "length", "linethickness", "lspace", "lquote", "mathbackground", "mathcolor", "mathsize", "mathvariant", "maxsize", "minsize", "movablelimits", "notation", "numalign", "open", "rowalign", "rowlines", "rowspacing", "rowspan", "rspace", "rquote", "scriptlevel", "scriptminsize", "scriptsizemultiplier", "selection", "separator", "separators", "stretchy", "subscriptshift", "supscriptshift", "symmetric", "voffset", "width", "xmlns"]),
    F = i(["xlink:href", "xml:id", "xlink:title", "xml:space", "xmlns:xlink"]),
    B = a(/\{\{[\w\W]*|[\w\W]*\}\}/gm),
    W = a(/<%[\w\W]*|[\w\W]*%>/gm),
    G = a(/\$\{[\w\W]*/gm),
    Y = a(/^data-[\-\w.\u00B7-\uFFFF]+$/),
    j = a(/^aria-[\-\w]+$/),
    X = a(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),
    q = a(/^(?:\w+script|data):/i),
    $ = a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),
    K = a(/^html$/i),
    V = a(/^[a-z][.\w]*(-[.\w]+)+$/i);
var Z = Object.freeze({
    __proto__: null,
    ARIA_ATTR: j,
    ATTR_WHITESPACE: $,
    CUSTOM_ELEMENT: V,
    DATA_ATTR: Y,
    DOCTYPE_NAME: K,
    ERB_EXPR: W,
    IS_ALLOWED_URI: X,
    IS_SCRIPT_OR_DATA: q,
    MUSTACHE_EXPR: B,
    TMPLIT_EXPR: G
});
const J = 1,
    Q = 3,
    ee = 7,
    te = 8,
    ne = 9;
var oe = function t() {
    let n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "undefined" == typeof window ? null : window;
    const o = e => t(e);
    if (o.version = "3.2.5", o.removed = [], !n || !n.document || n.document.nodeType !== ne || !n.Element) return o.isSupported = !1, o;
    let {
        document: r
    } = n;
    const a = r,
        c = a.currentScript,
        {
            DocumentFragment: s,
            HTMLTemplateElement: b,
            Node: R,
            Element: O,
            NodeFilter: B,
            NamedNodeMap: W = n.NamedNodeMap || n.MozNamedAttrMap,
            HTMLFormElement: G,
            DOMParser: Y,
            trustedTypes: j
        } = n,
        q = O.prototype,
        $ = D(q, "cloneNode"),
        V = D(q, "remove"),
        oe = D(q, "nextSibling"),
        re = D(q, "childNodes"),
        ie = D(q, "parentNode");
    if ("function" == typeof b) {
        const e = r.createElement("template");
        e.content && e.content.ownerDocument && (r = e.content.ownerDocument)
    }
    let ae, le = "";
    const {
        implementation: ce,
        createNodeIterator: se,
        createDocumentFragment: ue,
        getElementsByTagName: me
    } = r, {
        importNode: pe
    } = a;
    let fe = {
        afterSanitizeAttributes: [],
        afterSanitizeElements: [],
        afterSanitizeShadowDOM: [],
        beforeSanitizeAttributes: [],
        beforeSanitizeElements: [],
        beforeSanitizeShadowDOM: [],
        uponSanitizeAttribute: [],
        uponSanitizeElement: [],
        uponSanitizeShadowNode: []
    };
    o.isSupported = "function" == typeof e && "function" == typeof ie && ce && void 0 !== ce.createHTMLDocument;
    const {
        MUSTACHE_EXPR: de,
        ERB_EXPR: he,
        TMPLIT_EXPR: ge,
        DATA_ATTR: Te,
        ARIA_ATTR: ye,
        IS_SCRIPT_OR_DATA: Ee,
        ATTR_WHITESPACE: Ae,
        CUSTOM_ELEMENT: _e
    } = Z;
    let {
        IS_ALLOWED_URI: Se
    } = Z, Ne = null;
    const be = w({}, [...L, ...C, ...x, ...k, ...U]);
    let Re = null;
    const we = w({}, [...z, ...P, ...H, ...F]);
    let Oe = Object.seal(l(null, {
            tagNameCheck: {
                writable: !0,
                configurable: !1,
                enumerable: !0,
                value: null
            },
            attributeNameCheck: {
                writable: !0,
                configurable: !1,
                enumerable: !0,
                value: null
            },
            allowCustomizedBuiltInElements: {
                writable: !0,
                configurable: !1,
                enumerable: !0,
                value: !1
            }
        })),
        ve = null,
        De = null,
        Le = !0,
        Ce = !0,
        xe = !1,
        Ie = !0,
        ke = !1,
        Me = !0,
        Ue = !1,
        ze = !1,
        Pe = !1,
        He = !1,
        Fe = !1,
        Be = !1,
        We = !0,
        Ge = !1,
        Ye = !0,
        je = !1,
        Xe = {},
        qe = null;
    const $e = w({}, ["annotation-xml", "audio", "colgroup", "desc", "foreignobject", "head", "iframe", "math", "mi", "mn", "mo", "ms", "mtext", "noembed", "noframes", "noscript", "plaintext", "script", "style", "svg", "template", "thead", "title", "video", "xmp"]);
    let Ke = null;
    const Ve = w({}, ["audio", "video", "img", "source", "image", "track"]);
    let Ze = null;
    const Je = w({}, ["alt", "class", "for", "id", "label", "name", "pattern", "placeholder", "role", "summary", "title", "value", "style", "xmlns"]),
        Qe = "http://www.w3.org/1998/Math/MathML",
        et = "http://www.w3.org/2000/svg",
        tt = "http://www.w3.org/1999/xhtml";
    let nt = tt,
        ot = !1,
        rt = null;
    const it = w({}, [Qe, et, tt], g);
    let at = w({}, ["mi", "mo", "mn", "ms", "mtext"]),
        lt = w({}, ["annotation-xml"]);
    const ct = w({}, ["title", "style", "font", "a", "script"]);
    let st = null;
    const ut = ["application/xhtml+xml", "text/html"];
    let mt = null,
        pt = null;
    const ft = r.createElement("form"),
        dt = function(e) {
            return e instanceof RegExp || e instanceof Function
        },
        ht = function() {
            let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
            if (!pt || pt !== e) {
                if (e && "object" == typeof e || (e = {}), e = v(e), st = -1 === ut.indexOf(e.PARSER_MEDIA_TYPE) ? "text/html" : e.PARSER_MEDIA_TYPE, mt = "application/xhtml+xml" === st ? g : h, Ne = _(e, "ALLOWED_TAGS") ? w({}, e.ALLOWED_TAGS, mt) : be, Re = _(e, "ALLOWED_ATTR") ? w({}, e.ALLOWED_ATTR, mt) : we, rt = _(e, "ALLOWED_NAMESPACES") ? w({}, e.ALLOWED_NAMESPACES, g) : it, Ze = _(e, "ADD_URI_SAFE_ATTR") ? w(v(Je), e.ADD_URI_SAFE_ATTR, mt) : Je, Ke = _(e, "ADD_DATA_URI_TAGS") ? w(v(Ve), e.ADD_DATA_URI_TAGS, mt) : Ve, qe = _(e, "FORBID_CONTENTS") ? w({}, e.FORBID_CONTENTS, mt) : $e, ve = _(e, "FORBID_TAGS") ? w({}, e.FORBID_TAGS, mt) : {}, De = _(e, "FORBID_ATTR") ? w({}, e.FORBID_ATTR, mt) : {}, Xe = !!_(e, "USE_PROFILES") && e.USE_PROFILES, Le = !1 !== e.ALLOW_ARIA_ATTR, Ce = !1 !== e.ALLOW_DATA_ATTR, xe = e.ALLOW_UNKNOWN_PROTOCOLS || !1, Ie = !1 !== e.ALLOW_SELF_CLOSE_IN_ATTR, ke = e.SAFE_FOR_TEMPLATES || !1, Me = !1 !== e.SAFE_FOR_XML, Ue = e.WHOLE_DOCUMENT || !1, He = e.RETURN_DOM || !1, Fe = e.RETURN_DOM_FRAGMENT || !1, Be = e.RETURN_TRUSTED_TYPE || !1, Pe = e.FORCE_BODY || !1, We = !1 !== e.SANITIZE_DOM, Ge = e.SANITIZE_NAMED_PROPS || !1, Ye = !1 !== e.KEEP_CONTENT, je = e.IN_PLACE || !1, Se = e.ALLOWED_URI_REGEXP || X, nt = e.NAMESPACE || tt, at = e.MATHML_TEXT_INTEGRATION_POINTS || at, lt = e.HTML_INTEGRATION_POINTS || lt, Oe = e.CUSTOM_ELEMENT_HANDLING || {}, e.CUSTOM_ELEMENT_HANDLING && dt(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck) && (Oe.tagNameCheck = e.CUSTOM_ELEMENT_HANDLING.tagNameCheck), e.CUSTOM_ELEMENT_HANDLING && dt(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck) && (Oe.attributeNameCheck = e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck), e.CUSTOM_ELEMENT_HANDLING && "boolean" == typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (Oe.allowCustomizedBuiltInElements = e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements), ke && (Ce = !1), Fe && (He = !0), Xe && (Ne = w({}, U), Re = [], !0 === Xe.html && (w(Ne, L), w(Re, z)), !0 === Xe.svg && (w(Ne, C), w(Re, P), w(Re, F)), !0 === Xe.svgFilters && (w(Ne, x), w(Re, P), w(Re, F)), !0 === Xe.mathMl && (w(Ne, k), w(Re, H), w(Re, F))), e.ADD_TAGS && (Ne === be && (Ne = v(Ne)), w(Ne, e.ADD_TAGS, mt)), e.ADD_ATTR && (Re === we && (Re = v(Re)), w(Re, e.ADD_ATTR, mt)), e.ADD_URI_SAFE_ATTR && w(Ze, e.ADD_URI_SAFE_ATTR, mt), e.FORBID_CONTENTS && (qe === $e && (qe = v(qe)), w(qe, e.FORBID_CONTENTS, mt)), Ye && (Ne["#text"] = !0), Ue && w(Ne, ["html", "head", "body"]), Ne.table && (w(Ne, ["tbody"]), delete ve.tbody), e.TRUSTED_TYPES_POLICY) {
                    if ("function" != typeof e.TRUSTED_TYPES_POLICY.createHTML) throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');
                    if ("function" != typeof e.TRUSTED_TYPES_POLICY.createScriptURL) throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');
                    ae = e.TRUSTED_TYPES_POLICY, le = ae.createHTML("")
                } else void 0 === ae && (ae = function(e, t) {
                    if ("object" != typeof e || "function" != typeof e.createPolicy) return null;
                    let n = null;
                    const o = "data-tt-policy-suffix";
                    t && t.hasAttribute(o) && (n = t.getAttribute(o));
                    const r = "dompurify" + (n ? "#" + n : "");
                    try {
                        return e.createPolicy(r, {
                            createHTML: e => e,
                            createScriptURL: e => e
                        })
                    } catch (i) {
                        return null
                    }
                }(j, c)), null !== ae && "string" == typeof le && (le = ae.createHTML(""));
                i && i(e), pt = e
            }
        },
        gt = w({}, [...C, ...x, ...I]),
        Tt = w({}, [...k, ...M]),
        yt = function(e) {
            f(o.removed, {
                element: e
            });
            try {
                ie(e).removeChild(e)
            } catch (t) {
                V(e)
            }
        },
        Et = function(e, t) {
            try {
                f(o.removed, {
                    attribute: t.getAttributeNode(e),
                    from: t
                })
            } catch (n) {
                f(o.removed, {
                    attribute: null,
                    from: t
                })
            }
            if (t.removeAttribute(e), "is" === e)
                if (He || Fe) try {
                    yt(t)
                } catch (n) {} else try {
                    t.setAttribute(e, "")
                } catch (n) {}
        },
        At = function(e) {
            let t = null,
                n = null;
            if (Pe) e = "<remove></remove>" + e;
            else {
                const t = T(e, /^[\r\n\t ]+/);
                n = t && t[0]
            }
            "application/xhtml+xml" === st && nt === tt && (e = '<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>' + e + "</body></html>");
            const o = ae ? ae.createHTML(e) : e;
            if (nt === tt) try {
                t = (new Y).parseFromString(o, st)
            } catch (a) {}
            if (!t || !t.documentElement) {
                t = ce.createDocument(nt, "template", null);
                try {
                    t.documentElement.innerHTML = ot ? le : o
                } catch (a) {}
            }
            const i = t.body || t.documentElement;
            return e && n && i.insertBefore(r.createTextNode(n), i.childNodes[0] || null), nt === tt ? me.call(t, Ue ? "html" : "body")[0] : Ue ? t.documentElement : i
        },
        _t = function(e) {
            return se.call(e.ownerDocument || e, e, B.SHOW_ELEMENT | B.SHOW_COMMENT | B.SHOW_TEXT | B.SHOW_PROCESSING_INSTRUCTION | B.SHOW_CDATA_SECTION, null)
        },
        St = function(e) {
            return e instanceof G && ("string" != typeof e.nodeName || "string" != typeof e.textContent || "function" != typeof e.removeChild || !(e.attributes instanceof W) || "function" != typeof e.removeAttribute || "function" != typeof e.setAttribute || "string" != typeof e.namespaceURI || "function" != typeof e.insertBefore || "function" != typeof e.hasChildNodes)
        },
        Nt = function(e) {
            return "function" == typeof R && e instanceof R
        };

    function bt(e, t, n) {
        u(e, (e => {
            e.call(o, t, n, pt)
        }))
    }
    const Rt = function(e) {
            let t = null;
            if (bt(fe.beforeSanitizeElements, e, null), St(e)) return yt(e), !0;
            const n = mt(e.nodeName);
            if (bt(fe.uponSanitizeElement, e, {
                    tagName: n,
                    allowedTags: Ne
                }), e.hasChildNodes() && !Nt(e.firstElementChild) && S(/<[/\w!]/g, e.innerHTML) && S(/<[/\w!]/g, e.textContent)) return yt(e), !0;
            if (e.nodeType === ee) return yt(e), !0;
            if (Me && e.nodeType === te && S(/<[/\w]/g, e.data)) return yt(e), !0;
            if (!Ne[n] || ve[n]) {
                if (!ve[n] && Ot(n)) {
                    if (Oe.tagNameCheck instanceof RegExp && S(Oe.tagNameCheck, n)) return !1;
                    if (Oe.tagNameCheck instanceof Function && Oe.tagNameCheck(n)) return !1
                }
                if (Ye && !qe[n]) {
                    const t = ie(e) || e.parentNode,
                        n = re(e) || e.childNodes;
                    if (n && t) {
                        for (let o = n.length - 1; o >= 0; --o) {
                            const r = $(n[o], !0);
                            r.__removalCount = (e.__removalCount || 0) + 1, t.insertBefore(r, oe(e))
                        }
                    }
                }
                return yt(e), !0
            }
            return e instanceof O && ! function(e) {
                let t = ie(e);
                t && t.tagName || (t = {
                    namespaceURI: nt,
                    tagName: "template"
                });
                const n = h(e.tagName),
                    o = h(t.tagName);
                return !!rt[e.namespaceURI] && (e.namespaceURI === et ? t.namespaceURI === tt ? "svg" === n : t.namespaceURI === Qe ? "svg" === n && ("annotation-xml" === o || at[o]) : Boolean(gt[n]) : e.namespaceURI === Qe ? t.namespaceURI === tt ? "math" === n : t.namespaceURI === et ? "math" === n && lt[o] : Boolean(Tt[n]) : e.namespaceURI === tt ? !(t.namespaceURI === et && !lt[o]) && !(t.namespaceURI === Qe && !at[o]) && !Tt[n] && (ct[n] || !gt[n]) : !("application/xhtml+xml" !== st || !rt[e.namespaceURI]))
            }(e) ? (yt(e), !0) : "noscript" !== n && "noembed" !== n && "noframes" !== n || !S(/<\/no(script|embed|frames)/i, e.innerHTML) ? (ke && e.nodeType === Q && (t = e.textContent, u([de, he, ge], (e => {
                t = y(t, e, " ")
            })), e.textContent !== t && (f(o.removed, {
                element: e.cloneNode()
            }), e.textContent = t)), bt(fe.afterSanitizeElements, e, null), !1) : (yt(e), !0)
        },
        wt = function(e, t, n) {
            if (We && ("id" === t || "name" === t) && (n in r || n in ft)) return !1;
            if (Ce && !De[t] && S(Te, t));
            else if (Le && S(ye, t));
            else if (!Re[t] || De[t]) {
                if (!(Ot(e) && (Oe.tagNameCheck instanceof RegExp && S(Oe.tagNameCheck, e) || Oe.tagNameCheck instanceof Function && Oe.tagNameCheck(e)) && (Oe.attributeNameCheck instanceof RegExp && S(Oe.attributeNameCheck, t) || Oe.attributeNameCheck instanceof Function && Oe.attributeNameCheck(t)) || "is" === t && Oe.allowCustomizedBuiltInElements && (Oe.tagNameCheck instanceof RegExp && S(Oe.tagNameCheck, n) || Oe.tagNameCheck instanceof Function && Oe.tagNameCheck(n)))) return !1
            } else if (Ze[t]);
            else if (S(Se, y(n, Ae, "")));
            else if ("src" !== t && "xlink:href" !== t && "href" !== t || "script" === e || 0 !== E(n, "data:") || !Ke[e]) {
                if (xe && !S(Ee, y(n, Ae, "")));
                else if (n) return !1
            } else;
            return !0
        },
        Ot = function(e) {
            return "annotation-xml" !== e && T(e, _e)
        },
        vt = function(e) {
            bt(fe.beforeSanitizeAttributes, e, null);
            const {
                attributes: t
            } = e;
            if (!t || St(e)) return;
            const n = {
                attrName: "",
                attrValue: "",
                keepAttr: !0,
                allowedAttributes: Re,
                forceKeepAttr: void 0
            };
            let r = t.length;
            for (; r--;) {
                const a = t[r],
                    {
                        name: l,
                        namespaceURI: c,
                        value: s
                    } = a,
                    m = mt(l);
                let f = "value" === l ? s : A(s);
                if (n.attrName = m, n.attrValue = f, n.keepAttr = !0, n.forceKeepAttr = void 0, bt(fe.uponSanitizeAttribute, e, n), f = n.attrValue, !Ge || "id" !== m && "name" !== m || (Et(l, e), f = "user-content-" + f), Me && S(/((--!?|])>)|<\/(style|title)/i, f)) {
                    Et(l, e);
                    continue
                }
                if (n.forceKeepAttr) continue;
                if (Et(l, e), !n.keepAttr) continue;
                if (!Ie && S(/\/>/i, f)) {
                    Et(l, e);
                    continue
                }
                ke && u([de, he, ge], (e => {
                    f = y(f, e, " ")
                }));
                const d = mt(e.nodeName);
                if (wt(d, m, f)) {
                    if (ae && "object" == typeof j && "function" == typeof j.getAttributeType)
                        if (c);
                        else switch (j.getAttributeType(d, m)) {
                            case "TrustedHTML":
                                f = ae.createHTML(f);
                                break;
                            case "TrustedScriptURL":
                                f = ae.createScriptURL(f)
                        }
                    try {
                        c ? e.setAttributeNS(c, l, f) : e.setAttribute(l, f), St(e) ? yt(e) : p(o.removed)
                    } catch (i) {}
                }
            }
            bt(fe.afterSanitizeAttributes, e, null)
        },
        Dt = function e(t) {
            let n = null;
            const o = _t(t);
            for (bt(fe.beforeSanitizeShadowDOM, t, null); n = o.nextNode();) bt(fe.uponSanitizeShadowNode, n, null), Rt(n), vt(n), n.content instanceof s && e(n.content);
            bt(fe.afterSanitizeShadowDOM, t, null)
        };
    return o.sanitize = function(e) {
        let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
            n = null,
            r = null,
            i = null,
            l = null;
        if (ot = !e, ot && (e = "\x3c!--\x3e"), "string" != typeof e && !Nt(e)) {
            if ("function" != typeof e.toString) throw N("toString is not a function");
            if ("string" != typeof(e = e.toString())) throw N("dirty is not a string, aborting")
        }
        if (!o.isSupported) return e;
        if (ze || ht(t), o.removed = [], "string" == typeof e && (je = !1), je) {
            if (e.nodeName) {
                const t = mt(e.nodeName);
                if (!Ne[t] || ve[t]) throw N("root node is forbidden and cannot be sanitized in-place")
            }
        } else if (e instanceof R) n = At("\x3c!----\x3e"), r = n.ownerDocument.importNode(e, !0), r.nodeType === J && "BODY" === r.nodeName || "HTML" === r.nodeName ? n = r : n.appendChild(r);
        else {
            if (!He && !ke && !Ue && -1 === e.indexOf("<")) return ae && Be ? ae.createHTML(e) : e;
            if (n = At(e), !n) return He ? null : Be ? le : ""
        }
        n && Pe && yt(n.firstChild);
        const c = _t(je ? e : n);
        for (; i = c.nextNode();) Rt(i), vt(i), i.content instanceof s && Dt(i.content);
        if (je) return e;
        if (He) {
            if (Fe)
                for (l = ue.call(n.ownerDocument); n.firstChild;) l.appendChild(n.firstChild);
            else l = n;
            return (Re.shadowroot || Re.shadowrootmode) && (l = pe.call(a, l, !0)), l
        }
        let m = Ue ? n.outerHTML : n.innerHTML;
        return Ue && Ne["!doctype"] && n.ownerDocument && n.ownerDocument.doctype && n.ownerDocument.doctype.name && S(K, n.ownerDocument.doctype.name) && (m = "<!DOCTYPE " + n.ownerDocument.doctype.name + ">\n" + m), ke && u([de, he, ge], (e => {
            m = y(m, e, " ")
        })), ae && Be ? ae.createHTML(m) : m
    }, o.setConfig = function() {
        ht(arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}), ze = !0
    }, o.clearConfig = function() {
        pt = null, ze = !1
    }, o.isValidAttribute = function(e, t, n) {
        pt || ht({});
        const o = mt(e),
            r = mt(t);
        return wt(o, r, n)
    }, o.addHook = function(e, t) {
        "function" == typeof t && f(fe[e], t)
    }, o.removeHook = function(e, t) {
        if (void 0 !== t) {
            const n = m(fe[e], t);
            return -1 === n ? void 0 : d(fe[e], n, 1)[0]
        }
        return p(fe[e])
    }, o.removeHooks = function(e) {
        fe[e] = []
    }, o.removeAllHooks = function() {
        fe = {
            afterSanitizeAttributes: [],
            afterSanitizeElements: [],
            afterSanitizeShadowDOM: [],
            beforeSanitizeAttributes: [],
            beforeSanitizeElements: [],
            beforeSanitizeShadowDOM: [],
            uponSanitizeAttribute: [],
            uponSanitizeElement: [],
            uponSanitizeShadowNode: []
        }
    }, o
}();
export {
    oe as p
};