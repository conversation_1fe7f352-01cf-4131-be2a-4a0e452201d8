import {
    d as e,
    o as t,
    b as c
} from "./Cf0SOiw0.js";
const n = e => {
        const t = document.createElement("input");
        t.type = "file", t.accept = "image/*", t.style.display = "none", t.onchange = c => {
            const n = c.target.files[0];
            n && o(n, e), document.body.removeChild(t)
        }, document.body.appendChild(t), t.click()
    },
    o = async (e, t) => {
        if (e.size > 10485760) {
            throw new Error("Image size cannot exceed 10MB")
        }
        try {
            const c = await fetch("/api/get_upload_url");
            if (!c.ok) throw new Error(`HTTP error! status: ${c.status}`);
            const n = await c.json();
            if (0 !== n.status) throw new Error(`API error! code: ${n.code}`);
            const o = n.data.upload_image_url,
                a = n.data.page_url;
            if (!(await fetch(o, {
                    method: "PUT",
                    headers: {
                        "x-ms-blob-type": "BlockBlob"
                    },
                    body: e
                })).ok) throw new Error("Failed to upload image");
            return t && "function" == typeof t && t(a), a
        } catch (c) {
            throw c
        }
    },
    a = (e, t) => {
        if (!e) return () => {};
        const c = e,
            n = c.contentDocument || c.contentWindow.document;
        if (!n) return () => {};
        const a = async c => {
            var a;
            const r = c.clipboardData || (null == (a = c.originalEvent) ? void 0 : a.clipboardData);
            if (!r || !r.items) return;
            let i = null;
            for (const e of r.items)
                if ("file" === e.kind && e.type.startsWith("image/")) {
                    i = e.getAsFile();
                    break
                }
            if (!i) return;
            c.preventDefault();
            const l = n.createElement("span");
            l.textContent = "Uploading image...", l.style.color = "#666", l.style.fontStyle = "italic";
            const s = n.getSelection();
            if (s && s.rangeCount > 0) {
                const e = s.getRangeAt(0);
                e.deleteContents(), e.insertNode(l), e.collapse(!1)
            }
            try {
                const c = await o(i);
                l.parentNode && l.parentNode.removeChild(l), ((e, t) => {
                    if (!t) return;
                    const c = t,
                        n = c.contentDocument || c.contentWindow.document;
                    c.contentWindow.focus(), n.execCommand("insertHTML", !1, `<img src="${e}" style="max-width: 100%;" alt="Uploaded Image" />`)
                })(c, e), t && "function" == typeof t && t({
                    originalFile: i,
                    uploadedUrl: c,
                    success: !0
                })
            } catch (d) {
                l && l.parentNode && l.parentNode.removeChild(l);
                const e = n.createElement("span");
                e.textContent = "❌ Failed to upload image", e.style.color = "#ff4444", e.style.fontStyle = "italic";
                const c = n.getSelection();
                if (c && c.rangeCount > 0) {
                    const t = c.getRangeAt(0);
                    t.insertNode(e), t.collapse(!1)
                }
                t && "function" == typeof t && t({
                    originalFile: i,
                    uploadedUrl: null,
                    success: !1,
                    error: d
                })
            }
        };
        return n.addEventListener("paste", a), () => {
            n.removeEventListener("paste", a)
        }
    },
    r = {
        viewBox: "64 64 896 896",
        focusable: "false",
        "data-icon": "bold",
        width: "1em",
        height: "1em",
        fill: "currentColor",
        "aria-hidden": "true"
    };
const i = {
        render: function(n, o) {
            return t(), e("svg", r, o[0] || (o[0] = [c("path", {
                d: "M697.8 481.4c33.6-35 54.2-82.3 54.2-134.3v-10.2C752 229.3 663.9 142 555.3 142H259.4c-15.1 0-27.4 12.3-27.4 27.4v679.1c0 16.3 13.2 29.5 29.5 29.5h318.7c117 0 211.8-94.2 211.8-210.5v-11c0-73-37.4-137.3-94.2-175.1zM328 238h224.7c57.1 0 103.3 44.4 103.3 99.3v9.5c0 54.8-46.3 99.3-103.3 99.3H328V238zm366.6 429.4c0 62.9-51.7 113.9-115.5 113.9H328V542.7h251.1c63.8 0 115.5 51 115.5 113.9v10.8z"
            }, null, -1)]))
        }
    },
    l = {
        viewBox: "64 64 896 896",
        focusable: "false",
        "data-icon": "italic",
        width: "1em",
        height: "1em",
        fill: "currentColor",
        "aria-hidden": "true"
    };
const s = {
        render: function(n, o) {
            return t(), e("svg", l, o[0] || (o[0] = [c("path", {
                d: "M798 160H366c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h181.2l-156 544H229c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h432c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8H474.4l156-544H798c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"
            }, null, -1)]))
        }
    },
    d = {
        viewBox: "64 64 896 896",
        focusable: "false",
        "data-icon": "align-left",
        width: "1em",
        height: "1em",
        fill: "currentColor",
        "aria-hidden": "true"
    };
const u = {
        render: function(n, o) {
            return t(), e("svg", d, o[0] || (o[0] = [c("path", {
                d: "M120 230h496c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm0 424h496c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm784 140H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0-424H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"
            }, null, -1)]))
        }
    },
    h = {
        viewBox: "64 64 896 896",
        focusable: "false",
        "data-icon": "align-center",
        width: "1em",
        height: "1em",
        fill: "currentColor",
        "aria-hidden": "true"
    };
const f = {
        render: function(n, o) {
            return t(), e("svg", h, o[0] || (o[0] = [c("path", {
                d: "M264 230h496c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H264c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm496 424c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H264c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496zm144 140H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0-424H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"
            }, null, -1)]))
        }
    },
    v = {
        viewBox: "64 64 896 896",
        focusable: "false",
        "data-icon": "align-right",
        width: "1em",
        height: "1em",
        fill: "currentColor",
        "aria-hidden": "true"
    };
const m = {
    render: function(n, o) {
        return t(), e("svg", v, o[0] || (o[0] = [c("path", {
            d: "M904 158H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 424H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 212H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0-424H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"
        }, null, -1)]))
    }
};
export {
    u as A, i as B, s as I, f as a, m as b, n as h, a as s, o as u
};