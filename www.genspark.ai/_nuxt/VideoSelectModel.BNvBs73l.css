.unified-select-trigger[data-v-a26b99fd] {
    align-items: center;
    background-color: #fff;
    border: 1px solid #efefef;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    gap: 8px;
    height: 32px;
    justify-content: space-between;
    padding: 7px 12px;
    transition: background-color .2s;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.unified-select-trigger.small[data-v-a26b99fd] {
    font-size: 12px;
    height: 28px
}

.unified-select-trigger.large[data-v-a26b99fd] {
    font-size: 16px;
    height: 36px
}

.unified-select-trigger.active[data-v-a26b99fd],
.unified-select-trigger[data-v-a26b99fd]:hover {
    background-color: #f0f0f0
}

.selected-content[data-v-a26b99fd] {
    align-items: center;
    display: flex;
    gap: 8px
}

.selected-icon[data-v-a26b99fd] {
    align-items: center;
    border-radius: 50%;
    display: flex;
    height: 16px;
    justify-content: center;
    overflow: hidden;
    width: 16px
}

.selected-icon img[data-v-a26b99fd] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.selected-label[data-v-a26b99fd] {
    color: #909499
}

.trigger-icon[data-v-a26b99fd] {
    align-items: center;
    display: flex;
    transition: transform .2s
}

.trigger-icon.active[data-v-a26b99fd] {
    transform: rotate(180deg)
}

[data-v-a26b99fd] .n-popover.n-popover-shared.unified-select-popover {
    --n-padding: 12px 8px !important;
    padding: 12px 8px !important
}

.unified-select-dropdown[data-v-a26b99fd] {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 200px
}

.select-option[data-v-a26b99fd] {
    align-items: center;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    gap: 12px;
    padding: 8px 12px;
    position: relative;
    transition: background-color .2s
}

.select-option[data-v-a26b99fd]:hover:not(.disabled) {
    background-color: #f5f5f5
}

.select-option.active[data-v-a26b99fd] {
    background-color: transparent
}

.option-icon[data-v-a26b99fd] {
    align-items: center;
    border-radius: 50%;
    display: flex;
    height: 24px;
    justify-content: center;
    overflow: hidden;
    width: 24px
}

.option-icon img[data-v-a26b99fd] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.option-label[data-v-a26b99fd] {
    color: #232425;
    flex: 1
}

.checkmark[data-v-a26b99fd] {
    position: absolute;
    right: 12px
}

[data-v-a26b99fd] .n-checkbox.n-checkbox--checked {
    --n-box-color-checked: transparent !important;
    --n-check-mark-color: #232425 !important;
    --n-border-checked: 1px solid transparent !important;
    --n-color-checked: transparent !important
}

[data-v-a26b99fd] .n-checkbox {
    --n-box-color: transparent !important;
    --n-border: 1px solid transparent !important
}

@media (prefers-color-scheme:dark) {
    .unified-select-trigger[data-v-a26b99fd] {
        background-color: #333
    }
    .unified-select-trigger.active[data-v-a26b99fd],
    .unified-select-trigger[data-v-a26b99fd]:hover {
        background-color: #444
    }
    .selected-label[data-v-a26b99fd] {
        color: #ddd
    }
    .select-option[data-v-a26b99fd]:hover:not(.disabled) {
        background-color: #444
    }
    .select-option.active[data-v-a26b99fd] {
        background-color: transparent
    }
    .option-label[data-v-a26b99fd] {
        color: #ddd
    }
    [data-v-a26b99fd] .n-checkbox.n-checkbox--checked {
        --n-check-mark-color: #fff !important
    }
}

.user .content[data-v-5617fe9d] pre {
    background: none;
    margin: 0;
    padding: 0
}

.user .content[data-v-5617fe9d] pre code {
    font-family: sans-serif;
    white-space: pre-wrap
}

.conversation-content[data-v-5617fe9d] {
    background: transparent;
    color: #666;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding-bottom: 180px;
    position: relative
}

.conversation .role[data-v-5617fe9d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.conversation .avatar[data-v-5617fe9d] {
    height: 36px;
    width: 36px
}

.conversation .avatar img[data-v-5617fe9d] {
    width: 100%
}

.conversation .name[data-v-5617fe9d] {
    color: #252525;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal
}

.conversation-item-desc .bubble[data-v-5617fe9d] {
    align-items: flex-start;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    justify-content: flex-start;
    line-height: 20px;
    padding: 12px 16px
}

.conversation-item-desc .bubble code[data-v-5617fe9d],
.conversation-item-desc .bubble pre[data-v-5617fe9d] {
    font-size: 16px
}

.conversation-item-desc .button[data-v-5617fe9d] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 2px;
    line-height: normal;
    padding: 4px 8px
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc .button[data-v-5617fe9d] {
        border-color: #252525;
        color: #ddd
    }
}

.conversation-item-desc .button .icon[data-v-5617fe9d] {
    display: flex
}

@keyframes blink-animation-5617fe9d {
    0%,
    to {
        color: #000
    }
    50% {
        color: transparent
    }
}

@media (prefers-color-scheme:dark) {
    @keyframes blink-animation-5617fe9d {
        0%,
        to {
            color: #fff
        }
        50% {
            color: transparent
        }
    }
}

.conversation-item-desc.assistant .bubble[data-v-5617fe9d] .cursor {
    animation: blink-animation-5617fe9d .5s infinite;
    color: #606366;
    font-size: 12px
}

.conversation-item-desc[data-v-5617fe9d] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-width: 260px;
    width: 100%
}

.conversation-item-desc.user[data-v-5617fe9d] {
    align-items: flex-end;
    margin-left: 30px
}

.conversation-item-desc.assistant .bubble[data-v-5617fe9d] {
    border: 1px solid transparent;
    border-radius: 16px;
    min-width: 260px;
    padding-left: 0;
    padding-right: 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.retry[data-v-5617fe9d],
.conversation-item-desc.assistant .bubble.try_moa[data-v-5617fe9d] {
    align-items: center;
    background: #fafafa;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 18px 16px
}

.conversation-item-desc .retry .button[data-v-5617fe9d],
.conversation-item-desc .try_moa .button[data-v-5617fe9d] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 8px 16px
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.retry[data-v-5617fe9d],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-5617fe9d] {
        flex-direction: column
    }
}

.models-list[data-v-5617fe9d] {
    background-color: #fff;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    padding: 20px 8px
}

.models-list .model[data-v-5617fe9d] {
    color: #232425;
    margin-top: 4px
}

.models-list .model .row[data-v-5617fe9d] {
    border-radius: 8px;
    color: #232425;
    gap: 4px;
    justify-content: space-between
}

.models-list .model .row[data-v-5617fe9d],
.models-selected[data-v-5617fe9d] {
    display: flex;
    flex-direction: row;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.models-selected[data-v-5617fe9d] {
    background-color: #f4f4f4;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 150%;
    padding: 6px 12px
}

@media (prefers-color-scheme:dark) {
    .models-list[data-v-5617fe9d] {
        background-color: #252525
    }
    .models-list .model .row[data-v-5617fe9d] {
        color: #ddd
    }
    .conversation-item-desc.assistant .bubble[data-v-5617fe9d] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble.retry[data-v-5617fe9d],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-5617fe9d] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .retry .button[data-v-5617fe9d],
    .conversation-item-desc .try_moa .button[data-v-5617fe9d] {
        background: #fafafa;
        color: #232425
    }
}

.controls[data-v-5617fe9d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden
}

.controls[data-v-5617fe9d]::-webkit-scrollbar {
    display: none
}

.permission-wrapper[data-v-5617fe9d] {
    display: flex;
    justify-content: center;
    width: 100%
}

.empty-placeholder[data-v-5617fe9d] {
    color: #232425;
    font-family: Arial;
    font-size: 30px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin-top: 30vh;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .empty-placeholder[data-v-5617fe9d] {
        color: #fff
    }
}

.general-chat-wrapper[data-v-5617fe9d] {
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
    position: relative
}

.chat-wrapper[data-v-5617fe9d],
.general-chat-wrapper[data-v-5617fe9d] {
    box-sizing: border-box;
    display: flex;
    width: 100%
}

.chat-wrapper[data-v-5617fe9d] {
    align-items: flex-start;
    justify-content: center;
    overflow-x: hidden;
    padding: 16px 16px 100px
}

.chat-content-wrapper[data-v-5617fe9d] {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    max-width: 100%;
    width: var(--container-width, 680px)
}

.input-wrapper-wrapper[data-v-5617fe9d] {
    align-items: center;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-bottom: 4px;
    padding-top: 16px;
    width: 100%;
    z-index: 100
}

.input-wrapper[data-v-5617fe9d] {
    width: var(--container-width, 680px)
}

.conversation-wrapper[data-v-5617fe9d] {
    background-color: transparent;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    max-width: 100%;
    min-height: 200px;
    padding-bottom: 120px;
    width: var(--container-width, 680px)
}

.conversation-statement[data-v-5617fe9d] {
    display: flex;
    flex-direction: column;
    list-style: none;
    margin: 10px 0
}

.conversation-statement.user[data-v-5617fe9d] {
    align-items: flex-end
}

.conversation-statement.assistant+.conversation-statement.user[data-v-5617fe9d],
.conversation-statement.user+.conversation-statement.assistant[data-v-5617fe9d] {
    margin-top: 16px
}

.conversation-statement[data-v-5617fe9d]:first-child {
    margin-top: 5px
}

.conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-5617fe9d] {
    border: 1px solid #efefef;
    padding: 10px 16px;
    width: 100%
}

.conversation-item-desc.user .bubble[data-v-5617fe9d] {
    background: #f5f5f5;
    border-radius: 16px;
    color: #232425;
    margin-right: 2px;
    max-width: 70%
}

.conversation-item-desc.user .content-item[data-v-5617fe9d] {
    align-items: flex-end;
    display: flex
}

.conversation-item-desc.assistant .bubble[data-v-5617fe9d] {
    box-sizing: border-box;
    -webkit-hyphens: auto;
    hyphens: auto;
    max-width: 100%;
    padding: 0
}

.conversation-item-desc.assistant .bubble.text[data-v-5617fe9d] {
    margin: 24px 0 16px;
    padding: 10px 0
}

.conversation-item-desc.assistant .bubble[data-v-5617fe9d]:not(:first-child) {
    margin-top: 20px
}

.conversation-item-desc.user .bubble[data-v-5617fe9d]:not(:first-child) {
    margin-top: 12px
}

.conversation-item-desc .bubble .loading[data-v-5617fe9d],
.conversation-item-desc .bubble[data-v-5617fe9d] .cursor {
    display: none
}

.conversation-item-desc.assistant .bubble.thinking[data-v-5617fe9d] .cursor {
    display: inline
}

.conversation-item-desc .desc[data-v-5617fe9d] {
    display: flex;
    flex-direction: row;
    max-width: 100%
}

.conversation-item-desc .content[data-v-5617fe9d] {
    max-width: 100%
}

.creating-image-tasks[data-v-5617fe9d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    justify-content: center
}

.creating-image-tasks .text[data-v-5617fe9d] {
    color: #232425;
    font-size: 14px;
    line-height: 150%
}

.creating-image-tasks .model-icon-wrapper[data-v-5617fe9d] {
    align-items: center;
    background-color: #f0f0f0;
    border-radius: 50%;
    display: flex;
    flex-direction: row;
    gap: 4px;
    height: 18px;
    overflow: hidden;
    width: 18px
}

.creating-image-tasks .model-icon-wrapper .model-icon[data-v-5617fe9d] {
    height: 100%;
    width: 100%
}

.array-content-wrapper[data-v-5617fe9d] {
    align-items: flex-end;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%
}

.remix-button[data-v-5617fe9d] {
    align-items: center;
    background: #00000065;
    border-radius: 18px;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    line-height: 20px;
    opacity: 0;
    padding: 4px 12px;
    position: absolute;
    right: 8px;
    top: 8px;
    transition: transform .3s, opacity .3s;
    z-index: 2
}

.remix-button[data-v-5617fe9d]:hover {
    transform: scale(1.05)
}

@media (hover:hover) {
    .image-item:hover .remix-button[data-v-5617fe9d] {
        opacity: 1
    }
}

@media (hover:none) {
    .remix-button[data-v-5617fe9d] {
        opacity: 1
    }
}

.models-wrapper[data-v-5617fe9d] {
    width: -moz-fit-content;
    width: fit-content
}

.models-list[data-v-5617fe9d] {
    gap: 4px
}

.models-selected[data-v-5617fe9d] {
    align-items: center;
    background-color: transparent;
    border: 1px solid #efefef;
    box-sizing: border-box;
    justify-content: center;
    transition: all .2s ease
}

.models-selected[data-v-5617fe9d]:hover {
    background-color: #f0f0f0
}

.models-selected .model-selected[data-v-5617fe9d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.models-selected .model-selected .icon[data-v-5617fe9d] {
    align-items: center;
    background-color: transparent;
    border-radius: 50%;
    display: flex;
    height: 16px;
    justify-content: center;
    overflow: hidden;
    width: 16px
}

.models-selected .model-selected .icon[data-v-5617fe9d] svg {
    height: 16px;
    width: 16px
}

.models-selected .model-selected .icon img[data-v-5617fe9d] {
    height: 16px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 16px
}

.models-list .model[data-v-5617fe9d] {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 10px 6px 10px 10px
}

.models-list .model[data-v-5617fe9d]:first-child {
    margin-top: 0
}

.models-list .divider[data-v-5617fe9d] {
    background-color: #efefef;
    height: 1px;
    margin: 6px 0;
    width: 100%
}

.models-list .model .row[data-v-5617fe9d] {
    box-sizing: border-box;
    color: #606366;
    height: 20px;
    width: 100%
}

.models-list .model .description[data-v-5617fe9d] {
    color: #909499;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px
}

@media (hover:hover) {
    .models-list .model[data-v-5617fe9d]:hover {
        background-color: #f5f5f5;
        border-radius: 12px
    }
}

.models-list .model .left[data-v-5617fe9d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 10px;
    position: relative
}

.models-list .model .left .icon[data-v-5617fe9d] {
    align-items: center;
    background-color: transparent;
    border-radius: 50%;
    display: flex;
    height: 20px;
    justify-content: center;
    overflow: hidden;
    width: 20px
}

.models-list .model .left .icon[data-v-5617fe9d] svg {
    height: 20px;
    width: 20px
}

.models-list .model .left .icon img[data-v-5617fe9d] {
    height: 20px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 20px
}

.models-list .model .right[data-v-5617fe9d] {
    align-items: center;
    display: flex;
    flex-direction: row
}

.models-selected.remix-selector .icon[data-v-5617fe9d] svg {
    height: 16px;
    width: 16px
}

.models-selected.remix-selector .icon[data-v-5617fe9d] svg path {
    fill: #232425
}

@media (prefers-color-scheme:dark) {
    .models-list .model .row[data-v-5617fe9d] {
        color: #ddd
    }
    .models-selected.aspect-ratio-selector[data-v-5617fe9d],
    .models-selected.style-selector[data-v-5617fe9d] {
        border: 1px solid #efefef30;
        color: #fff
    }
    @media (hover:hover) {
        .models-selected.aspect-ratio-selector[data-v-5617fe9d]:hover,
        .models-selected.style-selector[data-v-5617fe9d]:hover {
            background-color: #f0f0f010
        }
        .models-list .model:hover .row[data-v-5617fe9d] {
            background-color: #333
        }
    }
    .models-list .model .description[data-v-5617fe9d] {
        color: #909499
    }
    .models-list .divider[data-v-5617fe9d] {
        background-color: #333
    }
    .remix-selector[data-v-5617fe9d] {
        border: 1px solid #efefef30;
        color: #fff
    }
    .remix-selector[data-v-5617fe9d]:hover {
        background-color: #f0f0f010
    }
}

.model-response-wrapper .title[data-v-5617fe9d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 4px
}

.model-response-wrapper .title .icon[data-v-5617fe9d] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    margin-right: 5px;
    width: 16px
}

.model-response-wrapper .status .icon[data-v-5617fe9d] {
    margin-right: 0
}

.model-response-wrapper .title .icon img[data-v-5617fe9d] {
    height: 100%;
    width: 100%
}

.model-response-wrapper[data-v-5617fe9d] {
    position: relative;
    width: 100%
}

.model-response-wrapper .content[data-v-5617fe9d] {
    transition: all .3s ease-in-out
}

.model-response-wrapper .content.no-expand[data-v-5617fe9d] {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    line-height: 30px;
    max-height: 0;
    overflow: hidden
}

.model-response-wrapper.thinking .content.no-expand[data-v-5617fe9d] {
    max-height: 90px
}

.model-response-wrapper.expanded .content[data-v-5617fe9d] {
    max-height: unset;
    padding-top: 20px
}

.model-response .title .status .icon.finished[data-v-5617fe9d] {
    color: #5cd4a1
}

.model-response .title .status .icon[data-v-5617fe9d] svg {
    height: 100%;
    width: 100%
}

.model-response-wrapper .expand-button[data-v-5617fe9d] {
    cursor: pointer;
    height: 20px;
    position: absolute;
    right: 10px;
    top: 12px;
    width: 20px
}

.model-response .divider[data-v-5617fe9d] {
    background-color: #efefef;
    height: 1px;
    margin-bottom: 20px
}

.model-response[data-v-5617fe9d] {
    color: #232425;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    width: 100%
}

.models-list[data-v-5617fe9d] .n-checkbox.n-checkbox--checked {
    --n-box-color-checked: transparent !important;
    --n-check-mark-color: #232425 !important;
    --n-border-checked: 1px solid transparent !important;
    --n-color-checked: transparent !important
}

.models-list[data-v-5617fe9d] .n-checkbox {
    --n-box-color: transparent !important;
    --n-border: 1px solid transparent !important
}

.reflection-toggle[data-v-5617fe9d] .n-checkbox.n-checkbox--show-label {
    align-items: center;
    display: flex;
    flex-direction: row
}

[data-v-5617fe9d] .models-selected {
    border-radius: 8px
}

.options-wrapper[data-v-5617fe9d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 8px
}

.aspect-ratio-selector[data-v-5617fe9d],
.gallery-toggle .icon[data-v-5617fe9d],
.models-selected[data-v-5617fe9d],
.reflection-toggle[data-v-5617fe9d],
.upload-attachments .icon[data-v-5617fe9d] {
    flex-shrink: 0;
    white-space: nowrap
}

.option[data-v-5617fe9d],
.personalize-options[data-v-5617fe9d],
.personalize-selected[data-v-5617fe9d] {
    align-items: center;
    display: flex
}

.personalize-selected[data-v-5617fe9d] {
    background-color: #fafafa;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    height: 28px;
    justify-content: space-between;
    line-height: 150%;
    padding: 0 12px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.image-content[data-v-5617fe9d] {
    border-radius: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-top: 12px;
    max-width: 40%;
    width: 100%
}

.gallery-toggle .icon[data-v-5617fe9d] {
    align-items: center;
    border-radius: 16px;
    color: #232425;
    cursor: pointer;
    display: flex;
    height: 30px;
    justify-content: center;
    transition: background-color .2s;
    width: 20px
}

.gallery-toggle .icon.active[data-v-5617fe9d] {
    background-color: #eef3ff
}

.expand-button[data-v-5617fe9d] {
    transform: rotate(180deg)
}

.expanded .expand-button[data-v-5617fe9d] {
    transform: rotate(0)
}

.upload-attachments .icon[data-v-5617fe9d] {
    align-items: center;
    border-radius: 16px;
    color: #232425;
    cursor: pointer;
    display: flex;
    height: 20px;
    justify-content: center;
    transition: transform .3s;
    width: 20px
}

.upload-attachments .icon[data-v-5617fe9d]:hover {
    transform: scale(1.1)
}

.content-item[data-v-5617fe9d] {
    align-items: center;
    display: flex;
    flex-direction: column;
    margin: 8px 0;
    width: 100%
}

.image-content img[data-v-5617fe9d] {
    border-radius: 8px;
    max-width: 100%;
    -o-object-fit: contain;
    object-fit: contain
}

.text-content[data-v-5617fe9d] {
    color: inherit;
    line-height: 1.5
}

.extra-options[data-v-5617fe9d] {
    flex-direction: row;
    gap: 12px;
    height: 30px
}

.extra-options[data-v-5617fe9d],
.profile-avatar[data-v-5617fe9d] {
    align-items: center;
    display: flex
}

.profile-avatar[data-v-5617fe9d] {
    background-color: #f5f5f5;
    border-radius: 50%;
    height: 32px;
    justify-content: center;
    overflow: hidden;
    width: 32px
}

.profile-avatar img[data-v-5617fe9d] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.profile-avatar .dot[data-v-5617fe9d] {
    background-color: #606366;
    border-radius: 50%;
    height: 8px;
    width: 8px
}

.personalize-list[data-v-5617fe9d] {
    min-width: 240px;
    padding: 8px
}

.profile-option[data-v-5617fe9d] {
    align-items: center;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    gap: 12px;
    padding: 12px;
    transition: background-color .2s
}

.profile-option[data-v-5617fe9d]:hover {
    background-color: #f5f5f5
}

.profile-option.active[data-v-5617fe9d] {
    background-color: #eef3ff
}

.profile-name[data-v-5617fe9d] {
    color: #232425;
    flex: 1;
    font-size: 14px
}

[data-v-5617fe9d] .moa-image-side-pannel {
    bottom: auto;
    left: auto;
    margin-top: 16px;
    max-height: 300px;
    overflow-y: auto;
    position: relative;
    transform: none;
    width: 100%
}

.side-panel-container[data-v-5617fe9d] {
    border-radius: 8px;
    margin-top: 16px;
    overflow: hidden
}

.hd-toggle[data-v-5617fe9d],
.trigger-icon[data-v-5617fe9d] {
    align-items: center;
    display: flex
}

.trigger-icon[data-v-5617fe9d] {
    transition: transform .2s
}

.trigger-icon.active[data-v-5617fe9d] {
    transform: rotate(180deg)
}

.moa-title[data-v-5617fe9d] {
    color: #ccc;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin: 24px 0 8px
}

.assistant-message-divider[data-v-5617fe9d] {
    background-color: #efefef;
    height: 1px;
    margin: 50px 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.try_moa[data-v-5617fe9d] {
    font-size: 16px;
    line-height: 1.5em;
    width: 100%
}

.conversation-item-desc .try_moa .button[data-v-5617fe9d] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    line-height: 1.5em;
    min-width: -moz-fit-content;
    min-width: fit-content;
    padding: 7.5px 16px;
    transition: transform .2s;
    white-space: nowrap
}

.conversation-item-desc .try_moa .button[data-v-5617fe9d]:hover {
    transform: scale(1.05)
}

.reflection-toggle[data-v-5617fe9d] {
    align-items: center;
    background-color: transparent;
    border: 1px solid #efefef;
    border-radius: 8px;
    box-sizing: border-box;
    display: flex;
    padding: 6px 12px;
    white-space: nowrap
}

.reflection-toggle[data-v-5617fe9d] .n-checkbox-box {
    height: 16px;
    width: 16px
}

.reflection-toggle[data-v-5617fe9d] .n-checkbox__label {
    color: #606366;
    font-size: 14px;
    font-weight: 400;
    line-height: 21px
}

.reflection-toggle[data-v-5617fe9d] .n-checkbox--show-label {
    line-height: 16px
}

@media (prefers-color-scheme:dark) {
    .input-wrapper-wrapper[data-v-5617fe9d] {
        background-color: #232425
    }
    .reflection-toggle[data-v-5617fe9d]:hover {
        background-color: #f0f0f010
    }
    .models-selected .model-selected .icon[data-v-5617fe9d] {
        background-color: #fff
    }
    .models-list[data-v-5617fe9d],
    .n-popover.models-popover[data-v-5617fe9d] {
        background-color: #252525
    }
    .models-list .model .left .text[data-v-5617fe9d] {
        color: #ddd
    }
    .models-list .model .left .icon[data-v-5617fe9d] {
        background-color: #efefef
    }
    @media (hover:hover) {
        .models-list .model[data-v-5617fe9d]:hover,
        .models-list .model:hover .row[data-v-5617fe9d] {
            background-color: #333
        }
    }
    .creating-image-tasks .text[data-v-5617fe9d] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble[data-v-5617fe9d],
    .conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-5617fe9d] {
        background: none;
        box-sizing: border-box;
        color: #fff
    }
    .conversation-item-desc.user .bubble[data-v-5617fe9d] {
        background: #eee;
        color: #000
    }
    .conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-5617fe9d] {
        background-color: #444
    }
    .model-response[data-v-5617fe9d] {
        color: #ddd
    }
    .section-title[data-v-5617fe9d] {
        color: #666
    }
    .upload-attachments .icon[data-v-5617fe9d],
    .personalize-options[data-v-5617fe9d] .n-radio {
        color: #ddd
    }
    .personalize-selected[data-v-5617fe9d] {
        background-color: #333;
        color: #ddd
    }
    .profile-avatar[data-v-5617fe9d],
    .profile-option[data-v-5617fe9d]:hover {
        background-color: #333
    }
    .profile-option.active[data-v-5617fe9d] {
        background-color: #444
    }
    .profile-name[data-v-5617fe9d] {
        color: #ddd
    }
    .profile-avatar .dot[data-v-5617fe9d] {
        background-color: #ddd
    }
    .gallery-toggle .icon[data-v-5617fe9d] {
        background-color: #333;
        color: #ddd
    }
    .gallery-toggle .icon.active[data-v-5617fe9d] {
        background-color: #444
    }
    .hd-toggle[data-v-5617fe9d] .n-checkbox {
        color: #ddd
    }
    .models-list[data-v-5617fe9d] .n-checkbox.n-checkbox--checked {
        --n-check-mark-color: #ddd !important
    }
    .reflection-toggle[data-v-5617fe9d] {
        background-color: #232425;
        border: 1px solid #efefef30
    }
    .reflection-toggle[data-v-5617fe9d] .n-checkbox {
        --n-border: 1px solid #444;
        --n-box-color-checked: #0f7fff;
        --n-check-mark-color: #fff;
        --n-border-checked: 1px solid #0f7fff
    }
    .reflection-toggle[data-v-5617fe9d] .n-checkbox__label {
        color: #fff
    }
}

.model-divider[data-v-5617fe9d] {
    background-color: #efefef;
    height: 1px;
    margin: 4px 12px
}

@media (prefers-color-scheme:dark) {
    .model-divider[data-v-5617fe9d] {
        background-color: #333
    }
}

.reflection-toggle[data-v-5617fe9d] .n-checkbox {
    --n-box-color: #fff !important;
    --n-border: 1px solid #efefef !important;
    --n-border-checked: 1px solid #0f7fff !important;
    --n-border-focus: 1px solid #0f7fff !important;
    --n-border-disabled: 1px solid #e0e0e6 !important;
    --n-border-disabled-checked: 1px solid #e0e0e6 !important;
    --n-box-shadow-focus: 0 0 0 2px rgba(15, 127, 255, .3) !important;
    --n-color: transparent !important;
    --n-color-checked: #0f7fff !important
}

.reflection-toggle[data-v-5617fe9d] .n-checkbox:hover .n-checkbox-box .n-checkbox-box__border {
    border: 1px solid #0f7fff !important
}

.reflection-toggle[data-v-5617fe9d] .n-checkbox:focus:not(:active) .n-checkbox-box .n-checkbox-box__border {
    border: 1px solid #0f7fff !important;
    box-shadow: 0 0 0 2px #0f7fff4d !important
}

.reflection-toggle[data-v-5617fe9d] .n-checkbox.n-checkbox--checked {
    --n-box-color-checked: #0f7fff !important;
    --n-check-mark-color: #fff !important;
    --n-border-checked: 1px solid #0f7fff !important;
    --n-color-checked: #0f7fff !important
}

.task-status[data-v-5617fe9d] {
    color: #666;
    font-size: 12px;
    margin-top: 8px
}

.status-complete[data-v-5617fe9d] {
    color: #4caf50
}

.status-pending[data-v-5617fe9d] {
    color: #2196f3
}

.models-list .model .right input[type=radio][data-v-5617fe9d] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    cursor: pointer;
    height: 16px;
    margin: 0;
    position: relative;
    width: 16px
}

.models-list .model .right input[type=radio][data-v-5617fe9d]:checked {
    background-color: #fff;
    border-color: #0f7fff
}

.models-list .model .right input[type=radio][data-v-5617fe9d]:checked:after {
    background: #0f7fff;
    border-radius: 50%;
    content: "";
    height: 10px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 10px
}

@media (prefers-color-scheme:dark) {
    .models-list .model .right input[type=radio][data-v-5617fe9d] {
        background-color: #fff;
        border-color: #666
    }
    .models-list .model .right input[type=radio][data-v-5617fe9d]:checked {
        background-color: #fff;
        border-color: #0f7fff
    }
}

.models-wrapper .models-selected[data-v-5617fe9d] {
    background-color: #f4f4f4;
    border: 1px solid #f4f4f4;
    border-radius: 16.5px
}

.models-wrapper .models-selected[data-v-5617fe9d]:hover {
    background-color: #e8e8e8;
    border: 1px solid #e8e8e8
}

@media (prefers-color-scheme:dark) {
    .aspect-ratio-selector .models-selected[data-v-5617fe9d] {
        background-color: #333;
        border-color: #444
    }
    .aspect-ratio-selector .models-selected[data-v-5617fe9d]:hover {
        background-color: #444
    }
    .conversation-item-desc.assistant .bubble.try_moa[data-v-5617fe9d] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .try_moa .button[data-v-5617fe9d] {
        background: #fafafa;
        color: #232425
    }
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.try_moa[data-v-5617fe9d] {
        flex-direction: column
    }
    .chat-wrapper[data-v-5617fe9d] {
        box-sizing: border-box;
        padding: 16px 16px 100px
    }
    .input-wrapper-wrapper[data-v-5617fe9d] {
        bottom: 0;
        box-sizing: border-box;
        left: 0;
        padding: 16px 16px 40px;
        position: fixed;
        width: 100%
    }
    .input-wrapper[data-v-5617fe9d],
    .input-wrapper-wrapper-inner[data-v-5617fe9d] {
        width: 100%
    }
    [data-v-5617fe9d] .moa-image-side-pannel {
        bottom: auto;
        left: auto;
        max-height: 200px;
        transform: none;
        width: 100%
    }
}

.task-content[data-v-5617fe9d] {
    margin: 0
}

.task-content[data-v-5617fe9d] h3 {
    color: #232425;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    margin: 12px 0
}

.prompt-box[data-v-5617fe9d] {
    border-radius: 4px;
    color: #909499;
    font-size: 16px;
    line-height: 1.5;
    margin: 12px 0;
    padding: 12px 16px
}

.prompt-box-title[data-v-5617fe9d] {
    font-weight: 700;
    margin-bottom: 12px
}

.task-box-divider[data-v-5617fe9d] {
    background-color: #efefef;
    height: 1px;
    margin: 40px 0
}

@media (prefers-color-scheme:dark) {
    .task-content[data-v-5617fe9d] h3 {
        color: #fff
    }
    .prompt-box[data-v-5617fe9d] {
        background-color: #333;
        border-left-color: #0f7fff;
        color: #ddd
    }
}

.llm-model-toggle[data-v-5617fe9d] {
    align-items: center;
    background-color: #fff;
    border-radius: 8px;
    box-sizing: border-box;
    display: flex;
    height: 28px
}

@media (prefers-color-scheme:dark) {
    .llm-model-toggle[data-v-5617fe9d] {
        background-color: #333
    }
    .llm-model-toggle[data-v-5617fe9d] .n-select {
        --n-color: #333;
        --n-text-color: #ddd
    }
}

.style-cards[data-v-5617fe9d] {
    background: #fff;
    border-radius: 12px;
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(3, 1fr);
    padding: 16px
}

.style-card[data-v-5617fe9d] {
    align-items: center;
    border: 2px solid transparent;
    border-radius: 10px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    transition: all .2s ease
}

.style-card[data-v-5617fe9d]:hover {
    background: #ffffff1a;
    transform: scale(1.05)
}

.style-card.active[data-v-5617fe9d] {
    background: #0f7fff1a;
    border-color: #0f7fff
}

.style-image[data-v-5617fe9d] {
    background: #333;
    border-radius: 8px;
    height: 101px;
    overflow: hidden;
    position: relative;
    width: 72px
}

.style-image[data-v-5617fe9d]:after {
    background: linear-gradient(180deg, transparent, transparent 50%, rgba(0, 0, 0, .6));
    bottom: 0;
    content: "";
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 0
}

.style-image img[data-v-5617fe9d] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.placeholder-image[data-v-5617fe9d] {
    align-items: center;
    color: #666;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%
}

.style-label[data-v-5617fe9d] {
    bottom: 4px;
    color: #fff;
    font-size: 12px;
    left: 0;
    line-height: 1.5;
    padding: 0 4px;
    position: absolute;
    right: 0;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .style-cards[data-v-5617fe9d] {
        background: #232425
    }
    .style-card[data-v-5617fe9d]:hover {
        background: #ffffff1a
    }
}

.model-preference-tip[data-v-5617fe9d] {
    align-items: center;
    background: #0f7fff0d;
    border-radius: 8px;
    color: #666;
    display: flex;
    font-size: 14px;
    gap: 8px;
    line-height: 1.4;
    margin: 20px 0;
    padding: 12px
}

.tip-icon[data-v-5617fe9d] {
    align-items: center;
    color: #0f7fff;
    display: flex;
    flex-shrink: 0;
    height: 16px;
    width: 16px
}

.tip-text[data-v-5617fe9d] {
    flex: 1
}

@media (prefers-color-scheme:dark) {
    .model-preference-tip[data-v-5617fe9d] {
        background: #0f7fff1a;
        color: #999
    }
}

.conversation-item-desc.assistant .bubble.try_moa .left[data-v-5617fe9d] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.conversation-item-desc.assistant .bubble.try_moa .left .main-text[data-v-5617fe9d] {
    font-size: 16px;
    line-height: 1.5em
}

.conversation-item-desc.assistant .bubble.try_moa .left .sub-text[data-v-5617fe9d] {
    color: #666;
    font-size: 14px;
    line-height: 1.4em
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc.assistant .bubble.try_moa .left .sub-text[data-v-5617fe9d] {
        color: #999
    }
}

.slide-left-enter-active[data-v-5617fe9d],
.slide-left-leave-active[data-v-5617fe9d],
.slide-right-enter-active[data-v-5617fe9d],
.slide-right-leave-active[data-v-5617fe9d] {
    transition: transform .3s ease
}

.slide-left-enter-from[data-v-5617fe9d] {
    transform: translate(100%)
}

.slide-left-leave-to[data-v-5617fe9d],
.slide-right-enter-from[data-v-5617fe9d] {
    transform: translate(-100%)
}

.slide-right-leave-to[data-v-5617fe9d] {
    transform: translate(100%)
}

[data-v-5617fe9d] .moa-image-detail {
    overflow: hidden;
    position: relative
}

[data-v-5617fe9d] .moa-image-detail img {
    transition: transform .3s ease;
    will-change: transform
}

[data-v-5617fe9d] .moa-image-detail {
    touch-action: pan-y pinch-zoom;
    -moz-user-select: none;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none
}

[data-v-5617fe9d] .moa-image-detail img {
    pointer-events: none
}

.slide-left-enter-active[data-v-5617fe9d],
.slide-left-leave-active[data-v-5617fe9d],
.slide-right-enter-active[data-v-5617fe9d],
.slide-right-leave-active[data-v-5617fe9d] {
    height: 100%;
    position: absolute;
    transition: transform .3s cubic-bezier(.4, 0, .2, 1);
    width: 100%
}

.input-wrapper-wrapper-inner[data-v-5617fe9d] {
    margin: 0 auto;
    max-width: 100%;
    position: relative;
    width: var(--container-width, 680px)
}

.media-gallery-container[data-v-5617fe9d] {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 10px #00000026;
    margin-bottom: 16px;
    overflow-y: auto;
    position: absolute
}

@media (prefers-color-scheme:dark) {
    .media-gallery-container[data-v-5617fe9d] {
        background-color: #2a2a2a;
        border-color: #333
    }
}

.images-row[data-v-5617fe9d] {
    align-items: flex-end;
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
    overflow-x: auto;
    padding-bottom: 4px;
    width: 100%
}

.image-item[data-v-5617fe9d] {
    flex: 0 0 auto;
    max-width: 40%;
    position: relative
}

.image-item img[data-v-5617fe9d] {
    border-radius: 8px;
    height: auto;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.remix-dropdown-container[data-v-5617fe9d] {
    display: inline-block;
    position: relative
}

.remix-dropdown-content[data-v-5617fe9d] {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 10px #00000026;
    left: 0;
    min-width: 200px;
    position: absolute;
    top: calc(100% + 8px);
    z-index: 1000
}

@media (prefers-color-scheme:dark) {
    .remix-dropdown-content[data-v-5617fe9d] {
        background: #252525
    }
}

.hide-scrollbar[data-v-5617fe9d]::-webkit-scrollbar {
    display: none
}

.vue-advanced-cropper {
    direction: ltr;
    max-height: 100%;
    max-width: 100%;
    position: relative;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.vue-advanced-cropper__stretcher {
    max-height: 100%;
    max-width: 100%;
    pointer-events: none;
    position: relative
}

.vue-advanced-cropper__image {
    max-width: none !important;
    position: absolute;
    transform-origin: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.vue-advanced-cropper__background,
.vue-advanced-cropper__foreground {
    background: #000;
    left: 50%;
    opacity: 1;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%)
}

.vue-advanced-cropper__foreground {
    opacity: .5
}

.vue-advanced-cropper__boundaries {
    left: 50%;
    opacity: 1;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%)
}

.vue-advanced-cropper__cropper-wrapper {
    height: 100%;
    width: 100%
}

.vue-advanced-cropper__image-wrapper {
    height: 100%;
    overflow: hidden;
    position: absolute;
    width: 100%
}

.vue-advanced-cropper__stencil-wrapper {
    position: absolute
}

.vue-simple-handler {
    background: #fff;
    display: block;
    height: 10px;
    width: 10px
}

.vue-handler-wrapper {
    height: 30px;
    position: absolute;
    transform: translate(-50%, -50%);
    width: 30px
}

.vue-handler-wrapper__draggable {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%
}

.vue-handler-wrapper--west-north {
    cursor: nw-resize
}

.vue-handler-wrapper--north {
    cursor: n-resize
}

.vue-handler-wrapper--east-north {
    cursor: ne-resize
}

.vue-handler-wrapper--east {
    cursor: e-resize
}

.vue-handler-wrapper--east-south {
    cursor: se-resize
}

.vue-handler-wrapper--south {
    cursor: s-resize
}

.vue-handler-wrapper--west-south {
    cursor: sw-resize
}

.vue-handler-wrapper--west {
    cursor: w-resize
}

.vue-handler-wrapper--disabled {
    cursor: auto
}

.vue-line-wrapper {
    align-items: center;
    background: none;
    display: flex;
    justify-content: center;
    position: absolute
}

.vue-line-wrapper--north,
.vue-line-wrapper--south {
    height: 12px;
    left: 0;
    transform: translateY(-50%);
    width: 100%
}

.vue-line-wrapper--north {
    cursor: n-resize;
    top: 0
}

.vue-line-wrapper--south {
    cursor: s-resize;
    top: 100%
}

.vue-line-wrapper--east,
.vue-line-wrapper--west {
    height: 100%;
    top: 0;
    transform: translate(-50%);
    width: 12px
}

.vue-line-wrapper--east {
    cursor: e-resize;
    left: 100%
}

.vue-line-wrapper--west {
    cursor: w-resize;
    left: 0
}

.vue-line-wrapper--disabled {
    cursor: auto
}

.vue-bounding-box {
    height: 100%;
    position: relative;
    width: 100%
}

.vue-bounding-box__handler {
    position: absolute
}

.vue-bounding-box__handler--west-north {
    left: 0;
    top: 0
}

.vue-bounding-box__handler--north {
    left: 50%;
    top: 0
}

.vue-bounding-box__handler--east-north {
    left: 100%;
    top: 0
}

.vue-bounding-box__handler--east {
    left: 100%;
    top: 50%
}

.vue-bounding-box__handler--east-south {
    left: 100%;
    top: 100%
}

.vue-bounding-box__handler--south {
    left: 50%;
    top: 100%
}

.vue-bounding-box__handler--west-south {
    left: 0;
    top: 100%
}

.vue-bounding-box__handler--west {
    left: 0;
    top: 50%
}

.vue-draggable-area {
    position: relative
}

.vue-preview-result {
    box-sizing: border-box;
    height: 100%;
    overflow: hidden;
    position: absolute;
    width: 100%
}

.vue-preview-result__wrapper {
    position: absolute
}

.vue-preview-result__image {
    max-width: none !important;
    pointer-events: none;
    position: relative;
    transform-origin: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.vue-rectangle-stencil {
    box-sizing: border-box
}

.vue-rectangle-stencil,
.vue-rectangle-stencil__preview {
    height: 100%;
    position: absolute;
    width: 100%
}

.vue-rectangle-stencil--movable {
    cursor: move
}

.vue-circle-stencil {
    box-sizing: content-box;
    cursor: move;
    height: 100%;
    position: absolute;
    width: 100%
}

.vue-circle-stencil__preview {
    border-radius: 50%;
    height: 100%;
    position: absolute;
    width: 100%
}

.vue-circle-stencil--movable {
    cursor: move
}

.vue-preview {
    box-sizing: border-box;
    overflow: hidden;
    position: relative
}

.vue-preview--fill,
.vue-preview__wrapper {
    height: 100%;
    position: absolute;
    width: 100%
}

.vue-preview__image {
    max-width: none !important;
    pointer-events: none;
    position: absolute;
    transform-origin: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.vue-simple-line {
    background: none;
    border: 0 solid hsla(0, 0%, 100%, .3);
    transition: border .5s
}

.vue-simple-line--north,
.vue-simple-line--south {
    height: 0;
    width: 100%
}

.vue-simple-line--east,
.vue-simple-line--west {
    height: 100%;
    width: 0
}

.vue-simple-line--east {
    border-right-width: 1px
}

.vue-simple-line--west {
    border-left-width: 1px
}

.vue-simple-line--south {
    border-bottom-width: 1px
}

.vue-simple-line--north {
    border-top-width: 1px
}

.vue-simple-line--hover {
    border-color: #fff;
    opacity: 1
}

.user .content[data-v-ca03bd6c] pre {
    background: none;
    margin: 0;
    padding: 0
}

.user .content[data-v-ca03bd6c] pre code {
    font-family: sans-serif;
    white-space: pre-wrap
}

.conversation-content[data-v-ca03bd6c] {
    background: transparent;
    color: #666;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding-bottom: 180px;
    position: relative
}

.conversation .role[data-v-ca03bd6c] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.conversation .avatar[data-v-ca03bd6c] {
    height: 36px;
    width: 36px
}

.conversation .avatar img[data-v-ca03bd6c] {
    width: 100%
}

.conversation .name[data-v-ca03bd6c] {
    color: #252525;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal
}

.conversation-item-desc .bubble[data-v-ca03bd6c] {
    align-items: flex-start;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    justify-content: flex-start;
    line-height: 20px;
    padding: 12px 16px
}

.conversation-item-desc .bubble code[data-v-ca03bd6c],
.conversation-item-desc .bubble pre[data-v-ca03bd6c] {
    font-size: 16px
}

.conversation-item-desc .button[data-v-ca03bd6c] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 2px;
    line-height: normal;
    padding: 4px 8px
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc .button[data-v-ca03bd6c] {
        border-color: #252525;
        color: #ddd
    }
}

.conversation-item-desc .button .icon[data-v-ca03bd6c] {
    display: flex
}

@keyframes blink-animation-ca03bd6c {
    0%,
    to {
        color: #000
    }
    50% {
        color: transparent
    }
}

@media (prefers-color-scheme:dark) {
    @keyframes blink-animation-ca03bd6c {
        0%,
        to {
            color: #fff
        }
        50% {
            color: transparent
        }
    }
}

.conversation-item-desc.assistant .bubble[data-v-ca03bd6c] .cursor {
    animation: blink-animation-ca03bd6c .5s infinite;
    color: #606366;
    font-size: 12px
}

.conversation-item-desc[data-v-ca03bd6c] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-width: 260px;
    width: 100%
}

.conversation-item-desc.user[data-v-ca03bd6c] {
    align-items: flex-end;
    margin-left: 30px
}

.conversation-item-desc.assistant .bubble[data-v-ca03bd6c] {
    border: 1px solid transparent;
    border-radius: 16px;
    min-width: 260px;
    padding-left: 0;
    padding-right: 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.retry[data-v-ca03bd6c],
.conversation-item-desc.assistant .bubble.try_moa[data-v-ca03bd6c] {
    align-items: center;
    background: #fafafa;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 18px 16px
}

.conversation-item-desc .retry .button[data-v-ca03bd6c],
.conversation-item-desc .try_moa .button[data-v-ca03bd6c] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 8px 16px
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.retry[data-v-ca03bd6c],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-ca03bd6c] {
        flex-direction: column
    }
}

.models-list[data-v-ca03bd6c] {
    background-color: #fff;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    padding: 20px 8px
}

.models-list .model[data-v-ca03bd6c] {
    color: #232425;
    margin-top: 4px
}

.models-list .model .row[data-v-ca03bd6c] {
    border-radius: 8px;
    color: #232425;
    gap: 4px;
    justify-content: space-between
}

.models-list .model .row[data-v-ca03bd6c],
.models-selected[data-v-ca03bd6c] {
    display: flex;
    flex-direction: row;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.models-selected[data-v-ca03bd6c] {
    background-color: #f4f4f4;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 150%;
    padding: 6px 12px
}

@media (prefers-color-scheme:dark) {
    .models-list[data-v-ca03bd6c] {
        background-color: #252525
    }
    .models-list .model .row[data-v-ca03bd6c] {
        color: #ddd
    }
    .conversation-item-desc.assistant .bubble[data-v-ca03bd6c] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble.retry[data-v-ca03bd6c],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-ca03bd6c] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .retry .button[data-v-ca03bd6c],
    .conversation-item-desc .try_moa .button[data-v-ca03bd6c] {
        background: #fafafa;
        color: #232425
    }
}

.controls[data-v-ca03bd6c] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden
}

.controls[data-v-ca03bd6c]::-webkit-scrollbar {
    display: none
}

.permission-wrapper[data-v-ca03bd6c] {
    display: flex;
    justify-content: center;
    width: 100%
}

.empty-placeholder[data-v-ca03bd6c] {
    color: #232425;
    font-family: Arial;
    font-size: 30px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin-top: 30vh;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .empty-placeholder[data-v-ca03bd6c] {
        color: #fff
    }
}

.general-chat-wrapper[data-v-ca03bd6c] {
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
    position: relative
}

.chat-wrapper[data-v-ca03bd6c],
.general-chat-wrapper[data-v-ca03bd6c] {
    box-sizing: border-box;
    display: flex;
    width: 100%
}

.chat-wrapper[data-v-ca03bd6c] {
    align-items: flex-start;
    justify-content: center;
    overflow-x: hidden;
    padding: 16px 16px 100px
}

.chat-content-wrapper[data-v-ca03bd6c] {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    max-width: 100%;
    width: var(--container-width, 680px)
}

.input-wrapper-wrapper[data-v-ca03bd6c] {
    align-items: center;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-bottom: 4px;
    padding-top: 16px;
    width: 100%;
    z-index: 100
}

.input-wrapper[data-v-ca03bd6c] {
    width: var(--container-width, 680px)
}

.conversation-wrapper[data-v-ca03bd6c] {
    background-color: transparent;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    max-width: 100%;
    min-height: 200px;
    padding-bottom: 120px;
    width: var(--container-width, 680px)
}

.array-content-wrapper[data-v-ca03bd6c] {
    align-items: flex-end;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%
}

.conversation-statement[data-v-ca03bd6c] {
    display: flex;
    flex-direction: column;
    list-style: none;
    margin: 10px 0
}

.conversation-statement.user[data-v-ca03bd6c] {
    align-items: flex-end
}

.conversation-statement.assistant+.conversation-statement.user[data-v-ca03bd6c],
.conversation-statement.user+.conversation-statement.assistant[data-v-ca03bd6c] {
    margin-top: 16px
}

.conversation-statement[data-v-ca03bd6c]:first-child {
    margin-top: 5px
}

.conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-ca03bd6c] {
    border: 1px solid #efefef;
    padding: 10px 16px;
    width: 100%
}

.conversation-item-desc.user .bubble[data-v-ca03bd6c] {
    background: #f5f5f5;
    border-radius: 16px;
    color: #232425;
    margin-right: 2px;
    max-width: 70%
}

.conversation-item-desc.user .content-item[data-v-ca03bd6c] {
    align-items: flex-end;
    display: flex
}

.conversation-item-desc.assistant .bubble[data-v-ca03bd6c] {
    box-sizing: border-box;
    -webkit-hyphens: auto;
    hyphens: auto;
    max-width: 100%;
    padding: 0
}

.conversation-item-desc.assistant .bubble.text[data-v-ca03bd6c] {
    margin: 24px 0 16px;
    padding: 10px 0
}

.conversation-item-desc.assistant .bubble[data-v-ca03bd6c]:not(:first-child) {
    margin-top: 20px
}

.conversation-item-desc.user .bubble[data-v-ca03bd6c]:not(:first-child) {
    margin-top: 12px
}

.conversation-item-desc .bubble .loading[data-v-ca03bd6c],
.conversation-item-desc .bubble[data-v-ca03bd6c] .cursor {
    display: none
}

.conversation-item-desc.assistant .bubble.thinking[data-v-ca03bd6c] .cursor {
    display: inline
}

.conversation-item-desc .desc[data-v-ca03bd6c] {
    display: flex;
    flex-direction: row;
    max-width: 100%
}

.conversation-item-desc .content[data-v-ca03bd6c] {
    max-width: 100%
}

.creating-image-tasks[data-v-ca03bd6c] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    justify-content: center
}

.creating-image-tasks .text[data-v-ca03bd6c] {
    color: #232425;
    font-size: 14px;
    line-height: 150%
}

.creating-image-tasks .model-icon-wrapper[data-v-ca03bd6c] {
    align-items: center;
    background-color: #f0f0f0;
    border-radius: 50%;
    display: flex;
    flex-direction: row;
    gap: 4px;
    height: 18px;
    overflow: hidden;
    width: 18px
}

.creating-image-tasks .model-icon-wrapper .model-icon[data-v-ca03bd6c] {
    height: 100%;
    width: 100%
}

.models-wrapper[data-v-ca03bd6c] {
    width: -moz-fit-content;
    width: fit-content
}

.models-list[data-v-ca03bd6c] {
    gap: 4px;
    max-width: 280px
}

.list-scroll[data-v-ca03bd6c] {
    max-height: 600px;
    overflow-y: auto
}

@media (max-width:768px) {
    .models-list[data-v-ca03bd6c] {
        max-height: 430px;
        overflow-y: scroll
    }
}

.models-selected[data-v-ca03bd6c] {
    align-items: center;
    background-color: transparent;
    border: 1px solid #efefef;
    box-sizing: border-box;
    justify-content: center;
    transition: background-color .2s
}

.models-selected[data-v-ca03bd6c]:hover {
    background-color: #f0f0f0
}

.models-selected .model-selected[data-v-ca03bd6c] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.models-selected .model-selected .icon[data-v-ca03bd6c] {
    align-items: center;
    background-color: transparent;
    border-radius: 50%;
    display: flex;
    height: 16px;
    justify-content: center;
    overflow: hidden;
    width: 16px
}

.models-selected .model-selected .icon[data-v-ca03bd6c] svg {
    height: 16px;
    width: 16px
}

.models-selected .model-selected .icon img[data-v-ca03bd6c] {
    height: 16px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 16px
}

.models-selected.remix-selector .icon[data-v-ca03bd6c] svg {
    color: #232425;
    height: 16px;
    width: 16px
}

.models-selected.aspect-ratio-selector[data-v-ca03bd6c],
.models-selected.duration-selector[data-v-ca03bd6c],
.models-selected.remix-selector[data-v-ca03bd6c] {
    border-radius: 8px
}

.models-list .model[data-v-ca03bd6c] {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 10px 6px 10px 10px
}

.models-list .model[data-v-ca03bd6c]:hover {
    background-color: #f5f5f5
}

.models-list .model.disabled[data-v-ca03bd6c] {
    cursor: not-allowed;
    opacity: .5
}

.models-list .model.disabled[data-v-ca03bd6c]:hover {
    background-color: transparent
}

.models-list .model[data-v-ca03bd6c]:first-child {
    margin-top: 0
}

.models-list .divider[data-v-ca03bd6c] {
    background-color: #efefef;
    height: 1px;
    margin: 6px 0;
    width: 100%
}

.models-list .model .row[data-v-ca03bd6c] {
    box-sizing: border-box;
    color: #606366;
    height: 20px;
    width: 100%
}

.models-list .model .description[data-v-ca03bd6c],
.models-list .model .tip[data-v-ca03bd6c] {
    color: #909499;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px
}

@media (hover:hover) {
    .models-list .model[data-v-ca03bd6c]:hover {
        background-color: #f5f5f5;
        border-radius: 12px
    }
}

.models-list .model .right[data-v-ca03bd6c] {
    align-items: center;
    display: flex;
    flex-direction: row
}

@media (prefers-color-scheme:dark) {
    .models-list .model .row[data-v-ca03bd6c] {
        color: #ddd
    }
    .models-selected.aspect-ratio-selector[data-v-ca03bd6c],
    .models-selected.style-selector[data-v-ca03bd6c] {
        border: 1px solid #efefef30;
        color: #fff
    }
    @media (hover:hover) {
        .models-selected.aspect-ratio-selector[data-v-ca03bd6c]:hover,
        .models-selected.style-selector[data-v-ca03bd6c]:hover {
            background-color: #f0f0f010
        }
        .models-list .model:hover .row[data-v-ca03bd6c] {
            background-color: #333
        }
    }
    .models-list .model .description[data-v-ca03bd6c] {
        color: #909499
    }
    .models-list .divider[data-v-ca03bd6c] {
        background-color: #333
    }
}

.model-response-wrapper .title[data-v-ca03bd6c] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 4px
}

.model-response-wrapper .title .icon[data-v-ca03bd6c] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    margin-right: 5px;
    width: 16px
}

.model-response-wrapper .status .icon[data-v-ca03bd6c] {
    margin-right: 0
}

.model-response-wrapper .title .icon img[data-v-ca03bd6c] {
    height: 100%;
    width: 100%
}

.model-response-wrapper[data-v-ca03bd6c] {
    position: relative;
    width: 100%
}

.model-response-wrapper .content[data-v-ca03bd6c] {
    transition: all .3s ease-in-out
}

.model-response-wrapper .content.no-expand[data-v-ca03bd6c] {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    line-height: 30px;
    max-height: 0;
    overflow: hidden
}

.model-response-wrapper.thinking .content.no-expand[data-v-ca03bd6c] {
    max-height: 90px
}

.model-response-wrapper.expanded .content[data-v-ca03bd6c] {
    max-height: unset;
    padding-top: 20px
}

.model-response .title .status .icon.finished[data-v-ca03bd6c] {
    color: #5cd4a1
}

.model-response .title .status .icon[data-v-ca03bd6c] svg {
    height: 100%;
    width: 100%
}

.model-response-wrapper .expand-button[data-v-ca03bd6c] {
    cursor: pointer;
    height: 20px;
    position: absolute;
    right: 10px;
    top: 12px;
    width: 20px
}

.model-response .divider[data-v-ca03bd6c] {
    background-color: #efefef;
    height: 1px;
    margin-bottom: 20px
}

.model-response[data-v-ca03bd6c] {
    color: #232425;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    width: 100%
}

.models-list[data-v-ca03bd6c] .n-checkbox.n-checkbox--checked {
    --n-box-color-checked: transparent !important;
    --n-check-mark-color: #232425 !important;
    --n-border-checked: 1px solid transparent !important;
    --n-color-checked: transparent !important
}

.models-list[data-v-ca03bd6c] .n-checkbox {
    --n-box-color: transparent !important;
    --n-border: 1px solid transparent !important
}

.controls[data-v-ca03bd6c] {
    flex-wrap: nowrap;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    white-space: nowrap
}

.controls[data-v-ca03bd6c]>* {
    flex-shrink: 0
}

.options-wrapper[data-v-ca03bd6c] {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 8px
}

.aspect-ratio-selector[data-v-ca03bd6c],
.gallery-toggle .icon[data-v-ca03bd6c],
.models-selected[data-v-ca03bd6c],
.reflection-toggle[data-v-ca03bd6c],
.upload-attachments .icon[data-v-ca03bd6c] {
    flex-shrink: 0;
    white-space: nowrap
}

.option[data-v-ca03bd6c],
.personalize-options[data-v-ca03bd6c],
.personalize-selected[data-v-ca03bd6c] {
    align-items: center;
    display: flex
}

.personalize-selected[data-v-ca03bd6c] {
    background-color: #fafafa;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    height: 28px;
    justify-content: space-between;
    line-height: 150%;
    padding: 0 12px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.image-content[data-v-ca03bd6c] {
    border-radius: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-top: 12px;
    max-width: 40%;
    width: 100%
}

.gallery-toggle .icon[data-v-ca03bd6c] {
    align-items: center;
    background-color: #fafafa;
    border-radius: 16px;
    color: #232425;
    cursor: pointer;
    display: flex;
    height: 30px;
    justify-content: center;
    padding: 5px 12px;
    transition: background-color .2s;
    width: 20px
}

.gallery-toggle .icon.active[data-v-ca03bd6c] {
    background-color: #eef3ff
}

.expand-button[data-v-ca03bd6c] {
    transform: rotate(180deg)
}

.expanded .expand-button[data-v-ca03bd6c] {
    transform: rotate(0)
}

.upload-attachments .icon[data-v-ca03bd6c] {
    align-items: center;
    border-radius: 16px;
    color: #232425;
    cursor: pointer;
    display: flex;
    height: 20px;
    justify-content: center;
    transition: transform .3s;
    width: 20px
}

.upload-attachments .icon[data-v-ca03bd6c]:hover {
    transform: scale(1.1)
}

.content-item[data-v-ca03bd6c] {
    align-items: center;
    display: flex;
    flex-direction: column;
    margin: 8px 0;
    width: 100%
}

.image-content img[data-v-ca03bd6c] {
    border-radius: 8px;
    max-width: 100%;
    -o-object-fit: contain;
    object-fit: contain
}

.text-content[data-v-ca03bd6c] {
    color: inherit;
    line-height: 1.5
}

.extra-options[data-v-ca03bd6c] {
    flex-direction: row;
    gap: 12px;
    height: 30px
}

.extra-options[data-v-ca03bd6c],
.profile-avatar[data-v-ca03bd6c] {
    align-items: center;
    display: flex
}

.profile-avatar[data-v-ca03bd6c] {
    background-color: #f5f5f5;
    border-radius: 50%;
    height: 32px;
    justify-content: center;
    overflow: hidden;
    width: 32px
}

.profile-avatar img[data-v-ca03bd6c] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.profile-avatar .dot[data-v-ca03bd6c] {
    background-color: #606366;
    border-radius: 50%;
    height: 8px;
    width: 8px
}

.personalize-list[data-v-ca03bd6c] {
    min-width: 240px;
    padding: 8px
}

.profile-option[data-v-ca03bd6c] {
    align-items: center;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    gap: 12px;
    padding: 12px;
    transition: background-color .2s
}

.profile-option[data-v-ca03bd6c]:hover {
    background-color: #f5f5f5
}

.profile-option.active[data-v-ca03bd6c] {
    background-color: #eef3ff
}

.profile-name[data-v-ca03bd6c] {
    color: #232425;
    flex: 1;
    font-size: 14px
}

[data-v-ca03bd6c] .moa-image-side-pannel {
    bottom: auto;
    left: auto;
    margin-top: 16px;
    max-height: 300px;
    overflow-y: auto;
    position: relative;
    transform: none;
    width: 100%
}

.side-panel-container[data-v-ca03bd6c] {
    border-radius: 8px;
    margin-top: 16px;
    overflow: hidden
}

.hd-toggle[data-v-ca03bd6c],
.trigger-icon[data-v-ca03bd6c] {
    align-items: center;
    display: flex
}

.trigger-icon[data-v-ca03bd6c] {
    transition: transform .2s
}

.trigger-icon.active[data-v-ca03bd6c] {
    transform: rotate(180deg)
}

.moa-title[data-v-ca03bd6c] {
    color: #ccc;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin: 24px 0 8px
}

.assistant-message-divider[data-v-ca03bd6c] {
    background-color: #efefef;
    height: 1px;
    margin: 50px 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.try_moa[data-v-ca03bd6c] {
    font-size: 16px;
    line-height: 1.5em;
    width: 100%
}

.conversation-item-desc .try_moa .button[data-v-ca03bd6c] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    line-height: 1.5em;
    min-width: -moz-fit-content;
    min-width: fit-content;
    padding: 7.5px 16px;
    transition: transform .2s;
    white-space: nowrap
}

.conversation-item-desc .try_moa .button[data-v-ca03bd6c]:hover {
    transform: scale(1.05)
}

.reflection-toggle[data-v-ca03bd6c] {
    align-items: center;
    background-color: transparent;
    border: 1px solid #efefef;
    border-radius: 8px;
    box-sizing: border-box;
    display: flex;
    height: 33px;
    padding: 6px 12px;
    white-space: nowrap
}

.reflection-toggle[data-v-ca03bd6c] .n-checkbox-box {
    height: 16px;
    width: 16px
}

.reflection-toggle[data-v-ca03bd6c] .n-checkbox__label {
    color: #606366;
    font-size: 14px;
    font-weight: 400;
    line-height: 16px
}

.reflection-toggle[data-v-ca03bd6c] .n-checkbox--show-label {
    line-height: 16px
}

@media (prefers-color-scheme:dark) {
    .input-wrapper-wrapper[data-v-ca03bd6c] {
        background-color: #232425
    }
    .reflection-toggle[data-v-ca03bd6c]:hover {
        background-color: #f0f0f010
    }
    .models-selected .model-selected .icon[data-v-ca03bd6c] {
        background-color: #fff
    }
    .models-list[data-v-ca03bd6c],
    .n-popover.models-popover[data-v-ca03bd6c] {
        background-color: #252525
    }
    .models-list .model .left .text[data-v-ca03bd6c] {
        color: #ddd
    }
    .models-list .model .left .icon[data-v-ca03bd6c] {
        background-color: #efefef
    }
    @media (hover:hover) {
        .models-list .model[data-v-ca03bd6c]:hover,
        .models-list .model:hover .row[data-v-ca03bd6c] {
            background-color: #333
        }
    }
    .creating-image-tasks .text[data-v-ca03bd6c] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble[data-v-ca03bd6c],
    .conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-ca03bd6c] {
        background: none;
        box-sizing: border-box;
        color: #fff
    }
    .conversation-item-desc.user .bubble[data-v-ca03bd6c] {
        background: #eee;
        color: #000
    }
    .conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-ca03bd6c] {
        background-color: #444
    }
    .model-response[data-v-ca03bd6c] {
        color: #ddd
    }
    .section-title[data-v-ca03bd6c] {
        color: #666
    }
    .upload-attachments .icon[data-v-ca03bd6c],
    .personalize-options[data-v-ca03bd6c] .n-radio {
        color: #ddd
    }
    .personalize-selected[data-v-ca03bd6c] {
        background-color: #333;
        color: #ddd
    }
    .profile-avatar[data-v-ca03bd6c],
    .profile-option[data-v-ca03bd6c]:hover {
        background-color: #333
    }
    .profile-option.active[data-v-ca03bd6c] {
        background-color: #444
    }
    .profile-name[data-v-ca03bd6c] {
        color: #ddd
    }
    .profile-avatar .dot[data-v-ca03bd6c] {
        background-color: #ddd
    }
    .gallery-toggle .icon[data-v-ca03bd6c] {
        background-color: #333;
        color: #ddd
    }
    .gallery-toggle .icon.active[data-v-ca03bd6c] {
        background-color: #444
    }
    .hd-toggle[data-v-ca03bd6c] .n-checkbox {
        color: #ddd
    }
    .models-list[data-v-ca03bd6c] .n-checkbox.n-checkbox--checked {
        --n-check-mark-color: #ddd !important
    }
    .reflection-toggle[data-v-ca03bd6c] {
        background-color: #232425;
        border: 1px solid #efefef30
    }
    .reflection-toggle[data-v-ca03bd6c] .n-checkbox {
        --n-border: 1px solid #444;
        --n-box-color-checked: #0f7fff;
        --n-check-mark-color: #fff;
        --n-border-checked: 1px solid #0f7fff
    }
    .reflection-toggle[data-v-ca03bd6c] .n-checkbox__label {
        color: #fff
    }
}

.model-divider[data-v-ca03bd6c] {
    background-color: #efefef;
    height: 1px;
    margin: 4px 12px
}

@media (prefers-color-scheme:dark) {
    .model-divider[data-v-ca03bd6c] {
        background-color: #333
    }
}

.reflection-toggle[data-v-ca03bd6c] .n-checkbox {
    --n-box-color: #fff !important;
    --n-border: 1px solid #efefef !important;
    --n-border-checked: 1px solid #0f7fff !important;
    --n-border-focus: 1px solid #0f7fff !important;
    --n-border-disabled: 1px solid #e0e0e6 !important;
    --n-border-disabled-checked: 1px solid #e0e0e6 !important;
    --n-box-shadow-focus: 0 0 0 2px rgba(15, 127, 255, .3) !important;
    --n-color: transparent !important;
    --n-color-checked: #0f7fff !important
}

.reflection-toggle[data-v-ca03bd6c] .n-checkbox:hover .n-checkbox-box .n-checkbox-box__border {
    border: 1px solid #0f7fff !important
}

.reflection-toggle[data-v-ca03bd6c] .n-checkbox:focus:not(:active) .n-checkbox-box .n-checkbox-box__border {
    border: 1px solid #0f7fff !important;
    box-shadow: 0 0 0 2px #0f7fff4d !important
}

.reflection-toggle[data-v-ca03bd6c] .n-checkbox.n-checkbox--checked {
    --n-box-color-checked: #0f7fff !important;
    --n-check-mark-color: #fff !important;
    --n-border-checked: 1px solid #0f7fff !important;
    --n-color-checked: #0f7fff !important
}

.task-status[data-v-ca03bd6c] {
    color: #666;
    font-size: 12px;
    margin-top: 8px
}

.status-complete[data-v-ca03bd6c] {
    color: #4caf50
}

.status-pending[data-v-ca03bd6c] {
    color: #2196f3
}

.models-list .model .left[data-v-ca03bd6c] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 10px;
    position: relative
}

.models-list .model .left .icon[data-v-ca03bd6c] {
    align-items: center;
    background-color: transparent;
    border-radius: 50%;
    display: flex;
    height: 20px;
    justify-content: center;
    overflow: hidden;
    width: 20px
}

.models-list .model .left .icon[data-v-ca03bd6c] svg {
    height: 20px;
    width: 20px
}

.models-list .model .left .icon img[data-v-ca03bd6c] {
    height: 20px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 20px
}

.models-list .model .right input[type=radio][data-v-ca03bd6c] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    cursor: pointer;
    height: 16px;
    margin: 0;
    position: relative;
    width: 16px
}

.models-list .model .right input[type=radio][data-v-ca03bd6c]:checked {
    background-color: #fff;
    border-color: #0f7fff
}

.models-list .model .right input[type=radio][data-v-ca03bd6c]:checked:after {
    background: #0f7fff;
    border-radius: 50%;
    content: "";
    height: 10px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 10px
}

@media (prefers-color-scheme:dark) {
    .models-list .model .right input[type=radio][data-v-ca03bd6c] {
        background-color: #fff;
        border-color: #666
    }
    .models-list .model .right input[type=radio][data-v-ca03bd6c]:checked {
        background-color: #fff;
        border-color: #0f7fff
    }
}

.models-wrapper .models-selected[data-v-ca03bd6c] {
    background-color: #f4f4f4;
    border: none;
    border-radius: 16.5px
}

.models-wrapper .models-selected[data-v-ca03bd6c]:hover {
    background-color: #e8e8e8
}

@media (prefers-color-scheme:dark) {
    .aspect-ratio-selector .models-selected[data-v-ca03bd6c] {
        background-color: #333;
        border-color: #444
    }
    .aspect-ratio-selector .models-selected[data-v-ca03bd6c]:hover {
        background-color: #444
    }
    .conversation-item-desc.assistant .bubble.try_moa[data-v-ca03bd6c] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .try_moa .button[data-v-ca03bd6c] {
        background: #fafafa;
        color: #232425
    }
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.try_moa[data-v-ca03bd6c] {
        flex-direction: column
    }
    .chat-wrapper[data-v-ca03bd6c] {
        box-sizing: border-box;
        padding: 16px 16px 100px
    }
    .input-wrapper-wrapper[data-v-ca03bd6c] {
        bottom: 0;
        box-sizing: border-box;
        left: 0;
        padding: 16px 16px 40px;
        position: fixed;
        width: 100%
    }
    .input-wrapper[data-v-ca03bd6c],
    .input-wrapper-wrapper-inner[data-v-ca03bd6c] {
        width: 100%
    }
    .options-wrapper[data-v-ca03bd6c] {
        flex-wrap: wrap;
        gap: 8px
    }
    [data-v-ca03bd6c] .moa-image-side-pannel {
        bottom: auto;
        left: auto;
        max-height: 200px;
        transform: none;
        width: 100%
    }
}

.task-content[data-v-ca03bd6c] {
    margin: 0
}

.task-content[data-v-ca03bd6c] h3 {
    color: #232425;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    margin: 12px 0
}

.prompt-box[data-v-ca03bd6c] {
    border-radius: 4px;
    color: #909499;
    font-size: 16px;
    line-height: 1.5;
    margin: 12px 0;
    padding: 12px 16px
}

.prompt-box-title[data-v-ca03bd6c] {
    font-weight: 700;
    margin-bottom: 12px
}

.task-box-divider[data-v-ca03bd6c] {
    background-color: #efefef;
    height: 1px;
    margin: 40px 0
}

@media (prefers-color-scheme:dark) {
    .task-content[data-v-ca03bd6c] h3 {
        color: #fff
    }
    .prompt-box[data-v-ca03bd6c] {
        background-color: #333;
        border-left-color: #0f7fff;
        color: #ddd
    }
}

.llm-model-toggle[data-v-ca03bd6c] {
    align-items: center;
    background-color: #fff;
    border-radius: 8px;
    box-sizing: border-box;
    display: flex;
    height: 28px
}

@media (prefers-color-scheme:dark) {
    .llm-model-toggle[data-v-ca03bd6c] {
        background-color: #333
    }
    .llm-model-toggle[data-v-ca03bd6c] .n-select {
        --n-color: #333;
        --n-text-color: #ddd
    }
}

.style-cards[data-v-ca03bd6c] {
    background: #fff;
    border-radius: 12px;
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(3, 1fr);
    padding: 16px
}

.style-card[data-v-ca03bd6c] {
    align-items: center;
    border: 2px solid transparent;
    border-radius: 10px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    transition: all .2s ease
}

.style-card[data-v-ca03bd6c]:hover {
    background: #ffffff1a;
    transform: scale(1.05)
}

.style-card.active[data-v-ca03bd6c] {
    background: #0f7fff1a;
    border-color: #0f7fff
}

.style-image[data-v-ca03bd6c] {
    background: #333;
    border-radius: 8px;
    height: 101px;
    overflow: hidden;
    position: relative;
    width: 72px
}

.style-image[data-v-ca03bd6c]:after {
    background: linear-gradient(180deg, transparent, transparent 50%, rgba(0, 0, 0, .6));
    bottom: 0;
    content: "";
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 0
}

.style-image img[data-v-ca03bd6c] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.placeholder-image[data-v-ca03bd6c] {
    align-items: center;
    color: #666;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%
}

.style-label[data-v-ca03bd6c] {
    bottom: 4px;
    color: #fff;
    font-size: 12px;
    left: 0;
    line-height: 1.5;
    padding: 0 4px;
    position: absolute;
    right: 0;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .style-cards[data-v-ca03bd6c] {
        background: #232425
    }
    .style-card[data-v-ca03bd6c]:hover {
        background: #ffffff1a
    }
}

.model-preference-tip[data-v-ca03bd6c] {
    align-items: center;
    background: #0f7fff0d;
    border-radius: 8px;
    color: #666;
    display: flex;
    font-size: 14px;
    gap: 8px;
    line-height: 1.4;
    margin: 20px 0;
    padding: 12px
}

.tip-icon[data-v-ca03bd6c] {
    align-items: center;
    color: #0f7fff;
    display: flex;
    flex-shrink: 0;
    height: 16px;
    width: 16px
}

.tip-text[data-v-ca03bd6c] {
    flex: 1
}

@media (prefers-color-scheme:dark) {
    .model-preference-tip[data-v-ca03bd6c] {
        background: #0f7fff1a;
        color: #999
    }
}

.conversation-item-desc.assistant .bubble.try_moa .left[data-v-ca03bd6c] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.conversation-item-desc.assistant .bubble.try_moa .left .main-text[data-v-ca03bd6c] {
    font-size: 16px;
    line-height: 1.5em
}

.conversation-item-desc.assistant .bubble.try_moa .left .sub-text[data-v-ca03bd6c] {
    color: #666;
    font-size: 14px;
    line-height: 1.4em
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc.assistant .bubble.try_moa .left .sub-text[data-v-ca03bd6c] {
        color: #999
    }
}

.slide-left-enter-active[data-v-ca03bd6c],
.slide-left-leave-active[data-v-ca03bd6c],
.slide-right-enter-active[data-v-ca03bd6c],
.slide-right-leave-active[data-v-ca03bd6c] {
    transition: transform .3s ease
}

.slide-left-enter-from[data-v-ca03bd6c] {
    transform: translate(100%)
}

.slide-left-leave-to[data-v-ca03bd6c],
.slide-right-enter-from[data-v-ca03bd6c] {
    transform: translate(-100%)
}

.slide-right-leave-to[data-v-ca03bd6c] {
    transform: translate(100%)
}

[data-v-ca03bd6c] .moa-image-detail {
    overflow: hidden;
    position: relative
}

[data-v-ca03bd6c] .moa-image-detail img {
    transition: transform .3s ease;
    will-change: transform
}

[data-v-ca03bd6c] .moa-image-detail {
    touch-action: pan-y pinch-zoom;
    -moz-user-select: none;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none
}

[data-v-ca03bd6c] .moa-image-detail img {
    pointer-events: none
}

.slide-left-enter-active[data-v-ca03bd6c],
.slide-left-leave-active[data-v-ca03bd6c],
.slide-right-enter-active[data-v-ca03bd6c],
.slide-right-leave-active[data-v-ca03bd6c] {
    height: 100%;
    position: absolute;
    transition: transform .3s cubic-bezier(.4, 0, .2, 1);
    width: 100%
}

.input-wrapper-wrapper-inner[data-v-ca03bd6c] {
    margin: 0 auto;
    max-width: 100%;
    position: relative;
    width: var(--container-width, 680px)
}

.crop-footer[data-v-ca03bd6c] {
    gap: 12px;
    margin-top: 16px
}

.crop-footer[data-v-ca03bd6c],
.images-row[data-v-ca03bd6c] {
    display: flex;
    justify-content: center
}

.images-row[data-v-ca03bd6c] {
    align-items: flex-end;
    flex-direction: column;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 4px;
    width: 100%
}

.image-item[data-v-ca03bd6c] {
    flex: 0 0 auto;
    max-width: 40%;
    position: relative
}

.image-item img[data-v-ca03bd6c] {
    border-radius: 8px;
    height: auto;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.remix-dropdown-container[data-v-ca03bd6c] {
    display: inline-block;
    position: relative
}

.remix-dropdown-content[data-v-ca03bd6c] {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 10px #00000026;
    left: 0;
    min-width: 200px;
    position: absolute;
    top: calc(100% + 8px);
    z-index: 1000
}

@media (prefers-color-scheme:dark) {
    .remix-dropdown-content[data-v-ca03bd6c] {
        background: #252525
    }
    .remix-selector[data-v-ca03bd6c] {
        border: 1px solid #efefef30;
        color: #fff
    }
    .remix-selector[data-v-ca03bd6c]:hover {
        background-color: #f0f0f010
    }
}

.cropper[data-v-ca03bd6c] {
    background: #000;
    height: 100%;
    width: 100%
}

.cropper-ratio-hint[data-v-ca03bd6c] {
    color: #0009;
    margin-top: 10px;
    text-align: center
}

.dialog-footer[data-v-ca03bd6c] {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 16px
}

.ratio-controls[data-v-ca03bd6c] {
    display: flex;
    gap: 15px;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.tool-button[data-v-ca03bd6c] {
    background: #f5f5f5;
    border: none;
    border-radius: 12px;
    color: #000;
    cursor: pointer;
    flex-shrink: 0;
    font-size: 14px;
    height: 30px;
    padding: 4px 20px 4px 40px;
    position: relative;
    transition: all .3s
}

.tool-button[data-v-ca03bd6c]:hover {
    background: #0000001a
}

.tool-button[data-v-ca03bd6c]:before {
    border: 1px solid;
    border-radius: 2px;
    content: "";
    height: 16px;
    left: 12px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 16px
}

.tool-button[data-ratio="1:1"][data-v-ca03bd6c]:before {
    height: 12px;
    width: 12px
}

.tool-button[data-ratio="4:3"][data-v-ca03bd6c]:before {
    height: 12px;
    width: 16px
}

.tool-button[data-ratio="16:9"][data-v-ca03bd6c]:before {
    height: 9px;
    width: 16px
}

.tool-button[data-ratio="9:16"][data-v-ca03bd6c]:before {
    height: 16px;
    width: 9px
}

.tool-button[data-ratio=original][data-v-ca03bd6c]:before {
    border-style: dashed;
    height: 14px;
    width: 14px
}

.models-list .model .left .text[data-v-ca03bd6c] {
    flex-direction: row
}

.models-list .model .left .text[data-v-ca03bd6c],
.plus-icon[data-v-ca03bd6c] {
    align-items: center;
    display: flex;
    gap: 10px;
    justify-content: center
}

.plus-icon[data-v-ca03bd6c] {
    background: #0f7fff;
    border-radius: 57px;
    box-sizing: border-box;
    color: #fff;
    flex-shrink: 0;
    font-family: Arial;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    height: 14px;
    line-height: 150%;
    opacity: 0;
    padding: 0 8px;
    text-align: center
}

.plus-icon-paid[data-v-ca03bd6c] {
    opacity: 1
}

.plus-icon-hidden[data-v-ca03bd6c] {
    display: none
}