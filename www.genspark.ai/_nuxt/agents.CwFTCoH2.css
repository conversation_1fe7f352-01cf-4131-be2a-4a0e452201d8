.code-sandbox-mode-selector[data-v-1f41d1eb] {
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 9999
}

.overlay[data-v-1f41d1eb] {
    align-items: center;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    background: #00000080;
    bottom: 0;
    display: flex;
    justify-content: center;
    left: 0;
    padding: 32px 20px 20px;
    position: absolute;
    right: 0;
    top: 0
}

.modal[data-v-1f41d1eb] {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px #0000001a, 0 10px 10px -5px #0000000a;
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    max-width: 600px;
    overflow: hidden;
    width: 100%
}

.modal-header[data-v-1f41d1eb] {
    padding: 32px 20px 0;
    text-align: center
}

.modal-header h2[data-v-1f41d1eb] {
    color: #1f2937;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0
}

.modal-content[data-v-1f41d1eb] {
    flex: 1;
    overflow-y: auto;
    padding: 20px 20px 0
}

.mode-options[data-v-1f41d1eb] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.mode-option[data-v-1f41d1eb] {
    background: #fff;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    padding: 12px 20px;
    transition: all .2s ease
}

.mode-option[data-v-1f41d1eb]:hover {
    border-color: #d1d5db
}

.mode-option.active[data-v-1f41d1eb] {
    background: #fff;
    border-color: #232425
}

.mode-option.disabled[data-v-1f41d1eb] {
    cursor: not-allowed;
    opacity: .6
}

.mode-option.disabled[data-v-1f41d1eb],
.mode-option.disabled[data-v-1f41d1eb]:hover {
    background: #f9fafb;
    border-color: #e5e7eb
}

.mode-option.coming-soon .mode-title[data-v-1f41d1eb]:after {
    content: " 🚀";
    font-size: .9em
}

.mode-content[data-v-1f41d1eb] {
    flex: 1
}

.mode-header[data-v-1f41d1eb] {
    justify-content: space-between;
    margin-bottom: 8px
}

.mode-header[data-v-1f41d1eb],
.mode-title[data-v-1f41d1eb] {
    align-items: center;
    display: flex
}

.mode-title[data-v-1f41d1eb] {
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
    gap: .5rem;
    margin: 0
}

.rec-badge[data-v-1f41d1eb] {
    background: #f3f9ff;
    border: 1px solid #cfe5ff;
    border-radius: 57px;
    color: #0f7fff;
    font-size: 10px;
    font-style: normal;
    font-weight: 700;
    gap: 10px;
    line-height: 150%;
    padding: 0 8px;
    text-align: center
}

.check-icon[data-v-1f41d1eb],
.rec-badge[data-v-1f41d1eb] {
    align-items: center;
    display: flex;
    justify-content: center
}

.check-icon[data-v-1f41d1eb] {
    color: #232425;
    flex-shrink: 0;
    height: 16px;
    width: 16px
}

.check-icon[data-v-1f41d1eb] .check-stroke {
    stroke: #fff
}

.mode-description[data-v-1f41d1eb] {
    color: #1f2937;
    font-weight: 500;
    margin: 0 0 4px
}

.mode-description[data-v-1f41d1eb],
.mode-example[data-v-1f41d1eb] {
    font-size: .875rem;
    line-height: 1.4
}

.mode-example[data-v-1f41d1eb] {
    color: #6b7280;
    margin: 0
}

.modal-footer[data-v-1f41d1eb] {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 24px 20px 20px
}

.start-working-btn[data-v-1f41d1eb] {
    align-items: center;
    background: #000;
    border: none;
    border-radius: 12px;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-size: 1rem;
    font-weight: 500;
    gap: .5rem;
    justify-content: center;
    min-height: 48px;
    padding: .75rem 1.5rem;
    transition: all .2s ease;
    width: 100%
}

.start-working-btn[data-v-1f41d1eb]:disabled {
    cursor: not-allowed;
    opacity: .7
}

.start-working-btn[data-v-1f41d1eb]:hover:not(:disabled) {
    background: #333
}

.loading-spinner[data-v-1f41d1eb] {
    animation: spin-1f41d1eb 1s linear infinite;
    border: 2px solid transparent;
    border-radius: 50%;
    border-top-color: currentcolor;
    height: 16px;
    width: 16px
}

.loading-text[data-v-1f41d1eb] {
    transition: opacity .2s ease
}

@keyframes spin-1f41d1eb {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.cancel-btn[data-v-1f41d1eb] {
    background: #f5f5f5;
    border: none;
    border-radius: 12px;
    color: #232425;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    min-height: 48px;
    padding: .75rem 1.5rem;
    transition: all .2s ease;
    width: 100%
}

.cancel-btn[data-v-1f41d1eb]:disabled {
    cursor: not-allowed;
    opacity: .5
}

.cancel-btn[data-v-1f41d1eb]:hover:not(:disabled) {
    background: #e5e5e5
}

@media (prefers-color-scheme:dark) {
    .modal[data-v-1f41d1eb] {
        background: #1f2937
    }
    .modal-header h2[data-v-1f41d1eb] {
        color: #f9fafb
    }
    .mode-option[data-v-1f41d1eb] {
        background: #1f2937;
        border-color: #374151
    }
    .mode-option[data-v-1f41d1eb]:hover {
        border-color: #4b5563
    }
    .mode-option.active[data-v-1f41d1eb] {
        background: #1f2937;
        border-color: #fff
    }
    .mode-option.disabled[data-v-1f41d1eb] {
        cursor: not-allowed;
        opacity: .6
    }
    .mode-option.disabled[data-v-1f41d1eb],
    .mode-option.disabled[data-v-1f41d1eb]:hover {
        background: #111827;
        border-color: #374151
    }
    .mode-description[data-v-1f41d1eb],
    .mode-title[data-v-1f41d1eb] {
        color: #f9fafb
    }
    .mode-example[data-v-1f41d1eb] {
        color: #d1d5db
    }
    .rec-badge[data-v-1f41d1eb] {
        background: #f3f9ff;
        border: 1px solid #cfe5ff;
        color: #0f7fff
    }
    .check-icon[data-v-1f41d1eb] {
        color: #fff
    }
    .check-icon[data-v-1f41d1eb] .check-stroke {
        stroke: #232425
    }
    .modal-footer[data-v-1f41d1eb] {
        border-top-color: #374151
    }
    .start-working-btn[data-v-1f41d1eb] {
        background: #fff;
        color: #232425
    }
    .start-working-btn[data-v-1f41d1eb]:hover:not(:disabled) {
        background: #e5e5e5
    }
    .cancel-btn[data-v-1f41d1eb] {
        background: #000;
        color: #fff
    }
    .cancel-btn[data-v-1f41d1eb]:hover,
    .cancel-btn[data-v-1f41d1eb]:hover:not(:disabled) {
        background: #333
    }
}

@media (max-width:768px) {
    .overlay[data-v-1f41d1eb] {
        padding: 20px 16px 16px
    }
    .modal[data-v-1f41d1eb] {
        max-width: none
    }
    .modal-header[data-v-1f41d1eb] {
        padding: 24px 16px 0
    }
    .modal-header h2[data-v-1f41d1eb] {
        font-size: 1.25rem
    }
    .modal-content[data-v-1f41d1eb] {
        padding: 16px 16px 0
    }
    .mode-option[data-v-1f41d1eb] {
        padding: 10px 16px
    }
    .mode-title[data-v-1f41d1eb] {
        font-size: 1rem
    }
    .mode-description[data-v-1f41d1eb] {
        font-size: .8rem
    }
    .mode-example[data-v-1f41d1eb] {
        font-size: .75rem
    }
    .rec-badge[data-v-1f41d1eb] {
        font-size: 9px;
        padding: 0 6px
    }
    .check-icon[data-v-1f41d1eb] {
        height: 14px;
        width: 14px
    }
    .modal-footer[data-v-1f41d1eb] {
        gap: 10px;
        padding: 20px 16px 16px
    }
}

.prompt-input-wrapper-upper[data-v-55a30980] {
    display: flex;
    justify-content: center;
    width: 100%
}

.prompt-input-wrapper-upper[data-v-55a30980] textarea {
    min-height: 52px
}

.docs-template-selector[data-v-55a30980] {
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    left: 0;
    margin: 0 auto;
    overflow: hidden;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1000
}

.main-content-loading-overlay[data-v-55a30980] {
    align-items: center;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    background: #fffc;
    display: flex;
    justify-content: center;
    pointer-events: auto
}

.loading-content[data-v-55a30980] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    pointer-events: none
}

.loading-text[data-v-55a30980] {
    color: #666;
    font-size: 1rem;
    font-weight: 500;
    margin: 0
}

@media (prefers-color-scheme:dark) {
    .docs-template-selector[data-v-55a30980] {
        background-color: #1a1a1a
    }
    .main-content-loading-overlay[data-v-55a30980] {
        background: #1a1a1acc
    }
    .loading-text[data-v-55a30980] {
        color: #ccc
    }
}

.header-controls[data-v-55a30980] {
    align-items: center;
    background: #fff;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    justify-content: flex-start;
    padding: 18px 24px;
    z-index: 10
}

@media (prefers-color-scheme:dark) {
    .header-controls[data-v-55a30980] {
        background: #1a1a1a
    }
}

.header-left[data-v-55a30980] {
    flex-direction: row;
    gap: 16px;
    justify-content: flex-start
}

.back-icon[data-v-55a30980],
.header-left[data-v-55a30980] {
    align-items: center;
    display: flex
}

.back-icon[data-v-55a30980] {
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    flex-shrink: 0;
    height: 28px;
    justify-content: center;
    padding: 4px;
    width: 28px
}

@media (hover:hover) {
    .back-icon[data-v-55a30980]:hover {
        background-color: #f5f5f5
    }
}

@media (prefers-color-scheme:dark) {
    .back-icon[data-v-55a30980] {
        background-color: #333
    }
    @media (hover:hover) {
        .back-icon[data-v-55a30980]:hover {
            background-color: #444
        }
    }
}

.left-toggle[data-v-55a30980] {
    align-items: center;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding: 4px
}

@media (hover:hover) {
    .left-toggle[data-v-55a30980]:hover {
        background-color: #f5f5f5
    }
}

@media (prefers-color-scheme:dark) {
    .left-toggle[data-v-55a30980] {
        background-color: #333
    }
    @media (hover:hover) {
        .left-toggle[data-v-55a30980]:hover {
            background-color: #444
        }
    }
}

.icon[data-v-55a30980] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    justify-content: center;
    width: 20px
}

.icon[data-v-55a30980] svg {
    height: 100%;
    width: 100%
}

.cursor-pointer[data-v-55a30980] {
    cursor: pointer
}

.main-content[data-v-55a30980] {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    position: relative
}

.header[data-v-55a30980] {
    margin-bottom: 3rem;
    text-align: center
}

.header h2[data-v-55a30980] {
    color: #232425;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: .5rem
}

.header p[data-v-55a30980] {
    color: #606366;
    font-size: 1.1rem;
    margin: 0
}

.error-section[data-v-55a30980],
.loading-section[data-v-55a30980] {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 3rem 0;
    text-align: center
}

.retry-button[data-v-55a30980] {
    background: #3498db;
    border: none;
    border-radius: 4px;
    color: #fff;
    cursor: pointer;
    margin-top: 1rem;
    padding: .5rem 1rem
}

.retry-button[data-v-55a30980]:hover {
    background: #2980b9
}

.prompt-input-section[data-v-55a30980] {
    margin-bottom: 8rem;
    padding: 1.5rem 0
}

.prompt-input-wrapper[data-v-55a30980] {
    display: flex;
    justify-content: center;
    margin: 0 auto;
    max-width: 800px
}

.editor-switch[data-v-55a30980] {
    bottom: 0;
    left: 0;
    position: absolute;
    z-index: 10
}

.editor-switch-buttons[data-v-55a30980] {
    background: #f4f4f4;
    border-radius: 8px;
    display: flex;
    gap: 2px;
    padding: 3px
}

.editor-option-button[data-v-55a30980] {
    align-items: center;
    background: #f4f4f4;
    border: none;
    border-radius: 6px;
    color: #6c757d;
    cursor: pointer;
    display: flex;
    font-size: 12px;
    font-weight: 500;
    gap: 6px;
    justify-content: center;
    padding: 2px 10px;
    transition: all .2s ease;
    white-space: nowrap
}

.editor-option-button.active[data-v-55a30980] {
    background: #232425;
    color: #fff
}

.check-icon[data-v-55a30980] {
    flex-shrink: 0;
    height: 12px;
    width: 12px
}

.option-label[data-v-55a30980] {
    font-size: 12px
}

@media (prefers-color-scheme:dark) {
    .editor-switch-buttons[data-v-55a30980] {
        background: #444;
        border-color: #444
    }
    .editor-option-button[data-v-55a30980] {
        background: #444;
        color: #fff
    }
    .editor-option-button.active[data-v-55a30980] {
        background: #fff;
        color: #232425;
        opacity: .8
    }
}

.prompt-suggestions-section[data-v-55a30980] {
    background: #f5f8fa;
    padding: 0
}

@media (prefers-color-scheme:dark) {
    .prompt-suggestions-section[data-v-55a30980] {
        background: #1a1a1a
    }
}

.prompt-suggestions-container[data-v-55a30980] {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding: 10px 20px;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.prompt-suggestions-container[data-v-55a30980]::-webkit-scrollbar {
    display: none
}

.prompt-suggestion-chip[data-v-55a30980] {
    align-items: center;
    background: #fff;
    border: 1px solid #eaf3ff;
    border-radius: 28px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    font-weight: 500;
    gap: 6px;
    padding: 2px 16px;
    transition: all .2s ease;
    white-space: nowrap
}

.prompt-suggestion-chip .icon[data-v-55a30980] {
    flex-shrink: 0;
    height: 12px;
    width: 12px
}

.suggestion-title[data-v-55a30980] {
    flex-shrink: 0
}

.suggestion-arrow[data-v-55a30980] {
    flex-shrink: 0;
    height: 12px;
    opacity: .6;
    transition: opacity .2s ease;
    width: 12px
}

.prompt-suggestion-chip[data-v-55a30980]:hover {
    border-color: #3498db;
    color: #3498db
}

.prompt-suggestion-chip:hover .suggestion-arrow[data-v-55a30980] {
    opacity: 1
}

@media (prefers-color-scheme:dark) {
    .prompt-suggestion-chip[data-v-55a30980] {
        background: #333;
        border-color: #444;
        color: #ccc
    }
    .prompt-suggestion-chip[data-v-55a30980]:hover {
        background: #444;
        border-color: #3498db;
        color: #3498db
    }
}

.categories-section[data-v-55a30980] {
    margin-bottom: 2rem;
    padding: 0 20px;
    position: relative
}

.category-tabs[data-v-55a30980] {
    display: flex;
    flex-wrap: nowrap;
    gap: .5rem;
    justify-content: flex-start;
    overflow-x: auto;
    scrollbar-width: none
}

.category-tabs[data-v-55a30980]::-webkit-scrollbar {
    display: none
}

.scroll-indicator[data-v-55a30980] {
    align-items: center;
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 0 2px 8px #00000026;
    cursor: pointer;
    display: flex;
    height: 28px;
    justify-content: center;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    transition: opacity .2s;
    width: 28px;
    z-index: 2
}

.scroll-indicator.left-scroll[data-v-55a30980] {
    left: -10px
}

.scroll-indicator.right-scroll[data-v-55a30980] {
    right: -10px
}

.scroll-indicator .icon[data-v-55a30980] {
    color: #666;
    height: 16px;
    width: 16px
}

.scroll-indicator .icon[data-v-55a30980] svg {
    height: 100%;
    width: 100%
}

.scroll-indicator .icon[data-v-55a30980] path {
    stroke: currentColor;
    fill: none
}

@media (hover:hover) {
    .scroll-indicator[data-v-55a30980]:hover {
        background-color: #f5f5f5
    }
}

@media (prefers-color-scheme:dark) {
    .scroll-indicator[data-v-55a30980] {
        background-color: #2a2a2a;
        box-shadow: 0 2px 8px #00000080
    }
    .scroll-indicator .icon[data-v-55a30980] {
        color: #ccc
    }
    @media (hover:hover) {
        .scroll-indicator[data-v-55a30980]:hover {
            background-color: #3a3a3a
        }
    }
}

.category-tab[data-v-55a30980] {
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 20px;
    cursor: pointer;
    flex-shrink: 0;
    font-size: 14px;
    padding: .5rem 1rem;
    transition: all .3s ease
}

.category-tab[data-v-55a30980]:hover {
    background: #e5e5e5
}

.category-tab.active[data-v-55a30980] {
    background: #f5f5f5;
    border: 1px solid #efefef;
    color: #232425;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    text-align: center
}

.category-tab.active[data-v-55a30980]:hover {
    background: #f5f5f5;
    color: #232425;
    opacity: .8
}

.template-waterfall[data-v-55a30980] {
    display: flex;
    flex-direction: row;
    gap: 16px;
    margin-bottom: 3rem
}

.template-column[data-v-55a30980] {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: var(--513d5063)
}

.template-card-wrapper[data-v-55a30980] {
    background: #fafafa;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    padding: 0;
    position: relative;
    transition: transform .3s ease;
    width: 100%
}

@media (hover:hover) {
    .template-card-wrapper[data-v-55a30980]:hover {
        transform: scale(1.02)
    }
}

@media (prefers-color-scheme:dark) {
    .template-card-wrapper[data-v-55a30980] {
        background: #333
    }
}

.template-card[data-v-55a30980] {
    background: transparent;
    border: 1px solid #efefef;
    border-radius: 12px;
    cursor: pointer;
    position: relative;
    transition: all .3s ease
}

.template-card[data-v-55a30980],
.template-preview[data-v-55a30980] {
    height: -moz-fit-content;
    height: fit-content;
    overflow: hidden;
    width: 100%
}

.template-preview[data-v-55a30980] {
    align-items: center;
    background: #f8f9fa;
    display: flex;
    justify-content: center
}

.template-screenshot[data-v-55a30980] {
    height: auto;
    margin: -1px;
    -o-object-fit: contain;
    object-fit: contain;
    transform: scale(1.02);
    width: 101%
}

.template-icon[data-v-55a30980] {
    color: #3498db;
    height: 48px;
    width: 48px
}

.template-card.blank-template-card[data-v-55a30980] {
    box-sizing: border-box;
    padding: 12px
}

.template-waterfall .blank-template-card[data-v-55a30980] {
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #efefef;
    border-radius: 12px;
    display: flex;
    justify-content: center
}

.template-waterfall .blank-template-card[data-v-55a30980]:hover {
    background: #f0f9ff;
    border-color: #0f7fff
}

.blank-template-content[data-v-55a30980] {
    align-items: center;
    color: #606366;
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
    transition: color .3s ease
}

.template-waterfall .blank-template-card:hover .blank-template-content[data-v-55a30980] {
    color: #0f7fff
}

.blank-template-content .blank-icon[data-v-55a30980] {
    height: 32px;
    width: 32px
}

.blank-template-content .blank-text[data-v-55a30980] {
    font-size: 1rem;
    font-weight: 500;
    margin: 0
}

.template-waterfall .template-content[data-v-55a30980] {
    padding: 1rem
}

.template-waterfall .template-content h3[data-v-55a30980] {
    color: #232425;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 .5rem
}

.template-waterfall .template-content p[data-v-55a30980] {
    color: #606366;
    font-size: .9rem;
    line-height: 1.4;
    margin: 0
}

@media (max-width:768px) {
    .main-content[data-v-55a30980] {
        padding: 1rem
    }
    .header h2[data-v-55a30980] {
        font-size: 1.5rem
    }
    .header p[data-v-55a30980] {
        font-size: 1rem
    }
    .header-controls[data-v-55a30980] {
        padding: 12px 16px
    }
    .category-tabs[data-v-55a30980] {
        justify-content: flex-start
    }
    .prompt-input-section[data-v-55a30980] {
        padding: 1rem 0
    }
    .prompt-input-wrapper[data-v-55a30980] {
        margin: 0;
        max-width: none
    }
    .prompt-suggestions-section[data-v-55a30980] {
        padding: 0
    }
    .editor-switch[data-v-55a30980] {
        bottom: 4px
    }
    .editor-switch-buttons[data-v-55a30980] {
        padding: 1px
    }
    .editor-option-button[data-v-55a30980] {
        font-size: 11px;
        padding: 3px 8px
    }
}

@media (max-width:1220px) {
    .docs-template-selector[data-v-55a30980] {
        padding-left: 0
    }
    .header-controls[data-v-55a30980] {
        background: #fff;
        left: 0;
        padding: 12px 16px;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 1001
    }
    .main-content[data-v-55a30980] {
        margin-left: 0;
        padding: 80px 1rem 1rem
    }
}

@media (min-width:1221px) {
    .main-content[data-v-55a30980] {
        transition: margin-left .3s ease
    }
    .main-content.full-width[data-v-55a30980] {
        margin-left: 0
    }
}

@media (prefers-color-scheme:dark) {
    .header h2[data-v-55a30980] {
        color: #fff
    }
    .header p[data-v-55a30980] {
        color: #ccc
    }
    .template-waterfall .blank-template-card[data-v-55a30980] {
        background: #333;
        border-color: #555
    }
    .template-waterfall .blank-template-card[data-v-55a30980]:hover {
        background: #1a2a3a;
        border-color: #0f7fff
    }
    .template-waterfall .template-content h3[data-v-55a30980] {
        color: #fff
    }
    .template-waterfall .template-content p[data-v-55a30980] {
        color: #ccc
    }
    .category-tab[data-v-55a30980] {
        background: #333;
        border-color: #444;
        color: #fff
    }
    .category-tab[data-v-55a30980]:hover {
        background: #444;
        color: #fff
    }
    .template-preview[data-v-55a30980] {
        background: #2a2a2a
    }
    @media (max-width:1220px) {
        .header-controls[data-v-55a30980] {
            background: #1a1a1a
        }
    }
}

.prompt-input-wrapper-upper[data-v-23f7a584] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 0 12px;
    position: relative;
    width: 100%
}

.prompt-input-wrapper-upper[data-v-23f7a584] textarea {
    min-height: 52px
}

.code-sandbox-template-selector[data-v-23f7a584] {
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    left: 0;
    margin: 0 auto;
    overflow: hidden;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1000
}

.main-content-loading-overlay[data-v-23f7a584] {
    align-items: center;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    background: #fffc;
    display: flex;
    justify-content: center;
    pointer-events: auto
}

.loading-content[data-v-23f7a584] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    pointer-events: none
}

.loading-text[data-v-23f7a584] {
    color: #666;
    font-size: 1rem;
    font-weight: 500;
    margin: 0
}

@media (prefers-color-scheme:dark) {
    .code-sandbox-template-selector[data-v-23f7a584] {
        background-color: #1a1a1a
    }
    .main-content-loading-overlay[data-v-23f7a584] {
        background: #1a1a1acc
    }
    .loading-text[data-v-23f7a584] {
        color: #ccc
    }
}

.header-controls[data-v-23f7a584] {
    align-items: center;
    background: #fff;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    justify-content: flex-start;
    padding: 18px 24px;
    z-index: 10
}

@media (prefers-color-scheme:dark) {
    .header-controls[data-v-23f7a584] {
        background: #1a1a1a
    }
}

.header-left[data-v-23f7a584] {
    flex-direction: row;
    gap: 16px;
    justify-content: flex-start
}

.back-icon[data-v-23f7a584],
.header-left[data-v-23f7a584] {
    align-items: center;
    display: flex
}

.back-icon[data-v-23f7a584] {
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    flex-shrink: 0;
    height: 28px;
    justify-content: center;
    padding: 4px;
    width: 28px
}

@media (hover:hover) {
    .back-icon[data-v-23f7a584]:hover {
        background-color: #f5f5f5
    }
}

@media (prefers-color-scheme:dark) {
    .back-icon[data-v-23f7a584] {
        background-color: #333
    }
    @media (hover:hover) {
        .back-icon[data-v-23f7a584]:hover {
            background-color: #444
        }
    }
}

.left-toggle[data-v-23f7a584] {
    align-items: center;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding: 4px
}

@media (hover:hover) {
    .left-toggle[data-v-23f7a584]:hover {
        background-color: #f5f5f5
    }
}

@media (prefers-color-scheme:dark) {
    .left-toggle[data-v-23f7a584] {
        background-color: #333
    }
    @media (hover:hover) {
        .left-toggle[data-v-23f7a584]:hover {
            background-color: #444
        }
    }
}

.icon[data-v-23f7a584] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    justify-content: center;
    width: 20px
}

.icon[data-v-23f7a584] svg {
    height: 100%;
    width: 100%
}

.cursor-pointer[data-v-23f7a584] {
    cursor: pointer
}

.main-content[data-v-23f7a584] {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    position: relative
}

.header[data-v-23f7a584] {
    margin-bottom: 3rem;
    text-align: center
}

.header h2[data-v-23f7a584] {
    color: #232425;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: .5rem
}

.header p[data-v-23f7a584] {
    color: #606366;
    font-size: 1.1rem;
    margin: 0
}

.form-container[data-v-23f7a584] {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 1px 3px #0000001a;
    margin: 0 auto;
    max-width: 800px;
    padding: 2rem
}

.form-section[data-v-23f7a584] {
    border-bottom: 1px solid #f3f4f6;
    margin-bottom: 2.5rem;
    padding-bottom: 2rem
}

.form-section[data-v-23f7a584]:last-child {
    border-bottom: none;
    margin-bottom: 0
}

.section-title[data-v-23f7a584] {
    color: #1f2937;
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0 0 1.5rem
}

.form-actions[data-v-23f7a584] {
    display: flex;
    justify-content: center;
    padding-top: 2rem
}

.ssh-section[data-v-23f7a584] {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-top: 1.5rem;
    padding: 1.5rem
}

.ssh-header[data-v-23f7a584] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem
}

.ssh-header h4[data-v-23f7a584] {
    color: #374151;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0
}

.no-ssh-hosts[data-v-23f7a584] {
    color: #6b7280;
    padding: 2rem;
    text-align: center
}

.no-ssh-hosts p[data-v-23f7a584] {
    font-style: italic;
    margin: 0
}

.ssh-host-radio[data-v-23f7a584] {
    width: 100%
}

[data-v-23f7a584] .ssh-host-radio .n-radio {
    align-items: flex-start;
    width: 100%
}

[data-v-23f7a584] .ssh-host-radio .n-radio__label {
    width: 100%
}

.ssh-host-info[data-v-23f7a584] {
    padding: .5rem 0;
    width: 100%
}

.ssh-host-primary[data-v-23f7a584] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: .5rem
}

.ssh-connection[data-v-23f7a584] {
    color: #1f2937;
    font-size: .9rem;
    font-weight: 600
}

.ssh-connection[data-v-23f7a584],
.ssh-host-details[data-v-23f7a584] {
    font-family: Monaco, Consolas, monospace
}

.ssh-host-details[data-v-23f7a584] {
    color: #6b7280;
    font-size: .8rem
}

.modal-header[data-v-23f7a584] {
    align-items: center;
    display: flex;
    gap: 8px
}

.modal-icon[data-v-23f7a584] {
    height: 20px;
    width: 20px
}

.modal-content[data-v-23f7a584] {
    padding: 16px 0
}

.auth-note[data-v-23f7a584] {
    margin-top: 16px
}

.modal-actions[data-v-23f7a584] {
    display: flex;
    gap: 12px;
    justify-content: flex-end
}

.github-actions[data-v-23f7a584] {
    margin-top: 8px
}

.selected-github-repo[data-v-23f7a584] {
    margin-top: 12px
}

.github-repo-info[data-v-23f7a584] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.github-repo-info .repo-name[data-v-23f7a584] {
    color: #1f2937;
    font-size: 16px;
    font-weight: 600
}

.github-repo-info .repo-description[data-v-23f7a584] {
    color: #6b7280;
    font-size: 14px;
    line-height: 1.4
}

.repo-meta[data-v-23f7a584] {
    align-items: center;
    display: flex;
    gap: 12px
}

.repo-language[data-v-23f7a584] {
    background: #f3f4f6;
    border-radius: 12px;
    color: #6b7280;
    font-size: 12px;
    padding: 2px 8px
}

.github-error[data-v-23f7a584] {
    margin-top: 8px
}

.prompt-input-section[data-v-23f7a584] {
    margin-bottom: 8rem;
    padding: 1.5rem 0
}

.prompt-input-wrapper[data-v-23f7a584] {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 1px 3px #0000001a;
    margin: 0 auto;
    max-width: 750px;
    overflow: hidden;
    position: relative;
    transition: all .3s ease
}

.language-selector[data-v-23f7a584] {
    bottom: 0;
    left: 0;
    position: absolute;
    z-index: 10
}

.language-selector-buttons[data-v-23f7a584] {
    background: #f4f4f4;
    border-radius: 8px;
    display: flex;
    gap: 2px;
    padding: 3px
}

.language-option-button[data-v-23f7a584] {
    align-items: center;
    background: #f4f4f4;
    border: none;
    border-radius: 6px;
    color: #6c757d;
    cursor: pointer;
    display: flex;
    font-size: 12px;
    font-weight: 500;
    gap: 6px;
    justify-content: center;
    padding: 2px 10px;
    transition: all .2s ease;
    white-space: nowrap
}

.language-option-button.active[data-v-23f7a584] {
    background: #232425;
    color: #fff
}

.check-icon[data-v-23f7a584] {
    flex-shrink: 0;
    height: 12px;
    width: 12px
}

.option-label[data-v-23f7a584] {
    font-size: 12px
}

@media (prefers-color-scheme:dark) {
    .prompt-input-wrapper[data-v-23f7a584] {
        background: #333;
        border-color: #444
    }
    .language-selector-buttons[data-v-23f7a584] {
        background: #444;
        border-color: #444
    }
    .language-option-button[data-v-23f7a584] {
        background: #444;
        color: #fff
    }
    .language-option-button.active[data-v-23f7a584] {
        background: #fff;
        color: #232425;
        opacity: .8
    }
}

.prompt-suggestions-section[data-v-23f7a584] {
    background: #f5f8fa;
    padding: 0
}

@media (prefers-color-scheme:dark) {
    .prompt-suggestions-section[data-v-23f7a584] {
        background: #1a1a1a
    }
}

.prompt-suggestions-container[data-v-23f7a584] {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding: 10px 20px;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.prompt-suggestions-container[data-v-23f7a584]::-webkit-scrollbar {
    display: none
}

.prompt-suggestion-chip[data-v-23f7a584] {
    align-items: center;
    background: #fff;
    border: 1px solid #eaf3ff;
    border-radius: 28px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    font-weight: 500;
    gap: 6px;
    padding: 2px 16px;
    transition: all .2s ease;
    white-space: nowrap
}

.prompt-suggestion-chip .icon[data-v-23f7a584] {
    flex-shrink: 0;
    height: 12px;
    width: 12px
}

.suggestion-title[data-v-23f7a584] {
    flex-shrink: 0
}

.suggestion-arrow[data-v-23f7a584] {
    flex-shrink: 0;
    height: 12px;
    opacity: .6;
    transition: opacity .2s ease;
    width: 12px
}

.prompt-suggestion-chip[data-v-23f7a584]:hover {
    border-color: #3498db;
    color: #3498db
}

.prompt-suggestion-chip:hover .suggestion-arrow[data-v-23f7a584] {
    opacity: 1
}

@media (prefers-color-scheme:dark) {
    .prompt-suggestion-chip[data-v-23f7a584] {
        background: #333;
        border-color: #444;
        color: #ccc
    }
    .prompt-suggestion-chip[data-v-23f7a584]:hover {
        background: #444;
        border-color: #3498db;
        color: #3498db
    }
}

@media (max-width:768px) {
    .main-content[data-v-23f7a584] {
        padding: 1rem
    }
    .header h2[data-v-23f7a584] {
        font-size: 1.5rem
    }
    .header p[data-v-23f7a584] {
        font-size: 1rem
    }
    .header-controls[data-v-23f7a584] {
        padding: 12px 16px
    }
    .prompt-input-section[data-v-23f7a584] {
        padding: 1rem 0
    }
    .prompt-input-wrapper[data-v-23f7a584] {
        margin: 0;
        max-width: none
    }
    .prompt-suggestions-section[data-v-23f7a584] {
        padding: 0
    }
    .language-selector[data-v-23f7a584] {
        bottom: 4px
    }
    .language-selector-buttons[data-v-23f7a584] {
        padding: 1px
    }
    .language-option-button[data-v-23f7a584] {
        font-size: 11px;
        padding: 3px 8px
    }
}

@media (max-width:1220px) {
    .code-sandbox-template-selector[data-v-23f7a584] {
        padding-left: 0
    }
    .header-controls[data-v-23f7a584] {
        background: #fff;
        left: 0;
        padding: 12px 16px;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 1001
    }
    .main-content[data-v-23f7a584] {
        margin-left: 0;
        padding: 80px 1rem 1rem
    }
}

@media (min-width:1221px) {
    .main-content[data-v-23f7a584] {
        transition: margin-left .3s ease
    }
    .main-content.full-width[data-v-23f7a584] {
        margin-left: 0
    }
}

@media (prefers-color-scheme:dark) {
    .header h2[data-v-23f7a584] {
        color: #fff
    }
    .header p[data-v-23f7a584] {
        color: #ccc
    }
    .form-container[data-v-23f7a584] {
        background: #333;
        border-color: #444
    }
    .form-section[data-v-23f7a584] {
        border-bottom-color: #444
    }
    .section-title[data-v-23f7a584] {
        color: #fff
    }
    .ssh-section[data-v-23f7a584] {
        background: #2a2a2a;
        border-color: #444
    }
    .ssh-header h4[data-v-23f7a584] {
        color: #fff
    }
    .no-ssh-hosts[data-v-23f7a584] {
        color: #ccc
    }
    .ssh-connection[data-v-23f7a584] {
        color: #fff
    }
    .ssh-host-details[data-v-23f7a584] {
        color: #ccc
    }
    .github-repo-info .repo-name[data-v-23f7a584] {
        color: #fff
    }
    .github-repo-info .repo-description[data-v-23f7a584] {
        color: #ccc
    }
    .repo-language[data-v-23f7a584] {
        background: #444;
        color: #ccc
    }
    @media (max-width:1220px) {
        .header-controls[data-v-23f7a584] {
            background: #1a1a1a
        }
    }
    .github-status-card[data-v-23f7a584] {
        background: #2a2a2a;
        border-color: #444
    }
    .github-status-header[data-v-23f7a584],
    .github-user-info .username[data-v-23f7a584] {
        color: #fff
    }
    .github-user-info .auth-type[data-v-23f7a584],
    .github-user-info .display-name[data-v-23f7a584],
    .github-user-info .email[data-v-23f7a584] {
        color: #ccc
    }
    .repository-card[data-v-23f7a584],
    .selected-repo-card[data-v-23f7a584] {
        background: #2a2a2a;
        border-color: #444
    }
    .repo-header[data-v-23f7a584],
    .repo-name[data-v-23f7a584],
    .selected-repo-header[data-v-23f7a584] {
        color: #fff
    }
    .repo-description[data-v-23f7a584],
    .repo-details .language[data-v-23f7a584],
    .repo-details .updated[data-v-23f7a584] {
        color: #ccc
    }
    .repositories-header h4[data-v-23f7a584] {
        color: #fff
    }
}

.github-browse-section[data-v-23f7a584],
.github-url-section[data-v-23f7a584] {
    margin-top: 16px
}

.github-status-card[data-v-23f7a584] {
    margin-bottom: 16px
}

.github-status-header[data-v-23f7a584] {
    align-items: center;
    display: flex;
    justify-content: space-between
}

.github-user-info[data-v-23f7a584] {
    margin-bottom: 12px
}

.user-details[data-v-23f7a584] {
    align-items: center;
    display: flex;
    gap: 12px
}

.user-avatar[data-v-23f7a584] {
    border-radius: 50%;
    height: 32px;
    width: 32px
}

.user-text[data-v-23f7a584] {
    display: flex;
    flex-direction: column;
    gap: 2px
}

.username[data-v-23f7a584] {
    font-size: 14px;
    font-weight: 600
}

.display-name[data-v-23f7a584] {
    color: #666;
    font-size: 13px
}

.email[data-v-23f7a584] {
    color: #666;
    font-size: 12px
}

.auth-type[data-v-23f7a584] {
    color: #888;
    font-size: 12px
}

.github-actions[data-v-23f7a584] {
    display: flex;
    flex-wrap: wrap;
    gap: 8px
}

.selected-repo-card[data-v-23f7a584] {
    margin-bottom: 16px
}

.selected-repo-header[data-v-23f7a584] {
    align-items: center;
    display: flex;
    justify-content: space-between
}

.selected-repo-info[data-v-23f7a584] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.repo-name[data-v-23f7a584] {
    font-size: 16px;
    font-weight: 600
}

.repo-description[data-v-23f7a584] {
    color: #666;
    font-size: 14px;
    line-height: 1.4
}

.repo-actions[data-v-23f7a584] {
    display: flex;
    gap: 12px;
    margin-top: 8px
}

.repositories-section[data-v-23f7a584] {
    margin-bottom: 24px
}

.repositories-header[data-v-23f7a584] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px
}

.repositories-header h4[data-v-23f7a584] {
    font-size: 16px;
    font-weight: 600;
    margin: 0
}

.repositories-list[data-v-23f7a584] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.repository-card[data-v-23f7a584] {
    padding: 16px
}

.repo-header[data-v-23f7a584] {
    align-items: center;
    display: flex;
    gap: 8px;
    margin-bottom: 8px
}

.repo-header .repo-name[data-v-23f7a584] {
    font-size: 14px;
    font-weight: 600
}

.repo-details[data-v-23f7a584] {
    align-items: center;
    color: #666;
    display: flex;
    font-size: 12px;
    gap: 16px;
    margin-bottom: 12px
}

.repo-details .language[data-v-23f7a584] {
    font-weight: 500
}

.app-repository-card[data-v-23f7a584] {
    border-left: 3px solid #22c55e
}

.oauth-repository-card[data-v-23f7a584] {
    border-left: 3px solid #3b82f6
}

.pagination-section[data-v-23f7a584] {
    border-top: 1px solid #e5e7eb;
    margin-top: 16px;
    padding-top: 16px
}

@media (prefers-color-scheme:dark) {
    .pagination-section[data-v-23f7a584] {
        border-top-color: #444
    }
}

.ssh-path-note[data-v-23f7a584] {
    color: #666;
    font-size: 12px;
    margin-top: 4px
}

@media (prefers-color-scheme:dark) {
    .ssh-path-note[data-v-23f7a584] {
        color: #ccc
    }
}

.modal-overlay[data-v-0fb89a27] {
    align-items: center;
    background-color: #000000b3;
    bottom: 0;
    display: flex;
    justify-content: center;
    left: 0;
    padding: 20px;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 10010
}

.modal-container[data-v-0fb89a27] {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px #00000026;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-height: 90vh;
    max-width: 1124px;
    overflow: visible;
    padding: 24px
}

.modal-header[data-v-0fb89a27] {
    align-items: center;
    display: flex;
    justify-content: space-between
}

.modal-title[data-v-0fb89a27] {
    color: #333;
    font-size: 20px;
    font-weight: 600;
    margin: 0
}

.close-button[data-v-0fb89a27] {
    background: none;
    border: none;
    border-radius: 4px;
    color: #666;
    cursor: pointer;
    padding: 4px;
    transition: color .2s ease, background-color .2s ease
}

.close-button[data-v-0fb89a27]:hover {
    background: #f8f9fa;
    color: #333
}

.close-icon[data-v-0fb89a27] {
    height: 24px;
    width: 24px;
    fill: currentColor
}

.modal-content[data-v-0fb89a27] {
    display: flex;
    gap: 24px;
    overflow: visible
}

.template-preview[data-v-0fb89a27] {
    aspect-ratio: 16/9;
    background-color: #f0f0f0;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    max-width: 800px;
    min-width: 400px;
    -o-object-fit: contain;
    object-fit: contain;
    overflow: hidden
}

.image-container[data-v-0fb89a27],
.template-preview[data-v-0fb89a27] {
    align-items: center;
    display: flex;
    justify-content: center;
    position: relative;
    width: 100%
}

.image-container[data-v-0fb89a27] {
    height: 100%
}

.main-preview-image[data-v-0fb89a27] {
    background-color: #f0f0f0;
    display: block;
    min-height: 150px;
    min-width: 200px;
    -o-object-fit: contain;
    object-fit: contain;
    opacity: 0;
    transition: opacity .3s ease
}

.main-preview-image.fade-in[data-v-0fb89a27] {
    opacity: 1
}

.loading-overlay[data-v-0fb89a27] {
    background-color: #f0f0f0cc;
    z-index: 1
}

.image-placeholder[data-v-0fb89a27],
.loading-overlay[data-v-0fb89a27] {
    align-items: center;
    bottom: 0;
    display: flex;
    justify-content: center;
    left: 0;
    position: absolute;
    right: 0;
    top: 0
}

.image-placeholder[data-v-0fb89a27] {
    color: #666;
    flex-direction: column;
    gap: 16px;
    height: 100%;
    width: 100%
}

.loading-spinner[data-v-0fb89a27] {
    animation: spin-0fb89a27 1s linear infinite;
    border: 3px solid #f3f3f3;
    border-radius: 50%;
    border-top-color: #007bff;
    height: 40px;
    width: 40px
}

.loading-text[data-v-0fb89a27] {
    color: #999;
    font-size: 14px
}

@keyframes spin-0fb89a27 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.template-details[data-v-0fb89a27] {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 300px
}

.template-info[data-v-0fb89a27] {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 0;
    overflow: hidden
}

.template-specs-container[data-v-0fb89a27] {
    flex: 1;
    margin-bottom: 20px;
    min-height: 0;
    overflow-y: auto;
    padding-right: 8px
}

.template-specs-container[data-v-0fb89a27]::-webkit-scrollbar {
    width: 6px
}

.template-specs-container[data-v-0fb89a27]::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px
}

.template-specs-container[data-v-0fb89a27]::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px
}

.template-specs-container[data-v-0fb89a27]::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8
}

.template-title[data-v-0fb89a27] {
    color: #333;
    font-size: 18px;
    font-weight: 700;
    margin: 0 0 16px
}

.template-description[data-v-0fb89a27] {
    color: #555;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px
}

.template-specs[data-v-0fb89a27] {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 24px
}

.spec-tag[data-v-0fb89a27] {
    background-color: #eee;
    border-radius: 8px;
    color: #909499;
    font-size: 14px;
    padding: 4px 8px
}

.color-spec[data-v-0fb89a27] {
    align-items: center;
    display: flex;
    gap: 8px
}

.color-label[data-v-0fb89a27] {
    margin: 0
}

.color-swatches[data-v-0fb89a27] {
    display: flex;
    gap: 4px
}

.color-swatch-mini[data-v-0fb89a27] {
    border-radius: 4px;
    height: 16px;
    width: 16px
}

.template-specifications[data-v-0fb89a27] {
    margin-bottom: 24px
}

.spec-item[data-v-0fb89a27] {
    display: flex;
    font-size: 14px;
    margin-bottom: 8px
}

.spec-label[data-v-0fb89a27] {
    color: #666;
    font-weight: 500;
    min-width: 100px
}

.spec-value[data-v-0fb89a27] {
    color: #333
}

.use-template-button[data-v-0fb89a27] {
    align-items: center;
    background-color: #222;
    border: none;
    border-radius: 18px;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    font-size: 16px;
    font-weight: 400;
    gap: 8px;
    justify-content: center;
    line-height: 1.5;
    padding: 6px 24px;
    transition: background-color .2s
}

.use-template-button[data-v-0fb89a27]:hover {
    background-color: #000
}

.use-icon[data-v-0fb89a27] {
    height: 20px;
    width: 20px;
    fill: currentColor
}

.preview-section[data-v-0fb89a27] {
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-width: 800px;
    min-width: 400px
}

.thumbnails-container[data-v-0fb89a27] {
    margin-top: 0;
    position: relative
}

.nav-arrow[data-v-0fb89a27] {
    align-items: center;
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 0 4px 4px #00000026;
    cursor: pointer;
    display: flex;
    font-size: 20px;
    height: 36px;
    justify-content: center;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    transition: all .2s ease;
    width: 36px;
    z-index: 2
}

.nav-arrow svg[data-v-0fb89a27] {
    color: #666;
    height: 16px;
    width: 16px;
    fill: none
}

.nav-arrow[data-v-0fb89a27]:hover {
    background-color: #f8f9fa;
    box-shadow: 0 4px 8px #007bff40
}

.nav-arrow:hover svg[data-v-0fb89a27] {
    color: #007bff
}

.nav-prev[data-v-0fb89a27] {
    left: -18px
}

.nav-next[data-v-0fb89a27] {
    right: -18px
}

.thumbnails-scroll[data-v-0fb89a27] {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    cursor: grab;
    scroll-behavior: smooth;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none
}

.thumbnails-scroll[data-v-0fb89a27]:active {
    cursor: grabbing
}

.thumbnails-scroll[data-v-0fb89a27]::-webkit-scrollbar {
    display: none
}

.thumbnail-item[data-v-0fb89a27] {
    border: 1px solid #eaeaea;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    flex: 0 0 auto;
    height: 68px;
    overflow: hidden;
    position: relative;
    transition: all .2s ease;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    width: 120px
}

.thumbnail-item.active[data-v-0fb89a27],
.thumbnail-item[data-v-0fb89a27]:hover {
    border: 2px solid #007bff
}

.thumbnail-image[data-v-0fb89a27] {
    display: block;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    width: 100%;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    pointer-events: none
}

@media (max-width:768px) {
    .modal-overlay[data-v-0fb89a27] {
        align-items: stretch;
        background-color: transparent;
        justify-content: stretch;
        padding: 0
    }
    .modal-container[data-v-0fb89a27] {
        background-color: #fff;
        border-radius: 0;
        box-shadow: none;
        gap: 20px;
        height: 100vh;
        max-height: 100vh;
        max-width: 100vw;
        padding: 24px;
        width: 100%
    }
    .modal-header[data-v-0fb89a27] {
        padding: 0 0 16px
    }
    .modal-title[data-v-0fb89a27] {
        font-size: 22px;
        font-weight: 700
    }
    .close-button[data-v-0fb89a27] {
        align-items: center;
        display: flex;
        justify-content: center;
        min-height: 48px;
        min-width: 48px;
        padding: 12px
    }
    .close-icon[data-v-0fb89a27] {
        height: 24px;
        width: 24px
    }
    .modal-content[data-v-0fb89a27] {
        flex: 1;
        flex-direction: column;
        gap: 20px;
        min-height: 0;
        overflow: visible
    }
    .preview-section[data-v-0fb89a27] {
        gap: 16px
    }
    .preview-section[data-v-0fb89a27],
    .template-preview[data-v-0fb89a27] {
        flex-shrink: 0;
        max-width: none;
        min-width: auto
    }
    .template-preview[data-v-0fb89a27] {
        aspect-ratio: 16/9
    }
    .thumbnails-container[data-v-0fb89a27] {
        margin-top: 0
    }
    .nav-arrow[data-v-0fb89a27] {
        height: 40px;
        min-height: 48px;
        min-width: 48px;
        width: 40px
    }
    .nav-arrow svg[data-v-0fb89a27] {
        height: 18px;
        width: 18px
    }
    .nav-prev[data-v-0fb89a27] {
        left: -20px
    }
    .nav-next[data-v-0fb89a27] {
        right: -20px
    }
    .thumbnails-scroll[data-v-0fb89a27] {
        gap: 12px
    }
    .thumbnail-item[data-v-0fb89a27] {
        height: 68px;
        width: 120px
    }
    .template-details[data-v-0fb89a27] {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: space-between;
        min-height: 0;
        width: 100%
    }
    .template-info[data-v-0fb89a27] {
        flex: 1;
        min-height: 0
    }
    .template-specs-container[data-v-0fb89a27] {
        flex: 1;
        margin-bottom: 20px;
        min-height: 0;
        overflow-y: auto
    }
    .template-title[data-v-0fb89a27] {
        font-size: 20px;
        font-weight: 700;
        margin: 0 0 16px
    }
    .template-description[data-v-0fb89a27] {
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 20px
    }
    .template-specs[data-v-0fb89a27] {
        gap: 10px;
        margin-bottom: 20px
    }
    .spec-tag[data-v-0fb89a27] {
        font-size: 14px;
        font-weight: 500;
        padding: 6px 12px
    }
    .color-spec[data-v-0fb89a27] {
        gap: 8px
    }
    .color-swatch-mini[data-v-0fb89a27] {
        height: 18px;
        width: 18px
    }
    .use-template-button[data-v-0fb89a27] {
        border-radius: 26px;
        flex-shrink: 0;
        font-size: 18px;
        font-weight: 600;
        min-height: 52px;
        padding: 16px 32px;
        width: 100%
    }
}

@media (max-width:480px) {
    .modal-overlay[data-v-0fb89a27] {
        padding: 0
    }
    .modal-container[data-v-0fb89a27] {
        gap: 16px;
        max-width: 100vw;
        padding: 20px
    }
    .modal-header[data-v-0fb89a27] {
        padding: 0 0 12px
    }
    .modal-title[data-v-0fb89a27] {
        font-size: 20px;
        font-weight: 700
    }
    .close-button[data-v-0fb89a27] {
        min-height: 44px;
        min-width: 44px;
        padding: 10px
    }
    .close-icon[data-v-0fb89a27] {
        height: 20px;
        width: 20px
    }
    .modal-content[data-v-0fb89a27] {
        flex: 1;
        gap: 16px;
        min-height: 0
    }
    .preview-section[data-v-0fb89a27] {
        gap: 12px
    }
    .template-preview[data-v-0fb89a27] {
        aspect-ratio: 16/9
    }
    .template-title[data-v-0fb89a27] {
        font-size: 18px;
        font-weight: 700;
        margin: 0 0 12px
    }
    .template-description[data-v-0fb89a27] {
        font-size: 15px;
        line-height: 1.5;
        margin-bottom: 16px
    }
    .template-specs[data-v-0fb89a27] {
        gap: 8px;
        margin-bottom: 16px
    }
    .spec-tag[data-v-0fb89a27] {
        font-size: 13px;
        font-weight: 500;
        padding: 5px 10px
    }
    .color-swatch-mini[data-v-0fb89a27] {
        height: 16px;
        width: 16px
    }
    .use-template-button[data-v-0fb89a27] {
        font-size: 16px;
        font-weight: 600;
        min-height: 48px;
        padding: 14px 28px
    }
    .thumbnails-container[data-v-0fb89a27] {
        margin-top: 0
    }
    .nav-arrow[data-v-0fb89a27] {
        height: 36px;
        min-height: 44px;
        min-width: 44px;
        width: 36px
    }
    .nav-arrow svg[data-v-0fb89a27] {
        height: 16px;
        width: 16px
    }
    .nav-prev[data-v-0fb89a27] {
        left: -18px
    }
    .nav-next[data-v-0fb89a27] {
        right: -18px
    }
    .thumbnails-scroll[data-v-0fb89a27] {
        gap: 10px
    }
    .thumbnail-item[data-v-0fb89a27] {
        height: 56px;
        width: 100px
    }
}

@media (hover:none) and (pointer:coarse) {
    .close-button[data-v-0fb89a27]:hover {
        background: none
    }
    .use-template-button[data-v-0fb89a27]:hover {
        background-color: #222
    }
    .nav-arrow[data-v-0fb89a27]:hover {
        background-color: #fff;
        box-shadow: 0 4px 4px #00000026
    }
    .nav-arrow:hover svg[data-v-0fb89a27] {
        color: #666
    }
    .thumbnail-item[data-v-0fb89a27]:hover {
        border: 1px solid #eaeaea
    }
    .close-button[data-v-0fb89a27],
    .nav-arrow[data-v-0fb89a27],
    .thumbnail-item[data-v-0fb89a27],
    .use-template-button[data-v-0fb89a27] {
        transition: transform .15s ease;
        -webkit-tap-highlight-color: transparent
    }
    .close-button[data-v-0fb89a27]:active,
    .nav-arrow[data-v-0fb89a27]:active,
    .thumbnail-item[data-v-0fb89a27]:active,
    .use-template-button[data-v-0fb89a27]:active {
        transform: scale(.95)
    }
}

@media (max-width:768px) and (orientation:landscape) {
    .modal-overlay[data-v-0fb89a27] {
        padding: 0
    }
    .modal-container[data-v-0fb89a27] {
        border-radius: 0;
        max-height: 100vh;
        max-width: 100vw;
        padding: 20px
    }
    .modal-content[data-v-0fb89a27] {
        flex: 1;
        flex-direction: row;
        gap: 20px;
        min-height: 0
    }
    .preview-section[data-v-0fb89a27] {
        max-height: 70vh;
        max-width: none;
        min-width: auto
    }
    .template-preview[data-v-0fb89a27] {
        aspect-ratio: auto;
        flex: 1;
        max-height: 50vh
    }
    .thumbnails-container[data-v-0fb89a27] {
        flex-shrink: 0;
        margin-top: 12px
    }
    .template-details[data-v-0fb89a27] {
        flex-shrink: 0;
        width: 250px
    }
    .template-title[data-v-0fb89a27] {
        font-size: 18px;
        font-weight: 700
    }
    .template-description[data-v-0fb89a27] {
        font-size: 15px;
        line-height: 1.5
    }
    .spec-tag[data-v-0fb89a27] {
        font-size: 13px;
        font-weight: 500
    }
    .use-template-button[data-v-0fb89a27] {
        font-size: 16px;
        font-weight: 600;
        min-height: 44px;
        padding: 12px 24px
    }
}

@media (prefers-color-scheme:dark) {
    .modal-container[data-v-0fb89a27] {
        background-color: #1a1a1a;
        color: #fff
    }
    .modal-title[data-v-0fb89a27] {
        color: #fff
    }
    .close-button[data-v-0fb89a27] {
        color: #ccc
    }
    .close-button[data-v-0fb89a27]:hover {
        background: #333;
        color: #fff
    }
    .template-preview[data-v-0fb89a27] {
        background-color: #2a2a2a
    }
    .image-placeholder[data-v-0fb89a27] {
        color: #ccc
    }
    .loading-text[data-v-0fb89a27] {
        color: #999
    }
    .template-title[data-v-0fb89a27] {
        color: #fff
    }
    .spec-tag[data-v-0fb89a27],
    .template-description[data-v-0fb89a27] {
        color: #ccc
    }
    .color-spec[data-v-0fb89a27],
    .spec-tag[data-v-0fb89a27] {
        background-color: #333
    }
    .template-specs-container[data-v-0fb89a27]::-webkit-scrollbar-track {
        background: #333
    }
    .template-specs-container[data-v-0fb89a27]::-webkit-scrollbar-thumb {
        background: #555
    }
    .template-specs-container[data-v-0fb89a27]::-webkit-scrollbar-thumb:hover {
        background: #666
    }
    .use-template-button[data-v-0fb89a27] {
        background-color: #007bff
    }
    .use-template-button[data-v-0fb89a27]:hover {
        background-color: #0056cc
    }
    .nav-arrow[data-v-0fb89a27] {
        background-color: #2a2a2a;
        box-shadow: 0 4px 4px #0000004d
    }
    .nav-arrow svg[data-v-0fb89a27] {
        color: #ccc
    }
    .nav-arrow[data-v-0fb89a27]:hover {
        background-color: #333;
        box-shadow: 0 4px 8px #007bff40
    }
    .nav-arrow:hover svg[data-v-0fb89a27] {
        color: #007bff
    }
    .thumbnail-item[data-v-0fb89a27] {
        border-color: #444
    }
    .thumbnail-item.active[data-v-0fb89a27],
    .thumbnail-item[data-v-0fb89a27]:hover {
        border-color: #007bff
    }
    @media (max-width:768px) {
        .modal-container[data-v-0fb89a27] {
            background-color: #1a1a1a
        }
    }
    @media (max-width:480px) {
        .modal-container[data-v-0fb89a27] {
            background-color: #1a1a1a
        }
    }
    @media (hover:none) and (pointer:coarse) {
        .close-button[data-v-0fb89a27]:hover {
            background: #333
        }
        .use-template-button[data-v-0fb89a27]:hover {
            background-color: #007bff
        }
        .nav-arrow[data-v-0fb89a27]:hover {
            background-color: #2a2a2a;
            box-shadow: 0 4px 4px #0000004d
        }
        .nav-arrow:hover svg[data-v-0fb89a27] {
            color: #ccc
        }
        .thumbnail-item[data-v-0fb89a27]:hover {
            border-color: #444
        }
    }
}

.template-card[data-v-091314e0] {
    box-sizing: border-box;
    cursor: pointer
}

.template-card[data-v-091314e0],
.template-card-inner[data-v-091314e0] {
    position: relative;
    transition: all .3s ease;
    width: 100%
}

.template-card-inner[data-v-091314e0] {
    align-items: center;
    background: transparent;
    border: 1px solid #efefef;
    border-radius: 12px;
    display: flex;
    height: 100%;
    justify-content: center;
    overflow: hidden
}

@media (hover:hover) {
    .template-card:hover .template-card-inner[data-v-091314e0] {
        transform: scale(1.02)
    }
}

.template-screenshot[data-v-091314e0] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.template-screenshot-container[data-v-091314e0] {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.template-icon[data-v-091314e0] {
    color: #3498db;
    height: 48px;
    width: 48px
}

.template-card .remove[data-v-091314e0],
.template-icon[data-v-091314e0] {
    align-items: center;
    display: flex;
    justify-content: center
}

.template-card .remove[data-v-091314e0] {
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    background-color: #222;
    border-radius: 50%;
    box-shadow: 0 4px 12px #00000026;
    cursor: pointer;
    height: 20px;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    right: -6px;
    top: -6px;
    transition: all .3s ease;
    width: 20px
}

.template-card:hover .remove[data-v-091314e0] {
    opacity: 1;
    pointer-events: auto
}

.remove[data-v-091314e0] svg {
    color: #fff;
    height: 14px;
    width: 14px
}

.remove[data-v-091314e0]:hover {
    background-color: #ff4757;
    box-shadow: 0 6px 16px #ff47574d;
    transform: scale(1.1)
}

.remove[data-v-091314e0]:hover svg {
    color: #fff
}

.template-use-button-overlay[data-v-091314e0] {
    bottom: 12px;
    left: 50%;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    transform: translate(-50%) translateY(10px);
    transition: all .3s ease
}

.template-card:hover .template-use-button-overlay[data-v-091314e0] {
    opacity: 1;
    pointer-events: auto;
    transform: translate(-50%) translateY(0)
}

.template-use-button[data-v-091314e0] {
    align-items: center;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    background-color: #222;
    border: none;
    border-radius: 18px;
    box-shadow: 0 4px 12px #00000026;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    font-weight: 400;
    gap: 8px;
    justify-content: center;
    line-height: 1.5;
    padding: 6px 16px;
    transition: all .2s ease;
    white-space: nowrap
}

.template-use-button[data-v-091314e0]:hover {
    background-color: #000;
    box-shadow: 0 6px 16px #0003;
    transform: scale(1.05)
}

.template-use-button[data-v-091314e0]:active {
    transform: scale(.95)
}

@media (max-width:768px) {
    .template-use-button-overlay[data-v-091314e0] {
        display: none
    }
    .template-card .remove[data-v-091314e0] {
        display: flex;
        height: 24px;
        opacity: 1;
        pointer-events: auto;
        right: -8px;
        top: -8px;
        width: 24px
    }
    .remove[data-v-091314e0] svg {
        height: 16px;
        width: 16px
    }
}

@media (hover:none) and (pointer:coarse) {
    .template-use-button-overlay[data-v-091314e0] {
        display: none
    }
    .template-card .remove[data-v-091314e0] {
        display: flex;
        height: 24px;
        opacity: 1;
        pointer-events: auto;
        right: -8px;
        top: -8px;
        width: 24px
    }
    .remove[data-v-091314e0] svg {
        height: 16px;
        width: 16px
    }
}

@media (prefers-color-scheme:dark) {
    .template-card-inner[data-v-091314e0] {
        background: #2a2a2a;
        border-color: #444
    }
    .template-card .remove[data-v-091314e0] {
        background-color: #fff;
        box-shadow: 0 4px 12px #0000004d
    }
    .remove[data-v-091314e0] svg {
        color: #222
    }
    .remove[data-v-091314e0]:hover {
        background-color: #ff4757;
        box-shadow: 0 6px 16px #ff475766;
        transform: scale(1.1)
    }
    .remove[data-v-091314e0]:hover svg {
        color: #fff
    }
    .template-use-button[data-v-091314e0] {
        background-color: #fff;
        color: #222
    }
    .template-use-button[data-v-091314e0]:hover {
        background-color: #f0f0f0;
        color: #000
    }
}

.add-template-guide-modal-overlay[data-v-cb17b1b2] {
    align-items: center;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    background: #0009;
    bottom: 0;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    left: 0;
    padding: 20px;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 10000
}

.add-template-guide-modal[data-v-cb17b1b2] {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 20px 60px #0000004d;
    display: flex;
    flex-direction: column;
    max-height: 80vh;
    max-width: 600px;
    overflow: hidden;
    width: 100%
}

.modal-header[data-v-cb17b1b2] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    padding: 24px 24px 0
}

.modal-title[data-v-cb17b1b2] {
    color: #232425;
    font-size: 20px;
    font-weight: 600;
    margin: 0
}

.close-button[data-v-cb17b1b2] {
    align-items: center;
    background: none;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    height: 32px;
    justify-content: center;
    padding: 8px;
    transition: background-color .2s ease;
    width: 32px
}

.close-button[data-v-cb17b1b2]:hover {
    background-color: #f5f5f5
}

.close-button[data-v-cb17b1b2] svg {
    color: #666;
    height: 16px;
    width: 16px
}

.modal-content[data-v-cb17b1b2] {
    flex: 1;
    overflow-y: auto;
    padding: 20px 24px 24px
}

.guide-section[data-v-cb17b1b2] {
    margin-bottom: 32px
}

.guide-section[data-v-cb17b1b2]:last-child {
    margin-bottom: 0
}

.section-title[data-v-cb17b1b2] {
    color: #232425;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 16px
}

.video-container[data-v-cb17b1b2] {
    background: #f8f9fa;
    border-radius: 12px;
    margin-bottom: 16px;
    overflow: hidden
}

.guide-video[data-v-cb17b1b2] {
    display: block;
    height: auto;
    max-height: 400px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100%
}

.guide-text[data-v-cb17b1b2] {
    text-align: left
}

.guide-text p[data-v-cb17b1b2] {
    color: #606366;
    font-size: 16px;
    line-height: 1.6;
    margin: 0
}

.more-ways-section[data-v-cb17b1b2] {
    border-top: 1px solid #e9ecef;
    padding-top: 24px
}

.coming-soon[data-v-cb17b1b2] {
    background: #f8f9fa;
    border: 2px dashed #e9ecef;
    border-radius: 12px;
    flex-direction: column;
    padding: 40px 20px;
    text-align: center
}

.coming-soon[data-v-cb17b1b2],
.coming-soon-icon[data-v-cb17b1b2] {
    align-items: center;
    display: flex;
    justify-content: center
}

.coming-soon-icon[data-v-cb17b1b2] {
    color: #9ca3af;
    height: 32px;
    margin-bottom: 12px;
    width: 32px
}

.coming-soon-icon[data-v-cb17b1b2] svg {
    height: 100%;
    width: 100%
}

.coming-soon-text[data-v-cb17b1b2] {
    color: #9ca3af;
    font-size: 16px;
    font-weight: 500;
    margin: 0
}

@media (max-width:768px) {
    .add-template-guide-modal-overlay[data-v-cb17b1b2] {
        padding: 16px
    }
    .add-template-guide-modal[data-v-cb17b1b2] {
        max-height: 85vh
    }
    .modal-header[data-v-cb17b1b2] {
        padding: 20px 20px 0
    }
    .modal-title[data-v-cb17b1b2] {
        font-size: 18px
    }
    .modal-content[data-v-cb17b1b2] {
        padding: 16px 20px 20px
    }
    .guide-video[data-v-cb17b1b2] {
        max-height: 300px
    }
    .guide-text p[data-v-cb17b1b2] {
        font-size: 14px
    }
    .section-title[data-v-cb17b1b2] {
        font-size: 16px
    }
    .guide-section[data-v-cb17b1b2] {
        margin-bottom: 24px
    }
    .more-ways-section[data-v-cb17b1b2] {
        padding-top: 20px
    }
    .coming-soon[data-v-cb17b1b2] {
        padding: 30px 16px
    }
}

@media (prefers-color-scheme:dark) {
    .add-template-guide-modal[data-v-cb17b1b2] {
        background: #1a1a1a;
        border: 1px solid #333
    }
    .modal-title[data-v-cb17b1b2] {
        color: #fff
    }
    .close-button[data-v-cb17b1b2]:hover {
        background-color: #333
    }
    .close-button[data-v-cb17b1b2] svg {
        color: #ccc
    }
    .video-container[data-v-cb17b1b2] {
        background: #2a2a2a
    }
    .guide-text p[data-v-cb17b1b2] {
        color: #ccc
    }
    .section-title[data-v-cb17b1b2] {
        color: #fff
    }
    .more-ways-section[data-v-cb17b1b2] {
        border-top-color: #374151
    }
    .coming-soon[data-v-cb17b1b2] {
        background: #2a2a2a;
        border-color: #374151
    }
    .coming-soon-icon[data-v-cb17b1b2],
    .coming-soon-text[data-v-cb17b1b2] {
        color: #6b7280
    }
}

.slides-template-selector[data-v-12d01e6e] {
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    left: 0;
    margin: 0 auto;
    overflow: hidden;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1000
}

.main-content-loading-overlay[data-v-12d01e6e] {
    align-items: center;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    background: #fffc;
    display: flex;
    justify-content: center;
    pointer-events: auto
}

.loading-content[data-v-12d01e6e] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    pointer-events: none
}

.loading-text[data-v-12d01e6e] {
    color: #666;
    font-size: 1rem;
    font-weight: 500;
    margin: 0
}

.header-controls[data-v-12d01e6e] {
    background: #fff;
    flex-shrink: 0;
    padding: 18px 24px;
    z-index: 10
}

.header-controls[data-v-12d01e6e],
.header-left[data-v-12d01e6e] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.header-left[data-v-12d01e6e] {
    gap: 16px
}

.back-icon[data-v-12d01e6e] {
    align-items: center;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    height: 28px;
    justify-content: center;
    padding: 4px;
    width: 28px
}

@media (hover:hover) {
    .back-icon[data-v-12d01e6e]:hover {
        background-color: #f5f5f5
    }
}

.left-toggle[data-v-12d01e6e] {
    align-items: center;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding: 4px
}

@media (hover:hover) {
    .left-toggle[data-v-12d01e6e]:hover {
        background-color: #f5f5f5
    }
}

.icon[data-v-12d01e6e] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    justify-content: center;
    width: 20px
}

.icon[data-v-12d01e6e] svg {
    height: 100%;
    width: 100%
}

.cursor-pointer[data-v-12d01e6e] {
    cursor: pointer
}

.main-content[data-v-12d01e6e] {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    position: relative
}

.header[data-v-12d01e6e] {
    margin-bottom: 32px;
    text-align: center
}

.header h2[data-v-12d01e6e] {
    color: #333;
    font-size: 28px;
    font-weight: 700;
    margin: 0
}

.error-section[data-v-12d01e6e],
.loading-section[data-v-12d01e6e],
.waterfall-empty-section[data-v-12d01e6e],
.waterfall-error-section[data-v-12d01e6e] {
    padding: 60px 20px;
    text-align: center
}

.error-section p[data-v-12d01e6e],
.loading-section p[data-v-12d01e6e],
.waterfall-empty-section p[data-v-12d01e6e],
.waterfall-error-section p[data-v-12d01e6e] {
    color: #666;
    font-size: 16px;
    margin: 16px 0
}

.retry-button[data-v-12d01e6e] {
    background: #007aff;
    border: none;
    border-radius: 8px;
    color: #fff;
    cursor: pointer;
    font-size: 14px;
    padding: 10px 20px;
    transition: background .2s ease
}

.retry-button[data-v-12d01e6e]:hover {
    background: #0056cc
}

.waterfall-empty-section[data-v-12d01e6e] {
    padding: 80px 20px
}

.empty-icon[data-v-12d01e6e] {
    align-items: center;
    color: #ddd;
    display: flex;
    height: 32px;
    justify-content: center;
    margin: 0 auto 24px;
    width: 32px
}

.empty-icon[data-v-12d01e6e] svg {
    height: 100%;
    width: 100%
}

.waterfall-empty-section .empty-text[data-v-12d01e6e] {
    color: #909499 !important;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
    margin: 0
}

.tab-navigation[data-v-12d01e6e] {
    border-bottom: 1px solid #efefef;
    display: flex;
    gap: 24px;
    margin-bottom: 20px
}

.tab-button[data-v-12d01e6e] {
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    box-sizing: border-box;
    color: #606366;
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5em;
    padding: 8px 0;
    position: relative;
    transition: all .2s ease
}

.tab-button[data-v-12d01e6e]:hover {
    color: #232425
}

.tab-button.active[data-v-12d01e6e] {
    border-bottom-color: #232425;
    color: #232425
}

.explore-content[data-v-12d01e6e],
.my-templates-content[data-v-12d01e6e],
.tab-content[data-v-12d01e6e] {
    display: flex;
    flex: 1;
    flex-direction: column
}

.search-filter-section[data-v-12d01e6e] {
    align-items: flex-start;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: flex-start;
    margin-bottom: 16px
}

.search-bar[data-v-12d01e6e] {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    box-sizing: border-box;
    display: flex;
    height: 37px;
    overflow: hidden;
    transition: all .2s ease;
    width: 400px
}

.search-bar[data-v-12d01e6e]:hover {
    background: #f5f5f5
}

.search-input[data-v-12d01e6e] {
    background: transparent;
    border: none;
    box-sizing: border-box;
    color: #495057;
    flex: 1;
    font-size: 14px;
    height: 35px;
    line-height: 1.5;
    outline: none;
    padding: 8px 16px
}

.search-bar[data-v-12d01e6e]:focus-within {
    background: #f0f9ff;
    border-color: #cfe5ff;
    box-shadow: none
}

.search-bar:focus-within .search-input[data-v-12d01e6e] {
    color: #0f7fff
}

.search-bar:focus-within .search-button[data-v-12d01e6e] {
    color: #0f7fff
}

.search-button[data-v-12d01e6e] {
    align-items: center;
    background: transparent;
    border: none;
    box-sizing: border-box;
    color: #495057;
    cursor: pointer;
    display: flex;
    height: 35px;
    justify-content: center;
    padding: 8px 12px
}

.search-button[data-v-12d01e6e]:hover {
    background: transparent;
    color: #007bff
}

.search-icon[data-v-12d01e6e] {
    color: currentColor;
    height: 16px;
    width: 16px
}

.filter-section[data-v-12d01e6e] {
    display: flex;
    flex-direction: row;
    gap: 12px
}

.filter-group[data-v-12d01e6e] {
    align-items: flex-start;
    display: flex;
    gap: 12px
}

.color-filter-container[data-v-12d01e6e] {
    position: relative
}

.color-filter-trigger[data-v-12d01e6e] {
    align-items: center;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    box-sizing: border-box;
    color: #495057;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    height: 37px;
    line-height: 1.5;
    padding: 8px 16px;
    transition: all .2s ease;
    white-space: nowrap
}

.color-filter-trigger[data-v-12d01e6e]:hover {
    background: #f5f5f5
}

.color-filter-trigger.active[data-v-12d01e6e] {
    background: #f0f9ff;
    border-color: #cfe5ff;
    color: #0f7fff
}

.color-filter-trigger-content[data-v-12d01e6e] {
    gap: 12px
}

.color-filter-text[data-v-12d01e6e],
.color-filter-trigger-content[data-v-12d01e6e] {
    align-items: center;
    display: flex;
    white-space: nowrap
}

.color-filter-text[data-v-12d01e6e] {
    text-align: left
}

.color-square[data-v-12d01e6e] {
    border: 1px solid rgba(0, 0, 0, .1);
    border-radius: 2px;
    display: inline-block;
    flex-shrink: 0;
    height: 12px;
    margin-right: 8px;
    width: 12px
}

.dropdown-container[data-v-12d01e6e] {
    position: relative
}

.dropdown-trigger[data-v-12d01e6e] {
    align-items: center;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    box-sizing: border-box;
    color: #495057;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    gap: 12px;
    height: 37px;
    line-height: 1.5;
    padding: 8px 16px;
    transition: all .2s ease;
    white-space: nowrap
}

.dropdown-trigger[data-v-12d01e6e]:hover {
    background: #f5f5f5
}

.dropdown-trigger.active[data-v-12d01e6e] {
    background: #f0f9ff;
    border-color: #cfe5ff;
    color: #0f7fff
}

.dropdown-text[data-v-12d01e6e] {
    text-align: left;
    white-space: nowrap
}

.dropdown-arrow[data-v-12d01e6e] {
    flex-shrink: 0;
    height: 10px;
    transition: transform .2s ease;
    width: 10px
}

.dropdown-arrow.rotated[data-v-12d01e6e] {
    transform: rotate(180deg)
}

.dropdown-menu[data-v-12d01e6e] {
    background: #fff;
    border-radius: 14px;
    box-shadow: 0 4px 12px #00000026;
    left: 0;
    margin-top: 8px;
    max-height: 300px;
    overflow-y: auto;
    padding: 16px;
    position: absolute;
    right: 0;
    top: 100%;
    width: 300px;
    z-index: 1000
}

.dropdown-item[data-v-12d01e6e] {
    align-items: center;
    cursor: pointer;
    display: flex;
    gap: 8px;
    padding: 10px 8px;
    transition: background-color .2s ease
}

.dropdown-item-text[data-v-12d01e6e] {
    color: #232425;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5
}

.dropdown-item[data-v-12d01e6e]:hover {
    background-color: #f8f9fa
}

.dropdown-item[data-v-12d01e6e]:last-child {
    border-bottom: none
}

.dropdown-checkbox[data-v-12d01e6e],
.dropdown-radio[data-v-12d01e6e] {
    cursor: pointer;
    flex-shrink: 0;
    height: 16px;
    width: 16px
}

.dropdown-loading[data-v-12d01e6e] {
    color: #6c757d;
    font-size: 13px;
    font-style: italic;
    padding: 10px 12px;
    text-align: center
}

.language-filter-button[data-v-12d01e6e] {
    align-items: center;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    box-sizing: border-box;
    color: #495057;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    height: 37px;
    justify-content: center;
    line-height: 1.5;
    padding: 8px 16px;
    transition: all .2s ease;
    white-space: nowrap
}

.language-filter-button[data-v-12d01e6e]:hover {
    background: #f5f5f5
}

.language-filter-button.active[data-v-12d01e6e] {
    background: #f0f9ff;
    border-color: #cfe5ff;
    color: #0f7fff
}

.prompt-input-section[data-v-12d01e6e] {
    margin-bottom: 80px
}

.prompt-input-wrapper[data-v-12d01e6e] {
    display: flex;
    justify-content: center;
    margin: 0 auto;
    max-width: 800px
}

.prompt-input-wrapper-upper[data-v-12d01e6e] {
    display: flex;
    justify-content: center;
    width: 100%
}

.template-waterfall[data-v-12d01e6e] {
    display: flex;
    flex-direction: row;
    gap: 16px;
    margin-bottom: 3rem;
    position: relative
}

.template-column[data-v-12d01e6e] {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: var(--0617a526)
}

.template-card.blank-template-card[data-v-12d01e6e] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    padding: 12px
}

.template-waterfall .blank-template-card[data-v-12d01e6e] {
    align-items: center;
    background: linear-gradient(180deg, #fff, #f2f2f2);
    border: 1px solid #efefef;
    border-radius: 12px;
    display: flex;
    justify-content: center
}

.template-waterfall .blank-template-card[data-v-12d01e6e]:hover {
    background: linear-gradient(180deg, #f0f9ff, #e6f3ff);
    border-color: #0f7fff
}

.template-waterfall .add-template-card[data-v-12d01e6e] {
    align-items: center;
    background: linear-gradient(180deg, #fff, #f2f2f2);
    border: 1px solid #efefef;
    border-radius: 12px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding: 12px;
    transition: all .3s ease
}

.template-waterfall .add-template-card[data-v-12d01e6e]:hover {
    background: linear-gradient(180deg, #f0f9ff, #e6f3ff);
    border-color: #0f7fff
}

.blank-template-content[data-v-12d01e6e] {
    align-items: center;
    color: #232425;
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
    transition: color .3s ease
}

.template-waterfall .blank-template-card:hover .blank-template-content[data-v-12d01e6e] {
    color: #60a5fa
}

.blank-template-content .blank-icon[data-v-12d01e6e] {
    height: 32px;
    width: 32px
}

.blank-template-content .blank-text[data-v-12d01e6e] {
    font-size: 1rem;
    font-weight: 400;
    margin: 0
}

.add-template-content[data-v-12d01e6e] {
    align-items: center;
    color: #232425;
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
    text-align: center;
    transition: color .3s ease
}

.template-waterfall .add-template-card:hover .add-template-content[data-v-12d01e6e] {
    color: #60a5fa
}

.add-template-content .add-template-icon[data-v-12d01e6e] {
    height: 32px;
    width: 32px
}

.add-template-content .add-template-text[data-v-12d01e6e] {
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.3;
    margin: 0
}

.template-waterfall .template-content[data-v-12d01e6e] {
    padding: 1rem
}

.template-waterfall .template-content h3[data-v-12d01e6e] {
    color: #232425;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 .5rem
}

.template-waterfall .template-content p[data-v-12d01e6e] {
    color: #606366;
    font-size: .9rem;
    line-height: 1.4;
    margin: 0
}

.skeleton-wrapper[data-v-12d01e6e] {
    pointer-events: none
}

.skeleton-card[data-v-12d01e6e] {
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 12px;
    cursor: default !important;
    overflow: hidden
}

.skeleton-card[data-v-12d01e6e]:hover {
    transform: none !important
}

.skeleton-image[data-v-12d01e6e] {
    animation: skeleton-pulse-12d01e6e 1.5s ease-in-out infinite;
    background: #f0f0f0;
    height: 100%;
    width: 100%
}

@keyframes skeleton-pulse-12d01e6e {
    0% {
        background-color: #f0f0f0;
        opacity: 1
    }
    50% {
        background-color: #d0d0d0;
        opacity: .3
    }
    to {
        background-color: #f0f0f0;
        opacity: 1
    }
}

.loading-more-section[data-v-12d01e6e] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: center;
    width: 100%
}

.loading-more-text[data-v-12d01e6e] {
    color: #666;
    font-size: 14px;
    font-weight: 400;
    margin: 0
}

.scroll-sentinel[data-v-12d01e6e] {
    bottom: -20px;
    height: 1px;
    left: 0;
    position: absolute;
    width: 100%
}

.skeleton-wrapper:nth-child(odd) .skeleton-image[data-v-12d01e6e] {
    animation-delay: 0s
}

.skeleton-wrapper:nth-child(2n) .skeleton-image[data-v-12d01e6e] {
    animation-delay: .5s
}

@media (max-width:768px) {
    .main-content[data-v-12d01e6e] {
        padding: 1rem
    }
    .header h2[data-v-12d01e6e] {
        font-size: 1.5rem
    }
    .prompt-input-wrapper[data-v-12d01e6e] {
        margin: 0;
        max-width: 100%
    }
    .prompt-input-wrapper-upper[data-v-12d01e6e] {
        width: 100%
    }
    .blank-template-content .blank-text[data-v-12d01e6e] {
        font-size: .875rem
    }
    .header-controls[data-v-12d01e6e] {
        padding: 12px 16px
    }
    .search-filter-section[data-v-12d01e6e] {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 12px;
        justify-content: flex-start
    }
    .search-bar[data-v-12d01e6e] {
        width: 100%
    }
    .filter-group[data-v-12d01e6e],
    .filter-section[data-v-12d01e6e] {
        flex-direction: column;
        gap: 8px
    }
    .dropdown-container[data-v-12d01e6e] {
        width: 100%
    }
    .dropdown-trigger[data-v-12d01e6e] {
        font-size: 14px;
        height: 37px;
        justify-content: space-between;
        padding: 8px 16px;
        width: 100%
    }
    .dropdown-text[data-v-12d01e6e] {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis
    }
    .dropdown-item[data-v-12d01e6e] {
        font-size: 13px;
        padding: 8px 10px
    }
    .search-input[data-v-12d01e6e] {
        font-size: 14px
    }
    .color-filter-container[data-v-12d01e6e] {
        width: 100%
    }
    .color-filter-trigger[data-v-12d01e6e] {
        font-size: 14px;
        height: 37px;
        justify-content: space-between;
        padding: 8px 16px;
        width: 100%
    }
    .color-filter-text[data-v-12d01e6e] {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis
    }
    .language-filter-button[data-v-12d01e6e] {
        font-size: 14px;
        height: 37px;
        justify-content: center;
        padding: 8px 16px;
        width: 100%
    }
}

@media (max-width:1220px) {
    .slides-template-selector[data-v-12d01e6e] {
        padding-left: 0
    }
    .header-controls[data-v-12d01e6e] {
        background: #fff;
        left: 0;
        padding: 12px 16px;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 1001
    }
    .main-content[data-v-12d01e6e] {
        margin-left: 0;
        padding: 80px 1rem 1rem
    }
}

@media (min-width:1221px) {
    .main-content[data-v-12d01e6e] {
        transition: margin-left .3s ease
    }
    .main-content.full-width[data-v-12d01e6e] {
        margin-left: 0
    }
}

@media (prefers-color-scheme:dark) {
    .slides-template-selector[data-v-12d01e6e] {
        background: #1a1a1a
    }
    .main-content-loading-overlay[data-v-12d01e6e] {
        background: #1a1a1acc
    }
    .loading-text[data-v-12d01e6e] {
        color: #ccc
    }
    .header-controls[data-v-12d01e6e] {
        background: #1a1a1a
    }
    .back-icon[data-v-12d01e6e] {
        background-color: #333
    }
    @media (hover:hover) {
        .back-icon[data-v-12d01e6e]:hover {
            background-color: #444
        }
    }
    .left-toggle[data-v-12d01e6e] {
        background-color: #333
    }
    @media (hover:hover) {
        .left-toggle[data-v-12d01e6e]:hover {
            background-color: #444
        }
    }
    .header h2[data-v-12d01e6e] {
        color: #fff
    }
    .template-waterfall .blank-template-card[data-v-12d01e6e] {
        background: linear-gradient(180deg, #333, #2a2a2a);
        border-color: #555
    }
    .template-waterfall .blank-template-card[data-v-12d01e6e]:hover {
        background: linear-gradient(180deg, #1a2a3a, #152535);
        border-color: #0f7fff
    }
    .template-waterfall .template-content h3[data-v-12d01e6e] {
        color: #fff
    }
    .template-waterfall .template-content p[data-v-12d01e6e] {
        color: #ccc
    }
    .blank-template-content[data-v-12d01e6e] {
        color: #fff
    }
    .template-waterfall .add-template-card[data-v-12d01e6e] {
        background: linear-gradient(180deg, #333, #2a2a2a);
        border-color: #555
    }
    .template-waterfall .add-template-card[data-v-12d01e6e]:hover {
        background: linear-gradient(180deg, #1a2a3a, #152535);
        border-color: #0f7fff
    }
    .add-template-content[data-v-12d01e6e] {
        color: #fff
    }
    .template-waterfall .add-template-card:hover .add-template-content[data-v-12d01e6e] {
        color: #60a5fa
    }
    .search-bar[data-v-12d01e6e] {
        background: #2a2a2a;
        border-color: #555
    }
    .search-bar[data-v-12d01e6e]:hover {
        background: #444
    }
    .search-input[data-v-12d01e6e] {
        background: transparent;
        color: #fff
    }
    .search-bar[data-v-12d01e6e]:focus-within {
        background: #1a2a3a;
        border-color: #2563eb;
        box-shadow: none
    }
    .search-bar:focus-within .search-input[data-v-12d01e6e] {
        color: #60a5fa
    }
    .search-bar:focus-within .search-button[data-v-12d01e6e] {
        color: #60a5fa
    }
    .search-button[data-v-12d01e6e] {
        background: transparent;
        color: #fff
    }
    .search-button[data-v-12d01e6e]:hover {
        background: transparent;
        color: #0f7fff
    }
    .dropdown-trigger[data-v-12d01e6e] {
        background: #2a2a2a;
        border-color: #555;
        color: #fff
    }
    .dropdown-trigger[data-v-12d01e6e]:hover {
        background: #444
    }
    .dropdown-trigger.active[data-v-12d01e6e] {
        background: #1a2a3a;
        border-color: #2563eb;
        color: #60a5fa
    }
    .dropdown-menu[data-v-12d01e6e] {
        background: #2a2a2a;
        border-color: #555
    }
    .dropdown-item[data-v-12d01e6e] {
        border-bottom-color: #444;
        color: #fff
    }
    .dropdown-item-text[data-v-12d01e6e] {
        color: #fff
    }
    .dropdown-item[data-v-12d01e6e]:hover {
        background-color: #444
    }
    .dropdown-loading[data-v-12d01e6e] {
        color: #ccc
    }
    .dropdown-checkbox[data-v-12d01e6e] {
        accent-color: #60a5fa
    }
    .color-filter-trigger[data-v-12d01e6e] {
        background: #2a2a2a;
        border-color: #555;
        color: #fff
    }
    .color-filter-trigger[data-v-12d01e6e]:hover {
        background: #444
    }
    .color-filter-trigger.active[data-v-12d01e6e] {
        background: #1a2a3a;
        border-color: #2563eb;
        color: #60a5fa
    }
    .color-square[data-v-12d01e6e] {
        border-color: #fff3
    }
    .empty-icon[data-v-12d01e6e] {
        color: #ddd
    }
    .waterfall-empty-section .empty-text[data-v-12d01e6e] {
        color: #909499 !important
    }
    .skeleton-card[data-v-12d01e6e] {
        background: #333;
        border-color: #555
    }
    .skeleton-image[data-v-12d01e6e] {
        background: #444
    }
    .skeleton-card[data-v-12d01e6e] {
        background: #2a2a2a
    }
    @keyframes skeleton-pulse-dark-12d01e6e {
        0% {
            background-color: #444;
            opacity: 1
        }
        50% {
            background-color: #555;
            opacity: .2
        }
        to {
            background-color: #444;
            opacity: 1
        }
    }
    .skeleton-image[data-v-12d01e6e] {
        animation-name: skeleton-pulse-dark-12d01e6e
    }
    .loading-more-text[data-v-12d01e6e] {
        color: #ccc
    }
    @media (max-width:1220px) {
        .header-controls[data-v-12d01e6e] {
            background: #1a1a1a
        }
    }
    .language-filter-button[data-v-12d01e6e] {
        background: #2a2a2a;
        border-color: #555;
        color: #fff
    }
    .language-filter-button[data-v-12d01e6e]:hover {
        background: #444
    }
    .language-filter-button.active[data-v-12d01e6e] {
        background: #1a2a3a;
        border-color: #2563eb;
        color: #60a5fa
    }
    .tab-navigation[data-v-12d01e6e] {
        border-bottom-color: #374151
    }
    .tab-button[data-v-12d01e6e] {
        color: #9ca3af
    }
    .tab-button[data-v-12d01e6e]:hover {
        color: #d1d5db
    }
    .tab-button.active[data-v-12d01e6e] {
        border-bottom-color: #fff;
        color: #fff
    }
}

.icon[data-v-f5ba4b2a] {
    align-items: center;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    justify-content: center
}

@media (hover:hover) {
    .icon[data-v-f5ba4b2a]:hover {
        background-color: #e5e5e5
    }
}

@media (prefers-color-scheme:dark) {
    @media (hover: hover) {
        .icon[data-v-f5ba4b2a]:hover {
            background-color:#3a3a3a
        }
    }
}

.earth-icon[data-v-f5ba4b2a] svg {
    height: 16px;
    width: 16px
}

.prompt-input-wrapper-upper[data-v-74f74fef] {
    display: flex;
    justify-content: center;
    width: 100%
}

.docs-template-selector[data-v-74f74fef] {
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    left: 0;
    margin: 0 auto;
    overflow: hidden;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1000
}

.main-content-loading-overlay[data-v-74f74fef] {
    align-items: center;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    background: #fffc;
    display: flex;
    justify-content: center;
    pointer-events: auto
}

.loading-content[data-v-74f74fef] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    pointer-events: none
}

.loading-text[data-v-74f74fef] {
    color: #666;
    font-size: 1rem;
    font-weight: 500;
    margin: 0
}

@media (prefers-color-scheme:dark) {
    .docs-template-selector[data-v-74f74fef] {
        background-color: #1a1a1a
    }
    .main-content-loading-overlay[data-v-74f74fef] {
        background: #1a1a1acc
    }
    .loading-text[data-v-74f74fef] {
        color: #ccc
    }
}

.header-controls[data-v-74f74fef] {
    align-items: center;
    background: #fff;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    justify-content: flex-start;
    padding: 18px 24px;
    z-index: 10
}

@media (prefers-color-scheme:dark) {
    .header-controls[data-v-74f74fef] {
        background: #1a1a1a
    }
}

.header-left[data-v-74f74fef] {
    flex-direction: row;
    gap: 16px;
    justify-content: flex-start
}

.back-icon[data-v-74f74fef],
.header-left[data-v-74f74fef] {
    align-items: center;
    display: flex
}

.back-icon[data-v-74f74fef] {
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    flex-shrink: 0;
    height: 28px;
    justify-content: center;
    padding: 4px;
    width: 28px
}

@media (hover:hover) {
    .back-icon[data-v-74f74fef]:hover {
        background-color: #f5f5f5
    }
}

@media (prefers-color-scheme:dark) {
    .back-icon[data-v-74f74fef] {
        background-color: #333
    }
    @media (hover:hover) {
        .back-icon[data-v-74f74fef]:hover {
            background-color: #444
        }
    }
}

.left-toggle[data-v-74f74fef] {
    align-items: center;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding: 4px
}

@media (hover:hover) {
    .left-toggle[data-v-74f74fef]:hover {
        background-color: #f5f5f5
    }
}

@media (prefers-color-scheme:dark) {
    .left-toggle[data-v-74f74fef] {
        background-color: #333
    }
    @media (hover:hover) {
        .left-toggle[data-v-74f74fef]:hover {
            background-color: #444
        }
    }
}

.icon[data-v-74f74fef] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    justify-content: center;
    width: 20px
}

.icon[data-v-74f74fef] svg {
    height: 100%;
    width: 100%
}

.cursor-pointer[data-v-74f74fef] {
    cursor: pointer
}

.main-content[data-v-74f74fef] {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    position: relative
}

.header[data-v-74f74fef] {
    margin-bottom: 3rem;
    text-align: center
}

.header h2[data-v-74f74fef] {
    color: #232425;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: .5rem
}

.error-section[data-v-74f74fef],
.loading-section[data-v-74f74fef] {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 3rem 0;
    text-align: center
}

.retry-button[data-v-74f74fef] {
    background: #3498db;
    border: none;
    border-radius: 4px;
    color: #fff;
    cursor: pointer;
    margin-top: 1rem;
    padding: .5rem 1rem
}

.retry-button[data-v-74f74fef]:hover {
    background: #2980b9
}

.prompt-input-section[data-v-74f74fef] {
    margin-bottom: 36px;
    padding: 1.5rem 0
}

.prompt-input-wrapper[data-v-74f74fef] {
    display: flex;
    justify-content: center;
    margin: 0 auto;
    max-width: 800px
}

@media (max-width:768px) {
    .main-content[data-v-74f74fef] {
        padding: 1rem
    }
    .header h2[data-v-74f74fef] {
        font-size: 1.5rem
    }
    .header-controls[data-v-74f74fef] {
        padding: 12px 16px
    }
    .prompt-input-section[data-v-74f74fef] {
        padding: 1rem 0
    }
    .prompt-input-wrapper[data-v-74f74fef] {
        margin: 0;
        max-width: none
    }
}

@media (max-width:1220px) {
    .docs-template-selector[data-v-74f74fef] {
        padding-left: 0
    }
    .header-controls[data-v-74f74fef] {
        background: #fff;
        left: 0;
        padding: 12px 16px;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 1001
    }
    .main-content[data-v-74f74fef] {
        margin-left: 0;
        padding: 80px 1rem 1rem
    }
}

@media (min-width:1221px) {
    .main-content[data-v-74f74fef] {
        transition: margin-left .3s ease
    }
    .main-content.full-width[data-v-74f74fef] {
        margin-left: 0
    }
}

@media (prefers-color-scheme:dark) {
    .header h2[data-v-74f74fef] {
        color: #fff
    }
    @media (max-width:1220px) {
        .header-controls[data-v-74f74fef] {
            background: #1a1a1a
        }
    }
}

@keyframes skeleton-dark-74f74fef {
    0% {
        background-position: -200px 0
    }
    to {
        background-position: calc(200px + 100%) 0
    }
}

.animate-skeleton-dark[data-v-74f74fef] {
    animation: skeleton-dark-74f74fef 1.5s linear infinite;
    background: linear-gradient(90deg, #333 25%, #2a2a2a, #333 75%);
    background-size: 200px 100%
}

@media (prefers-color-scheme:dark) {
    @media (max-width: 1220px) {
        body {
            background:#1a1b1c
        }
    }
}

.project-name[data-v-19e33e89] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 4px;
    justify-content: center;
    width: 100%
}

.project-name .text[data-v-19e33e89] {
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px
}

.project-name .edit-icon[data-v-19e33e89] {
    align-items: center;
    cursor: pointer;
    display: flex;
    height: 16px;
    justify-content: center;
    width: 16px
}

.project-name .edit-icon[data-v-19e33e89] svg {
    height: 100%;
    width: 100%
}

.global-loading-modal[data-v-19e33e89] {
    align-items: center;
    background: transparent;
    border-radius: 12px;
    box-shadow: none;
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: center;
    padding: 24px
}

.global-loading-modal .loading-text[data-v-19e33e89] {
    color: #232425;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px
}

@media (prefers-color-scheme:dark) {
    .global-loading-modal[data-v-19e33e89] {
        background: transparent
    }
    .global-loading-modal .loading-text[data-v-19e33e89] {
        color: #fff
    }
}

.main-mask[data-v-19e33e89] {
    background-color: #00000080;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 101
}

.main-inner.agent_chat[data-v-19e33e89],
.main-inner.agent_deep_research[data-v-19e33e89],
.main-inner.agentic_deep_research[data-v-19e33e89],
.main-inner.chat_agent[data-v-19e33e89],
.main-inner.super_agent[data-v-19e33e89],
.main-inner.super_chat[data-v-19e33e89] {
    align-items: flex-start;
    display: flex;
    flex-direction: row;
    justify-content: center
}

.main-inner.image_studio[data-v-19e33e89] {
    align-items: flex-start
}

.agent-content .buttons[data-v-19e33e89] {
    display: flex;
    flex-direction: row;
    gap: 12px
}

.like-dislike-controls[data-v-19e33e89] {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px #00000014;
    display: flex;
    flex-direction: row;
    gap: 16px;
    margin-top: 25px;
    padding: 6px 16px
}

.like-dislike-controls .dislike[data-v-19e33e89],
.like-dislike-controls .like[data-v-19e33e89] {
    align-items: center;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    height: 28px;
    justify-content: center;
    position: relative;
    width: 28px
}

.like-dislike-controls .like .anim[data-v-19e33e89] {
    display: none;
    flex-shrink: 0;
    height: 28px;
    position: absolute;
    width: 28px
}

.like-dislike-controls .like .anim.play[data-v-19e33e89] {
    animation: likeAnim-19e33e89 .5s ease-out forwards;
    display: block
}

@keyframes likeAnim-19e33e89 {
    0% {
        opacity: 1;
        transform: scale(1)
    }
    to {
        opacity: 0;
        transform: scale(3)
    }
}

.like-dislike-controls .dislike .icon[data-v-19e33e89],
.like-dislike-controls .like .icon[data-v-19e33e89] {
    height: 20px;
    width: 20px
}

.like-dislike-controls .dislike .icon[data-v-19e33e89] {
    height: 20px;
    transform: rotate(180deg);
    width: 20px
}

@media (hover:hover) {
    .like-dislike-controls .dislike[data-v-19e33e89]:hover,
    .like-dislike-controls .like[data-v-19e33e89]:hover {
        background: #0001
    }
}

.agents-index .advanced-agent .head .button[data-v-19e33e89] {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px
}

.agents-index .advanced-agent .head .button .icon.add_icon[data-v-19e33e89] {
    height: 12px;
    width: 12px
}

.new-tag[data-v-19e33e89] {
    align-items: center;
    background: #ff3d3d;
    border-radius: 16px;
    color: #fff;
    display: inline-flex;
    flex-shrink: 0;
    font-size: 12px;
    font-style: italic;
    font-weight: 700;
    gap: 10px;
    height: 16px;
    justify-content: center;
    line-height: normal;
    padding: 2px 6px
}

.blog-link[data-v-19e33e89] {
    font-size: 14px
}

.blog-link a[data-v-19e33e89] {
    align-items: center;
    color: #909499;
    display: flex;
    flex-direction: row;
    font-weight: 400;
    gap: 6px;
    text-decoration: none
}

.blog-link b[data-v-19e33e89] {
    font-weight: 700
}

.blog-link .icon[data-v-19e33e89] {
    height: 14px;
    opacity: .4;
    width: 14px
}

.blog-link .icon[data-v-19e33e89],
.blog-link .text[data-v-19e33e89] {
    display: inline-flex
}

@media (hover:hover) {
    .blog-link a[data-v-19e33e89]:hover {
        color: #232425
    }
}

.agents-index.main-inner[data-v-19e33e89] {
    box-sizing: border-box;
    height: unset;
    padding: 80px 30px 100px
}

.agents-index h1[data-v-19e33e89] {
    color: #232425;
    font-size: 22px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

.agents-index .basic-agent h1[data-v-19e33e89] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.agents-index h4[data-v-19e33e89] {
    color: #606366;
    display: block;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    margin-top: 8px
}

.agents-index h4 a[data-v-19e33e89] {
    color: inherit;
    display: inline-flex;
    flex-direction: row;
    font-style: normal;
    font-weight: 700;
    gap: 4px;
    text-decoration: none
}

.agents-index h4 .icon[data-v-19e33e89] {
    color: #909499;
    height: 14px;
    margin-top: 4px;
    opacity: .4;
    width: 14px
}

@media (hover:hover) {
    .agents-index h4 a[data-v-19e33e89]:hover {
        color: #232425;
        text-decoration: underline
    }
    .agents-index h4 a:hover .icon[data-v-19e33e89] {
        color: #232425;
        opacity: 1
    }
}

@media (prefers-color-scheme:dark) {
    .agents-index h4 a[data-v-19e33e89]:hover {
        color: #fff;
        text-decoration: underline
    }
    .agents-index h4 a:hover .icon[data-v-19e33e89] {
        color: #fff;
        opacity: 1
    }
}

.agents-index .agents[data-v-19e33e89] {
    margin-top: 16px
}

.agents-index .divider[data-v-19e33e89] {
    background: #efefef;
    height: 1px;
    width: 100%
}

@media (prefers-color-scheme:dark) {
    .agents-index .divider[data-v-19e33e89] {
        background: #f0f0f033;
        height: 1px;
        width: 100%
    }
}

.agents-index>.divider[data-v-19e33e89] {
    margin: 40px 0
}

.agents-index .advanced-agent[data-v-19e33e89] {
    width: 100%
}

.agents-index .advanced-agent .agents[data-v-19e33e89] {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 16px
}

.agents-index .advanced-agent .divider[data-v-19e33e89] {
    margin: 12px 0
}

.agents-index .advanced-agent .agent[data-v-19e33e89] {
    align-items: flex-start;
    background: #f5f5f5;
    border-radius: 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    justify-content: space-between;
    max-width: 100%;
    padding: 16px;
    width: 388px
}

.agents-index .advanced-agent .agent .agent-bottom[data-v-19e33e89],
.agents-index .advanced-agent .agent .agent-content[data-v-19e33e89] {
    width: 100%
}

.agents-index .advanced-agent .button[data-v-19e33e89] {
    align-items: center;
    background: #232425;
    border-radius: 14px;
    color: #fff;
    cursor: pointer;
    display: inline-flex;
    flex-shrink: 0;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    height: 28px;
    line-height: 21px;
    padding: 0 12px
}

.agents-index .advanced-agent .setting-button[data-v-19e33e89] {
    align-items: center;
    background: transparent;
    color: #232425;
    cursor: pointer;
    display: flex;
    justify-content: center
}

.agents-index .advanced-agent .setting-button .setting_icon[data-v-19e33e89] {
    cursor: pointer;
    height: 14px;
    width: 14px
}

@media (prefers-color-scheme:dark) {
    .agents-index .advanced-agent .setting-button .setting_icon[data-v-19e33e89] {
        color: #ccc
    }
}

.agents-index .advanced-agent .head[data-v-19e33e89] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%
}

.agents-index .advanced-agent .head .text[data-v-19e33e89] {
    align-items: center;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 4px;
    line-height: 21px
}

.agents-index .advanced-agent .head .icon[data-v-19e33e89] {
    flex-shrink: 0;
    height: 24px;
    width: 24px
}

.agents-index .advanced-agent .head .setting_icon[data-v-19e33e89] {
    flex-shrink: 0;
    height: 16px;
    width: 16px
}

.agents-index .advanced-agent .head .label[data-v-19e33e89] {
    color: #232425;
    font-family: Arial;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

.agents-index .advanced-agent .popular-tasks[data-v-19e33e89] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.agents-index .advanced-agent .popular-tasks .title[data-v-19e33e89] {
    color: #606366;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px
}

.agents-index .advanced-agent .popular-tasks .task[data-v-19e33e89] {
    background: #fff;
    border-radius: 8px;
    color: #909499;
    cursor: pointer;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    padding: 8px;
    position: relative;
    width: -moz-fit-content;
    width: fit-content
}

.agents-index .advanced-agent .popular-tasks .task .text[data-v-19e33e89] {
    display: inline;
    line-height: 18px
}

.agents-index .advanced-agent .popular-tasks .task .icon[data-v-19e33e89] {
    display: inline-flex;
    height: 12px;
    padding: 0 0 0 8px;
    transform: translateY(2px);
    width: 12px
}

.agents-index .basic-agent[data-v-19e33e89] {
    display: flex;
    flex-direction: column;
    width: 100%
}

.agents-index .basic-agent .agents[data-v-19e33e89] {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 16px
}

.agents-index .basic-agent .description[data-v-19e33e89] {
    align-items: center;
    background: #fafafa;
    border-radius: 30px;
    color: #909499;
    display: inline-flex;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 7px;
    line-height: 150%;
    padding: 6px 16px;
    text-align: center
}

.agents-index .basic-agent .agent .icons[data-v-19e33e89] {
    display: flex;
    flex-direction: row;
    gap: -8px
}

.agents-index .basic-agent .agent .icon[data-v-19e33e89] {
    width: 16px
}

.agents-index .basic-agent .agent .icons img[data-v-19e33e89] {
    background: #fff;
    border-radius: 50%;
    height: 20px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 20px
}

.agents-index .basic-agent .agent[data-v-19e33e89] {
    align-items: center;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 1px 5px #00000014;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    gap: 16px;
    padding: 16px 27px;
    width: 253px
}

.agents-index .basic-agent .button[data-v-19e33e89] {
    align-items: center;
    color: #232425;
    display: flex;
    flex-direction: column;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    gap: 12px;
    line-height: 150%;
    text-align: center
}

.agents-index .basic-agent .button .icon[data-v-19e33e89] {
    flex-shrink: 0;
    height: 28px;
    width: 28px
}

.main-inner-inner-wrapper[data-v-19e33e89] {
    align-items: center;
    margin-top: 24px
}

.main-inner-inner[data-v-19e33e89],
.main-inner-inner-wrapper[data-v-19e33e89] {
    display: flex;
    flex-direction: column;
    width: 100%
}

.main-inner-inner[data-v-19e33e89] {
    box-sizing: border-box;
    max-width: 760px
}

.bubble.assistant .bubble-inner[data-v-19e33e89] {
    width: calc(100% - 80px)
}

.pending_for_async.bubble[data-v-19e33e89] {
    background-color: #fffbee;
    border: 1px solid #f8e3b4;
    border-radius: 16px;
    box-sizing: border-box;
    padding: 12px 16px;
    width: calc(100% - 80px)
}

.pending_for_async.bubble .header[data-v-19e33e89] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 10px
}

.pending_for_async.bubble .header .icon[data-v-19e33e89] {
    color: #ffc107;
    height: 14px;
    width: 14px
}

.pending_for_async.bubble .header .text[data-v-19e33e89] {
    color: #475666;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 26px
}

.pending_for_async.bubble .content[data-v-19e33e89] {
    margin-top: 16px
}

.pending_for_async.bubble .content .text[data-v-19e33e89] {
    color: #232425;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    gap: 12px;
    line-height: 24px
}

.pending_for_async.text[data-v-19e33e89] {
    margin-left: -4px
}

.create_result[data-v-19e33e89] {
    background: #fff;
    border-radius: 16px;
    box-sizing: border-box;
    color: #232425;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    max-width: calc(100% - 40px);
    padding: 30px 20px 20px;
    text-align: center;
    width: 540px
}

.create_result .content[data-v-19e33e89] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%
}

.create_result .bold[data-v-19e33e89] {
    font-weight: 700
}

.create_result .buttons[data-v-19e33e89] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 36px;
    width: 100%
}

.create_result .button[data-v-19e33e89] {
    background: #f5f5f5;
    border-radius: 12px;
    box-sizing: border-box;
    cursor: pointer;
    padding: 10px 20px;
    width: 100%
}

.create_result .button.default[data-v-19e33e89] {
    background: #000;
    color: #fff
}

.shrink-grow[data-v-19e33e89] {
    animation: shrinkGrow-19e33e89 .6s forwards
}

@keyframes shrinkGrow-19e33e89 {
    0% {
        transform: scale(1)
    }
    50% {
        transform: scale(.9)
    }
    to {
        transform: scale(1)
    }
}

.left-main .logo-group[data-v-19e33e89] {
    margin-bottom: 0;
    padding: 0 8px
}

.logo-group[data-v-19e33e89] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.logo-group .title[data-v-19e33e89] {
    cursor: pointer;
    font-weight: 400
}

.logo-group .title[data-v-19e33e89],
.logo-group .title b[data-v-19e33e89] {
    color: #232425;
    font-size: 24px;
    font-style: normal;
    line-height: 36px
}

.logo-group .title b[data-v-19e33e89] {
    font-weight: 700
}

.logo-group .content[data-v-19e33e89] {
    color: #232425
}

.logo-group .content[data-v-19e33e89],
.logo-group .desc[data-v-19e33e89] {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px
}

.logo-group .desc[data-v-19e33e89] {
    align-items: center;
    color: #909499;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    gap: 6px
}

@media (hover:hover) {
    .logo-group .desc[data-v-19e33e89]:hover {
        color: #000
    }
}

.logo-group .desc .icon[data-v-19e33e89] {
    height: 16px;
    width: 16px
}

.halo[data-v-19e33e89] {
    height: 100%;
    pointer-events: none;
    position: absolute;
    transition: left .5s ease-out, top .5s ease-out;
    width: 100%
}

.shape2[data-v-19e33e89] {
    background: #7fd1ff;
    height: 200.532px;
    left: calc(50% + 100px);
    top: calc(50% - 100px);
    width: 200.532px
}

.shape1[data-v-19e33e89],
.shape2[data-v-19e33e89] {
    filter: blur(120px);
    flex-shrink: 0;
    position: absolute;
    transform: rotate(-131.346deg)
}

.shape1[data-v-19e33e89] {
    background: radial-gradient(55.64% 49.84% at 50% 86%, #2c10d600, #2c10d65c);
    border-radius: 410.888px;
    height: 410.888px;
    left: calc(50% - 320px);
    opacity: .6;
    top: calc(50% - 205px);
    width: 410.888px
}

.icon[data-v-19e33e89] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.icon[data-v-19e33e89] svg {
    height: 100%;
    width: 100%
}

.main-inner-wrapper[data-v-19e33e89] {
    height: 100%;
    overflow-y: auto;
    width: 100%
}

.main-inner-wrapper.noscroll[data-v-19e33e89] {
    overflow: hidden
}

.input-wrapper-wrapper[data-v-19e33e89] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    display: none;
    gap: 10px;
    justify-content: flex-start;
    padding: 20px;
    width: 100%
}

.input-wrapper[data-v-19e33e89] {
    max-width: 760px;
    width: 100%
}

.input-wrapper-wrapper .button[data-v-19e33e89],
.bubble[data-v-19e33e89] .cursor {
    display: none
}

.bubble.assistant .thinking[data-v-19e33e89] .cursor {
    display: inline
}

@keyframes blink-animation-19e33e89 {
    0%,
    to {
        color: #000
    }
    50% {
        color: transparent
    }
}

.bubble.assistant .thinking[data-v-19e33e89] .cursor {
    animation: blink-animation-19e33e89 .5s infinite;
    color: #606366;
    font-size: 12px
}

.fade-enter-active[data-v-19e33e89],
.fade-leave-active[data-v-19e33e89] {
    transition: opacity .3s ease
}

.fade-enter-from[data-v-19e33e89],
.fade-leave-to[data-v-19e33e89] {
    opacity: 0
}

.stop_button[data-v-19e33e89] {
    align-items: center;
    background: #fff;
    border-radius: 28px;
    bottom: 80px;
    box-shadow: 0 2px 10px #00000014;
    color: #475666;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 18px;
    padding: 6px 16px;
    position: fixed;
    z-index: 5
}

.stop_button .icon[data-v-19e33e89] {
    height: 12px;
    width: 12px
}

@keyframes fadeInOut-19e33e89 {
    0% {
        bottom: 80px;
        opacity: 0;
        transform: scale(.8)
    }
    6% {
        bottom: 120px;
        opacity: 1;
        transform: scale(1)
    }
    50% {
        bottom: 120px;
        opacity: 1;
        transform: scale(1)
    }
    56% {
        bottom: 160px;
        opacity: 0;
        transform: scale(.8)
    }
    to {
        bottom: 160px;
        opacity: 0;
        transform: scale(.8)
    }
}

.running_tips[data-v-19e33e89] {
    align-items: center;
    animation: fadeInOut-19e33e89 10s infinite;
    bottom: 80px;
    display: flex;
    gap: 10px;
    justify-content: center;
    max-width: calc(100% - 24px);
    opacity: 0;
    position: fixed;
    transition: all .3s ease;
    z-index: 5
}

.running_tips .text[data-v-19e33e89] {
    background: #e7f9f1;
    border-radius: 57px;
    color: #475666;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px;
    padding: 10px 16px;
    text-align: center
}

.running_tips.tips1[data-v-19e33e89] {
    animation-delay: 0s
}

.running_tips.tips2[data-v-19e33e89] {
    animation-delay: 5s
}

@keyframes hideAfter-19e33e89 {
    0% {
        display: block
    }
    to {
        display: none
    }
}

.refresh_button[data-v-19e33e89] {
    align-items: center;
    background: #fff;
    border-radius: 28px;
    bottom: 80px;
    box-shadow: 0 2px 10px #00000014;
    color: #475666;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 18px;
    padding: 6px 16px;
    position: fixed;
    z-index: 5
}

.refresh_button .icon[data-v-19e33e89] {
    color: #0f7fff;
    height: 16px;
    width: 16px
}

.coffee_message[data-v-19e33e89] {
    background: #232425;
    border-radius: 12px;
    box-shadow: 0 2px 10px #00000014;
    color: #fff;
    display: flex;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    line-height: 24px;
    margin: 30px 10px 0;
    padding: 10px 16px;
    position: absolute;
    z-index: 10
}

.coffee_message .icon[data-v-19e33e89] {
    color: #62e2ac;
    height: 24px;
    width: 24px
}

.app-container[data-v-19e33e89] {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    width: 100%
}

.main-wrapper[data-v-19e33e89] {
    display: flex;
    flex-direction: row;
    flex-grow: 1;
    height: 100vh
}

.left-side[data-v-19e33e89] {
    background: #fafafa;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    height: 100%;
    justify-content: space-between;
    position: absolute;
    width: 360px;
    z-index: 102
}

.left-side>.divider[data-v-19e33e89] {
    background: #eaeaea30;
    box-sizing: border-box;
    height: 1px;
    margin: 0 20px;
    width: calc(100% - 40px)
}

.left-side .controls[data-v-19e33e89] {
    display: flex;
    flex-direction: row;
    justify-content: flex-end
}

.left-side .controls .toggle[data-v-19e33e89] {
    color: #909499;
    cursor: pointer;
    display: flex;
    height: 16px;
    width: 16px
}

.left-main[data-v-19e33e89] {
    flex-grow: 1;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 0 20px
}

.left-bottom[data-v-19e33e89] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: row
}

.left-bottom .left-toggle[data-v-19e33e89] {
    display: none
}

.left-side .controls .create_button[data-v-19e33e89] {
    align-items: center;
    background: #232425;
    border: none;
    border-radius: 12px;
    box-sizing: border-box;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    gap: 8px;
    height: 40px;
    justify-content: center;
    margin-top: 30px;
    padding: 4px 16px;
    width: 100%
}

.left-side .controls .create_button .label[data-v-19e33e89] {
    align-items: center;
    display: flex;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    justify-content: center;
    line-height: normal;
    text-align: center
}

.left-side .controls .create_button .icon[data-v-19e33e89] {
    display: flex;
    height: 24px;
    width: 24px
}

.menu .button[data-v-19e33e89] {
    color: #ff3d3d;
    cursor: pointer
}

.menu-wrapper[data-v-19e33e89] {
    align-items: center;
    bottom: 6px;
    display: flex;
    flex-direction: row;
    gap: 10px;
    justify-content: center;
    position: absolute;
    right: 6px
}

.main[data-v-19e33e89] {
    background: #fff;
    flex-grow: 1;
    position: relative
}

.main[data-v-19e33e89],
.main-inner[data-v-19e33e89] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%
}

.main-inner[data-v-19e33e89] {
    padding: 60px 0 20px;
    --container-width: 680px
}

@media (min-width:1330px) {
    .main-inner[data-v-19e33e89] {
        --container-width: 760px
    }
}

pre[data-v-19e33e89] {
    white-space: pre-wrap;
    word-break: break-all
}

.header-right[data-v-19e33e89] {
    display: flex;
    flex-direction: row;
    position: absolute;
    right: 20px
}

.header-right .top[data-v-19e33e89] {
    align-items: center;
    display: flex;
    gap: 8px
}

.header-right .icon[data-v-19e33e89] {
    background: #f5f5f5;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    flex-shrink: 0;
    height: 28px;
    padding: 4px;
    width: 28px
}

@media (hover:hover) {
    .header-right .icon[data-v-19e33e89]:hover {
        background-color: #e5e5e5
    }
}

.header-left[data-v-19e33e89] {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-grow: 1;
    gap: 16px;
    justify-content: center;
    left: 20px;
    padding-right: 10px;
    position: absolute
}

.header-left .button[data-v-19e33e89] {
    cursor: pointer
}

.header-content .title[data-v-19e33e89] {
    align-items: center;
    color: #232425;
    flex-grow: 1;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    height: 27px;
    line-height: 150%;
    max-width: calc(100% - 130px);
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap
}

.header-content[data-v-19e33e89] {
    align-items: center;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background: #fff2;
    border-bottom: none;
    box-sizing: border-box;
    color: #232425;
    display: flex;
    flex: none;
    flex-direction: row;
    justify-content: center;
    padding: 18px 24px;
    position: absolute;
    width: 100%
}

.header-left .logo img[data-v-19e33e89] {
    width: 100%
}

.header-left .top[data-v-19e33e89] {
    margin-right: 24px
}

.page_bottom_placeholder[data-v-19e33e89]:after {
    content: "";
    display: block;
    min-height: 50px;
    width: 1px
}

.left-head[data-v-19e33e89] {
    align-items: center;
    background: #fafafa;
    box-sizing: border-box;
    color: #232425;
    display: flex;
    flex-direction: row;
    flex-grow: 0;
    flex-shrink: 0;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    justify-content: space-between;
    left: 0;
    line-height: 27px;
    padding: 12px 16px 12px 30px;
    top: 0;
    width: 100%;
    z-index: 2
}

.left-head .icon[data-v-19e33e89] {
    cursor: pointer
}

.header-left .logo[data-v-19e33e89] {
    display: none
}

.show_content_only .header-left .logo[data-v-19e33e89] {
    display: block
}

.agents-index .advanced-agent .popular-tasks.popular-tasks-mobile[data-v-19e33e89],
.mobile-running-tips[data-v-19e33e89] {
    display: none
}

.agents-index .basic-agent .agent-content[data-v-19e33e89] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: center;
    width: 100%
}

@media (min-width:1221px) {
    .left-side.expand-enter-active[data-v-19e33e89],
    .left-side.expand-leave-active[data-v-19e33e89] {
        overflow: hidden;
        transition: width .3s ease
    }
    .left-side.expand-enter-from[data-v-19e33e89],
    .left-side.expand-leave-to[data-v-19e33e89] {
        width: 0
    }
    .left-side[data-v-19e33e89] {
        left: 0;
        width: 335px
    }
    .left-main .logo-group[data-v-19e33e89] {
        display: none
    }
}

@media (max-width:500px) {
    .header-left .title[data-v-19e33e89] {
        font-size: 14px;
        padding: 7px 0 0;
        width: 200px
    }
}

@media (max-width:1220px) {
    .page_bottom_placeholder[data-v-19e33e89] {
        display: flex;
        height: 150px;
        width: 100%
    }
    .agents-index .advanced-agent .agent[data-v-19e33e89],
    .agents-index .basic-agent .agent[data-v-19e33e89] {
        width: 100%
    }
    .agents-index>.divider[data-v-19e33e89] {
        margin: 20px 0
    }
    .agents-index .advanced-agent .popular-tasks.popular-tasks-mobile[data-v-19e33e89] {
        display: flex
    }
    .agents-index .advanced-agent .popular-tasks[data-v-19e33e89],
    .agents-index .advanced-agent .popular-tasks-divider[data-v-19e33e89] {
        display: none
    }
    .agents-index .basic-agent .agents[data-v-19e33e89] {
        gap: 0
    }
    .agents-index .basic-agent .agent[data-v-19e33e89] {
        box-shadow: none;
        padding: 6px 0;
        width: 50%
    }
    .agents-index .basic-agent .agent[data-v-19e33e89]:nth-child(2n) {
        padding-left: 8px
    }
    .agents-index .basic-agent .agent[data-v-19e33e89]:nth-child(odd) {
        padding-right: 8px
    }
    .agents-index .basic-agent .agent-content[data-v-19e33e89] {
        align-items: center;
        border-radius: 16px;
        box-shadow: 0 1px 5px #00000014;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 12px 16px;
        width: 100%
    }
    .agents-index .basic-agent .description .text[data-v-19e33e89] {
        display: none
    }
    .pending_for_async.bubble[data-v-19e33e89] {
        width: 100%
    }
    .main-inner-inner-wrapper[data-v-19e33e89] {
        margin-top: 0
    }
    .main-inner-inner[data-v-19e33e89] {
        padding: 12px
    }
    .main-inner-wrapper[data-v-19e33e89] {
        overflow-y: hidden;
        padding-top: 43px
    }
    .browser-extension-main-inner-wrapper[data-v-19e33e89] {
        padding-top: 0
    }
    .mobile-running-tips[data-v-19e33e89] {
        align-items: center;
        background: #e7eff9;
        border-radius: 64px;
        bottom: 80px;
        box-sizing: border-box;
        color: #232425;
        cursor: pointer;
        display: flex;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        gap: 4px;
        justify-content: center;
        left: 16px;
        line-height: 20px;
        padding: 10px 16px;
        position: fixed;
        text-align: center;
        transition: all .3s ease;
        width: calc(100% - 32px);
        z-index: 5
    }
    .mobile-running-tips .anim[data-v-19e33e89] {
        width: 30px
    }
    .mobile-running-tips .anim[data-v-19e33e89],
    .mobile-running-tips .icon[data-v-19e33e89] {
        align-items: center;
        display: flex;
        height: 16px;
        justify-content: center
    }
    .mobile-running-tips .icon[data-v-19e33e89] {
        width: 16px
    }
    .app-container[data-v-19e33e89] {
        height: 100%;
        overflow-x: hidden
    }
    body[data-v-19e33e89] {
        background: #e6edf2
    }
    .left-side.expand-enter-active[data-v-19e33e89],
    .left-side.expand-leave-active[data-v-19e33e89] {
        transition: left .3s ease
    }
    .left-side.expand-enter-from[data-v-19e33e89],
    .left-side.expand-leave-to[data-v-19e33e89] {
        left: -335px
    }
    .left-side[data-v-19e33e89] {
        left: 0;
        width: 335px
    }
    .left-main .logo-group[data-v-19e33e89] {
        display: none
    }
    .header-content[data-v-19e33e89] {
        padding: 8px 16px;
        position: fixed;
        top: -100%;
        transition: top .6s ease;
        width: 100%;
        z-index: 2
    }
    .header-content.show[data-v-19e33e89] {
        top: 0
    }
    .header-left .logo[data-v-19e33e89] {
        display: block
    }
    .mobile-header-content-wrapper[data-v-19e33e89] {
        background: #e6edf2;
        display: flex;
        justify-content: center;
        position: fixed;
        top: 40px;
        width: 100%
    }
    .mobile-header-content[data-v-19e33e89] {
        align-items: center;
        background: #e6edf2;
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        max-width: 760px;
        padding: 0 28px 200px;
        width: 100%
    }
    .shape1[data-v-19e33e89] {
        top: 150px;
        z-index: 10
    }
    .shape2[data-v-19e33e89] {
        top: 300px;
        z-index: 10
    }
    .left-bottom[data-v-19e33e89] {
        background: #e6edf2;
        border-radius: 6px;
        bottom: 20px;
        left: 300px;
        position: fixed
    }
    .left-bottom .left-toggle[data-v-19e33e89] {
        display: flex;
        flex-direction: row
    }
    .left-bottom .create_button[data-v-19e33e89] {
        display: none
    }
    .mask[data-v-19e33e89] {
        background-color: #00000080;
        height: 100%;
        left: 0;
        min-height: 100vh;
        position: fixed;
        top: 0;
        transition: all .3s;
        width: 100%;
        z-index: 1000
    }
    .main-wrapper[data-v-19e33e89] {
        flex-direction: column;
        height: 100%
    }
    .left-side[data-v-19e33e89] {
        background: #fafafa;
        height: 100vh;
        position: fixed;
        z-index: 1001
    }
    .left-side .controls[data-v-19e33e89] {
        display: none
    }
    .left-main[data-v-19e33e89] {
        display: block;
        flex-grow: 1;
        flex-shrink: 1;
        height: 100%;
        padding: 25px 0 0;
        position: relative
    }
    .left-main .header-left[data-v-19e33e89],
    .left-side.hide[data-v-19e33e89] {
        display: none
    }
    .left-side.hide .controls[data-v-19e33e89] {
        padding: 0
    }
    .main[data-v-19e33e89] {
        overflow: hidden
    }
    .bubble.assistant .bubble-inner[data-v-19e33e89],
    .bubble.user .bubble-inner[data-v-19e33e89] {
        max-width: 100%;
        width: 100%
    }
    .main-inner[data-v-19e33e89] {
        background: #fff;
        min-height: 80vh;
        padding: 0
    }
    .main-inner.app_chat[data-v-19e33e89] {
        min-height: 100vh
    }
    .agents-index.main-inner[data-v-19e33e89] {
        padding: 30px 16px 0
    }
    .input-wrapper[data-v-19e33e89] {
        box-sizing: border-box
    }
    .input-wrapper-wrapper[data-v-19e33e89] {
        background: #fff;
        border-top: 1px solid #efefef;
        bottom: 0;
        color: #232425;
        display: flex;
        font-size: 10px;
        font-style: normal;
        font-weight: 400;
        justify-content: space-evenly;
        line-height: 15px;
        padding: 9px 0 14px;
        position: fixed;
        z-index: 9
    }
    @keyframes scaleCircle-19e33e89 {
        0% {
            opacity: 1;
            transform: scale(1)
        }
        50% {
            opacity: 0;
            transform: scale(2.66)
        }
        50.1% {
            opacity: 1;
            transform: scale(1)
        }
        to {
            opacity: 1;
            transform: scale(1)
        }
    }
}

@media (prefers-color-scheme:dark) {
    @media (hover: hover) {
        .blog-link a[data-v-19e33e89]:hover {
            color:#fff
        }
    }
    .header-right .icon[data-v-19e33e89] {
        background-color: #333
    }
    @media (hover:hover) {
        .header-right .icon[data-v-19e33e89]:hover {
            background-color: #333
        }
    }
    .agents-index .basic-agent .agent[data-v-19e33e89] {
        background: #333
    }
    .agents-index .advanced-agent .popular-tasks .task[data-v-19e33e89],
    .agents-index .basic-agent .description[data-v-19e33e89] {
        background: #444
    }
    .agents-index h1[data-v-19e33e89],
    .header-left .title[data-v-19e33e89] {
        color: #eee
    }
    .agents-index h4[data-v-19e33e89] {
        color: #ccc
    }
    .agents-index .advanced-agent .agent[data-v-19e33e89] {
        background: #333;
        color: #ccc
    }
    .agents-index .advanced-agent .head .label[data-v-19e33e89],
    .agents-index .advanced-agent .head>.icon[data-v-19e33e89] {
        color: #fff
    }
    .agents-index .advanced-agent .button[data-v-19e33e89] {
        background: #ccc;
        color: #000
    }
    .agents-index .advanced-agent .popular-tasks .task .text[data-v-19e33e89] {
        color: #aaa
    }
    .agents-index .advanced-agent .popular-tasks .title[data-v-19e33e89] {
        color: #ccc
    }
    .agents-index .basic-agent .button[data-v-19e33e89] {
        background: #333;
        color: #eee
    }
    .create_result[data-v-19e33e89] {
        background: #444;
        color: #fff
    }
    .create_result .button[data-v-19e33e89] {
        background: #232425
    }
    .create_result .button.default[data-v-19e33e89] {
        background: #fff;
        color: #000
    }
    .like-dislike-controls[data-v-19e33e89] {
        background: #333
    }
    .header-content[data-v-19e33e89] {
        background: #2324250e;
        color: #fff
    }
    .header-content .title[data-v-19e33e89] {
        color: #fff
    }
    .left-side[data-v-19e33e89] {
        background: #333
    }
    .logo[data-v-19e33e89] {
        color: #fff
    }
    .left-side .controls .create_button[data-v-19e33e89],
    .main[data-v-19e33e89] {
        background: #232425
    }
    .left-bottom .create_button[data-v-19e33e89] {
        border: 1px solid #fff
    }
    .left-bottom .create_button .label[data-v-19e33e89] {
        color: #fff
    }
    .refresh_button[data-v-19e33e89],
    .stop_button[data-v-19e33e89] {
        background: #0f7fff;
        color: #fff
    }
    .logo-group .content[data-v-19e33e89],
    .logo-group .title[data-v-19e33e89],
    .logo-group .title b[data-v-19e33e89],
    .refresh_button .icon[data-v-19e33e89] {
        color: #fff
    }
    @media (hover:hover) {
        .logo-group .desc[data-v-19e33e89]:hover {
            color: #fff
        }
    }
    .left-head[data-v-19e33e89] {
        background: #333;
        color: #fff
    }
    @media (max-width:1220px) {
        .agents-index .basic-agent .agent[data-v-19e33e89] {
            background: transparent
        }
        .agents-index .basic-agent .agent-content[data-v-19e33e89] {
            background: #333
        }
        .header-content[data-v-19e33e89],
        .left-bottom[data-v-19e33e89],
        .main-inner[data-v-19e33e89],
        .mobile-header-content[data-v-19e33e89],
        .mobile-header-content-wrapper[data-v-19e33e89] {
            background: #1a1b1c
        }
        .main[data-v-19e33e89] {
            background-color: #1a1b1c
        }
    }
}

.with-infinite-canvas .main-inner[data-v-19e33e89] {
    gap: 0
}