import {
    d as r,
    b as o,
    o as t
} from "./Cf0SOiw0.js";
const e = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    xmlns: "http://www.w3.org/2000/svg"
};
const n = {
    render: function(n, s) {
        return t(), r("svg", e, s[0] || (s[0] = [o("path", {
            d: "M17 7L3 7",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linecap": "round"
        }, null, -1), o("path", {
            d: "M13 13L3 13",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linecap": "round"
        }, null, -1)]))
    }
};
export {
    n as M
};