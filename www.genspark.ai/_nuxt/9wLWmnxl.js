import {
    p as r,
    F as e,
    as as n
} from "./Cf0SOiw0.js";

function s(t, i = !0, f = []) {
    return t.forEach((t => {
        if (null !== t)
            if ("object" == typeof t)
                if (Array.isArray(t)) s(t, i, f);
                else if (t.type === e) {
            if (null === t.children) return;
            Array.isArray(t.children) && s(t.children, i, f)
        } else {
            if (t.type === n && i) return;
            f.push(t)
        } else "string" != typeof t && "number" != typeof t || f.push(r(String(t)))
    })), f
}

function t(r, e = [], n) {
    const s = {};
    return e.forEach((e => {
        s[e] = r[e]
    })), Object.assign(s, n)
}
export {
    s as f, t as k
};