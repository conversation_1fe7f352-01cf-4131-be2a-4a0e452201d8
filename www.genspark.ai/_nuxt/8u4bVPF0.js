import {
    d as e,
    o as a,
    b as t,
    _ as l,
    r as s,
    c as n,
    h as o,
    x as r,
    v as i,
    e as u,
    w as c,
    l as v,
    H as f,
    f as m,
    a9 as d,
    L as y,
    y as g,
    p as h,
    t as p,
    F as w,
    k as b,
    q as k,
    n as C,
    U as I
} from "./Cf0SOiw0.js";
import {
    N as S
} from "./DQpEsQQa.js";
import {
    N as T
} from "./nuQnue4a.js";
import {
    N as F
} from "./CW991W2w.js";
const A = {
    viewBox: "64 64 896 896",
    focusable: "false",
    "data-icon": "down",
    width: "1em",
    height: "1em",
    fill: "currentColor",
    "aria-hidden": "true"
};
const L = {
    render: function(l, s) {
        return a(), e("svg", A, s[0] || (s[0] = [t("path", {
            d: "M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
        }, null, -1)]))
    }
};
let $ = null,
    _ = new Map;
const x = async (e, a = 0, t = 50) => {
        const l = await (async () => {
            if ($) return $;
            try {
                const e = await fetch("https://www.googleapis.com/webfonts/v1/webfonts?key=AIzaSyAppO28ps_pUJQCOXcy2jieaoC-sAuAI7E&sort=popularity"),
                    a = await e.json();
                if (a.items) return $ = a.items.map((e => ({
                    family: e.family,
                    variants: e.variants,
                    subsets: e.subsets,
                    category: e.category
                }))), $
            } catch (e) {}
            return $ = [{
                family: "Arial",
                variants: ["regular"],
                category: "sans-serif"
            }, {
                family: "Helvetica",
                variants: ["regular"],
                category: "sans-serif"
            }, {
                family: "Times New Roman",
                variants: ["regular"],
                category: "serif"
            }, {
                family: "Georgia",
                variants: ["regular"],
                category: "serif"
            }, {
                family: "Verdana",
                variants: ["regular"],
                category: "sans-serif"
            }, {
                family: "Roboto",
                variants: ["regular"],
                category: "sans-serif"
            }, {
                family: "Open Sans",
                variants: ["regular"],
                category: "sans-serif"
            }, {
                family: "Lato",
                variants: ["regular"],
                category: "sans-serif"
            }, {
                family: "Montserrat",
                variants: ["regular"],
                category: "sans-serif"
            }, {
                family: "Source Sans Pro",
                variants: ["regular"],
                category: "sans-serif"
            }], $
        })();
        let s = [];
        if (e && "" !== e.trim()) {
            const a = e.toLowerCase().trim();
            s = [...l.filter((e => e.family.toLowerCase().startsWith(a))), ...l.filter((e => e.family.toLowerCase().includes(a) && !e.family.toLowerCase().startsWith(a)))]
        } else s = l;
        const n = a * t,
            o = n + t;
        return {
            fonts: s.slice(n, o),
            hasMore: o < s.length,
            total: s.length,
            currentPage: a,
            pageSize: t
        }
    },
    N = async (e, a = "400", t = document) => {
        const l = t !== document,
            s = `${e}-${a}-${l?"iframe":"main"}`;
        if (_.has(s)) return _.get(s);
        if (["Arial", "Helvetica", "Times New Roman", "Georgia", "Verdana", "Courier New"].includes(e)) return _.set(s, Promise.resolve(!0)), !0;
        const n = new Promise(((l, s) => {
            try {
                if (t.querySelector(`link[href*="family=${encodeURIComponent(e)}"]`)) return void l(!0);
                const s = t.createElement("link");
                s.href = `https://fonts.googleapis.com/css2?family=${encodeURIComponent(e)}:wght@${a}&display=swap`, s.rel = "stylesheet", s.onload = () => {
                    const s = t.defaultView || t.parentWindow || window;
                    "fonts" in s && s.document.fonts ? s.document.fonts.load(`${"400"===a?"normal":a} 16px "${e}"`).then((() => l(!0))).catch((() => l(!1))) : setTimeout((() => l(!0)), 100)
                }, s.onerror = () => {
                    l(!1)
                };
                (t.head || t.getElementsByTagName("head")[0]).appendChild(s)
            } catch (n) {
                l(!1)
            }
        }));
        return _.set(s, n), n
    },
    j = async (e, a, t = "400") => {
        if (!a || !a.contentDocument) return !1;
        const l = a.contentDocument || a.contentWindow.document,
            [s, n] = await Promise.all([N(e, t, document), N(e, t, l)]);
        return s && n
    },
    M = e => e.includes(" ") ? `"${e}", sans-serif` : `${e}, sans-serif`,
    E = {
        class: "font-selector-wrapper"
    },
    P = {
        class: "icon"
    },
    R = {
        class: "font-search"
    },
    U = {
        class: "custom-input-wrapper"
    },
    z = {
        key: 0,
        class: "loading-item"
    },
    H = {
        key: 1,
        class: "font-count-info"
    },
    O = {
        key: 0,
        class: "search-term"
    },
    V = ["data-font-family", "onClick"],
    q = {
        class: "font-name"
    },
    D = {
        class: "font-category"
    },
    W = {
        key: 2,
        class: "loading-more-item"
    },
    B = {
        key: 3,
        class: "no-more-item"
    },
    G = {
        key: 4,
        class: "no-results"
    },
    J = l({
        __name: "FontSelector",
        props: {
            currentFont: {
                type: String,
                default: "Arial"
            },
            editorIframe: {
                type: Object,
                default: null
            }
        },
        emits: ["fontChange"],
        setup(l, {
            emit: A
        }) {
            const $ = l,
                _ = A,
                J = s(!1),
                K = s(""),
                Q = s(!1),
                X = s(!1),
                Y = s(null);
            s([]);
            const Z = s([]),
                ee = s(0),
                ae = s(!0),
                te = s(0),
                le = s(!1),
                se = s(null),
                ne = s(!1),
                oe = n((() => $.currentFont || "Arial")),
                re = n((() => M($.currentFont || "Arial"))),
                ie = s(null),
                ue = new Set;
            let ce = null;
            const ve = e => {
                    J.value = e, e ? (ne.value = !0, le.value = !1, se.value && clearTimeout(se.value), K.value = "", ee.value = 0, Z.value = [], ae.value = !0, te.value = 0, me(), I((() => {
                        we(), pe()
                    }))) : setTimeout((() => {
                        ne.value = !1
                    }), 100)
                },
                fe = () => {
                    me(!1)
                },
                me = async (e = !1) => {
                    ce && clearTimeout(ce), ce = setTimeout((async () => {
                        e ? X.value = !0 : (Q.value = !0, ee.value = 0, Z.value = [], ae.value = !0);
                        try {
                            const a = await x(K.value, ee.value);
                            Z.value = e ? [...Z.value, ...a.fonts] : a.fonts, ae.value = a.hasMore, te.value = a.total, ee.value = a.currentPage + 1;
                            (e ? a.fonts.slice(0, 3) : a.fonts.slice(0, 5)).forEach((e => {
                                $.editorIframe ? j(e.family, $.editorIframe).catch((() => {})) : N(e.family).catch((() => {}))
                            })), I((() => {
                                be()
                            }))
                        } catch (a) {} finally {
                            Q.value = !1, X.value = !1
                        }
                    }), e ? 0 : 300)
                },
                de = () => {
                    if (!Y.value || !ae.value || X.value) return;
                    const {
                        scrollTop: e,
                        scrollHeight: a,
                        clientHeight: t
                    } = Y.value;
                    e + t >= a - 50 && !X.value && ae.value && me(!0)
                },
                ye = async e => {
                    ne.value = !0, le.value = !1, se.value && clearTimeout(se.value);
                    try {
                        $.editorIframe && $.editorIframe.contentDocument ? await j(e, $.editorIframe) : await N(e), _("fontChange", e), J.value = !1
                    } catch (a) {
                        _("fontChange", e), J.value = !1
                    }
                    setTimeout((() => {
                        ne.value = !1
                    }), 200)
                },
                ge = () => {
                    Z.value.length > 0 && ye(Z.value[0].family)
                },
                he = () => {
                    K.value = "", fe()
                },
                pe = () => {
                    if (!Y.value) return;
                    const e = Y.value.querySelector(".font-item.active");
                    e && e.scrollIntoView({
                        block: "nearest"
                    })
                },
                we = () => {
                    ie.value && ie.value.disconnect(), ie.value = new IntersectionObserver((e => {
                        e.forEach((e => {
                            if (e.isIntersecting && e.target.dataset.fontFamily) {
                                const a = e.target.dataset.fontFamily;
                                ue.has(a) || (ue.add(a), N(a, "400", document).catch((() => {
                                    ue.delete(a)
                                })))
                            }
                        }))
                    }), {
                        root: Y.value || null,
                        rootMargin: "50px",
                        threshold: .1
                    })
                },
                be = () => {
                    ie.value && I((() => {
                        var e;
                        const a = null == (e = Y.value) ? void 0 : e.querySelectorAll(".font-item");
                        a && a.forEach((e => {
                            ie.value.observe(e)
                        }))
                    }))
                };
            return o((() => {
                we()
            })), r((() => {
                ce && clearTimeout(ce), se.value && clearTimeout(se.value), ie.value && (ie.value.disconnect(), ie.value = null)
            })), i((() => $.currentFont), (() => {
                I((() => {
                    pe()
                }))
            })), i((() => $.editorIframe), ((e, a) => {}), {
                immediate: !0
            }), i((() => $.currentFont), ((e, a) => {
                e === a || ne.value || (le.value = !0, se.value && clearTimeout(se.value), se.value = setTimeout((() => {
                    le.value = !1
                }), 2e3))
            }), {
                immediate: !1
            }), (s, n) => (a(), e("div", E, [u(g(F), {
                trigger: "hover",
                placement: "bottom-start",
                show: J.value,
                "onUpdate:show": ve,
                "show-arrow": !1,
                scrollable: !1,
                raw: "",
                style: {
                    padding: "0",
                    "--n-border-radius": "12px",
                    "--n-box-shadow": "0"
                }
            }, {
                trigger: c((() => [u(g(T), {
                    placement: "top",
                    trigger: "hover"
                }, {
                    trigger: c((() => [t("div", {
                        class: C(["font-selector-button", {
                            "font-changed": le.value
                        }])
                    }, [t("div", {
                        class: "current-font",
                        style: k({
                            fontFamily: re.value
                        })
                    }, p(oe.value), 5), t("div", P, [u(g(L))])], 2)])),
                    default: c((() => [n[3] || (n[3] = t("span", null, "Font", -1))])),
                    _: 1
                })])),
                default: c((() => [t("div", {
                    class: "font-dropdown",
                    onClick: n[2] || (n[2] = v((() => {}), ["stop"]))
                }, [t("div", R, [t("div", U, [n[4] || (n[4] = t("div", {
                    class: "search-icon"
                }, "🔍", -1)), f(t("input", {
                    "onUpdate:modelValue": n[0] || (n[0] = e => K.value = e),
                    type: "text",
                    placeholder: "Search fonts...",
                    class: "custom-input",
                    onInput: fe,
                    onKeydown: d(ge, ["enter"])
                }, null, 544), [
                    [y, K.value]
                ]), K.value ? (a(), e("button", {
                    key: 0,
                    type: "button",
                    class: "clear-button",
                    onClick: he,
                    onMousedown: n[1] || (n[1] = v((() => {}), ["prevent"]))
                }, " ✕ ", 32)) : m("", !0)])]), t("div", {
                    class: "font-list",
                    ref_key: "fontListRef",
                    ref: Y,
                    onScroll: de
                }, [Q.value ? (a(), e("div", z, [u(g(S), {
                    size: "small"
                }), n[5] || (n[5] = t("span", null, "Loading...", -1))])) : m("", !0), !Q.value && Z.value.length > 0 ? (a(), e("div", H, [h(" Showing " + p(Z.value.length) + " / " + p(te.value) + " fonts ", 1), K.value.trim() ? (a(), e("span", O, ' (Search: "' + p(K.value.trim()) + '") ', 1)) : m("", !0)])) : m("", !0), (a(!0), e(w, null, b(Z.value, (s => (a(), e("div", {
                    key: s.family,
                    class: C(["font-item", {
                        active: s.family === l.currentFont
                    }]),
                    "data-font-family": s.family,
                    onClick: e => ye(s.family),
                    style: k({
                        fontFamily: g(M)(s.family)
                    })
                }, [t("span", q, p(s.family), 1), t("span", D, p(s.category), 1)], 14, V)))), 128)), X.value ? (a(), e("div", W, [u(g(S), {
                    size: "small"
                }), n[6] || (n[6] = t("span", null, "Loading more fonts...", -1))])) : m("", !0), !Q.value && !X.value && !ae.value && Z.value.length > 0 ? (a(), e("div", B, " All fonts displayed ")) : m("", !0), Q.value || 0 !== Z.value.length ? m("", !0) : (a(), e("div", G, " No matching fonts found "))], 544)])])),
                _: 1
            }, 8, ["show"])]))
        }
    }, [
        ["__scopeId", "data-v-21596d1d"]
    ]);
export {
    L as D, J as F
};