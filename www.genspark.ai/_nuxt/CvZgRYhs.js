var A = function(e, t) {
    return (A = Object.setPrototypeOf || {
            __proto__: []
        }
        instanceof Array && function(A, e) {
            A.__proto__ = e
        } || function(A, e) {
            for (var t in e) Object.prototype.hasOwnProperty.call(e, t) && (A[t] = e[t])
        })(e, t)
};

function e(e, t) {
    if ("function" != typeof t && null !== t) throw new TypeError("Class extends value " + String(t) + " is not a constructor or null");

    function r() {
        this.constructor = e
    }
    A(e, t), e.prototype = null === t ? Object.create(t) : (r.prototype = t.prototype, new r)
}
var t = function() {
    return t = Object.assign || function(A) {
        for (var e, t = 1, r = arguments.length; t < r; t++)
            for (var B in e = arguments[t]) Object.prototype.hasOwnProperty.call(e, B) && (A[B] = e[B]);
        return A
    }, t.apply(this, arguments)
};

function r(A, e, t, r) {
    return new(t || (t = Promise))((function(e, B) {
        function n(A) {
            try {
                o(r.next(A))
            } catch (e) {
                B(e)
            }
        }

        function s(A) {
            try {
                o(r.throw(A))
            } catch (e) {
                B(e)
            }
        }

        function o(A) {
            var r;
            A.done ? e(A.value) : (r = A.value, r instanceof t ? r : new t((function(A) {
                A(r)
            }))).then(n, s)
        }
        o((r = r.apply(A, [])).next())
    }))
}

function B(A, e) {
    var t, r, B, n, s = {
        label: 0,
        sent: function() {
            if (1 & B[0]) throw B[1];
            return B[1]
        },
        trys: [],
        ops: []
    };
    return n = {
        next: o(0),
        throw: o(1),
        return: o(2)
    }, "function" == typeof Symbol && (n[Symbol.iterator] = function() {
        return this
    }), n;

    function o(n) {
        return function(o) {
            return function(n) {
                if (t) throw new TypeError("Generator is already executing.");
                for (; s;) try {
                    if (t = 1, r && (B = 2 & n[0] ? r.return : n[0] ? r.throw || ((B = r.return) && B.call(r), 0) : r.next) && !(B = B.call(r, n[1])).done) return B;
                    switch (r = 0, B && (n = [2 & n[0], B.value]), n[0]) {
                        case 0:
                        case 1:
                            B = n;
                            break;
                        case 4:
                            return s.label++, {
                                value: n[1],
                                done: !1
                            };
                        case 5:
                            s.label++, r = n[1], n = [0];
                            continue;
                        case 7:
                            n = s.ops.pop(), s.trys.pop();
                            continue;
                        default:
                            if (!(B = s.trys, (B = B.length > 0 && B[B.length - 1]) || 6 !== n[0] && 2 !== n[0])) {
                                s = 0;
                                continue
                            }
                            if (3 === n[0] && (!B || n[1] > B[0] && n[1] < B[3])) {
                                s.label = n[1];
                                break
                            }
                            if (6 === n[0] && s.label < B[1]) {
                                s.label = B[1], B = n;
                                break
                            }
                            if (B && s.label < B[2]) {
                                s.label = B[2], s.ops.push(n);
                                break
                            }
                            B[2] && s.ops.pop(), s.trys.pop();
                            continue
                    }
                    n = e.call(A, s)
                } catch (o) {
                    n = [6, o], r = 0
                } finally {
                    t = B = 0
                }
                if (5 & n[0]) throw n[1];
                return {
                    value: n[0] ? n[1] : void 0,
                    done: !0
                }
            }([n, o])
        }
    }
}
for (var n = function() {
        function A(A, e, t, r) {
            this.left = A, this.top = e, this.width = t, this.height = r
        }
        return A.prototype.add = function(e, t, r, B) {
            return new A(this.left + e, this.top + t, this.width + r, this.height + B)
        }, A.fromClientRect = function(e, t) {
            return new A(t.left + e.windowBounds.left, t.top + e.windowBounds.top, t.width, t.height)
        }, A.fromDOMRectList = function(e, t) {
            var r = Array.from(t).find((function(A) {
                return 0 !== A.width
            }));
            return r ? new A(r.left + e.windowBounds.left, r.top + e.windowBounds.top, r.width, r.height) : A.EMPTY
        }, A.EMPTY = new A(0, 0, 0, 0), A
    }(), s = function(A, e) {
        return n.fromClientRect(A, e.getBoundingClientRect())
    }, o = function(A) {
        for (var e = [], t = 0, r = A.length; t < r;) {
            var B = A.charCodeAt(t++);
            if (B >= 55296 && B <= 56319 && t < r) {
                var n = A.charCodeAt(t++);
                56320 == (64512 & n) ? e.push(((1023 & B) << 10) + (1023 & n) + 65536) : (e.push(B), t--)
            } else e.push(B)
        }
        return e
    }, i = function() {
        for (var A = [], e = 0; e < arguments.length; e++) A[e] = arguments[e];
        if (String.fromCodePoint) return String.fromCodePoint.apply(String, A);
        var t = A.length;
        if (!t) return "";
        for (var r = [], B = -1, n = ""; ++B < t;) {
            var s = A[B];
            s <= 65535 ? r.push(s) : (s -= 65536, r.push(55296 + (s >> 10), s % 1024 + 56320)), (B + 1 === t || r.length > 16384) && (n += String.fromCharCode.apply(String, r), r.length = 0)
        }
        return n
    }, Q = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", c = "undefined" == typeof Uint8Array ? [] : new Uint8Array(256), a = 0; a < 64; a++) c[Q.charCodeAt(a)] = a;
for (var w = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", g = "undefined" == typeof Uint8Array ? [] : new Uint8Array(256), u = 0; u < 64; u++) g[w.charCodeAt(u)] = u;
for (var U = function(A, e, t) {
        return A.slice ? A.slice(e, t) : new Uint16Array(Array.prototype.slice.call(A, e, t))
    }, C = function() {
        function A(A, e, t, r, B, n) {
            this.initialValue = A, this.errorValue = e, this.highStart = t, this.highValueIndex = r, this.index = B, this.data = n
        }
        return A.prototype.get = function(A) {
            var e;
            if (A >= 0) {
                if (A < 55296 || A > 56319 && A <= 65535) return e = ((e = this.index[A >> 5]) << 2) + (31 & A), this.data[e];
                if (A <= 65535) return e = ((e = this.index[2048 + (A - 55296 >> 5)]) << 2) + (31 & A), this.data[e];
                if (A < this.highStart) return e = 2080 + (A >> 11), e = this.index[e], e += A >> 5 & 63, e = ((e = this.index[e]) << 2) + (31 & A), this.data[e];
                if (A <= 1114111) return this.data[this.highValueIndex]
            }
            return this.errorValue
        }, A
    }(), l = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", F = "undefined" == typeof Uint8Array ? [] : new Uint8Array(256), h = 0; h < 64; h++) F[l.charCodeAt(h)] = h;
var d, f, H, p, E, I, y, K, m = 10,
    L = 13,
    b = 15,
    v = 17,
    D = 18,
    x = 19,
    M = 20,
    S = 21,
    T = 22,
    G = 24,
    O = 25,
    V = 26,
    k = 27,
    R = 28,
    N = 30,
    P = 32,
    X = 33,
    J = 34,
    Y = 35,
    W = 37,
    Z = 38,
    _ = 39,
    q = 40,
    j = 42,
    z = [9001, 65288],
    $ = "×",
    AA = "÷",
    eA = (p = function(A) {
        var e, t, r, B, n, s = .75 * A.length,
            o = A.length,
            i = 0;
        "=" === A[A.length - 1] && (s--, "=" === A[A.length - 2] && s--);
        var Q = "undefined" != typeof ArrayBuffer && "undefined" != typeof Uint8Array && void 0 !== Uint8Array.prototype.slice ? new ArrayBuffer(s) : new Array(s),
            c = Array.isArray(Q) ? Q : new Uint8Array(Q);
        for (e = 0; e < o; e += 4) t = g[A.charCodeAt(e)], r = g[A.charCodeAt(e + 1)], B = g[A.charCodeAt(e + 2)], n = g[A.charCodeAt(e + 3)], c[i++] = t << 2 | r >> 4, c[i++] = (15 & r) << 4 | B >> 2, c[i++] = (3 & B) << 6 | 63 & n;
        return Q
    }("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"), E = Array.isArray(p) ? function(A) {
        for (var e = A.length, t = [], r = 0; r < e; r += 4) t.push(A[r + 3] << 24 | A[r + 2] << 16 | A[r + 1] << 8 | A[r]);
        return t
    }(p) : new Uint32Array(p), I = Array.isArray(p) ? function(A) {
        for (var e = A.length, t = [], r = 0; r < e; r += 2) t.push(A[r + 1] << 8 | A[r]);
        return t
    }(p) : new Uint16Array(p), y = U(I, 12, E[4] / 2), K = 2 === E[5] ? U(I, (24 + E[4]) / 2) : (d = E, f = Math.ceil((24 + E[4]) / 4), d.slice ? d.slice(f, H) : new Uint32Array(Array.prototype.slice.call(d, f, H))), new C(E[0], E[1], E[2], E[3], y, K)),
    tA = [N, 36],
    rA = [1, 2, 3, 5],
    BA = [m, 8],
    nA = [k, V],
    sA = rA.concat(BA),
    oA = [Z, _, q, J, Y],
    iA = [b, L],
    QA = function(A, e, t, r) {
        var B = r[t];
        if (Array.isArray(A) ? -1 !== A.indexOf(B) : A === B)
            for (var n = t; n <= r.length;) {
                if ((i = r[++n]) === e) return !0;
                if (i !== m) break
            }
        if (B === m)
            for (n = t; n > 0;) {
                var s = r[--n];
                if (Array.isArray(A) ? -1 !== A.indexOf(s) : A === s)
                    for (var o = t; o <= r.length;) {
                        var i;
                        if ((i = r[++o]) === e) return !0;
                        if (i !== m) break
                    }
                if (s !== m) break
            }
        return !1
    },
    cA = function(A, e) {
        for (var t = A; t >= 0;) {
            var r = e[t];
            if (r !== m) return r;
            t--
        }
        return 0
    },
    aA = function(A, e, t, r, B) {
        if (0 === t[r]) return $;
        var n = r - 1;
        if (Array.isArray(B) && !0 === B[n]) return $;
        var s = n - 1,
            o = n + 1,
            i = e[n],
            Q = s >= 0 ? e[s] : 0,
            c = e[o];
        if (2 === i && 3 === c) return $;
        if (-1 !== rA.indexOf(i)) return "!";
        if (-1 !== rA.indexOf(c)) return $;
        if (-1 !== BA.indexOf(c)) return $;
        if (8 === cA(n, e)) return AA;
        if (11 === eA.get(A[n])) return $;
        if ((i === P || i === X) && 11 === eA.get(A[o])) return $;
        if (7 === i || 7 === c) return $;
        if (9 === i) return $;
        if (-1 === [m, L, b].indexOf(i) && 9 === c) return $;
        if (-1 !== [v, D, x, G, R].indexOf(c)) return $;
        if (cA(n, e) === T) return $;
        if (QA(23, T, n, e)) return $;
        if (QA([v, D], S, n, e)) return $;
        if (QA(12, 12, n, e)) return $;
        if (i === m) return AA;
        if (23 === i || 23 === c) return $;
        if (16 === c || 16 === i) return AA;
        if (-1 !== [L, b, S].indexOf(c) || 14 === i) return $;
        if (36 === Q && -1 !== iA.indexOf(i)) return $;
        if (i === R && 36 === c) return $;
        if (c === M) return $;
        if (-1 !== tA.indexOf(c) && i === O || -1 !== tA.indexOf(i) && c === O) return $;
        if (i === k && -1 !== [W, P, X].indexOf(c) || -1 !== [W, P, X].indexOf(i) && c === V) return $;
        if (-1 !== tA.indexOf(i) && -1 !== nA.indexOf(c) || -1 !== nA.indexOf(i) && -1 !== tA.indexOf(c)) return $;
        if (-1 !== [k, V].indexOf(i) && (c === O || -1 !== [T, b].indexOf(c) && e[o + 1] === O) || -1 !== [T, b].indexOf(i) && c === O || i === O && -1 !== [O, R, G].indexOf(c)) return $;
        if (-1 !== [O, R, G, v, D].indexOf(c))
            for (var a = n; a >= 0;) {
                if ((w = e[a]) === O) return $;
                if (-1 === [R, G].indexOf(w)) break;
                a--
            }
        if (-1 !== [k, V].indexOf(c))
            for (a = -1 !== [v, D].indexOf(i) ? s : n; a >= 0;) {
                var w;
                if ((w = e[a]) === O) return $;
                if (-1 === [R, G].indexOf(w)) break;
                a--
            }
        if (Z === i && -1 !== [Z, _, J, Y].indexOf(c) || -1 !== [_, J].indexOf(i) && -1 !== [_, q].indexOf(c) || -1 !== [q, Y].indexOf(i) && c === q) return $;
        if (-1 !== oA.indexOf(i) && -1 !== [M, V].indexOf(c) || -1 !== oA.indexOf(c) && i === k) return $;
        if (-1 !== tA.indexOf(i) && -1 !== tA.indexOf(c)) return $;
        if (i === G && -1 !== tA.indexOf(c)) return $;
        if (-1 !== tA.concat(O).indexOf(i) && c === T && -1 === z.indexOf(A[o]) || -1 !== tA.concat(O).indexOf(c) && i === D) return $;
        if (41 === i && 41 === c) {
            for (var g = t[n], u = 1; g > 0 && 41 === e[--g];) u++;
            if (u % 2 != 0) return $
        }
        return i === P && c === X ? $ : AA
    },
    wA = function(A, e) {
        e || (e = {
            lineBreak: "normal",
            wordBreak: "normal"
        });
        var t = function(A, e) {
                void 0 === e && (e = "strict");
                var t = [],
                    r = [],
                    B = [];
                return A.forEach((function(A, n) {
                    var s = eA.get(A);
                    if (s > 50 ? (B.push(!0), s -= 50) : B.push(!1), -1 !== ["normal", "auto", "loose"].indexOf(e) && -1 !== [8208, 8211, 12316, 12448].indexOf(A)) return r.push(n), t.push(16);
                    if (4 === s || 11 === s) {
                        if (0 === n) return r.push(n), t.push(N);
                        var o = t[n - 1];
                        return -1 === sA.indexOf(o) ? (r.push(r[n - 1]), t.push(o)) : (r.push(n), t.push(N))
                    }
                    return r.push(n), 31 === s ? t.push("strict" === e ? S : W) : s === j || 29 === s ? t.push(N) : 43 === s ? A >= 131072 && A <= 196605 || A >= 196608 && A <= 262141 ? t.push(W) : t.push(N) : void t.push(s)
                })), [r, t, B]
            }(A, e.lineBreak),
            r = t[0],
            B = t[1],
            n = t[2];
        "break-all" !== e.wordBreak && "break-word" !== e.wordBreak || (B = B.map((function(A) {
            return -1 !== [O, N, j].indexOf(A) ? W : A
        })));
        var s = "keep-all" === e.wordBreak ? n.map((function(e, t) {
            return e && A[t] >= 19968 && A[t] <= 40959
        })) : void 0;
        return [r, B, s]
    },
    gA = function() {
        function A(A, e, t, r) {
            this.codePoints = A, this.required = "!" === e, this.start = t, this.end = r
        }
        return A.prototype.slice = function() {
            return i.apply(void 0, this.codePoints.slice(this.start, this.end))
        }, A
    }(),
    uA = 45,
    UA = 43,
    CA = -1,
    lA = function(A) {
        return A >= 48 && A <= 57
    },
    FA = function(A) {
        return lA(A) || A >= 65 && A <= 70 || A >= 97 && A <= 102
    },
    hA = function(A) {
        return 10 === A || 9 === A || 32 === A
    },
    dA = function(A) {
        return function(A) {
            return function(A) {
                return A >= 97 && A <= 122
            }(A) || function(A) {
                return A >= 65 && A <= 90
            }(A)
        }(A) || function(A) {
            return A >= 128
        }(A) || 95 === A
    },
    fA = function(A) {
        return dA(A) || lA(A) || A === uA
    },
    HA = function(A) {
        return A >= 0 && A <= 8 || 11 === A || A >= 14 && A <= 31 || 127 === A
    },
    pA = function(A, e) {
        return 92 === A && 10 !== e
    },
    EA = function(A, e, t) {
        return A === uA ? dA(e) || pA(e, t) : !!dA(A) || !(92 !== A || !pA(A, e))
    },
    IA = function(A, e, t) {
        return A === UA || A === uA ? !!lA(e) || 46 === e && lA(t) : lA(46 === A ? e : A)
    },
    yA = function(A) {
        var e = 0,
            t = 1;
        A[e] !== UA && A[e] !== uA || (A[e] === uA && (t = -1), e++);
        for (var r = []; lA(A[e]);) r.push(A[e++]);
        var B = r.length ? parseInt(i.apply(void 0, r), 10) : 0;
        46 === A[e] && e++;
        for (var n = []; lA(A[e]);) n.push(A[e++]);
        var s = n.length,
            o = s ? parseInt(i.apply(void 0, n), 10) : 0;
        69 !== A[e] && 101 !== A[e] || e++;
        var Q = 1;
        A[e] !== UA && A[e] !== uA || (A[e] === uA && (Q = -1), e++);
        for (var c = []; lA(A[e]);) c.push(A[e++]);
        var a = c.length ? parseInt(i.apply(void 0, c), 10) : 0;
        return t * (B + o * Math.pow(10, -s)) * Math.pow(10, Q * a)
    },
    KA = {
        type: 2
    },
    mA = {
        type: 3
    },
    LA = {
        type: 4
    },
    bA = {
        type: 13
    },
    vA = {
        type: 8
    },
    DA = {
        type: 21
    },
    xA = {
        type: 9
    },
    MA = {
        type: 10
    },
    SA = {
        type: 11
    },
    TA = {
        type: 12
    },
    GA = {
        type: 14
    },
    OA = {
        type: 23
    },
    VA = {
        type: 1
    },
    kA = {
        type: 25
    },
    RA = {
        type: 24
    },
    NA = {
        type: 26
    },
    PA = {
        type: 27
    },
    XA = {
        type: 28
    },
    JA = {
        type: 29
    },
    YA = {
        type: 31
    },
    WA = {
        type: 32
    },
    ZA = function() {
        function A() {
            this._value = []
        }
        return A.prototype.write = function(A) {
            this._value = this._value.concat(o(A))
        }, A.prototype.read = function() {
            for (var A = [], e = this.consumeToken(); e !== WA;) A.push(e), e = this.consumeToken();
            return A
        }, A.prototype.consumeToken = function() {
            var A = this.consumeCodePoint();
            switch (A) {
                case 34:
                    return this.consumeStringToken(34);
                case 35:
                    var e = this.peekCodePoint(0),
                        t = this.peekCodePoint(1),
                        r = this.peekCodePoint(2);
                    if (fA(e) || pA(t, r)) {
                        var B = EA(e, t, r) ? 2 : 1;
                        return {
                            type: 5,
                            value: this.consumeName(),
                            flags: B
                        }
                    }
                    break;
                case 36:
                    if (61 === this.peekCodePoint(0)) return this.consumeCodePoint(), bA;
                    break;
                case 39:
                    return this.consumeStringToken(39);
                case 40:
                    return KA;
                case 41:
                    return mA;
                case 42:
                    if (61 === this.peekCodePoint(0)) return this.consumeCodePoint(), GA;
                    break;
                case UA:
                    if (IA(A, this.peekCodePoint(0), this.peekCodePoint(1))) return this.reconsumeCodePoint(A), this.consumeNumericToken();
                    break;
                case 44:
                    return LA;
                case uA:
                    var n = A,
                        s = this.peekCodePoint(0),
                        o = this.peekCodePoint(1);
                    if (IA(n, s, o)) return this.reconsumeCodePoint(A), this.consumeNumericToken();
                    if (EA(n, s, o)) return this.reconsumeCodePoint(A), this.consumeIdentLikeToken();
                    if (s === uA && 62 === o) return this.consumeCodePoint(), this.consumeCodePoint(), RA;
                    break;
                case 46:
                    if (IA(A, this.peekCodePoint(0), this.peekCodePoint(1))) return this.reconsumeCodePoint(A), this.consumeNumericToken();
                    break;
                case 47:
                    if (42 === this.peekCodePoint(0))
                        for (this.consumeCodePoint();;) {
                            var Q = this.consumeCodePoint();
                            if (42 === Q && 47 === (Q = this.consumeCodePoint())) return this.consumeToken();
                            if (Q === CA) return this.consumeToken()
                        }
                    break;
                case 58:
                    return NA;
                case 59:
                    return PA;
                case 60:
                    if (33 === this.peekCodePoint(0) && this.peekCodePoint(1) === uA && this.peekCodePoint(2) === uA) return this.consumeCodePoint(), this.consumeCodePoint(), kA;
                    break;
                case 64:
                    var c = this.peekCodePoint(0),
                        a = this.peekCodePoint(1),
                        w = this.peekCodePoint(2);
                    if (EA(c, a, w)) return {
                        type: 7,
                        value: this.consumeName()
                    };
                    break;
                case 91:
                    return XA;
                case 92:
                    if (pA(A, this.peekCodePoint(0))) return this.reconsumeCodePoint(A), this.consumeIdentLikeToken();
                    break;
                case 93:
                    return JA;
                case 61:
                    if (61 === this.peekCodePoint(0)) return this.consumeCodePoint(), vA;
                    break;
                case 123:
                    return SA;
                case 125:
                    return TA;
                case 117:
                case 85:
                    var g = this.peekCodePoint(0),
                        u = this.peekCodePoint(1);
                    return g !== UA || !FA(u) && 63 !== u || (this.consumeCodePoint(), this.consumeUnicodeRangeToken()), this.reconsumeCodePoint(A), this.consumeIdentLikeToken();
                case 124:
                    if (61 === this.peekCodePoint(0)) return this.consumeCodePoint(), xA;
                    if (124 === this.peekCodePoint(0)) return this.consumeCodePoint(), DA;
                    break;
                case 126:
                    if (61 === this.peekCodePoint(0)) return this.consumeCodePoint(), MA;
                    break;
                case CA:
                    return WA
            }
            return hA(A) ? (this.consumeWhiteSpace(), YA) : lA(A) ? (this.reconsumeCodePoint(A), this.consumeNumericToken()) : dA(A) ? (this.reconsumeCodePoint(A), this.consumeIdentLikeToken()) : {
                type: 6,
                value: i(A)
            }
        }, A.prototype.consumeCodePoint = function() {
            var A = this._value.shift();
            return void 0 === A ? -1 : A
        }, A.prototype.reconsumeCodePoint = function(A) {
            this._value.unshift(A)
        }, A.prototype.peekCodePoint = function(A) {
            return A >= this._value.length ? -1 : this._value[A]
        }, A.prototype.consumeUnicodeRangeToken = function() {
            for (var A = [], e = this.consumeCodePoint(); FA(e) && A.length < 6;) A.push(e), e = this.consumeCodePoint();
            for (var t = !1; 63 === e && A.length < 6;) A.push(e), e = this.consumeCodePoint(), t = !0;
            if (t) return {
                type: 30,
                start: parseInt(i.apply(void 0, A.map((function(A) {
                    return 63 === A ? 48 : A
                }))), 16),
                end: parseInt(i.apply(void 0, A.map((function(A) {
                    return 63 === A ? 70 : A
                }))), 16)
            };
            var r = parseInt(i.apply(void 0, A), 16);
            if (this.peekCodePoint(0) === uA && FA(this.peekCodePoint(1))) {
                this.consumeCodePoint(), e = this.consumeCodePoint();
                for (var B = []; FA(e) && B.length < 6;) B.push(e), e = this.consumeCodePoint();
                return {
                    type: 30,
                    start: r,
                    end: parseInt(i.apply(void 0, B), 16)
                }
            }
            return {
                type: 30,
                start: r,
                end: r
            }
        }, A.prototype.consumeIdentLikeToken = function() {
            var A = this.consumeName();
            return "url" === A.toLowerCase() && 40 === this.peekCodePoint(0) ? (this.consumeCodePoint(), this.consumeUrlToken()) : 40 === this.peekCodePoint(0) ? (this.consumeCodePoint(), {
                type: 19,
                value: A
            }) : {
                type: 20,
                value: A
            }
        }, A.prototype.consumeUrlToken = function() {
            var A = [];
            if (this.consumeWhiteSpace(), this.peekCodePoint(0) === CA) return {
                type: 22,
                value: ""
            };
            var e = this.peekCodePoint(0);
            if (39 === e || 34 === e) {
                var t = this.consumeStringToken(this.consumeCodePoint());
                return 0 === t.type && (this.consumeWhiteSpace(), this.peekCodePoint(0) === CA || 41 === this.peekCodePoint(0)) ? (this.consumeCodePoint(), {
                    type: 22,
                    value: t.value
                }) : (this.consumeBadUrlRemnants(), OA)
            }
            for (;;) {
                var r = this.consumeCodePoint();
                if (r === CA || 41 === r) return {
                    type: 22,
                    value: i.apply(void 0, A)
                };
                if (hA(r)) return this.consumeWhiteSpace(), this.peekCodePoint(0) === CA || 41 === this.peekCodePoint(0) ? (this.consumeCodePoint(), {
                    type: 22,
                    value: i.apply(void 0, A)
                }) : (this.consumeBadUrlRemnants(), OA);
                if (34 === r || 39 === r || 40 === r || HA(r)) return this.consumeBadUrlRemnants(), OA;
                if (92 === r) {
                    if (!pA(r, this.peekCodePoint(0))) return this.consumeBadUrlRemnants(), OA;
                    A.push(this.consumeEscapedCodePoint())
                } else A.push(r)
            }
        }, A.prototype.consumeWhiteSpace = function() {
            for (; hA(this.peekCodePoint(0));) this.consumeCodePoint()
        }, A.prototype.consumeBadUrlRemnants = function() {
            for (;;) {
                var A = this.consumeCodePoint();
                if (41 === A || A === CA) return;
                pA(A, this.peekCodePoint(0)) && this.consumeEscapedCodePoint()
            }
        }, A.prototype.consumeStringSlice = function(A) {
            for (var e = ""; A > 0;) {
                var t = Math.min(5e4, A);
                e += i.apply(void 0, this._value.splice(0, t)), A -= t
            }
            return this._value.shift(), e
        }, A.prototype.consumeStringToken = function(A) {
            for (var e = "", t = 0;;) {
                var r = this._value[t];
                if (r === CA || void 0 === r || r === A) return {
                    type: 0,
                    value: e += this.consumeStringSlice(t)
                };
                if (10 === r) return this._value.splice(0, t), VA;
                if (92 === r) {
                    var B = this._value[t + 1];
                    B !== CA && void 0 !== B && (10 === B ? (e += this.consumeStringSlice(t), t = -1, this._value.shift()) : pA(r, B) && (e += this.consumeStringSlice(t), e += i(this.consumeEscapedCodePoint()), t = -1))
                }
                t++
            }
        }, A.prototype.consumeNumber = function() {
            var A = [],
                e = 4,
                t = this.peekCodePoint(0);
            for (t !== UA && t !== uA || A.push(this.consumeCodePoint()); lA(this.peekCodePoint(0));) A.push(this.consumeCodePoint());
            t = this.peekCodePoint(0);
            var r = this.peekCodePoint(1);
            if (46 === t && lA(r))
                for (A.push(this.consumeCodePoint(), this.consumeCodePoint()), e = 8; lA(this.peekCodePoint(0));) A.push(this.consumeCodePoint());
            t = this.peekCodePoint(0), r = this.peekCodePoint(1);
            var B = this.peekCodePoint(2);
            if ((69 === t || 101 === t) && ((r === UA || r === uA) && lA(B) || lA(r)))
                for (A.push(this.consumeCodePoint(), this.consumeCodePoint()), e = 8; lA(this.peekCodePoint(0));) A.push(this.consumeCodePoint());
            return [yA(A), e]
        }, A.prototype.consumeNumericToken = function() {
            var A = this.consumeNumber(),
                e = A[0],
                t = A[1],
                r = this.peekCodePoint(0),
                B = this.peekCodePoint(1),
                n = this.peekCodePoint(2);
            return EA(r, B, n) ? {
                type: 15,
                number: e,
                flags: t,
                unit: this.consumeName()
            } : 37 === r ? (this.consumeCodePoint(), {
                type: 16,
                number: e,
                flags: t
            }) : {
                type: 17,
                number: e,
                flags: t
            }
        }, A.prototype.consumeEscapedCodePoint = function() {
            var A = this.consumeCodePoint();
            if (FA(A)) {
                for (var e = i(A); FA(this.peekCodePoint(0)) && e.length < 6;) e += i(this.consumeCodePoint());
                hA(this.peekCodePoint(0)) && this.consumeCodePoint();
                var t = parseInt(e, 16);
                return 0 === t || function(A) {
                    return A >= 55296 && A <= 57343
                }(t) || t > 1114111 ? 65533 : t
            }
            return A === CA ? 65533 : A
        }, A.prototype.consumeName = function() {
            for (var A = "";;) {
                var e = this.consumeCodePoint();
                if (fA(e)) A += i(e);
                else {
                    if (!pA(e, this.peekCodePoint(0))) return this.reconsumeCodePoint(e), A;
                    A += i(this.consumeEscapedCodePoint())
                }
            }
        }, A
    }(),
    _A = function() {
        function A(A) {
            this._tokens = A
        }
        return A.create = function(e) {
            var t = new ZA;
            return t.write(e), new A(t.read())
        }, A.parseValue = function(e) {
            return A.create(e).parseComponentValue()
        }, A.parseValues = function(e) {
            return A.create(e).parseComponentValues()
        }, A.prototype.parseComponentValue = function() {
            for (var A = this.consumeToken(); 31 === A.type;) A = this.consumeToken();
            if (32 === A.type) throw new SyntaxError("Error parsing CSS component value, unexpected EOF");
            this.reconsumeToken(A);
            var e = this.consumeComponentValue();
            do {
                A = this.consumeToken()
            } while (31 === A.type);
            if (32 === A.type) return e;
            throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")
        }, A.prototype.parseComponentValues = function() {
            for (var A = [];;) {
                var e = this.consumeComponentValue();
                if (32 === e.type) return A;
                A.push(e), A.push()
            }
        }, A.prototype.consumeComponentValue = function() {
            var A = this.consumeToken();
            switch (A.type) {
                case 11:
                case 28:
                case 2:
                    return this.consumeSimpleBlock(A.type);
                case 19:
                    return this.consumeFunction(A)
            }
            return A
        }, A.prototype.consumeSimpleBlock = function(A) {
            for (var e = {
                    type: A,
                    values: []
                }, t = this.consumeToken();;) {
                if (32 === t.type || Be(t, A)) return e;
                this.reconsumeToken(t), e.values.push(this.consumeComponentValue()), t = this.consumeToken()
            }
        }, A.prototype.consumeFunction = function(A) {
            for (var e = {
                    name: A.value,
                    values: [],
                    type: 18
                };;) {
                var t = this.consumeToken();
                if (32 === t.type || 3 === t.type) return e;
                this.reconsumeToken(t), e.values.push(this.consumeComponentValue())
            }
        }, A.prototype.consumeToken = function() {
            var A = this._tokens.shift();
            return void 0 === A ? WA : A
        }, A.prototype.reconsumeToken = function(A) {
            this._tokens.unshift(A)
        }, A
    }(),
    qA = function(A) {
        return 15 === A.type
    },
    jA = function(A) {
        return 17 === A.type
    },
    zA = function(A) {
        return 20 === A.type
    },
    $A = function(A) {
        return 0 === A.type
    },
    Ae = function(A, e) {
        return zA(A) && A.value === e
    },
    ee = function(A) {
        return 31 !== A.type
    },
    te = function(A) {
        return 31 !== A.type && 4 !== A.type
    },
    re = function(A) {
        var e = [],
            t = [];
        return A.forEach((function(A) {
            if (4 === A.type) {
                if (0 === t.length) throw new Error("Error parsing function args, zero tokens for arg");
                return e.push(t), void(t = [])
            }
            31 !== A.type && t.push(A)
        })), t.length && e.push(t), e
    },
    Be = function(A, e) {
        return 11 === e && 12 === A.type || (28 === e && 29 === A.type || 2 === e && 3 === A.type)
    },
    ne = function(A) {
        return 17 === A.type || 15 === A.type
    },
    se = function(A) {
        return 16 === A.type || ne(A)
    },
    oe = function(A) {
        return A.length > 1 ? [A[0], A[1]] : [A[0]]
    },
    ie = {
        type: 17,
        number: 0,
        flags: 4
    },
    Qe = {
        type: 16,
        number: 50,
        flags: 4
    },
    ce = {
        type: 16,
        number: 100,
        flags: 4
    },
    ae = function(A, e, t) {
        var r = A[0],
            B = A[1];
        return [we(r, e), we(void 0 !== B ? B : r, t)]
    },
    we = function(A, e) {
        if (16 === A.type) return A.number / 100 * e;
        if (qA(A)) switch (A.unit) {
            case "rem":
            case "em":
                return 16 * A.number;
            default:
                return A.number
        }
        return A.number
    },
    ge = "grad",
    ue = "turn",
    Ue = function(A, e) {
        if (15 === e.type) switch (e.unit) {
            case "deg":
                return Math.PI * e.number / 180;
            case ge:
                return Math.PI / 200 * e.number;
            case "rad":
                return e.number;
            case ue:
                return 2 * Math.PI * e.number
        }
        throw new Error("Unsupported angle type")
    },
    Ce = function(A) {
        return 15 === A.type && ("deg" === A.unit || A.unit === ge || "rad" === A.unit || A.unit === ue)
    },
    le = function(A) {
        switch (A.filter(zA).map((function(A) {
            return A.value
        })).join(" ")) {
            case "to bottom right":
            case "to right bottom":
            case "left top":
            case "top left":
                return [ie, ie];
            case "to top":
            case "bottom":
                return Fe(0);
            case "to bottom left":
            case "to left bottom":
            case "right top":
            case "top right":
                return [ie, ce];
            case "to right":
            case "left":
                return Fe(90);
            case "to top left":
            case "to left top":
            case "right bottom":
            case "bottom right":
                return [ce, ce];
            case "to bottom":
            case "top":
                return Fe(180);
            case "to top right":
            case "to right top":
            case "left bottom":
            case "bottom left":
                return [ce, ie];
            case "to left":
            case "right":
                return Fe(270)
        }
        return 0
    },
    Fe = function(A) {
        return Math.PI * A / 180
    },
    he = function(A, e) {
        if (18 === e.type) {
            var t = Ke[e.name];
            if (void 0 === t) throw new Error('Attempting to parse an unsupported color function "' + e.name + '"');
            return t(A, e.values)
        }
        if (5 === e.type) {
            if (3 === e.value.length) {
                var r = e.value.substring(0, 1),
                    B = e.value.substring(1, 2),
                    n = e.value.substring(2, 3);
                return He(parseInt(r + r, 16), parseInt(B + B, 16), parseInt(n + n, 16), 1)
            }
            if (4 === e.value.length) {
                r = e.value.substring(0, 1), B = e.value.substring(1, 2), n = e.value.substring(2, 3);
                var s = e.value.substring(3, 4);
                return He(parseInt(r + r, 16), parseInt(B + B, 16), parseInt(n + n, 16), parseInt(s + s, 16) / 255)
            }
            if (6 === e.value.length) {
                r = e.value.substring(0, 2), B = e.value.substring(2, 4), n = e.value.substring(4, 6);
                return He(parseInt(r, 16), parseInt(B, 16), parseInt(n, 16), 1)
            }
            if (8 === e.value.length) {
                r = e.value.substring(0, 2), B = e.value.substring(2, 4), n = e.value.substring(4, 6), s = e.value.substring(6, 8);
                return He(parseInt(r, 16), parseInt(B, 16), parseInt(n, 16), parseInt(s, 16) / 255)
            }
        }
        if (20 === e.type) {
            var o = Le[e.value.toUpperCase()];
            if (void 0 !== o) return o
        }
        return Le.TRANSPARENT
    },
    de = function(A) {
        return !(255 & A)
    },
    fe = function(A) {
        var e = 255 & A,
            t = 255 & A >> 8,
            r = 255 & A >> 16,
            B = 255 & A >> 24;
        return e < 255 ? "rgba(" + B + "," + r + "," + t + "," + e / 255 + ")" : "rgb(" + B + "," + r + "," + t + ")"
    },
    He = function(A, e, t, r) {
        return (A << 24 | e << 16 | t << 8 | Math.round(255 * r)) >>> 0
    },
    pe = function(A, e) {
        if (17 === A.type) return A.number;
        if (16 === A.type) {
            var t = 3 === e ? 1 : 255;
            return 3 === e ? A.number / 100 * t : Math.round(A.number / 100 * t)
        }
        return 0
    },
    Ee = function(A, e) {
        var t = e.filter(te);
        if (3 === t.length) {
            var r = t.map(pe),
                B = r[0],
                n = r[1],
                s = r[2];
            return He(B, n, s, 1)
        }
        if (4 === t.length) {
            var o = t.map(pe),
                i = (B = o[0], n = o[1], s = o[2], o[3]);
            return He(B, n, s, i)
        }
        return 0
    };

function Ie(A, e, t) {
    return t < 0 && (t += 1), t >= 1 && (t -= 1), t < 1 / 6 ? (e - A) * t * 6 + A : t < .5 ? e : t < 2 / 3 ? 6 * (e - A) * (2 / 3 - t) + A : A
}
var ye = function(A, e) {
        var t = e.filter(te),
            r = t[0],
            B = t[1],
            n = t[2],
            s = t[3],
            o = (17 === r.type ? Fe(r.number) : Ue(A, r)) / (2 * Math.PI),
            i = se(B) ? B.number / 100 : 0,
            Q = se(n) ? n.number / 100 : 0,
            c = void 0 !== s && se(s) ? we(s, 1) : 1;
        if (0 === i) return He(255 * Q, 255 * Q, 255 * Q, 1);
        var a = Q <= .5 ? Q * (i + 1) : Q + i - Q * i,
            w = 2 * Q - a,
            g = Ie(w, a, o + 1 / 3),
            u = Ie(w, a, o),
            U = Ie(w, a, o - 1 / 3);
        return He(255 * g, 255 * u, 255 * U, c)
    },
    Ke = {
        hsl: ye,
        hsla: ye,
        rgb: Ee,
        rgba: Ee
    },
    me = function(A, e) {
        return he(A, _A.create(e).parseComponentValue())
    },
    Le = {
        ALICEBLUE: 4042850303,
        ANTIQUEWHITE: 4209760255,
        AQUA: 16777215,
        AQUAMARINE: 2147472639,
        AZURE: 4043309055,
        BEIGE: 4126530815,
        BISQUE: 4293182719,
        BLACK: 255,
        BLANCHEDALMOND: 4293643775,
        BLUE: 65535,
        BLUEVIOLET: 2318131967,
        BROWN: 2771004159,
        BURLYWOOD: 3736635391,
        CADETBLUE: 1604231423,
        CHARTREUSE: 2147418367,
        CHOCOLATE: 3530104575,
        CORAL: 4286533887,
        CORNFLOWERBLUE: 1687547391,
        CORNSILK: 4294499583,
        CRIMSON: 3692313855,
        CYAN: 16777215,
        DARKBLUE: 35839,
        DARKCYAN: 9145343,
        DARKGOLDENROD: 3095837695,
        DARKGRAY: 2846468607,
        DARKGREEN: 6553855,
        DARKGREY: 2846468607,
        DARKKHAKI: 3182914559,
        DARKMAGENTA: 2332068863,
        DARKOLIVEGREEN: 1433087999,
        DARKORANGE: 4287365375,
        DARKORCHID: 2570243327,
        DARKRED: 2332033279,
        DARKSALMON: 3918953215,
        DARKSEAGREEN: 2411499519,
        DARKSLATEBLUE: 1211993087,
        DARKSLATEGRAY: 793726975,
        DARKSLATEGREY: 793726975,
        DARKTURQUOISE: 13554175,
        DARKVIOLET: 2483082239,
        DEEPPINK: 4279538687,
        DEEPSKYBLUE: 12582911,
        DIMGRAY: 1768516095,
        DIMGREY: 1768516095,
        DODGERBLUE: 512819199,
        FIREBRICK: 2988581631,
        FLORALWHITE: 4294635775,
        FORESTGREEN: 579543807,
        FUCHSIA: 4278255615,
        GAINSBORO: 3705462015,
        GHOSTWHITE: 4177068031,
        GOLD: 4292280575,
        GOLDENROD: 3668254975,
        GRAY: 2155905279,
        GREEN: 8388863,
        GREENYELLOW: 2919182335,
        GREY: 2155905279,
        HONEYDEW: 4043305215,
        HOTPINK: 4285117695,
        INDIANRED: 3445382399,
        INDIGO: 1258324735,
        IVORY: 4294963455,
        KHAKI: 4041641215,
        LAVENDER: 3873897215,
        LAVENDERBLUSH: 4293981695,
        LAWNGREEN: 2096890111,
        LEMONCHIFFON: 4294626815,
        LIGHTBLUE: 2916673279,
        LIGHTCORAL: 4034953471,
        LIGHTCYAN: 3774873599,
        LIGHTGOLDENRODYELLOW: 4210742015,
        LIGHTGRAY: 3553874943,
        LIGHTGREEN: 2431553791,
        LIGHTGREY: 3553874943,
        LIGHTPINK: 4290167295,
        LIGHTSALMON: 4288707327,
        LIGHTSEAGREEN: 548580095,
        LIGHTSKYBLUE: 2278488831,
        LIGHTSLATEGRAY: 2005441023,
        LIGHTSLATEGREY: 2005441023,
        LIGHTSTEELBLUE: 2965692159,
        LIGHTYELLOW: 4294959359,
        LIME: 16711935,
        LIMEGREEN: 852308735,
        LINEN: 4210091775,
        MAGENTA: 4278255615,
        MAROON: 2147483903,
        MEDIUMAQUAMARINE: 1724754687,
        MEDIUMBLUE: 52735,
        MEDIUMORCHID: 3126187007,
        MEDIUMPURPLE: 2473647103,
        MEDIUMSEAGREEN: 1018393087,
        MEDIUMSLATEBLUE: 2070474495,
        MEDIUMSPRINGGREEN: 16423679,
        MEDIUMTURQUOISE: 1221709055,
        MEDIUMVIOLETRED: 3340076543,
        MIDNIGHTBLUE: 421097727,
        MINTCREAM: 4127193855,
        MISTYROSE: 4293190143,
        MOCCASIN: 4293178879,
        NAVAJOWHITE: 4292783615,
        NAVY: 33023,
        OLDLACE: 4260751103,
        OLIVE: 2155872511,
        OLIVEDRAB: 1804477439,
        ORANGE: 4289003775,
        ORANGERED: 4282712319,
        ORCHID: 3664828159,
        PALEGOLDENROD: 4008225535,
        PALEGREEN: 2566625535,
        PALETURQUOISE: 2951671551,
        PALEVIOLETRED: 3681588223,
        PAPAYAWHIP: 4293907967,
        PEACHPUFF: 4292524543,
        PERU: 3448061951,
        PINK: 4290825215,
        PLUM: 3718307327,
        POWDERBLUE: 2967529215,
        PURPLE: 2147516671,
        REBECCAPURPLE: 1714657791,
        RED: 4278190335,
        ROSYBROWN: 3163525119,
        ROYALBLUE: 1097458175,
        SADDLEBROWN: 2336560127,
        SALMON: 4202722047,
        SANDYBROWN: 4104413439,
        SEAGREEN: 780883967,
        SEASHELL: 4294307583,
        SIENNA: 2689740287,
        SILVER: 3233857791,
        SKYBLUE: 2278484991,
        SLATEBLUE: 1784335871,
        SLATEGRAY: 1887473919,
        SLATEGREY: 1887473919,
        SNOW: 4294638335,
        SPRINGGREEN: 16744447,
        STEELBLUE: 1182971135,
        TAN: 3535047935,
        TEAL: 8421631,
        THISTLE: 3636451583,
        TOMATO: 4284696575,
        TRANSPARENT: 0,
        TURQUOISE: 1088475391,
        VIOLET: 4001558271,
        WHEAT: 4125012991,
        WHITE: 4294967295,
        WHITESMOKE: 4126537215,
        YELLOW: 4294902015,
        YELLOWGREEN: 2597139199
    },
    be = {
        name: "background-clip",
        initialValue: "border-box",
        prefix: !1,
        type: 1,
        parse: function(A, e) {
            return e.map((function(A) {
                if (zA(A)) switch (A.value) {
                    case "padding-box":
                        return 1;
                    case "content-box":
                        return 2
                }
                return 0
            }))
        }
    },
    ve = {
        name: "background-color",
        initialValue: "transparent",
        prefix: !1,
        type: 3,
        format: "color"
    },
    De = function(A, e) {
        var t = he(A, e[0]),
            r = e[1];
        return r && se(r) ? {
            color: t,
            stop: r
        } : {
            color: t,
            stop: null
        }
    },
    xe = function(A, e) {
        var t = A[0],
            r = A[A.length - 1];
        null === t.stop && (t.stop = ie), null === r.stop && (r.stop = ce);
        for (var B = [], n = 0, s = 0; s < A.length; s++) {
            var o = A[s].stop;
            if (null !== o) {
                var i = we(o, e);
                i > n ? B.push(i) : B.push(n), n = i
            } else B.push(null)
        }
        var Q = null;
        for (s = 0; s < B.length; s++) {
            var c = B[s];
            if (null === c) null === Q && (Q = s);
            else if (null !== Q) {
                for (var a = s - Q, w = (c - B[Q - 1]) / (a + 1), g = 1; g <= a; g++) B[Q + g - 1] = w * g;
                Q = null
            }
        }
        return A.map((function(A, t) {
            return {
                color: A.color,
                stop: Math.max(Math.min(1, B[t] / e), 0)
            }
        }))
    },
    Me = function(A, e, t) {
        var r = "number" == typeof A ? A : function(A, e, t) {
                var r = e / 2,
                    B = t / 2,
                    n = we(A[0], e) - r,
                    s = B - we(A[1], t);
                return (Math.atan2(s, n) + 2 * Math.PI) % (2 * Math.PI)
            }(A, e, t),
            B = Math.abs(e * Math.sin(r)) + Math.abs(t * Math.cos(r)),
            n = e / 2,
            s = t / 2,
            o = B / 2,
            i = Math.sin(r - Math.PI / 2) * o,
            Q = Math.cos(r - Math.PI / 2) * o;
        return [B, n - Q, n + Q, s - i, s + i]
    },
    Se = function(A, e) {
        return Math.sqrt(A * A + e * e)
    },
    Te = function(A, e, t, r, B) {
        return [
            [0, 0],
            [0, e],
            [A, 0],
            [A, e]
        ].reduce((function(A, e) {
            var n = e[0],
                s = e[1],
                o = Se(t - n, r - s);
            return (B ? o < A.optimumDistance : o > A.optimumDistance) ? {
                optimumCorner: e,
                optimumDistance: o
            } : A
        }), {
            optimumDistance: B ? 1 / 0 : -1 / 0,
            optimumCorner: null
        }).optimumCorner
    },
    Ge = function(A, e) {
        var t = Fe(180),
            r = [];
        return re(e).forEach((function(e, B) {
            if (0 === B) {
                var n = e[0];
                if (20 === n.type && -1 !== ["top", "left", "right", "bottom"].indexOf(n.value)) return void(t = le(e));
                if (Ce(n)) return void(t = (Ue(A, n) + Fe(270)) % Fe(360))
            }
            var s = De(A, e);
            r.push(s)
        })), {
            angle: t,
            stops: r,
            type: 1
        }
    },
    Oe = "closest-side",
    Ve = "farthest-side",
    ke = "closest-corner",
    Re = "farthest-corner",
    Ne = "circle",
    Pe = "ellipse",
    Xe = "cover",
    Je = "contain",
    Ye = function(A, e) {
        var t = 0,
            r = 3,
            B = [],
            n = [];
        return re(e).forEach((function(e, s) {
            var o = !0;
            if (0 === s ? o = e.reduce((function(A, e) {
                    if (zA(e)) switch (e.value) {
                        case "center":
                            return n.push(Qe), !1;
                        case "top":
                        case "left":
                            return n.push(ie), !1;
                        case "right":
                        case "bottom":
                            return n.push(ce), !1
                    } else if (se(e) || ne(e)) return n.push(e), !1;
                    return A
                }), o) : 1 === s && (o = e.reduce((function(A, e) {
                    if (zA(e)) switch (e.value) {
                        case Ne:
                            return t = 0, !1;
                        case Pe:
                            return t = 1, !1;
                        case Je:
                        case Oe:
                            return r = 0, !1;
                        case Ve:
                            return r = 1, !1;
                        case ke:
                            return r = 2, !1;
                        case Xe:
                        case Re:
                            return r = 3, !1
                    } else if (ne(e) || se(e)) return Array.isArray(r) || (r = []), r.push(e), !1;
                    return A
                }), o)), o) {
                var i = De(A, e);
                B.push(i)
            }
        })), {
            size: r,
            shape: t,
            stops: B,
            position: n,
            type: 2
        }
    },
    We = function(A, e) {
        if (22 === e.type) {
            var t = {
                url: e.value,
                type: 0
            };
            return A.cache.addImage(e.value), t
        }
        if (18 === e.type) {
            var r = qe[e.name];
            if (void 0 === r) throw new Error('Attempting to parse an unsupported image function "' + e.name + '"');
            return r(A, e.values)
        }
        throw new Error("Unsupported image type " + e.type)
    };
var Ze, _e, qe = {
        "linear-gradient": function(A, e) {
            var t = Fe(180),
                r = [];
            return re(e).forEach((function(e, B) {
                if (0 === B) {
                    var n = e[0];
                    if (20 === n.type && "to" === n.value) return void(t = le(e));
                    if (Ce(n)) return void(t = Ue(A, n))
                }
                var s = De(A, e);
                r.push(s)
            })), {
                angle: t,
                stops: r,
                type: 1
            }
        },
        "-moz-linear-gradient": Ge,
        "-ms-linear-gradient": Ge,
        "-o-linear-gradient": Ge,
        "-webkit-linear-gradient": Ge,
        "radial-gradient": function(A, e) {
            var t = 0,
                r = 3,
                B = [],
                n = [];
            return re(e).forEach((function(e, s) {
                var o = !0;
                if (0 === s) {
                    var i = !1;
                    o = e.reduce((function(A, e) {
                        if (i)
                            if (zA(e)) switch (e.value) {
                                case "center":
                                    return n.push(Qe), A;
                                case "top":
                                case "left":
                                    return n.push(ie), A;
                                case "right":
                                case "bottom":
                                    return n.push(ce), A
                            } else(se(e) || ne(e)) && n.push(e);
                            else if (zA(e)) switch (e.value) {
                            case Ne:
                                return t = 0, !1;
                            case Pe:
                                return t = 1, !1;
                            case "at":
                                return i = !0, !1;
                            case Oe:
                                return r = 0, !1;
                            case Xe:
                            case Ve:
                                return r = 1, !1;
                            case Je:
                            case ke:
                                return r = 2, !1;
                            case Re:
                                return r = 3, !1
                        } else if (ne(e) || se(e)) return Array.isArray(r) || (r = []), r.push(e), !1;
                        return A
                    }), o)
                }
                if (o) {
                    var Q = De(A, e);
                    B.push(Q)
                }
            })), {
                size: r,
                shape: t,
                stops: B,
                position: n,
                type: 2
            }
        },
        "-moz-radial-gradient": Ye,
        "-ms-radial-gradient": Ye,
        "-o-radial-gradient": Ye,
        "-webkit-radial-gradient": Ye,
        "-webkit-gradient": function(A, e) {
            var t = Fe(180),
                r = [],
                B = 1;
            return re(e).forEach((function(e, t) {
                var n = e[0];
                if (0 === t) {
                    if (zA(n) && "linear" === n.value) return void(B = 1);
                    if (zA(n) && "radial" === n.value) return void(B = 2)
                }
                if (18 === n.type)
                    if ("from" === n.name) {
                        var s = he(A, n.values[0]);
                        r.push({
                            stop: ie,
                            color: s
                        })
                    } else if ("to" === n.name) {
                    s = he(A, n.values[0]);
                    r.push({
                        stop: ce,
                        color: s
                    })
                } else if ("color-stop" === n.name) {
                    var o = n.values.filter(te);
                    if (2 === o.length) {
                        s = he(A, o[1]);
                        var i = o[0];
                        jA(i) && r.push({
                            stop: {
                                type: 16,
                                number: 100 * i.number,
                                flags: i.flags
                            },
                            color: s
                        })
                    }
                }
            })), 1 === B ? {
                angle: (t + Fe(180)) % Fe(360),
                stops: r,
                type: B
            } : {
                size: 3,
                shape: 0,
                stops: r,
                position: [],
                type: B
            }
        }
    },
    je = {
        name: "background-image",
        initialValue: "none",
        type: 1,
        prefix: !1,
        parse: function(A, e) {
            if (0 === e.length) return [];
            var t = e[0];
            return 20 === t.type && "none" === t.value ? [] : e.filter((function(A) {
                return te(A) && function(A) {
                    return !(20 === A.type && "none" === A.value || 18 === A.type && !qe[A.name])
                }(A)
            })).map((function(e) {
                return We(A, e)
            }))
        }
    },
    ze = {
        name: "background-origin",
        initialValue: "border-box",
        prefix: !1,
        type: 1,
        parse: function(A, e) {
            return e.map((function(A) {
                if (zA(A)) switch (A.value) {
                    case "padding-box":
                        return 1;
                    case "content-box":
                        return 2
                }
                return 0
            }))
        }
    },
    $e = {
        name: "background-position",
        initialValue: "0% 0%",
        type: 1,
        prefix: !1,
        parse: function(A, e) {
            return re(e).map((function(A) {
                return A.filter(se)
            })).map(oe)
        }
    },
    At = {
        name: "background-repeat",
        initialValue: "repeat",
        prefix: !1,
        type: 1,
        parse: function(A, e) {
            return re(e).map((function(A) {
                return A.filter(zA).map((function(A) {
                    return A.value
                })).join(" ")
            })).map(et)
        }
    },
    et = function(A) {
        switch (A) {
            case "no-repeat":
                return 1;
            case "repeat-x":
            case "repeat no-repeat":
                return 2;
            case "repeat-y":
            case "no-repeat repeat":
                return 3;
            default:
                return 0
        }
    };
(_e = Ze || (Ze = {})).AUTO = "auto", _e.CONTAIN = "contain", _e.COVER = "cover";
var tt, rt, Bt = {
        name: "background-size",
        initialValue: "0",
        prefix: !1,
        type: 1,
        parse: function(A, e) {
            return re(e).map((function(A) {
                return A.filter(nt)
            }))
        }
    },
    nt = function(A) {
        return zA(A) || se(A)
    },
    st = function(A) {
        return {
            name: "border-" + A + "-color",
            initialValue: "transparent",
            prefix: !1,
            type: 3,
            format: "color"
        }
    },
    ot = st("top"),
    it = st("right"),
    Qt = st("bottom"),
    ct = st("left"),
    at = function(A) {
        return {
            name: "border-radius-" + A,
            initialValue: "0 0",
            prefix: !1,
            type: 1,
            parse: function(A, e) {
                return oe(e.filter(se))
            }
        }
    },
    wt = at("top-left"),
    gt = at("top-right"),
    ut = at("bottom-right"),
    Ut = at("bottom-left"),
    Ct = function(A) {
        return {
            name: "border-" + A + "-style",
            initialValue: "solid",
            prefix: !1,
            type: 2,
            parse: function(A, e) {
                switch (e) {
                    case "none":
                        return 0;
                    case "dashed":
                        return 2;
                    case "dotted":
                        return 3;
                    case "double":
                        return 4
                }
                return 1
            }
        }
    },
    lt = Ct("top"),
    Ft = Ct("right"),
    ht = Ct("bottom"),
    dt = Ct("left"),
    ft = function(A) {
        return {
            name: "border-" + A + "-width",
            initialValue: "0",
            type: 0,
            prefix: !1,
            parse: function(A, e) {
                return qA(e) ? e.number : 0
            }
        }
    },
    Ht = ft("top"),
    pt = ft("right"),
    Et = ft("bottom"),
    It = ft("left"),
    yt = {
        name: "color",
        initialValue: "transparent",
        prefix: !1,
        type: 3,
        format: "color"
    },
    Kt = {
        name: "direction",
        initialValue: "ltr",
        prefix: !1,
        type: 2,
        parse: function(A, e) {
            return "rtl" === e ? 1 : 0
        }
    },
    mt = {
        name: "display",
        initialValue: "inline-block",
        prefix: !1,
        type: 1,
        parse: function(A, e) {
            return e.filter(zA).reduce((function(A, e) {
                return A | Lt(e.value)
            }), 0)
        }
    },
    Lt = function(A) {
        switch (A) {
            case "block":
            case "-webkit-box":
                return 2;
            case "inline":
                return 4;
            case "run-in":
                return 8;
            case "flow":
                return 16;
            case "flow-root":
                return 32;
            case "table":
                return 64;
            case "flex":
            case "-webkit-flex":
                return 128;
            case "grid":
            case "-ms-grid":
                return 256;
            case "ruby":
                return 512;
            case "subgrid":
                return 1024;
            case "list-item":
                return 2048;
            case "table-row-group":
                return 4096;
            case "table-header-group":
                return 8192;
            case "table-footer-group":
                return 16384;
            case "table-row":
                return 32768;
            case "table-cell":
                return 65536;
            case "table-column-group":
                return 131072;
            case "table-column":
                return 262144;
            case "table-caption":
                return 524288;
            case "ruby-base":
                return 1048576;
            case "ruby-text":
                return 2097152;
            case "ruby-base-container":
                return 4194304;
            case "ruby-text-container":
                return 8388608;
            case "contents":
                return 16777216;
            case "inline-block":
                return 33554432;
            case "inline-list-item":
                return 67108864;
            case "inline-table":
                return 134217728;
            case "inline-flex":
                return 268435456;
            case "inline-grid":
                return 536870912
        }
        return 0
    },
    bt = {
        name: "float",
        initialValue: "none",
        prefix: !1,
        type: 2,
        parse: function(A, e) {
            switch (e) {
                case "left":
                    return 1;
                case "right":
                    return 2;
                case "inline-start":
                    return 3;
                case "inline-end":
                    return 4
            }
            return 0
        }
    },
    vt = {
        name: "letter-spacing",
        initialValue: "0",
        prefix: !1,
        type: 0,
        parse: function(A, e) {
            return 20 === e.type && "normal" === e.value ? 0 : 17 === e.type || 15 === e.type ? e.number : 0
        }
    };
(rt = tt || (tt = {})).NORMAL = "normal", rt.STRICT = "strict";
var Dt, xt, Mt = {
        name: "line-break",
        initialValue: "normal",
        prefix: !1,
        type: 2,
        parse: function(A, e) {
            return "strict" === e ? tt.STRICT : tt.NORMAL
        }
    },
    St = {
        name: "line-height",
        initialValue: "normal",
        prefix: !1,
        type: 4
    },
    Tt = function(A, e) {
        return zA(A) && "normal" === A.value ? 1.2 * e : 17 === A.type ? e * A.number : se(A) ? we(A, e) : e
    },
    Gt = {
        name: "list-style-image",
        initialValue: "none",
        type: 0,
        prefix: !1,
        parse: function(A, e) {
            return 20 === e.type && "none" === e.value ? null : We(A, e)
        }
    },
    Ot = {
        name: "list-style-position",
        initialValue: "outside",
        prefix: !1,
        type: 2,
        parse: function(A, e) {
            return "inside" === e ? 0 : 1
        }
    },
    Vt = {
        name: "list-style-type",
        initialValue: "none",
        prefix: !1,
        type: 2,
        parse: function(A, e) {
            switch (e) {
                case "disc":
                    return 0;
                case "circle":
                    return 1;
                case "square":
                    return 2;
                case "decimal":
                    return 3;
                case "cjk-decimal":
                    return 4;
                case "decimal-leading-zero":
                    return 5;
                case "lower-roman":
                    return 6;
                case "upper-roman":
                    return 7;
                case "lower-greek":
                    return 8;
                case "lower-alpha":
                    return 9;
                case "upper-alpha":
                    return 10;
                case "arabic-indic":
                    return 11;
                case "armenian":
                    return 12;
                case "bengali":
                    return 13;
                case "cambodian":
                    return 14;
                case "cjk-earthly-branch":
                    return 15;
                case "cjk-heavenly-stem":
                    return 16;
                case "cjk-ideographic":
                    return 17;
                case "devanagari":
                    return 18;
                case "ethiopic-numeric":
                    return 19;
                case "georgian":
                    return 20;
                case "gujarati":
                    return 21;
                case "gurmukhi":
                case "hebrew":
                    return 22;
                case "hiragana":
                    return 23;
                case "hiragana-iroha":
                    return 24;
                case "japanese-formal":
                    return 25;
                case "japanese-informal":
                    return 26;
                case "kannada":
                    return 27;
                case "katakana":
                    return 28;
                case "katakana-iroha":
                    return 29;
                case "khmer":
                    return 30;
                case "korean-hangul-formal":
                    return 31;
                case "korean-hanja-formal":
                    return 32;
                case "korean-hanja-informal":
                    return 33;
                case "lao":
                    return 34;
                case "lower-armenian":
                    return 35;
                case "malayalam":
                    return 36;
                case "mongolian":
                    return 37;
                case "myanmar":
                    return 38;
                case "oriya":
                    return 39;
                case "persian":
                    return 40;
                case "simp-chinese-formal":
                    return 41;
                case "simp-chinese-informal":
                    return 42;
                case "tamil":
                    return 43;
                case "telugu":
                    return 44;
                case "thai":
                    return 45;
                case "tibetan":
                    return 46;
                case "trad-chinese-formal":
                    return 47;
                case "trad-chinese-informal":
                    return 48;
                case "upper-armenian":
                    return 49;
                case "disclosure-open":
                    return 50;
                case "disclosure-closed":
                    return 51;
                default:
                    return -1
            }
        }
    },
    kt = function(A) {
        return {
            name: "margin-" + A,
            initialValue: "0",
            prefix: !1,
            type: 4
        }
    },
    Rt = kt("top"),
    Nt = kt("right"),
    Pt = kt("bottom"),
    Xt = kt("left"),
    Jt = {
        name: "overflow",
        initialValue: "visible",
        prefix: !1,
        type: 1,
        parse: function(A, e) {
            return e.filter(zA).map((function(A) {
                switch (A.value) {
                    case "hidden":
                        return 1;
                    case "scroll":
                        return 2;
                    case "clip":
                        return 3;
                    case "auto":
                        return 4;
                    default:
                        return 0
                }
            }))
        }
    },
    Yt = {
        name: "overflow-wrap",
        initialValue: "normal",
        prefix: !1,
        type: 2,
        parse: function(A, e) {
            return "break-word" === e ? "break-word" : "normal"
        }
    },
    Wt = function(A) {
        return {
            name: "padding-" + A,
            initialValue: "0",
            prefix: !1,
            type: 3,
            format: "length-percentage"
        }
    },
    Zt = Wt("top"),
    _t = Wt("right"),
    qt = Wt("bottom"),
    jt = Wt("left"),
    zt = {
        name: "text-align",
        initialValue: "left",
        prefix: !1,
        type: 2,
        parse: function(A, e) {
            switch (e) {
                case "right":
                    return 2;
                case "center":
                case "justify":
                    return 1;
                default:
                    return 0
            }
        }
    },
    $t = {
        name: "position",
        initialValue: "static",
        prefix: !1,
        type: 2,
        parse: function(A, e) {
            switch (e) {
                case "relative":
                    return 1;
                case "absolute":
                    return 2;
                case "fixed":
                    return 3;
                case "sticky":
                    return 4
            }
            return 0
        }
    },
    Ar = {
        name: "text-shadow",
        initialValue: "none",
        type: 1,
        prefix: !1,
        parse: function(A, e) {
            return 1 === e.length && Ae(e[0], "none") ? [] : re(e).map((function(e) {
                for (var t = {
                        color: Le.TRANSPARENT,
                        offsetX: ie,
                        offsetY: ie,
                        blur: ie
                    }, r = 0, B = 0; B < e.length; B++) {
                    var n = e[B];
                    ne(n) ? (0 === r ? t.offsetX = n : 1 === r ? t.offsetY = n : t.blur = n, r++) : t.color = he(A, n)
                }
                return t
            }))
        }
    },
    er = {
        name: "text-transform",
        initialValue: "none",
        prefix: !1,
        type: 2,
        parse: function(A, e) {
            switch (e) {
                case "uppercase":
                    return 2;
                case "lowercase":
                    return 1;
                case "capitalize":
                    return 3
            }
            return 0
        }
    },
    tr = {
        name: "transform",
        initialValue: "none",
        prefix: !0,
        type: 0,
        parse: function(A, e) {
            if (20 === e.type && "none" === e.value) return null;
            if (18 === e.type) {
                var t = rr[e.name];
                if (void 0 === t) throw new Error('Attempting to parse an unsupported transform function "' + e.name + '"');
                return t(e.values)
            }
            return null
        }
    },
    rr = {
        matrix: function(A) {
            var e = A.filter((function(A) {
                return 17 === A.type
            })).map((function(A) {
                return A.number
            }));
            return 6 === e.length ? e : null
        },
        matrix3d: function(A) {
            var e = A.filter((function(A) {
                    return 17 === A.type
                })).map((function(A) {
                    return A.number
                })),
                t = e[0],
                r = e[1];
            e[2], e[3];
            var B = e[4],
                n = e[5];
            e[6], e[7], e[8], e[9], e[10], e[11];
            var s = e[12],
                o = e[13];
            return e[14], e[15], 16 === e.length ? [t, r, B, n, s, o] : null
        }
    },
    Br = {
        type: 16,
        number: 50,
        flags: 4
    },
    nr = [Br, Br],
    sr = {
        name: "transform-origin",
        initialValue: "50% 50%",
        prefix: !0,
        type: 1,
        parse: function(A, e) {
            var t = e.filter(se);
            return 2 !== t.length ? nr : [t[0], t[1]]
        }
    },
    or = {
        name: "visible",
        initialValue: "none",
        prefix: !1,
        type: 2,
        parse: function(A, e) {
            switch (e) {
                case "hidden":
                    return 1;
                case "collapse":
                    return 2;
                default:
                    return 0
            }
        }
    };
(xt = Dt || (Dt = {})).NORMAL = "normal", xt.BREAK_ALL = "break-all", xt.KEEP_ALL = "keep-all";
for (var ir = {
        name: "word-break",
        initialValue: "normal",
        prefix: !1,
        type: 2,
        parse: function(A, e) {
            switch (e) {
                case "break-all":
                    return Dt.BREAK_ALL;
                case "keep-all":
                    return Dt.KEEP_ALL;
                default:
                    return Dt.NORMAL
            }
        }
    }, Qr = {
        name: "z-index",
        initialValue: "auto",
        prefix: !1,
        type: 0,
        parse: function(A, e) {
            if (20 === e.type) return {
                auto: !0,
                order: 0
            };
            if (jA(e)) return {
                auto: !1,
                order: e.number
            };
            throw new Error("Invalid z-index number parsed")
        }
    }, cr = function(A, e) {
        if (15 === e.type) switch (e.unit.toLowerCase()) {
            case "s":
                return 1e3 * e.number;
            case "ms":
                return e.number
        }
        throw new Error("Unsupported time type")
    }, ar = {
        name: "opacity",
        initialValue: "1",
        type: 0,
        prefix: !1,
        parse: function(A, e) {
            return jA(e) ? e.number : 1
        }
    }, wr = {
        name: "text-decoration-color",
        initialValue: "transparent",
        prefix: !1,
        type: 3,
        format: "color"
    }, gr = {
        name: "text-decoration-line",
        initialValue: "none",
        prefix: !1,
        type: 1,
        parse: function(A, e) {
            return e.filter(zA).map((function(A) {
                switch (A.value) {
                    case "underline":
                        return 1;
                    case "overline":
                        return 2;
                    case "line-through":
                        return 3;
                    case "none":
                        return 4
                }
                return 0
            })).filter((function(A) {
                return 0 !== A
            }))
        }
    }, ur = {
        name: "font-family",
        initialValue: "",
        prefix: !1,
        type: 1,
        parse: function(A, e) {
            var t = [],
                r = [];
            return e.forEach((function(A) {
                switch (A.type) {
                    case 20:
                    case 0:
                        t.push(A.value);
                        break;
                    case 17:
                        t.push(A.number.toString());
                        break;
                    case 4:
                        r.push(t.join(" ")), t.length = 0
                }
            })), t.length && r.push(t.join(" ")), r.map((function(A) {
                return -1 === A.indexOf(" ") ? A : "'" + A + "'"
            }))
        }
    }, Ur = {
        name: "font-size",
        initialValue: "0",
        prefix: !1,
        type: 3,
        format: "length"
    }, Cr = {
        name: "font-weight",
        initialValue: "normal",
        type: 0,
        prefix: !1,
        parse: function(A, e) {
            return jA(e) ? e.number : zA(e) && "bold" === e.value ? 700 : 400
        }
    }, lr = {
        name: "font-variant",
        initialValue: "none",
        type: 1,
        prefix: !1,
        parse: function(A, e) {
            return e.filter(zA).map((function(A) {
                return A.value
            }))
        }
    }, Fr = {
        name: "font-style",
        initialValue: "normal",
        prefix: !1,
        type: 2,
        parse: function(A, e) {
            switch (e) {
                case "oblique":
                    return "oblique";
                case "italic":
                    return "italic";
                default:
                    return "normal"
            }
        }
    }, hr = function(A, e) {
        return !!(A & e)
    }, dr = {
        name: "content",
        initialValue: "none",
        type: 1,
        prefix: !1,
        parse: function(A, e) {
            if (0 === e.length) return [];
            var t = e[0];
            return 20 === t.type && "none" === t.value ? [] : e
        }
    }, fr = {
        name: "counter-increment",
        initialValue: "none",
        prefix: !0,
        type: 1,
        parse: function(A, e) {
            if (0 === e.length) return null;
            var t = e[0];
            if (20 === t.type && "none" === t.value) return null;
            for (var r = [], B = e.filter(ee), n = 0; n < B.length; n++) {
                var s = B[n],
                    o = B[n + 1];
                if (20 === s.type) {
                    var i = o && jA(o) ? o.number : 1;
                    r.push({
                        counter: s.value,
                        increment: i
                    })
                }
            }
            return r
        }
    }, Hr = {
        name: "counter-reset",
        initialValue: "none",
        prefix: !0,
        type: 1,
        parse: function(A, e) {
            if (0 === e.length) return [];
            for (var t = [], r = e.filter(ee), B = 0; B < r.length; B++) {
                var n = r[B],
                    s = r[B + 1];
                if (zA(n) && "none" !== n.value) {
                    var o = s && jA(s) ? s.number : 0;
                    t.push({
                        counter: n.value,
                        reset: o
                    })
                }
            }
            return t
        }
    }, pr = {
        name: "duration",
        initialValue: "0s",
        prefix: !1,
        type: 1,
        parse: function(A, e) {
            return e.filter(qA).map((function(e) {
                return cr(A, e)
            }))
        }
    }, Er = {
        name: "quotes",
        initialValue: "none",
        prefix: !0,
        type: 1,
        parse: function(A, e) {
            if (0 === e.length) return null;
            var t = e[0];
            if (20 === t.type && "none" === t.value) return null;
            var r = [],
                B = e.filter($A);
            if (B.length % 2 != 0) return null;
            for (var n = 0; n < B.length; n += 2) {
                var s = B[n].value,
                    o = B[n + 1].value;
                r.push({
                    open: s,
                    close: o
                })
            }
            return r
        }
    }, Ir = function(A, e, t) {
        if (!A) return "";
        var r = A[Math.min(e, A.length - 1)];
        return r ? t ? r.open : r.close : ""
    }, yr = {
        name: "box-shadow",
        initialValue: "none",
        type: 1,
        prefix: !1,
        parse: function(A, e) {
            return 1 === e.length && Ae(e[0], "none") ? [] : re(e).map((function(e) {
                for (var t = {
                        color: 255,
                        offsetX: ie,
                        offsetY: ie,
                        blur: ie,
                        spread: ie,
                        inset: !1
                    }, r = 0, B = 0; B < e.length; B++) {
                    var n = e[B];
                    Ae(n, "inset") ? t.inset = !0 : ne(n) ? (0 === r ? t.offsetX = n : 1 === r ? t.offsetY = n : 2 === r ? t.blur = n : t.spread = n, r++) : t.color = he(A, n)
                }
                return t
            }))
        }
    }, Kr = {
        name: "paint-order",
        initialValue: "normal",
        prefix: !1,
        type: 1,
        parse: function(A, e) {
            var t = [];
            return e.filter(zA).forEach((function(A) {
                switch (A.value) {
                    case "stroke":
                        t.push(1);
                        break;
                    case "fill":
                        t.push(0);
                        break;
                    case "markers":
                        t.push(2)
                }
            })), [0, 1, 2].forEach((function(A) {
                -1 === t.indexOf(A) && t.push(A)
            })), t
        }
    }, mr = {
        name: "-webkit-text-stroke-color",
        initialValue: "currentcolor",
        prefix: !1,
        type: 3,
        format: "color"
    }, Lr = {
        name: "-webkit-text-stroke-width",
        initialValue: "0",
        type: 0,
        prefix: !1,
        parse: function(A, e) {
            return qA(e) ? e.number : 0
        }
    }, br = function() {
        function A(A, e) {
            var t, r;
            this.animationDuration = xr(A, pr, e.animationDuration), this.backgroundClip = xr(A, be, e.backgroundClip), this.backgroundColor = xr(A, ve, e.backgroundColor), this.backgroundImage = xr(A, je, e.backgroundImage), this.backgroundOrigin = xr(A, ze, e.backgroundOrigin), this.backgroundPosition = xr(A, $e, e.backgroundPosition), this.backgroundRepeat = xr(A, At, e.backgroundRepeat), this.backgroundSize = xr(A, Bt, e.backgroundSize), this.borderTopColor = xr(A, ot, e.borderTopColor), this.borderRightColor = xr(A, it, e.borderRightColor), this.borderBottomColor = xr(A, Qt, e.borderBottomColor), this.borderLeftColor = xr(A, ct, e.borderLeftColor), this.borderTopLeftRadius = xr(A, wt, e.borderTopLeftRadius), this.borderTopRightRadius = xr(A, gt, e.borderTopRightRadius), this.borderBottomRightRadius = xr(A, ut, e.borderBottomRightRadius), this.borderBottomLeftRadius = xr(A, Ut, e.borderBottomLeftRadius), this.borderTopStyle = xr(A, lt, e.borderTopStyle), this.borderRightStyle = xr(A, Ft, e.borderRightStyle), this.borderBottomStyle = xr(A, ht, e.borderBottomStyle), this.borderLeftStyle = xr(A, dt, e.borderLeftStyle), this.borderTopWidth = xr(A, Ht, e.borderTopWidth), this.borderRightWidth = xr(A, pt, e.borderRightWidth), this.borderBottomWidth = xr(A, Et, e.borderBottomWidth), this.borderLeftWidth = xr(A, It, e.borderLeftWidth), this.boxShadow = xr(A, yr, e.boxShadow), this.color = xr(A, yt, e.color), this.direction = xr(A, Kt, e.direction), this.display = xr(A, mt, e.display), this.float = xr(A, bt, e.cssFloat), this.fontFamily = xr(A, ur, e.fontFamily), this.fontSize = xr(A, Ur, e.fontSize), this.fontStyle = xr(A, Fr, e.fontStyle), this.fontVariant = xr(A, lr, e.fontVariant), this.fontWeight = xr(A, Cr, e.fontWeight), this.letterSpacing = xr(A, vt, e.letterSpacing), this.lineBreak = xr(A, Mt, e.lineBreak), this.lineHeight = xr(A, St, e.lineHeight), this.listStyleImage = xr(A, Gt, e.listStyleImage), this.listStylePosition = xr(A, Ot, e.listStylePosition), this.listStyleType = xr(A, Vt, e.listStyleType), this.marginTop = xr(A, Rt, e.marginTop), this.marginRight = xr(A, Nt, e.marginRight), this.marginBottom = xr(A, Pt, e.marginBottom), this.marginLeft = xr(A, Xt, e.marginLeft), this.opacity = xr(A, ar, e.opacity);
            var B = xr(A, Jt, e.overflow);
            this.overflowX = B[0], this.overflowY = B[B.length > 1 ? 1 : 0], this.overflowWrap = xr(A, Yt, e.overflowWrap), this.paddingTop = xr(A, Zt, e.paddingTop), this.paddingRight = xr(A, _t, e.paddingRight), this.paddingBottom = xr(A, qt, e.paddingBottom), this.paddingLeft = xr(A, jt, e.paddingLeft), this.paintOrder = xr(A, Kr, e.paintOrder), this.position = xr(A, $t, e.position), this.textAlign = xr(A, zt, e.textAlign), this.textDecorationColor = xr(A, wr, null !== (t = e.textDecorationColor) && void 0 !== t ? t : e.color), this.textDecorationLine = xr(A, gr, null !== (r = e.textDecorationLine) && void 0 !== r ? r : e.textDecoration), this.textShadow = xr(A, Ar, e.textShadow), this.textTransform = xr(A, er, e.textTransform), this.transform = xr(A, tr, e.transform), this.transformOrigin = xr(A, sr, e.transformOrigin), this.visibility = xr(A, or, e.visibility), this.webkitTextStrokeColor = xr(A, mr, e.webkitTextStrokeColor), this.webkitTextStrokeWidth = xr(A, Lr, e.webkitTextStrokeWidth), this.wordBreak = xr(A, ir, e.wordBreak), this.zIndex = xr(A, Qr, e.zIndex)
        }
        return A.prototype.isVisible = function() {
            return this.display > 0 && this.opacity > 0 && 0 === this.visibility
        }, A.prototype.isTransparent = function() {
            return de(this.backgroundColor)
        }, A.prototype.isTransformed = function() {
            return null !== this.transform
        }, A.prototype.isPositioned = function() {
            return 0 !== this.position
        }, A.prototype.isPositionedWithZIndex = function() {
            return this.isPositioned() && !this.zIndex.auto
        }, A.prototype.isFloating = function() {
            return 0 !== this.float
        }, A.prototype.isInlineLevel = function() {
            return hr(this.display, 4) || hr(this.display, 33554432) || hr(this.display, 268435456) || hr(this.display, 536870912) || hr(this.display, 67108864) || hr(this.display, 134217728)
        }, A
    }(), vr = function() {
        return function(A, e) {
            this.content = xr(A, dr, e.content), this.quotes = xr(A, Er, e.quotes)
        }
    }(), Dr = function() {
        return function(A, e) {
            this.counterIncrement = xr(A, fr, e.counterIncrement), this.counterReset = xr(A, Hr, e.counterReset)
        }
    }(), xr = function(A, e, t) {
        var r = new ZA,
            B = null != t ? t.toString() : e.initialValue;
        r.write(B);
        var n = new _A(r.read());
        switch (e.type) {
            case 2:
                var s = n.parseComponentValue();
                return e.parse(A, zA(s) ? s.value : e.initialValue);
            case 0:
                return e.parse(A, n.parseComponentValue());
            case 1:
                return e.parse(A, n.parseComponentValues());
            case 4:
                return n.parseComponentValue();
            case 3:
                switch (e.format) {
                    case "angle":
                        return Ue(A, n.parseComponentValue());
                    case "color":
                        return he(A, n.parseComponentValue());
                    case "image":
                        return We(A, n.parseComponentValue());
                    case "length":
                        var o = n.parseComponentValue();
                        return ne(o) ? o : ie;
                    case "length-percentage":
                        var i = n.parseComponentValue();
                        return se(i) ? i : ie;
                    case "time":
                        return cr(A, n.parseComponentValue())
                }
        }
    }, Mr = function(A, e) {
        var t = function(A) {
            switch (A.getAttribute("data-html2canvas-debug")) {
                case "all":
                    return 1;
                case "clone":
                    return 2;
                case "parse":
                    return 3;
                case "render":
                    return 4;
                default:
                    return 0
            }
        }(A);
        return 1 === t || e === t
    }, Sr = function() {
        return function(A, e) {
            this.context = A, this.textNodes = [], this.elements = [], this.flags = 0, Mr(e, 3), this.styles = new br(A, window.getComputedStyle(e, null)), kB(e) && (this.styles.animationDuration.some((function(A) {
                return A > 0
            })) && (e.style.animationDuration = "0s"), null !== this.styles.transform && (e.style.transform = "none")), this.bounds = s(this.context, e), Mr(e, 4) && (this.flags |= 16)
        }
    }(), Tr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", Gr = "undefined" == typeof Uint8Array ? [] : new Uint8Array(256), Or = 0; Or < 64; Or++) Gr[Tr.charCodeAt(Or)] = Or;
for (var Vr = function(A, e, t) {
        return A.slice ? A.slice(e, t) : new Uint16Array(Array.prototype.slice.call(A, e, t))
    }, kr = function() {
        function A(A, e, t, r, B, n) {
            this.initialValue = A, this.errorValue = e, this.highStart = t, this.highValueIndex = r, this.index = B, this.data = n
        }
        return A.prototype.get = function(A) {
            var e;
            if (A >= 0) {
                if (A < 55296 || A > 56319 && A <= 65535) return e = ((e = this.index[A >> 5]) << 2) + (31 & A), this.data[e];
                if (A <= 65535) return e = ((e = this.index[2048 + (A - 55296 >> 5)]) << 2) + (31 & A), this.data[e];
                if (A < this.highStart) return e = 2080 + (A >> 11), e = this.index[e], e += A >> 5 & 63, e = ((e = this.index[e]) << 2) + (31 & A), this.data[e];
                if (A <= 1114111) return this.data[this.highValueIndex]
            }
            return this.errorValue
        }, A
    }(), Rr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", Nr = "undefined" == typeof Uint8Array ? [] : new Uint8Array(256), Pr = 0; Pr < 64; Pr++) Nr[Rr.charCodeAt(Pr)] = Pr;
var Xr, Jr, Yr = 8,
    Wr = 9,
    Zr = 11,
    _r = 12,
    qr = function() {
        for (var A = [], e = 0; e < arguments.length; e++) A[e] = arguments[e];
        if (String.fromCodePoint) return String.fromCodePoint.apply(String, A);
        var t = A.length;
        if (!t) return "";
        for (var r = [], B = -1, n = ""; ++B < t;) {
            var s = A[B];
            s <= 65535 ? r.push(s) : (s -= 65536, r.push(55296 + (s >> 10), s % 1024 + 56320)), (B + 1 === t || r.length > 16384) && (n += String.fromCharCode.apply(String, r), r.length = 0)
        }
        return n
    },
    jr = function(A) {
        var e = function(A) {
                var e, t, r, B, n, s = .75 * A.length,
                    o = A.length,
                    i = 0;
                "=" === A[A.length - 1] && (s--, "=" === A[A.length - 2] && s--);
                var Q = "undefined" != typeof ArrayBuffer && "undefined" != typeof Uint8Array && void 0 !== Uint8Array.prototype.slice ? new ArrayBuffer(s) : new Array(s),
                    c = Array.isArray(Q) ? Q : new Uint8Array(Q);
                for (e = 0; e < o; e += 4) t = Gr[A.charCodeAt(e)], r = Gr[A.charCodeAt(e + 1)], B = Gr[A.charCodeAt(e + 2)], n = Gr[A.charCodeAt(e + 3)], c[i++] = t << 2 | r >> 4, c[i++] = (15 & r) << 4 | B >> 2, c[i++] = (3 & B) << 6 | 63 & n;
                return Q
            }(A),
            t = Array.isArray(e) ? function(A) {
                for (var e = A.length, t = [], r = 0; r < e; r += 4) t.push(A[r + 3] << 24 | A[r + 2] << 16 | A[r + 1] << 8 | A[r]);
                return t
            }(e) : new Uint32Array(e),
            r = Array.isArray(e) ? function(A) {
                for (var e = A.length, t = [], r = 0; r < e; r += 2) t.push(A[r + 1] << 8 | A[r]);
                return t
            }(e) : new Uint16Array(e),
            B = Vr(r, 12, t[4] / 2),
            n = 2 === t[5] ? Vr(r, (24 + t[4]) / 2) : function(A, e, t) {
                return A.slice ? A.slice(e, t) : new Uint32Array(Array.prototype.slice.call(A, e, t))
            }(t, Math.ceil((24 + t[4]) / 4));
        return new kr(t[0], t[1], t[2], t[3], B, n)
    }("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"),
    zr = "×",
    $r = function(A) {
        return jr.get(A)
    },
    AB = function(A, e, t) {
        var r = t - 2,
            B = e[r],
            n = e[t - 1],
            s = e[t];
        if (2 === n && 3 === s) return zr;
        if (2 === n || 3 === n || 4 === n) return "÷";
        if (2 === s || 3 === s || 4 === s) return "÷";
        if (n === Yr && -1 !== [Yr, Wr, Zr, _r].indexOf(s)) return zr;
        if (!(n !== Zr && n !== Wr || s !== Wr && 10 !== s)) return zr;
        if ((n === _r || 10 === n) && 10 === s) return zr;
        if (13 === s || 5 === s) return zr;
        if (7 === s) return zr;
        if (1 === n) return zr;
        if (13 === n && 14 === s) {
            for (; 5 === B;) B = e[--r];
            if (14 === B) return zr
        }
        if (15 === n && 15 === s) {
            for (var o = 0; 15 === B;) o++, B = e[--r];
            if (o % 2 == 0) return zr
        }
        return "÷"
    },
    eB = function(A) {
        var e = function(A) {
                for (var e = [], t = 0, r = A.length; t < r;) {
                    var B = A.charCodeAt(t++);
                    if (B >= 55296 && B <= 56319 && t < r) {
                        var n = A.charCodeAt(t++);
                        56320 == (64512 & n) ? e.push(((1023 & B) << 10) + (1023 & n) + 65536) : (e.push(B), t--)
                    } else e.push(B)
                }
                return e
            }(A),
            t = e.length,
            r = 0,
            B = 0,
            n = e.map($r);
        return {
            next: function() {
                if (r >= t) return {
                    done: !0,
                    value: null
                };
                for (var A = zr; r < t && (A = AB(0, n, ++r)) === zr;);
                if (A !== zr || r === t) {
                    var s = qr.apply(null, e.slice(B, r));
                    return B = r, {
                        value: s,
                        done: !1
                    }
                }
                return {
                    done: !0,
                    value: null
                }
            }
        }
    },
    tB = function(A) {
        return 0 === A[0] && 255 === A[1] && 0 === A[2] && 255 === A[3]
    },
    rB = function(A, e, t, r, B) {
        var n = "http://www.w3.org/2000/svg",
            s = document.createElementNS(n, "svg"),
            o = document.createElementNS(n, "foreignObject");
        return s.setAttributeNS(null, "width", A.toString()), s.setAttributeNS(null, "height", e.toString()), o.setAttributeNS(null, "width", "100%"), o.setAttributeNS(null, "height", "100%"), o.setAttributeNS(null, "x", t.toString()), o.setAttributeNS(null, "y", r.toString()), o.setAttributeNS(null, "externalResourcesRequired", "true"), s.appendChild(o), o.appendChild(B), s
    },
    BB = function(A) {
        return new Promise((function(e, t) {
            var r = new Image;
            r.onload = function() {
                return e(r)
            }, r.onerror = t, r.src = "data:image/svg+xml;charset=utf-8," + encodeURIComponent((new XMLSerializer).serializeToString(A))
        }))
    },
    nB = {
        get SUPPORT_RANGE_BOUNDS() {
            var A = function(A) {
                if (A.createRange) {
                    var e = A.createRange();
                    if (e.getBoundingClientRect) {
                        var t = A.createElement("boundtest");
                        t.style.height = "123px", t.style.display = "block", A.body.appendChild(t), e.selectNode(t);
                        var r = e.getBoundingClientRect(),
                            B = Math.round(r.height);
                        if (A.body.removeChild(t), 123 === B) return !0
                    }
                }
                return !1
            }(document);
            return Object.defineProperty(nB, "SUPPORT_RANGE_BOUNDS", {
                value: A
            }), A
        },
        get SUPPORT_WORD_BREAKING() {
            var A = nB.SUPPORT_RANGE_BOUNDS && function(A) {
                var e = A.createElement("boundtest");
                e.style.width = "50px", e.style.display = "block", e.style.fontSize = "12px", e.style.letterSpacing = "0px", e.style.wordSpacing = "0px", A.body.appendChild(e);
                var t = A.createRange();
                e.innerHTML = "function" == typeof "".repeat ? "&#128104;".repeat(10) : "";
                var r = e.firstChild,
                    B = o(r.data).map((function(A) {
                        return i(A)
                    })),
                    n = 0,
                    s = {},
                    Q = B.every((function(A, e) {
                        t.setStart(r, n), t.setEnd(r, n + A.length);
                        var B = t.getBoundingClientRect();
                        n += A.length;
                        var o = B.x > s.x || B.y > s.y;
                        return s = B, 0 === e || o
                    }));
                return A.body.removeChild(e), Q
            }(document);
            return Object.defineProperty(nB, "SUPPORT_WORD_BREAKING", {
                value: A
            }), A
        },
        get SUPPORT_SVG_DRAWING() {
            var A = function(A) {
                var e = new Image,
                    t = A.createElement("canvas"),
                    r = t.getContext("2d");
                if (!r) return !1;
                e.src = "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";
                try {
                    r.drawImage(e, 0, 0), t.toDataURL()
                } catch (B) {
                    return !1
                }
                return !0
            }(document);
            return Object.defineProperty(nB, "SUPPORT_SVG_DRAWING", {
                value: A
            }), A
        },
        get SUPPORT_FOREIGNOBJECT_DRAWING() {
            var A = "function" == typeof Array.from && "function" == typeof window.fetch ? function(A) {
                var e = A.createElement("canvas"),
                    t = 100;
                e.width = t, e.height = t;
                var r = e.getContext("2d");
                if (!r) return Promise.reject(!1);
                r.fillStyle = "rgb(0, 255, 0)", r.fillRect(0, 0, t, t);
                var B = new Image,
                    n = e.toDataURL();
                B.src = n;
                var s = rB(t, t, 0, 0, B);
                return r.fillStyle = "red", r.fillRect(0, 0, t, t), BB(s).then((function(e) {
                    r.drawImage(e, 0, 0);
                    var B = r.getImageData(0, 0, t, t).data;
                    r.fillStyle = "red", r.fillRect(0, 0, t, t);
                    var s = A.createElement("div");
                    return s.style.backgroundImage = "url(" + n + ")", s.style.height = t + "px", tB(B) ? BB(rB(t, t, 0, 0, s)) : Promise.reject(!1)
                })).then((function(A) {
                    return r.drawImage(A, 0, 0), tB(r.getImageData(0, 0, t, t).data)
                })).catch((function() {
                    return !1
                }))
            }(document) : Promise.resolve(!1);
            return Object.defineProperty(nB, "SUPPORT_FOREIGNOBJECT_DRAWING", {
                value: A
            }), A
        },
        get SUPPORT_CORS_IMAGES() {
            var A = void 0 !== (new Image).crossOrigin;
            return Object.defineProperty(nB, "SUPPORT_CORS_IMAGES", {
                value: A
            }), A
        },
        get SUPPORT_RESPONSE_TYPE() {
            var A = "string" == typeof(new XMLHttpRequest).responseType;
            return Object.defineProperty(nB, "SUPPORT_RESPONSE_TYPE", {
                value: A
            }), A
        },
        get SUPPORT_CORS_XHR() {
            var A = "withCredentials" in new XMLHttpRequest;
            return Object.defineProperty(nB, "SUPPORT_CORS_XHR", {
                value: A
            }), A
        },
        get SUPPORT_NATIVE_TEXT_SEGMENTATION() {
            var A = !("undefined" == typeof Intl || !Intl.Segmenter);
            return Object.defineProperty(nB, "SUPPORT_NATIVE_TEXT_SEGMENTATION", {
                value: A
            }), A
        }
    },
    sB = function() {
        return function(A, e) {
            this.text = A, this.bounds = e
        }
    }(),
    oB = function(A, e) {
        var t = e.ownerDocument;
        if (t) {
            var r = t.createElement("html2canvaswrapper");
            r.appendChild(e.cloneNode(!0));
            var B = e.parentNode;
            if (B) {
                B.replaceChild(r, e);
                var o = s(A, r);
                return r.firstChild && B.replaceChild(r.firstChild, r), o
            }
        }
        return n.EMPTY
    },
    iB = function(A, e, t) {
        var r = A.ownerDocument;
        if (!r) throw new Error("Node has no owner document");
        var B = r.createRange();
        return B.setStart(A, e), B.setEnd(A, e + t), B
    },
    QB = function(A) {
        if (nB.SUPPORT_NATIVE_TEXT_SEGMENTATION) {
            var e = new Intl.Segmenter(void 0, {
                granularity: "grapheme"
            });
            return Array.from(e.segment(A)).map((function(A) {
                return A.segment
            }))
        }
        return function(A) {
            for (var e, t = eB(A), r = []; !(e = t.next()).done;) e.value && r.push(e.value.slice());
            return r
        }(A)
    },
    cB = function(A, e) {
        return 0 !== e.letterSpacing ? QB(A) : function(A, e) {
            if (nB.SUPPORT_NATIVE_TEXT_SEGMENTATION) {
                var t = new Intl.Segmenter(void 0, {
                    granularity: "word"
                });
                return Array.from(t.segment(A)).map((function(A) {
                    return A.segment
                }))
            }
            return wB(A, e)
        }(A, e)
    },
    aB = [32, 160, 4961, 65792, 65793, 4153, 4241],
    wB = function(A, e) {
        for (var t, r = function(A, e) {
                var t = o(A),
                    r = wA(t, e),
                    B = r[0],
                    n = r[1],
                    s = r[2],
                    i = t.length,
                    Q = 0,
                    c = 0;
                return {
                    next: function() {
                        if (c >= i) return {
                            done: !0,
                            value: null
                        };
                        for (var A = $; c < i && (A = aA(t, n, B, ++c, s)) === $;);
                        if (A !== $ || c === i) {
                            var e = new gA(t, A, Q, c);
                            return Q = c, {
                                value: e,
                                done: !1
                            }
                        }
                        return {
                            done: !0,
                            value: null
                        }
                    }
                }
            }(A, {
                lineBreak: e.lineBreak,
                wordBreak: "break-word" === e.overflowWrap ? "break-word" : e.wordBreak
            }), B = [], n = function() {
                if (t.value) {
                    var A = t.value.slice(),
                        e = o(A),
                        r = "";
                    e.forEach((function(A) {
                        -1 === aB.indexOf(A) ? r += i(A) : (r.length && B.push(r), B.push(i(A)), r = "")
                    })), r.length && B.push(r)
                }
            }; !(t = r.next()).done;) n();
        return B
    },
    gB = function() {
        return function(A, e, t) {
            this.text = uB(e.data, t.textTransform), this.textBounds = function(A, e, t, r) {
                var B = cB(e, t),
                    s = [],
                    o = 0;
                return B.forEach((function(e) {
                    if (t.textDecorationLine.length || e.trim().length > 0)
                        if (nB.SUPPORT_RANGE_BOUNDS) {
                            var B = iB(r, o, e.length).getClientRects();
                            if (B.length > 1) {
                                var i = QB(e),
                                    Q = 0;
                                i.forEach((function(e) {
                                    s.push(new sB(e, n.fromDOMRectList(A, iB(r, Q + o, e.length).getClientRects()))), Q += e.length
                                }))
                            } else s.push(new sB(e, n.fromDOMRectList(A, B)))
                        } else {
                            var c = r.splitText(e.length);
                            s.push(new sB(e, oB(A, r))), r = c
                        }
                    else nB.SUPPORT_RANGE_BOUNDS || (r = r.splitText(e.length));
                    o += e.length
                })), s
            }(A, this.text, t, e)
        }
    }(),
    uB = function(A, e) {
        switch (e) {
            case 1:
                return A.toLowerCase();
            case 3:
                return A.replace(UB, CB);
            case 2:
                return A.toUpperCase();
            default:
                return A
        }
    },
    UB = /(^|\s|:|-|\(|\))([a-z])/g,
    CB = function(A, e, t) {
        return A.length > 0 ? e + t.toUpperCase() : A
    },
    lB = function(A) {
        function t(e, t) {
            var r = A.call(this, e, t) || this;
            return r.src = t.currentSrc || t.src, r.intrinsicWidth = t.naturalWidth, r.intrinsicHeight = t.naturalHeight, r.context.cache.addImage(r.src), r
        }
        return e(t, A), t
    }(Sr),
    FB = function(A) {
        function t(e, t) {
            var r = A.call(this, e, t) || this;
            return r.canvas = t, r.intrinsicWidth = t.width, r.intrinsicHeight = t.height, r
        }
        return e(t, A), t
    }(Sr),
    hB = function(A) {
        function t(e, t) {
            var r = A.call(this, e, t) || this,
                B = new XMLSerializer,
                n = s(e, t);
            return t.setAttribute("width", n.width + "px"), t.setAttribute("height", n.height + "px"), r.svg = "data:image/svg+xml," + encodeURIComponent(B.serializeToString(t)), r.intrinsicWidth = t.width.baseVal.value, r.intrinsicHeight = t.height.baseVal.value, r.context.cache.addImage(r.svg), r
        }
        return e(t, A), t
    }(Sr),
    dB = function(A) {
        function t(e, t) {
            var r = A.call(this, e, t) || this;
            return r.value = t.value, r
        }
        return e(t, A), t
    }(Sr),
    fB = function(A) {
        function t(e, t) {
            var r = A.call(this, e, t) || this;
            return r.start = t.start, r.reversed = "boolean" == typeof t.reversed && !0 === t.reversed, r
        }
        return e(t, A), t
    }(Sr),
    HB = [{
        type: 15,
        flags: 0,
        unit: "px",
        number: 3
    }],
    pB = [{
        type: 16,
        flags: 0,
        number: 50
    }],
    EB = "checkbox",
    IB = "radio",
    yB = "password",
    KB = 707406591,
    mB = function(A) {
        function t(e, t) {
            var r, B, s, o = A.call(this, e, t) || this;
            switch (o.type = t.type.toLowerCase(), o.checked = t.checked, o.value = 0 === (B = (r = t).type === yB ? new Array(r.value.length + 1).join("•") : r.value).length ? r.placeholder || "" : B, o.type !== EB && o.type !== IB || (o.styles.backgroundColor = 3739148031, o.styles.borderTopColor = o.styles.borderRightColor = o.styles.borderBottomColor = o.styles.borderLeftColor = 2779096575, o.styles.borderTopWidth = o.styles.borderRightWidth = o.styles.borderBottomWidth = o.styles.borderLeftWidth = 1, o.styles.borderTopStyle = o.styles.borderRightStyle = o.styles.borderBottomStyle = o.styles.borderLeftStyle = 1, o.styles.backgroundClip = [0], o.styles.backgroundOrigin = [0], o.bounds = (s = o.bounds).width > s.height ? new n(s.left + (s.width - s.height) / 2, s.top, s.height, s.height) : s.width < s.height ? new n(s.left, s.top + (s.height - s.width) / 2, s.width, s.width) : s), o.type) {
                case EB:
                    o.styles.borderTopRightRadius = o.styles.borderTopLeftRadius = o.styles.borderBottomRightRadius = o.styles.borderBottomLeftRadius = HB;
                    break;
                case IB:
                    o.styles.borderTopRightRadius = o.styles.borderTopLeftRadius = o.styles.borderBottomRightRadius = o.styles.borderBottomLeftRadius = pB
            }
            return o
        }
        return e(t, A), t
    }(Sr),
    LB = function(A) {
        function t(e, t) {
            var r = A.call(this, e, t) || this,
                B = t.options[t.selectedIndex || 0];
            return r.value = B && B.text || "", r
        }
        return e(t, A), t
    }(Sr),
    bB = function(A) {
        function t(e, t) {
            var r = A.call(this, e, t) || this;
            return r.value = t.value, r
        }
        return e(t, A), t
    }(Sr),
    vB = function(A) {
        function t(e, t) {
            var r = A.call(this, e, t) || this;
            r.src = t.src, r.width = parseInt(t.width, 10) || 0, r.height = parseInt(t.height, 10) || 0, r.backgroundColor = r.styles.backgroundColor;
            try {
                if (t.contentWindow && t.contentWindow.document && t.contentWindow.document.documentElement) {
                    r.tree = SB(e, t.contentWindow.document.documentElement);
                    var B = t.contentWindow.document.documentElement ? me(e, getComputedStyle(t.contentWindow.document.documentElement).backgroundColor) : Le.TRANSPARENT,
                        n = t.contentWindow.document.body ? me(e, getComputedStyle(t.contentWindow.document.body).backgroundColor) : Le.TRANSPARENT;
                    r.backgroundColor = de(B) ? de(n) ? r.styles.backgroundColor : n : B
                }
            } catch (s) {}
            return r
        }
        return e(t, A), t
    }(Sr),
    DB = ["OL", "UL", "MENU"],
    xB = function(A, e, t, r) {
        for (var B = e.firstChild, n = void 0; B; B = n)
            if (n = B.nextSibling, OB(B) && B.data.trim().length > 0) t.textNodes.push(new gB(A, B, t.styles));
            else if (VB(B))
            if (An(B) && B.assignedNodes) B.assignedNodes().forEach((function(e) {
                return xB(A, e, t, r)
            }));
            else {
                var s = MB(A, B);
                s.styles.isVisible() && (TB(B, s, r) ? s.flags |= 4 : GB(s.styles) && (s.flags |= 2), -1 !== DB.indexOf(B.tagName) && (s.flags |= 8), t.elements.push(s), B.slot, B.shadowRoot ? xB(A, B.shadowRoot, s, r) : zB(B) || JB(B) || $B(B) || xB(A, B, s, r))
            }
    },
    MB = function(A, e) {
        return _B(e) ? new lB(A, e) : WB(e) ? new FB(A, e) : JB(e) ? new hB(A, e) : NB(e) ? new dB(A, e) : PB(e) ? new fB(A, e) : XB(e) ? new mB(A, e) : $B(e) ? new LB(A, e) : zB(e) ? new bB(A, e) : qB(e) ? new vB(A, e) : new Sr(A, e)
    },
    SB = function(A, e) {
        var t = MB(A, e);
        return t.flags |= 4, xB(A, e, t, t), t
    },
    TB = function(A, e, t) {
        return e.styles.isPositionedWithZIndex() || e.styles.opacity < 1 || e.styles.isTransformed() || YB(A) && t.styles.isTransparent()
    },
    GB = function(A) {
        return A.isPositioned() || A.isFloating()
    },
    OB = function(A) {
        return A.nodeType === Node.TEXT_NODE
    },
    VB = function(A) {
        return A.nodeType === Node.ELEMENT_NODE
    },
    kB = function(A) {
        return VB(A) && void 0 !== A.style && !RB(A)
    },
    RB = function(A) {
        return "object" == typeof A.className
    },
    NB = function(A) {
        return "LI" === A.tagName
    },
    PB = function(A) {
        return "OL" === A.tagName
    },
    XB = function(A) {
        return "INPUT" === A.tagName
    },
    JB = function(A) {
        return "svg" === A.tagName
    },
    YB = function(A) {
        return "BODY" === A.tagName
    },
    WB = function(A) {
        return "CANVAS" === A.tagName
    },
    ZB = function(A) {
        return "VIDEO" === A.tagName
    },
    _B = function(A) {
        return "IMG" === A.tagName
    },
    qB = function(A) {
        return "IFRAME" === A.tagName
    },
    jB = function(A) {
        return "STYLE" === A.tagName
    },
    zB = function(A) {
        return "TEXTAREA" === A.tagName
    },
    $B = function(A) {
        return "SELECT" === A.tagName
    },
    An = function(A) {
        return "SLOT" === A.tagName
    },
    en = function(A) {
        return A.tagName.indexOf("-") > 0
    },
    tn = function() {
        function A() {
            this.counters = {}
        }
        return A.prototype.getCounterValue = function(A) {
            var e = this.counters[A];
            return e && e.length ? e[e.length - 1] : 1
        }, A.prototype.getCounterValues = function(A) {
            var e = this.counters[A];
            return e || []
        }, A.prototype.pop = function(A) {
            var e = this;
            A.forEach((function(A) {
                return e.counters[A].pop()
            }))
        }, A.prototype.parse = function(A) {
            var e = this,
                t = A.counterIncrement,
                r = A.counterReset,
                B = !0;
            null !== t && t.forEach((function(A) {
                var t = e.counters[A.counter];
                t && 0 !== A.increment && (B = !1, t.length || t.push(1), t[Math.max(0, t.length - 1)] += A.increment)
            }));
            var n = [];
            return B && r.forEach((function(A) {
                var t = e.counters[A.counter];
                n.push(A.counter), t || (t = e.counters[A.counter] = []), t.push(A.reset)
            })), n
        }, A
    }(),
    rn = {
        integers: [1e3, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1],
        values: ["M", "CM", "D", "CD", "C", "XC", "L", "XL", "X", "IX", "V", "IV", "I"]
    },
    Bn = {
        integers: [9e3, 8e3, 7e3, 6e3, 5e3, 4e3, 3e3, 2e3, 1e3, 900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
        values: ["Ք", "Փ", "Ւ", "Ց", "Ր", "Տ", "Վ", "Ս", "Ռ", "Ջ", "Պ", "Չ", "Ո", "Շ", "Ն", "Յ", "Մ", "Ճ", "Ղ", "Ձ", "Հ", "Կ", "Ծ", "Խ", "Լ", "Ի", "Ժ", "Թ", "Ը", "Է", "Զ", "Ե", "Դ", "Գ", "Բ", "Ա"]
    },
    nn = {
        integers: [1e4, 9e3, 8e3, 7e3, 6e3, 5e3, 4e3, 3e3, 2e3, 1e3, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 19, 18, 17, 16, 15, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
        values: ["י׳", "ט׳", "ח׳", "ז׳", "ו׳", "ה׳", "ד׳", "ג׳", "ב׳", "א׳", "ת", "ש", "ר", "ק", "צ", "פ", "ע", "ס", "נ", "מ", "ל", "כ", "יט", "יח", "יז", "טז", "טו", "י", "ט", "ח", "ז", "ו", "ה", "ד", "ג", "ב", "א"]
    },
    sn = {
        integers: [1e4, 9e3, 8e3, 7e3, 6e3, 5e3, 4e3, 3e3, 2e3, 1e3, 900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
        values: ["ჵ", "ჰ", "ჯ", "ჴ", "ხ", "ჭ", "წ", "ძ", "ც", "ჩ", "შ", "ყ", "ღ", "ქ", "ფ", "ჳ", "ტ", "ს", "რ", "ჟ", "პ", "ო", "ჲ", "ნ", "მ", "ლ", "კ", "ი", "თ", "ჱ", "ზ", "ვ", "ე", "დ", "გ", "ბ", "ა"]
    },
    on = function(A, e, t, r, B, n) {
        return A < e || A > t ? ln(A, B, n.length > 0) : r.integers.reduce((function(e, t, B) {
            for (; A >= t;) A -= t, e += r.values[B];
            return e
        }), "") + n
    },
    Qn = function(A, e, t, r) {
        var B = "";
        do {
            t || A--, B = r(A) + B, A /= e
        } while (A * e >= e);
        return B
    },
    cn = function(A, e, t, r, B) {
        var n = t - e + 1;
        return (A < 0 ? "-" : "") + (Qn(Math.abs(A), n, r, (function(A) {
            return i(Math.floor(A % n) + e)
        })) + B)
    },
    an = function(A, e, t) {
        void 0 === t && (t = ". ");
        var r = e.length;
        return Qn(Math.abs(A), r, !1, (function(A) {
            return e[Math.floor(A % r)]
        })) + t
    },
    wn = function(A, e, t, r, B, n) {
        if (A < -9999 || A > 9999) return ln(A, 4, B.length > 0);
        var s = Math.abs(A),
            o = B;
        if (0 === s) return e[0] + o;
        for (var i = 0; s > 0 && i <= 4; i++) {
            var Q = s % 10;
            0 === Q && hr(n, 1) && "" !== o ? o = e[Q] + o : Q > 1 || 1 === Q && 0 === i || 1 === Q && 1 === i && hr(n, 2) || 1 === Q && 1 === i && hr(n, 4) && A > 100 || 1 === Q && i > 1 && hr(n, 8) ? o = e[Q] + (i > 0 ? t[i - 1] : "") + o : 1 === Q && i > 0 && (o = t[i - 1] + o), s = Math.floor(s / 10)
        }
        return (A < 0 ? r : "") + o
    },
    gn = "十百千萬",
    un = "拾佰仟萬",
    Un = "マイナス",
    Cn = "마이너스",
    ln = function(A, e, t) {
        var r = t ? ". " : "",
            B = t ? "、" : "",
            n = t ? ", " : "",
            s = t ? " " : "";
        switch (e) {
            case 0:
                return "•" + s;
            case 1:
                return "◦" + s;
            case 2:
                return "◾" + s;
            case 5:
                var o = cn(A, 48, 57, !0, r);
                return o.length < 4 ? "0" + o : o;
            case 4:
                return an(A, "〇一二三四五六七八九", B);
            case 6:
                return on(A, 1, 3999, rn, 3, r).toLowerCase();
            case 7:
                return on(A, 1, 3999, rn, 3, r);
            case 8:
                return cn(A, 945, 969, !1, r);
            case 9:
                return cn(A, 97, 122, !1, r);
            case 10:
                return cn(A, 65, 90, !1, r);
            case 11:
                return cn(A, 1632, 1641, !0, r);
            case 12:
            case 49:
                return on(A, 1, 9999, Bn, 3, r);
            case 35:
                return on(A, 1, 9999, Bn, 3, r).toLowerCase();
            case 13:
                return cn(A, 2534, 2543, !0, r);
            case 14:
            case 30:
                return cn(A, 6112, 6121, !0, r);
            case 15:
                return an(A, "子丑寅卯辰巳午未申酉戌亥", B);
            case 16:
                return an(A, "甲乙丙丁戊己庚辛壬癸", B);
            case 17:
            case 48:
                return wn(A, "零一二三四五六七八九", gn, "負", B, 14);
            case 47:
                return wn(A, "零壹貳參肆伍陸柒捌玖", un, "負", B, 15);
            case 42:
                return wn(A, "零一二三四五六七八九", gn, "负", B, 14);
            case 41:
                return wn(A, "零壹贰叁肆伍陆柒捌玖", un, "负", B, 15);
            case 26:
                return wn(A, "〇一二三四五六七八九", "十百千万", Un, B, 0);
            case 25:
                return wn(A, "零壱弐参四伍六七八九", "拾百千万", Un, B, 7);
            case 31:
                return wn(A, "영일이삼사오육칠팔구", "십백천만", Cn, n, 7);
            case 33:
                return wn(A, "零一二三四五六七八九", "十百千萬", Cn, n, 0);
            case 32:
                return wn(A, "零壹貳參四五六七八九", "拾百千", Cn, n, 7);
            case 18:
                return cn(A, 2406, 2415, !0, r);
            case 20:
                return on(A, 1, 19999, sn, 3, r);
            case 21:
                return cn(A, 2790, 2799, !0, r);
            case 22:
                return cn(A, 2662, 2671, !0, r);
            case 22:
                return on(A, 1, 10999, nn, 3, r);
            case 23:
                return an(A, "あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");
            case 24:
                return an(A, "いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");
            case 27:
                return cn(A, 3302, 3311, !0, r);
            case 28:
                return an(A, "アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン", B);
            case 29:
                return an(A, "イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス", B);
            case 34:
                return cn(A, 3792, 3801, !0, r);
            case 37:
                return cn(A, 6160, 6169, !0, r);
            case 38:
                return cn(A, 4160, 4169, !0, r);
            case 39:
                return cn(A, 2918, 2927, !0, r);
            case 40:
                return cn(A, 1776, 1785, !0, r);
            case 43:
                return cn(A, 3046, 3055, !0, r);
            case 44:
                return cn(A, 3174, 3183, !0, r);
            case 45:
                return cn(A, 3664, 3673, !0, r);
            case 46:
                return cn(A, 3872, 3881, !0, r);
            default:
                return cn(A, 48, 57, !0, r)
        }
    },
    Fn = "data-html2canvas-ignore",
    hn = function() {
        function A(A, e, t) {
            if (this.context = A, this.options = t, this.scrolledElements = [], this.referenceElement = e, this.counters = new tn, this.quoteDepth = 0, !e.ownerDocument) throw new Error("Cloned element does not have an owner document");
            this.documentElement = this.cloneNode(e.ownerDocument.documentElement, !1)
        }
        return A.prototype.toIFrame = function(A, e) {
            var t = this,
                n = Hn(A, e);
            if (!n.contentWindow) return Promise.reject("Unable to find iframe window");
            var s = A.defaultView.pageXOffset,
                o = A.defaultView.pageYOffset,
                i = n.contentWindow,
                Q = i.document,
                c = In(n).then((function() {
                    return r(t, 0, void 0, (function() {
                        var A, t;
                        return B(this, (function(r) {
                            switch (r.label) {
                                case 0:
                                    return this.scrolledElements.forEach(bn), i && (i.scrollTo(e.left, e.top), !/(iPad|iPhone|iPod)/g.test(navigator.userAgent) || i.scrollY === e.top && i.scrollX === e.left || (this.context.logger.warn("Unable to restore scroll position for cloned document"), this.context.windowBounds = this.context.windowBounds.add(i.scrollX - e.left, i.scrollY - e.top, 0, 0))), A = this.options.onclone, void 0 === (t = this.clonedReferenceElement) ? [2, Promise.reject("Error finding the " + this.referenceElement.nodeName + " in the cloned document")] : Q.fonts && Q.fonts.ready ? [4, Q.fonts.ready] : [3, 2];
                                case 1:
                                    r.sent(), r.label = 2;
                                case 2:
                                    return /(AppleWebKit)/g.test(navigator.userAgent) ? [4, En(Q)] : [3, 4];
                                case 3:
                                    r.sent(), r.label = 4;
                                case 4:
                                    return "function" == typeof A ? [2, Promise.resolve().then((function() {
                                        return A(Q, t)
                                    })).then((function() {
                                        return n
                                    }))] : [2, n]
                            }
                        }))
                    }))
                }));
            return Q.open(), Q.write(mn(document.doctype) + "<html></html>"), Ln(this.referenceElement.ownerDocument, s, o), Q.replaceChild(Q.adoptNode(this.documentElement), Q.documentElement), Q.close(), c
        }, A.prototype.createElementClone = function(A) {
            if (Mr(A, 2), WB(A)) return this.createCanvasClone(A);
            if (ZB(A)) return this.createVideoClone(A);
            if (jB(A)) return this.createStyleClone(A);
            var e = A.cloneNode(!1);
            return _B(e) && (_B(A) && A.currentSrc && A.currentSrc !== A.src && (e.src = A.currentSrc, e.srcset = ""), "lazy" === e.loading && (e.loading = "eager")), en(e) ? this.createCustomElementClone(e) : e
        }, A.prototype.createCustomElementClone = function(A) {
            var e = document.createElement("html2canvascustomelement");
            return Kn(A.style, e), e
        }, A.prototype.createStyleClone = function(A) {
            try {
                var e = A.sheet;
                if (e && e.cssRules) {
                    var t = [].slice.call(e.cssRules, 0).reduce((function(A, e) {
                            return e && "string" == typeof e.cssText ? A + e.cssText : A
                        }), ""),
                        r = A.cloneNode(!1);
                    return r.textContent = t, r
                }
            } catch (B) {
                if (this.context.logger.error("Unable to access cssRules property", B), "SecurityError" !== B.name) throw B
            }
            return A.cloneNode(!1)
        }, A.prototype.createCanvasClone = function(A) {
            var e;
            if (this.options.inlineImages && A.ownerDocument) {
                var t = A.ownerDocument.createElement("img");
                try {
                    return t.src = A.toDataURL(), t
                } catch (i) {
                    this.context.logger.info("Unable to inline canvas contents, canvas is tainted", A)
                }
            }
            var r = A.cloneNode(!1);
            try {
                r.width = A.width, r.height = A.height;
                var B = A.getContext("2d"),
                    n = r.getContext("2d");
                if (n)
                    if (!this.options.allowTaint && B) n.putImageData(B.getImageData(0, 0, A.width, A.height), 0, 0);
                    else {
                        var s = null !== (e = A.getContext("webgl2")) && void 0 !== e ? e : A.getContext("webgl");
                        if (s) {
                            var o = s.getContextAttributes();
                            !1 === (null == o ? void 0 : o.preserveDrawingBuffer) && this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false", A)
                        }
                        n.drawImage(A, 0, 0)
                    }
                return r
            } catch (i) {
                this.context.logger.info("Unable to clone canvas as it is tainted", A)
            }
            return r
        }, A.prototype.createVideoClone = function(A) {
            var e = A.ownerDocument.createElement("canvas");
            e.width = A.offsetWidth, e.height = A.offsetHeight;
            var t = e.getContext("2d");
            try {
                return t && (t.drawImage(A, 0, 0, e.width, e.height), this.options.allowTaint || t.getImageData(0, 0, e.width, e.height)), e
            } catch (B) {
                this.context.logger.info("Unable to clone video as it is tainted", A)
            }
            var r = A.ownerDocument.createElement("canvas");
            return r.width = A.offsetWidth, r.height = A.offsetHeight, r
        }, A.prototype.appendChildNode = function(A, e, t) {
            VB(e) && ("SCRIPT" === e.tagName || e.hasAttribute(Fn) || "function" == typeof this.options.ignoreElements && this.options.ignoreElements(e)) || this.options.copyStyles && VB(e) && jB(e) || A.appendChild(this.cloneNode(e, t))
        }, A.prototype.cloneChildNodes = function(A, e, t) {
            for (var r = this, B = A.shadowRoot ? A.shadowRoot.firstChild : A.firstChild; B; B = B.nextSibling)
                if (VB(B) && An(B) && "function" == typeof B.assignedNodes) {
                    var n = B.assignedNodes();
                    n.length && n.forEach((function(A) {
                        return r.appendChildNode(e, A, t)
                    }))
                } else this.appendChildNode(e, B, t)
        }, A.prototype.cloneNode = function(A, e) {
            if (OB(A)) return document.createTextNode(A.data);
            if (!A.ownerDocument) return A.cloneNode(!1);
            var t = A.ownerDocument.defaultView;
            if (t && VB(A) && (kB(A) || RB(A))) {
                var r = this.createElementClone(A);
                r.style.transitionProperty = "none";
                var B = t.getComputedStyle(A),
                    n = t.getComputedStyle(A, ":before"),
                    s = t.getComputedStyle(A, ":after");
                this.referenceElement === A && kB(r) && (this.clonedReferenceElement = r), YB(r) && Mn(r);
                var o = this.counters.parse(new Dr(this.context, B)),
                    i = this.resolvePseudoContent(A, r, n, Xr.BEFORE);
                en(A) && (e = !0), ZB(A) || this.cloneChildNodes(A, r, e), i && r.insertBefore(i, r.firstChild);
                var Q = this.resolvePseudoContent(A, r, s, Xr.AFTER);
                return Q && r.appendChild(Q), this.counters.pop(o), (B && (this.options.copyStyles || RB(A)) && !qB(A) || e) && Kn(B, r), 0 === A.scrollTop && 0 === A.scrollLeft || this.scrolledElements.push([r, A.scrollLeft, A.scrollTop]), (zB(A) || $B(A)) && (zB(r) || $B(r)) && (r.value = A.value), r
            }
            return A.cloneNode(!1)
        }, A.prototype.resolvePseudoContent = function(A, e, t, r) {
            var B = this;
            if (t) {
                var n = t.content,
                    s = e.ownerDocument;
                if (s && n && "none" !== n && "-moz-alt-content" !== n && "none" !== t.display) {
                    this.counters.parse(new Dr(this.context, t));
                    var o = new vr(this.context, t),
                        i = s.createElement("html2canvaspseudoelement");
                    Kn(t, i), o.content.forEach((function(e) {
                        if (0 === e.type) i.appendChild(s.createTextNode(e.value));
                        else if (22 === e.type) {
                            var t = s.createElement("img");
                            t.src = e.value, t.style.opacity = "1", i.appendChild(t)
                        } else if (18 === e.type) {
                            if ("attr" === e.name) {
                                var r = e.values.filter(zA);
                                r.length && i.appendChild(s.createTextNode(A.getAttribute(r[0].value) || ""))
                            } else if ("counter" === e.name) {
                                var n = e.values.filter(te),
                                    Q = n[0],
                                    c = n[1];
                                if (Q && zA(Q)) {
                                    var a = B.counters.getCounterValue(Q.value),
                                        w = c && zA(c) ? Vt.parse(B.context, c.value) : 3;
                                    i.appendChild(s.createTextNode(ln(a, w, !1)))
                                }
                            } else if ("counters" === e.name) {
                                var g = e.values.filter(te),
                                    u = (Q = g[0], g[1]);
                                c = g[2];
                                if (Q && zA(Q)) {
                                    var U = B.counters.getCounterValues(Q.value),
                                        C = c && zA(c) ? Vt.parse(B.context, c.value) : 3,
                                        l = u && 0 === u.type ? u.value : "",
                                        F = U.map((function(A) {
                                            return ln(A, C, !1)
                                        })).join(l);
                                    i.appendChild(s.createTextNode(F))
                                }
                            }
                        } else if (20 === e.type) switch (e.value) {
                            case "open-quote":
                                i.appendChild(s.createTextNode(Ir(o.quotes, B.quoteDepth++, !0)));
                                break;
                            case "close-quote":
                                i.appendChild(s.createTextNode(Ir(o.quotes, --B.quoteDepth, !1)));
                                break;
                            default:
                                i.appendChild(s.createTextNode(e.value))
                        }
                    })), i.className = vn + " " + Dn;
                    var Q = r === Xr.BEFORE ? " " + vn : " " + Dn;
                    return RB(e) ? e.className.baseValue += Q : e.className += Q, i
                }
            }
        }, A.destroy = function(A) {
            return !!A.parentNode && (A.parentNode.removeChild(A), !0)
        }, A
    }();
(Jr = Xr || (Xr = {}))[Jr.BEFORE = 0] = "BEFORE", Jr[Jr.AFTER = 1] = "AFTER";
var dn, fn, Hn = function(A, e) {
        var t = A.createElement("iframe");
        return t.className = "html2canvas-container", t.style.visibility = "hidden", t.style.position = "fixed", t.style.left = "-10000px", t.style.top = "0px", t.style.border = "0", t.width = e.width.toString(), t.height = e.height.toString(), t.scrolling = "no", t.setAttribute(Fn, "true"), A.body.appendChild(t), t
    },
    pn = function(A) {
        return new Promise((function(e) {
            A.complete ? e() : A.src ? (A.onload = e, A.onerror = e) : e()
        }))
    },
    En = function(A) {
        return Promise.all([].slice.call(A.images, 0).map(pn))
    },
    In = function(A) {
        return new Promise((function(e, t) {
            var r = A.contentWindow;
            if (!r) return t("No window assigned for iframe");
            var B = r.document;
            r.onload = A.onload = function() {
                r.onload = A.onload = null;
                var t = setInterval((function() {
                    B.body.childNodes.length > 0 && "complete" === B.readyState && (clearInterval(t), e(A))
                }), 50)
            }
        }))
    },
    yn = ["all", "d", "content"],
    Kn = function(A, e) {
        for (var t = A.length - 1; t >= 0; t--) {
            var r = A.item(t); - 1 === yn.indexOf(r) && e.style.setProperty(r, A.getPropertyValue(r))
        }
        return e
    },
    mn = function(A) {
        var e = "";
        return A && (e += "<!DOCTYPE ", A.name && (e += A.name), A.internalSubset && (e += A.internalSubset), A.publicId && (e += '"' + A.publicId + '"'), A.systemId && (e += '"' + A.systemId + '"'), e += ">"), e
    },
    Ln = function(A, e, t) {
        A && A.defaultView && (e !== A.defaultView.pageXOffset || t !== A.defaultView.pageYOffset) && A.defaultView.scrollTo(e, t)
    },
    bn = function(A) {
        var e = A[0],
            t = A[1],
            r = A[2];
        e.scrollLeft = t, e.scrollTop = r
    },
    vn = "___html2canvas___pseudoelement_before",
    Dn = "___html2canvas___pseudoelement_after",
    xn = '{\n    content: "" !important;\n    display: none !important;\n}',
    Mn = function(A) {
        Sn(A, "." + vn + ":before" + xn + "\n         ." + Dn + ":after" + xn)
    },
    Sn = function(A, e) {
        var t = A.ownerDocument;
        if (t) {
            var r = t.createElement("style");
            r.textContent = e, A.appendChild(r)
        }
    },
    Tn = function() {
        function A() {}
        return A.getOrigin = function(e) {
            var t = A._link;
            return t ? (t.href = e, t.href = t.href, t.protocol + t.hostname + t.port) : "about:blank"
        }, A.isSameOrigin = function(e) {
            return A.getOrigin(e) === A._origin
        }, A.setContext = function(e) {
            A._link = e.document.createElement("a"), A._origin = A.getOrigin(e.location.href)
        }, A._origin = "about:blank", A
    }(),
    Gn = function() {
        function A(A, e) {
            this.context = A, this._options = e, this._cache = {}
        }
        return A.prototype.addImage = function(A) {
            var e = Promise.resolve();
            return this.has(A) ? e : Xn(A) || Rn(A) ? ((this._cache[A] = this.loadImage(A)).catch((function() {})), e) : e
        }, A.prototype.match = function(A) {
            return this._cache[A]
        }, A.prototype.loadImage = function(A) {
            return r(this, 0, void 0, (function() {
                var e, t, r, n, s = this;
                return B(this, (function(B) {
                    switch (B.label) {
                        case 0:
                            return e = Tn.isSameOrigin(A), t = !Nn(A) && !0 === this._options.useCORS && nB.SUPPORT_CORS_IMAGES && !e, r = !Nn(A) && !e && !Xn(A) && "string" == typeof this._options.proxy && nB.SUPPORT_CORS_XHR && !t, e || !1 !== this._options.allowTaint || Nn(A) || Xn(A) || r || t ? (n = A, r ? [4, this.proxy(n)] : [3, 2]) : [2];
                        case 1:
                            n = B.sent(), B.label = 2;
                        case 2:
                            return this.context.logger.debug("Added image " + A.substring(0, 256)), [4, new Promise((function(A, e) {
                                var r = new Image;
                                r.onload = function() {
                                    return A(r)
                                }, r.onerror = e, (Pn(n) || t) && (r.crossOrigin = "anonymous"), r.src = n, !0 === r.complete && setTimeout((function() {
                                    return A(r)
                                }), 500), s._options.imageTimeout > 0 && setTimeout((function() {
                                    return e("Timed out (" + s._options.imageTimeout + "ms) loading image")
                                }), s._options.imageTimeout)
                            }))];
                        case 3:
                            return [2, B.sent()]
                    }
                }))
            }))
        }, A.prototype.has = function(A) {
            return void 0 !== this._cache[A]
        }, A.prototype.keys = function() {
            return Promise.resolve(Object.keys(this._cache))
        }, A.prototype.proxy = function(A) {
            var e = this,
                t = this._options.proxy;
            if (!t) throw new Error("No proxy defined");
            var r = A.substring(0, 256);
            return new Promise((function(B, n) {
                var s = nB.SUPPORT_RESPONSE_TYPE ? "blob" : "text",
                    o = new XMLHttpRequest;
                o.onload = function() {
                    if (200 === o.status)
                        if ("text" === s) B(o.response);
                        else {
                            var A = new FileReader;
                            A.addEventListener("load", (function() {
                                return B(A.result)
                            }), !1), A.addEventListener("error", (function(A) {
                                return n(A)
                            }), !1), A.readAsDataURL(o.response)
                        }
                    else n("Failed to proxy resource " + r + " with status code " + o.status)
                }, o.onerror = n;
                var i = t.indexOf("?") > -1 ? "&" : "?";
                if (o.open("GET", "" + t + i + "url=" + encodeURIComponent(A) + "&responseType=" + s), "text" !== s && o instanceof XMLHttpRequest && (o.responseType = s), e._options.imageTimeout) {
                    var Q = e._options.imageTimeout;
                    o.timeout = Q, o.ontimeout = function() {
                        return n("Timed out (" + Q + "ms) proxying " + r)
                    }
                }
                o.send()
            }))
        }, A
    }(),
    On = /^data:image\/svg\+xml/i,
    Vn = /^data:image\/.*;base64,/i,
    kn = /^data:image\/.*/i,
    Rn = function(A) {
        return nB.SUPPORT_SVG_DRAWING || !Jn(A)
    },
    Nn = function(A) {
        return kn.test(A)
    },
    Pn = function(A) {
        return Vn.test(A)
    },
    Xn = function(A) {
        return "blob" === A.substr(0, 4)
    },
    Jn = function(A) {
        return "svg" === A.substr(-3).toLowerCase() || On.test(A)
    },
    Yn = function() {
        function A(A, e) {
            this.type = 0, this.x = A, this.y = e
        }
        return A.prototype.add = function(e, t) {
            return new A(this.x + e, this.y + t)
        }, A
    }(),
    Wn = function(A, e, t) {
        return new Yn(A.x + (e.x - A.x) * t, A.y + (e.y - A.y) * t)
    },
    Zn = function() {
        function A(A, e, t, r) {
            this.type = 1, this.start = A, this.startControl = e, this.endControl = t, this.end = r
        }
        return A.prototype.subdivide = function(e, t) {
            var r = Wn(this.start, this.startControl, e),
                B = Wn(this.startControl, this.endControl, e),
                n = Wn(this.endControl, this.end, e),
                s = Wn(r, B, e),
                o = Wn(B, n, e),
                i = Wn(s, o, e);
            return t ? new A(this.start, r, s, i) : new A(i, o, n, this.end)
        }, A.prototype.add = function(e, t) {
            return new A(this.start.add(e, t), this.startControl.add(e, t), this.endControl.add(e, t), this.end.add(e, t))
        }, A.prototype.reverse = function() {
            return new A(this.end, this.endControl, this.startControl, this.start)
        }, A
    }(),
    _n = function(A) {
        return 1 === A.type
    },
    qn = function() {
        return function(A) {
            var e = A.styles,
                t = A.bounds,
                r = ae(e.borderTopLeftRadius, t.width, t.height),
                B = r[0],
                n = r[1],
                s = ae(e.borderTopRightRadius, t.width, t.height),
                o = s[0],
                i = s[1],
                Q = ae(e.borderBottomRightRadius, t.width, t.height),
                c = Q[0],
                a = Q[1],
                w = ae(e.borderBottomLeftRadius, t.width, t.height),
                g = w[0],
                u = w[1],
                U = [];
            U.push((B + o) / t.width), U.push((g + c) / t.width), U.push((n + u) / t.height), U.push((i + a) / t.height);
            var C = Math.max.apply(Math, U);
            C > 1 && (B /= C, n /= C, o /= C, i /= C, c /= C, a /= C, g /= C, u /= C);
            var l = t.width - o,
                F = t.height - a,
                h = t.width - c,
                d = t.height - u,
                f = e.borderTopWidth,
                H = e.borderRightWidth,
                p = e.borderBottomWidth,
                E = e.borderLeftWidth,
                I = we(e.paddingTop, A.bounds.width),
                y = we(e.paddingRight, A.bounds.width),
                K = we(e.paddingBottom, A.bounds.width),
                m = we(e.paddingLeft, A.bounds.width);
            this.topLeftBorderDoubleOuterBox = B > 0 || n > 0 ? jn(t.left + E / 3, t.top + f / 3, B - E / 3, n - f / 3, dn.TOP_LEFT) : new Yn(t.left + E / 3, t.top + f / 3), this.topRightBorderDoubleOuterBox = B > 0 || n > 0 ? jn(t.left + l, t.top + f / 3, o - H / 3, i - f / 3, dn.TOP_RIGHT) : new Yn(t.left + t.width - H / 3, t.top + f / 3), this.bottomRightBorderDoubleOuterBox = c > 0 || a > 0 ? jn(t.left + h, t.top + F, c - H / 3, a - p / 3, dn.BOTTOM_RIGHT) : new Yn(t.left + t.width - H / 3, t.top + t.height - p / 3), this.bottomLeftBorderDoubleOuterBox = g > 0 || u > 0 ? jn(t.left + E / 3, t.top + d, g - E / 3, u - p / 3, dn.BOTTOM_LEFT) : new Yn(t.left + E / 3, t.top + t.height - p / 3), this.topLeftBorderDoubleInnerBox = B > 0 || n > 0 ? jn(t.left + 2 * E / 3, t.top + 2 * f / 3, B - 2 * E / 3, n - 2 * f / 3, dn.TOP_LEFT) : new Yn(t.left + 2 * E / 3, t.top + 2 * f / 3), this.topRightBorderDoubleInnerBox = B > 0 || n > 0 ? jn(t.left + l, t.top + 2 * f / 3, o - 2 * H / 3, i - 2 * f / 3, dn.TOP_RIGHT) : new Yn(t.left + t.width - 2 * H / 3, t.top + 2 * f / 3), this.bottomRightBorderDoubleInnerBox = c > 0 || a > 0 ? jn(t.left + h, t.top + F, c - 2 * H / 3, a - 2 * p / 3, dn.BOTTOM_RIGHT) : new Yn(t.left + t.width - 2 * H / 3, t.top + t.height - 2 * p / 3), this.bottomLeftBorderDoubleInnerBox = g > 0 || u > 0 ? jn(t.left + 2 * E / 3, t.top + d, g - 2 * E / 3, u - 2 * p / 3, dn.BOTTOM_LEFT) : new Yn(t.left + 2 * E / 3, t.top + t.height - 2 * p / 3), this.topLeftBorderStroke = B > 0 || n > 0 ? jn(t.left + E / 2, t.top + f / 2, B - E / 2, n - f / 2, dn.TOP_LEFT) : new Yn(t.left + E / 2, t.top + f / 2), this.topRightBorderStroke = B > 0 || n > 0 ? jn(t.left + l, t.top + f / 2, o - H / 2, i - f / 2, dn.TOP_RIGHT) : new Yn(t.left + t.width - H / 2, t.top + f / 2), this.bottomRightBorderStroke = c > 0 || a > 0 ? jn(t.left + h, t.top + F, c - H / 2, a - p / 2, dn.BOTTOM_RIGHT) : new Yn(t.left + t.width - H / 2, t.top + t.height - p / 2), this.bottomLeftBorderStroke = g > 0 || u > 0 ? jn(t.left + E / 2, t.top + d, g - E / 2, u - p / 2, dn.BOTTOM_LEFT) : new Yn(t.left + E / 2, t.top + t.height - p / 2), this.topLeftBorderBox = B > 0 || n > 0 ? jn(t.left, t.top, B, n, dn.TOP_LEFT) : new Yn(t.left, t.top), this.topRightBorderBox = o > 0 || i > 0 ? jn(t.left + l, t.top, o, i, dn.TOP_RIGHT) : new Yn(t.left + t.width, t.top), this.bottomRightBorderBox = c > 0 || a > 0 ? jn(t.left + h, t.top + F, c, a, dn.BOTTOM_RIGHT) : new Yn(t.left + t.width, t.top + t.height), this.bottomLeftBorderBox = g > 0 || u > 0 ? jn(t.left, t.top + d, g, u, dn.BOTTOM_LEFT) : new Yn(t.left, t.top + t.height), this.topLeftPaddingBox = B > 0 || n > 0 ? jn(t.left + E, t.top + f, Math.max(0, B - E), Math.max(0, n - f), dn.TOP_LEFT) : new Yn(t.left + E, t.top + f), this.topRightPaddingBox = o > 0 || i > 0 ? jn(t.left + Math.min(l, t.width - H), t.top + f, l > t.width + H ? 0 : Math.max(0, o - H), Math.max(0, i - f), dn.TOP_RIGHT) : new Yn(t.left + t.width - H, t.top + f), this.bottomRightPaddingBox = c > 0 || a > 0 ? jn(t.left + Math.min(h, t.width - E), t.top + Math.min(F, t.height - p), Math.max(0, c - H), Math.max(0, a - p), dn.BOTTOM_RIGHT) : new Yn(t.left + t.width - H, t.top + t.height - p), this.bottomLeftPaddingBox = g > 0 || u > 0 ? jn(t.left + E, t.top + Math.min(d, t.height - p), Math.max(0, g - E), Math.max(0, u - p), dn.BOTTOM_LEFT) : new Yn(t.left + E, t.top + t.height - p), this.topLeftContentBox = B > 0 || n > 0 ? jn(t.left + E + m, t.top + f + I, Math.max(0, B - (E + m)), Math.max(0, n - (f + I)), dn.TOP_LEFT) : new Yn(t.left + E + m, t.top + f + I), this.topRightContentBox = o > 0 || i > 0 ? jn(t.left + Math.min(l, t.width + E + m), t.top + f + I, l > t.width + E + m ? 0 : o - E + m, i - (f + I), dn.TOP_RIGHT) : new Yn(t.left + t.width - (H + y), t.top + f + I), this.bottomRightContentBox = c > 0 || a > 0 ? jn(t.left + Math.min(h, t.width - (E + m)), t.top + Math.min(F, t.height + f + I), Math.max(0, c - (H + y)), a - (p + K), dn.BOTTOM_RIGHT) : new Yn(t.left + t.width - (H + y), t.top + t.height - (p + K)), this.bottomLeftContentBox = g > 0 || u > 0 ? jn(t.left + E + m, t.top + d, Math.max(0, g - (E + m)), u - (p + K), dn.BOTTOM_LEFT) : new Yn(t.left + E + m, t.top + t.height - (p + K))
        }
    }();
(fn = dn || (dn = {}))[fn.TOP_LEFT = 0] = "TOP_LEFT", fn[fn.TOP_RIGHT = 1] = "TOP_RIGHT", fn[fn.BOTTOM_RIGHT = 2] = "BOTTOM_RIGHT", fn[fn.BOTTOM_LEFT = 3] = "BOTTOM_LEFT";
var jn = function(A, e, t, r, B) {
        var n = (Math.sqrt(2) - 1) / 3 * 4,
            s = t * n,
            o = r * n,
            i = A + t,
            Q = e + r;
        switch (B) {
            case dn.TOP_LEFT:
                return new Zn(new Yn(A, Q), new Yn(A, Q - o), new Yn(i - s, e), new Yn(i, e));
            case dn.TOP_RIGHT:
                return new Zn(new Yn(A, e), new Yn(A + s, e), new Yn(i, Q - o), new Yn(i, Q));
            case dn.BOTTOM_RIGHT:
                return new Zn(new Yn(i, e), new Yn(i, e + o), new Yn(A + s, Q), new Yn(A, Q));
            case dn.BOTTOM_LEFT:
            default:
                return new Zn(new Yn(i, Q), new Yn(i - s, Q), new Yn(A, e + o), new Yn(A, e))
        }
    },
    zn = function(A) {
        return [A.topLeftBorderBox, A.topRightBorderBox, A.bottomRightBorderBox, A.bottomLeftBorderBox]
    },
    $n = function(A) {
        return [A.topLeftPaddingBox, A.topRightPaddingBox, A.bottomRightPaddingBox, A.bottomLeftPaddingBox]
    },
    As = function() {
        return function(A, e, t) {
            this.offsetX = A, this.offsetY = e, this.matrix = t, this.type = 0, this.target = 6
        }
    }(),
    es = function() {
        return function(A, e) {
            this.path = A, this.target = e, this.type = 1
        }
    }(),
    ts = function() {
        return function(A) {
            this.opacity = A, this.type = 2, this.target = 6
        }
    }(),
    rs = function(A) {
        return 1 === A.type
    },
    Bs = function(A, e) {
        return A.length === e.length && A.some((function(A, t) {
            return A === e[t]
        }))
    },
    ns = function() {
        return function(A) {
            this.element = A, this.inlineLevel = [], this.nonInlineLevel = [], this.negativeZIndex = [], this.zeroOrAutoZIndexOrTransformedOrOpacity = [], this.positiveZIndex = [], this.nonPositionedFloats = [], this.nonPositionedInlineLevel = []
        }
    }(),
    ss = function() {
        function A(A, e) {
            if (this.container = A, this.parent = e, this.effects = [], this.curves = new qn(this.container), this.container.styles.opacity < 1 && this.effects.push(new ts(this.container.styles.opacity)), null !== this.container.styles.transform) {
                var t = this.container.bounds.left + this.container.styles.transformOrigin[0].number,
                    r = this.container.bounds.top + this.container.styles.transformOrigin[1].number,
                    B = this.container.styles.transform;
                this.effects.push(new As(t, r, B))
            }
            if (0 !== this.container.styles.overflowX) {
                var n = zn(this.curves),
                    s = $n(this.curves);
                Bs(n, s) ? this.effects.push(new es(n, 6)) : (this.effects.push(new es(n, 2)), this.effects.push(new es(s, 4)))
            }
        }
        return A.prototype.getEffects = function(A) {
            for (var e = -1 === [2, 3].indexOf(this.container.styles.position), t = this.parent, r = this.effects.slice(0); t;) {
                var B = t.effects.filter((function(A) {
                    return !rs(A)
                }));
                if (e || 0 !== t.container.styles.position || !t.parent) {
                    if (r.unshift.apply(r, B), e = -1 === [2, 3].indexOf(t.container.styles.position), 0 !== t.container.styles.overflowX) {
                        var n = zn(t.curves),
                            s = $n(t.curves);
                        Bs(n, s) || r.unshift(new es(s, 6))
                    }
                } else r.unshift.apply(r, B);
                t = t.parent
            }
            return r.filter((function(e) {
                return hr(e.target, A)
            }))
        }, A
    }(),
    os = function(A, e, t, r) {
        A.container.elements.forEach((function(B) {
            var n = hr(B.flags, 4),
                s = hr(B.flags, 2),
                o = new ss(B, A);
            hr(B.styles.display, 2048) && r.push(o);
            var i = hr(B.flags, 8) ? [] : r;
            if (n || s) {
                var Q = n || B.styles.isPositioned() ? t : e,
                    c = new ns(o);
                if (B.styles.isPositioned() || B.styles.opacity < 1 || B.styles.isTransformed()) {
                    var a = B.styles.zIndex.order;
                    if (a < 0) {
                        var w = 0;
                        Q.negativeZIndex.some((function(A, e) {
                            return a > A.element.container.styles.zIndex.order ? (w = e, !1) : w > 0
                        })), Q.negativeZIndex.splice(w, 0, c)
                    } else if (a > 0) {
                        var g = 0;
                        Q.positiveZIndex.some((function(A, e) {
                            return a >= A.element.container.styles.zIndex.order ? (g = e + 1, !1) : g > 0
                        })), Q.positiveZIndex.splice(g, 0, c)
                    } else Q.zeroOrAutoZIndexOrTransformedOrOpacity.push(c)
                } else B.styles.isFloating() ? Q.nonPositionedFloats.push(c) : Q.nonPositionedInlineLevel.push(c);
                os(o, c, n ? c : t, i)
            } else B.styles.isInlineLevel() ? e.inlineLevel.push(o) : e.nonInlineLevel.push(o), os(o, e, t, i);
            hr(B.flags, 8) && is(B, i)
        }))
    },
    is = function(A, e) {
        for (var t = A instanceof fB ? A.start : 1, r = A instanceof fB && A.reversed, B = 0; B < e.length; B++) {
            var n = e[B];
            n.container instanceof dB && "number" == typeof n.container.value && 0 !== n.container.value && (t = n.container.value), n.listValue = ln(t, n.container.styles.listStyleType, !0), t += r ? -1 : 1
        }
    },
    Qs = function(A, e) {
        switch (e) {
            case 0:
                return as(A.topLeftBorderBox, A.topLeftPaddingBox, A.topRightBorderBox, A.topRightPaddingBox);
            case 1:
                return as(A.topRightBorderBox, A.topRightPaddingBox, A.bottomRightBorderBox, A.bottomRightPaddingBox);
            case 2:
                return as(A.bottomRightBorderBox, A.bottomRightPaddingBox, A.bottomLeftBorderBox, A.bottomLeftPaddingBox);
            default:
                return as(A.bottomLeftBorderBox, A.bottomLeftPaddingBox, A.topLeftBorderBox, A.topLeftPaddingBox)
        }
    },
    cs = function(A, e) {
        var t = [];
        return _n(A) ? t.push(A.subdivide(.5, !1)) : t.push(A), _n(e) ? t.push(e.subdivide(.5, !0)) : t.push(e), t
    },
    as = function(A, e, t, r) {
        var B = [];
        return _n(A) ? B.push(A.subdivide(.5, !1)) : B.push(A), _n(t) ? B.push(t.subdivide(.5, !0)) : B.push(t), _n(r) ? B.push(r.subdivide(.5, !0).reverse()) : B.push(r), _n(e) ? B.push(e.subdivide(.5, !1).reverse()) : B.push(e), B
    },
    ws = function(A) {
        var e = A.bounds,
            t = A.styles;
        return e.add(t.borderLeftWidth, t.borderTopWidth, -(t.borderRightWidth + t.borderLeftWidth), -(t.borderTopWidth + t.borderBottomWidth))
    },
    gs = function(A) {
        var e = A.styles,
            t = A.bounds,
            r = we(e.paddingLeft, t.width),
            B = we(e.paddingRight, t.width),
            n = we(e.paddingTop, t.width),
            s = we(e.paddingBottom, t.width);
        return t.add(r + e.borderLeftWidth, n + e.borderTopWidth, -(e.borderRightWidth + e.borderLeftWidth + r + B), -(e.borderTopWidth + e.borderBottomWidth + n + s))
    },
    us = function(A, e, t) {
        var r, B, n = (r = Fs(A.styles.backgroundOrigin, e), B = A, 0 === r ? B.bounds : 2 === r ? gs(B) : ws(B)),
            s = function(A, e) {
                return 0 === A ? e.bounds : 2 === A ? gs(e) : ws(e)
            }(Fs(A.styles.backgroundClip, e), A),
            o = ls(Fs(A.styles.backgroundSize, e), t, n),
            i = o[0],
            Q = o[1],
            c = ae(Fs(A.styles.backgroundPosition, e), n.width - i, n.height - Q);
        return [hs(Fs(A.styles.backgroundRepeat, e), c, o, n, s), Math.round(n.left + c[0]), Math.round(n.top + c[1]), i, Q]
    },
    Us = function(A) {
        return zA(A) && A.value === Ze.AUTO
    },
    Cs = function(A) {
        return "number" == typeof A
    },
    ls = function(A, e, t) {
        var r = e[0],
            B = e[1],
            n = e[2],
            s = A[0],
            o = A[1];
        if (!s) return [0, 0];
        if (se(s) && o && se(o)) return [we(s, t.width), we(o, t.height)];
        var i = Cs(n);
        if (zA(s) && (s.value === Ze.CONTAIN || s.value === Ze.COVER)) return Cs(n) ? t.width / t.height < n != (s.value === Ze.COVER) ? [t.width, t.width / n] : [t.height * n, t.height] : [t.width, t.height];
        var Q = Cs(r),
            c = Cs(B),
            a = Q || c;
        if (Us(s) && (!o || Us(o))) return Q && c ? [r, B] : i || a ? a && i ? [Q ? r : B * n, c ? B : r / n] : [Q ? r : t.width, c ? B : t.height] : [t.width, t.height];
        if (i) {
            var w = 0,
                g = 0;
            return se(s) ? w = we(s, t.width) : se(o) && (g = we(o, t.height)), Us(s) ? w = g * n : o && !Us(o) || (g = w / n), [w, g]
        }
        var u = null,
            U = null;
        if (se(s) ? u = we(s, t.width) : o && se(o) && (U = we(o, t.height)), null === u || o && !Us(o) || (U = Q && c ? u / r * B : t.height), null !== U && Us(s) && (u = Q && c ? U / B * r : t.width), null !== u && null !== U) return [u, U];
        throw new Error("Unable to calculate background-size for element")
    },
    Fs = function(A, e) {
        var t = A[e];
        return void 0 === t ? A[0] : t
    },
    hs = function(A, e, t, r, B) {
        var n = e[0],
            s = e[1],
            o = t[0],
            i = t[1];
        switch (A) {
            case 2:
                return [new Yn(Math.round(r.left), Math.round(r.top + s)), new Yn(Math.round(r.left + r.width), Math.round(r.top + s)), new Yn(Math.round(r.left + r.width), Math.round(i + r.top + s)), new Yn(Math.round(r.left), Math.round(i + r.top + s))];
            case 3:
                return [new Yn(Math.round(r.left + n), Math.round(r.top)), new Yn(Math.round(r.left + n + o), Math.round(r.top)), new Yn(Math.round(r.left + n + o), Math.round(r.height + r.top)), new Yn(Math.round(r.left + n), Math.round(r.height + r.top))];
            case 1:
                return [new Yn(Math.round(r.left + n), Math.round(r.top + s)), new Yn(Math.round(r.left + n + o), Math.round(r.top + s)), new Yn(Math.round(r.left + n + o), Math.round(r.top + s + i)), new Yn(Math.round(r.left + n), Math.round(r.top + s + i))];
            default:
                return [new Yn(Math.round(B.left), Math.round(B.top)), new Yn(Math.round(B.left + B.width), Math.round(B.top)), new Yn(Math.round(B.left + B.width), Math.round(B.height + B.top)), new Yn(Math.round(B.left), Math.round(B.height + B.top))]
        }
    },
    ds = "Hidden Text",
    fs = function() {
        function A(A) {
            this._data = {}, this._document = A
        }
        return A.prototype.parseMetrics = function(A, e) {
            var t = this._document.createElement("div"),
                r = this._document.createElement("img"),
                B = this._document.createElement("span"),
                n = this._document.body;
            t.style.visibility = "hidden", t.style.fontFamily = A, t.style.fontSize = e, t.style.margin = "0", t.style.padding = "0", t.style.whiteSpace = "nowrap", n.appendChild(t), r.src = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7", r.width = 1, r.height = 1, r.style.margin = "0", r.style.padding = "0", r.style.verticalAlign = "baseline", B.style.fontFamily = A, B.style.fontSize = e, B.style.margin = "0", B.style.padding = "0", B.appendChild(this._document.createTextNode(ds)), t.appendChild(B), t.appendChild(r);
            var s = r.offsetTop - B.offsetTop + 2;
            t.removeChild(B), t.appendChild(this._document.createTextNode(ds)), t.style.lineHeight = "normal", r.style.verticalAlign = "super";
            var o = r.offsetTop - t.offsetTop + 2;
            return n.removeChild(t), {
                baseline: s,
                middle: o
            }
        }, A.prototype.getMetrics = function(A, e) {
            var t = A + " " + e;
            return void 0 === this._data[t] && (this._data[t] = this.parseMetrics(A, e)), this._data[t]
        }, A
    }(),
    Hs = function() {
        return function(A, e) {
            this.context = A, this.options = e
        }
    }(),
    ps = function(A) {
        function t(e, t) {
            var r = A.call(this, e, t) || this;
            return r._activeEffects = [], r.canvas = t.canvas ? t.canvas : document.createElement("canvas"), r.ctx = r.canvas.getContext("2d"), t.canvas || (r.canvas.width = Math.floor(t.width * t.scale), r.canvas.height = Math.floor(t.height * t.scale), r.canvas.style.width = t.width + "px", r.canvas.style.height = t.height + "px"), r.fontMetrics = new fs(document), r.ctx.scale(r.options.scale, r.options.scale), r.ctx.translate(-t.x, -t.y), r.ctx.textBaseline = "bottom", r._activeEffects = [], r.context.logger.debug("Canvas renderer initialized (" + t.width + "x" + t.height + ") with scale " + t.scale), r
        }
        return e(t, A), t.prototype.applyEffects = function(A) {
            for (var e = this; this._activeEffects.length;) this.popEffect();
            A.forEach((function(A) {
                return e.applyEffect(A)
            }))
        }, t.prototype.applyEffect = function(A) {
            this.ctx.save(),
                function(A) {
                    return 2 === A.type
                }(A) && (this.ctx.globalAlpha = A.opacity),
                function(A) {
                    return 0 === A.type
                }(A) && (this.ctx.translate(A.offsetX, A.offsetY), this.ctx.transform(A.matrix[0], A.matrix[1], A.matrix[2], A.matrix[3], A.matrix[4], A.matrix[5]), this.ctx.translate(-A.offsetX, -A.offsetY)), rs(A) && (this.path(A.path), this.ctx.clip()), this._activeEffects.push(A)
        }, t.prototype.popEffect = function() {
            this._activeEffects.pop(), this.ctx.restore()
        }, t.prototype.renderStack = function(A) {
            return r(this, 0, void 0, (function() {
                return B(this, (function(e) {
                    switch (e.label) {
                        case 0:
                            return A.element.container.styles.isVisible() ? [4, this.renderStackContent(A)] : [3, 2];
                        case 1:
                            e.sent(), e.label = 2;
                        case 2:
                            return [2]
                    }
                }))
            }))
        }, t.prototype.renderNode = function(A) {
            return r(this, 0, void 0, (function() {
                return B(this, (function(e) {
                    switch (e.label) {
                        case 0:
                            return hr(A.container.flags, 16), A.container.styles.isVisible() ? [4, this.renderNodeBackgroundAndBorders(A)] : [3, 3];
                        case 1:
                            return e.sent(), [4, this.renderNodeContent(A)];
                        case 2:
                            e.sent(), e.label = 3;
                        case 3:
                            return [2]
                    }
                }))
            }))
        }, t.prototype.renderTextWithLetterSpacing = function(A, e, t) {
            var r = this;
            0 === e ? this.ctx.fillText(A.text, A.bounds.left, A.bounds.top + t) : QB(A.text).reduce((function(e, B) {
                return r.ctx.fillText(B, e, A.bounds.top + t), e + r.ctx.measureText(B).width
            }), A.bounds.left)
        }, t.prototype.createFontStyle = function(A) {
            var e = A.fontVariant.filter((function(A) {
                    return "normal" === A || "small-caps" === A
                })).join(""),
                t = ms(A.fontFamily).join(", "),
                r = qA(A.fontSize) ? "" + A.fontSize.number + A.fontSize.unit : A.fontSize.number + "px";
            return [
                [A.fontStyle, e, A.fontWeight, r, t].join(" "), t, r
            ]
        }, t.prototype.renderTextNode = function(A, e) {
            return r(this, 0, void 0, (function() {
                var t, r, n, s, o, i, Q, c, a = this;
                return B(this, (function(B) {
                    return t = this.createFontStyle(e), r = t[0], n = t[1], s = t[2], this.ctx.font = r, this.ctx.direction = 1 === e.direction ? "rtl" : "ltr", this.ctx.textAlign = "left", this.ctx.textBaseline = "alphabetic", o = this.fontMetrics.getMetrics(n, s), i = o.baseline, Q = o.middle, c = e.paintOrder, A.textBounds.forEach((function(A) {
                        c.forEach((function(t) {
                            switch (t) {
                                case 0:
                                    a.ctx.fillStyle = fe(e.color), a.renderTextWithLetterSpacing(A, e.letterSpacing, i);
                                    var r = e.textShadow;
                                    r.length && A.text.trim().length && (r.slice(0).reverse().forEach((function(t) {
                                        a.ctx.shadowColor = fe(t.color), a.ctx.shadowOffsetX = t.offsetX.number * a.options.scale, a.ctx.shadowOffsetY = t.offsetY.number * a.options.scale, a.ctx.shadowBlur = t.blur.number, a.renderTextWithLetterSpacing(A, e.letterSpacing, i)
                                    })), a.ctx.shadowColor = "", a.ctx.shadowOffsetX = 0, a.ctx.shadowOffsetY = 0, a.ctx.shadowBlur = 0), e.textDecorationLine.length && (a.ctx.fillStyle = fe(e.textDecorationColor || e.color), e.textDecorationLine.forEach((function(e) {
                                        switch (e) {
                                            case 1:
                                                a.ctx.fillRect(A.bounds.left, Math.round(A.bounds.top + i), A.bounds.width, 1);
                                                break;
                                            case 2:
                                                a.ctx.fillRect(A.bounds.left, Math.round(A.bounds.top), A.bounds.width, 1);
                                                break;
                                            case 3:
                                                a.ctx.fillRect(A.bounds.left, Math.ceil(A.bounds.top + Q), A.bounds.width, 1)
                                        }
                                    })));
                                    break;
                                case 1:
                                    e.webkitTextStrokeWidth && A.text.trim().length && (a.ctx.strokeStyle = fe(e.webkitTextStrokeColor), a.ctx.lineWidth = e.webkitTextStrokeWidth, a.ctx.lineJoin = window.chrome ? "miter" : "round", a.ctx.strokeText(A.text, A.bounds.left, A.bounds.top + i)), a.ctx.strokeStyle = "", a.ctx.lineWidth = 0, a.ctx.lineJoin = "miter"
                            }
                        }))
                    })), [2]
                }))
            }))
        }, t.prototype.renderReplacedElement = function(A, e, t) {
            if (t && A.intrinsicWidth > 0 && A.intrinsicHeight > 0) {
                var r = gs(A),
                    B = $n(e);
                this.path(B), this.ctx.save(), this.ctx.clip(), this.ctx.drawImage(t, 0, 0, A.intrinsicWidth, A.intrinsicHeight, r.left, r.top, r.width, r.height), this.ctx.restore()
            }
        }, t.prototype.renderNodeContent = function(A) {
            return r(this, 0, void 0, (function() {
                var e, r, s, o, i, Q, c, a, w, g, u, U, C, l, F, h, d, f;
                return B(this, (function(B) {
                    switch (B.label) {
                        case 0:
                            this.applyEffects(A.getEffects(4)), e = A.container, r = A.curves, s = e.styles, o = 0, i = e.textNodes, B.label = 1;
                        case 1:
                            return o < i.length ? (Q = i[o], [4, this.renderTextNode(Q, s)]) : [3, 4];
                        case 2:
                            B.sent(), B.label = 3;
                        case 3:
                            return o++, [3, 1];
                        case 4:
                            if (!(e instanceof lB)) return [3, 8];
                            B.label = 5;
                        case 5:
                            return B.trys.push([5, 7, , 8]), [4, this.context.cache.match(e.src)];
                        case 6:
                            return F = B.sent(), this.renderReplacedElement(e, r, F), [3, 8];
                        case 7:
                            return B.sent(), this.context.logger.error("Error loading image " + e.src), [3, 8];
                        case 8:
                            if (e instanceof FB && this.renderReplacedElement(e, r, e.canvas), !(e instanceof hB)) return [3, 12];
                            B.label = 9;
                        case 9:
                            return B.trys.push([9, 11, , 12]), [4, this.context.cache.match(e.svg)];
                        case 10:
                            return F = B.sent(), this.renderReplacedElement(e, r, F), [3, 12];
                        case 11:
                            return B.sent(), this.context.logger.error("Error loading svg " + e.svg.substring(0, 255)), [3, 12];
                        case 12:
                            return e instanceof vB && e.tree ? [4, new t(this.context, {
                                scale: this.options.scale,
                                backgroundColor: e.backgroundColor,
                                x: 0,
                                y: 0,
                                width: e.width,
                                height: e.height
                            }).render(e.tree)] : [3, 14];
                        case 13:
                            c = B.sent(), e.width && e.height && this.ctx.drawImage(c, 0, 0, e.width, e.height, e.bounds.left, e.bounds.top, e.bounds.width, e.bounds.height), B.label = 14;
                        case 14:
                            if (e instanceof mB && (a = Math.min(e.bounds.width, e.bounds.height), e.type === EB ? e.checked && (this.ctx.save(), this.path([new Yn(e.bounds.left + .39363 * a, e.bounds.top + .79 * a), new Yn(e.bounds.left + .16 * a, e.bounds.top + .5549 * a), new Yn(e.bounds.left + .27347 * a, e.bounds.top + .44071 * a), new Yn(e.bounds.left + .39694 * a, e.bounds.top + .5649 * a), new Yn(e.bounds.left + .72983 * a, e.bounds.top + .23 * a), new Yn(e.bounds.left + .84 * a, e.bounds.top + .34085 * a), new Yn(e.bounds.left + .39363 * a, e.bounds.top + .79 * a)]), this.ctx.fillStyle = fe(KB), this.ctx.fill(), this.ctx.restore()) : e.type === IB && e.checked && (this.ctx.save(), this.ctx.beginPath(), this.ctx.arc(e.bounds.left + a / 2, e.bounds.top + a / 2, a / 4, 0, 2 * Math.PI, !0), this.ctx.fillStyle = fe(KB), this.ctx.fill(), this.ctx.restore())), Es(e) && e.value.length) {
                                switch (w = this.createFontStyle(s), d = w[0], g = w[1], u = this.fontMetrics.getMetrics(d, g).baseline, this.ctx.font = d, this.ctx.fillStyle = fe(s.color), this.ctx.textBaseline = "alphabetic", this.ctx.textAlign = ys(e.styles.textAlign), f = gs(e), U = 0, e.styles.textAlign) {
                                    case 1:
                                        U += f.width / 2;
                                        break;
                                    case 2:
                                        U += f.width
                                }
                                C = f.add(U, 0, 0, -f.height / 2 + 1), this.ctx.save(), this.path([new Yn(f.left, f.top), new Yn(f.left + f.width, f.top), new Yn(f.left + f.width, f.top + f.height), new Yn(f.left, f.top + f.height)]), this.ctx.clip(), this.renderTextWithLetterSpacing(new sB(e.value, C), s.letterSpacing, u), this.ctx.restore(), this.ctx.textBaseline = "alphabetic", this.ctx.textAlign = "left"
                            }
                            if (!hr(e.styles.display, 2048)) return [3, 20];
                            if (null === e.styles.listStyleImage) return [3, 19];
                            if (0 !== (l = e.styles.listStyleImage).type) return [3, 18];
                            F = void 0, h = l.url, B.label = 15;
                        case 15:
                            return B.trys.push([15, 17, , 18]), [4, this.context.cache.match(h)];
                        case 16:
                            return F = B.sent(), this.ctx.drawImage(F, e.bounds.left - (F.width + 10), e.bounds.top), [3, 18];
                        case 17:
                            return B.sent(), this.context.logger.error("Error loading list-style-image " + h), [3, 18];
                        case 18:
                            return [3, 20];
                        case 19:
                            A.listValue && -1 !== e.styles.listStyleType && (d = this.createFontStyle(s)[0], this.ctx.font = d, this.ctx.fillStyle = fe(s.color), this.ctx.textBaseline = "middle", this.ctx.textAlign = "right", f = new n(e.bounds.left, e.bounds.top + we(e.styles.paddingTop, e.bounds.width), e.bounds.width, Tt(s.lineHeight, s.fontSize.number) / 2 + 1), this.renderTextWithLetterSpacing(new sB(A.listValue, f), s.letterSpacing, Tt(s.lineHeight, s.fontSize.number) / 2 + 2), this.ctx.textBaseline = "bottom", this.ctx.textAlign = "left"), B.label = 20;
                        case 20:
                            return [2]
                    }
                }))
            }))
        }, t.prototype.renderStackContent = function(A) {
            return r(this, 0, void 0, (function() {
                var e, t, r, n, s, o, i, Q, c, a, w, g, u, U, C;
                return B(this, (function(B) {
                    switch (B.label) {
                        case 0:
                            return hr(A.element.container.flags, 16), [4, this.renderNodeBackgroundAndBorders(A.element)];
                        case 1:
                            B.sent(), e = 0, t = A.negativeZIndex, B.label = 2;
                        case 2:
                            return e < t.length ? (C = t[e], [4, this.renderStack(C)]) : [3, 5];
                        case 3:
                            B.sent(), B.label = 4;
                        case 4:
                            return e++, [3, 2];
                        case 5:
                            return [4, this.renderNodeContent(A.element)];
                        case 6:
                            B.sent(), r = 0, n = A.nonInlineLevel, B.label = 7;
                        case 7:
                            return r < n.length ? (C = n[r], [4, this.renderNode(C)]) : [3, 10];
                        case 8:
                            B.sent(), B.label = 9;
                        case 9:
                            return r++, [3, 7];
                        case 10:
                            s = 0, o = A.nonPositionedFloats, B.label = 11;
                        case 11:
                            return s < o.length ? (C = o[s], [4, this.renderStack(C)]) : [3, 14];
                        case 12:
                            B.sent(), B.label = 13;
                        case 13:
                            return s++, [3, 11];
                        case 14:
                            i = 0, Q = A.nonPositionedInlineLevel, B.label = 15;
                        case 15:
                            return i < Q.length ? (C = Q[i], [4, this.renderStack(C)]) : [3, 18];
                        case 16:
                            B.sent(), B.label = 17;
                        case 17:
                            return i++, [3, 15];
                        case 18:
                            c = 0, a = A.inlineLevel, B.label = 19;
                        case 19:
                            return c < a.length ? (C = a[c], [4, this.renderNode(C)]) : [3, 22];
                        case 20:
                            B.sent(), B.label = 21;
                        case 21:
                            return c++, [3, 19];
                        case 22:
                            w = 0, g = A.zeroOrAutoZIndexOrTransformedOrOpacity, B.label = 23;
                        case 23:
                            return w < g.length ? (C = g[w], [4, this.renderStack(C)]) : [3, 26];
                        case 24:
                            B.sent(), B.label = 25;
                        case 25:
                            return w++, [3, 23];
                        case 26:
                            u = 0, U = A.positiveZIndex, B.label = 27;
                        case 27:
                            return u < U.length ? (C = U[u], [4, this.renderStack(C)]) : [3, 30];
                        case 28:
                            B.sent(), B.label = 29;
                        case 29:
                            return u++, [3, 27];
                        case 30:
                            return [2]
                    }
                }))
            }))
        }, t.prototype.mask = function(A) {
            this.ctx.beginPath(), this.ctx.moveTo(0, 0), this.ctx.lineTo(this.canvas.width, 0), this.ctx.lineTo(this.canvas.width, this.canvas.height), this.ctx.lineTo(0, this.canvas.height), this.ctx.lineTo(0, 0), this.formatPath(A.slice(0).reverse()), this.ctx.closePath()
        }, t.prototype.path = function(A) {
            this.ctx.beginPath(), this.formatPath(A), this.ctx.closePath()
        }, t.prototype.formatPath = function(A) {
            var e = this;
            A.forEach((function(A, t) {
                var r = _n(A) ? A.start : A;
                0 === t ? e.ctx.moveTo(r.x, r.y) : e.ctx.lineTo(r.x, r.y), _n(A) && e.ctx.bezierCurveTo(A.startControl.x, A.startControl.y, A.endControl.x, A.endControl.y, A.end.x, A.end.y)
            }))
        }, t.prototype.renderRepeat = function(A, e, t, r) {
            this.path(A), this.ctx.fillStyle = e, this.ctx.translate(t, r), this.ctx.fill(), this.ctx.translate(-t, -r)
        }, t.prototype.resizeImage = function(A, e, t) {
            var r;
            if (A.width === e && A.height === t) return A;
            var B = (null !== (r = this.canvas.ownerDocument) && void 0 !== r ? r : document).createElement("canvas");
            return B.width = Math.max(1, e), B.height = Math.max(1, t), B.getContext("2d").drawImage(A, 0, 0, A.width, A.height, 0, 0, e, t), B
        }, t.prototype.renderBackgroundImage = function(A) {
            return r(this, 0, void 0, (function() {
                var e, t, r, n, s, o;
                return B(this, (function(i) {
                    switch (i.label) {
                        case 0:
                            e = A.styles.backgroundImage.length - 1, t = function(t) {
                                var n, s, o, i, Q, c, a, w, g, u, U, C, l, F, h, d, f, H, p, E, I, y, K, m, L, b, v, D, x, M, S;
                                return B(this, (function(B) {
                                    switch (B.label) {
                                        case 0:
                                            if (0 !== t.type) return [3, 5];
                                            n = void 0, s = t.url, B.label = 1;
                                        case 1:
                                            return B.trys.push([1, 3, , 4]), [4, r.context.cache.match(s)];
                                        case 2:
                                            return n = B.sent(), [3, 4];
                                        case 3:
                                            return B.sent(), r.context.logger.error("Error loading background-image " + s), [3, 4];
                                        case 4:
                                            return n && (o = us(A, e, [n.width, n.height, n.width / n.height]), d = o[0], y = o[1], K = o[2], p = o[3], E = o[4], F = r.ctx.createPattern(r.resizeImage(n, p, E), "repeat"), r.renderRepeat(d, F, y, K)), [3, 6];
                                        case 5:
                                            1 === t.type ? (i = us(A, e, [null, null, null]), d = i[0], y = i[1], K = i[2], p = i[3], E = i[4], Q = Me(t.angle, p, E), c = Q[0], a = Q[1], w = Q[2], g = Q[3], u = Q[4], (U = document.createElement("canvas")).width = p, U.height = E, C = U.getContext("2d"), l = C.createLinearGradient(a, g, w, u), xe(t.stops, c).forEach((function(A) {
                                                return l.addColorStop(A.stop, fe(A.color))
                                            })), C.fillStyle = l, C.fillRect(0, 0, p, E), p > 0 && E > 0 && (F = r.ctx.createPattern(U, "repeat"), r.renderRepeat(d, F, y, K))) : function(A) {
                                                return 2 === A.type
                                            }(t) && (h = us(A, e, [null, null, null]), d = h[0], f = h[1], H = h[2], p = h[3], E = h[4], I = 0 === t.position.length ? [Qe] : t.position, y = we(I[0], p), K = we(I[I.length - 1], E), m = function(A, e, t, r, B) {
                                                var n = 0,
                                                    s = 0;
                                                switch (A.size) {
                                                    case 0:
                                                        0 === A.shape ? n = s = Math.min(Math.abs(e), Math.abs(e - r), Math.abs(t), Math.abs(t - B)) : 1 === A.shape && (n = Math.min(Math.abs(e), Math.abs(e - r)), s = Math.min(Math.abs(t), Math.abs(t - B)));
                                                        break;
                                                    case 2:
                                                        if (0 === A.shape) n = s = Math.min(Se(e, t), Se(e, t - B), Se(e - r, t), Se(e - r, t - B));
                                                        else if (1 === A.shape) {
                                                            var o = Math.min(Math.abs(t), Math.abs(t - B)) / Math.min(Math.abs(e), Math.abs(e - r)),
                                                                i = Te(r, B, e, t, !0),
                                                                Q = i[0],
                                                                c = i[1];
                                                            s = o * (n = Se(Q - e, (c - t) / o))
                                                        }
                                                        break;
                                                    case 1:
                                                        0 === A.shape ? n = s = Math.max(Math.abs(e), Math.abs(e - r), Math.abs(t), Math.abs(t - B)) : 1 === A.shape && (n = Math.max(Math.abs(e), Math.abs(e - r)), s = Math.max(Math.abs(t), Math.abs(t - B)));
                                                        break;
                                                    case 3:
                                                        if (0 === A.shape) n = s = Math.max(Se(e, t), Se(e, t - B), Se(e - r, t), Se(e - r, t - B));
                                                        else if (1 === A.shape) {
                                                            o = Math.max(Math.abs(t), Math.abs(t - B)) / Math.max(Math.abs(e), Math.abs(e - r));
                                                            var a = Te(r, B, e, t, !1);
                                                            Q = a[0], c = a[1], s = o * (n = Se(Q - e, (c - t) / o))
                                                        }
                                                }
                                                return Array.isArray(A.size) && (n = we(A.size[0], r), s = 2 === A.size.length ? we(A.size[1], B) : n), [n, s]
                                            }(t, y, K, p, E), L = m[0], b = m[1], L > 0 && b > 0 && (v = r.ctx.createRadialGradient(f + y, H + K, 0, f + y, H + K, L), xe(t.stops, 2 * L).forEach((function(A) {
                                                return v.addColorStop(A.stop, fe(A.color))
                                            })), r.path(d), r.ctx.fillStyle = v, L !== b ? (D = A.bounds.left + .5 * A.bounds.width, x = A.bounds.top + .5 * A.bounds.height, S = 1 / (M = b / L), r.ctx.save(), r.ctx.translate(D, x), r.ctx.transform(1, 0, 0, M, 0, 0), r.ctx.translate(-D, -x), r.ctx.fillRect(f, S * (H - x) + x, p, E * S), r.ctx.restore()) : r.ctx.fill())), B.label = 6;
                                        case 6:
                                            return e--, [2]
                                    }
                                }))
                            }, r = this, n = 0, s = A.styles.backgroundImage.slice(0).reverse(), i.label = 1;
                        case 1:
                            return n < s.length ? (o = s[n], [5, t(o)]) : [3, 4];
                        case 2:
                            i.sent(), i.label = 3;
                        case 3:
                            return n++, [3, 1];
                        case 4:
                            return [2]
                    }
                }))
            }))
        }, t.prototype.renderSolidBorder = function(A, e, t) {
            return r(this, 0, void 0, (function() {
                return B(this, (function(r) {
                    return this.path(Qs(t, e)), this.ctx.fillStyle = fe(A), this.ctx.fill(), [2]
                }))
            }))
        }, t.prototype.renderDoubleBorder = function(A, e, t, n) {
            return r(this, 0, void 0, (function() {
                var r, s;
                return B(this, (function(B) {
                    switch (B.label) {
                        case 0:
                            return e < 3 ? [4, this.renderSolidBorder(A, t, n)] : [3, 2];
                        case 1:
                            return B.sent(), [2];
                        case 2:
                            return r = function(A, e) {
                                switch (e) {
                                    case 0:
                                        return as(A.topLeftBorderBox, A.topLeftBorderDoubleOuterBox, A.topRightBorderBox, A.topRightBorderDoubleOuterBox);
                                    case 1:
                                        return as(A.topRightBorderBox, A.topRightBorderDoubleOuterBox, A.bottomRightBorderBox, A.bottomRightBorderDoubleOuterBox);
                                    case 2:
                                        return as(A.bottomRightBorderBox, A.bottomRightBorderDoubleOuterBox, A.bottomLeftBorderBox, A.bottomLeftBorderDoubleOuterBox);
                                    default:
                                        return as(A.bottomLeftBorderBox, A.bottomLeftBorderDoubleOuterBox, A.topLeftBorderBox, A.topLeftBorderDoubleOuterBox)
                                }
                            }(n, t), this.path(r), this.ctx.fillStyle = fe(A), this.ctx.fill(), s = function(A, e) {
                                switch (e) {
                                    case 0:
                                        return as(A.topLeftBorderDoubleInnerBox, A.topLeftPaddingBox, A.topRightBorderDoubleInnerBox, A.topRightPaddingBox);
                                    case 1:
                                        return as(A.topRightBorderDoubleInnerBox, A.topRightPaddingBox, A.bottomRightBorderDoubleInnerBox, A.bottomRightPaddingBox);
                                    case 2:
                                        return as(A.bottomRightBorderDoubleInnerBox, A.bottomRightPaddingBox, A.bottomLeftBorderDoubleInnerBox, A.bottomLeftPaddingBox);
                                    default:
                                        return as(A.bottomLeftBorderDoubleInnerBox, A.bottomLeftPaddingBox, A.topLeftBorderDoubleInnerBox, A.topLeftPaddingBox)
                                }
                            }(n, t), this.path(s), this.ctx.fill(), [2]
                    }
                }))
            }))
        }, t.prototype.renderNodeBackgroundAndBorders = function(A) {
            return r(this, 0, void 0, (function() {
                var e, t, r, n, s, o, i, Q, c = this;
                return B(this, (function(B) {
                    switch (B.label) {
                        case 0:
                            return this.applyEffects(A.getEffects(2)), e = A.container.styles, t = !de(e.backgroundColor) || e.backgroundImage.length, r = [{
                                style: e.borderTopStyle,
                                color: e.borderTopColor,
                                width: e.borderTopWidth
                            }, {
                                style: e.borderRightStyle,
                                color: e.borderRightColor,
                                width: e.borderRightWidth
                            }, {
                                style: e.borderBottomStyle,
                                color: e.borderBottomColor,
                                width: e.borderBottomWidth
                            }, {
                                style: e.borderLeftStyle,
                                color: e.borderLeftColor,
                                width: e.borderLeftWidth
                            }], n = Is(Fs(e.backgroundClip, 0), A.curves), t || e.boxShadow.length ? (this.ctx.save(), this.path(n), this.ctx.clip(), de(e.backgroundColor) || (this.ctx.fillStyle = fe(e.backgroundColor), this.ctx.fill()), [4, this.renderBackgroundImage(A.container)]) : [3, 2];
                        case 1:
                            B.sent(), this.ctx.restore(), e.boxShadow.slice(0).reverse().forEach((function(e) {
                                c.ctx.save();
                                var t, r, B, n, s, o = zn(A.curves),
                                    i = e.inset ? 0 : 1e4,
                                    Q = (t = o, r = -i + (e.inset ? 1 : -1) * e.spread.number, B = (e.inset ? 1 : -1) * e.spread.number, n = e.spread.number * (e.inset ? -2 : 2), s = e.spread.number * (e.inset ? -2 : 2), t.map((function(A, e) {
                                        switch (e) {
                                            case 0:
                                                return A.add(r, B);
                                            case 1:
                                                return A.add(r + n, B);
                                            case 2:
                                                return A.add(r + n, B + s);
                                            case 3:
                                                return A.add(r, B + s)
                                        }
                                        return A
                                    })));
                                e.inset ? (c.path(o), c.ctx.clip(), c.mask(Q)) : (c.mask(o), c.ctx.clip(), c.path(Q)), c.ctx.shadowOffsetX = e.offsetX.number + i, c.ctx.shadowOffsetY = e.offsetY.number, c.ctx.shadowColor = fe(e.color), c.ctx.shadowBlur = e.blur.number, c.ctx.fillStyle = e.inset ? fe(e.color) : "rgba(0,0,0,1)", c.ctx.fill(), c.ctx.restore()
                            })), B.label = 2;
                        case 2:
                            s = 0, o = 0, i = r, B.label = 3;
                        case 3:
                            return o < i.length ? 0 !== (Q = i[o]).style && !de(Q.color) && Q.width > 0 ? 2 !== Q.style ? [3, 5] : [4, this.renderDashedDottedBorder(Q.color, Q.width, s, A.curves, 2)] : [3, 11] : [3, 13];
                        case 4:
                            return B.sent(), [3, 11];
                        case 5:
                            return 3 !== Q.style ? [3, 7] : [4, this.renderDashedDottedBorder(Q.color, Q.width, s, A.curves, 3)];
                        case 6:
                            return B.sent(), [3, 11];
                        case 7:
                            return 4 !== Q.style ? [3, 9] : [4, this.renderDoubleBorder(Q.color, Q.width, s, A.curves)];
                        case 8:
                            return B.sent(), [3, 11];
                        case 9:
                            return [4, this.renderSolidBorder(Q.color, s, A.curves)];
                        case 10:
                            B.sent(), B.label = 11;
                        case 11:
                            s++, B.label = 12;
                        case 12:
                            return o++, [3, 3];
                        case 13:
                            return [2]
                    }
                }))
            }))
        }, t.prototype.renderDashedDottedBorder = function(A, e, t, n, s) {
            return r(this, 0, void 0, (function() {
                var r, o, i, Q, c, a, w, g, u, U, C, l, F, h, d, f;
                return B(this, (function(B) {
                    return this.ctx.save(), r = function(A, e) {
                        switch (e) {
                            case 0:
                                return cs(A.topLeftBorderStroke, A.topRightBorderStroke);
                            case 1:
                                return cs(A.topRightBorderStroke, A.bottomRightBorderStroke);
                            case 2:
                                return cs(A.bottomRightBorderStroke, A.bottomLeftBorderStroke);
                            default:
                                return cs(A.bottomLeftBorderStroke, A.topLeftBorderStroke)
                        }
                    }(n, t), o = Qs(n, t), 2 === s && (this.path(o), this.ctx.clip()), _n(o[0]) ? (i = o[0].start.x, Q = o[0].start.y) : (i = o[0].x, Q = o[0].y), _n(o[1]) ? (c = o[1].end.x, a = o[1].end.y) : (c = o[1].x, a = o[1].y), w = 0 === t || 2 === t ? Math.abs(i - c) : Math.abs(Q - a), this.ctx.beginPath(), 3 === s ? this.formatPath(r) : this.formatPath(o.slice(0, 2)), g = e < 3 ? 3 * e : 2 * e, u = e < 3 ? 2 * e : e, 3 === s && (g = e, u = e), U = !0, w <= 2 * g ? U = !1 : w <= 2 * g + u ? (g *= C = w / (2 * g + u), u *= C) : (l = Math.floor((w + u) / (g + u)), F = (w - l * g) / (l - 1), u = (h = (w - (l + 1) * g) / l) <= 0 || Math.abs(u - F) < Math.abs(u - h) ? F : h), U && (3 === s ? this.ctx.setLineDash([0, g + u]) : this.ctx.setLineDash([g, u])), 3 === s ? (this.ctx.lineCap = "round", this.ctx.lineWidth = e) : this.ctx.lineWidth = 2 * e + 1.1, this.ctx.strokeStyle = fe(A), this.ctx.stroke(), this.ctx.setLineDash([]), 2 === s && (_n(o[0]) && (d = o[3], f = o[0], this.ctx.beginPath(), this.formatPath([new Yn(d.end.x, d.end.y), new Yn(f.start.x, f.start.y)]), this.ctx.stroke()), _n(o[1]) && (d = o[1], f = o[2], this.ctx.beginPath(), this.formatPath([new Yn(d.end.x, d.end.y), new Yn(f.start.x, f.start.y)]), this.ctx.stroke())), this.ctx.restore(), [2]
                }))
            }))
        }, t.prototype.render = function(A) {
            return r(this, 0, void 0, (function() {
                var e;
                return B(this, (function(t) {
                    switch (t.label) {
                        case 0:
                            return this.options.backgroundColor && (this.ctx.fillStyle = fe(this.options.backgroundColor), this.ctx.fillRect(this.options.x, this.options.y, this.options.width, this.options.height)), r = new ss(A, null), B = new ns(r), os(r, B, B, n = []), is(r.container, n), e = B, [4, this.renderStack(e)];
                        case 1:
                            return t.sent(), this.applyEffects([]), [2, this.canvas]
                    }
                    var r, B, n
                }))
            }))
        }, t
    }(Hs),
    Es = function(A) {
        return A instanceof bB || (A instanceof LB || A instanceof mB && A.type !== IB && A.type !== EB)
    },
    Is = function(A, e) {
        switch (A) {
            case 0:
                return zn(e);
            case 2:
                return function(A) {
                    return [A.topLeftContentBox, A.topRightContentBox, A.bottomRightContentBox, A.bottomLeftContentBox]
                }(e);
            default:
                return $n(e)
        }
    },
    ys = function(A) {
        switch (A) {
            case 1:
                return "center";
            case 2:
                return "right";
            default:
                return "left"
        }
    },
    Ks = ["-apple-system", "system-ui"],
    ms = function(A) {
        return /iPhone OS 15_(0|1)/.test(window.navigator.userAgent) ? A.filter((function(A) {
            return -1 === Ks.indexOf(A)
        })) : A
    },
    Ls = function(A) {
        function t(e, t) {
            var r = A.call(this, e, t) || this;
            return r.canvas = t.canvas ? t.canvas : document.createElement("canvas"), r.ctx = r.canvas.getContext("2d"), r.options = t, r.canvas.width = Math.floor(t.width * t.scale), r.canvas.height = Math.floor(t.height * t.scale), r.canvas.style.width = t.width + "px", r.canvas.style.height = t.height + "px", r.ctx.scale(r.options.scale, r.options.scale), r.ctx.translate(-t.x, -t.y), r.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized (" + t.width + "x" + t.height + " at " + t.x + "," + t.y + ") with scale " + t.scale), r
        }
        return e(t, A), t.prototype.render = function(A) {
            return r(this, 0, void 0, (function() {
                var e, t;
                return B(this, (function(r) {
                    switch (r.label) {
                        case 0:
                            return e = rB(this.options.width * this.options.scale, this.options.height * this.options.scale, this.options.scale, this.options.scale, A), [4, bs(e)];
                        case 1:
                            return t = r.sent(), this.options.backgroundColor && (this.ctx.fillStyle = fe(this.options.backgroundColor), this.ctx.fillRect(0, 0, this.options.width * this.options.scale, this.options.height * this.options.scale)), this.ctx.drawImage(t, -this.options.x * this.options.scale, -this.options.y * this.options.scale), [2, this.canvas]
                    }
                }))
            }))
        }, t
    }(Hs),
    bs = function(A) {
        return new Promise((function(e, t) {
            var r = new Image;
            r.onload = function() {
                e(r)
            }, r.onerror = t, r.src = "data:image/svg+xml;charset=utf-8," + encodeURIComponent((new XMLSerializer).serializeToString(A))
        }))
    },
    vs = function() {
        function A(A) {
            var e = A.id,
                t = A.enabled;
            this.id = e, this.enabled = t, this.start = Date.now()
        }
        return A.prototype.debug = function() {
            for (var A = [], e = 0; e < arguments.length; e++) A[e] = arguments[e];
            this.enabled && ("undefined" != typeof window && window.console && "function" == typeof console.debug || this.info.apply(this, A))
        }, A.prototype.getTime = function() {
            return Date.now() - this.start
        }, A.prototype.info = function() {
            for (var A = [], e = 0; e < arguments.length; e++) A[e] = arguments[e];
            this.enabled && "undefined" != typeof window && window.console && console.info
        }, A.prototype.warn = function() {
            for (var A = [], e = 0; e < arguments.length; e++) A[e] = arguments[e];
            this.enabled && ("undefined" != typeof window && window.console && "function" == typeof console.warn || this.info.apply(this, A))
        }, A.prototype.error = function() {
            for (var A = [], e = 0; e < arguments.length; e++) A[e] = arguments[e];
            this.enabled && ("undefined" != typeof window && window.console && "function" == typeof console.error || this.info.apply(this, A))
        }, A.instances = {}, A
    }(),
    Ds = function() {
        function A(e, t) {
            var r;
            this.windowBounds = t, this.instanceName = "#" + A.instanceCount++, this.logger = new vs({
                id: this.instanceName,
                enabled: e.logging
            }), this.cache = null !== (r = e.cache) && void 0 !== r ? r : new Gn(this, e)
        }
        return A.instanceCount = 1, A
    }(),
    xs = function(A, e) {
        return void 0 === e && (e = {}), Ms(A, e)
    };
"undefined" != typeof window && Tn.setContext(window);
var Ms = function(A, e) {
        return r(void 0, 0, void 0, (function() {
            var r, o, i, Q, c, a, w, g, u, U, C, l, F, h, d, f, H, p, E, I, y, K, m, L, b, v, D, x, M, S, T, G, O, V, k, R, N, P;
            return B(this, (function(B) {
                switch (B.label) {
                    case 0:
                        if (!A || "object" != typeof A) return [2, Promise.reject("Invalid element provided as first argument")];
                        if (!(r = A.ownerDocument)) throw new Error("Element is not attached to a Document");
                        if (!(o = r.defaultView)) throw new Error("Document is not attached to a Window");
                        return i = {
                            allowTaint: null !== (K = e.allowTaint) && void 0 !== K && K,
                            imageTimeout: null !== (m = e.imageTimeout) && void 0 !== m ? m : 15e3,
                            proxy: e.proxy,
                            useCORS: null !== (L = e.useCORS) && void 0 !== L && L
                        }, Q = t({
                            logging: null === (b = e.logging) || void 0 === b || b,
                            cache: e.cache
                        }, i), c = {
                            windowWidth: null !== (v = e.windowWidth) && void 0 !== v ? v : o.innerWidth,
                            windowHeight: null !== (D = e.windowHeight) && void 0 !== D ? D : o.innerHeight,
                            scrollX: null !== (x = e.scrollX) && void 0 !== x ? x : o.pageXOffset,
                            scrollY: null !== (M = e.scrollY) && void 0 !== M ? M : o.pageYOffset
                        }, a = new n(c.scrollX, c.scrollY, c.windowWidth, c.windowHeight), w = new Ds(Q, a), g = null !== (S = e.foreignObjectRendering) && void 0 !== S && S, u = {
                            allowTaint: null !== (T = e.allowTaint) && void 0 !== T && T,
                            onclone: e.onclone,
                            ignoreElements: e.ignoreElements,
                            inlineImages: g,
                            copyStyles: g
                        }, w.logger.debug("Starting document clone with size " + a.width + "x" + a.height + " scrolled to " + -a.left + "," + -a.top), U = new hn(w, A, u), (C = U.clonedReferenceElement) ? [4, U.toIFrame(r, a)] : [2, Promise.reject("Unable to find element in cloned iframe")];
                    case 1:
                        return l = B.sent(), F = YB(C) || "HTML" === C.tagName ? function(A) {
                            var e = A.body,
                                t = A.documentElement;
                            if (!e || !t) throw new Error("Unable to get document size");
                            var r = Math.max(Math.max(e.scrollWidth, t.scrollWidth), Math.max(e.offsetWidth, t.offsetWidth), Math.max(e.clientWidth, t.clientWidth)),
                                B = Math.max(Math.max(e.scrollHeight, t.scrollHeight), Math.max(e.offsetHeight, t.offsetHeight), Math.max(e.clientHeight, t.clientHeight));
                            return new n(0, 0, r, B)
                        }(C.ownerDocument) : s(w, C), h = F.width, d = F.height, f = F.left, H = F.top, p = Ss(w, C, e.backgroundColor), E = {
                            canvas: e.canvas,
                            backgroundColor: p,
                            scale: null !== (O = null !== (G = e.scale) && void 0 !== G ? G : o.devicePixelRatio) && void 0 !== O ? O : 1,
                            x: (null !== (V = e.x) && void 0 !== V ? V : 0) + f,
                            y: (null !== (k = e.y) && void 0 !== k ? k : 0) + H,
                            width: null !== (R = e.width) && void 0 !== R ? R : Math.ceil(h),
                            height: null !== (N = e.height) && void 0 !== N ? N : Math.ceil(d)
                        }, g ? (w.logger.debug("Document cloned, using foreign object rendering"), [4, new Ls(w, E).render(C)]) : [3, 3];
                    case 2:
                        return I = B.sent(), [3, 5];
                    case 3:
                        return w.logger.debug("Document cloned, element located at " + f + "," + H + " with size " + h + "x" + d + " using computed rendering"), w.logger.debug("Starting DOM parsing"), y = SB(w, C), p === y.styles.backgroundColor && (y.styles.backgroundColor = Le.TRANSPARENT), w.logger.debug("Starting renderer for element at " + E.x + "," + E.y + " with size " + E.width + "x" + E.height), [4, new ps(w, E).render(y)];
                    case 4:
                        I = B.sent(), B.label = 5;
                    case 5:
                        return (null === (P = e.removeContainer) || void 0 === P || P) && (hn.destroy(l) || w.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")), w.logger.debug("Finished rendering"), [2, I]
                }
            }))
        }))
    },
    Ss = function(A, e, t) {
        var r = e.ownerDocument,
            B = r.documentElement ? me(A, getComputedStyle(r.documentElement).backgroundColor) : Le.TRANSPARENT,
            n = r.body ? me(A, getComputedStyle(r.body).backgroundColor) : Le.TRANSPARENT,
            s = "string" == typeof t ? me(A, t) : null === t ? Le.TRANSPARENT : 4294967295;
        return e === r.documentElement ? de(B) ? de(n) ? s : n : B : s
    };
export {
    xs as h
};