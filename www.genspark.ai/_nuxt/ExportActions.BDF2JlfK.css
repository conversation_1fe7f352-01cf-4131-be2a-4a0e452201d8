.vditor {
    --border-color: #d1d5da;
    --second-color: rgba(88, 96, 105, .36);
    --panel-background-color: #fff;
    --panel-shadow: 0 1px 2px rgba(0, 0, 0, .2);
    --toolbar-background-color: #f6f8fa;
    --toolbar-icon-color: #586069;
    --toolbar-icon-hover-color: #4285f4;
    --toolbar-height: 35px;
    --toolbar-divider-margin-top: 8px;
    --textarea-background-color: #fafbfc;
    --textarea-text-color: #24292e;
    --resize-icon-color: var(--toolbar-icon-color);
    --resize-background-color: var(--toolbar-background-color);
    --resize-hover-icon-color: var(--panel-background-color);
    --resize-hover-background-color: var(--toolbar-icon-hover-color);
    --count-background-color: rgba(27, 31, 35, .05);
    --heading-border-color: #eaecef;
    --blockquote-color: #6a737d;
    --ir-heading-color: #660e7a;
    --ir-title-color: grey;
    --ir-bi-color: #0033b3;
    --ir-link-color: green;
    --ir-bracket-color: #00f;
    --ir-paren-color: green
}

.vditor--dark {
    --border-color: #141414;
    --second-color: hsla(0, 0%, 73%, .36);
    --panel-background-color: #24292e;
    --panel-shadow: 0 1px 2px hsla(0, 0%, 100%, .2);
    --toolbar-background-color: #1d2125;
    --toolbar-icon-color: #b9b9b9;
    --toolbar-icon-hover-color: #fff;
    --textarea-background-color: #2f363d;
    --textarea-text-color: #d1d5da;
    --resize-icon-color: var(--border-color);
    --resize-background-color: var(--second-color);
    --resize-hover-icon-color: var(--toolbar-icon-hover-color);
    --resize-hover-background-color: hsla(0, 0%, 73%, .86);
    --count-background-color: rgba(66, 133, 244, .36);
    --heading-border-color: var(--textarea-text-color);
    --blockquote-color: var(--toolbar-icon-color);
    --ir-heading-color: #9876aa;
    --ir-title-color: grey;
    --ir-bi-color: #cc7832;
    --ir-link-color: #ffc66d;
    --ir-bracket-color: #287bde;
    --ir-paren-color: #6a8759
}

@keyframes tooltip-appear {
    0% {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

.vditor-tooltipped {
    cursor: pointer;
    position: relative
}

.vditor-tooltipped:after {
    font-size: 11px;
    font-weight: 400;
    padding: 5px 8px;
    z-index: 1000000;
    -webkit-font-smoothing: subpixel-antialiased;
    color: #fff;
    letter-spacing: normal;
    text-align: center;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    word-wrap: break-word;
    background: #3b3e43;
    border-radius: 3px;
    content: attr(aria-label);
    line-height: 16px;
    white-space: pre
}

.vditor-tooltipped:after,
.vditor-tooltipped:before {
    display: none;
    opacity: 0;
    pointer-events: none;
    position: absolute
}

.vditor-tooltipped:before {
    border: 5px solid transparent;
    color: #3b3e43;
    content: "";
    height: 0;
    width: 0;
    z-index: 1000001
}

.vditor-tooltipped--hover:after,
.vditor-tooltipped--hover:before,
.vditor-tooltipped:active:after,
.vditor-tooltipped:active:before,
.vditor-tooltipped:focus:after,
.vditor-tooltipped:focus:before,
.vditor-tooltipped:hover:after,
.vditor-tooltipped:hover:before {
    animation-duration: .15s;
    animation-fill-mode: forwards;
    animation-name: tooltip-appear;
    animation-timing-function: ease-in;
    display: inline-block;
    text-decoration: none
}

.vditor-tooltipped__s:after,
.vditor-tooltipped__se:after,
.vditor-tooltipped__sw:after {
    margin-top: 5px;
    right: 50%;
    top: 100%
}

.vditor-tooltipped__s:before,
.vditor-tooltipped__se:before,
.vditor-tooltipped__sw:before {
    border-bottom-color: #3b3e43;
    bottom: -5px;
    margin-right: -5px;
    right: 50%;
    top: auto
}

.vditor-tooltipped__se:after {
    left: 50%;
    margin-left: -15px;
    right: auto
}

.vditor-tooltipped__sw:after {
    margin-right: -15px
}

.vditor-tooltipped__n:after,
.vditor-tooltipped__ne:after,
.vditor-tooltipped__nw:after {
    bottom: 100%;
    margin-bottom: 5px;
    right: 50%
}

.vditor-tooltipped__n:before,
.vditor-tooltipped__ne:before,
.vditor-tooltipped__nw:before {
    border-top-color: #3b3e43;
    bottom: auto;
    margin-right: -5px;
    right: 50%;
    top: -5px
}

.vditor-tooltipped__ne:after {
    left: 50%;
    margin-left: -15px;
    right: auto
}

.vditor-tooltipped__nw:after {
    margin-right: -15px
}

.vditor-tooltipped__n:after,
.vditor-tooltipped__s:after {
    transform: translate(50%)
}

.vditor-tooltipped__w:after {
    bottom: 50%;
    margin-right: 5px;
    right: 100%;
    transform: translateY(50%)
}

.vditor-tooltipped__w:before {
    border-left-color: #3b3e43;
    bottom: 50%;
    left: -5px;
    margin-top: -5px;
    top: 50%
}

.vditor-tooltipped__e:after {
    bottom: 50%;
    left: 100%;
    margin-left: 5px;
    transform: translateY(50%)
}

.vditor-tooltipped__e:before {
    border-right-color: #3b3e43;
    bottom: 50%;
    margin-top: -5px;
    right: -5px;
    top: 50%
}

@media screen and (max-width:520px) {
    .vditor-tooltipped:after,
    .vditor-tooltipped:before {
        content: none
    }
}

@keyframes scale-in {
    0% {
        opacity: 0;
        transform: scale(.5)
    }
    to {
        opacity: 1;
        transform: scale(1)
    }
}

.vditor-panel {
    animation-duration: .15s;
    animation-name: scale-in;
    animation-timing-function: cubic-bezier(.2, 0, .13, 1.5);
    background-color: var(--panel-background-color);
    border-radius: 3px;
    box-shadow: var(--panel-shadow);
    color: var(--toolbar-icon-color);
    display: none;
    font-size: 14px;
    max-width: 320px;
    min-width: 80px;
    padding: 5px;
    position: absolute;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    z-index: 3
}

.vditor-panel--none {
    animation: none;
    display: none !important;
    max-width: none;
    min-width: auto;
    opacity: .86;
    padding: 0;
    white-space: nowrap
}

.vditor-panel--arrow:before {
    border: 7px solid transparent;
    border-bottom: 7px solid var(--panel-background-color);
    content: " ";
    height: 0;
    left: 5px;
    pointer-events: none;
    position: absolute;
    top: -14px;
    width: 0
}

.vditor-panel--left {
    right: 0
}

.vditor-panel--left.vditor-panel--arrow:before {
    left: auto;
    right: 5px
}

.vditor-input {
    background-color: var(--panel-background-color);
    border: 0;
    color: var(--textarea-text-color);
    font-size: 12px;
    padding: 3px 5px
}

.vditor-input:focus {
    background-color: var(--toolbar-background-color);
    outline: none
}

.vditor-icon {
    background-color: transparent;
    border: 0;
    box-sizing: border-box;
    color: var(--toolbar-icon-color);
    cursor: pointer;
    float: left;
    height: 21px;
    padding: 4px 5px;
    width: 23px
}

.vditor-icon--current,
.vditor-icon:hover {
    background-color: transparent;
    color: var(--toolbar-icon-hover-color)
}

.vditor-icon:focus {
    outline: none
}

.vditor-icon svg {
    float: left;
    height: 13px !important;
    width: 13px !important;
    fill: currentColor;
    pointer-events: none
}

.vditor-toolbar {
    background-color: var(--toolbar-background-color);
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 12px;
    box-shadow: 0 2px 8px #0000001a;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    line-height: 1;
    margin: 0 auto;
    max-width: 1000px;
    padding-left: 16px !important;
    padding: 12px 16px;
    position: sticky;
    top: 20px;
    width: -moz-fit-content;
    width: fit-content;
    z-index: 10
}

.vditor-toolbar--pin {
    position: sticky;
    top: 0;
    z-index: 1
}

.vditor-toolbar--hide {
    height: 5px;
    overflow: hidden;
    transition: all .15s ease-in-out
}

.vditor-toolbar--hide:hover {
    background-color: var(--toolbar-background-color);
    height: auto;
    overflow: visible
}

.vditor-toolbar__item {
    float: left;
    position: relative
}

.vditor-toolbar__item .vditor-tooltipped {
    background-color: transparent;
    border: 0;
    box-sizing: border-box;
    color: var(--toolbar-icon-color);
    font-size: 0;
    height: var(--toolbar-height);
    padding: 10px 5px;
    width: 25px
}

.vditor-toolbar__item .vditor-tooltipped:focus {
    color: var(--toolbar-icon-hover-color);
    cursor: pointer;
    outline: none
}

.vditor-toolbar__item svg {
    fill: currentColor;
    display: inline-block;
    stroke-width: 0;
    stroke: currentColor;
    height: 15px;
    width: 15px
}

.vditor-toolbar__item input {
    cursor: pointer;
    height: var(--toolbar-height);
    left: 0;
    opacity: .001;
    overflow: hidden;
    position: absolute;
    top: 0;
    width: 25px
}

.vditor-toolbar__divider {
    border-left: 1px solid var(--second-color);
    float: left;
    height: calc(var(--toolbar-height) - var(--toolbar-divider-margin-top)*2);
    margin: var(--toolbar-divider-margin-top) 8px
}

.vditor-toolbar__br {
    height: 0 !important;
    padding: 0 !important;
    width: 100%
}

.vditor-menu--current {
    color: var(--toolbar-icon-hover-color) !important
}

.vditor-menu--disabled {
    color: var(--second-color) !important;
    cursor: not-allowed !important
}

.vditor-emojis {
    display: inline-block;
    overflow: auto
}

.vditor-emojis::-webkit-scrollbar {
    display: none
}

.vditor-emojis__tip {
    color: var(--toolbar-icon-color);
    flex: 1;
    margin-right: 10px;
    min-width: 1px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 200px
}

.vditor-emojis__tail {
    color: var(--toolbar-icon-color);
    display: flex;
    font-size: 12px;
    margin-top: 5px
}

.vditor-emojis__tail a {
    color: var(--toolbar-icon-color);
    text-decoration: none
}

.vditor-emojis__tail a:hover {
    color: var(--toolbar-icon-hover-color)
}

.vditor-emojis button {
    background-color: transparent;
    border: 0;
    border-radius: 3px;
    box-sizing: border-box;
    cursor: pointer;
    float: left;
    font-size: 16px;
    height: 30px;
    line-height: 26px;
    margin: 0;
    overflow: hidden;
    padding: 3px;
    text-align: center;
    transition: all .15s ease-in-out;
    width: 30px
}

.vditor-emojis button:focus {
    outline: none
}

.vditor-emojis button:hover .vditor-emojis__icon {
    display: inline-block;
    transform: scale(1.2)
}

.vditor-emojis img {
    float: left;
    height: 20px;
    margin: 3px 0 0 3px;
    width: 20px
}

@media screen and (max-width:520px) {
    .vditor-panel--left.vditor-panel--arrow:before {
        right: 17px
    }
}

@media (hover:hover) and (pointer:fine) {
    .vditor-toolbar__item .vditor-tooltipped:hover {
        color: var(--toolbar-icon-hover-color)
    }
}

@keyframes slideInDown {
    0% {
        transform: translate3d(0, -100%, 0);
        visibility: visible
    }
    to {
        transform: translateZ(0)
    }
}

.vditor {
    border: 1px solid var(--border-color);
    border-radius: 3px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    font-family: Helvetica Neue, Luxi Sans, DejaVu Sans, Hiragino Sans GB, Microsoft Yahei, sans-serif, Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, Segoe UI Symbol, Android Emoji, EmojiSymbols
}

.vditor--fullscreen {
    border-radius: 0;
    height: 100vh !important;
    left: 0;
    position: fixed;
    top: 0;
    width: 100% !important;
    z-index: 90
}

.vditor-content {
    display: flex;
    flex: 1;
    min-height: 60px;
    min-width: 1px;
    position: relative
}

.vditor-preview {
    background-color: var(--textarea-background-color);
    border-left: 1px solid var(--border-color);
    border-radius: 0 0 3px;
    box-sizing: border-box;
    display: none !important;
    flex: 1;
    margin-left: -1px;
    min-width: 1px;
    overflow: auto
}

.vditor-preview::-webkit-scrollbar {
    display: none
}

.vditor-preview__action {
    background-color: var(--toolbar-background-color);
    padding: 10px;
    text-align: center
}

.vditor-preview__action button {
    background-color: var(--toolbar-background-color);
    border: 0;
    color: var(--toolbar-icon-color);
    cursor: pointer;
    font-size: 12px;
    line-height: 20px;
    margin: 0 10px;
    padding: 0 7px
}

.vditor-preview__action button.vditor-preview__action--current,
.vditor-preview__action button:hover {
    background-color: var(--toolbar-background-color);
    color: var(--toolbar-icon-hover-color)
}

.vditor-preview__action button:focus {
    outline: none
}

.vditor-preview__action button svg {
    fill: currentColor;
    height: 15px;
    vertical-align: middle;
    width: 15px
}

.vditor-preview>.vditor-reset {
    margin: 0 auto;
    padding: 10px
}

.vditor-preview img:not(.emoji) {
    cursor: pointer
}

.vditor-devtools {
    background-color: var(--textarea-background-color);
    border-radius: 0 0 3px;
    box-shadow: inset 1px 0 var(--border-color);
    box-sizing: border-box;
    display: none;
    flex: 1;
    min-width: 1px;
    overflow: auto;
    padding: 10px
}

.vditor-counter {
    background-color: var(--count-background-color);
    border-radius: 3px;
    color: var(--toolbar-icon-color);
    float: right;
    font-size: 12px;
    margin: 8px 3px 0 0;
    padding: 3px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.vditor-counter--error {
    background-color: #d23f311a;
    color: #d23f31
}

.vditor-resize {
    cursor: row-resize;
    padding: 3px 0;
    position: absolute;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 100%
}

.vditor-resize--top {
    top: -3px
}

.vditor-resize--bottom {
    bottom: -3px
}

.vditor-resize>div {
    background-color: var(--resize-background-color);
    height: 3px;
    transition: all .15s ease-in-out
}

.vditor-resize--selected>div,
.vditor-resize:hover>div {
    background-color: var(--resize-hover-background-color)
}

.vditor-resize--selected svg,
.vditor-resize:hover svg {
    color: var(--resize-hover-icon-color)
}

.vditor-resize svg {
    fill: currentColor;
    stroke-width: 0;
    stroke: currentColor;
    color: var(--resize-icon-color);
    display: block;
    height: 3px;
    margin: 0 auto;
    width: 13px
}

.vditor-upload {
    background-color: #4285f4;
    height: 3px;
    left: 0;
    position: absolute;
    top: -2px;
    transition: all .15s ease-in-out
}

.vditor-tip {
    animation-duration: .15s;
    animation-fill-mode: both;
    font-size: 12px;
    left: 50%;
    position: absolute;
    top: 10px;
    z-index: 5
}

.vditor-tip--show {
    animation-name: slideInDown;
    display: block
}

.vditor-tip__content {
    background: var(--toolbar-background-color);
    border-radius: 3px;
    box-shadow: var(--panel-shadow);
    color: var(--toolbar-icon-color);
    display: inline-block;
    line-height: 16px;
    margin-left: -50%;
    max-width: 100%;
    padding: 3px 10px;
    position: relative;
    text-align: left
}

.vditor-tip__content ul {
    margin: 2px 0;
    padding: 0 0 0 18px
}

.vditor-tip__content a {
    color: #4285f4
}

.vditor-tip__close {
    color: var(--toolbar-icon-color);
    cursor: pointer;
    font-weight: 700;
    position: absolute;
    right: -15px;
    top: -7px
}

.vditor-tip__close:hover {
    color: var(--toolbar-icon-hover-color)
}

.vditor-img {
    bottom: 0;
    display: flex;
    flex-direction: column;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 100
}

.vditor-img__bar {
    align-items: center;
    background-color: var(--toolbar-background-color);
    border-bottom: 1px solid var(--border-color);
    box-sizing: border-box;
    display: flex;
    height: 36px;
    justify-content: center;
    text-align: center
}

.vditor-img__btn {
    align-items: center;
    color: var(--toolbar-icon-color);
    cursor: pointer;
    display: flex;
    margin-left: 24px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.vditor-img__btn:hover {
    color: var(--toolbar-icon-hover-color)
}

.vditor-img__btn svg {
    height: 14px;
    margin-right: 8px;
    width: 14px;
    fill: currentColor
}

.vditor-img__img {
    background-color: var(--textarea-background-color);
    cursor: zoom-out;
    flex: 1;
    overflow: auto
}

.vditor-img__img img {
    max-width: none
}

.vditor-hint {
    background-color: var(--panel-background-color);
    border-radius: 3px;
    box-shadow: var(--panel-shadow);
    display: none;
    font-size: 12px;
    line-height: 20px;
    list-style: none;
    margin: 0;
    max-width: 250px;
    min-width: 80px;
    padding: 5px 0;
    position: absolute;
    z-index: 4
}

.vditor-hint .vditor-hint {
    left: 100%;
    margin-top: -31px;
    right: auto
}

.vditor-hint .vditor-hint.vditor-panel--left {
    left: auto;
    right: 100%
}

.vditor-hint button {
    background-color: transparent;
    border: 0;
    border-radius: 0;
    box-sizing: border-box;
    color: var(--toolbar-icon-color);
    cursor: pointer;
    display: block;
    line-height: 20px;
    margin: 0;
    overflow: hidden;
    padding: 3px 10px;
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

.vditor-hint button:focus {
    outline: none
}

.vditor-hint button:not(.vditor-menu--disabled):hover,
.vditor-hint--current {
    background-color: var(--toolbar-background-color) !important;
    color: var(--toolbar-icon-hover-color) !important
}

.vditor-hint__emoji {
    float: left;
    font-size: 16px;
    margin-right: 3px
}

.vditor-hint img {
    float: left;
    height: 20px;
    margin-right: 3px;
    width: 20px
}

.vditor-reset {
    color: #24292e;
    font-family: Helvetica Neue, Luxi Sans, DejaVu Sans, Hiragino Sans GB, Microsoft Yahei, sans-serif, Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, Segoe UI Symbol, Android Emoji, EmojiSymbols;
    font-variant-ligatures: no-common-ligatures;
    word-wrap: break-word;
    border-radius: 12px;
    font-size: 16px;
    line-height: 1.5;
    word-break: break-word
}

.vditor-reset--anchor {
    padding-left: 20px
}

.vditor-reset--error {
    color: #d23f31;
    display: block;
    font-size: 12px;
    line-height: 16px
}

.vditor-reset ul ul ul {
    list-style-type: square
}

.vditor-reset ul ul {
    list-style-type: circle
}

.vditor-reset ul {
    list-style-type: disc
}

.vditor-reset ol,
.vditor-reset ul {
    margin-bottom: 16px;
    margin-top: 0;
    padding-left: 2em
}

.vditor-reset li+li {
    margin-top: .25em
}

.vditor-reset li p {
    margin-top: 16px
}

.vditor-reset audio {
    max-width: 100%
}

.vditor-reset audio:focus {
    outline: none
}

.vditor-reset video {
    max-height: 90vh;
    max-width: 100%
}

.vditor-reset img {
    max-width: 100%
}

.vditor-reset img.emoji {
    cursor: auto;
    max-width: 20px;
    vertical-align: sub
}

.vditor-reset h1,
.vditor-reset h2,
.vditor-reset h3,
.vditor-reset h4,
.vditor-reset h5,
.vditor-reset h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: 16px;
    margin-top: 24px
}

.vditor-reset h1:hover .vditor-anchor svg,
.vditor-reset h2:hover .vditor-anchor svg,
.vditor-reset h3:hover .vditor-anchor svg,
.vditor-reset h4:hover .vditor-anchor svg,
.vditor-reset h5:hover .vditor-anchor svg,
.vditor-reset h6:hover .vditor-anchor svg {
    visibility: visible
}

.vditor-reset h1 {
    font-size: 1.75em
}

.vditor-reset h2 {
    font-size: 1.55em
}

.vditor-reset h3 {
    font-size: 1.38em
}

.vditor-reset h4 {
    font-size: 1.25em
}

.vditor-reset h5 {
    font-size: 1.13em
}

.vditor-reset h6 {
    font-size: 1em
}

.vditor-reset hr {
    background-color: #eaecef;
    border: 0;
    height: 2px;
    margin: 24px 0;
    padding: 0
}

.vditor-reset p {
    margin-bottom: 16px;
    margin-top: 0
}

.vditor-reset blockquote {
    border-left: .25em solid #eaecef;
    color: #6a737d;
    margin: 0 0 16px;
    padding: 0 1em
}

.vditor-reset blockquote>:first-child {
    margin-top: 0
}

.vditor-reset blockquote>:last-child {
    margin-bottom: 0
}

.vditor-reset ins>iframe {
    border: 0
}

.vditor-reset iframe {
    border: 1px solid #d1d5da;
    box-sizing: border-box;
    max-width: 100%
}

.vditor-reset iframe.iframe__video {
    min-height: 36vh;
    min-width: 80%
}

.vditor-reset table {
    border-collapse: collapse;
    border-spacing: 0;
    display: block;
    empty-cells: show;
    margin-bottom: 16px;
    overflow: auto;
    width: 100%;
    word-break: keep-all
}

.vditor-reset table tr {
    background-color: #fafbfc;
    border-top: 1px solid #c6cbd1
}

.vditor-reset table td,
.vditor-reset table th {
    border: 1px solid #dfe2e5;
    padding: 6px 13px;
    white-space: nowrap;
    word-break: normal
}

.vditor-reset table td:first-child:after,
.vditor-reset table th:first-child:after {
    content: "";
    display: inline-block;
    min-height: 24px;
    vertical-align: top
}

.vditor-reset table th {
    font-weight: 600
}

.vditor-reset table tbody tr:nth-child(2n) {
    background-color: #fff
}

.vditor-reset code:not(.hljs):not(.highlight-chroma) {
    background-size: 20px 20px;
    border-radius: 3px;
    font-family: mononoki, Consolas, Liberation Mono, Menlo, Courier, monospace, Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, Segoe UI Symbol, Android Emoji, EmojiSymbols;
    font-size: 85%;
    margin: 0;
    padding: .2em .4em;
    white-space: pre-wrap;
    word-break: break-word
}

.vditor-reset pre {
    margin: 1em 0
}

.vditor-reset pre>code {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8AgMAAABHkjHhAAAACVBMVEWAgIBaWlo+Pj7rTFvWAAAAA3RSTlMHCAw+VhR4AAAA+klEQVQoz4WSMW7EQAhFPxKWNh2FCx+HkaZI6RRb5DYbyVfIJXLKDCFoMbaTKSw/8ZnPAPjaH2xgZcUNUDADD7D9LtDBCLZ45fbkvo/30K8yeI64pPwl6znd/3n/Oe93P3ho9qeh72btTFzqkz0rsJle8Zr81OLEwZ1dv/713uWqvu2pl+k0fy7MWtj9r/tN5q/02z89qa/L4Dc2LvM93kezPfXlME/O86EbY/V9GB9ePX8G1/6W+/9h1dq/HGfTfzT3j/xNo7522Bfnqe5jO/fvhVthlfk434v3iO9zG/UOphyPeinPl1J8Gtaa7xPTa/Dk+RIs4deMvwGvcGsmsCvJ0AAAAABJRU5ErkJggg==);
    background-size: 20px 20px;
    border-radius: 5px;
    display: block;
    font-family: mononoki, Consolas, Liberation Mono, Menlo, Courier, monospace, Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, Segoe UI Symbol, Android Emoji, EmojiSymbols;
    font-size: 85%;
    margin: 0;
    overflow: auto;
    padding: .5em;
    white-space: pre;
    word-break: normal;
    word-wrap: normal
}

.vditor-reset pre:hover div.vditor-copy {
    display: block
}

.vditor-reset .language-abc,
.vditor-reset .language-echarts,
.vditor-reset .language-flowchart,
.vditor-reset .language-graphviz,
.vditor-reset .language-markmap,
.vditor-reset .language-math,
.vditor-reset .language-mermaid,
.vditor-reset .language-mindmap,
.vditor-reset .language-plantuml,
.vditor-reset .language-smiles {
    margin-bottom: 16px
}

.vditor-reset .language-math mjx-container:focus {
    cursor: context-menu;
    outline: none
}

.vditor-reset .language-math .katex-display>.katex>.katex-html>.tag {
    display: inline-block;
    font-size: 1.21rem;
    right: 2px
}

.vditor-reset .language-echarts,
.vditor-reset .language-mindmap {
    height: 420px;
    overflow: hidden
}

.vditor-reset .language-flowchart,
.vditor-reset .language-graphviz,
.vditor-reset .language-markmap,
.vditor-reset .language-mermaid {
    text-align: center
}

.vditor-reset .language-graphviz parsererror {
    overflow: auto
}

.vditor-reset kbd {
    background-color: #fafbfc;
    border: 1px solid #d1d5da;
    border-radius: 3px;
    box-shadow: inset 0 -1px #d1d5da;
    color: #24292e;
    display: inline-block;
    font: 11px Consolas, Liberation Mono, Menlo, Courier, monospace;
    line-height: 10px;
    padding: 3px 5px;
    vertical-align: middle
}

.vditor-reset summary {
    cursor: pointer
}

.vditor-reset summary:focus {
    outline: none
}

.vditor-reset svg {
    height: auto;
    width: auto;
    stroke-width: initial
}

.vditor-reset blockquote:last-child,
.vditor-reset hr:last-child,
.vditor-reset ol:last-child,
.vditor-reset p:last-child,
.vditor-reset pre:last-child,
.vditor-reset ul:last-child {
    margin-bottom: 0
}

.vditor-comment {
    border-bottom: 2px solid #f8e6ab
}

.vditor-comment--focus,
.vditor-comment--hover {
    background-color: #faf1d1;
    border-bottom: 2px solid #ffc60a
}

.vditor-comment--focus .vditor-comment,
.vditor-comment--hover .vditor-comment {
    border-bottom: 2px solid #ffc60a
}

.vditor-task {
    list-style: none !important;
    word-break: break-all
}

.vditor-task input {
    font-size: 12px;
    margin: 0 .2em .25em -1.6em;
    vertical-align: middle
}

.vditor-copy {
    display: none;
    position: relative;
    z-index: 1
}

.vditor-copy textarea {
    height: 10px;
    left: -100000px;
    position: absolute
}

.vditor-copy span {
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: .5em
}

.vditor-copy svg {
    color: #586069;
    display: block;
    height: 14px;
    width: 14px !important;
    fill: currentColor
}

.vditor-linenumber {
    padding-left: 4em !important;
    position: relative
}

.vditor-linenumber__rows {
    counter-reset: linenumber;
    left: 0;
    pointer-events: none;
    position: absolute;
    top: 1em;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 3em
}

.vditor-linenumber__rows>span {
    display: block;
    pointer-events: none
}

.vditor-linenumber__rows>span:before {
    color: #9d959561;
    content: counter(linenumber);
    counter-increment: linenumber;
    display: block;
    padding-right: 1em;
    text-align: right
}

.vditor-speech {
    background-color: #f6f8fa;
    border: 1px solid #d1d5da;
    border-radius: 3px;
    color: #586069;
    cursor: pointer;
    display: none;
    padding: 3px;
    position: absolute
}

.vditor-speech--current,
.vditor-speech:hover {
    color: #4285f4
}

.vditor-speech svg {
    height: 14px;
    width: 14px;
    fill: currentColor;
    display: block;
    stroke-width: 0;
    stroke: currentColor
}

.vditor-anchor {
    margin-left: 5px
}

.vditor-anchor--left {
    float: left;
    margin-left: -20px;
    padding-right: 4px
}

.vditor-anchor svg {
    visibility: hidden
}

.vditor-anchor:hover svg {
    visibility: visible
}

.vditor-anchor:focus {
    outline: none
}

.vditor-linkcard {
    cursor: pointer;
    margin: 31px auto 16px;
    max-width: 768px;
    padding: 0 10px;
    transition: all .15s ease-in-out
}

.vditor-linkcard a {
    background-color: #f6f8fa;
    border-radius: 3px;
    box-shadow: 0 1px 2px #0003;
    display: flex;
    flex-wrap: wrap-reverse;
    max-height: 250px;
    overflow: hidden;
    text-decoration: none
}

.vditor-linkcard a:hover {
    box-shadow: 0 0 3px #00000021, 0 3px 6px #00000042;
    text-decoration: none
}

.vditor-linkcard a:visited .vditor-linkcard__abstract {
    color: #5860695c
}

.vditor-linkcard__info {
    box-sizing: border-box;
    flex: 1;
    min-width: 200px;
    padding: 10px
}

.vditor-linkcard__title {
    align-items: center;
    color: #24292e;
    display: flex;
    font-size: 14px;
    font-weight: 400
}

.vditor-linkcard__title img {
    border-radius: 3px;
    cursor: pointer;
    flex-shrink: 0;
    height: 20px;
    margin-right: 5px;
    width: 20px
}

.vditor-linkcard__abstract {
    word-wrap: break-word;
    word-break: break-all;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    color: #586069;
    display: -webkit-box;
    font-size: 13px;
    margin: 5px 0
}

.vditor-linkcard__site {
    color: #4285f4;
    font-size: 12px
}

.vditor-linkcard__image {
    background-color: #5860695c;
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    cursor: pointer;
    max-width: 250px;
    min-width: 126px
}

.vditor-footnotes__goto-ref {
    text-decoration: none
}

.vditor-toc {
    color: #4285f4;
    margin-bottom: 16px;
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text
}

.vditor-toc .vditor-outline__action {
    display: none
}

.vditor-toc ul {
    list-style: none !important;
    padding-left: 1em
}

.vditor-toc>ul {
    padding-left: 0
}

.vditor-toc span {
    cursor: pointer
}

.vditor-toc li>span>svg {
    height: 0;
    width: 0
}

.vditor-outline {
    background-color: var(--panel-background-color);
    border-right: 1px solid var(--border-color);
    display: none;
    overflow: auto;
    width: 250px
}

.vditor-outline--right {
    border-left: 1px solid var(--border-color);
    border-right: 0
}

.vditor-outline::-webkit-scrollbar {
    display: none
}

.vditor-outline ul {
    list-style: none !important;
    margin: 0;
    padding-left: 1em
}

.vditor-outline__content>ul {
    padding-left: 0
}

.vditor-outline li>span {
    align-items: center;
    color: var(--textarea-text-color);
    cursor: pointer;
    display: flex;
    padding: 5px 10px
}

.vditor-outline li>span>svg {
    flex-shrink: 0;
    height: 10px;
    width: 10px
}

.vditor-outline li>span:hover {
    color: var(--toolbar-icon-hover-color)
}

.vditor-outline li>span>span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.vditor-outline__title {
    border-bottom: 1px dashed var(--border-color);
    color: var(--toolbar-icon-color);
    font-size: 12px;
    padding: 5px 10px
}

.vditor-outline__action {
    transition: all .15s ease-in-out;
    fill: currentColor;
    flex-shrink: 0;
    margin-right: 5px
}

.vditor-outline__action--close {
    transform: rotate(-90deg)
}

.vditor-wysiwyg {
    box-sizing: border-box;
    flex: 1;
    min-width: 1px;
    padding: 0 0 100px;
    position: relative;
    width: 100%
}

.vditor-wysiwyg pre.vditor-reset {
    background-color: var(--panel-background-color);
    border: 1px solid #ddd;
    box-shadow: 0 2px 8px #0000001a;
    box-sizing: border-box;
    height: 100%;
    margin: 30px auto;
    max-width: 800px;
    padding: 30px !important;
    white-space: pre-wrap
}

@media screen and (max-width:520px) {
    .vditor-wysiwyg pre.vditor-reset {
        padding: 10px !important
    }
}

.vditor-wysiwyg pre.vditor-reset:empty:before {
    color: var(--second-color);
    content: attr(placeholder)
}

.vditor-wysiwyg pre.vditor-reset:focus {
    background-color: #fff;
    outline: none
}

.vditor-wysiwyg pre.vditor-reset:after {
    content: "";
    display: block;
    height: var(--editor-bottom)
}

.vditor-wysiwyg blockquote:empty:before,
.vditor-wysiwyg h1:empty:after,
.vditor-wysiwyg h2:empty:after,
.vditor-wysiwyg h3:empty:after,
.vditor-wysiwyg h4:empty:after,
.vditor-wysiwyg h5:empty:after,
.vditor-wysiwyg h6:empty:after,
.vditor-wysiwyg p:empty:before,
.vditor-wysiwyg pre>code:empty:before {
    content: " "
}

.vditor-wysiwyg code[data-marker="`"] {
    padding-left: 0 !important;
    padding-right: 0 !important
}

.vditor-wysiwyg__block pre:first-child {
    margin-bottom: -1em
}

.vditor-wysiwyg__block pre:first-child code {
    color: var(--textarea-text-color);
    height: auto;
    text-align: left
}

.vditor-wysiwyg__block pre:last-child {
    margin-bottom: 1em
}

.vditor-wysiwyg__preview {
    cursor: pointer;
    min-height: 27px;
    white-space: normal
}

.vditor-wysiwyg .vditor-toc:before,
.vditor-wysiwyg div.vditor-wysiwyg__block:before,
.vditor-wysiwyg div[data-type=footnotes-block]:before,
.vditor-wysiwyg div[data-type=link-ref-defs-block]:before,
.vditor-wysiwyg>.vditor-reset>h1:before,
.vditor-wysiwyg>.vditor-reset>h2:before,
.vditor-wysiwyg>.vditor-reset>h3:before,
.vditor-wysiwyg>.vditor-reset>h4:before,
.vditor-wysiwyg>.vditor-reset>h5:before,
.vditor-wysiwyg>.vditor-reset>h6:before {
    color: var(--second-color);
    content: "H1";
    display: none;
    float: left;
    font-size: .85rem;
    font-weight: 400;
    margin-left: -29px;
    padding-right: 4px
}

.vditor-wysiwyg>.vditor-reset>h2:before {
    content: "H2"
}

.vditor-wysiwyg>.vditor-reset>h3:before {
    content: "H3"
}

.vditor-wysiwyg>.vditor-reset>h4:before {
    content: "H4"
}

.vditor-wysiwyg>.vditor-reset>h5:before {
    content: "H5"
}

.vditor-wysiwyg>.vditor-reset>h6:before {
    content: "H6"
}

.vditor-wysiwyg div[data-type=link-ref-defs-block]:before {
    content: '"A"'
}

.vditor-wysiwyg div[data-type=footnotes-block]:before {
    content: "^F"
}

.vditor-wysiwyg div.vditor-wysiwyg__block:before {
    content: "</>"
}

.vditor-wysiwyg div.vditor-wysiwyg__block[data-type=yaml-front-matter]:before {
    content: "F"
}

.vditor-wysiwyg div.vditor-wysiwyg__block[data-type=math-block]:before {
    content: "$$"
}

.vditor-wysiwyg .vditor-toc:before {
    content: "ToC"
}

.vditor-wysiwyg hr {
    display: inline-block;
    margin: 12px 0;
    width: 100%
}

.vditor-wysiwyg details {
    white-space: normal
}

.vditor-wysiwyg a {
    cursor: pointer
}

.vditor-wysiwyg span[data-type=backslash]>span {
    color: var(--second-color);
    display: none
}

.vditor-wysiwyg span[data-type=link-ref],
.vditor-wysiwyg sup[data-type=footnotes-ref] {
    color: #4285f4
}

.vditor-wysiwyg span[data-type=toc-h] {
    color: #4285f4;
    text-decoration: underline
}

.vditor-wysiwyg div[data-type=footnotes-block] {
    border-top: 2px solid var(--heading-border-color);
    margin-top: 24px;
    padding-top: 24px
}

.vditor-wysiwyg div[data-type=link-ref-defs-block] {
    color: var(--blockquote-color)
}

@media screen and (max-width:520px) {
    .vditor-wysiwyg .vditor-toc:before,
    .vditor-wysiwyg div.vditor-wysiwyg__block:before,
    .vditor-wysiwyg div[data-type=footnotes-block]:before,
    .vditor-wysiwyg div[data-type=link-ref-defs-block]:before,
    .vditor-wysiwyg h1:before,
    .vditor-wysiwyg h2:before,
    .vditor-wysiwyg h3:before,
    .vditor-wysiwyg h4:before,
    .vditor-wysiwyg h5:before,
    .vditor-wysiwyg h6:before {
        content: none
    }
}

.vditor-ir {
    box-sizing: border-box;
    flex: 1;
    min-width: 1px;
    position: relative;
    width: 100%
}

.vditor-ir__node[data-type=code-block]:after,
.vditor-ir__node[data-type=code-block]:before,
.vditor-ir__node[data-type=math-block]:after,
.vditor-ir__node[data-type=math-block]:before,
.vditor-ir__node[data-type=yaml-front-matter]:after,
.vditor-ir__node[data-type=yaml-front-matter]:before {
    color: var(--second-color);
    content: " "
}

.vditor-ir__node:not(.vditor-ir__node--expand) .vditor-ir__marker {
    padding: 0 !important
}

.vditor-ir__node:not(.vditor-ir__node--expand)[data-type=a] {
    cursor: pointer
}

.vditor-ir__node[data-type=footnotes-ref],
.vditor-ir__node[data-type=link-ref] {
    color: #4285f4
}

.vditor-ir__node[data-type=html-block] {
    margin-bottom: 1em
}

.vditor-ir__node .vditor-ir__marker {
    display: inline-block;
    height: 0;
    overflow: hidden;
    transition: all .15s ease-in-out;
    width: 0
}

.vditor-ir__node--hidden .vditor-ir__marker {
    visibility: hidden
}

.vditor-ir__node--expand .vditor-ir__marker {
    color: var(--second-color);
    display: inline;
    height: auto;
    width: auto
}

.vditor-ir__node--expand .vditor-ir__marker--hide {
    display: none
}

.vditor-ir__node--expand .vditor-ir__marker--heading {
    color: var(--ir-heading-color)
}

.vditor-ir__node--expand .vditor-ir__marker--bi {
    color: var(--ir-bi-color)
}

.vditor-ir__node--expand .vditor-ir__marker--link {
    color: var(--ir-link-color)
}

.vditor-ir__node--expand .vditor-ir__marker--title {
    color: var(--ir-title-color)
}

.vditor-ir__node--expand .vditor-ir__marker--bracket {
    color: var(--ir-bracket-color);
    text-decoration: underline
}

.vditor-ir__node--expand .vditor-ir__marker--paren {
    color: var(--ir-paren-color)
}

.vditor-ir__node--expand .vditor-ir__marker--info {
    color: var(--ir-heading-color)
}

.vditor-ir__node--expand .vditor-ir__marker--pre code {
    color: var(--textarea-text-color);
    height: auto;
    text-align: left
}

.vditor-ir__node--expand[data-type=code-block]:after,
.vditor-ir__node--expand[data-type=code-block]:before {
    content: "```"
}

.vditor-ir__node--expand[data-type=yaml-front-matter]:after,
.vditor-ir__node--expand[data-type=yaml-front-matter]:before {
    content: "---"
}

.vditor-ir__node--expand[data-type=math-block]:after,
.vditor-ir__node--expand[data-type=math-block]:before {
    content: "$$"
}

.vditor-ir__node span[data-type=code-block-close-marker],
.vditor-ir__node span[data-type=code-block-open-marker],
.vditor-ir__node span[data-type=math-block-close-marker],
.vditor-ir__node span[data-type=math-block-open-marker],
.vditor-ir__node span[data-type=yaml-front-matter-close-marker],
.vditor-ir__node span[data-type=yaml-front-matter-open-marker] {
    display: none
}

.vditor-ir__preview {
    cursor: pointer;
    min-height: 27px;
    white-space: normal
}

.vditor-ir__link {
    color: var(--ir-bracket-color);
    text-decoration: underline
}

.vditor-ir pre.vditor-reset {
    background-color: var(--panel-background-color);
    box-sizing: border-box;
    height: 100%;
    margin: 0;
    white-space: pre-wrap
}

.vditor-ir pre.vditor-reset[contenteditable=false] {
    cursor: not-allowed;
    opacity: .3
}

.vditor-ir pre.vditor-reset:empty:before {
    color: var(--second-color);
    content: attr(placeholder)
}

.vditor-ir pre.vditor-reset:focus {
    background-color: var(--textarea-background-color);
    outline: none
}

.vditor-ir pre.vditor-reset:after {
    content: "";
    display: block;
    height: var(--editor-bottom)
}

.vditor-ir pre.vditor-reset pre {
    margin: 0
}

.vditor-ir hr {
    display: inline-block;
    margin: 12px 0;
    width: 100%
}

.vditor-ir blockquote:empty:before,
.vditor-ir h1:empty:after,
.vditor-ir h2:empty:after,
.vditor-ir h3:empty:after,
.vditor-ir h4:empty:after,
.vditor-ir h5:empty:after,
.vditor-ir h6:empty:after,
.vditor-ir p:empty:before,
.vditor-ir pre>code:empty:before {
    content: " "
}

.vditor-ir .vditor-reset>h1:before,
.vditor-ir .vditor-reset>h2:before,
.vditor-ir .vditor-reset>h3:before,
.vditor-ir .vditor-reset>h4:before,
.vditor-ir .vditor-reset>h5:before,
.vditor-ir .vditor-reset>h6:before,
.vditor-ir .vditor-toc:before,
.vditor-ir div[data-type=footnotes-block]:before,
.vditor-ir div[data-type=link-ref-defs-block]:before {
    color: var(--second-color);
    content: "H1";
    float: left;
    font-size: .85rem;
    font-weight: 400;
    margin-left: -29px;
    padding-right: 4px
}

.vditor-ir .vditor-reset>h2:before {
    content: "H2"
}

.vditor-ir .vditor-reset>h3:before {
    content: "H3"
}

.vditor-ir .vditor-reset>h4:before {
    content: "H4"
}

.vditor-ir .vditor-reset>h5:before {
    content: "H5"
}

.vditor-ir .vditor-reset>h6:before {
    content: "H6"
}

.vditor-ir div[data-type=link-ref-defs-block] {
    color: var(--blockquote-color)
}

.vditor-ir div[data-type=link-ref-defs-block]:before {
    content: '"A"'
}

.vditor-ir div[data-type=footnotes-block] {
    border-top: 2px solid var(--heading-border-color);
    margin-top: 24px;
    padding-top: 24px
}

.vditor-ir div[data-type=footnotes-block]:before {
    content: "^F"
}

.vditor-ir div[data-type=footnotes-block]>div[data-type=footnotes-def]>blockquote,
.vditor-ir div[data-type=footnotes-block]>div[data-type=footnotes-def]>hr,
.vditor-ir div[data-type=footnotes-block]>div[data-type=footnotes-def]>ol,
.vditor-ir div[data-type=footnotes-block]>div[data-type=footnotes-def]>p,
.vditor-ir div[data-type=footnotes-block]>div[data-type=footnotes-def]>pre,
.vditor-ir div[data-type=footnotes-block]>div[data-type=footnotes-def]>table,
.vditor-ir div[data-type=footnotes-block]>div[data-type=footnotes-def]>ul {
    margin-left: 8px
}

.vditor-ir .vditor-toc:before {
    content: "ToC"
}

.vditor-ir .vditor-toc span[data-type=toc-h] {
    color: #4285f4;
    text-decoration: underline
}

@media screen and (max-width:520px) {
    .vditor-ir .vditor-toc:before,
    .vditor-ir div[data-type=footnotes-block]:before,
    .vditor-ir div[data-type=link-ref-defs-block]:before,
    .vditor-ir h1:before,
    .vditor-ir h2:before,
    .vditor-ir h3:before,
    .vditor-ir h4:before,
    .vditor-ir h5:before,
    .vditor-ir h6:before {
        content: none
    }
}

.vditor-sv {
    border-radius: 0 0 3px 3px;
    color: var(--textarea-text-color);
    flex: 1;
    font-family: Helvetica Neue, Luxi Sans, DejaVu Sans, Hiragino Sans GB, Microsoft Yahei, sans-serif, Apple Color Emoji, Segoe UI Emoji, Noto Color Emoji, Segoe UI Symbol, Android Emoji, EmojiSymbols;
    font-size: 16px;
    font-variant-ligatures: no-common-ligatures;
    line-height: 22px;
    min-width: 1px;
    outline: 0 none;
    overflow: auto;
    padding: 10px 9px 10px 10px;
    resize: none;
    width: 100%;
    word-break: break-word;
    word-wrap: break-word;
    background-color: var(--panel-background-color);
    border: 1px solid #ddd;
    box-shadow: 0 2px 8px #0000001a;
    box-sizing: border-box;
    height: 100%;
    margin: 30px auto;
    max-width: 800px;
    padding: 30px !important;
    white-space: pre-wrap
}

.vditor-sv[contenteditable=false] {
    cursor: not-allowed;
    opacity: .3
}

.vditor-sv:empty:before {
    color: var(--second-color);
    content: attr(placeholder)
}

.vditor-sv:focus {
    background-color: var(--textarea-background-color)
}

.vditor-sv:after {
    content: "";
    display: block;
    height: var(--editor-bottom)
}

.vditor-sv span[data-type=newline]+span[data-type=text]:empty {
    display: inherit
}

.vditor-sv .sup {
    font-size: smaller;
    vertical-align: super
}

.vditor-sv .strong {
    font-weight: 700
}

.vditor-sv .em {
    font-style: italic
}

.vditor-sv .s {
    text-decoration: line-through
}

.vditor-sv .mark:not(.vditor-sv__marker) {
    background-color: #ff0;
    color: #000
}

.vditor-sv .h1 {
    font-size: 1.75em;
    line-height: 44px
}

.vditor-sv .h2 {
    font-size: 1.55em;
    line-height: 38px
}

.vditor-sv .h3 {
    font-size: 1.38em;
    line-height: 27px
}

.vditor-sv .h4 {
    font-size: 1.25em;
    line-height: 25px
}

.vditor-sv .h5 {
    font-size: 1.13em
}

.vditor-sv .h6 {
    font-size: 1em
}

.vditor-sv__marker {
    color: var(--second-color)
}

.vditor-sv__marker--heading {
    color: var(--ir-heading-color)
}

.vditor-sv__marker--bi {
    color: var(--ir-bi-color)
}

.vditor-sv__marker--link {
    color: var(--ir-link-color)
}

.vditor-sv__marker--title {
    color: var(--ir-title-color)
}

.vditor-sv__marker--bracket {
    color: var(--ir-bracket-color)
}

.vditor-sv__marker--paren {
    color: var(--ir-paren-color)
}

.vditor-sv__marker--info {
    color: var(--ir-heading-color)
}

.vditor-sv__marker--strong {
    font-weight: 700
}

.read-only[data-v-1881fb30] .vditor-toolbar {
    display: none
}

.editor-container[data-v-1881fb30] li,
.editor-container[data-v-1881fb30] ol,
.editor-container[data-v-1881fb30] ul {
    list-style: unset
}

.markdown-editor-wrapper[data-v-1881fb30] {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    position: relative;
    width: 100%
}

.markdown-editor-wrapper.disabled[data-v-1881fb30] {
    opacity: .6;
    pointer-events: none
}

.editor-disabled-overlay[data-v-1881fb30] {
    background-color: #0000001a;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1005
}

.vditor-loading-overlay[data-v-1881fb30] {
    align-items: center;
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
    background: #fffc;
    bottom: 0;
    display: flex;
    justify-content: center;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1010
}

.loading-content[data-v-1881fb30] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    pointer-events: none
}

.loading-text[data-v-1881fb30] {
    color: #666;
    font-size: 1rem;
    font-weight: 500;
    margin: 0
}

@media (prefers-color-scheme:dark) {
    .vditor-loading-overlay[data-v-1881fb30] {
        background: #1a1a1acc
    }
    .loading-text[data-v-1881fb30] {
        color: #ccc
    }
}

.editor-container[data-v-1881fb30] {
    flex: 1;
    min-height: 500px;
    overflow-x: hidden;
    overflow-y: auto
}

.vditor-element[data-v-1881fb30] {
    background-color: #fafafa;
    height: -moz-fit-content !important;
    height: fit-content !important;
    min-height: 100%;
    padding: 12px;
    width: 100%
}

.editor-container[data-v-1881fb30] .vditor-toolbar {
    transition: all .3s ease
}

.editor-container[data-v-1881fb30] .vditor-toolbar__item {
    flex-shrink: 0;
    transition: all .2s ease
}

.editor-container[data-v-1881fb30] .vditor-toolbar__item[data-type=toggle-expanded] {
    margin-left: auto;
    order: 9999
}

@media screen and (max-width:768px) {
    .editor-container[data-v-1881fb30] .vditor-toolbar {
        padding: 4px
    }
}

.export-actions[data-v-a7167e99] {
    align-items: center;
    display: flex;
    gap: 8px
}

.button[data-v-a7167e99] {
    align-items: center;
    border-radius: 8px;
    box-sizing: content-box;
    color: #232425;
    cursor: pointer;
    display: inline-flex;
    height: 24px;
    justify-content: center;
    margin: 0;
    padding: 4px;
    position: relative;
    transition: all .2s ease;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 24px
}

@media (max-width:1220px) {
    .button.open-in-new-window[data-v-a7167e99] {
        display: none
    }
}

.button[data-v-a7167e99]:hover {
    background-color: #f5f5f5
}

.button[data-v-a7167e99]:active {
    background-color: #e5e5e5
}

.button .icon[data-v-a7167e99] {
    height: 16px;
    width: 16px
}

.button .icon[data-v-a7167e99] svg {
    height: 100%;
    width: 100%
}

.export-button svg[data-v-a7167e99] {
    height: 16px;
    width: 16px
}

.export-popover-content[data-v-a7167e99] {
    min-width: 280px;
    padding: 12px;
    --n-color: #0f7fff;
    --n-text-color: #0f7fff
}

.export-popover-content[data-v-a7167e99] .n-spin {
    color: #0f7fff;
    --n-color: #0f7fff;
    --n-text-color: #0f7fff
}

.export-options[data-v-a7167e99] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.export-option-title[data-v-a7167e99] {
    color: #333;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px
}

.export-option-list[data-v-a7167e99] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.export-option[data-v-a7167e99] {
    align-items: center;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    gap: 12px;
    padding: 12px;
    transition: all .2s ease
}

.export-option[data-v-a7167e99]:hover {
    background-color: #e3f2fd;
    border-color: #2196f3;
    transform: translateY(-1px)
}

.export-option-icon[data-v-a7167e99] {
    align-items: center;
    display: flex;
    font-size: 20px;
    height: 24px;
    justify-content: center;
    width: 24px
}

.export-option-text[data-v-a7167e99] {
    color: #333;
    font-size: 14px;
    font-weight: 500
}

.export-loading[data-v-a7167e99] {
    align-items: center;
    color: #666;
    display: flex;
    gap: 8px;
    padding: 12px
}

.export-success[data-v-a7167e99] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 12px
}

.export-success-message[data-v-a7167e99] {
    color: #232425;
    font-size: 14px;
    font-weight: 500
}

.exported-url-link[data-v-a7167e99] {
    align-items: center;
    background-color: #f0f8ff;
    border: 1px solid #1890ff;
    border-radius: 6px;
    color: #1890ff;
    display: flex;
    font-size: 14px;
    font-weight: 500;
    gap: 6px;
    padding: 10px 16px;
    text-decoration: none;
    transition: all .2s
}

.exported-url-link[data-v-a7167e99]:hover {
    background-color: #e6f7ff;
    transform: translateY(-1px)
}

.exported-url-link svg[data-v-a7167e99] {
    height: 16px;
    width: 16px
}

@media (prefers-color-scheme:dark) {
    .button[data-v-a7167e99] {
        color: #fff
    }
    .button[data-v-a7167e99]:hover {
        background-color: #333
    }
    .button[data-v-a7167e99]:active {
        background-color: #3a3a3a
    }
    .export-option-title[data-v-a7167e99] {
        color: #e0e0e0
    }
    .export-option[data-v-a7167e99] {
        background-color: #2a2a2a;
        border-color: #404040
    }
    .export-option[data-v-a7167e99]:hover {
        background-color: #1e3a5f;
        border-color: #4096ff
    }
    .export-option-text[data-v-a7167e99] {
        color: #e0e0e0
    }
    .export-loading[data-v-a7167e99],
    .export-success-message[data-v-a7167e99] {
        color: #ccc
    }
    .exported-url-link[data-v-a7167e99] {
        background-color: #1a1a1a;
        border-color: #4096ff;
        color: #4096ff
    }
    .exported-url-link[data-v-a7167e99]:hover {
        background-color: #262626
    }
}