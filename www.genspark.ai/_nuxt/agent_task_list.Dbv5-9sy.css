.project[data-v-c88f9155] {
    align-items: flex-start;
    border-radius: 12px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 4px;
    justify-content: space-between;
    margin-top: 0;
    padding: 8px 14px;
    position: relative;
    width: 100%;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.project.active[data-v-c88f9155],
.project.popover-visible[data-v-c88f9155] {
    background: #eee
}

.project .more[data-v-c88f9155] {
    background: #fff;
    border-radius: 30%;
    color: #232425;
    display: none;
    height: 20px;
    width: 20px
}

@media (prefers-color-scheme:dark) {
    .project .more[data-v-c88f9155] {
        background: #444;
        color: #fff
    }
}

@media (hover:hover) {
    .project.active[data-v-c88f9155]:hover,
    .project.popover-visible[data-v-c88f9155]:hover,
    .project[data-v-c88f9155]:hover {
        background: #eee
    }
    .project:hover .icon[data-v-c88f9155] {
        display: flex
    }
}

.project.active .icon[data-v-c88f9155],
.project.popover-visible .icon[data-v-c88f9155] {
    display: flex
}

@media (hover:none) {
    .project .more[data-v-c88f9155] {
        display: flex
    }
}

.project .name[data-v-c88f9155] {
    align-items: center;
    color: #232425;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 4px;
    justify-content: flex-start;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis
}

.project.active .name[data-v-c88f9155] {
    font-weight: 700
}

.project .name .icon[data-v-c88f9155] {
    color: #909499;
    display: flex;
    height: 16px;
    width: 16px
}

.project .name .icon.active[data-v-c88f9155] {
    color: #0f7fff
}

.project .name .text[data-v-c88f9155] {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    line-clamp: 1
}

.project-info[data-v-c88f9155] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    min-height: 20px;
    width: 100%
}

.time-display[data-v-c88f9155] {
    color: #909499;
    font-size: 12px;
    padding: 2px 0;
    white-space: nowrap
}

.project-controls[data-v-c88f9155] {
    box-sizing: border-box;
    color: #232425;
    justify-content: flex-end;
    min-height: 20px;
    padding: 0 10px
}

.menu-wrapper[data-v-c88f9155],
.project-controls[data-v-c88f9155] {
    display: flex;
    flex-direction: row;
    gap: 10px
}

.menu-wrapper[data-v-c88f9155] {
    align-items: center;
    bottom: 6px;
    justify-content: center;
    right: 6px
}

.icon[data-v-c88f9155] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.icon[data-v-c88f9155] svg {
    height: 100%;
    width: 100%
}

.menu[data-v-c88f9155] {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px #00000026;
    min-width: 120px;
    padding: 6px 0
}

.menu .button[data-v-c88f9155] {
    background: none;
    border: none;
    box-sizing: border-box;
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    padding: 8px 16px;
    text-align: left;
    transition: background-color .2s ease;
    width: 100%
}

.menu .button[data-v-c88f9155]:hover {
    background: #f5f5f5
}

.menu .button[data-v-c88f9155] {
    color: #232425
}

.menu .button.delete[data-v-c88f9155] {
    color: #ff3d3d
}

@media (prefers-color-scheme:dark) {
    .project .name[data-v-c88f9155] {
        color: #fff
    }
    .project.active[data-v-c88f9155],
    .project.popover-visible[data-v-c88f9155] {
        background: #444
    }
    .time-display[data-v-c88f9155] {
        color: #ccc
    }
    @media (hover:hover) {
        .project.active[data-v-c88f9155]:hover,
        .project.popover-visible[data-v-c88f9155]:hover,
        .project[data-v-c88f9155]:hover {
            background: #4448
        }
    }
    .menu[data-v-c88f9155] {
        background: #444
    }
    .menu .button[data-v-c88f9155]:hover {
        background: #555
    }
    .menu .button[data-v-c88f9155] {
        color: #fff
    }
}

.highlight-content[data-v-7442e604] em {
    color: #000;
    font-style: normal;
    font-weight: 700
}

.description-text[data-v-7442e604] {
    display: -webkit-box;
    text-overflow: ellipsis;
    word-break: break-all;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden
}

.time-display[data-v-7442e604] {
    color: #909499;
    font-size: 12px;
    white-space: nowrap
}

.main-mask[data-v-85465f6c] {
    background-color: #00000080;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1001
}

.fade-enter-active[data-v-85465f6c],
.fade-leave-active[data-v-85465f6c] {
    transition: opacity .3s ease
}

.fade-enter-from[data-v-85465f6c],
.fade-leave-to[data-v-85465f6c] {
    opacity: 0
}

.icon[data-v-85465f6c] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.icon[data-v-85465f6c] svg {
    height: 100%;
    width: 100%
}

.search-input[data-v-85465f6c] {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 13px;
    outline: none;
    padding: 8px 32px 8px 12px;
    transition: border-color .2s;
    width: 100%
}

.search-input[data-v-85465f6c]:focus {
    border-color: #007acc;
    box-shadow: 0 0 0 2px #007acc1a
}

.clear-icon[data-v-85465f6c],
.search-icon[data-v-85465f6c] {
    align-items: center;
    color: #666;
    cursor: pointer;
    display: flex;
    justify-content: center;
    position: absolute;
    right: 8px;
    transition: color .2s
}

.clear-icon[data-v-85465f6c]:hover,
.search-icon[data-v-85465f6c]:hover {
    color: #333
}

.search-status[data-v-85465f6c] {
    color: #666;
    font-size: 11px;
    margin-top: 8px;
    padding: 0 4px
}

.search-info[data-v-85465f6c] {
    font-weight: 500
}

.search-loading[data-v-85465f6c] {
    color: #007acc
}

.search-no-results[data-v-85465f6c] {
    color: #999
}

.my-projects[data-v-85465f6c] {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 20px;
    min-height: 200px;
    padding-bottom: 50px
}

.left-head[data-v-85465f6c] {
    align-items: center;
    background: #fafafa;
    box-sizing: border-box;
    color: #232425;
    display: flex;
    flex-direction: row;
    flex-grow: 0;
    flex-shrink: 0;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    justify-content: space-between;
    left: 0;
    line-height: 27px;
    padding: 12px 16px 12px 30px;
    top: 0;
    width: 100%;
    z-index: 2
}

.left-head .icon[data-v-85465f6c] {
    cursor: pointer
}

.left-side[data-v-85465f6c] {
    background: #fafafa;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    height: 100%;
    justify-content: space-between;
    position: absolute;
    width: 360px;
    z-index: 1002
}

.left-side>.divider[data-v-85465f6c] {
    background: #eaeaea30;
    box-sizing: border-box;
    height: 1px;
    margin: 0 20px;
    width: calc(100% - 40px)
}

.left-main[data-v-85465f6c] {
    flex-grow: 1;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 0 20px
}

@media (min-width:1221px) {
    .left-side.expand-enter-active[data-v-85465f6c],
    .left-side.expand-leave-active[data-v-85465f6c] {
        overflow: hidden;
        transition: width .3s ease
    }
    .left-side.expand-enter-from[data-v-85465f6c],
    .left-side.expand-leave-to[data-v-85465f6c] {
        width: 0
    }
    .left-side[data-v-85465f6c] {
        left: 0;
        width: 335px
    }
}

@media (max-width:1220px) {
    .my-projects[data-v-85465f6c] {
        margin-top: 0;
        padding: 0 16px 200px
    }
    .left-side.expand-enter-active[data-v-85465f6c],
    .left-side.expand-leave-active[data-v-85465f6c] {
        transition: left .3s ease
    }
    .left-side.expand-enter-from[data-v-85465f6c],
    .left-side.expand-leave-to[data-v-85465f6c] {
        left: -335px
    }
    .left-side[data-v-85465f6c] {
        background: #fafafa;
        height: 100vh;
        left: 0;
        position: fixed;
        width: 335px;
        z-index: 1002
    }
    .left-main[data-v-85465f6c] {
        display: block;
        flex-grow: 1;
        flex-shrink: 1;
        height: 100%;
        padding: 25px 0 0;
        position: relative
    }
}

.search-skeleton-container[data-v-85465f6c] {
    display: flex;
    flex-direction: column
}

.search-skeleton-card[data-v-85465f6c] {
    align-items: center;
    animation: pulse-85465f6c 1.5s infinite;
    background: #f5f5f5;
    display: flex;
    gap: 12px
}

@keyframes pulse-85465f6c {
    0% {
        background: #f5f5f5
    }
    50% {
        background: #ebebeb
    }
    to {
        background: #f5f5f5
    }
}

@media (prefers-color-scheme:dark) {
    .left-head[data-v-85465f6c],
    .left-side[data-v-85465f6c] {
        background: #333
    }
    .left-head[data-v-85465f6c] {
        color: #fff
    }
    .search-input[data-v-85465f6c] {
        background-color: #2a2a2a;
        border-color: #555;
        color: #fff
    }
    .search-input[data-v-85465f6c]:focus {
        border-color: #007acc;
        box-shadow: 0 0 0 2px #007acc33
    }
    .search-input[data-v-85465f6c]::-moz-placeholder {
        color: #999
    }
    .search-input[data-v-85465f6c]::placeholder {
        color: #999
    }
    .clear-icon[data-v-85465f6c],
    .search-icon[data-v-85465f6c] {
        color: #999
    }
    .clear-icon[data-v-85465f6c]:hover,
    .search-icon[data-v-85465f6c]:hover {
        color: #fff
    }
    .search-status[data-v-85465f6c] {
        color: #ccc
    }
    .search-loading[data-v-85465f6c] {
        color: #007acc
    }
    .search-no-results[data-v-85465f6c] {
        color: #999
    }
    .search-skeleton-card[data-v-85465f6c] {
        animation: pulse-dark-85465f6c 1.5s infinite;
        background: #333
    }
    @keyframes pulse-dark-85465f6c {
        0% {
            background: #333
        }
        50% {
            background: #3a3a3a
        }
        to {
            background: #333
        }
    }
}

.custom-modal-overlay[data-v-85465f6c] {
    left: 0;
    position: fixed;
    top: 0;
    z-index: 9999
}

.custom-modal-content[data-v-85465f6c],
.custom-modal-overlay[data-v-85465f6c] {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%
}

.custom-modal-content[data-v-85465f6c] {
    padding: 20px
}

.modal-fade-enter-active[data-v-85465f6c],
.modal-fade-leave-active[data-v-85465f6c] {
    transition: all .3s ease
}

.modal-fade-enter-from[data-v-85465f6c],
.modal-fade-leave-to[data-v-85465f6c] {
    opacity: 0;
    transform: translateY(100px)
}

.modal-fade-enter-to[data-v-85465f6c],
.modal-fade-leave-from[data-v-85465f6c] {
    opacity: 1;
    transform: translateY(0)
}