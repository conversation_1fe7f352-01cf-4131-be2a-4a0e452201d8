import {
    d as C,
    b as t,
    o
} from "./Cf0SOiw0.js";
const L = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const r = {
    render: function(r, s) {
        return o(), C("svg", L, s[0] || (s[0] = [t("g", {
            id: "Frame"
        }, [t("path", {
            id: "Vector",
            d: "M20.0297 6.76641C20.1703 6.90703 20.25 7.09687 20.25 7.29609V21.75C20.25 22.1648 19.9148 22.5 19.5 22.5H4.5C4.08516 22.5 3.75 22.1648 3.75 21.75V2.25C3.75 1.83516 4.08516 1.5 4.5 1.5H14.4539C14.6531 1.5 14.8453 1.57969 14.9859 1.72031L20.0297 6.76641ZM18.5203 7.64062L14.1094 3.22969V7.64062H18.5203ZM13.4845 11.1994L12.0497 13.597L10.6008 11.1984C10.5757 11.157 10.5404 11.1228 10.4982 11.099C10.4561 11.0752 10.4085 11.0627 10.3601 11.0627H9.45914C9.40605 11.0627 9.35405 11.0778 9.30914 11.1061C9.27787 11.1258 9.25079 11.1515 9.22945 11.1816C9.2081 11.2118 9.19292 11.2459 9.18475 11.2819C9.17659 11.318 9.17561 11.3553 9.18188 11.3917C9.18814 11.4281 9.20152 11.463 9.22125 11.4942L11.1511 14.5509L9.19523 17.663C9.16704 17.7078 9.15209 17.7597 9.15211 17.8127C9.15211 17.8873 9.18174 17.9589 9.23449 18.0116C9.28723 18.0644 9.35877 18.094 9.43336 18.094H10.241C10.289 18.094 10.3362 18.0817 10.378 18.0583C10.4199 18.0349 10.4551 18.0012 10.4803 17.9604L11.9498 15.5822L13.41 17.9599C13.4352 18.0009 13.4704 18.0347 13.5124 18.0582C13.5544 18.0817 13.6017 18.094 13.6498 18.094H14.5282C14.582 18.094 14.6348 18.0785 14.6801 18.0495C14.7112 18.0295 14.738 18.0036 14.7591 17.9733C14.7802 17.943 14.7951 17.9088 14.803 17.8727C14.8109 17.8366 14.8116 17.7994 14.805 17.763C14.7985 17.7267 14.7849 17.6919 14.7649 17.6609L12.8002 14.6011L14.7994 11.4963C14.8285 11.4509 14.844 11.398 14.8439 11.344C14.8439 11.2694 14.8143 11.1979 14.7615 11.1451C14.7088 11.0924 14.6372 11.0627 14.5627 11.0627H13.7259C13.6773 11.0628 13.6296 11.0754 13.5873 11.0994C13.545 11.1234 13.5097 11.1579 13.4848 11.1996L13.4845 11.1994Z",
            fill: "#4FA16B"
        })], -1)]))
    }
};
export {
    r as E
};