import {
    _ as e
} from "./e-ES_T8J.js";
import {
    _ as t,
    r as a,
    ax as o,
    G as s,
    V as i,
    S as r,
    s as l,
    c as n,
    i as p,
    ay as d,
    h as c,
    a3 as u,
    v as m,
    d as h,
    b as v,
    e as x,
    w as g,
    n as w,
    y as b,
    T as f,
    R as y,
    F as j,
    C as k,
    o as _,
    f as C,
    k as S,
    a as A,
    ai as I,
    q as T,
    t as E,
    a4 as U
} from "./Cf0SOiw0.js";
import {
    u as F,
    a as L,
    b as M,
    c as N,
    H as D,
    d as P,
    _ as B,
    A as G,
    I as H,
    M as $,
    e as R
} from "./HCr483ve.js";
import {
    A as q
} from "./Cl89jLsD.js";
import {
    u as K,
    I as W
} from "./BdlGQsae.js";
import {
    _ as z
} from "./DjawIWev.js";
import {
    Y as O
} from "./CQ2glRxo.js";
import {
    C as V
} from "./BspXWmOn.js";
import {
    M as J
} from "./Dc8Bac8D.js";
import {
    u as Q
} from "./B0db5Fvl.js";
import {
    A as Y,
    a as Z
} from "./4s4Iy95q.js";
import {
    G as X
} from "./DrzlY6u5.js";
import {
    N as ee
} from "./CW991W2w.js";
import {
    N as te
} from "./BjWUbj3w.js";
import "./Bm_HbXT2.js";
import "./BUs-AQWo.js";
import "./DW6cX6jm.js";
import "./CKd5XOy1.js";
import "./DOnko34f.js";
import "./DyMB-pVc.js";
import "./C38RzRfR.js";
import "./BLWq1oPC.js";
import "./WZsIN7xM.js";
import "./pB_XRIgB.js";
import "./Jr9eiJio.js";
import "./By6xEfKc.js";
import "./B7VeW_-d.js";
import "./BrPr1fm5.js";
import "./DAjjhrgi.js";
import "./MpDLC7up.js";
import "./DpMvtoun.js";
import "./9wLWmnxl.js";
import "./DGJMLFjI.js";
import "./BihyrXkC.js";
import "./BuhfKjCJ.js";
import "./BGK9k_mT.js";
import "./CAmLbDGM.js";
import "./DQpEsQQa.js";
import "./B6noBY_5.js";
import "./Cu_n4xpI.js";
import "./W5AxVKvJ.js";
import "./DWCxnUK7.js";
import "./P7LDOl0u.js";
import "./DT-NG54s.js";
import "./COYh4g7i.js";
import "./CaEkZ53E.js";
import "./DXvAIxvL.js";
import "./Bzg9uoz_.js";
import "./BPQGB51Y.js";
import "./C-H3edso.js";
const ae = {
        class: "index-layout"
    },
    oe = {
        class: "sidebar"
    },
    se = {
        class: "sidebar-header"
    },
    ie = {
        class: "header"
    },
    re = {
        key: 0,
        src: B
    },
    le = {
        class: "flex flex-col items-center justify-center gap-[8px] cursor-pointer group"
    },
    ne = {
        class: "w-[32px] h-[32px] rounded-[10px] p-[6px] box-border group-hover:bg-white dark:group-hover:bg-white/10"
    },
    pe = {
        class: "flex flex-wrap w-[281px] rounded-[12px] py-[15px] gap-[4px]"
    },
    de = ["onClick"],
    ce = {
        class: "flex flex-col items-center gap-[8px] mb-[4px]"
    },
    ue = {
        class: "text-[12px] text-[#606366] dark:text-[#b0b0b0] text-center mt-[6px]"
    },
    me = {
        class: "w-[90%] text-center text-[11px] mt-[2px] text-[#606366] dark:text-[#b0b0b0] truncate"
    },
    he = ["onClick"],
    ve = {
        class: "title text-[11px] mt-[2px] text-[#606366] dark:text-[#b0b0b0]"
    },
    xe = {
        key: 0,
        class: "new-tag"
    },
    ge = {
        key: 1,
        class: "w-2 h-2 bg-[#0F7FFF] rounded-full"
    },
    we = {
        class: "sidebar-footer"
    },
    be = {
        key: 0,
        class: "invite-wrapper"
    },
    fe = {
        class: "invite-banner"
    },
    ye = {
        class: "invite-title"
    },
    je = {
        class: "icon"
    },
    ke = {
        class: "w-[calc(100vw-32px)] md:w-[680px] min-h-[100px] md:h-[382px] animation-fadeIn"
    },
    _e = t(Object.assign({
        name: "IndexLayout"
    }, {
        __name: "index_layout",
        props: {
            hideBottomBar: {
                type: Boolean,
                default: !1
            },
            agentType: {
                type: String,
                default: ""
            },
            version: {
                type: String,
                default: ""
            },
            forceCollapseSidebar: {
                type: Boolean,
                default: !1
            },
            projectType: {
                type: String,
                default: ""
            }
        },
        emits: ["refresh"],
        setup(t, {
            expose: B,
            emit: _e
        }) {
            const Ce = t,
                Se = _e,
                Ae = p("currentUser"),
                {
                    getAgentListPopover: Ie
                } = F(),
                Te = Ie(),
                Ee = a(!0),
                Ue = L(),
                {
                    isClickedUpdate: Fe
                } = o(Ue),
                {
                    currentPath: Le,
                    showAiChatUpdateModal: Me,
                    handlerAiChatUpdateConfirmModal: Ne,
                    handlerUpgradeModal: De
                } = M(),
                Pe = a(null),
                Be = N(),
                {
                    shouldShowGuide: Ge
                } = o(Be),
                He = s(),
                $e = i(),
                Re = $e.path,
                {
                    hideBottomBar: qe,
                    agentType: Ke
                } = r(Ce),
                {
                    t: We
                } = l(),
                ze = a(!1);
            "/agents" === $e.path && localStorage.setItem("agentNewTagShown", "1"), ze.value = "1" !== localStorage.getItem("agentNewTagShown");
            const Oe = a(!1);
            Oe.value = "1" !== localStorage.getItem("settingNewTagShown");
            const Ve = () => {
                    Oe.value = !1, localStorage.setItem("settingNewTagShown", "1")
                },
                Je = n((() => {
                    var e;
                    return (null == (e = Ae.value) ? void 0 : e.gk_dogfood) || !1
                })),
                Qe = a([{
                    title: We("components.index_layout.home"),
                    icon: d(P),
                    iconActive: d(D),
                    path: "/",
                    group: "home",
                    active: !1
                }, {
                    title: "AI Inbox",
                    icon: d(W),
                    iconActive: d(W),
                    path: "/ai_inbox",
                    newTag: !1,
                    group: "me",
                    dogfoodFunc: !0
                }, {
                    title: We("components.index_layout.aidrive"),
                    icon: d(q),
                    iconActive: d(q),
                    path: `/${Y}`,
                    newTag: !1,
                    group: "me",
                    dogfoodFunc: !1
                }]),
                Ye = a(!0),
                Ze = a(0),
                Xe = () => {
                    Ze.value = window.innerWidth
                },
                et = a(!1),
                tt = {
                    youtubeLink: "https://www.youtube.com/watch?v=5Rba4qCZyL4",
                    videoModalKey: "indexVideoModalKey-11",
                    path: "/agents?type=ai_developer",
                    desc: We("pages.agents.ai_developer.title")
                },
                at = a({
                    link: tt.youtubeLink,
                    embedIframe: !1,
                    styleClass: "!bg-transparent"
                }),
                ot = Q(),
                {
                    PLACEHOLDER_MAP: st
                } = ot,
                it = K(),
                rt = () => {};
            c((() => {
                Ye.value = !1, Xe(), window.addEventListener("resize", Xe), Ue.setIsClickedUpdate("true" === localStorage.getItem("isClickedUpdate")), Pe.value = Te.find((e => {
                    if ($e.fullPath.startsWith("/agents") && $e.fullPath.startsWith(e.path) || Ce.projectType && e.type === Ce.projectType) return e
                }))
            })), u((() => {
                window.removeEventListener("resize", Xe)
            }));
            const lt = n((() => 0 !== Ze.value && Ze.value <= 1220));
            m([Ae, Ge], (() => {
                if ("/" === $e.path && (null == Ae ? void 0 : Ae.value) && !Ge.value) {
                    const e = tt.videoModalKey;
                    localStorage.getItem(e) || (et.value = !0, localStorage.setItem(e, "1"))
                }
                "/agents" === $e.path && $e.query.type && (Ee.value = !0)
            }), {
                immediate: !0
            });
            const nt = e => {
                k.log("onPopState", e), location.reload()
            };
            c((() => {
                window.addEventListener("popstate", nt)
            })), u((() => {
                window.removeEventListener("popstate", nt)
            }));
            const pt = e => !(e.path !== `/${Y}` || !Re.startsWith(`/${Z}`)) || (!0 === e.active || (Re === e.path.split("?")[0] ? "agents" !== e.group || Ke.value === e.agentType : void 0)),
                dt = e => {
                    Ye.value = !0, ot.setPlaceholder(st.SEARCH_AGENT), e.update && !Ue.isClickedUpdate ? (Me.value = !0, Ue.setIsClickedUpdate(!0), Le.value = e.path) : (Pe.value = null, Qe.value.forEach((e => {
                        e.active = !1
                    })), e.active = !0, setTimeout((() => {
                        history.pushState({}, "", e.path), He.push(e.path)
                    }), 300)), Se("refresh"), it.setEditSlideStatus("normal")
                },
                ct = () => {
                    Ne((() => {
                        history.pushState({}, "", Le.value), He.push(Le.value)
                    }))
                },
                ut = () => {
                    De((() => {
                        history.pushState({}, "", Le.value), He.push(Le.value)
                    }))
                },
                mt = () => {
                    et.value = !1
                },
                ht = () => {
                    window.open(tt.path, "_blank")
                };
            return B({
                isCollapsed: Ee
            }), (a, o) => {
                const s = e;
                return _(), h(j, null, [v("div", ae, [x(s, null, {
                    default: g((() => [b(k).isGensparkApp() || b(lt) && b(qe) ? C("", !0) : (_(), h("div", {
                        key: 0,
                        class: w(["index-layout-sidebar j-index-layout-sidebar w-[64px]", {
                            collapsed: (b(Ee) || t.forceCollapseSidebar) && !b(lt)
                        }])
                    }, [v("div", oe, [v("div", se, [v("div", ie, [v("div", {
                        class: "logo",
                        onClick: o[0] || (o[0] = e => dt(b(Qe)[0]))
                    }, [b(k).isGensparkAppIos() ? (_(), h("img", re)) : C("", !0), x(b(X), {
                        class: "w-[24px] h-[24px]"
                    })])]), v("div", {
                        class: w(["menu-items mt-[30px]", {
                            mobile: b(lt)
                        }])
                    }, [x(b(ee), {
                        trigger: "hover",
                        placement: "right",
                        class: "menu-popover-wrapper",
                        "show-arrow": !1,
                        "content-style": {
                            padding: "0px 7px"
                        }
                    }, {
                        trigger: g((() => [v("div", le, [v("div", ne, [x(b(G), {
                            class: "w-[20px] h-[20px] group-hover:scale-[1.2] transition-all duration-300 ease-in-out"
                        })]), o[4] || (o[4] = v("div", {
                            class: "text-center justify-center text-[#606366] dark:text-[#b0b0b0] text-[11px] font-normal font-['Arial'] leading-none tracking-tight"
                        }, " New ", -1))])])),
                        default: g((() => [v("div", pe, [o[5] || (o[5] = v("div", {
                            class: "absolute left-[-12px] top-[28px] w-0 h-0 border-t-[8px] border-b-[8px] border-l-[12px] border-t-transparent border-b-transparent border-l-white rotate-180 dark:border-l-[#2a2a2a]"
                        }, null, -1)), (_(!0), h(j, null, S(b(Te).filter((e => {
                            var t;
                            return !e.isDogfood || e.isDogfood && (null == (t = b(Ae)) ? void 0 : t.gk_dogfood)
                        })), (e => (_(), h("div", {
                            key: e.name,
                            class: "w-[91px] rounded-[16px] bg-white dark:bg-[#2a2a2a] p-[12px] box-border cursor-pointer hover:bg-[#F5F5F5] dark:hover:bg-[#3a3a3a] group",
                            onClick: t => (e => {
                                window.open(e.path, "_blank")
                            })(e)
                        }, [v("div", ce, [(_(), A(I(e.icon), {
                            class: "w-[24px] h-[24px] group-hover:scale-[1.2] transition-all duration-300 ease-in-out",
                            style: T({
                                color: e.color
                            })
                        }, null, 8, ["style"])), v("div", ue, E(e.name), 1)])], 8, de)))), 128))])])),
                        _: 1
                    }), x(U, {
                        name: "current-agent",
                        mode: "out-in",
                        appear: ""
                    }, {
                        default: g((() => [b(Pe) ? (_(), h("div", {
                            key: 0,
                            class: "menu-item flex flex-col items-center justify-center group",
                            onClick: rt
                        }, [v("div", {
                            class: w(["icon w-[32px] h-[32px] rounded-[10px] p-[6px] box-border group-hover:bg-white dark:group-hover:bg-white/10", [pt(b(Pe)) ? "bg-white dark:bg-white/10" : ""]])
                        }, [(_(), A(I(b(Pe).iconActive), {
                            class: "w-[24px] h-[24px] group-hover:scale-[1.2] transition-all duration-300 ease-in-out dark:text-white"
                        }))], 2), v("div", me, E(b(Pe).name), 1), o[6] || (o[6] = v("div", {
                            class: "w-[50%] h-[1px] bg-[#DEDEDE] dark:opacity-30 mt-[24px]"
                        }, null, -1))])) : C("", !0)])),
                        _: 1
                    }), (_(), h(j, null, S(["home", "agents", "me"], ((e, t) => (_(), h(j, null, [(_(!0), h(j, null, S(b(Qe).filter((t => t.group === e && !t.hide && (!t.dogfoodFunc || b(Je)))), (e => (_(), h("div", {
                        class: "menu-item flex flex-col items-center justify-center group",
                        key: e.title,
                        onClick: t => dt(e)
                    }, [v("div", {
                        class: w(["icon w-[32px] h-[32px] rounded-[10px] p-[6px] box-border group-hover:bg-white dark:group-hover:bg-white/10", [pt(e) ? "bg-white dark:bg-white/10" : ""]])
                    }, [pt(e) ? (_(), A(I(e.iconActive), {
                        key: 0,
                        class: "group-hover:scale-[1.2] transition-all duration-300 ease-in-out dark:text-white"
                    })) : (_(), A(I(e.icon), {
                        key: 1,
                        class: "group-hover:scale-[1.2] transition-all duration-300 ease-in-out"
                    }))], 2), v("div", ve, E(e.title), 1), e.newTag ? (_(), h("div", xe, "New")) : C("", !0), e.update && !b(Fe) ? (_(), h("div", ge)) : C("", !0)], 8, he)))), 128))], 64)))), 64))], 2)]), v("div", we, [new Date >= new Date("2025-05-29") ? (_(), h("div", be, [v("div", fe, [v("div", ye, E(a.$t("components.index_layout.get_credits_for_each")), 1), v("div", {
                        class: "invite-button hover:scale-[1.2] transition-all duration-300 ease-in-out",
                        onClick: o[1] || (o[1] = e => b(k).windowopen("/invite_member"))
                    }, [v("div", je, [x(b(H))]), v("span", null, E(a.$t("components.index_layout.share_with_friends")), 1)])])])) : C("", !0), v("div", {
                        class: "setting-wrapper",
                        onClick: Ve
                    }, [x(b(z), {
                        "show-my-sparkpage": !1,
                        "show-my-bookmarks": !1,
                        "show-my-tasks": !1,
                        "show-browsing-history": !1,
                        "show-pricing": !0,
                        placement: "top-start"
                    }, {
                        triggerIcon: g((() => [x(b($), {
                            class: "hover:scale-[1.2] transition-all duration-300 ease-in-out"
                        })])),
                        _: 1
                    }), C("", !0)])])])], 2))])),
                    _: 1
                }), v("div", {
                    class: w(["index-layout-content", {
                        loading: b(Ye)
                    }])
                }, [f(a.$slots, "content", {}, void 0, !0)], 2), x(b(te), {
                    show: b(et),
                    "onUpdate:show": o[2] || (o[2] = e => y(et) ? et.value = e : null),
                    "mask-closable": !1
                }, {
                    default: g((() => [v("div", null, [x(b(V), {
                        onClick: mt,
                        class: "fixed top-[30px] right-[30px] w-[32px] h-[32px] text-white cursor-pointer"
                    }), v("div", ke, [x(O, {
                        data: b(at),
                        is_editing: !1,
                        "cover-img-style-class": "w-[calc(100vw-32px)] md:w-[680px] md:h-[382px]"
                    }, null, 8, ["data"]), v("div", {
                        class: "absolute bottom-[-72px] left-[50%] translate-x-[-50%] px-[30px] py-2.5 border border-white rounded-[40px] inline-flex justify-center items-center gap-2.5 text-white text-base font-bold font-['Arial'] leading-normal cursor-pointer whitespace-nowrap dark:text-white",
                        onClick: ht
                    }, [v("div", null, E(tt.desc), 1), x(b(J), {
                        class: "w-[16px] h-[16px] rotate-180"
                    })])])])])),
                    _: 1
                }, 8, ["show"])]), x(b(te), {
                    show: b(Me),
                    "onUpdate:show": o[3] || (o[3] = e => y(Me) ? Me.value = e : null),
                    "mask-closable": !1
                }, {
                    default: g((() => [x(R, {
                        onHandlerAiChatUpdateConfirmModal: ct,
                        onHandlerUpgradeModal: ut
                    })])),
                    _: 1
                }, 8, ["show"])], 64)
            }
        }
    }), [
        ["__scopeId", "data-v-62861aa6"]
    ]);
export {
    _e as
    default
};