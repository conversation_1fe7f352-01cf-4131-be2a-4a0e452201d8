.tools-container[data-v-fdd87f59] {
    padding: 20px
}

@media (max-width:1220px) {
    .tools-container[data-v-fdd87f59] {
        box-sizing: border-box;
        max-width: 100%
    }
}

.main-tools[data-v-fdd87f59] {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
    width: 100%
}

.main-tools[data-v-fdd87f59]::-webkit-scrollbar {
    display: none
}

.tool-card[data-v-fdd87f59] {
    background-color: #f5f5f5;
    border-radius: 12px;
    box-sizing: border-box;
    cursor: pointer;
    flex-shrink: 0;
    height: 160px;
    padding-top: 29px;
    text-align: center;
    transition: transform .3s ease;
    width: 260px
}

.tool-card[data-v-fdd87f59]:hover:not(.coming-soon-card) {
    transform: scale(1.05)
}

.coming-soon-card[data-v-fdd87f59] {
    cursor: not-allowed
}

.coming-soon-card .tool-header[data-v-fdd87f59],
.coming-soon-card .tool-title[data-v-fdd87f59] {
    color: #666
}

.tool-content[data-v-fdd87f59] {
    flex-direction: column;
    position: relative
}

.tool-content[data-v-fdd87f59],
.tool-header[data-v-fdd87f59] {
    align-items: center;
    display: flex
}

.tool-header[data-v-fdd87f59] {
    gap: 8px;
    justify-content: center;
    margin-bottom: 8px
}

.tool-icon[data-v-fdd87f59] svg {
    height: 24px;
    width: 24px
}

.tool-title[data-v-fdd87f59] {
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

.tool-image[data-v-fdd87f59] {
    border-radius: 12px;
    height: auto;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.coming-soon[data-v-fdd87f59] {
    color: #666;
    font-size: 14px;
    position: absolute;
    top: 30px
}

.quick-apps-title[data-v-fdd87f59] {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 20px
}

.quick-tools-grid[data-v-fdd87f59] {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(3, 1fr);
    max-width: 800px
}

.quick-tool-card[data-v-fdd87f59] {
    border-radius: 12px;
    cursor: pointer;
    min-height: 140px;
    overflow: hidden;
    position: relative;
    transition: transform .3s ease
}

.quick-tool-card[data-v-fdd87f59]:hover {
    transform: scale(1.05)
}

.quick-tool-card img[data-v-fdd87f59] {
    height: auto;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.quick-tool-title[data-v-fdd87f59] {
    bottom: 5px;
    color: #fff;
    font-weight: 700;
    left: 10px;
    position: absolute;
    text-shadow: 0 2px 4px rgba(0, 0, 0, .3)
}

.hidden-input[data-v-fdd87f59] {
    display: none
}

.fullscreen-editor[data-v-fdd87f59] {
    background: #fff;
    box-sizing: border-box;
    height: 100vh;
    left: 0;
    position: fixed;
    top: 0;
    width: 100vw;
    z-index: 1000
}

@keyframes loading-fdd87f59 {
    0% {
        background-position: 200% 0
    }
    to {
        background-position: -200% 0
    }
}

.loading-skeleton[data-v-fdd87f59] {
    align-items: center;
    background: #fff;
    display: flex;
    height: 100vh;
    justify-content: center;
    left: 0;
    position: fixed;
    top: 0;
    width: 100vw;
    z-index: 999
}

.skeleton-content[data-v-fdd87f59] {
    animation: loading-fdd87f59 1.5s infinite;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: 12px;
    height: 80%;
    width: 80%
}

@media (max-width:768px) {
    .tools-container[data-v-fdd87f59] {
        margin-bottom: 100px
    }
    .quick-tools-grid[data-v-fdd87f59] {
        display: grid;
        gap: 20px;
        grid-template-columns: repeat(2, 1fr)
    }
    .quick-tool-card[data-v-fdd87f59] {
        min-height: 90px
    }
    .main-tools[data-v-fdd87f59] {
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none
    }
    .quick-tool-card[data-v-fdd87f59]:hover,
    .tool-card[data-v-fdd87f59]:hover:not(.coming-soon-card) {
        transform: none
    }
}

@media (prefers-color-scheme:dark) {
    .tool-card[data-v-fdd87f59] {
        background-color: #333
    }
    .loading-skeleton[data-v-fdd87f59] {
        background: #1a1a1a
    }
    .skeleton-content[data-v-fdd87f59] {
        background: linear-gradient(90deg, #333 25%, #444, #333 75%);
        background-size: 200% 100%
    }
}