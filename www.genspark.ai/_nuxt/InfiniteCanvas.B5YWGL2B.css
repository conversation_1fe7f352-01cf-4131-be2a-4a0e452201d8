.content-wrapper[data-v-736324de] {
    position: relative
}

.page-title[data-v-736324de] {
    overflow: hidden;
    pointer-events: auto;
    position: absolute;
    text-overflow: ellipsis;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    white-space: nowrap
}

.page-title-text[data-v-736324de] {
    color: #999;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif;
    font-size: 13px;
    line-height: 1.4;
    line-height: var(--text-node-default-line-height)
}

.resize-handle[data-v-736324de] {
    box-sizing: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.resize-handle[data-v-736324de]:hover {
    transform: scale(1.2)
}

.rotation-handle[data-v-736324de] {
    box-sizing: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.rotation-handle[data-v-736324de]:hover {
    transform: scale(1.2)
}

.rotation-line[data-v-736324de] {
    pointer-events: none
}

.frame-node[data-v-fd541656],
.frame-node-content[data-v-fd541656] {
    height: 100%;
    position: relative;
    width: 100%
}

.html-fragment-node[data-v-86daf7e4] {
    height: 100%;
    overflow: auto;
    position: relative;
    width: 100%
}

.html-content[data-v-86daf7e4] {
    position: relative
}

.image-node[data-v-57413c93] {
    overflow: hidden
}

.image-container[data-v-57413c93],
.image-node[data-v-57413c93] {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    position: relative;
    width: 100%
}

.image-content[data-v-57413c93] {
    height: auto;
    -o-object-fit: contain;
    object-fit: contain;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 100%;
    -webkit-user-drag: none;
    user-drag: none
}

.upload-overlay[data-v-57413c93] {
    background: #0006;
    border-radius: 4px;
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0
}

.upload-overlay[data-v-57413c93],
.upload-spinner[data-v-57413c93] {
    align-items: center;
    display: flex;
    justify-content: center
}

.upload-spinner-icon[data-v-57413c93] {
    animation: spin-57413c93 1s linear infinite;
    color: #fff;
    height: 24px;
    width: 24px
}

@keyframes spin-57413c93 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.loading-container[data-v-57413c93] {
    align-items: center;
    color: #666;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
    justify-content: center;
    position: relative;
    width: 100%
}

.loading-animation[data-v-57413c93] {
    border: 8px solid #c0ddff;
    overflow: hidden
}

.gradient-layer[data-v-57413c93],
.loading-animation[data-v-57413c93] {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.gradient-layer[data-v-57413c93] {
    animation-duration: 6.4s;
    animation-iteration-count: infinite
}

.gradient-layer-1[data-v-57413c93] {
    animation-name: fadeLayer1-57413c93;
    background: linear-gradient(153.34deg, #f3ccd4 -7.44%, #f8d09c 107.1%);
    opacity: 1
}

.gradient-layer-2[data-v-57413c93] {
    animation-name: fadeLayer2-57413c93;
    background: radial-gradient(104.23% 104.23% at 30.31% -4.23%, #ffd0c9 0, #bad4ff), radial-gradient(104.23% 104.23% at 30.31% -4.23%, #ffd0c9 0, #bad4ff);
    opacity: 0
}

.gradient-layer-3[data-v-57413c93] {
    animation-name: fadeLayer3-57413c93;
    background: radial-gradient(104.23% 104.23% at 30.31% -4.23%, #c3e6ea 0, #f6cad2);
    opacity: 0
}

.gradient-layer-4[data-v-57413c93] {
    animation-name: fadeLayer4-57413c93;
    background: radial-gradient(104.23% 104.23% at 30.31% -4.23%, #a3f9f9 0, #dbfdff);
    opacity: 0
}

@keyframes fadeLayer1-57413c93 {
    0% {
        animation-timing-function: ease-out;
        opacity: 1
    }
    25% {
        animation-timing-function: ease-out;
        opacity: 1
    }
    50% {
        animation-timing-function: ease-out;
        opacity: 0
    }
    75% {
        animation-timing-function: ease-out;
        opacity: 0
    }
    to {
        animation-timing-function: ease-out;
        opacity: 1
    }
}

@keyframes fadeLayer2-57413c93 {
    0% {
        animation-timing-function: ease-out;
        opacity: 0
    }
    25% {
        animation-timing-function: ease-out;
        opacity: 1
    }
    50% {
        animation-timing-function: ease-out;
        opacity: 1
    }
    75% {
        animation-timing-function: ease-out;
        opacity: 0
    }
    to {
        animation-timing-function: ease-out;
        opacity: 0
    }
}

@keyframes fadeLayer3-57413c93 {
    0% {
        animation-timing-function: ease-out;
        opacity: 0
    }
    25% {
        animation-timing-function: ease-out;
        opacity: 0
    }
    50% {
        animation-timing-function: ease-out;
        opacity: 1
    }
    75% {
        animation-timing-function: ease-out;
        opacity: 1
    }
    to {
        animation-timing-function: ease-out;
        opacity: 0
    }
}

@keyframes fadeLayer4-57413c93 {
    0% {
        animation-timing-function: ease-out;
        opacity: 0
    }
    25% {
        animation-timing-function: ease-out;
        opacity: 0
    }
    50% {
        animation-timing-function: ease-out;
        opacity: 0
    }
    75% {
        animation-timing-function: ease-out;
        opacity: 1
    }
    to {
        animation-timing-function: ease-out;
        opacity: 0
    }
}

.loading-text[data-v-57413c93] {
    align-items: center;
    backdrop-filter: blur(28.9px);
    -webkit-backdrop-filter: blur(28.9px);
    background: #0000004d;
    border-radius: 58px;
    color: #fff;
    display: flex;
    flex-direction: column;
    font-size: 43px;
    font-weight: 500;
    gap: 10px;
    justify-content: center;
    min-height: 126px;
    min-width: 449px;
    padding: 10px 30px;
    position: relative;
    text-align: center;
    z-index: 1
}

.progress[data-v-57413c93] {
    color: #fffc;
    font-size: 14px;
    font-weight: 600;
    margin-top: 2px
}

.status[data-v-57413c93] {
    color: #999;
    font-size: 40px;
    margin-top: 6px;
    text-align: center
}

.placeholder[data-v-57413c93] {
    color: silver;
    text-align: center
}

.text-node[data-v-f0693b44] {
    display: inline-block;
    transition: all .15s ease;
    word-wrap: break-word
}

.text-node.empty .text-display[data-v-f0693b44] {
    opacity: .6
}

.text-display[data-v-f0693b44] {
    cursor: pointer;
    transition: color .15s ease
}

.text-editor[data-v-f0693b44] {
    min-width: 20px;
    transition: all .15s ease
}

.text-editor[data-v-f0693b44]:focus {
    outline: none
}

.text-editor[contenteditable=true][data-v-f0693b44] {
    cursor: text
}

.text-display[data-v-f0693b44] {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.text-editor[data-v-f0693b44] {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text
}

@media (max-width:768px) {
    .text-node[data-v-f0693b44] {
        max-width: 90vw
    }
}

.svg-node-container[data-v-335f0658] {
    cursor: crosshair;
    pointer-events: none;
    position: absolute;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.svg-canvas[data-v-335f0658] {
    display: block;
    height: 100%;
    width: 100%
}

.current-path[data-v-335f0658] {
    opacity: 1;
    stroke-dasharray: 2 2;
    animation: dash-335f0658 .5s linear infinite
}

@keyframes dash-335f0658 {
    to {
        stroke-dashoffset: 4
    }
}

.svg-node-container[data-v-335f0658]:not(.doodle-mode) {
    cursor: default
}

.error-node[data-v-f5da5fb0] {
    background-color: red;
    border-radius: 4px;
    color: #fff;
    padding: 8px
}

.selection-overlay[data-v-253f2ca7] {
    height: 0;
    left: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    width: 0
}

.resize-handle[data-v-253f2ca7] {
    align-items: center;
    display: flex;
    justify-content: center
}

.cut-fragment[data-v-78f43cde] {
    pointer-events: auto;
    position: absolute;
    transition: opacity .2s
}

.fragment-image[data-v-78f43cde] {
    display: block;
    height: 100%;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 100%;
    -webkit-user-drag: none
}

.resize-handle[data-v-78f43cde] {
    align-items: center;
    display: flex;
    justify-content: center
}

.cutting-controls[data-v-037416b8] {
    pointer-events: none;
    transition: all .2s ease
}

.controls-container[data-v-037416b8] {
    background-color: #ffffffe6;
    border-radius: 4px;
    box-shadow: 0 2px 4px #0000001a;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    position: relative
}

.pointer-indicator[data-v-037416b8] {
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid hsla(0, 0%, 100%, .9);
    bottom: -8px;
    height: 0;
    position: absolute;
    right: 15px;
    width: 0
}

.fragments-info[data-v-037416b8] {
    color: #666;
    font-size: 12px;
    text-align: center
}

.buttons-container[data-v-037416b8] {
    display: flex;
    gap: 8px
}

.control-button[data-v-037416b8] {
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    padding: 6px 12px;
    pointer-events: auto;
    transition: background-color .2s
}

.apply-button[data-v-037416b8] {
    background-color: #0f7fff;
    color: #fff
}

.apply-button[data-v-037416b8]:hover {
    background-color: #06c
}

.apply-button[data-v-037416b8]:active {
    background-color: #0052a3
}

.cancel-button[data-v-037416b8] {
    background-color: #f5f5f5;
    color: #333
}

.cancel-button[data-v-037416b8]:hover {
    background-color: #e0e0e0
}

.cancel-button[data-v-037416b8]:active {
    background-color: #d0d0d0
}

.image-meta-editor[data-v-74f3dcb3] {
    pointer-events: auto;
    z-index: 101
}

.image-meta-editor-container[data-v-74f3dcb3] {
    display: flex;
    flex-direction: column;
    height: 100%;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity, 1));
    --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, .25);
    --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
    -webkit-backdrop-filter: blur(34.900001525878906px);
    backdrop-filter: blur(34.900001525878906px);
    background: #ffffffe5;
    border: 1px solid #e0e0e0;
    border-radius: 16px;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    box-shadow: 0 3px 30px #00000014;
    box-sizing: border-box;
    overflow-y: hidden;
    transition: width .3s ease;
    width: 400px
}

.image-meta-editor-container.wider-panel[data-v-74f3dcb3] {
    border-radius: 12px;
    width: 100%
}

.image-meta-editor-container.wider-panel .close-btn[data-v-74f3dcb3] {
    cursor: pointer;
    height: 24px;
    position: absolute;
    right: 16px;
    top: 20px;
    width: 24px;
    z-index: 10
}

.image-meta-editor-container.meta-form-panel[data-v-74f3dcb3] {
    max-height: 83vh;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.image-meta-editor-container.meta-form-panel[data-v-74f3dcb3]::-webkit-scrollbar {
    display: none
}

.header[data-v-74f3dcb3] {
    align-items: center;
    border-bottom-width: 1px;
    display: flex;
    justify-content: space-between;
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-border-opacity, 1));
    padding: 24px 16px
}

.title[data-v-74f3dcb3] {
    font-family: Arial;
    font-size: 18px;
    font-style: Bold;
    font-weight: 700;
    leading-trim: NONE;
    color: #232425;
    letter-spacing: 0;
    line-height: 150%
}

.close-btn[data-v-74f3dcb3] {
    border-radius: .5rem;
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.close-btn[data-v-74f3dcb3]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity, 1))
}

.close-btn[data-v-74f3dcb3] {
    cursor: pointer;
    height: 24px;
    transform: translateY(-8px);
    width: 24px
}

.close-btn svg[data-v-74f3dcb3] {
    vertical-align: middle
}

.slide-right-enter-active[data-v-74f3dcb3],
.slide-right-leave-active[data-v-74f3dcb3] {
    transition: all .3s cubic-bezier(.4, 0, .2, 1)
}

.slide-right-enter-from .image-meta-editor-container[data-v-74f3dcb3],
.slide-right-leave-to .image-meta-editor-container[data-v-74f3dcb3] {
    transform: translate(100%)
}

.slide-right-enter-from[data-v-74f3dcb3],
.slide-right-leave-to[data-v-74f3dcb3] {
    opacity: 0
}

@media (prefers-color-scheme:dark) {
    .image-meta-editor-container[data-v-74f3dcb3] {
        --tw-bg-opacity: 1;
        background-color: rgb(17 24 39/var(--tw-bg-opacity, 1));
        --tw-text-opacity: 1;
        color: rgb(255 255 255/var(--tw-text-opacity, 1))
    }
    .image-meta-editor-container.meta-form-panel[data-v-74f3dcb3] {
        border-color: #393a3b;
        box-shadow: 0 4px 12px #0006
    }
    .header[data-v-74f3dcb3] {
        --tw-border-opacity: 1;
        border-color: rgb(55 65 81/var(--tw-border-opacity, 1))
    }
    .title[data-v-74f3dcb3] {
        --tw-text-opacity: 1;
        color: rgb(255 255 255/var(--tw-text-opacity, 1))
    }
    .close-btn[data-v-74f3dcb3]:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(31 41 55/var(--tw-bg-opacity, 1))
    }
}

@media (max-width:640px) {
    .image-meta-editor-container[data-v-74f3dcb3] {
        max-width: 100%;
        width: 100%
    }
}

.content[data-v-7cb6fae0] {
    padding: 24px 16px
}

.section[data-v-7cb6fae0] {
    margin-bottom: 1.5rem
}

.section[data-v-7cb6fae0]:last-child {
    margin-bottom: 0
}

.label[data-v-7cb6fae0] {
    display: block;
    font-size: .875rem;
    line-height: 1.25rem;
    margin-bottom: .75rem;
    --tw-text-opacity: 1;
    color: rgb(55 65 81/var(--tw-text-opacity, 1));
    font-family: Arial;
    font-size: 14px;
    font-style: Bold;
    font-weight: 700;
    leading-trim: NONE;
    letter-spacing: 0;
    line-height: 150%
}

.font-size-control[data-v-7cb6fae0] {
    align-items: center;
    display: flex;
    gap: 8px
}

.font-size-input[data-v-7cb6fae0] {
    background: #fff;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    font-family: Arial;
    font-size: 14px;
    padding: 8px 12px;
    text-align: center;
    width: 80px
}

.font-size-input[data-v-7cb6fae0]:focus {
    border-color: #232425;
    box-shadow: 0 0 0 2px #2324251a;
    outline: none
}

.font-size-unit[data-v-7cb6fae0] {
    color: #606366;
    font-size: 14px
}

.color-control[data-v-7cb6fae0] {
    align-items: center;
    display: flex;
    gap: 8px
}

.color-picker[data-v-7cb6fae0] {
    background: none;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    cursor: pointer;
    height: 40px;
    padding: 0;
    width: 40px
}

.color-picker[data-v-7cb6fae0]::-webkit-color-swatch-wrapper {
    padding: 0
}

.color-picker[data-v-7cb6fae0]::-webkit-color-swatch {
    border: none;
    border-radius: 6px
}

.color-input[data-v-7cb6fae0] {
    background: #fff;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    flex: 1;
    font-family: Arial;
    font-size: 14px;
    padding: 8px 12px
}

.color-input[data-v-7cb6fae0]:focus {
    border-color: #232425;
    box-shadow: 0 0 0 2px #2324251a;
    outline: none
}

.style-controls[data-v-7cb6fae0] {
    display: flex;
    gap: 8px
}

.style-group[data-v-7cb6fae0] {
    display: flex;
    gap: 4px
}

.style-btn[data-v-7cb6fae0] {
    align-items: center;
    background: #fff;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    height: 40px;
    justify-content: center;
    transition: all .2s ease;
    width: 40px
}

.style-btn[data-v-7cb6fae0]:hover {
    background: #f5f5f5;
    border-color: #d0d0d0
}

.style-btn.active[data-v-7cb6fae0] {
    background: #232425;
    border-color: #232425;
    color: #fff
}

.style-btn svg[data-v-7cb6fae0] {
    height: 16px;
    width: 16px
}

.alignment-controls[data-v-7cb6fae0] {
    display: flex;
    gap: 4px
}

.alignment-btn[data-v-7cb6fae0] {
    align-items: center;
    background: #fff;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    height: 40px;
    justify-content: center;
    transition: all .2s ease;
    width: 40px
}

.alignment-btn[data-v-7cb6fae0]:hover {
    background: #f5f5f5;
    border-color: #d0d0d0
}

.alignment-btn.active[data-v-7cb6fae0] {
    background: #232425;
    border-color: #232425;
    color: #fff
}

.alignment-btn svg[data-v-7cb6fae0] {
    height: 16px;
    width: 16px
}

@media (prefers-color-scheme:dark) {
    .btn-secondary[data-v-7cb6fae0],
    .footer[data-v-7cb6fae0] {
        --tw-border-opacity: 1;
        border-color: rgb(55 65 81/var(--tw-border-opacity, 1))
    }
    .btn-secondary[data-v-7cb6fae0] {
        --tw-bg-opacity: 1;
        background-color: rgb(31 41 55/var(--tw-bg-opacity, 1));
        --tw-text-opacity: 1;
        color: rgb(209 213 219/var(--tw-text-opacity, 1))
    }
    .btn-secondary[data-v-7cb6fae0]:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(55 65 81/var(--tw-bg-opacity, 1))
    }
    .label[data-v-7cb6fae0] {
        --tw-text-opacity: 1;
        color: rgb(209 213 219/var(--tw-text-opacity, 1))
    }
    .color-input[data-v-7cb6fae0],
    .font-size-input[data-v-7cb6fae0] {
        --tw-border-opacity: 1;
        border-color: rgb(55 65 81/var(--tw-border-opacity, 1));
        --tw-bg-opacity: 1;
        background-color: rgb(31 41 55/var(--tw-bg-opacity, 1));
        --tw-text-opacity: 1;
        color: rgb(255 255 255/var(--tw-text-opacity, 1))
    }
    .color-input[data-v-7cb6fae0]:focus,
    .font-size-input[data-v-7cb6fae0]:focus {
        border-color: #e8e9ea;
        box-shadow: 0 0 0 2px #e7e8e91a
    }
    .alignment-btn[data-v-7cb6fae0],
    .style-btn[data-v-7cb6fae0] {
        --tw-border-opacity: 1;
        border-color: rgb(55 65 81/var(--tw-border-opacity, 1));
        --tw-bg-opacity: 1;
        background-color: rgb(31 41 55/var(--tw-bg-opacity, 1))
    }
    .alignment-btn[data-v-7cb6fae0]:hover,
    .style-btn[data-v-7cb6fae0]:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(55 65 81/var(--tw-bg-opacity, 1))
    }
    .alignment-btn.active[data-v-7cb6fae0],
    .style-btn.active[data-v-7cb6fae0] {
        --tw-bg-opacity: 1;
        background-color: rgb(229 231 235/var(--tw-bg-opacity, 1));
        --tw-text-opacity: 1;
        color: rgb(17 24 39/var(--tw-text-opacity, 1))
    }
    .font-size-unit[data-v-7cb6fae0] {
        --tw-text-opacity: 1;
        color: rgb(156 163 175/var(--tw-text-opacity, 1))
    }
}

.text-editor-panel[data-v-013d87fe] {
    pointer-events: auto
}

.text-editor-container[data-v-013d87fe] {
    display: flex;
    flex-direction: column;
    height: 100%;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity, 1));
    --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, .25);
    --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
    -webkit-backdrop-filter: blur(34.900001525878906px);
    backdrop-filter: blur(34.900001525878906px);
    background: #ffffffe5;
    border: 1px solid #e0e0e0;
    border-radius: 16px;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    box-shadow: 0 3px 30px #00000014;
    max-height: 80vh;
    overflow-y: auto;
    scrollbar-width: none;
    width: 368px;
    -ms-overflow-style: none
}

.text-editor-container[data-v-013d87fe]::-webkit-scrollbar {
    display: none
}

.header[data-v-013d87fe] {
    align-items: center;
    border-bottom-width: 1px;
    display: flex;
    justify-content: space-between;
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-border-opacity, 1));
    padding: 24px 16px
}

.title[data-v-013d87fe] {
    font-family: Arial;
    font-size: 18px;
    font-style: Bold;
    font-weight: 700;
    leading-trim: NONE;
    color: #232425;
    letter-spacing: 0;
    line-height: 150%
}

.close-btn[data-v-013d87fe] {
    border-radius: .5rem;
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.close-btn[data-v-013d87fe]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity, 1))
}

.close-btn[data-v-013d87fe] {
    cursor: pointer;
    height: 24px;
    transform: translateY(-8px);
    width: 24px
}

.close-btn svg[data-v-013d87fe] {
    vertical-align: middle
}

.slide-right-enter-active[data-v-013d87fe],
.slide-right-leave-active[data-v-013d87fe] {
    transition: all .3s cubic-bezier(.4, 0, .2, 1)
}

.slide-right-enter-from .text-editor-container[data-v-013d87fe],
.slide-right-leave-to .text-editor-container[data-v-013d87fe] {
    transform: translate(100%)
}

.slide-right-enter-from[data-v-013d87fe],
.slide-right-leave-to[data-v-013d87fe] {
    opacity: 0
}

@media (prefers-color-scheme:dark) {
    .text-editor-container[data-v-013d87fe] {
        --tw-bg-opacity: 1;
        background-color: rgb(17 24 39/var(--tw-bg-opacity, 1));
        --tw-text-opacity: 1;
        border-color: #393a3b;
        box-shadow: 0 4px 12px #0006;
        color: rgb(255 255 255/var(--tw-text-opacity, 1))
    }
    .header[data-v-013d87fe] {
        --tw-border-opacity: 1;
        border-color: rgb(55 65 81/var(--tw-border-opacity, 1))
    }
    .title[data-v-013d87fe] {
        --tw-text-opacity: 1;
        color: rgb(255 255 255/var(--tw-text-opacity, 1))
    }
    .close-btn[data-v-013d87fe]:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(31 41 55/var(--tw-bg-opacity, 1))
    }
}

@media (max-width:640px) {
    .text-editor-container[data-v-013d87fe] {
        max-width: 100%;
        width: 100%
    }
}

.edit-input-group[data-v-bcdbc05b] {
    display: flex;
    min-width: 316px;
    width: 100%
}

.edit-input-wrapper[data-v-bcdbc05b] {
    background: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    padding: 12px;
    position: relative;
    transition: border-color .15s ease, box-shadow .15s ease;
    width: 100%
}

.edit-input-wrapper.is-disabled[data-v-bcdbc05b] {
    background: #f5f5f5
}

.edit-input-top-row[data-v-bcdbc05b] {
    display: flex;
    gap: 8px;
    margin-bottom: 8px
}

.edit-input-bottom-row[data-v-bcdbc05b] {
    align-items: center;
    display: flex;
    gap: 8px
}

.mask-preview-container[data-v-bcdbc05b] {
    flex-shrink: 0
}

.mask-preview-box[data-v-bcdbc05b] {
    align-items: center;
    background: #eaeaea;
    border-radius: 12px;
    display: flex;
    height: 48px;
    justify-content: center;
    overflow: hidden;
    position: relative;
    width: 48px
}

.mask-preview-image[data-v-bcdbc05b] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.mask-loading[data-v-bcdbc05b] {
    color: #999;
    font-size: 12px
}

.mask-upload-overlay[data-v-bcdbc05b] {
    background: #0006;
    border-radius: 4px;
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0
}

.mask-upload-overlay[data-v-bcdbc05b],
.upload-spinner[data-v-bcdbc05b] {
    align-items: center;
    display: flex;
    justify-content: center
}

.upload-spinner-icon[data-v-bcdbc05b] {
    animation: spin-bcdbc05b 1s linear infinite;
    color: #fff;
    height: 20px;
    width: 20px
}

@keyframes spin-bcdbc05b {
    0% {
        transform: rotate(0);
        stroke-dashoffset: 60
    }
    50% {
        stroke-dashoffset: 0
    }
    to {
        transform: rotate(1turn);
        stroke-dashoffset: -60
    }
}

.edit-text-area[data-v-bcdbc05b] {
    flex: 1
}

.edit-textarea[data-v-bcdbc05b] {
    background: transparent;
    border: none;
    flex: 1;
    font-family: inherit;
    font-size: 14px;
    line-height: 1;
    max-height: 120px;
    outline: none;
    padding: 0;
    resize: none
}

.edit-textarea[data-v-bcdbc05b]::-moz-placeholder {
    color: #ccc;
    opacity: 1
}

.edit-textarea[data-v-bcdbc05b]::placeholder {
    color: #ccc;
    opacity: 1
}

@media (prefers-color-scheme:dark) {
    .edit-textarea[data-v-bcdbc05b]::-moz-placeholder {
        color: #666
    }
    .edit-textarea[data-v-bcdbc05b]::placeholder {
        color: #666
    }
}

.edit-input-top-row .edit-textarea[data-v-bcdbc05b] {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin-bottom: 0;
    max-height: 80px;
    padding: 8px;
    width: 100%
}

.edit-textarea[data-v-bcdbc05b]:disabled {
    color: #999;
    cursor: not-allowed
}

.edit-input-top-row .edit-textarea[data-v-bcdbc05b]:disabled {
    background: #f5f5f5
}

.menu-btn[data-v-bcdbc05b] {
    align-items: center;
    border: 1px solid transparent;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    font-weight: 600;
    gap: 8px;
    height: 32px;
    padding: 0 12px;
    position: relative;
    transition: all .15s cubic-bezier(.4, 0, .2, 1)
}

.menu-btn[data-v-bcdbc05b]:disabled {
    cursor: not-allowed
}

.edit-apply-btn[data-v-bcdbc05b] {
    border-radius: 35%;
    --tw-bg-opacity: 1;
    background-color: rgb(38 38 38/var(--tw-bg-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity, 1))
}

.edit-apply-btn[data-v-bcdbc05b]:hover {
    opacity: .8
}

@media (prefers-color-scheme:dark) {
    .edit-apply-btn[data-v-bcdbc05b] {
        --tw-bg-opacity: 1;
        background-color: rgb(238 238 238/var(--tw-bg-opacity, 1));
        --tw-text-opacity: 1;
        color: rgb(35 36 37/var(--tw-text-opacity, 1))
    }
}

.edit-apply-btn[data-v-bcdbc05b] {
    align-items: center;
    border: none;
    display: flex;
    height: 28px;
    justify-content: center;
    margin-left: auto;
    padding: 0;
    width: 28px
}

.edit-apply-btn[data-v-bcdbc05b]:disabled {
    --tw-bg-opacity: 1;
    background-color: rgb(244 244 244/var(--tw-bg-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(144 148 153/var(--tw-text-opacity, 1))
}

@media (prefers-color-scheme:dark) {
    .edit-apply-btn[data-v-bcdbc05b]:disabled {
        background-color: #eeeeee30
    }
}

.edit-apply-btn[data-v-bcdbc05b]:disabled {
    cursor: not-allowed
}

@media (prefers-color-scheme:dark) {
    .edit-input-wrapper[data-v-bcdbc05b] {
        background: #232425;
        border-color: #393a3b;
        box-shadow: 0 6px 16px #0009, 0 0 0 1px #ffffff1a
    }
    .edit-input-wrapper.is-disabled[data-v-bcdbc05b],
    .mask-preview-box[data-v-bcdbc05b] {
        background: #1a1b1c
    }
    .mask-preview-box[data-v-bcdbc05b] {
        border-color: #393a3b
    }
    .edit-input-top-row .edit-textarea[data-v-bcdbc05b] {
        background: #2a2b2c;
        border-color: #393a3b;
        color: #e8e9ea
    }
    .edit-input-top-row .edit-textarea[data-v-bcdbc05b]:disabled {
        background: #1a1b1c;
        color: #666
    }
    .edit-textarea[data-v-bcdbc05b] {
        background: transparent;
        color: #e8e9ea
    }
    .edit-textarea[data-v-bcdbc05b]:disabled {
        color: #666
    }
    .edit-apply-btn[data-v-bcdbc05b]:disabled {
        background: #444
    }
    .mask-upload-overlay[data-v-bcdbc05b] {
        background: #0009
    }
}

.add-options-dropdown[data-v-0fff0c82] {
    animation: dropdownFadeIn-0fff0c82 .15s cubic-bezier(.4, 0, .2, 1);
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 16px #0000001a;
    left: 0;
    min-width: 180px;
    padding: 8px;
    position: absolute;
    top: calc(100% + 8px);
    z-index: 200
}

@keyframes dropdownFadeIn-0fff0c82 {
    0% {
        opacity: 0;
        transform: translateY(-4px)
    }
    to {
        opacity: 1;
        transform: translateY(0)
    }
}

.dropdown-content[data-v-0fff0c82] {
    display: flex;
    flex-direction: column;
    gap: 4px
}

.option-btn[data-v-0fff0c82] {
    align-items: center;
    background: transparent;
    border: none;
    border-radius: 6px;
    color: #232425;
    cursor: pointer;
    display: flex;
    font-family: inherit;
    font-size: 14px;
    gap: 12px;
    padding: 8px 12px;
    text-align: left;
    transition: background-color .15s ease;
    width: 100%
}

.option-btn[data-v-0fff0c82]:hover:not(:disabled) {
    background: #f5f5f5
}

.option-btn[data-v-0fff0c82]:active:not(:disabled) {
    background: #e8e9ea;
    transform: translateY(1px)
}

.option-btn[data-v-0fff0c82]:disabled {
    cursor: not-allowed;
    opacity: .5
}

.option-icon[data-v-0fff0c82] {
    align-items: center;
    color: #606366;
    display: flex;
    height: 20px;
    justify-content: center;
    width: 20px
}

.option-text[data-v-0fff0c82] {
    flex: 1;
    font-weight: 400
}

.option-shortcut[data-v-0fff0c82] {
    background: #f0f0f0;
    border-radius: 3px;
    color: #999;
    font-family: monospace;
    font-size: 12px;
    min-width: 20px;
    padding: 2px 6px;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .add-options-dropdown[data-v-0fff0c82] {
        background: #232425;
        border-color: #393a3b;
        box-shadow: 0 4px 16px #0000004d
    }
    .option-btn[data-v-0fff0c82] {
        color: #e8e9ea
    }
    .option-btn[data-v-0fff0c82]:hover:not(:disabled) {
        background: #2a2b2c
    }
    .option-btn[data-v-0fff0c82]:active:not(:disabled) {
        background: #393a3b
    }
    .option-icon[data-v-0fff0c82] {
        color: #b8bbbf
    }
    .option-shortcut[data-v-0fff0c82] {
        background: #393a3b;
        color: #b8bbbf
    }
}

.size-button-wrapper[data-v-013f5c9a] {
    display: inline-block;
    position: relative
}

.menu-btn[data-v-013f5c9a] {
    align-items: center;
    background: transparent;
    border: none;
    border-radius: 8px;
    color: inherit;
    cursor: pointer;
    display: flex;
    font-family: Arial, sans-serif;
    font-size: 14px;
    gap: 6px;
    height: 100%;
    padding: 4px 8px;
    transition: background-color .15s ease;
    white-space: nowrap
}

.menu-btn[data-v-013f5c9a]:hover:not(:disabled):not(.loading) {
    background: #f5f5f5
}

.menu-btn[data-v-013f5c9a]:active:not(:disabled):not(.loading) {
    background: #e8e9ea;
    transform: translateY(1px)
}

.menu-btn.active[data-v-013f5c9a] {
    background: #0f7fff;
    color: #fff
}

.menu-btn.active[data-v-013f5c9a]:hover:not(:disabled):not(.loading) {
    background: #0e72e5
}

.menu-btn.active[data-v-013f5c9a]:active:not(:disabled):not(.loading) {
    background: #0d65d1;
    transform: translateY(1px)
}

.size-btn[data-v-013f5c9a] {
    gap: 8px
}

.size-btn.active .dropdown-arrow[data-v-013f5c9a] {
    transform: rotate(180deg)
}

.dropdown-arrow[data-v-013f5c9a] {
    color: currentColor;
    height: 12px;
    transition: transform .15s ease;
    width: 12px
}

.size-dropdown[data-v-013f5c9a] {
    background: #fff;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    box-shadow: 0 4px 12px #00000026;
    left: 0;
    min-width: 140px;
    overflow: hidden;
    position: absolute;
    top: calc(100% + 8px);
    z-index: 1000
}

.size-option[data-v-013f5c9a] {
    align-items: center;
    background: #fff;
    border: none;
    border-bottom: 1px solid #f0f0f0;
    color: #232425;
    cursor: pointer;
    display: flex;
    font-family: Arial;
    font-size: 14px;
    gap: 12px;
    padding: 12px 16px;
    text-align: left;
    transition: background-color .15s ease;
    width: 100%
}

.size-option[data-v-013f5c9a]:last-child {
    border-bottom: none
}

.size-option[data-v-013f5c9a]:hover {
    background: #f8f9fa
}

.size-option .size-icon[data-v-013f5c9a] {
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.size-label[data-v-013f5c9a] {
    flex: 1
}

.btn-text[data-v-013f5c9a] {
    position: relative;
    z-index: 1
}

@media (prefers-color-scheme:dark) {
    .menu-btn[data-v-013f5c9a]:hover:not(:disabled):not(.loading) {
        background: #2a2b2c
    }
    .menu-btn[data-v-013f5c9a]:active:not(:disabled):not(.loading) {
        background: #393a3b
    }
    .menu-btn.active[data-v-013f5c9a] {
        background: #3f99ff;
        color: #fff
    }
    .menu-btn.active[data-v-013f5c9a]:hover:not(:disabled):not(.loading) {
        background: #5ba5ff
    }
    .menu-btn.active[data-v-013f5c9a]:active:not(:disabled):not(.loading) {
        background: #2b8dff
    }
    .size-dropdown[data-v-013f5c9a] {
        background: #232425;
        border-color: #393a3b;
        box-shadow: 0 4px 12px #0009
    }
    .size-option[data-v-013f5c9a] {
        background: #232425;
        border-bottom-color: #393a3b;
        color: #e8e9ea
    }
    .size-option[data-v-013f5c9a]:hover {
        background: #2a2b2c
    }
}

.floating-menu[data-v-a75ae037] {
    align-items: center;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    box-shadow: 0 2px 8px #0000001a;
    display: flex;
    gap: 16px;
    position: absolute;
    transform: translate(-50%);
    z-index: 100
}

.floating-menu.only-button-group[data-v-a75ae037] {
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0
}

.menu-container[data-v-a75ae037] {
    align-items: center;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 10px #00000026;
    color: #232425;
    display: flex;
    gap: 6px;
    height: 40px;
    padding: 6px
}

.separator[data-v-a75ae037] {
    background: #eaeaea;
    height: 16px;
    margin: 0 6px;
    width: 1px
}

.menu-btn[data-v-a75ae037] {
    align-items: center;
    background: transparent;
    border: none;
    border-radius: 8px;
    color: inherit;
    cursor: pointer;
    display: flex;
    font-family: Arial, sans-serif;
    font-size: 14px;
    gap: 6px;
    height: 100%;
    padding: 4px 8px;
    white-space: nowrap
}

.menu-btn[data-v-a75ae037]:hover:not(:disabled):not(.loading) {
    background: #f5f5f5
}

.menu-btn[data-v-a75ae037]:active:not(:disabled):not(.loading) {
    background: #e8e9ea;
    transform: translateY(1px)
}

.menu-btn[data-v-a75ae037]:focus:not(:disabled):not(.loading) {
    outline: none
}

.menu-btn.active[data-v-a75ae037] {
    background: #0f7fff;
    color: #fff
}

.menu-btn.active[data-v-a75ae037]:hover:not(:disabled):not(.loading) {
    background: #0e72e5
}

.menu-btn.active[data-v-a75ae037]:active:not(:disabled):not(.loading) {
    background: #0d65d1;
    transform: translateY(1px)
}

.menu-btn.active[data-v-a75ae037]:focus:not(:disabled):not(.loading) {
    outline: none
}

.menu-btn[data-v-a75ae037]:disabled {
    cursor: not-allowed;
    opacity: .6
}

.menu-btn.loading[data-v-a75ae037] {
    pointer-events: none
}

.loading-spinner[data-v-a75ae037] {
    animation: spin-a75ae037 1s linear infinite;
    border: 2px solid #e0e0e0;
    border-radius: 50%;
    border-top-color: #232425;
    height: 16px;
    margin-right: 8px;
    width: 16px
}

@keyframes spin-a75ae037 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.btn-text[data-v-a75ae037] {
    position: relative;
    z-index: 1
}

.menu-icon[data-v-a75ae037] {
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.add-button-wrapper[data-v-a75ae037] {
    display: inline-block;
    position: relative
}

.add-btn[data-v-a75ae037] {
    gap: 8px
}

.dropdown-arrow[data-v-a75ae037] {
    color: currentColor;
    height: 12px;
    transition: transform .15s ease;
    width: 12px
}

.add-btn.active .dropdown-arrow[data-v-a75ae037] {
    transform: rotate(180deg)
}

@media (prefers-color-scheme:dark) {
    .menu-container[data-v-a75ae037] {
        background: #232425;
        box-shadow: 0 4px 16px #0000004d;
        color: #e8e9ea
    }
    .separator[data-v-a75ae037] {
        background: #393a3b
    }
    .menu-btn[data-v-a75ae037]:hover:not(:disabled):not(.loading) {
        background: #2a2b2c
    }
    .menu-btn[data-v-a75ae037]:active:not(:disabled):not(.loading) {
        background: #393a3b
    }
    .menu-btn.active[data-v-a75ae037] {
        background: #3f99ff;
        color: #fff
    }
    .menu-btn.active[data-v-a75ae037]:hover:not(:disabled):not(.loading) {
        background: #5ba5ff
    }
    .menu-btn.active[data-v-a75ae037]:active:not(:disabled):not(.loading) {
        background: #2b8dff
    }
    .loading-spinner[data-v-a75ae037] {
        border-color: #e8e9ea #393a3b #393a3b
    }
}

.image-toolbar[data-v-f96b886d] {
    align-items: flex-start;
    background: #fff;
    border: 1px solid #eaeaea;
    border-radius: 12px;
    box-shadow: 0 3px 30px #00000014;
    box-sizing: border-box;
    display: flex;
    overflow: visible;
    position: absolute;
    transform: translateY(-53%);
    z-index: 101
}

.dark .image-toolbar[data-v-f96b886d] {
    background-color: #232425;
    border-color: #393a3b;
    box-shadow: 0 4px 6px #00000080
}

.toolbar-container[data-v-f96b886d] {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: visible;
    padding: 24px 12px;
    width: 100%
}

.toolbar-content[data-v-f96b886d] {
    display: grid;
    gap: 4px;
    grid-template-columns: repeat(2, 1fr);
    width: 100%
}

.tooltip-container[data-v-f96b886d] {
    position: relative;
    width: 100%
}

.tool-button[data-v-f96b886d],
.tooltip-container[data-v-f96b886d] {
    align-items: center;
    display: flex;
    flex-direction: column
}

.tool-button[data-v-f96b886d] {
    background-color: transparent;
    border: none;
    border-radius: 6px;
    box-sizing: border-box;
    color: #232425;
    cursor: pointer;
    height: 60px;
    justify-content: center;
    padding: 6px;
    transition: all .15s ease;
    white-space: nowrap;
    width: 86px
}

.dark .tool-button[data-v-f96b886d] {
    color: var(--color-gray-400)
}

.tool-button[data-v-f96b886d]:hover {
    box-shadow: 0 2px 4px #0000000d;
    transform: translateY(-1px)
}

.dark .tool-button[data-v-f96b886d]:hover {
    background-color: var(--color-gray-800);
    box-shadow: 0 2px 4px #0003;
    color: var(--color-gray-100)
}

.tool-button[data-v-f96b886d]:active {
    color: var(--color-gray-900);
    transform: translateY(0)
}

.dark .tool-button[data-v-f96b886d]:active {
    box-shadow: inset 0 1px 2px #0000004d;
    color: var(--color-gray-100)
}

.tool-button.active[data-v-f96b886d] {
    background: #f5f5f5;
    position: relative
}

.dark .tool-button.active[data-v-f96b886d] {
    background-color: #1a365d;
    border-left: 3px solid var(--color-blue-500);
    color: var(--color-blue-300)
}

.tool-icon[data-v-f96b886d] {
    flex-shrink: 0;
    height: 20px;
    margin-bottom: 4px;
    width: 20px
}

.tool-label[data-v-f96b886d] {
    font-family: Arial;
    font-size: 10px;
    font-style: Regular;
    font-weight: 400;
    letter-spacing: 0;
    line-height: 15px;
    text-align: center;
    vertical-align: middle
}

.toolbar-separator.horizontal[data-v-f96b886d],
.toolbar-separator.vertical[data-v-f96b886d] {
    background-color: var(--color-gray-200);
    height: 1px;
    margin: 4px 0;
    width: 100%
}

.dark .toolbar-separator[data-v-f96b886d] {
    background-color: var(--color-gray-700)
}

.toolbar-container[data-v-f96b886d]::-webkit-scrollbar {
    width: 0
}

.toolbar-container[data-v-f96b886d] {
    scrollbar-width: none
}

.toolbar-slide-enter-active[data-v-f96b886d] {
    transition: all .3s cubic-bezier(.4, 0, .2, 1)
}

.toolbar-slide-enter-from[data-v-f96b886d],
.toolbar-slide-leave-to[data-v-f96b886d] {
    opacity: 0
}

@media (max-width:768px) {
    .tool-button[data-v-f96b886d] {
        height: 50px;
        padding: 4px
    }
    .tool-icon[data-v-f96b886d] {
        height: 16px;
        width: 16px
    }
    .tool-label[data-v-f96b886d] {
        font-size: 9px
    }
}

.canvas-top-toolbar[data-v-c8829781] {
    align-content: flex-start;
    align-items: flex-start;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    background: #ffffff73;
    border: 1px solid hsla(0, 0%, 100%, .2);
    border-radius: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    height: 56px;
    left: 50%;
    padding: 8px 12px;
    position: absolute;
    top: 19px;
    transform: translate(-50%);
    z-index: 100
}

.toolbar-button[data-v-c8829781] {
    align-items: center;
    background: #fcfcfc;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    color: #232425;
    cursor: pointer;
    display: flex;
    height: 40px;
    justify-content: center;
    transition: all .15s cubic-bezier(.4, 0, .2, 1);
    width: 40px
}

.toolbar-button[data-v-c8829781]:hover {
    background: #f5f5f5;
    transform: translateY(-1px)
}

.toolbar-button[data-v-c8829781]:active {
    box-shadow: 0 1px 4px #0000000d;
    transform: translateY(0)
}

.toolbar-button.active[data-v-c8829781] {
    background: #232425;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    color: #fff
}

.toolbar-button.active[data-v-c8829781]:hover {
    background: #1a1b1c
}

.toolbar-button.active[data-v-c8829781]:active {
    background: #0f1011
}

.toolbar-button svg[data-v-c8829781] {
    height: 20px;
    width: 20px
}

body:has(.main-inner .global-canvas .infinite-canvas-root-container) #genspark-float-bar {
    display: none
}

.canvas-container[data-v-3c0ddd40] {
    background: #f1f1f1;
    border: 1px solid #eaeaea;
    border-radius: 12px;
    box-shadow: 0 4px 15px #00000014
}

.canvas-container[data-v-3c0ddd40],
.viewport[data-v-3c0ddd40] {
    height: 100%;
    overflow: hidden;
    position: relative;
    width: 100%
}

.viewport[data-v-3c0ddd40] {
    background-image: radial-gradient(circle, #e0e0e0 1px, transparent 0);
    touch-action: none
}

.locating-container[data-v-3c0ddd40] {
    height: 0;
    overflow: visible;
    position: relative;
    transform-origin: 0 0;
    width: 0
}

.infinite-canvas-root-container[data-v-3c0ddd40] * {
    box-sizing: border-box
}

.logo-icon[data-v-3c0ddd40] {
    left: 19%;
    position: absolute;
    right: 19%;
    top: 50%;
    transform: translateY(-50%);
    width: 62%
}