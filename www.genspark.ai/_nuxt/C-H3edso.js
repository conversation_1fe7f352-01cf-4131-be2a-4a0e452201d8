import {
    dc as r,
    az as e,
    dd as t,
    de as a,
    df as n,
    dg as s,
    dh as c,
    di as o,
    dj as u,
    dk as i
} from "./Cf0SOiw0.js";
var f = r(e, "WeakMap"),
    b = t(Object.keys, Object),
    j = Object.prototype.hasOwnProperty;

function l(r) {
    if (!a(r)) return b(r);
    var e = [];
    for (var t in Object(r)) j.call(r, t) && "constructor" != t && e.push(t);
    return e
}

function p(r) {
    return s(r) ? n(r) : l(r)
}

function d(r, e) {
    for (var t = -1, a = e.length, n = r.length; ++t < a;) r[n + t] = e[t];
    return r
}

function v(r, e) {
    for (var t = -1, a = null == r ? 0 : r.length, n = 0, s = []; ++t < a;) {
        var c = r[t];
        e(c, t, r) && (s[n++] = c)
    }
    return s
}

function w() {
    return []
}
var O = Object.prototype.propertyIsEnumerable,
    y = Object.getOwnPropertySymbols,
    h = y ? function(r) {
        return null == r ? [] : (r = Object(r), v(y(r), (function(e) {
            return O.call(r, e)
        })))
    } : w;

function g(r, e, t) {
    var a = e(r);
    return c(r) ? a : d(a, t(r))
}

function m(r) {
    return g(r, p, h)
}
var k = r(e, "DataView"),
    P = r(e, "Promise"),
    S = r(e, "Set"),
    M = "[object Map]",
    W = "[object Promise]",
    D = "[object Set]",
    V = "[object WeakMap]",
    x = "[object DataView]",
    z = u(k),
    A = u(i),
    B = u(P),
    E = u(S),
    I = u(f),
    q = o;
(k && q(new k(new ArrayBuffer(1))) != x || i && q(new i) != M || P && q(P.resolve()) != W || S && q(new S) != D || f && q(new f) != V) && (q = function(r) {
    var e = o(r),
        t = "[object Object]" == e ? r.constructor : void 0,
        a = t ? u(t) : "";
    if (a) switch (a) {
        case z:
            return x;
        case A:
            return M;
        case B:
            return W;
        case E:
            return D;
        case I:
            return V
    }
    return e
});
export {
    S,
    f as W,
    d as a,
    g as b,
    q as c,
    m as d,
    l as e,
    v as f,
    h as g,
    p as k,
    w as s
};