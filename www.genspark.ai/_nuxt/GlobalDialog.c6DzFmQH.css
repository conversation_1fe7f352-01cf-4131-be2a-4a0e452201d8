.confirmation-dialog[data-v-ee822bf8] {
    align-items: center;
    background-color: #00000080;
    bottom: 0;
    display: flex;
    justify-content: center;
    left: 0;
    outline: none;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1000
}

.confirmation-content[data-v-ee822bf8] {
    animation: zoomIn-ee822bf8 .15s ease-out forwards;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 25px #00000026;
    color: #333;
    max-width: 400px;
    padding: 20px;
    width: 90%
}

@keyframes zoomIn-ee822bf8 {
    0% {
        transform: scale(.95)
    }
    to {
        transform: scale(1)
    }
}

.confirmation-header[data-v-ee822bf8] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px
}

.confirmation-header h3[data-v-ee822bf8] {
    margin: 0
}

.close-btn[data-v-ee822bf8] {
    align-items: center;
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    display: flex;
    font-size: 24px;
    height: 24px;
    justify-content: center;
    line-height: 24px;
    padding: 0;
    transition: color .2s ease;
    width: 24px
}

.close-btn[data-v-ee822bf8]:hover {
    color: #333
}

.close-btn[data-v-ee822bf8]:active {
    transform: translateY(1px)
}

.input-container[data-v-ee822bf8] {
    margin: 15px 0;
    width: 100%
}

.dialog-input[data-v-ee822bf8] {
    border: 1px solid #eaeaea;
    border-radius: 8px;
    box-sizing: border-box;
    font-size: 14px;
    padding: 8px 12px;
    transition: border-color .2s ease;
    width: 100%
}

.dialog-input[data-v-ee822bf8]:focus {
    outline: none
}

.confirmation-actions[data-v-ee822bf8] {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    width: 100%
}

.cancel-btn[data-v-ee822bf8],
.confirm-btn[data-v-ee822bf8] {
    align-items: center;
    border-radius: 8px;
    border-style: solid;
    border-width: 1px;
    cursor: pointer;
    display: inline-flex;
    flex: 1;
    font-size: 16px;
    font-weight: 500;
    height: 40px;
    justify-content: center;
    padding: 10px 16px;
    transition: all .2s ease
}

.cancel-btn[data-v-ee822bf8] {
    background-color: #f5f5f5;
    border-color: #f5f5f5;
    color: #333;
    margin-right: 8px
}

.cancel-btn[data-v-ee822bf8]:hover {
    background: #eaeaea;
    border-color: #eaeaea
}

.cancel-btn[data-v-ee822bf8]:active {
    background: #e0e0e0;
    transform: translateY(1px)
}

.confirm-btn[data-v-ee822bf8] {
    background-color: #1a1a1a;
    border-color: #1a1a1a;
    color: #fff;
    margin-left: 8px
}

.confirm-btn[data-v-ee822bf8]:hover {
    background-color: #333;
    border-color: #333
}

.confirm-btn[data-v-ee822bf8]:active {
    background-color: #000;
    transform: translateY(1px)
}

.cancel-btn[data-v-ee822bf8]:disabled,
.close-btn[data-v-ee822bf8]:disabled,
.confirm-btn[data-v-ee822bf8]:disabled {
    cursor: not-allowed;
    opacity: .7
}

.delete-btn[data-v-ee822bf8] {
    background-color: #ff3d3d;
    border-color: #ff3d3d
}

.delete-btn[data-v-ee822bf8]:hover {
    background-color: #e53935;
    border-color: #e53935
}

.delete-btn[data-v-ee822bf8]:active {
    background-color: #d32f2f
}

.delete-warning[data-v-ee822bf8] {
    background-color: #fffbee;
    border-radius: 8px;
    color: #333;
    font-size: 13px;
    margin: 10px 0;
    padding: 10px
}

@media (prefers-color-scheme:dark) {
    .confirmation-content[data-v-ee822bf8] {
        background-color: #282828;
        box-shadow: 0 4px 20px #0006;
        color: #f0f0f0
    }
    .confirmation-header h3[data-v-ee822bf8] {
        color: #f0f0f0
    }
    .close-btn[data-v-ee822bf8] {
        color: #b4b4b4
    }
    .close-btn[data-v-ee822bf8]:hover {
        color: #f0f0f0
    }
    .dialog-input[data-v-ee822bf8] {
        background-color: #3c3c3c;
        border-color: #505050;
        color: #f0f0f0
    }
    .dialog-input[data-v-ee822bf8]:focus {
        border-color: #4891d9;
        outline: none
    }
    .dialog-input[data-v-ee822bf8]::-moz-placeholder {
        color: #969696
    }
    .dialog-input[data-v-ee822bf8]::placeholder {
        color: #969696
    }
    .cancel-btn[data-v-ee822bf8] {
        background-color: #3c3c3c;
        border-color: #3c3c3c;
        color: #f0f0f0
    }
    .cancel-btn[data-v-ee822bf8]:hover {
        background: #464646
    }
    .confirm-btn[data-v-ee822bf8] {
        background-color: #1a1a1a;
        border-color: #1a1a1a
    }
    .confirm-btn[data-v-ee822bf8]:hover {
        background-color: #333
    }
    .delete-warning[data-v-ee822bf8] {
        color: #f44336
    }
    .delete-btn[data-v-ee822bf8] {
        background-color: #d32f2f;
        border-color: #d32f2f
    }
    .delete-btn[data-v-ee822bf8]:hover {
        background-color: #c62828;
        border-color: #e53935
    }
}