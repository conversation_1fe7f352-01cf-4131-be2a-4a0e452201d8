var e = Object.defineProperty,
    t = (t, n, s) => ((t, n, s) => n in t ? e(t, n, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: s
    }) : t[n] = s)(t, "symbol" != typeof n ? n + "" : n, s);
import {
    I as n
} from "./Dflnlfvw.js";
import {
    d as s,
    b as i,
    o,
    J as a,
    s as l,
    r,
    c as u,
    v as c,
    F as d,
    f as p,
    e as h,
    a9 as m,
    t as v,
    n as f,
    a as g,
    y as _,
    w,
    g as y,
    i as b,
    _ as x,
    q as k,
    l as C,
    W as I,
    C as R,
    ag as S,
    k as E,
    T as L,
    p as j,
    A as M,
    D,
    a2 as O,
    U as T,
    ay as A,
    ai as B,
    aK as N,
    H as P,
    R as F,
    I as q,
    V as z,
    aR as U
} from "./Cf0SOiw0.js";
import {
    N as $
} from "./CW991W2w.js";
import {
    W as H
} from "./BUCk-Nnr.js";
import {
    u as V
} from "./B6noBY_5.js";
import {
    D as W
} from "./WhweajiO.js";
import {
    B as G
} from "./CAfqOhBF.js";
import {
    O as K
} from "./DOWa3jpG.js";
import {
    b as J,
    A as Q
} from "./4s4Iy95q.js";
import {
    _ as Z
} from "./DKpDUEYb.js";
import {
    F as X
} from "./LGmiBiLz.js";
import {
    b as Y,
    o as ee
} from "./zH1ZpJ79.js";
import {
    N as te
} from "./nuQnue4a.js";
import {
    p as ne
} from "./NG4ombpb.js";
import {
    C as se
} from "./CRmNre8Y.js";
import {
    b as ie
} from "./DT-NG54s.js";
import {
    u as oe
} from "./gsZIXP6B.js";
import {
    S as ae
} from "./DxgY8w7w.js";
import {
    C as le,
    D as re
} from "./BwHsXuPr.js";
import {
    u as ue
} from "./B0db5Fvl.js";
import {
    G as ce
} from "./AGJ7ifDi.js";
const de = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 24 24"
};
const pe = {
        render: function(e, t) {
            return o(), s("svg", de, t[0] || (t[0] = [i("path", {
                d: "M9.29 15.88L13.17 12L9.29 8.12a.996.996 0 1 1 1.41-1.41l4.59 4.59c.39.39.39 1.02 0 1.41L10.7 17.3a.996.996 0 0 1-1.41 0c-.38-.39-.39-1.03 0-1.42z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    he = {
        width: "24",
        height: "25",
        viewBox: "0 0 24 25",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const me = {
        render: function(e, t) {
            return o(), s("svg", he, t[0] || (t[0] = [i("g", {
                id: "Frame"
            }, [i("path", {
                id: "Vector",
                d: "M8.49789 4.34277C8.86119 4.34277 9.19589 4.53978 9.37224 4.8574L10.5954 7.06081C10.7556 7.34931 10.7631 7.69826 10.6155 7.99341L9.43714 10.3503C9.43714 10.3503 9.77864 12.106 11.2078 13.5352C12.637 14.9644 14.3869 15.3 14.3869 15.3L16.7433 14.1218C17.0387 13.9741 17.3879 13.9817 17.6765 14.1422L19.8861 15.3707C20.2034 15.5471 20.4002 15.8816 20.4002 16.2447V18.7814C20.4002 20.0733 19.2003 21.0063 17.9762 20.5933C15.4623 19.745 11.5599 18.1299 9.08654 15.6565C6.61309 13.1831 4.998 9.28071 4.14973 6.76676C3.73672 5.54271 4.66976 4.34277 5.96159 4.34277H8.49789Z",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linejoin": "round"
            })], -1)]))
        }
    },
    ve = {
        width: "10",
        height: "11",
        viewBox: "0 0 10 11",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const fe = {
        render: function(e, t) {
            return o(), s("svg", ve, t[0] || (t[0] = [i("path", {
                d: "M1.25 4.03516L4.78553 7.56882L8.32107 4.03516",
                stroke: "#666666",
                "stroke-width": "0.833023",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    ge = {
        class: "dialog-content"
    },
    _e = {
        class: "dialog-header"
    },
    we = {
        class: "dialog-body"
    },
    ye = {
        class: "file-details"
    },
    be = {
        class: "file-name"
    },
    xe = {
        class: "file-size"
    },
    ke = {
        class: "file-details"
    },
    Ce = {
        class: "file-name"
    },
    Ie = {
        class: "file-size"
    },
    Re = {
        key: 1,
        class: "action-buttons"
    },
    Se = ["disabled"],
    Ee = {
        key: 2,
        class: "error-action-buttons"
    },
    Le = x(a({
        __name: "SaveResultDialog",
        props: {
            modelValue: {
                type: Boolean
            },
            uploadResult: {
                default: void 0
            },
            title: {
                default: ""
            },
            description: {
                default: ""
            }
        },
        emits: ["update:modelValue", "close"],
        setup(e, {
            emit: t
        }) {
            const {
                t: n
            } = l(), a = V();
            y();
            const x = b("jsBridge"),
                k = e,
                C = t,
                I = r(null),
                R = r(!1),
                S = u((() => {
                    var e;
                    if (!k.uploadResult) return !1;
                    if (!k.uploadResult.success) {
                        if (k.uploadResult.exception) {
                            const t = k.uploadResult.exception.name || k.uploadResult.exception.type || (null == (e = k.uploadResult.exception.constructor) ? void 0 : e.name) || "";
                            if (t.includes("StorageQuotaExceededException") || t.includes("QuotaExceeded") || k.uploadResult.error_message && k.uploadResult.error_message.includes("storage limit")) return !1
                        }
                        return !0
                    }
                    return !1
                })),
                E = u((() => S.value && k.uploadResult ? k.uploadResult.error_message ? k.uploadResult.error_message : k.uploadResult.exception ? k.uploadResult.exception.message || k.uploadResult.exception.toString() || n("components.aidrive.save_succeed.unknown_error") : n("components.aidrive.save_succeed.upload_failed_unknown") : "")),
                L = u((() => S.value ? n("components.aidrive.save_succeed.export_failed") : k.title || n("components.aidrive.save_succeed.title"))),
                j = u((() => S.value ? E.value : k.description || n("components.aidrive.save_succeed.description")));
            u((() => {
                var e;
                return (null == (e = k.uploadResult) ? void 0 : e.filename) || ""
            })), u((() => !1));
            const M = u((() => {
                    var e, t, n, s, i, o, a;
                    let l = null == (e = k.uploadResult) ? void 0 : e.path;
                    if (!l) {
                        const e = (null == (t = k.uploadResult) ? void 0 : t.directory) || "",
                            s = encodeURIComponent((null == (n = k.uploadResult) ? void 0 : n.filename) || "");
                        l = `${e.startsWith("/")?e:`/${e}`}/${s}`.replace(/\/+/g, "/")
                    }
                    return {
                        id: "temp-file-id",
                        name: (null == (s = k.uploadResult) ? void 0 : s.filename) || "",
                        path: l,
                        mime_type: (null == (i = k.uploadResult) ? void 0 : i.mime_type) || Y((null == (o = k.uploadResult) ? void 0 : o.filename) || ""),
                        isDir: !1,
                        size: (null == (a = k.uploadResult) ? void 0 : a.file_size) || 0,
                        type: "",
                        modified_time: 0,
                        parent_id: null,
                        index: 0
                    }
                })),
                D = u((() => ee(M.value))),
                O = e => {
                    if (0 === e) return "0 Bytes";
                    const t = Math.floor(Math.log(e) / Math.log(1024));
                    return parseFloat((e / Math.pow(1024, t)).toFixed(2)) + " " + ["Bytes", "KB", "MB", "GB", "TB"][t]
                },
                T = () => {
                    if (!D.value) return;
                    const e = M.value.path;
                    if (D.value) {
                        const t = btoa(encodeURIComponent(e)),
                            n = btoa(encodeURIComponent(JSON.stringify(M.value)));
                        localStorage.setItem(t, n);
                        const s = `/${J}${e}`;
                        window.open(s, "_blank")
                    }
                },
                A = () => {
                    R.value = !0, a.warning(n("components.aidrive.status_quota_exceeded")), I.value && I.value.trigger && I.value.trigger(), C("update:modelValue", !1), C("close")
                },
                B = () => {
                    C("update:modelValue", !1), C("close")
                },
                N = () => {
                    var e;
                    if (null == (e = k.uploadResult) ? void 0 : e.directory) {
                        const e = `/${Q}${k.uploadResult.directory}`;
                        window.open(e, "_blank")
                    }
                },
                P = r(!1);
            return c((() => x.value), (e => {
                e && x.value.callHandler("support", {
                    api: "download"
                }, (e => {
                    P.value = e
                }))
            }), {
                immediate: !0
            }), c([() => k.uploadResult, () => k.modelValue], (([e, t]) => {
                t ? e && t && !R.value && (R.value = (() => {
                    var e, t;
                    if (null == (e = k.uploadResult) ? void 0 : e.exception) {
                        const e = k.uploadResult.exception.name || k.uploadResult.exception.type || (null == (t = k.uploadResult.exception.constructor) ? void 0 : t.name) || "";
                        if (e.includes("StorageQuotaExceededException") || e.includes("QuotaExceeded") || k.uploadResult.error_message && k.uploadResult.error_message.includes("storage limit")) return A(), !0
                    }
                    return !1
                })()) : R.value = !1
            }), {
                immediate: !0
            }), (e, t) => {
                var a, l, r;
                return o(), s(d, null, [e.modelValue && !R.value ? (o(), s("div", {
                    key: 0,
                    class: "save-succeed-dialog",
                    onKeydown: m(B, ["esc"]),
                    tabindex: "0"
                }, [i("div", ge, [i("div", _e, [i("h3", null, v(L.value), 1), i("button", {
                    class: "close-btn",
                    onClick: B
                }, "×")]), i("div", we, [i("p", {
                    class: f(["description", {
                        error: S.value
                    }])
                }, v(j.value), 3), S.value ? p("", !0) : (o(), s(d, {
                    key: 0
                }, [D.value ? (o(), s("div", {
                    key: 1,
                    class: "file-info hoverable",
                    onClick: T
                }, [h(X, {
                    file: M.value
                }, null, 8, ["file"]), i("div", ke, [i("div", Ce, v((null == (a = e.uploadResult) ? void 0 : a.filename) || ""), 1), i("div", Ie, v(O((null == (l = e.uploadResult) ? void 0 : l.file_size) || 0)), 1)])])) : (o(), g(_(te), {
                    key: 0,
                    disabled: D.value,
                    placement: "top",
                    trigger: "hover"
                }, {
                    trigger: w((() => {
                        var t, n;
                        return [i("div", {
                            class: f(["file-info", {
                                hoverable: D.value,
                                "not-previewable": !D.value
                            }])
                        }, [h(X, {
                            file: M.value
                        }, null, 8, ["file"]), i("div", ye, [i("div", be, v((null == (t = e.uploadResult) ? void 0 : t.filename) || ""), 1), i("div", xe, v(O((null == (n = e.uploadResult) ? void 0 : n.file_size) || 0)), 1)])], 2)]
                    })),
                    default: w((() => [i("span", null, v(_(n)("components.aidrive.save_succeed.preview_not_supported")), 1)])),
                    _: 1
                }, 8, ["disabled"]))], 64)), S.value ? (o(), s("div", Ee, [i("button", {
                    class: "action-btn cancel-btn",
                    onClick: B
                }, v(_(n)("components.aidrive.save_succeed.close")), 1)])) : (o(), s("div", Re, [i("button", {
                    class: "action-btn confirm-btn",
                    onClick: N,
                    disabled: !(null == (r = e.uploadResult) ? void 0 : r.directory)
                }, v(_(n)("components.aidrive.save_succeed.view_in_aidrive")), 9, Se)]))])])], 32)) : p("", !0), h(Z, {
                    ref_key: "quotaExceedRef",
                    ref: I
                }, null, 512)], 64)
            }
        }
    }), [
        ["__scopeId", "data-v-bc738207"]
    ]),
    je = {
        class: "edit-in-slide-agent-text"
    },
    Me = x({
        __name: "EditInSheetsAgent",
        props: {
            message: {
                type: Object,
                default: null
            },
            agent_project_id: {
                type: String,
                default: null
            },
            taskUrl: {
                type: String,
                default: ""
            }
        },
        emits: ["update:agent_project_id"],
        setup(e, {
            emit: t
        }) {
            const {
                t: n
            } = l(), a = V(), u = e, c = t, d = r(!1), p = async () => {
                var e;
                if (!d.value) {
                    d.value = !0;
                    try {
                        if (u.taskUrl) return void window.open(u.taskUrl, "_blank");
                        if (u.agent_project_id) return void window.open(`/agents?id=${u.agent_project_id}`);
                        const t = null == (e = u.message) ? void 0 : e.session_state;
                        if (!t) throw new Error("session_state is required");
                        const s = t.csv_url,
                            i = t.csv_name || "Untitled",
                            o = t.csv_desc || i,
                            l = i,
                            r = t.column_description;
                        if (!s) throw new Error("csv_url is required");
                        if (!r || !Array.isArray(r) || 0 === r.length) throw new Error("column_description is required");
                        const d = await fetch("/api/project/create", {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json"
                                },
                                body: JSON.stringify({
                                    type: "sheets_agent",
                                    name: i,
                                    session_state: {
                                        messages: [{
                                            role: "user",
                                            content: n("components.sheets_agent.new_session_importing", {
                                                csv_name: i
                                            })
                                        }, {
                                            role: "assistant",
                                            content: n("components.sheets_agent.new_session_intruduction")
                                        }],
                                        dataset_canvas: {
                                            csv_info_map: {
                                                [l]: {
                                                    csv_url: s,
                                                    csv_name: i,
                                                    csv_desc: o,
                                                    key: l,
                                                    column_description: r
                                                }
                                            },
                                            sheets: [l]
                                        }
                                    },
                                    is_private: !0,
                                    run_in_background: !1
                                })
                            }),
                            p = await d.json();
                        if (0 === p.status && p.data && p.data.id) {
                            const e = p.data.id;
                            c("update:agent_project_id", e), window.open(`/agents?id=${e}`)
                        } else a.error(n("components.sheets_agent.create_failed", {
                            error: p.message || "Unknown error"
                        }))
                    } catch (t) {
                        a.error(n("components.sheets_agent.create_failed", {
                            error: t.message || "Unknown error"
                        }))
                    } finally {
                        d.value = !1
                    }
                }
            };
            return (e, t) => (o(), s("div", {
                class: "edit-in-slide-agent",
                onClick: C(p, ["stop"]),
                style: k({
                    cursor: d.value ? "not-allowed" : "pointer"
                })
            }, [i("div", je, v(_(n)("components.sheets_agent.continue_in_ai_sheets")), 1), d.value ? (o(), g(_(H), {
                key: 0,
                class: "white-loading-animation"
            })) : (o(), g(_(pe), {
                key: 1,
                class: "arrow-right-icon"
            }))], 4))
        }
    }, [
        ["__scopeId", "data-v-0fec8466"]
    ]),
    De = I("global-canvas-visible", (() => {
        const e = r(!R.isMobile());
        return {
            visible: e,
            markInvisibleInMobile: () => {
                R.isMobile() && (e.value = !1)
            },
            markVisible: () => {
                e.value = !0
            }
        }
    })),
    Oe = a({
        __name: "CloseGlobalCanvas",
        setup(e) {
            const t = De();
            return (e, n) => (o(), s("div", {
                class: "flex items-center justify-center w-[24px] h-[24px] rounded-[2px] bg-[#F5F5F5] dark:bg-[#333] ml-[12px]",
                onClick: n[0] || (n[0] = (...e) => _(t).markInvisibleInMobile && _(t).markInvisibleInMobile(...e))
            }, [h(_(se), {
                class: "w-[20px] h-[20px]"
            })]))
        }
    }),
    Te = {
        key: 0,
        class: "cursor-pointer flex-shrink-0 flex items-center ml-[12px]"
    },
    Ae = {
        key: 0,
        class: "flex items-center gap-[10px] bg-[#F5F5F5] dark:bg-[#333] px-[8px] py-[4px] rounded-lg whitespace-nowrap"
    },
    Be = {
        class: "justify-start text-zinc-600 dark:text-zinc-300 text-sm leading-tight"
    },
    Ne = {
        class: "flex flex-col gap-[12px] max-h-[300px] overflow-y-auto"
    },
    Pe = ["onClick"],
    Fe = {
        class: "flex justify-between items-center"
    },
    qe = ["title"],
    ze = a({
        __name: "VersionSelector",
        props: {
            project: {}
        },
        emits: ["select-version"],
        setup(e, {
            emit: t
        }) {
            const {
                t: n
            } = l(), a = r(!1);

            function u() {
                a.value = !a.value
            }
            const c = e,
                m = r([]);
            S((() => {
                var e, t;
                (null == (t = null == (e = c.project) ? void 0 : e.session_state) ? void 0 : t.canvas_history) ? m.value = c.project.session_state.canvas_history: m.value = []
            }));
            const y = r(null);
            S((() => {
                var e, t;
                if (!m.value.length) return void(y.value = null);
                const n = null == (t = null == (e = c.project) ? void 0 : e.session_state) ? void 0 : t.canvas_history_id;
                y.value = n ? m.value.find((e => e.id === n)) ? ? null : m.value[0]
            }));
            const b = e => {
                    if (e || (e = y.value), e || (e = m.value[0]), !e) return 0;
                    const t = m.value.findIndex((t => t.id === e.id)) ? ? 0;
                    return (m.value.length ? ? 0) - t
                },
                x = t;
            return (e, t) => (o(), g(_($), {
                show: _(a),
                placement: "bottom-start",
                trigger: "hover",
                "show-arrow": !1,
                raw: "",
                "onUpdate:show": u,
                class: "p-[12px] rounded-[12px] bg-white dark:bg-[#222]"
            }, {
                trigger: w((() => {
                    var t;
                    return [_(R).isMobile() ? p("", !0) : (o(), s("div", Te, [(null == (t = _(m)) ? void 0 : t.length) > 1 ? (o(), s("div", Ae, [i("div", Be, v(e.$t("components.slides.save_point")) + "-" + v(b(_(y))), 1), h(_(fe), {
                        class: f([_(a) && "rotate-180"])
                    }, null, 8, ["class"])])) : p("", !0)]))]
                })),
                default: w((() => [i("div", Ne, [(o(!0), s(d, null, E(_(m), ((t, n) => {
                    var l, r, u;
                    return o(), s("div", {
                        key: n,
                        class: f(["w-[257px] px-3 py-2 rounded-xl flex flex-col gap-1 cursor-pointer", t.id === (null == (l = _(y)) ? void 0 : l.id) ? "bg-[#0F7FFF] text-white" : "bg-neutral-100 hover:bg-[#E9E9E9] dark:bg-[#2a2a2a] dark:hover:bg-[#333] dark:text-white"]),
                        onClick: e => function(e) {
                            a.value = !1, x("select-version", e)
                        }(t)
                    }, [i("div", Fe, [i("div", null, v(e.$t("components.slides.save_point")) + "-" + v(b(t)), 1), i("div", {
                        class: f([t.id === (null == (r = _(y)) ? void 0 : r.id) ? "text-white" : "text-neutral-400"])
                    }, v(new Date(t.ctime + "Z").toLocaleString()), 3)]), t.name ? (o(), s("div", {
                        key: 0,
                        class: f(["text-sm whitespace-nowrap overflow-hidden text-ellipsis", t.id === (null == (u = _(y)) ? void 0 : u.id) ? "text-white" : "text-neutral-400"]),
                        title: t.name
                    }, v(t.name), 11, qe)) : p("", !0)], 10, Pe)
                })), 128))])])),
                _: 1
            }, 8, ["show"]))
        }
    }),
    Ue = {
        class: "tabs-component"
    },
    $e = {
        class: "tab-header"
    },
    He = {
        key: 0,
        class: "tab-title"
    },
    Ve = {
        class: "icon-container"
    },
    We = {
        class: "sheets-bar"
    },
    Ge = ["onClick", "title"],
    Ke = {
        class: "sheets-bar-item-name"
    },
    Je = {
        key: 1,
        class: "button-group"
    },
    Qe = {
        class: "content-area"
    },
    Ze = {
        class: "tab-pane"
    },
    Xe = x(a({
        __name: "Tabs",
        props: {
            tabs: {},
            modelValue: {},
            inGlobalCanvas: {
                type: Boolean
            },
            inSubAgent: {
                type: Boolean
            },
            readOnly: {
                type: Boolean
            },
            title: {},
            csvInfoList: {},
            activeSheet: {},
            sheetsUrl: {},
            project: {},
            message: {},
            tool_call: {}
        },
        emits: ["update:modelValue", "sheet-change", "select-version"],
        setup(e, {
            emit: t
        }) {
            const {
                t: a
            } = l(), m = e, y = r(m.activeSheet);
            c((() => m.activeSheet), (() => {
                m.activeSheet && (y.value = m.activeSheet)
            }));
            const x = t,
                C = M();

            function I() {
                var e;
                let t = m.sheetsUrl;
                if (!t) {
                    let n = null == (e = m.project) ? void 0 : e.id;
                    n || (n = new URLSearchParams(window.location.search).get("id")), n || (n = C.query.id), t = `/sheets?project_id=${n}`
                }
                window.open(t, "_blank")
            }

            function D(e) {
                x("select-version", e)
            }
            b("cookieFreeHost");
            const O = r(!1);
            S((() => {
                var e;
                (null == (e = m.csvInfoList) ? void 0 : e.length) && (O.value = m.csvInfoList.every((e => !!e.csv_url)))
            }));
            const T = u((() => {
                    var e, t;
                    return m.inGlobalCanvas || "create_data_set_tool_simple" !== (null == (t = null == (e = m.tool_call) ? void 0 : e.function) ? void 0 : t.name)
                })),
                A = r(null);
            S((() => {
                var e, t;
                const n = null == (t = null == (e = m.message) ? void 0 : e.session_state) ? void 0 : t.agent_project_id;
                n && (A.value = n)
            }));
            const B = e => {
                    A.value = e
                },
                N = r(!1),
                P = r(!1),
                F = r(null),
                q = async () => {
                    var e, t;
                    if (null == (e = y.value) ? void 0 : e.key) {
                        N.value = !0;
                        try {
                            let e = null == (t = m.project) ? void 0 : t.id;
                            e || (e = new URLSearchParams(window.location.search).get("id")), e || (e = C.query.id), e || (e = C.query.project_id);
                            const n = await fetch(`/api/project/export_sheets_csv?project_id=${e}&sheet_id=${y.value.key}`),
                                s = await n.json();
                            0 === s.status && (F.value = s.data, P.value = !0)
                        } catch (n) {} finally {
                            N.value = !1
                        }
                    }
                },
                z = () => {
                    P.value = !1, F.value = null
                };
            return c(P, (e => {
                const t = document.querySelector(".j-input-wrapper-wrapper");
                t && (e ? (t.style.zIndex, t.style.zIndex = "0") : t.style.zIndex = "")
            })), (e, t) => {
                var a, l;
                return o(), s("div", Ue, [i("div", $e, [e.csvInfoList.length ? p("", !0) : (o(), s("div", He, [i("span", Ve, [h(_(n))]), i("span", null, v(e.title), 1)])), i("div", We, [(o(!0), s(d, null, E(e.csvInfoList, (e => (o(), s("div", {
                    key: e.csv_url,
                    class: f(["sheets-bar-item", {
                        active: y.value === e
                    }]),
                    onClick: t => {
                        return n = e, y.value = n, void x("sheet-change", n);
                        var n
                    },
                    title: e.csv_name || e.csv_desc
                }, [h(_(n)), i("span", Ke, v(e.csv_name || e.csv_desc), 1)], 10, Ge)))), 128)), !e.readOnly && e.project ? (o(), g(ze, {
                    key: 0,
                    project: e.project,
                    onSelectVersion: D
                }, null, 8, ["project"])) : p("", !0)]), p("", !0), i("div", {
                    class: f(["flex flex-row items-center ml-[16px] flex-shrink-0", [!e.inGlobalCanvas || e.inSubAgent ? "mr-[32px]" : ""]])
                }, [!T.value && O.value ? (o(), g(Me, {
                    key: 0,
                    message: e.message,
                    agent_project_id: A.value,
                    "onUpdate:agent_project_id": B
                }, null, 8, ["message", "agent_project_id"])) : p("", !0), e.inGlobalCanvas ? (o(), s("div", Je, [(null == (a = e.csvInfoList) ? void 0 : a.length) ? (o(), g(_(te), {
                    key: 0,
                    placement: "top"
                }, {
                    trigger: w((() => [i("div", {
                        class: f(["button secondary", {
                            loading: !1
                        }]),
                        onClick: I
                    }, [h(_(K))])])),
                    default: w((() => [j(" " + v(e.$t("components.webpagerender.open-in-new-window")), 1)])),
                    _: 1
                })) : p("", !0), (null == (l = e.csvInfoList) ? void 0 : l.length) ? (o(), g(_(te), {
                    key: 1,
                    placement: "top"
                }, {
                    trigger: w((() => [i("div", {
                        class: f(["button secondary", {
                            loading: N.value
                        }]),
                        onClick: q,
                        style: k({
                            cursor: N.value ? "not-allowed" : "pointer"
                        })
                    }, [N.value ? (o(), g(_(G), {
                        key: 0,
                        class: "black-loading-animation"
                    })) : (o(), g(_(W), {
                        key: 1
                    }))], 6)])),
                    default: w((() => [j(" " + v(N.value ? e.$t("components.webpagerender.exporting") : e.$t("components.webpagerender.export")), 1)])),
                    _: 1
                })) : p("", !0)])) : p("", !0), _(R).isMobile() && e.inGlobalCanvas && !e.inSubAgent ? (o(), g(Oe, {
                    key: 2
                })) : p("", !0)], 2)]), i("div", Qe, [i("div", Ze, [L(e.$slots, "default", {
                    activeTab: e.modelValue
                }, void 0, !0)])]), h(Le, {
                    modelValue: P.value,
                    "onUpdate:modelValue": t[0] || (t[0] = e => P.value = e),
                    "upload-result": F.value,
                    title: e.$t("components.aidrive.save_succeed.title"),
                    description: e.$t("components.aidrive.save_succeed.description"),
                    onClose: z
                }, null, 8, ["modelValue", "upload-result", "title", "description"])])
            }
        }
    }), [
        ["__scopeId", "data-v-eccf9d95"]
    ]);
var Ye = (e => (e.ASCEND = "ascend", e.DESCEND = "descend", e.NONE = "none", e))(Ye || {});
const et = ["", null, void 0, "N/A", "n/a"];

function tt(e) {
    if (et.includes(e)) return !0;
    const t = Number(e);
    return !isNaN(t)
}

function nt(e) {
    return ne.sanitize(e ? ? "", {
        ALLOWED_TAGS: []
    })
}

function st(e, t) {
    const n = e.index;
    return t.every((e => {
        return !!tt(t = nt(t = e[n])) || tt(t = (t = t.replaceAll(",", "")).replaceAll("$", ""));
        var t
    }))
}

function it(e) {
    return e = (e = (e = nt(e)).replaceAll(",", "")).replaceAll("$", ""), Number(e)
}

function ot(e, t, n) {
    const s = e.localeCompare(t);
    return "ascend" === n ? s : -s
}
const at = {
    Number: function(e, t, n) {
        const s = Number(e) - Number(t);
        return "ascend" === n ? s : -s
    },
    Date: function(e, t, n) {
        const s = new Date(e),
            i = new Date(t),
            o = s.getTime() - i.getTime();
        return "ascend" === n ? o : -o
    },
    Email: ot,
    ImageList: ot,
    Link: ot,
    Enum: ot,
    String: ot,
    EnumList: ot
};

function lt(e, t) {
    const n = r([]);
    return S((() => {
        n.value = e.value;
        const s = t.value.column;
        if (null === s) return;
        const i = t.value.order;
        if ("none" === i) return;
        const o = st(s, e.value);
        n.value = [...e.value].sort(((e, t) => {
            const n = s.index,
                a = String(e[n]),
                l = String(t[n]);
            if (a === l) return 0;
            if (o) return function(e, t, n) {
                if (e === t) return 0;
                if (et.includes(e)) return 1;
                if (et.includes(t)) return -1;
                const s = it(e),
                    i = it(t);
                return "ascend" === n ? s - i : i - s
            }(a, l, i);
            return (at[s.type || "String"] || at.String)(a, l, i)
        }))
    })), n
}
const rt = {
    width: "17",
    height: "17",
    viewBox: "0 0 17 17",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const ut = {
        render: function(e, t) {
            return o(), s("svg", rt, t[0] || (t[0] = [i("path", {
                d: "M11.94 4.56641H2.60669",
                stroke: "#9CA3AF",
                "stroke-width": "1.33333",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), i("path", {
                d: "M14.6067 8.56641H2.60669",
                stroke: "#9CA3AF",
                "stroke-width": "1.33333",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), i("path", {
                d: "M10.6734 12.5H2.60669",
                stroke: "#9CA3AF",
                "stroke-width": "1.33333",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    ct = {
        width: "17",
        height: "17",
        viewBox: "0 0 17 17",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const dt = {
    render: function(e, t) {
        return o(), s("svg", ct, t[0] || (t[0] = [D('<path d="M5.93994 4.5H14.6066" stroke="#9CA3AF" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"></path><path d="M5.93994 8.5H14.6066" stroke="#9CA3AF" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"></path><path d="M5.93994 12.5H14.6066" stroke="#9CA3AF" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.60669 4.5H2.61336" stroke="#9CA3AF" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.60669 8.5H2.61336" stroke="#9CA3AF" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.60669 12.5H2.61336" stroke="#9CA3AF" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"></path>', 6)]))
    }
};
var pt, ht = {
    exports: {}
};
const mt = O((pt || (pt = 1, ht.exports = function e() {
        var t, n = "undefined" != typeof self ? self : "undefined" != typeof window ? window : void 0 !== n ? n : {},
            s = !n.document && !!n.postMessage,
            i = n.IS_PAPA_WORKER || !1,
            o = {},
            a = 0,
            l = {};

        function r(e) {
            this._handle = null, this._finished = !1, this._completed = !1, this._halted = !1, this._input = null, this._baseIndex = 0, this._partialLine = "", this._rowCount = 0, this._start = 0, this._nextChunk = null, this.isFirstChunk = !0, this._completeResults = {
                    data: [],
                    errors: [],
                    meta: {}
                },
                function(e) {
                    var t = w(e);
                    t.chunkSize = parseInt(t.chunkSize), e.step || e.chunk || (t.chunkSize = null), this._handle = new h(t), (this._handle.streamer = this)._config = t
                }.call(this, e), this.parseChunk = function(e, t) {
                    var s = parseInt(this._config.skipFirstNLines) || 0;
                    if (this.isFirstChunk && 0 < s) {
                        let t = this._config.newline;
                        t || (o = this._config.quoteChar || '"', t = this._handle.guessLineEndings(e, o)), e = [...e.split(t).slice(s)].join(t)
                    }
                    this.isFirstChunk && b(this._config.beforeFirstChunk) && void 0 !== (o = this._config.beforeFirstChunk(e)) && (e = o), this.isFirstChunk = !1, this._halted = !1, s = this._partialLine + e;
                    var o = (this._partialLine = "", this._handle.parse(s, this._baseIndex, !this._finished));
                    if (!this._handle.paused() && !this._handle.aborted()) {
                        if (e = o.meta.cursor, this._finished || (this._partialLine = s.substring(e - this._baseIndex), this._baseIndex = e), o && o.data && (this._rowCount += o.data.length), s = this._finished || this._config.preview && this._rowCount >= this._config.preview, i) n.postMessage({
                            results: o,
                            workerId: l.WORKER_ID,
                            finished: s
                        });
                        else if (b(this._config.chunk) && !t) {
                            if (this._config.chunk(o, this._handle), this._handle.paused() || this._handle.aborted()) return void(this._halted = !0);
                            this._completeResults = o = void 0
                        }
                        return this._config.step || this._config.chunk || (this._completeResults.data = this._completeResults.data.concat(o.data), this._completeResults.errors = this._completeResults.errors.concat(o.errors), this._completeResults.meta = o.meta), this._completed || !s || !b(this._config.complete) || o && o.meta.aborted || (this._config.complete(this._completeResults, this._input), this._completed = !0), s || o && o.meta.paused || this._nextChunk(), o
                    }
                    this._halted = !0
                }, this._sendError = function(e) {
                    b(this._config.error) ? this._config.error(e) : i && this._config.error && n.postMessage({
                        workerId: l.WORKER_ID,
                        error: e,
                        finished: !1
                    })
                }
        }

        function u(e) {
            var t;
            (e = e || {}).chunkSize || (e.chunkSize = l.RemoteChunkSize), r.call(this, e), this._nextChunk = s ? function() {
                this._readChunk(), this._chunkLoaded()
            } : function() {
                this._readChunk()
            }, this.stream = function(e) {
                this._input = e, this._nextChunk()
            }, this._readChunk = function() {
                if (this._finished) this._chunkLoaded();
                else {
                    if (t = new XMLHttpRequest, this._config.withCredentials && (t.withCredentials = this._config.withCredentials), s || (t.onload = y(this._chunkLoaded, this), t.onerror = y(this._chunkError, this)), t.open(this._config.downloadRequestBody ? "POST" : "GET", this._input, !s), this._config.downloadRequestHeaders) {
                        var e, n = this._config.downloadRequestHeaders;
                        for (e in n) t.setRequestHeader(e, n[e])
                    }
                    var i;
                    this._config.chunkSize && (i = this._start + this._config.chunkSize - 1, t.setRequestHeader("Range", "bytes=" + this._start + "-" + i));
                    try {
                        t.send(this._config.downloadRequestBody)
                    } catch (o) {
                        this._chunkError(o.message)
                    }
                    s && 0 === t.status && this._chunkError()
                }
            }, this._chunkLoaded = function() {
                var e;
                4 === t.readyState && (t.status < 200 || 400 <= t.status ? this._chunkError() : (this._start += this._config.chunkSize || t.responseText.length, this._finished = !this._config.chunkSize || this._start >= (null !== (e = (e = t).getResponseHeader("Content-Range")) ? parseInt(e.substring(e.lastIndexOf("/") + 1)) : -1), this.parseChunk(t.responseText)))
            }, this._chunkError = function(e) {
                e = t.statusText || e, this._sendError(new Error(e))
            }
        }

        function c(e) {
            (e = e || {}).chunkSize || (e.chunkSize = l.LocalChunkSize), r.call(this, e);
            var t, n, s = "undefined" != typeof FileReader;
            this.stream = function(e) {
                this._input = e, n = e.slice || e.webkitSlice || e.mozSlice, s ? ((t = new FileReader).onload = y(this._chunkLoaded, this), t.onerror = y(this._chunkError, this)) : t = new FileReaderSync, this._nextChunk()
            }, this._nextChunk = function() {
                this._finished || this._config.preview && !(this._rowCount < this._config.preview) || this._readChunk()
            }, this._readChunk = function() {
                var e = this._input,
                    i = (this._config.chunkSize && (i = Math.min(this._start + this._config.chunkSize, this._input.size), e = n.call(e, this._start, i)), t.readAsText(e, this._config.encoding));
                s || this._chunkLoaded({
                    target: {
                        result: i
                    }
                })
            }, this._chunkLoaded = function(e) {
                this._start += this._config.chunkSize, this._finished = !this._config.chunkSize || this._start >= this._input.size, this.parseChunk(e.target.result)
            }, this._chunkError = function() {
                this._sendError(t.error)
            }
        }

        function d(e) {
            var t;
            r.call(this, e = e || {}), this.stream = function(e) {
                return t = e, this._nextChunk()
            }, this._nextChunk = function() {
                var e, n;
                if (!this._finished) return e = this._config.chunkSize, t = e ? (n = t.substring(0, e), t.substring(e)) : (n = t, ""), this._finished = !t, this.parseChunk(n)
            }
        }

        function p(e) {
            r.call(this, e = e || {});
            var t = [],
                n = !0,
                s = !1;
            this.pause = function() {
                r.prototype.pause.apply(this, arguments), this._input.pause()
            }, this.resume = function() {
                r.prototype.resume.apply(this, arguments), this._input.resume()
            }, this.stream = function(e) {
                this._input = e, this._input.on("data", this._streamData), this._input.on("end", this._streamEnd), this._input.on("error", this._streamError)
            }, this._checkIsFinished = function() {
                s && 1 === t.length && (this._finished = !0)
            }, this._nextChunk = function() {
                this._checkIsFinished(), t.length ? this.parseChunk(t.shift()) : n = !0
            }, this._streamData = y((function(e) {
                try {
                    t.push("string" == typeof e ? e : e.toString(this._config.encoding)), n && (n = !1, this._checkIsFinished(), this.parseChunk(t.shift()))
                } catch (s) {
                    this._streamError(s)
                }
            }), this), this._streamError = y((function(e) {
                this._streamCleanUp(), this._sendError(e)
            }), this), this._streamEnd = y((function() {
                this._streamCleanUp(), s = !0, this._streamData("")
            }), this), this._streamCleanUp = y((function() {
                this._input.removeListener("data", this._streamData), this._input.removeListener("end", this._streamEnd), this._input.removeListener("error", this._streamError)
            }), this)
        }

        function h(e) {
            var t, n, s, i, o = Math.pow(2, 53),
                a = -o,
                r = /^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/,
                u = /^((\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z)))$/,
                c = this,
                d = 0,
                p = 0,
                h = !1,
                f = !1,
                g = [],
                _ = {
                    data: [],
                    errors: [],
                    meta: {}
                };

            function y(t) {
                return "greedy" === e.skipEmptyLines ? "" === t.join("").trim() : 1 === t.length && 0 === t[0].length
            }

            function x() {
                if (_ && s && (C("Delimiter", "UndetectableDelimiter", "Unable to auto-detect delimiting character; defaulted to '" + l.DefaultDelimiter + "'"), s = !1), e.skipEmptyLines && (_.data = _.data.filter((function(e) {
                        return !y(e)
                    }))), k()) {
                    let n = function(t, n) {
                        b(e.transformHeader) && (t = e.transformHeader(t, n)), g.push(t)
                    };
                    if (_)
                        if (Array.isArray(_.data[0])) {
                            for (var t = 0; k() && t < _.data.length; t++) _.data[t].forEach(n);
                            _.data.splice(0, 1)
                        } else _.data.forEach(n)
                }

                function n(t, n) {
                    for (var s = e.header ? {} : [], i = 0; i < t.length; i++) {
                        var l = i,
                            c = t[i];
                        d = l = e.header ? i >= g.length ? "__parsed_extra" : g[i] : l, h = c = e.transform ? e.transform(c, l) : c, m = void 0, m = d, e.dynamicTypingFunction && void 0 === e.dynamicTyping[m] && (e.dynamicTyping[m] = e.dynamicTypingFunction(m)), c = !0 === (e.dynamicTyping[m] || e.dynamicTyping) ? "true" === h || "TRUE" === h || "false" !== h && "FALSE" !== h && ((e => {
                            if (r.test(e) && (e = parseFloat(e), a < e && e < o)) return 1
                        })(h) ? parseFloat(h) : u.test(h) ? new Date(h) : "" === h ? null : h) : h, "__parsed_extra" === l ? (s[l] = s[l] || [], s[l].push(c)) : s[l] = c
                    }
                    var d, h, m;
                    return e.header && (i > g.length ? C("FieldMismatch", "TooManyFields", "Too many fields: expected " + g.length + " fields but parsed " + i, p + n) : i < g.length && C("FieldMismatch", "TooFewFields", "Too few fields: expected " + g.length + " fields but parsed " + i, p + n)), s
                }
                var i;
                _ && (e.header || e.dynamicTyping || e.transform) && (i = 1, !_.data.length || Array.isArray(_.data[0]) ? (_.data = _.data.map(n), i = _.data.length) : _.data = n(_.data, 0), e.header && _.meta && (_.meta.fields = g), p += i)
            }

            function k() {
                return e.header && 0 === g.length
            }

            function C(e, t, n, s) {
                e = {
                    type: e,
                    code: t,
                    message: n
                }, void 0 !== s && (e.row = s), _.errors.push(e)
            }
            b(e.step) && (i = e.step, e.step = function(t) {
                _ = t, k() ? x() : (x(), 0 !== _.data.length && (d += t.data.length, e.preview && d > e.preview ? n.abort() : (_.data = _.data[0], i(_, c))))
            }), this.parse = function(i, o, a) {
                var r = e.quoteChar || '"';
                return e.newline || (e.newline = this.guessLineEndings(i, r)), s = !1, e.delimiter ? b(e.delimiter) && (e.delimiter = e.delimiter(i), _.meta.delimiter = e.delimiter) : ((r = ((t, n, s, i, o) => {
                    var a, r, u, c;
                    o = o || [",", "\t", "|", ";", l.RECORD_SEP, l.UNIT_SEP];
                    for (var d = 0; d < o.length; d++) {
                        for (var p, h = o[d], m = 0, f = 0, g = 0, _ = (u = void 0, new v({
                                comments: i,
                                delimiter: h,
                                newline: n,
                                preview: 10
                            }).parse(t)), w = 0; w < _.data.length; w++) s && y(_.data[w]) ? g++ : (f += p = _.data[w].length, void 0 === u ? u = p : 0 < p && (m += Math.abs(p - u), u = p));
                        0 < _.data.length && (f /= _.data.length - g), (void 0 === r || m <= r) && (void 0 === c || c < f) && 1.99 < f && (r = m, a = h, c = f)
                    }
                    return {
                        successful: !!(e.delimiter = a),
                        bestDelimiter: a
                    }
                })(i, e.newline, e.skipEmptyLines, e.comments, e.delimitersToGuess)).successful ? e.delimiter = r.bestDelimiter : (s = !0, e.delimiter = l.DefaultDelimiter), _.meta.delimiter = e.delimiter), r = w(e), e.preview && e.header && r.preview++, t = i, n = new v(r), _ = n.parse(t, o, a), x(), h ? {
                    meta: {
                        paused: !0
                    }
                } : _ || {
                    meta: {
                        paused: !1
                    }
                }
            }, this.paused = function() {
                return h
            }, this.pause = function() {
                h = !0, n.abort(), t = b(e.chunk) ? "" : t.substring(n.getCharIndex())
            }, this.resume = function() {
                c.streamer._halted ? (h = !1, c.streamer.parseChunk(t, !0)) : setTimeout(c.resume, 3)
            }, this.aborted = function() {
                return f
            }, this.abort = function() {
                f = !0, n.abort(), _.meta.aborted = !0, b(e.complete) && e.complete(_), t = ""
            }, this.guessLineEndings = function(e, t) {
                e = e.substring(0, 1048576), t = new RegExp(m(t) + "([^]*?)" + m(t), "gm");
                var n = (e = e.replace(t, "")).split("\r");
                if (e = 1 < (t = e.split("\n")).length && t[0].length < n[0].length, 1 === n.length || e) return "\n";
                for (var s = 0, i = 0; i < n.length; i++) "\n" === n[i][0] && s++;
                return s >= n.length / 2 ? "\r\n" : "\r"
            }
        }

        function m(e) {
            return e.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
        }

        function v(e) {
            var t = (e = e || {}).delimiter,
                n = e.newline,
                s = e.comments,
                i = e.step,
                o = e.preview,
                a = e.fastMode,
                r = null,
                u = !1,
                c = null == e.quoteChar ? '"' : e.quoteChar,
                d = c;
            if (void 0 !== e.escapeChar && (d = e.escapeChar), ("string" != typeof t || -1 < l.BAD_DELIMITERS.indexOf(t)) && (t = ","), s === t) throw new Error("Comment character same as delimiter");
            !0 === s ? s = "#" : ("string" != typeof s || -1 < l.BAD_DELIMITERS.indexOf(s)) && (s = !1), "\n" !== n && "\r" !== n && "\r\n" !== n && (n = "\n");
            var p = 0,
                h = !1;
            this.parse = function(l, v, f) {
                if ("string" != typeof l) throw new Error("Input must be a string");
                var g = l.length,
                    _ = t.length,
                    w = n.length,
                    y = s.length,
                    x = b(i),
                    k = [],
                    C = [],
                    I = [],
                    R = p = 0;
                if (!l) return P();
                if (a || !1 !== a && -1 === l.indexOf(c)) {
                    for (var S = l.split(n), E = 0; E < S.length; E++) {
                        if (I = S[E], p += I.length, E !== S.length - 1) p += n.length;
                        else if (f) return P();
                        if (!s || I.substring(0, y) !== s) {
                            if (x) {
                                if (k = [], T(I.split(t)), F(), h) return P()
                            } else T(I.split(t));
                            if (o && o <= E) return k = k.slice(0, o), P(!0)
                        }
                    }
                    return P()
                }
                for (var L = l.indexOf(t, p), j = l.indexOf(n, p), M = new RegExp(m(d) + m(c), "g"), D = l.indexOf(c, p);;)
                    if (l[p] === c)
                        for (D = p, p++;;) {
                            if (-1 === (D = l.indexOf(c, D + 1))) return f || C.push({
                                type: "Quotes",
                                code: "MissingQuotes",
                                message: "Quoted field unterminated",
                                row: k.length,
                                index: p
                            }), B();
                            if (D === g - 1) return B(l.substring(p, D).replace(M, c));
                            if (c === d && l[D + 1] === d) D++;
                            else if (c === d || 0 === D || l[D - 1] !== d) {
                                -1 !== L && L < D + 1 && (L = l.indexOf(t, D + 1));
                                var O = A(-1 === (j = -1 !== j && j < D + 1 ? l.indexOf(n, D + 1) : j) ? L : Math.min(L, j));
                                if (l.substr(D + 1 + O, _) === t) {
                                    I.push(l.substring(p, D).replace(M, c)), l[p = D + 1 + O + _] !== c && (D = l.indexOf(c, p)), L = l.indexOf(t, p), j = l.indexOf(n, p);
                                    break
                                }
                                if (O = A(j), l.substring(D + 1 + O, D + 1 + O + w) === n) {
                                    if (I.push(l.substring(p, D).replace(M, c)), N(D + 1 + O + w), L = l.indexOf(t, p), D = l.indexOf(c, p), x && (F(), h)) return P();
                                    if (o && k.length >= o) return P(!0);
                                    break
                                }
                                C.push({
                                    type: "Quotes",
                                    code: "InvalidQuotes",
                                    message: "Trailing quote on quoted field is malformed",
                                    row: k.length,
                                    index: p
                                }), D++
                            }
                        } else if (s && 0 === I.length && l.substring(p, p + y) === s) {
                            if (-1 === j) return P();
                            p = j + w, j = l.indexOf(n, p), L = l.indexOf(t, p)
                        } else if (-1 !== L && (L < j || -1 === j)) I.push(l.substring(p, L)), p = L + _, L = l.indexOf(t, p);
                else {
                    if (-1 === j) break;
                    if (I.push(l.substring(p, j)), N(j + w), x && (F(), h)) return P();
                    if (o && k.length >= o) return P(!0)
                }
                return B();

                function T(e) {
                    k.push(e), R = p
                }

                function A(e) {
                    var t = 0;
                    return -1 !== e && (e = l.substring(D + 1, e)) && "" === e.trim() ? e.length : t
                }

                function B(e) {
                    return f || (void 0 === e && (e = l.substring(p)), I.push(e), p = g, T(I), x && F()), P()
                }

                function N(e) {
                    p = e, T(I), I = [], j = l.indexOf(n, p)
                }

                function P(s) {
                    if (e.header && !v && k.length && !u) {
                        var i = k[0],
                            o = {},
                            a = new Set(i);
                        let t = !1;
                        for (let n = 0; n < i.length; n++) {
                            let s = i[n];
                            if (o[s = b(e.transformHeader) ? e.transformHeader(s, n) : s]) {
                                let e, l = o[s];
                                for (; e = s + "_" + l, l++, a.has(e););
                                a.add(e), i[n] = e, o[s]++, t = !0, (r = null === r ? {} : r)[e] = s
                            } else o[s] = 1, i[n] = s;
                            a.add(s)
                        }
                        u = !0
                    }
                    return {
                        data: k,
                        errors: C,
                        meta: {
                            delimiter: t,
                            linebreak: n,
                            aborted: h,
                            truncated: !!s,
                            cursor: R + (v || 0),
                            renamedHeaders: r
                        }
                    }
                }

                function F() {
                    i(P()), k = [], C = []
                }
            }, this.abort = function() {
                h = !0
            }, this.getCharIndex = function() {
                return p
            }
        }

        function f(e) {
            var t = e.data,
                n = o[t.workerId],
                s = !1;
            if (t.error) n.userError(t.error, t.file);
            else if (t.results && t.results.data) {
                var i = {
                    abort: function() {
                        s = !0, g(t.workerId, {
                            data: [],
                            errors: [],
                            meta: {
                                aborted: !0
                            }
                        })
                    },
                    pause: _,
                    resume: _
                };
                if (b(n.userStep)) {
                    for (var a = 0; a < t.results.data.length && (n.userStep({
                            data: t.results.data[a],
                            errors: t.results.errors,
                            meta: t.results.meta
                        }, i), !s); a++);
                    delete t.results
                } else b(n.userChunk) && (n.userChunk(t.results, i, t.file), delete t.results)
            }
            t.finished && !s && g(t.workerId, t.results)
        }

        function g(e, t) {
            var n = o[e];
            b(n.userComplete) && n.userComplete(t), n.terminate(), delete o[e]
        }

        function _() {
            throw new Error("Not implemented.")
        }

        function w(e) {
            if ("object" != typeof e || null === e) return e;
            var t, n = Array.isArray(e) ? [] : {};
            for (t in e) n[t] = w(e[t]);
            return n
        }

        function y(e, t) {
            return function() {
                e.apply(t, arguments)
            }
        }

        function b(e) {
            return "function" == typeof e
        }
        return l.parse = function(t, s) {
            var i, r = (s = s || {}).dynamicTyping || !1;
            if (b(r) && (s.dynamicTypingFunction = r, r = {}), s.dynamicTyping = r, s.transform = !!b(s.transform) && s.transform, !s.worker || !l.WORKERS_SUPPORTED) return r = null, l.NODE_STREAM_INPUT, "string" == typeof t ? (t = 65279 !== (i = t).charCodeAt(0) ? i : i.slice(1), r = new(s.download ? u : d)(s)) : !0 === t.readable && b(t.read) && b(t.on) ? r = new p(s) : (n.File && t instanceof File || t instanceof Object) && (r = new c(s)), r.stream(t);
            (r = (() => {
                var t, s, i;
                return !!l.WORKERS_SUPPORTED && (s = n.URL || n.webkitURL || null, i = e.toString(), t = l.BLOB_URL || (l.BLOB_URL = s.createObjectURL(new Blob(["var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; ", "(", i, ")();"], {
                    type: "text/javascript"
                }))), (t = new n.Worker(t)).onmessage = f, t.id = a++, o[t.id] = t)
            })()).userStep = s.step, r.userChunk = s.chunk, r.userComplete = s.complete, r.userError = s.error, s.step = b(s.step), s.chunk = b(s.chunk), s.complete = b(s.complete), s.error = b(s.error), delete s.worker, r.postMessage({
                input: t,
                config: s,
                workerId: r.id
            })
        }, l.unparse = function(e, t) {
            var n = !1,
                s = !0,
                i = ",",
                o = "\r\n",
                a = '"',
                r = a + a,
                u = !1,
                c = null,
                d = !1,
                p = ((() => {
                    if ("object" == typeof t) {
                        if ("string" != typeof t.delimiter || l.BAD_DELIMITERS.filter((function(e) {
                                return -1 !== t.delimiter.indexOf(e)
                            })).length || (i = t.delimiter), "boolean" != typeof t.quotes && "function" != typeof t.quotes && !Array.isArray(t.quotes) || (n = t.quotes), "boolean" != typeof t.skipEmptyLines && "string" != typeof t.skipEmptyLines || (u = t.skipEmptyLines), "string" == typeof t.newline && (o = t.newline), "string" == typeof t.quoteChar && (a = t.quoteChar), "boolean" == typeof t.header && (s = t.header), Array.isArray(t.columns)) {
                            if (0 === t.columns.length) throw new Error("Option columns is empty");
                            c = t.columns
                        }
                        void 0 !== t.escapeChar && (r = t.escapeChar + a), t.escapeFormulae instanceof RegExp ? d = t.escapeFormulae : "boolean" == typeof t.escapeFormulae && t.escapeFormulae && (d = /^[=+\-@\t\r].*$/)
                    }
                })(), new RegExp(m(a), "g"));
            if ("string" == typeof e && (e = JSON.parse(e)), Array.isArray(e)) {
                if (!e.length || Array.isArray(e[0])) return h(null, e, u);
                if ("object" == typeof e[0]) return h(c || Object.keys(e[0]), e, u)
            } else if ("object" == typeof e) return "string" == typeof e.data && (e.data = JSON.parse(e.data)), Array.isArray(e.data) && (e.fields || (e.fields = e.meta && e.meta.fields || c), e.fields || (e.fields = Array.isArray(e.data[0]) ? e.fields : "object" == typeof e.data[0] ? Object.keys(e.data[0]) : []), Array.isArray(e.data[0]) || "object" == typeof e.data[0] || (e.data = [e.data])), h(e.fields || [], e.data || [], u);
            throw new Error("Unable to serialize unrecognized input");

            function h(e, t, n) {
                var a = "",
                    l = ("string" == typeof e && (e = JSON.parse(e)), "string" == typeof t && (t = JSON.parse(t)), Array.isArray(e) && 0 < e.length),
                    r = !Array.isArray(t[0]);
                if (l && s) {
                    for (var u = 0; u < e.length; u++) 0 < u && (a += i), a += v(e[u], u);
                    0 < t.length && (a += o)
                }
                for (var c = 0; c < t.length; c++) {
                    var d = (l ? e : t[c]).length,
                        p = !1,
                        h = l ? 0 === Object.keys(t[c]).length : 0 === t[c].length;
                    if (n && !l && (p = "greedy" === n ? "" === t[c].join("").trim() : 1 === t[c].length && 0 === t[c][0].length), "greedy" === n && l) {
                        for (var m = [], f = 0; f < d; f++) {
                            var g = r ? e[f] : f;
                            m.push(t[c][g])
                        }
                        p = "" === m.join("").trim()
                    }
                    if (!p) {
                        for (var _ = 0; _ < d; _++) {
                            0 < _ && !h && (a += i);
                            var w = l && r ? e[_] : _;
                            a += v(t[c][w], _)
                        }
                        c < t.length - 1 && (!n || 0 < d && !h) && (a += o)
                    }
                }
                return a
            }

            function v(e, t) {
                var s, o;
                return null == e ? "" : e.constructor === Date ? JSON.stringify(e).slice(1, 25) : (o = !1, d && "string" == typeof e && d.test(e) && (e = "'" + e, o = !0), s = e.toString().replace(p, r), (o = o || !0 === n || "function" == typeof n && n(e, t) || Array.isArray(n) && n[t] || ((e, t) => {
                    for (var n = 0; n < t.length; n++)
                        if (-1 < e.indexOf(t[n])) return !0;
                    return !1
                })(s, l.BAD_DELIMITERS) || -1 < s.indexOf(i) || " " === s.charAt(0) || " " === s.charAt(s.length - 1)) ? a + s + a : s)
            }
        }, l.RECORD_SEP = String.fromCharCode(30), l.UNIT_SEP = String.fromCharCode(31), l.BYTE_ORDER_MARK = "\ufeff", l.BAD_DELIMITERS = ["\r", "\n", '"', l.BYTE_ORDER_MARK], l.WORKERS_SUPPORTED = !s && !!n.Worker, l.NODE_STREAM_INPUT = 1, l.LocalChunkSize = 10485760, l.RemoteChunkSize = 5242880, l.DefaultDelimiter = ",", l.Parser = v, l.ParserHandle = h, l.NetworkStreamer = u, l.FileStreamer = c, l.StringStreamer = d, l.ReadableStreamStreamer = p, n.jQuery && ((t = n.jQuery).fn.parse = function(e) {
            var s = e.config || {},
                i = [];
            return this.each((function(e) {
                if ("INPUT" !== t(this).prop("tagName").toUpperCase() || "file" !== t(this).attr("type").toLowerCase() || !n.FileReader || !this.files || 0 === this.files.length) return !0;
                for (var o = 0; o < this.files.length; o++) i.push({
                    file: this.files[o],
                    inputElem: this,
                    instanceConfig: t.extend({}, s)
                })
            })), o(), this;

            function o() {
                if (0 === i.length) b(e.complete) && e.complete();
                else {
                    var n, s, o, r, u = i[0];
                    if (b(e.before)) {
                        var c = e.before(u.file, u.inputElem);
                        if ("object" == typeof c) {
                            if ("abort" === c.action) return n = "AbortError", s = u.file, o = u.inputElem, r = c.reason, void(b(e.error) && e.error({
                                name: n
                            }, s, o, r));
                            if ("skip" === c.action) return void a();
                            "object" == typeof c.config && (u.instanceConfig = t.extend(u.instanceConfig, c.config))
                        } else if ("skip" === c) return void a()
                    }
                    var d = u.instanceConfig.complete;
                    u.instanceConfig.complete = function(e) {
                        b(d) && d(e, u.file, u.inputElem), a()
                    }, l.parse(u.file, u.instanceConfig)
                }
            }

            function a() {
                i.splice(0, 1), o()
            }
        }), i && (n.onmessage = function(e) {
            e = e.data, void 0 === l.WORKER_ID && e && (l.WORKER_ID = e.workerId), "string" == typeof e.input ? n.postMessage({
                workerId: l.WORKER_ID,
                results: l.parse(e.input, e.config),
                finished: !0
            }) : (n.File && e.input instanceof File || e.input instanceof Object) && (e = l.parse(e.input, e.config)) && n.postMessage({
                workerId: l.WORKER_ID,
                results: e,
                finished: !0
            })
        }), (u.prototype = Object.create(r.prototype)).constructor = u, (c.prototype = Object.create(r.prototype)).constructor = c, (d.prototype = Object.create(d.prototype)).constructor = d, (p.prototype = Object.create(r.prototype)).constructor = p, l
    }()), ht.exports)),
    vt = {
        class: "text"
    },
    ft = {
        class: "icon"
    };

function gt(e) {
    var t;
    return t = e, !! function(e) {
        return e && "string" == typeof e && e.includes("-") || e.includes("(") && e.includes(")")
    }(e = ne.sanitize(t, {
        ALLOWED_TAGS: []
    })) && (function(e) {
        return /^\s*(?:\+81[\s\-\.]?|0)[1-9]\d{0,3}[\s\-\.]?\d{1,4}[\s\-\.]?\d{4}\s*$/.test(e.trim())
    }(e) || function(e) {
        return /^\s*(?:\+1[\s\-\.]?)?(?:\(\d{3}\)|\d{3})[\s\-\.]?\d{3}[\s\-\.]?\d{4}\s*$/.test(e.trim())
    }(e))
}
const _t = x(a({
        __name: "CallForMeButton",
        props: {
            phoneNum: {},
            tableRow: {},
            tableData: {}
        },
        setup(e) {
            const {
                t: t,
                locale: n
            } = l(), a = e;

            function r(e, t) {
                const n = t.columns.map((e => e.label)),
                    s = e;
                return JSON.stringify({
                    columns: n,
                    row: s
                })
            }
            async function u() {
                const e = await async function() {
                    const e = await fetch(`/api/project/table_row_phone_call_message?project_id=${ie()}`, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: r(a.tableRow, a.tableData)
                    });
                    e.ok;
                    const t = await e.json();
                    return !t || t.data, t.data
                }(a.phoneNum);
                ! function(e, t) {
                    const n = new URL("agents", window.location.origin);
                    n.searchParams.append("type", "phone_call"), t.place_id && n.searchParams.append("place_id", t.place_id), t.place_title && n.searchParams.append("place_title", t.place_title), t.place_address && n.searchParams.append("place_address", t.place_address), t.purpose && n.searchParams.append("purpose", t.purpose), t.contact_type && n.searchParams.append("contact_type", t.contact_type), t.country && n.searchParams.append("country", t.country), t.contact_name && n.searchParams.append("contact_name", t.contact_name), t.contact_phone && n.searchParams.append("contact_phone", t.contact_phone), t.country_code && n.searchParams.append("country_code", t.country_code), window.open(n.toString(), "_blank")
                }(a.phoneNum, e)
            }
            return (e, n) => (o(), s("button", {
                class: "call-for-me",
                onClick: u,
                onMousedown: n[0] || (n[0] = C((() => {}), ["stop"]))
            }, [i("div", vt, [i("div", ft, [h(_(me))]), i("span", null, v(_(t)("pages.call_assistant.call_for_me")), 1)])], 32))
        }
    }), [
        ["__scopeId", "data-v-ca8c5d1e"]
    ]),
    wt = ["textContent", "innerHTML"],
    yt = ["textContent", "innerHTML"],
    bt = {
        key: 1,
        class: "expanded-text"
    },
    xt = ["src"];
const kt = x(a({
        __name: "ExpandableText",
        props: {
            text: {
                type: String,
                required: !0
            },
            columnIndex: {
                type: Number,
                required: !0
            },
            forceShowExpanded: {
                type: Boolean,
                default: !1
            },
            tableRow: {
                type: Object,
                required: !1
            },
            tableData: {
                type: Object,
                required: !1
            },
            slotMode: {
                type: Boolean,
                default: !1
            }
        },
        setup(e) {
            const t = e,
                n = r(null),
                a = r(null),
                l = r(!1);
            S((() => {
                var e;
                l.value = "string" == typeof(e = t.text) && e.includes("<")
            }));
            const u = r("");
            S((() => {
                if (!l.value) return;
                u.value = ne.sanitize(t.text, {
                    FORBID_TAGS: ["script"],
                    ADD_TAGS: ["iframe"]
                });
                const e = String(t.text).trim().match(/^<ref-text +source='([^>]*?)'>.*<\/ref-text>$/);
                if (e) {
                    const t = e[1];
                    u.value = u.value + `<a href="${encodeURI(t)}" target="_blank" class="dataset-ref-text"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M8 6a.5.5 0 0 1 .09.992L8 7H6a3 3 0 0 0-.197 5.994L6 13h2a.5.5 0 0 1 .09.992L8 14H6a4 4 0 0 1-.22-7.994L6 6h2zm6 0a4 4 0 0 1 .22 7.994L14 14h-2a.5.5 0 0 1-.09-.992L12 13h2a3 3 0 0 0 .197-5.994L14 7h-2a.5.5 0 0 1-.09-.992L12 6h2zM6 9.5h8a.5.5 0 0 1 .09.992L14 10.5H6a.5.5 0 0 1-.09-.992L6 9.5h8h-8z" fill="currentColor"></path></g></svg></a>`
                }
            }));
            const h = r(!1);
            S((() => {
                h.value = !1, u.value && T((() => {
                    a.value && (h.value = Array.from(a.value.children).every((e => "IMG" === e.tagName)))
                }))
            }));
            const m = r(""),
                _ = r(null),
                w = r(null);

            function y(e) {
                if (m.value) return;
                const t = e.target;
                if ("IMG" === t.tagName && (m.value = t.src, null === _.value)) {
                    let e = function(e) {
                            let n = t.parentElement;
                            for (; n && n.tagName !== e;) n = n.parentElement;
                            return n
                        },
                        n = function(e) {
                            let n = t.parentElement;
                            for (; n && !n.classList.contains(e);) n = n.parentElement;
                            return n
                        };
                    const s = e("TR");
                    if (!s) return;
                    const i = s.getBoundingClientRect(),
                        o = t.getBoundingClientRect(),
                        a = 272,
                        l = 160;
                    _.value = o.left - i.left + a > i.width;
                    const r = e("TABLE");
                    if (!r) return;
                    const u = n("j-dataset-table-container");
                    if (!u) return;
                    const c = r.getBoundingClientRect(),
                        d = u.getBoundingClientRect();
                    w.value = o.top - c.top + l > Math.max(c.height, d.height)
                }
            }
            const b = r(!1);
            S((() => {
                t.text && t.tableRow && t.tableData && (b.value = gt(t.text))
            }));
            const x = r(!1);

            function k() {
                x.value = !0
            }

            function C() {
                x.value = !1, m.value = "", _.value = null, w.value = null
            }
            const I = r(!1);
            return S((() => {
                I.value = x.value || t.forceShowExpanded
            })), c((() => t.forceShowExpanded), (e => {
                e && T((() => {
                    if (!n.value) return;
                    const e = n.value.querySelector(".expandable-text-content");
                    null == e || e.scrollIntoView({
                        behavior: "smooth",
                        block: "center",
                        inline: "center"
                    })
                }))
            }), {
                immediate: !0
            }), (r, c) => (o(), s("div", {
                class: "expandable-text",
                ref_key: "elContainer",
                ref: n,
                onMouseenter: k,
                onMouseleave: C
            }, [e.slotMode ? (o(), s("span", {
                key: 1,
                class: "expandable-text-content",
                ref_key: "elExpandableTextContent",
                ref: a,
                onMousemove: y
            }, [L(r.$slots, "default", {}, void 0, !0)], 544)) : (o(), s("span", {
                key: 0,
                class: "expandable-text-content",
                ref_key: "elExpandableTextContent",
                ref: a,
                textContent: v(l.value ? null : t.text),
                innerHTML: l.value ? u.value : null,
                onMousemove: y
            }, null, 40, wt)), h.value || b.value || !I.value ? p("", !0) : (o(), s(d, {
                key: 2
            }, [e.slotMode ? (o(), s("div", bt, [L(r.$slots, "default", {}, void 0, !0)])) : (o(), s("div", {
                key: 0,
                class: "expanded-text",
                textContent: v(l.value ? null : t.text),
                innerHTML: l.value ? u.value : null
            }, null, 8, yt))], 64)), h.value && m.value ? (o(), s("div", {
                key: 3,
                class: f(["image-preview", {
                    "image-preview-on-right": _.value,
                    "image-preview-on-bottom": w.value
                }])
            }, [i("img", {
                src: m.value
            }, null, 8, xt)], 2)) : p("", !0), t.tableRow && t.tableData && b.value ? (o(), g(_t, {
                key: 4,
                "phone-num": t.text,
                "table-row": t.tableRow,
                "table-data": t.tableData
            }, null, 8, ["phone-num", "table-row", "table-data"])) : p("", !0)], 544))
        }
    }), [
        ["__scopeId", "data-v-373d688e"]
    ]),
    Ct = {
        class: "tag-value"
    },
    It = x(a({
        __name: "ColoredTagItem",
        props: {
            value: {
                type: String,
                required: !0
            },
            column: {
                type: Object,
                required: !0
            },
            clickable: {
                type: Boolean,
                default: !1
            },
            closable: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["click", "close"],
        setup(e, {
            emit: t
        }) {
            const n = e,
                a = u((() => {
                    var e, t;
                    return null == (t = null == (e = n.column) ? void 0 : e.enumItems) ? void 0 : t[n.value]
                })),
                l = u((() => !a.value)),
                r = u((() => {
                    var e;
                    return null == (e = a.value) ? void 0 : e.color
                })),
                c = t,
                m = () => {
                    l.value || (n.closable ? c("close") : n.clickable && c("click"))
                },
                g = u((() => n.value ? `<span class="tag" style="background-color: ${r.value}">${n.value}</span>` : ""));
            return (t, a) => {
                var u;
                return o(), s(d, null, [e.closable ? (o(), s("div", {
                    key: 0,
                    class: f(["colored-tag-item", {
                        invalid: l.value,
                        clickable: e.clickable,
                        closable: e.closable
                    }]),
                    style: k({
                        "background-color": r.value
                    }),
                    onClick: m
                }, [i("span", Ct, v(e.value), 1), h(_(se), {
                    class: "close-button"
                })], 6)) : p("", !0), h(kt, {
                    text: g.value,
                    "column-index": (null == (u = n.column) ? void 0 : u.index) ? ? 0
                }, null, 8, ["text", "column-index"])], 64)
            }
        }
    }), [
        ["__scopeId", "data-v-1a73836c"]
    ]),
    Rt = ["#fee3e3", "#fee7cd", "#faeec2", "#e4f0a4", "#d0f5ce", "#c4f2ec", "#caeffc", "#e0e9ff", "#ffe2f2", "#efe6fe", "#eef0f1"],
    St = [];

function Et(e, t) {
    const n = e[t.index];
    n && function(e, t) {
        if (t.enumItems ? ? (t.enumItems = {}), t.curEnumColorIndex ? ? (t.curEnumColorIndex = String(e).charCodeAt(e.length - 1) % Rt.length), t.enumItems[e]) return;
        t.enumItems[e] = {
            value: e,
            color: Rt[t.curEnumColorIndex]
        }, t.curEnumColorIndex++, t.curEnumColorIndex >= Rt.length && (t.curEnumColorIndex = 0)
    }(n, t)
}
const Lt = a({
    __name: "TableCellRadio",
    props: {
        column: {
            type: Object,
            required: !0
        },
        value: {
            type: String,
            required: !0
        }
    },
    setup(e) {
        const t = e,
            n = () => {
                var e;
                t.column && ((e = t.column).filterBy ? ? (e.filterBy = new Set), t.column.filterBy.add(t.value))
            },
            s = r(!1);
        return S((() => {
            var e;
            t.column && (s.value = (null == (e = t.column.filterBy) ? void 0 : e.has(t.value)) ? ? !1)
        })), (t, i) => (o(), g(It, {
            value: e.value,
            column: e.column,
            clickable: !_(s),
            onClick: n
        }, null, 8, ["value", "column", "clickable"]))
    }
});

function jt(e, t) {
    "Html" !== t.type && e.addColumnTypeFromServer(t)
}
const Mt = r(null);

function Dt() {
    return {
        lastUpdatedCell: Mt
    }
}
class Ot {
    constructor(e) {
        t(this, "tableData"), this.tableData = e
    }
}
class Tt extends Ot {
    constructor(e, n, s) {
        super(e), t(this, "newRows"), t(this, "index"), this.newRows = n, this.index = s
    }
    apply() {
        const e = this.tableData.value.limitRowsCount(this.newRows).map((e => this.tableData.value.createRow(e)));
        null != this.index ? this.tableData.value.rows.splice(this.index, 0, ...e) : this.tableData.value.rows.push(...e), this.tableData.value.columns.forEach((t => {
            jt(this.tableData.value, t), "EnumList" !== t.type && "Enum" !== t.type || e.forEach((e => Et(e, t)))
        }))
    }
}
class At extends Ot {
    constructor(e, n) {
        super(e), t(this, "removedRows"), this.removedRows = n
    }
    apply() {
        this.tableData.value.rows = this.tableData.value.rows.filter(((e, t) => !this.removedRows.includes(t)))
    }
}
class Bt extends Ot {
    constructor(e, n, s) {
        super(e), t(this, "newColumns"), t(this, "index"), this.newColumns = n, this.index = s ? ? e.value.columns.length
    }
    apply() {
        const e = this.tableData.value;
        let t = new Map,
            n = this.newColumns.map((e => {
                const n = Jt(e[0]);
                return t.set(n, e), n
            }));
        n.forEach(Qt), n.forEach(Zt), n.forEach(Xt), n.forEach((t => e.addColumnTypeFromServer(t))), e.columns.splice(this.index, 0, ...n), e.columns.forEach(Yt), e.rows.forEach(((e, s) => {
            const i = n.map((e => {
                const n = t.get(e);
                if (!n) throw new Error("original column not found");
                return n[s + 1]
            }));
            e.splice(this.index, 0, ...i)
        })), n.forEach((t => {
            jt(e, t), "EnumList" !== t.type && "Enum" !== t.type || e.rows.forEach((e => Et(e, t)))
        }))
    }
}
class Nt extends Ot {
    constructor(e, n) {
        super(e), t(this, "removedColumns"), this.removedColumns = n
    }
    apply() {
        let e = this.tableData.value.columns.map((e => !1));
        this.removedColumns.forEach((t => e[t] = !0));
        const t = [];
        e.forEach(((e, n) => {
            e && t.push(n)
        })), this.tableData.value.rows = this.tableData.value.rows.map((e => {
            const n = e.filter(((e, n) => !t.includes(n)));
            return n.belongsTo = e.belongsTo, n.raw = e.raw, n.id = e.id, n.imageUrl = e.imageUrl, n.html = e.html, n.tags = e.tags, n
        })), this.tableData.value.columns = this.tableData.value.columns.filter(((e, n) => !t.includes(n)))
    }
}

function Pt(e, t) {
    const n = e.value.columns,
        s = t + 1 - n.length;
    s >= 1 && n.push(...Array(s).fill(null).map(((e, t) => Jt("", t + n.length))))
}
class Ft extends Ot {
    constructor(e, n) {
        super(e), t(this, "updatedCells"), this.updatedCells = n
    }
    apply() {
        this.expandRowsIfNeeded(), this.expandColumnsIfNeeded(), this.updatedCells.forEach((([e, t, n]) => {
            this.tableData.value.rows[e][t] = n
        })), 1 === this.updatedCells.length && (Dt().lastUpdatedCell.value = [this.updatedCells[0][0], this.updatedCells[0][1]])
    }
    expandRowsIfNeeded() {
        const e = this.tableData.value.rows,
            t = Math.max(...this.updatedCells.map((([e]) => e)));
        t >= e.length && e.push(...Array(t - e.length + 1).fill(null).map((() => this.tableData.value.createRow([]))))
    }
    expandColumnsIfNeeded() {
        Pt(this.tableData, Math.max(...this.updatedCells.map((([, e]) => e))))
    }
}
class qt extends Ot {
    constructor(e, n) {
        super(e), t(this, "updatedRows"), t(this, "index"), this.updatedRows = n
    }
    apply() {
        const e = [];
        this.updatedRows.forEach((({
            index: t,
            values: n
        }) => {
            var s;
            const i = this.tableData.value.createRow(n),
                o = null == (s = this.tableData.value.rows[t]) ? void 0 : s.id;
            o && (i.id = o), this.tableData.value.rows[t] = i, e.push(i)
        })), this.tableData.value.columns.forEach((t => {
            jt(this.tableData.value, t), "EnumList" !== t.type && "Enum" !== t.type || e.forEach((e => Et(e, t)))
        }))
    }
}
class zt extends Ot {
    constructor(e, n) {
        super(e), t(this, "items"), this.items = n
    }
    apply() {
        this.expandColumnsIfNeeded(), this.items.forEach((([e, t]) => {
            this.tableData.value.columns[e].label = t
        }))
    }
    expandColumnsIfNeeded() {
        Pt(this.tableData, Math.max(...this.items.map((([e]) => e))))
    }
}

function Ut(e) {
    const t = Kt(e.trim());
    return t && t.length ? (t.length, t.length > 1 ? t : t[0]) : []
}

function $t(e, t) {
    let n;
    switch (e.type) {
        case "addRows":
            if (!e.newRows) break;
            n = new Tt(t, e.newRows.map(Ut), e.index);
            break;
        case "removeRows":
            if (!e.removedRows) break;
            n = new At(t, e.removedRows);
            break;
        case "addColumns":
            if (!e.newColumns) break;
            if (void 0 === e.index) break;
            n = new Bt(t, e.newColumns, e.index);
            break;
        case "removeColumns":
            if (!e.removedColumns) break;
            n = new Nt(t, e.removedColumns);
            break;
        case "updateCells":
            if (!e.updatedCells) break;
            n = new Ft(t, e.updatedCells);
            break;
        case "updateRows":
            if (!e.updatedRows) break;
            n = new qt(t, e.updatedRows);
            break;
        case "updateColumnNames":
            if (!e.items) break;
            n = new zt(t, e.items)
    }
    null == n || n.apply()
}
const Ht = 2e3,
    Vt = {
        entity_name: {
            label: "Name",
            icon: A(ut)
        },
        round_name: {
            label: "Name",
            icon: A(ut)
        },
        permalink: {},
        image_id: {
            visible: !1,
            asCrunchbaseImageId: !0
        },
        uuid: {
            asRowId: !0
        },
        short_description: {
            label: "Description",
            icon: A(dt)
        },
        linkedin: {
            isUrl: !0
        },
        twitter: {
            isUrl: !0
        },
        website: {
            isUrl: !0
        }
    },
    Wt = ["Number", "Date", "Email", "ImageList", "Link", "Enum", "String", "EnumList", "Person", "PersonList", "Organization", "OrganizationList", "Html"];
class Gt {
    constructor(e) {
        t(this, "rawColumns"), t(this, "columns"), t(this, "rows"), t(this, "_rowIdIndexInRaw"), t(this, "_crunchbaseImageIdIndexInRaw"), t(this, "_serverSideColumnConfigMap"), t(this, "hasMainEntity"), t(this, "_warningTooMany"), t(this, "_tooLarge", !1), t(this, "currentRowId", 0), this._warningTooMany = e, this.rawColumns = [], this.columns = [], this.rows = [], this._rowIdIndexInRaw = -1, this._crunchbaseImageIdIndexInRaw = -1, this._serverSideColumnConfigMap = {}, this.hasMainEntity = !1
    }
    limitRowsCount(e) {
        return this._tooLarge ? [] : (e.length + this.rows.length > Ht && (e = e.slice(0, Ht - this.rows.length), this._tooLarge = !0, this._warningTooMany && this._warningTooMany(Ht)), e)
    }
    _findRowIdIndexInRaw() {
        this.rawColumns.forEach(((e, t) => {
            const n = Vt[e.id];
            n && n.asRowId && (this._rowIdIndexInRaw = t)
        }))
    }
    _findImageIdIndexInRaw() {
        this.rawColumns.forEach(((e, t) => {
            const n = Vt[e.id];
            n && n.asCrunchbaseImageId && (this._crunchbaseImageIdIndexInRaw = t)
        }))
    }
    _parseServerSideColumnConfig(e) {
        var t;
        if (e.column_description) {
            null == (t = e.column_description) || t.forEach((e => {
                const t = e.column_name;
                if (!t) return;
                const n = e.column_type;
                n && (this._serverSideColumnConfigMap[t] = {
                    type: n
                })
            }));
            for (const e in this._serverSideColumnConfigMap) {
                const t = this._serverSideColumnConfigMap[e];
                "Organization" !== t.type && "Person" !== t.type || (this.hasMainEntity = !0)
            }
        }
    }
    addColumnTypeFromServer(e) {
        var t;
        const n = null == (t = this._serverSideColumnConfigMap[e.id]) ? void 0 : t.type;
        void 0 !== n ? (e.type = "String", n && (e.type = {
            string: "String",
            text: "String",
            image_list: "String",
            link: "String",
            email: "String"
        }[n] || n, Wt.includes(e.type) || (e.type = "String"))) : e.type = "Html"
    }
    createRow(e) {
        const t = [...e];
        if (t.belongsTo = this, t.raw = e, this._rowIdIndexInRaw > -1 ? t.id = e[this._rowIdIndexInRaw] : t.id = "" + ++this.currentRowId, this._crunchbaseImageIdIndexInRaw > -1) {
            const n = e[this._crunchbaseImageIdIndexInRaw];
            n && (t.imageUrl = `https://images.crunchbase.com/image/upload/c_pad,h_48,w_48,f_auto,b_white,q_auto:eco,dpr_1/${n}`)
        }
        return t
    }
    build(e, t) {
        this._parseServerSideColumnConfig(t);
        let {
            columns: n,
            rows: s
        } = function(e) {
            let t = [],
                n = [];
            if (!e) return {
                columns: t,
                rows: n
            };
            const s = Kt(e);
            if (0 === s.length) return {
                columns: t,
                rows: n
            };
            const i = s[0];
            return t = i.map(Jt), s.length <= 1 || (n = s.slice(1)), {
                columns: t,
                rows: n
            }
        }(e);
        this.rawColumns = n, this.columns = [...n], this.columns.forEach(Qt), this.columns.forEach(Zt), this.columns.forEach(Xt), this.columns.forEach((e => this.addColumnTypeFromServer(e))), this.columns.forEach(Yt), this._findRowIdIndexInRaw(), this._findImageIdIndexInRaw(), this.rows = this.limitRowsCount(s).map((e => this.createRow(e))), this.columns.forEach((e => {
            jt(this, e), "EnumList" !== e.type && "Enum" !== e.type || this.rows.forEach((t => Et(t, e)))
        }))
    }
    clear() {
        this.rawColumns = [], this.columns = [], this.rows = [], this._rowIdIndexInRaw = -1, this._crunchbaseImageIdIndexInRaw = -1, this._serverSideColumnConfigMap = {}, this.hasMainEntity = !1
    }
    noData() {
        return 0 === this.columns.length
    }
}

function Kt(e) {
    return mt.parse(e, {
        header: !1,
        skipEmptyLines: !0,
        dynamicTyping: !1
    }).data
}

function Jt(e, t) {
    return {
        index: null != t ? t : -1,
        id: e,
        label: e
    }
}

function Qt(e) {
    const t = Vt[e.id];
    t && (e.config = t)
}

function Zt(e) {
    var t;
    (null == (t = e.config) ? void 0 : t.label) && (e.label = e.config.label)
}

function Xt(e) {
    var t;
    (null == (t = e.config) ? void 0 : t.icon) && (e.icon = e.config.icon)
}

function Yt(e, t) {
    e.index = t
}

function en(e, t, n, s) {
    let i = r("");
    const o = r(new Gt(n));
    let a = r(void 0);
    return c(e, (async () => {
            var t, n;
            i.value ? "" === (null == (t = e.value) ? void 0 : t.csv_url) && (o.value.clear(), i.value = "") : (null == (n = e.value) ? void 0 : n.csv_url) && (a.value = e.value.delta, i.value = await async function(e, t) {
                try {
                    const t = await fetch(e);
                    if (!t.ok) throw new Error("fetch csv error: " + t.status + " " + t.statusText);
                    return await t.text()
                } catch (n) {
                    throw t && t(), n
                }
            }(e.value.csv_url, s), o.value.build(i.value, e.value))
        }), {
            immediate: !0
        }),
        function(e, t, n, s) {
            let i = !0;
            const o = [];
            c(t, (e => {
                var t;
                void 0 !== e && e.id !== (null == (t = s.value) ? void 0 : t.id) && (i ? o.push(e) : ($t(e, n), s.value = e))
            })), c(e, (() => {
                e.value && (i = !1, o.forEach((e => {
                    $t(e, n)
                })))
            }))
        }(i, t, o, a), o
}

function tn(e, t) {
    const n = t.filterBy;
    if (!n || 0 === n.size) return e;
    const s = t.index;
    return e.filter((e => {
        const t = e[s];
        return !!t && n.has(t)
    }))
}

function nn(e, t) {
    const n = t.filterBy;
    if (!n || 0 === n.size) return e;
    const s = t.index;
    return e.filter((e => {
        var t;
        const i = e[s];
        return !!i && (e.tags ? ? (e.tags = []), (t = e.tags)[s] ? ? (t[s] = i.split("、")), e.tags[s].some((e => n.has(e))))
    }))
}
const sn = {
    width: "12",
    height: "12",
    viewBox: "0 0 12 12",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    stroke: "#909499"
};
const on = {
        render: function(e, t) {
            return o(), s("svg", sn, t[0] || (t[0] = [i("path", {
                d: "M8.72727 7.46835L6 9.818M6 9.818L3.27273 7.46835M6 9.818V2.18164",
                "stroke-width": "0.845155",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    an = x(a({
        __name: "Sorter",
        props: {
            order: {}
        },
        emits: ["update"],
        setup(e, {
            emit: t
        }) {
            const n = e,
                i = t,
                a = u((() => n.order)),
                l = () => {
                    switch (a.value) {
                        case Ye.NONE:
                            i("update", Ye.DESCEND);
                            break;
                        case Ye.DESCEND:
                            i("update", Ye.ASCEND);
                            break;
                        case Ye.ASCEND:
                            i("update", Ye.NONE)
                    }
                };
            return (e, t) => (o(), s("i", {
                onClick: l,
                class: f({
                    active: a.value !== _(Ye).NONE,
                    ascending: a.value === _(Ye).ASCEND,
                    descending: a.value === _(Ye).DESCEND
                })
            }, [h(_(on))], 2))
        }
    }), [
        ["__scopeId", "data-v-92032089"]
    ]),
    ln = {
        width: "12",
        height: "12",
        viewBox: "0 0 12 12",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const rn = {
        render: function(e, t) {
            return o(), s("svg", ln, t[0] || (t[0] = [D('<g clip-path="url(#clip0_738_798)"><mask id="mask0_738_798" style="mask-type:luminance;" maskUnits="userSpaceOnUse" x="0" y="0" width="12" height="12"><path d="M12 0H0V12H12V0Z" fill="white"></path></mask><g mask="url(#mask0_738_798)"><path d="M3.2914 4.62379C3.83574 4.07942 4.48262 4.09448 5.18574 4.48007L7.81459 3.02296L7.66581 2L10 4.33418L8.97936 4.18774L7.51993 6.81428C7.88787 7.56475 7.92057 8.16426 7.37623 8.70862C7.37623 8.70862 6.49727 7.82966 5.77147 7.10386L2.41389 9.58612L4.88867 6.22106C4.16285 5.49524 3.2914 4.62379 3.2914 4.62379Z" stroke="#909499" stroke-width="0.85" stroke-linejoin="round"></path></g></g><defs><clipPath id="clip0_738_798"><rect width="12" height="12" fill="white"></rect></clipPath></defs>', 2)]))
        }
    },
    un = {
        width: "12",
        height: "12",
        viewBox: "0 0 12 12",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const cn = {
        render: function(e, t) {
            return o(), s("svg", un, t[0] || (t[0] = [D('<g clip-path="url(#clip0_738_804)"><mask id="mask0_738_804" style="mask-type:luminance;" maskUnits="userSpaceOnUse" x="0" y="0" width="12" height="12"><path d="M12 0H0V12H12V0Z" fill="white"></path></mask><g mask="url(#mask0_738_804)"><path d="M3.29139 4.62379C3.83573 4.07942 4.48261 4.09448 5.18573 4.48007L7.81458 3.02296L7.66581 2L10 4.33418L8.97935 4.18774L7.51992 6.81428C7.88786 7.56475 7.92057 8.16426 7.37622 8.70862C7.37622 8.70862 6.49726 7.82966 5.77146 7.10386L2.41388 9.58612L4.88866 6.22106C4.16284 5.49524 3.29139 4.62379 3.29139 4.62379Z" fill="#909499" stroke="#909499" stroke-width="0.85" stroke-linejoin="round"></path></g></g><defs><clipPath id="clip0_738_804"><rect width="12" height="12" fill="white"></rect></clipPath></defs>', 2)]))
        }
    },
    dn = ["title"],
    pn = {
        class: "header-content"
    },
    hn = x(a({
        __name: "DatasetTh",
        props: {
            column: {
                type: Object,
                required: !0
            },
            sortBy: {
                type: Object,
                required: !0
            },
            handleSorterUpdate: {
                type: Function,
                required: !0
            },
            handleFilterClose: {
                type: Function,
                required: !0
            }
        },
        setup(e) {
            const t = e,
                n = r(null),
                a = r(!1);
            return S((() => {
                n.value && t.column.label && T((() => {
                    n.value && (a.value = n.value.scrollWidth > n.value.clientWidth)
                }))
            })), (t, l) => (o(), s("th", {
                title: _(a) ? e.column.label : void 0
            }, [i("div", pn, [e.column.icon ? (o(), g(B(e.column.icon), {
                key: 0
            })) : p("", !0), i("span", {
                class: "header-title",
                ref_key: "refHeaderTitle",
                ref: n
            }, v(e.column.label), 513), h(an, {
                class: "icon",
                order: e.sortBy.column === e.column ? e.sortBy.order : _(Ye).NONE,
                onUpdate: l[0] || (l[0] = t => e.handleSorterUpdate(e.column, t))
            }, null, 8, ["order"]), e.column.pinned ? (o(), g(_(cn), {
                key: 1,
                class: "icon pin-icon",
                onClick: l[1] || (l[1] = t => e.column.pinned = !1)
            })) : (o(), g(_(rn), {
                key: 2,
                class: "pin-icon",
                onClick: l[2] || (l[2] = t => e.column.pinned = !0)
            }))]), (o(!0), s(d, null, E(e.column.filterBy, (t => (o(), g(It, {
                key: t,
                value: t,
                column: e.column,
                closable: !0,
                onClose: n => e.handleFilterClose(e.column, t)
            }, null, 8, ["value", "column", "onClose"])))), 128))], 8, dn))
        }
    }), [
        ["__scopeId", "data-v-8f67924e"]
    ]),
    mn = x(a({
        __name: "TableCellTagList",
        props: {
            column: {
                type: Object,
                required: !0
            },
            jsonString: {
                type: String,
                required: !0
            }
        },
        setup(e) {
            const t = e,
                n = r(null);
            S((() => {
                if (n.value = null, "string" == typeof t.jsonString)
                    if ("[" === t.jsonString[0]) try {
                        n.value = JSON.parse(t.jsonString)
                    } catch (e) {
                        n.value = null
                    } else n.value = t.jsonString.split(",").filter((e => "" !== e.trim())).map((e => e.trim()))
            }));
            const s = u((() => t.column.index)),
                i = r("");
            return S((() => {
                n.value ? i.value = n.value.map((e => `<span class="tag" style="background-color: ${function(e,t){if(St[t]||(St[t]={curIndex:0,map:new Map}),St[t].map.has(e))return St[t].map.get(e);const n=Rt[St[t].curIndex%Rt.length];return St[t].map.set(e,n),St[t].curIndex++,n}(e,s.value)}">${e}</span>`)).join(" ") : i.value = t.jsonString
            })), (e, t) => (o(), g(kt, {
                text: _(i),
                columnIndex: _(s)
            }, null, 8, ["text", "columnIndex"]))
        }
    }), [
        ["__scopeId", "data-v-9730319e"]
    ]),
    vn = ["src"],
    fn = ["href"],
    gn = {
        key: 2
    };
const _n = x(a({
        __name: "EntityItem",
        props: {
            entity: {},
            inList: {
                type: Boolean
            },
            useCirclePortrait: {
                type: Boolean
            },
            isPerson: {
                type: Boolean
            }
        },
        setup: e => (e, t) => {
            return o(), s("div", {
                class: f(["entity-item", {
                    "as-main-entity": !e.inList,
                    "use-circle-portrait": e.useCirclePortrait
                }])
            }, [e.entity.image_id ? (o(), s("img", {
                key: 0,
                src: (a = e.entity.image_id, `https://images.crunchbase.com/image/upload/c_pad,h_48,w_48,f_auto,b_white,q_auto:eco,dpr_1/${a}`),
                class: "td-portrait w-6 h-6"
            }, null, 8, vn)) : p("", !0), e.entity.permalink ? (o(), s("a", {
                key: 1,
                href: (n = e.entity.permalink, i = e.isPerson, i ? `https://www.crunchbase.com/person/${n}` : `https://www.crunchbase.com/organization/${n}`),
                target: "_blank",
                class: "td-text"
            }, v(e.entity.name), 9, fn)) : (o(), s("span", gn, v(e.entity.name), 1))], 2);
            var n, i, a
        }
    }), [
        ["__scopeId", "data-v-328b3bff"]
    ]),
    wn = {
        key: 1,
        class: "td-text"
    },
    yn = a({
        __name: "TableCellEntity",
        props: {
            jsonString: {},
            useCirclePortrait: {
                type: Boolean
            },
            isPerson: {
                type: Boolean
            }
        },
        setup(e) {
            const t = e,
                n = r(null);
            return S((() => {
                try {
                    n.value = JSON.parse(t.jsonString)
                } catch (e) {
                    n.value = null
                }
            })), (e, t) => _(n) ? (o(), g(_n, {
                key: 0,
                entity: _(n),
                "in-list": !1,
                "use-circle-portrait": e.useCirclePortrait,
                "is-person": e.isPerson
            }, null, 8, ["entity", "use-circle-portrait", "is-person"])) : (o(), s("div", wn, v(_(nt)(e.jsonString)), 1))
        }
    }),
    bn = x(a({
        __name: "TableCellEntityList",
        props: {
            jsonString: {},
            useCirclePortrait: {
                type: Boolean
            },
            columnIndex: {},
            isPerson: {
                type: Boolean
            }
        },
        setup(e) {
            const t = e,
                n = r(null);
            return S((() => {
                try {
                    n.value = JSON.parse(t.jsonString)
                } catch (e) {
                    n.value = null
                }
                Array.isArray(n.value) || (n.value = null)
            })), (e, t) => (o(), g(kt, {
                text: _(n) ? "" : e.jsonString,
                columnIndex: e.columnIndex,
                slotMode: !!_(n)
            }, {
                default: w((() => [(o(!0), s(d, null, E(_(n), (t => (o(), g(_n, {
                    class: "entity-item",
                    key: t.permalink,
                    entity: t,
                    "in-list": !0,
                    "use-circle-portrait": e.useCirclePortrait,
                    "is-person": e.isPerson
                }, null, 8, ["entity", "use-circle-portrait", "is-person"])))), 128))])),
                _: 1
            }, 8, ["text", "columnIndex", "slotMode"]))
        }
    }), [
        ["__scopeId", "data-v-b335a8cb"]
    ]),
    xn = x(a({
        __name: "DatasetTd",
        props: {
            tableData: {
                type: Object,
                required: !0
            },
            columnIndex: {
                type: Number,
                required: !0
            },
            row: {
                type: Object,
                required: !0
            },
            item: {
                type: [String, Number, Array, Object],
                required: !1
            },
            isSelected: {
                type: Boolean,
                default: !1
            },
            isTopInSelection: {
                type: Boolean,
                default: !1
            },
            isBottomInSelection: {
                type: Boolean,
                default: !1
            },
            isLeftInSelection: {
                type: Boolean,
                default: !1
            },
            isRightInSelection: {
                type: Boolean,
                default: !1
            }
        },
        setup(e) {
            const t = e;

            function n(e) {
                return null == e ? "" : String(e)
            }
            const {
                lastUpdatedCell: i
            } = Dt(), {
                isAsking: a
            } = oe(), l = u((() => !!a.value && (!!i.value && (t.tableData.rows[i.value[0]] === t.row && i.value[1] === t.columnIndex))));
            return (t, i) => (o(), s("td", {
                class: f({
                    selected: e.isSelected,
                    "top-in-selection": e.isTopInSelection,
                    "bottom-in-selection": e.isBottomInSelection,
                    "left-in-selection": e.isLeftInSelection,
                    "right-in-selection": e.isRightInSelection
                })
            }, [(o(!0), s(d, null, E([e.tableData.columns[e.columnIndex]], (t => (o(), s("div", {
                class: f(["td-content", {
                    "with-image": !e.tableData.hasMainEntity && 0 === e.columnIndex && e.row.imageUrl || "Organization" === (null == t ? void 0 : t.type)
                }])
            }, ["Enum" === (null == t ? void 0 : t.type) ? (o(), g(Lt, {
                key: 0,
                column: t,
                value: n(e.item)
            }, null, 8, ["column", "value"])) : "EnumList" === (null == t ? void 0 : t.type) ? (o(), g(mn, {
                key: 1,
                column: t,
                jsonString: n(e.item)
            }, null, 8, ["column", "jsonString"])) : "Organization" === (null == t ? void 0 : t.type) || "Person" === (null == t ? void 0 : t.type) ? (o(), g(yn, {
                key: 2,
                jsonString: n(e.item),
                "use-circle-portrait": "Person" === (null == t ? void 0 : t.type),
                "is-person": "Person" === (null == t ? void 0 : t.type)
            }, null, 8, ["jsonString", "use-circle-portrait", "is-person"])) : "OrganizationList" === (null == t ? void 0 : t.type) || "PersonList" === (null == t ? void 0 : t.type) ? (o(), g(bn, {
                key: 3,
                jsonString: n(e.item),
                "use-circle-portrait": "PersonList" === (null == t ? void 0 : t.type),
                "is-person": "PersonList" === (null == t ? void 0 : t.type),
                columnIndex: e.columnIndex
            }, null, 8, ["jsonString", "use-circle-portrait", "is-person", "columnIndex"])) : (o(), g(kt, {
                key: 4,
                text: n(e.item),
                "column-index": e.columnIndex,
                "force-show-expanded": _(l),
                "table-row": e.row,
                "table-data": e.tableData
            }, null, 8, ["text", "column-index", "force-show-expanded", "table-row", "table-data"]))], 2)))), 256))], 2))
        }
    }), [
        ["__scopeId", "data-v-275d7e20"]
    ]),
    kn = {
        class: "bottom-bar-container"
    },
    Cn = {
        class: "bottom-bar-inner"
    },
    In = {
        class: "buttons-container"
    },
    Rn = {
        class: "rows-counter"
    },
    Sn = x(a({
        __name: "BottomBar",
        props: {
            rowsCount: {}
        },
        setup(e) {
            const {
                t: t
            } = l(), n = e, a = ue(), r = De();

            function u() {
                a.setPrompt(t("components.sheets_agent.tools.continue_searching")), r.markInvisibleInMobile()
            }

            function c() {
                a.setPrompt(t("components.sheets_agent.cross_check_prompt")), r.markInvisibleInMobile()
            }

            function d() {
                a.setPrompt(t("components.sheets_agent.visualize_prompt")), r.markInvisibleInMobile()
            }
            return (e, a) => (o(), s("div", kn, [i("div", Cn, [i("div", In, [i("div", {
                class: "button",
                onClick: u
            }, [h(_(ae)), j(" " + v(_(t)("components.sheets_agent.tools.continue_searching")), 1)]), i("div", {
                class: "button",
                onClick: c
            }, [h(_(le)), j(" " + v(_(t)("components.sheets_agent.tools.fact_check_content")), 1)]), i("div", {
                class: "button",
                onClick: d
            }, [h(_(re)), j(" " + v(_(t)("components.sheets_agent.tools.visualize")), 1)])]), i("div", Rn, [h(N, {
                name: "counter"
            }, {
                default: w((() => [(o(), s("span", {
                    class: "counter",
                    key: n.rowsCount
                }, v(n.rowsCount ? ? 0), 1))])),
                _: 1
            }), j("  " + v(_(t)("components.sheets_agent.rows_counter")), 1)])])]))
        }
    }), [
        ["__scopeId", "data-v-682100e1"]
    ]),
    En = {
        class: "selection-operation-menu"
    },
    Ln = x(a({
        __name: "SelectionOperationMenu",
        props: {
            tableData: {},
            startingRowIndex: {},
            endingRowIndex: {},
            selectedColumnIndexList: {},
            onlyOneCell: {
                type: Boolean
            },
            displayedRows: {},
            projectId: {},
            sheetId: {}
        },
        emits: ["new-history-item"],
        setup(e, {
            emit: t
        }) {
            const n = V(),
                {
                    t: a
                } = l(),
                {
                    isAsking: u
                } = oe(),
                c = e,
                d = t,
                m = ue();

            function g(e) {
                return a("components.sheets_agent.describe_cell", {
                    column: e.columnName ? ? e.columnIndex + 1,
                    row: e.rowName ? ? e.rowIndex + 1,
                    cell: e.cellContent
                })
            }

            function w(e, t, n) {
                var s;
                return {
                    rowIndex: e,
                    columnIndex: t,
                    rowName: nt(c.displayedRows[e][0]),
                    columnName: null == (s = c.tableData.columns[t]) ? void 0 : s.label,
                    cellContent: nt(n)
                }
            }

            function y(e) {
                const t = [];
                for (let n = c.startingRowIndex; n <= c.endingRowIndex; n++) {
                    const s = c.displayedRows[n];
                    for (let i of c.selectedColumnIndexList) {
                        const o = s[i];
                        (null == o || "" === String(o).trim()) === e && t.push(w(n, i, o))
                    }
                }
                return t
            }
            const b = r([]),
                x = r([]),
                k = r(!1);
            S((() => {
                b.value = y(!0), x.value = y(!1), k.value = !1
            }));
            const C = () => {
                    if (0 === b.value.length) return;
                    const e = [a("components.sheets_agent.tools.continue_searching") + ":"].concat(b.value.map(g));
                    m.setPrompt(e.join("\n")), k.value = !0
                },
                I = () => {
                    if (0 === x.value.length) return;
                    const e = [a("components.sheets_agent.tools.fact_check_content") + ":"].concat(x.value.map(g));
                    m.setPrompt(e.join("\n")), k.value = !0
                },
                R = r(!1),
                E = r(""),
                L = async () => {
                    c.onlyOneCell && (u.value || (R.value = !0, E.value = nt(c.displayedRows[c.startingRowIndex][c.selectedColumnIndexList[0]] ? ? "")))
                },
                j = r(!1),
                M = async e => {
                    const t = w(c.startingRowIndex, c.selectedColumnIndexList[0], e);
                    j.value = !0;
                    try {
                        const n = await fetch("/api/sheets_agent/manual_update_cell", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify({
                                project_id: c.projectId,
                                sheet_id: c.sheetId,
                                row_index: t.rowIndex,
                                column_index: t.columnIndex,
                                value: e,
                                history_item_name: g(t)
                            })
                        });
                        if (!n.ok) throw new Error("Edit content response error: " + n.status + " " + n.statusText);
                        const s = await n.json();
                        if (!s.data || !s.data.sheets_agent) throw new Error("Edit content response has no data");
                        R.value = !1, d("new-history-item", s.data)
                    } catch (s) {
                        null == n || n.error(a("components.sheets_agent.tools.edit_content_error"))
                    } finally {
                        j.value = !1
                    }
                };
            return (e, t) => P((o(), s("div", En, [i("div", {
                class: f(["button", {
                    disabled: 0 === _(b).length
                }]),
                onClick: C
            }, v(e.$t("components.sheets_agent.tools.continue_searching")), 3), i("div", {
                class: f(["button", {
                    disabled: 0 === _(x).length
                }]),
                onClick: I
            }, v(e.$t("components.sheets_agent.tools.fact_check_content")), 3), e.onlyOneCell ? (o(), s("div", {
                key: 0,
                class: f(["button", {
                    disabled: _(u)
                }]),
                onClick: L
            }, v(e.$t("components.sheets_agent.tools.edit_content")), 3)) : p("", !0), h(ce, {
                modelValue: _(R),
                "onUpdate:modelValue": t[0] || (t[0] = e => F(R) ? R.value = e : null),
                mode: "input",
                title: _(a)("components.sheets_agent.tools.edit_content"),
                "initial-value": _(E),
                "confirm-button-text": _(a)("components.sheets_agent.tools.edit_content.confirm"),
                "cancel-button-text": _(a)("components.sheets_agent.tools.edit_content.cancel"),
                onInputConfirm: M,
                "allow-empty-value": !0,
                "confirm-loading": _(j)
            }, null, 8, ["modelValue", "title", "initial-value", "confirm-button-text", "cancel-button-text", "confirm-loading"])], 512)), [
                [q, !_(k)]
            ])
        }
    }), [
        ["__scopeId", "data-v-02a6504c"]
    ]);

function jn() {
    const e = z();
    return {
        isInPreviewPage: u((() => "/sheets" === e.path))
    }
}

function Mn(e, t, n) {
    const {
        isInPreviewPage: s
    } = jn(), i = () => !R.isMobile() && !s.value, o = r(-1), a = r(-1), l = r([]), u = r(!1), c = r({
        horizontalIsRight: !0,
        verticalIsDown: !0
    });
    S((() => {
        l.value = l.value.filter((t => t >= 0 && t < e.value.columns.length)), o.value = Math.min(o.value, e.value.rows.length - 1), a.value = Math.min(a.value, e.value.rows.length - 1)
    }));
    const d = r(!1);
    let p = {
            rowIndex: -1,
            columnIndex: -1
        },
        h = null,
        m = null;

    function v(e) {
        var t;
        0 === e.button && (t = e, c.value.horizontalIsRight = !0, c.value.verticalIsDown = !0, h && (l.value.length > 1 && t.clientX < h.clientX && (c.value.horizontalIsRight = !1), (0 === o.value && 0 === a.value || o.value < p.rowIndex) && (c.value.verticalIsDown = !1)), function() {
            if (d.value = !1, window.removeEventListener("mouseup", v), m) {
                const e = _();
                e && e.rowIndex === m.rowIndex && e.columnIndex === m.columnIndex && (o.value = -1, a.value = -1, l.value = [])
            }
        }())
    }

    function f(e) {
        e.rowIndex < p.rowIndex ? (o.value = e.rowIndex, a.value = p.rowIndex) : (o.value = p.rowIndex, a.value = e.rowIndex)
    }

    function g(t) {
        const n = [],
            s = Math.min(p.columnIndex, t.columnIndex),
            i = Math.max(p.columnIndex, t.columnIndex),
            o = e.value.columns[s],
            a = e.value.columns[i];
        if (o && a) {
            if (!!o.pinned == !!a.pinned) {
                const t = !!o.pinned;
                for (let o = s; o <= i; o++) {
                    const s = e.value.columns[o];
                    !!(null == s ? void 0 : s.pinned) === t && n.push(o)
                }
            } else {
                const t = o.pinned ? s : i,
                    a = o.pinned ? i : s,
                    l = e.value.columns.filter((e => e.pinned)).map((e => e.index)),
                    r = e.value.columns.filter((e => !e.pinned)).map((e => e.index));
                l.forEach((e => {
                    e >= t && n.push(e)
                })), r.forEach((e => {
                    e <= a && n.push(e)
                }))
            }
            l.value = n
        }
    }

    function _() {
        return -1 !== o.value && o.value === a.value && 1 === l.value.length ? {
            rowIndex: o.value,
            columnIndex: l.value[0]
        } : null
    }

    function w(t, n) {
        const s = e.value.columns[t],
            i = e.value.columns[n];
        return s && i ? s.pinned && !i.pinned ? n : !s.pinned && i.pinned ? t : Math.max(t, n) : t
    }

    function y(e, t) {
        return w(e, t) === e ? t : e
    }

    function b(e = !1) {
        const t = e ? y : w;
        return l.value.reduce(((e, n) => -1 === e ? n : e = t(e, n)), -1)
    }

    function x(t) {
        const n = e.value.columns.filter((e => e.pinned)).map((e => e.index)),
            s = e.value.columns.filter((e => !e.pinned)).map((e => e.index));
        return n.concat(s).indexOf(t)
    }

    function k(e, t) {
        const n = Array.from(e.children).filter((e => "TD" === e.tagName)).filter((e => !e.classList.contains("hidden")))[t + 1];
        return n || null
    }

    function C() {
        return c.value.horizontalIsRight ? function() {
            var e;
            const n = b(),
                s = null == (e = t.value) ? void 0 : e.querySelector(`tbody>tr:nth-child(${a.value+1})`);
            if (!s) return;
            const i = k(s, x(n));
            if (!i) return;
            const o = s.getBoundingClientRect(),
                l = i.getBoundingClientRect();
            let r = l.left - o.left + l.width;
            r + 142 > o.width && (r -= 142), L.value.left = r
        }() : function() {
            var e;
            const n = b(!0),
                s = null == (e = t.value) ? void 0 : e.querySelector(`tbody>tr:nth-child(${a.value+1})`);
            if (!s) return;
            const i = k(s, x(n));
            if (!i) return;
            const o = s.getBoundingClientRect();
            let l = i.getBoundingClientRect().left - o.left - 142;
            const r = function(e) {
                const t = e.querySelector("td:first-child");
                return t ? t.getBoundingClientRect().width : 0
            }(s);
            l < r && (l = r), L.value.left = l
        }()
    }

    function I() {
        return c.value.verticalIsDown, c.value.verticalIsDown ? function() {
            var e, n, s;
            const i = a.value,
                o = null == (e = t.value) ? void 0 : e.querySelector(`tbody>tr:nth-child(${i+1})`);
            if (!o) return;
            const l = o.getBoundingClientRect(),
                r = null == (n = t.value) ? void 0 : n.getBoundingClientRect();
            if (!r) return;
            const c = (null == (s = t.value) ? void 0 : s.scrollTop) ? ? 0;
            L.value.top = l.bottom - r.top - (u.value ? 110 : 72) + c
        }() : function() {
            var e, n, s;
            const i = o.value,
                a = null == (e = t.value) ? void 0 : e.querySelector(`tbody>tr:nth-child(${i+1})`);
            if (!a) return;
            const l = a.getBoundingClientRect(),
                r = null == (n = t.value) ? void 0 : n.getBoundingClientRect();
            if (!r) return;
            const u = (null == (s = t.value) ? void 0 : s.scrollTop) ? ? 0;
            L.value.top = l.top - r.top + u
        }()
    }
    const E = r([]);
    S((() => {
        E.value = l.value.sort(((e, t) => e - t))
    }));
    const L = r(null);
    return S((() => {
        L.value = null;
        const e = -1 !== o.value && -1 !== a.value && l.value.length > 0;
        u.value = o.value >= 0 && o.value === a.value && 1 === l.value.length, e && !d.value && (L.value = {
            top: 0,
            left: 0
        }, C(), I())
    })), {
        isSelecting: d,
        startingRowIndex: o,
        endingRowIndex: a,
        selectedColumnIndexList: l,
        sortedSelectedColumnIndexList: E,
        onlyOneCell: u,
        selectionOperationMenuPosition: L,
        onMouseDown: function(e, t, n) {
            0 === n.button && i() && (h = n, m = _(), d.value = !0, p = {
                rowIndex: e,
                columnIndex: t
            }, f(p), g(p), window.addEventListener("mouseup", v))
        },
        onMouseMove: function(e, t, n) {
            d.value && i() && (f({
                rowIndex: e
            }), g({
                columnIndex: t
            }))
        },
        isTableSelectionEnabled: i
    }
}
const Dn = {
        key: 0
    },
    On = {
        class: "sticky sticky-cell"
    },
    Tn = {
        class: "breathing-color"
    },
    An = x(a({
        __name: "TableDataComponent",
        props: {
            tableData: {
                type: Object,
                required: !0
            },
            sortByColumn: {
                type: Object,
                required: !1
            },
            readOnly: {
                type: Boolean,
                required: !0
            },
            projectId: {
                type: String,
                required: !1
            },
            sheetId: {
                type: String,
                required: !1
            }
        },
        emits: ["new-history-item"],
        setup(e, {
            emit: t
        }) {
            const {
                isInPreviewPage: n
            } = jn(), a = e, l = U(a, "tableData"), u = r({
                column: null,
                order: Ye.NONE
            });
            S((() => {
                if (!a.sortByColumn) return;
                if (l.value.noData()) return;
                const e = l.value.columns.find((e => e.id === a.sortByColumn.column));
                e && (u.value.column = e, u.value.order = a.sortByColumn.order)
            }));
            const m = (e, t) => {
                    u.value.column = e, u.value.order = t
                },
                y = (e, t) => {
                    var n;
                    null == (n = e.filterBy) || n.delete(t)
                },
                b = lt(function(e) {
                    const t = r([]);
                    return S((() => {
                        let n = e.value.rows;
                        for (const t of e.value.columns) n = "EnumList" === t.type ? nn(n, t) : tn(n, t);
                        t.value = n
                    })), t
                }(l), u),
                {
                    isAsking: x
                } = oe();
            c(x, (() => {
                x.value || (Dt().lastUpdatedCell.value = null)
            }));
            const C = r(null),
                I = r([]);
            let R = !1;
            S((() => {
                function e() {
                    let e = 37;
                    for (let t = 0; t < I.value.length; ++t) {
                        const n = I.value[t];
                        n.stickyLeft = e - t - 2, e += n.widthForSticky ? ? 0
                    }
                }
                I.value = l.value.columns.filter((e => e.pinned)), e(), T((() => {
                    const t = C.value;
                    if (!t) return;
                    const n = t.querySelector("table");
                    if (!n) return void(R || (R = !0));
                    const s = n.querySelectorAll("thead > tr > th");
                    for (let e = 0; e < I.value.length; ++e) {
                        const t = I.value[e],
                            n = s[e + 1];
                        if (!n) return;
                        t.widthForSticky = n.getBoundingClientRect().width
                    }
                    e()
                }))
            }));
            const L = r(0);

            function j(e) {
                return 1e4 - e
            }
            S((() => {
                L.value = b.value.length
            }));
            const M = Mn(l, C, a.readOnly),
                D = (e, t) => e >= M.startingRowIndex.value && e <= M.endingRowIndex.value && M.selectedColumnIndexList.value.includes(t),
                O = r(0),
                A = r(0);
            S((() => {
                var e, t, n;
                if (0 === M.sortedSelectedColumnIndexList.value.length) return;
                if (0 === I.value.length || M.sortedSelectedColumnIndexList.value.every((e => {
                        var t;
                        return !(null == (t = l.value.columns[e]) ? void 0 : t.pinned)
                    }))) O.value = M.sortedSelectedColumnIndexList.value[0], A.value = M.sortedSelectedColumnIndexList.value[M.sortedSelectedColumnIndexList.value.length - 1];
                else {
                    O.value = -1;
                    for (let t = 0; t < M.sortedSelectedColumnIndexList.value.length; t++) {
                        const n = M.sortedSelectedColumnIndexList.value[t];
                        if (null == (e = l.value.columns[n]) ? void 0 : e.pinned) {
                            O.value = n;
                            break
                        }
                    }
                    A.value = -1;
                    for (let e = M.sortedSelectedColumnIndexList.value.length - 1; e >= 0; e--) {
                        const n = M.sortedSelectedColumnIndexList.value[e];
                        if (!(null == (t = l.value.columns[n]) ? void 0 : t.pinned)) {
                            A.value = n;
                            break
                        }
                    }
                    if (-1 === A.value)
                        for (let e = I.value.length - 1; e >= 0; e--) {
                            const t = I.value[e].index;
                            if (null == (n = l.value.columns[t]) ? void 0 : n.pinned) {
                                A.value = t;
                                break
                            }
                        }
                }
            }));
            const B = (e, t) => e === M.startingRowIndex.value,
                F = (e, t) => e === M.endingRowIndex.value,
                z = (e, t) => t === O.value,
                $ = (e, t) => t === A.value,
                H = t,
                V = e => {
                    H("new-history-item", e)
                };
            return (t, a) => {
                var r, c;
                return o(), s("div", {
                    class: f(["dataset-table-container j-dataset-table-container", {
                        "table-selection-enabled": _(M).isTableSelectionEnabled()
                    }]),
                    ref_key: "refTableContainer",
                    ref: C
                }, [_(l).noData() ? p("", !0) : (o(), s("table", Dn, [i("thead", null, [i("tr", null, [i("th", {
                    class: "sticky sticky-corner",
                    style: k({
                        zIndex: j(0)
                    })
                }, null, 4), (o(!0), s(d, null, E(_(I), ((e, t) => (o(), g(hn, {
                    class: f(["sticky sticky-header", {
                        "last-sticky-in-line": t === _(I).length - 1
                    }]),
                    style: k({
                        left: (e.stickyLeft ? ? 0) + "px",
                        zIndex: j(-1)
                    }),
                    column: e,
                    "sort-by": _(u),
                    handleSorterUpdate: m,
                    handleFilterClose: y
                }, null, 8, ["class", "style", "column", "sort-by"])))), 256)), h(N, {
                    name: "header"
                }, {
                    default: w((() => [(o(!0), s(d, null, E(_(l).columns, (e => P((o(), g(hn, {
                        key: e.id,
                        class: "sticky sticky-header",
                        column: e,
                        "sort-by": _(u),
                        handleSorterUpdate: m,
                        handleFilterClose: y
                    }, null, 8, ["column", "sort-by"])), [
                        [q, !e.pinned]
                    ]))), 128))])),
                    _: 1
                })])]), h(N, {
                    name: "row",
                    tag: "tbody"
                }, {
                    default: w((() => [(o(!0), s(d, null, E(_(b), ((e, t) => (o(), s("tr", {
                        key: e.id
                    }, [i("td", On, v(t + 1), 1), (o(!0), s(d, null, E(_(I), ((n, s) => {
                        var i;
                        return o(), g(xn, {
                            key: null == (i = _(l).columns[n.index]) ? void 0 : i.id,
                            class: f(["sticky sticky-cell", {
                                "last-sticky-in-line": s === _(I).length - 1
                            }]),
                            style: k({
                                left: (n.stickyLeft ? ? 0) + "px",
                                zIndex: j(t)
                            }),
                            "table-data": _(l),
                            "column-index": n.index,
                            row: e,
                            item: e[n.index],
                            "is-selected": D(t, n.index),
                            "is-top-in-selection": B(t, n.index),
                            "is-bottom-in-selection": F(t, n.index),
                            "is-left-in-selection": z(0, n.index),
                            "is-right-in-selection": $(0, n.index),
                            onMousedown: e => _(M).onMouseDown(t, n.index, e),
                            onMousemove: e => _(M).onMouseMove(t, n.index, e)
                        }, null, 8, ["class", "style", "table-data", "column-index", "row", "item", "is-selected", "is-top-in-selection", "is-bottom-in-selection", "is-left-in-selection", "is-right-in-selection", "onMousedown", "onMousemove"])
                    })), 128)), (o(!0), s(d, null, E(e, ((n, s) => {
                        var i, a;
                        return o(), g(xn, {
                            key: null == (i = _(l).columns[s]) ? void 0 : i.id,
                            class: f({
                                hidden: null == (a = _(l).columns[s]) ? void 0 : a.pinned
                            }),
                            "table-data": _(l),
                            "column-index": s,
                            row: e,
                            item: n,
                            "is-selected": D(t, s),
                            "is-top-in-selection": B(t),
                            "is-bottom-in-selection": F(t),
                            "is-left-in-selection": z(0, s),
                            "is-right-in-selection": $(0, s),
                            onMousedown: e => _(M).onMouseDown(t, s, e),
                            onMousemove: e => _(M).onMouseMove(t, s, e)
                        }, null, 8, ["class", "table-data", "column-index", "row", "item", "is-selected", "is-top-in-selection", "is-bottom-in-selection", "is-left-in-selection", "is-right-in-selection", "onMousedown", "onMousemove"])
                    })), 128)), (o(!0), s(d, null, E(_(l).columns.length - e.length, (n => {
                        var s, i;
                        return o(), g(xn, {
                            key: null == (s = _(l).columns[e.length + n - 1]) ? void 0 : s.id,
                            class: f({
                                hidden: null == (i = _(l).columns[e.length + n - 1]) ? void 0 : i.pinned
                            }),
                            "table-data": _(l),
                            "column-index": e.length + n - 1,
                            row: e,
                            item: "",
                            "is-selected": D(t, e.length + n - 1),
                            "is-top-in-selection": B(t, e.length),
                            "is-bottom-in-selection": F(t, e.length),
                            "is-left-in-selection": z(0, e.length + n - 1),
                            "is-right-in-selection": $(0, e.length + n - 1),
                            onMousedown: s => _(M).onMouseDown(t, e.length + n - 1, s),
                            onMousemove: s => _(M).onMouseMove(t, e.length + n - 1, s)
                        }, null, 8, ["class", "table-data", "column-index", "row", "is-selected", "is-top-in-selection", "is-bottom-in-selection", "is-left-in-selection", "is-right-in-selection", "onMousedown", "onMousemove"])
                    })), 128))])))), 128))])),
                    _: 1
                }), _(x) ? (o(), s(d, {
                    key: 0
                }, E([
                    [],
                    [],
                    []
                ], ((e, t) => i("tr", Tn, [a[0] || (a[0] = i("td", {
                    class: "sticky sticky-cell breathing-cell"
                }, null, -1)), (o(!0), s(d, null, E(_(I), ((e, n) => {
                    var i;
                    return o(), s("td", {
                        key: null == (i = _(l).columns[e.index]) ? void 0 : i.id,
                        class: f(["sticky sticky-cell breathing-cell", {
                            "last-sticky-in-line": n === _(I).length - 1
                        }]),
                        style: k({
                            left: (e.stickyLeft ? ? 0) + "px",
                            zIndex: j(t + _(b).length)
                        })
                    }, null, 6)
                })), 128)), (o(!0), s(d, null, E(_(l).columns.length - e.length, (e => {
                    var t, n;
                    return o(), s("td", {
                        class: f(["breathing-cell", {
                            hidden: null == (t = _(l).columns[e - 1]) ? void 0 : t.pinned
                        }]),
                        key: null == (n = _(l).columns[e - 1]) ? void 0 : n.id
                    }, null, 2)
                })), 128))]))), 64)) : p("", !0)])), e.readOnly || _(l).noData() || _(x) || _(n) ? p("", !0) : (o(), g(Sn, {
                    key: 1,
                    class: "bottom-bar",
                    "rows-count": _(L)
                }, null, 8, ["rows-count"])), e.readOnly ? p("", !0) : P((o(), g(Ln, {
                    key: 2,
                    class: "selection-operation-menu",
                    style: k({
                        top: (null == (r = _(M).selectionOperationMenuPosition.value) ? void 0 : r.top) + "px",
                        left: (null == (c = _(M).selectionOperationMenuPosition.value) ? void 0 : c.left) + "px"
                    }),
                    "table-data": _(l),
                    "displayed-rows": _(b),
                    "starting-row-index": _(M).startingRowIndex.value,
                    "ending-row-index": _(M).endingRowIndex.value,
                    "selected-column-index-list": _(M).selectedColumnIndexList.value,
                    "only-one-cell": _(M).onlyOneCell.value,
                    "project-id": e.projectId,
                    "sheet-id": e.sheetId,
                    onNewHistoryItem: V
                }, null, 8, ["style", "table-data", "displayed-rows", "starting-row-index", "ending-row-index", "selected-column-index-list", "only-one-cell", "project-id", "sheet-id"])), [
                    [q, !!_(M).selectionOperationMenuPosition.value]
                ])], 2)
            }
        }
    }), [
        ["__scopeId", "data-v-36b68c71"]
    ]),
    Bn = a({
        __name: "TableSheet",
        props: {
            csvInfo: {
                type: Object,
                required: !0
            },
            readOnly: {
                type: Boolean,
                required: !0
            },
            projectId: {
                type: String,
                required: !1
            }
        },
        emits: ["new-history-item"],
        setup(e, {
            emit: t
        }) {
            const {
                t: n
            } = l(), s = V(), i = U(e, "csvInfo"), a = r(null);
            S((() => {
                i.value.order_by_table_column && (a.value = {
                    column: i.value.order_by_table_column,
                    order: "increase" === i.value.order ? Ye.ASCEND : Ye.DESCEND
                })
            }));
            const u = r({
                type: "none",
                id: ""
            });
            S((() => {
                i.value.delta && (i.value.delta.id && (i.value.delta.id, u.value.id), u.value = i.value.delta)
            }));
            const c = en(i, u, (e => {
                null == s || s.warning(n("components.sheets_agent.too_large_table", {
                    count: e
                }))
            }), (() => {
                null == s || s.error(n("components.sheets_agent.fetch_csv_error"), {
                    closable: !0,
                    duration: 36e5
                })
            }));
            i.value.tableData = c;
            const d = t,
                p = e => {
                    d("new-history-item", e)
                };
            return (t, n) => (o(), g(An, {
                "table-data": _(c),
                "sort-by-column": _(a),
                "read-only": e.readOnly,
                "sheet-id": _(i).key,
                "project-id": e.projectId,
                onNewHistoryItem: p
            }, null, 8, ["table-data", "sort-by-column", "read-only", "sheet-id", "project-id"]))
        }
    });
export {
    pe as A, Me as E, me as P, Le as S, Xe as T, Bn as _, fe as a, De as u
};