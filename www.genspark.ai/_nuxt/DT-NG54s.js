const e = (e, t = 800, a = .7) => new Promise(((o, n) => {
        const r = new FileReader;
        r.readAsDataURL(e), r.onload = e => {
            const r = new Image;
            r.src = e.target.result, r.onload = () => {
                const e = document.createElement("canvas");
                let n = r.width,
                    i = r.height;
                const c = Math.min(n, i),
                    s = t / c;
                c > t && (n = Math.floor(n * s), i = Math.floor(i * s)), e.width = n, e.height = i;
                e.getContext("2d").drawImage(r, 0, 0, n, i);
                const d = e.toDataURL("image/jpeg", a);
                o(d)
            }, r.onerror = e => {
                n(e)
            }
        }
    })),
    t = (e, t) => new Promise((a => {
        const o = e.split(","),
            n = o[0].match(/:(.*?);/)[1],
            r = atob(o[1]);
        let i = r.length;
        const c = new Uint8Array(i);
        for (; i--;) c[i] = r.charCodeAt(i);
        a(new File([c], t, {
            type: n
        }))
    })),
    a = async (e, t) => {
        try {
            const a = await fetch(e),
                o = await a.blob(),
                n = window.URL.createObjectURL(o),
                r = document.createElement("a");
            r.href = n, r.download = t || "genspark-image.jpg", document.body.appendChild(r), r.click(), document.body.removeChild(r), window.URL.revokeObjectURL(n)
        } catch (a) {}
    },
    o = () => /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
    n = e => !!e || (location.href = "/api/login?redirect_url=" + encodeURIComponent(location.pathname + location.search), !1),
    r = (e, t) => {
        e.contentWindow.postMessage(t, "*")
    },
    i = () => new URLSearchParams(window.location.search).get("id"),
    c = async e => {
        try {
            const t = await fetch(e),
                a = await t.blob(),
                o = new FileReader;
            return await new Promise((e => {
                o.onloadend = () => e(o.result), o.readAsDataURL(a)
            }))
        } catch (t) {
            try {
                const t = await fetch("/api/img_url_to_base64", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        url: e
                    })
                });
                return (await t.json()).base64
            } catch (a) {
                return ""
            }
        }
    };
export {
    a,
    i as b,
    e as c,
    t as d,
    c as g,
    o as i,
    r as p,
    n as r
};