import {
    L as t
} from "./Bzg9uoz_.js";
import {
    d as e,
    b as a,
    o,
    _ as i,
    R as r,
    r as s,
    i as n,
    f as l,
    q as c,
    n as u,
    t as d,
    p as m,
    F as g,
    E as p
} from "./Cf0SOiw0.js";
import {
    B as w
} from "./DAjjhrgi.js";
const h = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const v = {
        render: function(t, i) {
            return o(), e("svg", h, i[0] || (i[0] = [a("g", {
                "clip-path": "url(#clip0_3477_3021)"
            }, [a("path", {
                d: "M7.07031 17.1406L14.1414 10.0696L7.07031 2.99849",
                stroke: "white",
                "stroke-width": "2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            })], -1), a("defs", null, [a("clipPath", {
                id: "clip0_3477_3021"
            }, [a("rect", {
                width: "20",
                height: "20",
                fill: "white",
                transform: "matrix(-1 0 0 1 20 0)"
            })])], -1)]))
        }
    },
    b = {
        width: "10",
        height: "10",
        viewBox: "0 0 10 10",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const A = {
        render: function(t, i) {
            return o(), e("svg", b, i[0] || (i[0] = [a("path", {
                d: "M8.125 1.875H6.04167M8.125 1.875L5 5M8.125 1.875V3.95833",
                stroke: "#EFEFEF",
                "stroke-width": "0.6",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), a("path", {
                d: "M8.125 5.34722V7.43056C8.125 7.61473 8.05184 7.79137 7.9216 7.9216C7.79137 8.05184 7.61473 8.125 7.43056 8.125H2.56944C2.38527 8.125 2.20863 8.05184 2.0784 7.9216C1.94816 7.79137 1.875 7.61473 1.875 7.43056V2.56944C1.875 2.38527 1.94816 2.20863 2.0784 2.0784C2.20863 1.94816 2.38527 1.875 2.56944 1.875H4.65278",
                stroke: "#EFEFEF",
                "stroke-width": "0.6",
                "stroke-linecap": "round"
            }, null, -1)]))
        }
    },
    f = "data:image/png;base64,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";

function y(t, e = !1) {
    const a = t.match(/^.*(youtu.be\/|v\/|e\/|u\/\w+\/|embed\/|v=)([^#\&\?]*).*/);
    return a && 11 == a[2].length ? `https://www.youtube.com/embed/${a[2]}${e?"?autoplay=1":""}` : null
}

function O(t, e = "maxresdefault", a = "webp") {
    const o = t.match(/^.*(shorts\/)([^#\&\?]*).*/);
    if (o && o[2].length > 0) return `https://i.ytimg.com/vi/${o[2]}/frame0.jpg`;
    const i = t.match(/^.*(youtu.be\/|v\/|e\/|u\/\w+\/|embed\/|v=)([^#\&\?]*).*/);
    return i && 11 == i[2].length ? `https://i.ytimg.com/${"webp"==a?"vi_webp":"vi"}/${i[2]}/${e}.${a}` : null
}
const I = {
        name: "YoutubeWidget",
        components: {
            LeftArrowSvg: t,
            RightArrowSvg: v,
            NButton: w,
            LinkNewWindowSvg: A
        },
        props: {
            data: {
                type: Object
            },
            component_editor: {
                type: Object
            },
            editorTiptap: {
                type: Object
            },
            is_editing: {
                type: [Object, Boolean],
                default: !1
            },
            styleClass: {
                type: String
            },
            showDescription: {
                type: Boolean,
                default: !0
            },
            coverImgStyleClass: {
                type: String
            }
        },
        setup(t) {
            const e = r(t.data) ? t.data : s(t.data),
                a = s(JSON.stringify(e.value, null, 2)),
                o = s(null),
                i = () => {
                    t.component_editor.value.open(a.value, (i => {
                        try {
                            const t = JSON.parse(i);
                            e.value = t
                        } catch (l) {
                            return
                        } finally {
                            a.value = i
                        }
                        const r = t.editorTiptap.value.editor,
                            s = r.view,
                            n = s.posAtDOM(o.value.parentNode, 0);
                        if (n >= 0) {
                            const t = s.state.doc.resolve(n).nodeAfter,
                                a = r.state.tr.setNodeMarkup(n, null, {
                                    class: t.attrs.class,
                                    data: { ...t.attrs.data,
                                        ...e.value
                                    }
                                });
                            r.view.dispatch(a)
                        }
                    }))
                },
                l = n("mdExtExpose", null),
                c = s(!1);
            e.value.embedIframe && (c.value = e.value.embedIframe), l && l({
                edit: i
            });
            const u = s(!1);
            return {
                autoplay: u,
                imageclick: () => {
                    u.value = !0, c.value = !0
                },
                YoutubeIcon: f,
                convertToYoutubeImageUrl: O,
                is_editing: t.is_editing,
                convertToYouTubeEmbedURL: y,
                wrapper: o,
                windowopen: t => {
                    window.open(t)
                },
                getHost: t => new URL(t).host,
                data: e,
                edit: i,
                embedIframe: c
            }
        }
    },
    C = ["src"],
    N = ["src"],
    j = ["src"],
    k = {
        key: 0,
        class: "article"
    },
    Y = {
        key: 0,
        class: "description"
    },
    G = {
        class: "tip"
    };
const L = i(I, [
    ["render", function(t, i, r, s, n, w) {
        return p("n-button"), o(), e(g, null, [a("div", {
            class: u(["wrapper", s.data.styleClass]),
            ref: "wrapper"
        }, [a("div", {
            class: "iframe-container",
            style: c({
                "padding-top": s.embedIframe ? "56.25%" : "0"
            })
        }, [s.embedIframe ? (o(), e("iframe", {
            key: 0,
            width: "100%",
            src: s.convertToYouTubeEmbedURL(s.data.link, s.autoplay),
            frameborder: "0",
            allow: "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",
            allowfullscreen: ""
        }, null, 8, C)) : l("", !0), s.embedIframe ? l("", !0) : (o(), e("img", {
            key: 1,
            src: s.convertToYoutubeImageUrl(s.data.link),
            onLoad: i[0] || (i[0] = t => {
                t.target.naturalWidth < 150 && t.target.src.includes("maxresdefault") && (t.target.src = s.convertToYoutubeImageUrl(s.data.link, "sddefault")), t.target.naturalWidth < 150 && t.target.src.includes("sddefault") && (t.target.src = s.convertToYoutubeImageUrl(s.data.link, "hq720", "jpg")), t.target.naturalWidth < 150 && t.target.src.includes("hq720") && (t.target.src = s.convertToYoutubeImageUrl(s.data.link, "hqdefault", "jpg"))
            }),
            alt: "youtube",
            class: u(["youtube-image", r.coverImgStyleClass]),
            onClick: i[1] || (i[1] = (...t) => s.imageclick && s.imageclick(...t))
        }, null, 42, N)), s.embedIframe ? l("", !0) : (o(), e("img", {
            key: 2,
            src: s.YoutubeIcon,
            alt: "youtube",
            class: "youtube-icon",
            onClick: i[2] || (i[2] = (...t) => s.imageclick && s.imageclick(...t))
        }, null, 8, j))], 4), s.data.title || s.data.description ? (o(), e("div", k, [a("div", {
            class: "title",
            onClick: i[3] || (i[3] = t => s.windowopen(s.data.link))
        }, d(s.data.title), 1), r.showDescription ? (o(), e("div", Y, d(s.data.description), 1)) : l("", !0), a("div", G, [s.data.views && s.data.views > 0 ? (o(), e(g, {
            key: 0
        }, [m(d(t.$t("components.youtube.views-data-views", [s.data.views])), 1)], 64)) : l("", !0), m(d(s.data.published_date), 1)])])) : l("", !0)], 2), l("", !0)], 64)
    }],
    ["__scopeId", "data-v-2f15cd2e"]
]);
export {
    A as L, v as R, L as Y, f as a
};