var e = Object.defineProperty,
    t = (t, i, o) => ((t, i, o) => i in t ? e(t, i, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: o
    }) : t[i] = o)(t, "symbol" != typeof i ? i + "" : i, o);
import {
    _ as i
} from "./e-ES_T8J.js";
import {
    al as o,
    ak as a,
    aO as n,
    am as r,
    J as s,
    X as c,
    Y as l,
    Z as p,
    ec as d,
    K as m,
    aR as u,
    c as g,
    aq as h,
    aT as v,
    r as y,
    h as b,
    x as f,
    i as k,
    d as w,
    b as A,
    o as C,
    D as M,
    ay as x,
    _ as P,
    s as S,
    v as T,
    t as I,
    p as _,
    F as L,
    k as D,
    a as E,
    ai as F,
    C as O,
    f as B,
    y as j,
    n as z,
    l as R,
    H as G,
    L as q,
    cQ as N,
    q as U,
    aJ as W,
    e2 as V,
    e3 as H,
    U as Q,
    a3 as K,
    e as Z,
    aj as J,
    w as Y,
    S as X,
    R as $,
    a9 as ee,
    e0 as te,
    ax as ie,
    V as oe,
    j as ae,
    T as ne
} from "./Cf0SOiw0.js";
import {
    C as re
} from "./Bm_HbXT2.js";
import {
    U as se,
    E as ce,
    S as le,
    a as pe
} from "./BffV2qdL.js";
import {
    G as de,
    a as me,
    b as ue,
    N as ge,
    O as he,
    c as ve,
    M as ye,
    d as be,
    S as fe,
    e as ke,
    R as we
} from "./D0Ouax4K.js";
import {
    B as Ae
} from "./DnWGHCrg.js";
import {
    C as Ce
} from "./CQjXacSG.js";
import {
    I as Me
} from "./DqWfLcpp.js";
import {
    L as xe
} from "./CRIx66FB.js";
import {
    u as Pe
} from "./B6noBY_5.js";
import {
    P as Se,
    W as Te,
    F as Ie
} from "./CIlzw36e.js";
import {
    E as _e
} from "./D386eQgZ.js";
import {
    E as Le
} from "./D5ao1EUl.js";
import {
    u as De,
    S as Ee
} from "./B0db5Fvl.js";
import {
    S as Fe
} from "./C_XD2eP3.js";
import {
    C as Oe
} from "./BspXWmOn.js";
import {
    R as Be
} from "./Cu_n4xpI.js";
import {
    u as je,
    d as ze
} from "./BdlGQsae.js";
import {
    M as Re
} from "./Dc8Bac8D.js";
import {
    N as Ge
} from "./BJGgZAPd.js";
import {
    O as qe
} from "./Dd3cVSEE.js";
import {
    N as Ne
} from "./BjWUbj3w.js";
import {
    I as Ue,
    C as We
} from "./Chtxu0jj.js";
import {
    A as Ve
} from "./Cl89jLsD.js";
import {
    N as He
} from "./CW991W2w.js";
import {
    a as Qe
} from "./FCN43o2W.js";
import {
    f as Ke,
    i as Ze,
    d as Je
} from "./zH1ZpJ79.js";
import {
    B as Ye
} from "./CAfqOhBF.js";
import {
    F as Xe
} from "./LGmiBiLz.js";
import {
    i as $e
} from "./MpDLC7up.js";
import {
    a as et
} from "./B7VeW_-d.js";
import {
    N as tt
} from "./CaEkZ53E.js";
import {
    N as it
} from "./nuQnue4a.js";
import {
    u as ot
} from "./D5IxqnO4.js";
import {
    _ as at
} from "./C1lFdfgL.js";
import {
    r as nt
} from "./DT-NG54s.js";
import {
    N as rt
} from "./DQpEsQQa.js";
const st = o("breadcrumb", "\n white-space: nowrap;\n cursor: default;\n line-height: var(--n-item-line-height);\n", [a("ul", "\n list-style: none;\n padding: 0;\n margin: 0;\n "), a("a", "\n color: inherit;\n text-decoration: inherit;\n "), o("breadcrumb-item", "\n font-size: var(--n-font-size);\n transition: color .3s var(--n-bezier);\n display: inline-flex;\n align-items: center;\n ", [o("icon", "\n font-size: 18px;\n vertical-align: -.2em;\n transition: color .3s var(--n-bezier);\n color: var(--n-item-text-color);\n "), a("&:not(:last-child)", [r("clickable", [n("link", "\n cursor: pointer;\n ", [a("&:hover", "\n background-color: var(--n-item-color-hover);\n "), a("&:active", "\n background-color: var(--n-item-color-pressed); \n ")])])]), n("link", "\n padding: 4px;\n border-radius: var(--n-item-border-radius);\n transition:\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier);\n color: var(--n-item-text-color);\n position: relative;\n ", [a("&:hover", "\n color: var(--n-item-text-color-hover);\n ", [o("icon", "\n color: var(--n-item-text-color-hover);\n ")]), a("&:active", "\n color: var(--n-item-text-color-pressed);\n ", [o("icon", "\n color: var(--n-item-text-color-pressed);\n ")])]), n("separator", "\n margin: 0 8px;\n color: var(--n-separator-color);\n transition: color .3s var(--n-bezier);\n user-select: none;\n -webkit-user-select: none;\n "), a("&:last-child", [n("link", "\n font-weight: var(--n-font-weight-active);\n cursor: unset;\n color: var(--n-item-text-color-active);\n ", [o("icon", "\n color: var(--n-item-text-color-active);\n ")]), n("separator", "\n display: none;\n ")])])]),
    ct = v("n-breadcrumb"),
    lt = s({
        name: "Breadcrumb",
        props: Object.assign(Object.assign({}, p.props), {
            separator: {
                type: String,
                default: "/"
            }
        }),
        setup(e) {
            const {
                mergedClsPrefixRef: t,
                inlineThemeDisabled: i
            } = l(e), o = p("Breadcrumb", "-breadcrumb", st, d, e, t);
            m(ct, {
                separatorRef: u(e, "separator"),
                mergedClsPrefixRef: t
            });
            const a = g((() => {
                    const {
                        common: {
                            cubicBezierEaseInOut: e
                        },
                        self: {
                            separatorColor: t,
                            itemTextColor: i,
                            itemTextColorHover: a,
                            itemTextColorPressed: n,
                            itemTextColorActive: r,
                            fontSize: s,
                            fontWeightActive: c,
                            itemBorderRadius: l,
                            itemColorHover: p,
                            itemColorPressed: d,
                            itemLineHeight: m
                        }
                    } = o.value;
                    return {
                        "--n-font-size": s,
                        "--n-bezier": e,
                        "--n-item-text-color": i,
                        "--n-item-text-color-hover": a,
                        "--n-item-text-color-pressed": n,
                        "--n-item-text-color-active": r,
                        "--n-separator-color": t,
                        "--n-item-color-hover": p,
                        "--n-item-color-pressed": d,
                        "--n-item-border-radius": l,
                        "--n-font-weight-active": c,
                        "--n-item-line-height": m
                    }
                })),
                n = i ? h("breadcrumb", void 0, a, e) : void 0;
            return {
                mergedClsPrefix: t,
                cssVars: i ? void 0 : a,
                themeClass: null == n ? void 0 : n.themeClass,
                onRender: null == n ? void 0 : n.onRender
            }
        },
        render() {
            var e;
            return null === (e = this.onRender) || void 0 === e || e.call(this), c("nav", {
                class: [`${this.mergedClsPrefix}-breadcrumb`, this.themeClass],
                style: this.cssVars,
                "aria-label": "Breadcrumb"
            }, c("ul", null, this.$slots))
        }
    });
const pt = s({
        name: "BreadcrumbItem",
        props: {
            separator: String,
            href: String,
            clickable: {
                type: Boolean,
                default: !0
            },
            onClick: Function
        },
        slots: Object,
        setup(e, {
            slots: t
        }) {
            const i = k(ct, null);
            if (!i) return () => null;
            const {
                separatorRef: o,
                mergedClsPrefixRef: a
            } = i, n = function(e = ($e ? window : null)) {
                const t = () => {
                        const {
                            hash: t,
                            host: i,
                            hostname: o,
                            href: a,
                            origin: n,
                            pathname: r,
                            port: s,
                            protocol: c,
                            search: l
                        } = (null == e ? void 0 : e.location) || {};
                        return {
                            hash: t,
                            host: i,
                            hostname: o,
                            href: a,
                            origin: n,
                            pathname: r,
                            port: s,
                            protocol: c,
                            search: l
                        }
                    },
                    i = y(t()),
                    o = () => {
                        i.value = t()
                    };
                return b((() => {
                    e && (e.addEventListener("popstate", o), e.addEventListener("hashchange", o))
                })), f((() => {
                    e && (e.removeEventListener("popstate", o), e.removeEventListener("hashchange", o))
                })), i
            }(), r = g((() => e.href ? "a" : "span")), s = g((() => n.value.href === e.href ? "location" : null));
            return () => {
                const {
                    value: i
                } = a;
                return c("li", {
                    class: [`${i}-breadcrumb-item`, e.clickable && `${i}-breadcrumb-item--clickable`]
                }, c(r.value, {
                    class: `${i}-breadcrumb-item__link`,
                    "aria-current": s.value,
                    href: e.href,
                    onClick: e.onClick
                }, t), c("span", {
                    class: `${i}-breadcrumb-item__separator`,
                    "aria-hidden": "true"
                }, et(t.separator, (() => {
                    var t;
                    return [null !== (t = e.separator) && void 0 !== t ? t : o.value]
                }))))
            }
        }
    }),
    dt = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const mt = {
        render: function(e, t) {
            return C(), w("svg", dt, t[0] || (t[0] = [A("g", {
                id: "Frame"
            }, [A("path", {
                id: "Vector",
                d: "M8.3335 5.23777L8.3335 13.1743C8.3335 13.7034 8.43766 15.1584 10.0002 15.1584C11.5627 15.1584 11.6668 13.5711 11.6668 13.1743C11.6668 12.8568 11.6668 7.43684 11.6668 4.79134C11.6668 3.57606 11.0835 1.66634 8.75016 1.66634C6.41683 1.66634 5.8335 3.57606 5.8335 4.79134C5.8335 7.96594 5.8335 13.8489 5.8335 14.1663C5.8335 14.5632 5.8335 18.333 10.0002 18.333C13.3335 18.333 14.1668 15.4891 14.1668 14.1663L14.1668 5.23777",
                stroke: "currentColor",
                "stroke-width": "1.2"
            })], -1)]))
        }
    },
    ut = {
        width: "16",
        height: "16",
        viewBox: "0 0 16 16",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const gt = {
        render: function(e, t) {
            return C(), w("svg", ut, t[0] || (t[0] = [A("path", {
                d: "M9.80003 4.20113C9.67787 4.32575 9.60945 4.49329 9.60945 4.66779C9.60945 4.84229 9.67787 5.00984 9.80003 5.13446L10.8667 6.20113C10.9913 6.32328 11.1589 6.3917 11.3334 6.3917C11.5079 6.3917 11.6754 6.32328 11.8 6.20113L14.3134 3.68779C14.6486 4.42859 14.7501 5.25395 14.6043 6.05389C14.4586 6.85383 14.0725 7.59036 13.4975 8.16531C12.9226 8.74027 12.1861 9.12635 11.3861 9.2721C10.5862 9.41785 9.76082 9.31635 9.02003 8.98113L4.41336 13.5878C4.14814 13.853 3.78843 14.002 3.41336 14.002C3.03829 14.002 2.67858 13.853 2.41336 13.5878C2.14814 13.3226 1.99915 12.9629 1.99915 12.5878C1.99915 12.2127 2.14814 11.853 2.41336 11.5878L7.02003 6.98113C6.6848 6.24033 6.5833 5.41497 6.72905 4.61503C6.87481 3.81509 7.26088 3.07856 7.83584 2.50361C8.4108 1.92865 9.14732 1.54257 9.94726 1.39682C10.7472 1.25107 11.5726 1.35257 12.3134 1.68779L9.80669 4.19446L9.80003 4.20113Z",
                stroke: "currentColor",
                "stroke-width": "1.136",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    ht = {
        width: "15",
        height: "9",
        viewBox: "0 0 15 9",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const vt = {
        render: function(e, t) {
            return C(), w("svg", ht, t[0] || (t[0] = [A("path", {
                d: "M7.25008 0.749923C7.65693 0.343073 8.32234 0.362628 8.70459 0.792667L14.5206 7.33564C15.0938 7.98054 14.636 9 13.7732 9L1.41421 9C0.523308 9 0.0771421 7.92286 0.707107 7.29289L7.25008 0.749923Z",
                fill: "#ECECEC"
            }, null, -1)]))
        }
    },
    yt = {
        width: "48px",
        height: "66px",
        viewBox: "0 0 48 66",
        version: "1.1",
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink"
    };
const bt = {
        render: function(e, t) {
            return C(), w("svg", yt, t[0] || (t[0] = [M('<title>Slides-icon</title><desc>Created with Sketch.</desc><defs><path d="M29.5833333,0 L4.4375,0 C1.996875,0 0,1.996875 0,4.4375 L0,60.6458333 C0,63.0864583 1.996875,65.0833333 4.4375,65.0833333 L42.8958333,65.0833333 C45.3364583,65.0833333 47.3333333,63.0864583 47.3333333,60.6458333 L47.3333333,17.75 L29.5833333,0 Z" id="path-1"></path><path d="M29.5833333,0 L4.4375,0 C1.996875,0 0,1.996875 0,4.4375 L0,60.6458333 C0,63.0864583 1.996875,65.0833333 4.4375,65.0833333 L42.8958333,65.0833333 C45.3364583,65.0833333 47.3333333,63.0864583 47.3333333,60.6458333 L47.3333333,17.75 L29.5833333,0 Z" id="path-3"></path><path d="M29.5833333,0 L4.4375,0 C1.996875,0 0,1.996875 0,4.4375 L0,60.6458333 C0,63.0864583 1.996875,65.0833333 4.4375,65.0833333 L42.8958333,65.0833333 C45.3364583,65.0833333 47.3333333,63.0864583 47.3333333,60.6458333 L47.3333333,17.75 L29.5833333,0 Z" id="path-5"></path><linearGradient x1="50.0053945%" y1="8.58610612%" x2="50.0053945%" y2="100.013939%" id="linearGradient-7"><stop stop-color="#BF360C" stop-opacity="0.2" offset="0%"></stop><stop stop-color="#BF360C" stop-opacity="0.02" offset="100%"></stop></linearGradient><path d="M29.5833333,0 L4.4375,0 C1.996875,0 0,1.996875 0,4.4375 L0,60.6458333 C0,63.0864583 1.996875,65.0833333 4.4375,65.0833333 L42.8958333,65.0833333 C45.3364583,65.0833333 47.3333333,63.0864583 47.3333333,60.6458333 L47.3333333,17.75 L29.5833333,0 Z" id="path-8"></path><path d="M29.5833333,0 L4.4375,0 C1.996875,0 0,1.996875 0,4.4375 L0,60.6458333 C0,63.0864583 1.996875,65.0833333 4.4375,65.0833333 L42.8958333,65.0833333 C45.3364583,65.0833333 47.3333333,63.0864583 47.3333333,60.6458333 L47.3333333,17.75 L29.5833333,0 Z" id="path-10"></path><path d="M29.5833333,0 L4.4375,0 C1.996875,0 0,1.996875 0,4.4375 L0,60.6458333 C0,63.0864583 1.996875,65.0833333 4.4375,65.0833333 L42.8958333,65.0833333 C45.3364583,65.0833333 47.3333333,63.0864583 47.3333333,60.6458333 L47.3333333,17.75 L29.5833333,0 Z" id="path-12"></path><path d="M29.5833333,0 L4.4375,0 C1.996875,0 0,1.996875 0,4.4375 L0,60.6458333 C0,63.0864583 1.996875,65.0833333 4.4375,65.0833333 L42.8958333,65.0833333 C45.3364583,65.0833333 47.3333333,63.0864583 47.3333333,60.6458333 L47.3333333,17.75 L29.5833333,0 Z" id="path-14"></path><path d="M29.5833333,0 L4.4375,0 C1.996875,0 0,1.996875 0,4.4375 L0,60.6458333 C0,63.0864583 1.996875,65.0833333 4.4375,65.0833333 L42.8958333,65.0833333 C45.3364583,65.0833333 47.3333333,63.0864583 47.3333333,60.6458333 L47.3333333,17.75 L29.5833333,0 Z" id="path-16"></path><radialGradient cx="3.16804688%" cy="2.71744318%" fx="3.16804688%" fy="2.71744318%" r="161.248516%" gradientTransform="translate(0.031680,0.027174),scale(1.000000,0.727273),translate(-0.031680,-0.027174)" id="radialGradient-18"><stop stop-color="#FFFFFF" stop-opacity="0.1" offset="0%"></stop><stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop></radialGradient></defs><g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Consumer-Apps-Slides-Large-VD-R8" transform="translate(-449.000000, -452.000000)"><g id="Hero" transform="translate(0.000000, 63.000000)"><g id="Personal" transform="translate(277.000000, 299.000000)"><g id="Slides-icon" transform="translate(172.000000, 90.000000)"><g id="Group"><g id="Clipped"><mask id="mask-2" fill="white"><use xlink:href="#path-1"></use></mask><g id="SVGID_1_"></g><path d="M29.5833333,0 L4.4375,0 C1.996875,0 0,1.996875 0,4.4375 L0,60.6458333 C0,63.0864583 1.996875,65.0833333 4.4375,65.0833333 L42.8958333,65.0833333 C45.3364583,65.0833333 47.3333333,63.0864583 47.3333333,60.6458333 L47.3333333,17.75 L36.9791667,10.3541667 L29.5833333,0 Z" id="Path" fill="#F4B400" fill-rule="nonzero" mask="url(#mask-2)"></path></g><g id="Clipped"><mask id="mask-4" fill="white"><use xlink:href="#path-3"></use></mask><g id="SVGID_1_"></g><path d="M33.28125,29.5833333 L14.0520833,29.5833333 C12.8317708,29.5833333 11.8333333,30.5817708 11.8333333,31.8020833 L11.8333333,51.03125 C11.8333333,52.2515625 12.8317708,53.25 14.0520833,53.25 L33.28125,53.25 C34.5015625,53.25 35.5,52.2515625 35.5,51.03125 L35.5,31.8020833 C35.5,30.5817708 34.5015625,29.5833333 33.28125,29.5833333 Z M32.5416667,46.59375 L14.7916667,46.59375 L14.7916667,36.2395833 L32.5416667,36.2395833 L32.5416667,46.59375 Z" id="Shape" fill="#F1F1F1" fill-rule="nonzero" mask="url(#mask-4)"></path></g><g id="Clipped"><mask id="mask-6" fill="white"><use xlink:href="#path-5"></use></mask><g id="SVGID_1_"></g><polygon id="Path" fill="url(#linearGradient-7)" fill-rule="nonzero" mask="url(#mask-6)" points="30.8813021 16.4520313 47.3333333 32.9003646 47.3333333 17.75"></polygon></g><g id="Clipped"><mask id="mask-9" fill="white"><use xlink:href="#path-8"></use></mask><g id="SVGID_1_"></g><g id="Group" mask="url(#mask-9)"><g transform="translate(26.625000, -2.958333)"><path d="M2.95833333,2.95833333 L2.95833333,16.2708333 C2.95833333,18.7225521 4.94411458,20.7083333 7.39583333,20.7083333 L20.7083333,20.7083333 L2.95833333,2.95833333 Z" id="Path" fill="#FADA80" fill-rule="nonzero"></path></g></g></g><g id="Clipped"><mask id="mask-11" fill="white"><use xlink:href="#path-10"></use></mask><g id="SVGID_1_"></g><polygon id="Path" fill-opacity="0.1" fill="#FFFFFF" fill-rule="nonzero" mask="url(#mask-11)" points="29.5833333 0 29.5833333 0.369791667 46.9635417 17.75 47.3333333 17.75"></polygon></g><g id="Clipped"><mask id="mask-13" fill="white"><use xlink:href="#path-12"></use></mask><g id="SVGID_1_"></g><path d="M4.4375,0 C1.996875,0 0,1.996875 0,4.4375 L0,4.80729167 C0,2.36666667 1.996875,0.369791667 4.4375,0.369791667 L29.5833333,0.369791667 L29.5833333,0 L4.4375,0 Z" id="Path" fill-opacity="0.2" fill="#FFFFFF" fill-rule="nonzero" mask="url(#mask-13)"></path></g><g id="Clipped"><mask id="mask-15" fill="white"><use xlink:href="#path-14"></use></mask><g id="SVGID_1_"></g><path d="M42.8958333,64.7135417 L4.4375,64.7135417 C1.996875,64.7135417 0,62.7166667 0,60.2760417 L0,60.6458333 C0,63.0864583 1.996875,65.0833333 4.4375,65.0833333 L42.8958333,65.0833333 C45.3364583,65.0833333 47.3333333,63.0864583 47.3333333,60.6458333 L47.3333333,60.2760417 C47.3333333,62.7166667 45.3364583,64.7135417 42.8958333,64.7135417 Z" id="Path" fill-opacity="0.2" fill="#BF360C" fill-rule="nonzero" mask="url(#mask-15)"></path></g><g id="Clipped"><mask id="mask-17" fill="white"><use xlink:href="#path-16"></use></mask><g id="SVGID_1_"></g><path d="M34.0208333,17.75 C31.5691146,17.75 29.5833333,15.7642188 29.5833333,13.3125 L29.5833333,13.6822917 C29.5833333,16.1340104 31.5691146,18.1197917 34.0208333,18.1197917 L47.3333333,18.1197917 L47.3333333,17.75 L34.0208333,17.75 Z" id="Path" fill-opacity="0.1" fill="#BF360C" fill-rule="nonzero" mask="url(#mask-17)"></path></g></g><path d="M29.5833333,0 L4.4375,0 C1.996875,0 0,1.996875 0,4.4375 L0,60.6458333 C0,63.0864583 1.996875,65.0833333 4.4375,65.0833333 L42.8958333,65.0833333 C45.3364583,65.0833333 47.3333333,63.0864583 47.3333333,60.6458333 L47.3333333,17.75 L29.5833333,0 Z" id="Path" fill="url(#radialGradient-18)" fill-rule="nonzero"></path></g></g></g></g></g>', 4)]))
        }
    },
    ft = {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 300 100"
    };
const kt = {
        render: function(e, t) {
            return C(), w("svg", ft, t[0] || (t[0] = [M('<g transform="translate(-40, 20) scale(0.7 0.7)"><svg xmlns="http://www.w3.org/2000/svg" viewBox="52 42 88 66"><path fill="#4285f4" d="M58 108h14V74L52 59v43c0 3.32 2.69 6 6 6"></path><path fill="#34a853" d="M120 108h14c3.32 0 6-2.69 6-6V59l-20 15"></path><path fill="#fbbc04" d="M120 48v26l20-15v-8c0-7.42-8.47-11.65-14.4-7.2"></path><path fill="#ea4335" d="M72 74V48l24 18 24-18v26L96 92"></path><path fill="#c5221f" d="M52 51v8l20 15V48l-5.6-4.2c-5.94-4.45-14.4-.22-14.4 7.2"></path></svg></g><g transform="translate(60, 20) scale(0.7 0.7)"><svg viewBox="0 0 87.3 78" xmlns="http://www.w3.org/2000/svg"><path d="m6.6 66.85 3.85 6.65c.8 1.4 1.95 2.5 3.3 3.3l13.75-23.8h-27.5c0 1.55.4 3.1 1.2 4.5z" fill="#0066da"></path><path d="m43.65 25-13.75-23.8c-1.35.8-2.5 1.9-3.3 3.3l-25.4 44a9.06 9.06 0 0 0 -1.2 4.5h27.5z" fill="#00ac47"></path><path d="m73.55 76.8c1.35-.8 2.5-1.9 3.3-3.3l1.6-2.75 7.65-13.25c.8-1.4 1.2-2.95 1.2-4.5h-27.502l5.852 11.5z" fill="#ea4335"></path><path d="m43.65 25 13.75-23.8c-1.35-.8-2.9-1.2-4.5-1.2h-18.5c-1.6 0-3.15.45-4.5 1.2z" fill="#00832d"></path><path d="m59.8 53h-32.3l-13.75 23.8c1.35.8 2.9 1.2 4.5 1.2h50.8c1.6 0 3.15-.45 4.5-1.2z" fill="#2684fc"></path><path d="m73.4 26.5-12.7-22c-.8-1.4-1.95-2.5-3.3-3.3l-13.75 23.8 16.15 28h27.45c0-1.55-.4-3.1-1.2-4.5z" fill="#ffba00"></path></svg></g><g transform="translate(160, 20) scale(0.7 0.7)"><svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 200 200"><g><g transform="translate(3.75 3.75)"><path fill="#FFFFFF" d="M148.882,43.618l-47.368-5.263l-57.895,5.263L38.355,96.25l5.263,52.632l52.632,6.579l52.632-6.579\n            l5.263-53.947L148.882,43.618z"></path><path fill="#1A73E8" d="M65.211,125.276c-3.934-2.658-6.658-6.539-8.145-11.671l9.132-3.763c0.829,3.158,2.276,5.605,4.342,7.342\n            c2.053,1.737,4.553,2.592,7.474,2.592c2.987,0,5.553-0.908,7.697-2.724s3.224-4.132,3.224-6.934c0-2.868-1.132-5.211-3.395-7.026\n            s-5.105-2.724-8.5-2.724h-5.276v-9.039H76.5c2.921,0,5.382-0.789,7.382-2.368c2-1.579,3-3.737,3-6.487\n            c0-2.447-0.895-4.395-2.684-5.855s-4.053-2.197-6.803-2.197c-2.684,0-4.816,0.711-6.395,2.145s-2.724,3.197-3.447,5.276\n            l-9.039-3.763c1.197-3.395,3.395-6.395,6.618-8.987c3.224-2.592,7.342-3.895,12.342-3.895c3.697,0,7.026,0.711,9.974,2.145\n            c2.947,1.434,5.263,3.421,6.934,5.947c1.671,2.539,2.5,5.382,2.5,8.539c0,3.224-0.776,5.947-2.329,8.184\n            c-1.553,2.237-3.461,3.947-5.724,5.145v0.539c2.987,1.25,5.421,3.158,7.342,5.724c1.908,2.566,2.868,5.632,2.868,9.211\n            s-0.908,6.776-2.724,9.579c-1.816,2.803-4.329,5.013-7.513,6.618c-3.197,1.605-6.789,2.421-10.776,2.421\n            C73.408,129.263,69.145,127.934,65.211,125.276z"></path><path fill="#1A73E8" d="M121.25,79.961l-9.974,7.25l-5.013-7.605l17.987-12.974h6.895v61.197h-9.895L121.25,79.961z"></path><path fill="#EA4335" d="M148.882,196.25l47.368-47.368l-23.684-10.526l-23.684,10.526l-10.526,23.684L148.882,196.25z"></path><path fill="#34A853" d="M33.092,172.566l10.526,23.684h105.263v-47.368H43.618L33.092,172.566z"></path><path fill="#4285F4" d="M12.039-3.75C3.316-3.75-3.75,3.316-3.75,12.039v136.842l23.684,10.526l23.684-10.526V43.618h105.263\n            l10.526-23.684L148.882-3.75H12.039z"></path><path fill="#188038" d="M-3.75,148.882v31.579c0,8.724,7.066,15.789,15.789,15.789h31.579v-47.368H-3.75z"></path><path fill="#FBBC04" d="M148.882,43.618v105.263h47.368V43.618l-23.684-10.526L148.882,43.618z"></path><path fill="#1967D2" d="M196.25,43.618V12.039c0-8.724-7.066-15.789-15.789-15.789h-31.579v47.368H196.25z"></path></g></g></svg></g>', 3)]))
        }
    },
    wt = {
        xmlns: "http://www.w3.org/2000/svg",
        id: "image-2",
        fill: "none",
        viewBox: "4 2 40 44"
    };
const At = {
        render: function(e, t) {
            return C(), w("svg", wt, t[0] || (t[0] = [M('<title>Microsoft 365 logo (2022)</title><path d="M20.0842 3.02588L19.8595 3.16179C19.5021 3.37799 19.1654 3.61972 18.8512 3.88385L19.4993 3.42798H25L26 11L21 16L16 19.4754V23.4829C16 26.2819 17.4629 28.8774 19.8574 30.3268L25.1211 33.5129L14 40.0002H11.8551L7.85737 37.5804C5.46286 36.131 4 33.5355 4 30.7365V17.2606C4 14.4607 5.46379 11.8645 7.85952 10.4154L19.8595 3.15687C19.9339 3.11189 20.0088 3.06823 20.0842 3.02588Z" fill="url(#paint0_radial_2994_8373)"></path><path d="M20.0842 3.02588L19.8595 3.16179C19.5021 3.37799 19.1654 3.61972 18.8512 3.88385L19.4993 3.42798H25L26 11L21 16L16 19.4754V23.4829C16 26.2819 17.4629 28.8774 19.8574 30.3268L25.1211 33.5129L14 40.0002H11.8551L7.85737 37.5804C5.46286 36.131 4 33.5355 4 30.7365V17.2606C4 14.4607 5.46379 11.8645 7.85952 10.4154L19.8595 3.15687C19.9339 3.11189 20.0088 3.06823 20.0842 3.02588Z" fill="url(#paint1_linear_2994_8373)"></path><path d="M32 19V23.4803C32 26.2793 30.5371 28.8748 28.1426 30.3242L16.1426 37.5878C13.6878 39.0737 10.6335 39.1273 8.1355 37.7487L19.8573 44.844C22.4039 46.3855 25.5959 46.3855 28.1426 44.844L40.1426 37.5803C42.5371 36.1309 43.9999 33.5354 43.9999 30.7364V27.5L42.9999 26L32 19Z" fill="url(#paint2_radial_2994_8373)"></path><path d="M32 19V23.4803C32 26.2793 30.5371 28.8748 28.1426 30.3242L16.1426 37.5878C13.6878 39.0737 10.6335 39.1273 8.1355 37.7487L19.8573 44.844C22.4039 46.3855 25.5959 46.3855 28.1426 44.844L40.1426 37.5803C42.5371 36.1309 43.9999 33.5354 43.9999 30.7364V27.5L42.9999 26L32 19Z" fill="url(#paint3_linear_2994_8373)"></path><path d="M40.1405 10.4153L28.1405 3.15678C25.6738 1.66471 22.6021 1.61849 20.0979 3.01811L19.8595 3.16231C17.4638 4.61143 16 7.20757 16 10.0075V19.4914L19.8595 17.1568C22.4051 15.6171 25.5949 15.6171 28.1405 17.1568L40.1405 24.4153C42.4613 25.8192 43.9076 28.2994 43.9957 30.9985C43.9986 30.9113 44 30.824 44 30.7364V17.2605C44 14.4606 42.5362 11.8644 40.1405 10.4153Z" fill="url(#paint4_radial_2994_8373)"></path><path d="M40.1405 10.4153L28.1405 3.15678C25.6738 1.66471 22.6021 1.61849 20.0979 3.01811L19.8595 3.16231C17.4638 4.61143 16 7.20757 16 10.0075V19.4914L19.8595 17.1568C22.4051 15.6171 25.5949 15.6171 28.1405 17.1568L40.1405 24.4153C42.4613 25.8192 43.9076 28.2994 43.9957 30.9985C43.9986 30.9113 44 30.824 44 30.7364V17.2605C44 14.4606 42.5362 11.8644 40.1405 10.4153Z" fill="url(#paint5_linear_2994_8373)"></path><path d="M4.00428 30.9984C4.00428 30.9984 4.00428 30.9984 4.00428 30.9984Z" fill="url(#paint6_radial_2994_8373)"></path><path d="M4.00428 30.9984C4.00428 30.9984 4.00428 30.9984 4.00428 30.9984Z" fill="url(#paint7_linear_2994_8373)"></path><defs><radialGradient id="paint0_radial_2994_8373" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(17.4186 10.6383) rotate(110.528) scale(33.3657 58.1966)"><stop offset="0.06441" stop-color="#AE7FE2"></stop><stop offset="1" stop-color="#0078D4"></stop></radialGradient><linearGradient id="paint1_linear_2994_8373" x1="17.5119" y1="37.8685" x2="12.7513" y2="29.6347" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#114A8B"></stop><stop offset="1" stop-color="#0078D4" stop-opacity="0"></stop></linearGradient><radialGradient id="paint2_radial_2994_8373" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.4299 36.3511) rotate(-8.36717) scale(31.0503 20.5108)"><stop offset="0.133928" stop-color="#D59DFF"></stop><stop offset="1" stop-color="#5E438F"></stop></radialGradient><linearGradient id="paint3_linear_2994_8373" x1="40.3566" y1="25.3768" x2="35.2552" y2="32.6916" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#493474"></stop><stop offset="1" stop-color="#8C66BA" stop-opacity="0"></stop></linearGradient><radialGradient id="paint4_radial_2994_8373" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(41.0552 26.504) rotate(-165.772) scale(24.9228 41.9552)"><stop offset="0.0584996" stop-color="#50E6FF"></stop><stop offset="1" stop-color="#436DCD"></stop></radialGradient><linearGradient id="paint5_linear_2994_8373" x1="16.9758" y1="3.05655" x2="24.4868" y2="3.05655" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2D3F80"></stop><stop offset="1" stop-color="#436DCD" stop-opacity="0"></stop></linearGradient><radialGradient id="paint6_radial_2994_8373" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(41.0552 26.504) rotate(-165.772) scale(24.9228 41.9552)"><stop offset="0.0584996" stop-color="#50E6FF"></stop><stop offset="1" stop-color="#436DCD"></stop></radialGradient><linearGradient id="paint7_linear_2994_8373" x1="16.9758" y1="3.05655" x2="24.4868" y2="3.05655" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2D3F80"></stop><stop offset="1" stop-color="#436DCD" stop-opacity="0"></stop></linearGradient></defs>', 10)]))
        }
    },
    Ct = {
        width: "800px",
        height: "800px",
        viewBox: "0 -4 48 48",
        version: "1.1",
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink"
    };
const Mt = {
        render: function(e, t) {
            return C(), w("svg", Ct, t[0] || (t[0] = [A("title", null, "Reddit-color", -1), A("desc", null, "Created with Sketch.", -1), A("defs", null, null, -1), A("g", {
                id: "Icons",
                stroke: "none",
                "stroke-width": "1",
                fill: "none",
                "fill-rule": "evenodd"
            }, [A("g", {
                id: "Color-",
                transform: "translate(-800.000000, -566.000000)",
                fill: "#FF5700"
            }, [A("path", {
                d: "M831.14,592.325803 C829.346,592.325803 827.8385,590.884067 827.8385,589.106421 C827.8385,587.328775 829.346,585.839477 831.14,585.839477 C832.934,585.839477 834.389,587.328775 834.389,589.106421 C834.389,590.884067 832.934,592.325803 831.14,592.325803 M831.902,598.574316 C830.231,600.228597 827.654,601.032699 824.024,601.032699 C824.0165,601.032699 824.0075,601.031213 823.9985,601.031213 C823.991,601.031213 823.982,601.032699 823.973,601.032699 C820.343,601.032699 817.7675,600.228597 816.098,598.574316 C815.585,598.065993 815.585,597.244055 816.098,596.737218 C816.6095,596.23038 817.439,596.23038 817.952,596.737218 C819.104,597.878716 821.0735,598.434602 823.973,598.434602 C823.982,598.434602 823.991,598.436088 823.9985,598.436088 C824.0075,598.436088 824.0165,598.434602 824.024,598.434602 C826.9235,598.434602 828.8945,597.878716 830.048,596.737218 C830.561,596.228894 831.3905,596.23038 831.902,596.737218 C832.4135,597.245541 832.4135,598.067479 831.902,598.574316 M813.611,589.106421 C813.611,587.330262 815.1155,585.839477 816.908,585.839477 C818.702,585.839477 820.157,587.330262 820.157,589.106421 C820.157,590.884067 818.702,592.325803 816.908,592.325803 C815.1155,592.325803 813.611,590.884067 813.611,589.106421 M839.996,568.598098 C841.211,568.598098 842.1995,569.577586 842.1995,570.780024 C842.1995,571.983948 841.211,572.963436 839.996,572.963436 C838.781,572.963436 837.7925,571.983948 837.7925,570.780024 C837.7925,569.577586 838.781,568.598098 839.996,568.598098 M848,585.570452 C848,582.417955 845.4125,579.854043 842.231,579.854043 C840.854,579.854043 839.5895,580.335612 838.5965,581.136742 C835.079,578.945898 830.615,577.62604 825.83,577.346611 L828.326,569.527051 L835.1855,571.127824 C835.3655,573.602556 837.4535,575.561534 839.996,575.561534 C842.6555,575.561534 844.82,573.416766 844.82,570.780024 C844.82,568.144768 842.6555,566 839.996,566 C838.136,566 836.519,567.049346 835.7135,568.581748 L827.7425,566.722354 C827.075,566.56629 826.4,566.94679 826.193,567.594828 L823.094,577.300535 C817.9385,577.425386 813.092,578.749703 809.3165,581.068371 C808.337,580.308859 807.1055,579.854043 805.769,579.854043 C802.5875,579.854043 800,582.417955 800,585.570452 C800,587.519025 800.99,589.241677 802.4975,590.273187 C802.4345,590.726516 802.4015,591.182818 802.4015,591.645065 C802.4015,595.585315 804.713,599.250595 808.91,601.964625 C812.933,604.567182 818.258,606 823.9025,606 C829.547,606 834.872,604.567182 838.895,601.964625 C843.092,599.250595 845.4035,595.585315 845.4035,591.645065 C845.4035,591.224435 845.375,590.806778 845.3225,590.392093 C846.9305,589.376932 848,587.594828 848,585.570452",
                id: "Reddit"
            })])], -1)]))
        }
    },
    xt = "" + new URL("deepwiki_icon.BiOEdBz-.png",
        import.meta.url).href,
    Pt = {
        width: "800px",
        height: "800px",
        viewBox: "0 0 48 48",
        version: "1.1",
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink"
    };
const St = {
        render: function(e, t) {
            return C(), w("svg", Pt, t[0] || (t[0] = [M('<title>HackerNews-color</title><desc>Created with Sketch.</desc><defs></defs><g id="Icons" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Color-" transform="translate(-800.000000, -566.000000)" fill="#FF6600"><rect x="800" y="566" width="48" height="48" rx="3"></rect><g transform="translate(820, 581)" fill="#FFFFFF"><path d="M12,0 L8,0 L4,8 L0,0 L-4,0 L2,12 L2,18 L6,18 L6,12 L12,0 Z"></path></g></g></g>', 4)]))
        }
    },
    Tt = e => Ce.MD5(e).toString(),
    It = "google_suite",
    _t = "gmail",
    Lt = "google_calendar",
    Dt = "google_drive",
    Et = "google_slides",
    Ft = "notion",
    Ot = "microsoft_365",
    Bt = "outlook_email",
    jt = "outlook_calendar",
    zt = "microsoft_onedrive",
    Rt = "microsoft_sharepoint",
    Gt = "microsoft_teams",
    qt = "slack",
    Nt = e => [{
        id: 0,
        name: It,
        title: "Google Suite",
        icon: x(kt),
        description: e ? e("components.custom_tools.config.google_suite.description") : "Access your Google Workspace Suite, including Gmail, Calendar, Drive, and more.",
        oauth_url: "/api/oauth/google/incremental?scopes=gmail,calendar,drive",
        revoke_url: "/api/oauth/google/revoke?tool_name=gmail",
        installed: !1,
        config: {
            apiKey: "",
            endpoint: ""
        }
    }, {
        id: 1,
        name: _t,
        title: "Gmail",
        icon: x(de),
        description: e ? e("components.custom_tools.config.gmail.description") : "Access your Gmail inbox, read and send emails, and search through your messages.",
        oauth_url: "/api/oauth/google/incremental?scopes=gmail",
        revoke_url: "/api/oauth/google/revoke?tool_name=gmail",
        hint_query: e ? [e("components.custom_tools.config.gmail.hint_query_1")] : ["Check my latest gmail"],
        installed: !1,
        config: {
            apiKey: "",
            endpoint: ""
        }
    }, {
        id: 2,
        title: "Calendar",
        name: Lt,
        icon: x(me),
        description: e ? e("components.custom_tools.config.calendar.description") : "Manage your Google Calendar events, set up appointments, and check your schedule.",
        oauth_url: "/api/oauth/google/incremental?scopes=calendar",
        revoke_url: "/api/oauth/google/revoke?tool_name=calendar",
        hint_query: e ? [e("components.custom_tools.config.calendar.hint_query_1")] : ["Check my upcoming calendar"],
        installed: !1,
        config: {
            apiKey: "",
            endpoint: ""
        }
    }, {
        id: 3,
        title: "Drive",
        name: Dt,
        icon: x(ue),
        description: e ? e("components.custom_tools.config.drive.description") : "Access files stored in your Google Drive, upload documents, and share content.",
        oauth_url: "/api/oauth/google/incremental?scopes=drive",
        revoke_url: "/api/oauth/google/revoke?tool_name=drive",
        installed: !1,
        config: {
            apiKey: "",
            endpoint: ""
        }
    }, {
        id: 4,
        title: "Google Slides",
        name: Et,
        icon: x(bt),
        description: e ? e("components.custom_tools.config.google_slides.description") : "Create, edit, and manage Google Slides presentations. Add slides, insert images, shapes, and text to build professional presentations.",
        oauth_url: "/api/oauth/google/incremental?scopes=slides",
        revoke_url: "/api/oauth/google/revoke?tool_name=slides",
        hint_query: e ? [e("components.custom_tools.config.google_slides.hint_query_1"), e("components.custom_tools.config.google_slides.hint_query_2")] : ["Create a presentation about artificial intelligence", "Add a slide with my quarterly sales data"],
        installed: !1,
        config: {
            apiKey: "",
            endpoint: ""
        }
    }, {
        id: 5,
        title: "Notion",
        name: Ft,
        icon: x(ge),
        description: e ? e("components.custom_tools.config.notion.description") : "Access your Notion pages, create and edit content, and manage your workspace.",
        oauth_url: "/api/oauth/notion/login",
        revoke_url: "/api/oauth/notion/revoke",
        hint_query: e ? [e("components.custom_tools.config.notion.hint_query_1")] : ["Search my notion latest notes"]
    }, {
        id: 6,
        title: "Microsoft 365",
        name: Ot,
        icon: x(At),
        description: e ? e("components.custom_tools.config.microsoft_365.description") : "Full access to Microsoft 365! Search across all sites, manage content with full control permissions, and OneNote integration. Complete file and collaboration platform access with admin-level permissions.",
        oauth_url: "/api/oauth/microsoft/incremental?scopes=email,calendar,files,teams,user_management,notes",
        revoke_url: "/api/oauth/microsoft/revoke?tool_name=microsoft_365",
        installed: !1
    }, {
        id: 7,
        title: "Outlook Email",
        name: Bt,
        icon: x(he),
        description: e ? e("components.custom_tools.config.outlook_email.description") : "Enhanced Outlook access with full mailbox permissions! Read, send, and manage emails with complete folder access. Advanced user management capabilities and enterprise-level email integration.",
        oauth_url: "/api/oauth/microsoft/incremental?scopes=email",
        revoke_url: "/api/oauth/microsoft/revoke?tool_name=outlook_email",
        hint_query: e ? [e("components.custom_tools.config.outlook_email.hint_query_1"), e("components.custom_tools.config.outlook_email.hint_query_2")] : ["Search my outlook latest emails", "Find emails from specific senders across all folders"],
        installed: !1,
        config: {
            apiKey: "",
            endpoint: ""
        }
    }, {
        id: 8,
        title: "Outlook Calendar",
        name: jt,
        icon: x(ve),
        description: e ? e("components.custom_tools.config.outlook_calendar.description") : "Full access to your Outlook Calendar with enhanced permissions! Create, manage, and search calendar events with complete integration across Microsoft 365.",
        oauth_url: "/api/oauth/microsoft/incremental?scopes=calendar",
        revoke_url: "/api/oauth/microsoft/revoke?tool_name=outlook_calendar",
        hint_query: e ? [e("components.custom_tools.config.outlook_calendar.hint_query_1"), e("components.custom_tools.config.outlook_calendar.hint_query_2")] : ["Search my outlook latest calendar", "Find upcoming meetings with specific attendees"],
        installed: !1,
        config: {
            apiKey: "",
            endpoint: ""
        }
    }, {
        id: 9,
        title: "Microsoft Teams",
        name: Gt,
        icon: x(ye),
        description: e ? e("components.custom_tools.config.microsoft_teams.description") : "Full access to Microsoft Teams with admin permissions! Search all messages, channels, and chats. Send messages, manage teams, read all conversations, and access team member information. Complete Teams integration with enterprise-level capabilities.",
        oauth_url: "/api/oauth/microsoft/incremental?scopes=teams,user_management",
        revoke_url: "/api/oauth/microsoft/revoke?tool_name=microsoft_teams",
        hint_query: e ? [e("components.custom_tools.config.microsoft_teams.hint_query_1"), e("components.custom_tools.config.microsoft_teams.hint_query_2"), e("components.custom_tools.config.microsoft_teams.hint_query_3")] : ["Search my microsoft teams latest messages", "Find all messages from a specific team member", "Search for messages containing specific keywords across all channels"],
        installed: !1,
        config: {
            apiKey: "",
            endpoint: ""
        }
    }, {
        id: 10,
        title: "Microsoft OneDrive",
        name: zt,
        icon: x(be),
        description: e ? e("components.custom_tools.config.microsoft_onedrive.description") : "Access your personal OneDrive files and folders. Search, read, and analyze documents stored in your OneDrive with full file content processing capabilities.",
        oauth_url: "/api/oauth/microsoft/incremental?scopes=files",
        revoke_url: "/api/oauth/microsoft/revoke?tool_name=microsoft_onedrive",
        hint_query: e ? [e("components.custom_tools.config.microsoft_onedrive.hint_query_1"), e("components.custom_tools.config.microsoft_onedrive.hint_query_2")] : ["Search my OneDrive files", "Read and analyze my documents"],
        installed: !1
    }, {
        id: 11,
        title: "Microsoft SharePoint",
        name: Rt,
        icon: x(fe),
        description: e ? e("components.custom_tools.config.microsoft_sharepoint.description") : "Full enterprise access to SharePoint sites, pages, lists, and content. Search across all SharePoint sites, read site pages, access list items, and manage SharePoint content with admin-level permissions.",
        oauth_url: "/api/oauth/microsoft/incremental?scopes=files,notes",
        revoke_url: "/api/oauth/microsoft/revoke?tool_name=microsoft_sharepoint",
        hint_query: e ? [e("components.custom_tools.config.microsoft_sharepoint.hint_query_1"), e("components.custom_tools.config.microsoft_sharepoint.hint_query_2"), e("components.custom_tools.config.microsoft_sharepoint.hint_query_3")] : ["Search SharePoint sites and pages", "Find documents across all SharePoint sites", "Access SharePoint list items and content"],
        installed: !1
    }, {
        id: 12,
        title: "Slack",
        name: qt,
        icon: x(ke),
        description: e ? e("components.custom_tools.config.slack.description") : "Access your Slack, read and send messages, and search through your messages.",
        oauth_url: "/api/oauth/slack/login",
        revoke_url: "/api/oauth/slack/revoke",
        hint_query: e ? [e("components.custom_tools.config.slack.hint_query_1")] : ["Search my slack latest messages"],
        installed: !1
    }, {
        id: 101,
        title: "X/Twitter Content Explorer",
        name: "X/Twitter Content Explorer",
        type: "genspark_browser_mcp",
        mcp_id: "mcp_" + Tt("http://genspark.ai/browser/genspark_browser_mcp/mcp_genspark_browser_x_twitter_content_explorer").substring(0, 8),
        icon: x("data:image/png;base64,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"),
        isMcp: !0,
        description: e ? e("components.custom_tools.config.x_twitter.description") : "Search and explore X/Twitter content directly through browser automation. Find specific tweets, browse user profiles, search for keywords, or scroll through timelines. Works with your Twitter account to access the full range of platform content.",
        url: "http://genspark.ai/browser/genspark_browser_mcp/mcp_genspark_browser_x_twitter_content_explorer",
        genspark_browser_required: !0
    }, {
        id: 102,
        title: "Playwright Browser Automation",
        name: "Playwright Browser Automation",
        type: "genspark_browser_mcp",
        mcp_id: "mcp_" + Tt("http://genspark.ai/browser/genspark_browser_mcp/mcp_genspark_browser_playwright_browser_automation").substring(0, 8),
        icon: x("data:image/png;base64,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"),
        isMcp: !0,
        description: e ? e("components.custom_tools.config.playwright.description") : "Execute automated browser operations through natural language instructions. Tell it what to do in plain English and it will generate and run the corresponding Playwright code. Perfect for automating repetitive web tasks and interactions.",
        url: "http://genspark.ai/browser/genspark_browser_mcp/mcp_genspark_browser_playwright_browser_automation",
        genspark_browser_required: !0
    }, {
        id: 103,
        title: "Local Web Fetch",
        name: "Local Web Fetch",
        type: "genspark_browser_mcp",
        mcp_id: "mcp_" + Tt("http://genspark.ai/browser/genspark_browser_mcp/mcp_genspark_browser_local_web_fetch").substring(0, 8),
        icon: x("data:image/png;base64,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"),
        isMcp: !0,
        description: e ? e("components.custom_tools.config.local_web_fetch.description") : "Access web content using your local browser session. This tool can retrieve content from websites where you're already logged in, including your social media feeds, personalized dashboards, and member-only sites. It uses your existing browser credentials to access content that regular web crawlers can't reach.",
        url: "http://genspark.ai/browser/genspark_browser_mcp/mcp_genspark_browser_local_web_fetch",
        genspark_browser_required: !0
    }, {
        id: 104,
        title: "Reddit MCP",
        name: "Reddit MCP",
        mcp_id: "mcp_" + Tt("https://mcp-reddit.dev.sspark.ai/sse").substring(0, 8),
        isMcp: !0,
        icon: x(Mt),
        description: e ? e("components.custom_tools.config.reddit_mcp.description") : "Reddit MCP interact with Reddit's public API and exposes the functionality through MCP protocol.",
        type: "sse",
        hint_query: e ? [e("components.custom_tools.config.reddit_mcp.hint_query_1"), e("components.custom_tools.config.reddit_mcp.hint_query_2"), e("components.custom_tools.config.reddit_mcp.hint_query_3"), e("components.custom_tools.config.reddit_mcp.hint_query_4"), e("components.custom_tools.config.reddit_mcp.hint_query_5"), e("components.custom_tools.config.reddit_mcp.hint_query_6"), e("components.custom_tools.config.reddit_mcp.hint_query_7"), e("components.custom_tools.config.reddit_mcp.hint_query_8")] : ["What are the current hot posts on Reddit's frontpage?", "Tell me about the r/ClaudeAI subreddit", "What are the hot posts in the r/ClaudeAI subreddit?", "Show me the newest posts from r/ClaudeAI", "What are the top posts of all time in r/ClaudeAI?", "What posts are trending in r/ClaudeAI right now?", "Get the full content and comments of this Reddit post: [post_url]", "Summarize the comments on this Reddit post: [post_url]"],
        url: "http://mcp-reddit.dev.sspark.ai/sse"
    }, {
        id: 105,
        title: "Deep Wiki MCP",
        name: "Deep Wiki MCP",
        mcp_id: "mcp_" + Tt("https://mcp.deepwiki.com/sse").substring(0, 8),
        isMcp: !0,
        icon: x(xt),
        description: e ? e("components.custom_tools.config.deep_wiki_mcp.description") : "DeepWiki is a free version of Devin Wiki and Devin Search that works with public GitHub repositories. It automatically generates architecture diagrams, documentation, and links to source code to help you understand unfamiliar codebases quickly. You can also ask complex questions about the codebase to get context-grounded specific answers.",
        type: "sse",
        url: "https://mcp.deepwiki.com/sse",
        hint_query: e ? [e("components.custom_tools.config.deep_wiki_mcp.hint_query_1")] : ["use deep wiki to explain pytorch core infrastructure"],
        genspark_browser_required: !1
    }, {
        id: 106,
        title: "Chart Server MCP",
        name: "Chart Server MCP",
        mcp_id: "mcp_" + Tt("https://mcp-chart.dev.sspark.ai/mcp").substring(0, 8),
        isMcp: !0,
        icon: "https://avatars.githubusercontent.com/u/19199542?s=48&v=4",
        description: e ? e("components.custom_tools.config.chart_server_mcp.description") : "MCP Server Chart is a TypeScript-based MCP server that provides chart generation capabilities. It allows you to create various types of charts through MCP tools.",
        type: "streamableHttp",
        url: "http://mcp-chart.dev.sspark.ai/mcp",
        hint_query: e ? [e("components.custom_tools.config.chart_server_mcp.hint_query_1")] : ["use mcp server chart to explain pytorch core infrastructure"],
        genspark_browser_required: !1
    }, {
        id: 107,
        title: "Hacker News MCP",
        name: "Hacker News MCP",
        mcp_id: "mcp_" + Tt("http://mcp-hn.dev.sspark.ai/sse").substring(0, 8),
        isMcp: !0,
        icon: x(St),
        description: e ? e("components.custom_tools.config.hacker_news_mcp.description") : "Official Hacker News MCP Server - Adds powerful Hacker News integration, Access stories, comments, user profiles, and search functionality through the Model Context Protocol.",
        type: "sse",
        url: "http://mcp-hn.dev.sspark.ai/sse",
        hint_query: e ? [e("components.custom_tools.config.hacker_news_mcp.hint_query_1")] : ["What are the top stories on Hacker News?"]
    }],
    Ut = Nt(),
    Wt = (e, t, i) => e,
    Vt = {
        class: "custom-tools-modal"
    },
    Ht = {
        class: "dropdown-header"
    },
    Qt = {
        class: "tool-icon"
    },
    Kt = ["src"],
    Zt = {
        key: 2,
        class: "default-icon"
    },
    Jt = {
        class: "tool-name"
    },
    Yt = {
        class: "checkbox"
    },
    Xt = ["id", "checked", "onChange"],
    $t = ["for"],
    ei = P({
        __name: "CustomToolsConfigModal",
        emits: ["close", "addTools"],
        setup(e, {
            emit: t
        }) {
            const {
                t: i
            } = S(), o = y({}), a = y(null), n = g((() => {
                const e = Object.keys(o.value),
                    t = [],
                    a = new Set;
                for (const n of e) {
                    const e = n.toLowerCase();
                    if (a.has(e)) continue;
                    const r = Nt(i).some((t => t.name.toLowerCase() === e)),
                        s = /^mcp_[0-9a-fA-F]{8}$/i.test(n);
                    if (r || s) {
                        const i = n;
                        let r = i;
                        if (s) {
                            const e = o.value[i];
                            "string" == typeof e && "" !== e.trim() && (r = e)
                        }
                        t.push({
                            name: i,
                            displayName: r
                        }), a.add(e)
                    }
                }
                return t
            })), r = y([]), s = () => {
                const e = n.value.map((e => e.name));
                let t = [];
                try {
                    const e = localStorage.getItem("unselectedCustomTools");
                    if (e) {
                        const i = JSON.parse(e);
                        Array.isArray(i) && (t = i.filter((e => "string" == typeof e)))
                    }
                } catch (o) {}
                const i = t.filter((t => e.includes(t)));
                i.length !== t.length && (localStorage.setItem("unselectedCustomTools", JSON.stringify(i)), O.log("Cleaned unselectedCustomTools in localStorage", i)), r.value = e.filter((e => !i.includes(e))), O.log("Selection state synced:", {
                    valid: e,
                    unselected: i,
                    selected: r.value
                })
            }, c = () => {
                try {
                    o.value = JSON.parse(localStorage.getItem("installedCustomTools") || "{}"), O.log("localInstalledTools loaded:", o.value)
                } catch (e) {
                    o.value = {}
                }
            };
            b((() => {
                c(), s()
            })), T(n, (() => {
                O.log("validInstalledTools changed, re-syncing selection state."), s()
            }), {
                deep: !0
            }), window.addEventListener("storage", (e => {
                "installedCustomTools" === e.key && (c(), s())
            }));
            const l = e => {
                const t = e.toLowerCase(),
                    o = Nt(i).find((e => e.mcp_id && e.mcp_id.toLowerCase() === t));
                if (o) return o.icon;
                const a = Nt(i).find((e => e.name.toLowerCase() === t));
                return a ? a.icon : t.startsWith("mcp_") ? Ae : null
            };
            return (e, t) => (C(), w("div", Vt, [A("div", Ht, [A("h3", null, I(e.$t("components.custom_tools_config_modal.choose_tools")), 1), A("div", {
                class: "add-link",
                onClick: t[0] || (t[0] = t => e.$emit("addTools"))
            }, [_(I(e.$t("components.custom_tools_config_modal.add")) + " ", 1), t[1] || (t[1] = A("span", {
                class: "arrow"
            }, "›", -1))])]), A("div", {
                class: "tools-list",
                ref_key: "toolsListRef",
                ref: a
            }, [(C(!0), w(L, null, D(n.value, (e => {
                return C(), w("div", {
                    key: e.name,
                    class: "tool-item"
                }, [A("div", Qt, ["string" == typeof l(e.name) && (l(e.name).startsWith("http") || l(e.name).startsWith("data:image") || l(e.name).includes(".png") || l(e.name).includes(".jpg") || l(e.name).includes(".jpeg") || l(e.name).includes(".gif") || l(e.name).startsWith("/_nuxt/")) ? (C(), w("img", {
                    key: 0,
                    src: l(e.name),
                    alt: "Tool Icon"
                }, null, 8, Kt)) : l(e.name) ? (C(), E(F(l(e.name)), {
                    key: 1
                })) : (C(), w("div", Zt, I(e.name.charAt(0)), 1))]), A("div", Jt, I((t = e.displayName, t.replace(/[-_]/g, " ").replace(/\b\w+/g, (e => e !== e.toLowerCase() && e !== e.toUpperCase() ? e : e.charAt(0).toUpperCase() + e.slice(1).toLowerCase())))), 1), A("div", Yt, [A("input", {
                    type: "checkbox",
                    id: e.name,
                    checked: r.value.includes(e.name),
                    onChange: t => (e => {
                        let t = [];
                        try {
                            const e = localStorage.getItem("unselectedCustomTools");
                            if (e) {
                                const i = JSON.parse(e);
                                Array.isArray(i) && (t = i.filter((e => "string" == typeof e)))
                            }
                        } catch (o) {}
                        r.value.includes(e) ? (t.includes(e) || t.push(e), r.value = r.value.filter((t => t !== e))) : (t = t.filter((t => t !== e)), r.value.push(e));
                        const i = JSON.stringify(t);
                        O.log("unselectedString", i), localStorage.setItem("unselectedCustomTools", i), O.log("Toggled tool:", e, "{ newUnselected: unselectedNames, newSelectedRef: selectedTools.value }")
                    })(e.name)
                }, null, 40, Xt), A("label", {
                    for: e.name
                }, null, 8, $t)])]);
                var t
            })), 128))], 512)]))
        }
    }, [
        ["__scopeId", "data-v-39c05aa8"]
    ]),
    ti = [{
        id: 1,
        title: "Postgres Integration",
        name: "julien040/anyquery",
        description: "Query more than 40 apps with one binary using SQL. It can also connect to your PostgreSQL, MySQL, or SQLite compatible database. Local-first and private by design.",
        link: "https://github.com/julien040/anyquery",
        category: ["database", "storage"]
    }, {
        id: 2,
        title: "Metatool App Tool",
        name: "metatool-ai/metatool-app",
        description: "MetaMCP is the one unified middleware MCP server that manages your MCP connections with GUI.",
        link: "https://github.com/metatool-ai/metatool-app",
        category: ["aggregator", "integration"]
    }, {
        id: 3,
        title: "Database Integration",
        name: "mindsdb/mindsdb",
        description: "Connect and unify data across various platforms and databases with MindsDB as a single MCP server.",
        link: "https://github.com/mindsdb/mindsdb",
        category: ["database", "storage"]
    }, {
        id: 4,
        title: "A List Of S So You Can Ask Your Client Which S You Can Use To Improve Your Daily Workflow",
        name: "glenngillen/mcpmcp-server",
        description: "A list of MCP servers so you can ask your client which servers you can use to improve your daily workflow.",
        link: "https://github.com/glenngillen/mcpmcp-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 5,
        title: "Turn A Web Api Into In 10 Seconds And Add It To The Open Source Registry: Https://open-",
        name: "wegotdocs/open-mcp",
        description: "Turn a web API into an MCP server in 10 seconds and add it to the open source registry: https://open-mcp.org",
        link: "https://github.com/wegotdocs/open-mcp",
        category: ["tools", "integration"]
    }, {
        id: 6,
        title: "Pipedream Integration",
        name: "PipedreamHQ/pipedream",
        description: "Connect with 2,500 APIs with 8,000+ prebuilt tools, and manage servers for your users, in your own app.",
        link: "https://github.com/PipedreamHQ/pipedream/tree/master/modelcontextprotocol",
        category: ["tools", "integration"]
    }, {
        id: 7,
        title: "Pluggedin Mcp Proxy Tool",
        name: "VeriTeknik/pluggedin-mcp-proxy",
        description: "A comprehensive proxy server that combines multiple MCP servers into a single interface with extensive visibility features. It provides discovery and management of tools, prompts, resources, and templates across servers, plus a playground for debugging when building MCP servers.",
        link: "https://github.com/VeriTeknik/pluggedin-mcp-proxy",
        category: ["aggregator", "integration"]
    }, {
        id: 8,
        title: "Mcgravity Tool",
        name: "tigranbs/mcgravity",
        description: "A proxy tool for composing multiple MCP servers into one unified endpoint. Scale your AI tools by load balancing requests across multiple MCP servers, similar to how Nginx works for web servers.",
        link: "https://github.com/tigranbs/mcgravity",
        category: ["aggregator", "integration"]
    }, {
        id: 9,
        title: "Metatool App Tool",
        name: "metatool-ai/metatool-app",
        description: "MetaMCP is the one unified middleware MCP server that manages your MCP connections with GUI.",
        link: "https://github.com/metatool-ai/metatool-app",
        category: ["aggregator", "integration"]
    }, {
        id: 10,
        title: "Seamlessly And Securely Connect Claude Desktop And Other Hosts To Your Favorite Apps (notion",
        name: "waystation-ai/mcp",
        description: "Seamlessly and securely connect Claude Desktop and other MCP hosts to your favorite apps (Notion, Slack, Monday, Airtable, etc.). Takes less than 90 secs.",
        link: "https://github.com/waystation-ai/mcp",
        category: ["database", "storage"]
    }, {
        id: 11,
        title: "Mcp Access Point Tool",
        name: "sxhxliang/mcp-access-point",
        description: "Turn a web service into an MCP server in one click without making any code changes.",
        link: "https://github.com/sxhxliang/mcp-access-point",
        category: ["cli", "terminal", "command"]
    }, {
        id: 12,
        title: "A Powerful Image Generation Tool Using Google's Imagen 3",
        name: "hamflx/imagen3-mcp",
        description: "A powerful image generation tool using Google's Imagen 3.0 API through MCP. Generate high-quality images from text prompts with advanced photography, artistic, and photorealistic controls.",
        link: "https://github.com/hamflx/imagen3-mcp",
        category: ["art", "culture", "media"]
    }, {
        id: 13,
        title: "Openai Gpt Image Mcp",
        name: "SureScaleAI/openai-gpt-image-mcp",
        description: "OpenAI GPT image generation/editing MCP server.",
        link: "https://github.com/SureScaleAI/openai-gpt-image-mcp",
        category: ["multimedia", "processing"]
    }, {
        id: 14,
        title: "Manim Mcp Server Tool",
        name: "abhiemj/manim-mcp-server",
        description: "A local MCP server that generates animations using Manim.",
        link: "https://github.com/abhiemj/manim-mcp-server",
        category: ["art", "culture", "media"]
    }, {
        id: 15,
        title: "Video Editing Mcp Tool",
        name: "burningion/video-editing-mcp",
        description: "Add, Analyze, Search, and Generate Video Edits from your Video Jungle Collection",
        link: "https://github.com/burningion/video-editing-mcp",
        category: ["multimedia", "processing"]
    }, {
        id: 16,
        title: "Discogs Mcp Server Integration",
        name: "cswkim/discogs-mcp-server",
        description: "MCP server to interact with the Discogs API",
        link: "https://github.com/cswkim/discogs-mcp-server",
        category: ["art", "culture", "media"]
    }, {
        id: 17,
        title: "Quran Mcp Server Integration",
        name: "djalal/quran-mcp-server",
        description: "📇 ☁️ MCP server to interact with Quran.com corpus via the official REST API v4.",
        link: "https://github.com/djalal/quran-mcp-server",
        category: ["art", "culture", "media"]
    }, {
        id: 18,
        title: "Metropolitan Museum Of Art Collection Api Integration To Search And Display Artworks In The Collection",
        name: "mikechao/metmuseum-mcp",
        description: "Metropolitan Museum of Art Collection API integration to search and display artworks in the collection.",
        link: "https://github.com/mikechao/metmuseum-mcp",
        category: ["art", "culture", "media"]
    }, {
        id: 19,
        title: "Rijksmuseum Api Integration For Artwork Search",
        name: "r-huijts/rijksmuseum-mcp",
        description: "Rijksmuseum API integration for artwork search, details, and collections",
        link: "https://github.com/r-huijts/rijksmuseum-mcp",
        category: ["art", "culture", "media"]
    }, {
        id: 20,
        title: "Oorlogsbronnen (war Sources) Api Integration For Accessing Historical Wwii Records",
        name: "r-huijts/oorlogsbronnen-mcp",
        description: "Oorlogsbronnen (War Sources) API integration for accessing historical WWII records, photographs, and documents from the Netherlands (1940-1945)",
        link: "https://github.com/r-huijts/oorlogsbronnen-mcp",
        category: ["file", "storage"]
    }, {
        id: 21,
        title: "Davinci Resolve Mcp Integration",
        name: "samuelgursky/davinci-resolve-mcp",
        description: "MCP server integration for DaVinci Resolve providing powerful tools for video editing, color grading, media management, and project control",
        link: "https://github.com/samuelgursky/davinci-resolve-mcp",
        category: ["multimedia", "processing"]
    }, {
        id: 22,
        title: "Integrating Anilist Api For Anime And Manga Information",
        name: "yuna0x0/anilist-mcp",
        description: "A MCP server integrating AniList API for anime and manga information",
        link: "https://github.com/yuna0x0/anilist-mcp",
        category: ["art", "culture", "media"]
    }, {
        id: 23,
        title: "Using The Aseprite Api To Create Pixel Art",
        name: "diivi/aseprite-mcp",
        description: "MCP server using the Aseprite API to create pixel art",
        link: "https://github.com/diivi/aseprite-mcp",
        category: ["art", "culture", "media"]
    }, {
        id: 24,
        title: "Isaac Sim Mcp Tool",
        name: "omni-mcp/isaac-sim-mcp",
        description: "A MCP Server and an extension enables natural language control of NVIDIA Isaac Sim, Lab, OpenUSD and etc.",
        link: "https://github.com/omni-mcp/isaac-sim-mcp",
        category: ["translation", "language"]
    }, {
        id: 25,
        title: "Mcp Open Library Integration",
        name: "8enSmith/mcp-open-library",
        description: "A MCP server for the Open Library API that enables AI assistants to search for book information.",
        link: "https://github.com/8enSmith/mcp-open-library",
        category: ["search", "extraction"]
    }, {
        id: 26,
        title: "For Autodesk Maya",
        name: "PatrickPalmer/MayaMCP",
        description: "MCP server for Autodesk Maya",
        link: "https://github.com/PatrickPalmer/MayaMCP",
        category: ["tools", "integration"]
    }, {
        id: 27,
        title: "Provides Comprehensive And Accurate Bazi (chinese Astrology) Charting And Analysis",
        name: "cantian-ai/bazi-mcp",
        description: "Provides comprehensive and accurate Bazi (Chinese Astrology) charting and analysis",
        link: "https://github.com/cantian-ai/bazi-mcp",
        category: ["art", "culture", "media"]
    }, {
        id: 28,
        title: "A Fast-based Tool That Fetches Bilibili's Trending Videos And Exposes Them Via A Standard Interface",
        name: "xspadex/bilibili-mcp.git",
        description: "A FastMCP-based tool that fetches Bilibili's trending videos and exposes them via a standard MCP interface.",
        link: "https://github.com/xspadex/bilibili-mcp.git",
        category: ["multimedia", "processing"]
    }, {
        id: 29,
        title: "Bilibili Mcp Js Integration",
        name: "34892002/bilibili-mcp-js",
        description: "A MCP server that supports searching for Bilibili content. Provides LangChain integration examples and test scripts.",
        link: "https://github.com/34892002/bilibili-mcp-js",
        category: ["search", "extraction"]
    }, {
        id: 30,
        title: "Browser Automation Tool",
        name: "aircodelabs/grasp",
        description: "Self-hosted browser using agent with built-in MCP and A2A support.",
        link: "https://github.com/aircodelabs/grasp",
        category: ["browser", "automation", "web"]
    }, {
        id: 31,
        title: "Browser Automation Tool",
        name: "Automata-Labs-team/MCP-Server-Playwright",
        description: "An MCP server for browser automation using Playwright",
        link: "https://github.com/Automata-Labs-team/MCP-Server-Playwright",
        category: ["browser", "automation", "web"]
    }, {
        id: 32,
        title: "Browser Automation Tool",
        name: "blackwhite084/playwright-plus-python-mcp",
        description: "An MCP python server using Playwright for browser automation,more suitable for llm",
        link: "https://github.com/blackwhite084/playwright-plus-python-mcp",
        category: ["browser", "automation", "web"]
    }, {
        id: 33,
        title: "Browser Automation Tool",
        name: "browserbase/mcp-server-browserbase",
        description: "Automate browser interactions in the cloud (e.g. web navigation, data extraction, form filling, and more)",
        link: "https://github.com/browserbase/mcp-server-browserbase",
        category: ["browser", "automation", "web"]
    }, {
        id: 34,
        title: "Automate Your Local Chrome Browser",
        name: "browsermcp/mcp",
        description: "Automate your local Chrome browser",
        link: "https://github.com/browsermcp/mcp",
        category: ["browser", "automation", "web"]
    }, {
        id: 35,
        title: "Browser Automation Tool",
        name: "co-browser/browser-use-mcp-server",
        description: "browser-use packaged as an MCP server with SSE transport. includes a dockerfile to run chromium in docker + a vnc server.",
        link: "https://github.com/co-browser/browser-use-mcp-server",
        category: ["browser", "automation", "web"]
    }, {
        id: 36,
        title: "Using Playwright For Browser Automation And Webscrapping",
        name: "executeautomation/mcp-playwright",
        description: "An MCP server using Playwright for browser automation and webscrapping",
        link: "https://github.com/executeautomation/mcp-playwright",
        category: ["browser", "automation", "web"]
    }, {
        id: 37,
        title: "Browser Automation Tool",
        name: "eyalzh/browser-control-mcp",
        description: "An MCP server paired with a browser extension that enables LLM clients to control the user's browser (Firefox).",
        link: "https://github.com/eyalzh/browser-control-mcp",
        category: ["browser", "automation", "web"]
    }, {
        id: 38,
        title: "Mcp Server Apple Reminders",
        name: "FradSer/mcp-server-apple-reminders",
        description: "An MCP server for interacting with Apple Reminders on macOS",
        link: "https://github.com/FradSer/mcp-server-apple-reminders",
        category: ["tools", "integration"]
    }, {
        id: 39,
        title: "Extract Structured Data From Any Website",
        name: "getrupt/ashra-mcp",
        description: "Extract structured data from any website. Just prompt and get JSON.",
        link: "https://github.com/getrupt/ashra-mcp",
        category: ["tools", "integration"]
    }, {
        id: 40,
        title: "Mcp Server Youtube Transcript",
        name: "kimtaeyoon83/mcp-server-youtube-transcript",
        description: "Fetch YouTube subtitles and transcripts for AI analysis",
        link: "https://github.com/kimtaeyoon83/mcp-server-youtube-transcript",
        category: ["tools", "integration"]
    }, {
        id: 41,
        title: "Azure Integration",
        name: "kimtth/mcp-aoai-web-browsing",
        description: "A minimal server/client MCP implementation using Azure OpenAI and Playwright.",
        link: "https://github.com/kimtth/mcp-aoai-web-browsing",
        category: ["browser", "automation", "web"]
    }, {
        id: 42,
        title: "Official Microsoft Playwright",
        name: "microsoft/playwright-mcp",
        description: "Official Microsoft Playwright MCP server, enabling LLMs to interact with web pages through structured accessibility snapshots",
        link: "https://github.com/microsoft/playwright-mcp",
        category: ["browser", "automation", "web"]
    }, {
        id: 43,
        title: "Browser Automation Tool",
        name: "modelcontextprotocol/servers",
        description: "Browser automation for web scraping and interaction",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/puppeteer",
        category: ["browser", "automation", "web"]
    }, {
        id: 44,
        title: "Browser Automation Tool",
        name: "ndthanhdev/mcp-browser-kit",
        description: "An MCP Server for interacting with manifest v2 compatible browsers.",
        link: "https://github.com/ndthanhdev/mcp-browser-kit",
        category: ["browser", "automation", "web"]
    }, {
        id: 45,
        title: "Web Search Integration",
        name: "pskill9/web-search",
        description: "An MCP server that enables free web searching using Google search results, with no API keys required.",
        link: "https://github.com/pskill9/web-search",
        category: ["search", "extraction"]
    }, {
        id: 46,
        title: "Mcp Server Apple Shortcuts",
        name: "recursechat/mcp-server-apple-shortcuts",
        description: "An MCP Server Integration with Apple Shortcuts",
        link: "https://github.com/recursechat/mcp-server-apple-shortcuts",
        category: ["tools", "integration"]
    }, {
        id: 47,
        title: "Aws S For Seamless Integration With Aws Services And Resources",
        name: "awslabs/mcp",
        description: "AWS MCP servers for seamless integration with AWS services and resources.",
        link: "https://github.com/awslabs/mcp",
        category: ["cloud", "infrastructure"]
    }, {
        id: 48,
        title: "Qiniu Mcp Server Tool",
        name: "qiniu/qiniu-mcp-server",
        description: "A MCP built on Qiniu Cloud products, supporting access to Qiniu Cloud Storage, media processing services, etc.",
        link: "https://github.com/qiniu/qiniu-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 49,
        title: "Upload And Manipulation Of Ipfs Storage",
        name: "alexbakers/mcp-ipfs",
        description: "upload and manipulation of IPFS storage",
        link: "https://github.com/alexbakers/mcp-ipfs",
        category: ["file", "storage"]
    }, {
        id: 50,
        title: "Kubernetes Integration",
        name: "reza-gholizade/k8s-mcp-server",
        description: "A Kubernetes Model Context Protocol (MCP) server that provides tools for interacting with Kubernetes clusters through a standardized interface, including API resource discovery, resource management, pod logs, metrics, and events.",
        link: "https://github.com/reza-gholizade/k8s-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 51,
        title: "Books Mcp Server Tool",
        name: "VmLia/books-mcp-server",
        description: "This is an MCP server used for querying books, and it can be applied in common MCP clients, such as Cherry Studio.",
        link: "https://github.com/VmLia/books-mcp-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 52,
        title: "AWS Integration",
        name: "alexei-led/aws-mcp-server",
        description: "A lightweight but powerful server that enables AI assistants to execute AWS CLI commands, use Unix pipes, and apply prompt templates for common AWS tasks in a safe Docker environment with multi-architecture support",
        link: "https://github.com/alexei-led/aws-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 53,
        title: "Kubernetes Integration",
        name: "alexei-led/k8s-mcp-server",
        description: "A lightweight yet robust server that empowers AI assistants to securely execute Kubernetes CLI commands (kubectl, helm, istioctl, and argocd) using Unix pipes in a safe Docker environment with multi-architecture support.",
        link: "https://github.com/alexei-led/k8s-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 54,
        title: "Alibaba Cloud Ops Mcp Server",
        name: "aliyun/alibaba-cloud-ops-mcp-server",
        description: "A MCP server that enables AI assistants to operation resources on Alibaba Cloud, supporting ECS, Cloud Monitor, OOS and widely used cloud products.",
        link: "https://github.com/aliyun/alibaba-cloud-ops-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 55,
        title: "Esxi Mcp Server Integration",
        name: "bright8192/esxi-mcp-server",
        description: "A VMware ESXi/vCenter management server based on MCP (Model Control Protocol), providing simple REST API interfaces for virtual machine management.",
        link: "https://github.com/bright8192/esxi-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 56,
        title: "Mcp Server Cloudflare Integration",
        name: "cloudflare/mcp-server-cloudflare",
        description: "Integration with Cloudflare services including Workers, KV, R2, and D1",
        link: "https://github.com/cloudflare/mcp-server-cloudflare",
        category: ["cloud", "infrastructure"]
    }, {
        id: 57,
        title: "Fastly Openapi Schema Integration",
        name: "jedisct1/fastly-openapi-schema",
        description: "Integration with h Fastly services",
        link: "https://github.com/jedisct1/fastly-openapi-schema",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 58,
        title: "Kubernetes Integration",
        name: "Flux159/mcp-server-kubernetes",
        description: "Typescript implementation of Kubernetes cluster operations for pods, deployments, services.",
        link: "https://github.com/Flux159/mcp-server-kubernetes",
        category: ["cloud", "infrastructure"]
    }, {
        id: 59,
        title: "Azure Integration",
        name: "hardik-id/azure-resource-graph-mcp-server",
        description: "A Model Context Protocol server for querying and analyzing Azure resources at scale using Azure Resource Graph, enabling AI assistants to explore and monitor Azure infrastructure.",
        link: "https://github.com/hardik-id/azure-resource-graph-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 60,
        title: "Azure Integration",
        name: "jdubois/azure-cli-mcp",
        description: "A wrapper around the Azure CLI command line that allows you to talk directly to Azure",
        link: "https://github.com/jdubois/azure-cli-mcp",
        category: ["cloud", "infrastructure"]
    }, {
        id: 61,
        title: "An To Give Access To All Netskope Private Access Components Within A Netskope Private Access Environments Including Detailed Setup Information And Llm Examples On Usage",
        name: "johnneerdael/netskope-mcp",
        description: "An MCP to give access to all Netskope Private Access components within a Netskope Private Access environments including detailed setup information and LLM examples on usage.",
        link: "https://github.com/johnneerdael/netskope-mcp",
        category: ["tools", "integration"]
    }, {
        id: 62,
        title: "Kubernetes Integration",
        name: "manusa/kubernetes-mcp-server",
        description: "powerful Kubernetes MCP server with additional support for OpenShift. Besides providing CRUD operations for any Kubernetes resource, this server provides specialized tools to interact with your cluster.",
        link: "https://github.com/manusa/kubernetes-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 63,
        title: "A Terraform Allowing Ai Assistants To Manage And Operate Terraform Environments",
        name: "nwiizo/tfmcp",
        description: "A Terraform MCP server allowing AI assistants to manage and operate Terraform environments, enabling reading configurations, analyzing plans, applying configurations, and managing Terraform state.",
        link: "https://github.com/nwiizo/tfmcp",
        category: ["cloud", "infrastructure"]
    }, {
        id: 64,
        title: "For Interacting With Pulumi Using The Pulumi Automation Api And Pulumi Cloud Api",
        name: "pulumi/mcp-server",
        description: "MCP server for interacting with Pulumi using the Pulumi Automation API and Pulumi Cloud API. Enables MCP clients to perform Pulumi operations like retrieving package information, previewing changes, deploying updates, and retrieving stack outputs programmatically.",
        link: "https://github.com/pulumi/mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 65,
        title: "Kubernetes Integration",
        name: "rohitg00/kubectl-mcp-server",
        description: "A Model Context Protocol (MCP) server for Kubernetes that enables AI assistants like Claude, Cursor, and others to interact with Kubernetes clusters through natural language.",
        link: "https://github.com/rohitg00/kubectl-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 66,
        title: "Kubernetes Integration",
        name: "strowk/mcp-k8s-go",
        description: "Kubernetes cluster operations through MCP",
        link: "https://github.com/strowk/mcp-k8s-go",
        category: ["cloud", "infrastructure"]
    }, {
        id: 67,
        title: "Go-based For Interfacing With Nutanix Prism Central Resources",
        name: "thunderboltsid/mcp-nutanix",
        description: "Go-based MCP Server for interfacing with Nutanix Prism Central resources.",
        link: "https://github.com/thunderboltsid/mcp-nutanix",
        category: ["tools", "integration"]
    }, {
        id: 68,
        title: "AWS Integration",
        name: "trilogy-group/aws-pricing-mcp",
        description: "Get up-to-date EC2 pricing information with one call. Fast. Powered by a pre-parsed AWS pricing catalogue.",
        link: "https://github.com/trilogy-group/aws-pricing-mcp",
        category: ["cloud", "infrastructure"]
    }, {
        id: 69,
        title: "Kubernetes Integration",
        name: "weibaohui/k8m",
        description: "Provides MCP multi-cluster Kubernetes management and operations, featuring a management interface, logging, and nearly 50 built-in tools covering common DevOps and development scenarios. Supports both standard and CRD resources.",
        link: "https://github.com/weibaohui/k8m",
        category: ["cloud", "infrastructure"]
    }, {
        id: 70,
        title: "Kubernetes Integration",
        name: "weibaohui/kom",
        description: "Provides MCP multi-cluster Kubernetes management and operations. It can be integrated as an SDK into your own project and includes nearly 50 built-in tools covering common DevOps and development scenarios. Supports both standard and CRD resources.",
        link: "https://github.com/weibaohui/kom",
        category: ["cloud", "infrastructure"]
    }, {
        id: 71,
        title: "Kubernetes Integration",
        name: "wenhuwang/mcp-k8s-eye",
        description: "MCP Server for kubernetes management, and analyze your cluster, application health",
        link: "https://github.com/wenhuwang/mcp-k8s-eye",
        category: ["cloud", "infrastructure"]
    }, {
        id: 72,
        title: "Azure Integration",
        name: "erikhoward/adls-mcp-server",
        description: "MCP Server for Azure Data Lake Storage. It can perform manage containers, read/write/upload/download operations on container files and manage file metadata.",
        link: "https://github.com/erikhoward/adls-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 73,
        title: "-k8s Is An Ai-driven Kubernetes Resource Management Tool That Allows Users To Operate Any Resources In Kubernetes Clusters Through Natural Language Interaction",
        name: "silenceper/mcp-k8s",
        description: "MCP-K8S is an AI-driven Kubernetes resource management tool that allows users to operate any resources in Kubernetes clusters through natural language interaction, including native resources (like Deployment, Service) and custom resources (CRD). No need to memorize complex commands - just describe your needs, and AI will accurately execute the corresponding cluster operations, greatly enhancing the usability of Kubernetes.",
        link: "https://github.com/silenceper/mcp-k8s",
        category: ["cloud", "infrastructure"]
    }, {
        id: 74,
        title: "Database Integration",
        name: "redis/mcp-redis-cloud",
        description: "Manage your Redis Cloud resources effortlessly using natural language. Create databases, monitor subscriptions, and configure cloud deployments with simple commands.",
        link: "https://github.com/redis/mcp-redis-cloud",
        category: ["database", "storage"]
    }, {
        id: 75,
        title: "A Powerful That Enables Ai Assistants To Seamlessly Interact With Portainer Instances",
        name: "portainer/portainer-mcp",
        description: "A powerful MCP server that enables AI assistants to seamlessly interact with Portainer instances, providing natural language access to container management, deployment operations, and infrastructure monitoring capabilities.",
        link: "https://github.com/portainer/portainer-mcp",
        category: ["cloud", "infrastructure"]
    }, {
        id: 76,
        title: "Pydantic Ai Tool",
        name: "pydantic/pydantic-ai",
        description: "Run Python code in a secure sandbox via MCP tool calls",
        link: "https://github.com/pydantic/pydantic-ai/tree/main/mcp-run-python",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 77,
        title: "Mcp Server Js Tool",
        name: "yepcode/mcp-server-js",
        description: "Execute any LLM-generated code in a secure and scalable sandbox environment and create your own MCP tools using JavaScript or Python, with full support for NPM and PyPI packages",
        link: "https://github.com/yepcode/mcp-server-js",
        category: ["cli", "terminal", "command"]
    }, {
        id: 78,
        title: "Openapi-: Dockerized To Allow Your Ai Agent To Access Any Api With Existing Api Docs",
        name: "ckanthony/openapi-mcp",
        description: "OpenAPI-MCP: Dockerized MCP Server to allow your AI agent to access any API with existing api docs.",
        link: "https://github.com/ckanthony/openapi-mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 79,
        title: "Node Code Sandbox Mcp",
        name: "alfonsograziano/node-code-sandbox-mcp",
        description: "based sandboxes for executing JavaScript snippets with on-the-fly npm dependency installation and clean teardown",
        link: "https://github.com/alfonsograziano/node-code-sandbox-mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 80,
        title: "A Javascript Code Execution Sandbox That Uses V8 To Isolate Code To Run Ai Generated Javascript Locally Without Fear",
        name: "r33drichards/mcp-js",
        description: "A Javascript code execution sandbox that uses v8 to isolate code to run AI generated javascript locally without fear. Supports heap snapshotting for persistent sessions.",
        link: "https://github.com/r33drichards/mcp-js",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 81,
        title: "Serena Tool",
        name: "oraios/serena",
        description: "A fully-featured coding agent that relies on symbolic code operations by using language servers.",
        link: "https://github.com/oraios/serena",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 82,
        title: "Coding Agent With Basic Read",
        name: "ezyang/codemcp",
        description: "Coding agent with basic read, write and command line tools.",
        link: "https://github.com/ezyang/codemcp",
        category: ["cli", "terminal", "command"]
    }, {
        id: 83,
        title: "Mcp Server Leetcode Tool",
        name: "doggybee/mcp-server-leetcode",
        description: "An MCP server that enables AI models to search, retrieve, and solve LeetCode problems. Supports metadata filtering, user profiles, submissions, and contest data access.",
        link: "https://github.com/doggybee/mcp-server-leetcode",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 84,
        title: "Leetcode Mcp Server Tool",
        name: "jinzcdev/leetcode-mcp-server",
        description: "MCP server enabling automated access to LeetCode's programming problems, solutions, submissions and public data with optional authentication for user-specific features (e.g., notes), supporting both leetcode.com (global) and leetcode.cn (China) sites.",
        link: "https://github.com/jinzcdev/leetcode-mcp-server",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 85,
        title: "Vscode Mcp Server Tool",
        name: "juehang/vscode-mcp-server",
        description: "A MCP Server that allows AI such as Claude to read from the directory structure in a VS Code workspace, see problems picked up by linter(s) and the language server, read code files, and make edits.",
        link: "https://github.com/juehang/vscode-mcp-server",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 86,
        title: "A Model Context Protocol That Provides Access To Iterm",
        name: "ferrislucas/iterm-mcp",
        description: "A Model Context Protocol server that provides access to iTerm. You can run commands and ask questions about what you see in the iTerm terminal.",
        link: "https://github.com/ferrislucas/iterm-mcp",
        category: ["cli", "terminal", "command"]
    }, {
        id: 87,
        title: "Mcp Server Commands Tool",
        name: "g0t4/mcp-server-commands",
        description: "Run any command with run_command and run_script tools.",
        link: "https://github.com/g0t4/mcp-server-commands",
        category: ["cli", "terminal", "command"]
    }, {
        id: 88,
        title: "Mcp Safe Local Python Executor",
        name: "maxim-saplin/mcp_safe_local_python_executor",
        description: "Safe Python interpreter based on HF Smolagents LocalPythonExecutor",
        link: "https://github.com/maxim-saplin/mcp_safe_local_python_executor",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 89,
        title: "Cli Mcp Server Tool",
        name: "MladenSU/cli-mcp-server",
        description: "Command line interface with secure execution and customizable security policies",
        link: "https://github.com/MladenSU/cli-mcp-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 90,
        title: "Term Mcp Deepseek Tool",
        name: "OthmaneBlial/term_mcp_deepseek",
        description: "A DeepSeek MCP-like Server for Terminal",
        link: "https://github.com/OthmaneBlial/term_mcp_deepseek",
        category: ["cli", "terminal", "command"]
    }, {
        id: 91,
        title: "Mcp Shell Server Tool",
        name: "tumf/mcp-shell-server",
        description: "A secure shell command execution server implementing the Model Context Protocol (MCP)",
        link: "https://github.com/tumf/mcp-shell-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 92,
        title: "Cisco Pyats Enabling Structured",
        name: "automateyournetwork/pyATS_MCP",
        description: "Cisco pyATS server enabling structured, model-driven interaction with network devices.",
        link: "https://github.com/automateyournetwork/pyATS_MCP",
        category: ["tools", "integration"]
    }, {
        id: 93,
        title: "A Swiss-army-knife That Can Manage/execute Programs And Read/write/search/edit Code And Text Files",
        name: "wonderwhy-er/DesktopCommanderMCP",
        description: "A swiss-army-knife that can manage/execute programs and read/write/search/edit code and text files.",
        link: "https://github.com/wonderwhy-er/DesktopCommanderMCP",
        category: ["cli", "terminal", "command"]
    }, {
        id: 94,
        title: "A Nostr That Allows To Interact With Nostr",
        name: "AbdelStark/nostr-mcp",
        description: "A Nostr MCP server that allows to interact with Nostr, enabling posting notes, and more.",
        link: "https://github.com/AbdelStark/nostr-mcp",
        category: ["tools", "integration"]
    }, {
        id: 95,
        title: "Interact With Twitter Search And Timeline",
        name: "adhikasp/mcp-twikit",
        description: "Interact with Twitter search and timeline",
        link: "https://github.com/adhikasp/mcp-twikit",
        category: ["communication", "messaging"]
    }, {
        id: 96,
        title: "Agentmail Toolkit Tool",
        name: "agentmail-to/agentmail-toolkit",
        description: "An MCP server to create inboxes on the fly to send, receive, and take actions on email. We aren't AI agents for email, but email for AI Agents.",
        link: "https://github.com/agentmail-to/agentmail-toolkit/tree/main/mcp",
        category: ["communication", "messaging"]
    }, {
        id: 97,
        title: "To Interface With The Google Tasks Api",
        name: "arpitbatra123/mcp-googletasks",
        description: "An MCP server to interface with the Google Tasks API",
        link: "https://github.com/arpitbatra123/mcp-googletasks",
        category: ["tools", "integration"]
    }, {
        id: 98,
        title: "Database Integration",
        name: "carterlasalle/mac_messages_mcp",
        description: "An MCP server that securely interfaces with your iMessage database via the Model Context Protocol (MCP), allowing LLMs to query and analyze iMessage conversations. It includes robust phone number validation, attachment processing, contact management, group chat handling, and full support for sending and receiving messages.",
        link: "https://github.com/carterlasalle/mac_messages_mcp",
        category: ["database", "storage"]
    }, {
        id: 99,
        title: "Telegram Api Integration For Accessing User Data",
        name: "chaindead/telegram-mcp",
        description: "Telegram API integration for accessing user data, managing dialogs (chats, channels, groups), retrieving messages, and handling read status",
        link: "https://github.com/chaindead/telegram-mcp",
        category: ["communication", "messaging"]
    }, {
        id: 100,
        title: "Telegram Api Integration For Accessing User Data",
        name: "chigwell/telegram-mcp",
        description: "Telegram API integration for accessing user data, managing dialogs (chats, channels, groups), retrieving messages, sending messages and handling read status.",
        link: "https://github.com/chigwell/telegram-mcp",
        category: ["communication", "messaging"]
    }, {
        id: 101,
        title: "Inbox Zero Tool",
        name: "elie222/inbox-zero",
        description: "An MCP server for Inbox Zero. Adds functionality on top of Gmail like finding out which emails you need to reply to or need to follow up on.",
        link: "https://github.com/elie222/inbox-zero/tree/main/apps/mcp-server",
        category: ["communication", "messaging"]
    }, {
        id: 102,
        title: "Ntfy Me Mcp Tool",
        name: "gitmotion/ntfy-me-mcp",
        description: "An ntfy MCP server for sending/fetching ntfy notifications to your self-hosted ntfy server from AI Agents 📤 (supports secure token auth & more - use with npx or docker!)",
        link: "https://github.com/gitmotion/ntfy-me-mcp",
        category: ["communication", "messaging"]
    }, {
        id: 103,
        title: "Mcp Wecombot Server.Git Tool",
        name: "gotoolkits/mcp-wecombot-server.git",
        description: "An MCP server application that sends various types of messages to the WeCom group robot.",
        link: "https://github.com/gotoolkits/mcp-wecombot-server.git",
        category: ["communication", "messaging"]
    }, {
        id: 104,
        title: "Database Integration",
        name: "hannesrudolph/imessage-query-fastmcp-mcp-server",
        description: "An MCP server that provides safe access to your iMessage database through Model Context Protocol (MCP), enabling LLMs to query and analyze iMessage conversations with proper phone number validation and attachment handling",
        link: "https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server",
        category: ["database", "storage"]
    }, {
        id: 105,
        title: "Acting As An Adapter Into The Acp Ecosystem",
        name: "i-am-bee/acp-mcp",
        description: "An MCP server acting as an adapter into the ACP ecosystem. Seamlessly exposes ACP agents to MCP clients, bridging the communication gap between the two protocols.",
        link: "https://github.com/i-am-bee/acp-mcp",
        category: ["cli", "terminal", "command"]
    }, {
        id: 106,
        title: "Mattermost Mcp Host Tool",
        name: "jagan-shanmugam/mattermost-mcp-host",
        description: "A MCP server along with MCP host that provides access to Mattermost teams, channels and messages. MCP host is integrated as a bot in Mattermost with access to MCP servers that can be configured.",
        link: "https://github.com/jagan-shanmugam/mattermost-mcp-host",
        category: ["communication", "messaging"]
    }, {
        id: 107,
        title: "For Searching Your Personal Whatsapp Messages",
        name: "lharries/whatsapp-mcp",
        description: "An MCP server for searching your personal WhatsApp messages, contacts and sending messages to individuals or groups",
        link: "https://github.com/lharries/whatsapp-mcp",
        category: ["communication", "messaging"]
    }, {
        id: 108,
        title: "Line Bot Mcp Server",
        name: "line/line-bot-mcp-server",
        description: "MCP Server for Integrating LINE Official Account",
        link: "https://github.com/line/line-bot-mcp-server",
        category: ["communication", "messaging"]
    }, {
        id: 109,
        title: "Integration With Gmail And Google Calendar",
        name: "MarkusPfundstein/mcp-gsuite",
        description: "Integration with gmail and Google Calendar.",
        link: "https://github.com/MarkusPfundstein/mcp-gsuite",
        category: ["communication", "messaging"]
    }, {
        id: 110,
        title: "Bluesky Context Server Integration",
        name: "keturiosakys/bluesky-context-server",
        description: "Bluesky instance integration for querying and interaction",
        link: "https://github.com/keturiosakys/bluesky-context-server",
        category: ["social", "media"]
    }, {
        id: 111,
        title: "Slack Integration",
        name: "modelcontextprotocol/servers",
        description: "Slack workspace integration for channel management and messaging",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/slack",
        category: ["communication", "messaging"]
    }, {
        id: 112,
        title: "Slack Integration",
        name: "korotovsky/slack-mcp-server",
        description: "The most powerful MCP server for Slack Workspaces.",
        link: "https://github.com/korotovsky/slack-mcp-server",
        category: ["communication", "messaging"]
    }, {
        id: 113,
        title: "📇 🏠 This Is For Interacting With The Vrchat Api",
        name: "sawa-zen/vrchat-mcp",
        description: "📇 🏠 This is an MCP server for interacting with the VRChat API. You can retrieve information about friends, worlds, avatars, and more in VRChat.",
        link: "https://github.com/sawa-zen/vrchat-mcp",
        category: ["communication", "messaging"]
    }, {
        id: 114,
        title: "Google Calendar Mcp Integration",
        name: "takumi0706/google-calendar-mcp",
        description: "An MCP server to interface with the Google Calendar API. Based on TypeScript.",
        link: "https://github.com/takumi0706/google-calendar-mcp",
        category: ["communication", "messaging"]
    }, {
        id: 115,
        title: "The That Keeps You Informed By Sending The Notification On Phone Using Ntfy",
        name: "teddyzxcv/ntfy-mcp",
        description: "The MCP server that keeps you informed by sending the notification on phone using ntfy",
        link: "https://github.com/teddyzxcv/ntfy-mcp",
        category: ["communication", "messaging"]
    }, {
        id: 116,
        title: "For Didlogic",
        name: "UserAd/didlogic_mcp",
        description: "An MCP server for DIDLogic. Adds functionality to manage SIP endpoints, numbers and destinations.",
        link: "https://github.com/UserAd/didlogic_mcp",
        category: ["tools", "integration"]
    }, {
        id: 117,
        title: "To Manage Google Tasks",
        name: "zcaceres/gtasks-mcp",
        description: "An MCP server to Manage Google Tasks",
        link: "https://github.com/zcaceres/gtasks-mcp",
        category: ["tools", "integration"]
    }, {
        id: 118,
        title: "Mcp Teams Server Tool",
        name: "InditexTech/mcp-teams-server",
        description: "MCP server that integrates Microsoft Teams messaging (read, post, mention, list members and threads)",
        link: "https://github.com/InditexTech/mcp-teams-server",
        category: ["communication", "messaging"]
    }, {
        id: 119,
        title: "Ms 365 Mcp Server",
        name: "softeria/ms-365-mcp-server",
        description: "MCP server that connects to the whole Microsoft 365 suite using Graph API (including mail, files, Excel, calendar)",
        link: "https://github.com/softeria/ms-365-mcp-server",
        category: ["communication", "messaging"]
    }, {
        id: 120,
        title: "Ycloud Whatsapp Mcp Server",
        name: "YCloud-Developers/ycloud-whatsapp-mcp-server",
        description: "MCP server for WhatsApp Business Platform by YCloud.",
        link: "https://github.com/YCloud-Developers/ycloud-whatsapp-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 121,
        title: "Producthunt Mcp Server Tool",
        name: "jaipandya/producthunt-mcp-server",
        description: "MCP server for Product Hunt. Interact with trending posts, comments, collections, users, and more.",
        link: "https://github.com/jaipandya/producthunt-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 122,
        title: "Mcp Server Iaptic Tool",
        name: "iaptic/mcp-server-iaptic",
        description: "Connect with iaptic to ask about your Customer Purchases, Transaction data and App Revenue statistics.",
        link: "https://github.com/iaptic/mcp-server-iaptic",
        category: ["data", "customer", "analytics"]
    }, {
        id: 123,
        title: "Connect Any Open Data To Any Llm With Model Context Protocol",
        name: "OpenDataMCP/OpenDataMCP",
        description: "Connect any Open Data to any LLM with Model Context Protocol.",
        link: "https://github.com/OpenDataMCP/OpenDataMCP",
        category: ["tools", "integration"]
    }, {
        id: 124,
        title: "Inoyu Mcp Unomi Server",
        name: "sergehuber/inoyu-mcp-unomi-server",
        description: "An MCP server to access and updates profiles on an Apache Unomi CDP server.",
        link: "https://github.com/sergehuber/inoyu-mcp-unomi-server",
        category: ["data", "customer", "analytics"]
    }, {
        id: 125,
        title: "To Interact With A Tinybird Workspace From Any Client",
        name: "tinybirdco/mcp-tinybird",
        description: "An MCP server to interact with a Tinybird Workspace from any MCP client.",
        link: "https://github.com/tinybirdco/mcp-tinybird",
        category: ["cli", "terminal", "command"]
    }, {
        id: 126,
        title: "Mcp Server Chart Tool",
        name: "antvis/mcp-server-chart",
        description: "A Model Context Protocol server for generating visual charts using AntV.",
        link: "https://github.com/antvis/mcp-server-chart",
        category: ["art", "culture", "media"]
    }, {
        id: 127,
        title: "🐍 ☁️ 🎖️ - Navigate Your Aiven Projects And Interact With The Postgresql®",
        name: "Aiven-Open/mcp-aiven",
        description: "🐍 ☁️ 🎖️ -  Navigate your Aiven projects and interact with the PostgreSQL®, Apache Kafka®, ClickHouse® and OpenSearch® services",
        link: "https://github.com/Aiven-Open/mcp-aiven",
        category: ["database", "storage"]
    }, {
        id: 128,
        title: "Database Integration",
        name: "alexander-zuev/supabase-mcp-server",
        description: "Supabase MCP Server with support for SQL query execution and database exploration tools",
        link: "https://github.com/alexander-zuev/supabase-mcp-server",
        category: ["database", "storage"]
    }, {
        id: 129,
        title: "Alibabacloud Tablestore Mcp Server",
        name: "aliyun/alibabacloud-tablestore-mcp-server",
        description: "MCP service for Tablestore, features include adding documents, semantic search for documents based on vectors and scalars, RAG-friendly, and serverless.",
        link: "https://github.com/aliyun/alibabacloud-tablestore-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 130,
        title: "Mysql Integration",
        name: "benborla/mcp-server-mysql",
        description: "MySQL database integration in NodeJS with configurable access controls and schema inspection",
        link: "https://github.com/benborla/mcp-server-mysql",
        category: ["database", "storage"]
    }, {
        id: 131,
        title: "Database Integration",
        name: "bytebase/dbhub",
        description: "📇 🏠 – Universal database MCP server supporting mainstream databases.",
        link: "https://github.com/bytebase/dbhub",
        category: ["database", "storage"]
    }, {
        id: 132,
        title: "Database Integration",
        name: "c4pt0r/mcp-server-tidb",
        description: "TiDB database integration with schema inspection and query capabilities",
        link: "https://github.com/c4pt0r/mcp-server-tidb",
        category: ["database", "storage"]
    }, {
        id: 133,
        title: "Wren Engine Tool",
        name: "Canner/wren-engine",
        description: "The Semantic Engine for Model Context Protocol(MCP) Clients and AI Agents",
        link: "https://github.com/Canner/wren-engine",
        category: ["cli", "terminal", "command"]
    }, {
        id: 134,
        title: "Postgres Integration",
        name: "centralmind/gateway",
        description: "MCP and MCP SSE Server that automatically generate API based on database schema and data. Supports PostgreSQL, Clickhouse, MySQL, Snowflake, BigQuery, Supabase",
        link: "https://github.com/centralmind/gateway",
        category: ["database", "storage"]
    }, {
        id: 135,
        title: "Dicom Integration To Query",
        name: "ChristianHinge/dicom-mcp",
        description: "DICOM integration to query, read, and move medical images and reports from PACS and other DICOM compliant systems.",
        link: "https://github.com/ChristianHinge/dicom-mcp",
        category: ["multimedia", "processing"]
    }, {
        id: 136,
        title: "Chrom To Access Local And Cloud Chroma Instances For Retrieval Capabilities",
        name: "chroma-core/chroma-mcp",
        description: "Chroma MCP server to access local and cloud Chroma instances for retrieval capabilities",
        link: "https://github.com/chroma-core/chroma-mcp",
        category: ["cloud", "infrastructure"]
    }, {
        id: 137,
        title: "Clickhouse Database Integration With Schema Inspection And Query Capabilities",
        name: "ClickHouse/mcp-clickhouse",
        description: "ClickHouse database integration with schema inspection and query capabilities",
        link: "https://github.com/ClickHouse/mcp-clickhouse",
        category: ["database", "storage"]
    }, {
        id: 138,
        title: "Confluent Integration To Interact With Confluent Kafka And Confluent Cloud Rest Apis",
        name: "confluentinc/mcp-confluent",
        description: "Confluent integration to interact with Confluent Kafka and Confluent Cloud REST APIs.",
        link: "https://github.com/confluentinc/mcp-confluent",
        category: ["cloud", "infrastructure"]
    }, {
        id: 139,
        title: "Mcp Server Couchbase Tool",
        name: "Couchbase-Ecosystem/mcp-server-couchbase",
        description: "Couchbase MCP server provides unfied access to both Capella cloud and self-managed clusters for document operations, SQL++ queries and natural language data analysis.",
        link: "https://github.com/Couchbase-Ecosystem/mcp-server-couchbase",
        category: ["database", "storage"]
    }, {
        id: 140,
        title: "Elasticsearch Integration",
        name: "cr7258/elasticsearch-mcp-server",
        description: "MCP Server implementation that provides Elasticsearch interaction",
        link: "https://github.com/cr7258/elasticsearch-mcp-server",
        category: ["database", "storage"]
    }, {
        id: 141,
        title: "All-in-one For Postgres Development And Operations",
        name: "crystaldba/postgres-mcp",
        description: "All-in-one MCP server for Postgres development and operations, with tools for performance analysis, tuning, and health checks",
        link: "https://github.com/crystaldba/postgres-mcp",
        category: ["database", "storage"]
    }, {
        id: 142,
        title: "Mcp Server Trino Tool",
        name: "Dataring-engineering/mcp-server-trino",
        description: "Trino MCP Server to query and access data from Trino Clusters.",
        link: "https://github.com/Dataring-engineering/mcp-server-trino",
        category: ["database", "storage"]
    }, {
        id: 143,
        title: "A Go Implementation Of A Model Context Protocol () For Trino",
        name: "tuannvm/mcp-trino",
        description: "A Go implementation of a Model Context Protocol (MCP) server for Trino",
        link: "https://github.com/tuannvm/mcp-trino",
        category: ["database", "storage"]
    }, {
        id: 144,
        title: "Mysql Integration",
        name: "designcomputer/mysql_mcp_server",
        description: "MySQL database integration with configurable access controls, schema inspection, and comprehensive security guidelines",
        link: "https://github.com/designcomputer/mysql_mcp_server",
        category: ["database", "storage"]
    }, {
        id: 145,
        title: "Mysql Integration",
        name: "wenb1n-dev/mysql_mcp_server_pro",
        description: "Supports SSE, STDIO; not only limited to MySQL's CRUD functionality; also includes database exception analysis capabilities; controls database permissions based on roles; and makes it easy for developers to extend tools with customization",
        link: "https://github.com/wenb1n-dev/mysql_mcp_server_pro",
        category: ["database", "storage"]
    }, {
        id: 146,
        title: "Database Integration",
        name: "domdomegg/airtable-mcp-server",
        description: "Airtable database integration with schema inspection, read and write capabilities",
        link: "https://github.com/domdomegg/airtable-mcp-server",
        category: ["database", "storage"]
    }, {
        id: 147,
        title: "Database Integration",
        name: "edwinbernadus/nocodb-mcp-server",
        description: "Nocodb database integration, read and write capabilities",
        link: "https://github.com/edwinbernadus/nocodb-mcp-server",
        category: ["database", "storage"]
    }, {
        id: 148,
        title: "Database Integration",
        name: "ergut/mcp-bigquery-server",
        description: "Server implementation for Google BigQuery integration that enables direct BigQuery database access and querying capabilities",
        link: "https://github.com/ergut/mcp-bigquery-server",
        category: ["database", "storage"]
    }, {
        id: 149,
        title: "Mysql Integration",
        name: "f4ww4z/mcp-mysql-server",
        description: "Node.js-based MySQL database integration that provides secure MySQL database operations",
        link: "https://github.com/f4ww4z/mcp-mysql-server",
        category: ["database", "storage"]
    }, {
        id: 150,
        title: "Database Integration",
        name: "fireproof-storage/mcp-database-server",
        description: "Fireproof ledger database with multi-user sync",
        link: "https://github.com/fireproof-storage/mcp-database-server",
        category: ["database", "storage"]
    }, {
        id: 151,
        title: "Mysql Integration",
        name: "FreePeak/db-mcp-server",
        description: "performance multi-database MCP server built with Golang, supporting MySQL & PostgreSQL (NoSQL coming soon). Includes built-in tools for query execution, transaction management, schema exploration, query building, and performance analysis, with seamless Cursor integration for enhanced database workflows.",
        link: "https://github.com/FreePeak/db-mcp-server",
        category: ["database", "storage"]
    }, {
        id: 152,
        title: "Database Integration",
        name: "furey/mongodb-lens",
        description: "MongoDB Lens: Full Featured MCP Server for MongoDB Databases",
        link: "https://github.com/furey/mongodb-lens",
        category: ["database", "storage"]
    }, {
        id: 153,
        title: "Firebase Services Including Auth",
        name: "gannonh/firebase-mcp",
        description: "Firebase services including Auth, Firestore and Storage.",
        link: "https://github.com/gannonh/firebase-mcp",
        category: ["file", "storage"]
    }, {
        id: 154,
        title: "Database Integration",
        name: "get-convex/convex-backend",
        description: "backend 📇 ☁️ - Convex database integration to introspect tables, functions, and run oneoff queries (Source)",
        link: "https://github.com/get-convex/convex-backend/blob/main/npm-packages/convex/src/cli/mcp.ts",
        category: ["database", "storage"]
    }, {
        id: 155,
        title: "Database Integration",
        name: "googleapis/genai-toolbox",
        description: "Open source MCP server specializing in easy, fast, and secure tools for Databases.",
        link: "https://github.com/googleapis/genai-toolbox",
        category: ["database", "storage"]
    }, {
        id: 156,
        title: "Greptimedb Mcp Server Tool",
        name: "GreptimeTeam/greptimedb-mcp-server",
        description: "MCP Server for querying GreptimeDB.",
        link: "https://github.com/GreptimeTeam/greptimedb-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 157,
        title: "Database Integration",
        name: "hannesrudolph/sqlite-explorer-fastmcp-mcp-server",
        description: "An MCP server that provides safe, read-only access to SQLite databases through Model Context Protocol (MCP). This server is built with the FastMCP framework, which enables LLMs to explore and query SQLite databases with built-in safety features and query validation.",
        link: "https://github.com/hannesrudolph/sqlite-explorer-fastmcp-mcp-server",
        category: ["database", "storage"]
    }, {
        id: 158,
        title: "Influxdb Mcp Server Integration",
        name: "idoru/influxdb-mcp-server",
        description: "Run queries against InfluxDB OSS API v2.",
        link: "https://github.com/idoru/influxdb-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 159,
        title: "Mcp Snowflake Server Integration",
        name: "isaacwasserman/mcp-snowflake-server",
        description: "Snowflake integration implementing read and (optional) write operations as well as insight tracking",
        link: "https://github.com/isaacwasserman/mcp-snowflake-server",
        category: ["tools", "integration"]
    }, {
        id: 160,
        title: "Supabase Tool",
        name: "joshuarileydev/supabase",
        description: "Supabase MCP Server for managing and creating projects and organisations in Supabase",
        link: "https://github.com/joshuarileydev/supabase",
        category: ["database", "storage"]
    }, {
        id: 161,
        title: "For Apache Kafka And Timeplus",
        name: "jovezhong/mcp-timeplus",
        description: "MCP server for Apache Kafka and Timeplus. Able to list Kafka topics, poll Kafka messages, save Kafka data locally and query streaming data with SQL via Timeplus",
        link: "https://github.com/jovezhong/mcp-timeplus",
        category: ["database", "storage"]
    }, {
        id: 162,
        title: "Vikingdb Mcp Server Integration",
        name: "KashiwaByte/vikingdb-mcp-server",
        description: "VikingDB integration with collection and index introduction, vector store and search capabilities.",
        link: "https://github.com/KashiwaByte/vikingdb-mcp-server",
        category: ["search", "extraction"]
    }, {
        id: 163,
        title: "Mcp Mongo Server Tool",
        name: "kiliczsh/mcp-mongo-server",
        description: "A Model Context Protocol Server for MongoDB",
        link: "https://github.com/kiliczsh/mcp-mongo-server",
        category: ["database", "storage"]
    }, {
        id: 164,
        title: "Database Integration",
        name: "ktanaka101/mcp-server-duckdb",
        description: "DuckDB database integration with schema inspection and query capabilities",
        link: "https://github.com/ktanaka101/mcp-server-duckdb",
        category: ["database", "storage"]
    }, {
        id: 165,
        title: "Database Integration",
        name: "LucasHild/mcp-server-bigquery",
        description: "BigQuery database integration with schema inspection and query capabilities",
        link: "https://github.com/LucasHild/mcp-server-bigquery",
        category: ["database", "storage"]
    }, {
        id: 166,
        title: "Database Integration",
        name: "quarkiverse/quarkus-mcp-servers",
        description: "Connect to any JDBC-compatible database and query, insert, update, delete, and more.",
        link: "https://github.com/quarkiverse/quarkus-mcp-servers/tree/main/jdbc",
        category: ["database", "storage"]
    }, {
        id: 167,
        title: "Model Context Protocol () That Provides Comprehensive Sqlite Database Interaction Capabilities",
        name: "jparkerweb/mcp-sqlite",
        description: "Model Context Protocol (MCP) server that provides comprehensive SQLite database interaction capabilities.",
        link: "https://github.com/jparkerweb/mcp-sqlite",
        category: ["database", "storage"]
    }, {
        id: 168,
        title: "Memgraph - Includes A Tool To Run A Query Against Memgraph And A Schema Resource",
        name: "memgraph/mcp-memgraph",
        description: "Memgraph MCP Server - includes a tool to run a query against Memgraph and a schema resource.",
        link: "https://github.com/memgraph/mcp-memgraph",
        category: ["tools", "integration"]
    }, {
        id: 169,
        title: "Postgres Integration",
        name: "modelcontextprotocol/servers",
        description: "PostgreSQL database integration with schema inspection and query capabilities",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/postgres",
        category: ["database", "storage"]
    }, {
        id: 170,
        title: "Database Integration",
        name: "modelcontextprotocol/servers",
        description: "SQLite database operations with built-in analysis features",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/sqlite",
        category: ["database", "storage"]
    }, {
        id: 171,
        title: "Model Context Protocol With Neo4j (run Queries",
        name: "neo4j-contrib/mcp-neo4j",
        description: "Model Context Protocol with Neo4j (Run queries, Knowledge Graph Memory, Manaage Neo4j Aura Instances)",
        link: "https://github.com/neo4j-contrib/mcp-neo4j",
        category: ["tools", "integration"]
    }, {
        id: 172,
        title: "Postgres Integration",
        name: "neondatabase/mcp-server-neon",
        description: "📇 ☁️ — An MCP Server for creating and managing Postgres databases using Neon Serverless Postgres",
        link: "https://github.com/neondatabase/mcp-server-neon",
        category: ["database", "storage"]
    }, {
        id: 173,
        title: "Postgres Integration",
        name: "niledatabase/nile-mcp-server",
        description: "Manage and query Postgres databases, tenants, users, auth using LLMs",
        link: "https://github.com/niledatabase/nile-mcp-server",
        category: ["database", "storage"]
    }, {
        id: 174,
        title: "Database Integration",
        name: "OpenLinkSoftware/mcp-odbc-server",
        description: "An MCP server for generic Database Management System (DBMS) Connectivity via the Open Database Connectivity (ODBC) protocol",
        link: "https://github.com/OpenLinkSoftware/mcp-odbc-server",
        category: ["database", "storage"]
    }, {
        id: 175,
        title: "Database Integration",
        name: "OpenLinkSoftware/mcp-sqlalchemy-server",
        description: "An MCP server for generic Database Management System (DBMS) Connectivity via SQLAlchemy using Python ODBC (pyodbc)",
        link: "https://github.com/OpenLinkSoftware/mcp-sqlalchemy-server",
        category: ["database", "storage"]
    }, {
        id: 176,
        title: "Azure Integration",
        name: "pab1it0/adx-mcp-server",
        description: "Query and analyze Azure Data Explorer databases",
        link: "https://github.com/pab1it0/adx-mcp-server",
        category: ["database", "storage"]
    }, {
        id: 177,
        title: "Prometheus Mcp Server Tool",
        name: "pab1it0/prometheus-mcp-server",
        description: "Query and analyze Prometheus, open-source monitoring system.",
        link: "https://github.com/pab1it0/prometheus-mcp-server",
        category: ["monitoring", "observability"]
    }, {
        id: 178,
        title: "Postgres Integration",
        name: "prisma/prisma",
        description: "Gives LLMs the ability to manage Prisma Postgres databases (e.g. spin up new database instances or run schema migrations).",
        link: "https://github.com/prisma/prisma",
        category: ["database", "storage"]
    }, {
        id: 179,
        title: "Mcp Server Qdrant Tool",
        name: "qdrant/mcp-server-qdrant",
        description: "A Qdrant MCP server",
        link: "https://github.com/qdrant/mcp-server-qdrant",
        category: ["tools", "integration"]
    }, {
        id: 180,
        title: "Mongodb Integration That Enables Llms To Interact Directly With Databases",
        name: "QuantGeekDev/mongo-mcp",
        description: "MongoDB integration that enables LLMs to interact directly with databases.",
        link: "https://github.com/QuantGeekDev/mongo-mcp",
        category: ["database", "storage"]
    }, {
        id: 181,
        title: "Connect Ai Tools Directly To Airtable",
        name: "rashidazarang/airtable-mcp",
        description: "Connect AI tools directly to Airtable. Query, create, update, and delete records using natural language. Features include base management, table operations, schema manipulation, record filtering, and data migration through a standardized MCP interface.",
        link: "https://github.com/rashidazarang/airtable-mcp",
        category: ["database", "storage"]
    }, {
        id: 182,
        title: "The Redis Official Offers An Interface To Manage And Search Data In Redis",
        name: "redis/mcp-redis",
        description: "The Redis official MCP Server offers an interface to manage and search data in Redis.",
        link: "https://github.com/redis/mcp-redis",
        category: ["cloud", "infrastructure"]
    }, {
        id: 183,
        title: "Universal Sqlalchemy-based Database Integration Supporting Postgresql",
        name: "runekaagaard/mcp-alchemy",
        description: "Universal SQLAlchemy-based database integration supporting PostgreSQL, MySQL, MariaDB, SQLite, Oracle, MS SQL Server and many more databases. Features schema and relationship inspection, and large dataset analysis capabilities.",
        link: "https://github.com/runekaagaard/mcp-alchemy",
        category: ["database", "storage"]
    }, {
        id: 184,
        title: "Pinecone Integration With Vector Search Capabilities",
        name: "sirmews/mcp-pinecone",
        description: "Pinecone integration with vector search capabilities",
        link: "https://github.com/sirmews/mcp-pinecone",
        category: ["search", "extraction"]
    }, {
        id: 185,
        title: "Less Mariadb Cloud Db",
        name: "skysqlinc/skysql-mcp",
        description: "Serverless MariaDB Cloud DB MCP server. Tools to launch, delete, execute SQL and work with DB level AI agents for accurate text-2-sql and conversations.",
        link: "https://github.com/skysqlinc/skysql-mcp",
        category: ["database", "storage"]
    }, {
        id: 186,
        title: "Official Supabase To Connect Ai Assistants Directly With Your Supabase Project And Allows Them To Perform Tasks Like Managing Tables",
        name: "supabase-community/supabase-mcp",
        description: "Official Supabase MCP server to connect AI assistants directly with your Supabase project and allows them to perform tasks like managing tables, fetching config, and querying data.",
        link: "https://github.com/supabase-community/supabase-mcp",
        category: ["database", "storage"]
    }, {
        id: 187,
        title: "🐍 🏠 Universal Database Supporting Multiple Database Types Including Postgresql",
        name: "TheRaLabs/legion-mcp",
        description: "🐍 🏠 Universal database MCP server supporting multiple database types including PostgreSQL, Redshift, CockroachDB, MySQL, RDS MySQL, Microsoft SQL Server, BigQuery, Oracle DB, and SQLite.",
        link: "https://github.com/TheRaLabs/legion-mcp",
        category: ["database", "storage"]
    }, {
        id: 188,
        title: "Database Integration",
        name: "tradercjz/dolphindb-mcp-server",
        description: "TDolphinDB database integration with schema inspection and query capabilities",
        link: "https://github.com/tradercjz/dolphindb-mcp-server",
        category: ["database", "storage"]
    }, {
        id: 189,
        title: "Mcp Server Weaviate Tool",
        name: "weaviate/mcp-server-weaviate",
        description: "An MCP Server to connect to your Weaviate collections as a knowledge base as well as using Weaviate as a chat memory store.",
        link: "https://github.com/weaviate/mcp-server-weaviate",
        category: ["tools", "integration"]
    }, {
        id: 190,
        title: "Xiyan Mcp Server Tool",
        name: "XGenerationLab/xiyan_mcp_server",
        description: "to-SQL LLM.",
        link: "https://github.com/XGenerationLab/xiyan_mcp_server",
        category: ["database", "storage"]
    }, {
        id: 191,
        title: "Mcp Google Sheets Integration",
        name: "xing5/mcp-google-sheets",
        description: "A Model Context Protocol server for interacting with Google Sheets. This server provides tools to create, read, update, and manage spreadsheets through the Google Sheets API.",
        link: "https://github.com/xing5/mcp-google-sheets",
        category: ["tools", "integration"]
    }, {
        id: 192,
        title: "Mysql Integration",
        name: "Zhwt/go-mcp-mysql",
        description: "🏎️ 🏠 – Easy to use, zero dependency MySQL MCP server built with Golang with configurable readonly mode and schema inspection.",
        link: "https://github.com/Zhwt/go-mcp-mysql",
        category: ["database", "storage"]
    }, {
        id: 193,
        title: "For Interacting With Ydb Databases",
        name: "ydb-platform/ydb-mcp",
        description: "MCP server for interacting with YDB databases",
        link: "https://github.com/ydb-platform/ydb-mcp",
        category: ["database", "storage"]
    }, {
        id: 194,
        title: "Database Integration",
        name: "zilliztech/mcp-server-milvus",
        description: "MCP Server for Milvus / Zilliz, making it possible to interact with your database.",
        link: "https://github.com/zilliztech/mcp-server-milvus",
        category: ["database", "storage"]
    }, {
        id: 195,
        title: "Database Integration",
        name: "OpenLinkSoftware/mcp-jdbc-server",
        description: "An MCP server for generic Database Management System (DBMS) Connectivity via the Java Database Connectivity (JDBC) protocol",
        link: "https://github.com/OpenLinkSoftware/mcp-jdbc-server",
        category: ["database", "storage"]
    }, {
        id: 196,
        title: "Database Integration",
        name: "yincongcyincong/VictoriaMetrics-mcp-server",
        description: "An MCP server for interacting with VictoriaMetrics database.",
        link: "https://github.com/yincongcyincong/VictoriaMetrics-mcp-server",
        category: ["database", "storage"]
    }, {
        id: 197,
        title: "Hydrolix Time-series Datalake Integration Providing Schema Exploration And Query Capabilities To Llm-based Workflows",
        name: "hydrolix/mcp-hydrolix",
        description: "Hydrolix time-series datalake integration providing schema exploration and query capabilities to LLM-based workflows.",
        link: "https://github.com/hydrolix/mcp-hydrolix",
        category: ["tools", "integration"]
    }, {
        id: 198,
        title: "Mysql Integration",
        name: "dave-wind/mysql-mcp-server",
        description: "friendly read-only mysql mcp server for cursor and n8n...",
        link: "https://github.com/dave-wind/mysql-mcp-server",
        category: ["database", "storage"]
    }, {
        id: 199,
        title: "Mcp Flowcore Platform Tool",
        name: "flowcore-io/mcp-flowcore-platform",
        description: "Interact with Flowcore to perform actions, ingest data, and analyse, cross reference and utilise any data in your data cores, or in public data cores; all with human language.",
        link: "https://github.com/flowcore-io/mcp-flowcore-platform",
        category: ["translation", "language"]
    }, {
        id: 200,
        title: "Mcp Databricks Server Integration",
        name: "JordiNeil/mcp-databricks-server",
        description: "Connect to Databricks API, allowing LLMs to run SQL queries, list jobs, and get job status.",
        link: "https://github.com/JordiNeil/mcp-databricks-server",
        category: ["database", "storage"]
    }, {
        id: 201,
        title: "Databricks Genie Mcp Integration",
        name: "yashshingvi/databricks-genie-MCP",
        description: "A server that connects to the Databricks Genie API, allowing LLMs to ask natural language questions, run SQL queries, and interact with Databricks conversational agents.",
        link: "https://github.com/yashshingvi/databricks-genie-MCP",
        category: ["database", "storage"]
    }, {
        id: 202,
        title: "For Qlik Cloud Api That Enables Querying Applications",
        name: "jwaxman19/qlik-mcp",
        description: "MCP Server for Qlik Cloud API that enables querying applications, sheets, and extracting data from visualizations with comprehensive authentication and rate limiting support.",
        link: "https://github.com/jwaxman19/qlik-mcp",
        category: ["cloud", "infrastructure"]
    }, {
        id: 203,
        title: "Keboola Mcp Server Integration",
        name: "keboola/keboola-mcp-server",
        description: "interact with Keboola Connection Data Platform. This server provides tools for listing and accessing data from Keboola Storage API.",
        link: "https://github.com/keboola/keboola-mcp-server",
        category: ["file", "storage"]
    }, {
        id: 204,
        title: "Official For Dbt (data Build Tool) Providing Integration With Dbt Core/cloud Cli",
        name: "dbt-labs/dbt-mcp",
        description: "Official MCP server for dbt (data build tool) providing integration with dbt Core/Cloud CLI, project metadata discovery, model information, and semantic layer querying capabilities.",
        link: "https://github.com/dbt-labs/dbt-mcp",
        category: ["cloud", "infrastructure"]
    }, {
        id: 205,
        title: "Dbt Docs Mcp Tool",
        name: "mattijsdp/dbt-docs-mcp",
        description: "MCP server for dbt-core (OSS) users as the official dbt MCP only supports dbt Cloud. Supports project metadata, model and column-level lineage and dbt documentation.",
        link: "https://github.com/mattijsdp/dbt-docs-mcp",
        category: ["cloud", "infrastructure"]
    }, {
        id: 206,
        title: "Mac Monitor Mcp Tool",
        name: "Pratyay/mac-monitor-mcp",
        description: "Identifies resource-intensive processes on macOS and provides performance improvement suggestions.",
        link: "https://github.com/Pratyay/mac-monitor-mcp",
        category: ["monitoring", "observability"]
    }, {
        id: 207,
        title: "Create Crafted Ui Components Inspired By The Best 21st",
        name: "21st-dev/magic-mcp",
        description: "Create crafted UI components inspired by the best 21st.dev design engineers.",
        link: "https://github.com/21st-dev/magic-mcp",
        category: ["tools", "integration"]
    }, {
        id: 208,
        title: "Integration With Qa Sphere Test Management System",
        name: "Hypersequent/qasphere-mcp",
        description: "Integration with QA Sphere test management system, enabling LLMs to discover, summarize, and interact with test cases directly from AI-powered IDEs",
        link: "https://github.com/Hypersequent/qasphere-mcp",
        category: ["tools", "integration"]
    }, {
        id: 209,
        title: "Analyzes Your Codebase Identifying Important Files Based On Dependency Relationships",
        name: "admica/FileScopeMCP",
        description: "Analyzes your codebase identifying important files based on dependency relationships. Generates diagrams and importance scores, helping AI assistants understand the codebase.",
        link: "https://github.com/admica/FileScopeMCP",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 210,
        title: "📇 🏠 🍎 Implementation For Ios Simulator Control",
        name: "ambar/simctl-mcp",
        description: "📇 🏠 🍎 A MCP server implementation for iOS Simulator control.",
        link: "https://github.com/ambar/simctl-mcp",
        category: ["tools", "integration"]
    }, {
        id: 211,
        title: "🎖️ 📇 🏠 That Support For Querying And Managing All Resource In Apache Apisix",
        name: "api7/apisix-mcp",
        description: "🎖️ 📇 🏠 MCP Server that support for querying and managing all resource in Apache APISIX.",
        link: "https://github.com/api7/apisix-mcp",
        category: ["support", "service"]
    }, {
        id: 212,
        title: "Fastmcp Sonarqube Metrics Tool",
        name: "ArchAI-Labs/fastmcp-sonarqube-metrics",
        description: "A Model Context Protocol (MCP) server that provides a set of tools for retrieving information about SonarQube projects like metrics (actual and historical), issues, health status.",
        link: "https://github.com/ArchAI-Labs/fastmcp-sonarqube-metrics",
        category: ["monitoring", "observability"]
    }, {
        id: 213,
        title: "Seamlessly Integrate Any Api With Ai Agents (with Openapi Schema)",
        name: "automation-ai-labs/mcp-link",
        description: "Seamlessly Integrate Any API with AI Agents (with OpenAPI Schema)",
        link: "https://github.com/automation-ai-labs/mcp-link",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 214,
        title: "React Analyzer Mcp Tool",
        name: "azer/react-analyzer-mcp",
        description: "Analyze React code locally, generate docs / llm.txt for whole project at once",
        link: "https://github.com/azer/react-analyzer-mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 215,
        title: "Pox Mcp Server Tool",
        name: "davidlin2k/pox-mcp-server",
        description: "MCP server for the POX SDN controller to provides network control and management capabilities.",
        link: "https://github.com/davidlin2k/pox-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 216,
        title: "Codelogic Mcp Server Tool",
        name: "CodeLogicIncEngineering/codelogic-mcp-server",
        description: "Official MCP server for CodeLogic, providing access to code dependency analytics, architectural risk analysis, and impact assessment tools.",
        link: "https://github.com/CodeLogicIncEngineering/codelogic-mcp-server",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 217,
        title: "Use Natural Language To Explore Llm Observability",
        name: "comet-ml/opik-mcp",
        description: "Use natural language to explore LLM observability, traces, and monitoring data captured by Opik.",
        link: "https://github.com/comet-ml/opik-mcp",
        category: ["monitoring", "observability"]
    }, {
        id: 218,
        title: "Mcp Server Circleci Tool",
        name: "CircleCI-Public/mcp-server-circleci",
        description: "📇 ☁️ Enable AI Agents to fix build failures from CircleCI.",
        link: "https://github.com/CircleCI-Public/mcp-server-circleci",
        category: ["tools", "integration"]
    }, {
        id: 219,
        title: "🎖️ 📇 ☁️ Enable Ai Agents To Fix Playwright Test Failures Reported To Currents",
        name: "currents-dev/currents-mcp",
        description: "🎖️ 📇 ☁️ Enable AI Agents to fix Playwright test failures reported to Currents.",
        link: "https://github.com/currents-dev/currents-mcp",
        category: ["browser", "automation", "web"]
    }, {
        id: 220,
        title: "Postman Mcp Server Integration",
        name: "delano/postman-mcp-server",
        description: "Interact with Postman API",
        link: "https://github.com/delano/postman-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 221,
        title: "Mcp Server Flipt Tool",
        name: "flipt-io/mcp-server-flipt",
        description: "Enable AI assistants to interact with your feature flags in Flipt.",
        link: "https://github.com/flipt-io/mcp-server-flipt",
        category: ["tools", "integration"]
    }, {
        id: 222,
        title: "Figma Context Mcp Tool",
        name: "GLips/Figma-Context-MCP",
        description: "Provide coding agents direct access to Figma data to help them one-shot design implementation.",
        link: "https://github.com/GLips/Figma-Context-MCP",
        category: ["coding", "agent", "development"]
    }, {
        id: 223,
        title: "Integrates",
        name: "gofireflyio/firefly-mcp",
        description: "Integrates, discovers, manages, and codifies cloud resources with Firefly.",
        link: "https://github.com/gofireflyio/firefly-mcp",
        category: ["cloud", "infrastructure"]
    }, {
        id: 224,
        title: "Rust Docs Mcp Server",
        name: "Govcraft/rust-docs-mcp-server",
        description: "Provides up-to-date documentation context for a specific Rust crate to LLMs via an MCP tool, using semantic search (embeddings) and LLM summarization.",
        link: "https://github.com/Govcraft/rust-docs-mcp-server",
        category: ["file", "storage"]
    }, {
        id: 225,
        title: "Excel Mcp Server Tool",
        name: "haris-musa/excel-mcp-server",
        description: "An Excel manipulation server providing workbook creation, data operations, formatting, and advanced features (charts, pivot tables, formulae).",
        link: "https://github.com/haris-musa/excel-mcp-server",
        category: ["art", "culture", "media"]
    }, {
        id: 226,
        title: "Higress Ops Mcp Server",
        name: "higress-group/higress-ops-mcp-server",
        description: "MCP server that provides comprehensive tools for managing Higress gateway configurations and operations.",
        link: "https://github.com/higress-group/higress-ops-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 227,
        title: "Postmancer Tool",
        name: "hijaz/postmancer",
        description: "A MCP server for replacing Rest Clients like Postman/Insomnia, by allowing your LLM to maintain and use api collections.",
        link: "https://github.com/hijaz/postmancer",
        category: ["cli", "terminal", "command"]
    }, {
        id: 228,
        title: "For Interacting With Go's Language Protocol (gopls) And Benefit From Advanced Go Code Analysis Features",
        name: "hloiseaufcms/mcp-gopls",
        description: "A MCP server for interacting with Go's Language Server Protocol (gopls) and benefit from advanced Go code analysis features.",
        link: "https://github.com/hloiseaufcms/mcp-gopls",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 229,
        title: "For Interacting With Bruno Api Client",
        name: "hungthai1401/bruno-mcp",
        description: "A MCP server for interacting with Bruno API Client.",
        link: "https://github.com/hungthai1401/bruno-mcp",
        category: ["cli", "terminal", "command"]
    }, {
        id: 230,
        title: "Droidmind Tool",
        name: "hyperb1iss/droidmind",
        description: "Control Android devices with AI through MCP, enabling device control, debugging, system analysis, and UI automation with a comprehensive security framework.",
        link: "https://github.com/hyperb1iss/droidmind",
        category: ["security", "authentication"]
    }, {
        id: 231,
        title: "Harmonyos Mcp Server Tool",
        name: "XixianLiang/HarmonyOS-mcp-server",
        description: "Control HarmonyOS-next devices with AI through MCP. Support device control and UI automation.",
        link: "https://github.com/XixianLiang/HarmonyOS-mcp-server",
        category: ["support", "service"]
    }, {
        id: 232,
        title: "Gradle Mcp Server Integration",
        name: "IlyaGulya/gradle-mcp-server",
        description: "Gradle integration using the Gradle Tooling API to inspect projects, execute tasks, and run tests with per-test result reporting",
        link: "https://github.com/IlyaGulya/gradle-mcp-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 233,
        title: "Mcp Image Compression Tool",
        name: "InhiblabCore/mcp-image-compression",
        description: "MCP server for local compression of various image formats.",
        link: "https://github.com/InhiblabCore/mcp-image-compression",
        category: ["multimedia", "processing"]
    }, {
        id: 234,
        title: "Mcp Language Server Tool",
        name: "isaacphi/mcp-language-server",
        description: "MCP Language Server helps MCP enabled clients navigate codebases more easily by giving them access to semantic tools like get definition, references, rename, and diagnostics.",
        link: "https://github.com/isaacphi/mcp-language-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 235,
        title: "Ios Simulator Mcp Tool",
        name: "joshuayoes/ios-simulator-mcp",
        description: "A Model Context Protocol (MCP) server for interacting with iOS simulators. This server allows you to interact with iOS simulators by getting information about them, controlling UI interactions, and inspecting UI elements.",
        link: "https://github.com/joshuayoes/ios-simulator-mcp",
        category: ["tools", "integration"]
    }, {
        id: 236,
        title: "Mcp Server Simulator Ios Idb",
        name: "InditexTech/mcp-server-simulator-ios-idb",
        description: "A Model Context Protocol (MCP) server that enables LLMs to interact with iOS simulators (iPhone, iPad, etc.) through natural language commands.",
        link: "https://github.com/InditexTech/mcp-server-simulator-ios-idb",
        category: ["cli", "terminal", "command"]
    }, {
        id: 237,
        title: "Vercel Ai Docs Mcp",
        name: "IvanAmador/vercel-ai-docs-mcp",
        description: "A Model Context Protocol (MCP) server that provides AI-powered search and querying capabilities for the Vercel AI SDK documentation.",
        link: "https://github.com/IvanAmador/vercel-ai-docs-mcp",
        category: ["file", "storage"]
    }, {
        id: 238,
        title: "Database Integration",
        name: "j4c0bs/mcp-server-sql-analyzer",
        description: "MCP server that provides SQL analysis, linting, and dialect conversion using SQLGlot",
        link: "https://github.com/j4c0bs/mcp-server-sql-analyzer",
        category: ["database", "storage"]
    }, {
        id: 239,
        title: "Claude Debugs For You",
        name: "jasonjmcghee/claude-debugs-for-you",
        description: "An MCP Server and VS Code Extension which enables (language agnostic) automatic debugging via breakpoints and expression evaluation.",
        link: "https://github.com/jasonjmcghee/claude-debugs-for-you",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 240,
        title: "Connect To Jetbrains Ide",
        name: "JetBrains/mcpProxy",
        description: "Connect to JetBrains IDE",
        link: "https://github.com/JetBrains/mcpProxy",
        category: ["aggregator", "integration"]
    }, {
        id: 241,
        title: "Jmeter Mcp Server Tool",
        name: "QAInsights/jmeter-mcp-server",
        description: "JMeter MCP Server for performance testing",
        link: "https://github.com/QAInsights/jmeter-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 242,
        title: "Servemyapi Integration",
        name: "Jktfe/serveMyAPI",
        description: "A personal MCP (Model Context Protocol) server for securely storing and accessing API keys across projects using the macOS Keychain.",
        link: "https://github.com/Jktfe/serveMyAPI",
        category: ["tools", "integration"]
    }, {
        id: 243,
        title: "App Store Connect Mcp Server",
        name: "JoshuaRileyDev/app-store-connect-mcp-server",
        description: "An MCP server to communicate with the App Store Connect API for iOS Developers",
        link: "https://github.com/JoshuaRileyDev/app-store-connect-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 244,
        title: "Simulator Mcp Server Tool",
        name: "JoshuaRileyDev/simulator-mcp-server",
        description: "An MCP server to control iOS Simulators",
        link: "https://github.com/JoshuaRileyDev/simulator-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 245,
        title: "K6 Mcp Server Tool",
        name: "QAInsights/k6-mcp-server",
        description: "Grafana k6 MCP Server for performance testing",
        link: "https://github.com/QAInsights/k6-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 246,
        title: "Mcp Server Multiverse Tool",
        name: "lamemind/mcp-server-multiverse",
        description: "A middleware server that enables multiple isolated instances of the same MCP servers to coexist independently with unique namespaces and configurations.",
        link: "https://github.com/lamemind/mcp-server-multiverse",
        category: ["aggregator", "integration"]
    }, {
        id: 247,
        title: "Mcp Server Langfuse Tool",
        name: "langfuse/mcp-server-langfuse",
        description: "MCP server to access and manage LLM application prompts created with Langfuse Prompt Management.",
        link: "https://github.com/langfuse/mcp-server-langfuse",
        category: ["tools", "integration"]
    }, {
        id: 248,
        title: "For Android/ios Application And Device Automation",
        name: "mobile-next/mobile-mcp",
        description: "MCP Server for Android/iOS application and device automation, development and app scraping. Simulator/Emulator/Physical devices like iPhone, Google Pixel, Samsung supported.",
        link: "https://github.com/mobile-next/mobile-mcp",
        category: ["coding", "agent", "development"]
    }, {
        id: 249,
        title: "Locust Mcp Server Tool",
        name: "QAInsights/locust-mcp-server",
        description: "Locust MCP Server for performance testing",
        link: "https://github.com/QAInsights/locust-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 250,
        title: "User Feedback Mcp Tool",
        name: "mrexodia/user-feedback-mcp",
        description: "Simple MCP Server to enable a human-in-the-loop workflow in tools like Cline and Cursor.",
        link: "https://github.com/mrexodia/user-feedback-mcp",
        category: ["cli", "terminal", "command"]
    }, {
        id: 251,
        title: "That Uses Gitingest To Convert Any Git Repository Into A Simple Text Digest Of Its Codebase",
        name: "narumiruna/gitingest-mcp",
        description: "A MCP server that uses gitingest to convert any Git repository into a simple text digest of its codebase.",
        link: "https://github.com/narumiruna/gitingest-mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 252,
        title: "Lets Your Preferred Ai Agent Create & Run Fully Managed Octomind End-to-end Tests From Your Codebase Or Other Data Sources Like Jira",
        name: "OctoMind-dev/octomind-mcp",
        description: "lets your preferred AI agent create & run fully managed Octomind end-to-end tests from your codebase or other data sources like Jira, Slack or TestRail.",
        link: "https://github.com/OctoMind-dev/octomind-mcp",
        category: ["communication", "messaging"]
    }, {
        id: 253,
        title: "Mcp Openapi Schema Explorer",
        name: "kadykov/mcp-openapi-schema-explorer",
        description: "Token-efficient access to OpenAPI/Swagger specs via MCP Resources.",
        link: "https://github.com/kadykov/mcp-openapi-schema-explorer",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 254,
        title: "Website Downloader Tool",
        name: "pskill9/website-downloader",
        description: "This MCP server provides a tool to download entire websites using wget. It preserves the website structure and converts links to work locally.",
        link: "https://github.com/pskill9/website-downloader",
        category: ["tools", "integration"]
    }, {
        id: 255,
        title: "Providing Accurate Information About Nixos Packages",
        name: "utensils/mcp-nixos",
        description: "MCP server providing accurate information about NixOS packages, system options, Home Manager configurations, and nix-darwin macOS settings to prevent AI hallucinations.",
        link: "https://github.com/utensils/mcp-nixos",
        category: ["tools", "integration"]
    }, {
        id: 256,
        title: "Docker Container Management And Operations Through",
        name: "QuantGeekDev/docker-mcp",
        description: "Docker container management and operations through MCP",
        link: "https://github.com/QuantGeekDev/docker-mcp",
        category: ["cloud", "infrastructure"]
    }, {
        id: 257,
        title: "Mcp Server Docker Tool",
        name: "ckreiling/mcp-server-docker",
        description: "Integrate with Docker to manage containers, images, volumes, and networks.",
        link: "https://github.com/ckreiling/mcp-server-docker",
        category: ["cloud", "infrastructure"]
    }, {
        id: 258,
        title: "Xcode Mcp Server Integration",
        name: "r-huijts/xcode-mcp-server",
        description: "Xcode integration for project management, file operations, and build automation",
        link: "https://github.com/r-huijts/xcode-mcp-server",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 259,
        title: "That Lets Llms Know Everything About Your Openapi Specifications To Discover",
        name: "ReAPI-com/mcp-openapi",
        description: "MCP server that lets LLMs know everything about your OpenAPI specifications to discover, explain and generate code/mock data",
        link: "https://github.com/ReAPI-com/mcp-openapi",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 260,
        title: "Rootly Mcp Server Tool",
        name: "Rootly-AI-Labs/Rootly-MCP-server",
        description: "MCP server for the incident management platform Rootly.",
        link: "https://github.com/Rootly-AI-Labs/Rootly-MCP-server",
        category: ["tools", "integration"]
    }, {
        id: 261,
        title: "Mcp Package Version Tool",
        name: "sammcj/mcp-package-version",
        description: "An MCP Server to help LLMs suggest the latest stable package versions when writing code.",
        link: "https://github.com/sammcj/mcp-package-version",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 262,
        title: "Sonarqube Mcp Server Tool",
        name: "sapientpants/sonarqube-mcp-server",
        description: "A Model Context Protocol (MCP) server that integrates with SonarQube to provide AI assistants with access to code quality metrics, issues, and quality gate statuses",
        link: "https://github.com/sapientpants/sonarqube-mcp-server",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 263,
        title: "Mcp Claude Code Tool",
        name: "SDGLBL/mcp-claude-code",
        description: "An implementation of Claude Code capabilities using MCP, enabling AI code understanding, modification, and project analysis with comprehensive tool support.",
        link: "https://github.com/SDGLBL/mcp-claude-code",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 264,
        title: "Openapi Mcp Server Integration",
        name: "snaggle-ai/openapi-mcp-server",
        description: "Connect any HTTP/REST API server using an Open API spec (v3)",
        link: "https://github.com/snaggle-ai/openapi-mcp-server",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 265,
        title: "For Lldb Enabling Ai Binary And Core File Analysis",
        name: "stass/lldb-mcp",
        description: "A MCP server for LLDB enabling AI binary and core file analysis, debugging, disassembling.",
        link: "https://github.com/stass/lldb-mcp",
        category: ["file", "storage"]
    }, {
        id: 266,
        title: "Edgeone Pages Mcp Tool",
        name: "TencentEdgeOne/edgeone-pages-mcp",
        description: "An MCP service for deploying HTML content to EdgeOne Pages and obtaining a publicly accessible URL.",
        link: "https://github.com/TencentEdgeOne/edgeone-pages-mcp",
        category: ["support", "service"]
    }, {
        id: 267,
        title: "Mcp Text Editor Tool",
        name: "tumf/mcp-text-editor",
        description: "A line-oriented text file editor. Optimized for LLM tools with efficient partial file access to minimize token usage.",
        link: "https://github.com/tumf/mcp-text-editor",
        category: ["communication", "messaging"]
    }, {
        id: 268,
        title: "For Seamless Document Format Conversion Using Pandoc",
        name: "vivekVells/mcp-pandoc",
        description: "MCP server for seamless document format conversion using Pandoc, supporting Markdown, HTML, PDF, DOCX (.docx), csv and more.",
        link: "https://github.com/vivekVells/mcp-pandoc",
        category: ["file", "storage"]
    }, {
        id: 269,
        title: "Connect To Vscode Ide And Use Semantic Tools Like Find_usages",
        name: "biegehydra/BifrostMCP",
        description: "Connect to VSCode ide and use semantic tools like find_usages",
        link: "https://github.com/biegehydra/BifrostMCP",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 270,
        title: "Xcodebuild Tool",
        name: "ShenghaiWang/xcodebuild",
        description: "🍎 Build iOS Xcode workspace/project and feed back errors to llm.",
        link: "https://github.com/ShenghaiWang/xcodebuild",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 271,
        title: "Jvm Mcp Server Tool",
        name: "xzq-xu/jvm-mcp-server",
        description: "An implementation project of a JVM-based MCP (Model Context Protocol) server.",
        link: "https://github.com/xzq-xu/jvm-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 272,
        title: "Mcp Server Apache Airflow",
        name: "yangkyeongmo/mcp-server-apache-airflow",
        description: "MCP server that connects to Apache Airflow using official client.",
        link: "https://github.com/yangkyeongmo/mcp-server-apache-airflow",
        category: ["cli", "terminal", "command"]
    }, {
        id: 273,
        title: "Mindmap Mcp Server Tool",
        name: "YuChenSSR/mindmap-mcp-server",
        description: "A Model Context Protocol (MCP) server for generating a beautiful interactive mindmap.",
        link: "https://github.com/YuChenSSR/mindmap-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 274,
        title: "Multi Ai Advisor Mcp",
        name: "YuChenSSR/multi-ai-advisor-mcp",
        description: "A Model Context Protocol (MCP) server that queries multiple Ollama models and combines their responses, providing diverse AI perspectives on a single question.",
        link: "https://github.com/YuChenSSR/multi-ai-advisor-mcp",
        category: ["aggregator", "integration"]
    }, {
        id: 275,
        title: "That Provides Typescript Api Information Efficiently To The Agent To Enable It To Work With Untrained Apis",
        name: "yWorks/mcp-typescribe",
        description: "MCP server that provides Typescript API information efficiently to the agent to enable it to work with untrained APIs",
        link: "https://github.com/yWorks/mcp-typescribe",
        category: ["tools", "integration"]
    }, {
        id: 276,
        title: "To Flexibly Fetch Json",
        name: "zcaceres/fetch-mcp",
        description: "An MCP server to flexibly fetch JSON, text, and HTML data",
        link: "https://github.com/zcaceres/fetch-mcp",
        category: ["tools", "integration"]
    }, {
        id: 277,
        title: "To Connect With Your Zenml Mlops And Llmops Pipelines",
        name: "zenml-io/mcp-zenml",
        description: "An MCP server to connect with your ZenML MLOps and LLMOps pipelines",
        link: "https://github.com/zenml-io/mcp-zenml",
        category: ["communication", "messaging"]
    }, {
        id: 278,
        title: "Git Mcp Tool",
        name: "idosal/git-mcp",
        description: "gitmcp.io is a generic remote MCP server to connect to ANY GitHub repository or project for documentation",
        link: "https://github.com/idosal/git-mcp",
        category: ["file", "storage"]
    }, {
        id: 279,
        title: "For Interacting With Bugsnag",
        name: "tgeselle/bugsnag-mcp",
        description: "An MCP server for interacting with Bugsnag",
        link: "https://github.com/tgeselle/bugsnag-mcp",
        category: ["tools", "integration"]
    }, {
        id: 280,
        title: "For Csv Files",
        name: "JordanDalton/RestCsvMcpServer",
        description: "An MCP server for CSV files.",
        link: "https://github.com/JordanDalton/RestCsvMcpServer",
        category: ["file", "storage"]
    }, {
        id: 281,
        title: "Mcp Shrimp Task Manager",
        name: "cjo4m06/mcp-shrimp-task-manager",
        description: "focused task management system that boosts coding agents like Cursor AI with advanced task memory, self-reflection, and dependency management. ShrimpTaskManager",
        link: "https://github.com/cjo4m06/mcp-shrimp-task-manager",
        category: ["coding", "agent", "development"]
    }, {
        id: 282,
        title: "Mcp Code Runner Tool",
        name: "axliupore/mcp-code-runner",
        description: "An MCP server for running code locally via Docker and supporting multiple programming languages.",
        link: "https://github.com/axliupore/mcp-code-runner",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 283,
        title: "Godoc Mcp Server Tool",
        name: "yikakia/godoc-mcp-server",
        description: "Query Go package information on pkg.go.dev",
        link: "https://github.com/yikakia/godoc-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 284,
        title: "A Zero-configuration Go Library To Automatically Expose Existing Gin Web Framework Apis As Tools",
        name: "ckanthony/gin-mcp",
        description: "A zero-configuration Go library to automatically expose existing Gin web framework APIs as MCP tools.",
        link: "https://github.com/ckanthony/gin-mcp",
        category: ["tools", "integration"]
    }, {
        id: 285,
        title: "Github Repo Mcp Tool",
        name: "Ryan0204/github-repo-mcp",
        description: "GitHub Repo MCP allow your AI assistants browse GitHub repositories, explore directories, and view file contents.",
        link: "https://github.com/Ryan0204/github-repo-mcp",
        category: ["file", "storage"]
    }, {
        id: 286,
        title: "Webhook Tester Mcp Tool",
        name: "alimo7amed93/webhook-tester-mcp",
        description: "based server for interacting with webhook-test.com. Enables users to create, retrieve, and delete webhooks locally using Claude.",
        link: "https://github.com/alimo7amed93/webhook-tester-mcp",
        category: ["tools", "integration"]
    }, {
        id: 287,
        title: "Ros Mcp Server Tool",
        name: "lpigeon/ros-mcp-server",
        description: "The ROS MCP Server supports robot control by converting user-issued natural language commands into ROS or ROS2 control commands.",
        link: "https://github.com/lpigeon/ros-mcp-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 288,
        title: "Globalping Mcp Server Tool",
        name: "jsdelivr/globalping-mcp-server",
        description: "The Globalping MCP server provides users and LLMs access to run network tools like ping, traceroute, mtr, HTTP and DNS resolve from thousands of locations around the world.",
        link: "https://github.com/jsdelivr/globalping-mcp-server",
        category: ["location", "maps"]
    }, {
        id: 289,
        title: "Doordash Mcp Server Tool",
        name: "JordanDalton/DoorDash-MCP-Server",
        description: "🐍 – DoorDash Delivery (Unofficial)",
        link: "https://github.com/JordanDalton/DoorDash-MCP-Server",
        category: ["tools", "integration"]
    }, {
        id: 290,
        title: "Predict Anything With Chronulus Ai Forecasting And Prediction Agents",
        name: "ChronulusAI/chronulus-mcp",
        description: "Predict anything with Chronulus AI forecasting and prediction agents.",
        link: "https://github.com/ChronulusAI/chronulus-mcp",
        category: ["tools", "integration"]
    }, {
        id: 291,
        title: "Mcp Server Data Exploration",
        name: "reading-plus-ai/mcp-server-data-exploration",
        description: "Enables autonomous data exploration on .csv-based datasets, providing intelligent insights with minimal effort.",
        link: "https://github.com/reading-plus-ai/mcp-server-data-exploration",
        category: ["tools", "integration"]
    }, {
        id: 292,
        title: "To Convert Almost Any File Or Web Content Into Markdown",
        name: "zcaceres/markdownify-mcp",
        description: "An MCP server to convert almost any file or web content into Markdown",
        link: "https://github.com/zcaceres/markdownify-mcp",
        category: ["file", "storage"]
    }, {
        id: 293,
        title: "Jupyter Mcp Server Tool",
        name: "datalayer/jupyter-mcp-server",
        description: "Model Context Protocol (MCP) Server for Jupyter.",
        link: "https://github.com/datalayer/jupyter-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 294,
        title: "Jupyter Notebook Mcp Tool",
        name: "jjsantos01/jupyter-notebook-mcp",
        description: "connects Jupyter Notebook to Claude AI, allowing Claude to directly interact with and control Jupyter Notebooks.",
        link: "https://github.com/jjsantos01/jupyter-notebook-mcp",
        category: ["tools", "integration"]
    }, {
        id: 295,
        title: "Connects To Kaggle",
        name: "arrismo/kaggle-mcp",
        description: "Connects to Kaggle, ability to download and analyze datasets.",
        link: "https://github.com/arrismo/kaggle-mcp",
        category: ["tools", "integration"]
    }, {
        id: 296,
        title: "Zaturn Tool",
        name: "kdqed/zaturn",
        description: "Link multiple data sources (SQL, CSV, Parquet, etc.) and ask AI to analyze the data for insights and visualizations.",
        link: "https://github.com/kdqed/zaturn",
        category: ["database", "storage"]
    }, {
        id: 297,
        title: "Vizro Tool",
        name: "mckinsey/vizro",
        description: "Tools and templates to create validated and maintainable data charts and dashboards.",
        link: "https://github.com/mckinsey/vizro/tree/main/vizro-mcp",
        category: ["art", "culture", "media"]
    }, {
        id: 298,
        title: "🎖️ 📇 🏠 🪟 🐧 🍎 — Tools For Creating And Interacting With Growthbook Feature Flags And Experiments",
        name: "growthbook/growthbook-mcp",
        description: "🎖️ 📇 🏠 🪟 🐧 🍎 — Tools for creating and interacting with GrowthBook feature flags and experiments.",
        link: "https://github.com/growthbook/growthbook-mcp",
        category: ["tools", "integration"]
    }, {
        id: 299,
        title: "Workflow For Fixing Build Issues In Esp32 Series Chips Using Esp-idf",
        name: "horw/esp-mcp",
        description: "Workflow for fixing build issues in ESP32 series chips using ESP-IDF.",
        link: "https://github.com/horw/esp-mcp",
        category: ["tools", "integration"]
    }, {
        id: 300,
        title: "That Standardizes And Contextualizes Industrial Modbus Data",
        name: "kukapay/modbus-mcp",
        description: "An MCP server that standardizes and contextualizes industrial Modbus data.",
        link: "https://github.com/kukapay/modbus-mcp",
        category: ["tools", "integration"]
    }, {
        id: 301,
        title: "That Connects To Opc Ua-enabled Industrial Systems",
        name: "kukapay/opcua-mcp",
        description: "An MCP server that connects to OPC UA-enabled industrial systems.",
        link: "https://github.com/kukapay/opcua-mcp",
        category: ["tools", "integration"]
    }, {
        id: 302,
        title: "For Gnu Radio That Enables Llms To Autonomously Create And Modify Rf",
        name: "yoelbassin/gnuradioMCP",
        description: "An MCP server for GNU Radio that enables LLMs to autonomously create and modify RF .grc flowcharts.",
        link: "https://github.com/yoelbassin/gnuradioMCP",
        category: ["art", "culture", "media"]
    }, {
        id: 303,
        title: "Llm Context.Py Tool",
        name: "cyberchitta/llm-context.py",
        description: "Share code context with LLMs via MCP or clipboard",
        link: "https://github.com/cyberchitta/llm-context.py",
        category: ["cli", "terminal", "command"]
    }, {
        id: 304,
        title: "File System Manager",
        name: "exoticknight/mcp-file-merger",
        description: "File merger tool, suitable for AI chat length limits.",
        link: "https://github.com/exoticknight/mcp-file-merger",
        category: ["file", "storage"]
    }, {
        id: 305,
        title: "Quarkus Mcp Servers Tool",
        name: "quarkiverse/quarkus-mcp-servers",
        description: "A filesystem allowing for browsing and editing files implemented in Java using Quarkus. Available as jar or native image.",
        link: "https://github.com/quarkiverse/quarkus-mcp-servers/tree/main/filesystem",
        category: ["file", "storage"]
    }, {
        id: 306,
        title: "Box Mcp Server Integration",
        name: "hmk/box-mcp-server",
        description: "Box integration for listing, reading and searching files",
        link: "https://github.com/hmk/box-mcp-server",
        category: ["file", "storage"]
    }, {
        id: 307,
        title: "Mcp Everything Search Tool",
        name: "mamertofabian/mcp-everything-search",
        description: "Fast Windows file search using Everything SDK",
        link: "https://github.com/mamertofabian/mcp-everything-search",
        category: ["file", "storage"]
    }, {
        id: 308,
        title: "File System Manager",
        name: "mark3labs/mcp-filesystem-server",
        description: "Golang implementation for local file system access.",
        link: "https://github.com/mark3labs/mcp-filesystem-server",
        category: ["file", "storage"]
    }, {
        id: 309,
        title: "Azure Integration",
        name: "mickael-kerjean/filestash",
        description: "Remote Storage Access: SFTP, S3, FTP, SMB, NFS, WebDAV, GIT, FTPS, gcloud, azure blob, sharepoint, etc.",
        link: "https://github.com/mickael-kerjean/filestash/tree/master/server/plugin/plg_handler_mcp",
        category: ["cloud", "infrastructure"]
    }, {
        id: 310,
        title: "Markitdown Tool",
        name: "microsoft/markitdown",
        description: "MCP tool access to MarkItDown -- a library that converts many file formats (local or remote) to Markdown for LLM consumption.",
        link: "https://github.com/microsoft/markitdown/tree/main/packages/markitdown-mcp",
        category: ["file", "storage"]
    }, {
        id: 311,
        title: "File System Manager",
        name: "modelcontextprotocol/servers",
        description: "Direct local file system access.",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem",
        category: ["file", "storage"]
    }, {
        id: 312,
        title: "Servers Integration",
        name: "modelcontextprotocol/servers",
        description: "Google Drive integration for listing, reading, and searching files",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/gdrive",
        category: ["file", "storage"]
    }, {
        id: 313,
        title: "Mcp Server Opendal Tool",
        name: "Xuanwo/mcp-server-opendal",
        description: "Access any storage with Apache OpenDAL™",
        link: "https://github.com/Xuanwo/mcp-server-opendal",
        category: ["file", "storage"]
    }, {
        id: 314,
        title: "Web3 Research Mcp Tool",
        name: "aaronjmars/web3-research-mcp",
        description: "Deep Research for crypto - free & fully local",
        link: "https://github.com/aaronjmars/web3-research-mcp",
        category: ["search", "extraction"]
    }, {
        id: 315,
        title: "Alchemy Mcp Server Integration",
        name: "alchemyplatform/alchemy-mcp-server",
        description: "Allow AI agents to interact with Alchemy's blockchain APIs.",
        link: "https://github.com/alchemyplatform/alchemy-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 316,
        title: "Octagon Mcp Server Tool",
        name: "OctagonAI/octagon-mcp-server",
        description: "Octagon AI Agents to integrate private and public market data",
        link: "https://github.com/OctagonAI/octagon-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 317,
        title: "Coinmarket Mcp Server Integration",
        name: "anjor/coinmarket-mcp-server",
        description: "Coinmarket API integration to fetch cryptocurrency listings and quotes",
        link: "https://github.com/anjor/coinmarket-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 318,
        title: "Metatrader Mcp Server Tool",
        name: "ariadng/metatrader-mcp-server",
        description: "Enable AI LLMs to execute trades using MetaTrader 5 platform",
        link: "https://github.com/ariadng/metatrader-mcp-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 319,
        title: "Armor Crypto Mcp Tool",
        name: "armorwallet/armor-crypto-mcp",
        description: "MCP to interface with multiple blockchains, staking, DeFi, swap, bridging, wallet management, DCA, Limit Orders, Coin Lookup, Tracking and more.",
        link: "https://github.com/armorwallet/armor-crypto-mcp",
        category: ["aggregator", "integration"]
    }, {
        id: 320,
        title: "Bankless Onchain Api To Interact With Smart Contracts",
        name: "Bankless/onchain-mcp",
        description: "Bankless Onchain API to interact with smart contracts, query transaction and token information",
        link: "https://github.com/Bankless/onchain-mcp/",
        category: ["art", "culture", "media"]
    }, {
        id: 321,
        title: "Base Network Integration For Onchain Tools",
        name: "base/base-mcp",
        description: "Base Network integration for onchain tools, allowing interaction with Base Network and Coinbase API for wallet management, fund transfers, smart contracts, and DeFi operations",
        link: "https://github.com/base/base-mcp",
        category: ["art", "culture", "media"]
    }, {
        id: 322,
        title: "Alpha Vantage Mcp Integration",
        name: "berlinbra/alpha-vantage-mcp",
        description: "Alpha Vantage API integration to fetch both stock and crypto information",
        link: "https://github.com/berlinbra/alpha-vantage-mcp",
        category: ["tools", "integration"]
    }, {
        id: 323,
        title: "Risk Score / Asset Holdings Of Evm Blockchain Address (eoa",
        name: "ahnlabio/bicscan-mcp",
        description: "Risk score / asset holdings of EVM blockchain address (EOA, CA, ENS) and even domain names.",
        link: "https://github.com/ahnlabio/bicscan-mcp",
        category: ["tools", "integration"]
    }, {
        id: 324,
        title: "Bitte Protocol Integration To Run Ai Agents On Several Blockchains",
        name: "BitteProtocol/mcp",
        description: "Bitte Protocol integration to run AI Agents on several blockchains.",
        link: "https://github.com/BitteProtocol/mcp",
        category: ["tools", "integration"]
    }, {
        id: 325,
        title: "Agentkit Tool",
        name: "chargebee/agentkit",
        description: "MCP Server that connects AI agents to Chargebee platform.",
        link: "https://github.com/chargebee/agentkit/tree/main/modelcontextprotocol",
        category: ["tools", "integration"]
    }, {
        id: 326,
        title: "Codex Api Integration For Real-time Enriched Blockchain And Market Data On 60+ Networks",
        name: "Codex-Data/codex-mcp",
        description: "Codex API integration for real-time enriched blockchain and market data on 60+ networks",
        link: "https://github.com/Codex-Data/codex-mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 327,
        title: "Agentkit Integration",
        name: "chargebee/agentkit",
        description: "Coinpaprika's DexPaprika MCP server exposes high-performance DexPaprika API covering 20+ chains and 5M+ tokens with real time pricing, liquidity pool data & historical OHLCV data, providing AI agents standardized access to comprehensive market data through Model Context Protocol.",
        link: "https://github.com/chargebee/agentkit/tree/main/modelcontextprotocol",
        category: ["tools", "integration"]
    }, {
        id: 328,
        title: "Mcp Server Ccxt Tool",
        name: "doggybee/mcp-server-ccxt",
        description: "An MCP server for accessing real-time crypto market data and trading via 20+ exchanges using the CCXT library. Supports spot, futures, OHLCV, balances, orders, and more.",
        link: "https://github.com/doggybee/mcp-server-ccxt",
        category: ["support", "service"]
    }, {
        id: 329,
        title: "Investor Agent Integration",
        name: "ferdousbhai/investor-agent",
        description: "Yahoo Finance integration to fetch stock market data including options recommendations",
        link: "https://github.com/ferdousbhai/investor-agent",
        category: ["finance", "fintech"]
    }, {
        id: 330,
        title: "Tasty Agent Integration",
        name: "ferdousbhai/tasty-agent",
        description: "Tastyworks API integration to handle trading activities on Tastytrade",
        link: "https://github.com/ferdousbhai/tasty-agent",
        category: ["tools", "integration"]
    }, {
        id: 331,
        title: "Wsb Analyst Mcp Integration",
        name: "ferdousbhai/wsb-analyst-mcp",
        description: "Reddit integration to analyze content on WallStreetBets community",
        link: "https://github.com/ferdousbhai/wsb-analyst-mcp",
        category: ["tools", "integration"]
    }, {
        id: 332,
        title: "Nwc Mcp Server Integration",
        name: "getalby/nwc-mcp-server",
        description: "Bitcoin Lightning wallet integration powered by Nostr Wallet Connect",
        link: "https://github.com/getalby/nwc-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 333,
        title: "Heurist Mesh Mcp Server",
        name: "heurist-network/heurist-mesh-mcp-server",
        description: "Access specialized web3 AI agents for blockchain analysis, smart contract security auditing, token metrics evaluation, and on-chain interactions through the Heurist Mesh network. Provides comprehensive tools for DeFi analysis, NFT valuation, and transaction monitoring across multiple blockchains",
        link: "https://github.com/heurist-network/heurist-mesh-mcp-server",
        category: ["art", "culture", "media"]
    }, {
        id: 334,
        title: "Beeper Provides Transactions On Bsc",
        name: "intentos-labs/beeper-mcp",
        description: "Beeper provides transactions on BSC, including balance/token transfers, token swaps in Pancakeswap and beeper reward claims.",
        link: "https://github.com/intentos-labs/beeper-mcp",
        category: ["tools", "integration"]
    }, {
        id: 335,
        title: "That Delivers Blockchain News And In-depth Articles From Blockbeats For Ai Agents",
        name: "kukapay/blockbeats-mcp",
        description: "An MCP server that delivers blockchain news and in-depth articles from BlockBeats for AI agents.",
        link: "https://github.com/kukapay/blockbeats-mcp",
        category: ["art", "culture", "media"]
    }, {
        id: 336,
        title: "Bridge Rates Mcp Tool",
        name: "kukapay/bridge-rates-mcp",
        description: "Delivering real-time cross-chain bridge rates and optimal transfer routes to onchain AI agents.",
        link: "https://github.com/kukapay/bridge-rates-mcp",
        category: ["tools", "integration"]
    }, {
        id: 337,
        title: "Chainlink Feeds Mcp Tool",
        name: "kukapay/chainlink-feeds-mcp",
        description: "Providing real-time access to Chainlink's decentralized on-chain price feeds.",
        link: "https://github.com/kukapay/chainlink-feeds-mcp",
        category: ["tools", "integration"]
    }, {
        id: 338,
        title: "Providing Real-time Access To The Latest News From Cointelegraph",
        name: "kukapay/cointelegraph-mcp",
        description: "Providing real-time access to the latest news from Cointelegraph.",
        link: "https://github.com/kukapay/cointelegraph-mcp",
        category: ["tools", "integration"]
    }, {
        id: 339,
        title: "Crypto Feargreed Mcp Tool",
        name: "kukapay/crypto-feargreed-mcp",
        description: "Providing real-time and historical Crypto Fear & Greed Index data.",
        link: "https://github.com/kukapay/crypto-feargreed-mcp",
        category: ["tools", "integration"]
    }, {
        id: 340,
        title: "Crypto Indicators Mcp Tool",
        name: "kukapay/crypto-indicators-mcp",
        description: "An MCP server providing a range of cryptocurrency technical analysis indicators and strategie.",
        link: "https://github.com/kukapay/crypto-indicators-mcp",
        category: ["tools", "integration"]
    }, {
        id: 341,
        title: "Crypto News Mcp Tool",
        name: "kukapay/crypto-news-mcp",
        description: "An MCP server that provides real-time cryptocurrency news sourced from NewsData for AI agents.",
        link: "https://github.com/kukapay/crypto-news-mcp",
        category: ["tools", "integration"]
    }, {
        id: 342,
        title: "Crypto Portfolio Mcp Tool",
        name: "kukapay/crypto-portfolio-mcp",
        description: "An MCP server for tracking and managing cryptocurrency portfolio allocations.",
        link: "https://github.com/kukapay/crypto-portfolio-mcp",
        category: ["location", "maps"]
    }, {
        id: 343,
        title: "Crypto Rss Mcp Tool",
        name: "kukapay/crypto-rss-mcp",
        description: "An MCP server that aggregates real-time cryptocurrency news from multiple RSS feeds.",
        link: "https://github.com/kukapay/crypto-rss-mcp",
        category: ["aggregator", "integration"]
    }, {
        id: 344,
        title: "Crypto Sentiment Mcp Tool",
        name: "kukapay/crypto-sentiment-mcp",
        description: "An MCP server that delivers cryptocurrency sentiment analysis to AI agents.",
        link: "https://github.com/kukapay/crypto-sentiment-mcp",
        category: ["tools", "integration"]
    }, {
        id: 345,
        title: "Crypto Trending Mcp Tool",
        name: "kukapay/crypto-trending-mcp",
        description: "Tracking the latest trending tokens on CoinGecko.",
        link: "https://github.com/kukapay/crypto-trending-mcp",
        category: ["tools", "integration"]
    }, {
        id: 346,
        title: "Crypto Whitepapers Mcp Tool",
        name: "kukapay/crypto-whitepapers-mcp",
        description: "Serving as a structured knowledge base of crypto whitepapers.",
        link: "https://github.com/kukapay/crypto-whitepapers-mcp",
        category: ["tools", "integration"]
    }, {
        id: 347,
        title: "Cryptopanic Mcp Server Tool",
        name: "kukapay/cryptopanic-mcp-server",
        description: "Providing latest cryptocurrency news to AI agents, powered by CryptoPanic.",
        link: "https://github.com/kukapay/cryptopanic-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 348,
        title: "Defi Yields Mcp Tool",
        name: "kukapay/defi-yields-mcp",
        description: "An MCP server for AI agents to explore DeFi yield opportunities.",
        link: "https://github.com/kukapay/defi-yields-mcp",
        category: ["tools", "integration"]
    }, {
        id: 349,
        title: "Dune Analytics Mcp Tool",
        name: "kukapay/dune-analytics-mcp",
        description: "A mcp server that bridges Dune Analytics data to AI agents.",
        link: "https://github.com/kukapay/dune-analytics-mcp",
        category: ["tools", "integration"]
    }, {
        id: 350,
        title: "Etf Flow Mcp Tool",
        name: "kukapay/etf-flow-mcp",
        description: "Delivering crypto ETF flow data to power AI agents' decision-making.",
        link: "https://github.com/kukapay/etf-flow-mcp",
        category: ["tools", "integration"]
    }, {
        id: 351,
        title: "That Integrates With The Freqtrade Cryptocurrency Trading Bot",
        name: "kukapay/freqtrade-mcp",
        description: "An MCP server that integrates with the Freqtrade cryptocurrency trading bot.",
        link: "https://github.com/kukapay/freqtrade-mcp",
        category: ["tools", "integration"]
    }, {
        id: 352,
        title: "Funding Rates Mcp Tool",
        name: "kukapay/funding-rates-mcp",
        description: "Providing real-time funding rate data across major crypto exchanges.",
        link: "https://github.com/kukapay/funding-rates-mcp",
        category: ["tools", "integration"]
    }, {
        id: 353,
        title: "For Executing Token Swaps On The Solana Blockchain Using Jupiter's New Ultra Api",
        name: "kukapay/jupiter-mcp",
        description: "An MCP server for executing token swaps on the Solana blockchain using Jupiter's new Ultra API.",
        link: "https://github.com/kukapay/jupiter-mcp",
        category: ["tools", "integration"]
    }, {
        id: 354,
        title: "Pancakeswap Poolspy Mcp Tool",
        name: "kukapay/pancakeswap-poolspy-mcp",
        description: "An MCP server that tracks newly created pools on Pancake Swap.",
        link: "https://github.com/kukapay/pancakeswap-poolspy-mcp",
        category: ["tools", "integration"]
    }, {
        id: 355,
        title: "Rug Check Mcp Tool",
        name: "kukapay/rug-check-mcp",
        description: "An MCP server that detects potential risks in Solana meme tokens.",
        link: "https://github.com/kukapay/rug-check-mcp",
        category: ["tools", "integration"]
    }, {
        id: 356,
        title: "That Powers Ai Agents With Indexed Blockchain Data From The Graph",
        name: "kukapay/thegraph-mcp",
        description: "An MCP server that powers AI agents with indexed blockchain data from The Graph.",
        link: "https://github.com/kukapay/thegraph-mcp",
        category: ["tools", "integration"]
    }, {
        id: 357,
        title: "Token Minter Mcp Tool",
        name: "kukapay/token-minter-mcp",
        description: "An MCP server providing tools for AI agents to mint ERC-20 tokens across multiple blockchains.",
        link: "https://github.com/kukapay/token-minter-mcp",
        category: ["aggregator", "integration"]
    }, {
        id: 358,
        title: "Token Revoke Mcp Tool",
        name: "kukapay/token-revoke-mcp",
        description: "An MCP server for checking and revoking ERC-20 token allowances across multiple blockchains.",
        link: "https://github.com/kukapay/token-revoke-mcp",
        category: ["aggregator", "integration"]
    }, {
        id: 359,
        title: "Twitter Username Changes Mcp",
        name: "kukapay/twitter-username-changes-mcp",
        description: "An MCP server that tracks the historical changes of Twitter usernames.",
        link: "https://github.com/kukapay/twitter-username-changes-mcp",
        category: ["social", "media"]
    }, {
        id: 360,
        title: "Uniswap Poolspy Mcp Tool",
        name: "kukapay/uniswap-poolspy-mcp",
        description: "An MCP server that tracks newly created liquidity pools on Uniswap across multiple blockchains.",
        link: "https://github.com/kukapay/uniswap-poolspy-mcp",
        category: ["aggregator", "integration"]
    }, {
        id: 361,
        title: "Uniswap Trader Mcp Tool",
        name: "kukapay/uniswap-trader-mcp",
        description: "An MCP server for AI agents to automate token swaps on Uniswap DEX across multiple blockchains.",
        link: "https://github.com/kukapay/uniswap-trader-mcp",
        category: ["aggregator", "integration"]
    }, {
        id: 362,
        title: "Whale Tracker Mcp Tool",
        name: "kukapay/whale-tracker-mcp",
        description: "A mcp server for tracking cryptocurrency whale transactions.",
        link: "https://github.com/kukapay/whale-tracker-mcp",
        category: ["tools", "integration"]
    }, {
        id: 363,
        title: "For The Alpaca Trading Api To Manage Stock And Crypto Portfolios",
        name: "laukikk/alpaca-mcp",
        description: "An MCP Server for the Alpaca trading API to manage stock and crypto portfolios, place trades, and access market data.",
        link: "https://github.com/laukikk/alpaca-mcp",
        category: ["tools", "integration"]
    }, {
        id: 364,
        title: "Openapi Integration",
        name: "longportapp/openapi",
        description: "🐍 ☁️ - LongPort OpenAPI provides real-time stock market data, provides AI access analysis and trading capabilities through MCP.",
        link: "https://github.com/longportapp/openapi/tree/main/mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 365,
        title: "Evm Mcp Server Tool",
        name: "mcpdotdirect/evm-mcp-server",
        description: "Comprehensive blockchain services for 30+ EVM networks, supporting native tokens, ERC20, NFTs, smart contracts, transactions, and ENS resolution.",
        link: "https://github.com/mcpdotdirect/evm-mcp-server",
        category: ["art", "culture", "media"]
    }, {
        id: 366,
        title: "Starknet Mcp Server Integration",
        name: "mcpdotdirect/starknet-mcp-server",
        description: "Comprehensive Starknet blockchain integration with support for native tokens (ETH, STRK), smart contracts, StarknetID resolution, and token transfers.",
        link: "https://github.com/mcpdotdirect/starknet-mcp-server",
        category: ["art", "culture", "media"]
    }, {
        id: 367,
        title: "Mcp Server Ledger Integration",
        name: "minhyeoky/mcp-server-ledger",
        description: "A ledger-cli integration for managing financial transactions and generating reports.",
        link: "https://github.com/minhyeoky/mcp-server-ledger",
        category: ["cli", "terminal", "command"]
    }, {
        id: 368,
        title: "A Core Banking Integration For Managing Clients",
        name: "openMF/mcp-mifosx",
        description: "A core banking integration for managing clients, loans, savings, shares, financial transactions and generating financial reports.",
        link: "https://github.com/openMF/mcp-mifosx",
        category: ["cli", "terminal", "command"]
    }, {
        id: 369,
        title: "That Uses Yfinance To Obtain Information From Yahoo Finance",
        name: "narumiruna/yfinance-mcp",
        description: "An MCP server that uses yfinance to obtain information from Yahoo Finance.",
        link: "https://github.com/narumiruna/yfinance-mcp",
        category: ["finance", "fintech"]
    }, {
        id: 370,
        title: "That Provides Access To Polygon",
        name: "polygon-io/mcp_polygon",
        description: "An MCP server that provides access to Polygon.io financial market data APIs for stocks, indices, forex, options, and more.",
        link: "https://github.com/polygon-io/mcp_polygon",
        category: ["tools", "integration"]
    }, {
        id: 371,
        title: "Coin Mcp Server Integration",
        name: "pwh-pwh/coin-mcp-server",
        description: "Bitget API to fetch cryptocurrency price.",
        link: "https://github.com/pwh-pwh/coin-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 372,
        title: "Real-time Cryptocurrency Market Data Integration Using Coincap's Public Api",
        name: "QuantGeekDev/coincap-mcp",
        description: "Real-time cryptocurrency market data integration using CoinCap's public API, providing access to crypto prices and market information without API keys",
        link: "https://github.com/QuantGeekDev/coincap-mcp",
        category: ["tools", "integration"]
    }, {
        id: 373,
        title: "Crypto Trader Mcp Claudedesktop.Git",
        name: "SaintDoresh/Crypto-Trader-MCP-ClaudeDesktop.git",
        description: "An MCP tool that provides cryptocurrency market data using the CoinGecko API.",
        link: "https://github.com/SaintDoresh/Crypto-Trader-MCP-ClaudeDesktop.git",
        category: ["git", "version-control"]
    }, {
        id: 374,
        title: "Ts Version Of Yahoo Finance",
        name: "tooyipjee/yahoofinance-mcp.git",
        description: "TS version of yahoo finance mcp.",
        link: "https://github.com/tooyipjee/yahoofinance-mcp.git",
        category: ["finance", "fintech"]
    }, {
        id: 375,
        title: "Yfinance Trader Mcp Claudedesktop.Git",
        name: "SaintDoresh/YFinance-Trader-MCP-ClaudeDesktop.git",
        description: "An MCP tool that provides stock market data and analysis using the Yahoo Finance API.",
        link: "https://github.com/SaintDoresh/YFinance-Trader-MCP-ClaudeDesktop.git",
        category: ["finance", "fintech"]
    }, {
        id: 376,
        title: "For The Xrp Ledger That Provides Access To Account Information",
        name: "RomThpt/mcp-xrpl",
        description: "MCP server for the XRP Ledger that provides access to account information, transaction history, and network data. Allows querying ledger objects, submitting transactions, and monitoring the XRPL network.",
        link: "https://github.com/RomThpt/mcp-xrpl",
        category: ["monitoring", "observability"]
    }, {
        id: 377,
        title: "Real-time On-chain Market Prices Using Open And Free Dexscreener Api",
        name: "janswist/mcp-dexscreener",
        description: "Real-time on-chain market prices using open and free Dexscreener API",
        link: "https://github.com/janswist/mcp-dexscreener",
        category: ["tools", "integration"]
    }, {
        id: 378,
        title: "Mcp Baostock Server Tool",
        name: "HuggingAGI/mcp-baostock-server",
        description: "MCP server based on baostock, providing access and analysis capabilities for Chinese stock market data.",
        link: "https://github.com/HuggingAGI/mcp-baostock-server",
        category: ["tools", "integration"]
    }, {
        id: 379,
        title: "An Tool For Querying Solana Transactions Using Natural Language With Solscan Api",
        name: "wowinter13/solscan-mcp",
        description: "An MCP tool for querying Solana transactions using natural language with Solscan API.",
        link: "https://github.com/wowinter13/solscan-mcp",
        category: ["translation", "language"]
    }, {
        id: 380,
        title: "Mcp Server Wuye Ai",
        name: "wuye-ai/mcp-server-wuye-ai",
        description: "An MCP server that interact with capabilities of the CRIC Wuye AI platform, an intelligent assistant specifically for the property management industry.",
        link: "https://github.com/wuye-ai/mcp-server-wuye-ai",
        category: ["tools", "integration"]
    }, {
        id: 381,
        title: "Findata Mcp Server Tool",
        name: "zlinzzzz/finData-mcp-server",
        description: "An MCP server for accessing professional financial data, supporting multiple data providers such as Tushare.",
        link: "https://github.com/zlinzzzz/finData-mcp-server",
        category: ["support", "service"]
    }, {
        id: 382,
        title: "For Unity Editor And For A Game Made With Unity",
        name: "IvanMurzak/Unity-MCP",
        description: "MCP Server for Unity Editor and for a game made with Unity",
        link: "https://github.com/IvanMurzak/Unity-MCP",
        category: ["gaming", "entertainment"]
    }, {
        id: 383,
        title: "For Unity3d Game Engine Integration For Game Development",
        name: "CoderGamester/mcp-unity",
        description: "MCP Server for Unity3d Game Engine integration for game development",
        link: "https://github.com/CoderGamester/mcp-unity",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 384,
        title: "For Interacting With The Godot Game Engine",
        name: "Coding-Solo/godot-mcp",
        description: "A MCP server for interacting with the Godot game engine, providing tools for editing, running, debugging, and managing scenes in Godot projects.",
        link: "https://github.com/Coding-Solo/godot-mcp",
        category: ["coding", "agent", "development"]
    }, {
        id: 385,
        title: "Access Chess",
        name: "pab1it0/chess-mcp",
        description: "Access Chess.com player data, game records, and other public information through standardized MCP interfaces, allowing AI assistants to search and analyze chess information.",
        link: "https://github.com/pab1it0/chess-mcp",
        category: ["gaming", "entertainment"]
    }, {
        id: 386,
        title: "Playing Chess Against Llms",
        name: "jiayao/mcp-chess",
        description: "A MCP server playing chess against LLMs.",
        link: "https://github.com/jiayao/mcp-chess",
        category: ["tools", "integration"]
    }, {
        id: 387,
        title: "Fantasy Pl Mcp Tool",
        name: "rishijatia/fantasy-pl-mcp",
        description: "An MCP server for real-time Fantasy Premier League data and analysis tools.",
        link: "https://github.com/rishijatia/fantasy-pl-mcp/",
        category: ["tools", "integration"]
    }, {
        id: 388,
        title: "Access Real-time Gaming Data Across Popular Titles Like League Of Legends",
        name: "opgginc/opgg-mcp",
        description: "Access real-time gaming data across popular titles like League of Legends, TFT, and Valorant, offering champion analytics, esports schedules, meta compositions, and character statistics.",
        link: "https://github.com/opgginc/opgg-mcp",
        category: ["gaming", "entertainment"]
    }, {
        id: 389,
        title: "Mcp Server Runescape Tool",
        name: "stefan-xyz/mcp-server-runescape",
        description: "An MCP server with tools for interacting with RuneScape (RS) and Old School RuneScape (OSRS) data, including item prices, player hiscores, and more.",
        link: "https://github.com/stefan-xyz/mcp-server-runescape",
        category: ["tools", "integration"]
    }, {
        id: 390,
        title: "Memorymesh Tool",
        name: "CheMiguel23/MemoryMesh",
        description: "Enhanced graph-based memory with a focus on AI role-play and story generation",
        link: "https://github.com/CheMiguel23/MemoryMesh",
        category: ["tools", "integration"]
    }, {
        id: 391,
        title: "Slack Integration",
        name: "graphlit/graphlit-mcp-server",
        description: "Ingest anything from Slack, Discord, websites, Google Drive, Linear or GitHub into a Graphlit project - and then search and retrieve relevant knowledge within an MCP client like Cursor, Windsurf or Cline.",
        link: "https://github.com/graphlit/graphlit-mcp-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 392,
        title: "Implementation That Provides Tools For Retrieving And Processing Documentation Through Vector Search",
        name: "hannesrudolph/mcp-ragdocs",
        description: "An MCP server implementation that provides tools for retrieving and processing documentation through vector search, enabling AI assistants to augment their responses with relevant documentation context",
        link: "https://github.com/hannesrudolph/mcp-ragdocs",
        category: ["file", "storage"]
    }, {
        id: 393,
        title: "Browser Automation Tool",
        name: "jinzcdev/markmap-mcp-server",
        description: "An MCP server built on markmap that converts Markdown to interactive mind maps. Supports multi-format exports (PNG/JPG/SVG), live browser preview, one-click Markdown copy, and dynamic visualization features.",
        link: "https://github.com/jinzcdev/markmap-mcp-server",
        category: ["browser", "automation", "web"]
    }, {
        id: 394,
        title: "A Connector For Llms To Work With Collections And Sources On Your Zotero Cloud",
        name: "kaliaboi/mcp-zotero",
        description: "A connector for LLMs to work with collections and sources on your Zotero Cloud",
        link: "https://github.com/kaliaboi/mcp-zotero",
        category: ["cloud", "infrastructure"]
    }, {
        id: 395,
        title: "Ai Summarization",
        name: "0xshellming/mcp-summarizer",
        description: "AI Summarization MCP Server, Support for multiple content types: Plain text, Web pages, PDF documents, EPUB books, HTML content",
        link: "https://github.com/0xshellming/mcp-summarizer",
        category: ["cli", "terminal", "command"]
    }, {
        id: 396,
        title: "A Model Context Protocol For Mem0 That Helps Manage Coding Preferences And Patterns",
        name: "mem0ai/mem0-mcp",
        description: "A Model Context Protocol server for Mem0 that helps manage coding preferences and patterns, providing tools for storing, retrieving and semantically handling code implementations, best practices and technical documentation in IDEs like Cursor and Windsurf",
        link: "https://github.com/mem0ai/mem0-mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 397,
        title: "Servers Tool",
        name: "modelcontextprotocol/servers",
        description: "Knowledge graph-based persistent memory system for maintaining context",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/memory",
        category: ["tools", "integration"]
    }, {
        id: 398,
        title: "Connects To Your Pinecone Assistant And Gives The Agent Context From Its Knowledge Engine",
        name: "pinecone-io/assistant-mcp",
        description: "Connects to your Pinecone Assistant and gives the agent context from its knowledge engine.",
        link: "https://github.com/pinecone-io/assistant-mcp",
        category: ["tools", "integration"]
    }, {
        id: 399,
        title: "Ragie Mcp Server Integration",
        name: "ragieai/ragie-mcp-server",
        description: "Retrieve context from your Ragie (RAG) knowledge base connected to integrations like Google Drive, Notion, JIRA and more.",
        link: "https://github.com/ragieai/ragie-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 400,
        title: "Cognee Tool",
        name: "topoteretes/cognee",
        description: "Memory manager for AI apps and Agents using various graph and vector stores and allowing ingestion from 30+ data sources",
        link: "https://github.com/topoteretes/cognee/tree/dev/cognee-mcp",
        category: ["tools", "integration"]
    }, {
        id: 401,
        title: "Save And Query Your Agent Memory In Distributed Way By Membase",
        name: "unibaseio/membase-mcp",
        description: "Save and query your agent memory in distributed way by Membase",
        link: "https://github.com/unibaseio/membase-mcp",
        category: ["tools", "integration"]
    }, {
        id: 402,
        title: "Use Github Gists To Manage And Access Your Personal Knowledge",
        name: "lostintangent/gistpad-mcp",
        description: "Use GitHub Gists to manage and access your personal knowledge, daily notes, and reusable prompts. This acts as a companion to https://gistpad.dev and the GistPad VS Code extension.",
        link: "https://github.com/lostintangent/gistpad-mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 403,
        title: "A Model Context Protocol () That Implements The Zettelkasten Knowledge Management Methodology",
        name: "entanglr/zettelkasten-mcp",
        description: "A Model Context Protocol (MCP) server that implements the Zettelkasten knowledge management methodology, allowing you to create, link, and search atomic notes through Claude and other MCP-compatible clients.",
        link: "https://github.com/entanglr/zettelkasten-mcp",
        category: ["cli", "terminal", "command"]
    }, {
        id: 404,
        title: "Mcp Server Ipinfo Integration",
        name: "briandconnelly/mcp-server-ipinfo",
        description: "IP address geolocation and network information using IPInfo API",
        link: "https://github.com/briandconnelly/mcp-server-ipinfo",
        category: ["location", "maps"]
    }, {
        id: 405,
        title: "Weather Mcp Server Integration",
        name: "devilcoder01/weather-mcp-server",
        description: "Access real-time weather data for any location using the WeatherAPI.com API, providing detailed forecasts and current conditions.",
        link: "https://github.com/devilcoder01/weather-mcp-server",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 406,
        title: "Open Streetmap Mcp Tool",
        name: "jagan-shanmugam/open-streetmap-mcp",
        description: "An OpenStreetMap MCP server with location-based services and geospatial data.",
        link: "https://github.com/jagan-shanmugam/open-streetmap-mcp",
        category: ["location", "maps"]
    }, {
        id: 407,
        title: "Nearby Search Mcp Tool",
        name: "kukapay/nearby-search-mcp",
        description: "An MCP server for nearby place searches with IP-based location detection.",
        link: "https://github.com/kukapay/nearby-search-mcp",
        category: ["location", "maps"]
    }, {
        id: 408,
        title: "Servers Integration",
        name: "modelcontextprotocol/servers",
        description: "Google Maps integration for location services, routing, and place details",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/google-maps",
        category: ["location", "maps"]
    }, {
        id: 409,
        title: "Connects Qgis Desktop To Claude Ai Through The",
        name: "jjsantos01/qgis_mcp",
        description: "connects QGIS Desktop to Claude AI through the MCP. This integration enables prompt-assisted project creation, layer loading, code execution, and more.",
        link: "https://github.com/jjsantos01/qgis_mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 410,
        title: "Weather Mcp Claudedesktop.Git Integration",
        name: "SaintDoresh/Weather-MCP-ClaudeDesktop.git",
        description: "An MCP tool that provides real-time weather data, forecasts, and historical weather information using the OpenWeatherMap API.",
        link: "https://github.com/SaintDoresh/Weather-MCP-ClaudeDesktop.git",
        category: ["git", "version-control"]
    }, {
        id: 411,
        title: "Weekly Weather Mcp.Git Tool",
        name: "rossshannon/weekly-weather-mcp.git",
        description: "Weekly Weather MCP server which returns 7 full days of detailed weather forecasts anywhere in the world.",
        link: "https://github.com/rossshannon/weekly-weather-mcp.git",
        category: ["git", "version-control"]
    }, {
        id: 412,
        title: "Access The Time In Any Timezone And Get The Current Local Time",
        name: "SecretiveShell/MCP-timeserver",
        description: "Access the time in any timezone and get the current local time",
        link: "https://github.com/SecretiveShell/MCP-timeserver",
        category: ["cli", "terminal", "command"]
    }, {
        id: 413,
        title: "Accurate Weather Forecasts Via The Accuweather Api (free Tier Available)",
        name: "TimLukaHorstmann/mcp-weather",
        description: "Accurate weather forecasts via the AccuWeather API (free tier available).",
        link: "https://github.com/TimLukaHorstmann/mcp-weather",
        category: ["tools", "integration"]
    }, {
        id: 414,
        title: "Geocoding For Nominatim",
        name: "webcoderz/MCP-Geo",
        description: "Geocoding MCP server for nominatim, ArcGIS, Bing",
        link: "https://github.com/webcoderz/MCP-Geo",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 415,
        title: "Ipfind Mcp Server Integration",
        name: "ipfind/ipfind-mcp-server",
        description: "IP Address location service using the IP Find API",
        link: "https://github.com/ipfind/ipfind-mcp-server",
        category: ["location", "maps"]
    }, {
        id: 416,
        title: "🏠 – A Model Context Protocol () Implementation That Connects Llms To The Geo Rest Api",
        name: "mahdin75/geoserver-mcp",
        description: "🏠 – A Model Context Protocol (MCP) server implementation that connects LLMs to the GeoServer REST API, enabling AI assistants to interact with geospatial data and services.",
        link: "https://github.com/mahdin75/geoserver-mcp",
        category: ["support", "service"]
    }, {
        id: 417,
        title: "Aiwen Mcp Server Geoip",
        name: "ipfred/aiwen-mcp-server-geoip",
        description: "🐍 📇 ☁️ – MCP Server for the Aiwen IP Location, Get user network IP location, get IP details (country, province, city, lat, lon, ISP, owner, etc.)",
        link: "https://github.com/ipfred/aiwen-mcp-server-geoip",
        category: ["location", "maps"]
    }, {
        id: 418,
        title: "Facebook Ads Mcp Server",
        name: "gomarble-ai/facebook-ads-mcp-server",
        description: "MCP server acting as an interface to the Facebook Ads, enabling programmatic access to Facebook Ads data and management features.",
        link: "https://github.com/gomarble-ai/facebook-ads-mcp-server",
        category: ["social", "media"]
    }, {
        id: 419,
        title: "Osp Marketing Tools Tool",
        name: "open-strategy-partners/osp_marketing_tools",
        description: "A suite of marketing tools from Open Strategy Partners including writing style, editing codes, and product marketing value map creation.",
        link: "https://github.com/open-strategy-partners/osp_marketing_tools",
        category: ["art", "culture", "media"]
    }, {
        id: 420,
        title: "Meta Ads Mcp Integration",
        name: "nictuku/meta-ads-mcp",
        description: "Enables AI agents to monitor and optimize Meta ad performance, analyze campaign metrics, adjust audience targeting, manage creative assets, and make data-driven recommendations for ad spend and campaign settings through seamless Graph API integration.",
        link: "https://github.com/nictuku/meta-ads-mcp",
        category: ["monitoring", "observability"]
    }, {
        id: 421,
        title: "Search Dashboards",
        name: "grafana/mcp-grafana",
        description: "Search dashboards, investigate incidents and query datasources in your Grafana instance",
        link: "https://github.com/grafana/mcp-grafana",
        category: ["search", "extraction"]
    }, {
        id: 422,
        title: "Grafana Loki Mcp Integration",
        name: "tumf/grafana-loki-mcp",
        description: "An MCP server that allows querying Loki logs through the Grafana API.",
        link: "https://github.com/tumf/grafana-loki-mcp",
        category: ["tools", "integration"]
    }, {
        id: 423,
        title: "Enhance Ai-generated Code Quality Through Intelligent",
        name: "hyperb1iss/lucidity-mcp",
        description: "Enhance AI-generated code quality through intelligent, prompt-based analysis across 10 critical dimensions from complexity to security vulnerabilities",
        link: "https://github.com/hyperb1iss/lucidity-mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 424,
        title: "Last9 Mcp Server Tool",
        name: "last9/last9-mcp-server",
        description: "Seamlessly bring real-time production context—logs, metrics, and traces—into your local environment to auto-fix code faster",
        link: "https://github.com/last9/last9-mcp-server",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 425,
        title: "Kubernetes Integration",
        name: "metoro-io/metoro-mcp-server",
        description: "Query and interact with kubernetes environments monitored by Metoro",
        link: "https://github.com/metoro-io/metoro-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 426,
        title: "Mcp Server Raygun Integration",
        name: "MindscapeHQ/mcp-server-raygun",
        description: "Raygun API V3 integration for crash reporting and real user monitoring",
        link: "https://github.com/MindscapeHQ/mcp-server-raygun",
        category: ["monitoring", "observability"]
    }, {
        id: 427,
        title: "Servers Integration",
        name: "modelcontextprotocol/servers",
        description: "Sentry.io integration for error tracking and performance monitoring",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/sentry",
        category: ["monitoring", "observability"]
    }, {
        id: 428,
        title: "Provides Access To Opentelemetry Traces And Metrics Through Logfire",
        name: "pydantic/logfire-mcp",
        description: "Provides access to OpenTelemetry traces and metrics through Logfire",
        link: "https://github.com/pydantic/logfire-mcp",
        category: ["monitoring", "observability"]
    }, {
        id: 429,
        title: "A System Monitoring Tool That Exposes System Metrics Via The Model Context Protocol ()",
        name: "seekrays/mcp-monitor",
        description: "A system monitoring tool that exposes system metrics via the Model Context Protocol (MCP). This tool allows LLMs to retrieve real-time system information through an MCP-compatible interface.（support CPU、Memory、Disk、Network、Host、Process）",
        link: "https://github.com/seekrays/mcp-monitor",
        category: ["monitoring", "observability"]
    }, {
        id: 430,
        title: "Provides Comprehensive Integration With Your Victoriametrics Instance Apis And Documentation For Monitoring",
        name: "VictoriaMetrics-Community/mcp-victoriametrics",
        description: "Provides comprehensive integration with your VictoriaMetrics instance APIs and documentation for monitoring, observability, and debugging tasks related to your VictoriaMetrics instances",
        link: "https://github.com/VictoriaMetrics-Community/mcp-victoriametrics",
        category: ["file", "storage"]
    }, {
        id: 431,
        title: "Using Ffmpeg Command Line To Achieve",
        name: "video-creator/ffmpeg-mcp.git",
        description: "Using ffmpeg command line to achieve an mcp server, can be very convenient, through the dialogue to achieve the local video search, tailoring, stitching, playback and other functions",
        link: "https://github.com/video-creator/ffmpeg-mcp.git",
        category: ["cli", "terminal", "command"]
    }, {
        id: 432,
        title: "That Allows One To Examine Image Metadata Like Exif",
        name: "stass/exif-mcp",
        description: "A MCP server that allows one to examine image metadata like EXIF, XMP, JFIF and GPS.  This provides foundation for LLM-powered search and analysis of photo librares and image collections.",
        link: "https://github.com/stass/exif-mcp",
        category: ["location", "maps"]
    }, {
        id: 433,
        title: "This Repository Implements An (model Context Protocol) For Youtube Search And Transcript Retrieval Functionality",
        name: "Xyber-Labs/mcp-servers",
        description: "This repository implements an MCP (Model Context Protocol) server for YouTube search and transcript retrieval functionality. It allows language models or other agents to easily query YouTube content through a standardized protocol.",
        link: "https://github.com/Xyber-Labs/mcp-servers/tree/main/mcp-server-youtube",
        category: ["search", "extraction"]
    }, {
        id: 434,
        title: "Lets Ai Assistants Use The Wolfram Alpha Api For Real-time Access To Computational Knowledge And Data",
        name: "ricocf/mcp-wolframalpha",
        description: "An MCP server lets AI assistants use the Wolfram Alpha API for real-time access to computational knowledge and data.",
        link: "https://github.com/ricocf/mcp-wolframalpha",
        category: ["tools", "integration"]
    }, {
        id: 435,
        title: "Scrapeless Mcp Server Integration",
        name: "scrapeless-ai/scrapeless-mcp-server",
        description: "The Scrapeless Model Context Protocol service acts as an MCP server connector to the Google SERP API, enabling web search within the MCP ecosystem without leaving it.",
        link: "https://github.com/scrapeless-ai/scrapeless-mcp-server",
        category: ["search", "extraction"]
    }, {
        id: 436,
        title: "Job Searchoor Tool",
        name: "0xDAEF0F/job-searchoor",
        description: "An MCP server for searching job listings with filters for date, keywords, remote work options, and more.",
        link: "https://github.com/0xDAEF0F/job-searchoor",
        category: ["search", "extraction"]
    }, {
        id: 437,
        title: "Mcp Servers Kagi Integration",
        name: "ac3xx/mcp-servers-kagi",
        description: "Kagi search API integration",
        link: "https://github.com/ac3xx/mcp-servers-kagi",
        category: ["search", "extraction"]
    }, {
        id: 438,
        title: "Mcp Simple Arxiv Tool",
        name: "andybrandt/mcp-simple-arxiv",
        description: "🐍 ☁️  MCP for LLM to search and read papers from arXiv",
        link: "https://github.com/andybrandt/mcp-simple-arxiv",
        category: ["search", "extraction"]
    }, {
        id: 439,
        title: "🐍 ☁️ To Search Through Paperswithcode Api",
        name: "hbg/mcp-paperswithcode",
        description: "🐍 ☁️ MCP to search through PapersWithCode API",
        link: "https://github.com/hbg/mcp-paperswithcode",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 440,
        title: "Mcp Simple Pubmed Tool",
        name: "andybrandt/mcp-simple-pubmed",
        description: "🐍 ☁️  MCP to search and read medical / life sciences papers from PubMed.",
        link: "https://github.com/andybrandt/mcp-simple-pubmed",
        category: ["search", "extraction"]
    }, {
        id: 441,
        title: "Nyt Integration",
        name: "angheljf/nyt",
        description: "Search articles using the NYTimes API",
        link: "https://github.com/angheljf/nyt",
        category: ["art", "culture", "media"]
    }, {
        id: 442,
        title: "Browser Automation Tool",
        name: "apify/mcp-server-rag-web-browser",
        description: "An MCP server for Apify's open-source RAG Web Browser Actor to perform web searches, scrape URLs, and return content in Markdown.",
        link: "https://github.com/apify/mcp-server-rag-web-browser",
        category: ["browser", "automation", "web"]
    }, {
        id: 443,
        title: "Clojars Mcp Server Tool",
        name: "Bigsy/Clojars-MCP-Server",
        description: "Clojars MCP Server for upto date dependency information of Clojure libraries",
        link: "https://github.com/Bigsy/Clojars-MCP-Server",
        category: ["tools", "integration"]
    }, {
        id: 444,
        title: "Arxiv Mcp Server Tool",
        name: "blazickjp/arxiv-mcp-server",
        description: "Search ArXiv research papers",
        link: "https://github.com/blazickjp/arxiv-mcp-server",
        category: ["search", "extraction"]
    }, {
        id: 445,
        title: "Discover",
        name: "luminati-io/brightdata-mcp",
        description: "Discover, extract, and interact with the web - one interface powering automated access across the public internet.",
        link: "https://github.com/luminati-io/brightdata-mcp",
        category: ["tools", "integration"]
    }, {
        id: 446,
        title: "Server Google News Integration",
        name: "ChanMeng666/server-google-news",
        description: "Google News integration with automatic topic categorization, multi-language support, and comprehensive search capabilities including headlines, stories, and related topics through SerpAPI.",
        link: "https://github.com/ChanMeng666/server-google-news",
        category: ["communication", "messaging"]
    }, {
        id: 447,
        title: "Openai Websearch Mcp Tool",
        name: "ConechoAI/openai-websearch-mcp",
        description: "This is a Python-based MCP server that provides OpenAI web_search build-in tool.",
        link: "https://github.com/ConechoAI/openai-websearch-mcp/",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 448,
        title: "For Dealx Platform",
        name: "DealExpress/mcp-server",
        description: "MCP Server for DealX platform",
        link: "https://github.com/DealExpress/mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 449,
        title: "Trieve Tool",
        name: "devflowinc/trieve",
        description: "Crawl, embed, chunk, search, and retrieve information from datasets through Trieve",
        link: "https://github.com/devflowinc/trieve/tree/main/clients/mcp-server",
        category: ["search", "extraction"]
    }, {
        id: 450,
        title: "Mcp Server Dumplingai Integration",
        name: "Dumpling-AI/mcp-server-dumplingai",
        description: "Access data, web scraping, and document conversion APIs by Dumpling AI",
        link: "https://github.com/Dumpling-AI/mcp-server-dumplingai",
        category: ["file", "storage"]
    }, {
        id: 451,
        title: "To Search Hacker News",
        name: "erithwik/mcp-hn",
        description: "An MCP server to search Hacker News, get top stories, and more.",
        link: "https://github.com/erithwik/mcp-hn",
        category: ["search", "extraction"]
    }, {
        id: 452,
        title: "Exa Mcp Server Tool",
        name: "exa-labs/exa-mcp-server",
        description: "time web information in a safe and controlled way.",
        link: "https://github.com/exa-labs/exa-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 453,
        title: "Search Via Search1api (requires Paid Api Key)",
        name: "fatwang2/search1api-mcp",
        description: "Search via search1api (requires paid API key)",
        link: "https://github.com/fatwang2/search1api-mcp",
        category: ["search", "extraction"]
    }, {
        id: 454,
        title: "Biomedical Research Providing Access To Pubmed",
        name: "genomoncology/biomcp",
        description: "Biomedical research server providing access to PubMed, ClinicalTrials.gov, and MyVariant.info.",
        link: "https://github.com/genomoncology/biomcp",
        category: ["cli", "terminal", "command"]
    }, {
        id: 455,
        title: "Unsplash Mcp Server Tool",
        name: "hellokaton/unsplash-mcp-server",
        description: "A MCP server for Unsplash image search.",
        link: "https://github.com/hellokaton/unsplash-mcp-server",
        category: ["multimedia", "processing"]
    }, {
        id: 456,
        title: "A Model Context Protocol For Searxng",
        name: "ihor-sokoliuk/mcp-searxng",
        description: "A Model Context Protocol Server for SearXNG",
        link: "https://github.com/ihor-sokoliuk/mcp-searxng",
        category: ["tools", "integration"]
    }, {
        id: 457,
        title: "Naver Search Mcp Integration",
        name: "isnow890/naver-search-mcp",
        description: "MCP server for Naver Search API integration, supporting blog, news, shopping search and DataLab analytics features.",
        link: "https://github.com/isnow890/naver-search-mcp",
        category: ["search", "extraction"]
    }, {
        id: 458,
        title: "For Fetching Web Page Content Using Playwright Headless Browser",
        name: "jae-jae/fetcher-mcp",
        description: "MCP server for fetching web page content using Playwright headless browser, supporting Javascript rendering and intelligent content extraction, and outputting Markdown or HTML format.",
        link: "https://github.com/jae-jae/fetcher-mcp",
        category: ["browser", "automation", "web"]
    }, {
        id: 459,
        title: "G Search Mcp Tool",
        name: "jae-jae/g-search-mcp",
        description: "A powerful MCP server for Google search that enables parallel searching with multiple keywords simultaneously.",
        link: "https://github.com/jae-jae/g-search-mcp",
        category: ["search", "extraction"]
    }, {
        id: 460,
        title: "Bing Search Mcp Integration",
        name: "leehanchung/bing-search-mcp",
        description: "Web search capabilities using Microsoft Bing Search API",
        link: "https://github.com/leehanchung/bing-search-mcp",
        category: ["search", "extraction"]
    }, {
        id: 461,
        title: "☁️ 📇 – Official Kagi Search",
        name: "kagisearch/kagimcp",
        description: "☁️ 📇 – Official Kagi Search MCP Server",
        link: "https://github.com/kagisearch/kagimcp",
        category: ["search", "extraction"]
    }, {
        id: 462,
        title: "☁️ 📇 – Tavily Ai Search Api",
        name: "kshern/mcp-tavily.git",
        description: "☁️ 📇 – Tavily AI search API",
        link: "https://github.com/kshern/mcp-tavily.git",
        category: ["search", "extraction"]
    }, {
        id: 463,
        title: "Brave Search Mcp Integration",
        name: "mikechao/brave-search-mcp",
        description: "Web, Image, News, Video, and Local Point of Interest search capabilities using Brave's Search API",
        link: "https://github.com/mikechao/brave-search-mcp",
        category: ["multimedia", "processing"]
    }, {
        id: 464,
        title: "Plays Melrōse Music Expressions As Midi",
        name: "emicklei/melrose-mcp",
        description: "Plays Melrōse music expressions as MIDI",
        link: "https://github.com/emicklei/melrose-mcp",
        category: ["art", "culture", "media"]
    }, {
        id: 465,
        title: "Servers Integration",
        name: "modelcontextprotocol/servers",
        description: "Web search capabilities using Brave's Search API",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/brave-search",
        category: ["search", "extraction"]
    }, {
        id: 466,
        title: "Servers Tool",
        name: "modelcontextprotocol/servers",
        description: "Efficient web content fetching and processing for AI consumption",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/fetch",
        category: ["tools", "integration"]
    }, {
        id: 467,
        title: "Search Google And Do Deep Web Research On Any Topic",
        name: "mzxrai/mcp-webresearch",
        description: "Search Google and do deep web research on any topic",
        link: "https://github.com/mzxrai/mcp-webresearch",
        category: ["search", "extraction"]
    }, {
        id: 468,
        title: "Duckduckgo Mcp Server Tool",
        name: "nickclyde/duckduckgo-mcp-server",
        description: "Web search using DuckDuckGo",
        link: "https://github.com/nickclyde/duckduckgo-mcp-server",
        category: ["search", "extraction"]
    }, {
        id: 469,
        title: "Access Dutch Parliament (tweede Kamer) Information Including Documents",
        name: "r-huijts/opentk-mcp",
        description: "Access Dutch Parliament (Tweede Kamer) information including documents, debates, activities, and legislative cases through structured search capabilities (based on opentk project by Bert Hubert)",
        link: "https://github.com/r-huijts/opentk-mcp",
        category: ["file", "storage"]
    }, {
        id: 470,
        title: "Mcp Server Deep Research",
        name: "reading-plus-ai/mcp-server-deep-research",
        description: "MCP server providing OpenAI/Perplexity-like autonomous deep research, structured query elaboration, and concise reporting.",
        link: "https://github.com/reading-plus-ai/mcp-server-deep-research",
        category: ["search", "extraction"]
    }, {
        id: 471,
        title: "To Connect To Searxng Instances",
        name: "SecretiveShell/MCP-searxng",
        description: "An MCP Server to connect to searXNG instances",
        link: "https://github.com/SecretiveShell/MCP-searxng",
        category: ["cli", "terminal", "command"]
    }, {
        id: 472,
        title: "Arxiv Latex Mcp Tool",
        name: "takashiishida/arxiv-latex-mcp",
        description: "Get the LaTeX source of arXiv papers to handle mathematical content and equations",
        link: "https://github.com/takashiishida/arxiv-latex-mcp",
        category: ["tools", "integration"]
    }, {
        id: 473,
        title: "Geeknews Mcp Server Tool",
        name: "the0807/GeekNews-MCP-Server",
        description: "An MCP Server that retrieves and processes news data from the GeekNews site.",
        link: "https://github.com/the0807/GeekNews-MCP-Server",
        category: ["tools", "integration"]
    }, {
        id: 474,
        title: "That Provides Agentql's Data Extraction Capabilities",
        name: "tinyfish-io/agentql-mcp",
        description: "MCP server that provides AgentQL's data extraction capabilities.",
        link: "https://github.com/tinyfish-io/agentql-mcp",
        category: ["search", "extraction"]
    }, {
        id: 475,
        title: "Mcp Server Tavily Integration",
        name: "Tomatio13/mcp-server-tavily",
        description: "☁️ 🐍 – Tavily AI search API",
        link: "https://github.com/Tomatio13/mcp-server-tavily",
        category: ["search", "extraction"]
    }, {
        id: 476,
        title: "Vectorize Mcp Server Tool",
        name: "vectorize-io/vectorize-mcp-server",
        description: "Vectorize MCP server for advanced retrieval, Private Deep Research, Anything-to-Markdown file extraction and text chunking.",
        link: "https://github.com/vectorize-io/vectorize-mcp-server/",
        category: ["file", "storage"]
    }, {
        id: 477,
        title: "Webscraping Ai Mcp Server",
        name: "webscraping-ai/webscraping-ai-mcp-server",
        description: "Interact with WebScraping.ai for web data extraction and scraping.",
        link: "https://github.com/webscraping-ai/webscraping-ai-mcp-server",
        category: ["search", "extraction"]
    }, {
        id: 478,
        title: "Duckduckgo Mpc Server Tool",
        name: "zhsama/duckduckgo-mpc-server",
        description: "This is a TypeScript-based MCP server that provides DuckDuckGo search functionality.",
        link: "https://github.com/zhsama/duckduckgo-mpc-server/",
        category: ["search", "extraction"]
    }, {
        id: 479,
        title: "Querying Network Asset Information By Zoomeye",
        name: "zoomeye-ai/mcp_zoomeye",
        description: "Querying network asset information by ZoomEye MCP Server",
        link: "https://github.com/zoomeye-ai/mcp_zoomeye",
        category: ["tools", "integration"]
    }, {
        id: 480,
        title: "Baseline Mcp Server Integration",
        name: "yamanoku/baseline-mcp-server",
        description: "MCP server that searches Baseline status using Web Platform API",
        link: "https://github.com/yamanoku/baseline-mcp-server",
        category: ["communication", "messaging"]
    }, {
        id: 481,
        title: "To Interact With Biothings Api",
        name: "longevity-genie/biothings-mcp",
        description: "MCP server to interact with BioThings API, including genes, genetic variants, drugs, and taxonomic information",
        link: "https://github.com/longevity-genie/biothings-mcp",
        category: ["tools", "integration"]
    }, {
        id: 482,
        title: "A Model Context Protocol For Ghidra That Enables Llms To Autonomously Reverse Engineer Applications",
        name: "LaurieWired/GhidraMCP",
        description: "A Model Context Protocol server for Ghidra that enables LLMs to autonomously reverse engineer applications. Provides tools for decompiling binaries, renaming methods and data, and listing methods, classes, imports, and exports.",
        link: "https://github.com/LaurieWired/GhidraMCP",
        category: ["tools", "integration"]
    }, {
        id: 483,
        title: "Onepassword Mcp Server Tool",
        name: "dkvdm/onepassword-mcp-server",
        description: "An MCP server that enables secure credential retrieval from 1Password to be used by Agentic AI.",
        link: "https://github.com/dkvdm/onepassword-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 484,
        title: "📇 🏠 🍎 🪟 🐧 – A Secure (model Context Protocol) That Enables Ai Agents To Interact With The Authenticator App",
        name: "firstorderai/authenticator_mcp",
        description: "📇 🏠 🍎 🪟 🐧 – A secure MCP (Model Context Protocol) server that enables AI agents to interact with the Authenticator App.",
        link: "https://github.com/firstorderai/authenticator_mcp",
        category: ["security", "authentication"]
    }, {
        id: 485,
        title: "For Integrating Ghidra With Ai Assistants",
        name: "13bm/GhidraMCP",
        description: "MCP server for integrating Ghidra with AI assistants. This plugin enables binary analysis, providing tools for function inspection, decompilation, memory exploration, and import/export analysis via the Model Context Protocol.",
        link: "https://github.com/13bm/GhidraMCP",
        category: ["tools", "integration"]
    }, {
        id: 486,
        title: "Azure Integration",
        name: "atomicchonk/roadrecon_mcp_server",
        description: "🐍 🪟 🏠 MCP server for analyzing ROADrecon gather results from Azure tenant enumeration",
        link: "https://github.com/atomicchonk/roadrecon_mcp_server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 487,
        title: "For Dnstwist",
        name: "BurtTheCoder/mcp-dnstwist",
        description: "MCP server for dnstwist, a powerful DNS fuzzing tool that helps detect typosquatting, phishing, and corporate espionage.",
        link: "https://github.com/BurtTheCoder/mcp-dnstwist",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 488,
        title: "For Maigret",
        name: "BurtTheCoder/mcp-maigret",
        description: "MCP server for maigret, a powerful OSINT tool that collects user account information from various public sources. This server provides tools for searching usernames across social networks and analyzing URLs.",
        link: "https://github.com/BurtTheCoder/mcp-maigret",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 489,
        title: "For Querying The Shodan Api And Shodan Cvedb",
        name: "BurtTheCoder/mcp-shodan",
        description: "MCP server for querying the Shodan API and Shodan CVEDB. This server provides tools for IP lookups, device searches, DNS lookups, vulnerability queries, CPE lookups, and more.",
        link: "https://github.com/BurtTheCoder/mcp-shodan",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 490,
        title: "For Querying The Virustotal Api",
        name: "BurtTheCoder/mcp-virustotal",
        description: "MCP server for querying the VirusTotal API. This server provides tools for scanning URLs, analyzing file hashes, and retrieving IP address reports.",
        link: "https://github.com/BurtTheCoder/mcp-virustotal",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 491,
        title: "Binary Ninja Mcp Tool",
        name: "fosdickio/binary_ninja_mcp",
        description: "A Binary Ninja plugin, MCP server, and bridge that seamlessly integrates Binary Ninja with your favorite MCP client.  It enables you to automate the process of performing binary analysis and reverse engineering.",
        link: "https://github.com/fosdickio/binary_ninja_mcp",
        category: ["cli", "terminal", "command"]
    }, {
        id: 492,
        title: "For Querying The Orkl Api",
        name: "fr0gger/MCP_Security",
        description: "MCP server for querying the ORKL API. This server provides tools for fetching threat reports, analyzing threat actors, and retrieving intelligence sources.",
        link: "https://github.com/fr0gger/MCP_Security",
        category: ["security", "authentication"]
    }, {
        id: 493,
        title: "A Model Context Protocol () Designed To Connect To A Cctv Recording Program (vms) To Retrieve Recorded And Live Video Streams",
        name: "jyjune/mcp_vms",
        description: "A Model Context Protocol (MCP) server designed to connect to a CCTV recording program (VMS) to retrieve recorded and live video streams. It also provides tools to control the VMS software, such as showing live or playback dialogs for specific channels at specified times.",
        link: "https://github.com/jyjune/mcp_vms",
        category: ["multimedia", "processing"]
    }, {
        id: 494,
        title: "Mcp Security Audit Tool",
        name: "qianniuspace/mcp-security-audit",
        description: "time security checks.",
        link: "https://github.com/qianniuspace/mcp-security-audit",
        category: ["security", "authentication"]
    }, {
        id: 495,
        title: "📇 ☁️ Allow Ai Agents To Scan Code For Security Vulnerabilites Using Semgrep",
        name: "semgrep/mcp",
        description: "📇 ☁️ Allow AI agents to scan code for security vulnerabilites using Semgrep.",
        link: "https://github.com/semgrep/mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 496,
        title: "Cyberchef Api Mcp Server",
        name: "slouchd/cyberchef-api-mcp-server",
        description: "MCP server for interacting with the CyberChef server API which will allow an MCP client to utilise the CyberChef operations.",
        link: "https://github.com/slouchd/cyberchef-api-mcp-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 497,
        title: "Ida Pro Mcp Tool",
        name: "mrexodia/ida-pro-mcp",
        description: "MCP server for IDA Pro, allowing you to perform binary analysis with AI assistants. This plugin implement decompilation, disassembly and allows you to generate malware analysis reports automatically.",
        link: "https://github.com/mrexodia/ida-pro-mcp",
        category: ["tools", "integration"]
    }, {
        id: 498,
        title: "For Rad Security",
        name: "rad-security/mcp-server",
        description: "MCP server for RAD Security, providing AI-powered security insights for Kubernetes and cloud environments. This server provides tools for querying the Rad Security API and retrieving security findings, reports, runtime data and many more.",
        link: "https://github.com/rad-security/mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 499,
        title: "All-in-one Security Testing Toolbox That Brings Together Popular Open Source Tools Through A Single Interface",
        name: "securityfortech/secops-mcp",
        description: "All-in-one security testing toolbox that brings together popular open source tools through a single MCP interface. Connected to an AI agent, it enables tasks like pentesting, bug bounty hunting, threat hunting, and more.",
        link: "https://github.com/securityfortech/secops-mcp",
        category: ["security", "authentication"]
    }, {
        id: 500,
        title: "Cve Search Mcp Integration",
        name: "roadwy/cve-search_mcp",
        description: "A Model Context Protocol (MCP) server for querying the CVE-Search API. This server provides comprehensive access to CVE-Search, browse vendor and product、get CVE per CVE-ID、get the last updated CVEs.",
        link: "https://github.com/roadwy/cve-search_mcp",
        category: ["search", "extraction"]
    }, {
        id: 501,
        title: "Conversational Recon Interface And Powered By Httpx And Asnmap",
        name: "nickpending/mcp-recon",
        description: "Conversational recon interface and MCP server powered by httpx and asnmap. Supports various reconnaissance levels for domain analysis, security header inspection, certificate analysis, and ASN lookup.",
        link: "https://github.com/nickpending/mcp-recon",
        category: ["security", "authentication"]
    }, {
        id: 502,
        title: "For Volatility 3",
        name: "Gaffx/volatility-mcp",
        description: "MCP server for Volatility 3.x, allowing you to perform memory forensics analysis with AI assistant. Experience memory forensics without barriers as plugins like pslist and netscan become accessible through clean REST APIs and LLMs.",
        link: "https://github.com/Gaffx/volatility-mcp",
        category: ["tools", "integration"]
    }, {
        id: 503,
        title: "Attestable Mcp Server Tool",
        name: "co-browser/attestable-mcp-server",
        description: "An MCP server running inside a trusted execution environment (TEE) via Gramine, showcasing remote attestation using RA-TLS. This allows an MCP client to verify the server before conencting.",
        link: "https://github.com/co-browser/attestable-mcp-server",
        category: ["browser", "automation", "web"]
    }, {
        id: 504,
        title: "Jadx Ai Mcp Tool",
        name: "zinja-coder/jadx-ai-mcp",
        description: "JADX-AI-MCP is a plugin and MCP Server for the JADX decompiler that integrates directly with Model Context Protocol (MCP) to provide live reverse engineering support with LLMs like Claude.",
        link: "https://github.com/zinja-coder/jadx-ai-mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 505,
        title: "Apktool Mcp Server Tool",
        name: "zinja-coder/apktool-mcp-server",
        description: "APKTool MCP Server is a MCP server for the Apk Tool to provide automation in reverse engineering of Android APKs.",
        link: "https://github.com/zinja-coder/apktool-mcp-server",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 506,
        title: "All-in-one Twitter Management Solution Providing Timeline Access",
        name: "LuniaKunal/mcp-twitter",
        description: "All-in-one Twitter management solution providing timeline access, user tweet retrieval, hashtag monitoring, conversation analysis, direct messaging, sentiment analysis of a post, and complete post lifecycle control - all through a streamlined API.",
        link: "https://github.com/LuniaKunal/mcp-twitter",
        category: ["communication", "messaging"]
    }, {
        id: 507,
        title: "Facebook Mcp Server Integration",
        name: "HagaiHen/facebook-mcp-server",
        description: "Integrates with Facebook Pages to enable direct management of posts, comments, and engagement metrics through the Graph API for streamlined social media management.",
        link: "https://github.com/HagaiHen/facebook-mcp-server",
        category: ["communication", "messaging"]
    }, {
        id: 508,
        title: "That Integrates Balldontlie Api To Provide Information About Players",
        name: "mikechao/balldontlie-mcp",
        description: "MCP server that integrates balldontlie api to provide information about players, teams and games for the NBA, NFL and MLB",
        link: "https://github.com/mikechao/balldontlie-mcp",
        category: ["communication", "messaging"]
    }, {
        id: 509,
        title: "Access Cycling Race Data",
        name: "r-huijts/firstcycling-mcp",
        description: "Access cycling race data, results, and statistics through natural language. Features include retrieving start lists, race results, and rider information from firstcycling.com.",
        link: "https://github.com/r-huijts/firstcycling-mcp",
        category: ["cli", "terminal", "command"]
    }, {
        id: 510,
        title: "A Model Context Protocol () That Connects To Strava Api",
        name: "r-huijts/strava-mcp",
        description: "A Model Context Protocol (MCP) server that connects to Strava API, providing tools to access Strava data through LLMs",
        link: "https://github.com/r-huijts/strava-mcp",
        category: ["tools", "integration"]
    }, {
        id: 511,
        title: "Mcp Afl Server Integration",
        name: "willvelida/mcp-afl-server",
        description: "MCP server that integrates with the Squiggle API to provide information on Australian Football League teams, ladder standings, results, tips, and power rankings.",
        link: "https://github.com/willvelida/mcp-afl-server",
        category: ["communication", "messaging"]
    }, {
        id: 512,
        title: "That Integrates With Freshdesk",
        name: "effytech/freshdesk_mcp",
        description: "MCP server that integrates with Freshdesk, enabling AI models to interact with Freshdesk modules and perform various support operations.",
        link: "https://github.com/effytech/freshdesk_mcp",
        category: ["support", "service"]
    }, {
        id: 513,
        title: "A Go-based Connector For Jira That Enables Ai Assistants Like Claude To Interact With Atlassian Jira",
        name: "nguyenvanduocit/jira-mcp",
        description: "A Go-based MCP connector for Jira that enables AI assistants like Claude to interact with Atlassian Jira. This tool provides a seamless interface for AI models to perform common Jira operations including issue management, sprint planning, and workflow transitions.",
        link: "https://github.com/nguyenvanduocit/jira-mcp",
        category: ["tools", "integration"]
    }, {
        id: 514,
        title: "For Atlassian Products (confluence And Jira)",
        name: "sooperset/mcp-atlassian",
        description: "MCP server for Atlassian products (Confluence and Jira). Supports Confluence Cloud, Jira Cloud, and Jira Server/Data Center. Provides comprehensive tools for searching, reading, creating, and managing content across Atlassian workspaces.",
        link: "https://github.com/sooperset/mcp-atlassian",
        category: ["cloud", "infrastructure"]
    }, {
        id: 515,
        title: "For Lara Translate Api",
        name: "translated/lara-mcp",
        description: "MCP Server for Lara Translate API, enabling powerful translation capabilities with support for language detection and context-aware translations.",
        link: "https://github.com/translated/lara-mcp",
        category: ["support", "service"]
    }, {
        id: 516,
        title: "Kokoro Tts Mcp Tool",
        name: "mberg/kokoro-tts-mcp",
        description: "MCP Server that uses the open weight Kokoro TTS models to convert text-to-speech. Can convert text to MP3 on a local driver or auto-upload to an S3 bucket.",
        link: "https://github.com/mberg/kokoro-tts-mcp",
        category: ["tts", "audio"]
    }, {
        id: 517,
        title: "Mcp Server Airbnb Tool",
        name: "openbnb-org/mcp-server-airbnb",
        description: "Provides tools to search Airbnb and get listing details.",
        link: "https://github.com/openbnb-org/mcp-server-airbnb",
        category: ["search", "extraction"]
    }, {
        id: 518,
        title: "Mcp Server Nationalparks Integration",
        name: "KyrieTangSheng/mcp-server-nationalparks",
        description: "National Park Service API integration providing latest information of park details, alerts, visitor centers, campgrounds, and events for U.S. National Parks",
        link: "https://github.com/KyrieTangSheng/mcp-server-nationalparks",
        category: ["support", "service"]
    }, {
        id: 519,
        title: "Ns Mcp Server Tool",
        name: "r-huijts/ns-mcp-server",
        description: "Access Dutch Railways (NS) travel information, schedules, and real-time updates",
        link: "https://github.com/r-huijts/ns-mcp-server",
        category: ["travel", "transport"]
    }, {
        id: 520,
        title: "That Enables Llms To Interact With Tripadvisor Api",
        name: "pab1it0/tripadvisor-mcp",
        description: "A MCP server that enables LLMs to interact with Tripadvisor API, supporting location data, reviews, and photos through standardized MCP interfaces",
        link: "https://github.com/pab1it0/tripadvisor-mcp",
        category: ["location", "maps"]
    }, {
        id: 521,
        title: "Mcp Git Ingest Tool",
        name: "adhikasp/mcp-git-ingest",
        description: "Read and analyze GitHub repositories with your LLM",
        link: "https://github.com/adhikasp/mcp-git-ingest",
        category: ["git", "version-control"]
    }, {
        id: 522,
        title: "Github Enterprise Mcp Integration",
        name: "ddukbg/github-enterprise-mcp",
        description: "MCP server for GitHub Enterprise API integration",
        link: "https://github.com/ddukbg/github-enterprise-mcp",
        category: ["git", "version-control"]
    }, {
        id: 523,
        title: "Github Mcp Server Integration",
        name: "github/github-mcp-server",
        description: "Official GitHub server for integration with repository management, PRs, issues, and more.",
        link: "https://github.com/github/github-mcp-server",
        category: ["git", "version-control"]
    }, {
        id: 524,
        title: "Gitlab Mr Mcp Tool",
        name: "kopfrechner/gitlab-mr-mcp",
        description: "Interact seamlessly with issues and merge requests of your GitLab projects.",
        link: "https://github.com/kopfrechner/gitlab-mr-mcp",
        category: ["git", "version-control"]
    }, {
        id: 525,
        title: "Servers Tool",
        name: "modelcontextprotocol/servers",
        description: "Direct Git repository operations including reading, searching, and analyzing local repositories",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/git",
        category: ["search", "extraction"]
    }, {
        id: 526,
        title: "Servers Integration",
        name: "modelcontextprotocol/servers",
        description: "GitLab platform integration for project management and CI/CD operations",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/gitlab",
        category: ["git", "version-control"]
    }, {
        id: 527,
        title: "Gitee Integration",
        name: "oschina/gitee",
        description: "Gitee API integration, repository, issue, and pull request management, and more.",
        link: "https://github.com/oschina/gitee",
        category: ["git", "version-control"]
    }, {
        id: 528,
        title: "Azure Integration",
        name: "Tiberriver256/mcp-server-azure-devops",
        description: "Azure DevOps integration for repository management, work items, and pipelines.",
        link: "https://github.com/Tiberriver256/mcp-server-azure-devops",
        category: ["cloud", "infrastructure"]
    }, {
        id: 529,
        title: "Atomgit Mcp Server Integration",
        name: "kaiyuanxiaobing/atomgit-mcp-server",
        description: "Official AtomGit server for integration with repository management, PRs, issues, branches, labels, and more.",
        link: "https://github.com/kaiyuanxiaobing/atomgit-mcp-server",
        category: ["git", "version-control"]
    }, {
        id: 530,
        title: "₿ A Model Context Protocol () That Enables Ai Models To Interact With Bitcoin",
        name: "AbdelStark/bitcoin-mcp",
        description: "₿ A Model Context Protocol (MCP) server that enables AI models to interact with Bitcoin, allowing them to generate keys, validate addresses, decode transactions, query the blockchain, and more.",
        link: "https://github.com/AbdelStark/bitcoin-mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 531,
        title: "Bear Mcp Server Tool",
        name: "akseyh/bear-mcp-server",
        description: "Allows the AI to read from your Bear Notes (macOS only)",
        link: "https://github.com/akseyh/bear-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 532,
        title: "Mcp Server Home Assistant",
        name: "allenporter/mcp-server-home-assistant",
        description: "Expose all Home Assistant voice intents through a Model Context Protocol Server allowing home control.",
        link: "https://github.com/allenporter/mcp-server-home-assistant",
        category: ["tools", "integration"]
    }, {
        id: 533,
        title: "Mcp Server Amazon Bedrock",
        name: "zxkane/mcp-server-amazon-bedrock",
        description: "Use Amazon Nova Canvas model for image generation.",
        link: "https://github.com/zxkane/mcp-server-amazon-bedrock",
        category: ["multimedia", "processing"]
    }, {
        id: 534,
        title: "Unichat Mcp Server Integration",
        name: "amidabuddha/unichat-mcp-server",
        description: "Send requests to OpenAI, MistralAI, Anthropic, xAI, Google AI or DeepSeek using MCP protocol via tool or predefined prompts. Vendor API key required",
        link: "https://github.com/amidabuddha/unichat-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 535,
        title: "That Installs Other S For You",
        name: "anaisbetts/mcp-installer",
        description: "An MCP server that installs other MCP servers for you.",
        link: "https://github.com/anaisbetts/mcp-installer",
        category: ["tts", "audio"]
    }, {
        id: 536,
        title: "Fetch Youtube Subtitles",
        name: "anaisbetts/mcp-youtube",
        description: "Fetch YouTube subtitles",
        link: "https://github.com/anaisbetts/mcp-youtube",
        category: ["tts", "audio"]
    }, {
        id: 537,
        title: "Mcp Simple Openai Assistant",
        name: "andybrandt/mcp-simple-openai-assistant",
        description: "🐍 ☁️  MCP to talk to OpenAI assistants (Claude can use any GPT model as his assitant)",
        link: "https://github.com/andybrandt/mcp-simple-openai-assistant",
        category: ["tools", "integration"]
    }, {
        id: 538,
        title: "Mcp Simple Timeserver Tool",
        name: "andybrandt/mcp-simple-timeserver",
        description: "An MCP server that allows checking local time on the client machine or current UTC time from an NTP server",
        link: "https://github.com/andybrandt/mcp-simple-timeserver",
        category: ["cli", "terminal", "command"]
    }, {
        id: 539,
        title: "Actors Mcp Server Tool",
        name: "apify/actors-mcp-server",
        description: "Use 3,000+ pre-built cloud tools, known as Actors, to extract data from websites, e-commerce, social media, search engines, maps, and more",
        link: "https://github.com/apify/actors-mcp-server",
        category: ["cloud", "infrastructure"]
    }, {
        id: 540,
        title: "Piapi Mcp Server Tool",
        name: "apinetwork/piapi-mcp-server",
        description: "compatible apps.",
        link: "https://github.com/apinetwork/piapi-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 541,
        title: "Replicate Flux Mcp Integration",
        name: "awkoy/replicate-flux-mcp",
        description: "Provides the ability to generate images via Replicate's API.",
        link: "https://github.com/awkoy/replicate-flux-mcp",
        category: ["multimedia", "processing"]
    }, {
        id: 542,
        title: "Mcp Server Taskwarrior Tool",
        name: "awwaiid/mcp-server-taskwarrior",
        description: "An MCP server for basic local taskwarrior usage (add, update, remove tasks)",
        link: "https://github.com/awwaiid/mcp-server-taskwarrior",
        category: ["tools", "integration"]
    }, {
        id: 543,
        title: "Phabricator Mcp Server Integration",
        name: "baba786/phabricator-mcp-server",
        description: "Interacting with Phabricator API",
        link: "https://github.com/baba786/phabricator-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 544,
        title: "A Model Context Protocol () That Integrates With Notion's Api To Manage Personal Todo Lists Efficiently",
        name: "Badhansen/notion-mcp",
        description: "A Model Context Protocol (MCP) server that integrates with Notion's API to manage personal todo lists efficiently.",
        link: "https://github.com/Badhansen/notion-mcp",
        category: ["tools", "integration"]
    }, {
        id: 545,
        title: "My Bear Mcp Server",
        name: "bart6114/my-bear-mcp-server",
        description: "Allows to read notes and tags for the Bear Note taking app, through a direct integration with Bear's sqlitedb.",
        link: "https://github.com/bart6114/my-bear-mcp-server/",
        category: ["database", "storage"]
    }, {
        id: 546,
        title: "Mcp Chatgpt Responses Tool",
        name: "billster45/mcp-chatgpt-responses",
        description: "MCP server for Claude to talk to ChatGPT and use its web search capability.",
        link: "https://github.com/billster45/mcp-chatgpt-responses",
        category: ["search", "extraction"]
    }, {
        id: 547,
        title: "Allows The Ai To Query Graphql S",
        name: "blurrah/mcp-graphql",
        description: "Allows the AI to query GraphQL servers",
        link: "https://github.com/blurrah/mcp-graphql",
        category: ["tools", "integration"]
    }, {
        id: 548,
        title: "This Is A Connector To Allow Claude Desktop (or Any Client) To Read And Search Any Directory Containing Markdown Notes (such As An Obsidian Vault)",
        name: "calclavia/mcp-obsidian",
        description: "This is a connector to allow Claude Desktop (or any MCP client) to read and search any directory containing Markdown notes (such as an Obsidian vault).",
        link: "https://github.com/calclavia/mcp-obsidian",
        category: ["cli", "terminal", "command"]
    }, {
        id: 549,
        title: "Yet Another Cli Tool For Testing S",
        name: "chrishayuk/mcp-cli",
        description: "Yet another CLI tool for testing MCP servers",
        link: "https://github.com/chrishayuk/mcp-cli",
        category: ["cli", "terminal", "command"]
    }, {
        id: 550,
        title: "Integrates With Notion's Api To Manage Personal Todo Lists",
        name: "danhilse/notion_mcp",
        description: "Integrates with Notion's API to manage personal todo lists",
        link: "https://github.com/danhilse/notion_mcp",
        category: ["tools", "integration"]
    }, {
        id: 551,
        title: "Wrike Mcp Server Integration",
        name: "EKibort/wrike-mcp-server",
        description: "🐍 🏠 - A lightweight implementation of a Wrike MCP server for interacting with Wrike tasks via public API.",
        link: "https://github.com/EKibort/wrike-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 552,
        title: "Ticktick That Integrates With Ticktick's Api To Manage Personal Todo Projects And The Tasks",
        name: "ekkyarmandi/ticktick-mcp",
        description: "TickTick MCP server that integrates with TickTick's API to manage personal todo projects and the tasks.",
        link: "https://github.com/ekkyarmandi/ticktick-mcp",
        category: ["tools", "integration"]
    }, {
        id: 553,
        title: "Mcp Server Esignatures Integration",
        name: "esignaturescom/mcp-server-esignatures",
        description: "Contract and template management for drafting, reviewing, and sending binding contracts via the eSignatures API.",
        link: "https://github.com/esignaturescom/mcp-server-esignatures",
        category: ["tools", "integration"]
    }, {
        id: 554,
        title: "Access Miro Whiteboards",
        name: "evalstate/mcp-miro",
        description: "Access MIRO whiteboards, bulk create and read items. Requires OAUTH key for REST API.",
        link: "https://github.com/evalstate/mcp-miro",
        category: ["security", "authentication"]
    }, {
        id: 555,
        title: "Keep Mcp Tool",
        name: "feuerdev/keep-mcp",
        description: "Read, create, update and delete Google Keep notes.",
        link: "https://github.com/feuerdev/keep-mcp",
        category: ["tools", "integration"]
    }, {
        id: 556,
        title: "Gqai Tool",
        name: "fotoetienne/gqai",
        description: "Define tools using regular GraphQL queries/mutations and gqai automatically generates an MCP server for you.",
        link: "https://github.com/fotoetienne/gqai",
        category: ["tools", "integration"]
    }, {
        id: 557,
        title: "Mcp Server Calculator Tool",
        name: "githejie/mcp-server-calculator",
        description: "This server enables LLMs to use calculator for precise numerical calculations",
        link: "https://github.com/githejie/mcp-server-calculator",
        category: ["git", "version-control"]
    }, {
        id: 558,
        title: "Mcp Difyworkflow Server Tool",
        name: "gotoolkits/mcp-difyworkflow-server",
        description: "🏎️ ☁️ Tools to the query and execute of Dify workflows",
        link: "https://github.com/gotoolkits/mcp-difyworkflow-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 559,
        title: "Raindrop Io Mcp Server",
        name: "hiromitsusasaki/raindrop-io-mcp-server",
        description: "An integration that allows LLMs to interact with Raindrop.io bookmarks using the Model Context Protocol (MCP).",
        link: "https://github.com/hiromitsusasaki/raindrop-io-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 560,
        title: "Attio Mcp Server Tool",
        name: "hmk/attio-mcp-server",
        description: "📇 ☁️ Allows AI clients to manage records and notes in Attio CRM",
        link: "https://github.com/hmk/attio-mcp-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 561,
        title: "Mcp Vegalite Server Tool",
        name: "isaacwasserman/mcp-vegalite-server",
        description: "Generate visualizations from fetched data using the VegaLite format and renderer.",
        link: "https://github.com/isaacwasserman/mcp-vegalite-server",
        category: ["tools", "integration"]
    }, {
        id: 562,
        title: "Update",
        name: "ivo-toby/contentful-mcp",
        description: "Update, create, delete content, content-models and assets in your Contentful Space",
        link: "https://github.com/ivo-toby/contentful-mcp",
        category: ["tools", "integration"]
    }, {
        id: 563,
        title: "Speech.Sh Tool",
        name: "j3k0/speech.sh",
        description: "Let the agent speak things out loud, notify you when he's done working with a quick summary",
        link: "https://github.com/j3k0/speech.sh/blob/main/MCP_README.md",
        category: ["tts", "audio"]
    }, {
        id: 564,
        title: "Climatiq Mcp Server Integration",
        name: "jagan-shanmugam/climatiq-mcp-server",
        description: "A Model Context Protocol (MCP) server for accessing the Climatiq API to calculate carbon emissions. This allows AI assistants to perform real-time carbon calculations and provide climate impact insights.",
        link: "https://github.com/jagan-shanmugam/climatiq-mcp-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 565,
        title: "For Typst",
        name: "johannesbrandenburger/typst-mcp",
        description: "MCP server for Typst, a markup-based typesetting system. It provides tools for converting between LaTeX and Typst, validating Typst syntax, and generating images from Typst code.",
        link: "https://github.com/johannesbrandenburger/typst-mcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 566,
        title: "Mac Apps Launcher Tool",
        name: "JoshuaRileyDev/mac-apps-launcher",
        description: "An MCP server to list and launch applications on MacOS",
        link: "https://github.com/JoshuaRileyDev/mac-apps-launcher",
        category: ["tools", "integration"]
    }, {
        id: 567,
        title: "Jotdown Tool",
        name: "Harry-027/JotDown",
        description: "An MCP server to create/update pages in Notion app & auto generate mdBooks from structured content.",
        link: "https://github.com/Harry-027/JotDown",
        category: ["tools", "integration"]
    }, {
        id: 568,
        title: "Plane Mcp Server Integration",
        name: "kelvin6365/plane-mcp-server",
        description: "🏎️ 🏠 This MCP Server will help you to manage projects and issues through Plane's API",
        link: "https://github.com/kelvin6365/plane-mcp-server",
        category: ["support", "service"]
    }, {
        id: 569,
        title: "Mcp Server Rabbitmq Tool",
        name: "kenliao94/mcp-server-rabbitmq",
        description: "Enable interaction (admin operation, message enqueue/dequeue) with RabbitMQ",
        link: "https://github.com/kenliao94/mcp-server-rabbitmq",
        category: ["tools", "integration"]
    }, {
        id: 570,
        title: "Mcp Miro Tool",
        name: "k-jarzyna/mcp-miro",
        description: "Miro MCP server, exposing all functionalities available in official Miro SDK",
        link: "https://github.com/k-jarzyna/mcp-miro",
        category: ["tools", "integration"]
    }, {
        id: 571,
        title: "Mcp Remote Call Ping Pong",
        name: "kimtth/mcp-remote-call-ping-pong",
        description: "An experimental and educational app for Ping-pong server demonstrating remote MCP (Model Context Protocol) calls",
        link: "https://github.com/kimtth/mcp-remote-call-ping-pong",
        category: ["tools", "integration"]
    }, {
        id: 572,
        title: "📇 ☁️ Allows Ai Models To Interact With Kibela",
        name: "kj455/mcp-kibela",
        description: "📇 ☁️ Allows AI models to interact with Kibela",
        link: "https://github.com/kj455/mcp-kibela",
        category: ["tools", "integration"]
    }, {
        id: 573,
        title: "Mcp Kibela Server Integration",
        name: "kiwamizamurai/mcp-kibela-server",
        description: "📇 ☁️ Powerfully interact with Kibela API.",
        link: "https://github.com/kiwamizamurai/mcp-kibela-server",
        category: ["tools", "integration"]
    }, {
        id: 574,
        title: "Confluence Mcp Server Tool",
        name: "KS-GEN-AI/confluence-mcp-server",
        description: "Get Confluence data via CQL and read pages.",
        link: "https://github.com/KS-GEN-AI/confluence-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 575,
        title: "Jira Mcp Server Tool",
        name: "KS-GEN-AI/jira-mcp-server",
        description: "Read jira data via JQL and api and execute requests to create and edit tickets.",
        link: "https://github.com/KS-GEN-AI/jira-mcp-server",
        category: ["cli", "terminal", "command"]
    }, {
        id: 576,
        title: "With Basic Demonstration Of Interactions With Salesforce Instance",
        name: "salesforce-mcp/salesforce-mcp",
        description: "MCP server with basic demonstration of interactions with Salesforce instance",
        link: "https://github.com/salesforce-mcp/salesforce-mcp",
        category: ["tools", "integration"]
    }, {
        id: 577,
        title: "Specialized Llm Enhancement Prompts And Jailbreaks With Dynamic Schema Adaptation",
        name: "pollinations/chucknorris-mcp",
        description: "Specialized LLM enhancement prompts and jailbreaks with dynamic schema adaptation.",
        link: "https://github.com/pollinations/chucknorris-mcp",
        category: ["tools", "integration"]
    }, {
        id: 578,
        title: "With Basic Demonstration Of Getting Weather From Hong Kong Observatory",
        name: "louiscklaw/hko-mcp",
        description: "MCP server with basic demonstration of getting weather from Hong Kong Observatory",
        link: "https://github.com/louiscklaw/hko-mcp",
        category: ["tools", "integration"]
    }, {
        id: 579,
        title: "Use Huggingface Spaces Directly From Claude",
        name: "evalstate/mcp-hfspace",
        description: "Use HuggingFace Spaces directly from Claude. Use Open Source Image Generation, Chat, Vision tasks and more. Supports Image, Audio and text uploads/downloads.",
        link: "https://github.com/evalstate/mcp-hfspace",
        category: ["multimedia", "processing"]
    }, {
        id: 580,
        title: "Mcp Server Giphy Integration",
        name: "magarcia/mcp-server-giphy",
        description: "Search and retrieve GIFs from Giphy's vast library through the Giphy API.",
        link: "https://github.com/magarcia/mcp-server-giphy",
        category: ["search", "extraction"]
    }, {
        id: 581,
        title: "Make Mcp Server Tool",
        name: "integromat/make-mcp-server",
        description: "Turn your Make scenarios into callable tools for AI assistants.",
        link: "https://github.com/integromat/make-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 582,
        title: "Spotify Mcp Server Tool",
        name: "marcelmarais/spotify-mcp-server",
        description: "📇 🏠 Control Spotify playback and manage playlists.",
        link: "https://github.com/marcelmarais/spotify-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 583,
        title: "Interacting With Obsidian Via Rest Api",
        name: "MarkusPfundstein/mcp-obsidian",
        description: "Interacting with Obsidian via REST API",
        link: "https://github.com/MarkusPfundstein/mcp-obsidian",
        category: ["tools", "integration"]
    }, {
        id: 584,
        title: "Mcp Log Proxy Tool",
        name: "emicklei/mcp-log-proxy",
        description: "MCP server proxy that offers a Web UI to the full message flow",
        link: "https://github.com/emicklei/mcp-log-proxy",
        category: ["aggregator", "integration"]
    }, {
        id: 585,
        title: "Quarkus Mcp Servers Tool",
        name: "quarkiverse/quarkus-mcp-servers",
        description: "Draw on JavaFX canvas.",
        link: "https://github.com/quarkiverse/quarkus-mcp-servers/tree/main/jfx",
        category: ["tools", "integration"]
    }, {
        id: 586,
        title: "Screenpipe Tool",
        name: "mediar-ai/screenpipe",
        description: "🎖️ 🦀 🏠 🍎 Local-first system capturing screen/audio with timestamped indexing, SQL/embedding storage, semantic search, LLM-powered history analysis, and event-triggered actions - enables building context-aware AI agents through a NextJS plugin ecosystem.",
        link: "https://github.com/mediar-ai/screenpipe",
        category: ["database", "storage"]
    }, {
        id: 587,
        title: "Servers Tool",
        name: "modelcontextprotocol/servers",
        description: "MCP server that exercises all the features of the MCP protocol",
        link: "https://github.com/modelcontextprotocol/servers/tree/main/src/everything",
        category: ["tools", "integration"]
    }, {
        id: 588,
        title: "Token-efficient Go Documentation That Provides Ai Assistants With Smart Access To Package Docs And Types Without Reading Entire Source Files",
        name: "mrjoshuak/godoc-mcp",
        description: "Token-efficient Go documentation server that provides AI assistants with smart access to package docs and types without reading entire source files",
        link: "https://github.com/mrjoshuak/godoc-mcp",
        category: ["art", "culture", "media"]
    }, {
        id: 589,
        title: "Chat With Openai's Smartest Models",
        name: "mzxrai/mcp-openai",
        description: "Chat with OpenAI's smartest models",
        link: "https://github.com/mzxrai/mcp-openai",
        category: ["art", "culture", "media"]
    }, {
        id: 590,
        title: "Swift Mcp Gui.Git Tool",
        name: "NakaokaRei/swift-mcp-gui.git",
        description: "MCP server that can execute commands such as keyboard input and mouse movement",
        link: "https://github.com/NakaokaRei/swift-mcp-gui.git",
        category: ["cli", "terminal", "command"]
    }, {
        id: 591,
        title: "All In One Model Context Protocol",
        name: "nguyenvanduocit/all-in-one-model-context-protocol",
        description: "Some useful tools for developer, almost everything an engineer need: confluence, Jira, Youtube, run script, knowledge base RAG, fetch URL, Manage youtube channel, emails, calendar, gitlab",
        link: "https://github.com/nguyenvanduocit/all-in-one-model-context-protocol",
        category: ["communication", "messaging"]
    }, {
        id: 592,
        title: "Omniparser Autogui Mcp Tool",
        name: "NON906/omniparser-autogui-mcp",
        description: "🐍 Automatic operation of on-screen GUI.",
        link: "https://github.com/NON906/omniparser-autogui-mcp",
        category: ["tools", "integration"]
    }, {
        id: 593,
        title: "For Coda",
        name: "orellazri/coda-mcp",
        description: "MCP server for Coda",
        link: "https://github.com/orellazri/coda-mcp",
        category: ["tools", "integration"]
    }, {
        id: 594,
        title: "Mcp Server Openai Tool",
        name: "pierrebrunelle/mcp-server-openai",
        description: "Query OpenAI models directly from Claude using MCP protocol",
        link: "https://github.com/pierrebrunelle/mcp-server-openai",
        category: ["tools", "integration"]
    }, {
        id: 595,
        title: "Hn Server Tool",
        name: "pskill9/hn-server",
        description: "📇 ☁️ Parses the HTML content from news.ycombinator.com (Hacker News) and provides structured data for different types of stories (top, new, ask, show, jobs).",
        link: "https://github.com/pskill9/hn-server",
        category: ["tools", "integration"]
    }, {
        id: 596,
        title: "Vibe Check Mcp Server",
        name: "PV-Bhat/vibe-check-mcp-server",
        description: 'An MCP server that prevents cascading errors and scope creep by calling a "Vibe-check" agent to ensure user alignment.',
        link: "https://github.com/PV-Bhat/vibe-check-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 597,
        title: "For Mathematical Expression Calculation",
        name: "pwh-pwh/cal-mcp",
        description: "An MCP server for Mathematical expression calculation",
        link: "https://github.com/pwh-pwh/cal-mcp",
        category: ["tools", "integration"]
    }, {
        id: 598,
        title: "Any Chat Completions Mcp",
        name: "pyroprompts/any-chat-completions-mcp",
        description: "Chat with any other OpenAI SDK Compatible Chat Completions API, like Perplexity, Groq, xAI and more",
        link: "https://github.com/pyroprompts/any-chat-completions-mcp",
        category: ["tools", "integration"]
    }, {
        id: 599,
        title: "Enhances Any Agent's Reasoning Capabilities By Integrating The Think-tools",
        name: "Rai220/think-mcp",
        description: "Enhances any agent's reasoning capabilities by integrating the think-tools, as described in Anthropic's article.",
        link: "https://github.com/Rai220/think-mcp",
        category: ["art", "culture", "media"]
    }, {
        id: 600,
        title: "Allows The Ai To Read",
        name: "reeeeemo/ancestry-mcp",
        description: "Allows the AI to read .ged files and genetic data",
        link: "https://github.com/reeeeemo/ancestry-mcp",
        category: ["file", "storage"]
    }, {
        id: 601,
        title: "Create Spaced Repetition Flashcards In Rember To Remember Anything You Learn In Your Chats",
        name: "rember/rember-mcp",
        description: "Create spaced repetition flashcards in Rember to remember anything you learn in your chats.",
        link: "https://github.com/rember/rember-mcp",
        category: ["tools", "integration"]
    }, {
        id: 602,
        title: "Mcp Server Asana Integration",
        name: "roychri/mcp-server-asana",
        description: "📇 ☁️ This Model Context Protocol server implementation of Asana allows you to talk to Asana API from MCP Client such as Anthropic's Claude Desktop Application, and many more.",
        link: "https://github.com/roychri/mcp-server-asana",
        category: ["cli", "terminal", "command"]
    }, {
        id: 603,
        title: "Wcgw Tool",
        name: "rusiaaman/wcgw",
        description: "Autonomous shell execution, computer control and coding agent. (Mac)",
        link: "https://github.com/rusiaaman/wcgw/blob/main/src/wcgw/client/mcp_server/Readme.md",
        category: ["cli", "terminal", "command"]
    }, {
        id: 604,
        title: "Mcp Wolfram Alpha Integration",
        name: "SecretiveShell/MCP-wolfram-alpha",
        description: "An MCP server for querying wolfram alpha API.",
        link: "https://github.com/SecretiveShell/MCP-wolfram-alpha",
        category: ["cli", "terminal", "command"]
    }, {
        id: 605,
        title: "Interact With Tiktok Videos",
        name: "Seym0n/tiktok-mcp",
        description: "Interact with TikTok videos",
        link: "https://github.com/Seym0n/tiktok-mcp",
        category: ["multimedia", "processing"]
    }, {
        id: 606,
        title: "Model Context Protocol () That Interacts With Shopify Dev",
        name: "Shopify/dev-mcp",
        description: "Model Context Protocol (MCP) server that interacts with Shopify Dev.",
        link: "https://github.com/Shopify/dev-mcp",
        category: ["tools", "integration"]
    }, {
        id: 607,
        title: "Database Integration",
        name: "sirmews/apple-notes-mcp",
        description: "Allows the AI to read from your local Apple Notes database (macOS only)",
        link: "https://github.com/sirmews/apple-notes-mcp",
        category: ["database", "storage"]
    }, {
        id: 608,
        title: "For Atlassian Products (confluence And Jira)",
        name: "sooperset/mcp-atlassian",
        description: "MCP server for Atlassian products (Confluence and Jira). Supports Confluence Cloud, Jira Cloud, and Jira Server/Data Center. Provides comprehensive tools for searching, reading, creating, and managing content across Atlassian workspaces.",
        link: "https://github.com/sooperset/mcp-atlassian",
        category: ["cloud", "infrastructure"]
    }, {
        id: 609,
        title: "Mcp Notion Server Integration",
        name: "suekou/mcp-notion-server",
        description: "Interacting with Notion API",
        link: "https://github.com/suekou/mcp-notion-server",
        category: ["tools", "integration"]
    }, {
        id: 610,
        title: "Integrates With Linear Project Management System",
        name: "tacticlaunch/mcp-linear",
        description: "Integrates with Linear project management system",
        link: "https://github.com/tacticlaunch/mcp-linear",
        category: ["communication", "messaging"]
    }, {
        id: 611,
        title: "Mcp Server Perplexity Integration",
        name: "tanigami/mcp-server-perplexity",
        description: "Interacting with Perplexity API.",
        link: "https://github.com/tanigami/mcp-server-perplexity",
        category: ["tools", "integration"]
    }, {
        id: 612,
        title: "Access Home Assistant Data And Control Devices (lights",
        name: "tevonsb/homeassistant-mcp",
        description: "Access Home Assistant data and control devices (lights, switches, thermostats, etc).",
        link: "https://github.com/tevonsb/homeassistant-mcp",
        category: ["tools", "integration"]
    }, {
        id: 613,
        title: "Oura Mcp Server Tool",
        name: "tomekkorbak/oura-mcp-server",
        description: "An MCP server for Oura, an app for tracking sleep",
        link: "https://github.com/tomekkorbak/oura-mcp-server",
        category: ["tools", "integration"]
    }, {
        id: 614,
        title: "Mcp Graphql Forge Integration",
        name: "UnitVectorY-Labs/mcp-graphql-forge",
        description: "A lightweight, configuration-driven MCP server that exposes curated GraphQL queries as modular tools, enabling intentional API interactions from your agents.",
        link: "https://github.com/UnitVectorY-Labs/mcp-graphql-forge",
        category: ["tools", "integration"]
    }, {
        id: 615,
        title: "For Strava",
        name: "kw510/strava-mcp",
        description: "An MCP server for Strava, an app for tracking physical exercise",
        link: "https://github.com/kw510/strava-mcp",
        category: ["tools", "integration"]
    }, {
        id: 616,
        title: "Wanaku Tool",
        name: "wanaku-ai/wanaku",
        description: "☁️ 🏠 The Wanaku MCP Router is a SSE-based MCP server that provides an extensible routing engine that allows integrating your enterprise systems with AI agents.",
        link: "https://github.com/wanaku-ai/wanaku",
        category: ["tools", "integration"]
    }, {
        id: 617,
        title: "Cli Tool For Testing S",
        name: "wong2/mcp-cli",
        description: "CLI tool for testing MCP servers",
        link: "https://github.com/wong2/mcp-cli",
        category: ["cli", "terminal", "command"]
    }, {
        id: 618,
        title: "Wrap S With A Websocket (for Use With Kitbitz)",
        name: "nick1udwig/ws-mcp",
        description: "Wrap MCP servers with a WebSocket (for use with kitbitz)",
        link: "https://github.com/nick1udwig/ws-mcp",
        category: ["tools", "integration"]
    }, {
        id: 619,
        title: "Allows Ai Models To Interact With Hackmd",
        name: "yuna0x0/hackmd-mcp",
        description: "Allows AI models to interact with HackMD",
        link: "https://github.com/yuna0x0/hackmd-mcp",
        category: ["tools", "integration"]
    }, {
        id: 620,
        title: "Providing Date And Time Functions In Various Formats",
        name: "ZeparHyfar/mcp-datetime",
        description: "MCP server providing date and time functions in various formats",
        link: "https://github.com/ZeparHyfar/mcp-datetime",
        category: ["tools", "integration"]
    }, {
        id: 621,
        title: "Simple Web Ui To Install And Manage S For Claude Desktop App",
        name: "zueai/mcp-manager",
        description: "Simple Web UI to install and manage MCP servers for Claude Desktop App.",
        link: "https://github.com/zueai/mcp-manager",
        category: ["tools", "integration"]
    }, {
        id: 622,
        title: "Yuque Mcp Server Integration",
        name: "HenryHaoson/Yuque-MCP-Server",
        description: "📇 ☁️ A Model-Context-Protocol (MCP) server for integrating with Yuque API, allowing AI models to manage documents, interact with knowledge bases, search content, and access analytics data from the Yuque platform.",
        link: "https://github.com/HenryHaoson/Yuque-MCP-Server",
        category: ["file", "storage"]
    }, {
        id: 623,
        title: "Screenpilot Tool",
        name: "Mtehabsim/ScreenPilot",
        description: "enables AI to fully control and access GUI interactions by providing tools for mouse and keyboard, ideal for general automation, education, and experimentation.",
        link: "https://github.com/Mtehabsim/ScreenPilot",
        category: ["tools", "integration"]
    }, {
        id: 624,
        title: "Implementation Wrapping Ankr Advanced Api",
        name: "tumf/web3-mcp",
        description: "An MCP server implementation wrapping Ankr Advanced API. Access to NFT, token, and blockchain data across multiple chains including Ethereum, BSC, Polygon, Avalanche, and more.",
        link: "https://github.com/tumf/web3-mcp",
        category: ["aggregator", "integration"]
    }, {
        id: 625,
        title: "Pdf Tools Mcp Tool",
        name: "danielkennedy1/pdf-tools-mcp",
        description: "PDF download, view & manipulation utilities.",
        link: "https://github.com/danielkennedy1/pdf-tools-mcp",
        category: ["tools", "integration"]
    }, {
        id: 626,
        title: "Domain Lookup Mcp Tool",
        name: "dotemacs/domain-lookup-mcp",
        description: "Domain name lookup service, first via RDAP and then as a fallback via WHOIS",
        link: "https://github.com/dotemacs/domain-lookup-mcp",
        category: ["support", "service"]
    }, {
        id: 627,
        title: "Klavis Tool",
        name: "Klavis-AI/klavis",
        description: "Extract and convert YouTube video information.",
        link: "https://github.com/Klavis-AI/klavis/tree/main/mcp_servers/youtube",
        category: ["multimedia", "processing"]
    }, {
        id: 628,
        title: "Enables Interactive Llm Workflows By Adding Local User Prompts And Chat Capabilities Directly Into The Loop",
        name: "ttommyth/interactive-mcp",
        description: "Enables interactive LLM workflows by adding local user prompts and chat capabilities directly into the MCP loop.",
        link: "https://github.com/ttommyth/interactive-mcp",
        category: ["tools", "integration"]
    }, {
        id: 629,
        title: "When Your Llm Needs Human Assistance (through Aws Mechanical Turk)",
        name: "olalonde/mcp-human",
        description: "When your LLM needs human assistance (through AWS Mechanical Turk)",
        link: "https://github.com/olalonde/mcp-human",
        category: ["cloud", "infrastructure"]
    }, {
        id: 630,
        title: "A High-level Framework For Building S In Python",
        name: "jlowin/fastmcp",
        description: "A high-level framework for building MCP servers in Python",
        link: "https://github.com/jlowin/fastmcp",
        category: ["code", "execution", "sandbox"]
    }, {
        id: 631,
        title: "A High-level Framework For Building S In Typescript",
        name: "punkpeye/fastmcp",
        description: "A high-level framework for building MCP servers in TypeScript",
        link: "https://github.com/punkpeye/fastmcp",
        category: ["tools", "integration"]
    }],
    ii = {
        key: 0,
        style: {
            display: "none"
        }
    },
    oi = {
        key: 1,
        class: "modal-overlay"
    },
    ai = {
        class: "modal-container"
    },
    ni = {
        class: "header"
    },
    ri = {
        class: "title"
    },
    si = {
        class: "header-actions"
    },
    ci = {
        key: 0,
        class: "loading-container"
    },
    li = {
        key: 1
    },
    pi = {
        class: "tools-grid"
    },
    di = {
        class: "tool-card-top"
    },
    mi = {
        class: "tool-top-left"
    },
    ui = ["src"],
    gi = {
        key: 2,
        class: "default-icon"
    },
    hi = {
        class: "tool-title-section"
    },
    vi = {
        class: "tool-title"
    },
    yi = ["onClick", "disabled"],
    bi = {
        key: 0,
        class: "loading-spinner-small"
    },
    fi = {
        key: 1
    },
    ki = {
        class: "tool-card-bottom"
    },
    wi = {
        class: "tool-description"
    },
    Ai = {
        class: "mcp-section-header"
    },
    Ci = {
        class: "tools-grid mcp-tools-grid"
    },
    Mi = {
        class: "tool-card-top"
    },
    xi = {
        class: "tool-top-left"
    },
    Pi = {
        class: "tool-icon"
    },
    Si = ["src"],
    Ti = {
        key: 2,
        class: "default-mcp-icon"
    },
    Ii = {
        class: "tool-title-section"
    },
    _i = {
        class: "tool-title"
    },
    Li = ["onClick", "disabled"],
    Di = {
        key: 0,
        class: "loading-spinner-small"
    },
    Ei = {
        key: 1
    },
    Fi = {
        class: "tool-card-bottom"
    },
    Oi = {
        class: "tool-description"
    },
    Bi = {
        class: "mcp-info-section"
    },
    ji = {
        key: 0,
        class: "mcp-tools-tags"
    },
    zi = ["onMouseover", "onClick"],
    Ri = {
        key: 0,
        class: "empty-mcp-container"
    },
    Gi = {
        class: "empty-mcp-message"
    },
    qi = {
        class: "mcp-section-header"
    },
    Ni = {
        class: "category-filter-container"
    },
    Ui = {
        class: "category-filter-tags"
    },
    Wi = ["onClick"],
    Vi = {
        class: "tools-grid extended-mcp-tools-grid"
    },
    Hi = ["onClick"],
    Qi = {
        class: "tool-card-top"
    },
    Ki = {
        class: "tool-top-left"
    },
    Zi = {
        class: "tool-title-section"
    },
    Ji = {
        class: "tool-title"
    },
    Yi = {
        key: 0,
        class: "tool-categories"
    },
    Xi = {
        class: "tool-card-bottom"
    },
    $i = {
        class: "tool-description"
    },
    eo = {
        class: "tool-repository-info"
    },
    to = {
        class: "repository-name"
    },
    io = {
        class: "modal-container"
    },
    oo = {
        class: "modal-header"
    },
    ao = ["disabled"],
    no = {
        class: "modal-body"
    },
    ro = {
        for: "toolTitle"
    },
    so = ["placeholder", "disabled"],
    co = {
        class: "radio-group"
    },
    lo = {
        class: "radio-option"
    },
    po = ["disabled"],
    mo = {
        for: "typeSSE"
    },
    uo = {
        class: "radio-option"
    },
    go = ["disabled"],
    ho = {
        for: "typeStreamableHttp"
    },
    vo = {
        for: "toolIcon"
    },
    yo = ["disabled"],
    bo = {
        for: "toolDescription"
    },
    fo = ["placeholder", "disabled"],
    ko = {
        for: "requestHeader"
    },
    wo = ["disabled"],
    Ao = {
        class: "modal-footer"
    },
    Co = ["disabled"],
    Mo = ["disabled"],
    xo = {
        key: 0,
        class: "loading-spinner-small"
    },
    Po = {
        key: 1
    },
    So = {
        class: "tooltip-content"
    },
    To = {
        class: "tooltip-title"
    },
    Io = {
        key: 0,
        class: "tooltip-description"
    },
    _o = P({
        __name: "install_custom_tools",
        props: {
            isModal: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["close", "install", "uninstall"],
        setup(e, {
            emit: t
        }) {
            const i = y([]),
                o = y(!0),
                a = y({
                    uninstalling: new Set,
                    installing: new Set
                }),
                n = g((() => {
                    var e;
                    return (null == (e = null == Z ? void 0 : Z.value) ? void 0 : e.gk_dogfood) ? i.value.filter((e => !e.isMcp)) : i.value.filter((e => !e.isMcp && [0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12].includes(e.id)))
                })),
                r = g((() => i.value.filter((e => e.isMcp)))),
                s = e => a.value.installing.has(e),
                c = e => a.value.uninstalling.has(e),
                l = e => s(e) || c(e),
                p = e => {
                    const t = new Set(a.value.installing);
                    t.add(e), a.value.installing = t, O.log("ZZH Added to installing:", e, a.value)
                },
                d = e => {
                    const t = new Set(a.value.uninstalling);
                    t.add(e), a.value.uninstalling = t
                },
                m = e => {
                    const t = new Set(a.value.installing),
                        i = new Set(a.value.uninstalling);
                    t.delete(e), i.delete(e), a.value.installing = t, a.value.uninstalling = i
                },
                u = y(!1),
                h = y({
                    name: "",
                    url: "",
                    description: "",
                    type: "sse",
                    config: {},
                    genspark_browser_required: !1
                }),
                v = y(""),
                f = y(!1),
                P = y("ALL"),
                _ = y(""),
                W = k("gensparkBrowserId");
            y(new Set);
            const K = k("jsBridge"),
                Z = k("currentUser");
            !1 === (null == Z ? void 0 : Z.value) && (location.href = "/login");
            const J = y(""),
                Y = y(!1);
            T((() => K.value), (e => {
                e && K.value.callHandler("support", {
                    api: "oauthLogin"
                }, (e => {
                    Y.value = e
                }))
            }), {
                immediate: !0
            });
            const X = (e, t, i) => {
                    var o;
                    try {
                        const a = localStorage.getItem("installedCustomTools");
                        let n = {};
                        a && (n = JSON.parse(a), "object" == typeof n && null !== n || (n = {})), "add" === i ? n[e] = t : "remove" === i && delete n[e], localStorage.setItem("installedCustomTools", JSON.stringify(n)), null == (o = K.value) || o.callHandler("installedCustomToolsChanged", {}), O.log(`Updated installedCustomTools in localStorage for ${e}, action: ${i}`, n)
                    } catch (a) {}
                },
                $ = (e, t) => {
                    try {
                        const i = localStorage.getItem("unselectedCustomTools");
                        let o = [];
                        if (i) {
                            const e = JSON.parse(i);
                            Array.isArray(e) && (o = e.filter((e => "string" == typeof e)))
                        }
                        const a = o.indexOf(e);
                        "add" === t || "remove" === t && -1 !== a && o.splice(a, 1);
                        const n = JSON.stringify(o);
                        O.log("unselectedString", n), localStorage.setItem("unselectedCustomTools", n), O.log(`Updated unselectedCustomTools in localStorage for ${e}, action: ${t}`, o)
                    } catch (i) {}
                },
                ee = y(!1);
            b((() => {
                ee.value = window.matchMedia("(prefers-color-scheme: dark)").matches, window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change", (e => {
                    ee.value = e.matches
                }))
            })), g((() => ee.value ? xe : Me));
            b((async () => {
                try {
                    if (!1 === (null == Z ? void 0 : Z.value)) return void(location.href = "/login");
                    o.value = !0, i.value = await (async () => {
                        try {
                            const e = await fetch("/api/personalization/get_installed_custom_tools"),
                                t = await e.json();
                            let i = [];
                            if (0 === t.status) {
                                J.value = t.encrypted_cogen_id;
                                const e = t.installed_custom_tools || {},
                                    o = new Set(Object.keys(e));
                                O.log("installedCustomToolNames:", o), O.log("installedCustomToolsDict:", e);
                                const a = t.installed_mcp_tools || {};
                                O.log("installedMcpToolsData:", a), i = Nt(se).map((e => {
                                    const t = o.has(e.name) || e.mcp_id && a && a.hasOwnProperty(e.mcp_id);
                                    return { ...e,
                                        installed: t,
                                        isMcp: !!e.isMcp
                                    }
                                }));
                                for (const t in a)
                                    if (!Nt(se).some((e => e.mcp_id === t)) && a.hasOwnProperty(t)) {
                                        const e = a[t];
                                        let o = t,
                                            n = "Custom MCP Tool",
                                            r = null,
                                            s = "",
                                            c = "",
                                            l = [];
                                        "object" == typeof e && null !== e ? (o = e.name || e.title || t, n = e.description || n, (e.icon || e.iconUrl) && (r = Wt(e.icon || e.iconUrl)), s = e.url || "", c = e.type || "", l = e.tools || [], n = e.description || n) : "string" == typeof e && (o = e);
                                        const p = i.findIndex((e => e.name === t)); - 1 !== p ? (i[p].installed = !0, i[p].isMcp = !0, i[p].title = o, i[p].description = n, i[p].icon = r && x(r) || x(Ae), i[p].url = s, i[p].type = c, i[p].mcpTools = l) : i.push({
                                            id: t,
                                            name: t,
                                            title: o,
                                            description: n,
                                            installed: !0,
                                            icon: r && x(r) || x(Ae),
                                            isMcp: !0,
                                            url: s,
                                            type: c,
                                            mcpTools: l
                                        })
                                    }
                                const n = i.findIndex((e => "google_suite" === e.name));
                                if (-1 !== n) {
                                    const e = o.has("gmail"),
                                        t = o.has("google_calendar"),
                                        a = o.has("google_drive");
                                    i[n].installed = e && t && a
                                }
                                const r = i.findIndex((e => "microsoft_365" === e.name));
                                if (-1 !== r) {
                                    const e = o.has("outlook_email"),
                                        t = o.has("outlook_calendar"),
                                        a = o.has("microsoft_teams"),
                                        n = o.has("microsoft_onedrive");
                                    i[r].installed = e && t && a && n
                                }
                                return i
                            }
                            return Nt(se).map((e => ({ ...e,
                                installed: !1,
                                isMcp: !1
                            })))
                        } catch (e) {
                            return Nt(se).map((e => ({ ...e,
                                installed: !1,
                                isMcp: !1
                            })))
                        }
                    })(), _.value = encodeURIComponent(window.location.href)
                } catch (e) {
                    i.value = [...Nt(se)]
                } finally {
                    o.value = !1
                }
            }));
            const te = async e => {
                    d(e.name);
                    try {
                        const t = await fetch(`/api/mcp/${e.name}`, {
                                method: "DELETE",
                                headers: {}
                            }),
                            o = await t.json();
                        0 === o.status ? (i.value = i.value.filter((t => t.name !== e.name)), X(e.name, null, "remove"), $(e.name, "remove"), Q((() => {
                            V.emit(H.TOOLS_UPDATED)
                        }))) : alert(`${se("components.install_custom_tools.failed_to_uninstall_mcp_tool")} ${e.title}: ${o.message}`)
                    } catch (t) {
                        alert(`${se("components.install_custom_tools.error_uninstalling_mcp_tool")} ${e.title}: ${t.message}`)
                    } finally {
                        m(e.name)
                    }
                },
                ie = async e => {
                    O.log("ZZH handleToggleMCPToolInstall", e), !1 !== Z.value ? e.installed ? (async e => {
                        d(e.name);
                        try {
                            const t = await fetch(`/api/mcp/${e.mcp_id||e.name}`, {
                                    method: "DELETE"
                                }),
                                i = await t.json();
                            0 === i.status ? (e.installed = !1, X(e.mcp_id, null, "remove"), $(e.mcp_id, "remove"), Q((() => {
                                O.log("ZZH handleUninstallHostedMCPTool", i), V.emit(H.TOOLS_UPDATED)
                            }))) : alert(`Failed to uninstall MCP tool ${e.title}: ${i.message}`)
                        } catch (t) {
                            alert(`Error uninstalling MCP tool ${e.title}: ${t.message}`)
                        } finally {
                            m(e.name)
                        }
                    })(e) : oe(e) : location.href = "/login"
                },
                oe = async e => {
                    p(e.name);
                    const t = "genspark_browser_mcp" === e.type ? "/api/genspark_browser_mcp/register" : "/api/mcp/register",
                        i = await fetch(t, {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify({
                                mcp_id: e.mcp_id,
                                url: e.url,
                                name: e.name,
                                type: e.type,
                                request_header: e.requestHeader,
                                description: e.description
                            })
                        }),
                        o = await i.json();
                    0 === o.status ? (O.log("ZZH added newMCPTool", o), e.installed = !0, X(e.mcp_id, e.title || e.name, "add"), Q((() => {
                        V.emit(H.TOOLS_UPDATED)
                    })), m(e.name)) : m(e.name)
                },
                ae = async () => {
                    var e, t, a, n;
                    try {
                        if (!1 === Z.value) return void(location.href = "/login");
                        f.value = !0, o.value = !0;
                        const r = "genspark_browser_mcp" === h.value.type ? "/api/genspark_browser_mcp/register" : "/api/mcp/register",
                            s = await fetch(r, {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json"
                                },
                                body: JSON.stringify({
                                    url: h.value.url,
                                    name: h.value.name,
                                    description: h.value.description,
                                    request_header: h.value.requestHeader,
                                    type: h.value.type
                                })
                            }),
                            c = await s.json();
                        if (0 === c.status) {
                            Wt(h.value.icon);
                            const o = (null == (e = c.data) ? void 0 : e.mcp_id) || h.value.name;
                            O.log("toolIdentifier", o), X(o, h.value.name, "add");
                            const r = (null == (t = c.data) ? void 0 : t.mcp_config) || {},
                                s = {
                                    id: o,
                                    name: o,
                                    title: (null == (a = null == r ? void 0 : r.info) ? void 0 : a.title) || h.value.name,
                                    url: h.value.url,
                                    description: (null == (n = null == r ? void 0 : r.info) ? void 0 : n.description) || h.value.description,
                                    installed: !0,
                                    isMcp: !0,
                                    icon: x(Ae),
                                    requestHeader: h.value.requestHeader,
                                    type: h.value.type,
                                    mcpTools: (null == r ? void 0 : r.tools) || [],
                                    genspark_browser_required: h.value.genspark_browser_required
                                };
                            i.value.push(s), h.value = {
                                name: "",
                                url: "",
                                description: "",
                                requestHeader: ""
                            }, v.value = "", u.value = !1, V.emit(H.TOOLS_UPDATED)
                        } else alert(`${se("components.install_custom_tools.failed_to_register_mcp_server")}: ${c.message}`)
                    } catch (r) {
                        alert(se("components.install_custom_tools.error_failed_to_add_mcp_server") + (r.message || se("components.install_custom_tools.failed_to_add_mcp_server")))
                    } finally {
                        o.value = !1, f.value = !1
                    }
                },
                ne = y({
                    visible: !1,
                    tool: null,
                    position: {
                        top: 0,
                        left: 0
                    },
                    sticky: !1
                }),
                re = (e, t) => {
                    const i = e.target.getBoundingClientRect(),
                        o = window.innerWidth;
                    let a = i.left + window.scrollX;
                    a + 300 > o - 20 && (a = o - 300 - 20), ne.value = {
                        visible: !0,
                        tool: t,
                        position: {
                            top: i.bottom + window.scrollY + 5,
                            left: a
                        },
                        sticky: !1
                    }
                };
            g((() => {
                if (!ne.value.visible) return {};
                return {
                    left: `${ne.value.position.arrowLeft||20}px`
                }
            }));
            b((() => {
                document.addEventListener("click", (() => {
                    ne.value.visible && ne.value.sticky && (ne.value.visible = !1, ne.value.sticky = !1)
                }))
            })), Pe();
            const {
                t: se
            } = S(), ce = g((() => {
                const e = new Map;
                return (ti || []).forEach((t => {
                    t.category && Array.isArray(t.category) && t.category.forEach((t => {
                        e.set(t, (e.get(t) || 0) + 1)
                    }))
                })), Array.from(e.entries()).sort(((e, t) => t[1] - e[1])).map((([e]) => e))
            })), le = e => {
                const t = ti || [];
                return "ALL" === e ? t.length : t.filter((t => t.category && Array.isArray(t.category) && t.category.includes(e))).length
            }, pe = g((() => {
                const e = ti || [];
                return "ALL" === P.value ? e : e.filter((e => e.category && Array.isArray(e.category) && e.category.includes(P.value)))
            })), de = e => e.replace(/[-_]/g, " ").replace(/\b\w/g, (e => e.toUpperCase())), me = () => {
                window && window.open("https://genspark.ai/browser", "_blank")
            };
            return (t, a) => (C(), w(L, null, [!1 === j(Z) ? (C(), w("div", ii)) : e.isModal ? (C(), w("div", oi, [A("div", {
                class: "modal-wrapper",
                onClick: a[4] || (a[4] = R((e => t.$emit("close")), ["self"]))
            }, [A("div", ai, [A("div", ni, [A("h1", ri, I(t.$t("components.install_custom_tools.tools")), 1), A("div", si, [A("div", {
                class: "close",
                onClick: a[0] || (a[0] = e => t.$emit("close"))
            }, "×")])]), o.value ? (C(), w("div", ci, [a[23] || (a[23] = A("div", {
                class: "loading-spinner"
            }, null, -1)), A("p", null, I(t.$t("components.install_custom_tools.loading_tools")), 1)])) : (C(), w("div", li, [A("div", pi, [(C(!0), w(L, null, D(n.value, (e => (C(), w("div", {
                key: e.id,
                class: "tool-card"
            }, [A("div", di, [A("div", mi, [A("div", {
                class: z(0 === e.id ? "tool-icon-google-suite" : "tool-icon")
            }, ["string" == typeof e.icon && (e.icon.startsWith("http") || e.icon.startsWith("data:image") || e.icon.includes(".png") || e.icon.includes(".jpg") || e.icon.includes(".jpeg") || e.icon.includes(".gif") || e.icon.startsWith("/_nuxt/")) ? (C(), w("img", {
                key: 0,
                src: e.icon,
                alt: "Tool Icon"
            }, null, 8, ui)) : e.icon ? (C(), E(F(e.icon), {
                key: 1,
                class: z({
                    google_suite_icon: "google_suite" === e.name
                }),
                style: {
                    width: "96px",
                    height: "32px"
                }
            }, null, 8, ["class"])) : (C(), w("div", gi, I(e.title.charAt(0)), 1))], 2), A("div", hi, [A("h3", vi, I(e.title), 1)])]), A("button", {
                class: z(["tool-action-btn", s(e.name) ? "installing-btn" : c(e.name) ? "uninstalling-btn" : e.installed ? "uninstall-btn" : "install-btn"]),
                onClick: t => (e => {
                    if (O.log("ZZH handleToolInstall", e), !1 !== Z.value) return e.installed ? void(e.isMcp ? te(e) : (d(e.name), fetch(e.revoke_url).then((e => {
                        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                        return e.json()
                    })).then((t => {
                        if (O.log("ZZH Uninstalled tool:", t), 0 === t.status)
                            if (t.installed_custom_tools) {
                                localStorage.setItem("installedCustomTools", JSON.stringify(t.installed_custom_tools)), i.value = i.value.map((e => ({ ...e,
                                    installed: t.installed_custom_tools && t.installed_custom_tools.hasOwnProperty(e.name)
                                })));
                                const e = i.value.findIndex((e => "google_suite" === e.name));
                                if (-1 !== e) {
                                    const o = t.installed_custom_tools && t.installed_custom_tools.hasOwnProperty("gmail"),
                                        a = t.installed_custom_tools && t.installed_custom_tools.hasOwnProperty("google_calendar"),
                                        n = t.installed_custom_tools && t.installed_custom_tools.hasOwnProperty("google_drive");
                                    i.value[e].installed = o && a && n
                                }
                                const o = i.value.findIndex((e => "microsoft_365" === e.name));
                                if (-1 !== o) {
                                    const e = t.installed_custom_tools || {},
                                        a = e.hasOwnProperty("outlook_email"),
                                        n = e.hasOwnProperty("outlook_calendar"),
                                        r = e.hasOwnProperty("microsoft_teams"),
                                        s = e.hasOwnProperty("microsoft_onedrive");
                                    i.value[o].installed = a && n && r && s
                                }
                                V.emit(H.TOOLS_UPDATED)
                            } else {
                                const t = i.value.findIndex((t => t.name === e.name)); - 1 !== t && (i.value[t].installed = !1), "google_suite" !== e.name && (X(e.name, null, "remove"), $(e.name, "remove")), Q((() => {
                                    V.emit(H.TOOLS_UPDATED)
                                }))
                            }
                    })).catch((e => {})).finally((() => {
                        m(e.name)
                    })))) : (p(e.name), O.log("ZZH Installing tool:", e.name), "google_suite" !== e.name && (X(e.name, e.title || !0, "add"), $(e.name, "remove")), void setTimeout((() => {
                        const t = e.oauth_url.includes("?") ? "&" : "?";
                        let i = `${e.oauth_url}${t}redirect_url=${_.value}`;
                        if (O.isGensparkAppAndroid() || O.isGensparkAppIos() && Y.value) {
                            const o = encodeURIComponent(J.value);
                            i = `genspark://oauth/login?url=${encodeURIComponent(e.oauth_url+t+"encrypted_cogen_id="+o+"&redirect_url=genspark://oauth/redirect?url="+encodeURIComponent(_.value))}`
                        }
                        O.log("Auth URL:", i), window.location.href = i, V.emit(H.TOOLS_UPDATED)
                    }), 500));
                    location.href = "/login"
                })(e),
                disabled: l(e.name)
            }, [l(e.name) ? (C(), w("span", bi)) : (C(), w("span", fi, I(e.installed ? t.$t("components.install_custom_tools.uninstall") : t.$t("components.install_custom_tools.install")), 1))], 10, yi)]), a[24] || (a[24] = A("div", {
                class: "tool-card-divider"
            }, null, -1)), A("div", ki, [A("p", wi, I(e.description), 1)])])))), 128))]), A("div", Ai, [a[25] || (a[25] = A("div", {
                class: "divider-line"
            }, null, -1)), A("h2", null, I(t.$t("components.install_custom_tools.mcp_tools")), 1), a[26] || (a[26] = A("div", {
                class: "divider-line"
            }, null, -1))]), A("button", {
                class: "add-server-btn",
                onClick: a[1] || (a[1] = e => u.value = !0)
            }, [a[27] || (a[27] = A("svg", {
                width: "16",
                height: "16",
                viewBox: "0 0 16 16",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg"
            }, [A("path", {
                d: "M8 3V13",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round"
            }), A("path", {
                d: "M3 8H13",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round"
            })], -1)), A("span", null, I(t.$t("components.install_custom_tools.add_new_mcp_server")), 1)]), A("div", Ci, [(C(!0), w(L, null, D(r.value, (e => (C(), w("div", {
                key: e.id,
                class: "tool-card"
            }, [A("div", Mi, [A("div", xi, [A("div", Pi, ["string" == typeof e.icon && (e.icon.startsWith("http") || e.icon.startsWith("data:image") || e.icon.includes(".png") || e.icon.includes(".jpg") || e.icon.includes(".jpeg") || e.icon.includes(".gif") || e.icon.startsWith("/_nuxt/")) ? (C(), w("img", {
                key: 0,
                src: e.icon,
                alt: "Tool Icon"
            }, null, 8, Si)) : e.icon ? (C(), E(F(e.icon), {
                key: 1
            })) : (C(), w("div", Ti, I(e.isMcp ? "MCP" : "Custom"), 1))]), A("div", Ii, [A("h3", _i, I(e.title), 1)])]), e.genspark_browser_required && !j(W) ? (C(), w("button", {
                key: 0,
                class: "browser-required-btn",
                onClick: me
            }, I(t.$t("components.install_custom_tools.genspark_browser_required")), 1)) : (C(), w("button", {
                key: 1,
                class: z(["tool-action-btn", s(e.name) ? "installing-btn" : c(e.name) ? "uninstalling-btn" : e.installed ? "uninstall-btn" : "install-btn"]),
                onClick: t => ie(e),
                disabled: l(e.name)
            }, [l(e.name) ? (C(), w("span", Di)) : (C(), w("span", Ei, I(e.installed ? t.$t("components.install_custom_tools.uninstall") : t.$t("components.install_custom_tools.install")), 1))], 10, Li))]), a[28] || (a[28] = A("div", {
                class: "tool-card-divider"
            }, null, -1)), A("div", Fi, [A("p", Oi, I(e.description), 1), A("div", Bi, [e.mcpTools && e.mcpTools.length > 0 ? (C(), w("div", ji, [(C(!0), w(L, null, D(e.mcpTools, (e => (C(), w("span", {
                key: e.name,
                class: "mcp-tool-tag",
                onMouseover: t => re(t, e),
                onMouseout: a[2] || (a[2] = e => {
                    ne.value.sticky || (ne.value.visible = !1)
                }),
                onClick: t => ((e, t) => {
                    if (e.stopPropagation(), ne.value.visible && ne.value.sticky && ne.value.tool.name === t.name) return ne.value.visible = !1, void(ne.value.sticky = !1);
                    re(e, t), ne.value.sticky = !0
                })(t, e)
            }, I(e.name), 41, zi)))), 128))])) : B("", !0)])])])))), 128)), 0 === r.value.length ? (C(), w("div", Ri, [A("p", Gi, I(t.$t("components.install_custom_tools.no_mcp_servers_added_yet")), 1)])) : B("", !0)]), A("div", qi, [a[29] || (a[29] = A("div", {
                class: "divider-line"
            }, null, -1)), A("h2", null, I(t.$t("components.install_custom_tools.mcp_tools_from_community")), 1), a[30] || (a[30] = A("div", {
                class: "divider-line"
            }, null, -1))]), A("div", Ni, [A("div", Ui, [A("span", {
                class: z(["category-filter-tag", {
                    active: "ALL" === P.value
                }]),
                onClick: a[3] || (a[3] = e => P.value = "ALL")
            }, " ALL (" + I(le("ALL")) + ") ", 3), (C(!0), w(L, null, D(ce.value, (e => (C(), w("span", {
                key: e,
                class: z(["category-filter-tag", {
                    active: P.value === e
                }]),
                onClick: t => P.value = e
            }, I(de(e)) + " (" + I(le(e)) + ") ", 11, Wi)))), 128))])]), A("div", Vi, [(C(!0), w(L, null, D(pe.value, (e => (C(), w("div", {
                key: e.id,
                class: "tool-card clickable-card",
                onClick: t => {
                    var i;
                    (i = e.link) && window.open(i, "_blank", "noopener,noreferrer")
                }
            }, [A("div", Qi, [A("div", Ki, [a[31] || (a[31] = A("div", {
                class: "tool-icon"
            }, [A("div", {
                class: "default-mcp-icon"
            }, "MCP")], -1)), A("div", Zi, [A("h3", Ji, I(e.title), 1), e.category && e.category.length > 0 ? (C(), w("div", Yi, [(C(!0), w(L, null, D(e.category.slice(0, 2), (e => (C(), w("span", {
                key: e,
                class: "category-tag"
            }, I(de(e)), 1)))), 128))])) : B("", !0)])]), a[32] || (a[32] = M('<div class="external-link-icon" data-v-f99eab88><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-f99eab88><path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-f99eab88></path><path d="M15 3h6v6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-f99eab88></path><path d="M10 14L21 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-f99eab88></path></svg></div>', 1))]), a[33] || (a[33] = A("div", {
                class: "tool-card-divider"
            }, null, -1)), A("div", Xi, [A("p", $i, I(e.description), 1), A("div", eo, [A("span", to, I(e.name), 1)])])], 8, Hi)))), 128))])]))])])])) : B("", !0), u.value ? (C(), w("div", {
                key: 2,
                class: "modal-overlay",
                onClick: a[22] || (a[22] = R((e => u.value = !1), ["self"]))
            }, [A("div", io, [A("div", oo, [A("h2", null, I(t.$t("components.install_custom_tools.add_new_mcp_server")), 1), A("button", {
                class: "close-btn",
                onClick: a[5] || (a[5] = e => u.value = !1),
                disabled: f.value
            }, " × ", 8, ao)]), A("div", no, [A("div", {
                class: "form-group",
                onClick: a[8] || (a[8] = R((() => {}), ["stop"]))
            }, [A("label", ro, I(t.$t("components.install_custom_tools.server_name")), 1), G(A("input", {
                id: "toolTitle",
                "onUpdate:modelValue": a[6] || (a[6] = e => h.value.name = e),
                type: "text",
                placeholder: t.$t("components.install_custom_tools.server_name"),
                onClick: a[7] || (a[7] = R((() => {}), ["stop"])),
                disabled: f.value
            }, null, 8, so), [
                [q, h.value.name]
            ])]), A("div", {
                class: "form-group",
                onClick: a[11] || (a[11] = R((() => {}), ["stop"]))
            }, [A("label", null, I(t.$t("components.install_custom_tools.server_type")), 1), A("div", co, [A("div", lo, [G(A("input", {
                id: "typeSSE",
                type: "radio",
                "onUpdate:modelValue": a[9] || (a[9] = e => h.value.type = e),
                value: "sse",
                disabled: f.value
            }, null, 8, po), [
                [N, h.value.type]
            ]), A("label", mo, I(t.$t("components.install_custom_tools.sse")), 1)]), A("div", uo, [G(A("input", {
                id: "typeStreamableHttp",
                type: "radio",
                "onUpdate:modelValue": a[10] || (a[10] = e => h.value.type = e),
                value: "streamableHttp",
                disabled: f.value
            }, null, 8, go), [
                [N, h.value.type]
            ]), A("label", ho, I(t.$t("components.install_custom_tools.streamable_http")), 1)])])]), A("div", {
                class: "form-group",
                onClick: a[14] || (a[14] = R((() => {}), ["stop"]))
            }, [A("label", vo, I(t.$t("components.install_custom_tools.server_url")), 1), G(A("input", {
                id: "toolIcon",
                "onUpdate:modelValue": a[12] || (a[12] = e => h.value.url = e),
                type: "text",
                placeholder: "https://example.com/sse",
                onClick: a[13] || (a[13] = R((() => {}), ["stop"])),
                disabled: f.value
            }, null, 8, yo), [
                [q, h.value.url]
            ])]), A("div", {
                class: "form-group",
                onClick: a[17] || (a[17] = R((() => {}), ["stop"]))
            }, [A("label", bo, I(t.$t("components.install_custom_tools.description")), 1), G(A("textarea", {
                id: "toolDescription",
                "onUpdate:modelValue": a[15] || (a[15] = e => h.value.description = e),
                placeholder: t.$t("components.install_custom_tools.enter_server_description"),
                onClick: a[16] || (a[16] = R((() => {}), ["stop"])),
                disabled: f.value
            }, null, 8, fo), [
                [q, h.value.description]
            ])]), A("div", {
                class: "form-group",
                onClick: a[20] || (a[20] = R((() => {}), ["stop"]))
            }, [A("label", ko, I(t.$t("components.install_custom_tools.request_header")), 1), G(A("textarea", {
                id: "requestHeader",
                "onUpdate:modelValue": a[18] || (a[18] = e => h.value.requestHeader = e),
                placeholder: '{"Content-Type": "application/json", "Authorization": "Bearer token"}',
                onClick: a[19] || (a[19] = R((() => {}), ["stop"])),
                disabled: f.value
            }, null, 8, wo), [
                [q, h.value.requestHeader]
            ])])]), A("div", Ao, [A("button", {
                class: "cancel-btn",
                onClick: a[21] || (a[21] = e => u.value = !1),
                disabled: f.value
            }, I(t.$t("components.install_custom_tools.cancel")), 9, Co), A("button", {
                class: "add-btn",
                onClick: ae,
                disabled: f.value
            }, [f.value ? (C(), w("span", xo)) : (C(), w("span", Po, I(t.$t("components.install_custom_tools.add_server")), 1))], 8, Mo)])])])) : B("", !0), ne.value.visible ? (C(), w("div", {
                key: 3,
                class: z(["tool-tooltip", {
                    "tooltip-sticky": ne.value.sticky
                }]),
                style: U({
                    top: `${ne.value.position.top}px`,
                    left: `${ne.value.position.left}px`
                })
            }, [a[34] || (a[34] = A("div", {
                class: "tooltip-arrow"
            }, null, -1)), A("div", So, [A("div", To, I(ne.value.tool.name), 1), ne.value.tool.description ? (C(), w("div", Io, I(ne.value.tool.description), 1)) : B("", !0)])], 6)) : B("", !0)], 64))
        }
    }, [
        ["__scopeId", "data-v-f99eab88"]
    ]),
    Lo = ["src"],
    Do = {
        key: 1,
        class: "tool-icon"
    },
    Eo = {
        class: "tool-icon"
    },
    Fo = ["src"],
    Oo = {
        key: 1,
        class: "tool-icon"
    },
    Bo = {
        class: "tool-count"
    },
    jo = P({
        __name: "custom_tools_icon_button",
        props: {
            shouldShowTipsTriangleIcon: {
                type: Boolean,
                default: !0
            }
        },
        setup(e) {
            const t = y({}),
                i = y("default"),
                o = y([]),
                a = y(0),
                n = y(!1),
                r = y(!1),
                s = y(null),
                c = y(null),
                l = y(null),
                p = y(localStorage.getItem("toolsPromotionHidden") || !1);
            k("currentUser"), b((() => {
                u(), window.addEventListener("click", S), window.addEventListener("scroll", x, !0), window.addEventListener("resize", x), window.addEventListener("storage", d), V.on(H.OPEN_INSTALL_CUSTOM_TOOLS, _), V.on(H.TOOLS_UPDATED, u), V.on(H.PROMOTION_VISIBILITY_CHANGED, m)
            })), K((() => {
                window.removeEventListener("click", S), window.removeEventListener("scroll", x, !0), window.removeEventListener("resize", x), window.removeEventListener("storage", d), V.off(H.OPEN_INSTALL_CUSTOM_TOOLS, _), V.off(H.TOOLS_UPDATED, u), V.off(H.PROMOTION_VISIBILITY_CHANGED, m)
            }));
            const d = e => {
                    "installedCustomTools" === e.key ? u() : "toolsPromotionHidden" === e.key && (p.value = e.newValue)
                },
                m = ({
                    isVisible: e
                }) => {
                    p.value = !e
                },
                u = () => {
                    try {
                        const e = localStorage.getItem("installedCustomTools");
                        if (e) {
                            const i = JSON.parse(e);
                            i.google_authorized_scopes && delete i.google_authorized_scopes, t.value = i, g()
                        } else t.value = {}, g()
                    } catch (e) {
                        t.value = {}, g()
                    }
                },
                g = () => {
                    const e = Object.entries(t.value).map((([e, t]) => ({
                        name: e,
                        config: t
                    })));
                    O.log("ZZH toolsList", e), 0 === e.length ? (i.value = "default", o.value = []) : e.length <= 2 ? (i.value = "tools", o.value = e) : (i.value = "tools-plus", o.value = e.slice(0, 2), a.value = e.length - 2)
                },
                h = e => {
                    const t = {
                        gmail: de,
                        google_calendar: me,
                        google_drive: ue,
                        notion: ge
                    };
                    if (e.startsWith("mcp_")) {
                        const t = Ut.find((t => t.mcp_id === e));
                        return t && t.icon ? t.icon : Ae
                    }
                    return t[e] || gt
                },
                v = () => {
                    0 === Object.entries(t.value).map((([e, t]) => ({
                        name: e,
                        config: t
                    }))).length ? n.value = !0 : r.value = !r.value
                },
                f = () => {
                    n.value = !1, p.value = !1, V.emit(H.CUSTOM_TOOLS_CLOSED)
                },
                M = () => {
                    r.value = !1, n.value = !0
                },
                x = () => {
                    r.value && P()
                };
            T(r, (e => {
                e && Q(P)
            })), T(t, ((e, t) => {
                g()
            }), {
                deep: !0
            });
            const P = () => {
                    Q((() => {
                        if (l.value && s.value) {
                            const e = l.value,
                                i = s.value,
                                o = e.getBoundingClientRect(),
                                a = window.innerHeight - o.bottom,
                                n = o.top,
                                r = o.left + o.width / 2;
                            i.style.position = "fixed", i.style.left = `${r}px`, i.style.transform = "translateX(-20%)", i.style.visibility = "hidden", i.style.top = "0px", i.style.bottom = "auto", Q((() => {
                                const e = i.offsetHeight,
                                    r = e > 0 ? e : (() => {
                                        const e = Object.keys(t.value).length;
                                        if (0 === e) return 96;
                                        const i = 32 * e + 8 * Math.max(0, e - 1);
                                        return 76 + Math.min(i, 240) + 12
                                    })();
                                i.style.visibility = "visible", a < r && n > r ? (i.style.top = o.top - r - 5 + "px", i.style.bottom = "auto", i.classList.add("dropdown-above"), i.classList.remove("dropdown-below")) : (i.style.top = `${o.bottom+5}px`, i.style.bottom = "auto", i.classList.add("dropdown-below"), i.classList.remove("dropdown-above"))
                            }))
                        }
                    }))
                },
                S = e => {
                    r.value && s.value && !s.value.contains(e.target) && !e.target.closest(".custom-tools-icon-container") && (r.value = !1)
                },
                _ = e => {
                    n.value = !0, p.value = !0
                };
            return (t, d) => (C(), w(L, null, [n.value ? (C(), E(_o, {
                key: 0,
                isModal: !0,
                onClose: f
            })) : B("", !0), A("div", {
                class: "custom-tools-icon-container",
                ref_key: "iconContainerRef",
                ref: l
            }, ["default" === i.value ? (C(), w("div", {
                key: 0,
                class: z(["icon-wrapper text-[#909499] hover:bg-[#f0f0f0] dark:hover:bg-[#444]"]),
                onClick: v
            }, [(C(), E(F(j(gt))))])) : "tools" === i.value ? (C(), w("div", {
                key: 1,
                class: z(["tools-wrapper text-[#909499]"]),
                onClick: v
            }, [(C(!0), w(L, null, D(o.value, ((e, t) => (C(), w(L, {
                key: e.name
            }, ["string" == typeof h(e.name) && (h(e.name).startsWith("http") || h(e.name).startsWith("data:image") || h(e.name).includes(".png") || h(e.name).includes(".jpg") || h(e.name).includes(".jpeg") || h(e.name).includes(".gif") || h(e.name).startsWith("/_nuxt/")) ? (C(), w("img", {
                key: 0,
                src: h(e.name),
                alt: "Tool Icon",
                class: "tool-icon"
            }, null, 8, Lo)) : (C(), w("div", Do, [(C(), E(F(h(e.name))))]))], 64)))), 128)), A("div", Eo, [(C(), E(F(j(gt)), {
                class: z(["text-[#909499]"])
            }))])])) : "tools-plus" === i.value ? (C(), w("div", {
                key: 2,
                class: z(["tools-wrapper text-[#909499]"]),
                onClick: v
            }, [(C(!0), w(L, null, D(o.value, ((e, t) => (C(), w(L, {
                key: e.name
            }, ["string" == typeof h(e.name) && (h(e.name).startsWith("http") || h(e.name).startsWith("data:image") || h(e.name).includes(".png") || h(e.name).includes(".jpg") || h(e.name).includes(".jpeg") || h(e.name).includes(".gif") || h(e.name).startsWith("/_nuxt/")) ? (C(), w("img", {
                key: 0,
                src: h(e.name),
                alt: "Tool Icon",
                class: "tool-icon"
            }, null, 8, Fo)) : (C(), w("div", Oo, [(C(), E(F(h(e.name)), {
                class: z(["text-[#909499]"])
            }))]))], 64)))), 128)), A("div", Bo, "+" + I(a.value), 1)])) : B("", !0), (C(), E(J, {
                to: "body"
            }, [r.value ? (C(), w("div", {
                key: 0,
                class: "custom-tools-dropdown",
                ref_key: "modalRef",
                ref: s
            }, [Z(ei, {
                onClose: d[0] || (d[0] = e => r.value = !1),
                onAddTools: M
            })], 512)) : B("", !0)])), r.value || n.value || p.value || !e.shouldShowTipsTriangleIcon ? B("", !0) : (C(), w("div", {
                key: 3,
                class: "promption-tips-wrapper",
                ref_key: "tipsRef",
                ref: c
            }, [Z(j(vt))], 512))], 512)], 64))
        }
    }, [
        ["__scopeId", "data-v-effa800b"]
    ]),
    zo = ["src", "alt"],
    Ro = {
        class: "model-label"
    },
    Go = {
        class: "dropdown-content"
    },
    qo = ["onClick"],
    No = {
        class: "row"
    },
    Uo = {
        class: "left"
    },
    Wo = {
        class: "icon"
    },
    Vo = ["src", "alt"],
    Ho = {
        class: "text"
    },
    Qo = {
        class: "right"
    },
    Ko = ["checked"],
    Zo = {
        key: 0,
        class: "description"
    },
    Jo = {
        class: "text"
    },
    Yo = P({
        __name: "ModelSelectionButton",
        props: {
            availableModels: {
                type: Array,
                required: !0,
                default: () => []
            },
            defaultModel: {
                type: String,
                default: null
            },
            disabled: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["modelChanged"],
        setup(e, {
            emit: t
        }) {
            const i = e,
                o = t,
                a = y(null),
                n = y(null),
                r = y(!1),
                s = y(null);
            b((() => {
                if (i.defaultModel && i.availableModels.length > 0) {
                    const e = i.availableModels.find((e => e.name === i.defaultModel));
                    e && (s.value = e)
                }!s.value && i.availableModels.length > 0 && (s.value = i.availableModels[0], o("modelChanged", s.value.name)), document.addEventListener("click", p), window.addEventListener("scroll", d, !0), window.addEventListener("resize", d)
            })), K((() => {
                document.removeEventListener("click", p), window.removeEventListener("scroll", d, !0), window.removeEventListener("resize", d)
            }));
            const c = () => {
                    i.disabled || l()
                },
                l = () => {
                    r.value = !r.value, r.value && Q(d)
                },
                p = e => {
                    r.value && a.value && n.value && !a.value.contains(e.target) && !n.value.contains(e.target) && (r.value = !1)
                },
                d = () => {
                    Q((() => {
                        if (a.value && n.value) {
                            const e = a.value,
                                t = n.value,
                                i = e.getBoundingClientRect(),
                                o = window.innerHeight - i.bottom,
                                r = i.top,
                                s = i.left;
                            t.style.position = "fixed", t.style.left = `${s}px`, t.style.transform = "translateX(0)", t.style.visibility = "hidden", t.style.top = "0px", t.style.bottom = "auto", Q((() => {
                                const e = t.offsetHeight;
                                t.style.visibility = "visible", o < e && r > e ? (t.style.top = i.top - e - 8 + "px", t.style.bottom = "auto", t.classList.add("dropdown-above"), t.classList.remove("dropdown-below")) : (t.style.top = `${i.bottom+8}px`, t.style.bottom = "auto", t.classList.add("dropdown-below"), t.classList.remove("dropdown-above"))
                            }))
                        }
                    }))
                };
            return T(r, (e => {
                e && Q(d)
            })), T((() => i.defaultModel), (e => {
                if (e && i.availableModels.length > 0) {
                    const t = i.availableModels.find((t => t.name === e));
                    t && (s.value = t)
                }
            }), {
                immediate: !0
            }), T((() => i.availableModels), (e => {
                e.length > 0 && !s.value && (s.value = e[0], o("modelChanged", s.value.name))
            }), {
                immediate: !0
            }), (t, l) => {
                var p;
                return C(), w("div", {
                    class: "model-selection-container",
                    ref_key: "containerRef",
                    ref: a
                }, [A("div", {
                    class: z(["model-selection-button", {
                        active: r.value,
                        disabled: e.disabled
                    }]),
                    onClick: c
                }, ["object" == typeof s.value.icon ? (C(), E(F(s.value.icon), {
                    key: 0,
                    class: "model-icon"
                })) : (C(), w("img", {
                    key: 1,
                    src: s.value.icon,
                    alt: s.value.label,
                    class: "model-icon"
                }, null, 8, zo)), A("span", Ro, I((null == (p = s.value) ? void 0 : p.label) || "Select Model"), 1), (C(), w("svg", {
                    class: z(["dropdown-icon", {
                        rotated: r.value
                    }]),
                    viewBox: "0 0 24 24",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, l[0] || (l[0] = [A("path", {
                    d: "M7 10L12 15L17 10",
                    stroke: "currentColor",
                    "stroke-width": "2",
                    "stroke-linecap": "round",
                    "stroke-linejoin": "round"
                }, null, -1)]), 2))], 2), (C(), E(J, {
                    to: "body"
                }, [r.value ? (C(), w("div", {
                    key: 0,
                    class: "model-dropdown",
                    ref_key: "dropdownRef",
                    ref: n
                }, [A("div", Go, [(C(!0), w(L, null, D(e.availableModels, (e => {
                    var t, a;
                    return C(), w("div", {
                        key: e.name,
                        class: z(["model-option", {
                            selected: (null == (t = s.value) ? void 0 : t.name) === e.name
                        }]),
                        onClick: t => (e => {
                            i.disabled || (s.value = e, r.value = !1, o("modelChanged", e.name))
                        })(e)
                    }, [A("div", No, [A("div", Uo, [A("div", Wo, ["object" == typeof e.icon ? (C(), E(F(e.icon), {
                        key: 0
                    })) : (C(), w("img", {
                        key: 1,
                        src: e.icon,
                        alt: e.label,
                        class: "model-option-icon"
                    }, null, 8, Vo))]), A("div", Ho, I(e.label), 1)]), A("div", Qo, [A("input", {
                        type: "radio",
                        name: "model",
                        checked: (null == (a = s.value) ? void 0 : a.name) === e.name,
                        readonly: ""
                    }, null, 8, Ko)])]), e.description ? (C(), w("div", Zo, [A("div", Jo, I(e.description), 1)])) : B("", !0)], 10, qo)
                })), 128))])], 512)) : B("", !0)]))], 512)
            }
        }
    }, [
        ["__scopeId", "data-v-2c74e158"]
    ]),
    Xo = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const $o = {
        render: function(e, t) {
            return C(), w("svg", Xo, t[0] || (t[0] = [M('<g id="vuesax/linear/microphone-2"><g id="vuesax/linear/microphone-2_2"><g id="microphone-2"><path id="Vector" d="M13.3268 9.57878V4.99544C13.3268 3.15378 11.8352 1.66211 9.99349 1.66211C8.15182 1.66211 6.66016 3.15378 6.66016 4.99544V9.57878C6.66016 11.4204 8.15182 12.9121 9.99349 12.9121C11.8352 12.9121 13.3268 11.4204 13.3268 9.57878Z" stroke="currentColor" stroke-width="1.43" stroke-linecap="round" stroke-linejoin="round"></path><path id="Vector_2" d="M3.62439 8.04688V9.46354C3.62439 12.9802 6.48272 15.8385 9.99939 15.8385C13.5161 15.8385 16.3744 12.9802 16.3744 9.46354V8.04688" stroke="currentColor" stroke-width="1.42857" stroke-linecap="round" stroke-linejoin="round"></path><path id="Vector_3" d="M9.99719 15.8379V18.3379" stroke="currentColor" stroke-width="1.42857" stroke-linecap="round" stroke-linejoin="round"></path></g></g></g>', 1)]))
        }
    },
    ea = {
        __name: "InputWave",
        setup(e) {
            const t = y(null),
                i = y([]),
                o = y(0),
                a = y(null);
            let n = 0,
                r = null,
                s = null,
                c = null;
            return b((() => {
                n = Math.ceil(a.value.offsetWidth / 7), i.value = new Array(2 * n).fill(4), async function() {
                    c = await navigator.mediaDevices.getUserMedia({
                        audio: !0
                    }), s = new AudioContext;
                    const e = s.createMediaStreamSource(c),
                        t = s.createAnalyser();
                    t.fftSize = 64, e.connect(t);
                    const a = new Uint8Array(t.frequencyBinCount);
                    let l = 0,
                        p = 0;
                    ! function e() {
                        t.getByteFrequencyData(a), o.value -= .3, o.value < -2 * n * 7 && (i.value = i.value.slice(n), i.value = i.value.concat(new Array(n).fill(4)), o.value += 7 * n);
                        let s = Math.floor(a[1] / 15),
                            c = s > 20 ? 20 : s;
                        p = Math.floor(Math.abs(o.value) / 7), p !== l && (i.value[p + n - 1] = Math.max(c, 4), l = p), r = requestAnimationFrame(e)
                    }()
                }()
            })), f((() => {
                r && (cancelAnimationFrame(r), r = null), s && (s.close(), s = null), c && (c.getTracks().forEach((e => e.stop())), c = null)
            })), (e, n) => (C(), w("div", {
                class: "flex items-center justify-between flex-1 w-[1px] overflow-hidden mx-[11px]",
                ref_key: "container",
                ref: a
            }, [A("div", {
                class: "flex items-center justify-between gap-1",
                style: U({
                    transform: `translateX(${o.value}px)`
                })
            }, [(C(!0), w(L, null, D(i.value, ((e, i) => (C(), w("div", {
                ref_for: !0,
                ref_key: "bars",
                ref: t,
                key: i,
                style: U({
                    height: `${e}px`,
                    opacity: "" + (e > 4 ? 1 : .5)
                }),
                class: "w-[3px] bg-black rounded-[35px] dark:bg-gray-200 opacity-50"
            }, null, 4)))), 128))], 4)], 512))
        }
    },
    ta = {
        width: "16",
        height: "16",
        viewBox: "0 0 16 16",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const ia = {
        render: function(e, t) {
            return C(), w("svg", ta, t[0] || (t[0] = [A("path", {
                d: "M8 8C9.65683 8 11 6.65683 11 5C11 3.34317 9.65683 2 8 2C6.34317 2 5 3.34317 5 5C5 6.65683 6.34317 8 8 8Z",
                stroke: "currentColor",
                "stroke-width": "1.43",
                "stroke-linejoin": "round"
            }, null, -1), A("path", {
                d: "M13 13C13 10.2386 10.7614 8 8 8C5.23858 8 3 10.2386 3 13",
                stroke: "currentColor",
                "stroke-width": "1.43",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    oa = {
        width: "16",
        height: "16",
        viewBox: "0 0 16 16",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const aa = {
        render: function(e, t) {
            return C(), w("svg", oa, t[0] || (t[0] = [A("g", {
                id: "Frame"
            }, [A("path", {
                id: "Vector",
                d: "M4.33347 13.332L2.0002 11.332L4.33347 8.9987",
                stroke: "currentColor",
                "stroke-width": "1.2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }), A("path", {
                id: "Vector_2",
                d: "M2.0002 11.332H9.6648C11.9591 11.332 13.9076 9.45856 13.997 7.16536C14.0915 4.74217 12.0892 2.66537 9.6648 2.66537H3.99967",
                stroke: "currentColor",
                "stroke-width": "1.2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            })], -1)]))
        }
    },
    na = {
        class: "relative w-[calc(100vw-32px)] md:w-[708px] rounded-[16px]"
    },
    ra = ["src"],
    sa = {
        class: "absolute bottom-[-72px] left-[50%] translate-x-[-50%] px-[30px] py-2.5 border border-white rounded-[40px] inline-flex justify-center items-center gap-2.5 text-white text-base font-bold font-['Arial'] leading-normal whitespace-nowrap dark:text-white"
    },
    ca = {
        class: "justify-start text-neutral-50 text-base"
    },
    la = {
        __name: "SlideNewUpdateModel",
        props: {
            showModal: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["close"],
        setup(e, {
            emit: t
        }) {
            const i = t,
                o = ["https://gensparkpublicblob.blob.core.windows.net/user-upload-image/v1/16ac67a0-8918-4293-a126-ad34f086e007", "https://gensparkpublicblob.blob.core.windows.net/user-upload-image/v1/3d397cd7-f651-45d1-845c-efc52b9a9d86", "https://gensparkpublicblob.blob.core.windows.net/user-upload-image/v1/fe0733c1-84f3-4ef1-bfcb-03a34cbb8bd6"],
                a = y(null),
                n = y(1),
                r = () => {
                    a.value.prev(), n.value > 1 ? n.value-- : n.value = o.length
                },
                s = () => {
                    a.value.next(), n.value < o.length ? n.value++ : n.value = 1
                };
            return (e, t) => (C(), w("div", na, [Z(j(Oe), {
                onClick: t[0] || (t[0] = e => i("close")),
                class: "fixed top-[30px] right-[30px] w-[32px] h-[32px] text-white cursor-pointer"
            }), Z(j(Ge), {
                "show-dots": !1,
                ref_key: "carouselRef",
                ref: a,
                class: "rounded-[16px]"
            }, {
                default: Y((() => [(C(), w(L, null, D(o, ((e, t) => A("img", {
                    key: t,
                    class: "w-full rounded-[16px]",
                    src: e
                }, null, 8, ra))), 64))])),
                _: 1
            }, 512), A("div", sa, [Z(j(Re), {
                class: "w-[16px] h-[16px] cursor-pointer",
                onClick: r
            }), A("div", ca, I(n.value) + "/" + I(o.length), 1), Z(j(Re), {
                class: "w-[16px] h-[16px] rotate-180 cursor-pointer",
                onClick: s
            })])]))
        }
    };
(() => {
    async function e(e) {
        return new Promise(((t, i) => {
            document.querySelector(`script[src="${e}"]`) ? t() : document.head.appendChild(Object.assign(document.createElement("script"), {
                src: e,
                onload: t,
                onerror: i
            }))
        }))
    }
    var i, o = ((i = o || {})[i.FALSE = 0] = "FALSE", i[i.TRUE = 1] = "TRUE", i[i.DEFAULT = 2] = "DEFAULT", i);

    function a(e, t) {
        return e.hasAttribute(t)
    }

    function n(e, t, i, a) {
        let n = function(e, t) {
            var i;
            let a = null == (i = e.getAttribute(t)) ? void 0 : i.toUpperCase();
            if (!a) return 2;
            let n = o[a];
            if (void 0 !== n) return n;
            throw new Error(`Invalid value, "${a}", for attribute ${t}. Must be one of ${Object.keys(o).filter(Number.isNaN).join(", ")}`)
        }(t, e);
        return 2 === n ? a : i.call(a, 1 === n)
    }
    var r = class extends HTMLElement {
        constructor() {
            super(...arguments), t(this, "picker"), t(this, "observer"), t(this, "google"), t(this, "loading")
        }
        static get observedAttributes() {
            return ["app-id", "client-id", "developer-key", "hide-title-bar", "locale", "max-items", "mine-only", "multiselect", "nav-hidden", "oauth-token", "origin", "relay-url", "scope", "title"]
        }
        get visible() {
            var e;
            return !!(null == (e = this.picker) ? void 0 : e.isVisible())
        }
        set visible(e) {
            var t;
            null == (t = this.picker) || t.setVisible(e)
        }
        get tokenClientConfig() {
            let e = this.getAttribute("client-id"),
                t = this.getAttribute("scope") ? ? "https://www.googleapis.com/auth/drive.file";
            if (!e || !t) throw new Error("client-id and scope are required attributes");
            return {
                client_id: e,
                hd: this.getAttribute("hd") ? ? void 0,
                include_granted_scopes: !!this.getAttribute("include-granted-scope"),
                login_hint: this.getAttribute("login-hint") ? ? void 0,
                prompt: this.getAttribute("prompt") ? ? "",
                scope: t
            }
        }
        attributeChangedCallback() {
            this.build()
        }
        async build() {
            var e;
            if (null == (e = this.picker) || e.dispose(), await this.loading, !this.google) return;
            let t = (new this.google.picker.PickerBuilder).setCallback((e => {
                    this.callbackToDispatchEvent(e)
                })),
                i = this.getAttribute("app-id");
            null !== i && (t = t.setAppId(i));
            let o = this.getAttribute("developer-key");
            null !== o && (t = t.setDeveloperKey(o));
            let r = this.getAttribute("locale");
            null !== r && (t = t.setLocale(r));
            let s = function(e, t) {
                let i = e.getAttribute(t);
                return i ? Number(i) : null
            }(this, "max-items");
            null !== s && (t = t.setMaxItems(s));
            let c = this.getAttribute("origin");
            null !== c && (t = t.setOrigin(c));
            let l = this.getAttribute("relay-url");
            null !== l && (t = t.setRelayUrl(l));
            let p = this.getAttribute("title");
            null !== p && (t = t.setTitle(p)), n("hide-title-bar", this, t.hideTitleBar, t);
            let d = this.getAttribute("oauth-token") ? ? await this.requestAccessToken();
            if (d) {
                t = t.setOAuthToken(d), a(this, "multiselect") && (t = t.enableFeature(this.google.picker.Feature.MULTISELECT_ENABLED)), a(this, "mine-only") && (t = t.enableFeature(this.google.picker.Feature.MINE_ONLY)), a(this, "nav-hidden") && (t = t.enableFeature(this.google.picker.Feature.NAV_HIDDEN));
                for (let e of this.views) t = t.addView(e);
                this.picker = t.build(), this.picker.setVisible(!0)
            }
        }
        get views() {
            let e = function(e, t = "*") {
                return function(e) {
                    return e.filter((e => function(e) {
                        return "view" in e && e.view instanceof window.google.picker.View
                    }(e))).map((e => e.view))
                }(Array.from(e.querySelectorAll(t)))
            }(this);
            return e.length ? e : ["all"]
        }
        async connectedCallback() {
            var t;
            this.loading = async function(t = "client:picker") {
                return window.gapi || await e("https://apis.google.com/js/api.js"), await new Promise((e => {
                    window.gapi.load(t, e)
                })), window.google
            }().then((e => {
                this.google = e, this.build()
            })), this.observer = new MutationObserver((e => {
                e.filter((e => "childList" === e.type || "attributes" === e.type && e.target !== this)).length && this.build()
            })), null == (t = this.observer) || t.observe(this, {
                childList: !0,
                subtree: !0,
                attributes: !0
            })
        }
        callbackToDispatchEvent(e) {
            let t;
            switch (e.action) {
                case google.picker.Action.CANCEL:
                    t = "picker:canceled";
                    break;
                case google.picker.Action.PICKED:
                    t = "picker:picked";
                    break;
                case google.picker.Action.ERROR:
                    t = "picker:error";
                    break;
                default:
                    return
            }
            this.dispatchEvent(new CustomEvent(t, {
                detail: e
            }))
        }
        async requestAccessToken() {
            return async function(t) {
                var i, o;
                return (null == (o = null == (i = window.google) ? void 0 : i.accounts) ? void 0 : o.oauth2) || await e("https://accounts.google.com/gsi/client"), new Promise(((e, i) => {
                    window.google.accounts.oauth2.initTokenClient({ ...t,
                        callback: e,
                        error_callback: i
                    }).requestAccessToken()
                }))
            }(this.tokenClientConfig).then((e => {
                let {
                    access_token: t
                } = e;
                if (t) return this.dispatchEvent(new CustomEvent("picker:authenticated", {
                    detail: {
                        token: t
                    }
                })), this.dispatchEvent(new CustomEvent("picker:oauth:response", {
                    detail: e
                })), t;
                this.dispatchEvent(new CustomEvent("picker:oauth:error", {
                    detail: e
                }))
            })).catch((e => {
                this.dispatchEvent(new CustomEvent("picker:oauth:error", {
                    detail: e
                }))
            }))
        }
        disconnectedCallback() {
            var e;
            null == (e = this.picker) || e.dispose()
        }
    };
    var s = class extends HTMLElement {
        static get observedAttributes() {
            return ["enable-drives", "include-folders", "mime-types", "mode", "owned-by-me", "parent", "query", "select-folder-enabled", "starred", "view-id"]
        }
        get view() {
            let e = new window.google.picker.DocsView(this.viewId);
            n("enable-drives", this, e.setEnableDrives, e), n("include-folders", this, e.setIncludeFolders, e), n("owned-by-me", this, e.setOwnedByMe, e), n("select-folder-enabled", this, e.setSelectFolderEnabled, e), n("starred", this, e.setStarred, e);
            let t = this.getAttribute("mime-types");
            null !== t && e.setMimeTypes(t);
            let i = this.getAttribute("mode");
            i && e.setMode(google.picker.DocsViewMode[i]);
            let o = this.getAttribute("parent");
            null !== o && e.setParent(o);
            let a = this.getAttribute("query");
            return null !== a && e.setQuery(a), e
        }
        get viewId() {
            let e = this.getAttribute("view-id");
            return e ? window.google.picker.ViewId[e] : void 0
        }
    };
    customElements.define("drive-picker", r), customElements.define("drive-picker-docs-view", s)
})();
const pa = {
        name: "GoogleDrivePicker",
        components: {
            Teleport: J
        },
        emits: ["update:visible", "google-drive-selected", "google-drive-cancelled", "google-drive-error", "google-drive-authenticated", "google-drive-oauth-error", "google-drive-files-filtered"],
        props: {
            visible: {
                type: Boolean,
                default: !1
            },
            multiselect: {
                type: Boolean,
                default: !1
            },
            driveScope: {
                type: String,
                default: "https://www.googleapis.com/auth/drive.file"
            },
            locale: {
                type: String,
                default: "en"
            },
            mimeTypes: {
                type: Array,
                default: () => ["*"]
            },
            viewId: {
                type: String,
                default: "DOCS"
            },
            supportDrives: {
                type: Boolean,
                default: !1
            },
            includeFolders: {
                type: Boolean,
                default: !0
            },
            selectFolderEnabled: {
                type: Boolean,
                default: !0
            },
            ownedByMe: {
                type: Boolean,
                default: !0
            },
            starred: {
                type: Boolean,
                default: !1
            },
            maxFileSizeBytes: {
                type: Number,
                default: 0
            },
            maxFileSizeMB: {
                type: Number,
                default: 0
            }
        },
        data: () => ({
            config: null,
            error: null,
            defaultOrigin: "undefined" != typeof window ? window.location.origin : "",
            isPickerVisible: !1,
            isLoading: !1,
            configLoaded: !1,
            oauthRetryCount: 0,
            isRetryingOAuth: !1
        }),
        watch: {
            visible: {
                immediate: !0,
                handler(e) {
                    e ? this.isPickerVisible || this.isRetryingOAuth || (this.oauthRetryCount = 0, this.openPicker()) : this.isPickerVisible && this.hidePicker()
                }
            }
        },
        computed: {
            errorMessage() {
                var e, t;
                if (!this.error) return null;
                if ("string" == typeof this.error) {
                    if (this.error.includes("popup_closed_by_user")) return this.$t("googleDrive.authCancelledByUser");
                    if (this.error.includes("access_denied")) return this.$t("googleDrive.accessDenied")
                } else {
                    if (null == (e = this.error) ? void 0 : e.message) return this.$t("googleDrive.anErrorOccurred", {
                        message: this.error.message
                    });
                    if (null == (t = this.error) ? void 0 : t.type) return this.$t("googleDrive.pickerError", {
                        type: this.error.type
                    })
                }
                return this.$t("googleDrive.unexpectedError", {
                    error: JSON.stringify(this.error)
                })
            },
            effectiveMaxSizeBytes() {
                return this.maxFileSizeMB > 0 ? 1024 * this.maxFileSizeMB * 1024 : this.maxFileSizeBytes
            }
        },
        methods: {
            async openPicker() {
                try {
                    this.isLoading = !0, await this.ensureConfigLoaded(), await this.displayPicker()
                } catch (e) {
                    O.log("ZZH Error opening Google Drive Picker:", e), this.error = e, this.isPickerVisible = !1, this.$emit("google-drive-error", e), this.$emit("update:visible", !1)
                } finally {
                    this.isLoading = !1
                }
            },
            async ensureConfigLoaded() {
                this.error = null;
                try {
                    this.config = await this.fetchDriveConfig(), O.log("ZZH Drive config loaded:", this.config), this.configLoaded = !0, await this.$nextTick()
                } catch (e) {
                    throw e
                }
            },
            async displayPicker() {
                const e = this.$refs.drivePicker;
                if (!e) throw new Error("Drive Picker element not found. Ensure config is loaded and component rendered.");
                e.visible = !0, this.isPickerVisible = !0, this.error = null
            },
            hidePicker() {
                const e = this.$refs.drivePicker;
                e && e.visible && O.log("ZZH Attempting to hide picker (setting internal state)."), this.isPickerVisible = !1
            },
            async fetchDriveConfig() {
                this.error = null;
                try {
                    const t = await fetch("/api/google/drive-picker-config");
                    if (!t.ok) {
                        let i = this.$t("googleDrive.serverError");
                        try {
                            const e = await t.json();
                            i = e.message || e.error || JSON.stringify(e)
                        } catch (e) {}
                        throw new Error(`Failed to fetch config: ${t.status} ${t.statusText}. ${i}`)
                    }
                    const i = await t.json();
                    if (O.log("FetchDriveConfig response data:", i), i.status && "success" !== i.status && 200 !== i.status) throw new Error(i.message || this.$t("googleDrive.invalidConfigFromServer"));
                    const o = i.config || i;
                    if (!o.clientId || !o.apiKey || !o.appId) throw new Error(this.$t("googleDrive.serverConfigIncomplete"));
                    return o
                } catch (t) {
                    throw this.error = t.message || this.$t("googleDrive.fetchConfigFailed"), t
                }
            },
            handlePicked(e) {
                O.log("ZZH picker:picked event:", e.detail);
                const t = e.detail.docs;
                if (this.isPickerVisible = !1, this.$emit("update:visible", !1), t && t.length > 0) {
                    const e = t.map((e => ({
                            id: e.id,
                            name: e.name,
                            mimeType: e.mimeType,
                            iconUrl: e.iconUrl,
                            url: e.url,
                            embedUrl: e.embedUrl,
                            lastModified: e.lastEditedUtc ? new Date(e.lastEditedUtc) : void 0,
                            size: e.sizeBytes,
                            thumbnail: e.thumbnailLink
                        }))),
                        i = e.filter((e => !(this.effectiveMaxSizeBytes > 0 && e.size > this.effectiveMaxSizeBytes))),
                        o = e.length - i.length;
                    if (o > 0) {
                        const t = this.effectiveMaxSizeBytes / 1048576;
                        O.log(`ZZH ${o} file(s) filtered out due to size limit (>${t.toFixed(1)}MB)`), this.$emit("google-drive-files-filtered", {
                            totalFiles: e.length,
                            filteredFiles: i.length,
                            filteredOutCount: o,
                            maxSizeBytes: this.effectiveMaxSizeBytes,
                            maxSizeMB: t
                        })
                    }
                    if (i.length > 0) this.$emit("google-drive-selected", this.multiselect ? i : i[0]);
                    else if (e.length > 0) {
                        const e = (this.effectiveMaxSizeBytes / 1048576).toFixed(1);
                        this.error = this.$t("googleDrive.fileSizeExceeded", {
                            limit: e
                        }), this.$emit("google-drive-error", this.error)
                    } else O.log("ZZH picker:picked event received but no files found in detail.docs"), this.$emit("google-drive-cancelled")
                } else O.log("ZZH picker:picked event received but no files found in detail.docs"), this.$emit("google-drive-cancelled")
            },
            handleCanceled(e) {
                O.log("ZZH picker:canceled event:", e.detail), this.isPickerVisible = !1, this.$emit("update:visible", !1), this.$emit("google-drive-cancelled")
            },
            handleError(e) {
                O.log("ZZH picker:error event:", e.detail), this.error = e.detail, this.isPickerVisible = !1, this.$emit("update:visible", !1), this.$emit("google-drive-error", e.detail)
            },
            handleAuthenticated(e) {
                O.log("ZZH picker:authenticated event:", e.detail);
                const t = e.detail.token;
                this.$emit("google-drive-authenticated", t), this.error = null, this.oauthRetryCount = 0
            },
            async handleOAuthError(e) {
                const t = e.detail;
                if (t && "invalid_grant" === t.error) {
                    if (this.isRetryingOAuth) return void O.log("OAuth token refresh already in progress. Ignoring concurrent error.");
                    if (this.oauthRetryCount > 0) return O.log("OAuth token refresh failed after one attempt. Aborting."), this.error = this.$t("googleDrive.authFailedAfterRetry"), this.$emit("google-drive-error", this.error), void this.$emit("update:visible", !1);
                    this.isRetryingOAuth = !0, this.oauthRetryCount++, O.log("OAuth token expired or invalid. Attempting to refresh config and retry."), this.config = null, this.configLoaded = !1, this.isPickerVisible = !1;
                    try {
                        await this.openPicker()
                    } catch (i) {
                        O.log("ZZH OAuth retry failed:", i), this.error = this.$t("googleDrive.authRetryFailed"), this.$emit("google-drive-error", this.error), this.$emit("update:visible", !1)
                    } finally {
                        this.isRetryingOAuth = !1
                    }
                } else O.log("picker:oauth:error event:", e.detail), this.error = e.detail.type || e.detail.error || "OAuth Error", this.isPickerVisible = !1, this.$emit("update:visible", !1), this.$emit("google-drive-oauth-error", e.detail), this.$emit("google-drive-error", this.error)
            },
            handleOAuthResponse(e) {
                var t;
                O.log("ZZH picker:oauth:response event:", e.detail), (null == (t = e.detail) ? void 0 : t.error) && this.handleOAuthError(e)
            }
        }
    },
    da = {
        key: 0,
        class: "loading-popup"
    },
    ma = {
        class: "loading-content"
    },
    ua = {
        class: "loading-text"
    },
    ga = {
        key: 1,
        class: "error-popup"
    },
    ha = {
        class: "error-content"
    },
    va = {
        key: 2,
        class: "picker-wrapper"
    },
    ya = ["client-id", "app-id", "developer-key", "oauth-token", "origin", "scope", "multiselect", "locale"],
    ba = ["mime-types", "view-id", "enable-drives", "include-folders", "select-folder-enabled", "owned-by-me", "starred"];
const fa = P(pa, [
        ["render", function(e, t, i, o, a, n) {
            return C(), E(J, {
                to: "body"
            }, [i.visible && a.isLoading ? (C(), w("div", da, [A("div", ma, [t[7] || (t[7] = A("div", {
                class: "spinner"
            }, null, -1)), A("div", ua, I(e.$t("googleDrive.loading")), 1)])])) : B("", !0), i.visible && n.errorMessage ? (C(), w("div", ga, [A("div", ha, [_(I(n.errorMessage) + " ", 1), A("div", {
                class: "close-btn",
                onClick: t[0] || (t[0] = t => e.$emit("update:visible", !1))
            }, "×")])])) : B("", !0), i.visible && a.config ? (C(), w("div", va, [A("drive-picker", {
                ref: "drivePicker",
                "client-id": a.config.clientId,
                "app-id": a.config.appId,
                "developer-key": a.config.apiKey,
                "oauth-token": a.config.token || void 0,
                origin: a.config.origin || a.defaultOrigin,
                scope: i.driveScope,
                multiselect: i.multiselect,
                locale: i.locale,
                "onPicker:picked": t[1] || (t[1] = (...e) => n.handlePicked && n.handlePicked(...e)),
                "onPicker:canceled": t[2] || (t[2] = (...e) => n.handleCanceled && n.handleCanceled(...e)),
                "onPicker:error": t[3] || (t[3] = (...e) => n.handleError && n.handleError(...e)),
                "onPicker:authenticated": t[4] || (t[4] = (...e) => n.handleAuthenticated && n.handleAuthenticated(...e)),
                "onPicker:oauth:error": t[5] || (t[5] = (...e) => n.handleOAuthError && n.handleOAuthError(...e)),
                "onPicker:oauth:response": t[6] || (t[6] = (...e) => n.handleOAuthResponse && n.handleOAuthResponse(...e))
            }, [A("drive-picker-docs-view", {
                "mime-types": i.mimeTypes.join(","),
                "view-id": i.viewId,
                "enable-drives": i.supportDrives,
                "include-folders": i.includeFolders,
                "select-folder-enabled": i.selectFolderEnabled,
                "owned-by-me": i.ownedByMe,
                starred: i.starred
            }, null, 8, ba)], 40, ya)])) : B("", !0)])
        }],
        ["__scopeId", "data-v-9d93498a"]
    ]),
    ka = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "16",
        height: "16",
        viewBox: "0 0 16 16",
        fill: "none"
    };
const wa = {
        render: function(e, t) {
            return C(), w("svg", ka, t[0] || (t[0] = [A("path", {
                d: "M6.66602 1.64062C6.92479 1.69359 7.11907 1.92285 7.11914 2.19727C7.11914 2.47174 6.92483 2.70093 6.66602 2.75391L6.55176 2.76562H5.10156C3.81367 2.76575 2.76961 3.80976 2.76953 5.09766V9.79785C2.76953 11.6932 4.30584 13.2303 6.20117 13.2305H10.9014C12.1894 13.2305 13.2344 12.1855 13.2344 10.8975V9.44824C13.2345 9.13477 13.4883 8.88014 13.8018 8.87988C14.1154 8.87988 14.37 9.13461 14.3701 9.44824V10.8975C14.3701 12.8129 12.8168 14.3662 10.9014 14.3662H6.20117C3.67844 14.3661 1.63281 12.3206 1.63281 9.79785V5.09766C1.63289 3.18236 3.18628 1.62903 5.10156 1.62891H6.55176L6.66602 1.64062ZM10.3545 2.55664C11.2088 1.86026 12.4684 1.91083 13.2646 2.70703L13.4141 2.87109C14.0645 3.66834 14.0651 4.81972 13.415 5.61719L13.2646 5.78223L10.2666 8.78027C9.41875 9.62812 8.35115 10.2206 7.18652 10.4922L6.95215 10.543L6.7666 10.5781C5.95138 10.7362 5.23473 10.0203 5.39258 9.20508L5.42871 9.01953C5.67237 7.76379 6.28592 6.60861 7.19043 5.7041L10.1885 2.70605L10.3545 2.55664ZM12.416 3.55566C12.0352 3.17487 11.4179 3.17394 11.0371 3.55469L8.03906 6.55273C7.30377 7.28803 6.80455 8.22723 6.60645 9.24805L6.57812 9.39355L6.72266 9.36523C7.74354 9.16715 8.68263 8.66698 9.41797 7.93164L12.416 4.93359C12.7967 4.55285 12.7966 3.93648 12.416 3.55566Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    Aa = {
        class: "wrapper"
    },
    Ca = {
        class: "title"
    },
    Ma = {
        class: "desc text-lg mb-6"
    },
    xa = {
        class: "alert mb-6"
    },
    Pa = {
        class: "mb-2"
    },
    Sa = {
        class: "desc text-lg font-bold mb-4"
    },
    Ta = {
        class: "buttons flex justify-end gap-4"
    },
    Ia = {
        class: "label"
    },
    _a = {
        key: 0,
        class: "icon spin"
    },
    La = P({
        __name: "RevertGlobalCanvasDialog",
        props: {
            project: {
                type: Object,
                default: () => ({})
            }
        },
        emits: ["close", "confirm"],
        setup(e, {
            expose: t,
            emit: i
        }) {
            const o = e,
                {
                    project: a
                } = X(o),
                n = Pe(),
                r = y(!1),
                s = y(!1),
                c = i,
                l = () => {
                    r.value = !1, c("close")
                },
                p = async () => {
                    s.value || (c("confirm"), await (async () => {
                        const e = a.value.session_state.canvas_history_id;
                        a.value.session_state.current_chat_session_name = "", a.value.session_state.current_chat_session_id = "", a.value.session_state.messages = [];
                        const t = a.value.session_state.canvas_history.findIndex((t => t.id === e));
                        if (-1 !== t) {
                            s.value = !0;
                            try {
                                a.value.session_state.canvas_history.splice(0, t);
                                const i = await fetch("/api/project/update", {
                                    method: "POST",
                                    headers: {
                                        "Content-Type": "application/json"
                                    },
                                    body: JSON.stringify({
                                        id: a.value.id,
                                        session_state: {
                                            canvas_history_id: e,
                                            canvas_history: a.value.session_state.canvas_history,
                                            current_chat_session_name: "",
                                            current_chat_session_id: "",
                                            messages: [],
                                            docs_agent: a.value.session_state.docs_agent,
                                            dataset_canvas: a.value.session_state.dataset_canvas,
                                            podcasts_agent: a.value.session_state.podcasts_agent,
                                            files: a.value.session_state.files,
                                            schema: a.value.session_state.schema
                                        },
                                        is_revert: !0,
                                        request_not_update_permission: !0
                                    })
                                });
                                if (!i.ok) throw new Error("Failed to revert project");
                                0 === (await i.json()).status ? n.success("Project reverted successfully") : n.error("Failed to revert project")
                            } finally {
                                s.value = !1
                            }
                        }
                    })(), r.value = !1, c("close"))
                };
            return t({
                open: () => {
                    r.value = !0
                }
            }), (e, t) => (C(), E(j(Ne), {
                show: j(r),
                "onUpdate:show": t[0] || (t[0] = e => $(r) ? r.value = e : null)
            }, {
                default: Y((() => [A("div", Aa, [A("div", Ca, I(e.$t("components.agents.RevertGlobalCanvasDialog.title")), 1), A("p", Ma, I(e.$t("components.agents.RevertGlobalCanvasDialog.description")), 1), A("div", xa, [A("p", Pa, I(e.$t("components.agents.RevertGlobalCanvasDialog.warning1")), 1), A("p", null, I(e.$t("components.agents.RevertGlobalCanvasDialog.warning2")), 1)]), A("p", Sa, I(e.$t("components.agents.RevertGlobalCanvasDialog.confirm_question")), 1), A("div", Ta, [A("div", {
                    class: "button cancel",
                    onClick: l
                }, I(e.$t("components.agents.RevertGlobalCanvasDialog.cancel")), 1), A("div", {
                    class: "button confirm",
                    onClick: p
                }, [A("div", Ia, [_(I(e.$t("components.agents.RevertGlobalCanvasDialog.revert")) + " ", 1), j(s) ? (C(), w("div", _a, [Z(j(Fe))])) : B("", !0)])])])])])),
                _: 1
            }, 8, ["show"]))
        }
    }, [
        ["__scopeId", "data-v-0946bd67"]
    ]),
    Da = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 20 20"
    };
const Ea = {
        render: function(e, t) {
            return C(), w("svg", Da, t[0] || (t[0] = [A("g", {
                fill: "none"
            }, [A("path", {
                d: "M11.958 6.2a.5.5 0 1 0-.916-.4l-3.5 8a.5.5 0 1 0 .916.4l3.5-8zM6.854 7.646a.5.5 0 0 1 0 .708L5.207 10l1.647 1.646a.5.5 0 0 1-.708.708l-2-2a.5.5 0 0 1 0-.708l2-2a.5.5 0 0 1 .708 0zm6.292.708a.5.5 0 0 1 .708-.708l2 2a.5.5 0 0 1 0 .708l-2 2a.5.5 0 0 1-.708-.708L14.793 10l-1.647-1.646zM10 2a8 8 0 1 0 0 16a8 8 0 0 0 0-16zm-7 8a7 7 0 1 1 14 0a7 7 0 0 1-14 0z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    Fa = {
        key: 0,
        class: "github-binding-prompt-wrapper"
    },
    Oa = {
        class: "github-binding-prompt-inner"
    },
    Ba = {
        class: "description"
    },
    ja = {
        class: "buttons"
    },
    za = {
        class: "icon"
    },
    Ra = {
        class: "label"
    },
    Ga = P({
        __name: "GitHubBindingDialog",
        props: {
            project: {
                type: Object,
                default: () => ({})
            },
            showStopAsking: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["openGitHubTab"],
        setup(e, {
            emit: t
        }) {
            const i = e,
                o = t,
                a = g((() => {
                    var e, t, o, a, n, r, s;
                    return "code_sandbox" === (null == (e = i.project) ? void 0 : e.type) && "free_code" === (null == (a = null == (o = null == (t = i.project) ? void 0 : t.session_state) ? void 0 : o.system_prompt) ? void 0 : a.id) && !(null == (s = null == (r = null == (n = i.project) ? void 0 : n.session_state) ? void 0 : r.github) ? void 0 : s.full_name)
                })),
                n = e => {
                    e.stopPropagation(), e.preventDefault(), o("openGitHubTab")
                };
            return (e, t) => a.value ? (C(), w("div", Fa, [A("div", Oa, [A("div", Ba, I(e.$t("components.code_sandbox.GitHubBindingDialog.description")), 1), A("div", ja, [A("div", {
                class: "button primary",
                onClick: n
            }, [A("div", za, [Z(j(Ea))]), A("div", Ra, I(e.$t("components.code_sandbox.GitHubBindingDialog.bind_github")), 1)])])])])) : B("", !0)
        }
    }, [
        ["__scopeId", "data-v-16528abd"]
    ]),
    qa = {
        class: "upload-options-popover"
    },
    Na = {
        class: "option-icon"
    },
    Ua = {
        class: "option-label"
    },
    Wa = {
        class: "option-icon"
    },
    Va = {
        class: "option-label"
    },
    Ha = {
        class: "option-icon"
    },
    Qa = {
        class: "option-label"
    },
    Ka = P({
        __name: "UploadFromMultipleSourceButton",
        props: {
            disabled: {
                type: Boolean,
                default: !1
            },
            multiselect: {
                type: Boolean,
                default: !0
            },
            mimeTypes: {
                type: Array,
                default: () => []
            },
            supportImages: {
                type: Boolean,
                default: !1
            },
            googleDriveEnable: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["local-file-upload", "google-drive-upload", "aidrive-upload"],
        setup(e, {
            emit: t
        }) {
            const i = e,
                o = t,
                {
                    t: a
                } = S(),
                n = y(!1);
            k("currentUser");
            const r = e => {},
                s = () => {
                    i.disabled || (n.value = !1, o("local-file-upload"))
                },
                c = () => {
                    i.disabled || (n.value = !1, o("google-drive-upload"))
                },
                l = () => {
                    i.disabled || (n.value = !1, o("aidrive-upload"))
                };
            return (t, i) => (C(), w("div", {
                class: "upload-from-multiple-source-container",
                onClick: i[1] || (i[1] = R((() => {}), ["stop", "prevent"]))
            }, [Z(j(He), {
                show: n.value,
                "onUpdate:show": [i[0] || (i[0] = e => n.value = e), r],
                trigger: "click",
                placement: "top-start",
                "show-arrow": !1,
                style: {
                    "--n-padding": "0",
                    "--n-border-radius": "8px"
                }
            }, {
                trigger: Y((() => [A("div", {
                    class: z(["upload-trigger-button w-[20px] h-[20px] p-[8px] hover:bg-[#f0f0f0] dark:hover:bg-[#444] hover:text-[#232425] hover:rounded-[35%]", {
                        disabled: e.disabled
                    }])
                }, [Z(j(mt), {
                    class: z(["text-[#909499]"])
                })], 2)])),
                default: Y((() => [A("div", qa, [A("div", {
                    class: "upload-option-item",
                    onClick: s
                }, [A("div", Na, [Z(j(Ue), {
                    class: "invert-icon"
                })]), A("div", Ua, I(t.$t("components.upload.upload_from_local")), 1)]), A("div", {
                    class: "upload-option-item",
                    onClick: l
                }, [A("div", Wa, [Z(j(Ve), {
                    class: "invert-icon"
                })]), A("div", Va, I(t.$t("components.upload.upload_from_aidrive")), 1)]), e.googleDriveEnable ? (C(), w("div", {
                    key: 0,
                    class: "upload-option-item",
                    onClick: c
                }, [A("div", Ha, [Z(j(ue))]), A("div", Qa, I(t.$t("components.upload.upload_from_google_drive", "Upload from Google Drive")), 1)])) : B("", !0)])])),
                _: 1
            }, 8, ["show"])]))
        }
    }, [
        ["__scopeId", "data-v-c432c120"]
    ]),
    Za = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "1em",
        height: "1em",
        fill: "none",
        viewBox: "0 0 24 24"
    };
const Ja = {
        render: function(e, t) {
            return C(), w("svg", Za, t[0] || (t[0] = [A("path", {
                fill: "currentColor",
                d: "M7.707 21.707a1 1 0 0 1 0-1.414L16 12 7.707 3.707a1 1 0 0 1 1.414-1.414l8.293 8.293a2 2 0 0 1 0 2.828l-8.293 8.293a1 1 0 0 1-1.414 0"
            }, null, -1)]))
        }
    },
    Ya = {
        class: "dialog-content"
    },
    Xa = {
        class: "dialog-header"
    },
    $a = {
        class: "breadcrumb-header-container"
    },
    en = {
        class: "breadcrumb-section"
    },
    tn = {
        class: "last-crumb"
    },
    on = {
        class: "modified-header"
    },
    an = {
        class: "file-list-container"
    },
    nn = {
        key: 0,
        class: "loading-state"
    },
    rn = {
        key: 1,
        class: "empty-state"
    },
    sn = {
        key: 2,
        class: "file-list"
    },
    cn = ["onClick", "onDblclick"],
    ln = {
        key: 0,
        class: "checkbox-area"
    },
    pn = {
        class: "file-name"
    },
    dn = {
        class: "file-time"
    },
    mn = {
        class: "dialog-footer"
    },
    un = {
        class: "footer-left"
    },
    gn = {
        key: 0,
        class: "select-all-wrapper"
    },
    hn = {
        key: 0,
        class: "checkbox-selected"
    },
    vn = {
        key: 1,
        class: "checkbox-indeterminate"
    },
    yn = {
        key: 2,
        class: "checkbox-empty"
    },
    bn = {
        class: "selection-info"
    },
    fn = {
        class: "footer-right"
    },
    kn = ["disabled"],
    wn = P(s({
        __name: "SelectAIDriveFilesDialog",
        props: {
            visible: {
                type: Boolean
            },
            title: {},
            cancelButtonText: {},
            confirmButtonText: {},
            multiSelect: {
                type: Boolean,
                default: !0
            },
            supportMimetypes: {},
            maxFileSize: {
                default: void 0
            }
        },
        emits: ["update:visible", "cancel", "confirm"],
        setup(e, {
            emit: t
        }) {
            const {
                t: i
            } = S(), o = e, a = t, n = y(!1), r = y("/"), s = y([]), l = y([]), p = y([]), d = y(!1), m = () => {
                d.value = window.matchMedia("(prefers-color-scheme: dark)").matches
            }, u = g((() => ({
                Tooltip: {
                    color: d.value ? "#333333" : "#ffffff",
                    textColor: d.value ? "#ffffff" : "#333333",
                    fontSize: "14px",
                    boxShadow: d.value ? "0 2px 8px rgba(0, 0, 0, 0.2)" : "0 2px 8px rgba(0, 0, 0, 0.12)"
                }
            }))), h = async e => {
                n.value = !0;
                try {
                    const t = await Qe.fetch(e, "all");
                    s.value = t, r.value = e, v(e)
                } catch (t) {
                    s.value = []
                } finally {
                    n.value = !1
                }
            }, v = e => {
                if ("/" === e) return void(p.value = []);
                const t = e.split("/").filter(Boolean);
                p.value = t.map(((e, i) => ({
                    name: decodeURIComponent(e),
                    path: "/" + t.slice(0, i + 1).join("/")
                })))
            }, f = e => {
                h(e)
            }, k = e => {
                e !== p.value.length - 1 && f(p.value[e].path)
            }, M = e => {
                if (e.isDir) return !0;
                return !(!(Ke(e) || Ze(e) || Je(e)) && e.size && o.maxFileSize && e.size > o.maxFileSize)
            }, x = e => {
                if (e.isDir) return "";
                return !(Ke(e) || Ze(e) || Je(e)) && e.size && o.maxFileSize && e.size > o.maxFileSize ? i("components.aidrive.select_files.file_too_large", `File size exceeds ${O(o.maxFileSize)} limit`) : ""
            }, P = e => l.value.some((t => t.id === e.id)), F = e => {
                const t = new Date(1e3 * e),
                    i = new Date;
                return t.toDateString() === i.toDateString() ? t.toLocaleTimeString(void 0, {
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: !1
                }) : t.toLocaleDateString(void 0, {
                    month: "short",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: !1
                })
            }, O = e => {
                if (0 === e) return "0 B";
                const t = Math.floor(Math.log(e) / Math.log(1024));
                return parseFloat((e / Math.pow(1024, t)).toFixed(2)) + " " + ["B", "KB", "MB", "GB"][t]
            }, R = () => {
                a("update:visible", !1), a("cancel")
            }, G = () => {
                const e = l.value.filter((e => !e.isDir));
                a("confirm", e), a("update:visible", !1)
            };
            T((() => o.visible), (e => {
                e && (l.value = [], s.value = [], r.value = "/", p.value = [], h("/"))
            })), b((() => {
                o.visible && h("/"), m();
                const e = window.matchMedia("(prefers-color-scheme: dark)");
                return e.addEventListener("change", m), () => {
                    e.removeEventListener("change", m)
                }
            }));
            const q = () => c(Ja),
                N = g((() => p.value.length <= 3 ? [] : p.value.slice(0, -2).map(((e, t) => ({
                    label: e.name,
                    key: String(t)
                }))))),
                U = e => {
                    k(Number(e))
                },
                W = g((() => s.value.filter((e => !e.isDir && M(e))))),
                V = g((() => 0 !== W.value.length && W.value.every((e => P(e))))),
                H = g((() => 0 !== l.value.length && (!V.value && W.value.some((e => P(e)))))),
                Q = () => {
                    if (V.value) {
                        const e = new Set(W.value.map((e => e.id)));
                        l.value = l.value.filter((t => !e.has(t.id)))
                    } else {
                        const e = W.value.filter((e => !P(e)));
                        l.value = [...l.value, ...e]
                    }
                };
            return (e, t) => (C(), E(j(te), {
                "theme-overrides": u.value
            }, {
                default: Y((() => [(C(), E(J, {
                    to: "body"
                }, [e.visible ? (C(), w("div", {
                    key: 0,
                    class: "select-files-dialog",
                    onKeydown: ee(R, ["esc"]),
                    tabindex: "0"
                }, [A("div", Ya, [A("div", Xa, [A("h3", null, I(e.title || j(i)("components.aidrive.select_files.title")), 1), A("button", {
                    class: "close-btn",
                    onClick: R
                }, "×")]), A("div", $a, [A("div", en, [Z(j(lt), {
                    "separator-icon": q
                }, {
                    default: Y((() => [Z(j(pt), {
                        onClick: t[0] || (t[0] = e => f("/"))
                    }, {
                        default: Y((() => [_(I(j(i)("components.aidrive.select_files.all_files")), 1)])),
                        _: 1
                    }), p.value.length > 3 ? (C(), w(L, {
                        key: 0
                    }, [Z(j(pt), null, {
                        default: Y((() => [Z(j(tt), {
                            options: N.value,
                            onSelect: U,
                            placement: "bottom-start"
                        }, {
                            default: Y((() => t[2] || (t[2] = [A("div", {
                                class: "breadcrumb-ellipsis"
                            }, "...", -1)]))),
                            _: 1
                        }, 8, ["options"])])),
                        _: 1
                    }), Z(j(pt), {
                        onClick: t[1] || (t[1] = e => k(p.value.length - 2))
                    }, {
                        default: Y((() => [_(I(p.value[p.value.length - 2].name), 1)])),
                        _: 1
                    }), Z(j(pt), null, {
                        default: Y((() => [A("span", tn, I(p.value[p.value.length - 1].name), 1)])),
                        _: 1
                    })], 64)) : (C(!0), w(L, {
                        key: 1
                    }, D(p.value, ((e, t) => (C(), E(j(pt), {
                        key: t,
                        onClick: e => t < p.value.length - 1 ? k(t) : null
                    }, {
                        default: Y((() => [A("span", {
                            class: z({
                                "last-crumb": t === p.value.length - 1
                            })
                        }, I(e.name), 3)])),
                        _: 2
                    }, 1032, ["onClick"])))), 128))])),
                    _: 1
                })]), A("div", on, I(j(i)("components.aidrive.file_item.modified")), 1)]), A("div", an, [n.value ? (C(), w("div", nn, [Z(j(Ye), {
                    class: "loading-icon"
                })])) : 0 === s.value.length ? (C(), w("div", rn, I(j(i)("components.aidrive.select_files.empty_folder")), 1)) : (C(), w("div", sn, [(C(!0), w(L, null, D(s.value, (e => (C(), E(j(it), {
                    key: e.id,
                    disabled: e.isDir || M(e),
                    placement: "left-start",
                    overlap: !0,
                    trigger: "hover",
                    delay: 300,
                    duration: 50
                }, {
                    trigger: Y((() => [A("div", {
                        class: z(["file-item", {
                            selected: P(e),
                            unsupported: !M(e) && !e.isDir,
                            folder: e.isDir
                        }]),
                        onClick: t => (e => {
                            if (e.isDir) f(e.path);
                            else if (M(e))
                                if (o.multiSelect) {
                                    const t = l.value.findIndex((t => t.id === e.id));
                                    t > -1 ? l.value.splice(t, 1) : l.value.push(e)
                                } else l.value = P(e) ? [] : [e]
                        })(e),
                        onDblclick: e => {}
                    }, [e.isDir ? B("", !0) : (C(), w("div", ln, [P(e) ? (C(), w("div", {
                        key: 0,
                        class: z(["checkbox-selected", {
                            disabled: !M(e)
                        }])
                    }, [Z(j(We), {
                        class: "checkbox-icon"
                    })], 2)) : (C(), w("div", {
                        key: 1,
                        class: z(["checkbox-empty", {
                            disabled: !M(e)
                        }])
                    }, null, 2))])), A("div", {
                        class: z(["file-icon", {
                            "folder-icon": e.isDir
                        }])
                    }, [Z(Xe, {
                        file: e
                    }, null, 8, ["file"])], 2), A("div", pn, I(e.name), 1), A("div", dn, I(F(e.modified_time)), 1)], 42, cn)])),
                    default: Y((() => [_(" " + I(x(e)), 1)])),
                    _: 2
                }, 1032, ["disabled"])))), 128))]))]), A("div", mn, [A("div", un, [e.multiSelect ? (C(), w("div", gn, [A("div", {
                    class: "select-all-checkbox",
                    onClick: Q
                }, [V.value ? (C(), w("div", hn, [Z(j(We), {
                    class: "checkbox-icon"
                })])) : H.value ? (C(), w("div", vn, t[3] || (t[3] = [A("div", {
                    class: "indeterminate-icon"
                }, null, -1)]))) : (C(), w("div", yn))]), A("span", bn, I(j(i)("components.aidrive.select_files.selected_count", {
                    count: l.value.length
                })), 1)])) : B("", !0)]), A("div", fn, [A("button", {
                    class: "cancel-btn",
                    onClick: R
                }, I(e.cancelButtonText || j(i)("components.aidrive.dialog.cancel")), 1), A("button", {
                    class: "confirm-btn",
                    disabled: 0 === l.value.length,
                    onClick: G
                }, I(e.confirmButtonText || j(i)("components.aidrive.select_files.confirm_with_count", {
                    count: l.value.length
                })), 9, kn)])])])], 32)) : B("", !0)]))])),
                _: 1
            }, 8, ["theme-overrides"]))
        }
    }), [
        ["__scopeId", "data-v-a116a03a"]
    ]),
    An = {
        key: 0,
        class: "convert-document-type-prompt-wrapper"
    },
    Cn = {
        class: "convert-document-type-prompt-inner"
    },
    Mn = {
        class: "icon"
    },
    xn = P({
        __name: "ConvertDocumentTypePrompt",
        props: {
            project: {
                type: Object,
                required: !1
            },
            showStopAsking: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["convert"],
        setup(e, {
            emit: t
        }) {
            const i = e,
                o = t,
                a = ot(),
                n = g((() => {
                    var e, t, o;
                    if (!i.project || "docs_agent" !== i.project.type) return !1;
                    return a.userSelectEditorType !== ((null == (o = null == (t = null == (e = i.project) ? void 0 : e.session_state) ? void 0 : t.docs_agent) ? void 0 : o.type) || "html")
                })),
                r = g((() => i.showStopAsking)),
                s = () => {
                    if (r.value) return;
                    const e = a.userSelectEditorType;
                    o("convert", `regenerate the document in ${e} format`)
                };
            return (e, t) => n.value ? (C(), w("div", An, [A("div", Cn, [t[1] || (t[1] = A("div", {
                class: "text"
            }, "Would you like to convert document type?", -1)), A("div", {
                class: z(["button", {
                    disabled: r.value
                }]),
                onClick: s
            }, [A("div", Mn, [Z(j(aa))]), t[0] || (t[0] = A("div", {
                class: "label"
            }, "Convert document type", -1))], 2)])])) : B("", !0)
        }
    }, [
        ["__scopeId", "data-v-35796eef"]
    ]),
    Pn = () => {
        const e = y(""),
            t = y([]),
            i = y("start"),
            o = De(),
            {
                prompt: a
            } = ie(o),
            {
                setPrompt: n
            } = o,
            {
                locale: r
            } = S();
        let s = null,
            c = null,
            l = null,
            p = !1;
        const d = i => {
                switch (i.type) {
                    case "connection_established":
                        break;
                    case "transcript":
                        if (i.transcript)
                            if (i.is_final) e.value += i.transcript, t.value.push({
                                text: i.transcript,
                                isFinal: !0,
                                confidence: i.confidence
                            }), p && (n(a.value + e.value), e.value = "");
                            else {
                                const e = t.value.length - 1,
                                    o = t.value[e];
                                e >= 0 && o && "object" == typeof o && !1 === o.isFinal ? t.value[e] = {
                                    text: i.transcript,
                                    isFinal: !1,
                                    confidence: i.confidence
                                } : t.value.push({
                                    text: i.transcript,
                                    isFinal: !1,
                                    confidence: i.confidence
                                })
                            }
                        break;
                    case "error":
                        l && (clearTimeout(l), l = null)
                }
            },
            m = () => {
                l && (clearTimeout(l), l = null), c && c.readyState === WebSocket.OPEN && c.close()
            };
        return {
            transcript: e,
            transcriptList: t,
            recordingStatus: i,
            startRecording: function() {
                t.value = [], e.value = "", p = !1, l && (clearTimeout(l), l = null);
                const o = `${"https:"===window.location.protocol?"wss:":"ws:"}//${window.location.host}/ws/speech`;
                c = new WebSocket(o), c.onopen = () => {
                    c.send(JSON.stringify({
                        type: "start_recognition",
                        language_code: r.value
                    })), navigator.mediaDevices.getUserMedia({
                        audio: {
                            sampleRate: 16e3,
                            channelCount: 1,
                            echoCancellation: !0,
                            noiseSuppression: !0,
                            autoGainControl: !0
                        }
                    }).then((e => {
                        let t = null;
                        const o = ["audio/webm;codecs=opus", "audio/webm", "audio/ogg;codecs=opus", "audio/mp4", "audio/wav"];
                        for (const i of o) MediaRecorder.isTypeSupported(i) && !t && (t = i);
                        const a = {
                            audioBitsPerSecond: 32e3
                        };
                        t && (a.mimeType = t), s = new MediaRecorder(e, a), i.value = "speech", s.ondataavailable = async e => {
                            if (e.data.size > 0 && c && c.readyState === WebSocket.OPEN) try {
                                const i = await e.data.arrayBuffer(),
                                    o = new Uint8Array(i),
                                    a = 8192;
                                let n = "";
                                for (let e = 0; e < o.length; e += a) {
                                    const t = o.slice(e, e + a);
                                    n += String.fromCharCode.apply(null, t)
                                }
                                const s = {
                                    type: "audio_data",
                                    audio_data: btoa(n),
                                    language_code: r.value,
                                    mime_type: t
                                };
                                c.send(JSON.stringify(s))
                            } catch (i) {
                                c && c.readyState === WebSocket.OPEN && c.close()
                            }
                        }, s.start(200)
                    })).catch((e => {
                        i.value = "start", c && c.readyState === WebSocket.OPEN && c.close(), "NotAllowedError" !== e.name && "PermissionDeniedError" !== e.name || alert("Microphone access has been denied. Please enable microphone permission in your browser or system settings.")
                    }))
                }, c.onmessage = e => {
                    try {
                        const t = JSON.parse(e.data);
                        d(t)
                    } catch (t) {}
                }, c.onclose = e => {
                    l && (clearTimeout(l), l = null), "speech" === i.value && (i.value = "start", s && "recording" === s.state && s.stop(), s && s.stream && s.stream.getTracks().forEach((e => {
                        e.stop()
                    })), s = null)
                }, c.onerror = e => {
                    c && c.readyState === WebSocket.OPEN && c.close()
                }
            },
            resetRecordStatus: () => {
                if (s && "recording" === s.state && s.stop(), s && s.stream && s.stream.getTracks().forEach((e => {
                        e.stop()
                    })), c && c.readyState === WebSocket.OPEN) try {
                    c.send(JSON.stringify({
                        type: "stop_recognition"
                    })), l = setTimeout((() => {
                        m()
                    }), 3e3)
                } catch (e) {
                    m()
                } else c && c.readyState === WebSocket.CONNECTING && c.close();
                i.value = "start", s = null
            },
            setFinalResult: () => {
                p = !0, n(a.value + e.value), e.value = ""
            },
            closeWebSocketConnection: m
        }
    },
    Sn = {
        key: 0,
        class: "rollback-prompt-wrapper"
    },
    Tn = {
        class: "rollback-prompt-inner"
    },
    In = {
        class: "text"
    },
    _n = {
        class: "icon"
    },
    Ln = {
        class: "label"
    },
    Dn = {
        key: 1,
        class: "z-[100] absolute left-0 top-0 right-0 bottom-0 opacity-50 cursor-not-allowed"
    },
    En = {
        key: 2,
        class: "prompt-files"
    },
    Fn = ["onClick"],
    On = ["src"],
    Bn = ["src"],
    jn = {
        key: 2,
        class: "flex items-center justify-center w-[100%] h-[100%] bg-[#F5F5F5] rounded-[12px]"
    },
    zn = {
        class: "prompt-file-inner"
    },
    Rn = ["onClick"],
    Gn = {
        key: 0,
        class: "file-wrapper"
    },
    qn = {
        class: "flex items-center justify-center w-[36px] h-[36px] bg-white rounded-lg"
    },
    Nn = {
        class: "file-info"
    },
    Un = {
        class: "file-name"
    },
    Wn = {
        class: "file-size"
    },
    Vn = {
        key: 1,
        class: "file-wrapper"
    },
    Hn = {
        class: "icon file-icon"
    },
    Qn = {
        class: "file-info"
    },
    Kn = {
        class: "file-name"
    },
    Zn = {
        class: "file-size"
    },
    Jn = {
        key: 1,
        class: "prompt-file template-card"
    },
    Yn = {
        class: "prompt-file-inner"
    },
    Xn = {
        class: "template-card-wrapper"
    },
    $n = ["src", "alt"],
    er = {
        class: "template-info"
    },
    tr = {
        class: "template-name"
    },
    ir = {
        class: "template-label"
    },
    or = {
        key: 3,
        class: "flex flex-col items-center justify-center h-[120px]"
    },
    ar = {
        class: "text-center justify-start text-neutral-800 text-base font-normal font-['Arial'] leading-normal dark:text-white"
    },
    nr = {
        class: "text-center justify-start text-white text-sm font-normal font-['Helvetica_Neue'] leading-[24px]"
    },
    rr = {
        key: 4,
        class: "textarea-wrapper flex-col pt-[12px]"
    },
    sr = ["placeholder"],
    cr = {
        key: 0,
        class: "flex items-center gap-2"
    },
    lr = {
        class: "icon"
    },
    pr = {
        class: "chat-session-popover"
    },
    dr = {
        class: "new-chat-session"
    },
    mr = {
        class: "desc"
    },
    ur = {
        class: "icon"
    },
    gr = {
        class: "label"
    },
    hr = {
        key: 1,
        class: z(["flex items-center justify-center rounded-md cursor-pointer text-sm text-[#909499]"])
    },
    vr = {
        key: 0
    },
    yr = {
        class: z(["flex items-center justify-start text-[#909499]"])
    },
    br = {
        class: "flex items-center mr-2"
    },
    fr = {
        key: 0,
        class: "text-sm"
    },
    kr = {
        key: 1,
        class: "text-sm"
    },
    wr = {
        class: "flex items-center gap-4 flex-grow"
    },
    Ar = {
        key: 1,
        class: "flex justify-between w-full"
    },
    Cr = {
        key: 2,
        class: z(["right-icon-group text-[#909499]"])
    },
    Mr = {
        key: 0,
        class: "enter-icon-wrapper bg-[#232425] rounded-[35%] text-[#fff] dark:bg-[#eeeeee] dark:text-[#232425]"
    },
    xr = {
        key: 2,
        class: "enter-icon-wrapper rounded-[35%] bg-[#232425] spin-icon"
    },
    Pr = P({
        __name: "PromptInputSuper",
        props: {
            expand: {
                type: Boolean,
                default: !1
            },
            disableExpand: {
                type: Boolean,
                default: !1
            },
            styleClass: String,
            modelValue: String,
            placeholder: String,
            useSuggestion: {
                type: Boolean,
                default: !0
            },
            emptySuggestions: {
                type: Object,
                default: null
            },
            supportImages: {
                type: Boolean,
                default: !1
            },
            showStopAsking: {
                type: Boolean,
                default: !1
            },
            isEditSlideMode: {
                type: Boolean,
                default: !1
            },
            showPersonalizationButton: {
                type: Boolean,
                default: !1
            },
            shouldShowTipsTriangleIcon: {
                type: Boolean,
                default: !1
            },
            projectType: {
                type: String,
                default: ""
            },
            project: {
                type: Object,
                default: null
            },
            messages: {
                type: Object,
                default: null
            }
        },
        emits: ["update:modelValue", "update:expand", "submitPrompt", "inputFocus", "inputBlur", "update:images", "update:files", "stopAsking", "errorMessage", "update:customTools", "createNewChatSession", "selectChatSession", "google-drive-error", "google-drive-cancelled", "google-drive-authenticated", "google-drive-oauth-error", "openGitHubTab"],
        setup(e, {
            expose: t,
            emit: o
        }) {
            const a = () => `data:image/svg+xml;charset=utf-8,${encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">\n  <circle cx="50" cy="50" r="25" stroke="#e0e0e0" stroke-width="4" fill="none" />\n  <path d="M50 25 A25 25 0 0 1 75 50" stroke="#666" stroke-width="4" fill="none" stroke-linecap="round">\n    <animateTransform\n      attributeName="transform"\n      type="rotate"\n      from="0 50 50"\n      to="360 50 50"\n      dur="0.8s"\n      repeatCount="indefinite" />\n  </path>\n</svg>')}`,
                n = [{
                    ext: "pdf",
                    mime: "application/pdf"
                }, {
                    ext: "epub",
                    mime: "application/epub+zip"
                }, {
                    ext: "csv",
                    mime: "text/csv"
                }, {
                    ext: "md",
                    mime: "text/markdown"
                }, {
                    ext: "xlsx",
                    mime: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                }, {
                    ext: "docx",
                    mime: "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                }, {
                    ext: "pptx",
                    mime: "application/vnd.openxmlformats-officedocument.presentationml.presentation"
                }, {
                    ext: "xls",
                    mime: "application/vnd.ms-excel"
                }, {
                    ext: "doc",
                    mime: "application/msword"
                }, {
                    ext: "ppt",
                    mime: "application/vnd.ms-powerpoint"
                }, {
                    ext: "html",
                    mime: "text/html"
                }, {
                    ext: "json",
                    mime: "application/json"
                }, {
                    ext: "xml",
                    mime: "application/xml"
                }, {
                    ext: "yaml",
                    mime: "application/yaml"
                }, {
                    ext: "sh",
                    mime: "text/x-sh"
                }, {
                    ext: "py",
                    mime: "text/x-python-script"
                }, {
                    ext: "js",
                    mime: "application/javascript"
                }, {
                    ext: "ts",
                    mime: "application/typescript"
                }, {
                    ext: "css",
                    mime: "text/css"
                }, {
                    ext: "mjs",
                    mime: "text/javascript"
                }, {
                    ext: "cjs",
                    mime: "text/javascript"
                }, {
                    ext: "cpp",
                    mime: "text/x-c"
                }, {
                    ext: "h",
                    mime: "text/x-chdr"
                }, {
                    ext: "hpp",
                    mime: "text/x-c++hdr"
                }, {
                    ext: "c",
                    mime: "text/x-csrc"
                }, {
                    ext: "cxx",
                    mime: "text/x-c++src"
                }, {
                    ext: "vue",
                    mime: "text/x-vue"
                }, {
                    ext: "txt",
                    mime: "text/plain"
                }, {
                    ext: "mp4",
                    mime: "video/mp4"
                }, {
                    ext: "mp3",
                    mime: "audio/mpeg"
                }, {
                    ext: "wav",
                    mime: "audio/wav"
                }, {
                    ext: "ogg",
                    mime: "audio/ogg"
                }, {
                    ext: "m4a",
                    mime: "audio/m4a"
                }, {
                    ext: "aac",
                    mime: "audio/aac"
                }, {
                    ext: "webm",
                    mime: "video/webm"
                }, {
                    ext: "m4v",
                    mime: "video/mp4"
                }, {
                    ext: "mov",
                    mime: "video/quicktime"
                }, {
                    ext: "avi",
                    mime: "video/x-msvideo"
                }, {
                    ext: "mkv",
                    mime: "video/x-matroska"
                }, {
                    ext: "wmv",
                    mime: "video/x-ms-wmv"
                }],
                r = e,
                s = o,
                c = y(r.showStopAsking),
                l = y(!1),
                p = y(!1),
                d = y(!1),
                m = y(!1),
                u = y(!1),
                h = y(!1),
                v = y(null),
                f = y({}),
                M = y([]),
                x = De(),
                {
                    recordingStatus: P,
                    startRecording: _,
                    resetRecordStatus: F,
                    setFinalResult: N
                } = Pn(),
                W = k("currentUser"),
                Q = y(null),
                J = e => !!Q.value && Q.value.isProjectTypeSupportModelSelection(e),
                ee = e => Q.value ? Q.value.getAvailableModelsForProjectType(e) : [],
                te = e => {
                    x.setAgentSelectedModel(e)
                };
            T(W, (e => {
                (null == e ? void 0 : e.installed_custom_tools) && (f.value = JSON.parse(localStorage.getItem("installedCustomTools") || "{}"))
            }));
            const {
                t: de
            } = S(), me = Pe(), ue = oe(), {
                modelsSelected: ge,
                placeholder: he,
                prompt: ve,
                images: ye,
                files: be,
                loadingNext: fe,
                selectAgent: ke,
                agentSelectedModel: Ae
            } = ie(x), {
                setPrompt: Ce,
                setImages: Me,
                setFiles: xe,
                PLACEHOLDER_MAP: Re
            } = x, Ge = je(), Ue = ze(), {
                isDisableInputPrompt: We,
                isShowRollbackBtn: Ve
            } = ie(Ge), {
                currentVersion: Ke,
                selectedTemplate: Ze
            } = ie(Ue), {
                setSelectedTemplate: Je
            } = Ue, {
                locale: Ye
            } = S();
            let Xe = y(!1);
            T((() => r.showStopAsking), (e => {
                c.value = e
            }));
            const {
                project: $e
            } = X(r), et = g((() => {
                if (!Q.value || !Ae.value) return r.supportImages;
                const e = Q.value.getAvailableModelsForProjectType(r.projectType).find((e => (null == e ? void 0 : e.name) === Ae.value));
                return (!e || !e.not_support_image) && r.supportImages
            })), tt = () => {
                s("createNewChatSession")
            }, it = y(r.expand), ot = y(r.disableExpand), st = y(r.styleClass ? r.styleClass : ""), ct = y(!1), lt = y(null), pt = () => {
                setTimeout((() => {
                    if (lt.value && lt.value.scrollHeight > 0) {
                        lt.value.style.height = "42px";
                        const e = lt.value.scrollHeight;
                        lt.value.style.height = e >= 216 ? "216px" : e + "px"
                    }
                }), 0)
            };
            r.modelValue && Ce(r.modelValue);
            const dt = () => {
                We.value || lt.value && lt.value.focus()
            };
            b((() => {
                var e;
                ue.query.from !== ae && pt(), O.isMobile() || dt();
                const t = "undefined" != typeof WebSocket,
                    i = "undefined" != typeof MediaRecorder,
                    o = navigator.mediaDevices && "function" == typeof navigator.mediaDevices.getUserMedia;
                if (t && i && o && (Xe.value = !0), Et.value = "true" === localStorage.getItem("researchMeClicked"), !O.isGensparkApp()) {
                    localStorage.getItem("SlideStyleModifyFeatureModelIsShow") || (h.value = !0)
                }
                if (null == (e = W.value) ? void 0 : e.installed_custom_tools) {
                    f.value = JSON.parse(localStorage.getItem("installedCustomTools") || "{}"); {
                        const e = localStorage.getItem("selectedCustomTools");
                        if (e) try {
                            M.value = JSON.parse(e)
                        } catch (a) {}
                    }
                }
                T((() => {
                    var e, t;
                    return null == (t = null == (e = $e.value) ? void 0 : e.session_state) ? void 0 : t.use_model
                }), (e => {
                    x.setAgentSelectedModel(e)
                }), {
                    immediate: !0
                }), V.on(H.OPEN_INSTALL_CUSTOM_TOOLS, (() => {
                    p.value = !0
                })), V.on(H.OPEN_MANAGE_CUSTOM_TOOLS, (e => {
                    e && e.query && (Ce(e.query), mt())
                })), window.appInterface || (window.appInterface = {}), window.appInterface.showPersonalization = () => {
                    Ft()
                }
            })), T(it, (e => {
                s("update:expand", e)
            })), T(ve, (e => {
                s("update:modelValue", e), pt()
            })), T((() => r.modelValue), (e => {
                Ce(e)
            })), T(he, (e => {
                e === Re.ASK_GENSPARK_TO_EDIT && dt()
            }));
            const mt = () => {
                    c.value || (be.value.find((e => null == e.private_storage_url)) ? alert("Please wait for all files to be uploaded.") : (ve.value || ye.value.length > 0 || be.value.length > 0 || Ze.value) && (s("submitPrompt", ve.value, ye.value, be.value, null), Ge.setEditSlideStatus("normal")))
                },
                ut = () => {
                    setTimeout((() => {
                        const e = document.querySelector(".tools-btn"),
                            t = v.value;
                        if (O.log("ZZH toolsBtn", e), O.log("ZZH dropdown", t), e && t) {
                            const i = 40,
                                o = 45,
                                a = (Object.keys(f.value || {}).length || 0) * i + o,
                                n = e.getBoundingClientRect(),
                                r = window.innerHeight - n.bottom;
                            t.style.position = "fixed", t.style.left = `${n.left}px`, t.style.display = "block", t.style.top = r < a ? n.top - a + "px" : `${n.bottom}px`, t.style.visibility = "visible"
                        }
                    }), 0)
                },
                gt = e => {
                    const t = document.querySelector(".tools-btn"),
                        i = v.value;
                    !i || i.contains(e.target) || t && t.contains(e.target) || (m.value = !1, window.removeEventListener("click", gt), window.removeEventListener("scroll", ut, !0), window.removeEventListener("resize", ut))
                };
            K((() => {
                window.removeEventListener("click", gt), window.removeEventListener("scroll", ut, !0), window.removeEventListener("resize", ut), V.off(H.OPEN_INSTALL_CUSTOM_TOOLS), V.off(H.OPEN_MANAGE_CUSTOM_TOOLS)
            }));
            const ht = e => {
                    e.target.composing = !0
                },
                vt = e => {
                    e.target.composing = !1
                },
                yt = y(null);

            function bt(e, t, i) {
                return new Promise(((o, a) => {
                    const n = document.createElement("canvas"),
                        r = n.getContext("2d");
                    let s = e.width,
                        c = e.height;
                    s > c ? s > t && (c = Math.round(c * (t / s)), s = t) : c > t && (s = Math.round(s * (t / c)), c = t), n.width = s, n.height = c, r.drawImage(e, 0, 0, s, c);
                    o(n.toDataURL("image/jpeg", i))
                }))
            }
            const ft = e => {
                    const t = new FileReader;
                    t.onload = function(e) {
                        const t = new Image;
                        t.onload = () => {
                            O.log("image loaded"), bt(t, 1024, .8).then((e => {
                                O.log("image resized"), Me([...ye.value, e])
                            })).catch((e => {}))
                        }, t.src = e.target.result
                    }, t.readAsDataURL(e)
                },
                kt = (e, t) => {
                    const i = new FileReader;
                    i.onload = function(e) {
                        const i = new Image;
                        i.onload = () => {
                            bt(i, 1024, .8).then((e => {
                                const i = [...ye.value];
                                i[t] = e, Me(i)
                            })).catch((e => {
                                const i = [...ye.value];
                                i.splice(t, 1), Me(i)
                            }))
                        }, i.src = e.target.result
                    }, i.readAsDataURL(e)
                },
                wt = e => {
                    if (!et.value) return void me.error("This model does not support images");
                    const t = (e.clipboardData || e.originalEvent.clipboardData).items;
                    if (!Array.from(t).find((e => "string" === e.kind && "text/plain" == e.type)))
                        for (let i in t) {
                            const o = t[i];
                            if ("file" === o.kind) {
                                if (o.type.startsWith("image/")) {
                                    const t = o.getAsFile();
                                    return ft(t), void e.preventDefault()
                                }
                                const t = o.getAsFile();
                                si(t), e.preventDefault()
                            }
                        }
                },
                At = {},
                Ct = async (e, t) => {
                    const i = await (async e => {
                        const t = await e.arrayBuffer(),
                            i = await crypto.subtle.digest("SHA-256", t);
                        return Array.from(new Uint8Array(i)).map((e => e.toString(16).padStart(2, "0"))).join("")
                    })(e);
                    if (i && At[i]) return O.log("use cache for file: " + e.name), void xe([...be.value, At[i]]);
                    if (e.size) {
                        let t, i;
                        if (e.type.startsWith("image/") ? (t = 104857600, i = "components.prompt_input_super.file_type.image") : e.type.startsWith("audio/") ? (t = 104857600, i = "components.prompt_input_super.file_type.audio") : e.type.startsWith("video/") ? (t = 104857600, i = "components.prompt_input_super.file_type.video") : (t = 104857600, i = "components.prompt_input_super.file_type.file"), e.size > t) {
                            const e = Math.round(t / 1048576),
                                o = de("components.prompt_input_super.file_size_limit_error", {
                                    fileType: de(i),
                                    maxSize: e
                                });
                            return void alert(o)
                        }
                    }
                    try {
                        be.value.push({
                            name: e.name,
                            type: e.type,
                            size: e.size,
                            ext: t.ext
                        });
                        let o = be.value[be.value.length - 1];
                        const {
                            uploadImageUrl: a,
                            privateStorageUrl: n
                        } = await (async () => {
                            const e = await fetch("/api/get_upload_url");
                            if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                            const t = await e.json();
                            if (!t || 0 !== t.status) throw new Error(`API error! code: ${t.code}`);
                            const i = t.data.upload_image_url,
                                o = t.data.private_storage_url;
                            return {
                                uploadImageUrl: i,
                                path: t.data.path,
                                privateStorageUrl: o
                            }
                        })();
                        if (!a || !n) return;
                        (new FormData).append("file", e);
                        if (!(await fetch(a, {
                                method: "PUT",
                                headers: {
                                    "x-ms-blob-type": "BlockBlob"
                                },
                                body: e
                            })).ok) throw new Error("Network response was not ok");
                        O.log("upload file ok: " + a), o.private_storage_url = n, i && (O.log("cache file: " + e.name), At[i] = o)
                    } catch (o) {
                        be.value = be.value.filter((e => e !== fileObj))
                    }
                },
                Mt = (e, t) => {
                    const i = (() => {
                        const e = document.createElement("input");
                        e.type = "file", e.style.display = "none", e.multiple = !0;
                        const t = n.map((e => e.mime));
                        return t.splice(0, 0, n.map((e => `.${e.ext}`))), et.value && t.push("image/*"), e.accept = t.join(","), e
                    })();
                    let o = !1;
                    const a = () => {
                        document.body.contains(i) && document.body.removeChild(i)
                    };
                    O.log("selectFile"), i.onchange = async e => {
                        try {
                            if (O.log("file input onchange"), o) return;
                            o = !0;
                            let t = e.target.files;
                            if (!t) return;
                            for (let e of t) O.log(`name: ${e.name}, type: ${e.type}`), si(e)
                        } catch (t) {} finally {
                            a()
                        }
                    }, i.oncancel = () => {
                        o || a()
                    }, document.body.appendChild(i), i.click()
                },
                xt = (e, t) => new Promise(((e, i) => {
                    const o = (e => {
                        const t = document.createElement("input");
                        return t.type = "file", t.accept = "image/*", t.style.display = "none", "capture" === e && (t.capture = "camera"), t
                    })(t);
                    O.log("selectImage"), o.onchange = async t => {
                        O.log("image input onchange");
                        let a = t.target.files[0];
                        if (a) {
                            O.log("file", a.name, a.type);
                            try {
                                ft(a), e(!0)
                            } catch (n) {
                                i(n)
                            } finally {
                                setTimeout((() => {
                                    document.body.contains(o) && document.body.removeChild(o)
                                }), 300)
                            }
                        } else i(new Error("No file selected"))
                    }, o.onerror = e => {
                        i(e)
                    }, document.body.appendChild(o), o.click()
                })),
                Pt = async () => {
                    nt(null == W ? void 0 : W.value) && _()
                },
                St = () => {
                    N(), F()
                },
                Tt = () => {
                    F()
                },
                It = O.filesizeString,
                _t = y(!1),
                Lt = y(null),
                Et = y(!1),
                Ft = () => {
                    "true" === localStorage.getItem("researchMeClicked") ? (l.value = !1, d.value = !0) : (l.value = !0, d.value = !1), Et.value = !0, localStorage.setItem("researchMeClicked", "true")
                },
                Ot = () => {
                    Ue.setIsShowRevertVersionModal(!0)
                },
                Bt = () => {
                    u.value = !1, h.value = !1
                },
                jt = e => {
                    O.log("Manual input:", e)
                },
                zt = e => {
                    O.isGensparkApp() ? O.windowopen(`/agents?type=super_agent&prompt=${e}`) : (ve.value = e, mt())
                },
                Rt = e => {
                    Ce(e), mt()
                },
                Gt = y(!1),
                qt = y(!1),
                Nt = y(0);
            g((() => Nt.value > 0));
            const Ut = () => {
                    "o1-preview" != ge.value ? ke.value === Ee.GENERATE_IMAGE || ke.value === Ee.GENERATE_VIDEO ? xt() : Mt() : me.info("O1 Preview does not support file upload.")
                },
                Wt = () => {
                    "o1-preview" != ge.value ? Gt.value = !0 : me.info("O1 Preview does not support file upload.")
                },
                Vt = () => {
                    qt.value = !0
                },
                Ht = async e => {
                    const t = (Array.isArray(e) ? e : [e]).map((e => Qt(e)));
                    try {
                        await Promise.all(t)
                    } catch (i) {}
                },
                Qt = async e => {
                    var t;
                    const i = {
                        name: e.name,
                        type: e.mimeType,
                        size: e.size || 0,
                        ext: e.name.split(".").pop() || "txt",
                        private_storage_url: null
                    };
                    let o = -1;
                    if (e.mimeType.startsWith("image/")) {
                        o = ye.value.length;
                        const t = a();
                        O.log("ZZH loading picture:", e.name), Me([...ye.value, t])
                    } else be.value.push(i);
                    try {
                        const a = await fetch("/api/get_gdrive_uploaded_url", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify({
                                fileId: e.id,
                                name: e.name,
                                mimeType: e.mimeType,
                                url: e.url
                            })
                        });
                        if (!a.ok) throw new Error(`Failed to get upload URL: ${a.status} ${a.statusText}`);
                        const r = await a.json();
                        if (!r || 0 !== r.status || !(null == (t = r.data) ? void 0 : t.private_storage_url)) {
                            const e = 0 !== (null == r ? void 0 : r.status) ? (null == r ? void 0 : r.message) || "Server returned an error processing the Google Drive file" : "Server did not return a valid private_storage_url";
                            throw new Error(e)
                        } {
                            const t = r.data.private_storage_url,
                                a = r.data.image_data_base64;
                            if (e.mimeType.startsWith("image/"))
                                if (a) {
                                    if (O.log("ZZH using server provided image data"), -1 !== o) {
                                        const t = [...ye.value],
                                            i = a.startsWith("data:") ? a : `data:${e.mimeType};base64,${a}`;
                                        t[o] = i, Me(t)
                                    }
                                } else try {
                                    O.log("ZZH trying to fetch image data from URL:", t);
                                    const e = await fetch(t);
                                    if (!e.ok) throw new Error(`Failed to fetch image: ${e.status}`);
                                    const i = await e.blob();
                                    kt(i, o), O.log("ZZH processing image with placeholder")
                                } catch (n) {
                                    if (-1 !== o) {
                                        const e = [...ye.value];
                                        e[o] = t, Me(e), O.log("ZZH fallback to using privateUrl directly")
                                    }
                                } else {
                                    const e = be.value.findIndex((e => e.name === i.name && e.size === i.size && e.type === i.type && null === e.private_storage_url));
                                    if (-1 !== e) {
                                        const o = { ...i,
                                            private_storage_url: t
                                        };
                                        be.value.splice(e, 1, o)
                                    }
                                }
                        }
                    } catch (n) {
                        const t = "function" == typeof de ? de("components.promptinput.gdrive_processing_error", "Failed to process Google Drive file") : "Failed to process Google Drive file";
                        if (me.error(t), e.mimeType.startsWith("image/") && -1 !== o) {
                            const e = [...ye.value];
                            e.splice(o, 1), Me(e)
                        } else {
                            const e = be.value.findIndex((e => e.name === i.name && e.size === i.size && e.type === i.type)); - 1 !== e && be.value.splice(e, 1)
                        }
                    }
                },
                Kt = e => {
                    me.error(e)
                },
                Zt = g((() => {
                    const e = n.map((e => e.mime));
                    if (et.value) {
                        const t = ["image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp", "image/svg+xml", "image/tiff", "image/x-icon", "image/heif", "image/heic", "image/avif"];
                        e.push(...t)
                    }
                    return e
                })),
                Jt = g((() => [])),
                Yt = g((() => !!f.value[Dt])),
                Xt = e => {
                    e.preventDefault()
                },
                $t = e => {
                    e.preventDefault(), We.value || Nt.value++
                },
                ei = e => {
                    We.value || (e.preventDefault(), Nt.value--)
                },
                ti = async e => {
                    if (e.preventDefault(), Nt.value = 0, We.value) return;
                    if ("o1-preview" === ge.value) return void me.info("O1 Preview does not support file upload.");
                    const t = e.dataTransfer;
                    if (t && 0 !== t.files.length)
                        for (let i = 0; i < t.files.length; i++) {
                            const e = t.files[i];
                            si(e)
                        }
                },
                ii = async e => {
                    if (!e || 0 === e.length) return;
                    const t = e.map((e => oi(e)));
                    try {
                        await Promise.all(t)
                    } catch (i) {}
                },
                oi = async e => {
                    try {
                        if (e.mime_type && e.mime_type.startsWith("image/")) {
                            const i = ye.value.length,
                                o = a();
                            Me([...ye.value, o]);
                            try {
                                const t = await fetch(Qe.downloadFile(e.path)),
                                    o = await t.blob();
                                kt(o, i)
                            } catch (t) {
                                const e = [...ye.value];
                                throw e.splice(i, 1), Me(e), t
                            }
                        } else {
                            const i = {
                                name: e.name,
                                type: e.mime_type || "application/octet-stream",
                                size: e.size || 0,
                                ext: e.name.split(".").pop() || "txt",
                                private_storage_url: null,
                                from_ai_drive: !0
                            };
                            be.value.push(i);
                            try {
                                const t = await Qe.getPrivateStorageUrl(e.path),
                                    o = be.value.findIndex((e => e.name === i.name && !e.private_storage_url)); - 1 !== o && (be.value[o].private_storage_url = t)
                            } catch (t) {
                                be.value = be.value.filter((e => e !== i)), me.error(de("components.promptinput.aidrive_processing_error"))
                            }
                        }
                    } catch (t) {
                        me.error(de("components.promptinput.aidrive_processing_error"))
                    }
                },
                ai = () => {
                    if (!Ze.value) return 85;
                    const e = Ze.value,
                        t = (e.width || 1280) / (e.height || 720),
                        i = Math.round(48 * t);
                    return Math.max(60, Math.min(120, i))
                };
            t({
                getDom: () => yt.value,
                setInputHeight: pt,
                focus: () => {
                    lt.value.focus()
                },
                searchInputFocus: dt,
                selectImage: xt,
                selectFile: Mt,
                addImageUrl(e) {
                    e && "string" == typeof e && (ye.value = [...ye.value, e], s("update:images", ye.value))
                },
                handleResearchMeClick: Ft
            });
            const ni = y(null),
                ri = y(null);
            T((() => {
                var e, t, i, o, a;
                return (null == (t = null == (e = $e.value) ? void 0 : e.session_state) ? void 0 : t.canvas_history_id) + (null == (a = null == (o = null == (i = $e.value) ? void 0 : i.session_state) ? void 0 : o.canvas_history) ? void 0 : a.length)
            }), (e => {
                var t, i, o, a;
                if (!(null == (i = null == (t = $e.value) ? void 0 : t.session_state) ? void 0 : i.canvas_history_id) || !(null == (a = null == (o = $e.value) ? void 0 : o.session_state) ? void 0 : a.canvas_history)) return void(ri.value = null);
                const n = $e.value.session_state.canvas_history.findIndex((e => {
                    var t, i;
                    return e.id === (null == (i = null == (t = $e.value) ? void 0 : t.session_state) ? void 0 : i.canvas_history_id)
                }));
                ri.value = -1 !== n && 0 !== n ? {
                    canvas_history_item: $e.value.session_state.canvas_history[n],
                    canvas_history_item_index: n
                } : null
            }));
            const si = e => {
                const t = n.find((t => t.mime == e.type)) || n.find((t => t.ext == e.name.split(".").pop()));
                if (e.type.startsWith("image/")) return et.value ? (ft(e), !0) : (s("errorMessage", "Image is not supported"), !1);
                if (t) {
                    if ("application/json" === e.type || t.mime && t.mime.startsWith("text/")) {
                        const i = new FileReader;
                        i.onload = function(i) {
                            const o = i.target.result,
                                a = new File([o], e.name.replace(".json", ".json.txt"), {
                                    type: "text/plain"
                                });
                            t.ext = "txt", t.mime = "text/plain", Ct(a, t)
                        }, i.readAsText(e)
                    } else Ct(e, t);
                    return !0
                }
                return alert(`${e.name} is not supported, type is: ${e.type}`), !1
            };
            return (t, o) => {
                var a, n, p, m, g, h, v, y, b, f, k, M, x, S;
                const T = i;
                return C(), w(L, null, [A("div", {
                    class: z(["input px-[12px] pb-[12px] cursor-text", [ct.value ? "active" : "", st.value, Lt.value && Lt.value.isVisible() ? "has-suggestions" : "", _t.value ? "focus" : "", j(ve).length > 0 || c.value ? "" : "empty", j(We) ? "opacity-50" : ""]]),
                    onClick: dt,
                    ref_key: "inputWrapper",
                    ref: yt,
                    onDragover: Xt,
                    onDragenter: $t,
                    onDragleave: ei,
                    onDrop: ti
                }, [ri.value ? (C(), w("div", Sn, [A("div", Tn, [A("div", In, I(t.$t("components.slides.unsatisfied_with_recent_changes")), 1), A("div", {
                    class: "button",
                    onClick: o[0] || (o[0] = () => {
                        ni.value.open()
                    })
                }, [A("div", _n, [Z(j(aa))]), A("div", Ln, I(t.$t("components.slides.rollback_to", {
                    name: ri.value.canvas_history_item.name
                })), 1)])])])) : B("", !0), Z(xn, {
                    project: j($e),
                    "show-stop-asking": c.value,
                    onConvert: Rt
                }, null, 8, ["project", "show-stop-asking"]), Z(Ga, {
                    project: j($e),
                    showStopAsking: c.value,
                    onOpenGitHubTab: o[1] || (o[1] = e => t.$emit("openGitHubTab"))
                }, null, 8, ["project", "showStopAsking"]), Z(Be, {
                    modelValue: l.value,
                    "onUpdate:modelValue": o[2] || (o[2] = e => l.value = e),
                    showPersonalizationDialog: d.value,
                    "onUpdate:showPersonalizationDialog": o[3] || (o[3] = e => d.value = e),
                    onManualInput: jt,
                    onAutoResearch: zt
                }, null, 8, ["modelValue", "showPersonalizationDialog"]), Z(T, null, {
                    default: Y((() => [Z(fa, {
                        visible: Gt.value,
                        "onUpdate:visible": o[4] || (o[4] = e => Gt.value = e),
                        multiselect: !0,
                        mimeTypes: Zt.value,
                        onGoogleDriveSelected: Ht,
                        onGoogleDriveCancelled: o[5] || (o[5] = e => Gt.value = !1),
                        onGoogleDriveError: Kt
                    }, null, 8, ["visible", "mimeTypes"])])),
                    _: 1
                }), Z(wn, {
                    visible: qt.value,
                    "onUpdate:visible": o[6] || (o[6] = e => qt.value = e),
                    "multi-select": !0,
                    "support-mimetypes": Jt.value,
                    onConfirm: ii
                }, null, 8, ["visible", "support-mimetypes"]), j(We) ? (C(), w("div", Dn)) : B("", !0), et.value && (null == (a = j(ye)) ? void 0 : a.length) > 0 || (null == (n = j(be)) ? void 0 : n.length) > 0 || j(Ze) ? (C(), w("div", En, [et.value ? (C(!0), w(L, {
                    key: 0
                }, D(j(ye), (e => {
                    var t;
                    return C(), w("div", {
                        class: "prompt-file image",
                        key: e
                    }, [A("div", {
                        class: "remove",
                        onClick: () => {
                            ye.value = j(ye).filter((t => t != e))
                        }
                    }, [Z(j(re))], 8, Fn), e.includes("base64") ? (C(), w("img", {
                        key: 0,
                        src: e
                    }, null, 8, On)) : "image_agent" == (null == (t = j($e)) ? void 0 : t.type) && e ? (C(), w("img", {
                        key: 1,
                        src: e
                    }, null, 8, Bn)) : (C(), w("div", jn, [Z(j(rt), {
                        stroke: "#666",
                        size: 20
                    })]))])
                })), 128)) : B("", !0), (C(!0), w(L, null, D(j(be), (e => (C(), w("div", {
                    class: "prompt-file",
                    key: e.name
                }, [A("div", zn, [A("div", {
                    class: "remove",
                    onClick: t => be.value = j(be).filter((t => t != e))
                }, [Z(j(re))], 8, Rn), e.private_storage_url ? (C(), w("div", Vn, [A("div", Hn, ["pdf" == e.ext ? (C(), E(j(Se), {
                    key: 0
                })) : "doc" == e.ext || "docx" == e.ext ? (C(), E(j(Te), {
                    key: 1
                })) : "xls" == e.ext || "xlsx" == e.ext ? (C(), E(j(_e), {
                    key: 2
                })) : (C(), E(j(Ie), {
                    key: 3
                }))]), A("div", Qn, [A("div", Kn, I(e.name), 1), A("div", Zn, I(j(It)(e.size)), 1)])])) : (C(), w("div", Gn, [A("div", qn, [Z(j(rt), {
                    stroke: "#666",
                    size: 20
                })]), A("div", Nn, [A("div", Un, I(e.name), 1), A("div", Wn, I(t.$t("components.tryon.uploading")), 1)])]))])])))), 128)), j(Ze) ? (C(), w("div", Jn, [A("div", Yn, [A("div", {
                    class: "remove",
                    onClick: o[7] || (o[7] = e => j(Je)(null))
                }, [Z(j(re))]), A("div", Xn, [A("div", {
                    class: "template-preview",
                    style: U({
                        width: ai() + "px"
                    })
                }, [A("img", {
                    src: j(Ze).pages && j(Ze).pages.length > 0 ? j(Ze).pages[0].thumbnail_url || j(Ze).pages[0].screenshot_url : "https://www.genspark.ai/image_placeholder.png",
                    alt: j(Ze).name,
                    class: "template-preview-image"
                }, null, 8, $n)], 4), A("div", er, [A("div", tr, I(j(Ze).name), 1), A("div", ir, I(t.$t("components.slides.template_label")), 1)])])])])) : B("", !0)])) : B("", !0), j(Ve) ? (C(), w("div", or, [A("div", ar, I(t.$t("components.slides.unsatisfied_with_recent_changes")), 1), A("div", {
                    class: "mt-[12px] px-[14px] py-[6px] bg-neutral-800 rounded-xl inline-flex justify-center items-center gap-2.5 cursor-pointer",
                    onClick: Ot
                }, [Z(j(aa)), A("div", nr, I(t.$t("components.slides.rollback_to_save_point")) + " -" + I(j(Ke).version_number), 1)])])) : (C(), w("div", rr, [G(A("textarea", {
                    onPaste: wt,
                    name: "query",
                    ref_key: "searchInput",
                    ref: lt,
                    "onUpdate:modelValue": o[8] || (o[8] = e => $(ve) ? ve.value = e : null),
                    onKeydown: o[9] || (o[9] = e => {
                        var t;
                        (e => {
                            if ("Enter" === e.key) {
                                if (O.isMobile()) return;
                                if (e.isComposing || 229 === e.keyCode || e.target && e.target.composing) return;
                                if (e.metaKey || e.ctrlKey || e.shiftKey) {
                                    e.preventDefault();
                                    const t = e.target,
                                        i = t.selectionStart,
                                        o = t.value.substring(0, i),
                                        a = t.value.substring(i);
                                    t.value = o + "\n" + a, t.scrollTop = t.scrollHeight, t.selectionStart = t.selectionEnd = i + 1, e = new Event("input", {
                                        bubbles: !0,
                                        cancelable: !0
                                    }), t.dispatchEvent(e)
                                } else e.preventDefault(), mt()
                            }
                        })(e), null == (t = Lt.value) || t.onKeydown(e)
                    }),
                    onCompositionstart: ht,
                    onCompositionend: vt,
                    onFocus: o[10] || (o[10] = e => {
                        var t;
                        _t.value = !0, null == (t = Lt.value) || t.onFocus(e), s("inputFocus", e)
                    }),
                    onBlur: o[11] || (o[11] = e => {
                        var t;
                        _t.value = !1, null == (t = Lt.value) || t.onBlur(e), s("inputBlur", e)
                    }),
                    onInput: o[12] || (o[12] = e => ct.value = !0),
                    class: z([ct.value ? "active" : "", "search-input j-search-input h-[42px] placeholder:text-[#909499] dark:text-white"]),
                    type: "text",
                    placeholder: j(he)
                }, null, 42, sr), [
                    [q, j(ve)]
                ]), A("div", {
                    class: z(["icon-group justify-between items-center", {
                        "in-speech": "speech" == j(P)
                    }])
                }, ["speech" != j(P) ? (C(), w("div", cr, [(_ = null == (p = j($e)) ? void 0 : p.type, Q.value && Q.value.isProjectTypeSupportChatSessions(_) ? (C(), w("div", {
                    key: 0,
                    class: "chat-session-wrapper",
                    onClick: o[13] || (o[13] = R((() => {}), ["stop", "prevent"]))
                }, [Z(j(He), {
                    trigger: "click",
                    placement: "top-start",
                    "show-arrow": !1,
                    style: {
                        "--n-padding": "0",
                        "--n-border-radius": "8px"
                    }
                }, {
                    trigger: Y((() => [A("div", lr, [Z(j(qe))])])),
                    default: Y((() => [A("div", pr, [A("div", dr, [A("div", mr, I(t.$t("components.prompt_input_super.long_chat_affect_quality")), 1), A("div", {
                        class: "button",
                        onClick: tt
                    }, [A("div", ur, [Z(j(wa))]), A("div", gr, I(t.$t("components.prompt_input_super.new_chat")), 1)])]), B("", !0)])])),
                    _: 1
                })])) : B("", !0)), j(ke) == j(Ee).INIT_AGENT && r.showPersonalizationButton ? (C(), w("div", hr, [(null == (g = null == (m = j(W)) ? void 0 : m.custom_instructions) ? void 0 : g.nickname) || (null == (v = null == (h = j(W)) ? void 0 : h.custom_instructions) ? void 0 : v.occupation) || (null == (b = null == (y = j(W)) ? void 0 : y.custom_instructions) ? void 0 : b.traits) || (null == (k = null == (f = j(W)) ? void 0 : f.custom_instructions) ? void 0 : k.user_profile) || !j(W) ? (C(), w("div", vr, [A("div", {
                    class: z(["research-me-btn-icon hover:bg-[#f0f0f0] dark:hover:bg-[#444]"]),
                    onClick: R(Ft, ["stop"])
                }, [Z(j(ia))])])) : (C(), w("div", {
                    key: 1,
                    class: "research-me-btn",
                    onClick: R(Ft, ["stop"])
                }, [A("div", yr, [A("span", br, [Z(j(ia))]), Et.value ? (C(), w("div", fr, I(t.$t("pages.channel.personalize")), 1)) : (C(), w("div", kr, I(t.$t("pages.channel.research_me")), 1))])]))])) : B("", !0), r.showPersonalizationButton && j(ke) == j(Ee).INIT_AGENT && j(ue).query.from !== j(ae) ? (C(), w("div", {
                    key: 2,
                    class: "custom-tools-icon-container",
                    onClick: o[14] || (o[14] = R((() => {}), ["stop"]))
                }, [Z(jo, {
                    shouldShowTipsTriangleIcon: r.shouldShowTipsTriangleIcon
                }, null, 8, ["shouldShowTipsTriangleIcon"])])) : B("", !0), J(r.projectType) ? (C(), w("div", {
                    key: 3,
                    class: "model-selection-icon-container",
                    onClick: o[15] || (o[15] = R((() => {}), ["stop"]))
                }, [Z(Yo, {
                    availableModels: ee(r.projectType),
                    defaultModel: j(Ae),
                    disabled: (null == (x = null == (M = e.messages) ? void 0 : M.value) ? void 0 : x.length) > 0 || (null == (S = e.messages) ? void 0 : S.length) > 0,
                    onModelChanged: te
                }, null, 8, ["availableModels", "defaultModel", "disabled"])])) : B("", !0)])) : B("", !0), A("div", wr, ["sparkQuestion" != st.value || ot.value ? B("", !0) : (C(), w("div", {
                    key: 0,
                    onClick: o[16] || (o[16] = e => {
                        e.preventDefault(), it.value = !it.value
                    }),
                    class: "input-icon expand"
                }, [it.value ? (C(), E(j(se), {
                    key: 0
                })) : (C(), E(j(ce), {
                    key: 1
                }))])), "speech" == j(P) ? (C(), w("div", Ar, [A("div", {
                    class: "flex justify-center items-center w-[36px] h-[36px] bg-gray-200 rounded-full cursor-pointer dark:invert",
                    onClick: R(Tt, ["stop"])
                }, [Z(j(Oe), {
                    class: "w-[22px] h-[22px] text-[#262626]"
                })]), Z(ea), A("div", {
                    class: "flex justify-center items-center w-[36px] h-[36px] bg-[#262626] rounded-full cursor-pointer dark:invert",
                    onClick: R(St, ["stop"])
                }, [Z(j(we), {
                    class: "w-[30px] h-[30px] text-white"
                })])])) : (C(), w("div", Cr, [j(ke) != j(Ee).GENERATE_IMAGE && j(ke) != j(Ee).GENERATE_VIDEO ? (C(), w("div", {
                    key: 0,
                    class: z(["upload-attachments flex items-center", {
                        disabled: "o1-preview" == j(ge)
                    }])
                }, [Z(Ka, {
                    disabled: "o1-preview" === j(ge),
                    multiselect: !0,
                    mimeTypes: Zt.value,
                    supportImages: et.value,
                    googleDriveEnable: Yt.value,
                    onLocalFileUpload: Ut,
                    onGoogleDriveUpload: Wt,
                    onAidriveUpload: Vt
                }, null, 8, ["disabled", "mimeTypes", "supportImages", "googleDriveEnable"])], 2)) : B("", !0), j(Xe) && "start" == j(P) && j(ue).query.from !== j(ae) ? (C(), w("div", {
                    key: 1,
                    onClick: R(Pt, ["stop"]),
                    class: z(["cursor-pointer flex items-center p-[8px] rounded-[35%] hover:bg-[#f0f0f0] dark:hover:bg-[#444]"])
                }, [Z(j($o))])) : B("", !0), A("div", {
                    onClick: o[17] || (o[17] = R((e => {
                        var t;
                        c.value ? s("stopAsking") : (e.stopPropagation(), null == (t = Lt.value) || t.hide(), mt())
                    }), ["stop"])),
                    class: z(["input-icon ml-[8px]"])
                }, [c.value ? (C(), w("div", Mr, [Z(j(le), {
                    class: "stop-icon"
                })])) : "searchAskFollowUp" == st.value ? (C(), E(j(pe), {
                    key: 1
                })) : j(fe) ? (C(), w("div", xr, [Z(j(Fe), {
                    class: "animate-spin"
                })])) : (C(), w("div", {
                    key: 3,
                    class: z(["enter-icon items-end cursor-pointer", j(ve).length > 0 ? "active" : ""])
                }, [A("div", {
                    class: z(["enter-icon-wrapper rounded-[35%] md:hover:opacity-80", j(ve) || j(ye).length > 0 || j(be).length > 0 ? "bg-[#262626] active text-white dark:bg-[#eeeeee] dark:text-[#232425]" : "bg-[#f4f4f4] text-[#909499] dark:bg-[#eeeeee30]"])
                }, [Z(j(Le))], 2)], 2))])]))])], 2), ne(t.$slots, "bottom-in-textarea-wrapper", {}, void 0, !0)])), Z(j(Ne), {
                    show: u.value,
                    "onUpdate:show": o[18] || (o[18] = e => u.value = e),
                    "mask-closable": !1
                }, {
                    default: Y((() => [Z(la, {
                        onClose: Bt
                    })])),
                    _: 1
                }, 8, ["show"])], 34), Z(La, {
                    ref_key: "revertGlobalCanvasDialogRef",
                    ref: ni,
                    project: j($e),
                    onConfirm: o[19] || (o[19] = () => {
                        tt()
                    })
                }, null, 8, ["project"]), Z(at, {
                    ref_key: "agentsConfigsRef",
                    ref: Q,
                    style: {
                        display: "none"
                    }
                }, null, 512)], 64);
                var _
            }
        }
    }, [
        ["__scopeId", "data-v-ff21ecef"]
    ]),
    Sr = {
        INIT: "init",
        AGENT_SELECTED: "agent-selected"
    },
    Tr = () => {
        const e = y(Sr.INIT);
        return {
            inputStatus: e,
            setInputStatus: t => {
                e.value = t
            }
        }
    },
    Ir = P({
        __name: "SearchInputWrapper",
        props: {
            inputStatus: {
                type: String,
                required: !1
            }
        },
        setup: e => (k("currentUser"), (t, i) => (C(), w("div", {
            class: z(["w-full md:min-w-[760px] md:w-[var(--container-width, 760px)] mt-[12px] bg-white border border-gray-200 dark:border-[#e6e9eb40] overflow-visible input-wrapper box-border search-input-wrapper max-sm:w-[calc(100vw-32px)] dark:bg-[#333] rounded-[24px] shadow-[0px_6px_30px_0px_rgba(0,0,0,0.08)]", "agent-selected" == e.inputStatus ? "mt-[24px]" : "mt-[12px]"])
        }, [ne(t.$slots, "default", {}, void 0, !0)], 2)))
    }, [
        ["__scopeId", "data-v-e894f63c"]
    ]);
export {
    Sr as I, wa as N, Ir as S, mt as U, _o as _, Pr as a, Nt as g, Tr as u
};