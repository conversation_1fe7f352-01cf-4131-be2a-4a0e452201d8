import {
    d as r,
    o as e,
    b as l
} from "./Cf0SOiw0.js";
const t = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const o = {
        render: function(o, n) {
            return e(), r("svg", t, n[0] || (n[0] = [l("circle", {
                cx: "11.9967",
                cy: "11.9967",
                r: "9.53571",
                stroke: "currentColor",
                "stroke-width": "1.5"
            }, null, -1), l("path", {
                "fill-rule": "evenodd",
                "clip-rule": "evenodd",
                d: "M19.3761 9.33502C19.669 9.04212 19.669 8.56725 19.3761 8.27436C19.0832 7.98146 18.6084 7.98146 18.3155 8.27436L13.4667 13.1232L12.5285 12.1836C12.2358 11.8904 11.761 11.8901 11.4679 12.1827C11.1747 12.4754 11.1744 12.9503 11.467 13.2434L12.9355 14.7142C13.0761 14.855 13.2669 14.9342 13.466 14.9342C13.665 14.9343 13.8559 14.8553 13.9966 14.7146L19.3761 9.33502ZM14.0077 9.40198C14.3006 9.10909 14.3006 8.63421 14.0077 8.34132C13.7148 8.04843 13.2399 8.04843 12.947 8.34132L8.09778 13.1905L5.67096 10.7637C5.37806 10.4708 4.90319 10.4708 4.61029 10.7637C4.3174 11.0566 4.3174 11.5315 4.61029 11.8244L7.56745 14.7815C7.86034 15.0744 8.33521 15.0744 8.62811 14.7815L14.0077 9.40198Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    n = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const C = {
    render: function(t, o) {
        return e(), r("svg", n, o[0] || (o[0] = [l("rect", {
            x: "3.32031",
            y: "2.46094",
            width: "17.3571",
            height: "19.0714",
            rx: "2.67857",
            stroke: "currentColor",
            "stroke-width": "1.5"
        }, null, -1), l("path", {
            "fill-rule": "evenodd",
            "clip-rule": "evenodd",
            d: "M9.32731 12.8524C9.32729 12.4401 8.98992 12.1059 8.57378 12.1059C8.15763 12.1059 7.8203 12.4402 7.82031 12.8524L7.82049 17.1642C7.82051 17.5765 8.15787 17.9107 8.57402 17.9107C8.99016 17.9107 9.3275 17.5764 9.32749 17.1642L9.32731 12.8524ZM16.1344 6.85445C16.1344 6.44223 15.797 6.10805 15.3808 6.10805C14.9646 6.10805 14.6272 6.44223 14.6272 6.85445L14.6272 17.1677C14.6272 17.5799 14.9646 17.9141 15.3808 17.9141C15.797 17.9141 16.1344 17.5799 16.1344 17.1677L16.1344 6.85445ZM12.7093 10.2856C12.7093 9.87337 12.372 9.53919 11.9558 9.53916C11.5396 9.53914 11.2022 9.8733 11.2021 10.2855L11.2018 17.1676C11.2018 17.5798 11.5391 17.914 11.9553 17.9141C12.3715 17.9141 12.7089 17.5799 12.709 17.1677L12.7093 10.2856Z",
            fill: "currentColor"
        }, null, -1)]))
    }
};
export {
    o as C, C as D
};