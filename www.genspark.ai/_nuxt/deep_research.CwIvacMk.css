[data-v-967c3835] .n-data-table .n-data-table-table {
    word-break: unset
}

.fade-enter-active[data-v-967c3835],
.fade-leave-active[data-v-967c3835] {
    transition: opacity .5s
}

.fade-enter[data-v-967c3835],
.fade-leave-to[data-v-967c3835] {
    opacity: 0
}

.scroll_icon[data-v-967c3835] {
    align-items: center;
    background: #232425;
    border-radius: 8px;
    box-shadow: 0 4px 10px #00000026;
    cursor: pointer;
    display: flex;
    height: 26px;
    justify-content: center;
    position: absolute;
    right: 8px;
    top: 10px;
    width: 56px;
    z-index: 1
}

.scroll_icon .icon[data-v-967c3835] {
    animation: moveAround-967c3835 1s infinite;
    color: #fff;
    height: 16px;
    transform: translate(-2px);
    width: 16px
}

@keyframes moveAround-967c3835 {
    0%,
    to {
        transform: translate(-2px)
    }
    50% {
        transform: translate(2px)
    }
}

[data-v-967c3835] .cell_value_ele .value {
    font-weight: 700;
    margin-right: 8px
}

[data-v-967c3835] .cell_value_ele .desc {
    margin-right: 8px
}

[data-v-967c3835] .cell_value_ele .ref_urls a {
    background: #eee;
    border-radius: 18px;
    color: #666;
    display: inline-flex;
    font-size: 8px;
    font-style: normal;
    font-weight: 400;
    height: 12px;
    line-height: 14px;
    margin-right: 4px;
    overflow: hidden;
    padding: 0 6px;
    text-decoration: none
}

[data-v-967c3835] .cell_value_ele .ref_urls a:hover {
    background: #0c66cc;
    color: #fff
}

.table_cell_details[data-v-967c3835] {
    width: 100%
}

.chart_wrapper[data-v-967c3835] {
    margin-top: 18px
}

.controls .button[data-v-967c3835] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    cursor: pointer;
    gap: 4px;
    justify-content: flex-start;
    padding: 2px 8px
}

.controls[data-v-967c3835],
.controls .button[data-v-967c3835] {
    display: flex;
    flex-direction: row
}

.controls[data-v-967c3835] {
    gap: 8px;
    justify-content: flex-end;
    margin-top: 8px
}

.resource.highlight[data-v-967c3835] {
    background-color: #ff0;
    transition: none
}

.full_screen_chart_wrapper[data-v-967c3835] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    height: 100%;
    justify-content: center;
    padding: 20px;
    width: 100%
}

.full_screen_chart[data-v-967c3835] {
    background: #fff;
    height: 100%;
    width: 100%
}

.full_screen_table_wrapper[data-v-967c3835] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    padding: 20px;
    width: 100%
}

.full_screen_table[data-v-967c3835] {
    max-width: 100%;
    width: -moz-fit-content;
    width: fit-content
}

.resource[data-v-967c3835] {
    background: #f8f8f8;
    border-radius: 8px;
    box-sizing: border-box;
    margin-top: 12px;
    padding: 12px 16px;
    transition: background-color 3s ease
}

.resource[data-v-967c3835] .block_quote_wrapper {
    background-color: #fff
}

.resource .markdown-viewer[data-v-967c3835] {
    margin-top: 16px
}

.resource .sub-title[data-v-967c3835] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-top: 8px;
    overflow: hidden;
    width: 100%
}

.resource .sub-title .label[data-v-967c3835] {
    flex-shrink: 0
}

.resource .sub-title .favicon[data-v-967c3835] {
    border-radius: 4px;
    flex-shrink: 0;
    height: 20px;
    overflow: hidden;
    width: 20px
}

.resource .sub-title .favicon img[data-v-967c3835] {
    height: 100%;
    width: 100%
}

.resource_title .icon[data-v-967c3835] {
    border-radius: 4px;
    overflow: hidden
}

.resource_title img[data-v-967c3835] {
    height: 100%;
    width: 100%
}

.resource_header[data-v-967c3835] {
    cursor: pointer;
    justify-content: space-between
}

.resource_header[data-v-967c3835],
.resource_header .resource_title[data-v-967c3835] {
    align-items: center;
    display: flex;
    flex-direction: row
}

.resource_header .resource_title[data-v-967c3835] {
    gap: 8px
}

.resource_header .resource_title[data-v-967c3835],
.resource_header .resource_title .text[data-v-967c3835] {
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis
}

.resource_header .resource_title .text[data-v-967c3835] {
    display: -webkit-box
}

.resource_header .resource_title .text a[data-v-967c3835] {
    color: #606366;
    text-decoration: none
}

.resource_header.expand .resource_title .text a[data-v-967c3835] {
    color: #0f7fff;
    text-decoration: none
}

.resource_header.expand .resource_title .text a[data-v-967c3835]:hover {
    text-decoration: underline
}

.icon[data-v-967c3835] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.resource_header .icon[data-v-967c3835] {
    color: #909499
}

@media (prefers-color-scheme:dark) {
    .scroll_icon[data-v-967c3835] {
        background-color: #eee
    }
    .scroll_icon .icon[data-v-967c3835] {
        color: #232425
    }
    .resource[data-v-967c3835] {
        background: #222
    }
    .resource[data-v-967c3835] .block_quote_wrapper {
        background-color: #333
    }
}

.call-information[data-v-a3ae3df8] {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%
}

.call-information__container[data-v-a3ae3df8] {
    align-items: stretch;
    border: 1px solid #efefef;
    border-radius: 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    gap: 18px;
    justify-content: flex-start;
    padding: 10px 16px 20px;
    width: auto
}

@media (prefers-color-scheme:dark) {
    .call-information__container[data-v-a3ae3df8] {
        border: 1px solid hsla(0, 0%, 94%, .3)
    }
}

.call-information__title[data-v-a3ae3df8] {
    color: #232425;
    flex-shrink: 0;
    font-family: Arial;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    left: 16px;
    line-height: 150%;
    top: 10px;
    width: 384px
}

@media (prefers-color-scheme:dark) {
    .call-information__title[data-v-a3ae3df8] {
        color: #fff
    }
}

.call-information__body[data-v-a3ae3df8],
.call-information__contact[data-v-a3ae3df8] {
    display: flex;
    flex-direction: column;
    gap: 16px
}

.call-information__contact[data-v-a3ae3df8] {
    background: #fafafa;
    border-radius: 8px;
    left: 16px;
    padding: 12px;
    top: 18px
}

@media (prefers-color-scheme:dark) {
    .call-information__contact[data-v-a3ae3df8] {
        background: #fafafa20
    }
}

.call-information__contact-details[data-v-a3ae3df8] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.call-information__contact-name[data-v-a3ae3df8] {
    color: #232425;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    width: 100%
}

@media (prefers-color-scheme:dark) {
    .call-information__contact-name[data-v-a3ae3df8] {
        color: #fff
    }
}

.call-information__contact-address[data-v-a3ae3df8] {
    color: #606366;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    left: 16px;
    line-height: 150%;
    top: 18px;
    width: 100%
}

@media (prefers-color-scheme:dark) {
    .call-information__contact-address[data-v-a3ae3df8] {
        color: #ccc
    }
}

.call-information__contact-phone[data-v-a3ae3df8] {
    color: #606366;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    left: 16px;
    line-height: 150%;
    top: 18px;
    width: 100%
}

@media (prefers-color-scheme:dark) {
    .call-information__contact-phone[data-v-a3ae3df8] {
        color: #ccc
    }
}

.call-information__contact-map[data-v-a3ae3df8] {
    border: 1px solid #efefef;
    border-radius: 12px;
    height: 66px
}

@media (prefers-color-scheme:dark) {
    .call-information__contact-map[data-v-a3ae3df8] {
        border: 1px solid hsla(0, 0%, 94%, .3)
    }
}

.call-information__divider[data-v-a3ae3df8] {
    border: 1px solid #eaeaea;
    left: 16px;
    top: 16px;
    width: 100%
}

@media (prefers-color-scheme:dark) {
    .call-information__divider[data-v-a3ae3df8] {
        border-color: #444
    }
}

.call-information__purpose[data-v-a3ae3df8] {
    color: #232425;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    width: 100%
}

@media (prefers-color-scheme:dark) {
    .call-information__purpose[data-v-a3ae3df8] {
        color: #fff
    }
}

.call-information__thinking[data-v-a3ae3df8] {
    border: 1px solid #efefef;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    gap: 16px;
    padding: 10px 16px 0
}

@media (prefers-color-scheme:dark) {
    .call-information__thinking[data-v-a3ae3df8] {
        border: 1px solid hsla(0, 0%, 94%, .3)
    }
}

.call-information__thinking-title[data-v-a3ae3df8] {
    color: #232425;
    font-family: Arial;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

@media (prefers-color-scheme:dark) {
    .call-information__thinking-title[data-v-a3ae3df8] {
        color: #fff
    }
}

.call-information__thinking-content[data-v-a3ae3df8] {
    color: #232425;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%
}

.call-information__thinking-content .markdown-viewer[data-v-a3ae3df8] :last-child {
    margin-bottom: 0
}

.call-information__thinking-content .markdown-viewer[data-v-a3ae3df8]>:first-child {
    margin-top: 0;
    max-width: 100%
}

.call-information__thinking-content .markdown-viewer[data-v-a3ae3df8] a {
    color: #000;
    cursor: pointer;
    display: inline-flex;
    line-height: calc(1em + 2px);
    max-width: 100%;
    outline: none;
    position: relative;
    text-decoration: none;
    transition: all .3s ease
}

@media (prefers-color-scheme:dark) {
    .call-information__thinking-content[data-v-a3ae3df8] {
        color: #fff
    }
    .call-information__thinking-content .markdown-viewer[data-v-a3ae3df8] a {
        color: #eee
    }
}

.phone_call_group[data-v-ccdbbb95] {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%
}

.phone_call[data-v-ccdbbb95] {
    border: 1px solid #efefef;
    border-radius: 16px;
    width: auto
}

@media (prefers-color-scheme:dark) {
    .phone_call[data-v-ccdbbb95] {
        border: 1px solid hsla(0, 0%, 94%, .3)
    }
}

.phone_call .phone_call_header[data-v-ccdbbb95] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 16px 24px 16px 16px
}

.phone_call .phone_call_header .phone_call_title[data-v-ccdbbb95] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 10px
}

.phone_call .phone_call_header .phone_call_title .icon-container[data-v-ccdbbb95] {
    display: inline-block;
    flex-shrink: 0;
    height: 48px;
    position: relative;
    width: 48px
}

.phone_call .phone_call_header .phone_call_title .icon-container .phone-icon[data-v-ccdbbb95] {
    color: #232425;
    flex-shrink: 0;
    height: 24px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 24px
}

.phone_call .phone_call_header .phone_call_title .phone_call_title_container[data-v-ccdbbb95] {
    display: flex;
    flex-direction: column;
    gap: 0
}

.phone_call .phone_call_header .phone_call_title .phone_call_title_container .phone_call_title[data-v-ccdbbb95] {
    color: #232425;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

.phone_call .phone_call_header .phone_call_status[data-v-ccdbbb95],
.phone_call .phone_call_header .phone_call_title .phone_call_title_container .phone_call_subtitle[data-v-ccdbbb95] {
    color: #909499;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%
}

.phone_call .phone_call_header .phone_call_status[data-v-ccdbbb95] {
    text-align: right
}

.phone_call .phone_call_header .phone_call_status.before_call[data-v-ccdbbb95] {
    color: #0f7fff
}

.phone_call .phone_call_header .phone_call_status.during_call[data-v-ccdbbb95] {
    color: #5cd4a1
}

@media (max-width:768px) {
    .phone_call .phone_call_header[data-v-ccdbbb95] {
        align-items: flex-start;
        flex-direction: column;
        padding: 16px 16px 0
    }
    .phone_call .phone_call_header .phone_call_status[data-v-ccdbbb95] {
        padding-top: 16px
    }
}

.phone_call .divider[data-v-ccdbbb95] {
    background: #eaeaea;
    height: 1px;
    margin-bottom: 24px
}

.phone_call_button[data-v-ccdbbb95] {
    align-items: center;
    border: 6px solid #fff;
    border-radius: 28px;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    gap: 8px;
    justify-content: center;
    padding: 6px 12px;
    width: 140px
}

.phone_call_button.hang_up[data-v-ccdbbb95] {
    background: #ff3838
}

.phone_call_button.call_again[data-v-ccdbbb95] {
    background: #0f7fff
}

.phone_call_button.start_new_call[data-v-ccdbbb95] {
    background: #333
}

@media (prefers-color-scheme:dark) {
    .phone_call_button[data-v-ccdbbb95] {
        border: 6px solid #222
    }
}

.phone_call_button .phone_call_button_icon[data-v-ccdbbb95] {
    flex-shrink: 0;
    height: 24px;
    width: 24px
}

.phone_call_button .phone_call_button_text[data-v-ccdbbb95] {
    color: #fff;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal
}

.realtime_conversation_items[data-v-ccdbbb95] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    gap: 0;
    margin-top: -30px;
    padding: 0 16px 24px;
    width: auto
}

.realtime_conversation_item[data-v-ccdbbb95] {
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-top: 30px
}

.realtime_conversation_item .realtime_conversation_item_time[data-v-ccdbbb95] {
    color: #0f7fff;
    cursor: pointer;
    font-family: Arial;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin-left: 8px;
    margin-right: 8px
}

.realtime_conversation_item .realtime_conversation_item_time_invisible[data-v-ccdbbb95] {
    color: transparent;
    font-family: Arial;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin-left: 8px;
    margin-right: 8px
}

.realtime_conversation_item_content[data-v-ccdbbb95] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.realtime_conversation_item .role[data-v-ccdbbb95] {
    color: #909499;
    font-family: Arial;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

.realtime_conversation_item .content[data-v-ccdbbb95] {
    color: #232425;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    overflow-wrap: break-word;
    white-space: pre-wrap
}

.realtime_conversation_item .key_pressed[data-v-ccdbbb95] {
    font-size: 16px;
    font-style: normal;
    font-style: italic;
    font-weight: 400
}

.phone_call .call_ended[data-v-ccdbbb95] {
    color: #909499;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    margin-bottom: 16px;
    margin-left: 16px;
    margin-top: -12px
}

@media (prefers-color-scheme:dark) {
    .phone_call .phone_call_header .phone_call_title .phone_call_title_container .phone_call_title[data-v-ccdbbb95] {
        color: #fff
    }
    .phone_call .phone_call_header .phone_call_status[data-v-ccdbbb95],
    .phone_call .phone_call_header .phone_call_title .phone_call_title_container .phone_call_subtitle[data-v-ccdbbb95],
    .realtime_conversation_item .role[data-v-ccdbbb95] {
        color: #ccc
    }
    .realtime_conversation_item .content[data-v-ccdbbb95] {
        color: #fff
    }
    .realtime_conversation_item .key_pressed[data-v-ccdbbb95] {
        color: #ccc
    }
    .phone_call .divider[data-v-ccdbbb95],
    .phone_call .divider_end[data-v-ccdbbb95] {
        background: #444
    }
    .phone_call .call_ended[data-v-ccdbbb95] {
        color: #ccc
    }
}

.phone_call_group .phone_call_error[data-v-ccdbbb95] {
    color: #ff3838;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    margin-top: 4px
}

.phone_call_group .phone_call_error.error_type_canceled[data-v-ccdbbb95] {
    color: #909499
}

.phone_call_group .summary_card[data-v-ccdbbb95] {
    border: 1px solid #efefef;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    gap: 16px;
    padding: 10px 16px 0
}

@media (prefers-color-scheme:dark) {
    .phone_call_group .summary_card[data-v-ccdbbb95] {
        border: 1px solid hsla(0, 0%, 94%, .3)
    }
}

.phone_call_group .summary_card .title[data-v-ccdbbb95] {
    font-weight: 700
}

.phone_call_group .summary_card .content[data-v-ccdbbb95],
.phone_call_group .summary_card .title[data-v-ccdbbb95] {
    color: #232425;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    line-height: 150%
}

.phone_call_group .summary_card .content[data-v-ccdbbb95] {
    font-weight: 400
}

@media (prefers-color-scheme:dark) {
    .phone_call_group .summary_card .content[data-v-ccdbbb95],
    .phone_call_group .summary_card .title[data-v-ccdbbb95] {
        color: #fff
    }
}

.audio_player[data-v-ccdbbb95] {
    align-items: center;
    background: #fff;
    border: 1.508px solid #efefef;
    border-radius: 24px;
    display: flex;
    flex-direction: row;
    gap: 25px;
    justify-content: space-between;
    padding: 12px 24px
}

@media (prefers-color-scheme:dark) {
    .audio_player[data-v-ccdbbb95] {
        border: 1px solid hsla(0, 0%, 94%, .3)
    }
}

.play_button[data-v-ccdbbb95] {
    align-items: center;
    background: #ff3838;
    border-radius: 25px;
    cursor: pointer;
    display: flex;
    gap: 8.621px;
    height: 48px;
    justify-content: center;
    width: 48px
}

.play_button .pause_icon[data-v-ccdbbb95],
.play_button .play_icon[data-v-ccdbbb95] {
    flex-shrink: 0;
    height: 24px;
    width: 24px
}

.time_display[data-v-ccdbbb95] {
    color: #232425;
    font-family: Arial;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%
}

@media (prefers-color-scheme:dark) {
    .audio_player[data-v-ccdbbb95] {
        background: #fafafa20
    }
    .time_display[data-v-ccdbbb95] {
        color: #fff
    }
}

.progress_bar[data-v-ccdbbb95] {
    background: #ddd;
    border-radius: 17px;
    cursor: pointer;
    flex-grow: 1;
    height: 5px;
    position: relative
}

.progress_bar_filled[data-v-ccdbbb95] {
    background: #0f7fff;
    border-radius: 17px;
    height: 100%;
    left: 0;
    position: absolute;
    position: relative;
    top: 0
}

.progress_handle[data-v-ccdbbb95] {
    background: #fff;
    border: 1px solid #0f7fff;
    border-radius: 10px;
    box-shadow: 0 0 4px #0000001a;
    height: 12px;
    position: absolute;
    right: -3.5px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px
}

@media (hover:hover) {
    .progress_bar:hover .progress_handle[data-v-ccdbbb95] {
        transform: translateY(-50%) scale(1.2);
        transition: transform .2s ease
    }
}

@media (prefers-color-scheme:dark) {
    .progress_handle[data-v-ccdbbb95] {
        background: #0f7fff;
        border-color: #efefef
    }
}

.loading-anim[data-v-ccdbbb95] {
    margin: 12px 0
}

[data-v-ccdbbb95] .verbatim-highlight {
    background: #ff0;
    color: #000
}

.markdown-viewer.markdown-viewer-loading[data-v-ccdbbb95] {
    align-items: flex-end;
    display: flex;
    flex-direction: row;
    max-height: 60px;
    overflow: hidden
}

.bubble.user+.hide-intermediate-steps-switch.bubble.assistant[data-v-ccdbbb95] {
    margin-top: 44px
}

.hide-intermediate-steps-switch.bubble.assistant[data-v-ccdbbb95] {
    position: relative
}

.hide-intermediate-steps-switch.bubble.assistant .bubble-inner.card-place-holder1[data-v-ccdbbb95],
.hide-intermediate-steps-switch.bubble.assistant .bubble-inner.card-place-holder2[data-v-ccdbbb95] {
    border-radius: 16px;
    height: 40px;
    position: absolute;
    top: -10px;
    transform: scale(.97);
    z-index: 2
}

.hide-intermediate-steps-switch.bubble.assistant .bubble-inner.card-place-holder2[data-v-ccdbbb95] {
    top: -20px;
    transform: scale(.94);
    z-index: 1
}

.hide-intermediate-steps-switch.bubble.assistant .bubble-inner[data-v-ccdbbb95] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: center;
    position: relative;
    z-index: 3
}

.hide-intermediate-steps-switch .see-more[data-v-ccdbbb95] {
    align-items: center;
    background: #efefef;
    border-radius: 39px;
    color: #666;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    flex-grow: 0;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    gap: 4px;
    justify-content: center;
    line-height: normal;
    padding: 4px 16px;
    width: -moz-fit-content;
    width: fit-content
}

.partner-data-container[data-v-ccdbbb95] {
    align-items: flex-start;
    display: flex;
    margin-top: 8px
}

.bubble .spark_result_card[data-v-ccdbbb95] {
    background: #f5f5f5;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
    padding: 16px
}

.bubble .spark_result_card .logo[data-v-ccdbbb95] {
    display: flex;
    height: 22px;
    width: 71px
}

.bubble .spark_result_card .logo img[data-v-ccdbbb95] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.bubble .spark_result_card .title[data-v-ccdbbb95] {
    color: #094c99;
    cursor: pointer;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 27px
}

.bubble .spark_result_card .content[data-v-ccdbbb95] {
    color: #232425;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px
}

.bubble .spark_result_card .see-more[data-v-ccdbbb95] {
    align-items: center;
    background: #0f7fff;
    border-radius: 39px;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    gap: 4px;
    justify-content: center;
    padding: 4px 16px
}

.bubble .spark_result_card .see-more .icon[data-v-ccdbbb95] {
    color: #fff;
    height: 16px;
    width: 16px
}

.gallery[data-v-ccdbbb95] {
    align-items: center;
    display: flex;
    flex-direction: row;
    height: 184px;
    justify-content: space-between;
    overflow: hidden
}

.gallery .image[data-v-ccdbbb95] {
    cursor: pointer;
    display: flex;
    height: 100%;
    position: relative;
    width: 100%
}

.gallery .image img[data-v-ccdbbb95] {
    border-radius: 8px;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.gallery .image .youtube_icon[data-v-ccdbbb95] {
    height: 32px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 45px
}

.gallery .gallery__item[data-v-ccdbbb95] {
    height: 100%
}

.gallery_2 .gallery__item[data-v-ccdbbb95] {
    width: 49%
}

.gallery_3 .gallery__item[data-v-ccdbbb95] {
    width: 24%
}

.gallery_3 .gallery__item[data-v-ccdbbb95]:first-child {
    width: 48%
}

.gallery_4 .gallery__item[data-v-ccdbbb95] {
    width: 24%
}

.stop_icon[data-v-ccdbbb95] {
    align-items: center;
    color: #909499;
    display: flex;
    height: 12px;
    justify-content: center;
    width: 12px
}

.verify_results[data-v-ccdbbb95] {
    margin-top: -8px
}

.verify_results.not_expand[data-v-ccdbbb95] {
    margin-top: 0
}

[data-v-ccdbbb95] svg {
    height: 100%;
    width: 100%
}

.resource_content .text-content[data-v-ccdbbb95] {
    padding-top: 28px;
    position: relative
}

.quote-left[data-v-ccdbbb95],
.quote-right[data-v-ccdbbb95] {
    align-items: center;
    color: #eaeaea;
    display: flex;
    height: 60px;
    justify-content: center;
    position: absolute;
    width: 60px;
    z-index: 1
}

.quote-left[data-v-ccdbbb95] {
    left: -2px;
    top: 12px
}

.quote-right[data-v-ccdbbb95] {
    bottom: 12px;
    right: 18px;
    transform: rotate(180deg)
}

.expand .icon[data-v-ccdbbb95] {
    border-radius: 50%
}

.bubble-inner .block[data-v-ccdbbb95] {
    margin-top: 16px;
    position: relative
}

.bubble-inner .block[data-v-ccdbbb95]:first-child {
    margin-top: 0
}

.bubble .evidence .title .source[data-v-ccdbbb95] {
    font-style: normal;
    font-weight: 400;
    padding-left: 5px
}

.bubble .evidence .title[data-v-ccdbbb95] {
    display: block
}

.seq_icon[data-v-ccdbbb95] {
    align-items: center;
    background: #efefef;
    border-radius: 50%;
    display: flex;
    font-weight: 400;
    height: 16px;
    justify-content: center;
    width: 16px
}

.statements[data-v-ccdbbb95] {
    gap: 10px
}

.statement-details[data-v-ccdbbb95],
.statements[data-v-ccdbbb95] {
    display: flex;
    flex-direction: column;
    margin-top: 20px
}

.statement-details[data-v-ccdbbb95] {
    gap: 30px
}

.statement[data-v-ccdbbb95] {
    border-bottom: 1px solid #efefef;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-bottom: 24px;
    padding-top: 24px
}

.statement.first[data-v-ccdbbb95] {
    padding-top: 0
}

.statement.last[data-v-ccdbbb95] {
    border-bottom: none;
    padding-bottom: 0
}

.statement .header[data-v-ccdbbb95] {
    cursor: pointer;
    display: flex;
    flex-direction: row;
    justify-content: space-between
}

.statement .content[data-v-ccdbbb95] {
    color: #000;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px
}

.statement .check-button[data-v-ccdbbb95] {
    align-items: center;
    color: #0f7fff;
    display: flex
}

.evidence_str[data-v-ccdbbb95] {
    display: flex;
    flex-direction: column;
    gap: 12px;
    line-height: 1.8;
    padding-top: 12px;
    position: relative;
    z-index: 2
}

.explanation[data-v-ccdbbb95] {
    margin-top: 18px
}

.detailed_analysis[data-v-ccdbbb95] {
    margin-top: 20px
}

.statement-content[data-v-ccdbbb95] {
    color: #000;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 1.5
}

.block[data-v-ccdbbb95] {
    background: #fff;
    border-radius: 8px
}

.block .block[data-v-ccdbbb95] {
    background: #fafafa
}

.expand[data-v-ccdbbb95],
.expand-conclusion[data-v-ccdbbb95],
.expand-evidences[data-v-ccdbbb95],
.expand-generate-markdown[data-v-ccdbbb95],
.expand-resources[data-v-ccdbbb95] {
    align-items: center;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    gap: 12px;
    justify-content: space-between
}

.expand-resources[data-v-ccdbbb95] {
    align-items: flex-start;
    display: flex;
    flex-direction: column
}

.expand-resources .title .title-content[data-v-ccdbbb95] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.bubble-inner .expand-resources .title>.icon[data-v-ccdbbb95] {
    align-items: center;
    color: #909499;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.expand-resources .title[data-v-ccdbbb95] {
    justify-content: space-between
}

.expand-resources .title[data-v-ccdbbb95],
.verify_results .sub-title[data-v-ccdbbb95] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    width: 100%
}

.verify_results .sub-title[data-v-ccdbbb95] {
    overflow: hidden
}

.verify_results .sub-title .label[data-v-ccdbbb95] {
    flex-shrink: 0
}

.verify_results .sub-title .favicon[data-v-ccdbbb95] {
    border-radius: 4px;
    flex-shrink: 0;
    height: 20px;
    overflow: hidden;
    width: 20px
}

.verify_results .sub-title .favicon img[data-v-ccdbbb95] {
    height: 100%;
    width: 100%
}

.resource .sub-title[data-v-ccdbbb95] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    overflow: hidden;
    width: 100%
}

.resource .sub-title .label[data-v-ccdbbb95] {
    flex-shrink: 0
}

.resource .sub-title .favicon[data-v-ccdbbb95] {
    border-radius: 4px;
    flex-shrink: 0;
    height: 20px;
    overflow: hidden;
    width: 20px
}

.resource .sub-title .favicon img[data-v-ccdbbb95] {
    height: 100%;
    width: 100%
}

.resource_title .icon[data-v-ccdbbb95] {
    border-radius: 4px;
    overflow: hidden
}

.resource_title img[data-v-ccdbbb95] {
    height: 100%;
    width: 100%
}

.expand-evidences .text[data-v-ccdbbb95] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.expand-conclusion .text[data-v-ccdbbb95] {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis
}

.conclusion[data-v-ccdbbb95] {
    line-height: 1.8
}

.bubble[data-v-ccdbbb95],
.conclusion[data-v-ccdbbb95],
.conclusion .block[data-v-ccdbbb95] {
    margin-top: 16px
}

.bubble[data-v-ccdbbb95] {
    display: flex;
    flex-direction: row;
    width: 100%
}

.bubble-inner[data-v-ccdbbb95] {
    box-sizing: border-box;
    max-width: 100%;
    padding: 16px
}

.bubble.user+.bubble.assistant[data-v-ccdbbb95] {
    margin-top: 24px
}

.markdown-viewer[data-v-ccdbbb95] :last-child {
    margin-bottom: 0
}

.bubble.user[data-v-ccdbbb95] {
    justify-content: flex-end
}

.bubble.user .bubble-inner[data-v-ccdbbb95] {
    background: #fff;
    border-radius: 16px
}

.bubble.user.article-content .bubble-inner[data-v-ccdbbb95] {
    background: linear-gradient(0deg, #232425, #232425), #fff;
    color: #fff;
    padding: 16px
}

.bubble.assistant .bubble-inner[data-v-ccdbbb95] {
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 16px
}

.bubble.assistant .bubble-inner[data-v-ccdbbb95],
.bubble.assistant .bubble-no-wrapper[data-v-ccdbbb95] {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: 100%
}

.bubble.assistant .bubble-inner.notice[data-v-ccdbbb95],
.bubble.assistant .bubble-inner.notice .block[data-v-ccdbbb95] {
    background: #fafafa
}

.bubble.assistant.full_width .bubble-inner[data-v-ccdbbb95] {
    width: 100%
}

.bubble .statement .header .title[data-v-ccdbbb95] {
    font-size: 16px;
    margin: 0
}

.bubble .title[data-v-ccdbbb95] {
    align-items: center;
    color: #232425;
    display: flex;
    flex-direction: row;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    gap: 8px;
    line-height: 20px
}

.bubble .title a[data-v-ccdbbb95] {
    color: #0c66cc
}

.resource_content .link[data-v-ccdbbb95] {
    margin-bottom: 12px;
    margin-top: 10px
}

.resource_content .link a[data-v-ccdbbb95] {
    color: #0f7fff;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    text-decoration: none;
    word-break: break-all
}

.resource_content .divider[data-v-ccdbbb95] {
    background: #eaeaea;
    height: 1px;
    margin-bottom: 12px;
    margin-top: 12px
}

.statements-header[data-v-ccdbbb95] {
    align-items: center;
    color: #475666;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 20px
}

.resource[data-v-ccdbbb95] {
    background: #f8f8f8;
    border-radius: 8px;
    box-sizing: border-box;
    margin-top: 12px;
    padding: 12px 16px;
    transition: background-color 3s ease
}

.resource[data-v-ccdbbb95] .block_quote_wrapper {
    background-color: #fff
}

.resource .markdown-viewer[data-v-ccdbbb95] {
    margin-top: 16px
}

.resource_header[data-v-ccdbbb95] {
    cursor: pointer;
    justify-content: space-between
}

.resource_header[data-v-ccdbbb95],
.resource_header .resource_title[data-v-ccdbbb95] {
    align-items: center;
    display: flex;
    flex-direction: row
}

.resource_header .resource_title[data-v-ccdbbb95] {
    gap: 8px
}

.resource_header .resource_title[data-v-ccdbbb95],
.resource_header .resource_title .text[data-v-ccdbbb95] {
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis
}

.resource_header .resource_title .text[data-v-ccdbbb95] {
    display: -webkit-box
}

.resource_header .resource_title .text a[data-v-ccdbbb95] {
    color: #606366;
    text-decoration: none
}

.resource_header.expand .resource_title .text a[data-v-ccdbbb95] {
    color: #0f7fff;
    text-decoration: none
}

.resource_header.expand .resource_title .text a[data-v-ccdbbb95]:hover {
    text-decoration: underline
}

.search_queries[data-v-ccdbbb95] {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    position: relative
}

.search_queries .expand-search-queries[data-v-ccdbbb95] {
    align-items: center;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 100%
}

.search_queries .search_query[data-v-ccdbbb95] {
    background: #f8f8f8;
    border-radius: 8px;
    color: #232425;
    display: flex;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    padding: 8px 12px
}

.icon[data-v-ccdbbb95] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.statements .input[type=checkbox][data-v-ccdbbb95] {
    height: 16px;
    min-width: 16px;
    width: 16px
}

.statements .input[data-v-ccdbbb95] {
    align-items: center;
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 12px;
    display: flex;
    flex-direction: row;
    margin-top: 24px;
    min-width: 300px;
    position: relative;
    width: 100%
}

.statements .input.disabled[data-v-ccdbbb95] {
    opacity: .3
}

.statements input[data-v-ccdbbb95] {
    background: transparent;
    border: none;
    outline: none;
    padding: 10px 0 10px 20px;
    width: 100%
}

.statements .add_button[data-v-ccdbbb95] {
    cursor: pointer;
    padding-right: 10px
}

.statements .begin_cross_check[data-v-ccdbbb95] {
    align-items: center;
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 12px;
    color: #666;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    gap: 10px;
    justify-content: center;
    line-height: normal;
    margin-top: 24px;
    padding: 10px
}

.statements .begin_cross_check.disabled[data-v-ccdbbb95] {
    opacity: .3
}

.statements .begin_cross_check .icon[data-v-ccdbbb95] {
    height: 16px;
    width: 16px
}

.bubble-inner .icon[data-v-ccdbbb95] {
    color: #909499
}

.bubble-inner .title .icon[data-v-ccdbbb95] {
    align-items: center;
    color: #5cd4a1;
    display: flex;
    height: 16px;
    justify-content: center;
    width: 26px
}

.bubble-inner .title .icon[data-v-ccdbbb95] .loading-animation {
    margin-top: 0
}

.markdown-viewer[data-v-ccdbbb95]>:first-child {
    margin-top: 0;
    max-width: 100%
}

.markdown-viewer[data-v-ccdbbb95] .-md-ext-block-quote {
    display: none
}

.markdown-viewer[data-v-ccdbbb95] .-md-ext-block-quote.-md-ext-initialized {
    display: block
}

.markdown-viewer[data-v-ccdbbb95] .-md-ext-promptlink {
    background-color: #eaeaea;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3E%3Cpath fill='%230F7FFF' d='m1.89 12.164-.003.012z'/%3E%3Cpath fill='%23000' d='M9.371 13.293a.5.5 0 0 0-.322-.253c-.18-.048-.39-.04-.552-.051a6.3 6.3 0 0 1-4.025-1.836A6.23 6.23 0 0 1 2.7 7.63c-.044-.304-.044-.38-.044-.38a.5.5 0 0 0-.875-.33s-.246.267-.362.536a5.07 5.07 0 0 0 .344 4.69c.*************.063.338l-.313 1.623a.75.75 0 0 0 .25.716l.015.012a.76.76 0 0 0 .753.109l1.672-.645a.25.25 0 0 1 .18 0 5.8 5.8 0 0 0 1.992.375c.81 0 1.609-.177 2.341-.521.17-.08.438-.167.594-.348a.47.47 0 0 0 .061-.512'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M13.446 12.613q.143.059.3.062a.75.75 0 0 0 .715-.973l-.492-1.748.034-.066.005-.008.003-.004.008-.01.005-.007.023-.031.005-.01A5.56 5.56 0 0 0 15 6.72a5.56 5.56 0 0 0-1.647-3.964A6 6 0 0 0 9.07 1a6.1 6.1 0 0 0-3.762 1.297 5.7 5.7 0 0 0-2.048 3.25q-.127.58-.127 1.175a5.73 5.73 0 0 0 1.69 4.078 5.83 5.83 0 0 0 4.154 1.707c.617 0 1.386-.197 1.607-.258.407-.112.796-.258.839-.274a.32.32 0 0 1 .235.003l.013.005zM6.498 6a.5.5 0 1 0 0 1h5a.5.5 0 1 0 0-1zm-.5-1.5a.5.5 0 0 1 .5-.5h5a.5.5 0 1 1 0 1h-5a.5.5 0 0 1-.5-.5m.5 3.5a.5.5 0 1 0 0 1h3a.5.5 0 0 0 0-1z' clip-rule='evenodd'/%3E%3C/svg%3E");
    background-position: right 10px center;
    background-repeat: no-repeat;
    border-radius: 8px 2px 8px 8px;
    color: #232425;
    cursor: pointer;
    display: inline-flex;
    padding: 3px 30px 3px 10px;
    text-decoration: none
}

.markdown-viewer[data-v-ccdbbb95] div+.-md-ext-block-quote {
    margin: 16px 0 10px
}

.markdown-viewer[data-v-ccdbbb95] .-md-ext-gallery {
    margin: 0 0 16px
}

.markdown-viewer[data-v-ccdbbb95] .-md-ext-promptlink:hover {
    background-color: #0f7fff;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3E%3Cpath fill='%230F7FFF' d='m1.89 12.164-.003.012z'/%3E%3Cpath fill='%23fff' d='M9.371 13.293a.5.5 0 0 0-.322-.253c-.18-.048-.39-.04-.552-.051a6.3 6.3 0 0 1-4.025-1.836A6.23 6.23 0 0 1 2.7 7.63c-.044-.304-.044-.38-.044-.38a.5.5 0 0 0-.875-.33s-.246.267-.362.536a5.07 5.07 0 0 0 .344 4.69c.*************.063.338l-.313 1.623a.75.75 0 0 0 .25.716l.015.012a.76.76 0 0 0 .753.109l1.672-.645a.25.25 0 0 1 .18 0 5.8 5.8 0 0 0 1.992.375c.81 0 1.609-.177 2.341-.521.17-.08.438-.167.594-.348a.47.47 0 0 0 .061-.512'/%3E%3Cpath fill='%23fff' fill-rule='evenodd' d='M13.446 12.613q.143.059.3.062a.75.75 0 0 0 .715-.973l-.492-1.748.034-.066.005-.008.003-.004.008-.01.005-.007.023-.031.005-.01A5.56 5.56 0 0 0 15 6.72a5.56 5.56 0 0 0-1.647-3.964A6 6 0 0 0 9.07 1a6.1 6.1 0 0 0-3.762 1.297 5.7 5.7 0 0 0-2.048 3.25q-.127.58-.127 1.175a5.73 5.73 0 0 0 1.69 4.078 5.83 5.83 0 0 0 4.154 1.707c.617 0 1.386-.197 1.607-.258.407-.112.796-.258.839-.274a.32.32 0 0 1 .235.003l.013.005zM6.498 6a.5.5 0 1 0 0 1h5a.5.5 0 1 0 0-1zm-.5-1.5a.5.5 0 0 1 .5-.5h5a.5.5 0 1 1 0 1h-5a.5.5 0 0 1-.5-.5m.5 3.5a.5.5 0 1 0 0 1h3a.5.5 0 0 0 0-1z' clip-rule='evenodd'/%3E%3C/svg%3E");
    color: #fff
}

.markdown-viewer[data-v-ccdbbb95] .-md-ext-promptlink:after {
    content: none
}

.markdown-viewer[data-v-ccdbbb95] .digital-citation-link:after {
    content: none
}

.markdown-viewer[data-v-ccdbbb95] a:has(>img):after {
    content: none
}

.markdown-viewer[data-v-ccdbbb95] .-md-ext-promptlink:hover:after {
    content: none
}

.markdown-viewer[data-v-ccdbbb95] blockquote {
    border-left: 4px solid #eaeaea;
    color: #606366;
    margin: 0;
    padding-left: 12px
}

.markdown-viewer[data-v-ccdbbb95] a {
    color: #000;
    cursor: pointer;
    display: inline-flex;
    line-height: calc(1em + 2px);
    max-width: 100%;
    outline: none;
    position: relative;
    text-decoration: none;
    transition: all .3s ease
}

.markdown-viewer[data-v-ccdbbb95] .digital-citation-link {
    align-items: center;
    background: #eee;
    border-radius: 18px;
    color: #666;
    display: inline-flex;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    height: 18px;
    justify-content: center;
    line-height: 18px;
    text-decoration: none;
    transform: translateY(-2px);
    width: 18px
}

.markdown-viewer[data-v-ccdbbb95] .digital-citation-link:hover {
    background: #0c66cc;
    color: #fff
}

@media (hover:none) {
    .markdown-viewer[data-v-ccdbbb95] a {
        display: inline-flex;
        text-decoration: underline;
        text-decoration-color: #0f7fff
    }
}

@media (hover:hover) {
    .markdown-viewer[data-v-ccdbbb95] a:after {
        background-color: #0f7fff;
        bottom: -2px;
        content: "";
        height: 2px;
        left: 0;
        position: absolute;
        transition: all .3s ease;
        width: 100%
    }
    .markdown-viewer[data-v-ccdbbb95] a:hover {
        color: #0f7fff
    }
    .markdown-viewer[data-v-ccdbbb95] a:focus:after,
    .markdown-viewer[data-v-ccdbbb95] a:hover:after {
        background-color: #0f7fff3f;
        height: calc(100% + 3px);
        left: 0;
        width: 100%
    }
}

.markdown-viewer[data-v-ccdbbb95] iframe {
    border: 0;
    height: 382.5px;
    margin: 0 0 10px;
    padding: 0;
    width: 680px
}

.markdown-viewer[data-v-ccdbbb95] div+.-md-ext-block-link {
    margin: 20px 0 10px
}

.markdown-viewer[data-v-ccdbbb95] div+iframe {
    margin: 30px 0 10px
}

.markdown-viewer[data-v-ccdbbb95] .-md-ext-youtube-widget,
.markdown-viewer[data-v-ccdbbb95] .-md-ext-product-comments {
    margin: 30px 0 10px
}

.markdown-viewer[data-v-ccdbbb95] p img,
.markdown-viewer[data-v-ccdbbb95] table img {
    display: inline-block
}

.markdown-viewer[data-v-ccdbbb95] table a img {
    display: inline-block;
    vertical-align: middle
}

.markdown-viewer[data-v-ccdbbb95] table a {
    display: inline-block;
    margin-left: 5px;
    vertical-align: middle
}

.markdown-viewer[data-v-ccdbbb95] img {
    display: block;
    max-width: 100%
}

.markdown-viewer[data-v-ccdbbb95] li p {
    line-height: 28px;
    margin-bottom: 0
}

.markdown-viewer[data-v-ccdbbb95] p {
    font-size: 16px;
    line-height: 28px;
    margin-bottom: 20px
}

.markdown-viewer[data-v-ccdbbb95] table p {
    margin-bottom: 0
}

.markdown-viewer[data-v-ccdbbb95] li {
    line-height: 28px;
    list-style: inherit;
    margin-left: 20px
}

.markdown-viewer[data-v-ccdbbb95] ul {
    list-style-type: disc;
    margin-bottom: 20px
}

.markdown-viewer[data-v-ccdbbb95] ol {
    list-style-type: decimal;
    margin-bottom: 20px
}

.markdown-viewer[data-v-ccdbbb95] h1 {
    margin: 30px 0 10px
}

.markdown-viewer[data-v-ccdbbb95] h2 {
    font-size: 16px;
    margin: 24px 0 10px
}

.markdown-viewer[data-v-ccdbbb95] h3 {
    font-size: 14px;
    margin: 20px 0 8px
}

.markdown-viewer[data-v-ccdbbb95] h4 {
    font-size: 12px;
    margin: 16px 0 5px
}

.markdown-viewer[data-v-ccdbbb95] hr {
    background-color: #eaeaea;
    border: none;
    border-top: 1px solid #eaeaea;
    color: #eaeaea
}

.markdown-viewer[data-v-ccdbbb95] table {
    border: 1px solid #000;
    border-collapse: collapse;
    margin: 30px 0 0;
    min-width: 100px;
    padding: 8px;
    text-align: left
}

.markdown-viewer[data-v-ccdbbb95] th {
    background-color: #f8f8f8;
    border: 1px solid #ccc;
    font-size: 18px;
    min-width: 100px;
    padding: 8px 16px;
    text-align: left
}

.markdown-viewer[data-v-ccdbbb95] td {
    border: 1px solid #ccc;
    min-width: 100px;
    padding: 8px 16px;
    text-align: left
}

.markdown-viewer[data-v-ccdbbb95] blockquote p img {
    border: 8px solid #eaeaea;
    border-radius: 8px;
    box-sizing: border-box
}

@media (max-width:1220px) {
    .bubble.assistant .bubble-inner[data-v-ccdbbb95],
    .bubble.user .bubble-inner[data-v-ccdbbb95] {
        max-width: calc(100% - 28px)
    }
    .bubble.assistant .bubble-inner[data-v-ccdbbb95] {
        max-width: 100%
    }
    .bubble.assistant.full_width .bubble-inner[data-v-ccdbbb95] {
        width: 100%
    }
    .bubble.user.article-content .bubble-inner[data-v-ccdbbb95] {
        padding: 18px
    }
}

@media (prefers-color-scheme:dark) {
    [data-v-ccdbbb95] .cell_value_ele .ref_urls a {
        background-color: #eee;
        color: #000
    }
    .full_screen_chart[data-v-ccdbbb95],
    .hide-intermediate-steps-switch.bubble.assistant .bubble-inner.card-place-holder1[data-v-ccdbbb95],
    .hide-intermediate-steps-switch.bubble.assistant .bubble-inner.card-place-holder2[data-v-ccdbbb95] {
        background: #333
    }
    .hide-intermediate-steps-switch.bubble.assistant .bubble-inner[data-v-ccdbbb95] {
        border: 1px solid #000
    }
    .bubble .spark_result_card[data-v-ccdbbb95] {
        background: #232425
    }
    .bubble .spark_result_card .title[data-v-ccdbbb95] {
        color: #0f7fff
    }
    .bubble .spark_result_card .content[data-v-ccdbbb95] {
        color: #ccc
    }
    .resource_header .resource_title .text a[data-v-ccdbbb95] {
        color: #fff
    }
    .bubble-inner .title .icon[data-v-ccdbbb95] {
        background: #444
    }
    .resource_content .divider[data-v-ccdbbb95] {
        background: #333
    }
    .expand .icon[data-v-ccdbbb95] {
        background: #909499;
        color: #000
    }
    .bubble.user.article-content .bubble-inner[data-v-ccdbbb95] {
        background: #ccc;
        color: #232425
    }
    .bubble.assistant .bubble-inner[data-v-ccdbbb95] {
        background: #000;
        border: 1px solid #444;
        color: #fff
    }
    .bubble.assistant .bubble-inner.notice[data-v-ccdbbb95],
    .bubble.assistant .bubble-inner.notice .block[data-v-ccdbbb95] {
        background: #1a1a1a
    }
    .statements-header[data-v-ccdbbb95] {
        color: #ccc
    }
    .bubble .title[data-v-ccdbbb95],
    .statement-content[data-v-ccdbbb95] {
        color: #eee
    }
    .block[data-v-ccdbbb95] {
        background: #444
    }
    .block .block[data-v-ccdbbb95] {
        background: #222
    }
    .search_queries .search_query[data-v-ccdbbb95] {
        background: #222;
        color: #eee
    }
    .expand-conclusion[data-v-ccdbbb95],
    .expand-evidences[data-v-ccdbbb95],
    .expand-resources[data-v-ccdbbb95],
    .search_queries .expand-search-queries[data-v-ccdbbb95] {
        color: #ccc
    }
    .resource[data-v-ccdbbb95] {
        background: #222
    }
    .resource[data-v-ccdbbb95] .block_quote_wrapper {
        background-color: #333
    }
    .bubble .title a[data-v-ccdbbb95] {
        color: #0f7fff
    }
    .markdown-viewer[data-v-ccdbbb95] blockquote {
        color: #aaa
    }
    .markdown-viewer[data-v-ccdbbb95] a {
        color: #eee
    }
    .bubble.assistant .bubble-inner[data-v-ccdbbb95] {
        background: #444
    }
    .quote-left[data-v-ccdbbb95],
    .quote-right[data-v-ccdbbb95] {
        color: #333
    }
    @media (max-width:1220px) {
        .markdown-viewer[data-v-ccdbbb95] th {
            background: #222
        }
    }
}

.bottom_buttons_container[data-v-ccdbbb95] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 40px;
    justify-content: center;
    margin-top: 40px
}

@media (max-width:768px) {
    .bottom_buttons_container[data-v-ccdbbb95] {
        flex-direction: column;
        gap: 10px
    }
}

[data-v-ccdbbb95] .blurred-text {
    color: #000c;
    display: inline-block;
    filter: blur(6px);
    position: relative;
    transform: scale(1.1);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    z-index: 1
}

@media (prefers-color-scheme:dark) {
    [data-v-ccdbbb95] .blurred-text {
        color: #fffc
    }
}

.loading-anim[data-v-c42d874d] {
    margin: 12px 0
}

[data-v-c42d874d] .verbatim-highlight {
    background: #ff0;
    color: #000
}

.markdown-viewer.markdown-viewer-loading[data-v-c42d874d] {
    align-items: flex-end;
    display: flex;
    flex-direction: row;
    max-height: 60px;
    overflow: hidden
}

.bubble.user+.hide-intermediate-steps-switch.bubble.assistant[data-v-c42d874d] {
    margin-top: 44px
}

.hide-intermediate-steps-switch.bubble.assistant[data-v-c42d874d] {
    position: relative
}

.hide-intermediate-steps-switch.bubble.assistant .bubble-inner.card-place-holder1[data-v-c42d874d],
.hide-intermediate-steps-switch.bubble.assistant .bubble-inner.card-place-holder2[data-v-c42d874d] {
    border-radius: 16px;
    height: 40px;
    position: absolute;
    top: -10px;
    transform: scale(.97);
    z-index: 2
}

.hide-intermediate-steps-switch.bubble.assistant .bubble-inner.card-place-holder2[data-v-c42d874d] {
    top: -20px;
    transform: scale(.94);
    z-index: 1
}

.hide-intermediate-steps-switch.bubble.assistant .bubble-inner[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: center;
    position: relative;
    z-index: 3
}

.hide-intermediate-steps-switch .see-more[data-v-c42d874d] {
    align-items: center;
    background: #efefef;
    border-radius: 39px;
    color: #666;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    flex-grow: 0;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    gap: 4px;
    justify-content: center;
    line-height: normal;
    padding: 4px 16px;
    width: -moz-fit-content;
    width: fit-content
}

.partner-data-container[data-v-c42d874d] {
    align-items: flex-start;
    display: flex;
    margin-top: 8px
}

.bubble .spark_result_card[data-v-c42d874d] {
    background: #f5f5f5;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
    padding: 16px
}

.bubble .spark_result_card .logo[data-v-c42d874d] {
    display: flex;
    height: 22px;
    width: 71px
}

.bubble .spark_result_card .logo img[data-v-c42d874d] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.bubble .spark_result_card .title[data-v-c42d874d] {
    color: #094c99;
    cursor: pointer;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 27px
}

.bubble .spark_result_card .content[data-v-c42d874d] {
    color: #232425;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px
}

.bubble .spark_result_card .see-more[data-v-c42d874d] {
    align-items: center;
    background: #0f7fff;
    border-radius: 39px;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    gap: 4px;
    justify-content: center;
    padding: 4px 16px
}

.bubble .spark_result_card .see-more .icon[data-v-c42d874d] {
    color: #fff;
    height: 16px;
    width: 16px
}

.gallery[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    height: 184px;
    justify-content: space-between;
    overflow: hidden
}

.gallery .image[data-v-c42d874d] {
    cursor: pointer;
    display: flex;
    height: 100%;
    position: relative;
    width: 100%
}

.gallery .image img[data-v-c42d874d] {
    border-radius: 8px;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.gallery .image .youtube_icon[data-v-c42d874d] {
    height: 32px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 45px
}

.gallery .gallery__item[data-v-c42d874d] {
    height: 100%
}

.gallery_2 .gallery__item[data-v-c42d874d] {
    width: 49%
}

.gallery_3 .gallery__item[data-v-c42d874d] {
    width: 24%
}

.gallery_3 .gallery__item[data-v-c42d874d]:first-child {
    width: 48%
}

.gallery_4 .gallery__item[data-v-c42d874d] {
    width: 24%
}

.stop_icon[data-v-c42d874d] {
    align-items: center;
    color: #909499;
    display: flex;
    height: 12px;
    justify-content: center;
    width: 12px
}

.verify_results[data-v-c42d874d] {
    margin-top: -8px
}

.verify_results.not_expand[data-v-c42d874d] {
    margin-top: 0
}

[data-v-c42d874d] svg {
    height: 100%;
    width: 100%
}

.resource_content .text-content[data-v-c42d874d] {
    padding-top: 28px;
    position: relative
}

.quote-left[data-v-c42d874d],
.quote-right[data-v-c42d874d] {
    align-items: center;
    color: #eaeaea;
    display: flex;
    height: 60px;
    justify-content: center;
    position: absolute;
    width: 60px;
    z-index: 1
}

.quote-left[data-v-c42d874d] {
    left: -2px;
    top: 12px
}

.quote-right[data-v-c42d874d] {
    bottom: 12px;
    right: 18px;
    transform: rotate(180deg)
}

.expand .icon[data-v-c42d874d] {
    border-radius: 50%
}

.main-header-wrapper[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
    max-width: var(--container-width, 760px);
    width: 100%
}

.bubble-inner .block[data-v-c42d874d] {
    margin-top: 16px;
    position: relative
}

.bubble-inner .block[data-v-c42d874d]:first-child {
    margin-top: 0
}

.main-inner-inner-wrapper[data-v-c42d874d] {
    align-items: center;
    background: #fff
}

.main-inner-inner[data-v-c42d874d],
.main-inner-inner-wrapper[data-v-c42d874d] {
    display: flex;
    flex-direction: column;
    width: 100%
}

.main-inner-inner[data-v-c42d874d] {
    box-sizing: border-box
}

.main-header[data-v-c42d874d],
.main-inner-inner[data-v-c42d874d] {
    max-width: var(--container-width, 760px)
}

.main-header[data-v-c42d874d] {
    background: #fff;
    border-radius: 16px;
    filter: drop-shadow(0 2px 10px rgba(0, 0, 0, .08));
    justify-content: space-between;
    width: 100%
}

.main-header[data-v-c42d874d],
.main-header-left[data-v-c42d874d] {
    display: flex;
    flex-direction: row
}

.main-header-left[data-v-c42d874d] {
    align-items: center;
    box-sizing: border-box;
    gap: 10px;
    padding: 20px 16px
}

.main-header-right[data-v-c42d874d] {
    gap: 10px;
    justify-content: center
}

.main-header .title[data-v-c42d874d],
.main-header-right[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-direction: row
}

.main-header .title[data-v-c42d874d] {
    color: #232425;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    gap: 8px;
    line-height: 27px
}

.main-header .content[data-v-c42d874d] {
    align-items: center;
    color: #606366;
    display: flex;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    justify-content: center;
    line-height: 20px
}

.main-header .divider[data-v-c42d874d] {
    background: #606366;
    height: 16px;
    width: 1px
}

.main-header .icon[data-v-c42d874d] {
    display: flex;
    height: 30px;
    width: 30px
}

.bubble .evidence .title .source[data-v-c42d874d] {
    font-style: normal;
    font-weight: 400;
    padding-left: 5px
}

.bubble .evidence .title[data-v-c42d874d] {
    display: block
}

.seq_icon[data-v-c42d874d] {
    align-items: center;
    background: #efefef;
    border-radius: 50%;
    display: flex;
    font-weight: 400;
    height: 16px;
    justify-content: center;
    width: 16px
}

.statements[data-v-c42d874d] {
    gap: 10px
}

.statement-details[data-v-c42d874d],
.statements[data-v-c42d874d] {
    display: flex;
    flex-direction: column;
    margin-top: 20px
}

.statement-details[data-v-c42d874d] {
    gap: 30px
}

.statement[data-v-c42d874d] {
    border-bottom: 1px solid #efefef;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-bottom: 24px;
    padding-top: 24px
}

.statement.first[data-v-c42d874d] {
    padding-top: 0
}

.statement.last[data-v-c42d874d] {
    border-bottom: none;
    padding-bottom: 0
}

.statement .header[data-v-c42d874d] {
    cursor: pointer;
    display: flex;
    flex-direction: row;
    justify-content: space-between
}

.statement .content[data-v-c42d874d] {
    color: #000;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px
}

.statement .check-button[data-v-c42d874d] {
    align-items: center;
    color: #0f7fff;
    display: flex
}

.evidence_str[data-v-c42d874d] {
    display: flex;
    flex-direction: column;
    gap: 12px;
    line-height: 1.8;
    padding-top: 12px;
    position: relative;
    z-index: 2
}

.explanation[data-v-c42d874d] {
    margin-top: 18px
}

.detailed_analysis[data-v-c42d874d] {
    margin-top: 20px
}

.statement-content[data-v-c42d874d] {
    color: #000;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 1.5
}

.block[data-v-c42d874d] {
    background: #fff;
    border-radius: 8px
}

.block .block[data-v-c42d874d] {
    background: #fafafa
}

.expand[data-v-c42d874d],
.expand-conclusion[data-v-c42d874d],
.expand-evidences[data-v-c42d874d],
.expand-generate-markdown[data-v-c42d874d],
.expand-resources[data-v-c42d874d] {
    align-items: center;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    gap: 12px;
    justify-content: space-between
}

.expand-resources[data-v-c42d874d] {
    align-items: flex-start;
    display: flex;
    flex-direction: column
}

.expand-resources .title .title-content[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.bubble-inner .expand-resources .title>.icon[data-v-c42d874d] {
    align-items: center;
    color: #909499;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.expand-resources .title[data-v-c42d874d] {
    justify-content: space-between
}

.expand-resources .title[data-v-c42d874d],
.verify_results .sub-title[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    width: 100%
}

.verify_results .sub-title[data-v-c42d874d] {
    overflow: hidden
}

.verify_results .sub-title .label[data-v-c42d874d] {
    flex-shrink: 0
}

.verify_results .sub-title .favicon[data-v-c42d874d] {
    border-radius: 4px;
    flex-shrink: 0;
    height: 20px;
    overflow: hidden;
    width: 20px
}

.verify_results .sub-title .favicon img[data-v-c42d874d] {
    height: 100%;
    width: 100%
}

.resource .sub-title[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    overflow: hidden;
    width: 100%
}

.resource .sub-title .label[data-v-c42d874d] {
    flex-shrink: 0
}

.resource .sub-title .favicon[data-v-c42d874d] {
    border-radius: 4px;
    flex-shrink: 0;
    height: 20px;
    overflow: hidden;
    width: 20px
}

.resource .sub-title .favicon img[data-v-c42d874d] {
    height: 100%;
    width: 100%
}

.resource_title .icon[data-v-c42d874d] {
    border-radius: 4px;
    overflow: hidden
}

.resource_title img[data-v-c42d874d] {
    height: 100%;
    width: 100%
}

.expand-evidences .text[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.expand-conclusion .text[data-v-c42d874d] {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis
}

.conclusion[data-v-c42d874d] {
    line-height: 1.8
}

.bubble[data-v-c42d874d],
.conclusion[data-v-c42d874d],
.conclusion .block[data-v-c42d874d] {
    margin-top: 16px
}

.bubble[data-v-c42d874d] {
    display: flex;
    flex-direction: row;
    width: 100%
}

.bubble-inner[data-v-c42d874d] {
    box-sizing: border-box;
    max-width: 100%;
    padding: 16px
}

.bubble.user+.bubble.assistant[data-v-c42d874d] {
    margin-top: 24px
}

.markdown-viewer[data-v-c42d874d] :last-child {
    margin-bottom: 0
}

.bubble.user[data-v-c42d874d] {
    justify-content: flex-end
}

.bubble.user .bubble-inner[data-v-c42d874d] {
    background: #fff;
    border-radius: 16px
}

.bubble.user.article-content .bubble-inner[data-v-c42d874d] {
    background: linear-gradient(0deg, #232425, #232425), #fff;
    color: #fff;
    padding: 16px
}

.bubble.assistant .bubble-inner[data-v-c42d874d] {
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: 100%
}

.bubble.assistant .bubble-inner.notice[data-v-c42d874d],
.bubble.assistant .bubble-inner.notice .block[data-v-c42d874d] {
    background: #fafafa
}

.bubble.assistant.full_width .bubble-inner[data-v-c42d874d] {
    width: 100%
}

.bubble .statement .header .title[data-v-c42d874d] {
    font-size: 16px;
    margin: 0
}

.bubble .title[data-v-c42d874d] {
    align-items: center;
    color: #232425;
    display: flex;
    flex-direction: row;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    gap: 8px;
    line-height: 20px
}

.bubble .title a[data-v-c42d874d] {
    color: #0c66cc
}

.resource_content .link[data-v-c42d874d] {
    margin-bottom: 12px;
    margin-top: 10px
}

.resource_content .link a[data-v-c42d874d] {
    color: #0f7fff;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    text-decoration: none;
    word-break: break-all
}

.resource_content .divider[data-v-c42d874d] {
    background: #eaeaea;
    height: 1px;
    margin-bottom: 12px;
    margin-top: 12px
}

.statements-header[data-v-c42d874d] {
    align-items: center;
    color: #475666;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 20px
}

.resource[data-v-c42d874d] {
    background: #f8f8f8;
    border-radius: 8px;
    box-sizing: border-box;
    margin-top: 12px;
    padding: 12px 16px;
    transition: background-color 3s ease
}

.resource[data-v-c42d874d] .block_quote_wrapper {
    background-color: #fff
}

.resource .markdown-viewer[data-v-c42d874d] {
    margin-top: 16px
}

.resource_header[data-v-c42d874d] {
    cursor: pointer;
    justify-content: space-between
}

.resource_header[data-v-c42d874d],
.resource_header .resource_title[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-direction: row
}

.resource_header .resource_title[data-v-c42d874d] {
    gap: 8px
}

.resource_header .resource_title[data-v-c42d874d],
.resource_header .resource_title .text[data-v-c42d874d] {
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis
}

.resource_header .resource_title .text[data-v-c42d874d] {
    display: -webkit-box
}

.resource_header .resource_title .text a[data-v-c42d874d] {
    color: #606366;
    text-decoration: none
}

.resource_header.expand .resource_title .text a[data-v-c42d874d] {
    color: #0f7fff;
    text-decoration: none
}

.resource_header.expand .resource_title .text a[data-v-c42d874d]:hover {
    text-decoration: underline
}

.search_queries[data-v-c42d874d] {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    position: relative
}

.search_queries .expand-search-queries[data-v-c42d874d] {
    align-items: center;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 100%
}

.search_queries .search_query[data-v-c42d874d] {
    background: #f8f8f8;
    border-radius: 8px;
    color: #232425;
    display: flex;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    padding: 8px 12px
}

.icon[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.statements .input[type=checkbox][data-v-c42d874d] {
    height: 16px;
    min-width: 16px;
    width: 16px
}

.statements .input[data-v-c42d874d] {
    align-items: center;
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 12px;
    display: flex;
    flex-direction: row;
    margin-top: 24px;
    min-width: 300px;
    position: relative;
    width: 100%
}

.statements .input.disabled[data-v-c42d874d] {
    opacity: .3
}

.statements input[data-v-c42d874d] {
    background: transparent;
    border: none;
    outline: none;
    padding: 10px 0 10px 20px;
    width: 100%
}

.statements .add_button[data-v-c42d874d] {
    cursor: pointer;
    padding-right: 10px
}

.statements .begin_cross_check[data-v-c42d874d] {
    align-items: center;
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 12px;
    color: #666;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    gap: 10px;
    justify-content: center;
    line-height: normal;
    margin-top: 24px;
    padding: 10px
}

.statements .begin_cross_check.disabled[data-v-c42d874d] {
    opacity: .3
}

.statements .begin_cross_check .icon[data-v-c42d874d] {
    height: 16px;
    width: 16px
}

.bubble-inner .icon[data-v-c42d874d] {
    color: #909499
}

.bubble-inner .title .icon[data-v-c42d874d] {
    align-items: center;
    color: #5cd4a1;
    display: flex;
    height: 16px;
    justify-content: center;
    width: 26px
}

.bubble-inner .title .icon[data-v-c42d874d] .loading-animation {
    margin-top: 0
}

.permission[data-v-c42d874d] {
    cursor: pointer;
    display: flex;
    flex: 0;
    flex-direction: row;
    margin-right: 16px;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.permission .dropDownList[data-v-c42d874d] {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 4px 10px #00000026;
    padding: 12px 11px;
    position: absolute;
    right: 0;
    top: 25px;
    width: 156px;
    z-index: 1
}

.permission .dropDownList li[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.permission .dropDownList .icon[data-v-c42d874d] {
    display: flex;
    height: 14px;
    width: 14px
}

.permission .dropDownList li[data-v-c42d874d]:not(:first-child) {
    margin-top: 18px
}

.permission .dropDownList .desc[data-v-c42d874d] {
    margin-top: 2px
}

.permission .dropDownList .selected[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-grow: 1;
    justify-content: center
}

.permission .dropDownList .wrapper[data-v-c42d874d] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: 130px
}

.permission .dropDownList .name[data-v-c42d874d] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.permission .dropDownList .desc[data-v-c42d874d] {
    color: #909499;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 13px
}

.permission .dropDownList .text[data-v-c42d874d] {
    color: #232425;
    flex: none;
    flex-grow: 0;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 15px;
    margin-left: 5px;
    order: 1
}

.permission .dropdown[data-v-c42d874d] {
    align-items: center;
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 6px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    font-size: 12px;
    height: 30px;
    padding-left: 10px;
    width: 88px
}

.permission .dropdown .text[data-v-c42d874d] {
    flex-grow: 1;
    margin-left: 8px;
    width: 50px
}

.permission .dropdown .icon[data-v-c42d874d] {
    display: flex;
    height: 14px;
    width: 14px
}

.permission .dropdown .triangle[data-v-c42d874d] {
    align-items: center;
    display: flex;
    height: 6px;
    justify-content: flex-start;
    margin-left: 8px;
    margin-right: 8px;
    width: 20px
}

.permission .dropdown .reverse[data-v-c42d874d] svg {
    transform: rotate(180deg)
}

@media (max-width:1220px) {
    .main-header-wrapper[data-v-c42d874d] {
        margin-top: 20px;
        max-width: 100%
    }
    .bubble.assistant .bubble-inner[data-v-c42d874d],
    .bubble.user .bubble-inner[data-v-c42d874d] {
        max-width: calc(100% - 28px)
    }
    .bubble.assistant .bubble-inner[data-v-c42d874d] {
        max-width: 100%
    }
    .bubble.assistant.full_width .bubble-inner[data-v-c42d874d] {
        width: 100%
    }
    .main-header[data-v-c42d874d] {
        box-sizing: border-box;
        min-width: unset;
        position: relative;
        width: calc(100% - 24px)
    }
    .main-header-left[data-v-c42d874d] {
        align-items: flex-start;
        display: flex;
        flex-direction: column
    }
    .main-header-left .divider[data-v-c42d874d] {
        display: none
    }
    .main-header-right[data-v-c42d874d] {
        align-items: flex-start;
        position: absolute;
        right: 10px
    }
    .main-header-right .permission[data-v-c42d874d] {
        margin-top: 20px
    }
    .main-inner-inner[data-v-c42d874d] {
        background: #fff;
        padding: 12px
    }
    .bubble.user.article-content .bubble-inner[data-v-c42d874d] {
        padding: 18px
    }
}

@media (prefers-color-scheme:dark) {
    [data-v-c42d874d] .cell_value_ele .ref_urls a {
        background-color: #eee;
        color: #000
    }
    .full_screen_chart[data-v-c42d874d],
    .hide-intermediate-steps-switch.bubble.assistant .bubble-inner.card-place-holder1[data-v-c42d874d],
    .hide-intermediate-steps-switch.bubble.assistant .bubble-inner.card-place-holder2[data-v-c42d874d] {
        background: #333
    }
    .hide-intermediate-steps-switch.bubble.assistant .bubble-inner[data-v-c42d874d] {
        border: 1px solid #000
    }
    .bubble .spark_result_card[data-v-c42d874d] {
        background: #232425
    }
    .bubble .spark_result_card .title[data-v-c42d874d] {
        color: #0f7fff
    }
    .bubble .spark_result_card .content[data-v-c42d874d] {
        color: #ccc
    }
    .resource_header .resource_title .text a[data-v-c42d874d] {
        color: #fff
    }
    .bubble-inner .title .icon[data-v-c42d874d],
    .resource_content .divider[data-v-c42d874d] {
        background: #333
    }
    .expand .icon[data-v-c42d874d] {
        background: #909499;
        color: #000
    }
    .main-header[data-v-c42d874d] {
        background: #000;
        color: #fff
    }
    .main-header .content[data-v-c42d874d],
    .main-header .title[data-v-c42d874d] {
        color: #fff
    }
    .bubble.user.article-content .bubble-inner[data-v-c42d874d] {
        background: #ccc;
        color: #232425
    }
    .bubble.assistant .bubble-inner[data-v-c42d874d] {
        background: #000;
        border: 1px solid #333;
        color: #fff
    }
    .bubble.assistant .bubble-inner.notice[data-v-c42d874d],
    .bubble.assistant .bubble-inner.notice .block[data-v-c42d874d] {
        background: #1a1a1a
    }
    .statements-header[data-v-c42d874d] {
        color: #ccc
    }
    .bubble .title[data-v-c42d874d],
    .statement-content[data-v-c42d874d] {
        color: #eee
    }
    .block[data-v-c42d874d] {
        background: #333
    }
    .block .block[data-v-c42d874d] {
        background: #222
    }
    .search_queries .search_query[data-v-c42d874d] {
        background: #222;
        color: #eee
    }
    .expand-conclusion[data-v-c42d874d],
    .expand-evidences[data-v-c42d874d],
    .expand-resources[data-v-c42d874d],
    .search_queries .expand-search-queries[data-v-c42d874d] {
        color: #ccc
    }
    .resource[data-v-c42d874d] {
        background: #222
    }
    .resource[data-v-c42d874d] .block_quote_wrapper {
        background-color: #333
    }
    .bubble .title a[data-v-c42d874d] {
        color: #0f7fff
    }
    .markdown-viewer[data-v-c42d874d] blockquote {
        color: #aaa
    }
    .markdown-viewer[data-v-c42d874d] a {
        color: #eee
    }
    .permission .dropdown[data-v-c42d874d] {
        background: #333;
        border: 1px solid #333
    }
    .main-inner-inner-wrapper[data-v-c42d874d] {
        background: transparent
    }
    .bubble.assistant .bubble-inner[data-v-c42d874d] {
        background: #333
    }
    .quote-left[data-v-c42d874d],
    .quote-right[data-v-c42d874d] {
        color: #333
    }
    @media (max-width:1220px) {
        .main-header[data-v-c42d874d] {
            background: #222
        }
        .main-inner-inner[data-v-c42d874d] {
            background: #000
        }
        .markdown-viewer[data-v-c42d874d] th {
            background: #222
        }
    }
}

.user .content[data-v-1ea34e58] pre {
    background: none;
    margin: 0;
    padding: 0
}

.user .content[data-v-1ea34e58] pre code {
    font-family: sans-serif;
    white-space: pre-wrap
}

.conversation-content[data-v-1ea34e58] {
    background: transparent;
    color: #666;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding-bottom: 180px;
    position: relative
}

.conversation .role[data-v-1ea34e58] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.conversation .avatar[data-v-1ea34e58] {
    height: 36px;
    width: 36px
}

.conversation .avatar img[data-v-1ea34e58] {
    width: 100%
}

.conversation .name[data-v-1ea34e58] {
    color: #252525;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal
}

.conversation-item-desc .bubble[data-v-1ea34e58] {
    align-items: flex-start;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    justify-content: flex-start;
    line-height: 20px;
    padding: 12px 16px
}

.conversation-item-desc .bubble code[data-v-1ea34e58],
.conversation-item-desc .bubble pre[data-v-1ea34e58] {
    font-size: 16px
}

.conversation-item-desc .button[data-v-1ea34e58] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 2px;
    line-height: normal;
    padding: 4px 8px
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc .button[data-v-1ea34e58] {
        border-color: #252525;
        color: #ddd
    }
}

.conversation-item-desc .button .icon[data-v-1ea34e58] {
    display: flex
}

@keyframes blink-animation-1ea34e58 {
    0%,
    to {
        color: #000
    }
    50% {
        color: transparent
    }
}

@media (prefers-color-scheme:dark) {
    @keyframes blink-animation-1ea34e58 {
        0%,
        to {
            color: #fff
        }
        50% {
            color: transparent
        }
    }
}

.conversation-item-desc.assistant .bubble[data-v-1ea34e58] .cursor {
    animation: blink-animation-1ea34e58 .5s infinite;
    color: #606366;
    font-size: 12px
}

.conversation-item-desc[data-v-1ea34e58] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-width: 260px;
    width: 100%
}

.conversation-item-desc.user[data-v-1ea34e58] {
    align-items: flex-end;
    margin-left: 30px
}

.conversation-item-desc.assistant .bubble[data-v-1ea34e58] {
    border: 1px solid transparent;
    border-radius: 16px;
    box-sizing: border-box;
    min-width: 260px;
    padding-left: 0;
    padding-right: 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.retry[data-v-1ea34e58],
.conversation-item-desc.assistant .bubble.try_moa[data-v-1ea34e58] {
    align-items: center;
    background: #fafafa;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 18px 16px
}

.conversation-item-desc .retry .button[data-v-1ea34e58],
.conversation-item-desc .try_moa .button[data-v-1ea34e58] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 8px 16px
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.retry[data-v-1ea34e58],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-1ea34e58] {
        flex-direction: column
    }
}

.models-list[data-v-1ea34e58] {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px 8px
}

.models-list[data-v-1ea34e58],
.models-list .model[data-v-1ea34e58] {
    display: flex;
    flex-direction: column
}

.models-list .model[data-v-1ea34e58] {
    color: #232425;
    cursor: pointer;
    margin-top: 4px;
    padding: 10px 6px 10px 10px
}

.models-list .model .row[data-v-1ea34e58] {
    border-radius: 8px;
    color: #232425;
    gap: 4px;
    justify-content: space-between
}

.models-list .model .row[data-v-1ea34e58],
.models-selected[data-v-1ea34e58] {
    display: flex;
    flex-direction: row;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.models-selected[data-v-1ea34e58] {
    align-items: center;
    background-color: #f4f4f4;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 150%;
    padding: 6px 12px
}

@media (prefers-color-scheme:dark) {
    .models-list[data-v-1ea34e58] {
        background-color: #252525
    }
    .models-list .model .row[data-v-1ea34e58] {
        color: #ddd
    }
    .conversation-item-desc.assistant .bubble[data-v-1ea34e58] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble.retry[data-v-1ea34e58],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-1ea34e58] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .retry .button[data-v-1ea34e58],
    .conversation-item-desc .try_moa .button[data-v-1ea34e58] {
        background: #fafafa;
        color: #232425
    }
}

.controls[data-v-1ea34e58]::-webkit-scrollbar {
    display: none
}

.to-bottom-icon[data-v-1ea34e58] {
    cursor: pointer;
    left: 50%;
    position: absolute;
    top: -35px;
    transform: translate(-50%)
}

.empty-placeholder[data-v-1ea34e58] {
    color: #232425;
    font-family: Arial;
    font-size: 30px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin-top: 30vh;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .empty-placeholder[data-v-1ea34e58] {
        color: #fff
    }
}

.model-response .title[data-v-1ea34e58] {
    cursor: pointer
}

.model-response .title .status[data-v-1ea34e58] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    width: 28px
}

.model-response .title .status .icon.finished[data-v-1ea34e58] {
    color: #5cd4a1
}

.model-response .title .status .icon[data-v-1ea34e58] svg {
    height: 100%;
    width: 100%
}

.moa-title[data-v-1ea34e58] {
    color: #ccc;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

.assistant-message-divider[data-v-1ea34e58] {
    background-color: #efefef;
    height: 1px;
    margin: 50px 0;
    width: 100%
}

.conversation-item-desc .try_moa .button[data-v-1ea34e58] {
    align-items: center;
    background: #232425;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 8px 16px
}

.arrow-right-icon[data-v-1ea34e58] {
    cursor: pointer;
    flex-shrink: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.source-language-wrapper[data-v-1ea34e58] {
    flex-shrink: 0
}

.target-language-menu[data-v-1ea34e58] {
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 300px;
    overflow-y: auto;
    padding: 8px 4px
}

.icon[data-v-1ea34e58] {
    height: 16px;
    width: 16px
}

.icon[data-v-1ea34e58],
.source-language[data-v-1ea34e58] {
    align-items: center;
    display: flex;
    justify-content: center
}

.source-language[data-v-1ea34e58],
.target-language[data-v-1ea34e58] {
    border: 1px solid #efefef;
    border-radius: 8px;
    box-sizing: border-box;
    color: #606366;
    font-size: 14px;
    padding: 5px 12px
}

.target-language[data-v-1ea34e58] {
    align-items: center;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    gap: 10px;
    line-height: 20px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.target-language[data-v-1ea34e58]:hover {
    background-color: #f0f0f0
}

.target-language.active .icon[data-v-1ea34e58] {
    transform: rotate(180deg)
}

.target-language-menu-item[data-v-1ea34e58] {
    border-radius: 8px;
    cursor: pointer;
    padding: 4px 12px
}

@media (hover:hover) {
    .target-language-menu-item[data-v-1ea34e58]:hover {
        background-color: #f5f5f5
    }
}

.model-response-wrapper .title[data-v-1ea34e58] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 4px
}

.model-response-wrapper .title .icon[data-v-1ea34e58] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    margin-right: 5px;
    width: 16px
}

.model-response-wrapper .status .icon[data-v-1ea34e58] {
    margin-right: 0
}

.model-response-wrapper .title .icon img[data-v-1ea34e58] {
    height: 100%;
    width: 100%
}

.controls[data-v-1ea34e58] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden
}

.upload-attachments[data-v-1ea34e58] {
    flex-shrink: 0
}

.upload-attachments .icon[data-v-1ea34e58] {
    align-items: center;
    color: #232425;
    cursor: pointer;
    display: flex;
    height: 20px;
    justify-content: center;
    width: 20px
}

.models-selected .icon.active[data-v-1ea34e58] {
    transform: rotate(180deg)
}

.models-list .model[data-v-1ea34e58]:first-child {
    margin-top: 0
}

.models-list .divider[data-v-1ea34e58] {
    background-color: #efefef;
    height: 1px;
    margin-top: 10px;
    width: 100%
}

.models-list .divider+.model[data-v-1ea34e58] {
    margin-top: 10px
}

@media (prefers-color-scheme:dark) {
    .models-list .divider[data-v-1ea34e58] {
        background-color: #333
    }
}

.models-list .model .description[data-v-1ea34e58] {
    color: #909499;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px
}

.models-list .model.disabled[data-v-1ea34e58] {
    opacity: .5;
    pointer-events: none
}

@media (hover:hover) {
    .models-list .model[data-v-1ea34e58]:hover {
        background-color: #f5f5f5;
        border-radius: 12px
    }
}

.models-list .model .left[data-v-1ea34e58] {
    display: flex;
    flex-direction: row;
    gap: 8px
}

.models-list .model .left .icon[data-v-1ea34e58] {
    height: 20px;
    width: 20px
}

.models-list .model .left .icon img[data-v-1ea34e58] {
    height: 100%;
    width: 100%
}

.models-list .model .right[data-v-1ea34e58] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-end
}

.models-list .model .right input[type=radio][data-v-1ea34e58] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    cursor: pointer;
    height: 16px;
    margin: 0;
    position: relative;
    width: 16px
}

.models-list .model .right input[type=radio][data-v-1ea34e58]:checked {
    background-color: #fff;
    border-color: #0f7fff
}

.models-list .model .right input[type=radio][data-v-1ea34e58]:checked:after {
    background: #0f7fff;
    border-radius: 50%;
    content: "";
    height: 10px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 10px
}

@media (prefers-color-scheme:dark) {
    .models-list .model .right input[type=radio][data-v-1ea34e58] {
        background-color: #fff;
        border-color: #666
    }
    .models-list .model .right input[type=radio][data-v-1ea34e58]:checked {
        background-color: #fff;
        border-color: #0f7fff
    }
}

.models-wrapper[data-v-1ea34e58] {
    flex-shrink: 0;
    width: -moz-fit-content;
    width: fit-content
}

.models-selected[data-v-1ea34e58]:hover {
    background-color: #f0f0f0
}

.models-selected>.icon[data-v-1ea34e58] {
    height: 8px;
    width: 8px
}

.models-selected .model-selected[data-v-1ea34e58] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.models-selected .model-selected .icon[data-v-1ea34e58] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 16px;
    justify-content: center;
    width: 16px
}

.models-selected .model-selected .icon img[data-v-1ea34e58] {
    height: 100%;
    width: 100%
}

.expand-button[data-v-1ea34e58] {
    transform: rotate(180deg)
}

.expanded .expand-button[data-v-1ea34e58] {
    transform: rotate(0)
}

.model-response-wrapper[data-v-1ea34e58] {
    position: relative
}

.model-response-wrapper .content[data-v-1ea34e58] {
    transition: all .3s ease-in-out
}

.model-response-wrapper.thinking .content.no-expand[data-v-1ea34e58] {
    max-height: 90px
}

.model-response-wrapper .content.no-expand[data-v-1ea34e58] {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    line-height: 30px;
    max-height: 0;
    overflow: hidden
}

.model-response-wrapper.expanded .content[data-v-1ea34e58] {
    max-height: unset;
    padding-top: 20px
}

.model-response-wrapper .expand-button[data-v-1ea34e58] {
    align-items: center;
    cursor: pointer;
    display: flex;
    height: 20px;
    justify-content: center;
    position: absolute;
    right: 10px;
    top: 10px;
    width: 20px
}

.model-response .divider[data-v-1ea34e58] {
    background-color: #efefef;
    height: 1px;
    margin-bottom: 20px
}

.model-response[data-v-1ea34e58] {
    box-sizing: border-box;
    color: #475666;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    padding: 0 12px;
    width: 100%
}

.conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-1ea34e58] {
    background-color: #fafafa;
    border: 1px solid #fafafa
}

.general-chat-wrapper[data-v-1ea34e58] {
    flex-direction: column;
    height: 100%;
    justify-content: space-between
}

.chat-wrapper[data-v-1ea34e58],
.general-chat-wrapper[data-v-1ea34e58] {
    box-sizing: border-box;
    display: flex;
    width: 100%
}

.chat-wrapper[data-v-1ea34e58] {
    align-items: flex-start;
    justify-content: center;
    overflow-x: hidden;
    overflow-y: auto
}

.conversation-wrapper[data-v-1ea34e58] {
    background-color: transparent;
    display: flex;
    flex-direction: column;
    min-height: 200px;
    padding-bottom: 100px;
    width: 680px
}

.input-wrapper-wrapper[data-v-1ea34e58] {
    align-items: center;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-bottom: 4px;
    padding-top: 16px;
    position: relative;
    width: 100%
}

.input-wrapper[data-v-1ea34e58] {
    width: 680px
}

.conversation-item-desc.user .bubble[data-v-1ea34e58] {
    background-color: #f5f5f5;
    border-radius: 16px;
    color: #232425;
    margin-right: 2px;
    max-width: 70%
}

.conversation-item-desc.assistant .bubble[data-v-1ea34e58]:not(:first-child) {
    margin-top: 20px
}

.conversation-item-desc .bubble .loading[data-v-1ea34e58],
.conversation-item-desc .bubble[data-v-1ea34e58] .cursor {
    display: none
}

.conversation-item-desc.assistant .bubble.thinking[data-v-1ea34e58] .cursor {
    display: inline
}

.conversation-item-desc .desc[data-v-1ea34e58] {
    display: flex;
    flex-direction: column;
    max-width: 100%
}

.conversation-item-desc .content[data-v-1ea34e58] {
    max-width: 100%
}

.conversation-statement[data-v-1ea34e58] {
    display: flex;
    flex-direction: column;
    list-style: none;
    margin: 10px 0
}

.conversation-statement.user[data-v-1ea34e58] {
    align-items: flex-end
}

.conversation-statement.assistant+.conversation-statement.user[data-v-1ea34e58],
.conversation-statement.user+.conversation-statement.assistant[data-v-1ea34e58] {
    margin-top: 16px
}

.conversation-statement[data-v-1ea34e58]:first-child {
    margin-top: 5px
}

@media (max-width:1220px) {
    .chat-wrapper[data-v-1ea34e58] {
        box-sizing: border-box;
        padding: 16px 16px 100px
    }
    .input-wrapper-wrapper[data-v-1ea34e58] {
        bottom: 0;
        box-sizing: border-box;
        left: 0;
        padding: 16px 16px 40px;
        position: fixed;
        width: 100%
    }
    .input-wrapper[data-v-1ea34e58],
    .input-wrapper-wrapper-inner[data-v-1ea34e58] {
        width: 100%
    }
}

@media (prefers-color-scheme:dark) {
    .input-wrapper-wrapper[data-v-1ea34e58] {
        background-color: #232425
    }
    .source-language[data-v-1ea34e58],
    .target-language[data-v-1ea34e58] {
        color: #fff
    }
    .target-language[data-v-1ea34e58]:hover {
        background-color: #f0f0f010
    }
    .conversation-item-desc .try_moa .button[data-v-1ea34e58] {
        background: #fafafa;
        color: #232425
    }
    .target-language[data-v-1ea34e58] {
        display: flex
    }
    .source-language[data-v-1ea34e58],
    .target-language[data-v-1ea34e58] {
        border: 1px solid #efefef30;
        border-radius: 8px
    }
    @media (hover:hover) {
        .target-language-menu-item[data-v-1ea34e58]:hover {
            background-color: #232425
        }
    }
    .models-list .model[data-v-1ea34e58],
    .upload-attachments .icon[data-v-1ea34e58] {
        color: #ddd
    }
    @media (hover:hover) {
        .models-list .model[data-v-1ea34e58]:hover {
            background-color: #333
        }
    }
    .conversation-item-desc.user .bubble[data-v-1ea34e58] {
        background: #eee;
        color: #000
    }
    .conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-1ea34e58] {
        background-color: #444
    }
    .model-response[data-v-1ea34e58] {
        color: #ddd
    }
}

.moa-side-panel[data-v-be7e023b] {
    background: var(--panel-bg);
    bottom: 200px;
    box-shadow: 0 -2px 8px #0000001a;
    height: 80vh;
    left: 0;
    position: relative;
    right: 0;
    width: 100%;
    z-index: 1000
}

.panel-content[data-v-be7e023b] {
    height: calc(100% - 60px);
    opacity: 1;
    overflow-y: auto
}

.panel-header[data-v-be7e023b] {
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    display: flex;
    height: 48px;
    justify-content: space-between;
    padding: 0 16px
}

.panel-header h3[data-v-be7e023b] {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 500;
    margin: 0
}

.panel-header .icon[data-v-be7e023b] {
    align-items: center;
    display: flex;
    height: 24px;
    justify-content: center;
    transform: rotate(180deg);
    transition: transform .3s ease;
    width: 24px
}

.panel-header .icon.expanded[data-v-be7e023b] {
    transform: rotate(0)
}

.images-container[data-v-be7e023b] {
    display: flex;
    gap: 16px;
    padding: 16px 16px 72px
}

.image-column[data-v-be7e023b] {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 16px
}

.image-item[data-v-be7e023b] {
    width: 100%;
    -moz-column-break-inside: avoid;
    border-radius: 8px;
    break-inside: avoid;
    cursor: pointer;
    overflow: hidden;
    position: relative
}

.image-item img[data-v-be7e023b] {
    display: block;
    height: auto;
    transition: transform .2s ease;
    width: 100%
}

.image-overlay[data-v-be7e023b] {
    align-items: center;
    background: #0006;
    bottom: 0;
    display: flex;
    justify-content: center;
    left: 0;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 0;
    transition: opacity .2s ease
}

.image-item:hover .image-overlay[data-v-be7e023b] {
    opacity: 1;
    pointer-events: auto
}

.image-item:hover img[data-v-be7e023b] {
    transform: scale(1.05)
}

.image-actions[data-v-be7e023b] {
    display: flex;
    gap: 8px;
    z-index: 2
}

.action-btn[data-v-be7e023b] {
    align-items: center;
    background: #ffffffe6;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    height: 32px;
    justify-content: center;
    transition: background-color .2s ease;
    width: 32px;
    z-index: 3
}

.action-btn[data-v-be7e023b]:hover {
    background: #fff
}

.empty-state[data-v-be7e023b] {
    align-items: center;
    color: var(--text-secondary);
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: center;
    padding: 24px
}

.empty-icon[data-v-be7e023b] {
    height: 48px;
    margin-bottom: 16px;
    opacity: .5;
    width: 48px
}

.loading-overlay[data-v-be7e023b] {
    align-items: center;
    background: #fffc;
    display: flex;
    justify-content: center;
    top: 0
}

.loading-overlay[data-v-be7e023b],
.search-box[data-v-be7e023b] {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0
}

.search-box[data-v-be7e023b] {
    background: var(--panel-bg);
    border-top: 1px solid var(--border-color);
    padding: 16px;
    z-index: 1001
}

.search-box .input[data-v-be7e023b] {
    align-items: center;
    border-radius: 12px;
    box-sizing: border-box;
    display: flex;
    gap: 10px;
    overflow: hidden;
    padding: 8px 12px;
    position: relative;
    z-index: 1
}

.search-box .textarea-wrapper[data-v-be7e023b] {
    align-items: center;
    display: flex;
    gap: 10px;
    width: 100%
}

.search-box .search-input[data-v-be7e023b] {
    background: transparent;
    border: 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    outline: none;
    padding: 0;
    width: 100%
}

.search-box .search-input[data-v-be7e023b]::-moz-placeholder {
    color: var(--text-secondary);
    opacity: .65
}

.search-box .search-input[data-v-be7e023b]::placeholder {
    color: var(--text-secondary);
    opacity: .65
}

.search-box .input-icon[data-v-be7e023b] {
    align-items: center;
    background: #eee;
    border-radius: 35%;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    height: 32px;
    justify-content: center;
    padding: 6px;
    width: 32px
}

@media (prefers-color-scheme:dark) {
    .search-box .input-icon[data-v-be7e023b] {
        background: transparent;
        color: #909499
    }
    .search-box .input.moa.active .input-icon[data-v-be7e023b] {
        color: #fff
    }
}

[data-v-be7e023b]:root {
    --panel-bg: #fff;
    --border-color: #eaeaea;
    --text-primary: #333;
    --text-secondary: #666
}

@media (prefers-color-scheme:dark) {
    [data-v-be7e023b]:root {
        --panel-bg: #1a1a1a;
        --border-color: #333;
        --text-primary: #fff;
        --text-secondary: #999
    }
    .loading-overlay[data-v-be7e023b] {
        background: #000c
    }
}

.user .content[data-v-4b702e6b] pre {
    background: none;
    margin: 0;
    padding: 0
}

.user .content[data-v-4b702e6b] pre code {
    font-family: sans-serif;
    white-space: pre-wrap
}

.conversation-content[data-v-4b702e6b] {
    background: transparent;
    color: #666;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding-bottom: 180px;
    position: relative
}

.conversation .role[data-v-4b702e6b] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.conversation .avatar[data-v-4b702e6b] {
    height: 36px;
    width: 36px
}

.conversation .avatar img[data-v-4b702e6b] {
    width: 100%
}

.conversation .name[data-v-4b702e6b] {
    color: #252525;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal
}

.conversation-item-desc .bubble[data-v-4b702e6b] {
    align-items: flex-start;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    justify-content: flex-start;
    line-height: 20px;
    padding: 12px 16px
}

.conversation-item-desc .bubble code[data-v-4b702e6b],
.conversation-item-desc .bubble pre[data-v-4b702e6b] {
    font-size: 16px
}

.conversation-item-desc .button[data-v-4b702e6b] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 2px;
    line-height: normal;
    padding: 4px 8px
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc .button[data-v-4b702e6b] {
        border-color: #252525;
        color: #ddd
    }
}

.conversation-item-desc .button .icon[data-v-4b702e6b] {
    display: flex
}

@keyframes blink-animation-4b702e6b {
    0%,
    to {
        color: #000
    }
    50% {
        color: transparent
    }
}

@media (prefers-color-scheme:dark) {
    @keyframes blink-animation-4b702e6b {
        0%,
        to {
            color: #fff
        }
        50% {
            color: transparent
        }
    }
}

.conversation-item-desc.assistant .bubble[data-v-4b702e6b] .cursor {
    animation: blink-animation-4b702e6b .5s infinite;
    color: #606366;
    font-size: 12px
}

.conversation-item-desc[data-v-4b702e6b] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-width: 260px;
    width: 100%
}

.conversation-item-desc.user[data-v-4b702e6b] {
    align-items: flex-end;
    margin-left: 30px
}

.conversation-item-desc.assistant .bubble[data-v-4b702e6b] {
    border: 1px solid transparent;
    border-radius: 16px;
    min-width: 260px;
    padding-left: 0;
    padding-right: 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.retry[data-v-4b702e6b],
.conversation-item-desc.assistant .bubble.try_moa[data-v-4b702e6b] {
    align-items: center;
    background: #fafafa;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 18px 16px
}

.conversation-item-desc .retry .button[data-v-4b702e6b],
.conversation-item-desc .try_moa .button[data-v-4b702e6b] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 8px 16px
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.retry[data-v-4b702e6b],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-4b702e6b] {
        flex-direction: column
    }
}

.models-list[data-v-4b702e6b] {
    background-color: #fff;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    padding: 20px 8px
}

.models-list .model[data-v-4b702e6b] {
    color: #232425;
    margin-top: 4px
}

.models-list .model .row[data-v-4b702e6b] {
    border-radius: 8px;
    color: #232425;
    gap: 4px;
    justify-content: space-between
}

.models-list .model .row[data-v-4b702e6b],
.models-selected[data-v-4b702e6b] {
    display: flex;
    flex-direction: row;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.models-selected[data-v-4b702e6b] {
    background-color: #f4f4f4;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 150%;
    padding: 6px 12px
}

@media (prefers-color-scheme:dark) {
    .models-list[data-v-4b702e6b] {
        background-color: #252525
    }
    .models-list .model .row[data-v-4b702e6b] {
        color: #ddd
    }
    .conversation-item-desc.assistant .bubble[data-v-4b702e6b] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble.retry[data-v-4b702e6b],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-4b702e6b] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .retry .button[data-v-4b702e6b],
    .conversation-item-desc .try_moa .button[data-v-4b702e6b] {
        background: #fafafa;
        color: #232425
    }
}

.controls[data-v-4b702e6b] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden
}

.controls[data-v-4b702e6b]::-webkit-scrollbar {
    display: none
}

.permission-wrapper[data-v-4b702e6b] {
    display: flex;
    justify-content: center;
    width: 100%
}

.empty-placeholder[data-v-4b702e6b] {
    color: #232425;
    font-family: Arial;
    font-size: 30px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin-top: 30vh;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .empty-placeholder[data-v-4b702e6b] {
        color: #fff
    }
}

.general-chat-wrapper[data-v-4b702e6b] {
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
    position: relative
}

.chat-wrapper[data-v-4b702e6b],
.general-chat-wrapper[data-v-4b702e6b] {
    box-sizing: border-box;
    display: flex;
    width: 100%
}

.chat-wrapper[data-v-4b702e6b] {
    align-items: flex-start;
    justify-content: center;
    overflow-x: hidden;
    padding: 16px 16px 100px
}

.chat-content-wrapper[data-v-4b702e6b] {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    max-width: 100%;
    width: var(--container-width, 680px)
}

.input-wrapper-wrapper[data-v-4b702e6b] {
    align-items: center;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-bottom: 4px;
    padding-top: 16px;
    width: 100%;
    z-index: 100
}

.input-wrapper[data-v-4b702e6b] {
    width: var(--container-width, 680px)
}

.conversation-wrapper[data-v-4b702e6b] {
    background-color: transparent;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    max-width: 100%;
    min-height: 200px;
    padding-bottom: 120px;
    width: var(--container-width, 680px)
}

.conversation-statement[data-v-4b702e6b] {
    display: flex;
    flex-direction: column;
    list-style: none;
    margin: 10px 0
}

.conversation-statement.user[data-v-4b702e6b] {
    align-items: flex-end
}

.conversation-statement.assistant+.conversation-statement.user[data-v-4b702e6b],
.conversation-statement.user+.conversation-statement.assistant[data-v-4b702e6b] {
    margin-top: 16px
}

.conversation-statement[data-v-4b702e6b]:first-child {
    margin-top: 5px
}

.conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-4b702e6b] {
    border: 1px solid #efefef;
    padding: 10px 16px;
    width: 100%
}

.conversation-item-desc.user .bubble[data-v-4b702e6b] {
    background: #f5f5f5;
    border-radius: 16px;
    color: #232425;
    margin-right: 2px;
    max-width: 70%
}

.conversation-item-desc.user .content-item[data-v-4b702e6b] {
    align-items: flex-end;
    display: flex
}

.conversation-item-desc.assistant .bubble[data-v-4b702e6b] {
    box-sizing: border-box;
    -webkit-hyphens: auto;
    hyphens: auto;
    max-width: 100%;
    padding: 0
}

.conversation-item-desc.assistant .bubble.text[data-v-4b702e6b] {
    margin: 24px 0 16px;
    padding: 10px 0
}

.conversation-item-desc.assistant .bubble[data-v-4b702e6b]:not(:first-child) {
    margin-top: 20px
}

.conversation-item-desc.user .bubble[data-v-4b702e6b]:not(:first-child) {
    margin-top: 12px
}

.conversation-item-desc .bubble .loading[data-v-4b702e6b],
.conversation-item-desc .bubble[data-v-4b702e6b] .cursor {
    display: none
}

.conversation-item-desc.assistant .bubble.thinking[data-v-4b702e6b] .cursor {
    display: inline
}

.conversation-item-desc .desc[data-v-4b702e6b] {
    display: flex;
    flex-direction: row;
    max-width: 100%
}

.conversation-item-desc .content[data-v-4b702e6b] {
    max-width: 100%
}

.creating-image-tasks[data-v-4b702e6b] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    justify-content: center
}

.creating-image-tasks .text[data-v-4b702e6b] {
    color: #232425;
    font-size: 14px;
    line-height: 150%
}

.creating-image-tasks .model-icon-wrapper[data-v-4b702e6b] {
    align-items: center;
    background-color: #f0f0f0;
    border-radius: 50%;
    display: flex;
    flex-direction: row;
    gap: 4px;
    height: 18px;
    overflow: hidden;
    width: 18px
}

.creating-image-tasks .model-icon-wrapper .model-icon[data-v-4b702e6b] {
    height: 100%;
    width: 100%
}

.array-content-wrapper[data-v-4b702e6b] {
    align-items: flex-end;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%
}

.remix-button[data-v-4b702e6b] {
    align-items: center;
    background: #00000065;
    border-radius: 18px;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    line-height: 20px;
    opacity: 0;
    padding: 4px 12px;
    position: absolute;
    right: 8px;
    top: 8px;
    transition: transform .3s, opacity .3s;
    z-index: 2
}

.remix-button[data-v-4b702e6b]:hover {
    transform: scale(1.05)
}

@media (hover:hover) {
    .image-item:hover .remix-button[data-v-4b702e6b] {
        opacity: 1
    }
}

@media (hover:none) {
    .remix-button[data-v-4b702e6b] {
        opacity: 1
    }
}

.models-wrapper[data-v-4b702e6b] {
    width: -moz-fit-content;
    width: fit-content
}

.models-list[data-v-4b702e6b] {
    gap: 4px
}

.list-scroll[data-v-4b702e6b] {
    max-height: 600px;
    overflow-y: auto
}

.models-selected[data-v-4b702e6b] {
    align-items: center;
    background-color: transparent;
    border: 1px solid #efefef;
    box-sizing: border-box;
    justify-content: center;
    transition: all .2s ease
}

.models-selected[data-v-4b702e6b]:hover {
    background-color: #f0f0f0
}

.models-selected .model-selected[data-v-4b702e6b] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.models-selected .model-selected .icon[data-v-4b702e6b] {
    align-items: center;
    background-color: transparent;
    border-radius: 50%;
    display: flex;
    height: 16px;
    justify-content: center;
    overflow: hidden;
    width: 16px
}

.models-selected .model-selected .icon[data-v-4b702e6b] svg {
    height: 16px;
    width: 16px
}

.models-selected .model-selected .icon img[data-v-4b702e6b] {
    height: 16px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 16px
}

.models-list .model[data-v-4b702e6b] {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 10px 6px 10px 10px
}

.models-list .model[data-v-4b702e6b]:first-child {
    margin-top: 0
}

.models-list .divider[data-v-4b702e6b] {
    background-color: #efefef;
    height: 1px;
    margin: 6px 0;
    width: 100%
}

.models-list .model .row[data-v-4b702e6b] {
    box-sizing: border-box;
    color: #606366;
    height: 20px;
    width: 100%
}

.models-list .model .description[data-v-4b702e6b] {
    color: #909499;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px
}

@media (hover:hover) {
    .models-list .model[data-v-4b702e6b]:hover {
        background-color: #f5f5f5;
        border-radius: 12px
    }
}

.models-list .model .left[data-v-4b702e6b] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 10px
}

.models-list .model .left .icon[data-v-4b702e6b] {
    align-items: center;
    background-color: transparent;
    border-radius: 50%;
    display: flex;
    height: 20px;
    justify-content: center;
    overflow: hidden;
    width: 20px
}

.models-list .model .left .icon[data-v-4b702e6b] svg {
    height: 20px;
    width: 20px
}

.models-list .model .left .icon img[data-v-4b702e6b] {
    height: 20px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 20px
}

.models-list .model .right[data-v-4b702e6b] {
    align-items: center;
    display: flex;
    flex-direction: row
}

.models-selected.remix-selector .icon[data-v-4b702e6b] svg {
    height: 16px;
    width: 16px
}

.models-selected.remix-selector .icon[data-v-4b702e6b] svg path {
    fill: #232425
}

@media (prefers-color-scheme:dark) {
    .models-list .model .row[data-v-4b702e6b] {
        color: #ddd
    }
    .models-selected.aspect-ratio-selector[data-v-4b702e6b],
    .models-selected.style-selector[data-v-4b702e6b] {
        border: 1px solid #efefef30;
        color: #fff
    }
    @media (hover:hover) {
        .models-selected.aspect-ratio-selector[data-v-4b702e6b]:hover,
        .models-selected.style-selector[data-v-4b702e6b]:hover {
            background-color: #f0f0f010
        }
        .models-list .model:hover .row[data-v-4b702e6b] {
            background-color: #333
        }
    }
    .models-list .model .description[data-v-4b702e6b] {
        color: #909499
    }
    .models-list .divider[data-v-4b702e6b] {
        background-color: #333
    }
    .remix-selector[data-v-4b702e6b] {
        border: 1px solid #efefef30;
        color: #fff
    }
    .remix-selector[data-v-4b702e6b]:hover {
        background-color: #f0f0f010
    }
}

.model-response-wrapper .title[data-v-4b702e6b] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 4px
}

.model-response-wrapper .title .icon[data-v-4b702e6b] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    margin-right: 5px;
    width: 16px
}

.model-response-wrapper .status .icon[data-v-4b702e6b] {
    margin-right: 0
}

.model-response-wrapper .title .icon img[data-v-4b702e6b] {
    height: 100%;
    width: 100%
}

.model-response-wrapper[data-v-4b702e6b] {
    position: relative;
    width: 100%
}

.model-response-wrapper .content[data-v-4b702e6b] {
    transition: all .3s ease-in-out
}

.model-response-wrapper .content.no-expand[data-v-4b702e6b] {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    line-height: 30px;
    max-height: 0;
    overflow: hidden
}

.model-response-wrapper.thinking .content.no-expand[data-v-4b702e6b] {
    max-height: 90px
}

.model-response-wrapper.expanded .content[data-v-4b702e6b] {
    max-height: unset;
    padding-top: 20px
}

.model-response .title .status .icon.finished[data-v-4b702e6b] {
    color: #5cd4a1
}

.model-response .title .status .icon[data-v-4b702e6b] svg {
    height: 100%;
    width: 100%
}

.model-response-wrapper .expand-button[data-v-4b702e6b] {
    cursor: pointer;
    height: 20px;
    position: absolute;
    right: 10px;
    top: 12px;
    width: 20px
}

.model-response .divider[data-v-4b702e6b] {
    background-color: #efefef;
    height: 1px;
    margin-bottom: 20px
}

.model-response[data-v-4b702e6b] {
    color: #232425;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    width: 100%
}

.models-list[data-v-4b702e6b] .n-checkbox.n-checkbox--checked {
    --n-box-color-checked: transparent !important;
    --n-check-mark-color: #232425 !important;
    --n-border-checked: 1px solid transparent !important;
    --n-color-checked: transparent !important
}

.models-list[data-v-4b702e6b] .n-checkbox {
    --n-box-color: transparent !important;
    --n-border: 1px solid transparent !important
}

.reflection-toggle[data-v-4b702e6b] .n-checkbox.n-checkbox--show-label {
    align-items: center;
    display: flex;
    flex-direction: row
}

[data-v-4b702e6b] .models-selected {
    border-radius: 8px
}

.controls[data-v-4b702e6b] {
    flex-wrap: nowrap;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    white-space: nowrap
}

.controls[data-v-4b702e6b]>* {
    flex-shrink: 0
}

.options-wrapper[data-v-4b702e6b] {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 8px
}

.aspect-ratio-selector[data-v-4b702e6b],
.gallery-toggle .icon[data-v-4b702e6b],
.models-selected[data-v-4b702e6b],
.reflection-toggle[data-v-4b702e6b],
.upload-attachments .icon[data-v-4b702e6b] {
    flex-shrink: 0;
    white-space: nowrap
}

.option[data-v-4b702e6b],
.personalize-options[data-v-4b702e6b],
.personalize-selected[data-v-4b702e6b] {
    align-items: center;
    display: flex
}

.personalize-selected[data-v-4b702e6b] {
    background-color: #fafafa;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    height: 28px;
    justify-content: space-between;
    line-height: 150%;
    padding: 0 12px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.image-content[data-v-4b702e6b] {
    border-radius: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-top: 12px;
    max-width: 40%;
    width: 100%
}

.gallery-toggle .icon[data-v-4b702e6b] {
    align-items: center;
    border-radius: 16px;
    color: #232425;
    cursor: pointer;
    display: flex;
    height: 30px;
    justify-content: center;
    transition: background-color .2s;
    width: 20px
}

.gallery-toggle .icon.active[data-v-4b702e6b] {
    background-color: #eef3ff
}

.expand-button[data-v-4b702e6b] {
    transform: rotate(180deg)
}

.expanded .expand-button[data-v-4b702e6b] {
    transform: rotate(0)
}

.upload-attachments .icon[data-v-4b702e6b] {
    align-items: center;
    border-radius: 16px;
    color: #232425;
    cursor: pointer;
    display: flex;
    height: 20px;
    justify-content: center;
    transition: transform .3s;
    width: 20px
}

.upload-attachments .icon[data-v-4b702e6b]:hover {
    transform: scale(1.1)
}

.content-item[data-v-4b702e6b] {
    align-items: center;
    display: flex;
    flex-direction: column;
    margin: 8px 0;
    width: 100%
}

.image-content img[data-v-4b702e6b] {
    border-radius: 8px;
    max-width: 100%;
    -o-object-fit: contain;
    object-fit: contain
}

.text-content[data-v-4b702e6b] {
    color: inherit;
    line-height: 1.5
}

.extra-options[data-v-4b702e6b] {
    flex-direction: row;
    gap: 12px;
    height: 30px
}

.extra-options[data-v-4b702e6b],
.profile-avatar[data-v-4b702e6b] {
    align-items: center;
    display: flex
}

.profile-avatar[data-v-4b702e6b] {
    background-color: #f5f5f5;
    border-radius: 50%;
    height: 32px;
    justify-content: center;
    overflow: hidden;
    width: 32px
}

.profile-avatar img[data-v-4b702e6b] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.profile-avatar .dot[data-v-4b702e6b] {
    background-color: #606366;
    border-radius: 50%;
    height: 8px;
    width: 8px
}

.personalize-list[data-v-4b702e6b] {
    min-width: 240px;
    padding: 8px
}

.profile-option[data-v-4b702e6b] {
    align-items: center;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    gap: 12px;
    padding: 12px;
    transition: background-color .2s
}

.profile-option[data-v-4b702e6b]:hover {
    background-color: #f5f5f5
}

.profile-option.active[data-v-4b702e6b] {
    background-color: #eef3ff
}

.profile-name[data-v-4b702e6b] {
    color: #232425;
    flex: 1;
    font-size: 14px
}

[data-v-4b702e6b] .moa-image-side-pannel {
    bottom: auto;
    left: auto;
    margin-top: 16px;
    max-height: 300px;
    overflow-y: auto;
    position: relative;
    transform: none;
    width: 100%
}

.side-panel-container[data-v-4b702e6b] {
    border-radius: 8px;
    margin-top: 16px;
    overflow: hidden
}

.hd-toggle[data-v-4b702e6b],
.trigger-icon[data-v-4b702e6b] {
    align-items: center;
    display: flex
}

.trigger-icon[data-v-4b702e6b] {
    transition: transform .2s
}

.trigger-icon.active[data-v-4b702e6b] {
    transform: rotate(180deg)
}

.moa-title[data-v-4b702e6b] {
    color: #ccc;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin: 24px 0 8px
}

.assistant-message-divider[data-v-4b702e6b] {
    background-color: #efefef;
    height: 1px;
    margin: 50px 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.try_moa[data-v-4b702e6b] {
    font-size: 16px;
    line-height: 1.5em;
    width: 100%
}

.conversation-item-desc .try_moa .button[data-v-4b702e6b] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    line-height: 1.5em;
    min-width: -moz-fit-content;
    min-width: fit-content;
    padding: 7.5px 16px;
    transition: transform .2s;
    white-space: nowrap
}

.conversation-item-desc .try_moa .button[data-v-4b702e6b]:hover {
    transform: scale(1.05)
}

.reflection-toggle[data-v-4b702e6b] {
    align-items: center;
    background-color: transparent;
    border: 1px solid #efefef;
    border-radius: 8px;
    box-sizing: border-box;
    display: flex;
    padding: 6px 12px;
    white-space: nowrap
}

.reflection-toggle[data-v-4b702e6b] .n-checkbox-box {
    height: 16px;
    width: 16px
}

.reflection-toggle[data-v-4b702e6b] .n-checkbox__label {
    color: #606366;
    font-size: 14px;
    font-weight: 400;
    line-height: 21px
}

.reflection-toggle[data-v-4b702e6b] .n-checkbox--show-label {
    line-height: 16px
}

@media (prefers-color-scheme:dark) {
    .input-wrapper-wrapper[data-v-4b702e6b] {
        background-color: #232425
    }
    .reflection-toggle[data-v-4b702e6b]:hover {
        background-color: #f0f0f010
    }
    .models-selected .model-selected .icon[data-v-4b702e6b] {
        background-color: #fff
    }
    .models-list[data-v-4b702e6b],
    .n-popover.models-popover[data-v-4b702e6b] {
        background-color: #252525
    }
    .models-list .model .left .text[data-v-4b702e6b] {
        color: #ddd
    }
    .models-list .model .left .icon[data-v-4b702e6b] {
        background-color: #efefef
    }
    @media (hover:hover) {
        .models-list .model[data-v-4b702e6b]:hover,
        .models-list .model:hover .row[data-v-4b702e6b] {
            background-color: #333
        }
    }
    .creating-image-tasks .text[data-v-4b702e6b] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble[data-v-4b702e6b],
    .conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-4b702e6b] {
        background: none;
        box-sizing: border-box;
        color: #fff
    }
    .conversation-item-desc.user .bubble[data-v-4b702e6b] {
        background: #eee;
        color: #000
    }
    .conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-4b702e6b] {
        background-color: #444
    }
    .model-response[data-v-4b702e6b] {
        color: #ddd
    }
    .section-title[data-v-4b702e6b] {
        color: #666
    }
    .upload-attachments .icon[data-v-4b702e6b],
    .personalize-options[data-v-4b702e6b] .n-radio {
        color: #ddd
    }
    .personalize-selected[data-v-4b702e6b] {
        background-color: #333;
        color: #ddd
    }
    .profile-avatar[data-v-4b702e6b],
    .profile-option[data-v-4b702e6b]:hover {
        background-color: #333
    }
    .profile-option.active[data-v-4b702e6b] {
        background-color: #444
    }
    .profile-name[data-v-4b702e6b] {
        color: #ddd
    }
    .profile-avatar .dot[data-v-4b702e6b] {
        background-color: #ddd
    }
    .gallery-toggle .icon[data-v-4b702e6b] {
        background-color: #333;
        color: #ddd
    }
    .gallery-toggle .icon.active[data-v-4b702e6b] {
        background-color: #444
    }
    .hd-toggle[data-v-4b702e6b] .n-checkbox {
        color: #ddd
    }
    .models-list[data-v-4b702e6b] .n-checkbox.n-checkbox--checked {
        --n-check-mark-color: #ddd !important
    }
    .reflection-toggle[data-v-4b702e6b] {
        background-color: #232425;
        border: 1px solid #efefef30
    }
    .reflection-toggle[data-v-4b702e6b] .n-checkbox {
        --n-border: 1px solid #444;
        --n-box-color-checked: #0f7fff;
        --n-check-mark-color: #fff;
        --n-border-checked: 1px solid #0f7fff
    }
    .reflection-toggle[data-v-4b702e6b] .n-checkbox__label {
        color: #fff
    }
}

.model-divider[data-v-4b702e6b] {
    background-color: #efefef;
    height: 1px;
    margin: 4px 12px
}

@media (prefers-color-scheme:dark) {
    .model-divider[data-v-4b702e6b] {
        background-color: #333
    }
}

.reflection-toggle[data-v-4b702e6b] .n-checkbox {
    --n-box-color: #fff !important;
    --n-border: 1px solid #efefef !important;
    --n-border-checked: 1px solid #0f7fff !important;
    --n-border-focus: 1px solid #0f7fff !important;
    --n-border-disabled: 1px solid #e0e0e6 !important;
    --n-border-disabled-checked: 1px solid #e0e0e6 !important;
    --n-box-shadow-focus: 0 0 0 2px rgba(15, 127, 255, .3) !important;
    --n-color: transparent !important;
    --n-color-checked: #0f7fff !important
}

.reflection-toggle[data-v-4b702e6b] .n-checkbox:hover .n-checkbox-box .n-checkbox-box__border {
    border: 1px solid #0f7fff !important
}

.reflection-toggle[data-v-4b702e6b] .n-checkbox:focus:not(:active) .n-checkbox-box .n-checkbox-box__border {
    border: 1px solid #0f7fff !important;
    box-shadow: 0 0 0 2px #0f7fff4d !important
}

.reflection-toggle[data-v-4b702e6b] .n-checkbox.n-checkbox--checked {
    --n-box-color-checked: #0f7fff !important;
    --n-check-mark-color: #fff !important;
    --n-border-checked: 1px solid #0f7fff !important;
    --n-color-checked: #0f7fff !important
}

.task-status[data-v-4b702e6b] {
    color: #666;
    font-size: 12px;
    margin-top: 8px
}

.status-complete[data-v-4b702e6b] {
    color: #4caf50
}

.status-pending[data-v-4b702e6b] {
    color: #2196f3
}

.models-list .model .right input[type=radio][data-v-4b702e6b] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    cursor: pointer;
    height: 16px;
    margin: 0;
    position: relative;
    width: 16px
}

.models-list .model .right input[type=radio][data-v-4b702e6b]:checked {
    background-color: #fff;
    border-color: #0f7fff
}

.models-list .model .right input[type=radio][data-v-4b702e6b]:checked:after {
    background: #0f7fff;
    border-radius: 50%;
    content: "";
    height: 10px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 10px
}

@media (prefers-color-scheme:dark) {
    .models-list .model .right input[type=radio][data-v-4b702e6b] {
        background-color: #fff;
        border-color: #666
    }
    .models-list .model .right input[type=radio][data-v-4b702e6b]:checked {
        background-color: #fff;
        border-color: #0f7fff
    }
}

.models-wrapper .models-selected[data-v-4b702e6b] {
    background-color: #f4f4f4;
    border: 1px solid #f4f4f4;
    border-radius: 16.5px
}

.models-wrapper .models-selected[data-v-4b702e6b]:hover {
    background-color: #e8e8e8;
    border: 1px solid #e8e8e8
}

@media (prefers-color-scheme:dark) {
    .aspect-ratio-selector .models-selected[data-v-4b702e6b] {
        background-color: #333;
        border-color: #444
    }
    .aspect-ratio-selector .models-selected[data-v-4b702e6b]:hover {
        background-color: #444
    }
    .conversation-item-desc.assistant .bubble.try_moa[data-v-4b702e6b] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .try_moa .button[data-v-4b702e6b] {
        background: #fafafa;
        color: #232425
    }
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.try_moa[data-v-4b702e6b] {
        flex-direction: column
    }
    .chat-wrapper[data-v-4b702e6b] {
        box-sizing: border-box;
        padding: 16px 16px 100px
    }
    .input-wrapper-wrapper[data-v-4b702e6b] {
        bottom: 0;
        box-sizing: border-box;
        left: 0;
        padding: 16px 16px 40px;
        position: fixed;
        width: 100%
    }
    .input-wrapper[data-v-4b702e6b],
    .input-wrapper-wrapper-inner[data-v-4b702e6b] {
        width: 100%
    }
    .options-wrapper[data-v-4b702e6b] {
        flex-wrap: wrap;
        gap: 8px
    }
    [data-v-4b702e6b] .moa-image-side-pannel {
        bottom: auto;
        left: auto;
        max-height: 200px;
        transform: none;
        width: 100%
    }
}

.task-content[data-v-4b702e6b] {
    margin: 0
}

.task-content[data-v-4b702e6b] h3 {
    color: #232425;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    margin: 12px 0
}

.prompt-box[data-v-4b702e6b] {
    border-radius: 4px;
    color: #909499;
    font-size: 16px;
    line-height: 1.5;
    margin: 12px 0;
    padding: 12px 16px
}

.prompt-box-title[data-v-4b702e6b] {
    font-weight: 700;
    margin-bottom: 12px
}

.task-box-divider[data-v-4b702e6b] {
    background-color: #efefef;
    height: 1px;
    margin: 40px 0
}

@media (prefers-color-scheme:dark) {
    .task-content[data-v-4b702e6b] h3 {
        color: #fff
    }
    .prompt-box[data-v-4b702e6b] {
        background-color: #333;
        border-left-color: #0f7fff;
        color: #ddd
    }
}

.llm-model-toggle[data-v-4b702e6b] {
    align-items: center;
    background-color: #fff;
    border-radius: 8px;
    box-sizing: border-box;
    display: flex;
    height: 28px
}

@media (prefers-color-scheme:dark) {
    .llm-model-toggle[data-v-4b702e6b] {
        background-color: #333
    }
    .llm-model-toggle[data-v-4b702e6b] .n-select {
        --n-color: #333;
        --n-text-color: #ddd
    }
}

.style-cards[data-v-4b702e6b] {
    background: #fff;
    border-radius: 12px;
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(3, 1fr);
    padding: 16px
}

.style-card[data-v-4b702e6b] {
    align-items: center;
    border: 2px solid transparent;
    border-radius: 10px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    transition: all .2s ease
}

.style-card[data-v-4b702e6b]:hover {
    background: #ffffff1a;
    transform: scale(1.05)
}

.style-card.active[data-v-4b702e6b] {
    background: #0f7fff1a;
    border-color: #0f7fff
}

.style-image[data-v-4b702e6b] {
    background: #333;
    border-radius: 8px;
    height: 101px;
    overflow: hidden;
    position: relative;
    width: 72px
}

.style-image[data-v-4b702e6b]:after {
    background: linear-gradient(180deg, transparent, transparent 50%, rgba(0, 0, 0, .6));
    bottom: 0;
    content: "";
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 0
}

.style-image img[data-v-4b702e6b] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.placeholder-image[data-v-4b702e6b] {
    align-items: center;
    color: #666;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%
}

.style-label[data-v-4b702e6b] {
    bottom: 4px;
    color: #fff;
    font-size: 12px;
    left: 0;
    line-height: 1.5;
    padding: 0 4px;
    position: absolute;
    right: 0;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .style-cards[data-v-4b702e6b] {
        background: #232425
    }
    .style-card[data-v-4b702e6b]:hover {
        background: #ffffff1a
    }
}

.model-preference-tip[data-v-4b702e6b] {
    align-items: center;
    background: #0f7fff0d;
    border-radius: 8px;
    color: #666;
    display: flex;
    font-size: 14px;
    gap: 8px;
    line-height: 1.4;
    margin: 20px 0;
    padding: 12px
}

.tip-icon[data-v-4b702e6b] {
    align-items: center;
    color: #0f7fff;
    display: flex;
    flex-shrink: 0;
    height: 16px;
    width: 16px
}

.tip-text[data-v-4b702e6b] {
    flex: 1
}

@media (prefers-color-scheme:dark) {
    .model-preference-tip[data-v-4b702e6b] {
        background: #0f7fff1a;
        color: #999
    }
}

.conversation-item-desc.assistant .bubble.try_moa .left[data-v-4b702e6b] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.conversation-item-desc.assistant .bubble.try_moa .left .main-text[data-v-4b702e6b] {
    font-size: 16px;
    line-height: 1.5em
}

.conversation-item-desc.assistant .bubble.try_moa .left .sub-text[data-v-4b702e6b] {
    color: #666;
    font-size: 14px;
    line-height: 1.4em
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc.assistant .bubble.try_moa .left .sub-text[data-v-4b702e6b] {
        color: #999
    }
}

.slide-left-enter-active[data-v-4b702e6b],
.slide-left-leave-active[data-v-4b702e6b],
.slide-right-enter-active[data-v-4b702e6b],
.slide-right-leave-active[data-v-4b702e6b] {
    transition: transform .3s ease
}

.slide-left-enter-from[data-v-4b702e6b] {
    transform: translate(100%)
}

.slide-left-leave-to[data-v-4b702e6b],
.slide-right-enter-from[data-v-4b702e6b] {
    transform: translate(-100%)
}

.slide-right-leave-to[data-v-4b702e6b] {
    transform: translate(100%)
}

[data-v-4b702e6b] .moa-image-detail {
    overflow: hidden;
    position: relative
}

[data-v-4b702e6b] .moa-image-detail img {
    transition: transform .3s ease;
    will-change: transform
}

[data-v-4b702e6b] .moa-image-detail {
    touch-action: pan-y pinch-zoom;
    -moz-user-select: none;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none
}

[data-v-4b702e6b] .moa-image-detail img {
    pointer-events: none
}

.slide-left-enter-active[data-v-4b702e6b],
.slide-left-leave-active[data-v-4b702e6b],
.slide-right-enter-active[data-v-4b702e6b],
.slide-right-leave-active[data-v-4b702e6b] {
    height: 100%;
    position: absolute;
    transition: transform .3s cubic-bezier(.4, 0, .2, 1);
    width: 100%
}

.input-wrapper-wrapper-inner[data-v-4b702e6b] {
    margin: 0 auto;
    max-width: 100%;
    position: relative;
    width: var(--container-width, 680px)
}

.media-gallery-container[data-v-4b702e6b] {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 10px #00000026;
    margin-bottom: 16px;
    overflow-y: auto;
    position: absolute;
    width: 100%
}

@media (prefers-color-scheme:dark) {
    .media-gallery-container[data-v-4b702e6b] {
        background-color: #2a2a2a;
        border-color: #333
    }
}

.images-row[data-v-4b702e6b] {
    align-items: flex-end;
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
    overflow-x: auto;
    padding-bottom: 4px;
    width: 100%
}

.image-item[data-v-4b702e6b] {
    flex: 0 0 auto;
    max-width: 40%;
    position: relative
}

.image-item img[data-v-4b702e6b] {
    border-radius: 8px;
    height: auto;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.remix-dropdown-container[data-v-4b702e6b] {
    display: inline-block;
    position: relative
}

.remix-dropdown-content[data-v-4b702e6b] {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 10px #00000026;
    left: 0;
    min-width: 200px;
    position: absolute;
    top: calc(100% + 8px);
    z-index: 1000
}

@media (prefers-color-scheme:dark) {
    .remix-dropdown-content[data-v-4b702e6b] {
        background: #252525
    }
}

.user .content[data-v-57261172] pre {
    background: none;
    margin: 0;
    padding: 0
}

.user .content[data-v-57261172] pre code {
    font-family: sans-serif;
    white-space: pre-wrap
}

.conversation-content[data-v-57261172] {
    background: transparent;
    color: #666;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding-bottom: 180px;
    position: relative
}

.conversation .role[data-v-57261172] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.conversation .avatar[data-v-57261172] {
    height: 36px;
    width: 36px
}

.conversation .avatar img[data-v-57261172] {
    width: 100%
}

.conversation .name[data-v-57261172] {
    color: #252525;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal
}

.conversation-item-desc .bubble[data-v-57261172] {
    align-items: flex-start;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    justify-content: flex-start;
    line-height: 20px;
    padding: 12px 16px
}

.conversation-item-desc .bubble code[data-v-57261172],
.conversation-item-desc .bubble pre[data-v-57261172] {
    font-size: 16px
}

.conversation-item-desc .button[data-v-57261172] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 2px;
    line-height: normal;
    padding: 4px 8px
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc .button[data-v-57261172] {
        border-color: #252525;
        color: #ddd
    }
}

.conversation-item-desc .button .icon[data-v-57261172] {
    display: flex
}

@keyframes blink-animation-57261172 {
    0%,
    to {
        color: #000
    }
    50% {
        color: transparent
    }
}

@media (prefers-color-scheme:dark) {
    @keyframes blink-animation-57261172 {
        0%,
        to {
            color: #fff
        }
        50% {
            color: transparent
        }
    }
}

.conversation-item-desc.assistant .bubble[data-v-57261172] .cursor {
    animation: blink-animation-57261172 .5s infinite;
    color: #606366;
    font-size: 12px
}

.conversation-item-desc[data-v-57261172] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-width: 260px;
    width: 100%
}

.conversation-item-desc.user[data-v-57261172] {
    align-items: flex-end;
    margin-left: 30px
}

.conversation-item-desc.assistant .bubble[data-v-57261172] {
    border: 1px solid transparent;
    border-radius: 16px;
    min-width: 260px;
    padding-left: 0;
    padding-right: 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.retry[data-v-57261172],
.conversation-item-desc.assistant .bubble.try_moa[data-v-57261172] {
    align-items: center;
    background: #fafafa;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 18px 16px
}

.conversation-item-desc .retry .button[data-v-57261172],
.conversation-item-desc .try_moa .button[data-v-57261172] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 8px 16px
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.retry[data-v-57261172],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-57261172] {
        flex-direction: column
    }
}

.models-list[data-v-57261172] {
    background-color: #fff;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    padding: 20px 8px
}

.models-list .model[data-v-57261172] {
    color: #232425;
    margin-top: 4px
}

.models-list .model .row[data-v-57261172] {
    border-radius: 8px;
    color: #232425;
    gap: 4px;
    justify-content: space-between
}

.models-list .model .row[data-v-57261172],
.models-selected[data-v-57261172] {
    display: flex;
    flex-direction: row;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.models-selected[data-v-57261172] {
    background-color: #f4f4f4;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 150%;
    padding: 6px 12px
}

@media (prefers-color-scheme:dark) {
    .models-list[data-v-57261172] {
        background-color: #252525
    }
    .models-list .model .row[data-v-57261172] {
        color: #ddd
    }
    .conversation-item-desc.assistant .bubble[data-v-57261172] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble.retry[data-v-57261172],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-57261172] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .retry .button[data-v-57261172],
    .conversation-item-desc .try_moa .button[data-v-57261172] {
        background: #fafafa;
        color: #232425
    }
}

.controls[data-v-57261172] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden
}

.controls[data-v-57261172]::-webkit-scrollbar {
    display: none
}

.permission-wrapper[data-v-57261172] {
    display: flex;
    justify-content: center;
    width: 100%
}

.empty-placeholder[data-v-57261172] {
    color: #232425;
    font-family: Arial;
    font-size: 30px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin-top: 30vh;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .empty-placeholder[data-v-57261172] {
        color: #fff
    }
}

.general-chat-wrapper[data-v-57261172] {
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
    position: relative
}

.chat-wrapper[data-v-57261172],
.general-chat-wrapper[data-v-57261172] {
    box-sizing: border-box;
    display: flex;
    width: 100%
}

.chat-wrapper[data-v-57261172] {
    align-items: flex-start;
    justify-content: center;
    overflow-x: hidden;
    padding: 16px 16px 100px
}

.chat-content-wrapper[data-v-57261172] {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    max-width: 100%;
    width: var(--container-width, 680px)
}

.input-wrapper-wrapper[data-v-57261172] {
    align-items: center;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-bottom: 4px;
    padding-top: 16px;
    width: 100%;
    z-index: 100
}

.input-wrapper[data-v-57261172] {
    width: var(--container-width, 680px)
}

.conversation-wrapper[data-v-57261172] {
    background-color: transparent;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    max-width: 100%;
    min-height: 200px;
    padding-bottom: 120px;
    width: var(--container-width, 680px)
}

.array-content-wrapper[data-v-57261172] {
    align-items: flex-end;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%
}

.conversation-statement[data-v-57261172] {
    display: flex;
    flex-direction: column;
    list-style: none;
    margin: 10px 0
}

.conversation-statement.user[data-v-57261172] {
    align-items: flex-end
}

.conversation-statement.assistant+.conversation-statement.user[data-v-57261172],
.conversation-statement.user+.conversation-statement.assistant[data-v-57261172] {
    margin-top: 16px
}

.conversation-statement[data-v-57261172]:first-child {
    margin-top: 5px
}

.conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-57261172] {
    border: 1px solid #efefef;
    padding: 10px 16px;
    width: 100%
}

.conversation-item-desc.user .bubble[data-v-57261172] {
    background: #f5f5f5;
    border-radius: 16px;
    color: #232425;
    margin-right: 2px;
    max-width: 70%
}

.conversation-item-desc.user .content-item[data-v-57261172] {
    align-items: flex-end;
    display: flex
}

.conversation-item-desc.assistant .bubble[data-v-57261172] {
    box-sizing: border-box;
    -webkit-hyphens: auto;
    hyphens: auto;
    max-width: 100%;
    padding: 0
}

.conversation-item-desc.assistant .bubble.text[data-v-57261172] {
    margin: 24px 0 16px;
    padding: 10px 0
}

.conversation-item-desc.assistant .bubble[data-v-57261172]:not(:first-child) {
    margin-top: 20px
}

.conversation-item-desc.user .bubble[data-v-57261172]:not(:first-child) {
    margin-top: 12px
}

.conversation-item-desc .bubble .loading[data-v-57261172],
.conversation-item-desc .bubble[data-v-57261172] .cursor {
    display: none
}

.conversation-item-desc.assistant .bubble.thinking[data-v-57261172] .cursor {
    display: inline
}

.conversation-item-desc .desc[data-v-57261172] {
    display: flex;
    flex-direction: row;
    max-width: 100%
}

.conversation-item-desc .content[data-v-57261172] {
    max-width: 100%
}

.creating-image-tasks[data-v-57261172] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    justify-content: center
}

.creating-image-tasks .text[data-v-57261172] {
    color: #232425;
    font-size: 14px;
    line-height: 150%
}

.creating-image-tasks .model-icon-wrapper[data-v-57261172] {
    align-items: center;
    background-color: #f0f0f0;
    border-radius: 50%;
    display: flex;
    flex-direction: row;
    gap: 4px;
    height: 18px;
    overflow: hidden;
    width: 18px
}

.creating-image-tasks .model-icon-wrapper .model-icon[data-v-57261172] {
    height: 100%;
    width: 100%
}

.models-wrapper[data-v-57261172] {
    width: -moz-fit-content;
    width: fit-content
}

.models-list[data-v-57261172] {
    gap: 4px;
    max-width: 280px
}

.list-scroll[data-v-57261172] {
    max-height: 600px;
    overflow-y: auto
}

@media (max-width:768px) {
    .models-list[data-v-57261172] {
        max-height: 430px;
        overflow-y: scroll
    }
}

.models-selected[data-v-57261172] {
    align-items: center;
    background-color: transparent;
    border: 1px solid #efefef;
    box-sizing: border-box;
    justify-content: center;
    transition: background-color .2s
}

.models-selected[data-v-57261172]:hover {
    background-color: #f0f0f0
}

.models-selected .model-selected[data-v-57261172] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.models-selected .model-selected .icon[data-v-57261172] {
    align-items: center;
    background-color: transparent;
    border-radius: 50%;
    display: flex;
    height: 16px;
    justify-content: center;
    overflow: hidden;
    width: 16px
}

.models-selected .model-selected .icon[data-v-57261172] svg {
    height: 16px;
    width: 16px
}

.models-selected .model-selected .icon img[data-v-57261172] {
    height: 16px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 16px
}

.models-selected.remix-selector .icon[data-v-57261172] svg {
    color: #232425;
    height: 16px;
    width: 16px
}

.models-selected.aspect-ratio-selector[data-v-57261172],
.models-selected.duration-selector[data-v-57261172],
.models-selected.remix-selector[data-v-57261172] {
    border-radius: 8px
}

.models-list .model[data-v-57261172] {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 10px 6px 10px 10px
}

.models-list .model[data-v-57261172]:hover {
    background-color: #f5f5f5
}

.models-list .model.disabled[data-v-57261172] {
    cursor: not-allowed;
    opacity: .5
}

.models-list .model.disabled[data-v-57261172]:hover {
    background-color: transparent
}

.models-list .model[data-v-57261172]:first-child {
    margin-top: 0
}

.models-list .divider[data-v-57261172] {
    background-color: #efefef;
    height: 1px;
    margin: 6px 0;
    width: 100%
}

.models-list .model .row[data-v-57261172] {
    box-sizing: border-box;
    color: #606366;
    height: 20px;
    width: 100%
}

.models-list .model .description[data-v-57261172],
.models-list .model .tip[data-v-57261172] {
    color: #909499;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px
}

@media (hover:hover) {
    .models-list .model[data-v-57261172]:hover {
        background-color: #f5f5f5;
        border-radius: 12px
    }
}

.models-list .model .left[data-v-57261172] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 10px
}

.models-list .model .left .icon[data-v-57261172] {
    align-items: center;
    background-color: transparent;
    border-radius: 50%;
    display: flex;
    height: 20px;
    justify-content: center;
    overflow: hidden;
    width: 20px
}

.models-list .model .left .icon[data-v-57261172] svg {
    height: 20px;
    width: 20px
}

.models-list .model .left .icon img[data-v-57261172] {
    height: 20px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 20px
}

.models-list .model .right[data-v-57261172] {
    align-items: center;
    display: flex;
    flex-direction: row
}

@media (prefers-color-scheme:dark) {
    .models-list .model .row[data-v-57261172] {
        color: #ddd
    }
    .models-selected.aspect-ratio-selector[data-v-57261172],
    .models-selected.style-selector[data-v-57261172] {
        border: 1px solid #efefef30;
        color: #fff
    }
    @media (hover:hover) {
        .models-selected.aspect-ratio-selector[data-v-57261172]:hover,
        .models-selected.style-selector[data-v-57261172]:hover {
            background-color: #f0f0f010
        }
        .models-list .model:hover .row[data-v-57261172] {
            background-color: #333
        }
    }
    .models-list .model .description[data-v-57261172] {
        color: #909499
    }
    .models-list .divider[data-v-57261172] {
        background-color: #333
    }
}

.model-response-wrapper .title[data-v-57261172] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 4px
}

.model-response-wrapper .title .icon[data-v-57261172] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    margin-right: 5px;
    width: 16px
}

.model-response-wrapper .status .icon[data-v-57261172] {
    margin-right: 0
}

.model-response-wrapper .title .icon img[data-v-57261172] {
    height: 100%;
    width: 100%
}

.model-response-wrapper[data-v-57261172] {
    position: relative;
    width: 100%
}

.model-response-wrapper .content[data-v-57261172] {
    transition: all .3s ease-in-out
}

.model-response-wrapper .content.no-expand[data-v-57261172] {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    line-height: 30px;
    max-height: 0;
    overflow: hidden
}

.model-response-wrapper.thinking .content.no-expand[data-v-57261172] {
    max-height: 90px
}

.model-response-wrapper.expanded .content[data-v-57261172] {
    max-height: unset;
    padding-top: 20px
}

.model-response .title .status .icon.finished[data-v-57261172] {
    color: #5cd4a1
}

.model-response .title .status .icon[data-v-57261172] svg {
    height: 100%;
    width: 100%
}

.model-response-wrapper .expand-button[data-v-57261172] {
    cursor: pointer;
    height: 20px;
    position: absolute;
    right: 10px;
    top: 12px;
    width: 20px
}

.model-response .divider[data-v-57261172] {
    background-color: #efefef;
    height: 1px;
    margin-bottom: 20px
}

.model-response[data-v-57261172] {
    color: #232425;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    width: 100%
}

.models-list[data-v-57261172] .n-checkbox.n-checkbox--checked {
    --n-box-color-checked: transparent !important;
    --n-check-mark-color: #232425 !important;
    --n-border-checked: 1px solid transparent !important;
    --n-color-checked: transparent !important
}

.models-list[data-v-57261172] .n-checkbox {
    --n-box-color: transparent !important;
    --n-border: 1px solid transparent !important
}

.controls[data-v-57261172] {
    flex-wrap: nowrap;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    white-space: nowrap
}

.controls[data-v-57261172]>* {
    flex-shrink: 0
}

.options-wrapper[data-v-57261172] {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 8px
}

.aspect-ratio-selector[data-v-57261172],
.gallery-toggle .icon[data-v-57261172],
.models-selected[data-v-57261172],
.reflection-toggle[data-v-57261172],
.upload-attachments .icon[data-v-57261172] {
    flex-shrink: 0;
    white-space: nowrap
}

.option[data-v-57261172],
.personalize-options[data-v-57261172],
.personalize-selected[data-v-57261172] {
    align-items: center;
    display: flex
}

.personalize-selected[data-v-57261172] {
    background-color: #fafafa;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    height: 28px;
    justify-content: space-between;
    line-height: 150%;
    padding: 0 12px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.image-content[data-v-57261172] {
    border-radius: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-top: 12px;
    max-width: 40%;
    width: 100%
}

.gallery-toggle .icon[data-v-57261172] {
    align-items: center;
    background-color: #fafafa;
    border-radius: 16px;
    color: #232425;
    cursor: pointer;
    display: flex;
    height: 30px;
    justify-content: center;
    padding: 5px 12px;
    transition: background-color .2s;
    width: 20px
}

.gallery-toggle .icon.active[data-v-57261172] {
    background-color: #eef3ff
}

.expand-button[data-v-57261172] {
    transform: rotate(180deg)
}

.expanded .expand-button[data-v-57261172] {
    transform: rotate(0)
}

.upload-attachments .icon[data-v-57261172] {
    align-items: center;
    border-radius: 16px;
    color: #232425;
    cursor: pointer;
    display: flex;
    height: 20px;
    justify-content: center;
    transition: transform .3s;
    width: 20px
}

.upload-attachments .icon[data-v-57261172]:hover {
    transform: scale(1.1)
}

.content-item[data-v-57261172] {
    align-items: center;
    display: flex;
    flex-direction: column;
    margin: 8px 0;
    width: 100%
}

.image-content img[data-v-57261172] {
    border-radius: 8px;
    max-width: 100%;
    -o-object-fit: contain;
    object-fit: contain
}

.text-content[data-v-57261172] {
    color: inherit;
    line-height: 1.5
}

.extra-options[data-v-57261172] {
    flex-direction: row;
    gap: 12px;
    height: 30px
}

.extra-options[data-v-57261172],
.profile-avatar[data-v-57261172] {
    align-items: center;
    display: flex
}

.profile-avatar[data-v-57261172] {
    background-color: #f5f5f5;
    border-radius: 50%;
    height: 32px;
    justify-content: center;
    overflow: hidden;
    width: 32px
}

.profile-avatar img[data-v-57261172] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.profile-avatar .dot[data-v-57261172] {
    background-color: #606366;
    border-radius: 50%;
    height: 8px;
    width: 8px
}

.personalize-list[data-v-57261172] {
    min-width: 240px;
    padding: 8px
}

.profile-option[data-v-57261172] {
    align-items: center;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    gap: 12px;
    padding: 12px;
    transition: background-color .2s
}

.profile-option[data-v-57261172]:hover {
    background-color: #f5f5f5
}

.profile-option.active[data-v-57261172] {
    background-color: #eef3ff
}

.profile-name[data-v-57261172] {
    color: #232425;
    flex: 1;
    font-size: 14px
}

[data-v-57261172] .moa-image-side-pannel {
    bottom: auto;
    left: auto;
    margin-top: 16px;
    max-height: 300px;
    overflow-y: auto;
    position: relative;
    transform: none;
    width: 100%
}

.side-panel-container[data-v-57261172] {
    border-radius: 8px;
    margin-top: 16px;
    overflow: hidden
}

.hd-toggle[data-v-57261172],
.trigger-icon[data-v-57261172] {
    align-items: center;
    display: flex
}

.trigger-icon[data-v-57261172] {
    transition: transform .2s
}

.trigger-icon.active[data-v-57261172] {
    transform: rotate(180deg)
}

.moa-title[data-v-57261172] {
    color: #ccc;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin: 24px 0 8px
}

.assistant-message-divider[data-v-57261172] {
    background-color: #efefef;
    height: 1px;
    margin: 50px 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.try_moa[data-v-57261172] {
    font-size: 16px;
    line-height: 1.5em;
    width: 100%
}

.conversation-item-desc .try_moa .button[data-v-57261172] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    line-height: 1.5em;
    min-width: -moz-fit-content;
    min-width: fit-content;
    padding: 7.5px 16px;
    transition: transform .2s;
    white-space: nowrap
}

.conversation-item-desc .try_moa .button[data-v-57261172]:hover {
    transform: scale(1.05)
}

.reflection-toggle[data-v-57261172] {
    align-items: center;
    background-color: transparent;
    border: 1px solid #efefef;
    border-radius: 8px;
    box-sizing: border-box;
    display: flex;
    height: 33px;
    padding: 6px 12px;
    white-space: nowrap
}

.reflection-toggle[data-v-57261172] .n-checkbox-box {
    height: 16px;
    width: 16px
}

.reflection-toggle[data-v-57261172] .n-checkbox__label {
    color: #606366;
    font-size: 14px;
    font-weight: 400;
    line-height: 16px
}

.reflection-toggle[data-v-57261172] .n-checkbox--show-label {
    line-height: 16px
}

@media (prefers-color-scheme:dark) {
    .input-wrapper-wrapper[data-v-57261172] {
        background-color: #232425
    }
    .reflection-toggle[data-v-57261172]:hover {
        background-color: #f0f0f010
    }
    .models-selected .model-selected .icon[data-v-57261172] {
        background-color: #fff
    }
    .models-list[data-v-57261172],
    .n-popover.models-popover[data-v-57261172] {
        background-color: #252525
    }
    .models-list .model .left .text[data-v-57261172] {
        color: #ddd
    }
    .models-list .model .left .icon[data-v-57261172] {
        background-color: #efefef
    }
    @media (hover:hover) {
        .models-list .model[data-v-57261172]:hover,
        .models-list .model:hover .row[data-v-57261172] {
            background-color: #333
        }
    }
    .creating-image-tasks .text[data-v-57261172] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble[data-v-57261172],
    .conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-57261172] {
        background: none;
        box-sizing: border-box;
        color: #fff
    }
    .conversation-item-desc.user .bubble[data-v-57261172] {
        background: #eee;
        color: #000
    }
    .conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-57261172] {
        background-color: #444
    }
    .model-response[data-v-57261172] {
        color: #ddd
    }
    .section-title[data-v-57261172] {
        color: #666
    }
    .upload-attachments .icon[data-v-57261172],
    .personalize-options[data-v-57261172] .n-radio {
        color: #ddd
    }
    .personalize-selected[data-v-57261172] {
        background-color: #333;
        color: #ddd
    }
    .profile-avatar[data-v-57261172],
    .profile-option[data-v-57261172]:hover {
        background-color: #333
    }
    .profile-option.active[data-v-57261172] {
        background-color: #444
    }
    .profile-name[data-v-57261172] {
        color: #ddd
    }
    .profile-avatar .dot[data-v-57261172] {
        background-color: #ddd
    }
    .gallery-toggle .icon[data-v-57261172] {
        background-color: #333;
        color: #ddd
    }
    .gallery-toggle .icon.active[data-v-57261172] {
        background-color: #444
    }
    .hd-toggle[data-v-57261172] .n-checkbox {
        color: #ddd
    }
    .models-list[data-v-57261172] .n-checkbox.n-checkbox--checked {
        --n-check-mark-color: #ddd !important
    }
    .reflection-toggle[data-v-57261172] {
        background-color: #232425;
        border: 1px solid #efefef30
    }
    .reflection-toggle[data-v-57261172] .n-checkbox {
        --n-border: 1px solid #444;
        --n-box-color-checked: #0f7fff;
        --n-check-mark-color: #fff;
        --n-border-checked: 1px solid #0f7fff
    }
    .reflection-toggle[data-v-57261172] .n-checkbox__label {
        color: #fff
    }
}

.model-divider[data-v-57261172] {
    background-color: #efefef;
    height: 1px;
    margin: 4px 12px
}

@media (prefers-color-scheme:dark) {
    .model-divider[data-v-57261172] {
        background-color: #333
    }
}

.reflection-toggle[data-v-57261172] .n-checkbox {
    --n-box-color: #fff !important;
    --n-border: 1px solid #efefef !important;
    --n-border-checked: 1px solid #0f7fff !important;
    --n-border-focus: 1px solid #0f7fff !important;
    --n-border-disabled: 1px solid #e0e0e6 !important;
    --n-border-disabled-checked: 1px solid #e0e0e6 !important;
    --n-box-shadow-focus: 0 0 0 2px rgba(15, 127, 255, .3) !important;
    --n-color: transparent !important;
    --n-color-checked: #0f7fff !important
}

.reflection-toggle[data-v-57261172] .n-checkbox:hover .n-checkbox-box .n-checkbox-box__border {
    border: 1px solid #0f7fff !important
}

.reflection-toggle[data-v-57261172] .n-checkbox:focus:not(:active) .n-checkbox-box .n-checkbox-box__border {
    border: 1px solid #0f7fff !important;
    box-shadow: 0 0 0 2px #0f7fff4d !important
}

.reflection-toggle[data-v-57261172] .n-checkbox.n-checkbox--checked {
    --n-box-color-checked: #0f7fff !important;
    --n-check-mark-color: #fff !important;
    --n-border-checked: 1px solid #0f7fff !important;
    --n-color-checked: #0f7fff !important
}

.task-status[data-v-57261172] {
    color: #666;
    font-size: 12px;
    margin-top: 8px
}

.status-complete[data-v-57261172] {
    color: #4caf50
}

.status-pending[data-v-57261172] {
    color: #2196f3
}

.models-list .model .right input[type=radio][data-v-57261172] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    cursor: pointer;
    height: 16px;
    margin: 0;
    position: relative;
    width: 16px
}

.models-list .model .right input[type=radio][data-v-57261172]:checked {
    background-color: #fff;
    border-color: #0f7fff
}

.models-list .model .right input[type=radio][data-v-57261172]:checked:after {
    background: #0f7fff;
    border-radius: 50%;
    content: "";
    height: 10px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 10px
}

@media (prefers-color-scheme:dark) {
    .models-list .model .right input[type=radio][data-v-57261172] {
        background-color: #fff;
        border-color: #666
    }
    .models-list .model .right input[type=radio][data-v-57261172]:checked {
        background-color: #fff;
        border-color: #0f7fff
    }
}

.models-wrapper .models-selected[data-v-57261172] {
    background-color: #f4f4f4;
    border: none;
    border-radius: 16.5px
}

.models-wrapper .models-selected[data-v-57261172]:hover {
    background-color: #e8e8e8
}

@media (prefers-color-scheme:dark) {
    .aspect-ratio-selector .models-selected[data-v-57261172] {
        background-color: #333;
        border-color: #444
    }
    .aspect-ratio-selector .models-selected[data-v-57261172]:hover {
        background-color: #444
    }
    .conversation-item-desc.assistant .bubble.try_moa[data-v-57261172] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .try_moa .button[data-v-57261172] {
        background: #fafafa;
        color: #232425
    }
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.try_moa[data-v-57261172] {
        flex-direction: column
    }
    .chat-wrapper[data-v-57261172] {
        box-sizing: border-box;
        padding: 16px 16px 100px
    }
    .input-wrapper-wrapper[data-v-57261172] {
        bottom: 0;
        box-sizing: border-box;
        left: 0;
        padding: 16px 16px 40px;
        position: fixed;
        width: 100%
    }
    .input-wrapper[data-v-57261172],
    .input-wrapper-wrapper-inner[data-v-57261172] {
        width: 100%
    }
    .options-wrapper[data-v-57261172] {
        flex-wrap: wrap;
        gap: 8px
    }
    [data-v-57261172] .moa-image-side-pannel {
        bottom: auto;
        left: auto;
        max-height: 200px;
        transform: none;
        width: 100%
    }
}

.task-content[data-v-57261172] {
    margin: 0
}

.task-content[data-v-57261172] h3 {
    color: #232425;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    margin: 12px 0
}

.prompt-box[data-v-57261172] {
    border-radius: 4px;
    color: #909499;
    font-size: 16px;
    line-height: 1.5;
    margin: 12px 0;
    padding: 12px 16px
}

.prompt-box-title[data-v-57261172] {
    font-weight: 700;
    margin-bottom: 12px
}

.task-box-divider[data-v-57261172] {
    background-color: #efefef;
    height: 1px;
    margin: 40px 0
}

@media (prefers-color-scheme:dark) {
    .task-content[data-v-57261172] h3 {
        color: #fff
    }
    .prompt-box[data-v-57261172] {
        background-color: #333;
        border-left-color: #0f7fff;
        color: #ddd
    }
}

.llm-model-toggle[data-v-57261172] {
    align-items: center;
    background-color: #fff;
    border-radius: 8px;
    box-sizing: border-box;
    display: flex;
    height: 28px
}

@media (prefers-color-scheme:dark) {
    .llm-model-toggle[data-v-57261172] {
        background-color: #333
    }
    .llm-model-toggle[data-v-57261172] .n-select {
        --n-color: #333;
        --n-text-color: #ddd
    }
}

.style-cards[data-v-57261172] {
    background: #fff;
    border-radius: 12px;
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(3, 1fr);
    padding: 16px
}

.style-card[data-v-57261172] {
    align-items: center;
    border: 2px solid transparent;
    border-radius: 10px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    transition: all .2s ease
}

.style-card[data-v-57261172]:hover {
    background: #ffffff1a;
    transform: scale(1.05)
}

.style-card.active[data-v-57261172] {
    background: #0f7fff1a;
    border-color: #0f7fff
}

.style-image[data-v-57261172] {
    background: #333;
    border-radius: 8px;
    height: 101px;
    overflow: hidden;
    position: relative;
    width: 72px
}

.style-image[data-v-57261172]:after {
    background: linear-gradient(180deg, transparent, transparent 50%, rgba(0, 0, 0, .6));
    bottom: 0;
    content: "";
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 0
}

.style-image img[data-v-57261172] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.placeholder-image[data-v-57261172] {
    align-items: center;
    color: #666;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%
}

.style-label[data-v-57261172] {
    bottom: 4px;
    color: #fff;
    font-size: 12px;
    left: 0;
    line-height: 1.5;
    padding: 0 4px;
    position: absolute;
    right: 0;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .style-cards[data-v-57261172] {
        background: #232425
    }
    .style-card[data-v-57261172]:hover {
        background: #ffffff1a
    }
}

.model-preference-tip[data-v-57261172] {
    align-items: center;
    background: #0f7fff0d;
    border-radius: 8px;
    color: #666;
    display: flex;
    font-size: 14px;
    gap: 8px;
    line-height: 1.4;
    margin: 20px 0;
    padding: 12px
}

.tip-icon[data-v-57261172] {
    align-items: center;
    color: #0f7fff;
    display: flex;
    flex-shrink: 0;
    height: 16px;
    width: 16px
}

.tip-text[data-v-57261172] {
    flex: 1
}

@media (prefers-color-scheme:dark) {
    .model-preference-tip[data-v-57261172] {
        background: #0f7fff1a;
        color: #999
    }
}

.conversation-item-desc.assistant .bubble.try_moa .left[data-v-57261172] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.conversation-item-desc.assistant .bubble.try_moa .left .main-text[data-v-57261172] {
    font-size: 16px;
    line-height: 1.5em
}

.conversation-item-desc.assistant .bubble.try_moa .left .sub-text[data-v-57261172] {
    color: #666;
    font-size: 14px;
    line-height: 1.4em
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc.assistant .bubble.try_moa .left .sub-text[data-v-57261172] {
        color: #999
    }
}

.slide-left-enter-active[data-v-57261172],
.slide-left-leave-active[data-v-57261172],
.slide-right-enter-active[data-v-57261172],
.slide-right-leave-active[data-v-57261172] {
    transition: transform .3s ease
}

.slide-left-enter-from[data-v-57261172] {
    transform: translate(100%)
}

.slide-left-leave-to[data-v-57261172],
.slide-right-enter-from[data-v-57261172] {
    transform: translate(-100%)
}

.slide-right-leave-to[data-v-57261172] {
    transform: translate(100%)
}

[data-v-57261172] .moa-image-detail {
    overflow: hidden;
    position: relative
}

[data-v-57261172] .moa-image-detail img {
    transition: transform .3s ease;
    will-change: transform
}

[data-v-57261172] .moa-image-detail {
    touch-action: pan-y pinch-zoom;
    -moz-user-select: none;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none
}

[data-v-57261172] .moa-image-detail img {
    pointer-events: none
}

.slide-left-enter-active[data-v-57261172],
.slide-left-leave-active[data-v-57261172],
.slide-right-enter-active[data-v-57261172],
.slide-right-leave-active[data-v-57261172] {
    height: 100%;
    position: absolute;
    transition: transform .3s cubic-bezier(.4, 0, .2, 1);
    width: 100%
}

.input-wrapper-wrapper-inner[data-v-57261172] {
    margin: 0 auto;
    max-width: 100%;
    position: relative;
    width: var(--container-width, 680px)
}

.cropper-dialog[data-v-57261172],
.media-gallery-container[data-v-57261172] {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 10px #00000026;
    margin-bottom: 16px;
    overflow-y: auto;
    position: absolute;
    width: 100%
}

.cropper-dialog[data-v-57261172] {
    box-sizing: border-box;
    padding: 12px 20px
}

.crop-footer[data-v-57261172] {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 16px
}

.cropper-dialog button[data-v-57261172] {
    background: #232425;
    border-radius: 12px;
    color: #fff;
    cursor: pointer;
    font-size: 14px;
    font-weight: 700;
    height: 40px;
    padding: 0 8px;
    width: 201px
}

.images-row[data-v-57261172] {
    align-items: flex-end;
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
    overflow-x: auto;
    padding-bottom: 4px;
    width: 100%
}

.image-item[data-v-57261172] {
    flex: 0 0 auto;
    max-width: 40%;
    position: relative
}

.image-item img[data-v-57261172] {
    border-radius: 8px;
    height: auto;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.remix-dropdown-container[data-v-57261172] {
    display: inline-block;
    position: relative
}

.remix-dropdown-content[data-v-57261172] {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 10px #00000026;
    left: 0;
    min-width: 200px;
    position: absolute;
    top: calc(100% + 8px);
    z-index: 1000
}

@media (prefers-color-scheme:dark) {
    .cropper-dialog[data-v-57261172],
    .media-gallery-container[data-v-57261172] {
        background-color: #2a2a2a;
        border-color: #333
    }
    .remix-dropdown-content[data-v-57261172] {
        background: #252525
    }
    .remix-selector[data-v-57261172] {
        border: 1px solid #efefef30;
        color: #fff
    }
    .remix-selector[data-v-57261172]:hover {
        background-color: #f0f0f010
    }
}

.image-cropper-container[data-v-57261172] {
    align-items: center;
    background-color: #f5f5f5;
    display: flex;
    height: 420px;
    justify-content: center;
    width: 100%
}

.cropper[data-v-57261172] {
    background: #000;
    height: 100%;
    width: 100%
}

.cropper-ratio-hint[data-v-57261172] {
    color: #0009;
    margin-top: 10px;
    text-align: center
}

.dialog-footer[data-v-57261172] {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 16px
}

.ratio-controls[data-v-57261172] {
    display: flex;
    gap: 15px;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.tool-button[data-v-57261172] {
    background: #f5f5f5;
    border: none;
    border-radius: 12px;
    color: #000;
    cursor: pointer;
    flex-shrink: 0;
    font-size: 14px;
    height: 30px;
    padding: 4px 20px 4px 40px;
    position: relative;
    transition: all .3s
}

.tool-button[data-v-57261172]:hover {
    background: #0000001a
}

.tool-button[data-v-57261172]:before {
    border: 1px solid;
    border-radius: 2px;
    content: "";
    height: 16px;
    left: 12px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 16px
}

.tool-button[data-ratio="1:1"][data-v-57261172]:before {
    height: 12px;
    width: 12px
}

.tool-button[data-ratio="4:3"][data-v-57261172]:before {
    height: 12px;
    width: 16px
}

.tool-button[data-ratio="16:9"][data-v-57261172]:before {
    height: 9px;
    width: 16px
}

.tool-button[data-ratio="9:16"][data-v-57261172]:before {
    height: 16px;
    width: 9px
}

.tool-button[data-ratio=original][data-v-57261172]:before {
    border-style: dashed;
    height: 14px;
    width: 14px
}

.models-list .model .left .text[data-v-57261172] {
    flex-direction: row
}

.models-list .model .left .text[data-v-57261172],
.plus-icon[data-v-57261172] {
    align-items: center;
    display: flex;
    gap: 10px;
    justify-content: center
}

.plus-icon[data-v-57261172] {
    background: #0f7fff;
    border-radius: 57px;
    box-sizing: border-box;
    color: #fff;
    flex-shrink: 0;
    font-family: Arial;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    height: 14px;
    line-height: 150%;
    opacity: 0;
    padding: 0 8px;
    text-align: center
}

.plus-icon-paid[data-v-57261172] {
    opacity: 1
}

.plus-icon-hidden[data-v-57261172] {
    display: none
}

.chat-progress-bar[data-v-c02f45a1] {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 10px #00000014;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 30px 18px;
    position: relative
}

@media (prefers-color-scheme:dark) {
    .chat-progress-bar[data-v-c02f45a1] {
        background: #1a1a1a;
        box-shadow: 0 2px 10px #00000014
    }
}

.chat-progress-bar-step[data-v-c02f45a1] {
    align-items: center;
    color: #909499;
    display: flex;
    flex-direction: column;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    justify-content: center;
    line-height: normal;
    text-align: center;
    z-index: 2
}

.chat-progress-bar-step.doing[data-v-c02f45a1] {
    color: #000
}

@media (prefers-color-scheme:dark) {
    .chat-progress-bar-step.doing[data-v-c02f45a1] {
        color: #fff
    }
}

.chat-progress-bar-step.finished[data-v-c02f45a1] {
    color: #0f7fff
}

.chat-progress-bar-step .icon-wrapper[data-v-c02f45a1] {
    padding: 8px
}

.chat-progress-bar-step .icon[data-v-c02f45a1] {
    align-items: center;
    background: #efefef;
    border-radius: 50%;
    box-sizing: border-box;
    display: flex;
    height: 20px;
    justify-content: center;
    width: 20px
}

.chat-progress-bar-step.doing .icon[data-v-c02f45a1] {
    background: #0f7fff;
    color: #fff
}

.chat-progress-bar-step .icon.finished[data-v-c02f45a1] {
    background: none
}

.chat-progress-bar-line[data-v-c02f45a1] {
    background: #efefef;
    box-sizing: border-box;
    height: 1px;
    left: 50%;
    position: absolute;
    top: 28px;
    transform: translate(-50%, -50%);
    width: calc(100% - 110px);
    z-index: 1
}

.chat-progress-bar-step.doing .chat-progress-bar-step-label[data-v-c02f45a1] {
    display: inline;
    position: relative
}

.chat-progress-bar-step.doing .chat-progress-bar-step-label[data-v-c02f45a1]:before {
    animation: shine-c02f45a1 2s linear infinite;
    background: linear-gradient(90deg, #fff0, #fffc 50%, #fff0);
    bottom: 0;
    content: "";
    left: 10px;
    -webkit-mask: content-box;
    mask: content-box;
    position: absolute;
    right: 0;
    top: 0;
    width: 20px
}

@media (prefers-color-scheme:dark) {
    .chat-progress-bar-step.doing .chat-progress-bar-step-label[data-v-c02f45a1]:before {
        background: linear-gradient(90deg, #1a1a1a00, #1a1a1acc 50%, #1a1a1a00)
    }
}

@keyframes shine-c02f45a1 {
    0% {
        transform: translate(-100%)
    }
    50% {
        transform: translate(200%)
    }
    to {
        transform: translate(-100%)
    }
}

.user .content[data-v-15fa5e36] pre {
    background: none;
    margin: 0;
    padding: 0
}

.user .content[data-v-15fa5e36] pre code {
    font-family: sans-serif;
    white-space: pre-wrap
}

.conversation-content[data-v-15fa5e36] {
    background: transparent;
    color: #666;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding-bottom: 180px;
    position: relative
}

.conversation .role[data-v-15fa5e36] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.conversation .avatar[data-v-15fa5e36] {
    height: 36px;
    width: 36px
}

.conversation .avatar img[data-v-15fa5e36] {
    width: 100%
}

.conversation .name[data-v-15fa5e36] {
    color: #252525;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal
}

.conversation-item-desc .bubble[data-v-15fa5e36] {
    align-items: flex-start;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    justify-content: flex-start;
    line-height: 20px;
    padding: 12px 16px
}

.conversation-item-desc .bubble code[data-v-15fa5e36],
.conversation-item-desc .bubble pre[data-v-15fa5e36] {
    font-size: 16px
}

.conversation-item-desc .button[data-v-15fa5e36] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 2px;
    line-height: normal;
    padding: 4px 8px
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc .button[data-v-15fa5e36] {
        border-color: #252525;
        color: #ddd
    }
}

.conversation-item-desc .button .icon[data-v-15fa5e36] {
    display: flex
}

@keyframes blink-animation-15fa5e36 {
    0%,
    to {
        color: #000
    }
    50% {
        color: transparent
    }
}

@media (prefers-color-scheme:dark) {
    @keyframes blink-animation-15fa5e36 {
        0%,
        to {
            color: #fff
        }
        50% {
            color: transparent
        }
    }
}

.conversation-item-desc.assistant .bubble[data-v-15fa5e36] .cursor {
    animation: blink-animation-15fa5e36 .5s infinite;
    color: #606366;
    font-size: 12px
}

.conversation-item-desc[data-v-15fa5e36] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-width: 260px;
    width: 100%
}

.conversation-item-desc.user[data-v-15fa5e36] {
    align-items: flex-end;
    margin-left: 30px
}

.conversation-item-desc.assistant .bubble[data-v-15fa5e36] {
    border: 1px solid transparent;
    border-radius: 16px;
    box-sizing: border-box;
    min-width: 260px;
    padding-left: 0;
    padding-right: 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.retry[data-v-15fa5e36],
.conversation-item-desc.assistant .bubble.try_moa[data-v-15fa5e36] {
    align-items: center;
    background: #fafafa;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 18px 16px
}

.conversation-item-desc .retry .button[data-v-15fa5e36],
.conversation-item-desc .try_moa .button[data-v-15fa5e36] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 8px 16px
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.retry[data-v-15fa5e36],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-15fa5e36] {
        flex-direction: column
    }
}

.models-list[data-v-15fa5e36] {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px 8px
}

.models-list[data-v-15fa5e36],
.models-list .model[data-v-15fa5e36] {
    display: flex;
    flex-direction: column
}

.models-list .model[data-v-15fa5e36] {
    color: #232425;
    cursor: pointer;
    margin-top: 4px;
    padding: 10px 6px 10px 10px
}

.models-list .model .row[data-v-15fa5e36] {
    border-radius: 8px;
    color: #232425;
    gap: 4px;
    justify-content: space-between
}

.models-list .model .row[data-v-15fa5e36],
.models-selected[data-v-15fa5e36] {
    display: flex;
    flex-direction: row;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.models-selected[data-v-15fa5e36] {
    align-items: center;
    background-color: #f4f4f4;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 150%;
    padding: 6px 12px
}

@media (prefers-color-scheme:dark) {
    .models-list[data-v-15fa5e36] {
        background-color: #252525
    }
    .models-list .model .row[data-v-15fa5e36] {
        color: #ddd
    }
    .conversation-item-desc.assistant .bubble[data-v-15fa5e36] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble.retry[data-v-15fa5e36],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-15fa5e36] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .retry .button[data-v-15fa5e36],
    .conversation-item-desc .try_moa .button[data-v-15fa5e36] {
        background: #fafafa;
        color: #232425
    }
}

.controls[data-v-15fa5e36] {
    gap: 8px;
    margin-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden
}

.controls[data-v-15fa5e36]::-webkit-scrollbar {
    display: none
}

.plan_bottom_wrapper[data-v-15fa5e36] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between
}

@media (max-width:768px) {
    .plan_bottom_wrapper[data-v-15fa5e36] {
        align-items: flex-start;
        flex-direction: column;
        gap: 10px;
        justify-content: flex-start
    }
}

.refresh_button_wrapper[data-v-15fa5e36] {
    justify-content: center;
    width: 100%
}

.refresh_button[data-v-15fa5e36],
.refresh_button_wrapper[data-v-15fa5e36] {
    align-items: center;
    display: flex;
    z-index: 5
}

.refresh_button[data-v-15fa5e36] {
    background: #fff;
    border-radius: 28px;
    box-shadow: 0 2px 10px #00000014;
    color: #475666;
    cursor: pointer;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    left: 50%;
    line-height: 18px;
    padding: 6px 16px;
    position: absolute;
    top: calc(100vh - 200px);
    transform: translate(-50%)
}

.refresh_button .icon[data-v-15fa5e36] {
    color: #0f7fff;
    height: 16px;
    width: 16px
}

@media (prefers-color-scheme:dark) {
    .refresh_button[data-v-15fa5e36] {
        background: #0f7fff;
        color: #fff
    }
    .refresh_button .icon[data-v-15fa5e36] {
        color: #fff
    }
}

.progress-bar-wrapper[data-v-15fa5e36] {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: sticky;
    top: 0;
    width: 100%;
    width: var(--container-width, 680px);
    z-index: 10
}

@media (max-width:768px) {
    .progress-bar-wrapper[data-v-15fa5e36] {
        width: 100%
    }
}

.research_plan_title[data-v-15fa5e36] {
    color: #232425;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin-bottom: 8px
}

@media (prefers-color-scheme:dark) {
    .research_plan_title[data-v-15fa5e36] {
        color: #eee
    }
}

.research_plan[data-v-15fa5e36] {
    background-color: #fff;
    border: 1px solid #efefef;
    border-radius: 12px;
    padding: 16px
}

@media (max-width:768px) {
    .research_plan[data-v-15fa5e36] {
        padding: 8px
    }
}

@media (prefers-color-scheme:dark) {
    .research_plan[data-v-15fa5e36] {
        background-color: #232425;
        border-color: #333
    }
}

.start_buttons[data-v-15fa5e36] {
    display: flex;
    flex-direction: row;
    font-size: 14px;
    gap: 8px;
    justify-content: flex-end
}

.start_buttons .start_button[data-v-15fa5e36] {
    background-color: #232425;
    border: 1px solid #232425;
    border-radius: 24px;
    color: #fff
}

.start_buttons .edit_button[data-v-15fa5e36],
.start_buttons .start_button[data-v-15fa5e36] {
    cursor: pointer;
    display: flex;
    flex-direction: row;
    gap: 8px;
    padding: 8px 16px
}

.start_buttons .edit_button[data-v-15fa5e36] {
    background-color: #f4f4f4;
    border-radius: 24px;
    color: #232425
}

@media (hover:hover) {
    .start_buttons .start_button[data-v-15fa5e36]:hover {
        background-color: #333;
        border-color: #333
    }
}

@media (prefers-color-scheme:dark) {
    .start_buttons .start_button[data-v-15fa5e36] {
        background-color: #ddd;
        border-color: #ddd;
        color: #232425
    }
    .start_buttons .edit_button[data-v-15fa5e36] {
        background-color: #333;
        color: #fff
    }
    @media (hover:hover) {
        .start_buttons .start_button[data-v-15fa5e36]:hover {
            background-color: #fff;
            border-color: #fff
        }
    }
}

[data-v-15fa5e36] .thinking_prompt {
    color: #666;
    display: inline;
    position: relative
}

[data-v-15fa5e36] .thinking_prompt:before {
    animation: shine-15fa5e36 2s linear infinite;
    background: linear-gradient(90deg, #fff0, #fffc 50%, #fff0);
    bottom: 0;
    content: "";
    left: 10px;
    -webkit-mask: content-box;
    mask: content-box;
    position: absolute;
    right: 0;
    top: 0;
    width: 20px
}

@keyframes shine-15fa5e36 {
    0% {
        transform: translate(-100%)
    }
    50% {
        transform: translate(200%)
    }
    to {
        transform: translate(-100%)
    }
}

@media (prefers-color-scheme:dark) {
    [data-v-15fa5e36] .thinking_prompt {
        color: #999
    }
    [data-v-15fa5e36] .thinking_prompt:before {
        background: linear-gradient(90deg, #fff0, #ffffff4d 50%, #fff0)
    }
}

.to-bottom-icon[data-v-15fa5e36] {
    cursor: pointer;
    left: 50%;
    position: absolute;
    top: -35px;
    transform: translate(-50%)
}

.empty-placeholder[data-v-15fa5e36] {
    color: #232425;
    font-family: Arial;
    font-size: 30px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin-top: 30vh;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .empty-placeholder[data-v-15fa5e36] {
        color: #fff
    }
}

.file-wrapper[data-v-15fa5e36] {
    align-items: center;
    background: #fafafa;
    border: 1px solid #efefef;
    border-radius: 12px;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    gap: 8px;
    height: 48px;
    justify-content: flex-start;
    width: 152px
}

.file-wrapper .file-icon[data-v-15fa5e36] {
    background-color: #fff;
    border-radius: 8px;
    box-sizing: border-box;
    flex-shrink: 0;
    height: 36px;
    padding: 6px;
    width: 36px
}

.file-wrapper .file-icon[data-v-15fa5e36] svg {
    height: 100%;
    width: 100%
}

.file-wrapper .file-info[data-v-15fa5e36] {
    flex-grow: 1;
    max-width: 100px
}

.file-wrapper .file-info .file-name[data-v-15fa5e36] {
    color: #232425;
    display: block;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

.file-wrapper .file-info .file-size[data-v-15fa5e36] {
    color: #909499;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    width: 100%
}

.model-response .title[data-v-15fa5e36] {
    cursor: pointer
}

.model-response .title .status[data-v-15fa5e36] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    width: 28px
}

.model-response .title .status .icon.finished[data-v-15fa5e36] {
    color: #5cd4a1
}

.model-response .title .status .icon[data-v-15fa5e36] svg {
    height: 100%;
    width: 100%
}

.moa-title[data-v-15fa5e36] {
    color: #ccc;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin: 24px 0 8px
}

.assistant-message-divider[data-v-15fa5e36] {
    background-color: #efefef;
    height: 1px;
    margin: 50px 0;
    width: 100%
}

.private-file-wrappers[data-v-15fa5e36] {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px
}

.private-file-wrapper[data-v-15fa5e36] {
    background-color: #fafafa;
    border-radius: 8px;
    color: #606366;
    padding: 4px 8px;
    width: -moz-fit-content;
    width: fit-content
}

.model-response-wrapper .title[data-v-15fa5e36],
.private-file-wrapper[data-v-15fa5e36] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 4px
}

.model-response-wrapper .title .icon[data-v-15fa5e36] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    margin-right: 5px;
    width: 16px
}

.model-response-wrapper .status .icon[data-v-15fa5e36] {
    margin-right: 0
}

.model-response-wrapper .title .icon img[data-v-15fa5e36] {
    height: 100%;
    width: 100%
}

.upload-attachments.disabled[data-v-15fa5e36] {
    cursor: not-allowed;
    opacity: .5
}

.upload-attachments[data-v-15fa5e36] {
    flex-shrink: 0
}

.upload-attachments .icon[data-v-15fa5e36] {
    align-items: center;
    color: #232425;
    cursor: pointer;
    display: flex;
    height: 20px;
    justify-content: center;
    width: 20px
}

.models-selected .icon.active[data-v-15fa5e36] {
    transform: rotate(180deg)
}

.models-list .model[data-v-15fa5e36]:first-child {
    margin-top: 0
}

.models-list .divider[data-v-15fa5e36] {
    background-color: #efefef;
    height: 1px;
    margin-top: 10px;
    width: 100%
}

.models-list .divider+.model[data-v-15fa5e36] {
    margin-top: 10px
}

@media (prefers-color-scheme:dark) {
    .models-list .divider[data-v-15fa5e36] {
        background-color: #333
    }
}

.models-list .model .description[data-v-15fa5e36] {
    color: #909499;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px
}

.models-list .model.disabled[data-v-15fa5e36] {
    cursor: not-allowed;
    opacity: .5;
    pointer-events: none
}

@media (hover:hover) {
    .models-list .model[data-v-15fa5e36]:hover {
        background-color: #f5f5f5;
        border-radius: 12px
    }
}

.models-list .model .left[data-v-15fa5e36] {
    display: flex;
    flex-direction: row;
    gap: 8px
}

.models-list .model .left .icon[data-v-15fa5e36] {
    height: 20px;
    width: 20px
}

.models-list .model .left .icon img[data-v-15fa5e36] {
    height: 100%;
    width: 100%
}

.models-list .model .right[data-v-15fa5e36] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-end
}

.models-list .model .right input[type=radio][data-v-15fa5e36] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    cursor: pointer;
    height: 16px;
    margin: 0;
    position: relative;
    width: 16px
}

.models-list .model .right input[type=radio][data-v-15fa5e36]:checked {
    background-color: #fff;
    border-color: #0f7fff
}

.models-list .model .right input[type=radio][data-v-15fa5e36]:checked:after {
    background: #0f7fff;
    border-radius: 50%;
    content: "";
    height: 10px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 10px
}

@media (prefers-color-scheme:dark) {
    .models-list .model .right input[type=radio][data-v-15fa5e36] {
        background-color: #fff;
        border-color: #666
    }
    .models-list .model .right input[type=radio][data-v-15fa5e36]:checked {
        background-color: #fff;
        border-color: #0f7fff
    }
}

.models-wrapper[data-v-15fa5e36] {
    flex-shrink: 0;
    width: -moz-fit-content;
    width: fit-content
}

.models-selected .model-selected[data-v-15fa5e36] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.models-selected .model-selected .icon[data-v-15fa5e36] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 16px;
    justify-content: center;
    width: 16px
}

.models-selected .model-selected .icon img[data-v-15fa5e36] {
    height: 100%;
    width: 100%
}

.expand-button[data-v-15fa5e36] {
    transform: rotate(180deg)
}

.expanded .expand-button[data-v-15fa5e36] {
    transform: rotate(0)
}

.model-response-wrapper[data-v-15fa5e36] {
    position: relative
}

.content[data-v-15fa5e36] pre {
    background-color: #fafafa;
    border-radius: 10px;
    padding: 10px
}

.model-response-wrapper .content[data-v-15fa5e36] {
    transition: all .3s ease-in-out
}

.model-response-wrapper.thinking .content.no-expand[data-v-15fa5e36] {
    max-height: 90px
}

.model-response-wrapper .content.no-expand[data-v-15fa5e36] {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    line-height: 30px;
    max-height: 0;
    overflow: hidden
}

.model-response-wrapper.expanded .content[data-v-15fa5e36] {
    max-height: unset;
    padding-top: 20px
}

.model-response-wrapper .expand-button[data-v-15fa5e36] {
    cursor: pointer;
    height: 20px;
    position: absolute;
    right: 10px;
    top: 10px;
    width: 20px
}

.model-response .divider[data-v-15fa5e36] {
    background-color: #efefef;
    height: 1px;
    margin-bottom: 20px
}

.model-response[data-v-15fa5e36] {
    color: #475666;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    width: 100%
}

.conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-15fa5e36] {
    border: 1px solid #efefef
}

.general-chat-wrapper[data-v-15fa5e36] {
    height: 100%;
    justify-content: space-between;
    position: relative
}

.chat-wrapper[data-v-15fa5e36],
.general-chat-wrapper[data-v-15fa5e36] {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    width: 100%
}

.chat-wrapper[data-v-15fa5e36] {
    align-items: center;
    justify-content: flex-start;
    min-height: 300px;
    overflow-x: hidden;
    overflow-y: auto;
    padding-top: 10px
}

.conversation-wrapper[data-v-15fa5e36] {
    background-color: transparent;
    display: flex;
    flex-direction: column;
    min-height: 200px;
    padding-bottom: 100px;
    position: relative;
    width: var(--container-width, 680px)
}

@media (max-width:768px) {
    .conversation-wrapper[data-v-15fa5e36] {
        width: 100%
    }
}

.input-wrapper-wrapper[data-v-15fa5e36] {
    align-items: center;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-bottom: 4px;
    padding-top: 16px;
    position: relative;
    width: 100%
}

.input-wrapper[data-v-15fa5e36] {
    width: var(--container-width, 680px)
}

.conversation-item-desc.user .bubble[data-v-15fa5e36] {
    background-color: #f5f5f5;
    border-radius: 16px;
    color: #232425;
    margin-top: 12px;
    max-width: 70%
}

.conversation-item-desc.user .bubble[data-v-15fa5e36]:not(:first-child) {
    margin-top: 12px
}

.conversation-item-desc.user .bubble.private_file[data-v-15fa5e36] {
    padding: 0
}

.conversation-item-desc.user .bubble.image_url[data-v-15fa5e36] {
    max-width: 40%;
    padding: 0
}

.conversation-item-desc.user .bubble.image_url .image_url_wrapper[data-v-15fa5e36] {
    border-radius: 16px;
    box-sizing: border-box;
    display: flex;
    overflow: hidden;
    padding: 0
}

.conversation-item-desc.user .bubble.image_url .image_url_wrapper img[data-v-15fa5e36] {
    height: 100%;
    width: 100%
}

.conversation-item-desc.assistant .bubble[data-v-15fa5e36]:not(:first-child) {
    margin-top: 12px
}

.conversation-item-desc .bubble .loading[data-v-15fa5e36],
.conversation-item-desc .bubble[data-v-15fa5e36] .cursor {
    display: none
}

.conversation-item-desc.assistant .bubble.thinking[data-v-15fa5e36] .cursor {
    display: inline
}

.conversation-item-desc .desc[data-v-15fa5e36] {
    display: flex;
    flex-direction: column;
    max-width: 100%
}

.conversation-item-desc .content[data-v-15fa5e36] {
    max-width: 100%
}

.conversation-statement[data-v-15fa5e36] {
    display: flex;
    flex-direction: column;
    list-style: none;
    margin: 10px 0
}

.conversation-statement.user[data-v-15fa5e36] {
    align-items: flex-end
}

.conversation-statement.assistant+.conversation-statement.user[data-v-15fa5e36],
.conversation-statement.user+.conversation-statement.assistant[data-v-15fa5e36] {
    margin-top: 16px
}

.conversation-statement[data-v-15fa5e36]:first-child {
    margin-top: 5px
}

@media (max-width:1220px) {
    .chat-wrapper[data-v-15fa5e36] {
        box-sizing: border-box;
        overflow-y: hidden;
        padding: 16px 16px 100px
    }
    .input-wrapper-wrapper[data-v-15fa5e36] {
        bottom: 0;
        box-sizing: border-box;
        left: 0;
        padding: 16px 16px 40px;
        position: fixed;
        width: 100%
    }
    .input-wrapper[data-v-15fa5e36],
    .input-wrapper-wrapper-inner[data-v-15fa5e36] {
        width: 100%
    }
}

@media (prefers-color-scheme:dark) {
    .content[data-v-15fa5e36] pre {
        background-color: #222
    }
    .input-wrapper-wrapper[data-v-15fa5e36] {
        background-color: #232425
    }
    .assistant-message-divider[data-v-15fa5e36] {
        background-color: #efefef30
    }
    .private-file-wrapper[data-v-15fa5e36] {
        background-color: #333;
        color: #ddd
    }
    .upload-attachments .icon[data-v-15fa5e36] {
        color: #ddd
    }
    @media (hover:hover) {
        .models-list .model[data-v-15fa5e36]:hover {
            background-color: #333
        }
    }
    .conversation-item-desc.user .bubble[data-v-15fa5e36] {
        background: #eee;
        color: #000
    }
    .conversation-item-desc.assistant .bubble.model-response-wrapper[data-v-15fa5e36] {
        background-color: #444
    }
    .model-response[data-v-15fa5e36] {
        color: #ddd
    }
}

.controls[data-v-15fa5e36] {
    flex-direction: row;
    gap: 12px
}

.checkbox-label[data-v-15fa5e36],
.controls[data-v-15fa5e36] {
    align-items: center;
    display: flex
}

.checkbox-label[data-v-15fa5e36] {
    color: #232425;
    cursor: pointer;
    font-size: 14px;
    gap: 6px
}

.checkbox-label input[type=checkbox][data-v-15fa5e36] {
    cursor: pointer;
    height: 16px;
    width: 16px
}

.checkbox-label input[type=checkbox][data-v-15fa5e36]:disabled {
    cursor: not-allowed;
    opacity: .5
}

@media (prefers-color-scheme:dark) {
    .checkbox-label[data-v-15fa5e36] {
        color: #ddd
    }
}

.mobile-sidebar-page[data-v-15fa5e36] {
    background: #fff;
    height: 100%;
    left: 0;
    overflow-y: auto;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000
}

.slide-enter-active[data-v-15fa5e36],
.slide-leave-active[data-v-15fa5e36] {
    transition: transform .3s ease
}

.slide-enter-from[data-v-15fa5e36],
.slide-leave-to[data-v-15fa5e36] {
    transform: translate(100%)
}

.slide-enter-to[data-v-15fa5e36],
.slide-leave-from[data-v-15fa5e36] {
    transform: translate(0)
}

@media (prefers-color-scheme:dark) {
    .mobile-sidebar-page[data-v-15fa5e36] {
        background: #1a1a1a
    }
}

.search-source-sidebar.desktop[data-v-15fa5e36] {
    background: #fff;
    box-shadow: -2px 0 8px #00000026;
    height: 100vh;
    position: fixed;
    right: 0;
    top: 0;
    width: 400px;
    z-index: 100
}

.search-source-sidebar.desktop[data-v-15fa5e36] .sidebar-content {
    height: 100%;
    overflow-y: auto
}

.slide-desktop-enter-active[data-v-15fa5e36],
.slide-desktop-leave-active[data-v-15fa5e36] {
    transition: transform .3s ease
}

.slide-desktop-enter-from[data-v-15fa5e36],
.slide-desktop-leave-to[data-v-15fa5e36] {
    transform: translate(100%)
}

.slide-desktop-enter-to[data-v-15fa5e36],
.slide-desktop-leave-from[data-v-15fa5e36] {
    transform: translate(0)
}

@media (prefers-color-scheme:dark) {
    .search-source-sidebar.desktop[data-v-15fa5e36] {
        background: #1a1a1a;
        box-shadow: -2px 0 8px #0000004d
    }
}

.web-knowledge-toggle[data-v-15fa5e36] {
    align-items: center;
    background-color: transparent;
    border: 1px solid #efefef;
    border-radius: 8px;
    box-sizing: border-box;
    display: flex;
    height: 33px;
    padding: 6px 12px;
    white-space: nowrap
}

.web-knowledge-toggle[data-v-15fa5e36] .n-checkbox-box {
    height: 16px;
    width: 16px
}

.web-knowledge-toggle[data-v-15fa5e36] .n-checkbox__label {
    color: #606366;
    font-size: 14px;
    font-weight: 400;
    line-height: 16px
}

.web-knowledge-toggle[data-v-15fa5e36] .n-checkbox--show-label {
    line-height: 16px
}

.web-knowledge-toggle[data-v-15fa5e36] .n-checkbox {
    --n-box-color: #fff !important;
    --n-border: 1px solid #efefef !important;
    --n-border-checked: 1px solid #0f7fff !important;
    --n-border-focus: 1px solid #0f7fff !important;
    --n-border-disabled: 1px solid #e0e0e6 !important;
    --n-border-disabled-checked: 1px solid #e0e0e6 !important;
    --n-box-shadow-focus: 0 0 0 2px rgba(15, 127, 255, .3) !important;
    --n-color: transparent !important;
    --n-color-checked: #0f7fff !important
}

.web-knowledge-toggle[data-v-15fa5e36] .n-checkbox:hover .n-checkbox-box .n-checkbox-box__border {
    border: 1px solid #0f7fff !important
}

.web-knowledge-toggle[data-v-15fa5e36] .n-checkbox:focus:not(:active) .n-checkbox-box .n-checkbox-box__border {
    border: 1px solid #0f7fff !important;
    box-shadow: 0 0 0 2px #0f7fff4d !important
}

.web-knowledge-toggle[data-v-15fa5e36] .n-checkbox.n-checkbox--checked {
    --n-box-color-checked: #0f7fff !important;
    --n-check-mark-color: #fff !important;
    --n-border-checked: 1px solid #0f7fff !important;
    --n-color-checked: #0f7fff !important
}

@media (prefers-color-scheme:dark) {
    .web-knowledge-toggle[data-v-15fa5e36] .n-checkbox.n-checkbox--checked {
        --n-check-mark-color: #fff !important
    }
}