html {
    touch-action: manipulation
}

body .n-modal-mask {
    background-color: #000c
}

.n-popover-shared.n-popover.menu-popover-wrapper {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px #00000026;
    margin-left: -2px;
    margin-top: -21px
}

@media (prefers-color-scheme:dark) {
    .n-popover-shared.n-popover.menu-popover-wrapper {
        background-color: #2a2a2a
    }
}

.menu-items .divider[data-v-62861aa6] {
    background-color: #dedede;
    box-sizing: border-box;
    height: 1px;
    width: calc(100% - 12px)
}

.new-tag[data-v-62861aa6] {
    align-items: center;
    background: #ff3d3d;
    border-radius: 16px;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 12px;
    font-style: italic;
    font-weight: 700;
    gap: 10px;
    height: 16px;
    justify-content: center;
    line-height: normal;
    padding: 2px 6px
}

.sidebar[data-v-62861aa6] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between
}

.sidebar[data-v-62861aa6],
.sidebar-footer[data-v-62861aa6],
.sidebar-header[data-v-62861aa6] {
    width: 100%
}

.menu-items[data-v-62861aa6] {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%
}

.menu-item[data-v-62861aa6] {
    cursor: pointer
}

.menu-item .icon[data-v-62861aa6] {
    align-items: center;
    display: flex;
    justify-content: center
}

.menu-item .icon img[data-v-62861aa6] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.collapse-button[data-v-62861aa6] {
    color: #232425;
    cursor: pointer;
    height: 16px;
    width: 16px
}

.index-layout[data-v-62861aa6] {
    align-items: stretch;
    display: flex;
    flex-direction: row;
    height: 100vh;
    overflow: hidden;
    width: 100%
}

.index-layout-sidebar[data-v-62861aa6] {
    align-items: flex-start;
    background-color: #f2f2f2;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    justify-content: space-between;
    transition: width .3s ease-in-out
}

.index-layout-content[data-v-62861aa6] {
    flex-grow: 1;
    overflow-y: auto;
    position: relative
}

.header[data-v-62861aa6] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 16px 8px 0 20px
}

.logo[data-v-62861aa6] {
    cursor: pointer;
    height: 24px;
    width: 132px
}

.logo img[data-v-62861aa6] {
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100%
}

.setting[data-v-62861aa6] {
    cursor: pointer;
    height: 24px;
    width: 24px
}

.setting-wrapper[data-v-62861aa6] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 4px
}

@media (min-width:1221px) {
    .collapsed.index-layout-sidebar[data-v-62861aa6] {
        width: 64px
    }
    .menu-items.mobile[data-v-62861aa6] {
        justify-content: center;
        padding: 16px 0 60px
    }
    .icon.active[data-v-62861aa6] {
        background: #e5e5e5;
        color: #232425
    }
}

.mobile .menu-item[data-v-62861aa6] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    justify-content: center;
    line-height: normal;
    outline: none;
    text-align: center;
    -moz-user-select: none;
    user-select: none;
    width: 80px;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none
}

.mobile .menu-item[data-v-62861aa6]:active {
    background: none;
    -webkit-tap-highlight-color: transparent
}

.mobile .menu-item .icon[data-v-62861aa6] {
    height: 24px;
    width: 24px
}

.mobile .menu-item .icon[data-v-62861aa6],
.mobile .menu-item .icon img[data-v-62861aa6] {
    outline: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.mobile .menu-item .icon img[data-v-62861aa6] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.mobile .menu-item .title[data-v-62861aa6] {
    outline: none;
    position: absolute;
    top: 50px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-layout-sidebar.mobile[data-v-62861aa6] {
    display: none
}

@media (max-width:1220px) {
    .new-tag[data-v-62861aa6] {
        display: none
    }
    .index-layout[data-v-62861aa6] {
        background: #fff;
        display: flex;
        flex-direction: column-reverse;
        height: unset;
        min-height: 100vh;
        min-height: 100dvh
    }
    .index-layout-sidebar[data-v-62861aa6] {
        display: none
    }
    .index-layout-sidebar.mobile[data-v-62861aa6] {
        bottom: 0;
        display: block;
        position: relative;
        width: 100%;
        z-index: 1000
    }
    .index-layout-sidebar .sidebar-footer[data-v-62861aa6],
    .index-layout-sidebar .sidebar-header .header[data-v-62861aa6] {
        display: none
    }
    .index-layout-sidebar .menu-items[data-v-62861aa6] {
        background: #fff;
        border-top: 1px solid #efefef30;
        bottom: 0;
        flex-direction: row;
        justify-content: center;
        padding: 16px 0 45px;
        position: fixed
    }
    @media (prefers-color-scheme:dark) {
        .index-layout-sidebar .menu-items[data-v-62861aa6] {
            background: #1a1a1a
        }
        .index-layout[data-v-62861aa6] {
            background-color: #000
        }
    }
}

@media (max-width:430px) {
    .index-layout-sidebar .menu-items[data-v-62861aa6] {
        gap: 50px
    }
}

@media (max-width:390px) {
    .index-layout-sidebar .menu-items[data-v-62861aa6] {
        gap: 40px
    }
}

@media (prefers-color-scheme:dark) {
    .index-layout-content[data-v-62861aa6] {
        background-color: #1a1b1c
    }
    .collapse-button[data-v-62861aa6] {
        color: #909499
    }
    .menu-items .divider[data-v-62861aa6] {
        background-color: #dedede30
    }
    .mobile .menu-item.active .icon[data-v-62861aa6] {
        color: #000;
        filter: invert(1)
    }
    .index-layout-sidebar[data-v-62861aa6] {
        background-color: #1a1a1a;
        color: #fff
    }
    .logo[data-v-62861aa6] {
        filter: invert(1)
    }
    .menu-item[data-v-62861aa6] {
        color: #fff
    }
    .menu-item.active .icon[data-v-62861aa6] {
        color: #000;
        filter: none
    }
    .menu-item .icon.me[data-v-62861aa6] {
        filter: none
    }
    .menu-item .icon[data-v-62861aa6] {
        color: #efefef
    }
}

.invite-wrapper[data-v-62861aa6] {
    box-sizing: border-box;
    margin-bottom: 16px;
    padding: 0 12px;
    width: 100%
}

.invite-banner[data-v-62861aa6] {
    align-items: center;
    background-color: #fff;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='172' height='79' fill='none' viewBox='0 0 172 79'%3E%3Cmask id='a' width='172' height='79' x='0' y='0' maskUnits='userSpaceOnUse' style='mask-type:alpha'%3E%3Crect width='172' height='79' fill='%23fff' rx='8'/%3E%3C/mask%3E%3Cg mask='url(%23a)'%3E%3Cg filter='url(%23b)' opacity='.6'%3E%3Ccircle cx='10.843' cy='80.843' r='36.733' fill='url(%23c)' transform='rotate(-131.346 10.843 80.843)'/%3E%3C/g%3E%3Cg filter='url(%23d)'%3E%3Ccircle cx='160.739' cy='25.739' r='37.368' fill='%237FD1FF' transform='rotate(-131.346 160.739 25.739)'/%3E%3C/g%3E%3C/g%3E%3Cdefs%3E%3Cfilter id='b' width='313.467' height='313.467' x='-145.891' y='-75.891' color-interpolation-filters='sRGB' filterUnits='userSpaceOnUse'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape'/%3E%3CfeGaussianBlur result='effect1_foregroundBlur_462_2026' stdDeviation='60'/%3E%3C/filter%3E%3Cfilter id='d' width='314.737' height='314.737' x='3.37' y='-131.63' color-interpolation-filters='sRGB' filterUnits='userSpaceOnUse'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape'/%3E%3CfeGaussianBlur result='effect1_foregroundBlur_462_2026' stdDeviation='60'/%3E%3C/filter%3E%3CradialGradient id='c' cx='0' cy='0' r='1' gradientTransform='rotate(-150.992 19.3 52.243)scale(36.6119 40.8773)' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%232C10D6'/%3E%3Cstop offset='1' stop-color='%232C10D6' stop-opacity='.36'/%3E%3C/radialGradient%3E%3C/defs%3E%3C/svg%3E");
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    text-align: center
}

.invite-title[data-v-62861aa6] {
    color: #232425
}

.invite-title[data-v-62861aa6],
.invite-title-free[data-v-62861aa6] {
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

.invite-title-free[data-v-62861aa6] {
    color: #0f7fff
}

.invite-button[data-v-62861aa6] {
    align-items: center;
    background-color: #0f7fff;
    border-radius: 53px;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 120%;
    padding: 8px 16px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.invite-button .icon[data-v-62861aa6] {
    flex-shrink: 0;
    height: 16px;
    width: 16px
}

.invite-icon[data-v-62861aa6] {
    font-size: 20px
}

.collapsed .invite-banner[data-v-62861aa6] {
    background: none;
    padding: 0
}

.collapsed .invite-button[data-v-62861aa6] {
    padding: 6px
}

.collapsed .invite-title[data-v-62861aa6],
.collapsed .invite-wrapper span[data-v-62861aa6] {
    display: none
}

.current-agent-enter-active[data-v-62861aa6],
.current-agent-leave-active[data-v-62861aa6] {
    overflow: hidden;
    transition: all .3s ease-in-out
}

.current-agent-enter-from[data-v-62861aa6],
.current-agent-leave-to[data-v-62861aa6] {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px)
}

.current-agent-enter-to[data-v-62861aa6],
.current-agent-leave-from[data-v-62861aa6] {
    max-height: 100px;
    opacity: 1;
    transform: translateY(0)
}