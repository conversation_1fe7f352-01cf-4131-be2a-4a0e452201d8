.font-selector-wrapper[data-v-21596d1d] {
    display: inline-block;
    position: relative
}

.font-selector-button[data-v-21596d1d] {
    align-items: center;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-sizing: border-box;
    color: #232425;
    cursor: pointer;
    display: flex;
    gap: 8px;
    height: 28px;
    max-width: 120px;
    min-width: 120px;
    padding: 4px 8px;
    transition: all .2s ease
}

.font-selector-button.font-changed[data-v-21596d1d] {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px #1890ff33
}

@media (hover:hover) {
    .font-selector-button[data-v-21596d1d]:hover {
        background-color: #f5f5f5;
        border-color: #ddd;
        color: #232425
    }
    .font-selector-button:hover .icon[data-v-21596d1d] {
        color: #232425
    }
}

.font-selector-button[data-v-21596d1d]:active {
    background-color: #e6f4ff;
    border-color: #1677ff;
    color: #1677ff
}

.current-font[data-v-21596d1d] {
    flex: 1;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.icon[data-v-21596d1d] {
    align-items: center;
    color: #232425;
    display: flex;
    flex-shrink: 0;
    height: 12px;
    justify-content: center;
    line-height: 1;
    width: 12px
}

.icon svg[data-v-21596d1d] {
    height: 100%;
    width: 100%
}

.font-dropdown[data-v-21596d1d] {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 12px;
    box-shadow: 0 4px 12px #00000026;
    display: flex;
    flex-direction: column;
    max-height: 400px;
    max-width: 320px;
    min-width: 320px
}

.font-search[data-v-21596d1d] {
    border-bottom: 1px solid #eee;
    padding: 8px
}

.custom-input-wrapper[data-v-21596d1d] {
    align-items: center;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    display: flex;
    position: relative;
    transition: all .2s ease
}

.custom-input-wrapper[data-v-21596d1d]:focus-within {
    border-color: #4096ff;
    box-shadow: 0 0 0 2px #4096ff33
}

.search-icon[data-v-21596d1d] {
    align-items: center;
    color: #666;
    display: flex;
    flex-shrink: 0;
    font-size: 12px;
    justify-content: center;
    padding: 0 8px
}

.custom-input[data-v-21596d1d] {
    background: transparent;
    border: none;
    color: #333;
    flex: 1;
    font-size: 14px;
    min-width: 0;
    outline: none;
    padding: 3px 8px 3px 0
}

.custom-input[data-v-21596d1d]::-moz-placeholder {
    color: #bfbfbf
}

.custom-input[data-v-21596d1d]::placeholder {
    color: #bfbfbf
}

.clear-button[data-v-21596d1d] {
    align-items: center;
    background: transparent;
    border: none;
    border-radius: 50%;
    color: #bfbfbf;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    font-size: 10px;
    height: 16px;
    justify-content: center;
    line-height: 1;
    margin-right: 6px;
    transition: all .2s ease;
    width: 16px
}

.clear-button[data-v-21596d1d]:hover {
    background: #f5f5f5;
    color: #666
}

.clear-button[data-v-21596d1d]:active {
    background: #e6e6e6
}

.font-list[data-v-21596d1d] {
    flex: 1;
    max-height: 320px;
    overflow-y: auto
}

.loading-item[data-v-21596d1d] {
    color: #666;
    font-size: 12px;
    gap: 8px;
    padding: 12px
}

.font-item[data-v-21596d1d],
.loading-item[data-v-21596d1d] {
    align-items: center;
    display: flex
}

.font-item[data-v-21596d1d] {
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    justify-content: space-between;
    min-height: 44px;
    padding: 10px 12px;
    transition: background-color .2s
}

.font-item[data-v-21596d1d]:hover {
    background: #f5f5f5
}

.font-item.active[data-v-21596d1d] {
    background: #e6f3ff;
    color: #1890ff
}

.font-name[data-v-21596d1d] {
    flex: 1;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.4;
    margin-right: 12px;
    min-width: 0;
    word-break: break-word
}

.font-category[data-v-21596d1d] {
    background: #f0f0f0;
    border-radius: 10px;
    color: #999;
    flex-shrink: 0;
    font-size: 11px;
    padding: 2px 6px;
    text-transform: capitalize;
    white-space: nowrap
}

.no-results[data-v-21596d1d] {
    color: #999;
    font-size: 12px;
    padding: 20px;
    text-align: center
}

.font-count-info[data-v-21596d1d] {
    border-bottom: 1px solid #eee;
    color: #666;
    font-size: 12px;
    padding: 8px
}

.search-term[data-v-21596d1d] {
    color: #999;
    font-size: 11px
}

.loading-more-item[data-v-21596d1d] {
    align-items: center;
    color: #666;
    display: flex;
    font-size: 12px;
    justify-content: center;
    padding: 12px
}

.no-more-item[data-v-21596d1d] {
    color: #999;
    font-size: 12px;
    padding: 20px;
    text-align: center
}