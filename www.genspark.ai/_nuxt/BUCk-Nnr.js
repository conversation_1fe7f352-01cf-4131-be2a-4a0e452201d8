import {
    d as t,
    o,
    D as s,
    J as e,
    a as i,
    y as n,
    _ as a
} from "./Cf0SOiw0.js";
import {
    B as r
} from "./CAfqOhBF.js";
const p = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const d = {
        render: function(e, i) {
            return o(), t("svg", p, i[0] || (i[0] = [s('<path d="M10.0003 1.6665C5.39795 1.6665 1.66699 5.39746 1.66699 9.99984C1.66699 14.6022 5.39795 18.3332 10.0003 18.3332V18.3332C14.6027 18.3332 18.3337 14.6022 18.3337 9.99984" stroke="url(#paint0_linear_4891_1884)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M10.0003 1.6665C5.39795 1.6665 1.66699 5.39746 1.66699 9.99984C1.66699 14.6022 5.39795 18.3332 10.0003 18.3332V18.3332C14.6027 18.3332 18.3337 14.6022 18.3337 9.99984" stroke="url(#paint1_linear_4891_1884)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><defs><linearGradient id="paint0_linear_4891_1884" x1="13.9695" y1="3.49376" x2="0.957357" y2="8.97445" gradientUnits="userSpaceOnUse"><stop stop-color="white" stop-opacity="0.01"></stop><stop offset="1" stop-color="white"></stop></linearGradient><linearGradient id="paint1_linear_4891_1884" x1="7.73281" y1="2.8403" x2="14.3633" y2="7.37534" gradientUnits="userSpaceOnUse"><stop stop-color="white"></stop><stop offset="1" stop-color="white" stop-opacity="0.01"></stop></linearGradient></defs>', 3)]))
        }
    },
    l = {
        class: "loading-icon-container"
    },
    c = a(e({
        __name: "LoadingIcon",
        props: {
            color: {
                type: String,
                default: "white"
            }
        },
        setup: s => (e, a) => (o(), t("div", l, ["black" === s.color ? (o(), i(n(r), {
            key: 0,
            class: "loading-icon rotating"
        })) : (o(), i(n(d), {
            key: 1,
            class: "loading-icon rotating"
        }))]))
    }), [
        ["__scopeId", "data-v-b4e2d342"]
    ]);
export {
    c as L, d as W
};