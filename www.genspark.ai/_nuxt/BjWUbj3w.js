import {
    dl as e,
    a0 as n,
    r as o,
    d9 as t,
    c9 as r,
    a3 as i,
    h as a,
    v as s,
    ak as l,
    al as d,
    aX as c,
    aY as u,
    ds as v,
    am as f,
    aO as g,
    J as h,
    X as p,
    Y as m,
    Z as b,
    dt as C,
    aV as y,
    c as x,
    ap as w,
    aq as k,
    b0 as z,
    aT as S,
    da as $,
    b6 as O,
    du as B,
    dv as R,
    dw as F,
    dx as P,
    dy as j,
    i as E,
    M as T,
    x as A,
    au as M,
    bv as L,
    av as D,
    H as _,
    a4 as I,
    I as N,
    aR as H,
    n as q,
    U as X,
    K as Y,
    dz as V,
    dp as U
} from "./Cf0SOiw0.js";
import {
    r as W,
    c as J,
    e as K,
    a as Z
} from "./B7VeW_-d.js";
import {
    a as G,
    f as Q
} from "./pB_XRIgB.js";
import {
    k as ee,
    f as ne,
    e as oe
} from "./BLWq1oPC.js";
import {
    B as te
} from "./DAjjhrgi.js";
import {
    e as re,
    F as ie,
    c as ae,
    f as se,
    m as le,
    d as de,
    p as ce,
    L as ue,
    z as ve,
    h as fe
} from "./DpMvtoun.js";
import {
    o as ge,
    a as he,
    g as pe
} from "./Jr9eiJio.js";
import {
    S as me
} from "./WZsIN7xM.js";
import {
    k as be
} from "./9wLWmnxl.js";
import {
    i as Ce
} from "./MpDLC7up.js";
const ye = o(null);

function xe(e) {
    if (e.clientX > 0 || e.clientY > 0) ye.value = {
        x: e.clientX,
        y: e.clientY
    };
    else {
        const {
            target: n
        } = e;
        if (n instanceof Element) {
            const {
                left: e,
                top: o,
                width: t,
                height: r
            } = n.getBoundingClientRect();
            ye.value = e > 0 || o > 0 ? {
                x: e + t / 2,
                y: o + r / 2
            } : {
                x: 0,
                y: 0
            }
        } else ye.value = null
    }
}
let we = 0,
    ke = !0;

function ze() {
    if (!e) return n(o(null));
    0 === we && ge("click", document, xe, !0);
    const a = () => {
        we += 1
    };
    return ke && (ke = t()) ? (r(a), i((() => {
        we -= 1, 0 === we && he("click", document, xe, !0)
    }))) : a(), n(ye)
}
const Se = o(void 0);
let $e = 0;

function Oe() {
    Se.value = Date.now()
}
let Be = !0;

function Re(a) {
    if (!e) return n(o(!1));
    const s = o(!1);
    let l = null;

    function d() {
        null !== l && window.clearTimeout(l)
    }

    function c() {
        d(), s.value = !0, l = window.setTimeout((() => {
            s.value = !1
        }), a)
    }
    0 === $e && ge("click", window, Oe, !0);
    const u = () => {
        $e += 1, ge("click", window, c, !0)
    };
    return Be && (Be = t()) ? (r(u), i((() => {
        $e -= 1, 0 === $e && he("click", window, Oe, !0), he("click", window, c, !0), d()
    }))) : u(), n(s)
}
const Fe = o(!1);

function Pe() {
    Fe.value = !0
}

function je() {
    Fe.value = !1
}
let Ee = 0;
let Te = 0,
    Ae = "",
    Me = "",
    Le = "",
    De = "";
const _e = o("0px");
const Ie = l([d("card", "\n font-size: var(--n-font-size);\n line-height: var(--n-line-height);\n display: flex;\n flex-direction: column;\n width: 100%;\n box-sizing: border-box;\n position: relative;\n border-radius: var(--n-border-radius);\n background-color: var(--n-color);\n color: var(--n-text-color);\n word-break: break-word;\n transition: \n color .3s var(--n-bezier),\n background-color .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier),\n border-color .3s var(--n-bezier);\n ", [v({
        background: "var(--n-color-modal)"
    }), f("hoverable", [l("&:hover", "box-shadow: var(--n-box-shadow);")]), f("content-segmented", [l(">", [g("content", {
        paddingTop: "var(--n-padding-bottom)"
    })])]), f("content-soft-segmented", [l(">", [g("content", "\n margin: 0 var(--n-padding-left);\n padding: var(--n-padding-bottom) 0;\n ")])]), f("footer-segmented", [l(">", [g("footer", {
        paddingTop: "var(--n-padding-bottom)"
    })])]), f("footer-soft-segmented", [l(">", [g("footer", "\n padding: var(--n-padding-bottom) 0;\n margin: 0 var(--n-padding-left);\n ")])]), l(">", [d("card-header", "\n box-sizing: border-box;\n display: flex;\n align-items: center;\n font-size: var(--n-title-font-size);\n padding:\n var(--n-padding-top)\n var(--n-padding-left)\n var(--n-padding-bottom)\n var(--n-padding-left);\n ", [g("main", "\n font-weight: var(--n-title-font-weight);\n transition: color .3s var(--n-bezier);\n flex: 1;\n min-width: 0;\n color: var(--n-title-text-color);\n "), g("extra", "\n display: flex;\n align-items: center;\n font-size: var(--n-font-size);\n font-weight: 400;\n transition: color .3s var(--n-bezier);\n color: var(--n-text-color);\n "), g("close", "\n margin: 0 0 0 8px;\n transition:\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier);\n ")]), g("action", "\n box-sizing: border-box;\n transition:\n background-color .3s var(--n-bezier),\n border-color .3s var(--n-bezier);\n background-clip: padding-box;\n background-color: var(--n-action-color);\n "), g("content", "flex: 1; min-width: 0;"), g("content, footer", "\n box-sizing: border-box;\n padding: 0 var(--n-padding-left) var(--n-padding-bottom) var(--n-padding-left);\n font-size: var(--n-font-size);\n ", [l("&:first-child", {
        paddingTop: "var(--n-padding-bottom)"
    })]), g("action", "\n background-color: var(--n-action-color);\n padding: var(--n-padding-bottom) var(--n-padding-left);\n border-bottom-left-radius: var(--n-border-radius);\n border-bottom-right-radius: var(--n-border-radius);\n ")]), d("card-cover", "\n overflow: hidden;\n width: 100%;\n border-radius: var(--n-border-radius) var(--n-border-radius) 0 0;\n ", [l("img", "\n display: block;\n width: 100%;\n ")]), f("bordered", "\n border: 1px solid var(--n-border-color);\n ", [l("&:target", "border-color: var(--n-color-target);")]), f("action-segmented", [l(">", [g("action", [l("&:not(:first-child)", {
        borderTop: "1px solid var(--n-border-color)"
    })])])]), f("content-segmented, content-soft-segmented", [l(">", [g("content", {
        transition: "border-color 0.3s var(--n-bezier)"
    }, [l("&:not(:first-child)", {
        borderTop: "1px solid var(--n-border-color)"
    })])])]), f("footer-segmented, footer-soft-segmented", [l(">", [g("footer", {
        transition: "border-color 0.3s var(--n-bezier)"
    }, [l("&:not(:first-child)", {
        borderTop: "1px solid var(--n-border-color)"
    })])])]), f("embedded", "\n background-color: var(--n-color-embedded);\n ")]), c(d("card", "\n background: var(--n-color-modal);\n ", [f("embedded", "\n background-color: var(--n-color-embedded-modal);\n ")])), u(d("card", "\n background: var(--n-color-popover);\n ", [f("embedded", "\n background-color: var(--n-color-embedded-popover);\n ")]))]),
    Ne = {
        title: [String, Function],
        contentClass: String,
        contentStyle: [Object, String],
        headerClass: String,
        headerStyle: [Object, String],
        headerExtraClass: String,
        headerExtraStyle: [Object, String],
        footerClass: String,
        footerStyle: [Object, String],
        embedded: Boolean,
        segmented: {
            type: [Boolean, Object],
            default: !1
        },
        size: {
            type: String,
            default: "medium"
        },
        bordered: {
            type: Boolean,
            default: !0
        },
        closable: Boolean,
        hoverable: Boolean,
        role: String,
        onClose: [Function, Array],
        tag: {
            type: String,
            default: "div"
        },
        cover: Function,
        content: [String, Function],
        footer: Function,
        action: Function,
        headerExtra: Function
    },
    He = ee(Ne),
    qe = h({
        name: "Card",
        props: Object.assign(Object.assign({}, b.props), Ne),
        slots: Object,
        setup(e) {
            const {
                inlineThemeDisabled: n,
                mergedClsPrefixRef: o,
                mergedRtlRef: t
            } = m(e), r = b("Card", "-card", Ie, C, e, o), i = y("Card", t, o), a = x((() => {
                const {
                    size: n
                } = e, {
                    self: {
                        color: o,
                        colorModal: t,
                        colorTarget: i,
                        textColor: a,
                        titleTextColor: s,
                        titleFontWeight: l,
                        borderColor: d,
                        actionColor: c,
                        borderRadius: u,
                        lineHeight: v,
                        closeIconColor: f,
                        closeIconColorHover: g,
                        closeIconColorPressed: h,
                        closeColorHover: p,
                        closeColorPressed: m,
                        closeBorderRadius: b,
                        closeIconSize: C,
                        closeSize: y,
                        boxShadow: x,
                        colorPopover: k,
                        colorEmbedded: z,
                        colorEmbeddedModal: S,
                        colorEmbeddedPopover: $,
                        [w("padding", n)]: O,
                        [w("fontSize", n)]: B,
                        [w("titleFontSize", n)]: R
                    },
                    common: {
                        cubicBezierEaseInOut: F
                    }
                } = r.value, {
                    top: P,
                    left: j,
                    bottom: E
                } = G(O);
                return {
                    "--n-bezier": F,
                    "--n-border-radius": u,
                    "--n-color": o,
                    "--n-color-modal": t,
                    "--n-color-popover": k,
                    "--n-color-embedded": z,
                    "--n-color-embedded-modal": S,
                    "--n-color-embedded-popover": $,
                    "--n-color-target": i,
                    "--n-text-color": a,
                    "--n-line-height": v,
                    "--n-action-color": c,
                    "--n-title-text-color": s,
                    "--n-title-font-weight": l,
                    "--n-close-icon-color": f,
                    "--n-close-icon-color-hover": g,
                    "--n-close-icon-color-pressed": h,
                    "--n-close-color-hover": p,
                    "--n-close-color-pressed": m,
                    "--n-border-color": d,
                    "--n-box-shadow": x,
                    "--n-padding-top": P,
                    "--n-padding-bottom": E,
                    "--n-padding-left": j,
                    "--n-font-size": B,
                    "--n-title-font-size": R,
                    "--n-close-size": y,
                    "--n-close-icon-size": C,
                    "--n-close-border-radius": b
                }
            })), s = n ? k("card", x((() => e.size[0])), a, e) : void 0;
            return {
                rtlEnabled: i,
                mergedClsPrefix: o,
                mergedTheme: r,
                handleCloseClick: () => {
                    const {
                        onClose: n
                    } = e;
                    n && J(n)
                },
                cssVars: n ? void 0 : a,
                themeClass: null == s ? void 0 : s.themeClass,
                onRender: null == s ? void 0 : s.onRender
            }
        },
        render() {
            const {
                segmented: e,
                bordered: n,
                hoverable: o,
                mergedClsPrefix: t,
                rtlEnabled: r,
                onRender: i,
                embedded: a,
                tag: s,
                $slots: l
            } = this;
            return null == i || i(), p(s, {
                class: [`${t}-card`, this.themeClass, a && `${t}-card--embedded`, {
                    [`${t}-card--rtl`]: r,
                    [`${t}-card--content${"boolean"!=typeof e&&"soft"===e.content?"-soft":""}-segmented`]: !0 === e || !1 !== e && e.content,
                    [`${t}-card--footer${"boolean"!=typeof e&&"soft"===e.footer?"-soft":""}-segmented`]: !0 === e || !1 !== e && e.footer,
                    [`${t}-card--action-segmented`]: !0 === e || !1 !== e && e.action,
                    [`${t}-card--bordered`]: n,
                    [`${t}-card--hoverable`]: o
                }],
                style: this.cssVars,
                role: this.role
            }, W(l.cover, (e => {
                const n = this.cover ? K([this.cover()]) : e;
                return n && p("div", {
                    class: `${t}-card-cover`,
                    role: "none"
                }, n)
            })), W(l.header, (e => {
                const {
                    title: n
                } = this, o = n ? K("function" == typeof n ? [n()] : [n]) : e;
                return o || this.closable ? p("div", {
                    class: [`${t}-card-header`, this.headerClass],
                    style: this.headerStyle,
                    role: "heading"
                }, p("div", {
                    class: `${t}-card-header__main`,
                    role: "heading"
                }, o), W(l["header-extra"], (e => {
                    const n = this.headerExtra ? K([this.headerExtra()]) : e;
                    return n && p("div", {
                        class: [`${t}-card-header__extra`, this.headerExtraClass],
                        style: this.headerExtraStyle
                    }, n)
                })), this.closable && p(z, {
                    clsPrefix: t,
                    class: `${t}-card-header__close`,
                    onClick: this.handleCloseClick,
                    absolute: !0
                })) : null
            })), W(l.default, (e => {
                const {
                    content: n
                } = this, o = n ? K("function" == typeof n ? [n()] : [n]) : e;
                return o && p("div", {
                    class: [`${t}-card__content`, this.contentClass],
                    style: this.contentStyle,
                    role: "none"
                }, o)
            })), W(l.footer, (e => {
                const n = this.footer ? K([this.footer()]) : e;
                return n && p("div", {
                    class: [`${t}-card__footer`, this.footerClass],
                    style: this.footerStyle,
                    role: "none"
                }, n)
            })), W(l.action, (e => {
                const n = this.action ? K([this.action()]) : e;
                return n && p("div", {
                    class: `${t}-card__action`,
                    role: "none"
                }, n)
            })))
        }
    }),
    Xe = S("n-dialog-provider"),
    Ye = S("n-dialog-api"),
    Ve = S("n-dialog-reactive-list"),
    Ue = {
        icon: Function,
        type: {
            type: String,
            default: "default"
        },
        title: [String, Function],
        closable: {
            type: Boolean,
            default: !0
        },
        negativeText: String,
        positiveText: String,
        positiveButtonProps: Object,
        negativeButtonProps: Object,
        content: [String, Function],
        action: Function,
        showIcon: {
            type: Boolean,
            default: !0
        },
        loading: Boolean,
        bordered: Boolean,
        iconPlacement: String,
        titleClass: [String, Array],
        titleStyle: [String, Object],
        contentClass: [String, Array],
        contentStyle: [String, Object],
        actionClass: [String, Array],
        actionStyle: [String, Object],
        onPositiveClick: Function,
        onNegativeClick: Function,
        onClose: Function
    },
    We = ee(Ue),
    Je = l([d("dialog", "\n --n-icon-margin: var(--n-icon-margin-top) var(--n-icon-margin-right) var(--n-icon-margin-bottom) var(--n-icon-margin-left);\n word-break: break-word;\n line-height: var(--n-line-height);\n position: relative;\n background: var(--n-color);\n color: var(--n-text-color);\n box-sizing: border-box;\n margin: auto;\n border-radius: var(--n-border-radius);\n padding: var(--n-padding);\n transition: \n border-color .3s var(--n-bezier),\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier);\n ", [g("icon", {
        color: "var(--n-icon-color)"
    }), f("bordered", {
        border: "var(--n-border)"
    }), f("icon-top", [g("close", {
        margin: "var(--n-close-margin)"
    }), g("icon", {
        margin: "var(--n-icon-margin)"
    }), g("content", {
        textAlign: "center"
    }), g("title", {
        justifyContent: "center"
    }), g("action", {
        justifyContent: "center"
    })]), f("icon-left", [g("icon", {
        margin: "var(--n-icon-margin)"
    }), f("closable", [g("title", "\n padding-right: calc(var(--n-close-size) + 6px);\n ")])]), g("close", "\n position: absolute;\n right: 0;\n top: 0;\n margin: var(--n-close-margin);\n transition:\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier);\n z-index: 1;\n "), g("content", "\n font-size: var(--n-font-size);\n margin: var(--n-content-margin);\n position: relative;\n word-break: break-word;\n ", [f("last", "margin-bottom: 0;")]), g("action", "\n display: flex;\n justify-content: flex-end;\n ", [l("> *:not(:last-child)", "\n margin-right: var(--n-action-space);\n ")]), g("icon", "\n font-size: var(--n-icon-size);\n transition: color .3s var(--n-bezier);\n "), g("title", "\n transition: color .3s var(--n-bezier);\n display: flex;\n align-items: center;\n font-size: var(--n-title-font-size);\n font-weight: var(--n-title-font-weight);\n color: var(--n-title-text-color);\n "), d("dialog-icon-container", "\n display: flex;\n justify-content: center;\n ")]), c(d("dialog", "\n width: 446px;\n max-width: calc(100vw - 32px);\n ")), d("dialog", [v("\n width: 446px;\n max-width: calc(100vw - 32px);\n ")])]),
    Ke = {
        default: () => p(j, null),
        info: () => p(j, null),
        success: () => p(P, null),
        warning: () => p(F, null),
        error: () => p(R, null)
    },
    Ze = h({
        name: "Dialog",
        alias: ["NimbusConfirmCard", "Confirm"],
        props: Object.assign(Object.assign({}, b.props), Ue),
        slots: Object,
        setup(e) {
            const {
                mergedComponentPropsRef: n,
                mergedClsPrefixRef: o,
                inlineThemeDisabled: t,
                mergedRtlRef: r
            } = m(e), i = y("Dialog", r, o), a = x((() => {
                var o, t;
                const {
                    iconPlacement: r
                } = e;
                return r || (null === (t = null === (o = null == n ? void 0 : n.value) || void 0 === o ? void 0 : o.Dialog) || void 0 === t ? void 0 : t.iconPlacement) || "left"
            }));
            const s = b("Dialog", "-dialog", Je, B, e, o),
                l = x((() => {
                    const {
                        type: n
                    } = e, o = a.value, {
                        common: {
                            cubicBezierEaseInOut: t
                        },
                        self: {
                            fontSize: r,
                            lineHeight: i,
                            border: l,
                            titleTextColor: d,
                            textColor: c,
                            color: u,
                            closeBorderRadius: v,
                            closeColorHover: f,
                            closeColorPressed: g,
                            closeIconColor: h,
                            closeIconColorHover: p,
                            closeIconColorPressed: m,
                            closeIconSize: b,
                            borderRadius: C,
                            titleFontWeight: y,
                            titleFontSize: x,
                            padding: k,
                            iconSize: z,
                            actionSpace: S,
                            contentMargin: $,
                            closeSize: O,
                            ["top" === o ? "iconMarginIconTop" : "iconMargin"]: B,
                            ["top" === o ? "closeMarginIconTop" : "closeMargin"]: R,
                            [w("iconColor", n)]: F
                        }
                    } = s.value, P = G(B);
                    return {
                        "--n-font-size": r,
                        "--n-icon-color": F,
                        "--n-bezier": t,
                        "--n-close-margin": R,
                        "--n-icon-margin-top": P.top,
                        "--n-icon-margin-right": P.right,
                        "--n-icon-margin-bottom": P.bottom,
                        "--n-icon-margin-left": P.left,
                        "--n-icon-size": z,
                        "--n-close-size": O,
                        "--n-close-icon-size": b,
                        "--n-close-border-radius": v,
                        "--n-close-color-hover": f,
                        "--n-close-color-pressed": g,
                        "--n-close-icon-color": h,
                        "--n-close-icon-color-hover": p,
                        "--n-close-icon-color-pressed": m,
                        "--n-color": u,
                        "--n-text-color": c,
                        "--n-border-radius": C,
                        "--n-padding": k,
                        "--n-line-height": i,
                        "--n-border": l,
                        "--n-content-margin": $,
                        "--n-title-font-size": x,
                        "--n-title-font-weight": y,
                        "--n-title-text-color": d,
                        "--n-action-space": S
                    }
                })),
                d = t ? k("dialog", x((() => `${e.type[0]}${a.value[0]}`)), l, e) : void 0;
            return {
                mergedClsPrefix: o,
                rtlEnabled: i,
                mergedIconPlacement: a,
                mergedTheme: s,
                handlePositiveClick: function(n) {
                    const {
                        onPositiveClick: o
                    } = e;
                    o && o(n)
                },
                handleNegativeClick: function(n) {
                    const {
                        onNegativeClick: o
                    } = e;
                    o && o(n)
                },
                handleCloseClick: function() {
                    const {
                        onClose: n
                    } = e;
                    n && n()
                },
                cssVars: t ? void 0 : l,
                themeClass: null == d ? void 0 : d.themeClass,
                onRender: null == d ? void 0 : d.onRender
            }
        },
        render() {
            var e;
            const {
                bordered: n,
                mergedIconPlacement: o,
                cssVars: t,
                closable: r,
                showIcon: i,
                title: a,
                content: s,
                action: l,
                negativeText: d,
                positiveText: c,
                positiveButtonProps: u,
                negativeButtonProps: v,
                handlePositiveClick: f,
                handleNegativeClick: g,
                mergedTheme: h,
                loading: m,
                type: b,
                mergedClsPrefix: C
            } = this;
            null === (e = this.onRender) || void 0 === e || e.call(this);
            const y = i ? p(O, {
                    clsPrefix: C,
                    class: `${C}-dialog__icon`
                }, {
                    default: () => W(this.$slots.icon, (e => e || (this.icon ? $(this.icon) : Ke[this.type]())))
                }) : null,
                x = W(this.$slots.action, (e => e || c || d || l ? p("div", {
                    class: [`${C}-dialog__action`, this.actionClass],
                    style: this.actionStyle
                }, e || (l ? [$(l)] : [this.negativeText && p(te, Object.assign({
                    theme: h.peers.Button,
                    themeOverrides: h.peerOverrides.Button,
                    ghost: !0,
                    size: "small",
                    onClick: g
                }, v), {
                    default: () => $(this.negativeText)
                }), this.positiveText && p(te, Object.assign({
                    theme: h.peers.Button,
                    themeOverrides: h.peerOverrides.Button,
                    size: "small",
                    type: "default" === b ? "primary" : b,
                    disabled: m,
                    loading: m,
                    onClick: f
                }, u), {
                    default: () => $(this.positiveText)
                })])) : null));
            return p("div", {
                class: [`${C}-dialog`, this.themeClass, this.closable && `${C}-dialog--closable`, `${C}-dialog--icon-${o}`, n && `${C}-dialog--bordered`, this.rtlEnabled && `${C}-dialog--rtl`],
                style: t,
                role: "dialog"
            }, r ? W(this.$slots.close, (e => {
                const n = [`${C}-dialog__close`, this.rtlEnabled && `${C}-dialog--rtl`];
                return e ? p("div", {
                    class: n
                }, e) : p(z, {
                    clsPrefix: C,
                    class: n,
                    onClick: this.handleCloseClick
                })
            })) : null, i && "top" === o ? p("div", {
                class: `${C}-dialog-icon-container`
            }, y) : null, p("div", {
                class: [`${C}-dialog__title`, this.titleClass],
                style: this.titleStyle
            }, i && "left" === o ? y : null, Z(this.$slots.header, (() => [$(a)]))), p("div", {
                class: [`${C}-dialog__content`, x ? "" : `${C}-dialog__content--last`, this.contentClass],
                style: this.contentStyle
            }, Z(this.$slots.default, (() => [$(s)]))), x)
        }
    }),
    Ge = S("n-modal-provider"),
    Qe = S("n-modal-api"),
    en = S("n-modal-reactive-list");

function nn() {
    const e = E(Qe, null);
    return null === e && T("use-modal", "No outer <n-modal-provider /> founded."), e
}
const on = "n-draggable";
const tn = Object.assign(Object.assign({}, Ne), Ue),
    rn = ee(tn),
    an = h({
        name: "ModalBody",
        inheritAttrs: !1,
        slots: Object,
        props: Object.assign(Object.assign({
            show: {
                type: Boolean,
                required: !0
            },
            preset: String,
            displayDirective: {
                type: String,
                required: !0
            },
            trapFocus: {
                type: Boolean,
                default: !0
            },
            autoFocus: {
                type: Boolean,
                default: !0
            },
            blockScroll: Boolean,
            draggable: {
                type: [Boolean, Object],
                default: !1
            }
        }, tn), {
            renderMask: Function,
            onClickoutside: Function,
            onBeforeLeave: {
                type: Function,
                required: !0
            },
            onAfterLeave: {
                type: Function,
                required: !0
            },
            onPositiveClick: {
                type: Function,
                required: !0
            },
            onNegativeClick: {
                type: Function,
                required: !0
            },
            onClose: {
                type: Function,
                required: !0
            },
            onAfterEnter: Function,
            onEsc: Function
        }),
        setup(e) {
            const n = o(null),
                t = o(null),
                r = o(e.show),
                l = o(null),
                d = o(null),
                c = E(se);
            let u = null;
            s(H(e, "show"), (e => {
                e && (u = c.getMousePosition())
            }), {
                immediate: !0
            });
            const {
                stopDrag: v,
                startDrag: f,
                draggableRef: g,
                draggableClassRef: h
            } = function(e, n) {
                let o;
                const t = x((() => !1 !== e.value)),
                    r = x((() => t.value ? on : "")),
                    i = x((() => {
                        const n = e.value;
                        return !0 === n || !1 === n || !n || "none" !== n.bounds
                    }));

                function a() {
                    o && (o(), o = void 0)
                }
                return A(a), {
                    stopDrag: a,
                    startDrag: function(e) {
                        const t = e.querySelector(`.${on}`);
                        if (!t || !r.value) return;
                        let a, s = 0,
                            l = 0,
                            d = 0,
                            c = 0,
                            u = 0,
                            v = 0;

                        function f(n) {
                            n.preventDefault(), a = n;
                            const {
                                x: o,
                                y: t,
                                right: r,
                                bottom: i
                            } = e.getBoundingClientRect();
                            l = o, c = t, s = window.innerWidth - r, d = window.innerHeight - i;
                            const {
                                left: f,
                                top: g
                            } = e.style;
                            u = +g.slice(0, -2), v = +f.slice(0, -2)
                        }

                        function g(n) {
                            if (!a) return;
                            const {
                                clientX: o,
                                clientY: t
                            } = a;
                            let r = n.clientX - o,
                                f = n.clientY - t;
                            i.value && (r > s ? r = s : -r > l && (r = -l), f > d ? f = d : -f > c && (f = -c));
                            const g = r + v,
                                h = f + u;
                            e.style.top = `${h}px`, e.style.left = `${g}px`
                        }

                        function h() {
                            a = void 0, n.onEnd(e)
                        }
                        ge("mousedown", t, f), ge("mousemove", window, g), ge("mouseup", window, h), o = () => {
                            he("mousedown", t, f), ge("mousemove", window, g), ge("mouseup", window, h)
                        }
                    },
                    draggableRef: t,
                    draggableClassRef: r
                }
            }(H(e, "draggable"), {
                onEnd: e => {
                    C(e)
                }
            }), p = x((() => q([e.titleClass, h.value]))), m = x((() => q([e.headerClass, h.value])));

            function b() {
                if ("center" === c.transformOriginRef.value) return "";
                const {
                    value: e
                } = l, {
                    value: n
                } = d;
                if (null === e || null === n) return "";
                if (t.value) {
                    return `${e}px ${n+t.value.containerScrollTop}px`
                }
                return ""
            }

            function C(e) {
                if ("center" === c.transformOriginRef.value) return;
                if (!u) return;
                if (!t.value) return;
                const n = t.value.containerScrollTop,
                    {
                        offsetLeft: o,
                        offsetTop: r
                    } = e,
                    i = u.y,
                    a = u.x;
                l.value = -(o - a), d.value = -(r - i - n), e.style.transformOrigin = b()
            }
            s(H(e, "show"), (e => {
                    e && (r.value = !0)
                })),
                function(e) {
                    if ("undefined" == typeof document) return;
                    const n = document.documentElement;
                    let o, t = !1;
                    const r = () => {
                        n.style.marginRight = Ae, n.style.overflow = Me, n.style.overflowX = Le, n.style.overflowY = De, _e.value = "0px"
                    };
                    a((() => {
                        o = s(e, (e => {
                            if (e) {
                                if (!Te) {
                                    const e = window.innerWidth - n.offsetWidth;
                                    e > 0 && (Ae = n.style.marginRight, n.style.marginRight = `${e}px`, _e.value = `${e}px`), Me = n.style.overflow, Le = n.style.overflowX, De = n.style.overflowY, n.style.overflow = "hidden", n.style.overflowX = "hidden", n.style.overflowY = "hidden"
                                }
                                t = !0, Te++
                            } else Te--, Te || r(), t = !1
                        }), {
                            immediate: !0
                        })
                    })), i((() => {
                        null == o || o(), t && (Te--, Te || r(), t = !1)
                    }))
                }(x((() => e.blockScroll && r.value)));
            const y = o(null);
            return s(y, (e => {
                e && X((() => {
                    const o = e.el;
                    o && n.value !== o && (n.value = o)
                }))
            })), Y(le, n), Y(de, null), Y(ce, null), {
                mergedTheme: c.mergedThemeRef,
                appear: c.appearRef,
                isMounted: c.isMountedRef,
                mergedClsPrefix: c.mergedClsPrefixRef,
                bodyRef: n,
                scrollbarRef: t,
                draggableClass: h,
                displayed: r,
                childNodeRef: y,
                cardHeaderClass: m,
                dialogTitleClass: p,
                handlePositiveClick: function() {
                    e.onPositiveClick()
                },
                handleNegativeClick: function() {
                    e.onNegativeClick()
                },
                handleCloseClick: function() {
                    const {
                        onClose: n
                    } = e;
                    n && n()
                },
                handleAfterEnter: function(n) {
                    const o = n;
                    g.value && f(o), e.onAfterEnter && e.onAfterEnter(o)
                },
                handleAfterLeave: function() {
                    r.value = !1, l.value = null, d.value = null, v(), e.onAfterLeave()
                },
                handleBeforeLeave: function(n) {
                    n.style.transformOrigin = b(), e.onBeforeLeave()
                },
                handleEnter: function(e) {
                    X((() => {
                        C(e)
                    }))
                }
            }
        },
        render() {
            const {
                $slots: e,
                $attrs: n,
                handleEnter: o,
                handleAfterEnter: t,
                handleAfterLeave: r,
                handleBeforeLeave: i,
                preset: a,
                mergedClsPrefix: s
            } = this;
            let l = null;
            if (!a) {
                if (l = re("default", e.default, {
                        draggableClass: this.draggableClass
                    }), !l) return void M("modal", "default slot is empty");
                l = L(l), l.props = D({
                    class: `${s}-modal`
                }, n, l.props || {})
            }
            return "show" === this.displayDirective || this.displayed || this.show ? _(p("div", {
                role: "none",
                class: `${s}-modal-body-wrapper`
            }, p(me, {
                ref: "scrollbarRef",
                theme: this.mergedTheme.peers.Scrollbar,
                themeOverrides: this.mergedTheme.peerOverrides.Scrollbar,
                contentClass: `${s}-modal-scroll-content`
            }, {
                default: () => {
                    var n;
                    return [null === (n = this.renderMask) || void 0 === n ? void 0 : n.call(this), p(ie, {
                        disabled: !this.trapFocus,
                        active: this.show,
                        onEsc: this.onEsc,
                        autoFocus: this.autoFocus
                    }, {
                        default: () => {
                            var n;
                            return p(I, {
                                name: "fade-in-scale-up-transition",
                                appear: null !== (n = this.appear) && void 0 !== n ? n : this.isMounted,
                                onEnter: o,
                                onAfterEnter: t,
                                onAfterLeave: r,
                                onBeforeLeave: i
                            }, {
                                default: () => {
                                    const n = [
                                            [N, this.show]
                                        ],
                                        {
                                            onClickoutside: o
                                        } = this;
                                    return o && n.push([ae, this.onClickoutside, void 0, {
                                        capture: !0
                                    }]), _("confirm" === this.preset || "dialog" === this.preset ? p(Ze, Object.assign({}, this.$attrs, {
                                        class: [`${s}-modal`, this.$attrs.class],
                                        ref: "bodyRef",
                                        theme: this.mergedTheme.peers.Dialog,
                                        themeOverrides: this.mergedTheme.peerOverrides.Dialog
                                    }, be(this.$props, We), {
                                        titleClass: this.dialogTitleClass,
                                        "aria-modal": "true"
                                    }), e) : "card" === this.preset ? p(qe, Object.assign({}, this.$attrs, {
                                        ref: "bodyRef",
                                        class: [`${s}-modal`, this.$attrs.class],
                                        theme: this.mergedTheme.peers.Card,
                                        themeOverrides: this.mergedTheme.peerOverrides.Card
                                    }, be(this.$props, He), {
                                        headerClass: this.cardHeaderClass,
                                        "aria-modal": "true",
                                        role: "dialog"
                                    }), e) : this.childNodeRef = l, n)
                                }
                            })
                        }
                    })]
                }
            })), [
                [N, "if" === this.displayDirective || this.displayed || this.show]
            ]) : null
        }
    }),
    sn = l([d("modal-container", "\n position: fixed;\n left: 0;\n top: 0;\n height: 0;\n width: 0;\n display: flex;\n "), d("modal-mask", "\n position: fixed;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n background-color: rgba(0, 0, 0, .4);\n ", [Q({
        enterDuration: ".25s",
        leaveDuration: ".25s",
        enterCubicBezier: "var(--n-bezier-ease-out)",
        leaveCubicBezier: "var(--n-bezier-ease-out)"
    })]), d("modal-body-wrapper", "\n position: fixed;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n overflow: visible;\n ", [d("modal-scroll-content", "\n min-height: 100%;\n display: flex;\n position: relative;\n ")]), d("modal", "\n position: relative;\n align-self: center;\n color: var(--n-text-color);\n margin: auto;\n box-shadow: var(--n-box-shadow);\n ", [ne({
        duration: ".25s",
        enterScale: ".5"
    }), l(`.${on}`, "\n cursor: move;\n user-select: none;\n ")])]),
    ln = Object.assign(Object.assign(Object.assign(Object.assign({}, b.props), {
        show: Boolean,
        unstableShowMask: {
            type: Boolean,
            default: !0
        },
        maskClosable: {
            type: Boolean,
            default: !0
        },
        preset: String,
        to: [String, Object],
        displayDirective: {
            type: String,
            default: "if"
        },
        transformOrigin: {
            type: String,
            default: "mouse"
        },
        zIndex: Number,
        autoFocus: {
            type: Boolean,
            default: !0
        },
        trapFocus: {
            type: Boolean,
            default: !0
        },
        closeOnEsc: {
            type: Boolean,
            default: !0
        },
        blockScroll: {
            type: Boolean,
            default: !0
        }
    }), tn), {
        draggable: [Boolean, Object],
        onEsc: Function,
        "onUpdate:show": [Function, Array],
        onUpdateShow: [Function, Array],
        onAfterEnter: Function,
        onBeforeLeave: Function,
        onAfterLeave: Function,
        onClose: Function,
        onPositiveClick: Function,
        onNegativeClick: Function,
        onMaskClick: Function,
        internalDialog: Boolean,
        internalModal: Boolean,
        internalAppear: {
            type: Boolean,
            default: void 0
        },
        overlayStyle: [String, Object],
        onBeforeHide: Function,
        onAfterHide: Function,
        onHide: Function
    }),
    dn = h({
        name: "Modal",
        inheritAttrs: !1,
        props: ln,
        slots: Object,
        setup(e) {
            const n = o(null),
                {
                    mergedClsPrefixRef: t,
                    namespaceRef: a,
                    inlineThemeDisabled: s
                } = m(e),
                l = b("Modal", "-modal", sn, V, e, t),
                d = Re(64),
                c = ze(),
                u = U(),
                v = e.internalDialog ? E(Xe, null) : null,
                f = e.internalModal ? E(fe, null) : null,
                g = (Ce && (r((() => {
                    Ee || (window.addEventListener("compositionstart", Pe), window.addEventListener("compositionend", je)), Ee++
                })), i((() => {
                    Ee <= 1 ? (window.removeEventListener("compositionstart", Pe), window.removeEventListener("compositionend", je), Ee = 0) : Ee--
                }))), Fe);

            function h(n) {
                const {
                    onUpdateShow: o,
                    "onUpdate:show": t,
                    onHide: r
                } = e;
                o && J(o, n), t && J(t, n), r && !n && r(n)
            }
            Y(se, {
                getMousePosition: () => {
                    const e = v || f;
                    if (e) {
                        const {
                            clickedRef: n,
                            clickedPositionRef: o
                        } = e;
                        if (n.value && o.value) return o.value
                    }
                    return d.value ? c.value : null
                },
                mergedClsPrefixRef: t,
                mergedThemeRef: l,
                isMountedRef: u,
                appearRef: H(e, "internalAppear"),
                transformOriginRef: H(e, "transformOrigin")
            });
            const p = x((() => {
                    const {
                        common: {
                            cubicBezierEaseOut: e
                        },
                        self: {
                            boxShadow: n,
                            color: o,
                            textColor: t
                        }
                    } = l.value;
                    return {
                        "--n-bezier-ease-out": e,
                        "--n-box-shadow": n,
                        "--n-color": o,
                        "--n-text-color": t
                    }
                })),
                C = s ? k("theme-class", void 0, p, e) : void 0;
            return {
                mergedClsPrefix: t,
                namespace: a,
                isMounted: u,
                containerRef: n,
                presetProps: x((() => be(e, rn))),
                handleEsc: function(n) {
                    var o;
                    null === (o = e.onEsc) || void 0 === o || o.call(e), e.show && e.closeOnEsc && oe(n) && (g.value || h(!1))
                },
                handleAfterLeave: function() {
                    const {
                        onAfterLeave: n,
                        onAfterHide: o
                    } = e;
                    n && J(n), o && o()
                },
                handleClickoutside: function(o) {
                    var t;
                    const {
                        onMaskClick: r
                    } = e;
                    r && r(o), e.maskClosable && (null === (t = n.value) || void 0 === t ? void 0 : t.contains(pe(o))) && h(!1)
                },
                handleBeforeLeave: function() {
                    const {
                        onBeforeLeave: n,
                        onBeforeHide: o
                    } = e;
                    n && J(n), o && o()
                },
                doUpdateShow: h,
                handleNegativeClick: function() {
                    const {
                        onNegativeClick: n
                    } = e;
                    n ? Promise.resolve(n()).then((e => {
                        !1 !== e && h(!1)
                    })) : h(!1)
                },
                handlePositiveClick: function() {
                    const {
                        onPositiveClick: n
                    } = e;
                    n ? Promise.resolve(n()).then((e => {
                        !1 !== e && h(!1)
                    })) : h(!1)
                },
                handleCloseClick: function() {
                    const {
                        onClose: n
                    } = e;
                    n ? Promise.resolve(n()).then((e => {
                        !1 !== e && h(!1)
                    })) : h(!1)
                },
                cssVars: s ? void 0 : p,
                themeClass: null == C ? void 0 : C.themeClass,
                onRender: null == C ? void 0 : C.onRender
            }
        },
        render() {
            const {
                mergedClsPrefix: e
            } = this;
            return p(ue, {
                to: this.to,
                show: this.show
            }, {
                default: () => {
                    var n;
                    null === (n = this.onRender) || void 0 === n || n.call(this);
                    const {
                        unstableShowMask: o
                    } = this;
                    return _(p("div", {
                        role: "none",
                        ref: "containerRef",
                        class: [`${e}-modal-container`, this.themeClass, this.namespace],
                        style: this.cssVars
                    }, p(an, Object.assign({
                        style: this.overlayStyle
                    }, this.$attrs, {
                        ref: "bodyWrapper",
                        displayDirective: this.displayDirective,
                        show: this.show,
                        preset: this.preset,
                        autoFocus: this.autoFocus,
                        trapFocus: this.trapFocus,
                        draggable: this.draggable,
                        blockScroll: this.blockScroll
                    }, this.presetProps, {
                        onEsc: this.handleEsc,
                        onClose: this.handleCloseClick,
                        onNegativeClick: this.handleNegativeClick,
                        onPositiveClick: this.handlePositiveClick,
                        onBeforeLeave: this.handleBeforeLeave,
                        onAfterEnter: this.onAfterEnter,
                        onAfterLeave: this.handleAfterLeave,
                        onClickoutside: o ? void 0 : this.handleClickoutside,
                        renderMask: o ? () => {
                            var n;
                            return p(I, {
                                name: "fade-in-transition",
                                key: "mask",
                                appear: null !== (n = this.internalAppear) && void 0 !== n ? n : this.isMounted
                            }, {
                                default: () => this.show ? p("div", {
                                    "aria-hidden": !0,
                                    ref: "containerRef",
                                    class: `${e}-modal-mask`,
                                    onClick: this.handleClickoutside
                                }) : null
                            })
                        } : void 0
                    }), this.$slots)), [
                        [ve, {
                            zIndex: this.zIndex,
                            enabled: this.show
                        }]
                    ])
                }
            })
        }
    });
export {
    dn as N, qe as a, Ze as b, We as c, Ye as d, Ue as e, Re as f, Xe as g, Ve as h, Ge as i, Qe as j, en as k, nn as l, ln as m, ze as u
};