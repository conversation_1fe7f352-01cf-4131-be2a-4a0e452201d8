import {
    d as e,
    o as t,
    b as a,
    _ as l,
    s as o,
    r as s,
    ay as n,
    l as i,
    t as r,
    y as c,
    F as u,
    k as d,
    n as p,
    f as v,
    p as m,
    a as h,
    ai as g,
    J as _,
    g as f,
    i as y,
    v as k,
    e as w,
    cH as x,
    h as b,
    c as j,
    a3 as C,
    w as S,
    q as T,
    C as E,
    U as L,
    H as P,
    I as $,
    x as I,
    A as D,
    D as A,
    ag as M,
    z as R,
    ah as N,
    m as U,
    aj as O,
    a4 as q,
    j as B,
    u as H,
    d0 as z
} from "./Cf0SOiw0.js";
import {
    _ as F
} from "./D6bQc9d9.js";
import {
    _ as W
} from "./e-ES_T8J.js";
import {
    _ as G
} from "./Dwl0GcWq.js";
import {
    _ as Z
} from "./UVj2ej2A.js";
import {
    A as V
} from "./DeFGaANp.js";
import {
    i as J,
    j as X,
    k as Q,
    N as K,
    l as Y,
    m as ee,
    n as te,
    S as ae,
    o as le,
    C as oe,
    P as se,
    R as ne,
    h as ie
} from "./DC84aLnd.js";
import re from "./CfpQPauw.js";
import {
    _ as ce
} from "./k2Gnd_E0.js";
import {
    D as ue,
    G as de,
    d as pe,
    M as ve,
    b as me,
    c as he
} from "./QQkd3sR0.js";
import {
    C as ge,
    S as _e,
    A as fe,
    a as ye
} from "./DCkkKq1S.js";
import {
    M as ke
} from "./BMP7zQoC.js";
import {
    M as we
} from "./DZ51nUum.js";
import {
    N as xe,
    A as be
} from "./DdNe12v6.js";
import {
    S as je
} from "./BffV2qdL.js";
import {
    _ as Ce
} from "./DKpDUEYb.js";
import {
    _ as Se
} from "./C1lFdfgL.js";
import {
    E as Te
} from "./DWsxX4PV.js";
import {
    M as Ee
} from "./Dc8Bac8D.js";
import {
    f as Le
} from "./Bl-gMEVt.js";
import {
    C as Pe,
    a as $e
} from "./JllO9TFG.js";
import {
    C as Ie,
    _ as De
} from "./BUDvXs4e.js";
import {
    d as Ae,
    u as Me
} from "./BdlGQsae.js";
import {
    E as Re
} from "./CsEkj3AT.js";
import {
    u as Ne,
    A as Ue
} from "./DmCciZvb.js";
import {
    A as Oe
} from "./4s4Iy95q.js";
import {
    b as qe
} from "./DbJ6Dt9m.js";
import {
    u as Be
} from "./B6noBY_5.js";
import {
    a as He,
    S as ze
} from "./IgM9N0FT.js";
import {
    u as Fe
} from "./D5IxqnO4.js";
import {
    u as We
} from "./B0db5Fvl.js";
import {
    D as Ge
} from "./BThdTUgg.js";
import {
    R as Ze
} from "./CQ2glRxo.js";
import {
    L as Ve
} from "./Bzg9uoz_.js";
import {
    A as Je
} from "./CEhuGuk0.js";
import {
    d as Xe
} from "./DOnko34f.js";
import {
    s as Qe
} from "./CVrRKK4N.js";
import {
    B as Ke
} from "./DAjjhrgi.js";
import {
    a as Ye,
    N as et
} from "./BjWUbj3w.js";
import {
    a as tt,
    b as at
} from "./Dp4W8C_b.js";
import {
    N as lt
} from "./BQrni_b3.js";
import {
    N as ot
} from "./B-XpIQkh.js";
import {
    N as st
} from "./BrPr1fm5.js";
import {
    N as nt
} from "./BZiwhdnP.js";
import {
    C as it
} from "./Bm_HbXT2.js";
import {
    G as rt
} from "./AGJ7ifDi.js";
import {
    L as ct
} from "./CRDCtKfR.js";
import {
    a as ut
} from "./DmzoGJ5T.js";
import {
    P as dt
} from "./BWQxP2bF.js";
import {
    u as pt,
    f as vt
} from "./HCr483ve.js";
import {
    L as mt
} from "./CMNqFBX1.js";
import {
    u as ht,
    R as gt
} from "./BL3u8nKF.js";
import {
    R as _t
} from "./D0Ouax4K.js";
import {
    C as ft
} from "./CRmNre8Y.js";
import {
    u as yt
} from "./DJ-JsGJu.js";
import {
    N as kt
} from "./CW991W2w.js";
import "./DdaMTYTP.js";
import "./BLjKtaC-.js";
import "./mJse5lCL.js";
import "./BH52nuY1.js";
import "./D5LV4gfS.js";
import "./CHwVUzYg.js";
import "./CRIx66FB.js";
import "./DqWfLcpp.js";
import "./CsPFbezH.js";
import "./DnZj1005.js";
import "./B56nEZrv.js";
import "./tBofk-gQ.js";
import "./WZsIN7xM.js";
import "./pB_XRIgB.js";
import "./Jr9eiJio.js";
import "./BLWq1oPC.js";
import "./9wLWmnxl.js";
import "./MpDLC7up.js";
import "./Jx3-I-D7.js";
import "./Cp7w48vH.js";
import "./C-H3edso.js";
import "./BPQGB51Y.js";
import "./CSefR0kE.js";
import "./DyMB-pVc.js";
import "./BYvs8isC.js";
import "./0tM3vo_n.js";
import "./BuZX8s8J.js";
import "./DkAysl9I.js";
import "./Cy7E5O2b.js";
import "./D5ao1EUl.js";
import "./DcBgjX7B.js";
import "./Dnth285N.js";
import "./DW6cX6jm.js";
import "./C_XD2eP3.js";
import "./DHyvnBfI.js";
import "./BDUh8PoD.js";
import "./DwpGavSW.js";
import "./BAZ5Sqbz.js";
import "./BSM9O9PP.js";
import "./x7yzXAMO.js";
import "./L54g9xmZ.js";
import "./V-H-Vsd5.js";
import "./CAmLbDGM.js";
import "./DQpEsQQa.js";
import "./BGK9k_mT.js";
import "./DXvAIxvL.js";
import "./C38RzRfR.js";
import "./By6xEfKc.js";
import "./B7VeW_-d.js";
import "./DpMvtoun.js";
import "./DGJMLFjI.js";
import "./BihyrXkC.js";
import "./BuhfKjCJ.js";
import "./DYrQUwU9.js";
import "./CvZgRYhs.js";
import "./BrcpPT-Q.js";
import "./WhweajiO.js";
import "./CaEkZ53E.js";
import "./DmWk8H2v.js";
import "./BXgTVD7d.js";
import "./Db8eFYek.js";
import "./CQjXacSG.js";
import "./F4fwMVvr.js";
import "./qk0HrepY.js";
import "./wD492QF4.js";
import "./CqNssBtC.js";
import "./BqHcVhvy.js";
import "./DLUhQFIq.js";
import "./C_QvFyDp.js";
import "./DR_b14-4.js"; /* empty css        */
import "./BX7SPHBj.js";
import "./B5SiUF0y.js";
import "./Cn9HXEST.js";
import "./D9ll07Bp.js";
import "./CIlzw36e.js";
import "./D386eQgZ.js";
import "./Boc3hm_9.js";
import "./BUjMLq-a.js";
import "./BN-NNxvY.js";
import "./nuQnue4a.js";
import "./CmeRl4Ak.js";
import "./DJt7CPhG.js";
import "./CVKRwtBu.js";
import "./DxgY8w7w.js";
import "./DZBrT1el.js";
import "./nw2_yPEg.js";
import "./CAfqOhBF.js";
import "./DT-NG54s.js";
import "./BwHsXuPr.js";
import "./BUs-AQWo.js";
import "./DOWa3jpG.js";
import "./Ug8thHSu.js";
import "./BUCk-Nnr.js";
import "./zge8xy1K.js";
import "./Dflnlfvw.js";
import "./HR06imN1.js";
import "./CbfxwEmT.js";
import "./BV6guSOS.js";
import "./CAzLTKWw.js";
import "./CJmWj3ri.js";
import "./9KCDKcmx.js";
import "./0XKHBXCr.js";
import "./CfHz9NLm.js";
import "./gsZIXP6B.js";
import "./ID2-UV3f.js";
import "./C1MFiWVQ.js";
import "./CKd5XOy1.js";
import "./Cl89jLsD.js";
import "./CouGvJje.js";
import "./LGmiBiLz.js";
import "./zH1ZpJ79.js";
import "./CrbPJ6Kt.js";
import "./CaQJ23Tc.js";
import "./DsPg3ryP.js";
import "./DWCxnUK7.js";
import "./m6pNMthJ.js";
import "./Cu_n4xpI.js";
import "./DrzlY6u5.js";
import "./D_mTlMH9.js";
import "./zB2AApOf.js";
import "./CmF_-QGy.js";
import "./C7XKdzt5.js";
import "./DnWGHCrg.js";
import "./DjawIWev.js";
import "./W5AxVKvJ.js";
import "./P7LDOl0u.js";
import "./COYh4g7i.js";
import "./BspXWmOn.js";
import "./3_kYp8w9.js";
import "./C1hkGl0A.js";
import "./CztlUxD-.js";
import "./DHMy_TLW.js";
import "./BpWej-le.js";
import "./BZ8pMrhH.js";
import "./CIu9VtBu.js";
import "./DPPm6Skg.js";
import "./lJbCVLd4.js";
import "./DY44xVYu.js";
import "./Di7Ot5aL.js";
import "./FCN43o2W.js";
import "./BXNOMSAZ.js";
import "./Dd3cVSEE.js";
import "./BiRGpvqQ.js";
import "./Chtxu0jj.js";
import "./BJGgZAPd.js";
import "./BjAOOWF7.js";
import "./8u4bVPF0.js";
import "./4oYWEiiU.js";
import "./DKfgo1Ia.js";
import "./NG4ombpb.js";
import "./Bju5W73y.js";
import "./Btf03DwY.js";
import "./O-S7dcNF.js";
const wt = {
    xmlns: "http://www.w3.org/2000/svg",
    width: "16",
    height: "16",
    viewBox: "0 0 16 16",
    fill: "none"
};
const xt = {
        render: function(l, o) {
            return t(), e("svg", wt, o[0] || (o[0] = [a("path", {
                d: "M16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16C12.4183 16 16 12.4183 16 8Z",
                fill: "currentColor"
            }, null, -1), a("path", {
                class: "check-stroke",
                d: "M4 8.27273L6.66667 11L12 6",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    bt = {
        class: "code-sandbox-mode-selector"
    },
    jt = {
        class: "overlay"
    },
    Ct = {
        class: "modal-header"
    },
    St = {
        class: "modal-content"
    },
    Tt = {
        class: "mode-options"
    },
    Et = ["onClick"],
    Lt = {
        class: "mode-content"
    },
    Pt = {
        class: "mode-header"
    },
    $t = {
        class: "mode-title"
    },
    It = {
        key: 0,
        class: "check-icon"
    },
    Dt = {
        class: "mode-description"
    },
    At = {
        class: "modal-footer"
    },
    Mt = ["disabled"],
    Rt = {
        key: 0,
        class: "loading-spinner"
    },
    Nt = ["disabled"],
    Ut = l({
        __name: "CodeSandboxModeSelector",
        props: {
            project: {
                type: Object,
                default: null
            }
        },
        emits: ["projectCreated", "back", "editProjectName", "submitPrompt"],
        setup(l, {
            emit: _
        }) {
            const f = l,
                y = _,
                {
                    t: k
                } = o(),
                w = s(null),
                x = s(!1),
                b = [{
                    id: "simple_web_app",
                    title: k("components.code_sandbox.mode_selector.simple_website_title"),
                    description: k("components.code_sandbox.mode_selector.simple_website_description"),
                    example: k("components.code_sandbox.mode_selector.simple_website_example"),
                    recommended: !0,
                    icon: n(J),
                    projectData: {
                        type: "code_sandbox_light",
                        name: "Simple Website or Web App",
                        session_state: {
                            is_blank: !1
                        }
                    }
                }, {
                    id: "fullstack_web_app",
                    title: k("components.code_sandbox.mode_selector.fullstack_title"),
                    description: k("components.code_sandbox.mode_selector.fullstack_description"),
                    example: k("components.code_sandbox.mode_selector.fullstack_example"),
                    icon: n(X),
                    projectData: {
                        type: "code_sandbox",
                        name: "Full‑Stack Websites or App",
                        session_state: {
                            is_blank: !1,
                            form_config: {
                                systemPrompt: "cloudflare_pages_system_prompt",
                                environmentType: "e2b"
                            }
                        }
                    }
                }, {
                    id: "existing_github_project",
                    title: k("components.code_sandbox.mode_selector.github_title"),
                    description: k("components.code_sandbox.mode_selector.github_description"),
                    example: k("components.code_sandbox.mode_selector.github_example"),
                    icon: n(Q),
                    projectData: {
                        type: "code_sandbox",
                        name: "Existing GitHub Project",
                        session_state: {
                            is_blank: !1,
                            form_config: {
                                systemPrompt: "free_code",
                                environmentType: "e2b"
                            }
                        }
                    }
                }],
                j = () => {
                    y("back")
                },
                C = async () => {
                    if (w.value && !x.value) {
                        x.value = !0;
                        try {
                            const e = b.find((e => e.id === w.value));
                            if (!e) return;
                            if (e.disabled) return void(x.value = !1);
                            const t = { ...e.projectData,
                                    client_generated_id: S()
                                },
                                a = await fetch("/api/project/create", {
                                    method: "POST",
                                    headers: {
                                        "Content-Type": "application/json"
                                    },
                                    body: JSON.stringify(t)
                                }),
                                l = await a.json();
                            if (-5 === l.status) return void(location.href = "/login?redirect_url=" + encodeURIComponent(window.location.href));
                            if (0 === l.status && l.data) {
                                const t = { ...f.project,
                                    type: l.data.type,
                                    id: l.data.id,
                                    name: l.data.name,
                                    session_state: l.data.session_state
                                };
                                y("projectCreated", t);
                                const a = e.title;
                                y("submitPrompt", a, [], [])
                            }
                        } catch (e) {} finally {
                            x.value = !1
                        }
                    }
                },
                S = () => "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function(e) {
                    const t = 16 * Math.random() | 0;
                    return ("x" == e ? t : 3 & t | 8).toString(16)
                }));
            return (l, o) => (t(), e("div", bt, [a("div", jt, [a("div", {
                class: "modal",
                onClick: o[0] || (o[0] = i((() => {}), ["stop"]))
            }, [a("div", Ct, [a("h2", null, "🧑‍💻 " + r(c(k)("components.code_sandbox.mode_selector.title")), 1)]), a("div", St, [a("div", Tt, [(t(), e(u, null, d(b, (l => a("div", {
                key: l.id,
                class: p(["mode-option", {
                    active: w.value === l.id,
                    disabled: l.disabled,
                    "coming-soon": l.comingSoon
                }]),
                onClick: e => (e => {
                    const t = b.find((t => t.id === e));
                    t && t.disabled || (w.value = e)
                })(l.id)
            }, [a("div", Lt, [a("div", Pt, [a("h3", $t, [m(r(l.title) + " ", 1), v("", !0)]), w.value === l.id ? (t(), e("div", It, [(t(), h(g(n(c(xt)))))])) : v("", !0)]), a("p", Dt, r(l.description), 1), v("", !0)])], 10, Et))), 64))])]), a("div", At, [a("button", {
                class: "start-working-btn",
                disabled: !w.value || x.value,
                onClick: C
            }, [x.value ? (t(), e("span", Rt)) : v("", !0), a("span", {
                class: p({
                    "loading-text": x.value
                })
            }, r(x.value ? c(k)("components.code_sandbox.mode_selector.creating") : c(k)("components.code_sandbox.mode_selector.start_building")), 3)], 8, Mt), a("button", {
                class: "cancel-btn",
                disabled: x.value,
                onClick: j
            }, r(c(k)("components.code_sandbox.mode_selector.cancel")), 9, Nt)])])])]))
        }
    }, [
        ["__scopeId", "data-v-1f41d1eb"]
    ]),
    Ot = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const qt = {
        render: function(l, o) {
            return t(), e("svg", Ot, o[0] || (o[0] = [a("path", {
                d: "M13.754 3.31405L15.5408 6.91735C15.7845 7.41894 16.4343 7.90006 16.9826 7.99218L20.2213 8.53472C22.2924 8.88277 22.7798 10.3978 21.2873 11.8923L18.7695 14.431C18.343 14.8609 18.1096 15.6901 18.2415 16.2839L18.9624 19.4265C19.5309 21.914 18.2211 22.8763 16.0384 21.5762L13.0027 19.7643C12.4545 19.4368 11.5509 19.4368 10.9924 19.7643L7.9568 21.5762C5.78411 22.8763 4.46427 21.9037 5.03282 19.4265L5.75366 16.2839C5.88564 15.6901 5.65213 14.8609 5.22571 14.431L2.70785 11.8923C1.22555 10.3978 1.70273 8.88277 3.77388 8.53472L7.0126 7.99218C7.55069 7.90006 8.20046 7.41894 8.44413 6.91735L10.231 3.31405C11.2057 1.35886 12.7895 1.35886 13.754 3.31405Z",
                stroke: "currentColor",
                "stroke-width": "1.8",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    Bt = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Ht = {
        render: function(l, o) {
            return t(), e("svg", Bt, o[0] || (o[0] = [a("path", {
                d: "M13.754 3.31405L15.5408 6.91735C15.7845 7.41894 16.4343 7.90006 16.9826 7.99218L20.2213 8.53472C22.2924 8.88277 22.7798 10.3978 21.2873 11.8923L18.7695 14.431C18.343 14.8609 18.1096 15.6901 18.2415 16.2839L18.9624 19.4265C19.5309 21.914 18.2211 22.8763 16.0384 21.5762L13.0027 19.7643C12.4545 19.4368 11.5509 19.4368 10.9924 19.7643L7.9568 21.5762C5.78411 22.8763 4.46427 21.9037 5.03282 19.4265L5.75366 16.2839C5.88564 15.6901 5.65213 14.8609 5.22571 14.431L2.70785 11.8923C1.22555 10.3978 1.70273 8.88277 3.77388 8.53472L7.0126 7.99218C7.55069 7.90006 8.20046 7.41894 8.44413 6.91735L10.231 3.31405C11.2057 1.35886 12.7895 1.35886 13.754 3.31405Z",
                fill: "currentColor",
                stroke: "currentColor",
                "stroke-width": "1.8",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    zt = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 24 24"
    };
const Ft = {
        render: function(l, o) {
            return t(), e("svg", zt, o[0] || (o[0] = [a("g", {
                fill: "none"
            }, [a("path", {
                d: "M11.75 3a.75.75 0 0 1 .743.648l.007.102l.001 7.25h7.253a.75.75 0 0 1 .102 1.493l-.102.007h-7.253l.002 7.25a.75.75 0 0 1-1.493.101l-.007-.102l-.002-7.249H3.752a.75.75 0 0 1-.102-1.493L3.752 11h7.25L11 3.75a.75.75 0 0 1 .75-.75z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    Wt = _({
        __name: "DownloaderWrapper",
        props: {
            breadcrumbText: {
                type: String,
                default: ""
            },
            save_path: {
                type: String,
                default: "/"
            }
        },
        emits: ["route-to-super-agent"],
        setup(a, {
            expose: l,
            emit: n
        }) {
            const {
                t: i
            } = o(), r = Be(), c = f(), u = s(!1), d = s(null), p = y("currentUser"), v = Ne(), m = n, h = a;
            k(p, (e => {
                null !== e && v.setCurrentUser(e)
            }), {
                immediate: !0
            });
            const g = () => {
                    u.value = !1
                },
                _ = () => {
                    r.info(i("components.aidrive.status_download_started"));
                    const e = qe(Oe) + qe(h.save_path);
                    c.push({
                        path: e
                    })
                },
                x = () => {
                    r.warning(i("components.aidrive.status_quota_exceeded")), d.value && d.value.trigger && d.value.trigger()
                },
                b = e => {
                    u.value = !1, m("route-to-super-agent", e)
                };
            return l({
                openDownloader: () => {
                    u.value = !0
                }
            }), (l, o) => (t(), e("div", null, [w(Ue, {
                visible: u.value,
                "onUpdate:visible": o[0] || (o[0] = e => u.value = e),
                onCancel: g,
                onDownloadConfirmed: _,
                onQuotaExceeded: x,
                onRouteToSuperAgent: b,
                breadcrumbText: a.breadcrumbText,
                save_path: a.save_path
            }, null, 8, ["visible", "breadcrumbText", "save_path"]), w(Ce, {
                ref_key: "quotaExceedRef",
                ref: d
            }, null, 512)]))
        }
    }),
    Gt = {
        class: "docs-template-selector"
    },
    Zt = {
        class: "header-controls"
    },
    Vt = {
        class: "header-left"
    },
    Jt = {
        class: "icon"
    },
    Xt = {
        class: "icon"
    },
    Qt = {
        class: "header"
    },
    Kt = {
        key: 0,
        class: "loading-section"
    },
    Yt = {
        key: 1,
        class: "error-section"
    },
    ea = {
        key: 2
    },
    ta = {
        class: "prompt-input-section"
    },
    aa = {
        class: "prompt-input-wrapper"
    },
    la = {
        class: "prompt-input-wrapper-upper"
    },
    oa = {
        class: "editor-switch"
    },
    sa = {
        class: "editor-switch-buttons"
    },
    na = ["onClick"],
    ia = {
        class: "option-label"
    },
    ra = {
        key: 0,
        class: "categories-section"
    },
    ca = {
        class: "icon"
    },
    ua = ["onClick"],
    da = {
        class: "icon"
    },
    pa = {
        class: "template-waterfall"
    },
    va = {
        class: "blank-template-content"
    },
    ma = {
        class: "blank-icon"
    },
    ha = {
        class: "blank-text"
    },
    ga = ["onClick"],
    _a = ["data-src", "alt"],
    fa = {
        key: 1,
        class: "template-icon"
    },
    ya = {
        class: "loading-content"
    },
    ka = {
        class: "loading-text"
    },
    wa = 240,
    xa = l({
        __name: "DocsTemplateSelector",
        props: {
            project: {
                type: Object,
                default: null
            }
        },
        emits: ["projectCreated", "back", "editProjectName", "submitPrompt"],
        setup(l, {
            emit: n
        }) {
            x((e => ({
                "513d5063": z.value + "px"
            })));
            const m = l,
                {
                    t: g
                } = o();
            f();
            const _ = n,
                P = y("currentUser");
            y("jsBridge");
            const $ = Fe(),
                I = We();
            b((() => {
                I.setPlaceholder(g("components.docs_template_selector.prompt_placeholder"))
            }));
            const D = s(!1);
            s(!0);
            const A = s(null),
                M = s(null),
                R = s([]),
                N = s(!1),
                U = s(!1),
                O = s("All Templates"),
                q = s([]),
                B = s(null),
                H = s(wa),
                z = s(wa),
                W = s(12),
                G = s(null),
                Z = s(!1),
                J = s(!1),
                X = s(null),
                Q = s(!1),
                K = Xe((() => {
                    B.value && (Q.value = B.value.clientWidth)
                }), 200),
                Y = s(0),
                ee = Xe((() => {
                    B.value && Y.value++
                }), 16),
                te = s(!0);
            s([]);
            const ae = [{
                    value: "html",
                    label: "Rich Text"
                }, {
                    value: "markdown",
                    label: "Markdown"
                }],
                le = j((() => $.userSelectEditorType)),
                oe = {
                    "All Templates": g("components.docs_template_selector.all_templates"),
                    "Job Applications": g("components.docs_template_selector.job_applications"),
                    "Business Communications": g("components.docs_template_selector.business_communications"),
                    "Reports and Analysis": g("components.docs_template_selector.reports_and_analysis1"),
                    "Forms and Surveys": g("components.docs_template_selector.forms_and_surveys1"),
                    "Legal and Finance": g("components.docs_template_selector.legal_and_finance"),
                    "Education and Training": g("components.docs_template_selector.education_and_training1"),
                    "Creative and Marketing": g("components.docs_template_selector.creative_and_marketing1"),
                    "Event and Planning": g("components.docs_template_selector.event_and_planning")
                },
                se = j((() => {
                    const e = [...new Set(R.value.map((e => e.classification)).filter(Boolean))];
                    return ["All Templates"].concat(e)
                })),
                ne = j((() => "All Templates" === O.value ? R.value : R.value.filter((e => e.classification === O.value))));
            s(1);
            const ie = j((() => {
                    if (Y.value, !B.value) return {
                        position: "fixed",
                        top: "64px",
                        left: "0",
                        right: "0",
                        bottom: "0",
                        zIndex: 9999
                    };
                    const e = B.value.getBoundingClientRect();
                    return {
                        position: "fixed",
                        top: `${e.top}px`,
                        left: `${e.left}px`,
                        width: `${e.width}px`,
                        height: `${e.height}px`,
                        zIndex: 9999
                    }
                })),
                re = (e, t) => {
                    if (!e || 0 === e.length) return [];
                    if (0 === t || !1 === t) return [];
                    let a = 0,
                        l = 0,
                        o = 200;
                    ({
                        columnNum: a,
                        flowItemPadding: W.value,
                        columnWidth: l,
                        blankCardHeight: o
                    } = (e => {
                        let t, a, l = !1,
                            o = 12,
                            s = 200;
                        const n = e;
                        if (e <= 768) {
                            l = !0, t = 2, o = 8;
                            const e = 10;
                            a = Math.floor((n - (t - 1) * e) / t)
                        } else l = !1, t = Math.floor((n + 16) / 256), t < 1 && (t = 1), a = Math.floor((n - 16 * (t - 1)) / t);
                        return s = .8333333333333334 * a, {
                            narrowScreenMode: l,
                            columnNum: t,
                            flowItemPadding: o,
                            columnWidth: a,
                            blankCardHeight: s
                        }
                    })(t)), z.value = l;
                    const s = Array.from({
                            length: a
                        }, (() => [])),
                        n = Array.from({
                            length: a
                        }, (() => 0));
                    if ("All Templates" === O.value) {
                        const e = {
                                id: "blank",
                                isBlank: !0,
                                finalHeight: o
                            },
                            t = 0;
                        s[t].push(e), n[t] += e.finalHeight + 20
                    }
                    for (let i = 0; i < e.length; i++) {
                        const t = e[i];
                        let a = 200;
                        if (t.screenshot_width && t.screenshot_height) {
                            a = t.screenshot_height * (l - 2 * W.value) / t.screenshot_width + 40
                        }
                        t.finalHeight = a;
                        const o = n.reduce(((e, t, a, l) => t < l[e] ? a : e), 0);
                        s[o].push(t), n[o] += t.finalHeight + 20
                    }
                    return s
                },
                ce = async () => {
                    N.value = !0, U.value = !1;
                    try {
                        const e = await fetch("/docs_agent/templates.json");
                        if (!e.ok) throw new Error("Failed to load templates");
                        const t = await e.json();
                        R.value = t || []
                    } catch (e) {
                        U.value = !0, R.value = []
                    } finally {
                        N.value = !1
                    }
                };
            b((async () => {
                window.addEventListener("resize", K), window.addEventListener("resize", ee), window.addEventListener("scroll", ee), setTimeout((() => {
                    K()
                }), 0), await ce()
            })), C((() => {
                window.removeEventListener("resize", K), window.removeEventListener("resize", ee), window.removeEventListener("scroll", ee), ue.value && ue.value.disconnect(), pe && pe.cancel()
            })), k((() => `${ne.value.length}-${Q.value}-${O.value}`), (() => {
                q.value = re(ne.value, Q.value), ve()
            })), k((() => [se.value, Q.value]), (() => {
                L((() => {
                    me()
                }))
            }), {
                deep: !0,
                immediate: !0
            }), k((() => [te.value, Q.value, D.value]), (() => {
                D.value && L((() => {
                    ee()
                }))
            }), {
                immediate: !0
            });
            const ue = s(null);
            let de = [],
                pe = null;
            const ve = () => {
                    ue.value && ue.value.disconnect(), pe && pe.cancel(), de = [], pe = Xe((() => {
                        de.forEach((e => {
                            const t = e.querySelector("img[data-src]");
                            t && (t.src = t.dataset.src, t.removeAttribute("data-src"))
                        })), de = []
                    }), 100);
                    const e = {
                        root: B.value,
                        rootMargin: "0px 0px 300px 0px",
                        threshold: 0
                    };
                    ue.value = new IntersectionObserver(((e, t) => {
                        let a = !1;
                        e.forEach((e => {
                            e.isIntersecting && (de.push(e.target), t.unobserve(e.target), a = !0)
                        })), a && pe()
                    }), e), L((() => {
                        if (!B.value) return;
                        B.value.querySelectorAll(".template-card-wrapper").forEach((e => {
                            e.querySelector("img[data-src]") && ue.value.observe(e)
                        }))
                    }))
                },
                me = () => {
                    if (G.value) {
                        const {
                            scrollWidth: e,
                            clientWidth: t,
                            scrollLeft: a
                        } = G.value, l = e - t, o = 1;
                        Z.value = a > o, J.value = l > o && a < l - o
                    } else Z.value = !1, J.value = !1
                },
                he = Xe((() => {
                    me()
                }), 150),
                ge = () => {
                    if (G.value) {
                        const {
                            clientWidth: e
                        } = G.value, t = Math.min(200, .6 * e);
                        G.value.scrollBy({
                            left: t,
                            behavior: "smooth"
                        })
                    }
                },
                _e = () => {
                    if (G.value) {
                        const {
                            clientWidth: e
                        } = G.value, t = Math.min(200, .6 * e);
                        G.value.scrollBy({
                            left: -t,
                            behavior: "smooth"
                        })
                    }
                },
                fe = () => {
                    _("back")
                },
                ye = () => {
                    te.value = !te.value
                },
                ke = e => {
                    _("editProjectName", e)
                },
                xe = async (e, t, a) => {
                    be(null), _("submitPrompt", e, t, a)
                },
                be = async e => {
                    if (!D.value) {
                        D.value = !0;
                        try {
                            let t, a;
                            if (e ? (t = e.content, a = e.type || "html") : (t = "", a = "html"), !e) {
                                const e = { ...m.project,
                                    session_state: { ...m.project.session_state || {},
                                        docs_agent: {
                                            content: t,
                                            type: le.value
                                        },
                                        is_blank: !0
                                    }
                                };
                                return void _("projectCreated", e)
                            }
                            const l = {
                                    type: "docs_agent",
                                    name: e.name,
                                    session_state: {
                                        steps: [],
                                        messages: [],
                                        docs_agent: {
                                            content: t,
                                            type: a
                                        },
                                        is_blank: !1,
                                        create_by_template: e ? e.name : null
                                    },
                                    client_generated_id: "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function(e) {
                                        const t = 16 * Math.random() | 0;
                                        return ("x" == e ? t : 3 & t | 8).toString(16)
                                    }))
                                },
                                o = await fetch("/api/project/create", {
                                    method: "POST",
                                    headers: {
                                        "Content-Type": "application/json"
                                    },
                                    body: JSON.stringify(l)
                                }),
                                s = await o.json();
                            if (-5 === s.status) return void(location.href = "/login?redirect_url=" + encodeURIComponent(window.location.href));
                            if (0 === s.status && s.data) {
                                const e = { ...m.project,
                                    id: s.data.id,
                                    name: s.data.name,
                                    session_state: l.session_state
                                };
                                _("projectCreated", e)
                            }
                        } catch (t) {} finally {
                            D.value = !1
                        }
                    }
                };
            return (l, o) => (t(), e("div", Gt, [a("div", Zt, [a("div", Vt, [a("div", {
                class: "back-icon cursor-pointer",
                onClick: fe
            }, [a("div", Jt, [w(c(Ee))])]), a("div", {
                class: "button left-toggle",
                onClick: i(ye, ["stop"])
            }, [a("div", Xt, [w(c(we))])])])]), c(P) ? (t(), h(V, {
                key: 0,
                ref_key: "agentTaskListRef",
                ref: A,
                hideLeftSide: te.value,
                onSetShowLeftSide: o[0] || (o[0] = e => {
                    te.value = e
                }),
                onEditProjectName: ke
            }, null, 8, ["hideLeftSide"])) : v("", !0), a("div", {
                ref_key: "mainContentRef",
                ref: B,
                class: p(["main-content", {
                    "full-width": te.value || (!1 === Q.value ? E.isMobile() : Q.value <= 1220)
                }]),
                style: T({
                    "--column_width": H.value + "px",
                    "--flow_item_padding": W.value + "px"
                })
            }, [a("div", Qt, [a("h2", null, r(l.$t("components.docs_template_selector.title1")), 1)]), N.value ? (t(), e("div", Kt, [w(F), a("p", null, r(l.$t("common.loading")) + "...", 1)])) : U.value ? (t(), e("div", Yt, [a("p", null, r(l.$t("common.error_loading_templates")), 1), a("button", {
                onClick: ce,
                class: "retry-button"
            }, r(l.$t("common.retry")), 1)])) : (t(), e("div", ea, [a("div", ta, [a("div", aa, [a("div", la, [w(ze, {
                ref: "inputWrapper"
            }, {
                default: S((() => [w(He, {
                    ref_key: "promptInputRef",
                    ref: M,
                    onSubmitPrompt: xe,
                    supportImages: !0,
                    useSuggestion: !1,
                    styleClass: "docs-template",
                    showPersonalizationButton: !1
                }, {
                    "bottom-in-textarea-wrapper": S((() => [a("div", oa, [a("div", sa, [(t(), e(u, null, d(ae, (e => a("button", {
                        key: e.value,
                        onClick: t => {
                            return a = e.value, void $.setUserSelectEditorType(a);
                            var a
                        },
                        class: p(["editor-option-button", {
                            active: le.value === e.value
                        }])
                    }, [le.value === e.value ? (t(), h(c(Ie), {
                        key: 0,
                        class: "check-icon"
                    })) : v("", !0), a("span", ia, r(e.label), 1)], 10, na))), 64))])])])),
                    _: 1
                }, 512)])),
                _: 1
            }, 512)]), v("", !0)])]), se.value.length > 0 ? (t(), e("div", ra, [Z.value ? (t(), e("div", {
                key: 0,
                class: "scroll-indicator left-scroll",
                onClick: _e
            }, [a("div", ca, [w(c(Ve))])])) : v("", !0), a("div", {
                class: "category-tabs",
                ref_key: "categoryTabsRef",
                ref: G,
                onScroll: o[1] || (o[1] = (...e) => c(he) && c(he)(...e))
            }, [(t(!0), e(u, null, d(se.value, (a => (t(), e("button", {
                key: a,
                class: p(["category-tab", {
                    active: O.value === a
                }]),
                onClick: e => O.value = a
            }, r(oe[a] || a), 11, ua)))), 128))], 544), J.value ? (t(), e("div", {
                key: 1,
                class: "scroll-indicator right-scroll",
                onClick: ge
            }, [a("div", da, [w(c(Ze))])])) : v("", !0)])) : v("", !0), a("div", pa, [(t(!0), e(u, null, d(q.value, ((s, n) => (t(), e("div", {
                key: n,
                class: "template-column"
            }, [(t(!0), e(u, null, d(s, ((s, n) => (t(), e("div", {
                key: s.id || n,
                class: "template-card-wrapper"
            }, [s.isBlank ? (t(), e("div", {
                key: 0,
                class: p(["template-card blank-template-card", {
                    "is-creating": D.value
                }]),
                onClick: o[2] || (o[2] = e => be(null)),
                style: T({
                    height: s.finalHeight + "px"
                })
            }, [a("div", va, [a("div", ma, [w(c(Je))]), a("h3", ha, r(l.$t("components.docs_template_selector.blank_document")), 1)])], 6)) : (t(), e("div", {
                key: 1,
                class: p(["template-card", {
                    "is-creating": D.value
                }]),
                onClick: e => be(s)
            }, [a("div", {
                class: "template-preview",
                style: T({
                    aspectRatio: s.screenshot_width && s.screenshot_height ? `${s.screenshot_width} / ${s.screenshot_height}` : "auto"
                })
            }, [s.screenshot_cdn_url ? (t(), e("img", {
                key: 0,
                "data-src": s.screenshot_cdn_url,
                alt: `${s.name}${s.classification?" - "+s.classification:""}`,
                class: "template-screenshot"
            }, null, 8, _a)) : s.screenshot_cdn_url ? v("", !0) : (t(), e("div", fa, [w(c(Ge))]))], 4)], 10, ga))])))), 128))])))), 128))])]))], 6), D.value ? (t(), e("div", {
                key: 1,
                ref_key: "loadingOverlayRef",
                ref: X,
                class: "main-content-loading-overlay",
                style: T(ie.value)
            }, [a("div", ya, [w(F), a("p", ka, r(l.$t("common.loading")) + "...", 1)])], 4)) : v("", !0)]))
        }
    }, [
        ["__scopeId", "data-v-55a30980"]
    ]),
    ba = {
        class: "modal-header"
    },
    ja = {
        class: "modal-title"
    },
    Ca = {
        class: "modal-content"
    },
    Sa = {
        class: "preview-section"
    },
    Ta = {
        class: "template-preview"
    },
    Ea = {
        key: 0,
        class: "image-placeholder"
    },
    La = {
        class: "loading-text"
    },
    Pa = ["src", "alt"],
    $a = {
        key: 0,
        class: "loading-overlay"
    },
    Ia = {
        key: 0,
        class: "thumbnails-container"
    },
    Da = ["onClick"],
    Aa = ["src", "alt"],
    Ma = {
        class: "template-details"
    },
    Ra = {
        class: "template-info"
    },
    Na = {
        class: "template-title"
    },
    Ua = {
        class: "template-specs-container"
    },
    Oa = {
        class: "template-description"
    },
    qa = {
        class: "template-specs"
    },
    Ba = {
        key: 0,
        class: "spec-tag color-spec"
    },
    Ha = {
        class: "color-label"
    },
    za = {
        class: "color-swatches"
    },
    Fa = ["title"],
    Wa = {
        class: "spec-tag"
    };
const Ga = l({
        name: "SlidesTemplateModal",
        props: {
            template: {
                type: Object,
                required: !0
            }
        },
        emits: ["close", "use"],
        setup(e) {
            const t = s(null),
                a = s(null),
                l = s(!1),
                o = s(!1),
                n = s(!1),
                i = s(!1),
                r = s(!1),
                c = () => {
                    r.value = window.innerWidth <= 768
                },
                u = j((() => e.template.pages ? e.template.pages.length : 0)),
                d = j((() => e.template.pages && e.template.pages.length > 0 ? e.template.pages.map((e => ({
                    thumbnail: e.thumbnail_url || e.screenshot_url,
                    screenshot: e.screenshot_url
                }))).filter((e => e.thumbnail && "" !== e.thumbnail.trim())) : [])),
                p = j((() => e.template.pages && e.template.pages.length > 0 ? e.template.pages[0].screenshot_url || "" : "https://via.placeholder.com/640x360/E5E5E5/999999?text=No+Preview")),
                v = j((() => t.value || p.value)),
                m = j((() => e.template.color && 0 !== e.template.color.length ? [e.template.color] : [])),
                h = j((() => (e.template.width || 1920) / (e.template.height || 1080) > 16 / 9 ? {
                    width: "100%",
                    height: "auto",
                    maxHeight: "100%"
                } : {
                    width: "auto",
                    height: "100%",
                    maxWidth: "100%"
                })),
                g = e => {
                    e !== v.value && (i.value = !0, t.value = e)
                },
                _ = () => {
                    if (!a.value) return;
                    const e = a.value;
                    l.value = e.scrollLeft > 0, o.value = e.scrollLeft < e.scrollWidth - e.clientWidth
                },
                f = () => {
                    _()
                },
                y = s(!1),
                w = s(0),
                x = () => {
                    r.value && (document.body.style.overflow = "hidden", document.body.style.position = "fixed", document.body.style.width = "100%")
                },
                C = () => {
                    r.value && (document.body.style.overflow = "", document.body.style.position = "", document.body.style.width = "")
                };
            return k(v, ((e, t) => {
                void 0 === t ? n.value = !1 : (n.value = !1, setTimeout((() => {
                    n.value || (i.value = !0)
                }), 100))
            })), b((() => {
                c(), window.addEventListener("resize", c), a.value && (a.value.addEventListener("scroll", f), _()), n.value = !1, x()
            })), I((() => {
                window.removeEventListener("resize", c), a.value && a.value.removeEventListener("scroll", f), C()
            })), {
                selectedThumbnail: t,
                thumbnailsScroll: a,
                canScrollLeft: l,
                canScrollRight: o,
                imageLoaded: n,
                isTransitioning: i,
                isMobile: r,
                isDragging: y,
                touchStartTime: w,
                pageCount: u,
                thumbnailUrls: d,
                defaultPreviewImage: p,
                currentPreviewImage: v,
                normalizedColors: m,
                previewImageStyle: h,
                selectThumbnail: g,
                handleThumbnailClick: (e, t) => {
                    if (y.value) return t.preventDefault(), void t.stopPropagation();
                    Date.now() - w.value < 200 ? setTimeout((() => {
                        y.value || g(e)
                    }), 50) : g(e)
                },
                handleMouseDown: e => {
                    if (r.value) return;
                    e.preventDefault();
                    const t = e.clientX,
                        l = Date.now(),
                        o = a.value,
                        s = o.scrollLeft;
                    w.value = l, y.value = !1;
                    const n = e => {
                            const a = e.clientX - t;
                            Math.abs(a) > 5 && (y.value = !0, o.style.cursor = "grabbing"), o.scrollLeft = s - a
                        },
                        i = () => {
                            y.value ? setTimeout((() => {
                                y.value = !1, o.style.cursor = ""
                            }), 100) : (y.value = !1, o.style.cursor = ""), document.removeEventListener("mousemove", n), document.removeEventListener("mouseup", i)
                        };
                    document.addEventListener("mousemove", n), document.addEventListener("mouseup", i)
                },
                scrollThumbnails: e => {
                    if (!a.value) return;
                    const t = a.value.scrollLeft;
                    "left" === e ? a.value.scrollTo({
                        left: t - 200,
                        behavior: "smooth"
                    }) : a.value.scrollTo({
                        left: t + 200,
                        behavior: "smooth"
                    })
                },
                onImageLoad: () => {
                    n.value = !0, i.value = !1
                },
                onImageError: () => {
                    n.value = !0, i.value = !1
                },
                handleTouchStart: e => {
                    const t = e.touches[0],
                        a = t.clientX,
                        l = t.clientY,
                        o = Date.now();
                    w.value = o, y.value = !1;
                    const s = e => {
                            const t = e.touches[0],
                                o = t.clientX - a,
                                s = t.clientY - l;
                            Math.sqrt(o * o + s * s) > 10 && (y.value = !0), Math.abs(o) > Math.abs(s) && e.preventDefault()
                        },
                        n = () => {
                            y.value ? setTimeout((() => {
                                y.value = !1
                            }), 100) : y.value = !1, document.removeEventListener("touchmove", s), document.removeEventListener("touchend", n)
                        };
                    document.addEventListener("touchmove", s, {
                        passive: !1
                    }), document.addEventListener("touchend", n)
                },
                preventBodyScroll: x,
                restoreBodyScroll: C,
                handleTouchFeedback: e => {
                    if (!r.value) return;
                    const t = e.currentTarget;
                    t.style.transform = "scale(0.95)", setTimeout((() => {
                        t.style.transform = ""
                    }), 150)
                }
            }
        }
    }, [
        ["render", function(l, o, s, n, c, m) {
            return t(), e("div", {
                class: "modal-overlay",
                onClick: o[14] || (o[14] = e => l.$emit("close")),
                onTouchstart: o[15] || (o[15] = (...e) => n.handleTouchStart && n.handleTouchStart(...e))
            }, [a("div", {
                class: "modal-container",
                onClick: o[13] || (o[13] = i((() => {}), ["stop"]))
            }, [a("div", ba, [a("h2", ja, r(l.$t("components.slides_template_modal.template_details")), 1), a("button", {
                onClick: o[0] || (o[0] = e => l.$emit("close")),
                class: "close-button",
                onTouchstart: o[1] || (o[1] = (...e) => n.handleTouchFeedback && n.handleTouchFeedback(...e))
            }, o[16] || (o[16] = [a("svg", {
                class: "close-icon",
                viewBox: "0 0 24 24"
            }, [a("path", {
                d: "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            })], -1)]), 32)]), a("div", Ca, [a("div", Sa, [a("div", Ta, [n.imageLoaded || n.isTransitioning ? v("", !0) : (t(), e("div", Ea, [o[17] || (o[17] = a("div", {
                class: "loading-spinner"
            }, null, -1)), a("span", La, r(l.$t("components.slides_template_modal.loading_preview")), 1)])), a("div", {
                class: p(["image-container", {
                    loading: !n.imageLoaded && n.isTransitioning
                }])
            }, [a("img", {
                src: n.currentPreviewImage,
                alt: s.template.name,
                class: p(["main-preview-image", {
                    "fade-in": n.imageLoaded
                }]),
                style: T(n.previewImageStyle),
                onLoad: o[2] || (o[2] = (...e) => n.onImageLoad && n.onImageLoad(...e)),
                onError: o[3] || (o[3] = (...e) => n.onImageError && n.onImageError(...e))
            }, null, 46, Pa), !n.imageLoaded && n.isTransitioning ? (t(), e("div", $a, o[18] || (o[18] = [a("div", {
                class: "loading-spinner"
            }, null, -1)]))) : v("", !0)], 2)]), n.thumbnailUrls && n.thumbnailUrls.length > 0 ? (t(), e("div", Ia, [P(a("button", {
                class: "nav-arrow nav-prev",
                onClick: o[4] || (o[4] = e => n.scrollThumbnails("left")),
                onTouchstart: o[5] || (o[5] = (...e) => n.handleTouchFeedback && n.handleTouchFeedback(...e))
            }, o[19] || (o[19] = [a("svg", {
                width: "16",
                height: "16",
                viewBox: "0 0 20 20",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg"
            }, [a("path", {
                d: "M12.9297 17.1406L5.85862 10.0696L12.9297 2.99849",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            })], -1)]), 544), [
                [$, n.canScrollLeft && !n.isMobile]
            ]), a("div", {
                class: "thumbnails-scroll",
                ref: "thumbnailsScroll",
                onTouchstart: o[7] || (o[7] = (...e) => n.handleTouchStart && n.handleTouchStart(...e)),
                onMousedown: o[8] || (o[8] = (...e) => n.handleMouseDown && n.handleMouseDown(...e))
            }, [(t(!0), e(u, null, d(n.thumbnailUrls, ((s, i) => (t(), e("div", {
                key: i,
                class: p(["thumbnail-item", {
                    active: n.currentPreviewImage === s.screenshot
                }]),
                onClick: e => n.handleThumbnailClick(s.screenshot, e),
                onTouchstart: o[6] || (o[6] = (...e) => n.handleTouchFeedback && n.handleTouchFeedback(...e))
            }, [a("img", {
                src: s.thumbnail,
                alt: `${l.$t("components.slides_template_modal.page")} ${i+1}`,
                class: "thumbnail-image"
            }, null, 8, Aa)], 42, Da)))), 128))], 544), P(a("button", {
                class: "nav-arrow nav-next",
                onClick: o[9] || (o[9] = e => n.scrollThumbnails("right")),
                onTouchstart: o[10] || (o[10] = (...e) => n.handleTouchFeedback && n.handleTouchFeedback(...e))
            }, o[20] || (o[20] = [a("svg", {
                width: "16",
                height: "16",
                viewBox: "0 0 20 20",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg"
            }, [a("path", {
                d: "M7.07031 17.1406L14.1414 10.0696L7.07031 2.99849",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            })], -1)]), 544), [
                [$, n.canScrollRight && !n.isMobile]
            ])])) : v("", !0)]), a("div", Ma, [a("div", Ra, [a("h3", Na, r(s.template.name), 1), a("div", Ua, [a("p", Oa, r(s.template.description), 1), a("div", qa, [n.normalizedColors.length > 0 && s.template.is_official ? (t(), e("span", Ba, [a("span", Ha, r(l.$t("components.slides_template_modal.theme_color")), 1), a("div", za, [(t(!0), e(u, null, d(n.normalizedColors, ((a, l) => (t(), e("div", {
                key: l,
                class: "color-swatch-mini",
                style: T({
                    backgroundColor: `rgb(${a[0]}, ${a[1]}, ${a[2]})`
                }),
                title: `rgb(${a[0]}, ${a[1]}, ${a[2]})`
            }, null, 12, Fa)))), 128))])])) : v("", !0), a("span", Wa, r(s.template.width) + " × " + r(s.template.height), 1), (t(!0), e(u, null, d(s.template.theme, (a => (t(), e("span", {
                key: a,
                class: "spec-tag"
            }, r(a), 1)))), 128)), (t(!0), e(u, null, d(s.template.style, (a => (t(), e("span", {
                key: a,
                class: "spec-tag"
            }, r(a), 1)))), 128))])])]), a("button", {
                onClick: o[11] || (o[11] = e => l.$emit("use", s.template)),
                class: "use-template-button",
                onTouchstart: o[12] || (o[12] = (...e) => n.handleTouchFeedback && n.handleTouchFeedback(...e))
            }, r(l.$t("components.slides_template_modal.use_this_template")), 33)])])])], 32)
        }],
        ["__scopeId", "data-v-0fb89a27"]
    ]),
    Za = {
        class: "template-card-inner"
    },
    Va = ["src", "alt"],
    Ja = ["src", "alt"],
    Xa = {
        key: 1,
        class: "template-icon"
    },
    Qa = {
        class: "template-use-button-overlay"
    },
    Ka = {
        __name: "TemplateCard",
        props: {
            template: {
                type: Object,
                required: !0
            },
            finalHeight: {
                type: Number,
                default: 200
            },
            isCreating: {
                type: Boolean,
                default: !1
            },
            carouselState: {
                type: Object,
                default: () => ({})
            },
            showDeleteButton: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["templateClick", "templateUse", "updateCarouselState", "deleteTemplate"],
        setup(l, {
            emit: n
        }) {
            const {
                t: m
            } = o(), h = l, g = n, _ = s(null), f = () => E.isMobile(), y = () => {
                h.template && g("templateUse", h.template)
            }, k = () => {
                h.template && g("deleteTemplate", h.template)
            }, x = j((() => {
                var e, t, a, l, o;
                if (f()) {
                    if ((null == (e = h.template.originalData) ? void 0 : e.pages) && h.template.originalData.pages.length > 0) {
                        const e = h.template.originalData.pages.filter((e => e.screenshot_url));
                        if (e.length > 0) return e[0].thumbnail_url || e[0].screenshot_url
                    }
                    return h.template.screenshot_cdn_url || ""
                }
                const s = null == (t = h.template) ? void 0 : t.id,
                    n = null == (a = h.carouselState) ? void 0 : a[s];
                if (!(null == (l = h.template.originalData) ? void 0 : l.pages) || 0 === h.template.originalData.pages.length) return h.template.screenshot_cdn_url || "";
                const i = h.template.originalData.pages.filter((e => e.screenshot_url));
                return 0 === i.length ? h.template.screenshot_cdn_url || "" : n && (null == (o = i[n.currentIndex]) ? void 0 : o.screenshot_url) || i[0].screenshot_url
            })), b = e => {
                var t, a;
                const l = null == (t = h.template) ? void 0 : t.id,
                    o = null == (a = h.carouselState) ? void 0 : a[l];
                return o ? o.currentIndex === e ? 1 : 0 : 0 === e ? 1 : 0
            }, S = e => {
                const t = e.currentTarget;
                t.style.transform = "scale(0.95)", setTimeout((() => {
                    t.style.transform = ""
                }), 150)
            };
            return C((() => {
                _.value && clearInterval(_.value)
            })), (o, s) => {
                var n;
                return t(), e("div", {
                    class: p(["template-card", {
                        "is-creating": l.isCreating
                    }]),
                    onClick: s[0] || (s[0] = e => {
                        h.template && g("templateClick", h.template)
                    }),
                    onMouseenter: s[1] || (s[1] = e => !f() && (e => {
                        var t;
                        const a = h.template;
                        if (!a || !a.id || !(null == (t = a.originalData) ? void 0 : t.pages) || a.originalData.pages.length <= 1) return;
                        const l = a.originalData.pages.filter((e => e.screenshot_url));
                        if (l.length <= 1) return;
                        const o = e.currentTarget.getBoundingClientRect(),
                            s = e.clientX - o.left,
                            n = o.width,
                            i = Math.floor(s / n * l.length),
                            r = Math.max(0, Math.min(i, l.length - 1));
                        g("updateCarouselState", {
                            templateId: a.id,
                            currentIndex: r,
                            totalImages: l.length
                        })
                    })(e)),
                    onMousemove: s[2] || (s[2] = e => !f() && (e => {
                        var t;
                        const a = h.template;
                        if (!a || !a.id || !(null == (t = a.originalData) ? void 0 : t.pages) || a.originalData.pages.length <= 1) return;
                        const l = a.originalData.pages.filter((e => e.screenshot_url));
                        if (l.length <= 1) return;
                        const o = e.currentTarget.getBoundingClientRect(),
                            s = e.clientX - o.left,
                            n = o.width,
                            i = Math.floor(s / n * l.length),
                            r = Math.max(0, Math.min(i, l.length - 1));
                        g("updateCarouselState", {
                            templateId: a.id,
                            currentIndex: r,
                            totalImages: l.length
                        })
                    })(e)),
                    onMouseleave: s[3] || (s[3] = e => !f() && (_.value && (clearInterval(_.value), _.value = null), void(h.template && h.template.id && g("updateCarouselState", {
                        templateId: h.template.id,
                        currentIndex: 0,
                        totalImages: 0
                    })))),
                    style: T({
                        height: l.finalHeight + "px"
                    })
                }, [a("div", Za, [!f() && (null == (n = l.template.originalData) ? void 0 : n.pages) && l.template.originalData.pages.length > 1 ? (t(!0), e(u, {
                    key: 0
                }, d(l.template.originalData.pages.filter((e => e.screenshot_url)), ((o, s) => (t(), e("div", {
                    key: s,
                    class: "template-screenshot-container",
                    style: T({
                        opacity: b(s)
                    })
                }, [a("img", {
                    src: o.thumbnail_url || o.screenshot_url,
                    alt: l.template.name,
                    class: "template-screenshot"
                }, null, 8, Va)], 4)))), 128)) : (t(), e(u, {
                    key: 1
                }, [x.value ? (t(), e("img", {
                    key: 0,
                    src: x.value,
                    alt: l.template.name,
                    class: "template-screenshot"
                }, null, 8, Ja)) : (t(), e("div", Xa, [w(c(te))]))], 64)), a("div", Qa, [a("button", {
                    class: "template-use-button",
                    onClick: i(y, ["stop"]),
                    onTouchstart: S
                }, r(c(m)("components.agents.image_studio.apply")), 33)])]), l.showDeleteButton ? (t(), e("div", {
                    key: 0,
                    class: "remove",
                    onClick: i(k, ["stop"])
                }, [w(c(it))])) : v("", !0)], 38)
            }
        }
    },
    Ya = l(Ka, [
        ["__scopeId", "data-v-091314e0"]
    ]),
    el = {
        class: "modal-header"
    },
    tl = {
        class: "modal-title"
    },
    al = {
        class: "modal-content"
    },
    ll = {
        class: "guide-section"
    },
    ol = {
        class: "section-title"
    },
    sl = {
        class: "video-container"
    },
    nl = {
        class: "guide-text"
    },
    il = {
        class: "guide-section more-ways-section"
    },
    rl = {
        class: "section-title"
    },
    cl = {
        class: "coming-soon"
    },
    ul = {
        class: "coming-soon-icon"
    },
    dl = {
        class: "coming-soon-text"
    },
    pl = l({
        __name: "AddTemplateGuideModal",
        emits: ["close"],
        setup(l, {
            emit: n
        }) {
            const {
                t: u
            } = o(), d = n, p = s(null), v = () => {
                d("close")
            }, m = e => {
                "Escape" === e.key && v()
            };
            return b((() => {
                document.addEventListener("keydown", m), p.value && p.value.play().catch((() => {}))
            })), C((() => {
                document.removeEventListener("keydown", m)
            })), (l, o) => (t(), e("div", {
                class: "add-template-guide-modal-overlay",
                onClick: v
            }, [a("div", {
                class: "add-template-guide-modal",
                onClick: o[0] || (o[0] = i((() => {}), ["stop"]))
            }, [a("div", el, [a("h3", tl, r(c(u)("components.slides.add_template_guide_title")), 1), a("button", {
                class: "close-button",
                onClick: v
            }, [w(c(it))])]), a("div", al, [a("div", ll, [a("h4", ol, r(c(u)("components.slides.add_from_history_projects")), 1), a("div", sl, [a("video", {
                ref_key: "videoRef",
                ref: p,
                src: "https://cdn1.genspark.ai/user-upload-image/slide_agent/assets/slides_template_guide.mp4",
                autoplay: "",
                loop: "",
                muted: "",
                playsinline: "",
                class: "guide-video"
            }, null, 512)]), a("div", nl, [a("p", null, r(c(u)("components.slides.add_template_guide_description")), 1)])]), a("div", il, [a("h4", rl, r(c(u)("components.slides.more_ways_to_add")), 1), a("div", cl, [a("div", ul, [w(c(ct))]), a("p", dl, r(c(u)("components.slides.coming_soon")), 1)])])])])]))
        }
    }, [
        ["__scopeId", "data-v-cb17b1b2"]
    ]),
    vl = {
        class: "slides-template-selector"
    },
    ml = {
        class: "header-controls"
    },
    hl = {
        class: "header-left"
    },
    gl = {
        class: "icon"
    },
    _l = {
        class: "icon"
    },
    fl = {
        class: "header"
    },
    yl = {
        class: "prompt-input-section"
    },
    kl = {
        class: "prompt-input-wrapper"
    },
    wl = {
        class: "prompt-input-wrapper-upper"
    },
    xl = {
        class: "tab-navigation"
    },
    bl = {
        key: 0,
        class: "search-filter-section"
    },
    jl = {
        class: "filter-group"
    },
    Cl = {
        class: "dropdown-text"
    },
    Sl = {
        key: 0,
        class: "dropdown-menu"
    },
    Tl = ["checked"],
    El = {
        class: "dropdown-item-text"
    },
    Ll = ["onClick"],
    Pl = ["checked"],
    $l = {
        class: "dropdown-item-text"
    },
    Il = {
        key: 1,
        class: "dropdown-loading"
    },
    Dl = {
        class: "filter-group"
    },
    Al = {
        class: "dropdown-text"
    },
    Ml = {
        key: 0,
        class: "dropdown-menu"
    },
    Rl = ["checked"],
    Nl = {
        class: "dropdown-item-text"
    },
    Ul = ["onClick"],
    Ol = ["checked"],
    ql = {
        class: "dropdown-item-text"
    },
    Bl = {
        key: 1,
        class: "dropdown-loading"
    },
    Hl = {
        class: "filter-group"
    },
    zl = {
        class: "dropdown-text"
    },
    Fl = {
        key: 0,
        class: "dropdown-menu"
    },
    Wl = ["checked"],
    Gl = {
        class: "dropdown-item-text"
    },
    Zl = ["checked"],
    Vl = {
        class: "dropdown-item-text"
    },
    Jl = {
        key: 0,
        class: "filter-group"
    },
    Xl = {
        class: "tab-content"
    },
    Ql = {
        key: 0,
        class: "explore-content"
    },
    Kl = {
        key: 0,
        class: "template-waterfall"
    },
    Yl = {
        key: 1,
        class: "waterfall-error-section"
    },
    eo = {
        key: 2,
        class: "waterfall-empty-section"
    },
    to = {
        class: "empty-icon"
    },
    ao = {
        class: "empty-text"
    },
    lo = {
        key: 3,
        class: "template-waterfall"
    },
    oo = {
        class: "blank-template-content"
    },
    so = {
        class: "blank-icon"
    },
    no = {
        class: "blank-text"
    },
    io = {
        key: 0,
        class: "scroll-sentinel"
    },
    ro = {
        key: 4,
        class: "loading-more-section"
    },
    co = {
        class: "loading-more-text"
    },
    uo = {
        key: 1,
        class: "my-templates-content"
    },
    po = {
        key: 0,
        class: "template-waterfall"
    },
    vo = {
        key: 1,
        class: "waterfall-error-section"
    },
    mo = {
        key: 2,
        class: "template-waterfall"
    },
    ho = {
        class: "add-template-content"
    },
    go = {
        class: "add-template-icon"
    },
    _o = {
        class: "add-template-text"
    },
    fo = {
        key: 0,
        class: "scroll-sentinel user-scroll-sentinel"
    },
    yo = {
        key: 3,
        class: "loading-more-section"
    },
    ko = {
        class: "loading-more-text"
    },
    wo = {
        class: "loading-content"
    },
    xo = {
        class: "loading-text"
    },
    bo = l({
        __name: "SlidesTemplateSelector",
        props: {
            project: {
                type: Object,
                default: null
            },
            initialTab: {
                type: String,
                default: "explore"
            }
        },
        emits: ["projectCreated", "back", "editProjectName", "submitPrompt"],
        setup(l, {
            emit: n
        }) {
            x((e => ({
                "0617a526": Pe.value + "px"
            })));
            const m = l,
                {
                    t: g,
                    locale: _
                } = o(),
                f = Be(),
                P = n,
                $ = y("currentUser"),
                I = y("jsBridge"),
                D = s(!1),
                A = s(null),
                M = s(null),
                R = s(!0),
                N = s(null),
                U = s(m.initialTab),
                O = s([]),
                q = s(!1),
                B = s(!1),
                H = s(!1),
                z = s(null),
                W = s(!1),
                G = s(null),
                Z = s(!1),
                J = s(!1),
                X = s([]),
                Q = s(!1),
                K = s(!1),
                Y = s(0),
                ee = s(20),
                te = s(!1),
                ae = s(!1);
            s(null);
            const le = s(null),
                oe = s({}),
                se = s(new Map),
                ne = s(""),
                ie = s([]),
                re = s([]),
                ce = s(""),
                ue = s([]),
                de = s([]),
                pe = s(0),
                ve = s(20),
                me = s(!1),
                he = s("usage_count"),
                ge = j((() => {
                    const e = _.value || "en-US";
                    return "ja-JP" === e || "en-US" === e
                })),
                _e = s(!1),
                fe = s(null),
                ye = s(!1),
                ke = s(null),
                xe = s(!1),
                be = s(!1),
                je = s(!1),
                Ce = s(!1),
                Se = s(null),
                Te = s(null),
                Le = s(null),
                Pe = s(240),
                $e = s(null),
                Ie = s([]),
                De = s(!1),
                Me = Xe((() => {
                    $e.value && (De.value = $e.value.clientWidth)
                }), 200),
                Re = s(0),
                Ne = Xe((() => {
                    $e.value && Re.value++
                }), 16),
                Ue = j((() => {
                    if (Re.value, !$e.value) return {
                        position: "fixed",
                        top: "64px",
                        left: "0",
                        right: "0",
                        bottom: "0",
                        zIndex: 9999
                    };
                    const e = $e.value.getBoundingClientRect();
                    return {
                        position: "fixed",
                        top: `${e.top}px`,
                        left: `${e.left}px`,
                        width: `${e.width}px`,
                        height: `${e.height}px`,
                        zIndex: 9999
                    }
                })),
                Oe = () => !1 === De.value ? E.isMobile() : De.value <= 768,
                qe = j((() => g("common.delete_confirmation") || "Delete Confirmation")),
                Fe = j((() => {
                    var e, t, a;
                    const l = (null == (t = null == (e = G.value) ? void 0 : e.originalData) ? void 0 : t.name) || (null == (a = G.value) ? void 0 : a.name) || "this template";
                    return g("components.slides.delete_template_confirm_message", {
                        name: l
                    }) || `Are you sure you want to delete "${l}"? This action cannot be undone.`
                })),
                We = j((() => g("common.cancel") || "Cancel")),
                Ge = j((() => g("common.delete") || "Delete")),
                Ze = () => {
                    R.value = !R.value
                },
                Ve = () => {
                    E.isGensparkApp() && I.value ? I.value.callHandler("goBack", {}) : P("back")
                },
                Je = async (e, t, a) => {
                    await dt(e, t, a)
                },
                Qe = Ae(),
                {
                    setSelectedTemplate: Ke
                } = Qe,
                Ye = e => {
                    if (0 === e || !1 === e) return [];
                    let t = 0,
                        a = 0;
                    ({
                        columnNum: t,
                        columnWidth: a
                    } = at(e)), Pe.value = a;
                    const l = Array.from({
                            length: t
                        }, (() => [])),
                        o = Array.from({
                            length: t
                        }, (() => 0)),
                        s = ((e = ve.value) => {
                            const t = [];
                            for (let a = 0; a < e; a++) t.push({
                                id: `skeleton-${a}`,
                                name: `Skeleton Template ${a+1}`,
                                content: "",
                                screenshot_cdn_url: "",
                                screenshot_width: 1280,
                                screenshot_height: 720,
                                isSkeleton: !0
                            });
                            return t
                        })();
                    for (let n = 0; n < s.length; n++) {
                        const e = s[n];
                        let t = 200;
                        if (e.screenshot_width && e.screenshot_height) {
                            t = e.screenshot_height * a / e.screenshot_width
                        }
                        e.finalHeight = t;
                        const i = o.reduce(((e, t, a, l) => t < l[e] ? a : e), 0);
                        l[i].push(e), o[i] += e.finalHeight + 20
                    }
                    return l
                },
                et = j((() => q.value || Q.value ? Ye(De.value) : [])),
                tt = j((() => lt(X.value, De.value, !1, !0))),
                at = e => {
                    let t = !1;
                    let a, l, o = 32;
                    const s = e;
                    return a < 1 && (a = 1), e <= 768 ? (t = !0, a = 2, o = 16) : e <= 1220 ? (t = !0, o = 16, a = Math.floor((s - 2 * o) / 256)) : (a = Math.floor((s - 2 * o) / 256), t = !1), l = Math.floor((s - 2 * o - 16 * (a - 1)) / a), {
                        narrowScreenMode: t,
                        columnNum: a,
                        columnWidth: l
                    }
                },
                lt = (e, t, a = !0, l = !1) => {
                    if (0 === t || !1 === t) return [];
                    if (!(e && 0 !== e.length || a || l)) return [];
                    let o = 0,
                        s = 0;
                    ({
                        columnNum: o,
                        columnWidth: s
                    } = at(t)), Pe.value = s;
                    const n = Array.from({
                            length: o
                        }, (() => [])),
                        i = Array.from({
                            length: o
                        }, (() => 0));
                    if (a) {
                        const e = {
                            id: "blank",
                            isBlank: !0,
                            screenshot_width: 1280,
                            screenshot_height: 720
                        };
                        let t = 200;
                        if (e.screenshot_width && e.screenshot_height) {
                            t = e.screenshot_height * s / e.screenshot_width
                        }
                        e.finalHeight = t;
                        const a = 0;
                        n[a].push(e), i[a] += e.finalHeight + 20
                    }
                    if (l) {
                        const e = {
                            id: "add-template",
                            isAddTemplate: !0,
                            screenshot_width: 1280,
                            screenshot_height: 720
                        };
                        let t = 200;
                        if (e.screenshot_width && e.screenshot_height) {
                            t = e.screenshot_height * s / e.screenshot_width
                        }
                        e.finalHeight = t;
                        const a = 0;
                        n[a].push(e), i[a] += e.finalHeight + 20
                    }
                    if (e && e.length > 0)
                        for (let r = 0; r < e.length; r++) {
                            const t = e[r];
                            let a = 200;
                            if (t.screenshot_width && t.screenshot_height) {
                                a = t.screenshot_height * s / t.screenshot_width
                            }
                            t.finalHeight = a;
                            const l = i.reduce(((e, t, a, l) => t < l[e] ? a : e), 0);
                            n[l].push(t), i[l] += t.finalHeight + 20
                        }
                    return n
                },
                ot = e => {
                    st(e)
                },
                st = e => {
                    z.value = e.originalData || e, H.value = !0
                },
                nt = () => {
                    H.value = !1, z.value = null
                },
                it = async e => {
                    if (!D.value) try {
                        const t = e.originalData || e,
                            a = {
                                id: t.id,
                                name: t.name,
                                content: t.description || e.content,
                                screenshot_cdn_url: e.screenshot_cdn_url,
                                screenshot_width: e.screenshot_width,
                                screenshot_height: e.screenshot_height,
                                pages: t.pages || [],
                                theme: t.theme || [],
                                style: t.style || []
                            };
                        Ke(a), M.value && (await L(), M.value.searchInputFocus ? M.value.searchInputFocus() : M.value.focus())
                    } catch (t) {}
                },
                dt = async (e = "", t = [], a = []) => {
                    if (!D.value) {
                        D.value = !0;
                        try {
                            const l = { ...m.project,
                                session_state: { ...m.project.session_state || {},
                                    file_contents: [],
                                    meta_data: {},
                                    is_blank: !0,
                                    initial_prompt: e
                                }
                            };
                            P("projectCreated", l), e && P("submitPrompt", e, t, a)
                        } catch (l) {} finally {
                            D.value = !1
                        }
                    }
                },
                pt = async (e = !1) => {
                    le.value && le.value.abort();
                    const t = new AbortController;
                    le.value = t, e ? ae.value = !0 : (Q.value = !0, K.value = !1);
                    try {
                        const a = new URLSearchParams({
                                page: Y.value.toString(),
                                size: ee.value.toString(),
                                sort_by: "created_time"
                            }),
                            l = await fetch(`/api/slides_templates/user?${a}`, {
                                signal: t.signal
                            });
                        if (!l.ok) throw new Error(`HTTP ${l.status}: ${g("components.slides.failed_to_load_templates")}`);
                        const o = await l.json();
                        if (!o.templates || !Array.isArray(o.templates)) throw new Error(g("components.slides.invalid_api_response"));
                        const s = o.templates.map((e => e.id && e.name ? {
                            id: e.id,
                            project_id: e.project_id,
                            name: e.name || "Untitled Template",
                            content: e.description || "",
                            screenshot_cdn_url: e.pages && e.pages.length > 0 && (e.pages[0].thumbnail_url || e.pages[0].screenshot_url) || "",
                            screenshot_width: e.width || 1280,
                            screenshot_height: e.height || 720,
                            originalData: e
                        } : null)).filter(Boolean);
                        X.value = e ? [...X.value, ...s] : s, te.value = o.has_more
                    } catch (a) {
                        if ("AbortError" === a.name) return;
                        le.value === t && (e || (K.value = !0, X.value = []))
                    } finally {
                        le.value === t && (e ? ae.value = !1 : Q.value = !1)
                    }
                },
                vt = () => (Y.value = 0, pt()),
                mt = async (e = !1) => {
                    fe.value && fe.value.abort();
                    const t = new AbortController;
                    fe.value = t, e ? ye.value = !0 : (q.value = !0, B.value = !1);
                    try {
                        const a = new URLSearchParams({
                            keyword: ne.value,
                            page: pe.value.toString(),
                            size: ve.value.toString(),
                            sort_by: he.value
                        });
                        if (ie.value.length > 0 && ie.value.forEach((e => {
                                a.append("styles", e)
                            })), re.value.length > 0 && re.value.forEach((e => {
                                a.append("themes", e)
                            })), ce.value) {
                            const e = ce.value,
                                t = parseInt(e.slice(1, 3), 16),
                                l = parseInt(e.slice(3, 5), 16),
                                o = parseInt(e.slice(5, 7), 16);
                            a.append("color_rgb", JSON.stringify([t, l, o]))
                        }
                        if (_e.value) {
                            const e = _.value || "en-US";
                            a.append("lang_filter", e)
                        }
                        const l = await fetch(`/api/slides_templates/search?${a}`, {
                            signal: t.signal
                        });
                        if (!l.ok) throw new Error(`HTTP ${l.status}: ${g("components.slides.failed_to_load_templates")}`);
                        const o = await l.json();
                        if (!o.templates || !Array.isArray(o.templates)) throw new Error(g("components.slides.invalid_api_response"));
                        const s = o.templates.map((e => e.id && e.name ? {
                            id: e.id,
                            project_id: e.project_id,
                            name: e.name || "Untitled Template",
                            content: e.description || "",
                            screenshot_cdn_url: e.pages && e.pages.length > 0 && (e.pages[0].thumbnail_url || e.pages[0].screenshot_url) || "",
                            screenshot_width: e.width || 1280,
                            screenshot_height: e.height || 720,
                            originalData: e
                        } : null)).filter(Boolean);
                        O.value = e ? [...O.value, ...s] : s, me.value = o.has_more
                    } catch (a) {
                        if ("AbortError" === a.name) return;
                        fe.value === t && (e || (B.value = !0, O.value = []))
                    } finally {
                        fe.value === t && (e ? ye.value = !1 : q.value = !1)
                    }
                },
                ht = () => {
                    ke.value && ke.value.disconnect(), ke.value = new IntersectionObserver((e => {
                        e.forEach((e => {
                            e.isIntersecting && ("explore" === U.value && me.value && !ye.value ? (async () => {
                                !ye.value && me.value && (pe.value++, await mt(!0))
                            })() : "my" === U.value && te.value && !ae.value && (async () => {
                                !ae.value && te.value && (Y.value++, await pt(!0))
                            })())
                        }))
                    }), {
                        root: $e.value,
                        rootMargin: "0px 0px 200px 0px",
                        threshold: .1
                    }), L((() => {
                        const e = "explore" === U.value ? ".scroll-sentinel:not(.user-scroll-sentinel)" : ".user-scroll-sentinel",
                            t = document.querySelector(e);
                        t && ke.value && ke.value.observe(t)
                    }))
                },
                gt = () => (pe.value = 0, mt()),
                _t = () => {
                    pe.value = 0, mt()
                };
            Xe(_t, 300);
            const ft = e => {
                    P("editProjectName", e)
                },
                yt = async e => {
                    var t, a;
                    const l = {
                        originalData: e,
                        screenshot_cdn_url: (null == (a = null == (t = e.pages) ? void 0 : t[0]) ? void 0 : a.screenshot_url) || "",
                        screenshot_width: e.width || 1280,
                        screenshot_height: e.height || 720,
                        content: e.description
                    };
                    await it(l), nt()
                },
                kt = e => {
                    var t;
                    e && (null == (t = e.originalData) ? void 0 : t.id) ? (G.value = e, W.value = !0) : f.error(g("components.slides.delete_template_error"))
                },
                wt = async () => {
                    var e;
                    if (!G.value || !(null == (e = G.value.originalData) ? void 0 : e.id)) return f.error(g("components.slides.delete_template_error")), void(G.value = null);
                    Z.value = !0, W.value = !0;
                    try {
                        const e = G.value.originalData.id,
                            t = await fetch(`/api/slides_templates/${e}`, {
                                method: "DELETE"
                            });
                        if (t.ok) {
                            const t = X.value.findIndex((t => {
                                var a;
                                return (null == (a = t.originalData) ? void 0 : a.id) === e
                            })); - 1 !== t && X.value.splice(t, 1), f.success(g("components.slides.delete_template_success"))
                        } else {
                            const e = (await t.json().catch((() => ({})))).detail || g("components.slides.delete_template_error");
                            f.error(e)
                        }
                    } catch (t) {
                        f.error(g("components.slides.delete_template_error"))
                    } finally {
                        Z.value = !1, G.value = null, W.value = !1
                    }
                },
                xt = () => {
                    Z.value || (W.value = !1, G.value = null, Z.value = !1)
                },
                bt = e => {
                    const {
                        templateId: t,
                        currentIndex: a,
                        totalImages: l
                    } = e;
                    0 === l ? oe.value[t] && (oe.value[t].currentIndex = 0) : oe.value[t] ? oe.value[t].currentIndex = a : oe.value[t] = {
                        currentIndex: a,
                        totalImages: l
                    }
                },
                jt = s(null);
            let Ct = [],
                St = null;
            const Tt = () => {
                    jt.value && jt.value.disconnect(), St && St.cancel(), Ct = [], St = Xe((() => {
                        Ct.forEach((e => {
                            const t = e.querySelector("img[data-src]");
                            t && (t.src = t.dataset.src, t.removeAttribute("data-src"))
                        })), Ct = []
                    }), 100);
                    const e = {
                        root: $e.value,
                        rootMargin: "0px 0px 300px 0px",
                        threshold: 0
                    };
                    jt.value = new IntersectionObserver(((e, t) => {
                        let a = !1;
                        e.forEach((e => {
                            e.isIntersecting && (Ct.push(e.target), t.unobserve(e.target), a = !0)
                        })), a && St()
                    }), e), L((() => {
                        if (!$e.value) return;
                        $e.value.querySelectorAll(".template-card, .blank-template-card").forEach((e => {
                            e.querySelector("img[data-src]") && jt.value.observe(e)
                        }))
                    }))
                },
                Et = e => {
                    "Escape" === e.key && H.value && nt()
                },
                Lt = () => {
                    xe.value = !xe.value, xe.value && (be.value = !1, je.value = !1, Ce.value = !1)
                },
                Pt = () => {
                    be.value = !be.value, be.value && (xe.value = !1, je.value = !1, Ce.value = !1)
                },
                $t = () => {
                    Ce.value = !Ce.value, Ce.value && (xe.value = !1, be.value = !1, je.value = !1)
                },
                It = () => {
                    ie.value = [], _t()
                },
                Dt = () => {
                    re.value = [], _t()
                },
                At = e => {
                    he.value = e, _t()
                },
                Mt = () => {
                    _e.value = !_e.value, _t()
                },
                Rt = e => e.replace(/_/g, " ").replace(/\b\w/g, (e => e.toUpperCase())),
                Nt = e => {
                    Se.value && !Se.value.contains(e.target) && (xe.value = !1), Te.value && !Te.value.contains(e.target) && (be.value = !1), Le.value && !Le.value.contains(e.target) && (Ce.value = !1)
                };
            return b((async () => {
                Oe() && (R.value = !0), window.addEventListener("resize", Me), window.addEventListener("resize", Ne), window.addEventListener("scroll", Ne), document.addEventListener("keydown", Et), document.addEventListener("click", Nt), setTimeout((() => {
                    Me()
                }), 0), "my" === U.value ? vt() : gt(), (async () => {
                    try {
                        const e = await fetch("/api/slides_templates/filters");
                        if (e.ok) {
                            const t = await e.json();
                            ue.value = t.styles || [], de.value = t.themes || []
                        } else ue.value = [], de.value = []
                    } catch (e) {
                        ue.value = [], de.value = []
                    }
                })(), ht()
            })), C((() => {
                window.removeEventListener("resize", Me), window.removeEventListener("resize", Ne), window.removeEventListener("scroll", Ne), document.removeEventListener("keydown", Et), document.removeEventListener("click", Nt), jt.value && jt.value.disconnect(), ke.value && ke.value.disconnect(), St && St.cancel(), fe.value && fe.value.abort(), le.value && le.value.abort(), se.value.forEach((e => {
                    clearInterval(e)
                })), se.value.clear(), oe.value = {}
            })), k((() => U.value), (e => {
                "my" === e ? vt() : "explore" === e && gt(), L((() => {
                    ht()
                }))
            })), k((() => [O.value, De.value]), (() => {
                Ie.value = lt(O.value, De.value), Tt(), L((() => {
                    ht()
                }))
            })), k((() => [X.value, De.value]), (() => {
                Tt()
            })), k((() => [R.value, De.value, D.value]), (() => {
                D.value && L((() => {
                    Ne()
                }))
            }), {
                immediate: !0
            }), (l, o) => (t(), e("div", vl, [a("div", ml, [a("div", hl, [a("div", {
                class: "back-icon cursor-pointer",
                onClick: Ve
            }, [a("div", gl, [w(c(Ee))])]), a("div", {
                class: "button left-toggle",
                onClick: i(Ze, ["stop"])
            }, [a("div", _l, [w(c(we))])])])]), c($) ? (t(), h(V, {
                key: 0,
                ref_key: "agentTaskListRef",
                ref: A,
                hideLeftSide: R.value,
                onSetShowLeftSide: o[0] || (o[0] = e => {
                    R.value = e
                }),
                onEditProjectName: ft
            }, null, 8, ["hideLeftSide"])) : v("", !0), a("div", {
                ref_key: "mainContentRef",
                ref: $e,
                class: p(["main-content", {
                    "full-width": R.value || Oe()
                }]),
                style: {}
            }, [a("div", fl, [a("h2", null, r(c(g)("components.slides.ready_to_create_slides")), 1)]), a("div", yl, [a("div", kl, [a("div", wl, [w(ze, {
                ref: "inputWrapper"
            }, {
                default: S((() => [w(He, {
                    ref_key: "promptInputRef",
                    ref: M,
                    onSubmitPrompt: Je,
                    supportImages: !0,
                    useSuggestion: !1,
                    styleClass: "slides-template",
                    showPersonalizationButton: !1,
                    placeholder: c(g)("components.slides.enter_presentation_topic")
                }, null, 8, ["placeholder"])])),
                _: 1
            }, 512)])])]), a("div", xl, [a("button", {
                class: p(["tab-button", {
                    active: "explore" === U.value
                }]),
                onClick: o[1] || (o[1] = e => U.value = "explore")
            }, r(c(g)("components.slides.explore")), 3), a("button", {
                class: p(["tab-button", {
                    active: "my" === U.value
                }]),
                onClick: o[2] || (o[2] = e => U.value = "my")
            }, r(c(g)("components.slides.my_templates")), 3)]), "explore" === U.value ? (t(), e("div", bl, [a("div", jl, [a("div", {
                class: "dropdown-container",
                ref_key: "styleDropdownContainer",
                ref: Se
            }, [a("button", {
                onClick: Lt,
                class: p(["dropdown-trigger", {
                    active: xe.value
                }])
            }, [a("span", Cl, r(0 === ie.value.length ? g("components.slides.all_styles") : 1 === ie.value.length ? Rt(ie.value[0]) : g("components.slides.styles_selected", {
                count: ie.value.length
            })), 1), w(c(ut), {
                class: p(["dropdown-arrow", {
                    rotated: xe.value
                }])
            }, null, 8, ["class"])], 2), xe.value ? (t(), e("div", Sl, [a("div", {
                class: "dropdown-item",
                onClick: i(It, ["stop"])
            }, [a("input", {
                type: "checkbox",
                checked: 0 === ie.value.length,
                class: "dropdown-checkbox"
            }, null, 8, Tl), a("span", El, r(c(g)("components.slides.all_styles")), 1)]), ue.value.length > 0 ? (t(!0), e(u, {
                key: 0
            }, d(ue.value, (l => (t(), e("div", {
                key: l,
                class: "dropdown-item",
                onClick: i((e => (e => {
                    const t = ie.value.indexOf(e);
                    t > -1 ? ie.value.splice(t, 1) : ie.value.push(e), _t()
                })(l)), ["stop"])
            }, [a("input", {
                type: "checkbox",
                checked: ie.value.includes(l),
                class: "dropdown-checkbox"
            }, null, 8, Pl), a("span", $l, r(Rt(l)), 1)], 8, Ll)))), 128)) : (t(), e("div", Il, r(c(g)("components.slides.loading_styles")), 1))])) : v("", !0)], 512)]), a("div", Dl, [a("div", {
                class: "dropdown-container",
                ref_key: "themeDropdownContainer",
                ref: Te
            }, [a("button", {
                onClick: Pt,
                class: p(["dropdown-trigger", {
                    active: be.value
                }])
            }, [a("span", Al, r(0 === re.value.length ? g("components.slides.all_themes") : 1 === re.value.length ? Rt(re.value[0]) : g("components.slides.themes_selected", {
                count: re.value.length
            })), 1), w(c(ut), {
                class: p(["dropdown-arrow", {
                    rotated: be.value
                }])
            }, null, 8, ["class"])], 2), be.value ? (t(), e("div", Ml, [a("div", {
                class: "dropdown-item",
                onClick: i(Dt, ["stop"])
            }, [a("input", {
                type: "checkbox",
                checked: 0 === re.value.length,
                class: "dropdown-checkbox"
            }, null, 8, Rl), a("span", Nl, r(c(g)("components.slides.all_themes")), 1)]), de.value.length > 0 ? (t(!0), e(u, {
                key: 0
            }, d(de.value, (l => (t(), e("div", {
                key: l,
                class: "dropdown-item",
                onClick: i((e => (e => {
                    const t = re.value.indexOf(e);
                    t > -1 ? re.value.splice(t, 1) : re.value.push(e), _t()
                })(l)), ["stop"])
            }, [a("input", {
                type: "checkbox",
                checked: re.value.includes(l),
                class: "dropdown-checkbox"
            }, null, 8, Ol), a("span", ql, r(Rt(l)), 1)], 8, Ul)))), 128)) : (t(), e("div", Bl, r(c(g)("components.slides.loading_themes")), 1))])) : v("", !0)], 512)]), a("div", Hl, [a("div", {
                class: "dropdown-container",
                ref_key: "sortDropdownContainer",
                ref: Le
            }, [a("button", {
                onClick: $t,
                class: p(["dropdown-trigger", {
                    active: Ce.value
                }])
            }, [a("span", zl, r("usage_count" === he.value ? `${g("components.slides.sort_by")}: ${g("components.slides.popularity")}` : `${g("components.slides.sort_by")}: ${g("components.slides.newest")}`), 1), w(c(ut), {
                class: p(["dropdown-arrow", {
                    rotated: Ce.value
                }])
            }, null, 8, ["class"])], 2), Ce.value ? (t(), e("div", Fl, [a("div", {
                class: "dropdown-item",
                onClick: o[3] || (o[3] = i((e => At("usage_count")), ["stop"]))
            }, [a("input", {
                type: "radio",
                checked: "usage_count" === he.value,
                class: "dropdown-radio",
                name: "sort-option"
            }, null, 8, Wl), a("span", Gl, r(c(g)("components.slides.popularity")), 1)]), a("div", {
                class: "dropdown-item",
                onClick: o[4] || (o[4] = i((e => At("created_time")), ["stop"]))
            }, [a("input", {
                type: "radio",
                checked: "created_time" === he.value,
                class: "dropdown-radio",
                name: "sort-option"
            }, null, 8, Zl), a("span", Vl, r(c(g)("components.slides.newest")), 1)])])) : v("", !0)], 512)]), ge.value ? (t(), e("div", Jl, [a("button", {
                onClick: Mt,
                class: p(["language-filter-button", {
                    active: _e.value
                }])
            }, r(c(g)("components.slides.my_language_only")), 3)])) : v("", !0)])) : v("", !0), a("div", Xl, ["explore" === U.value ? (t(), e("div", Ql, [q.value ? (t(), e("div", Kl, [(t(!0), e(u, null, d(et.value, ((l, s) => (t(), e("div", {
                key: `skeleton-column-${s}`,
                class: "template-column"
            }, [(t(!0), e(u, null, d(l, ((l, s) => (t(), e("div", {
                key: `skeleton-${l.id||s}`,
                class: "skeleton-wrapper"
            }, [a("div", {
                class: "template-card skeleton-card",
                style: T({
                    height: l.finalHeight + "px"
                })
            }, o[9] || (o[9] = [a("div", {
                class: "skeleton-image"
            }, null, -1)]), 4)])))), 128))])))), 128))])) : B.value ? (t(), e("div", Yl, [a("p", null, r(c(g)("components.slides.error_loading_templates")), 1), a("button", {
                onClick: gt,
                class: "retry-button"
            }, r(c(g)("components.slides.retry")), 1)])) : 0 !== O.value.length || q.value || B.value ? (t(), e("div", lo, [(t(!0), e(u, null, d(Ie.value, ((l, s) => (t(), e("div", {
                key: s,
                class: "template-column"
            }, [(t(!0), e(u, null, d(l, ((l, s) => (t(), e("div", {
                key: l.id || s
            }, [l.isBlank ? (t(), e("div", {
                key: 0,
                class: p(["template-card blank-template-card", {
                    "is-creating": D.value
                }]),
                onClick: o[5] || (o[5] = e => dt()),
                style: T({
                    height: l.finalHeight + "px"
                })
            }, [a("div", oo, [a("div", so, [w(c(Ft))]), a("h3", no, r(c(g)("components.slides.create_blank_slides")), 1)])], 6)) : v("", !0), l.isBlank ? v("", !0) : (t(), h(Ya, {
                key: 1,
                template: l,
                finalHeight: l.finalHeight,
                isCreating: D.value,
                carouselState: oe.value,
                onTemplateClick: ot,
                onTemplateUse: it,
                onUpdateCarouselState: bt
            }, null, 8, ["template", "finalHeight", "isCreating", "carouselState"]))])))), 128))])))), 128)), me.value && !ye.value ? (t(), e("div", io)) : v("", !0)])) : (t(), e("div", eo, [a("div", to, [w(c(ct))]), a("p", ao, r(c(g)("components.slides.no_templates_found")), 1)])), ye.value ? (t(), e("div", ro, [w(F), a("p", co, r(c(g)("common.loading")), 1)])) : v("", !0)])) : "my" === U.value ? (t(), e("div", uo, [Q.value ? (t(), e("div", po, [(t(!0), e(u, null, d(et.value, ((l, s) => (t(), e("div", {
                key: `skeleton-column-${s}`,
                class: "template-column"
            }, [(t(!0), e(u, null, d(l, ((l, s) => (t(), e("div", {
                key: `skeleton-${l.id||s}`,
                class: "skeleton-wrapper"
            }, [a("div", {
                class: "template-card skeleton-card",
                style: T({
                    height: l.finalHeight + "px"
                })
            }, o[10] || (o[10] = [a("div", {
                class: "skeleton-image"
            }, null, -1)]), 4)])))), 128))])))), 128))])) : K.value ? (t(), e("div", vo, [a("p", null, r(c(g)("components.slides.error_loading_templates")), 1), a("button", {
                onClick: vt,
                class: "retry-button"
            }, r(c(g)("components.slides.retry")), 1)])) : (t(), e("div", mo, [(t(!0), e(u, null, d(tt.value, ((l, s) => (t(), e("div", {
                key: s,
                class: "template-column"
            }, [(t(!0), e(u, null, d(l, ((l, s) => (t(), e("div", {
                key: l.id || s
            }, [l.isAddTemplate ? (t(), e("div", {
                key: 0,
                class: "template-card add-template-card",
                onClick: o[6] || (o[6] = e => J.value = !0),
                style: T({
                    height: l.finalHeight + "px"
                })
            }, [a("div", ho, [a("div", go, [w(c(Ft))]), a("h3", _o, r(c(g)("components.slides.add_my_template")), 1)])], 4)) : (t(), h(Ya, {
                key: 1,
                template: l,
                finalHeight: l.finalHeight,
                isCreating: D.value,
                carouselState: oe.value,
                showDeleteButton: "my" === U.value,
                onTemplateClick: ot,
                onTemplateUse: it,
                onUpdateCarouselState: bt,
                onDeleteTemplate: kt
            }, null, 8, ["template", "finalHeight", "isCreating", "carouselState", "showDeleteButton"]))])))), 128))])))), 128)), te.value && !ae.value ? (t(), e("div", fo)) : v("", !0)])), ae.value ? (t(), e("div", yo, [w(F), a("p", ko, r(c(g)("common.loading")), 1)])) : v("", !0)])) : v("", !0)])], 2), D.value ? (t(), e("div", {
                key: 1,
                ref_key: "loadingOverlayRef",
                ref: N,
                class: "main-content-loading-overlay",
                style: T(Ue.value)
            }, [a("div", wo, [w(F), a("p", xo, r(c(g)("components.slides.loading")), 1)])], 4)) : v("", !0), H.value ? (t(), h(Ga, {
                key: 2,
                template: z.value,
                onClose: nt,
                onUse: yt
            }, null, 8, ["template"])) : v("", !0), w(rt, {
                modelValue: W.value,
                "onUpdate:modelValue": o[7] || (o[7] = e => W.value = e),
                mode: "confirm",
                title: qe.value,
                content: Fe.value,
                "cancel-button-text": We.value,
                "confirm-button-text": Ge.value,
                "is-dangerous": !0,
                "confirm-loading": !!Z.value,
                onConfirm: wt,
                onCancel: xt
            }, null, 8, ["modelValue", "title", "content", "cancel-button-text", "confirm-button-text", "confirm-loading"]), J.value ? (t(), h(pl, {
                key: 3,
                onClose: o[8] || (o[8] = e => J.value = !1)
            })) : v("", !0)]))
        }
    }, [
        ["__scopeId", "data-v-12d01e6e"]
    ]),
    jo = {
        key: 0,
        class: "text-[14px] text-[#232425] dark:text-[#fff]"
    },
    Co = l({
        __name: "PermissionTrigger",
        props: {
            project: {
                type: Object,
                required: !1
            },
            isMenuItem: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["open"],
        setup(l) {
            const o = y("currentUser");
            return (s, n) => c(o) && l.project ? (t(), e("div", {
                key: 0,
                onClick: n[0] || (n[0] = i((e => s.$emit("open", e)), ["stop"])),
                class: "flex items-center gap-[6px]"
            }, [a("div", {
                class: p(["icon earth-icon", l.isMenuItem ? "w-[16px] h-[16px]" : "w-[28px] h-[28px] bg-[#f5f5f5] p-[4px] dark:bg-[#3a3a3a]"])
            }, [w(c(ae))], 2), l.isMenuItem ? (t(), e("span", jo, r(s.$t("pages.spark.share")), 1)) : v("", !0)])) : v("", !0)
        }
    }, [
        ["__scopeId", "data-v-f5ba4b2a"]
    ]),
    So = {
        class: "docs-template-selector"
    },
    To = {
        class: "header-controls"
    },
    Eo = {
        class: "header-left"
    },
    Lo = {
        class: "icon"
    },
    Po = {
        class: "icon"
    },
    $o = {
        key: 0,
        class: "loading-section"
    },
    Io = {
        key: 1,
        class: "error-section"
    },
    Do = {
        key: 2
    },
    Ao = {
        class: "prompt-input-section"
    },
    Mo = {
        class: "prompt-input-wrapper relative z-[1]"
    },
    Ro = {
        class: "prompt-input-wrapper-upper"
    },
    No = {
        key: 0,
        class: "relative flex flex-col gap-[5px] w-[calc(100%-48px)] max-w-[702px] mx-auto mt-[-2px] p-[12px] rounded-b-3xl border-b border-zinc-100 bg-[#FAFAFA] dark:bg-[#333] dark:border-zinc-700"
    },
    Uo = {
        class: "ml-[10px]"
    },
    Oo = {
        class: "flex items-center gap-[12px] w-full justify-center mb-[24px]"
    },
    qo = {
        class: "px-[35px] py-[6px] bg-neutral-100 rounded-2xl flex justify-center items-center gap-2.5 overflow-hidden text-neutral-800 text-xs font-bold font-['Arial'] whitespace-nowrap"
    },
    Bo = {
        class: "loading-content"
    },
    Ho = {
        class: "loading-text"
    },
    zo = l({
        __name: "PodsRecommendList",
        props: {
            project: {
                type: Object,
                default: null
            }
        },
        emits: ["projectCreated", "back", "editProjectName", "submitPrompt"],
        setup(l, {
            emit: n
        }) {
            const {
                t: m,
                locale: g
            } = o();
            f();
            const _ = D(),
                x = n,
                P = y("currentUser"),
                $ = We();
            b((() => {
                $.setPlaceholder("Describe the pods you want to create...")
            }));
            const I = s(!1),
                M = s(null),
                R = s(null),
                N = s(!1),
                U = s(!1),
                O = s(null),
                q = s(240),
                B = s(12),
                H = s(null),
                z = s(null),
                W = s(!0);
            s(!1);
            const G = s(!1),
                {
                    columnWidth: Z,
                    columnCount: J,
                    containerRef: X,
                    calculateLayout: Q,
                    initializeLayout: K
                } = ht(),
                Y = j((() => Math.max(2 * J.value, 6)));
            k((() => z.value), (e => {
                X.value = e, e && G.value && K()
            })), k((() => te.value), (e => {
                e && G.value && z.value && Q(z.value.clientWidth)
            }));
            const ee = s([{
                    title: m("components.podcasts_agent.welcome_tip1")
                }, {
                    title: m("components.podcasts_agent.welcome_tip2")
                }, {
                    title: m("components.podcasts_agent.welcome_tip3")
                }]),
                te = s(!1),
                ae = Xe((() => {
                    O.value && (te.value = O.value.clientWidth)
                }), 200),
                oe = s([]),
                se = s(0),
                ne = Xe((() => {
                    O.value && se.value++
                }), 16),
                ie = s(!0),
                re = j((() => {
                    if (se.value, !O.value) return {
                        position: "fixed",
                        top: "64px",
                        left: "0",
                        right: "0",
                        bottom: "0",
                        zIndex: 9999
                    };
                    const e = O.value.getBoundingClientRect();
                    return {
                        position: "fixed",
                        top: `${e.top}px`,
                        left: `${e.left}px`,
                        width: `${e.width}px`,
                        height: `${e.height}px`,
                        zIndex: 9999
                    }
                }));
            b((() => {
                he(), _.query.publishId && ue(_.query.publishId), O.value && (te.value = O.value.clientWidth, Q(te.value)), window.addEventListener("resize", ae), setTimeout((() => {
                    ae()
                }), 0)
            }));
            const ce = (e, t = "recommend") => {
                    if ("publish" === t) {
                        const t = e.session_state.podcasts_agent.content.result,
                            a = le(t);
                        return {
                            id: e.id || e.content_id,
                            name: t.podcast_name,
                            poster_url: t.poster_url,
                            link: `/pods/${e.id||e.content_id}`,
                            audio_url: a,
                            nickname: t.nickname,
                            avatar: t.avatar,
                            duration: t.duration,
                            like_count: t.like_count,
                            is_liked: t.is_liked
                        }
                    }
                    return { ...e,
                        poster_url: e.image,
                        name: e.title,
                        id: e.link.match(/id=([^&]+)/)[1],
                        audio_url: e.meta_data.audio_url,
                        nickname: e.meta_data.nickname,
                        avatar: e.meta_data.avatar,
                        duration: e.meta_data.duration,
                        like_count: e.meta_data.like_count,
                        is_liked: e.meta_data.is_liked
                    }
                },
                ue = async e => {
                    try {
                        const t = await fetch(`/api/publish/resource?id=${e}`, {
                                method: "GET",
                                headers: {
                                    "Content-Type": "application/json"
                                }
                            }),
                            a = await t.json();
                        if (0 === a.status && a.data) {
                            const e = a.data,
                                t = ce(e, "publish");
                            oe.value.unshift(t)
                        }
                    } catch (t) {}
                };
            k((() => [ie.value, te.value, I.value]), (() => {
                I.value && L((() => {
                    ne()
                }))
            }), {
                immediate: !0
            });
            const de = () => {
                    x("back")
                },
                pe = () => {
                    ie.value = !ie.value
                },
                ve = e => {
                    x("editProjectName", e)
                },
                me = async (e, t, a) => {
                    x("submitPrompt", e, t, a)
                },
                he = async () => {
                    G.value = !0;
                    try {
                        const e = await fetch("/api/recommend_type_resources", {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json"
                                },
                                body: JSON.stringify({
                                    recommend_types: {
                                        podcasts_agent: {
                                            language: g.value.split("-")[0],
                                            num_recommendations: 50
                                        }
                                    }
                                })
                            }),
                            t = await e.json();
                        if (0 !== t.status) return [];
                        oe.value.push(...t.data.flow_item_list.map((e => ce(e, "recommend"))))
                    } catch (e) {
                        return []
                    } finally {
                        G.value = !1
                    }
                },
                ge = () => {
                    W.value = !1
                };
            return C((() => {
                window.removeEventListener("resize", ae)
            })), (l, o) => (t(), e("div", So, [a("div", To, [a("div", Eo, [a("div", {
                class: "back-icon cursor-pointer",
                onClick: de
            }, [a("div", Lo, [w(c(Ee))])]), a("div", {
                class: "button left-toggle",
                onClick: i(pe, ["stop"])
            }, [a("div", Po, [w(c(we))])])])]), c(P) ? (t(), h(V, {
                key: 0,
                ref_key: "agentTaskListRef",
                ref: M,
                hideLeftSide: ie.value,
                onSetShowLeftSide: o[0] || (o[0] = e => {
                    ie.value = e
                }),
                onEditProjectName: ve
            }, null, 8, ["hideLeftSide"])) : v("", !0), a("div", {
                ref_key: "mainContentRef",
                ref: O,
                class: p(["main-content", {
                    "full-width": ie.value || (!1 === te.value ? E.isMobile() : te.value <= 1220)
                }]),
                style: T({
                    "--column_width": q.value + "px",
                    "--flow_item_padding": B.value + "px"
                })
            }, [o[3] || (o[3] = a("div", {
                class: "header"
            }, [a("h2", null, "Ready to create your pods?")], -1)), N.value ? (t(), e("div", $o, [w(F), a("p", null, r(l.$t("common.loading")) + "...", 1)])) : U.value ? (t(), e("div", Io, [a("p", null, r(l.$t("common.error_loading_templates")), 1), a("button", {
                onClick: o[1] || (o[1] = (...e) => l.loadTemplates && l.loadTemplates(...e)),
                class: "retry-button"
            }, r(l.$t("common.retry")), 1)])) : (t(), e("div", Do, [a("div", Ao, [a("div", Mo, [a("div", Ro, [w(ze, {
                ref: "inputWrapper"
            }, {
                default: S((() => [w(He, {
                    ref_key: "promptInputRef",
                    ref: R,
                    onSubmitPrompt: me,
                    supportImages: !0,
                    useSuggestion: !1,
                    styleClass: "podcasts-template",
                    showPersonalizationButton: !1,
                    placeholder: l.$t("components.podcasts_agent.enter_podcast_topic")
                }, null, 8, ["placeholder"])])),
                _: 1
            }, 512)])]), W.value ? (t(), e("div", No, [a("div", {
                class: "absolute top-[12px] right-[12px] cursor-pointer",
                onClick: ge
            }, [w(c(ft), {
                class: "w-4 h-4 text-[#909499]"
            })]), (t(!0), e(u, null, d(ee.value, (l => (t(), e("div", {
                className: "flex items-center text-neutral-800 dark:text-white text-[14px] font-normal font-['Arial'] leading-tight pr-[20px]",
                key: l.title
            }, [w(c(_t), {
                class: "w-4 h-4 text-[#5CD4A1]"
            }), a("div", Uo, r(l.title), 1)])))), 128))])) : v("", !0)]), a("div", Oo, [w(c(mt), {
                class: "w-[100px] md:w-[210px] h-1 dark:rotate-180"
            }), a("div", qo, r(l.$t("pages.agents.for_you")), 1), w(c(mt), {
                class: "w-[100px] md:w-[210px] h-1 rotate-180 dark:rotate-0"
            })]), G.value ? (t(), e("div", {
                key: 0,
                ref_key: "skeletonWrapper",
                ref: z,
                class: "flex flex-wrap gap-[12px] md:px-[25px] pb-[100px] box-border justify-center"
            }, [(t(!0), e(u, null, d(Y.value, (a => (t(), e("div", {
                key: `skeleton-${a}`,
                class: "rounded-[12px] bg-[#fafafa] dark:bg-[#333] px-[12px] py-[16px] box-border pointer-events-none",
                style: T({
                    width: c(Z) + "px"
                })
            }, o[2] || (o[2] = [A('<div class="relative rounded-[12px] w-full aspect-square bg-gray-200 animate-skeleton dark:bg-gradient-to-r dark:from-[#333] dark:to-[#2a2a2a] dark:animate-skeleton-dark flex justify-center items-center" data-v-74f74fef><div class="w-[42px] h-[42px] bg-gray-300 animate-skeleton dark:bg-gradient-to-r dark:from-[#333] dark:to-[#2a2a2a] rounded-full dark:animate-skeleton-dark" data-v-74f74fef></div></div><div class="h-[40px] mt-[12px] space-y-1" data-v-74f74fef><div class="h-[20px] bg-gray-200 animate-skeleton dark:bg-gradient-to-r dark:from-[#333] dark:to-[#2a2a2a] rounded dark:animate-skeleton-dark" data-v-74f74fef></div><div class="h-[20px] w-3/4 bg-gray-200 animate-skeleton dark:bg-gradient-to-r dark:from-[#333] dark:to-[#2a2a2a] rounded dark:animate-skeleton-dark" data-v-74f74fef></div></div><div class="flex items-center justify-between gap-[10px] mt-[8px]" data-v-74f74fef><div class="flex items-center gap-[6px]" data-v-74f74fef><div class="w-5 h-5 rounded-full bg-gray-200 animate-skeleton dark:bg-gradient-to-r dark:from-[#333] dark:to-[#2a2a2a] dark:animate-skeleton-dark" data-v-74f74fef></div><div class="h-[14px] w-16 bg-gray-200 animate-skeleton dark:bg-gradient-to-r dark:from-[#333] dark:to-[#2a2a2a] rounded dark:animate-skeleton-dark" data-v-74f74fef></div></div><div class="flex items-center gap-[4px]" data-v-74f74fef><div class="w-[14px] h-[14px] bg-gray-200 animate-skeleton dark:bg-gradient-to-r dark:from-[#333] dark:to-[#2a2a2a] rounded dark:animate-skeleton-dark" data-v-74f74fef></div><div class="h-[12px] w-6 bg-gray-200 animate-skeleton dark:bg-gradient-to-r dark:from-[#333] dark:to-[#2a2a2a] rounded dark:animate-skeleton-dark" data-v-74f74fef></div></div></div>', 3)]), 4)))), 128))], 512)) : oe.value && oe.value.length ? (t(), h(gt, {
                key: 1,
                dataList: oe.value
            }, null, 8, ["dataList"])) : v("", !0)]))], 6), I.value ? (t(), e("div", {
                key: 1,
                ref_key: "loadingOverlayRef",
                ref: H,
                class: "main-content-loading-overlay",
                style: T(re.value)
            }, [a("div", Bo, [w(F), a("p", Ho, r(l.$t("common.loading")) + "...", 1)])], 4)) : v("", !0)]))
        }
    }, [
        ["__scopeId", "data-v-74f74fef"]
    ]),
    Fo = {
        style: {
            height: "100%"
        }
    },
    Wo = {
        class: "global-loading-modal"
    },
    Go = {
        class: "create_result"
    },
    Zo = {
        class: "content"
    },
    Vo = {
        class: "bold"
    },
    Jo = {
        class: "buttons"
    },
    Xo = {
        class: "app-container"
    },
    Qo = {
        class: "main-wrapper"
    },
    Ko = {
        key: 0,
        class: "coffee_message"
    },
    Yo = {
        class: "text"
    },
    es = {
        class: "icon"
    },
    ts = {
        class: "running_tips tips1"
    },
    as = {
        class: "text"
    },
    ls = {
        key: 0,
        class: "running_tips tips2"
    },
    os = {
        class: "text"
    },
    ss = {
        class: "text"
    },
    ns = {
        class: "icon"
    },
    is = {
        class: "header-left"
    },
    rs = {
        class: "icon"
    },
    cs = {
        class: "title"
    },
    us = {
        key: 2,
        class: "project-name"
    },
    ds = {
        class: "text"
    },
    ps = {
        class: "header-right"
    },
    vs = {
        class: "top"
    },
    ms = {
        key: 1
    },
    hs = {
        class: "icon more-menu"
    },
    gs = {
        class: "flex flex-col gap-[16px]"
    },
    _s = {
        class: "text-[14px] text-[#232425] dark:text-[#fff]"
    },
    fs = {
        class: "flex items-center gap-[6px]"
    },
    ys = {
        key: 1,
        class: "main-inner"
    },
    ks = {
        key: 1,
        style: {
            "margin-top": "50px"
        }
    },
    ws = {
        key: 2,
        class: "like-dislike-controls"
    },
    xs = {
        class: "icon"
    },
    bs = {
        class: "icon"
    },
    js = {
        key: 0,
        class: "flex flex-col px-[16px] pt-[14px] md:pt-[80px] box-border w-full agents-index",
        style: {
            "--container-width": "680px"
        }
    },
    Cs = {
        class: "flex flex-wrap gap-[12px] mt-[16px]"
    },
    Ss = ["onClick"],
    Ts = {
        key: 0,
        class: "new-tag absolute left-[8px] top-[8px]"
    },
    Es = {
        class: "flex gap-[12px] absolute right-[12px] top-[12px]"
    },
    Ls = ["onClick"],
    Ps = {
        class: "flex items-center gap-[4px] leading-[21px]"
    },
    $s = {
        class: "flex-shrink-0 w-[16px] h-[16px] text-[#909499] dark:text-[#fff]"
    },
    Is = {
        class: "px-[12px]"
    },
    Ds = {
        class: "w-full flex flex-col justify-center items-center head"
    },
    As = {
        class: "flex flex-col items-center gap-[12px]"
    },
    Ms = {
        class: "flex gap-[6px] items-center text-center text-[16px] font-bold text-[#232425] leading-[1.5] dark:text-[#fff]"
    },
    Rs = {
        key: 1,
        class: "main-inner agents-index"
    },
    Ns = {
        class: "advanced-agent"
    },
    Us = {
        class: "agents"
    },
    Os = ["onClick"],
    qs = {
        class: "agent-content"
    },
    Bs = {
        class: "head"
    },
    Hs = {
        class: "text"
    },
    zs = {
        class: "icon"
    },
    Fs = {
        class: "label flex gap-[6px] items-center"
    },
    Ws = {
        key: 0,
        class: "new-tag"
    },
    Gs = {
        class: "buttons"
    },
    Zs = ["onClick"],
    Vs = {
        class: "text"
    },
    Js = {
        class: "setting_icon"
    },
    Xs = ["onClick"],
    Qs = {
        class: "text"
    },
    Ks = {
        class: "icon add_icon"
    },
    Ys = {
        class: "popular-tasks"
    },
    en = {
        class: "title"
    },
    tn = ["onClick"],
    an = {
        class: "text"
    },
    ln = {
        class: "icon"
    },
    on = {
        key: 0,
        class: "agent-bottom"
    },
    sn = {
        class: "basic-agent"
    },
    nn = {
        class: "text"
    },
    rn = {
        class: "icon"
    },
    cn = {
        class: "agents"
    },
    un = ["onClick"],
    dn = {
        class: "agent-content"
    },
    pn = {
        class: "button"
    },
    vn = {
        class: "icon"
    },
    mn = {
        class: "text"
    },
    hn = {
        class: "description"
    },
    gn = {
        class: "icons"
    },
    _n = {
        class: "icon"
    },
    fn = ["src"],
    yn = l({
        __name: "agents",
        setup(l) {
            y("IS_CN");
            const n = s(null),
                _ = s("phone_call"),
                x = D(),
                P = f(),
                $ = s(null),
                A = s(null),
                J = y("currentUser"),
                X = y("updateUserInfo"),
                Q = s(!1),
                K = s(!1),
                Y = s([]),
                {
                    t: ee,
                    locale: te
                } = o(),
                ae = Be(),
                le = y("jsBridge"),
                ce = s(""),
                xe = s(""),
                je = s(""),
                Ie = s(""),
                Ae = s(!1),
                Ne = s(null),
                Ue = s(null),
                Oe = s(!1),
                qe = s(!1),
                He = s(!1),
                ze = s(null),
                Fe = s(!1),
                We = s(null),
                Ge = s(),
                {
                    agentsPageList: Ze
                } = pt(),
                Ve = y("isDarkMode");
            k((() => le.value), (e => {
                e && le.value.callHandler("support", {
                    api: "openTaskView"
                }, (e => {
                    He.value = e
                }))
            }), {
                immediate: !0
            });
            const Je = () => {
                    Ct.value && He.value ? le.value.callHandler("openTaskView", {}) : Ct.value = !Ct.value
                },
                Qe = Me(),
                Ke = () => {
                    ma() ? ta({
                        type: "phone_call"
                    }) : n.value.show()
                },
                Ye = j((() => x.query.place_id || "personal" === x.query.contact_type ? {
                    type: x.query.contact_type || "business",
                    place_id: x.query.place_id,
                    title: x.query.place_title || x.query.contact_name || "",
                    address: x.query.place_address || "",
                    phone: x.query.place_phone || x.query.contact_phone || "",
                    country: x.query.country || "",
                    countryCode: x.query.country_code || ""
                } : null)),
                tt = j((() => x.query.purpose || "")),
                at = s(null);
            k((() => J.value), (() => {
                J.value && (Q.value = !0, ce.value = J.value.phone_call_user_phone_number, xe.value = J.value.phone_call_user_country_code, je.value = J.value.phone_call_user_name, Ie.value = J.value.phone_call_voice, Ae.value = J.value.phone_call_is_not_first_time)
            }), {
                immediate: !0
            });
            const lt = e => {
                    const t = {
                        type: e.type || "moa_chat",
                        name: e.name || "AI Chat",
                        session_state: {
                            steps: [],
                            messages: []
                        }
                    };
                    return t.client_generated_id = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function(e) {
                        const t = 16 * Math.random() | 0;
                        return ("x" == e ? t : 3 & t | 8).toString(16)
                    })), t
                },
                ot = e => {
                    const t = e.alias_id || e.id;
                    E.isGensparkAppMainTabView() ? E.windowopen(`/agents?type=${t}`) : (history.pushState({}, "", `?type=${t}`), ut.value = `?type=${t}`, e.component || (gt.value = lt({
                        type: e.id,
                        name: e.name
                    })))
                },
                st = e => {
                    if (E.isGensparkAppMainTabView()) {
                        const t = Object.entries(e.query).map((([e, t]) => `${e}=${encodeURIComponent(t)}`)).join("&");
                        E.windowopen(`${e.path}?${t}`)
                    } else {
                        const t = Object.entries(e.query).map((([e, t]) => `${e}=${encodeURIComponent(t)}`)).join("&");
                        history.pushState({}, "", `?${t}`), ut.value = `?${t}`;
                        const a = e.query.type;
                        if (a) {
                            const e = ct(a);
                            e && !e.component && (gt.value = lt({
                                type: a,
                                name: e.name || a
                            }))
                        }
                    }
                },
                nt = e => {
                    if (Tt.value)
                        if (Q.value) Ke();
                        else {
                            Oe.value = !0, K.value && (X(), K.value = !1);
                            const e = k((() => J.value), (e => {
                                if (e) {
                                    if (!Y.value.length) return;
                                    for (Oe.value = !1; Y.value.length;) {
                                        let e;
                                        try {
                                            e = Y.value.pop()
                                        } catch (t) {
                                            e = null
                                        }
                                        e && e()
                                    }
                                    Q.value = !0, ce.value = e.phone_call_user_phone_number, xe.value = e.phone_call_user_country_code, je.value = e.phone_call_user_name, Ie.value = e.phone_call_voice, Ae.value = e.phone_call_is_not_first_time, Ke()
                                }
                            }), {
                                immediate: !0
                            });
                            Y.value.push(e)
                        }
                    else Kt()
                },
                it = e => {
                    n.value.show()
                },
                rt = s(null),
                ct = e => {
                    var t, a;
                    if (!e) return null;
                    return null == (a = null == (t = rt.value) ? void 0 : t.agentsPageConfig) ? void 0 : a.agents.find((t => t.id == e || t.alias_id == e))
                },
                ut = s("");
            ut.value = window.location.search;
            const mt = () => {
                    const e = new URLSearchParams(ut.value);
                    let t = e.get("type");
                    if ("call_for_me" === t) {
                        if ("1" !== e.get("agenticMode")) {
                            const e = new URL(window.location);
                            return e.searchParams.set("type", "phone_call"), void(window.location.href = e.toString())
                        }
                    }
                    let a = ct(t);
                    !a || a.component || a.is_advanced && !a.is_deep_research && !a.is_agent || (gt.value = lt({
                        type: a.id,
                        name: a.name
                    })), a && a.is_advanced && !a.is_deep_research && !a.is_agent && a.onClick(a)
                },
                ht = () => {
                    if (gt.value && gt.value.type) {
                        const e = ct(gt.value.type);
                        return e ? e.agent_type : gt.value.type
                    } {
                        const e = new URLSearchParams(ut.value).get("type");
                        let t = ct(e);
                        if (t) return t.id
                    }
                    return ""
                };
            b((() => {
                mt()
            })), k((() => ut.value), (() => {
                {
                    const e = new URLSearchParams(window.location.search);
                    if ("call_for_me" === e.get("type")) {
                        if ("1" !== e.get("agenticMode")) {
                            const e = new URL(window.location);
                            return e.searchParams.set("type", "phone_call"), void(window.location.href = e.toString())
                        }
                    }
                }
            }), {
                immediate: !0
            });
            const gt = s(null),
                _t = s(!1),
                ft = s("article_verification"),
                wt = s(!1);
            k((() => wt.value), (e => {
                e || (at.value = null)
            }));
            const xt = s(null),
                bt = s(null),
                jt = s(!1),
                Ct = (s(!1), s(!0)),
                St = s(null),
                Tt = s(!1),
                Et = s(!1),
                Lt = s(!1),
                Pt = s(null),
                $t = s(!1),
                It = s(!1),
                Dt = s(null);
            let At = null,
                Mt = null;
            const Rt = s(0),
                Nt = s(!1),
                Ot = Xe((() => {
                    E.log("onWindowResize", window.innerWidth), Nt.value = window.innerWidth
                }), 200);
            b((() => {
                window.addEventListener("resize", Ot), Ot()
            })), C((() => {
                window.removeEventListener("resize", Ot)
            }));
            const Bt = () => !1 === Nt.value ? E.isMobile() : Nt.value <= 1220;
            x.query.show_generate_sparkpage_gan && ($t.value = !0), k((() => gt.value && gt.value.id), ((e, t) => {
                if (e) {
                    const t = new URLSearchParams(window.location.search).get("debug"),
                        a = `?id=${e}${t?"&debug="+t:""}`;
                    window.history.replaceState({}, "", a), ut.value = a
                }
            })), k((() => gt.value && gt.value.name), (e => {
                gt.value && gt.value.name ? H({
                    title: `Genspark - ${gt.value.name}`
                }) : H({
                    title: "Genspark Agents"
                })
            })), k((() => gt.value), (e => {
                e || window.history.pushState({}, "", "?"), E.log("clear project in nuxt data"), E.log(e), z("project")
            }));
            k((() => gt.value && gt.value.session_state && gt.value.session_state.name ? gt.value.session_state.name : gt.value && gt.value.name ? gt.value.name : ""), (e => {
                e && (gt.value.name = e, Ne.value && Ne.value.addOrUpdateProject(gt.value))
            }));
            const zt = s(null);
            k((() => Pt.value), (() => {
                Pt.value && Pt.value.addEventListener("scroll", (() => {
                    Pt.value.scrollTop > 140 ? Lt.value = !0 : Lt.value = !1
                }))
            }));
            const Ft = s(!0);
            let Gt = 0;
            const Zt = () => {
                const e = document.documentElement.scrollTop;
                e > Gt && e > 140 ? Ft.value = !1 : (e < Gt || e <= 140) && (Ft.value = !0), Gt = e, Lt.value = e > 140
            };
            b((() => {
                document.addEventListener("scroll", Zt)
            })), I((() => {
                document.removeEventListener("scroll", Zt)
            }));
            const Vt = s(!1);
            k((() => Vt.value && null !== J.value), (e => {
                e && $.value.trigger()
            })), (() => {
                var e, t;
                const {
                    data: a
                } = R("is_login");
                if (a.value && 0 === a.value.status && (Tt.value = !0), R("project") && R("project").data) {
                    const {
                        data: l
                    } = R("project");
                    if (l.value && 0 === l.value.status) {
                        if (gt.value = l.value.data, E.log("project", gt.value), ia(), x.query.prompt && x.query.autoSubmit && (E.log("ZZH Auto-submitting prompt from URL parameter:", x.query.prompt), L((() => {
                                setTimeout((() => {
                                    E.log("ZZH Triggering auto-submit via URL parameter");
                                    const e = document.querySelector(".input.px-\\[12px\\].pb-\\[12px\\]");
                                    if (!e) {
                                        E.log("ZZH Prompt input component not found, trying alternative selector");
                                        const e = document.querySelector("textarea.search-input.j-search-input");
                                        return void(e ? (E.log("ZZH Found textarea directly"), a(e)) : E.log("ZZH No textarea found"))
                                    }
                                    const t = e.querySelector("textarea.search-input.j-search-input");

                                    function a(e) {
                                        E.log("ZZH Setting textarea value and triggering submit"), e.value = decodeURIComponent(x.query.prompt), e.focus(), e.dispatchEvent(new Event("input", {
                                            bubbles: !0
                                        }));
                                        const t = new KeyboardEvent("keydown", {
                                            key: "Enter",
                                            code: "Enter",
                                            keyCode: 13,
                                            which: 13,
                                            bubbles: !0,
                                            cancelable: !0
                                        });
                                        E.log("ZZH Triggering Enter key event"), e.dispatchEvent(t)
                                    }
                                    t ? a(t) : E.log("ZZH Textarea not found within prompt input component")
                                }), 1500)
                            }))), x.query.test) {
                            async function o() {
                                try {
                                    const e = (await U((() =>
                                            import ("./CnGkyhum.js")), [],
                                        import.meta.url)).default;
                                    gt.value.session_state.steps.unshift(e);
                                    const t = (await U((() =>
                                            import ("./C5JlNeVW.js")), [],
                                        import.meta.url)).default;
                                    gt.value.session_state.steps.unshift(t);
                                    const a = (await U((() =>
                                            import ("./DfSgAeDq.js")), [],
                                        import.meta.url)).default;
                                    gt.value.session_state.steps.unshift(a)
                                } catch (e) {}
                            }
                            o()
                        }
                        x.query.id || (window.history.replaceState({}, "", `?id=${gt.value.id}`), ut.value = `?id=${gt.value.id}`)
                    } else -2 === (null == (e = null == l ? void 0 : l.value) ? void 0 : e.status) ? location.href = "/agents" : -13 === (null == (t = null == l ? void 0 : l.value) ? void 0 : t.status) && (location.href = "/no_permission")
                }
                if (x.query.test_moa_deep_research) {
                    async function s() {
                        const e = (await U((() =>
                                import ("./kOuJAkkE.js")), [],
                            import.meta.url)).default;
                        gt.value = e.data
                    }
                    s()
                }
            })();
            const Jt = async e => {
                    var t;
                    if (!(gt.value && (null == (t = ct(gt.value.type)) ? void 0 : t.no_run_project) || (e || (e = {
                            force: !1
                        }), jt.value))) try {
                        if (At && (At.abort(), await new Promise((e => setTimeout(e, 0)))), At = new AbortController, jt.value = !0, !gt.value || !gt.value.id) return;
                        if (Ne.value && Ne.value.addOrUpdateProject(gt.value), Rt.value++, Rt.value > 4) return;
                        await Le("/api/project/run", {
                            project_id: gt.value.id,
                            ...e
                        }, (e => {
                            e.project_id == gt.value.id && (gt.value && !gt.value.session_state && (gt.value.session_state = {}), "SESSION_STATE_FIELD" === e.type && ("" == e.field_name ? N(gt.value.session_state, (() => e.field_value)) : yt(gt.value.session_state, e.field_name, (() => e.field_value))), "SESSION_STATE_FIELD_DELTA" === e.type && yt(gt.value.session_state, e.field_name, (t => (t || "") + e.delta)), "SESSION_STATE_FIELD_APPEND_ITEM" === e.type && yt(gt.value.session_state, e.field_name, (t => [...t || [], e.field_value])))
                        }), At.signal), Xt(gt.value.id, {
                            silent: !0
                        })
                    } finally {
                        jt.value = !1
                    }
                },
                Xt = async (e, t) => {
                    const a = !(!t || !t.no_run),
                        l = !(!t || !t.silent);
                    Mt && (Mt.abort(), Mt = null), jt.value && Qt(), Mt = new AbortController;
                    try {
                        l || (bt.value = e, window.history.pushState({}, "", `?id=${e}`));
                        const t = await fetch(`/api/project?id=${e}`, {
                                signal: Mt.signal
                            }),
                            a = await t.json();
                        if (0 !== a.status || !a.data) return;
                        gt.value && gt.value.id;
                        gt.value = a.data, ia()
                    } catch (o) {} finally {
                        bt.value == e && (bt.value = null, Mt = null), Qe.setEditSlideStatus("normal")
                    }
                    a || !gt.value || "STARTED" != gt.value.status && "PROGRESS" != gt.value.status && "PENDING" != gt.value.status || Jt(), Ne.value && Ne.value.addOrUpdateProject(gt.value)
                };
            !gt.value || "STARTED" != gt.value.status && "PROGRESS" != gt.value.status && "PENDING" != gt.value.status || Jt();
            const Qt = () => {
                    At && At.abort(), jt.value = !1
                },
                Kt = () => {
                    location.href = "/login?redirect_url=" + encodeURIComponent(location.href)
                },
                Yt = (() => {
                    let e = null;
                    return () => {
                        Et.value = !0, e && clearTimeout(e), e = setTimeout((() => {
                            Et.value = !1
                        }), 5e3)
                    }
                })(),
                ea = async e => {
                    if (!e || !e.id) return;
                    const t = {
                        id: e.id
                    };
                    e.name && (t.name = e.name), e.description && (t.description = e.description), !0 === e.is_private || !1 === e.is_private ? t.is_private = e.is_private : t.request_not_update_permission = !0;
                    const a = await fetch("/api/project/update", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify(t)
                        }),
                        l = await a.json();
                    0 === l.status || E.log(l)
                },
                ta = e => {
                    ft.value = e.type, "phone_call" == e.type ? wt.value = !0 : _t.value = !0
                },
                aa = s(!1),
                la = s(!1),
                oa = async () => {
                    if (aa.value = !aa.value, la.value = !1, aa.value) {
                        const e = await fetch("/api/project/like", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify({
                                project_id: gt.value.id
                            })
                        });
                        (await e.json()).status
                    }
                },
                sa = async () => {
                    if (la.value = !la.value, aa.value = !1, la.value) {
                        const e = await fetch("/api/project/dislike", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify({
                                project_id: gt.value.id
                            })
                        });
                        (await e.json()).status
                    }
                },
                na = async () => {
                    var e;
                    if (!gt.value) return;
                    const t = !qe.value,
                        a = ae.loading(ee(t ? "pages.agents.adding_bookmark" : "pages.agents.removing_bookmark"));
                    try {
                        const e = await E.postRequest("/api/user/project_bookmark", {
                            project_id: gt.value.id,
                            name: gt.value.name,
                            is_deleted: !t,
                            source: "agents"
                        });
                        a.destroy(), 0 === e.status ? (qe.value = t, ae.success(ee(t ? "components.podcast.action_buttons.added_to_bookmarks" : "components.podcast.action_buttons.removed_from_bookmarks"))) : ae.error(ee("pages.agents.failed_to_add_bookmark"))
                    } catch (l) {
                        a.destroy(), ae.error(ee("pages.agents.failed_to_add_bookmark"))
                    }
                    null == (e = null == Ge ? void 0 : Ge.value) || e.setShow(!1)
                };
            async function ia() {
                var e, t, a;
                if ((null == (e = gt.value) ? void 0 : e.id) && Tt.value) try {
                    const e = await fetch(`/api/user/project_bookmark?project_id=${gt.value.id}`);
                    if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                    const l = await e.json();
                    0 === l.status ? qe.value = (null == (a = null == (t = l.data) ? void 0 : t.project_list) ? void 0 : a.length) > 0 : qe.value = !1
                } catch (l) {
                    qe.value = !1
                } else qe.value = !1
            }
            const ra = () => {
                    var e;
                    gt.value = lt({
                        type: gt.value.type,
                        name: null == (e = ct(gt.value.type)) ? void 0 : e.name
                    });
                    const t = ct(gt.value.type),
                        a = (null == t ? void 0 : t.alias_id) || gt.value.type;
                    window.history.replaceState({}, "", "?type=" + a), ut.value = `?type=${a}`, E.log("createNewSession", gt.value), Qe.setEditSlideStatus("normal"), qe.value = !1
                },
                ca = () => {
                    const e = ct(gt.value.type),
                        t = (null == e ? void 0 : e.alias_id) || gt.value.type;
                    window.open(`${window.location.pathname}?type=${t}`, "_blank")
                },
                ua = j((() => {
                    if ("1" === new URLSearchParams(window.location.search).get("debug")) return E.log("Debug mode enabled"), !0;
                    return E.log("Debug mode disabled"), !1
                }));
            b((() => {
                setTimeout((() => {
                    document.body.classList.add("agents-page")
                }), 0), E.isGensparkApp() && (window.appInterface || (window.appInterface = {}), window.appInterface.showPermissionPopover = () => {
                    var e;
                    null == (e = Ue.value) || e.show()
                }), window.addEventListener("openPhoneCallForm", wa)
            })), C((() => {
                document.body.classList.remove("agents-page"), window.removeEventListener("openPhoneCallForm", wa)
            }));
            const da = s(!1);
            k((() => da.value), (e => {
                jt.value = e
            }));
            const pa = (e, t) => {
                    const a = [ee("pages.autopilotagent.running_tips1"), ee("pages.autopilotagent.running_tips2")],
                        l = ct(e.type);
                    return l && l.runningTips && (a[0] = l.runningTips[0], a[1] = l.runningTips[1]), a[t]
                },
                va = () => {
                    gt.value = null, qe.value = !1, Mt && (Mt.abort(), Mt = null), bt.value = null, window.history.replaceState({}, "", "?"), ut.value = ""
                },
                ma = () => Ae.value && je.value && Ie.value && ce.value;
            k((() => gt.value ? gt.value.name + gt.value.id : ""), (e => {
                gt.value && gt.value.name && gt.value.id && (async () => {
                    var e;
                    if (!Tt.value) return;
                    let t;
                    const a = ct(null == (e = gt.value) ? void 0 : e.type);
                    if (!(gt.value && gt.value.name && gt.value.id && a && a.name != gt.value.name)) return void E.log("no project no log");
                    t = {
                        type: "agent",
                        agent_type: gt.value.type,
                        key: gt.value.id,
                        raw_url: location.href,
                        favicon_url: a.favicon_url,
                        title: gt.value.name
                    };
                    const l = await fetch("/api/history/log", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify(t)
                    });
                    (await l.json()).status
                })()
            }), {
                immediate: !0
            }), k((() => {
                var e;
                return null == (e = gt.value) ? void 0 : e.id
            }), ((e, t) => {
                e !== t && (e ? ia() : qe.value = !1)
            }));
            const ha = s(E.isGensparkApp()),
                ga = s(E.isGensparkAppChatView()),
                _a = s(null),
                fa = e => {
                    var t;
                    null == (t = _a.value) || t.startEdit(e)
                },
                ya = e => {
                    var t;
                    null == (t = Ne.value) || t.addOrUpdateProject(e), gt.value && e.id == gt.value.id && (gt.value.name = e.name), ea({
                        id: e.id,
                        name: e.name
                    })
                },
                ka = () => {
                    ze.value.openDownloader()
                },
                wa = e => {
                    if (e.detail && "phone_call" === e.detail.type) {
                        if (!Tt.value) return void Kt();
                        const t = e.detail.emailContext;
                        if (t) {
                            const e = /(\+?[\d\s\-\(\)]{10,})/g,
                                a = (t.text_body || t.html_body || t.snippet || "").match(e);
                            at.value = {
                                contact: {
                                    type: "personal",
                                    title: t.from ? t.from.split("<")[0].trim() : "",
                                    phone: a ? a[0].replace(/\D/g, "") : "",
                                    country: "",
                                    countryCode: ""
                                },
                                purpose: `Regarding email: "${t.subject||"No Subject"}"\n\nOriginal email from: ${t.from||"Unknown"}\nDate: ${t.date||"Unknown"}\n\nEmail content:\n${t.snippet||"No content available"}`
                            }
                        }
                        ma() ? ta({
                            type: "phone_call"
                        }) : n.value.show()
                    }
                },
                ba = e => {
                    gt.value = e, e.id && (Ne.value && Ne.value.addOrUpdateProject(e), window.history.replaceState({}, "", `?id=${e.id}`), ut.value = `?id=${e.id}`)
                };
            M((() => {
                var e;
                Fe.value = "image_agent" === (null == (e = gt.value) ? void 0 : e.type)
            }));
            return M((() => {
                var e;
                gt.value && void 0 !== gt.value.is_private && (e = !gt.value.is_private, E.isGensparkApp() && le.value && le.value.callHandler("permissionChanged", {
                    isPublic: e
                }))
            })), (l, o) => {
                var s, f, y, k;
                const b = F,
                    j = W,
                    C = G;
                return t(), e(u, null, [w(Se, {
                    ref_key: "agentsConfigsRef",
                    ref: rt,
                    onBasic_agent_click: ot,
                    onPhone_call_click: nt,
                    onPhone_call_setting_click: it,
                    onOpen_create_task_form: ta,
                    onAi_drive_click: ka
                }, null, 512), a("div", Fo, [w(re, {
                    onRefresh: o[44] || (o[44] = () => {
                        zt.value.reset(), setTimeout((() => {
                            gt.value = null, qe.value = !1, mt()
                        }), 0), ut.value = window.location.search
                    }),
                    hideBottomBar: !(!gt.value && !bt.value),
                    agentType: ht(),
                    forceCollapseSidebar: Fe.value,
                    projectType: null == (s = gt.value) ? void 0 : s.type
                }, {
                    content: S((() => [a("div", {
                        class: p({
                            "with-infinite-canvas": Fe.value
                        })
                    }, [w(j, null, {
                        default: S((() => {
                            var s, n, f, y, k, L, $, I, D, M, R, N, U, H, z, F, W, G, Z, X, Q, K, Y, ee, te, ae, oe, se, re, ce, ye, xe, je, Ce, Se, Le, Ie, Ae, Me, Re, Be, He, ze, Fe, Xe, Qe, Ke, Ye, tt, at, lt, st, ut;
                            return [w(c(et), {
                                show: Oe.value,
                                "onUpdate:show": o[0] || (o[0] = e => Oe.value = e),
                                "mask-closable": !1
                            }, {
                                default: S((() => [a("div", Wo, [w(b)])])),
                                _: 1
                            }, 8, ["show"]), w(c(et), {
                                show: It.value,
                                "onUpdate:show": o[3] || (o[3] = e => It.value = e)
                            }, {
                                default: S((() => [a("div", Go, [a("div", Zo, [o[53] || (o[53] = a("p", {
                                    style: {
                                        "font-size": "30px"
                                    }
                                }, "🎉 🎉 🎉", -1)), a("p", null, r(l.$t("pages.autopilotagent.task_submitted_desc")), 1), a("p", Vo, r(l.$t("pages.autopilotagent.task_submitted_desc_feel_free")), 1)]), a("div", Jo, [a("div", {
                                    class: "button default",
                                    onClick: o[1] || (o[1] = () => {
                                        It.value = !1
                                    })
                                }, r(l.$t("pages.autopilotagent.ok")), 1), a("div", {
                                    class: "button",
                                    onClick: o[2] || (o[2] = async () => {
                                        It.value = !1, c(E).isGensparkAppMainTabView() ? c(E).windowopen(`/agents?id=${Dt.value}`) : (await Xt(Dt.value), Jt())
                                    })
                                }, r(l.$t("pages.autopilotagent.check_task_status")), 1)])])])),
                                _: 1
                            }, 8, ["show"]), a("div", Xo, ["docs_agent" != (null == (s = gt.value) ? void 0 : s.type) || (null == (n = gt.value) ? void 0 : n.id) || (null == (y = null == (f = gt.value) ? void 0 : f.session_state) ? void 0 : y.is_blank) ? v("", !0) : (t(), h(xa, {
                                key: 0,
                                project: gt.value,
                                onProjectCreated: ba,
                                onBack: o[4] || (o[4] = () => {
                                    c(E).isGensparkApp() ? c(le).callHandler("goBack", {}) : c(E).isShowBackIcon(c(x).query.version) && c(E).isMobile() ? c(P).back() : va()
                                }),
                                onSubmitPrompt: o[5] || (o[5] = () => {
                                    var e;
                                    null == (e = We.value) || e.chat_now()
                                }),
                                onEditProjectName: fa
                            }, null, 8, ["project"])), v("", !0), "code_sandbox" != (null == (k = gt.value) ? void 0 : k.type) && "code_sandbox_light" != (null == (L = gt.value) ? void 0 : L.type) || (null == ($ = gt.value) ? void 0 : $.id) || (null == (D = null == (I = gt.value) ? void 0 : I.session_state) ? void 0 : D.is_blank) ? v("", !0) : (t(), h(Ut, {
                                key: 2,
                                project: gt.value,
                                onProjectCreated: ba,
                                onBack: o[8] || (o[8] = () => {
                                    c(E).isGensparkApp() ? c(le).callHandler("goBack", {}) : c(E).isShowBackIcon(c(x).query.version) && c(E).isMobile() ? c(P).back() : va()
                                }),
                                onSubmitPrompt: o[9] || (o[9] = () => {
                                    var e;
                                    null == (e = We.value) || e.chat_now()
                                }),
                                onEditProjectName: fa
                            }, null, 8, ["project"])), "slides_agent" != (null == (M = gt.value) ? void 0 : M.type) || (null == (R = gt.value) ? void 0 : R.id) || (null == (U = null == (N = gt.value) ? void 0 : N.session_state) ? void 0 : U.is_blank) ? v("", !0) : (t(), h(bo, {
                                key: 3,
                                project: gt.value,
                                initialTab: "my" === c(x).query.tab ? "my" : "explore",
                                onProjectCreated: ba,
                                onBack: o[10] || (o[10] = () => {
                                    c(E).isGensparkApp() ? c(le).callHandler("goBack", {}) : c(E).isShowBackIcon(c(x).query.version) && c(E).isMobile() ? c(P).back() : va()
                                }),
                                onSubmitPrompt: o[11] || (o[11] = () => {
                                    var e;
                                    null == (e = We.value) || e.chat_now()
                                }),
                                onEditProjectName: fa
                            }, null, 8, ["project", "initialTab"])), !(null == (H = c(J)) ? void 0 : H.gk_dogfood) || "podcasts_agent" != (null == (z = gt.value) ? void 0 : z.type) || (null == (F = gt.value) ? void 0 : F.id) || (null == (G = null == (W = gt.value) ? void 0 : W.session_state) ? void 0 : G.is_blank) ? v("", !0) : (t(), h(zo, {
                                key: 4,
                                project: gt.value,
                                onProjectCreated: ba,
                                onBack: o[12] || (o[12] = () => {
                                    c(E).isGensparkApp() ? c(le).callHandler("goBack", {}) : c(E).isShowBackIcon(c(x).query.version) && c(E).isMobile() ? c(P).back() : va()
                                }),
                                onSubmitPrompt: o[13] || (o[13] = () => {
                                    var e;
                                    null == (e = We.value) || e.chat_now()
                                }),
                                onEditProjectName: fa
                            }, null, 8, ["project"])), a("div", Qo, [c(J) ? (t(), h(V, {
                                key: 0,
                                ref_key: "agentTaskListRef",
                                ref: Ne,
                                hideLeftSide: Ct.value,
                                onSetShowLeftSide: o[14] || (o[14] = e => {
                                    Ct.value = e
                                }),
                                onEditProjectName: fa
                            }, null, 8, ["hideLeftSide"])) : v("", !0), a("div", {
                                class: "main",
                                onClick: o[43] || (o[43] = e => {
                                    !Ct.value && Tt.value
                                })
                            }, [(t(), h(O, {
                                to: "body"
                            }, [w(q, {
                                name: "fade"
                            }, {
                                default: S((() => [!Ct.value && Tt.value ? (t(), e("div", {
                                    key: 0,
                                    class: "mask",
                                    onClick: o[15] || (o[15] = e => {
                                        e.preventDefault(), e.stopPropagation(), Ct.value = !0
                                    }),
                                    onTouchmove: o[16] || (o[16] = e => {
                                        e.preventDefault(), e.stopPropagation()
                                    })
                                }, null, 32)) : v("", !0)])),
                                _: 1
                            })])), w(q, {
                                name: "fade"
                            }, {
                                default: S((() => [jt.value && Et.value ? (t(), e("div", Ko, [a("div", Yo, r(l.$t("pages.article_verification.continuing-to-work-even-when-you-close-the-page")), 1), a("div", es, [w(c(ge))])])) : v("", !0)])),
                                _: 1
                            }), w(q, {
                                name: "fade"
                            }, {
                                default: S((() => [v("", !0)])),
                                _: 1
                            }), jt.value && gt.value ? (t(), e(u, {
                                key: 0
                            }, [a("div", ts, [a("div", as, r(pa(gt.value, 0)), 1)]), jt.value && gt.value ? (t(), e("div", ls, [a("div", os, r(pa(gt.value, 1)), 1)])) : v("", !0)], 64)) : v("", !0), w(q, {
                                name: "fade"
                            }, {
                                default: S((() => {
                                    var s, n;
                                    return [!(null == (n = ct(null == (s = gt.value) ? void 0 : s.type)) ? void 0 : n.no_run_project) && !jt.value && gt.value && !bt.value && ["STARTED", "PENDING", "PROGRESS"].indexOf(gt.value.status) >= 0 ? (t(), e("div", {
                                        key: 0,
                                        class: "refresh_button",
                                        onClick: o[18] || (o[18] = e => {
                                            Rt.value = 0, Xt(gt.value.id), gt.value.status = "PENDING", gt.value.session_state = {}
                                        })
                                    }, [a("div", ss, r(l.$t("pages.article_verification.refresh")), 1), a("div", ns, [w(c(ne))])])) : v("", !0)]
                                })),
                                _: 1
                            }), ha.value && ga.value || c(x).query.from === c(B) ? v("", !0) : (t(), e("div", {
                                key: 1,
                                class: p(["header-content", {
                                    show: Ft.value
                                }])
                            }, [a("div", is, [gt.value || bt.value || (null == (Z = ct(ht())) ? void 0 : Z.component) || c(E).isShowBackIcon(c(x).query.version) ? (t(), e("div", {
                                key: 0,
                                class: "back-icon icon cursor-pointer",
                                onClick: o[19] || (o[19] = () => {
                                    c(E).isGensparkApp() ? c(le).callHandler("goBack", {}) : c(E).isShowBackIcon(c(x).query.version) && c(E).isMobile() ? c(P).back() : va()
                                })
                            }, [w(c(Ee))])) : v("", !0), Ct.value ? (t(), e("div", {
                                key: 1,
                                class: "button left-toggle",
                                onClick: i(Je, ["stop"])
                            }, [a("div", rs, [w(c(we))])])) : v("", !0)]), a("div", cs, [(null == (Q = ct(null == (X = gt.value) ? void 0 : X.type)) ? void 0 : Q.show_title) ? (t(), e(u, {
                                key: 0
                            }, [Bt() ? (t(), e(u, {
                                key: 0
                            }, [m(r(null == (Y = ct(null == (K = gt.value) ? void 0 : K.type)) ? void 0 : Y.name), 1)], 64)) : bt.value ? (t(), e(u, {
                                key: 1
                            }, [], 64)) : (t(), e("div", us, [a("div", ds, r(null == (ee = gt.value) ? void 0 : ee.name), 1), (null == (te = gt.value) ? void 0 : te.id) ? (t(), e("div", {
                                key: 0,
                                class: "edit-icon",
                                onClick: o[20] || (o[20] = e => fa(gt.value))
                            }, [w(c(Te))])) : v("", !0)]))], 64)) : v("", !0)]), a("div", ps, [a("div", vs, [Bt() ? (t(), e(u, {
                                key: 0
                            }, [gt.value && (!(null == (ae = ct(gt.value.type)) ? void 0 : ae.is_advanced) || (null == (oe = ct(gt.value.type)) ? void 0 : oe.is_deep_research) || (null == (se = ct(gt.value.type)) ? void 0 : se.is_agent)) ? (t(), e("div", {
                                key: 0,
                                class: "icon",
                                onClick: ca
                            }, [w(c(be))])) : v("", !0), gt.value && gt.value.id && (!(null == (re = ct(gt.value.type)) ? void 0 : re.is_advanced) || (null == (ce = ct(gt.value.type)) ? void 0 : ce.is_deep_research) || (null == (ye = ct(gt.value.type)) ? void 0 : ye.is_agent)) ? (t(), e("div", ms, [w(c(kt), {
                                ref_key: "moreMenuPopoverRef",
                                ref: Ge,
                                "show-arrow": !1,
                                style: {
                                    padding: "0",
                                    "border-radius": "8px"
                                },
                                "content-style": "border-radius: 8px; padding: 0; padding: 16px 12px;",
                                placement: "bottom-end",
                                trigger: "click",
                                setShow: ""
                            }, {
                                trigger: S((() => [a("div", hs, [w(c(ke))])])),
                                default: S((() => {
                                    var s, n, i;
                                    return [a("div", gs, [gt.value && gt.value.id && (!(null == (s = ct(gt.value.type)) ? void 0 : s.is_advanced) || (null == (n = ct(gt.value.type)) ? void 0 : n.is_deep_research) || (null == (i = ct(gt.value.type)) ? void 0 : i.is_agent)) ? (t(), e("div", {
                                        key: 0,
                                        class: "menu-item flex items-center gap-[6px]",
                                        onClick: na
                                    }, [qe.value ? (t(), h(c(Ht), {
                                        key: 0,
                                        class: "w-[15px] h-[15px]"
                                    })) : (t(), h(c(qt), {
                                        key: 1,
                                        class: "w-[15px] h-[15px]"
                                    })), a("span", _s, r(qe.value ? l.$t("pages.spark.bookmarked") : l.$t("pages.agents.add_bookmark")), 1)])) : v("", !0), w(j, null, {
                                        default: S((() => [a("div", fs, [gt.value ? (t(), h(Co, {
                                            key: 0,
                                            project: gt.value,
                                            "is-menu-item": !0,
                                            onOpen: o[21] || (o[21] = e => {
                                                var t, a;
                                                null == (t = Ue.value) || t.show(e), null == (a = Ge.value) || a.setShow(!1)
                                            }),
                                            class: "menu-item"
                                        }, null, 8, ["project"])) : v("", !0)])])),
                                        _: 1
                                    })])]
                                })),
                                _: 1
                            }, 512)])) : v("", !0)], 64)) : (t(), e(u, {
                                key: 1
                            }, [gt.value && gt.value.id && (!(null == (xe = ct(gt.value.type)) ? void 0 : xe.is_advanced) || (null == (je = ct(gt.value.type)) ? void 0 : je.is_deep_research) || (null == (Ce = ct(gt.value.type)) ? void 0 : Ce.is_agent)) ? (t(), e("div", {
                                key: 0,
                                class: "icon",
                                onClick: na
                            }, [qe.value ? (t(), h(c(Ht), {
                                key: 0
                            })) : (t(), h(c(qt), {
                                key: 1
                            }))])) : v("", !0), gt.value && (!(null == (Se = ct(gt.value.type)) ? void 0 : Se.is_advanced) || (null == (Le = ct(gt.value.type)) ? void 0 : Le.is_deep_research) || (null == (Ie = ct(gt.value.type)) ? void 0 : Ie.is_agent)) ? (t(), e("div", {
                                key: 1,
                                class: "icon",
                                onClick: ca
                            }, [w(c(be))])) : v("", !0), w(j, null, {
                                default: S((() => [gt.value ? (t(), h(Co, {
                                    key: 0,
                                    project: gt.value,
                                    onOpen: o[22] || (o[22] = e => {
                                        var t;
                                        null == (t = Ue.value) || t.show(e)
                                    })
                                }, null, 8, ["project"])) : v("", !0)])),
                                _: 1
                            }), v("", !0)], 64))])])], 2)), a("div", {
                                class: p(["main-inner-wrapper", c(x).query.from === c(B) ? "browser-extension-main-inner-wrapper" : ""]),
                                ref_key: "mainScrollElement",
                                ref: Pt
                            }, [!gt.value || bt.value || (null == (Ae = ct(gt.value.type)) ? void 0 : Ae.is_advanced) && !(null == (Me = ct(gt.value.type)) ? void 0 : Me.is_deep_research) && !(null == (Re = ct(gt.value.type)) ? void 0 : Re.is_agent) ? gt.value || bt.value ? (t(), e("div", ys, [gt.value && !bt.value ? (t(), h(c(me), {
                                key: 0,
                                ref_key: "crossCheckContent",
                                ref: St,
                                project: gt.value,
                                loadingProjectId: bt.value,
                                runningProject: jt.value,
                                hide_intermediate_steps: !0,
                                onRunProject: o[38] || (o[38] = e => {
                                    c(Yt)(), Jt(e)
                                }),
                                onUpdateProject: o[39] || (o[39] = e => {
                                    ea(e)
                                }),
                                onPhoneCallTaskCreated: o[40] || (o[40] = async e => {
                                    var t;
                                    _t.value = !1, It.value = !0, Dt.value = e.id, Bt() ? Ct.value = !0 : Ct.value = !1, await (null == (t = Ne.value) ? void 0 : t.loadMyProjects())
                                }),
                                onQuotaExceed: o[41] || (o[41] = e => {
                                    _.value = "EXHAUSTED" === e ? "standard" : "LOW" === e ? "credit_insufficient" : "phone_call", A.value.trigger()
                                })
                            }, null, 8, ["project", "loadingProjectId", "runningProject"])) : v("", !0), bt.value ? (t(), e("div", ks, [w(b)])) : v("", !0), !bt.value && ["SUCCESS", "FINISHED"].indexOf(gt.value.status) >= 0 && "phone_call" != gt.value.type ? (t(), e("div", ws, [a("div", {
                                class: p(["like", aa.value ? "active" : ""]),
                                onClick: oa
                            }, [a("div", xs, [aa.value ? (t(), h(c(Pe), {
                                key: 0
                            })) : (t(), h(c($e), {
                                key: 1
                            }))]), a("div", {
                                class: p(["anim", {
                                    play: aa.value
                                }])
                            }, [w(c(he))], 2)], 2), a("div", {
                                class: p(["dislike", la.value ? "active" : ""]),
                                onClick: sa
                            }, [a("div", bs, [la.value ? (t(), h(c(Pe), {
                                key: 0
                            })) : (t(), h(c($e), {
                                key: 1
                            }))])], 2)])) : v("", !0), o[54] || (o[54] = a("div", {
                                class: "page_bottom_placeholder"
                            }, null, -1))])) : (null == (Ke = ct(ht())) ? void 0 : Ke.component) ? (t(), e("div", {
                                key: 2,
                                class: p(["main-inner", null == (Ye = ct(ht())) ? void 0 : Ye.id])
                            }, [(t(), h(g(null == (tt = ct(ht())) ? void 0 : tt.component), {
                                onBasic_agent_click: ot
                            }, null, 32))], 2)) : (t(), e(u, {
                                key: 3
                            }, [c(E).isMobile() ? (t(), e("div", js, [a("h1", null, r(l.$t("components.index_layout.all_agents")), 1), a("h4", null, r(l.$t("components.agents.all_agents_description")), 1), a("div", Cs, [(t(!0), e(u, null, d(c(Ze).filter((e => {
                                var t;
                                return !e.isDogfood || e.isDogfood && (null == (t = c(J)) ? void 0 : t.gk_dogfood)
                            })), (l => (t(), e("div", {
                                class: p(["flex flex-col flex-shrink-0 items-center justify-center w-[calc(50%-6px)] md:w-[208px] h-[160px] md:h-[132px] cursor-pointer border border-[#EFEFEF] rounded-[16px] box-border relative md:hover:shadow-[0px_4px_15px_0px_rgba(0,0,0,0.08)] group md:hover:scale-[1.02] transition-all duration-300 ease-in-out dark:border-[#efefef30]", {
                                    disabled: l.disabled
                                }]),
                                onClick: i((e => (e => {
                                    Tt.value ? e.onClick ? e.type === vt.CALL_FOR_ME ? nt() : e.type === vt.DOWNLOAD_FOR_ME ? ka() : null == window || window.open(e.path, "_blank") : null == window || window.open(e.path, "_blank") : Kt()
                                })(l)), ["stop"])
                            }, [l.isNew ? (t(), e("span", Ts, " New ")) : v("", !0), a("div", Es, [l.onSetting ? (t(), e("div", {
                                key: 0,
                                class: "flex items-center justify-center cursor-pointer text-[#232425] dark:text-[#fff] bg-transparent",
                                onClick: i((() => {
                                    Tt.value ? l.onSetting && l.type === c(vt).CALL_FOR_ME && it() : Kt()
                                }), ["stop"])
                            }, [a("div", Ps, [a("div", $s, [w(c(_e))])])], 8, Ls)) : v("", !0)]), a("div", Is, [a("div", Ds, [a("div", As, [a("div", {
                                class: "w-[48px] h-[48px] flex items-center justify-center rounded-full",
                                style: T({
                                    backgroundColor: c(Ve) ? l.darkBgColor : l.bgColor
                                })
                            }, [(t(), h(g(l.icon), {
                                class: "w-[24px] h-[24px] md:group-hover:scale-[1.2] transition-all duration-300 ease-in-out",
                                style: T({
                                    color: c(Ve) && l.darkColor ? l.darkColor : l.color
                                })
                            }, null, 8, ["style"]))], 4), a("div", Ms, r(l.name), 1)])])])], 10, Ss)))), 256))]), o[55] || (o[55] = a("div", {
                                class: "page_bottom_placeholder"
                            }, null, -1))])) : (t(), e("div", Rs, [a("div", Ns, [a("h1", null, r(l.$t("pages.agents.advanced_agent_title")), 1), a("h4", null, r(l.$t("pages.agents.advanced_agent_description1")), 1), a("div", Us, [(t(!0), e(u, null, d(null == (lt = null == (at = rt.value) ? void 0 : at.agentsPageConfig) ? void 0 : lt.agents.filter((e => e.is_advanced && !e.hide)), (s => (t(), e("div", {
                                class: p(["agent", {
                                    disabled: s.disabled
                                }]),
                                onClick: i((() => {
                                    c(E).isMobile() && (Tt.value ? s.onClick ? s.onClick(s) : ta({
                                        type: s.id
                                    }) : Kt())
                                }), ["stop"])
                            }, [a("div", qs, [a("div", Bs, [a("div", Hs, [a("div", zs, [(t(), h(g(s.icon)))]), a("div", Fs, [m(r(s.name) + " ", 1), s.is_new ? (t(), e("span", Ws, " New ")) : v("", !0)])]), a("div", Gs, [s.onSetting ? (t(), e("div", {
                                key: 0,
                                class: "setting-button",
                                onClick: i((() => {
                                    Tt.value ? s.onSetting && s.onSetting() : Kt()
                                }), ["stop"])
                            }, [a("div", Vs, [a("div", Js, [w(c(_e))])])], 8, Zs)) : v("", !0), c(E).isMobile() ? v("", !0) : (t(), e("div", {
                                key: 1,
                                class: "button",
                                onClick: () => {
                                    Tt.value ? s.onClick ? s.onClick(s) : ta({
                                        type: s.id
                                    }) : Kt()
                                }
                            }, [a("div", Qs, [a("div", Ks, [w(c(be))]), m(" " + r(l.$t("pages.article_verification.task")), 1)])], 8, Xs))])]), o[56] || (o[56] = a("div", {
                                class: "divider popular-tasks-divider"
                            }, null, -1)), a("div", Ys, [a("div", en, r(l.$t("pages.article_verification.popular-tasks")), 1), (t(!0), e(u, null, d(s.popularTasks, (l => (t(), e("div", {
                                class: "task",
                                onClick: i((e => c(E).windowopen(`https://www.genspark.ai/agents?id=${l.id}`)), ["stop"])
                            }, [a("div", an, r(l.name), 1), a("div", ln, [w(c(ie))])], 8, tn)))), 256))])]), s.blog_url && s.blog_title ? (t(), e("div", on, [v("", !0), v("", !0)])) : v("", !0)], 10, Os)))), 256))])]), o[58] || (o[58] = a("div", {
                                class: "divider"
                            }, null, -1)), a("div", sn, [a("h1", null, [a("div", nn, r(l.$t("pages.agents.basic_agent_title")), 1), v("", !0)]), a("h4", null, [m(r(l.$t("pages.agents.worlds_first")) + " ", 1), a("a", {
                                href: "https://mainfunc.ai/blog/genspark_mixture_of_agents",
                                onClick: o[42] || (o[42] = i((e => {
                                    c(E).windowopen(e.target.href)
                                }), ["prevent"]))
                            }, [m(r(l.$t("pages.agents.mixture_of_agents_system")), 1), a("span", rn, [w(c(fe))])])]), a("div", cn, [(t(!0), e(u, null, d(null == (ut = null == (st = rt.value) ? void 0 : st.agentsPageConfig) ? void 0 : ut.agents.filter((e => !e.hide && !e.is_advanced)), (l => (t(), e("div", {
                                class: p(["agent", {
                                    disabled: l.disabled
                                }]),
                                onClick: () => {
                                    Tt.value && !l.disabled ? l.onClick(l) : Kt()
                                }
                            }, [a("div", dn, [a("div", pn, [a("div", vn, [(t(), h(g(l.icon)))]), a("div", mn, r(l.name), 1)]), a("div", hn, [o[57] || (o[57] = a("div", {
                                class: "text"
                            }, "Mixture-of-Agents", -1)), a("div", gn, [(t(!0), e(u, null, d(l.modelIcons, (l => (t(), e("div", _n, [a("img", {
                                src: l
                            }, null, 8, fn)])))), 256))])])])], 10, un)))), 256))])]), o[59] || (o[59] = a("div", {
                                class: "page_bottom_placeholder"
                            }, null, -1))]))], 64)) : (t(), e("div", {
                                key: 0,
                                class: p(["main-inner j-chat-agent", {
                                    [null == (Be = ct(gt.value.type)) ? void 0 : Be.id]: !0,
                                    chat_agent: null == (He = ct(gt.value.type)) ? void 0 : He.is_agent,
                                    app_chat: (null == (ze = ct(gt.value.type)) ? void 0 : ze.is_agent) && ga.value
                                }])
                            }, [(null == (Fe = ct(gt.value.type)) ? void 0 : Fe.is_deep_research) ? (t(), h(ue, {
                                key: 0,
                                project: gt.value,
                                "onUpdate:isRunningBackground": o[23] || (o[23] = e => {
                                    da.value = e
                                }),
                                onRequestRefreshProject: o[24] || (o[24] = () => {
                                    Xt(gt.value.id, {
                                        silent: !0
                                    })
                                }),
                                onNewSessionCreated: o[25] || (o[25] = e => {
                                    var t;
                                    gt.value = e, null == (t = Ne.value) || t.addOrUpdateProject(e)
                                }),
                                onRequestCreateNewSession: o[26] || (o[26] = () => {
                                    ra()
                                })
                            }, null, 8, ["project"])) : "general_chat" == gt.value.type || "moa_chat" == gt.value.type ? (t(), h(C, {
                                key: 1,
                                project: gt.value,
                                onNewSessionCreated: o[27] || (o[27] = e => {
                                    var t;
                                    gt.value = e, null == (t = Ne.value) || t.addOrUpdateProject(e)
                                }),
                                onRequestCreateNewSession: o[28] || (o[28] = () => {
                                    ra()
                                }),
                                onRequestLoadProject: o[29] || (o[29] = e => {
                                    Xt(e)
                                })
                            }, null, 8, ["project"])) : v("", !0), "moa_generate_image" == gt.value.type ? (t(), h(c(de), {
                                key: 2,
                                ref: "generateImageContent",
                                project: gt.value,
                                debug: ua.value,
                                onNewSessionCreated: o[30] || (o[30] = e => {
                                    var t;
                                    gt.value = e, null == (t = Ne.value) || t.addOrUpdateProject(e)
                                })
                            }, null, 8, ["project", "debug"])) : v("", !0), "moa_generate_video" == gt.value.type ? (t(), h(c(pe), {
                                key: 3,
                                ref: "generateVideoContent",
                                project: gt.value,
                                onNewSessionCreated: o[31] || (o[31] = e => {
                                    var t;
                                    gt.value = e, null == (t = Ne.value) || t.addOrUpdateProject(e)
                                })
                            }, null, 8, ["project"])) : (null == (Xe = ct(gt.value.type)) ? void 0 : Xe.is_agent) ? (t(), h(De, {
                                key: 4,
                                ref_key: "chatAgentRef",
                                ref: We,
                                no_input: ha.value && ga.value,
                                project: gt.value,
                                is_slide_agent: null == (Qe = ct(gt.value.type)) ? void 0 : Qe.is_slide_agent,
                                onNewSessionCreated: o[32] || (o[32] = e => {
                                    var t;
                                    gt.value = e, null == (t = Ne.value) || t.addOrUpdateProject(e)
                                }),
                                onRequestLoadProject: o[33] || (o[33] = e => {
                                    Xt(e)
                                }),
                                onRequestCreateNewSession: o[34] || (o[34] = () => {
                                    ra()
                                }),
                                onPhoneCallTaskCreated: o[35] || (o[35] = async e => {
                                    var t;
                                    _t.value = !1, It.value = !0, Dt.value = e.id, Bt() ? Ct.value = !0 : Ct.value = !1, await (null == (t = Ne.value) ? void 0 : t.loadMyProjects())
                                }),
                                onQuotaExceed: o[36] || (o[36] = e => {
                                    _.value = "EXHAUSTED" === e ? "standard" : "LOW" === e ? "credit_insufficient" : "phone_call", A.value.trigger()
                                })
                            }, null, 8, ["no_input", "project", "is_slide_agent"])) : v("", !0), "moa_translator" == gt.value.type ? (t(), h(c(ve), {
                                key: 5,
                                ref: "moaTranslatorContent",
                                project: gt.value,
                                onNewSessionCreated: o[37] || (o[37] = e => {
                                    var t;
                                    gt.value = e, null == (t = Ne.value) || t.addOrUpdateProject(e)
                                })
                            }, null, 8, ["project"])) : v("", !0)], 2))], 2)])])])]
                        })),
                        _: 1
                    })], 2)])),
                    _: 1
                }, 8, ["hideBottomBar", "agentType", "forceCollapseSidebar", "projectType"]), w(j, null, {
                    default: S((() => [w(ye, {
                        show: _t.value,
                        "onUpdate:show": o[45] || (o[45] = e => _t.value = e),
                        agentConfig: ct(ft.value),
                        onCreated: o[46] || (o[46] = async e => {
                            var t;
                            _t.value = !1, It.value = !0, Dt.value = e.id, Bt() ? Ct.value = !0 : Ct.value = !1, await (null == (t = Ne.value) ? void 0 : t.loadMyProjects())
                        }),
                        onQuotaExceed: o[47] || (o[47] = () => {
                            $.value.trigger()
                        }),
                        ref_key: "createFormAutopilotAgent",
                        ref: zt
                    }, null, 8, ["show", "agentConfig"])])),
                    _: 1
                }), w(oe, {
                    show: wt.value,
                    "onUpdate:show": o[48] || (o[48] = e => wt.value = e),
                    agentConfig: ct(ft.value),
                    "initial-contact": Ye.value || (null == (f = at.value) ? void 0 : f.contact),
                    "initial-purpose": tt.value || (null == (y = at.value) ? void 0 : y.purpose),
                    onCreated: o[49] || (o[49] = async e => {
                        var t;
                        wt.value = !1, It.value = !0, Dt.value = e.id, Bt() ? Ct.value = !0 : Ct.value = !1, await (null == (t = Ne.value) ? void 0 : t.loadMyProjects())
                    }),
                    onQuotaExceed: o[50] || (o[50] = e => {
                        _.value = "EXHAUSTED" === e ? "standard" : "LOW" === e ? "credit_insufficient" : "phone_call", A.value.trigger()
                    }),
                    ref_key: "createFormPhoneCall",
                    ref: xt
                }, null, 8, ["show", "agentConfig", "initial-contact", "initial-purpose"])]), w(Ce, {
                    ref_key: "quotaExceed",
                    ref: $
                }, null, 512), w(Ce, {
                    ref_key: "quotaExceedPhoneCall",
                    ref: A,
                    windowType: _.value
                }, null, 8, ["windowType"]), w(j, null, {
                    default: S((() => [(t(), h(O, {
                        to: "body"
                    }, [w(q, {
                        name: "fade"
                    }, {
                        default: S((() => [w(Z, {
                            styleClass: "index"
                        })])),
                        _: 1
                    })]))])),
                    _: 1
                }), w(se, {
                    ref_key: "phoneCallSetting",
                    ref: n,
                    onCreated: o[51] || (o[51] = async e => {
                        var t, a;
                        c(J).phone_call_is_not_first_time || (K.value = !0, Q.value = !1), null == (t = n.value) || t.hide(), It.value = !0, Dt.value = e.id, Bt() ? Ct.value = !0 : Ct.value = !1, await (null == (a = Ne.value) ? void 0 : a.loadMyProjects())
                    }),
                    onQuotaExceed: o[52] || (o[52] = e => {
                        _.value = "EXHAUSTED" === e ? "standard" : "LOW" === e ? "credit_insufficient" : "phone_call", A.value.trigger()
                    })
                }, null, 512), w(Re, {
                    ref_key: "editProjectNameModalRef",
                    ref: _a,
                    onFinishEditProjectName: ya
                }, null, 512), w(Wt, {
                    ref_key: "downloaderWrapperRef",
                    ref: ze,
                    onRouteToSuperAgent: st
                }, null, 512), w(dt, {
                    isAsking: null == (k = We.value) ? void 0 : k.isAsking(),
                    ref_key: "permissionPopoverRef",
                    ref: Ue,
                    project: gt.value
                }, null, 8, ["isAsking", "project"])], 64)
            }
        }
    }, [
        ["__scopeId", "data-v-19e33e89"]
    ]);
export {
    yn as
    default
};