import {
    d as o,
    o as r,
    b as t
} from "./Cf0SOiw0.js";
const n = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const e = {
    render: function(e, s) {
        return r(), o("svg", n, s[0] || (s[0] = [t("path", {
            d: "M17.0003 3H11.4448M17.0003 3L8.66699 11.3333M17.0003 3V8.55556",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }, null, -1), t("path", {
            d: "M17 13.1111V15.4444C17 15.857 16.8361 16.2527 16.5444 16.5444C16.2527 16.8361 15.857 17 15.4444 17H4.55556C4.143 17 3.74733 16.8361 3.45561 16.5444C3.16389 16.2527 3 15.857 3 15.4444V4.55556C3 4.143 3.16389 3.74733 3.45561 3.45561C3.74733 3.16389 4.143 3 4.55556 3H6.88889",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linecap": "round"
        }, null, -1)]))
    }
};
export {
    e as O
};