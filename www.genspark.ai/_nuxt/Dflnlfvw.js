import {
    d as t,
    o,
    D as r
} from "./Cf0SOiw0.js";
const e = {
    width: "16",
    height: "17",
    viewBox: "0 0 16 17",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const s = {
    render: function(s, n) {
        return o(), t("svg", e, n[0] || (n[0] = [r('<path d="M13.6667 2.16602H2.33335C1.96516 2.16602 1.66669 2.46449 1.66669 2.83268V14.166C1.66669 14.5342 1.96516 14.8327 2.33335 14.8327H13.6667C14.0349 14.8327 14.3334 14.5342 14.3334 14.166V2.83268C14.3334 2.46449 14.0349 2.16602 13.6667 2.16602Z" stroke="#606366" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"></path><path d="M1.66669 6.5H14.3334" stroke="#606366" stroke-width="1.33333" stroke-linecap="round"></path><path d="M1.66669 10.5H14.3334" stroke="#606366" stroke-width="1.33333" stroke-linecap="round"></path><path d="M5.66669 2.16602V14.8327" stroke="#606366" stroke-width="1.33333" stroke-linecap="round"></path><path d="M10 2.16602V14.8327" stroke="#606366" stroke-width="1.33333" stroke-linecap="round"></path>', 5)]))
    }
};
export {
    s as I
};