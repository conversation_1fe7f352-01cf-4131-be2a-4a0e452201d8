import {
    d as r,
    D as t,
    o as e,
    b as o
} from "./Cf0SOiw0.js";
const n = {
    viewBox: "0 0 30 30",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const i = {
        render: function(o, i) {
            return e(), r("svg", n, i[0] || (i[0] = [t('<circle cx="15.0056" cy="15.0017" r="11.8571" fill="transparent"></circle><circle cx="15.0056" cy="15.0017" r="11.8571" stroke="currentColor" stroke-width="2"></circle><path fill-rule="evenodd" clip-rule="evenodd" d="M17.5467 18.4394L24.2711 11.715L22.8569 10.3008L16.8401 16.3176L15.7116 15.1873L14.2963 16.6004L16.1319 18.4389L16.839 19.1471L17.5467 18.4394ZM10.8372 18.5215L17.5617 11.797L16.1474 10.3828L10.1301 16.4001L7.14078 13.4108L5.72656 14.825L9.423 18.5215L10.1301 19.2286L10.8372 18.5215Z" fill="currentColor"></path><defs><radialGradient id="paint0_radial_7043_13924" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(15.0056 15.0017) rotate(90) scale(16.0714)"><stop></stop><stop offset="1"></stop></radialGradient></defs>', 4)]))
        }
    },
    s = {
        width: "28",
        height: "28",
        viewBox: "0 0 28 28",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const d = {
        render: function(t, n) {
            return e(), r("svg", s, n[0] || (n[0] = [o("g", {
                id: "chatting-01"
            }, [o("path", {
                id: "Vector",
                d: "M22.1667 10.7553C21.342 6.62229 17.5133 3.49609 12.9156 3.49609C7.71606 3.49609 3.5 7.49408 3.5 12.425C3.5 14.7942 4.47294 16.9469 6.05998 18.5443C6.4094 18.8961 6.64269 19.3766 6.54854 19.8712C6.39315 20.6799 6.04101 21.4342 5.52539 22.0629C6.88202 22.313 8.29264 22.0878 9.50238 21.45C9.93003 21.2246 10.1438 21.1119 10.2947 21.089C10.4004 21.073 10.5379 21.088 10.7593 21.1259",
                stroke: "currentColor",
                "stroke-width": "1.86667",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }), o("path", {
                id: "Vector_2",
                d: "M12.834 18.4126C12.834 21.4945 15.446 23.9932 18.6673 23.9932C19.0461 23.9937 19.4237 23.9587 19.7958 23.8889C20.0636 23.8385 20.1976 23.8134 20.291 23.8277C20.3844 23.8419 20.517 23.9124 20.782 24.0533C21.5314 24.4519 22.4053 24.5926 23.2458 24.4363C22.9264 24.0434 22.7082 23.572 22.6119 23.0665C22.5536 22.7574 22.6982 22.4571 22.9146 22.2372C23.8979 21.2387 24.5007 19.8934 24.5007 18.4126C24.5007 15.3308 21.8886 12.832 18.6673 12.832C15.446 12.832 12.834 15.3308 12.834 18.4126Z",
                stroke: "currentColor",
                "stroke-width": "1.86667",
                "stroke-linejoin": "round"
            })], -1)]))
        }
    },
    C = {
        width: "28",
        height: "28",
        viewBox: "0 0 28 28",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const a = {
    render: function(o, n) {
        return e(), r("svg", C, n[0] || (n[0] = [t('<g id="ai-image"><path id="Vector" d="M8.16602 11.082C9.13251 11.082 9.91602 10.2985 9.91602 9.33203C9.91602 8.36553 9.13251 7.58203 8.16602 7.58203C7.19952 7.58203 6.41602 8.36553 6.41602 9.33203C6.41602 10.2985 7.19952 11.082 8.16602 11.082Z" stroke="currentColor" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round"></path><path id="Vector_2" d="M24.498 12.8333C24.5006 13.3818 24.5007 13.9642 24.5007 14.5833C24.5007 19.808 24.5007 22.4204 22.8776 24.0436C21.2544 25.6667 18.642 25.6667 13.4173 25.6667C8.19259 25.6667 5.58022 25.6667 3.9571 24.0436C2.33398 22.4204 2.33398 19.808 2.33398 14.5833C2.33398 9.3586 2.33398 6.74624 3.9571 5.12311C5.58022 3.5 8.19259 3.5 13.4173 3.5C14.0365 3.5 14.6189 3.5 15.1673 3.5027" stroke="currentColor" stroke-width="1.75" stroke-linecap="round"></path><path id="Vector_3" d="M21.5833 2.33203L21.8842 3.14523C22.2788 4.21155 22.4761 4.74472 22.865 5.13365C23.254 5.5226 23.7872 5.71988 24.8535 6.11446L25.6667 6.41536L24.8535 6.71627C23.7872 7.11085 23.254 7.30814 22.865 7.69708C22.4761 8.08601 22.2788 8.61917 21.8842 9.6855L21.5833 10.4987L21.2824 9.6855C20.8879 8.61917 20.6906 8.08601 20.3016 7.69708C19.9127 7.30814 19.3795 7.11085 18.3132 6.71627L17.5 6.41536L18.3132 6.11446C19.3795 5.71988 19.9127 5.5226 20.3016 5.13365C20.6906 4.74472 20.8879 4.21155 21.2824 3.14523L21.5833 2.33203Z" stroke="currentColor" stroke-width="1.75" stroke-linejoin="round"></path><path id="Vector_4" d="M5.25 25.082C10.3512 18.9861 16.0698 10.9467 24.4971 16.3814" stroke="currentColor" stroke-width="1.75"></path></g>', 1)]))
    }
};
export {
    i as C, d as G, a
};