import {
    M as e
} from "./BLjKtaC-.js";
import {
    L as a,
    R as t,
    Y as n
} from "./CQ2glRxo.js";
import {
    _ as s,
    r as o,
    d as l,
    H as r,
    f as i,
    q as c,
    e as d,
    F as p,
    k as u,
    b as h,
    E as v,
    aE as g,
    o as _,
    n as m,
    t as w,
    I as k,
    D as f,
    y,
    J as C,
    s as S,
    a as x,
    w as b,
    v as A,
    i as M,
    a4 as I,
    C as L,
    p as T,
    l as R,
    aa as P,
    L as E,
    h as B,
    x as H,
    c as $,
    U as j,
    ai as U,
    V as D,
    G as N,
    S as F,
    cv as V,
    av as O,
    dP as W
} from "./Cf0SOiw0.js";
import {
    L as z
} from "./Bzg9uoz_.js";
import {
    _ as q,
    l as Z,
    m as G
} from "./CsPFbezH.js";
import {
    _ as J
} from "./D6bQc9d9.js";
import {
    _ as Q
} from "./e-ES_T8J.js";
import {
    M as Y
} from "./B56nEZrv.js";
import {
    c as K,
    S as X,
    N as ee
} from "./tBofk-gQ.js";
import {
    S as ae,
    F as te,
    k as ne,
    e as se
} from "./Jx3-I-D7.js";
import {
    F as oe,
    M as le
} from "./DkAysl9I.js";
import {
    E as re
} from "./Cy7E5O2b.js";
import {
    E as ie
} from "./D5ao1EUl.js";
import {
    N as ce
} from "./CW991W2w.js";
import {
    S as de
} from "./DcBgjX7B.js";
import {
    C as pe
} from "./Dnth285N.js";
import {
    C as ue
} from "./DW6cX6jm.js";
import {
    S as he
} from "./C_XD2eP3.js";
import {
    f as ve
} from "./Bl-gMEVt.js";
import {
    P as ge
} from "./DHyvnBfI.js";
import {
    T as _e,
    P as me
} from "./BDUh8PoD.js";
import {
    A as we
} from "./DwpGavSW.js";
import {
    u as ke
} from "./DJ-JsGJu.js";
import {
    N as fe
} from "./BjWUbj3w.js";
import ye from "./BAZ5Sqbz.js";
import Ce from "./x7yzXAMO.js";
import {
    B as Se
} from "./DYrQUwU9.js";
import {
    h as xe
} from "./CvZgRYhs.js";
import {
    M as be
} from "./Dc8Bac8D.js";
import {
    C as Ae
} from "./BrcpPT-Q.js";
import {
    D as Me
} from "./WhweajiO.js";
import {
    I as Ie
} from "./DqWfLcpp.js";
import {
    L as Le
} from "./CRIx66FB.js";
import {
    M as Te
} from "./BYvs8isC.js";
import {
    N as Re
} from "./CaEkZ53E.js";
const Pe = ["src"],
    Ee = {
        key: 0,
        class: "source-wrapper"
    },
    Be = ["onClick"],
    He = {
        class: "text"
    },
    $e = {
        class: "icon"
    },
    je = {
        key: 2,
        class: "dot-wrapper"
    },
    Ue = ["onClick"],
    De = {
        class: "image_load_wrapper"
    },
    Ne = ["src", "onLoad"];
const Fe = s({
        name: "",
        components: {
            LeftArrowSvg: z,
            RightArrowSvg: t,
            LinkNewWindowSvg: a
        },
        props: {
            images: {
                type: Array,
                default: () => []
            }
        },
        setup(e) {
            const a = o(e.images),
                t = o([]),
                n = o(0),
                s = o(100),
                l = o(null);
            return {
                loadedImagelist: t,
                imgRef: (e, a) => {
                    0 === a && e && (e.complete ? setTimeout((() => {
                        s.value = Math.max(e.naturalHeight, s.value)
                    }), 0) : e.onload = e => {
                        s.value = Math.max(e.target.naturalHeight, s.value)
                    })
                },
                height: s,
                wrapper: l,
                currentIndex: n,
                imagelist: a,
                imageLoadError: e => {},
                windowopen: e => {
                    window.open(e, "_blank")
                }
            }
        }
    }, [
        ["render", function(e, a, t, n, s, o) {
            const f = v("LeftArrowSvg"),
                y = v("RightArrowSvg"),
                C = v("LinkNewWindowSvg"),
                S = g("touch");
            return _(), l(p, null, [n.loadedImagelist.length > 0 ? r((_(), l("div", {
                key: 0,
                ref: "wrapper",
                class: "wrapper",
                style: c({
                    height: n.height + "px"
                })
            }, [n.loadedImagelist.length > 1 ? (_(), l("div", {
                key: 0,
                class: "arrow left",
                onClick: a[0] || (a[0] = e => n.currentIndex > 0 ? n.currentIndex -= 1 : n.currentIndex = n.loadedImagelist.length - 1)
            }, [d(f)])) : i("", !0), n.loadedImagelist.length > 1 ? (_(), l("div", {
                key: 1,
                class: "arrow right",
                onClick: a[1] || (a[1] = e => n.currentIndex < n.loadedImagelist.length - 1 ? n.currentIndex += 1 : n.currentIndex = 0)
            }, [d(y)])) : i("", !0), (_(!0), l(p, null, u(n.loadedImagelist, ((e, a) => (_(), l("div", {
                key: a,
                class: m(["image", 0 == a ? "first" : ""]),
                style: c({
                    marginLeft: 100 * (a - n.currentIndex) + "%"
                })
            }, [h("img", {
                src: e.thumbnail_url,
                ref_for: !0,
                ref: e => {
                    n.imgRef(e, a)
                }
            }, null, 8, Pe), e.source ? (_(), l("div", Ee, [h("div", {
                class: "source",
                onClick: a => n.windowopen(e.source_url)
            }, [h("div", He, w(e.source), 1), h("div", $e, [d(C)])], 8, Be)])) : i("", !0)], 6)))), 128)), n.loadedImagelist.length > 1 ? (_(), l("div", je, [(_(!0), l(p, null, u(n.loadedImagelist, ((e, a) => (_(), l("div", {
                key: a,
                class: m(["dot", {
                    active: a === n.currentIndex
                }]),
                onClick: e => n.currentIndex = a
            }, null, 10, Ue)))), 128))])) : i("", !0)], 4)), [
                [S, () => {
                    n.loadedImagelist.length > 1 && (n.currentIndex > 0 ? n.currentIndex -= 1 : n.currentIndex = n.loadedImagelist.length - 1)
                }, "swipe", {
                    right: !0
                }],
                [S, () => {
                    n.loadedImagelist.length > 1 && (n.currentIndex < n.loadedImagelist.length - 1 ? n.currentIndex += 1 : n.currentIndex = 0)
                }, "swipe", {
                    left: !0
                }]
            ]) : i("", !0), r(h("div", De, [(_(!0), l(p, null, u(n.imagelist, ((e, t) => (_(), l("img", {
                src: e.thumbnail_url,
                onLoad: a => {
                    n.loadedImagelist.splice(t, 0, e)
                },
                onError: a[2] || (a[2] = (...e) => n.imageLoadError && n.imageLoadError(...e))
            }, null, 40, Ne)))), 256))], 512), [
                [k, !1]
            ])], 64)
        }],
        ["__scopeId", "data-v-6bd45b9a"]
    ]),
    Ve = {
        width: "10",
        height: "10",
        viewBox: "0 0 10 10",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Oe = {
        render: function(e, a) {
            return _(), l("svg", Ve, a[0] || (a[0] = [f('<g clip-path="url(#clip0_4275_293)"><path d="M4.49999 8C6.43299 8 8 6.43299 8 4.49999C8 2.567 6.43299 1 4.49999 1C2.567 1 1 2.567 1 4.49999C1 6.43299 2.567 8 4.49999 8Z" stroke="#0C66CC" stroke-width="1.5" stroke-linecap="round"></path><path d="M7 7L9 9" stroke="#0C66CC" stroke-width="1.5" stroke-linecap="round"></path></g><defs><clipPath id="clip0_4275_293"><rect width="10" height="10" fill="white"></rect></clipPath></defs>', 2)]))
        }
    },
    We = {
        class: "magnifier-animation"
    },
    ze = s({
        __name: "MagnifierAnimation",
        setup: e => (e, a) => (_(), l("div", We, [d(y(Oe), {
            class: "magnifier-icon"
        })]))
    }, [
        ["__scopeId", "data-v-50c10e26"]
    ]),
    qe = {
        width: "11",
        height: "6",
        viewBox: "0 0 11 6",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Ze = {
        render: function(e, a) {
            return _(), l("svg", qe, a[0] || (a[0] = [h("g", {
                "clip-path": "url(#clip0_2335_1670)"
            }, [h("path", {
                d: "M6.34585 5.1647C5.94825 5.60995 5.25165 5.60995 4.85406 5.1647L-2.9582 -3.58394C-3.53363 -4.22834 -3.07623 -5.25 -2.21231 -5.25L13.4122 -5.25C14.2761 -5.25 14.7335 -4.22834 14.1581 -3.58394L6.34585 5.1647Z",
                fill: "#03172F"
            })], -1), h("defs", null, [h("clipPath", {
                id: "clip0_2335_1670"
            }, [h("rect", {
                width: "11",
                height: "6",
                fill: "white"
            })])], -1)]))
        }
    },
    Ge = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAYAAACohjseAAAACXBIWXMAACxLAAAsSwGlPZapAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAE6SURBVHgB7ddBaoNAFMbxp90U3OQIOUJB3Cc36BG6Unc9QtJdly6tXqK9QbvrQqFHyLJLewC1ryUDIRhjfDNvEb4fiMMMKH9BdIgAAAAAAAAAAAAs80hJkiSPXddt/2/qeVlRFE+k4IYUxHG84dMzh93+HTxehWFIdV1/kGPOA/dx24EllUingSNxhvNIZ4ET4gynkU4CR+Iafge/+bw4mncWaT1wLK7v+7Xv+y88vielSKuB5+LKsvyqqqqJouiNlCKtBU6JMxOakVYCL4kztCLFgXPiDI1IUaAkzpgQ+cORnzSTTzIPA3OT44w8z3f8+VjzsRtY3pCANPD4iV8cZ4xELkhAGni4I5gdZ5yIFO06xNulNE1XbdsugyB4zbKsIQv4mkveWt3xT0HD0e8EAAAAAAAAAHBdfgGplsF47N7PeQAAAABJRU5ErkJggg==",
    Je = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Qe = {
        render: function(e, a) {
            return _(), l("svg", Je, a[0] || (a[0] = [f('<g clip-path="url(#clip0_4236_208)"><path opacity="0.9" d="M10 -4.37114e-07C15.5228 -1.95703e-07 20 4.47715 20 10V10C20 15.5228 15.5228 20 10 20V20C4.47715 20 -1.6322e-06 15.5228 -1.39079e-06 10V10C-1.14938e-06 4.47715 4.47715 -6.78525e-07 10 -4.37114e-07V-4.37114e-07Z" fill="#EFEFEF"></path><path d="M14.502 11.7578L10.2577 7.51352L6.01336 11.7578" stroke="#666666" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g><defs><clipPath id="clip0_4236_208"><rect width="20" height="20" fill="white"></rect></clipPath></defs>', 2)]))
        }
    },
    Ye = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Ke = {
        render: function(e, a) {
            return _(), l("svg", Ye, a[0] || (a[0] = [h("path", {
                d: "M6 12L16 12",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), h("path", {
                d: "M13.1506 7.93282L16.3129 11.2734C16.6926 11.6745 16.6926 12.3248 16.3129 12.7259L13.1506 16.0665",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    Xe = {
        width: "16",
        height: "16",
        viewBox: "0 0 16 16",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const ea = {
        render: function(e, a) {
            return _(), l("svg", Xe, a[0] || (a[0] = [f('<path d="M3.19922 6.40039V12.0004H11.1992V2.40039H3.19922V3.80039" stroke="black" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"></path><path d="M8.13216 13.6006L12.7988 13.6006L12.7988 4.00059L11.1988 4.00059M5.96549 13.6006L4.79883 13.6006L4.79883 12.0006" stroke="black" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"></path><path d="M7.19922 4.7998H9.59922" stroke="black" stroke-width="0.8" stroke-linecap="round"></path><path d="M7.19922 9.60059H9.59922" stroke="black" stroke-width="0.8" stroke-linecap="round"></path><path d="M7.19922 7.2002H9.59922" stroke="black" stroke-width="0.8" stroke-linecap="round"></path><path d="M4.79883 4.7998H5.59883" stroke="#FF5622" stroke-width="0.8" stroke-linecap="round"></path><path d="M4.79883 9.60059H5.59883" stroke="#42A5F5" stroke-width="0.8" stroke-linecap="round"></path><path d="M4.79883 7.2002H5.59883" stroke="#FFCA28" stroke-width="0.8" stroke-linecap="round"></path>', 8)]))
        }
    },
    aa = C({
        components: {
            PartnerDataSvg: ea,
            NPopover: ce
        },
        props: {
            text: {
                type: String,
                required: !1
            },
            hoverShowText: {
                type: Boolean,
                required: !1
            }
        },
        setup(e) {
            const a = o(e.text),
                t = o(e.hoverShowText),
                n = o(""),
                {
                    t: s
                } = S();
            return a.value || (a.value = s("components.partner_data.partner_data")), t.value && (n.value = s("components.partner_data.hover_text")), {
                hoverShowText: t,
                hoverText: n,
                text: a
            }
        }
    }),
    ta = {
        class: "partner-data-container"
    },
    na = {
        key: 0,
        class: "partner-data-text"
    },
    sa = {
        class: "popover"
    },
    oa = {
        key: 1,
        class: "partner-data-container"
    },
    la = {
        key: 0,
        class: "partner-data-text"
    };
const ra = s(aa, [
        ["render", function(e, a, t, n, s, o) {
            const r = v("PartnerDataSvg"),
                c = v("NPopover");
            return e.hoverText ? (_(), x(c, {
                key: 0,
                placement: "bottom-start",
                trigger: "hover",
                content: e.hoverText
            }, {
                trigger: b((() => [h("div", ta, [d(r), e.text ? (_(), l("div", na, w(e.text), 1)) : i("", !0)])])),
                default: b((() => [h("div", sa, w(e.hoverText), 1)])),
                _: 1
            }, 8, ["content"])) : (_(), l("div", oa, [d(r), e.text ? (_(), l("div", la, w(e.text), 1)) : i("", !0)]))
        }],
        ["__scopeId", "data-v-c5f7ce9e"]
    ]),
    ia = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 24 24"
    };
const ca = {
        render: function(e, a) {
            return _(), l("svg", ia, a[0] || (a[0] = [h("path", {
                d: "M16.59 8.59L12 13.17L7.41 8.59L6 10l6 6l6-6l-1.41-1.41z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    da = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 24 24"
    };
const pa = {
        render: function(e, a) {
            return _(), l("svg", da, a[0] || (a[0] = [h("path", {
                d: "M12 8l-6 6l1.41 1.41L12 10.83l4.59 4.58L18 14l-6-6z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    ua = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 24 24"
    };
const ha = {
        render: function(e, a) {
            return _(), l("svg", ua, a[0] || (a[0] = [h("path", {
                d: "M19 19H5V5h7V3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83l1.41 1.41L19 6.41V10h2V3h-7z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    va = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 32 32"
    };
const ga = {
        render: function(e, a) {
            return _(), l("svg", va, a[0] || (a[0] = [h("path", {
                d: "M26 12h-6V6a3.003 3.003 0 0 0-3-3h-2.133a2.01 2.01 0 0 0-1.98 1.717l-.845 5.917L8.465 16H2v14h21a7.008 7.008 0 0 0 7-7v-7a4.005 4.005 0 0 0-4-4zM8 28H4V18h4zm20-5a5.006 5.006 0 0 1-5 5H10V17.303l3.958-5.937l.91-6.366H17a1 1 0 0 1 1 1v8h8a2.002 2.002 0 0 1 2 2z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    _a = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 32 32"
    };
const ma = {
        render: function(e, a) {
            return _(), l("svg", _a, a[0] || (a[0] = [h("path", {
                d: "M2 16h5v14H2z",
                fill: "currentColor"
            }, null, -1), h("path", {
                d: "M23 30H9V15.197l3.042-4.563l.845-5.917A2.01 2.01 0 0 1 14.867 3H15a3.003 3.003 0 0 1 3 3v6h8a4.005 4.005 0 0 1 4 4v7a7.008 7.008 0 0 1-7 7z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    wa = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const ka = {
        render: function(e, a) {
            return _(), l("svg", wa, a[0] || (a[0] = [h("path", {
                "fill-rule": "evenodd",
                "clip-rule": "evenodd",
                d: "M13.8958 3.12773C13.0133 3.12773 12.2979 3.84314 12.2979 4.72564C12.2979 5.60815 13.0133 6.32355 13.8958 6.32355C14.7783 6.32355 15.4937 5.60815 15.4937 4.72564C15.4937 3.84314 14.7783 3.12773 13.8958 3.12773ZM11.0979 4.72564C11.0979 3.1804 12.3506 1.92773 13.8958 1.92773C15.4411 1.92773 16.6937 3.1804 16.6937 4.72564C16.6937 6.27089 15.4411 7.52355 13.8958 7.52355C13.1763 7.52355 12.5202 7.25196 12.0245 6.80571L7.8048 9.26652C7.86824 9.50047 7.90209 9.7466 7.90209 10.0006C7.90209 10.2671 7.86484 10.5249 7.79527 10.769L11.7906 13.4326C12.3035 12.8473 13.0565 12.4777 13.8958 12.4777C15.4411 12.4777 16.6937 13.7304 16.6937 15.2756C16.6937 16.8209 15.4411 18.0735 13.8958 18.0735C12.3506 18.0735 11.0979 16.8209 11.0979 15.2756C11.0979 15.0021 11.1372 14.7378 11.2103 14.4879L7.22205 11.8291C6.70899 12.4228 5.95047 12.7985 5.10418 12.7985C3.55894 12.7985 2.30627 11.5459 2.30627 10.0006C2.30627 8.45538 3.55894 7.20272 5.10418 7.20272C5.96467 7.20272 6.73444 7.59117 7.24768 8.20227L11.3226 5.82592C11.178 5.4882 11.0979 5.11627 11.0979 4.72564ZM5.10418 8.40272C4.22168 8.40272 3.50627 9.11813 3.50627 10.0006C3.50627 10.8831 4.22168 11.5985 5.10418 11.5985C5.98669 11.5985 6.70209 10.8831 6.70209 10.0006C6.70209 9.11813 5.98669 8.40272 5.10418 8.40272ZM12.2979 15.2756C12.2979 14.3931 13.0133 13.6777 13.8958 13.6777C14.7783 13.6777 15.4937 14.3931 15.4937 15.2756C15.4937 16.1581 14.7783 16.8735 13.8958 16.8735C13.0133 16.8735 12.2979 16.1581 12.2979 15.2756Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    fa = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const ya = {
        render: function(e, a) {
            return _(), l("svg", fa, a[0] || (a[0] = [h("path", {
                "fill-rule": "evenodd",
                "clip-rule": "evenodd",
                d: "M4.6 2.89453C3.16406 2.89453 2 4.05859 2 5.49453V11.3243C2 11.6557 2.26863 11.9243 2.6 11.9243C2.93137 11.9243 3.2 11.6557 3.2 11.3243V5.49453C3.2 4.72133 3.8268 4.09453 4.6 4.09453H10.0177C10.349 4.09453 10.6177 3.8259 10.6177 3.49453C10.6177 3.16316 10.349 2.89453 10.0177 2.89453H4.6ZM7.07266 5.36719C5.63672 5.36719 4.47266 6.53125 4.47266 7.96719V15.5058C4.47266 16.9417 5.63672 18.1058 7.07266 18.1058H14.6113C16.0472 18.1058 17.2113 16.9417 17.2113 15.5058V7.96719C17.2113 6.53125 16.0472 5.36719 14.6113 5.36719H7.07266ZM5.67266 7.96719C5.67266 7.19399 6.29946 6.56719 7.07266 6.56719H14.6113C15.3845 6.56719 16.0113 7.19399 16.0113 7.96719V15.5058C16.0113 16.279 15.3845 16.9058 14.6113 16.9058H7.07266C6.29946 16.9058 5.67266 16.279 5.67266 15.5058V7.96719Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    Ca = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 32 32"
    };
const Sa = {
        render: function(e, a) {
            return _(), l("svg", Ca, a[0] || (a[0] = [h("path", {
                d: "M13 24l-9-9l1.414-1.414L13 21.171L26.586 7.586L28 9L13 24z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    xa = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const ba = {
        render: function(e, a) {
            return _(), l("svg", xa, a[0] || (a[0] = [h("path", {
                "fill-rule": "evenodd",
                "clip-rule": "evenodd",
                d: "M12.2017 6.10039H11.0999C10.5476 6.10039 10.0999 6.54811 10.0999 7.10039V12.1158C10.0999 12.5662 9.96459 12.9849 9.7324 13.3336H12.1344C12.4422 11.8491 13.7573 10.7334 15.3331 10.7334C17.1372 10.7334 18.5997 12.1959 18.5997 14.0001C18.5997 15.8042 17.1372 17.2667 15.3331 17.2667C13.7106 17.2667 12.3644 16.0839 12.1098 14.5336H7.88988C7.6352 16.0839 6.28901 17.2667 4.66657 17.2667C2.86244 17.2667 1.3999 15.8042 1.3999 14.0001C1.3999 12.1959 2.86244 10.7334 4.66657 10.7334C6.16426 10.7334 7.42655 11.7413 7.81214 13.1158H7.8999C8.45219 13.1158 8.8999 12.6681 8.8999 12.1158V7.10039C8.8999 5.88536 9.88488 4.90039 11.0999 4.90039H12.0771C12.2128 3.22099 13.6187 1.90039 15.3331 1.90039C17.1372 1.90039 18.5997 3.36293 18.5997 5.16706C18.5997 6.97119 17.1372 8.43372 15.3331 8.43372C13.8533 8.43372 12.6033 7.44975 12.2017 6.10039ZM13.2664 5.16706C13.2664 4.02567 14.1917 3.10039 15.3331 3.10039C16.4745 3.10039 17.3997 4.02567 17.3997 5.16706C17.3997 6.30845 16.4745 7.23372 15.3331 7.23372C14.1917 7.23372 13.2664 6.30845 13.2664 5.16706ZM4.66657 11.9334C3.52518 11.9334 2.5999 12.8587 2.5999 14.0001C2.5999 15.1415 3.52518 16.0667 4.66657 16.0667C5.80796 16.0667 6.73324 15.1415 6.73324 14.0001C6.73324 12.8587 5.80796 11.9334 4.66657 11.9334ZM15.3331 11.9334C14.1917 11.9334 13.2664 12.8587 13.2664 14.0001C13.2664 15.1415 14.1917 16.0667 15.3331 16.0667C16.4745 16.0667 17.3997 15.1415 17.3997 14.0001C17.3997 12.8587 16.4745 11.9334 15.3331 11.9334Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    Aa = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 32 32"
    };
const Ma = {
        render: function(e, a) {
            return _(), l("svg", Aa, a[0] || (a[0] = [h("path", {
                d: "M16 2l-4.55 9.22l-10.17 1.47l7.36 7.18L6.9 30l9.1-4.78L25.1 30l-1.74-10.13l7.36-7.17l-10.17-1.48z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    Ia = {
        class: "create_result"
    },
    La = {
        class: "content"
    },
    Ta = {
        class: "buttons"
    },
    Ra = {
        key: 0,
        class: "cross-check expandable"
    },
    Pa = {
        class: "icon"
    },
    Ea = {
        class: "text"
    },
    Ba = {
        key: 0,
        ref: "cross_check_elem",
        class: "cross-check"
    },
    Ha = {
        class: "cross-check-inner"
    },
    $a = {
        class: "header"
    },
    ja = {
        class: "left"
    },
    Ua = {
        class: "icon"
    },
    Da = {
        class: "text"
    },
    Na = {
        class: "right"
    },
    Fa = {
        class: "content"
    },
    Va = {
        key: 0,
        class: "cross_check_statements"
    },
    Oa = {
        class: "cross_check_statement"
    },
    Wa = ["onUpdate:modelValue"],
    za = {
        key: 1,
        class: "cross_check_parsing_statement"
    },
    qa = {
        class: "anim"
    },
    Za = {
        class: "text"
    },
    Ga = {
        key: 0,
        class: "cross_check_error_message"
    },
    Ja = ["placeholder"],
    Qa = {
        class: "button"
    },
    Ya = {
        class: "text"
    };
const Ka = s({
        name: "CrossCheck",
        components: {
            RightArrowIcon: Ke,
            CloseIcon: pe,
            ChecksCircleIcon: ue,
            SpinIcon: he,
            PublicIcon: ge,
            PrivateIcon: me,
            TriangleIconSvg: _e,
            AutoPilotLogoIcon: we,
            LoadingAnimation: J,
            NModal: fe,
            NPopover: ce
        },
        props: {
            article: {
                type: String,
                default: ""
            }
        },
        setup(e, {
            emit: a
        }) {
            const t = o(e.article),
                n = o(!1);
            A((() => e.article), (e => {
                t.value = e
            }));
            const s = o(!1);
            A(s, (async e => {
                if (e && g.value)
                    if (d.value || c.value.statements.length > 0) L.log("Already parsing or statements exist, return");
                    else {
                        d.value = !0;
                        try {
                            c.value = {
                                statements: []
                            };
                            const e = c.value;
                            await ve("/api/project/parsing_statements", {
                                article: t.value
                            }, (a => {
                                "message_field_delta" !== a.type && L.log("message event", a.type, a), "message_field" === a.type && ke(e, a.field_name, (() => a.field_value)), "message_field_delta" === a.type && ke(e, a.field_name, (e => (e || "") + a.delta)), "message_field_append_item" === a.type && ke(e, a.field_name, (e => [...e || [], a.field_value]))
                            }))
                        } finally {
                            d.value = !1
                        }
                    }
            }));
            const l = o(0),
                r = o(!1),
                i = o(!0),
                c = o({
                    statements: []
                }),
                d = o(!1),
                p = o(""),
                u = o(!1),
                h = o(null),
                v = o([]),
                g = M("currentUser"),
                _ = o("");
            return {
                currentUser: g,
                gotoLogin: () => {
                    location.href = "/login"
                },
                cross_check_begining_cross_check_results: v,
                jump_to_new_tasks: () => {
                    v.value.length > 0 ? L.windowopen(`/agents?id=${v.value[0].id}`) : L.windowopen("/agents")
                },
                create_task_result_show: n,
                cross_check_error_message: _,
                cross_check_elem: h,
                cross_check_inner_height: l,
                beginCrossCheck: async () => {
                    if (!u.value && g.value) {
                        u.value = !0;
                        try {
                            let e = c.value.statements.filter((e => e.checked)).map((e => e.statement));
                            if (0 == e.length && !p.value) return;
                            const a = await fetch("/api/project/begin_cross_check", {
                                    method: "POST",
                                    headers: {
                                        "Content-Type": "application/json"
                                    },
                                    body: JSON.stringify({
                                        statements: e,
                                        user_input: p.value
                                    })
                                }),
                                t = await a.json();
                            t && t.data && t.data.projects && (v.value = t.data.projects, n.value = !0, c.value.statements.forEach((e => {
                                e.checked = !1
                            })), p.value = "", s.value = !1)
                        } catch (e) {} finally {
                            u.value = !1
                        }
                    }
                },
                cross_check_begining_cross_check: u,
                cross_check_statements: c,
                cross_check_parsing_statement: d,
                cross_check_user_input: p,
                cross_check_expand: s,
                cross_check_public_expand: r,
                cross_check_public: i
            }
        }
    }, [
        ["render", function(e, a, t, n, s, o) {
            const g = v("NModal"),
                k = Q,
                f = v("AutoPilotLogoIcon"),
                y = (v("RightArrowIcon"), v("ChecksCircleIcon")),
                C = (v("PublicIcon"), v("PrivateIcon"), v("TriangleIconSvg"), v("NPopover"), v("CloseIcon")),
                S = J,
                x = v("SpinIcon");
            return _(), l("div", {
                class: "cross-check-wrapper",
                style: c({
                    "--cross-check-inner-height": n.cross_check_inner_height + "px"
                })
            }, [d(k, null, {
                default: b((() => [d(g, {
                    show: n.create_task_result_show,
                    "onUpdate:show": a[2] || (a[2] = e => n.create_task_result_show = e)
                }, {
                    default: b((() => {
                        var t, s, o;
                        return [h("div", Ia, [h("div", La, [a[10] || (a[10] = h("p", {
                            style: {
                                "font-size": "30px"
                            }
                        }, "🎉 🎉 🎉", -1)), h("p", null, [1 == (null == (t = n.cross_check_begining_cross_check_results) ? void 0 : t.length) ? (_(), l(p, {
                            key: 0
                        }, [T(w(e.$t("components.search_result.cross_check.check_task_status1")), 1)], 64)) : (null == (s = n.cross_check_begining_cross_check_results) ? void 0 : s.length) > 1 ? (_(), l(p, {
                            key: 1
                        }, [T(w(e.$t("components.search_result.cross_check.check_task_status2", [null == (o = n.cross_check_begining_cross_check_results) ? void 0 : o.length])), 1)], 64)) : (_(), l(p, {
                            key: 2
                        }, [T(w(e.$t("components.search_result.cross_check.check_task_status")), 1)], 64))])]), h("div", Ta, [h("div", {
                            class: "button default",
                            onClick: a[0] || (a[0] = () => {
                                n.create_task_result_show = !1
                            })
                        }, w(e.$t("pages.autopilotagent.ok")), 1), h("div", {
                            class: "button",
                            onClick: a[1] || (a[1] = async () => {
                                n.create_task_result_show = !1, n.jump_to_new_tasks()
                            })
                        }, w(e.$t("pages.autopilotagent.check_task_status")), 1)])])]
                    })),
                    _: 1
                }, 8, ["show"])])),
                _: 1
            }), d(I, {
                name: "expand2"
            }, {
                default: b((() => [0 == n.cross_check_expand ? (_(), l("div", Ra, [h("div", {
                    class: "expand-wrapper",
                    onClick: a[3] || (a[3] = () => {
                        n.currentUser ? n.cross_check_expand = !0 : n.gotoLogin()
                    })
                }, [h("div", Pa, [d(f)]), h("div", Ea, w(e.$t("components.search_result.cross_check.cross-check")), 1), i("", !0)])])) : i("", !0)])),
                _: 1
            }), d(I, {
                name: "expand1"
            }, {
                default: b((() => [n.cross_check_expand ? (_(), l("div", Ba, [h("div", Ha, [h("div", $a, [h("div", ja, [h("div", Ua, [d(y)]), h("div", Da, w(e.$t("components.search_result.cross_check.cross-check")), 1), i("", !0)]), h("div", Na, [h("div", {
                    class: "icon",
                    onClick: a[7] || (a[7] = () => {
                        n.cross_check_inner_height = n.cross_check_elem.scrollHeight, n.cross_check_expand = !1
                    })
                }, [d(C)])])]), h("div", Fa, [n.cross_check_statements && n.cross_check_statements.statements && n.cross_check_statements.statements.length > 0 ? (_(), l("div", Va, [(_(!0), l(p, null, u(n.cross_check_statements.statements, ((e, a) => (_(), l("div", Oa, [h("label", null, [r(h("input", {
                    type: "checkbox",
                    "onUpdate:modelValue": a => e.checked = a
                }, null, 8, Wa), [
                    [P, e.checked]
                ]), T(" " + w(e.statement), 1)])])))), 256))])) : i("", !0), n.cross_check_parsing_statement ? (_(), l("div", za, [h("div", qa, [d(S)]), h("div", Za, w(e.$t("components.search_result.cross_check.parsing-statements")), 1)])) : i("", !0), n.cross_check_parsing_statement ? i("", !0) : (_(), l("div", {
                    key: 2,
                    class: m(["cross_check_user_input", {
                        error: n.cross_check_error_message
                    }])
                }, [n.cross_check_error_message ? (_(), l("div", Ga, w(n.cross_check_error_message), 1)) : i("", !0), r(h("textarea", {
                    "onUpdate:modelValue": a[8] || (a[8] = e => n.cross_check_user_input = e),
                    placeholder: e.$t("components.search_result.cross_check.other-statement-you-d-like-cross-checked")
                }, null, 8, Ja), [
                    [E, n.cross_check_user_input]
                ])], 2)), n.cross_check_parsing_statement ? i("", !0) : (_(), l("div", {
                    key: 3,
                    class: m(["cross_check_begin_cross_check", {
                        disabled: !n.cross_check_user_input && !n.cross_check_statements.statements.some((e => e.checked))
                    }]),
                    onClick: a[9] || (a[9] = () => {
                        n.cross_check_begining_cross_check || !n.cross_check_user_input && !n.cross_check_statements.statements.some((e => e.checked)) || n.beginCrossCheck()
                    })
                }, [h("div", Qa, [h("div", Ya, [h("div", {
                    class: m(["icon", {
                        spin: n.cross_check_begining_cross_check
                    }])
                }, [d(x)], 2), T(" " + w(e.$t("components.search_result.cross_check.begin-cross-check")), 1)])])], 2))])])], 512)) : i("", !0)])),
                _: 1
            })], 4)
        }],
        ["__scopeId", "data-v-3c11b75f"]
    ]),
    Xa = {
        key: 0,
        class: "message"
    },
    et = {
        class: "action-text"
    },
    at = {
        key: 1,
        class: "message"
    },
    tt = {
        class: "message-text"
    },
    nt = {
        class: "favicon-container"
    },
    st = {
        key: 0,
        class: "favicon-list"
    },
    ot = ["src", "alt"];
const lt = s({
        name: "SearchStatusTopBar",
        components: {
            LoadingAnimation: J,
            CheckIcon: oe
        },
        emits: ["click"],
        setup(e, {
            emit: a
        }) {
            const t = o(null);
            t.value = L.isDarkMode();
            return {
                isDarkMode: t,
                handleClick: () => {
                    a("click", {
                        searchResults: e.data.search_results,
                        keywords: e.data.keywords,
                        jumpToUrl: ""
                    })
                }
            }
        },
        props: {
            data: {
                type: Object,
                required: !0,
                validator: e => e.status && void 0 !== e.action,
                default: () => ({
                    status: "loading",
                    action: "",
                    message: null,
                    favicons: null
                })
            }
        },
        methods: {
            getStatusText: e => "loading" === e ? "Loading" : "Finished"
        },
        computed: {
            limitedFavicons() {
                var e;
                return (null == (e = this.data.favicons) ? void 0 : e.slice(0, 5)) || []
            }
        }
    }, [
        ["render", function(e, a, t, n, s, o) {
            const r = J,
                c = v("CheckIcon");
            return _(), l("div", {
                class: "status_top_bar",
                onClick: a[0] || (a[0] = (...e) => n.handleClick && n.handleClick(...e))
            }, [h("div", {
                class: m(["status", t.data.status])
            }, ["loading" === t.data.status ? (_(), x(r, {
                key: 0
            })) : "finished" === t.data.status ? (_(), x(c, {
                key: 1,
                class: "check-icon"
            })) : i("", !0)], 2), t.data.action ? (_(), l("div", Xa, [h("span", et, w(t.data.action), 1)])) : i("", !0), t.data.message ? (_(), l("div", at, [h("span", tt, w(t.data.message), 1)])) : i("", !0), h("div", nt, [t.data.favicons && t.data.favicons.length ? (_(), l("div", st, [(_(!0), l(p, null, u(o.limitedFavicons, ((e, a) => (_(), l("img", {
                key: a,
                src: e,
                alt: `Source favicon ${a+1}`,
                class: "favicon-icon"
            }, null, 8, ot)))), 128))])) : i("", !0)])])
        }],
        ["__scopeId", "data-v-f301fd12"]
    ]),
    rt = {
        class: "carousel-container"
    },
    it = {
        class: "carousel-wrapper"
    },
    ct = ["href"],
    dt = {
        class: "icon-wrapper"
    },
    pt = ["src", "alt"],
    ut = {
        class: "card-info"
    },
    ht = {
        class: "card-title"
    },
    vt = {
        class: "card-link"
    },
    gt = {
        class: "sources-overview-content"
    },
    _t = {
        class: "sources-count"
    },
    mt = {
        class: "sources-icons"
    },
    wt = ["src", "alt"],
    kt = s({
        __name: "search_source_top_bar",
        props: {
            sourceList: {
                type: Array,
                default: () => []
            }
        },
        emits: ["showSideBar"],
        setup(e, {
            emit: a
        }) {
            const t = o(null),
                n = e,
                s = o(!1),
                r = o(!1),
                v = o(!1);
            o(null).value = L.isDarkMode();
            const g = () => {
                const e = t.value;
                if (!e) return;
                const a = e.scrollWidth > e.clientWidth,
                    o = e.scrollLeft + e.clientWidth >= e.scrollWidth - 10,
                    l = e.scrollLeft <= 10;
                v.value = window.innerWidth < 768, s.value = !v.value && a && !o && n.sourceList.length > 2, r.value = !v.value && a && !l && n.sourceList.length > 2
            };
            A((() => n.sourceList), (async () => {
                await j(), g()
            }), {
                immediate: !0
            }), B((() => {
                const e = t.value;
                e && (e.addEventListener("scroll", g), window.addEventListener("resize", g), g())
            })), H((() => {
                const e = t.value;
                e && (e.removeEventListener("scroll", g), window.removeEventListener("resize", g))
            }));
            const m = $((() => n.sourceList.map((e => ({
                    host: e.host,
                    url: e.url,
                    title: e.title,
                    favicon: e.favicon
                }))))),
                k = $((() => ({
                    count: `${n.sourceList.length} sources`,
                    text: "sources"
                }))),
                f = () => {
                    t.value.scrollBy({
                        left: 300,
                        behavior: "smooth"
                    })
                },
                C = () => {
                    t.value.scrollBy({
                        left: -300,
                        behavior: "smooth"
                    })
                };
            return (a, n) => {
                var o;
                return _(), l("div", rt, [h("div", it, [r.value ? (_(), l("button", {
                    key: 0,
                    class: "nav-button prev",
                    onClick: C
                }, [d(y(Se))])) : i("", !0), h("div", {
                    class: "carousel",
                    ref_key: "carouselRef",
                    ref: t
                }, [0 === (null == (o = e.sourceList) ? void 0 : o.length) ? (_(), l(p, {
                    key: 0
                }, u(3, (e => h("div", {
                    key: `skeleton-${e}`,
                    class: "card skeleton"
                }, n[1] || (n[1] = [h("div", {
                    class: "skeleton-card-content"
                }, null, -1)])))), 64)) : i("", !0), (_(!0), l(p, null, u(m.value.slice(0, 3), ((e, a) => (_(), l("div", {
                    key: a,
                    class: "card"
                }, [h("a", {
                    href: e.url,
                    class: "card-content",
                    target: "_blank",
                    rel: "noopener noreferrer"
                }, [h("div", dt, [h("img", {
                    src: e.favicon,
                    alt: e.host,
                    class: "source-icon"
                }, null, 8, pt)]), h("div", ut, [h("h3", ht, w(e.title), 1), h("span", vt, w(e.url), 1)])], 8, ct)])))), 128)), e.sourceList.length > 0 ? (_(), l("div", {
                    key: 1,
                    class: "sources-overview",
                    onClick: n[0] || (n[0] = e => a.$emit("showSideBar"))
                }, [h("div", gt, [h("div", _t, w(k.value.count), 1), h("div", mt, [(_(!0), l(p, null, u(e.sourceList.slice(0, 8), ((e, a) => (_(), l("img", {
                    key: a,
                    src: e.favicon,
                    alt: e.host,
                    class: "overview-icon",
                    style: c({
                        left: 12 * a + "px"
                    })
                }, null, 12, wt)))), 128))])])])) : i("", !0)], 512), s.value ? (_(), l("button", {
                    key: 1,
                    class: "nav-button next",
                    onClick: f
                }, [d(y(Se), {
                    style: {
                        transform: "rotate(180deg)"
                    }
                })])) : i("", !0)])])
            }
        }
    }, [
        ["__scopeId", "data-v-9e559b37"]
    ]),
    ft = {
        class: "recommendation-header"
    },
    yt = {
        class: "carousel-container"
    },
    Ct = ["disabled"],
    St = ["onClick"],
    xt = {
        class: "poster-container"
    },
    bt = {
        class: "item-index"
    },
    At = ["src", "alt", "onError"],
    Mt = {
        class: "item-info"
    },
    It = {
        key: 0,
        class: "item-title"
    },
    Lt = {
        key: 1,
        class: "item-title"
    },
    Tt = ["disabled"];
const Rt = s({
        name: "SparkRecommendationItems",
        props: {
            items: {
                type: Array,
                default: () => []
            },
            title: {
                type: String,
                default: "Recommendations"
            },
            type: {
                type: String,
                default: "product"
            }
        },
        emits: ["item-click"],
        setup(e, {
            emit: a
        }) {
            const t = o(null),
                n = o(!0),
                s = o(!1),
                l = o(!1),
                r = o({}),
                i = $((() => l.value ? 2 : 5)),
                c = e => {
                    l.value ? window.location.href = e : window.open(e, "_blank")
                },
                d = () => {
                    const e = t.value;
                    e && (n.value = e.scrollLeft <= 10, s.value = e.scrollLeft + e.clientWidth >= e.scrollWidth - 10)
                },
                p = () => {
                    l.value = window.innerWidth < 768
                },
                u = () => {
                    if ("undefined" == typeof window) return;
                    const e = document.querySelector(".general-chat-wrapper"),
                        a = document.querySelector(".conversation-wrapper");
                    if (!e || !a) return;
                    const t = e.offsetWidth,
                        n = a.offsetWidth;
                    if (window.innerWidth < 768) r.value = {
                        width: "100%",
                        left: "0"
                    };
                    else {
                        const e = t,
                            a = Math.min(e, 1.5 * n) / n * 100,
                            s = (a - 100) / 2 * -1;
                        r.value = {
                            width: `${a}%`,
                            left: `${s}%`
                        }
                    }
                };
            return B((() => {
                p(), d(), u(), window.addEventListener("resize", p), window.addEventListener("resize", d), window.addEventListener("resize", u)
            })), H((() => {
                window.removeEventListener("resize", p), window.removeEventListener("resize", d), window.removeEventListener("resize", u)
            })), {
                carouselRef: t,
                isAtStart: n,
                isAtEnd: s,
                isMobile: l,
                visibleItems: i,
                scrollRight: () => {
                    const e = t.value;
                    e && e.scrollBy({
                        left: .8 * e.clientWidth,
                        behavior: "smooth"
                    })
                },
                scrollLeft: () => {
                    const e = t.value;
                    e && e.scrollBy({
                        left: .8 * -e.clientWidth,
                        behavior: "smooth"
                    })
                },
                checkScrollPosition: d,
                handleItemClick: async t => {
                    if (L.log("handleItemClick", e.type, t), "product" === e.type) {
                        if (t.post_payload) {
                            L.log("handleItemClick post_payload", "product", t.data.post_payload);
                            const e = await fetch("/api/copilot/create_product_spark", {
                                    method: "POST",
                                    headers: {
                                        "Content-Type": "application/json"
                                    },
                                    body: JSON.stringify({
                                        payload: { ...t.post_payload,
                                            original_width: t.data.original_width,
                                            original_height: t.data.original_height
                                        }
                                    })
                                }),
                                a = await e.json();
                            a && "ok" === a.status && c(a.data.redirect_url)
                        }
                    } else "travel" === e.type && t.link ? (L.log("handleItemClick link", "travel", t.link), c(t.link)) : a("item-click", t)
                },
                recommendationBarStyle: r,
                handleImageError: (e, a) => {
                    if (L.log("handleImageError", a), a.data.orgImageUrl && a.data.orgImageUrl.length > 0) {
                        const t = e.target.src,
                            n = a.data.orgImageUrl;
                        let s = -1;
                        if (a.data.imageUrl && a.data.imageUrl.includes(t)) s = 0;
                        else {
                            for (let e = 0; e < n.length; e++)
                                if (n[e] === t) {
                                    s = e + 1;
                                    break
                                }(-1 === s || s >= n.length) && (s = 0)
                        }
                        if (s < n.length) return void(e.target.src = n[s])
                    }
                    const t = document.createElement("canvas");
                    t.width = 300, t.height = 200;
                    const n = t.getContext("2d"),
                        s = n.createLinearGradient(0, 0, 0, t.height);
                    s.addColorStop(0, "#f5f5f5"), s.addColorStop(1, "#e0e0e0"), n.fillStyle = s, n.fillRect(0, 0, t.width, t.height), n.font = "bold 20px Arial", n.fillStyle = "#888888", n.textAlign = "center", n.fillText("Genspark", t.width / 2, t.height / 2 - 10), n.font = "16px Arial", n.fillStyle = "#888888", n.fillText("Image unavailable", t.width / 2, t.height / 2 + 20), e.target.src = t.toDataURL("image/png")
                }
            }
        }
    }, [
        ["render", function(e, a, t, n, s, o) {
            return _(), l("div", {
                class: "recommendation-bar",
                style: c(n.recommendationBarStyle)
            }, [h("div", ft, ["travel" === t.type ? (_(), l(p, {
                key: 0
            }, [a[3] || (a[3] = h("img", {
                src: "https://cdn1.genspark.ai/user-upload-image/manual/avatar-travel.png"
            }, null, -1)), a[4] || (a[4] = h("span", null, "Genspark Travel", -1))], 64)) : i("", !0), "product" === t.type ? (_(), l(p, {
                key: 1
            }, [a[5] || (a[5] = h("img", {
                src: "https://cdn1.genspark.ai/user-upload-image/manual/avatar-products.png"
            }, null, -1)), a[6] || (a[6] = h("span", null, "Genspark Products", -1))], 64)) : i("", !0)]), h("div", yt, [!n.isMobile && t.items.length > n.visibleItems ? r((_(), l("button", {
                key: 0,
                class: "nav-button prev",
                onClick: a[0] || (a[0] = (...e) => n.scrollLeft && n.scrollLeft(...e)),
                disabled: n.isAtStart
            }, a[7] || (a[7] = [h("svg", {
                width: "24",
                height: "24",
                viewBox: "0 0 24 24",
                fill: "none"
            }, [h("path", {
                d: "M15 18l-6-6 6-6",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round"
            })], -1)]), 8, Ct)), [
                [k, !n.isAtStart]
            ]) : i("", !0), h("div", {
                class: "carousel",
                ref: "carouselRef",
                onScroll: a[1] || (a[1] = (...e) => n.checkScrollPosition && n.checkScrollPosition(...e))
            }, [(_(!0), l(p, null, u(t.items, ((e, s) => (_(), l("div", {
                key: s,
                class: "item-card",
                onClick: a => n.handleItemClick(e)
            }, [h("div", xt, [h("div", bt, w(s + 1), 1), h("img", {
                src: e.data.imageUrl[0],
                alt: e.data.name,
                class: "poster-image",
                onError: a => n.handleImageError(a, e)
            }, null, 40, At), a[8] || (a[8] = h("div", {
                class: "hover-overlay"
            }, [h("span", {
                class: "view-details"
            }, "View Details")], -1))]), h("div", Mt, ["product" === t.type ? (_(), l("h3", It, w(e.data.name), 1)) : i("", !0), "travel" === t.type ? (_(), l("h3", Lt, w(e.data.title), 1)) : i("", !0)])], 8, St)))), 128))], 544), !n.isMobile && t.items.length > n.visibleItems ? r((_(), l("button", {
                key: 1,
                class: "nav-button next",
                onClick: a[2] || (a[2] = (...e) => n.scrollRight && n.scrollRight(...e)),
                disabled: n.isAtEnd
            }, a[9] || (a[9] = [h("svg", {
                width: "24",
                height: "24",
                viewBox: "0 0 24 24",
                fill: "none"
            }, [h("path", {
                d: "M9 18l6-6-6-6",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round"
            })], -1)]), 8, Tt)), [
                [k, !n.isAtEnd]
            ]) : i("", !0)])], 4)
        }],
        ["__scopeId", "data-v-3323d2dc"]
    ]),
    Pt = {
        class: "sources-container"
    },
    Et = {
        class: "source-header"
    },
    Bt = ["src"],
    Ht = ["href"],
    $t = {
        class: "source-title"
    },
    jt = {
        class: "source-description"
    },
    Ut = s({
        __name: "search_result_sources",
        props: {
            sources: {
                type: Array,
                required: !0
            }
        },
        setup: e => (a, t) => (_(), l("div", Pt, [(_(!0), l(p, null, u(e.sources, (e => (_(), l("div", {
            key: e.url,
            class: "source-card"
        }, [h("div", Et, [h("img", {
            src: e.icon,
            alt: "source icon",
            class: "source-icon"
        }, null, 8, Bt), h("a", {
            href: e.url,
            class: "source-url",
            target: "_blank",
            rel: "noopener"
        }, w(e.url), 9, Ht)]), h("h3", $t, w(e.title), 1), h("p", jt, w(e.description), 1)])))), 128))]))
    }, [
        ["__scopeId", "data-v-bf5cbdfa"]
    ]),
    Dt = {
        class: "preview-container"
    },
    Nt = {
        class: "content-wrapper"
    },
    Ft = {
        class: "share-header"
    },
    Vt = {
        class: "ai-answer-tag-container"
    },
    Ot = {
        class: "ai-answer-tag"
    },
    Wt = {
        key: 0,
        class: "mind-map-wrapper"
    },
    zt = {
        class: "mind-map-title"
    },
    qt = {
        class: "deep_dive_answer"
    },
    Zt = {
        class: "logo-container"
    },
    Gt = {
        class: "slogan-text"
    },
    Jt = s({
        __name: "SharePreview",
        props: {
            title: {
                type: String,
                required: !0
            },
            markdownContent: {
                type: String,
                required: !0
            },
            mindMapContent: {
                type: String,
                required: !1
            }
        },
        setup(e, {
            expose: a
        }) {
            const t = e,
                n = o(null);
            n.value = L.isDarkMode();
            const s = o(null),
                r = $((() => t.markdownContent.replace(/\[\d+\](?:\[[^\]]*\]|\([^)]*\))?/g, "").replace(/\[([^\]]+)\]\([^)]+\)/g, "$1").replace(/\[([^\]]+)\]\[[^\]]*\]/g, "$1").replace(/^\[[^\]]+\]:\s*http[^\n]+$/gm, "")));
            return a({
                contentWrapper: s
            }), (a, t) => (_(), l("div", {
                class: "share-preview",
                ref_key: "contentWrapper",
                ref: s
            }, [h("div", Dt, [h("div", Nt, [h("div", Ft, [h("h2", null, w(e.title), 1)]), t[0] || (t[0] = h("div", {
                class: "divider-container"
            }, [h("div", {
                class: "divider"
            })], -1)), h("div", Vt, [h("div", Ot, w(a.$t("components.search_result.direct-answer-by-ai")), 1)]), e.mindMapContent ? (_(), l("div", Wt, [h("div", zt, w(a.$t("components.deep_dive_vertical.mindmap")), 1), d(ae, {
                "markdown-content": e.mindMapContent,
                showControls: !1,
                initialExpandLevel: 3,
                recenterTimeout: 500,
                enableInteraction: !1,
                autoHeight: !0
            }, null, 8, ["markdown-content"])])) : i("", !0), h("div", qt, [d(y(Y), {
                class: "markdown-viewer",
                source: r.value
            }, null, 8, ["source"])])]), h("div", Zt, [(_(), x(U(n.value ? y(Le) : y(Ie)), {
                class: "logo-icon"
            })), h("div", Gt, w(a.$t("pages.index.title_sub_title2")), 1)])])], 512))
        }
    }, [
        ["__scopeId", "data-v-7410584f"]
    ]),
    Qt = {
        width: "32",
        height: "32",
        viewBox: "0 0 32 32",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Yt = {
        render: function(e, a) {
            return _(), l("svg", Qt, a[0] || (a[0] = [f('<g clip-path="url(#clip0_5858_119)"><circle cx="16" cy="16.0015" r="16" fill="url(#paint0_radial_5858_119)"></circle><path d="M9 15.5657C9 12.2658 9 10.6159 10.0251 9.5908C11.0503 8.56567 12.7002 8.56567 16 8.56567C19.2998 8.56567 20.9497 8.56567 21.9749 9.5908C23 10.6159 23 12.2658 23 15.5657C23 18.8655 23 20.5154 21.9749 21.5406C20.9497 22.5657 19.2998 22.5657 16 22.5657C12.7002 22.5657 11.0503 22.5657 10.0251 21.5406C9 20.5154 9 18.8655 9 15.5657Z" stroke="white" stroke-width="1.5"></path><path d="M19.3162 13.3553C19.9266 13.3553 20.4215 12.8605 20.4215 12.25C20.4215 11.6396 19.9266 11.1448 19.3162 11.1448C18.7058 11.1448 18.2109 11.6396 18.2109 12.25C18.2109 12.8605 18.7058 13.3553 19.3162 13.3553Z" stroke="white" stroke-width="1.5"></path><path d="M18.9476 22.9343C18.4911 21.2947 17.4257 19.8263 15.9092 18.7595C14.2743 17.609 12.2214 17.0002 10.1171 17.0415C9.86718 17.0409 9.61753 17.0489 9.36865 17.0654" stroke="white" stroke-width="1.5" stroke-linejoin="round"></path><path d="M16.7368 19.9868C17.9906 19.0093 19.3412 18.5078 20.7056 18.5132C21.4793 18.5124 22.2493 18.6764 23 19.0007" stroke="white" stroke-width="1.5" stroke-linejoin="round"></path></g><defs><radialGradient id="paint0_radial_5858_119" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(16 16.0015) rotate(90) scale(20)"><stop></stop><stop offset="1"></stop></radialGradient><clipPath id="clip0_5858_119"><rect width="32" height="32" fill="white"></rect></clipPath></defs>', 2)]))
        }
    },
    Kt = {
        class: "modal-container"
    },
    Xt = {
        class: "close-button-wrapper"
    },
    en = ["src"],
    an = {
        class: "mobile-title-bar"
    },
    tn = {
        class: "title-bar-title"
    },
    nn = {
        class: "image-right"
    },
    sn = {
        key: 0,
        class: "share-options-wrapper"
    },
    on = {
        class: "share-options"
    },
    ln = {
        class: "copy-button"
    },
    rn = {
        key: 1,
        class: "spinner-wrapper"
    },
    cn = {
        key: 2,
        class: "download-image"
    },
    dn = {
        name: "DeepDiveSearchVerticalResult",
        components: {
            CrossCheck: Ka,
            RightArrowIcon: Ke,
            FinishedIcon: oe,
            ExpandIcon: Qe,
            ExpandMoreIcon: ca,
            ExpandLessIcon: pa,
            ShareIcon: ka,
            FullIcon: ha,
            ThumbsUpIcon: ga,
            ThumbsUpFilledIcon: ma,
            CopyIcon: ya,
            CheckIcon: Sa,
            CitationPopover: q,
            NSkeleton: ee,
            Markdown: Y,
            ShieldIcon: X,
            ShieldTriangle: Ze,
            SearchResultGallery: Fe,
            LoadingAnimation: J,
            Echarts: re,
            EnterIcon: ie,
            YoutubeWidget: n,
            StarIcon: de,
            StarFilledIcon: Ma,
            PartnerData: ra,
            PartnerDataSvg: ea,
            StockPriceChart: ye,
            SankeyReport: Ce,
            SearchStatusTopBar: lt,
            SearchSourceTopBar: kt,
            SearchResultMindMap: ae,
            SparkRecommendationItems: Rt,
            SearchResultSources: Ut,
            NModal: fe,
            SearchResultShareModal: s({
                __name: "search_result_share_modal",
                props: {
                    title: {
                        type: String,
                        required: !0
                    },
                    shareUrl: {
                        type: String,
                        required: !1
                    },
                    markdownContent: {
                        type: String,
                        required: !0
                    },
                    mindMapContent: {
                        type: String,
                        required: !1
                    }
                },
                emits: ["close"],
                setup(e, {
                    expose: a,
                    emit: t
                }) {
                    const n = t,
                        s = e;
                    o(null), o(null);
                    const r = o(!1),
                        p = o(!1),
                        u = o(s.title),
                        v = o(s.mindMapContent),
                        {
                            t: g
                        } = S();
                    o(s.shareUrl);
                    const m = o(null),
                        k = o(null),
                        f = o(!1);
                    k.value = L.isDarkMode(), f.value = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
                    const C = () => {
                            n("close")
                        },
                        {
                            message: b
                        } = K(["message", "dialog", "notification", "loadingBar"], {});
                    $((() => s.markdownContent.replace(/\[\d+\](?:\[[^\]]*\]|\([^)]*\))?/g, "").replace(/\[([^\]]+)\]\([^)]+\)/g, "$1").replace(/\[([^\]]+)\]\[[^\]]*\]/g, "$1").replace(/^\[[^\]]+\]:\s*http[^\n]+$/gm, "")));
                    const A = async () => {
                            var e;
                            const a = null == (e = m.value) ? void 0 : e.contentWrapper;
                            if (a) try {
                                a.classList.add("generating-image");
                                const e = await xe(a, {
                                    useCORS: !0,
                                    scale: 2,
                                    logging: !1,
                                    onclone: e => {
                                        const a = e.querySelector(".content-wrapper");
                                        a && a.classList.add("generating-image")
                                    }
                                });
                                return a.classList.remove("generating-image"), e.toDataURL("image/png")
                            } catch (t) {
                                return null == a || a.classList.remove("generating-image"), null
                            }
                        },
                        M = async () => {
                            r.value = !0;
                            try {
                                const e = await A();
                                if (!e) return;
                                const a = document.createElement("a");
                                a.href = e, a.download = `${u.value||"share"}.png`, document.body.appendChild(a), a.click(), document.body.removeChild(a), r.value = !1
                            } catch (e) {
                                r.value = !1
                            }
                        },
                        I = async () => {
                            p.value = !0;
                            try {
                                const e = await A();
                                if (!e) return;
                                const a = await fetch(e),
                                    t = await a.blob();
                                await navigator.clipboard.write([new ClipboardItem({
                                    "image/png": t
                                })]), b.success(g("components.deep_dive_vertical.image-copied-to-clipboard")), p.value = !1
                            } catch (e) {
                                b.error(g("components.deep_dive_vertical.failed-to-copy-image")), p.value = !1
                            }
                        },
                        P = o(null),
                        E = o(1),
                        j = () => {
                            P.value && setTimeout((() => {
                                const e = P.value.offsetWidth;
                                E.value = e < 500 ? .9 * e / 400 : 1
                            }), 500)
                        };
                    return B((() => {
                        j(), window.addEventListener("resize", j)
                    })), H((() => {
                        window.removeEventListener("resize", j)
                    })), a({
                        generateImage: A
                    }), (a, t) => (_(), l("div", Kt, [h("div", {
                        class: "overlay",
                        onClick: C
                    }, [h("div", Xt, [h("img", {
                        src: y(Ae),
                        onClick: R(C, ["stop"]),
                        class: "icon"
                    }, null, 8, en)])]), h("div", {
                        class: "image-display",
                        onTouchmove: t[0] || (t[0] = R((() => {}), ["stop"]))
                    }, [h("div", an, [h("div", {
                        class: "back-button",
                        onClick: C
                    }, [d(y(be), {
                        class: "icon"
                    })]), h("div", tn, w(a.$t("components.deep_dive_vertical.share")), 1)]), h("div", {
                        class: "image-left",
                        ref_key: "imageLeft",
                        ref: P
                    }, [d(Jt, {
                        ref_key: "sharePreview",
                        ref: m,
                        title: u.value,
                        "markdown-content": e.markdownContent,
                        "mind-map-content": v.value,
                        style: c({
                            transform: `scale(${E.value})`,
                            transformOrigin: "top center"
                        }),
                        class: "share-preview-container"
                    }, null, 8, ["title", "markdown-content", "mind-map-content", "style"])], 512), h("div", nn, [f.value ? i("", !0) : (_(), l("div", sn, [h("div", on, [f.value ? i("", !0) : (_(), l("div", {
                        key: 0,
                        class: "share-url",
                        onClick: I
                    }, [h("div", ln, [p.value ? (_(), l("div", rn, t[1] || (t[1] = [h("div", {
                        class: "spinner"
                    }, null, -1)]))) : (_(), x(U(y(Yt)), {
                        key: 0
                    })), T(" " + w(a.$t("components.deep_dive_vertical.copy-as-image")), 1)])]))])])), r.value ? (_(), l("div", cn, [t[2] || (t[2] = h("div", {
                        class: "spinner"
                    }, null, -1)), T(" " + w(a.$t("components.deep_dive_vertical.downloading")), 1)])) : (_(), l("div", {
                        key: 1,
                        class: "download-image",
                        onClick: M
                    }, [(_(), x(U(y(Me)))), T(w(a.$t("components.deep_dive_vertical.download-as-image")), 1)]))])], 32)]))
                }
            }, [
                ["__scopeId", "data-v-43894f41"]
            ]),
            MindMapIcon: ba,
            FullScreenIcon: te,
            NDropdown: Re
        },
        props: {
            showResultButtons: {
                type: Boolean,
                default: !0
            },
            config: {
                type: Object,
                default: () => ({})
            },
            externalApiResponse: {
                type: String,
                default: () => ""
            },
            sparkId: {
                type: String,
                required: !1
            },
            externalStreamJsonString: {
                type: String,
                default: () => ""
            },
            externalNotifyRealtimeAnswer: {
                type: String,
                default: () => ""
            },
            externalIsLoading: {
                type: Boolean,
                default: !0
            },
            externalThinking: {
                type: String,
                default: ""
            },
            externalStreamingAnswer: {
                type: [String, Array],
                default: ""
            },
            externalStreamingDetailAnswer: {
                type: [String, Array],
                default: ""
            },
            externalSearchStatusTopBarData: {
                type: Object,
                default: () => ({})
            },
            externalSearchSourceTopBarData: {
                type: Array,
                default: () => []
            },
            externalSearchPlan: {
                type: Array,
                default: () => []
            },
            externalSubAnswer: {
                type: Array,
                default: () => []
            },
            searchQuery: {
                type: String,
                default: ""
            },
            showCharts: {
                type: Boolean,
                default: !0
            },
            showCrossCheck: {
                type: Boolean,
                default: !0
            },
            showFollowupQuestion: {
                type: Boolean,
                default: !0
            },
            oneboxStockSymbol: {
                type: Object,
                default: () => ({})
            },
            oneboxFinancialReportSankey: {
                type: Object,
                default: () => ({})
            },
            oneboxFashionTrend: {
                type: Object,
                default: () => ({})
            },
            showSearchStatusTopBar: {
                type: Boolean,
                default: !0
            },
            showSearchSourceTopBar: {
                type: Boolean,
                default: !0
            },
            showThinking: {
                type: Boolean,
                default: !0
            },
            showTryMoa: {
                type: Boolean,
                default: !1
            },
            externalStreamingMarkmap: {
                type: String,
                default: ""
            },
            externalAnswerDivider: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["triggerAction", "toggleSidebar", "showSidebar", "try-moa", "finish-loading"],
        setup(e, {
            emit: a
        }) {
            const t = D();
            N();
            const {
                config: n,
                externalApiResponse: s,
                sparkId: l,
                externalStreamJsonString: r,
                externalNotifyRealtimeAnswer: i,
                externalThinking: c,
                externalSearchPlan: d,
                externalSubAnswer: p,
                externalSearchStatusTopBarData: u,
                externalSearchSourceTopBarData: h,
                searchQuery: v,
                showCharts: g,
                showCrossCheck: _,
                showFollowupQuestion: m,
                oneboxStockSymbol: w,
                oneboxFinancialReportSankey: k,
                oneboxFashionTrend: f,
                externalStreamingAnswer: y,
                externalStreamingDetailAnswer: C,
                externalStreamingMarkmap: x,
                externalAnswerDivider: b,
                showSearchStatusTopBar: I,
                showSearchSourceTopBar: T,
                showResultButtons: R,
                showThinking: P,
                showTryMoa: E
            } = F(e), H = o(!0), j = o(!0), U = o(!0), O = o(""), z = o(""), q = o(null);
            O.value = s.value;
            const J = o(O.value ? O.value.thumbnail : []),
                Q = o(!1),
                Y = o(!1),
                X = o({}),
                ee = o([]),
                ae = o([]),
                te = o(""),
                oe = o("true"),
                re = o(""),
                ie = o(""),
                ce = o(""),
                de = o(""),
                pe = o(""),
                ue = o(""),
                he = o(""),
                ve = o(""),
                ge = o("");
            o("");
            const _e = o(null);
            o("");
            const me = o(!1),
                we = o(!1),
                ke = e => `https://t3.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${encodeURIComponent(e)}&size=64`,
                fe = [{
                    icon: ke("https://wikipedia.org"),
                    url: "https://wikipedia.org",
                    title: "Wikipedia",
                    description: "Wikipedia is a free online encyclopedia, created and edited by volunteers around the world and hosted by the Wikimedia Foundation."
                }],
                ye = o(""),
                Ce = o([]),
                Se = new Set(["explicit_translation", "calculator", "unit_converter"]),
                xe = o(!1),
                be = $((() => v.value ? v.value : t.query.query)),
                Ae = (e, t) => {
                    e.user_s_input && (e.user_s_input = e.user_s_input.replace(/[#*_~`]+/g, "").trim()), a("triggerAction", e, t)
                },
                Me = $((() => !X.value || !X.value.plan_steps || 0 === X.value.plan_steps.length || X.value.plan_steps.every((e => "finished" === e.status)))),
                Ie = $((() => {
                    X.value && X.value.plan_steps && 0 !== X.value.plan_steps.length && X.value.plan_steps.every((e => "finished" === e.status)) && (L.log("finishedWritingPlan is true"), setTimeout((() => (oe.value = !1, ze(), setTimeout((() => {
                        qe()
                    }), 0), !0)), 500))
                }));

            function Le(e) {
                window.open(e, "_blank")
            }
            const {
                message: Re
            } = K(["message", "dialog", "notification", "loadingBar"], {}), {
                t: Pe
            } = S(), Ee = () => {
                if (O.value) {
                    L.log("apiResponse.value.title:", O.value.title);
                    const e = encodeURIComponent(O.value.title.toLowerCase().replace(/[^\p{L}\p{N}]+/gu, "-").replace(/^-+|-+$/g, ""));
                    return `${window.location.origin}/spark/${e}/${O.value.id}`
                }
                return location.href
            }, Be = o([]);
            A((() => X.value.plan_steps), (() => {
                const e = X.value.plan_steps ? X.value.plan_steps.length : 0;
                Be.value = new Array(e).fill(!1)
            }), {
                immediate: !0
            });
            const He = o(!1),
                $e = $((() => !(!X.value || !X.value.plan_steps) && X.value.plan_steps.some((e => e.engine && ("semantic_scholar" === e.engine.toLowerCase() || "financial_modeling_prep" === e.engine.toLowerCase()))))),
                je = V(ya),
                Ue = o(!1),
                De = o(0),
                Ne = o(!1),
                Fe = o(!1),
                Ve = M("currentUser"),
                Oe = o(null),
                We = o(null),
                ze = () => {
                    L.log("开始计算planContainerHeight"), Oe.value ? (L.log("发生点击时planContainer是:", Oe.value.offsetHeight), We.value = Oe.value.offsetHeight) : (L.log("发生点击时planContainer为空，设置高度为0"), We.value = 0)
                };
            B((() => {
                ze()
            }));
            const qe = () => {
                L.log("开始计算ShowSubAnswer"), "" !== te.value ? (U.value = te.value, L.log("用户有点击，以用户点击行为为准：ShowSubAnswer", U.value)) : (U.value = oe.value, L.log("用户没点击，以系统值为准：ShowSubAnswer", U.value))
            };

            function Ze(e, a) {
                const t = [],
                    n = `"${a}"`,
                    s = `\\"${a}\\"`;
                let o = 0;
                for (;;) {
                    const a = e.indexOf(n, o),
                        r = e.indexOf(s, o);
                    let i = -1,
                        c = 0;
                    if (-1 === a && -1 === r) break; - 1 === a ? (i = r, c = s.length) : -1 === r ? (i = a, c = n.length) : (i = Math.min(a, r), c = i === a ? n.length : s.length);
                    const d = i + c + 1;
                    let p = e.indexOf('"', d);
                    if (-1 !== p) {
                        p++;
                        let a = p;
                        for (; a < e.length && ('"' !== e[a] || "\\" === e[a - 1]);) a++;
                        const n = e.substring(p, a);
                        try {
                            const e = JSON.parse('"' + n + '"');
                            t.push(e)
                        } catch (l) {}
                    }
                    o = i + 1
                }
                return t.length > 1 ? t : t[0] || null
            }
            A(Ie, (() => {
                Ie.value && L.log("finishedWritingPlan change to true")
            })), A(s, (e => {
                var t, n;
                L.log("externalApiResponse changed:", e), e && ("string" == typeof e && (e = JSON.parse(e)), O.value = e, J.value = O.value.thumbnail, O.value.detailAnswer && (L.log("ZZH handleDeepDiveFinishLoading from deep dive", O.value.detailAnswer), a("finish-loading")), (null == (t = O.value.answer_mindmap) ? void 0 : t.markmap) && (Y.value = !1, _e.value = null == (n = O.value.answer_mindmap) ? void 0 : n.markmap), O.value.search_status_top_bar_data && (L.log("ZZH found cached search_status_top_bar_data:", O.value.search_status_top_bar_data), ye.value = O.value.search_status_top_bar_data, L.log("ZZH assigned cached search_status_top_bar_data:", ye.value)), O.value.source && (Ce.value = O.value.source), O.value.media_info && O.value.media_info[0] && O.value.media_info[0].link && (q.value = {
                    link: O.value.media_info[0].link,
                    embedIframe: !0,
                    styleClass: ""
                }), O.value.title && (de.value = O.value.title, L.log("externalApiResponse apiResponse.value.title", O.value.title)), H.value = !1, Se.has(O.value.intent) && (xe.value = !0), O.value.subAnswer && (re.value = O.value.subAnswer), O.value.searchPlan && (X.value = O.value.searchPlan, L.log("searchPlan updated by externalApiResponse:", X.value)))
            }), {
                immediate: !0
            }), A(d, (e => {
                if (L.log("externalSearchPlan changed:", e), e && e.length > 0)
                    for (let a of e) switch (a.type) {
                        case "set_plan":
                            X.value = {
                                plan_steps: a.data.search_keywords.map((e => ({
                                    keyword: e.keyword,
                                    information_to_retrieve: e.information_to_retrieve.replace(/\.$/, "").replace("。", ""),
                                    webpages_name: [],
                                    current_webpage_name: "",
                                    status: "set_plan",
                                    autoToggled: !1,
                                    engine: e.engine
                                })))
                            };
                            break;
                        case "browsing":
                            X.value.plan_steps[a.index].webpages_name.push(a.data.webpages_name), X.value.plan_steps[a.index].current_webpage_name = a.data.webpages_name, X.value.plan_steps[a.index].status = "browsing";
                            break;
                        case "analyzing":
                            X.value.plan_steps[a.index].status = "analyzing";
                            break;
                        case "analyzed":
                            X.value.plan_steps[a.index].status = "analyzed";
                            break;
                        case "finished":
                            X.value.plan_steps[a.index].status = "finished", X.value.plan_steps[a.index].autoToggled = !1;
                            break;
                        case "add_plan":
                            if (L.log("ZZH add_plan:", a), X.value.plan_steps || (X.value = {
                                    plan_steps: []
                                }), a.index < X.value.plan_steps.length) {
                                L.log("ZZH Plan step already exists at index:", a.index);
                                break
                            }
                            const e = {
                                keyword: a.data.keyword || "",
                                information_to_retrieve: (a.data.information_to_retrieve || "").replace(/\.$/, "").replace("。", ""),
                                webpages_name: [],
                                current_webpage_name: "",
                                status: "set_plan",
                                autoToggled: !1,
                                engine: a.data.engine || "",
                                ...a.data
                            };
                            X.value.plan_steps.push(e);
                            break;
                        case "update_plan":
                            L.log("ZZH update_plan:", a), X.value.plan_steps[a.index] = a.data;
                            break;
                        default:
                            L.log("Unhandled type:", a.type)
                    }
            }), {
                immediate: !0
            }), A(p, (e => {
                L.log("externalSubAnswer changed:", e), e && (re.value = e)
            }), {
                immediate: !0
            });
            A(r, (e => {
                if (e) {
                    let a;
                    L.log("externalStreamJsonString changed:", e), ie.value = e, a = Ze(e, "title"), a && (de.value = a), a = Ze(e, "answer"), a && (pe.value = a), a = Ze(e, "subTopic"), a && (ve.value = a), a = Ze(e, "detailAnswer"), a && (ge.value = a), a = Ze(e, "extraction_plan"), a && (ue.value = a), a = Ze(e, "reflection"), a && (he.value = a)
                }
            }), {
                immediate: !0
            }), A(y, (e => {
                e && (pe.value = e)
            })), A(i, (a => {
                if (a) {
                    const t = JSON.parse(a);
                    L.log("ZZH realtime externalNotifyRealtimeAnswer changed using summary answer:", t), t.detail_answer && W.emit("function-call-final-answer", {
                        query: e.searchQuery,
                        detail_answer: t.detail_answer
                    }), t.search_results && (L.log("ZZH realtime externalNotifyRealtimeAnswer changed using serp:", JSON.stringify(t.search_results)), W.emit("function-call-final-answer", {
                        query: e.searchQuery,
                        search_results: "Here is the search results from search engine, please summarize and answer the question:" + JSON.stringify(t.search_results)
                    }))
                }
            })), A(C, ((e, a) => {
                e && (Array.isArray(e) && e.length > 1 && (Q.value = !0), ge.value = e)
            }), {
                immediate: !0,
                deep: !0
            }), A(b, ((e, a) => {
                e && e && (Q.value = !0)
            }), {
                immediate: !0
            }), A(x, ((e, a) => {
                e && (_e.value = e, Q.value = !1, Y.value = !0)
            }), {
                immediate: !0,
                deep: !0
            }), A(ce, (e => {
                e && (ie.value = e)
            }), {
                immediate: !0
            });
            const Je = o(null),
                Qe = o([{
                    plugin: G
                }, {
                    plugin: Z
                }, {
                    plugin: se,
                    options: {
                        engine: ne,
                        delimiters: ["brackets", "dollars"]
                    }
                }]),
                Ye = o(!0),
                Ke = o(null),
                Xe = $((() => Ke.value ? Ke.value.clientWidth : 0)),
                ea = o(!1),
                aa = o(!1);
            A(u, (e => {
                e && (ye.value = e)
            })), A(h, (e => {
                L.log("externalSearchSourceTopBarData newValue", e), e && (Ce.value = e)
            }), {
                immediate: !0
            });
            const ta = e => {
                    const a = (new Te).render(e),
                        t = document.createElement("div");
                    return t.innerHTML = a, t.textContent || t.innerText || ""
                },
                na = $((() => {
                    var e;
                    const a = new URL(window.location.href).searchParams.get("debug"),
                        t = (null == (e = Ve.value) ? void 0 : e.gk_realtime_dogfood) || !1;
                    return a && t
                })),
                sa = $((() => new URL(window.location.href).searchParams.get("srid")));
            return {
                continueAnswerStarted: Q,
                processCitationPlaceholder: e => e.replace(/\[(\d+)\](?!\()/g, ((e, a) => `<a class="digital-citation-link">${a}</a>`)),
                cross_check_switch: Ye,
                markdownPlugins: Qe,
                citationLink: Je,
                markdownDigitalCitationLinks: G,
                linkOpenTarget: Z,
                isLoading: H,
                apiResponse: O,
                embededYoutubeData: q,
                sparkId: l,
                streamingIntroduction: ie,
                streamingSection: ce,
                streamingTitle: de,
                streamingAnswer: pe,
                streamingExtractionPlan: ue,
                streamingReflection: he,
                streamingSubTopic: ve,
                streamingDetailAnswer: ge,
                hoveringLink: me,
                hoveringShield: we,
                toggleHoverLink: e => {
                    me.value = e
                },
                toggleHoverShield: e => {
                    we.value = e
                },
                openVideoInNewWindow: e => {
                    window.open(e, "_blank")
                },
                MoreIcon: le,
                LessIcon: Ge,
                seeMore: j,
                loadedImagelist: J,
                isDirectAnswer: xe,
                externalThinking: c,
                externalSearchStatusTopBarData: u,
                externalSearchSourceTopBarData: h,
                externalStreamingAnswer: y,
                externalStreamingDetailAnswer: C,
                openNewWindow: (e, a) => {
                    if (e && a) {
                        const t = `spark?id=${a}&query=${encodeURIComponent(e)}&copilot_on=true`;
                        window.open(t, "_blank")
                    }
                },
                userQuery: be,
                newSearchQuery: z,
                triggerParentAction: Ae,
                currentUser: Ve,
                login: () => {
                    location.href = "/api/login?redirect_url=" + encodeURIComponent(location.pathname + location.search)
                },
                searchPlan: X,
                toggleStep: e => {
                    ae.value[e] = !ae.value[e], ee.value[e] = !0
                },
                subAnswer: re,
                openWebpage: Le,
                isNotWritingSearchPlan: Me,
                finishedWritingPlan: Ie,
                computeExpanded: e => (ae.value[e] || (ae.value[e] = !1), ee.value[e] ? ae.value[e] : X.value.plan_steps[e].autoToggled),
                userToggled: ae,
                isUserToggled: ee,
                planContainer: Oe,
                planContainerHeight: We,
                showSubAnswer: U,
                userExpanded: te,
                autoExpanded: oe,
                clickShowSubAnswer: () => {
                    L.log("用户点击了clickShowSubAnswer"), ze(), setTimeout((() => {
                        te.value = !te.value, qe()
                    }), 0)
                },
                handleResultOpenClick: function() {
                    if (O.value.id) {
                        Le(Ee())
                    }
                },
                shareSparkSelected: e => {
                    if (!O.value.id) return;
                    const a = Ee();
                    if ("CopyUrl" === e && (navigator.clipboard.writeText(a), Re.success(Pe("components.deep_dive_vertical.url-has-been-copied-to-the-clipboard"))), "Twitter" === e) {
                        const e = O.value.title + " " + a;
                        window.open("https://twitter.com/intent/tweet?text=" + encodeURIComponent(e))
                    }
                    "Facebook" === e && window.open("https://www.facebook.com/sharer/sharer.php?u=" + encodeURIComponent(a))
                },
                dislikeActive: Ne,
                dislikeSpark: async e => {
                    if (!O.value) return;
                    if (!Ve.value || !Ve.value.id) return void(location.href = "/login?redirect_url=" + encodeURIComponent(location.href));
                    Ne.value = !0, Ne.value && (Ue.value = !1);
                    const a = await fetch("/api/spark/dislike", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify({
                            id: O.value.id,
                            dislike: Ne.value,
                            dislike_comment: e.comment,
                            dislike_reason: e.reason,
                            url: location.href
                        })
                    });
                    if (Re.success(Pe("pages.spark.sent")), !a.ok) throw new Error(`HTTP error! status: ${a.status}`);
                    const t = await a.json();
                    if (L.log("Success:", t), 0 !== t.status) throw new Error(`request error message: ${t.message}`)
                },
                clapSpark: async () => {
                    if (!O.value) return;
                    Ue.value = !Ue.value, Ue.value && (Ne.value = !1, Re.success(Pe("components.deep_dive_vertical.thanks-for-the-like")));
                    const e = await fetch("/api/spark/clap", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify({
                            id: O.value.id,
                            clap: Ue.value
                        })
                    });
                    if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                    const a = await e.json();
                    if (L.log("Success:", a), 0 !== a.status) throw new Error(`request error message: ${a.message}`)
                },
                clapActive: Ue,
                clapCount: De,
                copyAnswer: e => {
                    if (!O.value) return Re.error("answer-not-found");
                    const a = "CopyMarkdown" === e ? O.value.detailAnswer : ta(O.value.detailAnswer);
                    navigator.clipboard.writeText(a), Re.success(Pe("components.deep_dive_vertical.answer-copied")), je.value = Sa, setTimeout((() => {
                        je.value = ya
                    }), 1e3)
                },
                iconComponent: je,
                getEngineIcon: e => e ? (e.toLowerCase(), {
                    component: ra,
                    props: {
                        text: Pe("components.deep_dive_vertical.partner-data")
                    }
                }) : {
                    component: ra,
                    props: {
                        text: Pe("components.deep_dive_vertical.partner-data")
                    }
                },
                bookmarked: Fe,
                setBookmark: async e => {
                    if (!Ve.value || !Ve.value.id) return void(location.href = "/login?redirect_url=" + encodeURIComponent(location.href));
                    if (!O.value.id) return;
                    Fe.value = e;
                    const a = await fetch("/api/user/bookmark", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify({
                            spark_id: O.value.id,
                            is_deleted: !Fe.value
                        })
                    });
                    if (!a.ok) throw new Error(`HTTP error! status: ${a.status}`);
                    const t = await a.json();
                    if (L.log("Success:", t), 0 !== t.status) throw new Error(`request error message: ${t.message}`)
                },
                getEngineHoverText: e => e ? (e.toLowerCase(), Pe("components.deep_dive_vertical.partner-data-description")) : Pe("components.deep_dive_vertical.partner-data-description"),
                toggleHoverEngine: (e, a) => {
                    Be.value[e] = a
                },
                hoveringEngine: Be,
                hasPartnerData: $e,
                hoveringGeneralEngine: He,
                toggleGeneralHoverEngine: e => {
                    He.value = e
                },
                containerRef: Ke,
                containerWidth: Xe,
                handleStatusBarClick: () => {
                    var e, t, n, s, o;
                    if (!O.value) return;
                    L.log("handleStatusBarClick", null == (e = O.value) ? void 0 : e.processed_crawl_result);
                    const l = (null == (s = null == (n = null == (t = O.value) ? void 0 : t.searchPlan) ? void 0 : n.search_keywords) ? void 0 : s.map((e => e.keyword))) || [];
                    a("toggleSidebar", {
                        searchResults: null == (o = O.value) ? void 0 : o.processed_crawl_result,
                        keywords: l
                    })
                },
                handleCitationLinkClick: e => {
                    if (e.target.classList.contains("digital-citation-link")) {
                        e.preventDefault();
                        const t = e.target.getAttribute("href");
                        "finished" === ye.value.status ? a("showSidebar", {
                            searchResults: ye.value.search_results,
                            keywords: ye.value.keywords,
                            jumpToUrl: t
                        }) : t && window.open(t, "_blank")
                    }
                },
                showMindmapModal: ea,
                showMindmap: () => {
                    Y.value || (ea.value = !0)
                },
                showShareModalVisible: aa,
                showShareModal: () => {
                    aa.value = !0
                },
                localSearchStatusTopBarData: ye,
                localSearchSourceTopBarData: Ce,
                getFavIcon: ke,
                sourcesData: fe,
                handleMindMapMarkdown: e => {
                    O.value.answer_mindmap.markmap = e
                },
                handleMindMapClickNode: e => {
                    Ae({
                        type: "ACTION_ASK",
                        user_s_input: e,
                        action_params: {
                            no_auto_scroll: !0
                        }
                    })
                },
                getCurrentUrl: () => {
                    {
                        const e = window.location.href;
                        return L.log("getCurrentUrl:", e), e
                    }
                },
                convertMarkdownToPlainText: ta,
                tryMoa: () => {
                    a("try-moa")
                },
                showTryMoa: E,
                streamingMarkmap: _e,
                externalStreamingMarkmap: x,
                showSearchStatusTopBar: I,
                showSearchSourceTopBar: T,
                mindMapStarted: Y,
                handleShowSideBar: () => {
                    ye.value && a("showSidebar", {
                        searchResults: ye.value.search_results,
                        keywords: ye.value.keywords
                    })
                },
                enablePresentation: na,
                tryPresentation: () => {
                    const e = {
                            type: "generate_presentation",
                            query: be.value,
                            srid: sa.value
                        },
                        a = `/agents_ppt_89757?${Object.entries(e).map((([e,a])=>`${encodeURIComponent(e)}=${encodeURIComponent(a||"")}`)).join("&")}`;
                    window.open(a, "_blank")
                }
            }
        },
        methods: {
            getLimitedItems: (e, a) => null == e ? [] : e.slice(0, a),
            toggleHover(e) {
                this.hoveringLink = e
            }
        }
    },
    pn = {
        class: "deep_dive_result_container",
        ref: "containerRef"
    },
    un = {
        key: 4,
        class: "deep_dive_container"
    },
    hn = {
        key: 0,
        class: "deep_dive_answer_preview"
    },
    vn = {
        key: 1,
        class: "deep_dive_answer"
    },
    gn = {
        key: 0,
        class: "deep_dive_direct_answer"
    },
    _n = {
        key: 1,
        class: "deep_dive_answer"
    },
    mn = {
        key: 8,
        class: "deep_dive_media"
    },
    wn = {
        key: 0,
        class: "gradient-overlay"
    },
    kn = {
        key: 1,
        class: "deep_dive_answer"
    },
    fn = {
        class: "mindmap-control-container"
    },
    yn = {
        class: "mindmap-control-left"
    },
    Cn = {
        class: "mindmap-control-right-icon-container"
    },
    Sn = {
        class: "mindmap-control-right-text"
    },
    xn = {
        key: 11,
        class: "deep-dive-thinking"
    },
    bn = {
        class: "loading_icon"
    },
    An = {
        key: 0,
        class: "deep-dive-thinking-text"
    },
    Mn = {
        key: 1,
        class: "deep-dive-thinking-text"
    },
    In = {
        key: 2,
        class: "deep-dive-thinking-text"
    },
    Ln = {
        key: 12,
        class: "result_buttons"
    },
    Tn = {
        class: "result_operation_right"
    },
    Rn = {
        class: "result_operation"
    },
    Pn = {
        class: "result_operation-icon right"
    },
    En = {
        key: 13,
        class: "bubble try_moa"
    },
    Bn = {
        class: "left"
    },
    Hn = {
        class: "right"
    },
    $n = {
        key: 14,
        class: "bubble try_moa"
    },
    jn = {
        class: "right"
    },
    Un = {
        key: 15,
        class: "answer-divider"
    },
    Dn = {
        key: 16
    },
    Nn = {
        key: 0,
        class: "plan_container_expander_title"
    },
    Fn = {
        class: "plan_step_index_loading_container"
    },
    Vn = {
        class: "plan_step_title_status_text_left"
    },
    On = {
        class: "engine-icon-wrapper"
    },
    Wn = ["data-show"],
    zn = {
        class: "engine-hover-inner"
    },
    qn = {
        class: "engine-hover-triangle general"
    },
    Zn = {
        key: 1,
        class: "plan_container_expander_title"
    },
    Gn = ["data-show"],
    Jn = {
        class: "shield-on-hover-inner"
    },
    Qn = {
        class: "shield-on-hover-triangle search-plan"
    },
    Yn = {
        class: "plan_step_title_status_text_left"
    },
    Kn = {
        class: "engine-icon-wrapper"
    },
    Xn = ["data-show"],
    es = {
        class: "engine-hover-inner"
    },
    as = {
        class: "engine-hover-triangle general"
    },
    ts = {
        class: "expand_icon"
    },
    ns = ["onClick"],
    ss = {
        key: 0,
        class: "plan_step_title"
    },
    os = {
        key: 0,
        class: "plan_step_finished_icon"
    },
    ls = {
        key: 1,
        class: "plan_step_index_circle_inline"
    },
    rs = {
        class: "plan_step_index_text"
    },
    is = {
        key: 0,
        class: "engine-icon-wrapper"
    },
    cs = ["data-show"],
    ds = {
        class: "engine-hover-inner"
    },
    ps = {
        class: "engine-hover-triangle"
    },
    us = {
        key: 0,
        class: "plan_step_title_status_container"
    },
    hs = {
        key: 0,
        class: "plan_step_title_status"
    },
    vs = {
        class: "plan_step_title_status_text_left"
    },
    gs = {
        key: 1,
        class: "plan_step_title_status"
    },
    _s = {
        key: 0,
        class: "plan_step_webpages"
    },
    ms = {
        class: "plan_step_webpage_name_preview_container"
    },
    ws = ["onClick"],
    ks = ["src"],
    fs = {
        class: "plan_step_webpage_name_text"
    },
    ys = {
        key: 2,
        class: "plan_step_title_status"
    },
    Cs = {
        class: "plan_step_title_status_text_left"
    },
    Ss = {
        class: "plan_step_sub_answer_preview"
    },
    xs = {
        class: "plan_step_sub_answer_container preview"
    },
    bs = {
        class: "deep_dive_answer preview"
    },
    As = {
        key: 3,
        class: "plan_step_title_status"
    },
    Ms = {
        key: 0,
        class: "plan_step_title_status_text_left"
    },
    Is = {
        key: 4,
        class: "plan_step_title_status"
    },
    Ls = {
        class: "plan_step_title_status_text_left"
    },
    Ts = {
        class: "plan_step_content_container"
    },
    Rs = {
        class: "plan_step_webpages"
    },
    Ps = {
        class: "plan_step_sub_answer"
    },
    Es = {
        class: "plan_step_sub_answer_container"
    },
    Bs = {
        class: "plan_step_sub_answer_content"
    },
    Hs = {
        key: 0,
        class: "deep_dive_answer"
    },
    $s = {
        key: 0,
        class: "plan_step_sub_answer_divider"
    },
    js = {
        class: "plan_step_title_status_text_left"
    },
    Us = {
        class: "plan_step_webpage_name_container"
    },
    Ds = ["onClick"],
    Ns = ["src"],
    Fs = {
        key: 0,
        class: "plan_step_webpages"
    },
    Vs = {
        class: "expand_icon query"
    },
    Os = {
        key: 17,
        class: "page-source"
    },
    Ws = ["data-show"],
    zs = {
        class: "shield-on-hover-inner"
    },
    qs = {
        class: "shield-on-hover-triangle"
    },
    Zs = ["data-show"],
    Gs = {
        class: "hover-content-inner"
    },
    Js = ["src"],
    Qs = ["href"],
    Ys = {
        key: 18
    },
    Ks = {
        key: 19,
        class: "people-also-ask"
    },
    Xs = ["onClick"],
    eo = {
        class: "text"
    },
    ao = {
        class: "icon"
    },
    to = {
        key: 0,
        class: "login-overlay"
    };
const no = s(dn, [
    ["render", function(a, t, s, o, g, f) {
        var y, C, S, A, M, L, P, E;
        const B = v("StockPriceChart"),
            H = e,
            $ = v("SearchStatusTopBar"),
            j = v("SearchSourceTopBar"),
            D = v("Markdown"),
            N = n,
            F = v("echarts"),
            V = Fe,
            W = q,
            z = v("FullScreenIcon"),
            Z = v("SearchResultMindMap"),
            G = J,
            Y = v("n-dropdown"),
            K = Q,
            X = v("ShareIcon"),
            ee = ze,
            ae = v("ShieldTriangle"),
            te = v("ShieldIcon"),
            ne = v("ExpandIcon"),
            se = v("FinishedIcon"),
            oe = v("PartnerDataSvg"),
            le = v("ExpandLessIcon"),
            re = v("ExpandMoreIcon"),
            ie = v("SparkRecommendationItems"),
            ce = v("RightArrowIcon"),
            de = v("n-modal"),
            pe = v("SearchResultShareModal");
        return _(), l("div", pn, [s.oneboxStockSymbol && s.oneboxStockSymbol.symbol ? (_(), x(B, {
            key: 0,
            symbol: s.oneboxStockSymbol.symbol
        }, null, 8, ["symbol"])) : i("", !0), s.oneboxFinancialReportSankey && s.oneboxFinancialReportSankey.symbol ? (_(), x(H, {
            key: 1,
            "search-filter": s.oneboxFinancialReportSankey,
            "show-logo": !1,
            width: o.containerWidth
        }, null, 8, ["search-filter", "width"])) : i("", !0), (null == (y = o.localSearchStatusTopBarData) ? void 0 : y.status) && o.showSearchStatusTopBar ? (_(), x($, {
            key: 2,
            data: o.localSearchStatusTopBarData,
            onClick: o.handleStatusBarClick
        }, null, 8, ["data", "onClick"])) : i("", !0), o.showSearchSourceTopBar ? (_(), x(j, {
            key: 3,
            "source-list": o.localSearchSourceTopBarData,
            onShowSearchSideBar: o.handleShowSideBar
        }, null, 8, ["source-list", "onShowSearchSideBar"])) : i("", !0), o.isLoading ? (_(), l("div", un, [o.streamingDetailAnswer ? (_(), l("div", hn, [Array.isArray(o.streamingDetailAnswer) ? (_(!0), l(p, {
            key: 0
        }, u(o.streamingDetailAnswer, ((e, a) => (_(), l("div", {
            key: a,
            class: "deep_dive_answer"
        }, [d(D, {
            class: "markdown-viewer",
            html: !0,
            source: o.processCitationPlaceholder(e),
            plugins: o.markdownPlugins
        }, null, 8, ["source", "plugins"])])))), 128)) : (_(), l("div", vn, [d(D, {
            class: "markdown-viewer",
            html: !0,
            source: o.processCitationPlaceholder(o.streamingDetailAnswer),
            plugins: o.markdownPlugins
        }, null, 8, ["source", "plugins"])]))])) : i("", !0)])) : i("", !0), o.embededYoutubeData ? (_(), x(N, {
            key: 5,
            data: o.embededYoutubeData,
            is_editing: !1
        }, null, 8, ["data"])) : i("", !0), o.apiResponse.answer ? (_(), l(p, {
            key: 6
        }, [o.isDirectAnswer ? (_(), l("div", gn, [d(D, {
            class: "markdown-viewer",
            source: o.apiResponse.answer,
            plugins: o.markdownPlugins
        }, null, 8, ["source", "plugins"])])) : (_(), l("div", _n, [d(D, {
            class: "markdown-viewer",
            source: o.apiResponse.answer,
            plugins: o.markdownPlugins
        }, null, 8, ["source", "plugins"])]))], 64)) : i("", !0), o.apiResponse.chart_data && o.apiResponse.chart_data.length > 0 && s.showCharts ? (_(!0), l(p, {
            key: 7
        }, u(o.apiResponse.chart_data, (e => (_(), l("div", null, [d(F, {
            "chart-data": e
        }, null, 8, ["chart-data"])])))), 256)) : i("", !0), o.loadedImagelist && o.loadedImagelist.length >= 1 ? (_(), l("div", mn, [d(V, {
            images: o.loadedImagelist
        }, null, 8, ["images"])])) : i("", !0), o.apiResponse.detailAnswer || o.apiResponse.subTopic ? (_(), l("div", {
            key: 9,
            class: m(["deep_dive_answer_preview", {
                seeMore: o.seeMore
            }])
        }, [o.seeMore ? i("", !0) : (_(), l("div", wn)), o.apiResponse.detailAnswer ? (_(), l("div", kn, [d(D, {
            class: "markdown-viewer",
            source: o.apiResponse.detailAnswer,
            onClick: R(o.handleCitationLinkClick, ["prevent"]),
            onMouseover: t[0] || (t[0] = e => {
                o.citationLink = e.target
            }),
            onMouseleave: t[1] || (t[1] = e => {
                o.citationLink = null
            }),
            plugins: o.markdownPlugins
        }, null, 8, ["source", "onClick", "plugins"])])) : i("", !0), d(W, {
            citationLink: o.citationLink
        }, null, 8, ["citationLink"])], 2)) : i("", !0), o.streamingMarkmap ? (_(), l("div", {
            key: 10,
            class: "mindmap-container",
            onClick: t[3] || (t[3] = (...e) => o.showMindmap && o.showMindmap(...e))
        }, [h("div", fn, [h("div", yn, w(a.$t("components.deep_dive_vertical.mindmap")), 1), h("div", {
            class: "mindmap-control-right",
            onClick: t[2] || (t[2] = (...e) => o.showMindmap && o.showMindmap(...e))
        }, [h("div", Cn, [d(z, {
            class: "mindmap-control-right-icon"
        })]), h("div", Sn, w(a.$t("components.deep_dive_vertical.click-to-view-full-version")), 1)])]), o.streamingMarkmap ? (_(), x(Z, {
            key: 0,
            "markdown-content": o.streamingMarkmap,
            showControls: !1,
            initialExpandLevel: 3,
            recenterTimeout: 150,
            enableInteraction: !1,
            autoHeight: !0,
            canShrinkHeight: !1
        }, null, 8, ["markdown-content"])) : i("", !0)])) : i("", !0), s.showThinking && o.isLoading ? (_(), l("div", xn, [h("div", bn, [d(G)]), o.continueAnswerStarted ? (_(), l("div", An, w(a.$t("components.deep_dive_vertical.continue_answer_started")), 1)) : i("", !0), o.mindMapStarted ? (_(), l("div", Mn, w(a.$t("components.deep_dive_vertical.mindmap_started")), 1)) : i("", !0), o.isNotWritingSearchPlan && !o.localSearchStatusTopBarData.status ? (_(), l("div", In, w(o.externalThinking), 1)) : i("", !0)])) : i("", !0), (null == (C = o.apiResponse) ? void 0 : C.detailAnswer) && s.showResultButtons ? (_(), l("div", Ln, [h("div", Tn, [d(K, null, {
            default: b((() => [d(Y, {
                placement: "bottom-start",
                trigger: "click",
                size: "small",
                options: [{
                    label: a.$t("components.deep_dive_vertical.copy-as-plain-text"),
                    key: "CopyPlainText"
                }, {
                    label: a.$t("components.deep_dive_vertical.copy-as-markdown"),
                    key: "CopyMarkdown"
                }],
                onSelect: o.copyAnswer
            }, {
                default: b((() => [h("div", Rn, [(_(), x(U(o.iconComponent), {
                    class: "result_operation-icon right"
                }))])])),
                _: 1
            }, 8, ["options", "onSelect"])])),
            _: 1
        }), h("div", {
            class: "result_operation",
            onClick: t[4] || (t[4] = (...e) => o.showShareModal && o.showShareModal(...e))
        }, [h("div", Pn, [d(X)])])])])) : i("", !0), (null == (S = o.apiResponse) ? void 0 : S.detailAnswer) && o.showTryMoa && o.currentUser && o.currentUser.id ? (_(), l("div", En, [h("div", Bn, w(a.$t("components.deep_dive_vertical.not_good_enough")), 1), h("div", Hn, [h("div", {
            class: "button",
            onClick: t[5] || (t[5] = (...e) => o.tryMoa && o.tryMoa(...e))
        }, w(a.$t("components.deep_dive_vertical.try_mixture_of_agents")), 1)])])) : i("", !0), o.enablePresentation ? (_(), l("div", $n, [t[26] || (t[26] = h("div", {
            class: "left"
        }, "想做成PPT？", -1)), h("div", jn, [h("div", {
            class: "button",
            onClick: t[6] || (t[6] = (...e) => o.tryPresentation && o.tryPresentation(...e))
        }, "一键生成幻灯片")])])) : i("", !0), (null == (A = o.apiResponse) ? void 0 : A.detailAnswer) && o.showTryMoa ? (_(), l("div", Un, t[27] || (t[27] = [h("hr", null, null, -1)]))) : i("", !0), o.searchPlan && o.searchPlan.plan_steps && !o.localSearchStatusTopBarData.status ? (_(), l("div", Dn, [h("div", {
            class: m(["plan_container_expander", {
                expanded: !o.showSubAnswer
            }]),
            onClick: t[15] || (t[15] = (...e) => o.clickShowSubAnswer && o.clickShowSubAnswer(...e))
        }, [o.isNotWritingSearchPlan ? (_(), l("div", Zn, [h("div", {
            class: "shield-on-hover",
            "data-show": o.hoveringShield
        }, [h("div", Jn, [h("p", null, w(a.$t("components.deep_dive_vertical.shield-on-hover-overlay-content")), 1)]), h("div", Qn, [d(ae)])], 8, Gn), d(te, {
            class: "shield-icon",
            onMouseover: t[9] || (t[9] = e => o.toggleHoverShield(!0)),
            onMouseleave: t[10] || (t[10] = e => o.toggleHoverShield(!1))
        }), h("div", Yn, w(a.$t("components.deep_dive_vertical.generated_with_sparks", {
            count: o.searchPlan.plan_steps.length
        })), 1), h("div", Kn, [h("div", {
            class: "engine-hover",
            "data-show": o.hasPartnerData && o.hoveringGeneralEngine
        }, [h("div", es, [h("p", null, w(o.getEngineHoverText()), 1)]), h("div", as, [d(ae)])], 8, Xn), o.hasPartnerData ? (_(), x(U(o.getEngineIcon().component), O({
            key: 0
        }, o.getEngineIcon().props, {
            onMouseover: t[11] || (t[11] = e => o.toggleGeneralHoverEngine(!0)),
            onMouseleave: t[12] || (t[12] = e => o.toggleGeneralHoverEngine(!1))
        }), null, 16)) : i("", !0)])])) : (_(), l("div", Nn, [h("div", Fn, [d(ee)]), h("div", Vn, w(a.$t("components.deep_dive_vertical.researching_perspectives", {
            count: o.searchPlan.plan_steps.length
        })), 1), h("div", On, [h("div", {
            class: "engine-hover",
            "data-show": o.hasPartnerData && o.hoveringGeneralEngine
        }, [h("div", zn, [h("p", null, w(o.getEngineHoverText()), 1)]), h("div", qn, [d(ae)])], 8, Wn), o.hasPartnerData ? (_(), x(U(o.getEngineIcon().component), O({
            key: 0
        }, o.getEngineIcon().props, {
            onMouseover: t[7] || (t[7] = e => o.toggleGeneralHoverEngine(!0)),
            onMouseleave: t[8] || (t[8] = e => o.toggleGeneralHoverEngine(!1))
        }), null, 16)) : i("", !0)])])), h("div", ts, [o.showSubAnswer ? (_(), x(ne, {
            key: 0,
            onClick: t[13] || (t[13] = e => o.toggleStep(a.index))
        })) : (_(), x(ne, {
            key: 1,
            class: "unexpanded",
            onClick: t[14] || (t[14] = e => o.toggleStep(a.index))
        }))])], 2), d(I, {
            name: "status-slide"
        }, {
            default: b((() => [o.showSubAnswer ? (_(), l("div", {
                key: 0,
                class: "plan_container",
                ref: "planContainer",
                style: c({
                    "--plan-height": o.planContainerHeight + "px"
                })
            }, [(_(!0), l(p, null, u(o.searchPlan.plan_steps, ((e, n) => (_(), l("div", {
                key: n,
                class: "plan_step_container"
            }, [h("div", {
                class: m(["plan_step_title_container", {
                    expanded: !o.computeExpanded(n) && "finished" == e.status
                }]),
                onClick: e => o.toggleStep(n)
            }, [e.keyword ? (_(), l("div", ss, ["finished" == e.status ? (_(), l("div", os, [d(se)])) : (_(), l("div", ls, [h("div", rs, w(n + 1), 1)])), h("div", {
                class: m({
                    plan_step_title_text: !0,
                    search_class: e.engine && "search" === e.engine.toLowerCase(),
                    scholar_class: e.engine && "semantic_scholar" === e.engine.toLowerCase(),
                    financial_class: e.engine && "financial_modeling_prep" === e.engine.toLowerCase()
                })
            }, [h("p", null, w(e.information_to_retrieve), 1), e.engine && "search" !== e.engine.toLowerCase() ? (_(), l("div", is, [h("div", {
                class: "engine-hover",
                "data-show": o.hoveringEngine[n]
            }, [h("div", ds, [h("p", null, w(o.getEngineHoverText(e.engine)), 1)]), h("div", ps, [d(ae)])], 8, cs), (_(), x(U(o.getEngineIcon(e.engine).component), O({
                ref_for: !0
            }, o.getEngineIcon(e.engine).props, {
                onMouseover: e => o.toggleHoverEngine(n, !0),
                onMouseleave: e => o.toggleHoverEngine(n, !1)
            }), null, 16, ["onMouseover", "onMouseleave"]))])) : i("", !0)], 2)])) : i("", !0)], 10, ns), d(I, {
                name: "slide"
            }, {
                default: b((() => [o.computeExpanded(n) || "finished" === e.status ? i("", !0) : (_(), l("div", us, ["set_plan" == e.status ? (_(), l("div", hs, [h("div", vs, w(a.$t("components.deep_dive_vertical.starting-research")), 1)])) : "browsing" == e.status ? (_(), l("div", gs, [t[28] || (t[28] = h("div", {
                    class: "plan_step_title_status_text_left"
                }, "Browsing", -1)), e.webpages_name && e.webpages_name.length > 0 ? (_(), l("div", _s, [h("div", ms, [(_(!0), l(p, null, u(e.webpages_name, ((e, a) => (_(), l("div", {
                    key: a,
                    class: "plan_step_webpage_name",
                    onClick: a => o.openWebpage(e.url)
                }, [e.favicon ? (_(), l("img", {
                    key: 0,
                    src: e.favicon,
                    class: "plan_step_webpage_favicon"
                }, null, 8, ks)) : i("", !0), h("div", fs, w(e.name), 1)], 8, ws)))), 128))])])) : i("", !0)])) : "analyzing" == e.status ? (_(), l("div", ys, [h("div", Cs, w(a.$t("components.deep_dive_vertical.summarizing")), 1), h("div", Ss, [h("div", xs, [h("div", bs, [d(D, {
                    class: "markdown-viewer",
                    source: o.subAnswer[n],
                    plugins: o.markdownPlugins
                }, null, 8, ["source", "plugins"])])])])])) : "finished" == e.status ? (_(), l("div", As, [e.webpages_name ? (_(), l("div", Ms, w(a.$t("components.deep_dive_vertical.see-research-result")), 1)) : i("", !0)])) : (_(), l("div", Is, [h("div", Ls, w(a.$t("components.deep_dive_vertical.analyzing")), 1)])), t[29] || (t[29] = h("div", {
                    class: "plan_step_title_status_gradient"
                }, null, -1))]))])),
                _: 2
            }, 1024), r(h("div", Ts, [h("div", Rs, [h("div", Ps, [h("div", Es, [h("div", Bs, [o.subAnswer[n] ? (_(), l(p, {
                key: 0
            }, [o.subAnswer[n] ? (_(), l("div", Hs, [d(D, {
                class: "markdown-viewer",
                source: o.subAnswer[n],
                onMouseover: t[16] || (t[16] = e => {
                    o.citationLink = e.target
                }),
                onMouseleave: t[17] || (t[17] = e => {
                    o.citationLink = null
                }),
                plugins: o.markdownPlugins
            }, null, 8, ["source", "plugins"]), d(W, {
                citationLink: o.citationLink
            }, null, 8, ["citationLink"])])) : i("", !0)], 64)) : i("", !0), e.webpages_name.length > 0 ? (_(), l(p, {
                key: 1
            }, [e.webpages_name.length > 0 && o.subAnswer[n] ? (_(), l("div", $s)) : i("", !0), h("div", js, w(a.$t("components.deep_dive_vertical.sources")), 1), h("div", Us, [(_(!0), l(p, null, u(e.webpages_name, ((a, t) => (_(), l("div", {
                key: t,
                class: m(["plan_step_webpage_name", {
                    search_class: e.engine && "search" === e.engine.toLowerCase(),
                    scholar_class: e.engine && "semantic_scholar" === e.engine.toLowerCase(),
                    financial_class: e.engine && "financial_modeling_prep" === e.engine.toLowerCase()
                }]),
                onClick: e => o.openWebpage(a.url)
            }, [h("div", {
                class: m(["plan_step_webpage_index", {
                    search_class: e.engine && "search" === e.engine.toLowerCase(),
                    scholar_class: e.engine && "semantic_scholar" === e.engine.toLowerCase(),
                    financial_class: e.engine && "financial_modeling_prep" === e.engine.toLowerCase()
                }])
            }, w(t + 1), 3), !e.engine || "semantic_scholar" !== e.engine.toLowerCase() && "financial_modeling_prep" !== e.engine.toLowerCase() ? a.favicon ? (_(), l("img", {
                key: 1,
                src: a.favicon,
                class: "plan_step_webpage_favicon"
            }, null, 8, Ns)) : i("", !0) : (_(), x(oe, {
                key: 0,
                class: "plan_step_webpage_favicon"
            })), h("div", {
                class: m(["plan_step_webpage_name_text", {
                    search_class: e.engine && "search" === e.engine.toLowerCase(),
                    scholar_class: e.engine && "semantic_scholar" === e.engine.toLowerCase(),
                    financial_class: e.engine && "financial_modeling_prep" === e.engine.toLowerCase()
                }])
            }, w(a.name), 3)], 10, Ds)))), 128))])], 64)) : i("", !0)])])])]), e.webpages_name && e.webpages_name.length > 0 ? (_(), l("div", Fs)) : i("", !0)], 512), [
                [k, o.computeExpanded(n)]
            ]), h("div", Vs, [o.computeExpanded(n) ? (_(), x(le, {
                key: 0,
                style: {
                    width: "20px",
                    height: "20px"
                },
                onClick: e => o.toggleStep(n)
            }, null, 8, ["onClick"])) : (_(), x(re, {
                key: 1,
                style: {
                    width: "20px",
                    height: "20px"
                },
                onClick: e => o.toggleStep(n)
            }, null, 8, ["onClick"]))])])))), 128))], 4)) : i("", !0)])),
            _: 1
        })])) : i("", !0), o.apiResponse.link_num > 0 && !(o.searchPlan || o.searchPlan.plan_steps || o.localSearchStatusTopBarData.status) ? (_(), l("div", Os, [h("div", {
            class: "shield-on-hover",
            "data-show": o.hoveringShield
        }, [h("div", zs, [h("p", null, w(a.$t("components.deep_dive_vertical.shield-on-hover-overlay-content")), 1)]), h("div", qs, [d(ae)])], 8, Ws), d(te, {
            class: "shield-icon",
            onMouseover: t[18] || (t[18] = e => o.toggleHoverShield(!0)),
            onMouseleave: t[19] || (t[19] = e => o.toggleHoverShield(!1))
        }), T(" " + w(a.$t("components.deep_dive_vertical.generated-with-sparks-and-insights-from")) + " ", 1), h("span", {
            class: "hover-target",
            onMouseover: t[20] || (t[20] = e => o.toggleHoverLink(!0)),
            onMouseleave: t[21] || (t[21] = e => o.toggleHoverLink(!1))
        }, [T(w(o.apiResponse.link_num) + " " + w(a.$t("components.deep_dive_vertical.sources")) + " ", 1), h("div", {
            class: "hover-content",
            "data-show": o.hoveringLink
        }, [h("div", Gs, [h("ul", null, [(_(!0), l(p, null, u(o.apiResponse.link, (e => (_(), l("li", {
            key: e.link_host
        }, [h("img", {
            src: e.link_favicon,
            class: "hover-content-inner-favicon"
        }, null, 8, Js), h("a", {
            href: e.link_url,
            target: "_blank",
            rel: "noopener noreferrer"
        }, w(e.link_host), 9, Qs)])))), 128))])])], 8, Zs)], 32)])) : i("", !0), (null == (M = o.apiResponse) ? void 0 : M.recommendation) && (null == (P = null == (L = o.apiResponse.recommendation) ? void 0 : L.flow_items) ? void 0 : P.length) > 0 ? (_(), l("div", Ys, [d(ie, {
            items: o.apiResponse.recommendation.flow_items,
            title: o.apiResponse.recommendation.recommendation_type,
            type: o.apiResponse.recommendation.recommendation_type
        }, null, 8, ["items", "title", "type"])])) : i("", !0), s.showFollowupQuestion && o.apiResponse.people_also_ask ? (_(), l("div", Ks, [(_(!0), l(p, null, u(o.apiResponse.people_also_ask, (e => (_(), l("div", {
            class: "ask-query",
            key: e,
            onClick: a => {
                o.triggerParentAction({
                    type: "ACTION_ASK",
                    user_s_input: e.query,
                    action_params: {
                        no_auto_scroll: !0
                    }
                }, a)
            }
        }, [h("div", eo, w(e.query), 1), h("div", ao, [d(ce)])], 8, Xs)))), 128)), o.currentUser && o.currentUser.id || !(null == (E = o.apiResponse) ? void 0 : E.detailAnswer) ? i("", !0) : (_(), l("div", to))])) : i("", !0), d(de, {
            show: o.showMindmapModal,
            "onUpdate:show": t[23] || (t[23] = e => o.showMindmapModal = e),
            title: a.$t("components.deep_dive_vertical.mindmap")
        }, {
            default: b((() => [d(Z, {
                "markdown-content": o.apiResponse.answer_mindmap.markmap,
                onClose: t[22] || (t[22] = e => o.showMindmapModal = !1),
                onMindMapMarkdown: o.handleMindMapMarkdown,
                onMindMapClickNode: o.handleMindMapClickNode
            }, null, 8, ["markdown-content", "onMindMapMarkdown", "onMindMapClickNode"])])),
            _: 1
        }, 8, ["show", "title"]), d(de, {
            show: o.showShareModalVisible,
            "onUpdate:show": t[25] || (t[25] = e => o.showShareModalVisible = e)
        }, {
            default: b((() => [d(pe, {
                title: o.apiResponse.title,
                "markdown-content": o.apiResponse.detailAnswer,
                "mind-map-content": o.apiResponse.answer_mindmap.markmap,
                "share-url": o.getCurrentUrl(),
                onClose: t[24] || (t[24] = e => o.showShareModalVisible = !1)
            }, null, 8, ["title", "markdown-content", "mind-map-content", "share-url"])])),
            _: 1
        }, 8, ["show"])], 512)
    }],
    ["__scopeId", "data-v-3f3b11ec"]
]);
export {
    pa as E, Ge as L, ze as M, ra as P, kt as S, no as _, ca as a, Qe as b, lt as c, Oe as d, Ze as e, Rt as f
};