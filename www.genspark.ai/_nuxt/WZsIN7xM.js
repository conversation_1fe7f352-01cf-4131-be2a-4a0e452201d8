import {
    c8 as l,
    cb as o,
    J as t,
    al as r,
    ak as e,
    am as n,
    aO as a,
    X as i,
    F as s,
    Y as c,
    aV as u,
    r as v,
    Z as d,
    c_ as h,
    c as b,
    ag as f,
    h as p,
    a3 as m,
    aq as g,
    a4 as w,
    av as S
} from "./Cf0SOiw0.js";
import {
    a as z,
    f as y,
    d as x
} from "./pB_XRIgB.js";
import {
    V as R,
    a as $,
    o as B,
    g as T
} from "./Jr9eiJio.js";
const P = "undefined" != typeof window && (/iPad|iPhone|iPod/.test(navigator.platform) || "MacIntel" === navigator.platform && navigator.maxTouchPoints > 1) && !window.MSStream;

function M(l) {
    const {
        left: o,
        right: t,
        top: r,
        bottom: e
    } = z(l);
    return `${r} ${o} ${e} ${t}`
}
const C = t({
        render() {
            var l, o;
            return null === (o = (l = this.$slots).default) || void 0 === o ? void 0 : o.call(l)
        }
    }),
    D = r("scrollbar", "\n overflow: hidden;\n position: relative;\n z-index: auto;\n height: 100%;\n width: 100%;\n", [e(">", [r("scrollbar-container", "\n width: 100%;\n overflow: scroll;\n height: 100%;\n min-height: inherit;\n max-height: inherit;\n scrollbar-width: none;\n ", [e("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb", "\n width: 0;\n height: 0;\n display: none;\n "), e(">", [r("scrollbar-content", "\n box-sizing: border-box;\n min-width: 100%;\n ")])])]), e(">, +", [r("scrollbar-rail", "\n position: absolute;\n pointer-events: none;\n user-select: none;\n background: var(--n-scrollbar-rail-color);\n -webkit-user-select: none;\n ", [n("horizontal", "\n height: var(--n-scrollbar-height);\n ", [e(">", [a("scrollbar", "\n height: var(--n-scrollbar-height);\n border-radius: var(--n-scrollbar-border-radius);\n right: 0;\n ")])]), n("horizontal--top", "\n top: var(--n-scrollbar-rail-top-horizontal-top); \n right: var(--n-scrollbar-rail-right-horizontal-top); \n bottom: var(--n-scrollbar-rail-bottom-horizontal-top); \n left: var(--n-scrollbar-rail-left-horizontal-top); \n "), n("horizontal--bottom", "\n top: var(--n-scrollbar-rail-top-horizontal-bottom); \n right: var(--n-scrollbar-rail-right-horizontal-bottom); \n bottom: var(--n-scrollbar-rail-bottom-horizontal-bottom); \n left: var(--n-scrollbar-rail-left-horizontal-bottom); \n "), n("vertical", "\n width: var(--n-scrollbar-width);\n ", [e(">", [a("scrollbar", "\n width: var(--n-scrollbar-width);\n border-radius: var(--n-scrollbar-border-radius);\n bottom: 0;\n ")])]), n("vertical--left", "\n top: var(--n-scrollbar-rail-top-vertical-left); \n right: var(--n-scrollbar-rail-right-vertical-left); \n bottom: var(--n-scrollbar-rail-bottom-vertical-left); \n left: var(--n-scrollbar-rail-left-vertical-left); \n "), n("vertical--right", "\n top: var(--n-scrollbar-rail-top-vertical-right); \n right: var(--n-scrollbar-rail-right-vertical-right); \n bottom: var(--n-scrollbar-rail-bottom-vertical-right); \n left: var(--n-scrollbar-rail-left-vertical-right); \n "), n("disabled", [e(">", [a("scrollbar", "pointer-events: none;")])]), e(">", [a("scrollbar", "\n z-index: 1;\n position: absolute;\n cursor: pointer;\n pointer-events: all;\n background-color: var(--n-scrollbar-color);\n transition: background-color .2s var(--n-scrollbar-bezier);\n ", [y(), e("&:hover", "background-color: var(--n-scrollbar-color-hover);")])])])])]),
    W = t({
        name: "Scrollbar",
        props: Object.assign(Object.assign({}, d.props), {
            duration: {
                type: Number,
                default: 0
            },
            scrollable: {
                type: Boolean,
                default: !0
            },
            xScrollable: Boolean,
            trigger: {
                type: String,
                default: "hover"
            },
            useUnifiedContainer: Boolean,
            triggerDisplayManually: Boolean,
            container: Function,
            content: Function,
            containerClass: String,
            containerStyle: [String, Object],
            contentClass: [String, Array],
            contentStyle: [String, Object],
            horizontalRailStyle: [String, Object],
            verticalRailStyle: [String, Object],
            onScroll: Function,
            onWheel: Function,
            onResize: Function,
            internalOnUpdateScrollLeft: Function,
            internalHoistYRail: Boolean,
            yPlacement: {
                type: String,
                default: "right"
            },
            xPlacement: {
                type: String,
                default: "bottom"
            }
        }),
        inheritAttrs: !1,
        setup(t) {
            const {
                mergedClsPrefixRef: r,
                inlineThemeDisabled: e,
                mergedRtlRef: n
            } = c(t), a = u("Scrollbar", n, r), i = v(null), s = v(null), w = v(null), S = v(null), y = v(null), R = v(null), C = v(null), W = v(null), L = v(null), H = v(null), X = v(null), j = v(0), O = v(0), Y = v(!1), k = v(!1);
            let I, E, F = !1,
                V = !1,
                _ = 0,
                U = 0,
                A = 0,
                N = 0;
            const q = P,
                G = d("Scrollbar", "-scrollbar", D, h, t, r),
                J = b((() => {
                    const {
                        value: l
                    } = W, {
                        value: o
                    } = R, {
                        value: t
                    } = H;
                    return null === l || null === o || null === t ? 0 : Math.min(l, t * l / o + 1.5 * x(G.value.self.width))
                })),
                Z = b((() => `${J.value}px`)),
                K = b((() => {
                    const {
                        value: l
                    } = L, {
                        value: o
                    } = C, {
                        value: t
                    } = X;
                    return null === l || null === o || null === t ? 0 : t * l / o + 1.5 * x(G.value.self.height)
                })),
                Q = b((() => `${K.value}px`)),
                ll = b((() => {
                    const {
                        value: l
                    } = W, {
                        value: o
                    } = j, {
                        value: t
                    } = R, {
                        value: r
                    } = H;
                    if (null === l || null === t || null === r) return 0; {
                        const e = t - l;
                        return e ? o / e * (r - J.value) : 0
                    }
                })),
                ol = b((() => `${ll.value}px`)),
                tl = b((() => {
                    const {
                        value: l
                    } = L, {
                        value: o
                    } = O, {
                        value: t
                    } = C, {
                        value: r
                    } = X;
                    if (null === l || null === t || null === r) return 0; {
                        const e = t - l;
                        return e ? o / e * (r - K.value) : 0
                    }
                })),
                rl = b((() => `${tl.value}px`)),
                el = b((() => {
                    const {
                        value: l
                    } = W, {
                        value: o
                    } = R;
                    return null !== l && null !== o && o > l
                })),
                nl = b((() => {
                    const {
                        value: l
                    } = L, {
                        value: o
                    } = C;
                    return null !== l && null !== o && o > l
                })),
                al = b((() => {
                    const {
                        trigger: l
                    } = t;
                    return "none" === l || Y.value
                })),
                il = b((() => {
                    const {
                        trigger: l
                    } = t;
                    return "none" === l || k.value
                })),
                sl = b((() => {
                    const {
                        container: l
                    } = t;
                    return l ? l() : s.value
                })),
                cl = b((() => {
                    const {
                        content: l
                    } = t;
                    return l ? l() : w.value
                })),
                ul = (l, o) => {
                    if (!t.scrollable) return;
                    if ("number" == typeof l) return void dl(l, null != o ? o : 0, 0, !1, "auto");
                    const {
                        left: r,
                        top: e,
                        index: n,
                        elSize: a,
                        position: i,
                        behavior: s,
                        el: c,
                        debounce: u = !0
                    } = l;
                    void 0 === r && void 0 === e || dl(null != r ? r : 0, null != e ? e : 0, 0, !1, s), void 0 !== c ? dl(0, c.offsetTop, c.offsetHeight, u, s) : void 0 !== n && void 0 !== a ? dl(0, n * a, a, u, s) : "bottom" === i ? dl(0, Number.MAX_SAFE_INTEGER, 0, !1, s) : "top" === i && dl(0, 0, 0, !1, s)
                },
                vl = function(t) {
                    const r = {
                        isDeactivated: !1
                    };
                    let e = !1;
                    return l((() => {
                        r.isDeactivated = !1, e ? t() : e = !0
                    })), o((() => {
                        r.isDeactivated = !0, e || (e = !0)
                    })), r
                }((() => {
                    t.container || ul({
                        top: j.value,
                        left: O.value
                    })
                }));

            function dl(l, o, t, r, e) {
                const {
                    value: n
                } = sl;
                if (n) {
                    if (r) {
                        const {
                            scrollTop: r,
                            offsetHeight: a
                        } = n;
                        if (o > r) return void(o + t <= r + a || n.scrollTo({
                            left: l,
                            top: o + t - a,
                            behavior: e
                        }))
                    }
                    n.scrollTo({
                        left: l,
                        top: o,
                        behavior: e
                    })
                }
            }

            function hl() {
                ! function() {
                    void 0 !== E && window.clearTimeout(E);
                    E = window.setTimeout((() => {
                        k.value = !1
                    }), t.duration)
                }(),
                function() {
                    void 0 !== I && window.clearTimeout(I);
                    I = window.setTimeout((() => {
                        Y.value = !1
                    }), t.duration)
                }()
            }

            function bl() {
                const {
                    value: l
                } = sl;
                l && (j.value = l.scrollTop, O.value = l.scrollLeft * ((null == a ? void 0 : a.value) ? -1 : 1))
            }

            function fl() {
                const {
                    value: l
                } = sl;
                l && (j.value = l.scrollTop, O.value = l.scrollLeft * ((null == a ? void 0 : a.value) ? -1 : 1), W.value = l.offsetHeight, L.value = l.offsetWidth, R.value = l.scrollHeight, C.value = l.scrollWidth);
                const {
                    value: o
                } = y, {
                    value: t
                } = S;
                o && (X.value = o.offsetWidth), t && (H.value = t.offsetHeight)
            }

            function pl() {
                t.scrollable && (t.useUnifiedContainer ? fl() : (! function() {
                    const {
                        value: l
                    } = cl;
                    l && (R.value = l.offsetHeight, C.value = l.offsetWidth);
                    const {
                        value: o
                    } = sl;
                    o && (W.value = o.offsetHeight, L.value = o.offsetWidth);
                    const {
                        value: t
                    } = y, {
                        value: r
                    } = S;
                    t && (X.value = t.offsetWidth), r && (H.value = r.offsetHeight)
                }(), bl()))
            }

            function ml(l) {
                var o;
                return !(null === (o = i.value) || void 0 === o ? void 0 : o.contains(T(l)))
            }

            function gl(l) {
                if (!V) return;
                void 0 !== I && window.clearTimeout(I), void 0 !== E && window.clearTimeout(E);
                const {
                    value: o
                } = L, {
                    value: r
                } = C, {
                    value: e
                } = K;
                if (null === o || null === r) return;
                const n = (null == a ? void 0 : a.value) ? window.innerWidth - l.clientX - A : l.clientX - A,
                    i = r - o;
                let s = U + n * (r - o) / (o - e);
                s = Math.min(i, s), s = Math.max(s, 0);
                const {
                    value: c
                } = sl;
                if (c) {
                    c.scrollLeft = s * ((null == a ? void 0 : a.value) ? -1 : 1);
                    const {
                        internalOnUpdateScrollLeft: l
                    } = t;
                    l && l(s)
                }
            }

            function wl(l) {
                l.preventDefault(), l.stopPropagation(), $("mousemove", window, gl, !0), $("mouseup", window, wl, !0), V = !1, pl(), ml(l) && hl()
            }

            function Sl(l) {
                if (!F) return;
                void 0 !== I && window.clearTimeout(I), void 0 !== E && window.clearTimeout(E);
                const {
                    value: o
                } = W, {
                    value: t
                } = R, {
                    value: r
                } = J;
                if (null === o || null === t) return;
                const e = l.clientY - N,
                    n = t - o;
                let a = _ + e * (t - o) / (o - r);
                a = Math.min(n, a), a = Math.max(a, 0);
                const {
                    value: i
                } = sl;
                i && (i.scrollTop = a)
            }

            function zl(l) {
                l.preventDefault(), l.stopPropagation(), $("mousemove", window, Sl, !0), $("mouseup", window, zl, !0), F = !1, pl(), ml(l) && hl()
            }
            f((() => {
                const {
                    value: l
                } = nl, {
                    value: o
                } = el, {
                    value: t
                } = r, {
                    value: e
                } = y, {
                    value: n
                } = S;
                e && (l ? e.classList.remove(`${t}-scrollbar-rail--disabled`) : e.classList.add(`${t}-scrollbar-rail--disabled`)), n && (o ? n.classList.remove(`${t}-scrollbar-rail--disabled`) : n.classList.add(`${t}-scrollbar-rail--disabled`))
            })), p((() => {
                t.container || pl()
            })), m((() => {
                void 0 !== I && window.clearTimeout(I), void 0 !== E && window.clearTimeout(E), $("mousemove", window, Sl, !0), $("mouseup", window, zl, !0)
            }));
            const yl = b((() => {
                    const {
                        common: {
                            cubicBezierEaseInOut: l
                        },
                        self: {
                            color: o,
                            colorHover: t,
                            height: r,
                            width: e,
                            borderRadius: n,
                            railInsetHorizontalTop: i,
                            railInsetHorizontalBottom: s,
                            railInsetVerticalRight: c,
                            railInsetVerticalLeft: u,
                            railColor: v
                        }
                    } = G.value, {
                        top: d,
                        right: h,
                        bottom: b,
                        left: f
                    } = z(i), {
                        top: p,
                        right: m,
                        bottom: g,
                        left: w
                    } = z(s), {
                        top: S,
                        right: y,
                        bottom: x,
                        left: R
                    } = z((null == a ? void 0 : a.value) ? M(c) : c), {
                        top: $,
                        right: B,
                        bottom: T,
                        left: P
                    } = z((null == a ? void 0 : a.value) ? M(u) : u);
                    return {
                        "--n-scrollbar-bezier": l,
                        "--n-scrollbar-color": o,
                        "--n-scrollbar-color-hover": t,
                        "--n-scrollbar-border-radius": n,
                        "--n-scrollbar-width": e,
                        "--n-scrollbar-height": r,
                        "--n-scrollbar-rail-top-horizontal-top": d,
                        "--n-scrollbar-rail-right-horizontal-top": h,
                        "--n-scrollbar-rail-bottom-horizontal-top": b,
                        "--n-scrollbar-rail-left-horizontal-top": f,
                        "--n-scrollbar-rail-top-horizontal-bottom": p,
                        "--n-scrollbar-rail-right-horizontal-bottom": m,
                        "--n-scrollbar-rail-bottom-horizontal-bottom": g,
                        "--n-scrollbar-rail-left-horizontal-bottom": w,
                        "--n-scrollbar-rail-top-vertical-right": S,
                        "--n-scrollbar-rail-right-vertical-right": y,
                        "--n-scrollbar-rail-bottom-vertical-right": x,
                        "--n-scrollbar-rail-left-vertical-right": R,
                        "--n-scrollbar-rail-top-vertical-left": $,
                        "--n-scrollbar-rail-right-vertical-left": B,
                        "--n-scrollbar-rail-bottom-vertical-left": T,
                        "--n-scrollbar-rail-left-vertical-left": P,
                        "--n-scrollbar-rail-color": v
                    }
                })),
                xl = e ? g("scrollbar", void 0, yl, t) : void 0,
                Rl = {
                    scrollTo: ul,
                    scrollBy: (l, o) => {
                        if (!t.scrollable) return;
                        const {
                            value: r
                        } = sl;
                        r && ("object" == typeof l ? r.scrollBy(l) : r.scrollBy(l, o || 0))
                    },
                    sync: pl,
                    syncUnifiedContainer: fl,
                    handleMouseEnterWrapper: function() {
                        ! function() {
                            void 0 !== I && window.clearTimeout(I);
                            Y.value = !0
                        }(),
                        function() {
                            void 0 !== E && window.clearTimeout(E);
                            k.value = !0
                        }(), pl()
                    },
                    handleMouseLeaveWrapper: function() {
                        hl()
                    }
                };
            return Object.assign(Object.assign({}, Rl), {
                mergedClsPrefix: r,
                rtlEnabled: a,
                containerScrollTop: j,
                wrapperRef: i,
                containerRef: s,
                contentRef: w,
                yRailRef: S,
                xRailRef: y,
                needYBar: el,
                needXBar: nl,
                yBarSizePx: Z,
                xBarSizePx: Q,
                yBarTopPx: ol,
                xBarLeftPx: rl,
                isShowXBar: al,
                isShowYBar: il,
                isIos: q,
                handleScroll: function(l) {
                    const {
                        onScroll: o
                    } = t;
                    o && o(l), bl()
                },
                handleContentResize: () => {
                    vl.isDeactivated || pl()
                },
                handleContainerResize: l => {
                    if (vl.isDeactivated) return;
                    const {
                        onResize: o
                    } = t;
                    o && o(l), pl()
                },
                handleYScrollMouseDown: function(l) {
                    l.preventDefault(), l.stopPropagation(), F = !0, B("mousemove", window, Sl, !0), B("mouseup", window, zl, !0), _ = j.value, N = l.clientY
                },
                handleXScrollMouseDown: function(l) {
                    l.preventDefault(), l.stopPropagation(), V = !0, B("mousemove", window, gl, !0), B("mouseup", window, wl, !0), U = O.value, A = (null == a ? void 0 : a.value) ? window.innerWidth - l.clientX : l.clientX
                },
                cssVars: e ? void 0 : yl,
                themeClass: null == xl ? void 0 : xl.themeClass,
                onRender: null == xl ? void 0 : xl.onRender
            })
        },
        render() {
            var l;
            const {
                $slots: o,
                mergedClsPrefix: t,
                triggerDisplayManually: r,
                rtlEnabled: e,
                internalHoistYRail: n,
                yPlacement: a,
                xPlacement: c,
                xScrollable: u
            } = this;
            if (!this.scrollable) return null === (l = o.default) || void 0 === l ? void 0 : l.call(o);
            const v = "none" === this.trigger,
                d = (l, o) => i("div", {
                    ref: "yRailRef",
                    class: [`${t}-scrollbar-rail`, `${t}-scrollbar-rail--vertical`, `${t}-scrollbar-rail--vertical--${a}`, l],
                    "data-scrollbar-rail": !0,
                    style: [o || "", this.verticalRailStyle],
                    "aria-hidden": !0
                }, i(v ? C : w, v ? null : {
                    name: "fade-in-transition"
                }, {
                    default: () => this.needYBar && this.isShowYBar && !this.isIos ? i("div", {
                        class: `${t}-scrollbar-rail__scrollbar`,
                        style: {
                            height: this.yBarSizePx,
                            top: this.yBarTopPx
                        },
                        onMousedown: this.handleYScrollMouseDown
                    }) : null
                })),
                h = () => {
                    var l, a;
                    return null === (l = this.onRender) || void 0 === l || l.call(this), i("div", S(this.$attrs, {
                        role: "none",
                        ref: "wrapperRef",
                        class: [`${t}-scrollbar`, this.themeClass, e && `${t}-scrollbar--rtl`],
                        style: this.cssVars,
                        onMouseenter: r ? void 0 : this.handleMouseEnterWrapper,
                        onMouseleave: r ? void 0 : this.handleMouseLeaveWrapper
                    }), [this.container ? null === (a = o.default) || void 0 === a ? void 0 : a.call(o) : i("div", {
                        role: "none",
                        ref: "containerRef",
                        class: [`${t}-scrollbar-container`, this.containerClass],
                        style: this.containerStyle,
                        onScroll: this.handleScroll,
                        onWheel: this.onWheel
                    }, i(R, {
                        onResize: this.handleContentResize
                    }, {
                        default: () => i("div", {
                            ref: "contentRef",
                            role: "none",
                            style: [{
                                width: this.xScrollable ? "fit-content" : null
                            }, this.contentStyle],
                            class: [`${t}-scrollbar-content`, this.contentClass]
                        }, o)
                    })), n ? null : d(void 0, void 0), u && i("div", {
                        ref: "xRailRef",
                        class: [`${t}-scrollbar-rail`, `${t}-scrollbar-rail--horizontal`, `${t}-scrollbar-rail--horizontal--${c}`],
                        style: this.horizontalRailStyle,
                        "data-scrollbar-rail": !0,
                        "aria-hidden": !0
                    }, i(v ? C : w, v ? null : {
                        name: "fade-in-transition"
                    }, {
                        default: () => this.needXBar && this.isShowXBar && !this.isIos ? i("div", {
                            class: `${t}-scrollbar-rail__scrollbar`,
                            style: {
                                width: this.xBarSizePx,
                                right: e ? this.xBarLeftPx : void 0,
                                left: e ? void 0 : this.xBarLeftPx
                            },
                            onMousedown: this.handleXScrollMouseDown
                        }) : null
                    }))])
                },
                b = this.container ? h() : i(R, {
                    onResize: this.handleContainerResize
                }, {
                    default: h
                });
            return n ? i(s, null, b, d(this.themeClass, this.cssVars)) : b
        }
    }),
    L = W;
export {
    W as S, C as W, L as X
};