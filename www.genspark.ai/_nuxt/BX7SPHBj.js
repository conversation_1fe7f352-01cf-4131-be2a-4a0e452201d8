import {
    _ as e
} from "./DKpDUEYb.js";
import {
    _ as t,
    r as a,
    s as o,
    S as n,
    v as s,
    c as l,
    h as i,
    C as r,
    d as u,
    T as c,
    e as p,
    F as _,
    U as g,
    V as m,
    o as v
} from "./Cf0SOiw0.js";
import {
    f as d
} from "./Bl-gMEVt.js";
import {
    T as h
} from "./B5SiUF0y.js";
import {
    A as f
} from "./tBofk-gQ.js";
import {
    c as y
} from "./Cp7w48vH.js";
import {
    u as k
} from "./DJ-JsGJu.js";
import {
    u as A
} from "./B6noBY_5.js";
const w = t({
    name: "Copilot",
    components: {
        ToBottomIcon: h,
        QuotaExceed: e
    },
    props: {
        copilotType: {
            type: String,
            default: "default"
        },
        firstSearchQuery: {
            type: String,
            default: ""
        },
        not_login: {
            type: <PERSON>olean,
            default: !1
        },
        defaultMessageContent: {
            type: String,
            default: "thinking..."
        },
        getWritingContent: {
            type: Function,
            default: () => {}
        },
        onProjectEvent: {
            type: Function,
            default: () => {}
        },
        extra_data: {
            type: Object,
            default: {}
        },
        force_recaptcha: {
            type: Boolean,
            default: !1
        },
        noAutoScroll: {
            type: Boolean,
            default: !1
        }
    },
    emits: ["triggerAction", "copilotAskFinish", "copilotAskStart"],
    setup(e, {
        emit: t,
        expose: u
    }) {
        const c = a(null),
            p = a(null),
            _ = a("standard"),
            v = A(),
            {
                t: h
            } = o(),
            {
                extra_data: w,
                not_login: b,
                copilotType: C,
                defaultMessageContent: T
            } = n(e),
            E = a(null);
        E.value = document.documentElement;
        const S = e => {
            if ("A" == e.target.tagName) {
                let a = e.target.getAttribute("href");
                if (!a || !a.startsWith("prompt://do_action")) return;
                e.preventDefault();
                try {
                    const t = new URL(e.target.href).searchParams.get("action"),
                        a = JSON.parse(t);
                    V(a)
                } catch (t) {}
            }
        };
        s((() => c.value), ((e, t) => {
            r.log("slotContent changed", e, t), t && t.removeEventListener("click", S), e && e.addEventListener("click", S)
        }));
        const j = a([]),
            x = () => j.value.length > 0 && j.value[j.value.length - 1].thinking,
            I = () => {
                const e = new URL(location.href);
                if (e.searchParams.set("type", C.value), !e.searchParams.get("id") && e.pathname.match(/^\/spark\//)) {
                    const t = e.pathname.split("/");
                    e.searchParams.set("id", t[t.length - 1]), r.log("set id", t[t.length - 1])
                }
                return e.search.replace(/^\?/, "")
            },
            P = () => {
                const e = new URL(location.href);
                if (e.searchParams.set("type", C.value), !e.searchParams.get("id") && e.pathname.match(/^\/spark\//)) {
                    const t = e.pathname.split("/");
                    e.searchParams.set("id", t[t.length - 1]), r.log("set id", t[t.length - 1])
                }
                return e.searchParams.get("id")
            },
            B = a(null),
            N = l((() => null !== B.value)),
            R = new f,
            U = R.toBottomVisible;
        s((() => R.toBottomVisible.value), (() => {
            r.log("toBottomVisible changed", R.toBottomVisible.value), U.value = R.toBottomVisible.value
        }));
        const L = () => {
                E.value && (R.setScrollableParent(E.value), R.start())
            },
            F = async (a = null, o = {}, n = null, s = !1) => {
                var l, i;
                window.gtag && gtag("event", "copilot_ask", {
                    user_s_input: a,
                    model: null == (i = null == (l = w.value) ? void 0 : l.models) ? void 0 : i.join(",")
                });
                const u = e.noAutoScroll || !!o && !!o.no_auto_scroll;
                if (b.value) {
                    m();
                    const e = location.href,
                        t = "/login?redirect_url=" + encodeURIComponent(e);
                    return j.value.push({
                        role: "assistant",
                        content: h("components.copilot.please-login-login_url-to-use-this-feature", [t]),
                        is_prompt: !0
                    }), !1
                }
                if (x()) return v.info(h("components.copilot.thinking-please-wait")), !1;
                B.value && B.value.abort(), B.value = new AbortController;
                const c = B.value;
                if (r.log("submit prompt:", a), !(o && o.force || a || !(j.value.length > 0 && "string" == typeof j.value[j.value.length - 1].content || 0 === j.value.length))) return r.log("user_s_input is empty"), !1;
                let p = {
                    thinking: !0,
                    role: "assistant",
                    content: T.value,
                    is_prompt: !0,
                    hide: s
                };
                if (j.value.push(p), p = j.value[j.value.length - 1], n) return p.content = n, p.thinking = !1, void(p.is_prompt = !1);
                if (E.value && !u && (R.setScrollableParent(E.value), R.start()), w.value && (w.value.writingContent = null), e.getWritingContent) {
                    const t = e.getWritingContent();
                    t && (w.value.writingContent = t)
                }
                let _ = 0;
                try {
                    const n = j.value.filter((e => !e.is_prompt && !e.thinking)),
                        l = await r.getRecaptchaToken("copilot");
                    e.force_recaptcha && !l && location.reload();
                    const i = y({
                        type: C.value,
                        current_query_string: I(),
                        project_id: P(),
                        messages: n,
                        user_s_input: a,
                        action_params: o,
                        extra_data: w.value,
                        g_recaptcha_token: l
                    });
                    t("copilotAskStart", i);
                    let c = "/api/copilot/ask";
                    s && (c = "/api/copilot/ask_proxy_events"), await d(c, i, (t => {
                        _++, s && p.hide && (p.hide = !1), t.render_template && (p.render_template = t.render_template), "message_field_delta" !== t.type && r.log("message event", t.type, t), "message_field" === t.type && k(p, t.field_name, (() => t.field_value)), "message_field_delta" === t.type && k(p, t.field_name, (e => (e || "") + t.delta)), "message_field_append_item" === t.type && k(p, t.field_name, (e => [...e || [], t.field_value])), "message_result" == t.type && (Object.assign(p, t), t.action && V(t.action)), "message_do_action" == t.type && t.action && V(t.action), "project_start" != t.type && "project_field" != t.type && "project_field_delta" != t.type || e.onProjectEvent && e.onProjectEvent(t), "project_data" == t.type && t.project_data && t.project_data.session_state && t.project_data.session_state.messages && (j.value = t.project_data.session_state.messages, j.value.push(p)), u || R.autoScrollToBottom()
                    }), B.value.signal), setTimeout((() => {
                        u || R.autoScrollToBottom()
                    }), 0)
                } catch (g) {
                    if (p.error = !0, c.signal.aborted) p.is_aborted = !0;
                    else if ("Unauthorized" == g.message) {
                        const e = location.href,
                            t = "/login?redirect_url=" + encodeURIComponent(e);
                        p.content = h("components.copilot.please-login-login_url-to-use-this-feature", [t]), p.is_prompt = !0
                    } else p.content = g.message, p.should_retry = !0, p._all_events_count = _, p.is_prompt = !0, window.$azureAppInsights && window.$azureAppInsights.trackException({
                        exception: g,
                        properties: {
                            eventType: "copilot_should_retry",
                            copilotType: C.value,
                            userInput: a,
                            errorMessage: g.message
                        }
                    })
                } finally {
                    p.thinking = !1, p.is_prompt = !1, p.is_aborted && (p.is_prompt = !0), c === B.value && (B.value = null), D.value && D.value(), p.content || (p.should_retry = !0, p._all_events_count = _)
                }
                t("copilotAskFinish", {
                    user_s_input: a,
                    message: p
                }), gtag("event", "copilot_ask_finish", {
                    user_s_input: a
                })
            },
            O = async (t, a = !0, o = {}) => {
                const n = e.noAutoScroll || !!o && !!o.no_auto_scroll,
                    s = { ...t,
                        content: "",
                        thinking: !0
                    };
                j.value.push(s);
                const l = j.value.length - 1,
                    i = t.content;
                if (a)
                    for (let e = 0; e < i.length; e++) await new Promise((e => setTimeout(e, 20))), j.value[l].content += i[e];
                else j.value[l].content = i;
                j.value[l].thinking = !1, n || setTimeout((() => {
                    E.value && (E.value.scrollTop = E.value.scrollHeight)
                }), 0), t.action && await V(t.action)
            },
            V = async e => {
                "ACTION_ASK" == e.type && (!e.user_s_input || e.action_params && e.action_params.no_user_message || await O({
                    role: "user",
                    content: e.user_s_input
                }, !0, e.action_params), await F(e.user_s_input, e.action_params)), "ACTION_CREDIT_EXHAUSTED" == e.type ? (_.value = "standard", g((() => {
                    p.value.trigger()
                }))) : "ACTION_CAPACITY_EXHAUSTED" == e.type ? (_.value = "capacity_exhausted", g((() => {
                    p.value.trigger()
                }))) : "ACTION_CREDIT_INSUFFICIENT" == e.type && (_.value = "credit_insufficient", g((() => {
                    p.value.trigger()
                }))), W.value[e.type] && W.value[e.type](e)
            };
        i((() => {
            if (e.firstSearchQuery) {
                const t = {
                    type: "ACTION_ASK",
                    user_s_input: e.firstSearchQuery,
                    action_params: {
                        no_auto_scroll: !0
                    }
                };
                V(t)
            }
        }));
        const W = a({}),
            D = a(null);
        return u({
            retry: async (e = {}) => {
                e || (e = {});
                const t = e.only_get_events || !1;
                return j.value.length > 0 && "assistant" == j.value[j.value.length - 1].role && j.value.pop(), await F("", {
                    force: !0
                }, "", t)
            },
            toBottomVisible: U,
            toBottom: L,
            setAskFinishCallback: e => {
                D.value = e
            },
            setActionHandler: (e, t) => {
                W.value[e] = t
            },
            addMessage: O,
            doAction: V,
            is_thinking: x,
            is_asking: N,
            cancelAskCopilot: async (e = {}) => {
                if (B.value && (B.value.abort(), B.value = null), e && e.userAborted) {
                    const e = P();
                    if (e) {
                        (await fetch(`/api/copilot/ask_abort?project_id=${e}`)).ok
                    }
                }
            },
            askCopilot: (e = {}) => {
                F(e.user_s_input, e.action_params, e.answer, e.only_get_events)
            },
            forceSubmit: async () => {
                await F("", {
                    force: !0
                }, "")
            },
            setRecommendations: e => {
                let t = j.value.filter((e => e.is_prompt)).length == j.value.length;
                e && t && j.value.length > 0 && (j.value[j.value.length - 1].recommend_actions = e)
            },
            setNotLogin: e => {
                b.value = e
            },
            submitPrompt: async (e = "", t = "", a = [], o = [], n = {}) => {
                if (a || (a = []), o || (o = []), !(e.trim() || a && 0 !== a.length || o && 0 !== o.length || t)) return void r.log("submitPrompt return");
                let s = e;
                if (e.startsWith("vision://")) {
                    s = `<img src="${e.slice(9)}" alt="Image" style="max-height: 100px;">`
                }
                const l = {
                    role: "user",
                    content: s
                };
                (a.length > 0 || o.length > 0) && (l.content = [], a.forEach((e => {
                    l.content.push({
                        type: "image_url",
                        image_url: {
                            url: e
                        }
                    })
                })), o.forEach((e => {
                    l.content.push({
                        type: "private_file",
                        private_file: e
                    })
                })), e && l.content.push({
                    type: "text",
                    text: e
                })), j.value.push(l);
                const i = { ...n || {}
                };
                await F(e, i, t)
            },
            setContentElement: e => {
                c.value = e
            },
            setScrollElement: e => {
                e && E.value !== e && function(e) {
                    let t = 0;
                    e.addEventListener("touchstart", (function(e) {
                        r.log("touchstart!!!!!!"), t = e.touches ? e.touches[0].screenY : e.screenY
                    })), e.addEventListener("touchmove", (function(a) {
                        var o = a.touches ? a.touches[0].screenY - t : a.screenY - t,
                            n = e.scrollTop,
                            s = e.scrollHeight,
                            l = e.offsetHeight;
                        (0 === n && o > 0 || n + l >= s && o < 0) && a.preventDefault()
                    }))
                }(e), r.log("setScrollElement", e), E.value = e, c.value || (c.value = e), R.setScrollableParent(e), R.start()
            },
            getMessagesRef: () => j,
            setMessages: e => {
                j.value = e
            },
            abort: () => {
                B.value && (B.value.abort(), B.value = null)
            }
        }), {
            toBottomVisible: U,
            toBottom: L,
            slotContent: c,
            quotaExceed: p,
            doAction: V,
            messages: j,
            quotaExceedWindowType: _
        }
    }
}, [
    ["render", function(t, a, o, n, s, l) {
        const i = e;
        return v(), u(_, null, [c(t.$slots, "default", {
            messages: n.messages,
            doAction: n.doAction,
            ref: "slotContent"
        }), p(i, {
            ref: "quotaExceed",
            "window-type": n.quotaExceedWindowType
        }, null, 8, ["window-type"])], 64)
    }]
]);
export {
    w as _
};