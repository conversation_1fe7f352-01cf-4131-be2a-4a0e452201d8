async function t(t, e, n, r) {
    try {
        const o = await fetch(t, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(e),
            signal: r
        });
        if ("401" == o.status) throw new Error("Unauthorized");
        if (!o.ok) throw new Error(`HTTP error! status: ${o.status} ${o.statusText} ${t}`);
        const s = o.body.pipeThrough(new TextDecoderStream).getReader();
        let i = "";
        for (;;) {
            const {
                value: t,
                done: e
            } = await s.read();
            for (null != t && (i += t); - 1 !== i.indexOf("\n\n");) {
                let t = i.substring(0, i.indexOf("\n\n"));
                i = i.substring(i.indexOf("\n\n") + 2);
                let e = t.substring(t.indexOf("{"), t.length),
                    r = JSON.parse(e);
                try {
                    n(r)
                } catch (a) {}
            }
            if (e) break
        }
    } catch (a) {
        throw a.name, a
    }
}
export {
    t as f
};