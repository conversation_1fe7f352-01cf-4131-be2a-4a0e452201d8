.mind-map-container[data-v-e4dd97e3] {
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    max-width: 100%;
    overflow: hidden;
    padding: 16px;
    position: relative
}

.mind-map[data-v-e4dd97e3] {
    display: block;
    flex: 1;
    height: 100%;
    width: 100%
}

[data-v-e4dd97e3] .markmap-node {
    pointer-events: all !important
}

[data-v-e4dd97e3] .markmap-node-circle {
    fill: #4a90e2
}

[data-v-e4dd97e3] .markmap-node-text {
    fill: #333 !important;
    cursor: pointer !important;
    pointer-events: all !important;
    text-decoration: underline !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    user-select: none !important
}

[data-v-e4dd97e3] .markmap-node-text:hover {
    fill: #4a90e2 !important
}

[data-v-e4dd97e3] .markmap-link {
    stroke-width: 1.5px
}

.close-button[data-v-e4dd97e3] {
    align-items: center;
    background: #f5f5f5;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    height: 24px;
    justify-content: center;
    position: absolute;
    right: 16px;
    top: 16px;
    transition: all .2s;
    width: 24px;
    z-index: 1000
}

.close-button[data-v-e4dd97e3]:hover {
    transform: scale(1.1)
}

.close-button .icon-button[data-v-e4dd97e3] {
    height: 20px;
    width: 20px
}

.controls[data-v-e4dd97e3] {
    align-items: center;
    bottom: 24px;
    box-sizing: border-box;
    display: flex;
    gap: 12px;
    justify-content: space-between;
    padding: 0 24px;
    position: absolute;
    right: 0;
    width: 100%;
    z-index: 1000
}

.controls-left[data-v-e4dd97e3] {
    color: #a8adb2;
    font-size: 12px
}

.controls-right[data-v-e4dd97e3] {
    display: flex;
    gap: 12px
}

.controls .icon-button-wrapper[data-v-e4dd97e3] {
    align-items: center;
    background: #f5f5f5;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    height: 24px;
    justify-content: center;
    transition: all .2s;
    transition: transform .2s;
    width: 24px
}

.controls .icon-button[data-v-e4dd97e3] {
    height: 20px;
    width: 20px
}

.controls .icon-button-wrapper[data-v-e4dd97e3]:hover {
    transform: scale(1.1)
}

[data-v-e4dd97e3] .markmap-svg {
    pointer-events: all !important
}

[data-v-e4dd97e3] .markmap-node,
[data-v-e4dd97e3] .markmap-svg,
[data-v-e4dd97e3] g {
    pointer-events: all !important
}

[data-v-e4dd97e3] [data-lines] {
    cursor: pointer !important;
    pointer-events: all !important
}

[data-v-e4dd97e3] [data-lines]:hover {
    fill: #4a90e2 !important
}

[data-v-e4dd97e3] .content-wrap {
    display: flex;
    margin-left: -16px;
    transform-origin: 0 0;
    -webkit-transform-origin: 0 0
}

[data-v-e4dd97e3] .text-wrap {
    flex-shrink: 0;
    padding-left: 20px
}

[data-v-e4dd97e3] .text-wrap:hover {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAYAAACohjseAAAACXBIWXMAACxLAAAsSwGlPZapAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAQgSURBVHgB7Zr/ddMwEMe/8v9AmAAzQcsGZgLaCZpO0DBBnS5AOgHpBMAEpBPgToCZALcDxNw5yntpdLJP/lGT9/J5L01jK5K+1kk63QU4cuTImBj0zCQtJ/QW0+uU39fAu+29CPiDEgW1mtHHrEhNgYHpRWAlqsS0BD5hI2yi/GpGHbijXnwnsTkGoJNAFrZe48oYzKAXJUIPZxkZzPsW2lrg63l5ZdZI0VHYPn0LDRZIoxaXJb7SvwmGIzcbkUt0JEggiTslcT+hG7WCOpnTe7ZzLebvUh2niu+DyqVPN2aODqgFvpqXF9Eay4bKMvrzg/7lRSPzlWMrwMYCLkhEUldnV5EqgXbkftUUYZO6JFErBGLFplT/ha9MF5GNAu2cY7OMxQpK3BY3ZoaOUDtTaufa2w7wuZibBQJpFPjmuvztaZTn2HmbUfNhH+Y3QJyj3N6H0NU1qrtJW4HviXJjH/sUV1VKned68Xxh2jKxq3cQ3hG0T5PnnbNiDiFur+2JbTuG2/ZlyPbhH8HNfHDFldX+tMKAsI/K5i/ds/NUjSiweoLyRp7TgpLiBeBthjd74VZM/UugxDeCZ5DNo9Om2wJeNZ0TR8goigJp9KQ9Ke/DdQrBmuqtcCuxx7JG5BEUvAtajW4xDkvP9QQKHIFe+zZYYQSqrQOVT/ucdUuB8Ox7db7l4BjcO9ci2eNxi7nEbv0YTxxTuu3TQnMCBboRNHjEmBi0jt1EmkIUOPqLccmFazEUqAT+B0hbgmpUI80XqdBbjEuPAkvXHLQTekCc45ORzdbBFWhke9d6DkNAntWJcPEBCiQTXXnKnmEEbFA5cW5Euq3LEViF0wWvpS5mMjC+B7uCAnEVJfu+Fy6zg6sK9/WJ5+SQaUMXvm1i4WnsC14QDkRBPrapHX9RoM9MsRnFBC+ADZn4zn0rKPFu9GSm4uGWAz82ljksnhBiabAMiax5BVZxF0prCbeq0N6Q2wZH82hrmAq38ghhUYVaV42DrZA9hipHMcRIsjibtXL70yLrpIlsn9lgrERuQ4g5OlLlGmkRM/LIVab5lJpLBKLNTaR1gR7OHUQR7toKtQ+RV+i4ptg22Bx0NlVnl5pEEjnNm5U2ebmX9k6gI1hkaH6wSeSWjDqe0QR/sL4tz+NJ9SLHvdw4zwnaESQyPMN7Xc6og8Nv+LyCb3xQ8aikFRl84OUUFlX+HsrjSgsKXr0fU3NuEzHSKj6xq3ij6xg8gs9aacjpBbIN8i52fz/TkDZvHMlOAnc6MYUiHS12YOMSsnO/8P0wqIvIXgTudIQ7kNgXH1LZlGLbMTa9wkbIsurAGvADoLYiexU4NG1EHkpUrcKm1LwLD8U3p/sXD0og0yDS4eAEMh6RBalxDuoHNQf3saeZmf24HDVBdOTIEZF/XkPL/Om+sOkAAAAASUVORK5CYII=);
    background-position: 0;
    background-repeat: no-repeat;
    background-size: 16px
}

[data-v-e4dd97e3] .text-wrap.highlighted {
    background-color: #ffe000;
    border-radius: 2px;
    box-sizing: border-box;
    color: #000
}

[data-v-e4dd97e3] .markmap-foreign {
    overflow: visible !important
}

@media (prefers-color-scheme:dark) {
    .mind-map-container[data-v-e4dd97e3] {
        background-color: #1a1a1a
    }
    .close-button[data-v-e4dd97e3],
    .controls .icon-button-wrapper[data-v-e4dd97e3] {
        background: #2a2a2a;
        color: #e5e5e5
    }
    .markmap-foreign[data-v-e4dd97e3] {
        color: #e5e5e5
    }
    [data-v-e4dd97e3] .markmap {
        --markmap-text-color: #e5e5e5 !important
    }
}

[data-v-e4dd97e3] .search-icon-container {
    pointer-events: none
}

[data-v-e4dd97e3] .search-icon-container svg {
    height: 100%;
    width: 100%;
    fill: currentColor
}

[data-v-e4dd97e3] .markmap-foreign div {
    cursor: pointer;
    transition: all .2s ease
}

[data-v-e4dd97e3] .markmap-foreign div:hover {
    color: #0f7fff
}

.no-hover[data-v-e4dd97e3] .markmap-foreign div:hover {
    color: unset;
    cursor: unset
}

@media (prefers-color-scheme:dark) {
    [data-v-e4dd97e3] .markmap-foreign div:hover {
        color: #0f7fff
    }
    .no-hover[data-v-e4dd97e3] .markmap-foreign div:hover {
        color: unset
    }
}

[data-v-e4dd97e3] .progress-container {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%
}

[data-v-e4dd97e3] .progress-text {
    color: #0f7fff;
    font-family: Arial, sans-serif;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    text-align: left
}

[data-v-e4dd97e3] .loading-animation-container {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%
}

[data-v-e4dd97e3] .loading-circles {
    display: flex;
    gap: 2px;
    height: 12px
}

[data-v-e4dd97e3] .circle {
    align-items: center;
    display: flex;
    justify-content: center;
    width: 12px
}

[data-v-e4dd97e3] .circle-dot {
    background-color: #0f7fff;
    border-radius: 50%
}

@keyframes grow-e4dd97e3 {
    0%,
    to {
        height: 4px;
        opacity: .45;
        width: 4px
    }
    50% {
        height: 12px;
        opacity: 1;
        width: 12px
    }
}

@keyframes shrink-e4dd97e3 {
    0%,
    to {
        height: 12px;
        opacity: 1;
        width: 12px
    }
    50% {
        height: 4px;
        opacity: .45;
        width: 4px
    }
}

[data-v-e4dd97e3] .circle-small {
    animation: grow-e4dd97e3 2s infinite
}

[data-v-e4dd97e3] .circle-large {
    animation: shrink-e4dd97e3 2s infinite
}

.dot-wrapper[data-v-f443b43f] {
    align-items: center;
    bottom: 20px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
    position: absolute;
    width: 100%
}

.dot[data-v-f443b43f] {
    background: #bbbcbd;
    border-radius: 50%;
    cursor: pointer;
    height: 9px;
    width: 9px
}

.dot.active[data-v-f443b43f] {
    background: #0f7fff
}

.dot[data-v-f443b43f]:hover {
    background: #0f7fff7f
}

.wrapper[data-v-f443b43f] {
    background: #fafafa;
    display: block;
    max-height: 600px;
    min-height: 300px;
    overflow: hidden;
    position: relative;
    width: 100%
}

.arrow[data-v-f443b43f],
.wrapper[data-v-f443b43f] {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.arrow[data-v-f443b43f] {
    align-items: center;
    background: #23242533;
    border-radius: 50%;
    color: #fff;
    cursor: pointer;
    display: flex;
    height: 40px;
    justify-content: center;
    margin-top: -20px;
    position: absolute;
    top: 50%;
    width: 40px;
    z-index: 1
}

.arrow[data-v-f443b43f]:hover {
    background: #232425
}

.arrow.left[data-v-f443b43f] {
    left: 10px
}

.arrow.right[data-v-f443b43f] {
    right: 10px
}

.image-container[data-v-f443b43f] {
    height: 100%;
    position: relative;
    width: 100%
}

.image-container img[data-v-f443b43f] {
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100%
}

.hotspot[data-v-f443b43f] {
    background-color: #fff;
    border: 2px solid #0f7fff;
    border-radius: 50%;
    cursor: pointer;
    height: 20px;
    left: 50%;
    opacity: 0;
    pointer-events: auto;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    transition: opacity .3s ease;
    width: 20px;
    z-index: 2
}

.image.show-dots .hotspot[data-v-f443b43f] {
    opacity: 1
}

.segment-outline[data-v-f443b43f] {
    border: 2px solid #0f7fff;
    bottom: 0;
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1
}

.magnifier[data-v-f443b43f] {
    background-color: #ffffffb3;
    border-radius: 50%;
    bottom: 10px;
    cursor: pointer;
    height: 30px;
    right: 10px;
    width: 30px;
    z-index: 3
}

.image[data-v-f443b43f],
.magnifier[data-v-f443b43f] {
    align-items: center;
    display: flex;
    justify-content: center;
    position: absolute
}

.image[data-v-f443b43f] {
    height: 100%;
    left: 0;
    overflow: hidden;
    top: 0;
    transition: all .3s;
    width: 100%
}

@media (hover:hover) {
    .wrapper:hover .image .source-wrapper[data-v-f443b43f] {
        bottom: 0
    }
}

@media (hover:none) {
    .wrapper .image .source-wrapper[data-v-f443b43f] {
        bottom: 0
    }
    .dot-wrapper[data-v-f443b43f] {
        display: none
    }
}

.image .source-wrapper[data-v-f443b43f] {
    align-items: center;
    background: linear-gradient(0deg, rgba(0, 0, 0, .5), transparent 60px, transparent);
    bottom: -100%;
    display: flex;
    flex-direction: row;
    gap: 5px;
    height: 100%;
    height: 60px;
    justify-content: flex-start;
    position: absolute;
    width: 100%
}

.image .source-wrapper .source[data-v-f443b43f] {
    background: #0009;
    border-radius: 28px;
    color: #efefef;
    cursor: pointer;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 5px;
    line-height: 18px;
    margin-left: 20px;
    padding: 4px 12px;
    text-decoration: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    z-index: 10
}

.image .source-wrapper .icon[data-v-f443b43f],
.image .source-wrapper .source[data-v-f443b43f] {
    align-items: center;
    display: flex;
    justify-content: center
}

.image img[data-v-f443b43f] {
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100%
}

@media (prefers-color-scheme:dark) {
    .wrapper[data-v-f443b43f] {
        background: #232425
    }
}

.screenshot[data-v-8bfdc6cd] {
    height: 100%;
    max-width: 400px;
    width: 100%
}

.screenshot img[data-v-8bfdc6cd] {
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100%
}

[data-v-8bfdc6cd] svg {
    height: 100%;
    width: 100%
}

.title[data-v-8bfdc6cd] {
    align-items: center;
    color: #232425;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    gap: 12px;
    line-height: 21px;
    overflow-x: hidden;
    padding: 16px
}

.title[data-v-8bfdc6cd]:hover {
    color: #0c66cc;
    text-decoration: underline
}

.content.image[data-v-8bfdc6cd] {
    box-sizing: border-box;
    padding: 16px 16px 0
}

.content.image img[data-v-8bfdc6cd] {
    box-shadow: 0 4px 15px #00000026
}

.host .divider[data-v-8bfdc6cd] {
    background-color: #606366;
    height: 16px;
    width: 1px
}

.host[data-v-8bfdc6cd] {
    align-items: center;
    color: #606366;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 5px;
    line-height: 21px
}

.divider[data-v-8bfdc6cd] {
    background-color: #eaeaea;
    height: 1px;
    margin: 0 auto;
    width: calc(100% - 32px)
}

.favicon[data-v-8bfdc6cd] {
    display: flex;
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.favicon img[data-v-8bfdc6cd] {
    display: flex;
    height: 100%;
    width: 100%
}

.block_quote_wrapper[data-v-8bfdc6cd] {
    background-color: #f8f8f8;
    border-radius: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: 100%
}

.content[data-v-8bfdc6cd] {
    color: #232425;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 30px;
    position: relative
}

.content .text[data-v-8bfdc6cd] {
    padding: 30px 16px 4px;
    position: relative;
    z-index: 1
}

.content .text[data-v-8bfdc6cd] p {
    font-size: 14px;
    margin-bottom: 16px
}

.quote-left[data-v-8bfdc6cd],
.quote-right[data-v-8bfdc6cd] {
    align-items: center;
    color: #efefef;
    display: flex;
    height: 60px;
    justify-content: center;
    position: absolute;
    width: 60px
}

.quote-left[data-v-8bfdc6cd] {
    left: 18px;
    top: 12px
}

.quote-right[data-v-8bfdc6cd] {
    bottom: 12px;
    right: 18px;
    transform: rotate(180deg)
}

.source[data-v-8bfdc6cd] {
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 16px;
    justify-content: space-between;
    line-height: 21px;
    padding: 20px 16px
}

.source .site[data-v-8bfdc6cd] {
    color: #0c66cc;
    cursor: pointer
}

.source .date[data-v-8bfdc6cd] {
    color: #909499
}

@media (prefers-color-scheme:dark) {
    .block_quote_wrapper[data-v-8bfdc6cd] {
        background-color: #222;
        color: #fff
    }
    .content[data-v-8bfdc6cd],
    .title[data-v-8bfdc6cd] {
        color: #fff
    }
    .quote-left[data-v-8bfdc6cd],
    .quote-right[data-v-8bfdc6cd] {
        color: #333
    }
    .divider[data-v-8bfdc6cd] {
        background-color: #333
    }
}

.mindmap-container[data-v-de3f69ae] svg {
    transition: all .3s ease
}

@media (hover:hover) {
    .mindmap-container[data-v-de3f69ae]:hover svg {
        transform: scale(1.01)
    }
}

.mindmap-container[data-v-de3f69ae] {
    border: 1px solid #f4f4f4;
    border-radius: 8px;
    cursor: pointer;
    padding: 2px;
    position: relative
}

.mindmap-control-left[data-v-de3f69ae] {
    color: #232425;
    font-size: 16px;
    font-weight: 700;
    left: 10px;
    line-height: 1.5em;
    position: absolute;
    top: 10px;
    z-index: 1
}

@media (prefers-color-scheme:dark) {
    .mindmap-control-left[data-v-de3f69ae] {
        color: #909499
    }
}

.mindmap-control-right[data-v-de3f69ae] {
    position: absolute;
    right: 10px;
    top: 10px;
    transition: all .3s ease;
    z-index: 1
}

.mindmap-control-right[data-v-de3f69ae],
.mindmap-control-right-icon-container[data-v-de3f69ae] {
    align-items: center;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    gap: 8px;
    justify-content: center
}

.mindmap-control-right-icon-container[data-v-de3f69ae] {
    background-color: #f4f4f4;
    border-radius: 8px;
    height: 24px;
    width: 24px
}

@media (prefers-color-scheme:dark) {
    .mindmap-control-right-icon-container[data-v-de3f69ae] {
        background-color: #333
    }
}

.mindmap-control-right-icon[data-v-de3f69ae] {
    height: 20px;
    width: 20px
}

.mindmap-control-right-text[data-v-de3f69ae] {
    color: #606366;
    font-size: 14px;
    line-height: 1.5em
}

@media (prefers-color-scheme:dark) {
    .mindmap-control-right-text[data-v-de3f69ae] {
        color: #909499
    }
}