import {
    d as C,
    o as t,
    D as i
} from "./Cf0SOiw0.js";
const h = {
    width: "30",
    height: "30",
    viewBox: "0 0 30 30",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const o = {
    render: function(o, d) {
        return t(), C("svg", h, d[0] || (d[0] = [i('<g id="Group"><path id="Vector" d="M24.3026 0H5.6974C2.55081 0 0 2.55081 0 5.6974V24.3026C0 27.4492 2.55081 30 5.6974 30H24.3026C27.4492 30 30 27.4492 30 24.3026V5.6974C30 2.55081 27.4492 0 24.3026 0Z" fill="black"></path><g id="Group_2"><path id="Vector_2" d="M25.1605 22.9316H4.91513C4.49508 22.9316 4.15457 23.2722 4.15457 23.6922V25.2891C4.15457 25.7091 4.49508 26.0496 4.91513 26.0496H25.1605C25.5806 26.0496 25.9211 25.7091 25.9211 25.2891V23.6922C25.9211 23.2722 25.5806 22.9316 25.1605 22.9316Z" fill="white"></path><g id="Group_3"><path id="Vector_3" d="M10.913 19.676C10.7912 19.676 10.6883 19.5866 10.6694 19.4648C9.87635 14.2221 9.30254 13.7593 4.09774 12.9744C3.93805 12.95 3.81897 12.812 3.81897 12.6496C3.81897 12.4872 3.93805 12.3492 4.09774 12.3248C9.27547 11.5426 9.7356 11.0798 10.5178 5.90476C10.5422 5.74507 10.6802 5.62598 10.8426 5.62598C11.005 5.62598 11.143 5.74507 11.1674 5.90476C11.9496 11.0798 12.4124 11.5426 17.5875 12.3248C17.7471 12.3492 17.8662 12.4872 17.8662 12.6496C17.8662 12.812 17.7471 12.95 17.5875 12.9744C12.3881 13.7593 11.9442 14.2221 11.1566 19.4648C11.1376 19.5839 11.0348 19.676 10.913 19.676Z" fill="white"></path><path id="Vector_4" d="M20.7921 12.7392C20.7163 12.7392 20.6513 12.6824 20.6405 12.6066C20.1452 9.33159 19.7852 9.04199 16.5346 8.55209C16.4344 8.53585 16.3586 8.45195 16.3586 8.34909C16.3586 8.24895 16.4317 8.16234 16.5346 8.1461C19.769 7.65891 20.0586 7.3693 20.5458 4.13491C20.562 4.03477 20.6459 3.95898 20.7488 3.95898C20.8489 3.95898 20.9355 4.03206 20.9518 4.13491C21.4389 7.3693 21.7286 7.65891 24.9629 8.1461C25.0631 8.16234 25.1389 8.24624 25.1389 8.34909C25.1389 8.44924 25.0658 8.53585 24.9629 8.55209C21.715 9.04199 21.4362 9.33159 20.9436 12.6066C20.9328 12.6824 20.8678 12.7392 20.7921 12.7392Z" fill="white"></path><path id="Vector_5" d="M20.7649 20.9126C20.7162 20.9126 20.6756 20.8774 20.6675 20.8287C20.3535 18.7446 20.1235 18.5606 18.0556 18.2493C17.9907 18.2385 17.9446 18.1843 17.9446 18.1194C17.9446 18.0544 17.9907 18.0003 18.0556 17.9895C20.1126 17.6782 20.2967 17.4942 20.6079 15.4371C20.6188 15.3722 20.6729 15.3262 20.7379 15.3262C20.8028 15.3262 20.857 15.3722 20.8678 15.4371C21.179 17.4942 21.3631 17.6782 23.4201 17.9895C23.4851 18.0003 23.5311 18.0544 23.5311 18.1194C23.5311 18.1843 23.4851 18.2385 23.4201 18.2493C21.3523 18.5606 21.1763 18.7446 20.8624 20.8287C20.8542 20.8774 20.8137 20.9126 20.7649 20.9126Z" fill="white"></path></g></g></g>', 1)]))
    }
};
export {
    o as G
};