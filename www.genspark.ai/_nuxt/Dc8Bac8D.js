import {
    d as o,
    b as r,
    o as t
} from "./Cf0SOiw0.js";
const n = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const e = {
    render: function(e, s) {
        return t(), o("svg", n, s[0] || (s[0] = [r("path", {
            d: "M12.071 16.853L4.99998 9.78196L12.071 2.71089",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }, null, -1)]))
    }
};
export {
    e as M
};