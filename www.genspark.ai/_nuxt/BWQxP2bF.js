import {
    d as e,
    o as s,
    b as i,
    _ as o,
    r as n,
    S as a,
    s as r,
    h as t,
    x as l,
    v as p,
    a as v,
    e as c,
    w as d,
    f as u,
    p as m,
    t as _,
    y,
    n as h,
    i as k,
    F as f,
    k as w,
    H as g,
    L as b,
    a9 as C,
    l as j,
    q as x,
    a4 as P,
    aj as $,
    C as A
} from "./Cf0SOiw0.js";
import {
    L as E
} from "./Bju5W73y.js";
import {
    t as L
} from "./Jx3-I-D7.js";
import {
    u as O
} from "./B6noBY_5.js";
const T = {
    width: "17",
    height: "17",
    viewBox: "0 0 17 17",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const S = {
        render: function(o, n) {
            return s(), e("svg", T, n[0] || (n[0] = [i("path", {
                d: "M8.5 8.5C10.1568 8.5 11.5 7.15683 11.5 5.5C11.5 3.84317 10.1568 2.5 8.5 2.5C6.84317 2.5 5.5 3.84317 5.5 5.5C5.5 7.15683 6.84317 8.5 8.5 8.5Z",
                stroke: "currentColor",
                "stroke-width": "1.43",
                "stroke-linejoin": "round"
            }, null, -1), i("path", {
                d: "M13.5 13.5C13.5 10.7386 11.2614 8.5 8.5 8.5C5.73858 8.5 3.5 10.7386 3.5 13.5",
                stroke: "currentColor",
                "stroke-width": "1.43",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    W = {
        class: "permission-popover-header"
    },
    q = {
        class: "permission-popover-header-title"
    },
    B = {
        key: 0,
        class: "loading-indicator"
    },
    F = {
        class: "permission-popover-content"
    },
    I = {
        class: "permission-popover-content-item"
    },
    M = {
        class: "permission-popover-content-item-title"
    },
    U = {
        class: "access-options"
    },
    H = ["checked", "disabled"],
    J = {
        for: "anyone-link"
    },
    N = ["checked", "disabled"],
    V = {
        for: "only-invited"
    },
    D = {
        key: 0,
        class: "permission-popover-content-item"
    },
    G = {
        class: "permission-popover-content-item-title"
    },
    K = {
        class: "people-list"
    },
    R = {
        class: "person"
    },
    Y = {
        class: "person-avatar"
    },
    Z = ["src", "alt"],
    z = {
        class: "person-info"
    },
    Q = {
        class: "person-name"
    },
    X = {
        class: "person-email"
    },
    ee = {
        class: "person-role"
    },
    se = {
        class: "person-avatar default-avatar"
    },
    ie = {
        class: "person-info"
    },
    oe = {
        class: "person-email"
    },
    ne = {
        class: "person-role"
    },
    ae = ["onClick"],
    re = {
        class: "invite-form"
    },
    te = ["disabled"],
    le = ["disabled"],
    pe = {
        key: 1,
        class: "divider"
    },
    ve = {
        class: "icon link-icon"
    },
    ce = {
        class: "link-text"
    },
    de = o({
        __name: "PermissionPopover",
        props: {
            project: {
                type: Object,
                required: !1
            },
            isAsking: {
                type: Boolean,
                required: !1
            }
        },
        emits: ["permissionChanged"],
        setup(o, {
            expose: T,
            emit: de
        }) {
            const ue = o,
                me = de,
                _e = n(null),
                ye = n(""),
                he = O(),
                ke = k("currentUser"),
                {
                    project: fe,
                    isAsking: we
                } = a(ue),
                ge = n(0),
                be = n(0),
                {
                    t: Ce
                } = r(),
                je = () => {
                    Pe.value = !1
                };
            t((() => {
                document.addEventListener("click", je)
            })), l((() => {
                document.removeEventListener("click", je)
            }));
            let xe = null;
            const Pe = n(!1),
                $e = n(!0),
                Ae = () => {
                    const e = (() => {
                            try {
                                const e = localStorage.getItem("installedCustomTools");
                                if (e) return JSON.parse(e)
                            } catch (e) {}
                            return []
                        })(),
                        s = ["gmail", "calendar", "drive"];
                    if (Array.isArray(e)) return e.some((e => s.includes(e.toLowerCase())));
                    if ("object" == typeof e && null !== e) {
                        const i = [...Object.keys(e), ...Object.values(e)];
                        for (const e of i)
                            if ("string" == typeof e) {
                                const i = e.toLowerCase();
                                for (const o of s)
                                    if (i.includes(o)) return A.log("PermissionPopover: Found sensitive tool:", e, "contains", o), !0
                            }
                    }
                    return !1
                },
                Ee = () => Ae() ? (A.log("PermissionPopover: Forcing private mode due to sensitive tools"), !0) : $e.value;
            p((() => fe.value), (e => {
                fe.value && !fe.value.id && (fe.value.is_private = Ee(), A.log("project.value is_private", fe.value.is_private))
            }), {
                immediate: !0
            });
            const Le = L((async e => {
                    var s;
                    if (s = e.is_private, $e.value = s, fe.value && fe.value.id) try {
                        xe && xe.abort(), xe = new AbortController;
                        const s = xe.signal,
                            i = {
                                id: fe.value.id,
                                is_private: fe.value.is_private,
                                read_permissions: fe.value.read_permissions || [],
                                ...e
                            },
                            o = await fetch("/api/project/update", {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json"
                                },
                                body: JSON.stringify(i),
                                signal: s
                            });
                        0 !== (await o.json()).status ? he.error(Ce("components.permission_popover.update_permission_failed")) : ("is_private" in e && he.success(fe.value.is_private ? Ce("components.permission_popover.set_to_invite_only") : Ce("components.permission_popover.set_to_anyone_access")), "read_permissions" in e && he.success(Ce("components.permission_popover.access_permission_updated")), me("permissionChanged", {
                            isPublic: !fe.value.is_private
                        })), xe = null
                    } catch (i) {
                        "AbortError" !== i.name && he.error(Ce("components.permission_popover.update_permission_failed"))
                    }
                }), 2e3, {
                    trailing: !1
                }),
                Oe = () => {
                    ye.value && (fe.value.read_permissions || (fe.value.read_permissions = []), fe.value.read_permissions.includes(ye.value) ? he.warning(Ce("components.permission_popover.email_already_in_list")) : (fe.value.read_permissions.push(ye.value), Le({
                        read_permissions: fe.value.read_permissions
                    }), ye.value = ""))
                },
                Te = n(Ce("components.permission_popover.copy_share_link"));
            let Se = null;
            const We = () => {
                if (we.value) return;
                const e = `${window.location.origin}/agents?id=${fe.value.id}`;
                navigator.clipboard.writeText(e).then((() => {
                    Te.value = Ce("components.permission_popover.link_copied1"), Se && clearTimeout(Se), Se = setTimeout((() => {
                        Te.value = Ce("components.permission_popover.copy_share_link"), Se = null
                    }), 2e3)
                })).catch((() => {
                    he.error(Ce("components.permission_popover.copy_link_failed"))
                }))
            };
            return T({
                getDefaultIsPrivate: Ee,
                show: e => {
                    (e => {
                        const s = 460;
                        if (window.innerWidth <= 1220) {
                            be.value = A.isGensparkApp() ? 10 : 45;
                            const e = 30;
                            window.innerWidth >= s + e ? ge.value = window.innerWidth - s - e : ge.value = Math.max(14, (window.innerWidth - s) / 2)
                        } else if (e) {
                            const i = e.target.getBoundingClientRect();
                            ge.value = i.left - s + 28, be.value = i.top + 30
                        } else ge.value = window.innerWidth / 2 - 230, be.value = window.innerHeight / 2 - 250
                    })(e), Pe.value = !0
                },
                hide: () => {
                    Pe.value = !1
                }
            }), (o, n) => (s(), v($, {
                to: "body"
            }, [c(P, {
                name: "fade"
            }, {
                default: d((() => {
                    var a, r, t, l, p, v, d;
                    return [Pe.value ? (s(), e("div", {
                        key: 0,
                        class: h(["permission-popover", {
                            disabled: y(we)
                        }]),
                        style: x({
                            top: be.value + "px",
                            left: ge.value + "px"
                        }),
                        onClick: n[6] || (n[6] = j((() => {}), ["stop"])),
                        ref_key: "popover",
                        ref: _e
                    }, [i("div", W, [i("div", q, [m(_(o.$t("components.permission_popover.access_permission")) + " ", 1), y(we) ? (s(), e("div", B, n[7] || (n[7] = [i("div", {
                        class: "spinner"
                    }, null, -1)]))) : u("", !0)])]), i("div", F, [i("div", I, [i("div", M, _(o.$t("components.permission_popover.general_access")), 1), i("div", U, [i("div", {
                        class: h(["access-option", {
                            selected: !y(fe).is_private
                        }]),
                        onClick: n[1] || (n[1] = () => {
                            y(we) || (y(fe).is_private = !1, y(Le)({
                                is_private: y(fe).is_private
                            }))
                        })
                    }, [i("input", {
                        type: "radio",
                        id: "anyone-link",
                        name: "access-type",
                        checked: !y(fe).is_private,
                        disabled: y(we),
                        onChange: n[0] || (n[0] = () => {
                            y(we) || (y(fe).is_private = !1, y(Le)({
                                is_private: y(fe).is_private
                            }))
                        })
                    }, null, 40, H), i("label", J, _(o.$t("components.permission_popover.anyone_with_link")), 1)], 2), i("div", {
                        class: h(["access-option", {
                            selected: y(fe).is_private
                        }]),
                        onClick: n[4] || (n[4] = () => {
                            y(we) || (y(fe).is_private = !0, y(Le)({
                                is_private: y(fe).is_private
                            }))
                        })
                    }, [i("input", {
                        type: "radio",
                        id: "only-invited",
                        name: "access-type",
                        checked: y(fe).is_private,
                        disabled: y(we),
                        onChange: n[2] || (n[2] = () => {
                            y(we) || (y(fe).is_private = !0, y(Le)({
                                is_private: y(fe).is_private
                            }))
                        })
                    }, null, 40, N), i("label", V, _(o.$t("components.permission_popover.only_people_invited")), 1), u("", !0)], 2)])]), y(fe).is_private ? (s(), e("div", D, [i("div", G, _(o.$t("components.permission_popover.people_with_access")), 1), i("div", K, [i("div", R, [i("div", Y, [(null == (a = y(ke)) ? void 0 : a.avatar) ? (s(), e("img", {
                        key: 0,
                        src: null == (r = y(ke)) ? void 0 : r.avatar,
                        alt: null == (t = y(ke)) ? void 0 : t.name,
                        onerror: "this.src='/assets/avatar.png'"
                    }, null, 8, Z)) : (s(), e(f, {
                        key: 1
                    }, [m(_((null == (p = null == (l = y(ke)) ? void 0 : l.name) ? void 0 : p[0]) || "E"), 1)], 64))]), i("div", z, [i("div", Q, _((null == (v = y(ke)) ? void 0 : v.name) || "currentUser?.email") + " (you) ", 1), i("div", X, _(null == (d = y(ke)) ? void 0 : d.email), 1)]), i("div", ee, _(o.$t("components.permission_popover.owner")), 1)]), (s(!0), e(f, null, w(y(fe).read_permissions, ((o, a) => (s(), e("div", {
                        class: "person",
                        key: a
                    }, [i("div", se, [c(y(S))]), i("div", ie, [i("div", oe, [m(_(o) + " ", 1), n[8] || (n[8] = i("div", {
                        class: "view-only-badge"
                    }, [i("span", null, "View only")], -1))])]), i("div", ne, [i("div", {
                        class: "delete-icon",
                        onClick: e => y(we) ? null : (e => {
                            fe.value && fe.value.read_permissions && (fe.value.read_permissions = fe.value.read_permissions.filter((s => s !== e)), Le({
                                read_permissions: fe.value.read_permissions
                            }))
                        })(o)
                    }, " × ", 8, ae)])])))), 128))]), i("div", re, [g(i("input", {
                        type: "email",
                        placeholder: "Email",
                        class: "invite-input",
                        disabled: y(we),
                        "onUpdate:modelValue": n[5] || (n[5] = e => ye.value = e),
                        onKeydown: C(Oe, ["enter"])
                    }, null, 40, te), [
                        [b, ye.value]
                    ]), i("button", {
                        class: h(["invite-button", {
                            active: ye.value
                        }]),
                        disabled: y(we),
                        onClick: Oe
                    }, _(o.$t("components.permission_popover.invite")), 11, le)])])) : u("", !0), y(fe).is_private ? u("", !0) : (s(), e("hr", pe)), y(fe).is_private ? u("", !0) : (s(), e("div", {
                        key: 2,
                        class: "share-link-button",
                        onClick: We
                    }, [i("div", ve, [c(y(E))]), i("div", ce, _(Te.value), 1)]))])], 6)) : u("", !0)]
                })),
                _: 1
            })]))
        }
    }, [
        ["__scopeId", "data-v-94412cae"]
    ]);
export {
    de as P
};