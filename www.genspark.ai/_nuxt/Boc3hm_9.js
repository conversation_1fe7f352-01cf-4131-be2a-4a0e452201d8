import {
    d as e,
    o as t,
    b as a,
    cZ as n,
    a2 as i,
    _ as o,
    c as r,
    C as l,
    r as s,
    s as c,
    h as _,
    a3 as g,
    i as m,
    f as p,
    l as u,
    y as d,
    a as f,
    e as v,
    t as b,
    w as h,
    p as w
} from "./Cf0SOiw0.js";
import {
    D as y
} from "./WhweajiO.js";
import {
    C as x
} from "./BrcpPT-Q.js";
import {
    M as k
} from "./Dc8Bac8D.js";
import {
    R as A
} from "./BUjMLq-a.js";
import {
    D as C,
    F as q,
    G as L,
    R,
    I as j
} from "./BN-NNxvY.js";
import {
    _ as B
} from "./CAmLbDGM.js";
import {
    N as U
} from "./nuQnue4a.js";
import {
    u as D
} from "./B6noBY_5.js";
const E = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAQSSURBVHgB5VvRdeIwEBx49x9KUCqAElQCJZAKIBXAVQBXAXQAqcBcBUAFdiogqYDTGMQZYRvbWoOTzHuTC7a1p9GuVivFtFAfOobaUBl2DXuna8p5Ljrxw3BnuDbcnj43HhQ0NgwMD56kjRGuB6gR0JARmSdeowHQqFeoy9CwjwdA4b5CXc5xx1AfGu7xOLFJbw9QI5iQpgAODeMUNYBiNwAODSX7piAEhWP4NFVsMsQVfojYpOgOPPDQMFZKVWm3qSr64QlquVxWFV06kQ18OyvBzWZzGAwGVduPUBAKAvO23+8fOp1O5fZsS8xms6o29iiYxOZVOwk578QDRgRB4NOPADfQ9zB+QU/vxEIttNY+fdF5gkMPw2f2er24o6vVqnRbhvJ0Oj0kEYahj+ggS6z2EZkkQ5nY7/eFBDJ8R6NRnJXZJgucJhwMPs9BLdEnnSY4kBI8n8/Pnby1rFAowzdPaJpw/h8llqzAFaukxJIMQQsKKtqOAhgdyfYWHBSGtUfmvyhGRlJi2Sm3o2VtUHhS9GQykejbJCk4EDB45V2LKgmHkUHQnlDfAiu2I2TwKrtacH6WTDLnwqNKps9hHNbax4jNskwkt8BEU0Y4bZbJAQXY/4WSp4FmfsGEKLrdLkznYxrRhdqahBQziiKs12vsdjtst9uYHx/Xx9D2viAUfyxRYHRsMVEX0kKXGdunHk/hvIUSZ770LL2Z9HBR77pIetn+nuZlYcThEsJj1Oj5xWKRmp1dcIninBT2WhmGkDLGtZOlYRaEk48PZQ0mdzkWQoVDMwW7lZZP4eCzn76bYNfLPqHscaaVyTaOf5sVxdvb2/l3Zt+q4CrAFUEQEX+IH8XaNbvIfjiLw+EwtsFqSzCrBxS8EDJ2QbsMlWljDwLcxMc8wLKU9z1DfMWQFq3dLFgSsqgoA1t4uAUIi5vPz8/4X8/iJNYqdnCXJIsRn4Rlj4noXcGQ1vEAChm7Ck/fDOt71JvCcx0cCBoVI495y+6jcxhQaPsk+C8aCC5pgtvDeK1snT7Q1Xt8bzwbRtbDTH1rfF+skVJgadx5jt6R2opsOaID5BwGsNQbj8eVN/114OXlJT5AyAFvPmfd1MgZqbSt36PBCgz53r14ka3tCF7je83lheEqeaGd8tALvsibrDcQGf52L7aLPkjc4ZCtNFhjZ4AaIvfir4yHZzie4Q6TF19fX/H+/o6npyc0ARRrqrG0W39wDOdSaPrbd1ncwAMUHQI4fBGyrwqeUPhBrx4mRTf95dJaqqEZgEPDmJq1JDFAM0Kcu7vCb9v5QqGmw7+CDPCgb7uwTg1xX6EaDYDGD/kajwuF47ySEE8bEwhn3xbqAzvaw9EzXfz/Gp5ynotw3Kzw8Gp3+rxGTRuYf4aIQqDPEhPuAAAAAElFTkSuQmCC",
    H = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const M = {
        render: function(n, i) {
            return t(), e("svg", H, i[0] || (i[0] = [a("path", {
                "fill-rule": "evenodd",
                "clip-rule": "evenodd",
                d: "M4.59961 2.74414C3.08083 2.74414 1.84961 3.97536 1.84961 5.49414V11.3239C1.84961 11.7381 2.1854 12.0739 2.59961 12.0739C3.01382 12.0739 3.34961 11.7381 3.34961 11.3239V5.49414C3.34961 4.80378 3.90925 4.24414 4.59961 4.24414H10.0173C10.4315 4.24414 10.7673 3.90835 10.7673 3.49414C10.7673 3.07993 10.4315 2.74414 10.0173 2.74414H4.59961ZM7.07227 5.2168C5.55348 5.2168 4.32227 6.44802 4.32227 7.9668V15.5054C4.32227 17.0242 5.55348 18.2554 7.07227 18.2554H14.6109C16.1297 18.2554 17.3609 17.0242 17.3609 15.5054V7.9668C17.3609 6.44801 16.1297 5.2168 14.6109 5.2168H7.07227ZM5.82227 7.9668C5.82227 7.27644 6.38191 6.7168 7.07227 6.7168H14.6109C15.3012 6.7168 15.8609 7.27644 15.8609 7.9668V15.5054C15.8609 16.1958 15.3012 16.7554 14.6109 16.7554H7.07227C6.38191 16.7554 5.82227 16.1958 5.82227 15.5054V7.9668Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    Q = {
        width: "16",
        height: "16",
        viewBox: "0 0 16 16",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const F = {
        render: function(n, i) {
            return t(), e("svg", Q, i[0] || (i[0] = [a("path", {
                d: "M3 4.5C3 3.67157 3.67157 3 4.5 3H11.5C12.3284 3 13 3.67157 13 4.5V9.5C13 10.3284 12.3284 11 11.5 11H8.5L6 13.5V11H4.5C3.67157 11 3 10.3284 3 9.5V4.5Z",
                stroke: "currentColor",
                "stroke-width": "1.2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), a("path", {
                d: "M5.5 6H10.5",
                stroke: "currentColor",
                "stroke-width": "1.2",
                "stroke-linecap": "round"
            }, null, -1), a("path", {
                d: "M5.5 8H8.5",
                stroke: "currentColor",
                "stroke-width": "1.2",
                "stroke-linecap": "round"
            }, null, -1)]))
        }
    },
    O = "" + new URL("bria-icon.BHasLJ0-.jpg",
        import.meta.url).href,
    V = "" + new URL("meta-icon.B91jfMkt.png",
        import.meta.url).href,
    S = "" + new URL("iclight-icon.XQPHnTpm.png",
        import.meta.url).href,
    G = "" + new URL("moa_image_preview_cartoon.BDJEz38H.png",
        import.meta.url).href,
    P = "" + new URL("moa_image_preview_watercolor.Bgke2qmq.png",
        import.meta.url).href,
    T = "" + new URL("moa_image_preview_anime.BkBL6SS8.png",
        import.meta.url).href,
    X = "" + new URL("moa_image_preview_oil_painting.BSDFCb2c.png",
        import.meta.url).href,
    Z = "" + new URL("moa_image_preview_3D.DbOpejUw.png",
        import.meta.url).href,
    I = "" + new URL("moa_image_preview_minimalist.8hLQaCaA.png",
        import.meta.url).href,
    z = "" + new URL("moa_image_preview_pop_art.D1vhYOSD.png",
        import.meta.url).href,
    J = "" + new URL("moa_image_preview_realistic.CdA7Sgyl.png",
        import.meta.url).href,
    Y = "" + new URL("moa_image_preview_auto.DRZHPGWi.png",
        import.meta.url).href,
    K = "" + new URL("byteplus.DpZM8Rzz.png",
        import.meta.url).href,
    N = [{
        name: "auto",
        icon: E,
        label: e => e("components.generate_image_content.moa_label"),
        description: e => e("components.generate_image_content.moa_description"),
        divider: !0
    }, {
        name: "gpt-image-1",
        icon: C,
        label: "GPT Image",
        generating_bg_color: "#c9d9ba",
        speed: "slow",
        quality: "excellent",
        price_level: "premium",
        capabilities: ["text_to_image", "image_editing", "multi_image"],
        features: ["text_rendering", "complex_editing", "versatile"],
        best_for: ["image_editing", "complex_scenes", "images_with_text"],
        tip: e => e("components.generate_image_content.gpt_image_features"),
        description: e => e("components.generate_image_content.gpt_image_description")
    }, {
        name: "flux-pro/ultra",
        icon: q,
        label: e => e("components.generate_image_content.flux_pro_ultra_label"),
        generating_bg_color: "#bac9d9",
        speed: "fast",
        quality: "high",
        price_level: "moderate",
        capabilities: ["text_to_image"],
        features: ["fast_generation", "stable_output", "diverse_styles"],
        best_for: ["quick_generation", "artistic_styles", "creative_images"],
        tip: e => e("components.generate_image_content.flux_ultra_features"),
        description: e => e("components.generate_image_content.flux_ultra_description")
    }, {
        name: "flux-pro/kontext/pro",
        icon: q,
        label: "FLUX.1 Kontext Pro",
        new: !0,
        generating_bg_color: "#bac9d9",
        speed: "very_fast",
        quality: "high",
        price_level: "moderate",
        capabilities: ["text_to_image", "image_editing", "context_aware"],
        features: ["context_editing", "fast_processing", "english_optimized"],
        best_for: ["reference_editing", "style_transfer", "quick_modifications"],
        tip: e => e("components.generate_image_content.kontext_pro_features"),
        description: e => e("components.generate_image_content.kontext_pro_description")
    }, {
        name: "flux-pro/kontext/max",
        icon: q,
        label: "FLUX.1 Kontext Max",
        new: !0,
        generating_bg_color: "#bac9d9",
        speed: "medium",
        quality: "excellent",
        price_level: "premium",
        capabilities: ["text_to_image", "image_editing", "context_aware"],
        features: ["highest_quality", "fine_control", "professional_editing"],
        best_for: ["high_quality_editing", "professional_work", "detailed_control"],
        tip: e => e("components.generate_image_content.kontext_max_features"),
        description: e => e("components.generate_image_content.kontext_max_description")
    }, {
        name: "imagen4",
        icon: L,
        label: "Gemini Imagen 4 Preview",
        new: !0,
        generating_bg_color: "#d9d0ba",
        speed: "medium",
        quality: "excellent",
        price_level: "moderate",
        capabilities: ["text_to_image"],
        features: ["latest_technology", "high_quality", "google_ai"],
        best_for: ["latest_experience", "realistic_images", "high_quality_content"],
        tip: e => e("components.generate_image_content.imagen4_features"),
        description: e => e("components.generate_image_content.imagen4_description")
    }, {
        name: "imagen3",
        icon: L,
        label: e => e("components.generate_image_content.imagen_3_label"),
        generating_bg_color: "#d9d0ba",
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        capabilities: ["text_to_image"],
        features: ["safety_filters", "natural_style", "google_quality"],
        best_for: ["photorealistic_style", "safe_content", "natural_images"],
        tip: e => e("components.generate_image_content.imagen3_features"),
        description: e => e("components.generate_image_content.imagen3_description")
    }, {
        name: "recraft-v3",
        icon: R,
        label: e => e("components.generate_image_content.recraft_v3_label"),
        generating_bg_color: "#d9c9ba",
        speed: "fast",
        quality: "high",
        price_level: "moderate",
        capabilities: ["text_to_image", "style_control"],
        features: ["photorealistic_specialty", "style_control", "commercial_use"],
        best_for: ["realistic_photos", "commercial_images", "product_images"],
        tip: e => e("components.generate_image_content.recraft_features"),
        description: e => e("components.generate_image_content.recraft_description")
    }, {
        name: "fal-ai/bytedance/seedream/v3/text-to-image",
        icon: K,
        label: "Bytedance SeedDream v3",
        generating_bg_color: "#c2bad9",
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        capabilities: ["text_to_image", "high_resolution"],
        features: ["2k_resolution", "text_layout", "chinese_friendly"],
        best_for: ["high_resolution", "images_with_text", "chinese_content"],
        tip: e => e("components.generate_image_content.seedream_features"),
        description: e => e("components.generate_image_content.seedream_description"),
        divider: !0
    }, {
        name: "flux",
        icon: q,
        label: e => e("components.generate_image_content.flux_label"),
        generating_bg_color: "#bac9d9",
        speed: "medium",
        quality: "high",
        price_level: "budget",
        capabilities: ["text_to_image"],
        features: ["reliable_generation", "good_quality", "versatile"],
        best_for: ["general_purpose", "reliable_results", "everyday_use"],
        tip: e => e("components.generate_image_content.flux_features")
    }, {
        name: "flux-speed",
        icon: q,
        label: e => e("components.generate_image_content.flux_speed_label"),
        generating_bg_color: "#bac9d9",
        speed: "very_fast",
        quality: "medium",
        price_level: "budget",
        capabilities: ["text_to_image"],
        features: ["fast_generation", "quick_results"],
        best_for: ["quick_prototyping", "fast_iterations", "speed_priority"],
        tip: e => e("components.generate_image_content.flux_speed_features")
    }, {
        name: "ideogram",
        icon: j,
        label: e => e("components.generate_image_content.ideogram_label"),
        generating_bg_color: "#bad6d9",
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        capabilities: ["text_to_image"],
        features: ["text_rendering", "creative_design"],
        best_for: ["logos", "text_in_images", "creative_design"],
        tip: e => e("components.generate_image_content.ideogram_features")
    }, {
        name: "ideogram/V_2A",
        icon: j,
        label: e => e("components.generate_image_content.ideogram_2a_label"),
        generating_bg_color: "#bad6d9",
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        capabilities: ["text_to_image"],
        features: ["improved_text", "enhanced_quality"],
        best_for: ["text_heavy_images", "improved_results", "design_work"],
        tip: e => e("components.generate_image_content.ideogram_2a_features")
    }, {
        name: "dalle-3",
        icon: C,
        label: e => e("components.generate_image_content.dalle_3_label"),
        generating_bg_color: "#c9d9ba",
        speed: "medium",
        quality: "high",
        price_level: "expensive",
        capabilities: ["text_to_image"],
        features: ["openai_quality", "creative_interpretation"],
        best_for: ["creative_images", "artistic_content", "unique_concepts"],
        tip: e => e("components.generate_image_content.dalle3_features")
    }, {
        name: "gemini-flash-2.0",
        icon: L,
        label: "Gemini Flash 2.0 - (Experimental)",
        generating_bg_color: "#d9d0ba",
        speed: "fast",
        quality: "high",
        price_level: "moderate",
        capabilities: ["text_to_image"],
        features: ["experimental", "fast_generation"],
        best_for: ["experimental_features", "quick_results"],
        tip: e => e("components.generate_image_content.gemini_flash_features")
    }, {
        name: "flux-subject",
        icon: q,
        label: "FLUX.1 Subject [schnell]",
        is_i2i: !0,
        generating_bg_color: "#bac9d9",
        speed: "fast",
        quality: "high",
        price_level: "moderate",
        capabilities: ["image_to_image", "subject_control"],
        features: ["subject_consistency", "fast_processing"],
        best_for: ["subject_variations", "character_consistency"],
        tip: e => e("components.generate_image_content.flux_subject_features")
    }, {
        name: "flux-pro/v1.1-ultra/redux",
        icon: q,
        label: "FLUX 1.1 [pro] [ultra] Redux",
        is_i2i: !0,
        generating_bg_color: "#bac9d9",
        speed: "medium",
        quality: "excellent",
        price_level: "moderate",
        capabilities: ["image_to_image", "style_transfer"],
        features: ["style_control", "high_quality"],
        best_for: ["style_variations", "image_enhancement"],
        tip: e => e("components.generate_image_content.flux_redux_features")
    }, {
        name: "fal-ai/recraft-clarity-upscale",
        icon: R,
        label: "Recraft Clarity Upscale",
        hidden: !0,
        is_i2i: !0,
        generating_bg_color: "#d9c9ba",
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        capabilities: ["image_upscale"],
        features: ["detail_enhancement", "resolution_increase"],
        best_for: ["image_enlargement", "detail_enhancement"],
        tip: e => e("components.generate_image_content.upscale_features")
    }, {
        name: "rmbg",
        icon: O,
        label: "Bria Background Remover",
        hidden: !0,
        is_i2i: !0,
        generating_bg_color: "#f0e6d2",
        speed: "fast",
        quality: "excellent",
        price_level: "budget",
        capabilities: ["background_removal"],
        features: ["precise_edges", "professional_results"],
        best_for: ["remove_backgrounds", "transparent_images"],
        tip: e => e("components.generate_image_content.rmbg_features")
    }, {
        name: "segment",
        icon: V,
        label: "Segment",
        hidden: !0,
        is_i2i: !0,
        generating_bg_color: "#e8f0ff",
        speed: "fast",
        quality: "high",
        price_level: "budget",
        capabilities: ["image_segmentation"],
        features: ["precise_extraction", "object_isolation"],
        best_for: ["extract_subjects", "background_separation"],
        tip: e => e("components.generate_image_content.segment_features")
    }, {
        name: "fal-ai/iclight-v2",
        icon: S,
        label: "ICLight",
        hidden: !0,
        is_i2i: !0,
        generating_bg_color: "#fff8e1",
        speed: "medium",
        quality: "high",
        price_level: "expensive",
        capabilities: ["lighting_control"],
        features: ["lighting_adjustment", "professional_lighting"],
        best_for: ["lighting_effects", "portrait_enhancement"],
        tip: e => e("components.generate_image_content.iclight_features")
    }, {
        name: "flux-tryon",
        icon: q,
        label: "Flux Tryon",
        hidden: !0,
        is_i2i: !0,
        generating_bg_color: "#bac9d9",
        speed: "medium",
        quality: "high",
        price_level: "budget",
        capabilities: ["virtual_tryon"],
        features: ["clothing_tryon", "realistic_fitting"],
        best_for: ["virtual_fashion", "clothing_preview"],
        tip: e => e("components.generate_image_content.tryon_features")
    }, {
        name: "flux-pro/outpaint",
        icon: q,
        label: "Flux Pro Fill",
        hidden: !0,
        is_i2i: !0,
        generating_bg_color: "#bac9d9",
        speed: "fast",
        quality: "high",
        price_level: "moderate",
        capabilities: ["image_extension"],
        features: ["seamless_extension", "aspect_ratio_change"],
        best_for: ["extend_images", "change_aspect_ratio"],
        tip: e => e("components.generate_image_content.outpaint_features")
    }],
    W = e => [{
        label: e("components.generate_image_content.style_auto"),
        value: "auto",
        preview: Y
    }, {
        label: e("components.generate_image_content.style_realistic"),
        value: "realistic_image",
        preview: J
    }, {
        label: e("components.generate_image_content.style_cartoon"),
        value: "cartoon",
        group: "digital",
        preview: G
    }, {
        label: e("components.generate_image_content.style_watercolor"),
        value: "watercolor",
        group: "digital",
        preview: P
    }, {
        label: e("components.generate_image_content.style_anime"),
        value: "anime",
        group: "digital",
        preview: T
    }, {
        label: e("components.generate_image_content.style_oil_painting"),
        value: "oil_painting",
        group: "digital",
        preview: X
    }, {
        label: e("components.generate_image_content.style_3d"),
        value: "3d",
        group: "digital",
        preview: Z
    }, {
        label: e("components.generate_image_content.style_minimalist"),
        value: "minimalist",
        group: "digital",
        preview: I
    }, {
        label: e("components.generate_image_content.style_pop_art"),
        value: "pop_art",
        group: "digital",
        preview: z
    }];
var $, ee = {
    exports: {}
};
var te, ae = ($ || ($ = 1, te = ee, function() {
    function e(e, t) {
        return void 0 === t ? t = {
            autoBom: !1
        } : "object" != typeof t && (t = {
            autoBom: !t
        }), t.autoBom && /^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type) ? new Blob(["\ufeff", e], {
            type: e.type
        }) : e
    }

    function t(e, t, a) {
        var n = new XMLHttpRequest;
        n.open("GET", e), n.responseType = "blob", n.onload = function() {
            l(n.response, t, a)
        }, n.onerror = function() {}, n.send()
    }

    function a(e) {
        var t = new XMLHttpRequest;
        t.open("HEAD", e, !1);
        try {
            t.send()
        } catch (a) {}
        return 200 <= t.status && 299 >= t.status
    }

    function i(e) {
        try {
            e.dispatchEvent(new MouseEvent("click"))
        } catch (a) {
            var t = document.createEvent("MouseEvents");
            t.initMouseEvent("click", !0, !0, window, 0, 0, 0, 80, 20, !1, !1, !1, !1, 0, null), e.dispatchEvent(t)
        }
    }
    var o = "object" == typeof window && window.window === window ? window : "object" == typeof self && self.self === self ? self : "object" == typeof n && n.global === n ? n : void 0,
        r = o.navigator && /Macintosh/.test(navigator.userAgent) && /AppleWebKit/.test(navigator.userAgent) && !/Safari/.test(navigator.userAgent),
        l = o.saveAs || ("object" != typeof window || window !== o ? function() {} : "download" in HTMLAnchorElement.prototype && !r ? function(e, n, r) {
            var l = o.URL || o.webkitURL,
                s = document.createElement("a");
            n = n || e.name || "download", s.download = n, s.rel = "noopener", "string" == typeof e ? (s.href = e, s.origin === location.origin ? i(s) : a(s.href) ? t(e, n, r) : i(s, s.target = "_blank")) : (s.href = l.createObjectURL(e), setTimeout((function() {
                l.revokeObjectURL(s.href)
            }), 4e4), setTimeout((function() {
                i(s)
            }), 0))
        } : "msSaveOrOpenBlob" in navigator ? function(n, o, r) {
            if (o = o || n.name || "download", "string" != typeof n) navigator.msSaveOrOpenBlob(e(n, r), o);
            else if (a(n)) t(n, o, r);
            else {
                var l = document.createElement("a");
                l.href = n, l.target = "_blank", setTimeout((function() {
                    i(l)
                }))
            }
        } : function(e, a, n, i) {
            if ((i = i || open("", "_blank")) && (i.document.title = i.document.body.innerText = "downloading..."), "string" == typeof e) return t(e, a, n);
            var l = "application/octet-stream" === e.type,
                s = /constructor/i.test(o.HTMLElement) || o.safari,
                c = /CriOS\/[\d]+/.test(navigator.userAgent);
            if ((c || l && s || r) && "undefined" != typeof FileReader) {
                var _ = new FileReader;
                _.onloadend = function() {
                    var e = _.result;
                    e = c ? e : e.replace(/^data:[^;]*;/, "data:attachment/file;"), i ? i.location.href = e : location = e, i = null
                }, _.readAsDataURL(e)
            } else {
                var g = o.URL || o.webkitURL,
                    m = g.createObjectURL(e);
                i ? i.location = m : location.href = m, i = null, setTimeout((function() {
                    g.revokeObjectURL(m)
                }), 4e4)
            }
        });
    o.saveAs = l.saveAs = l, te.exports = l
}()), ee.exports);
const ne = i(ae),
    ie = {
        key: 0,
        class: "modal-container"
    },
    oe = {
        class: "close-button-wrapper"
    },
    re = ["src"],
    le = {
        class: "mobile-title-bar"
    },
    se = {
        class: "image-left"
    },
    ce = ["src", "alt"],
    _e = {
        class: "image-right"
    },
    ge = {
        class: "image-right-top"
    },
    me = {
        class: "prompt-wrapper"
    },
    pe = {
        class: "info"
    },
    ue = {
        class: "info-wrapper"
    },
    de = {
        class: "model-icon-wrapper"
    },
    fe = ["src"],
    ve = {
        class: "model-name-wrapper"
    },
    be = {
        key: 0,
        class: "info-wrapper style-label"
    },
    he = {
        class: "info-wrapper"
    },
    we = {
        key: 1,
        class: "info-wrapper auto-prompt-label"
    },
    ye = {
        class: "action-buttons"
    },
    xe = {
        class: "image-right-bottom"
    },
    ke = o({
        __name: "MOAImageDetail",
        props: {
            imageData: {
                type: Object,
                required: !0
            }
        },
        emits: ["close", "add-image-url", "navigate"],
        setup(n, {
            emit: i
        }) {
            const o = r((() => {
                    var e;
                    return null == (e = l) ? void 0 : e.isGensparkAppAndroid()
                })),
                C = s(!1),
                q = m("currentUser"),
                L = n;
            s(null).value = l.isDarkMode();
            const R = i,
                j = s(!0),
                E = D(),
                H = s({
                    width: 0,
                    height: 0
                }),
                Q = s(""),
                {
                    t: O
                } = c(),
                V = () => {
                    j.value = !1, R("close")
                },
                S = async () => {
                    E.info(O("components.moa_image_detail.downloading"));
                    try {
                        const e = e => new Promise(((t, a) => {
                                const n = new Image;
                                n.crossOrigin = "anonymous", n.onload = () => t(n), n.onerror = () => a(new Error("Image loading failed"));
                                (new Date).getTime();
                                Q.value = Q.value ? Q.value : `${e}${e.includes("?")?"&":"?"}_t=buster`, n.src = Q.value
                            })),
                            t = await e($.value),
                            a = document.createElement("canvas");
                        a.width = t.width, a.height = t.height;
                        a.getContext("2d").drawImage(t, 0, 0);
                        const n = await new Promise((e => a.toBlob(e, "image/png"))),
                            i = I.value.prompt.slice(0, 50).replace(/[^\w\s-]/g, "").replace(/\s+/g, "_");
                        ne.saveAs(n, `${i}-${Date.now()}.png`)
                    } catch (e) {}
                },
                G = () => {
                    navigator.clipboard.writeText(I.value.prompt), E.success(O("components.moa_image_detail.copy_success"))
                },
                P = e => {
                    "Escape" === e.key && V()
                },
                T = () => {
                    var e;
                    V();
                    const t = {
                        url: I.value.url,
                        model: I.value.model
                    };
                    (null == (e = I.value.cdn_urls) ? void 0 : e.length) > 1 && (t.url = I.value.cdn_urls[1]), R("add-image-url", t)
                },
                X = () => {
                    window.open($.value, "_blank")
                },
                Z = e => {
                    H.value = {
                        width: e.target.naturalWidth,
                        height: e.target.naturalHeight
                    }
                };
            _((() => {
                window.addEventListener("keydown", P)
            })), g((() => {
                window.removeEventListener("keydown", P)
            }));
            const I = s({
                prompt: (z = L.imageData).prompt,
                model: z.model || z.model_name || "",
                style: z.style || "",
                reflection_enabled: z.reflection_enabled || !1,
                url: z.original || z.link || z.url || "",
                url_nowatermark: z.url_nowatermark || "",
                cdn_urls: z.cnd_src_sets || z.src_set || []
            });
            var z;
            const J = r((() => {
                    const e = N.find((e => e.name.toLowerCase() === I.value.model.toLowerCase()));
                    return (null == e ? void 0 : e.icon) || null
                })),
                Y = r((() => e => {
                    const t = I.value.model.toLowerCase(),
                        a = N.find((e => e.name.toLowerCase() === t));
                    return (null == a ? void 0 : a.label) ? "function" == typeof a.label ? a.label(O) : a.label : I.value.model
                })),
                K = r((() => {
                    if (!I.value.style) return "";
                    const e = W(O).find((e => e.value === I.value.style));
                    return e ? e.label : null
                })),
                $ = r((() => {
                    if (!q.value) return "";
                    return ["plus", "gold", "diamond"].includes(q.value.plan) && I.value.url_nowatermark ? I.value.url_nowatermark : I.value.url
                }));
            return r((() => (l.log("currentUser:", q.value), !l.isGensparkApp() && !(!q.value || !q.value.gk_realtime_dogfood)))), (n, i) => j.value ? (t(), e("div", ie, [a("div", {
                class: "overlay",
                onClick: V
            }, [a("div", oe, [a("img", {
                src: d(x),
                onClick: u(V, ["stop"]),
                class: "icon"
            }, null, 8, re)])]), a("div", {
                class: "image-display",
                onTouchmove: i[2] || (i[2] = u((() => {}), ["stop"]))
            }, [a("div", le, [a("div", {
                class: "back-button",
                onClick: V
            }, [v(d(k), {
                class: "icon"
            })])]), a("div", se, [$.value ? (t(), e("img", {
                key: 0,
                src: $.value,
                alt: I.value.prompt,
                onClick: X,
                onLoad: Z,
                class: "clickable-image"
            }, null, 40, ce)) : p("", !0)]), a("div", _e, [a("div", ge, [a("div", me, [a("p", null, b(I.value.prompt), 1)]), a("div", pe, [a("div", ue, [a("div", de, [a("img", {
                src: J.value,
                class: "model-icon"
            }, null, 8, fe)]), a("div", ve, b(Y.value(I.value.model)), 1)]), I.value.style && K.value ? (t(), e("div", be, b(K.value), 1)) : p("", !0), a("div", he, b(H.value.width) + " x " + b(H.value.height), 1), I.value.reflection_enabled ? (t(), e("div", we, b(d(O)("components.generate_image_content.auto_prompt")), 1)) : p("", !0)]), a("div", ye, [v(d(U), {
                trigger: "hover"
            }, {
                trigger: h((() => [a("div", {
                    class: "button-wrapper",
                    onClick: S
                }, [v(d(y), {
                    class: "icon"
                })])])),
                default: h((() => [w(" " + b(d(O)("components.moa_image_detail.download_image")), 1)])),
                _: 1
            }), v(d(U), {
                trigger: "hover"
            }, {
                trigger: h((() => [a("div", {
                    class: "button-wrapper",
                    onClick: G
                }, [v(d(M), {
                    class: "icon"
                })])])),
                default: h((() => [w(" " + b(d(O)("components.moa_image_detail.copy_prompt")), 1)])),
                _: 1
            }), v(d(U), {
                trigger: "hover"
            }, {
                trigger: h((() => [o.value ? (t(), e("div", {
                    key: 0,
                    class: "button-wrapper",
                    onClick: i[0] || (i[0] = e => C.value = !0)
                }, [v(d(F), {
                    class: "icon"
                })])) : p("", !0)])),
                default: h((() => [w(" " + b(d(O)("pages.channel.feedback")), 1)])),
                _: 1
            })])]), a("div", xe, [a("div", {
                class: "remix-button",
                onClick: T
            }, [v(d(A)), w(b(d(O)("components.generate_image_content.remix")), 1)])])]), o.value ? (t(), f(B, {
                key: 0,
                show: C.value,
                "onUpdate:show": i[1] || (i[1] = e => C.value = e)
            }, null, 8, ["show"])) : p("", !0)], 32)])) : p("", !0)
        }
    }, [
        ["__scopeId", "data-v-164828a5"]
    ]),
    Ae = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Ce = {
        render: function(n, i) {
            return t(), e("svg", Ae, i[0] || (i[0] = [a("path", {
                d: "M5.83398 7.91602C6.52434 7.91602 7.08398 7.35637 7.08398 6.66602C7.08398 5.97566 6.52434 5.41602 5.83398 5.41602C5.14363 5.41602 4.58398 5.97566 4.58398 6.66602C4.58398 7.35637 5.14363 7.91602 5.83398 7.91602Z",
                stroke: "currentColor",
                "stroke-width": "1.25",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), a("path", {
                d: "M17.4974 9.16667C17.4993 9.55842 17.4993 9.97442 17.4993 10.4167C17.4993 14.1486 17.4993 16.0146 16.34 17.174C15.1806 18.3333 13.3146 18.3333 9.58268 18.3333C5.85073 18.3333 3.98476 18.3333 2.82538 17.174C1.66602 16.0146 1.66602 14.1486 1.66602 10.4167C1.66602 6.68472 1.66602 4.81874 2.82538 3.65937C3.98476 2.5 5.85073 2.5 9.58268 2.5C10.0249 2.5 10.4409 2.5 10.8327 2.50193",
                stroke: "currentColor",
                "stroke-width": "1.25",
                "stroke-linecap": "round"
            }, null, -1), a("path", {
                "fill-rule": "evenodd",
                "clip-rule": "evenodd",
                d: "M14.375 7.29102C14.375 7.63619 14.6548 7.91602 15 7.91602C15.3452 7.91602 15.625 7.63619 15.625 7.29102V5.41602H17.5C17.8452 5.41602 18.125 5.13619 18.125 4.79102C18.125 4.44584 17.8452 4.16602 17.5 4.16602H15.625V2.29102C15.625 1.94584 15.3452 1.66602 15 1.66602C14.6548 1.66602 14.375 1.94584 14.375 2.29102V4.16602H12.5C12.1548 4.16602 11.875 4.44584 11.875 4.79102C11.875 5.13619 12.1548 5.41602 12.5 5.41602H14.375V7.29102Z",
                fill: "currentColor"
            }, null, -1), a("path", {
                d: "M3.75 17.916C7.39372 13.5618 11.4784 7.81936 17.4979 11.7013",
                stroke: "currentColor",
                "stroke-width": "1.25"
            }, null, -1)]))
        }
    },
    qe = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 32 32"
    };
const Le = {
    render: function(n, i) {
        return t(), e("svg", qe, i[0] || (i[0] = [a("path", {
            d: "M30 3.414L28.586 2L2 28.586L3.414 30l2-2H26a2.003 2.003 0 0 0 2-2V5.414zM26 26H7.414l7.793-7.793l2.379 2.379a2 2 0 0 0 2.828 0L22 19l4 3.997zm0-5.832l-2.586-2.586a2 2 0 0 0-2.828 0L19 19.168l-2.377-2.377L26 7.414z",
            fill: "currentColor"
        }, null, -1), a("path", {
            d: "M6 22v-3l5-4.997l1.373 1.374l1.416-1.416l-1.375-1.375a2 2 0 0 0-2.828 0L6 16.172V6h16V4H6a2.002 2.002 0 0 0-2 2v16z",
            fill: "currentColor"
        }, null, -1)]))
    }
};
export {
    Y as A, K as B, M as C, ne as F, E as M, Le as N, X as O, z as P, J as R, Z as T, Ce as U, P as W, ke as _, F as a, ae as b, G as c, T as d, I as e, N as m, W as s
};