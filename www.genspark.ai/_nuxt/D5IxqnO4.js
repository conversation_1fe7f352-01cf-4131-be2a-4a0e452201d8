import {
    W as e
} from "./Cf0SOiw0.js";
const t = e("editorSelection", {
    state: () => ({
        inspectMode: !1,
        selectedElement: null,
        hoveredElement: null,
        selectedElementPath: "",
        currentBlockElement: null,
        textSelection: {
            range: null,
            text: "",
            startContainer: null,
            endContainer: null,
            startOffset: 0,
            endOffset: 0,
            commonAncestorContainer: null
        },
        activeSelectionType: "none",
        vditorSelection: {
            text: "",
            range: null,
            startPos: 0,
            endPos: 0,
            editorInstance: null
        },
        selectionMetadata: {
            timestamp: null,
            source: ""
        },
        editorType: "html",
        formatState: {
            bold: !1,
            italic: !1,
            underline: !1,
            strikethrough: !1
        }
    }),
    getters: {
        hasElementSelection: e => null !== e.selectedElement,
        hasTextSelection: e => "" !== e.textSelection.text,
        hasVditorSelection: e => "" !== e.vditorSelection.text,
        hasAnySelection: e => null !== e.selectedElement || "" !== e.textSelection.text || "" !== e.vditorSelection.text,
        selectionInfo: e => e.selectedElement ? {
            type: "element",
            element: e.selectedElement,
            path: e.selectedElementPath,
            metadata: e.selectionMetadata
        } : e.vditorSelection.text ? {
            type: "vditor",
            text: e.vditorSelection.text,
            range: e.vditorSelection.range,
            startPos: e.vditorSelection.startPos,
            endPos: e.vditorSelection.endPos,
            editorInstance: e.vditorSelection.editorInstance,
            metadata: e.selectionMetadata
        } : e.textSelection.text ? {
            type: "text",
            text: e.textSelection.text,
            range: e.textSelection.range,
            startContainer: e.textSelection.startContainer,
            endContainer: e.textSelection.endContainer,
            startOffset: e.textSelection.startOffset,
            endOffset: e.textSelection.endOffset,
            commonAncestorContainer: e.textSelection.commonAncestorContainer,
            metadata: e.selectionMetadata
        } : {
            type: "none",
            metadata: e.selectionMetadata
        },
        debugInfo: e => {
            var t;
            return {
                inspectMode: e.inspectMode,
                activeSelectionType: e.activeSelectionType,
                hasElement: !!e.selectedElement,
                hasText: !!e.textSelection.text,
                elementTag: null == (t = e.selectedElement) ? void 0 : t.tagName,
                elementPath: e.selectedElementPath,
                textLength: e.textSelection.text.length,
                timestamp: e.selectionMetadata.timestamp,
                source: e.selectionMetadata.source
            }
        },
        userSelectEditorType: e => e.editorType,
        currentFormatState: e => e.formatState,
        isFormatActive: e => t => e.formatState[t] || !1,
        tableContext: e => {
            var t, n;
            const l = e.currentBlockElement;
            if (!l) return {
                isTableElement: !1,
                cellType: null,
                isHeader: !1,
                position: null
            };
            const a = null == (t = l.tagName) ? void 0 : t.toLowerCase();
            if (["td", "th", "tr", "table"].includes(a)) {
                const e = !0;
                let t = null,
                    n = !1,
                    o = null;
                if ("td" === a || "th" === a) {
                    t = a.toUpperCase(), n = "th" === a || null !== l.closest("thead");
                    try {
                        const e = l.parentElement,
                            t = l.closest("table");
                        if (e && t) {
                            const n = Array.from(t.querySelectorAll("tr")),
                                a = Array.from(e.children);
                            o = {
                                rowIndex: n.indexOf(e),
                                cellIndex: a.indexOf(l),
                                totalRows: n.length,
                                totalCells: a.length
                            }
                        }
                    } catch (i) {}
                } else "tr" === a ? (t = "ROW", n = null !== l.closest("thead")) : "table" === a && (t = "TABLE");
                return {
                    isTableElement: e,
                    cellType: t,
                    isHeader: n,
                    position: o
                }
            }
            if (null == (n = l.closest) ? void 0 : n.call(l, "table")) {
                const e = l.closest("td, th");
                if (e) {
                    const t = e.tagName.toLowerCase();
                    let n = null;
                    try {
                        const t = e.parentElement,
                            l = e.closest("table");
                        if (t && l) {
                            const a = Array.from(l.querySelectorAll("tr")),
                                i = Array.from(t.children);
                            n = {
                                rowIndex: a.indexOf(t),
                                cellIndex: i.indexOf(e),
                                totalRows: a.length,
                                totalCells: i.length
                            }
                        }
                    } catch (i) {}
                    return {
                        isTableElement: !0,
                        cellType: t.toUpperCase(),
                        isHeader: "th" === t || null !== e.closest("thead"),
                        position: n,
                        isInsideCell: !0
                    }
                }
                return {
                    isTableElement: !0,
                    cellType: "INSIDE_TABLE",
                    isHeader: !1,
                    position: null
                }
            }
            return {
                isTableElement: !1,
                cellType: null,
                isHeader: !1,
                position: null
            }
        }
    },
    actions: {
        setInspectMode(e, t = "user") {
            this.inspectMode = e, this._updateMetadata(t), e || this.clearElementSelection("inspectMode disabled")
        },
        setSelectedElement(e, t = "", n = "user") {
            this.selectedElement = e, this.selectedElementPath = t, this.activeSelectionType = e ? "element" : "none", this._updateMetadata(n), e && this._clearTextSelectionInternal()
        },
        setHoveredElement(e, t = "user") {
            this.hoveredElement = e, this._updateMetadata(t)
        },
        setCurrentBlockElement(e, t = "user") {
            this.currentBlockElement = e, this._updateMetadata(t)
        },
        setTextSelection(e, t = "user") {
            e.range && (this.textSelection.range = e.range, this.textSelection.startContainer = e.range.startContainer, this.textSelection.endContainer = e.range.endContainer, this.textSelection.startOffset = e.range.startOffset, this.textSelection.endOffset = e.range.endOffset, this.textSelection.commonAncestorContainer = e.range.commonAncestorContainer), this.textSelection.text = e.text || "", Object.assign(this.textSelection, e), this.activeSelectionType = this.textSelection.text ? "text" : "none", this._updateMetadata(t), this.textSelection.text && !this.inspectMode && this._clearElementSelectionInternal()
        },
        updateTextSelection(e, t = "user") {
            Object.assign(this.textSelection, e), void 0 !== e.text && (this.activeSelectionType = e.text ? "text" : "none"), this._updateMetadata(t)
        },
        setVditorSelection(e, t = "user") {
            this.vditorSelection.text = e.text || "", this.vditorSelection.range = e.range || null, this.vditorSelection.startPos = e.startPos || 0, this.vditorSelection.endPos = e.endPos || 0, this.vditorSelection.editorInstance = e.editorInstance || null, this.activeSelectionType = this.vditorSelection.text ? "vditor" : "none", this._updateMetadata(t), this.vditorSelection.text && (this._clearElementSelectionInternal(), this._clearTextSelectionInternal())
        },
        clearVditorSelection(e = "user") {
            this._clearVditorSelectionInternal(), this._updateMetadata(e), "vditor" === this.activeSelectionType && (this.activeSelectionType = "none")
        },
        clearElementSelection(e = "user") {
            this._clearElementSelectionInternal(), this._updateMetadata(e), "element" === this.activeSelectionType && (this.activeSelectionType = "none")
        },
        clearTextSelection(e = "user") {
            this._clearTextSelectionInternal(), this._updateMetadata(e), "text" === this.activeSelectionType && (this.activeSelectionType = "none")
        },
        clearAllSelections(e = "user") {
            this._clearElementSelectionInternal(), this._clearTextSelectionInternal(), this._clearVditorSelectionInternal(), this.activeSelectionType = "none", this._updateMetadata(e)
        },
        updateFromIframeSelection(e, t = "user") {
            if (!e || 0 === e.rangeCount) return void this.clearTextSelection(t);
            const n = e.getRangeAt(0),
                l = e.toString();
            this.setTextSelection({
                range: n,
                text: l,
                startContainer: n.startContainer,
                endContainer: n.endContainer,
                startOffset: n.startOffset,
                endOffset: n.endOffset,
                commonAncestorContainer: n.commonAncestorContainer
            }, t)
        },
        updateFormatState(e, t = "user") {
            Object.assign(this.formatState, e), this._updateMetadata(t)
        },
        setFormatState(e, t, n = "user") {
            this.formatState.hasOwnProperty(e) && (this.formatState[e] = t, this._updateMetadata(n))
        },
        clearFormatState(e = "user") {
            this.formatState = {
                bold: !1,
                italic: !1,
                underline: !1,
                strikethrough: !1
            }, this._updateMetadata(e)
        },
        getSelectionSummary() {
            const e = this.selectionInfo;
            switch (e.type) {
                case "element":
                    return {
                        type: "element",
                        summary: `Selected ${e.element.tagName.toLowerCase()}${e.element.id?`#${e.element.id}`:""}${e.element.className?`.${e.element.className.split(" ").join(".")}`:""}`,
                        element: e.element,
                        path: e.path
                    };
                case "vditor":
                    return {
                        type: "vditor",
                        summary: `Vditor selected text: "${e.text.substring(0,50)}${e.text.length>50?"...":""}"`,
                        text: e.text,
                        length: e.text.length,
                        startPos: e.startPos,
                        endPos: e.endPos
                    };
                case "text":
                    return {
                        type: "text",
                        summary: `Selected text: "${e.text.substring(0,50)}${e.text.length>50?"...":""}"`,
                        text: e.text,
                        length: e.text.length
                    };
                default:
                    return {
                        type: "none",
                        summary: "No selection"
                    }
            }
        },
        _clearElementSelectionInternal() {
            this.selectedElement = null, this.hoveredElement = null, this.selectedElementPath = "", this.currentBlockElement = null
        },
        _clearTextSelectionInternal() {
            this.textSelection = {
                range: null,
                text: "",
                startContainer: null,
                endContainer: null,
                startOffset: 0,
                endOffset: 0,
                commonAncestorContainer: null
            }
        },
        _clearVditorSelectionInternal() {
            this.vditorSelection = {
                text: "",
                range: null,
                startPos: 0,
                endPos: 0,
                editorInstance: null
            }
        },
        _updateMetadata(e) {
            this.selectionMetadata = {
                timestamp: Date.now(),
                source: e
            }
        },
        setUserSelectEditorType(e, t = "user") {
            this.editorType = e, this._updateMetadata(t)
        }
    }
});
export {
    t as u
};