import {
    d as n,
    b as o,
    o as t
} from "./Cf0SOiw0.js";
const r = {
    version: "1.1",
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    x: "0px",
    y: "0px",
    viewBox: "0 0 512 512",
    "enable-background": "new 0 0 512 512",
    "xml:space": "preserve"
};
const e = {
        render: function(e, s) {
            return t(), n("svg", r, s[0] || (s[0] = [o("g", null, [o("polygon", {
                points: "396.795,396.8 320,396.8 320,448 448,448 448,320 396.795,320 \t"
            }), o("polygon", {
                points: "396.8,115.205 396.8,192 448,192 448,64 320,64 320,115.205 \t"
            }), o("polygon", {
                points: "115.205,115.2 192,115.2 192,64 64,64 64,192 115.205,192 \t"
            }), o("polygon", {
                points: "115.2,396.795 115.2,320 64,320 64,448 192,448 192,396.795 \t"
            })], -1)]))
        }
    },
    s = {
        version: "1.1",
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        x: "0px",
        y: "0px",
        viewBox: "0 0 512 512",
        "enable-background": "new 0 0 512 512",
        "xml:space": "preserve"
    };
const l = {
        render: function(r, e) {
            return t(), n("svg", s, e[0] || (e[0] = [o("g", null, [o("g", null, [o("path", {
                d: "M64,371.2h76.795V448H192V320H64V371.2z M140.795,140.8H64V192h128V64h-51.205V140.8z M320,448h51.2v-76.8H448V320H320\r\n\t\t\tV448z M371.2,140.8V64H320v128h128v-51.2H371.2z"
            })])], -1)]))
        }
    },
    w = {
        width: "32",
        height: "32",
        viewBox: "0 0 32 32",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const i = {
        render: function(r, e) {
            return t(), n("svg", w, e[0] || (e[0] = [o("g", {
                opacity: "0.65"
            }, [o("g", {
                opacity: "0.65"
            }, [o("path", {
                d: "M8.29883 16L22.2988 16",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }), o("path", {
                d: "M19.1506 11.9332L22.3129 15.2738C22.6926 15.6749 22.6926 16.3251 22.3129 16.7262L19.1506 20.0668",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            })])], -1)]))
        }
    },
    p = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 24 24"
    };
const g = {
    render: function(r, e) {
        return t(), n("svg", p, e[0] || (e[0] = [o("g", {
            fill: "none"
        }, [o("path", {
            d: "M4.75 3A1.75 1.75 0 0 0 3 4.75v14.5c0 .966.784 1.75 1.75 1.75h14.5A1.75 1.75 0 0 0 21 19.25V4.75A1.75 1.75 0 0 0 19.25 3H4.75z",
            fill: "currentColor"
        })], -1)]))
    }
};
export {
    e as E, g as S, l as U, i as a
};