import {
    d as t,
    b as e,
    o,
    D as r
} from "./Cf0SOiw0.js";
const i = {
    width: "24",
    height: "25",
    viewBox: "0 0 24 25",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const n = {
        render: function(r, n) {
            return o(), t("svg", i, n[0] || (n[0] = [e("g", {
                id: "Frame"
            }, [e("path", {
                id: "Vector",
                d: "M20.8921 10.7994C21.0185 9.95149 20.977 9.08344 20.7677 8.24879C20.4913 7.14619 19.9219 6.10199 19.0597 5.23974C18.1974 4.3775 17.1532 3.80814 16.0506 3.53167C15.216 3.32238 14.3479 3.28093 13.5 3.40732",
                stroke: "#232425",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }), e("path", {
                id: "Vector_2",
                d: "M17.0992 10.4017C17.2346 9.49343 16.9527 8.53493 16.2535 7.83573C15.5542 7.13653 14.5957 6.85458 13.6875 6.98998",
                stroke: "#232425",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }), e("path", {
                id: "Vector_3",
                d: "M7.1893 4.89453C7.5526 4.89453 7.8873 5.09154 8.0636 5.40916L9.28685 7.61257C9.447 7.90107 9.4545 8.25002 9.30695 8.54517L8.12855 10.902C8.12855 10.902 8.47005 12.6577 9.89925 14.0869C11.3285 15.5161 13.0783 15.8517 13.0783 15.8517L15.4348 14.6735C15.7301 14.5258 16.0793 14.5335 16.3679 14.6939L18.5776 15.9224C18.8949 16.0988 19.0917 16.4334 19.0917 16.7964V19.3332C19.0917 20.625 17.8917 21.5581 16.6677 21.145C14.1537 20.2968 10.2514 18.6817 7.77795 16.2082C5.3045 13.7348 3.68941 9.83247 2.84114 7.31852C2.42813 6.09447 3.36117 4.89453 4.653 4.89453H7.1893Z",
                stroke: "#232425",
                "stroke-width": "1.5",
                "stroke-linejoin": "round"
            })], -1)]))
        }
    },
    s = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const d = {
        render: function(e, i) {
            return o(), t("svg", s, i[0] || (i[0] = [r('<g id="Frame"><path id="Vector" d="M7.99814 4.34375C8.36144 4.34375 8.69614 4.54076 8.87249 4.85838L10.0957 7.06178C10.2558 7.35028 10.2634 7.69923 10.1158 7.99438L8.93739 10.3512C8.93739 10.3512 9.27889 12.1069 10.7081 13.5361C12.1373 14.9653 13.8871 15.3009 13.8871 15.3009L16.2436 14.1227C16.5389 13.975 16.8881 13.9827 17.1767 14.1431L19.3864 15.3716C19.7037 15.5481 19.9005 15.8826 19.9005 16.2456V18.7824C19.9005 20.0742 18.7005 21.0073 17.4765 20.5942C14.9625 19.746 11.0602 18.1309 8.58679 15.6574C6.11334 13.184 4.49824 9.28169 3.64997 6.76774C3.23696 5.54368 4.17001 4.34375 5.46184 4.34375H7.99814Z" stroke="#FF3838" stroke-width="1.5" stroke-linejoin="round"></path><path id="Vector_2" opacity="0.01" d="M20.5 3.5H14V10H20.5V3.5Z" fill="#FF3838"></path><path id="Vector_3" d="M19.5 4.5L15 9" stroke="#FF3838" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path id="Vector_4" d="M15 4.5L19.5 9" stroke="#FF3838" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g>', 1)]))
        }
    },
    h = {
        width: "25",
        height: "24",
        viewBox: "0 0 25 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const a = {
        render: function(r, i) {
            return o(), t("svg", h, i[0] || (i[0] = [e("g", {
                id: "Frame"
            }, [e("path", {
                id: "Vector",
                d: "M10.2218 10.7509C10.0084 10.2172 9.93729 9.85122 9.93729 9.85122L11.1157 7.49437C11.2632 7.19922 11.2557 6.85027 11.0956 6.56177L9.87234 4.35838C9.69604 4.04076 9.36134 3.84375 8.99804 3.84375H6.46174C5.16991 3.84375 4.23721 5.04488 4.65026 6.26888C5.25599 8.06388 6.25254 10.5662 7.67319 12.7509M13.8385 14.4567C14.452 14.7175 14.887 14.801 14.887 14.801L17.2435 13.6227C17.5388 13.4751 17.888 13.4827 18.1766 13.6431L20.3863 14.8716C20.7036 15.0481 20.9004 15.3826 20.9004 15.7456V18.2824C20.9004 19.5742 19.6986 20.5067 18.4746 20.0936C16.601 19.4613 13.957 18.4033 11.708 16.8818",
                stroke: "white",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }), e("path", {
                id: "Vector_2",
                d: "M20.5 4L4.5 20",
                stroke: "white",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            })], -1)]))
        }
    },
    C = {
        width: "25",
        height: "24",
        viewBox: "0 0 25 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const l = {
        render: function(r, i) {
            return o(), t("svg", C, i[0] || (i[0] = [e("g", {
                id: "Frame"
            }, [e("path", {
                id: "Vector",
                d: "M8.99789 3.84375C9.36119 3.84375 9.69589 4.04076 9.87224 4.35838L11.0954 6.56178C11.2556 6.85028 11.2631 7.19923 11.1155 7.49438L9.93714 9.85123C9.93714 9.85123 10.2786 11.6069 11.7078 13.0361C13.137 14.4653 14.8869 14.8009 14.8869 14.8009L17.2433 13.6227C17.5387 13.475 17.8879 13.4827 18.1765 13.6431L20.3861 14.8716C20.7034 15.0481 20.9002 15.3826 20.9002 15.7456V18.2824C20.9002 19.5742 19.7003 20.5073 18.4762 20.0942C15.9623 19.246 12.0599 17.6309 9.58654 15.1574C7.11309 12.684 5.498 8.78169 4.64973 6.26774C4.23672 5.04368 5.16976 3.84375 6.46159 3.84375H8.99789Z",
                stroke: "white",
                "stroke-width": "1.5",
                "stroke-linejoin": "round"
            })], -1)]))
        }
    },
    w = {
        width: "25",
        height: "24",
        viewBox: "0 0 25 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const c = {
        render: function(r, i) {
            return o(), t("svg", w, i[0] || (i[0] = [e("g", {
                id: "Frame"
            }, [e("path", {
                id: "Vector",
                d: "M21.3921 10.2994C21.5185 9.45149 21.477 8.58344 21.2677 7.74879C20.9913 6.64619 20.4219 5.60199 19.5597 4.73974C18.6974 3.8775 17.6532 3.30814 16.5506 3.03167C15.716 2.82238 14.8479 2.78093 14 2.90732",
                stroke: "white",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }), e("path", {
                id: "Vector_2",
                d: "M17.5992 9.90173C17.7346 8.99343 17.4527 8.03493 16.7535 7.33573C16.0542 6.63653 15.0957 6.35458 14.1875 6.48998",
                stroke: "white",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }), e("path", {
                id: "Vector_3",
                d: "M7.6893 4.39453C8.0526 4.39453 8.3873 4.59154 8.5636 4.90916L9.78685 7.11257C9.947 7.40107 9.9545 7.75002 9.80695 8.04517L8.62855 10.402C8.62855 10.402 8.97005 12.1577 10.3993 13.5869C11.8285 15.0161 13.5783 15.3517 13.5783 15.3517L15.9348 14.1735C16.2301 14.0258 16.5793 14.0335 16.8679 14.1939L19.0776 15.4224C19.3949 15.5988 19.5917 15.9334 19.5917 16.2964V18.8332C19.5917 20.125 18.3917 21.0581 17.1677 20.645C14.6537 19.7968 10.7514 18.1817 8.27795 15.7082C5.8045 13.2348 4.18941 9.33247 3.34114 6.81852C2.92813 5.59447 3.86117 4.39453 5.153 4.39453H7.6893Z",
                stroke: "white",
                "stroke-width": "1.5",
                "stroke-linejoin": "round"
            })], -1)]))
        }
    },
    p = {
        width: "48",
        height: "48",
        viewBox: "0 0 48 48",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const k = {
    render: function(r, i) {
        return o(), t("svg", p, i[0] || (i[0] = [e("g", {
            id: "Component 15352"
        }, [e("rect", {
            id: "Rectangle 34691256",
            width: "48",
            height: "48",
            rx: "24",
            fill: "#F5F5F5"
        })], -1)]))
    }
};

function g(t) {
    if (!t) return "";
    let e = !1;
    try {
        window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches && (e = !0)
    } catch (o) {}
    return t.replace(/(\*[\s\*]*\*)+/g, (t => {
        const o = t.replace(/\s/g, "").replace(/\*/g, "5");
        return `<span style="display:inline-block; filter:blur(8px); background-color:${e?"rgba(100,100,100,0.3)":"rgba(200,200,200,0.2)"}; border-radius:3px; color:inherit; font-weight:bold; user-select:none; position:relative; padding:0 2px; margin:0 1px; transform:scale(1.05); opacity:0.95;">${o}</span>`
    }))
}
export {
    k as C, a as H, d as P, l as S, c as a, n as b, g as m
};