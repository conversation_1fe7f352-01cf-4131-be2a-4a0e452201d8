const t = t => {
        const a = t.mime_type;
        return !!a && (a.startsWith("image/") || a.startsWith("video/") || a.startsWith("audio/"))
    },
    a = {
        jpg: "image/jpeg",
        jpeg: "image/jpeg",
        png: "image/png",
        gif: "image/gif",
        webp: "image/webp",
        svg: "image/svg+xml",
        bmp: "image/bmp",
        ico: "image/vnd.microsoft.icon",
        tiff: "image/tiff",
        tif: "image/tiff",
        mp4: "video/mp4",
        avi: "video/avi",
        mkv: "video/mkv",
        mov: "video/mov",
        wmv: "video/wmv",
        flv: "video/flv",
        webm: "video/webm",
        m4v: "video/mp4",
        "3gp": "video/3gpp",
        ogv: "video/ogg",
        mts: "video/mp2t",
        mp3: "audio/mp3",
        wav: "audio/wav",
        flac: "audio/flac",
        aac: "audio/aac",
        ogg: "audio/ogg",
        wma: "audio/wma",
        m4a: "audio/mp4",
        opus: "audio/opus",
        ape: "audio/ape",
        ac3: "audio/ac3",
        pdf: "application/pdf",
        doc: "application/msword",
        docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        xls: "application/vnd.ms-excel",
        xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ppt: "application/vnd.ms-powerpoint",
        pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        txt: "text/plain",
        md: "text/markdown",
        markdown: "text/markdown",
        json: "application/json",
        xml: "application/xml",
        html: "text/html",
        htm: "text/html",
        css: "text/css",
        js: "text/javascript",
        jsx: "text/javascript",
        ts: "text/typescript",
        tsx: "text/typescript",
        py: "text/plain",
        java: "text/plain",
        c: "text/plain",
        cpp: "text/plain",
        cc: "cpp",
        cxx: "cpp",
        cs: "csharp",
        go: "text/plain",
        rs: "text/plain",
        rb: "text/plain",
        php: "text/plain",
        csv: "text/csv",
        yaml: "application/x-yaml",
        yml: "application/x-yaml"
    },
    s = {
        js: "javascript",
        jsx: "javascript",
        ts: "typescript",
        tsx: "typescript",
        py: "python",
        java: "java",
        c: "c",
        cpp: "cpp",
        cc: "cpp",
        cxx: "cpp",
        cs: "csharp",
        go: "go",
        rs: "rust",
        rb: "ruby",
        php: "php",
        html: "html",
        htm: "html",
        css: "css",
        scss: "scss",
        sass: "sass",
        less: "less",
        xml: "xml",
        yaml: "yaml",
        yml: "yaml",
        toml: "toml",
        sh: "bash",
        bash: "bash",
        zsh: "bash",
        fish: "bash",
        ps1: "powershell",
        r: "r",
        sql: "sql",
        swift: "swift",
        kt: "kotlin",
        scala: "scala",
        lua: "lua",
        dart: "dart",
        vue: "vue",
        svelte: "svelte"
    },
    e = [".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg", ".bmp", ".ico", ".tiff", ".tif", ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v", ".3gp", ".ogv", ".mts", ".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma", ".m4a", ".opus", ".ape", ".ac3", ".pdf", ".md", ".markdown", ".js", ".jsx", ".ts", ".tsx", ".py", ".java", ".c", ".cpp", ".cc", ".cxx", ".cs", ".go", ".rs", ".rb", ".php", ".html", ".htm", ".css", ".scss", ".sass", ".less", ".xml", ".yaml", ".yml", ".toml", ".sh", ".bash", ".zsh", ".fish", ".ps1", ".r", ".sql", ".swift", ".kt", ".scala", ".lua", ".dart", ".vue", ".svelte", ".json", ".csv", ".tsv", ".txt", ".log", ".conf", ".cfg", ".ini"],
    i = [".md", ".markdown", ".js", ".jsx", ".ts", ".tsx", ".py", ".java", ".c", ".cpp", ".cc", ".cxx", ".cs", ".go", ".rs", ".rb", ".php", ".html", ".htm", ".css", ".scss", ".sass", ".less", ".xml", ".yaml", ".yml", ".toml", ".sh", ".bash", ".zsh", ".fish", ".ps1", ".r", ".sql", ".swift", ".kt", ".scala", ".lua", ".dart", ".vue", ".svelte", ".json", ".csv", ".tsv", ".txt", ".log", ".conf", ".cfg", ".ini"],
    p = t => {
        const s = t.toLowerCase().split(".").pop() || "";
        return a[s] || "application/octet-stream"
    },
    o = t => t.toLowerCase().split(".").pop() || "",
    n = t => Object.entries(a).filter((([, a]) => a.startsWith(t))).map((([t]) => t)),
    m = t => {
        const a = t.mime_type,
            s = o(t.name || "");
        return !(!a || !a.startsWith("image/")) || n("image/").includes(s)
    },
    c = t => {
        const a = t.mime_type,
            s = o(t.name || "");
        return !(!a || !a.startsWith("video/")) || n("video/").includes(s)
    },
    r = t => {
        const a = t.mime_type,
            s = o(t.name || "");
        return !(!a || !a.startsWith("audio/")) || n("audio/").includes(s)
    },
    l = t => {
        const a = t.mime_type,
            s = o(t.name || "");
        return "application/pdf" === a || "pdf" === s
    },
    x = t => {
        const a = t.mime_type,
            s = o(t.name || "");
        return "application/msword" === a || "application/vnd.openxmlformats-officedocument.wordprocessingml.document" === a || "doc" === s || "docx" === s
    },
    d = t => {
        const a = t.mime_type,
            s = o(t.name || "");
        return "application/vnd.ms-excel" === a || "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" === a || "xls" === s || "xlsx" === s
    },
    v = t => {
        const a = t.mime_type,
            s = o(t.name || "");
        return "application/vnd.ms-powerpoint" === a || "application/vnd.openxmlformats-officedocument.presentationml.presentation" === a || "ppt" === s || "pptx" === s
    },
    h = t => {
        const a = t.mime_type,
            s = (t.name || "").toLowerCase();
        if (a) {
            if (a.startsWith("text/")) return !0;
            if (["application/json", "application/xml", "application/javascript", "application/typescript", "application/x-sh", "application/x-httpd-php", "application/markdown", "application/x-yaml"].includes(a)) return !0
        }
        return i.some((t => s.endsWith(t)))
    },
    f = t => t.isDir ? "folder" : m(t) ? "image" : c(t) ? "video" : l(t) ? "pdf" : x(t) ? "word" : d(t) ? "excel" : v(t) ? "powerpoint" : r(t) ? "audio" : y(t) ? "archive" : h(t) ? "document" : "unknown",
    u = t => {
        const a = t.mime_type,
            s = (t.name || "").toLowerCase();
        if (a) {
            if (a.startsWith("image/") || a.startsWith("video/") || a.startsWith("audio/") || "application/pdf" === a) return !0;
            if (a.startsWith("text/") || "application/json" === a || "application/xml" === a || "application/javascript" === a || "application/typescript" === a || "application/x-yaml" === a) return !0
        }
        return e.some((t => s.endsWith(t)))
    },
    g = t => {
        const a = "." + o(t);
        return s[a.substring(1)] || "plaintext"
    },
    w = t => {
        const a = t.mime_type,
            e = (t.name || "").toLowerCase();
        if (a && a.startsWith("image/")) return "image";
        if (a && a.startsWith("video/")) return "video";
        if (a && a.startsWith("audio/")) return "audio";
        if ("application/pdf" === a) return "pdf";
        if ("text/html" === a || e.endsWith(".html") || e.endsWith(".htm")) return "html";
        if ("text/markdown" === a || "text/x-markdown" === a || e.endsWith(".md") || e.endsWith(".markdown")) return "markdown";
        if ("application/json" === a || e.endsWith(".json")) return "json";
        if ("text/csv" === a || e.endsWith(".csv") || e.endsWith(".tsv")) return "csv";
        const i = o(e);
        if (i && s[i]) return "code";
        return a && ["text/javascript", "application/javascript", "text/typescript", "application/typescript", "text/x-python", "text/x-java", "text/x-c", "text/x-cpp", "text/x-csharp", "text/x-go", "text/x-rust", "text/x-ruby", "text/x-php", "text/html", "text/css", "text/x-scss", "text/x-sass", "text/x-less", "application/xml", "text/x-yaml", "application/x-yaml", "text/x-toml", "application/toml"].includes(a) ? "code" : a && a.startsWith("text/") ? "text" : o(e) || "unknown"
    },
    y = t => {
        const a = (t.name || "").toLowerCase();
        return [".zip", ".tar.gz", ".tgz", ".tar.bz2", ".tbz2", ".tar", ".gz"].some((t => a.endsWith(t)))
    };
export {
    g as a, p as b, o as c, r as d, f as e, m as f, w as g, l as h, c as i, x as j, d as k, v as l, h as m, y as n, u as o, t as p
};