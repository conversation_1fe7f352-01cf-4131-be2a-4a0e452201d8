.drawable-canvas-container[data-v-bca4e9e1] {
    flex-direction: column;
    height: 100vh;
    padding: 80px 0;
    position: relative
}

.drawable-canvas-container[data-v-bca4e9e1],
.toolbar[data-v-bca4e9e1] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    width: 100%
}

.toolbar[data-v-bca4e9e1] {
    color: #000;
    font-size: 14px;
    height: 50px;
    left: 0;
    padding: 8px;
    position: absolute;
    top: 0
}

.toolbar[data-v-bca4e9e1],
.tools-container[data-v-bca4e9e1] {
    gap: 12px;
    justify-content: center
}

.tools-container[data-v-bca4e9e1] {
    align-items: center;
    display: flex
}

.tool-separator[data-v-bca4e9e1] {
    color: #eaeaea
}

.v-stage-wrapper[data-v-bca4e9e1] {
    align-items: center;
    display: flex;
    flex: 1;
    height: 80%;
    justify-content: center;
    width: 100%
}

.enable-paint[data-v-bca4e9e1] canvas {
    cursor: none !important
}

.tool-button[data-v-bca4e9e1] {
    background: #f5f5f5;
    border-radius: 12px;
    color: #000;
    cursor: pointer;
    font-size: 14px;
    height: 30px;
    padding: 4px 16px;
    transition: all .3s
}

.tool-button[data-v-bca4e9e1]:hover:not(:disabled) {
    background: #0000004d
}

.tool-button[data-v-bca4e9e1]:disabled {
    cursor: not-allowed;
    opacity: .5
}

.stroke-width-control[data-v-bca4e9e1] {
    align-items: center;
    display: flex;
    gap: 8px
}

.stroke-width-slider[data-v-bca4e9e1] {
    cursor: pointer;
    width: 100px
}

.stroke-width-value[data-v-bca4e9e1] {
    min-width: 20px
}

.prompt-input-overlay[data-v-bca4e9e1] {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px #00000026;
    display: flex;
    gap: 8px;
    padding: 8px 0 8px 10px;
    position: absolute;
    transform: translate(-50%);
    z-index: 1000
}

.prompt-input[data-v-bca4e9e1] {
    border-radius: 4px;
    box-sizing: border-box;
    color: #000;
    font-size: 14px;
    line-height: 1.5;
    min-height: 60px;
    outline: none;
    padding-right: 30px;
    resize: none;
    width: 300px
}

.submit-button[data-v-bca4e9e1] {
    align-items: center;
    background: #000c;
    border: 1px solid #fff;
    border-radius: 10px;
    bottom: 4px;
    color: #fff;
    cursor: pointer;
    display: flex;
    justify-content: center;
    position: absolute;
    right: 4px
}

.submit-button[data-v-bca4e9e1],
.submit-prompt-icon[data-v-bca4e9e1] {
    height: 30px;
    width: 30px
}

.submit-button[data-v-bca4e9e1]:hover {
    background: #000
}

@media (max-width:768px) {
    .v-stage-wrapper[data-v-bca4e9e1] {
        width: calc(100% - 20px)
    }
    .drawable-canvas-container[data-v-bca4e9e1] {
        padding: 60px 0 0
    }
    .toolbar[data-v-bca4e9e1] {
        background: #fff;
        border-bottom: none;
        bottom: 60px;
        position: fixed;
        top: unset;
        z-index: 1
    }
    .toolbar-bottom-10[data-v-bca4e9e1] {
        bottom: 0
    }
    .toolbar-bottom-10 .tools-container[data-v-bca4e9e1] {
        margin-bottom: 20px
    }
    .button-reset[data-v-bca4e9e1] {
        display: none
    }
    .v-stage-wrapper[data-v-bca4e9e1] {
        align-items: unset
    }
}

.aspect-ratio-canvas-container[data-v-35738fb2] {
    flex-direction: column;
    height: calc(100vh - 40px);
    padding: 80px 0;
    position: relative
}

.aspect-ratio-canvas-container[data-v-35738fb2],
.toolbar[data-v-35738fb2] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    width: 100%
}

.toolbar[data-v-35738fb2] {
    background: #fff;
    color: #000;
    font-size: 14px;
    gap: 12px;
    height: 50px;
    justify-content: center;
    left: 0;
    overflow: hidden;
    padding: 8px;
    position: absolute;
    top: 0
}

.ratio-controls[data-v-35738fb2] {
    display: flex;
    gap: 15px;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.ratio-controls[data-v-35738fb2]::-webkit-scrollbar {
    display: none
}

.v-stage-wrapper[data-v-35738fb2] {
    align-items: center;
    display: flex;
    flex: 1;
    height: 80%;
    justify-content: center;
    width: 100%
}

.tool-button[data-v-35738fb2] {
    background: #f5f5f5;
    border: none;
    border-radius: 12px;
    color: #000;
    cursor: pointer;
    flex-shrink: 0;
    font-size: 14px;
    height: 30px;
    padding: 4px 20px 4px 40px;
    position: relative;
    transition: all .3s
}

.tool-button[data-v-35738fb2]:hover {
    background: #0000001a
}

.tool-button[data-v-35738fb2]:before {
    border: 1px solid;
    border-radius: 2px;
    content: "";
    height: 16px;
    left: 12px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 16px
}

.tool-button[data-ratio="1:1"][data-v-35738fb2]:before {
    height: 12px;
    width: 12px
}

.tool-button[data-ratio="4:3"][data-v-35738fb2]:before {
    height: 12px;
    width: 16px
}

.tool-button[data-ratio="16:9"][data-v-35738fb2]:before {
    height: 9px;
    width: 16px
}

.tool-button[data-ratio="9:16"][data-v-35738fb2]:before {
    height: 16px;
    width: 9px
}

.tool-button[data-ratio=original][data-v-35738fb2]:before {
    border-style: dashed;
    height: 14px;
    width: 14px
}

@media (max-width:768px) {
    .aspect-ratio-canvas-container[data-v-35738fb2] {
        padding: 60px 0
    }
    .toolbar[data-v-35738fb2] {
        background: #fff;
        border-bottom: none;
        bottom: 70px;
        padding: 0 10px;
        position: fixed;
        top: unset;
        z-index: 1
    }
    .ratio-controls[data-v-35738fb2] {
        width: 100%
    }
}

.image-editor[data-v-ab8fa342] {
    margin: 0 auto;
    width: 100%
}

.upload-area[data-v-ab8fa342] {
    align-items: center;
    border: 2px dashed #ccc;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    height: 300px;
    justify-content: center;
    position: relative;
    width: 100%
}

.file-input[data-v-ab8fa342] {
    cursor: pointer;
    height: 100%;
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.upload-hint[data-v-ab8fa342] {
    color: #666;
    font-size: 16px;
    margin-bottom: 20px
}

.url-input-container[data-v-ab8fa342] {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    position: relative;
    z-index: 1
}

.url-input[data-v-ab8fa342] {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 8px;
    width: 300px
}

.controls[data-v-ab8fa342] {
    bottom: 20px;
    padding: 0 10px;
    position: absolute;
    text-align: center;
    width: 100%
}

.controls button[data-v-ab8fa342] {
    border-radius: 4px;
    cursor: pointer;
    padding: 8px 16px
}

.controls button[data-v-ab8fa342]:disabled {
    cursor: not-allowed
}

.editor-container[data-v-ab8fa342] {
    position: relative;
    width: 100%
}

.toolbar[data-v-ab8fa342] {
    border-bottom: 1px solid #eee;
    box-sizing: border-box;
    color: #000;
    font-size: 14px;
    gap: 12px;
    height: 50px;
    justify-content: center;
    left: 0;
    padding: 8px;
    position: absolute;
    top: 0;
    width: 100%
}

.toolbar[data-v-ab8fa342],
.toolbar button[data-v-ab8fa342] {
    align-items: center;
    display: flex
}

.toolbar button[data-v-ab8fa342] {
    background-color: transparent;
    cursor: pointer;
    flex-direction: row;
    gap: 8px;
    min-width: 120px;
    padding: 8px 0
}

.toolbar button[data-v-ab8fa342] svg {
    height: 20px;
    width: 20px
}

.preview-container[data-v-ab8fa342] {
    align-items: center;
    background-color: #fff9;
    box-sizing: border-box;
    display: flex;
    height: 100vh;
    justify-content: center;
    opacity: 1;
    position: absolute;
    top: 0;
    transition: opacity 1s ease;
    width: 100vw
}

.fade-leave-active[data-v-ab8fa342] {
    opacity: 0
}

.background-image-absolute[data-v-ab8fa342] {
    align-items: unset;
    justify-content: unset;
    padding-top: 80px
}

.background-image[data-v-ab8fa342] {
    background-color: #fff;
    background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 0), linear-gradient(-45deg, #f0f0f0 25%, transparent 0), linear-gradient(45deg, transparent 75%, #f0f0f0 0), linear-gradient(-45deg, transparent 75%, #f0f0f0 0);
    background-position: 0 0, 0 10px, 10px -10px, -10px 0;
    background-size: 20px 20px
}

.original-image[data-v-ab8fa342],
.preview-image[data-v-ab8fa342] {
    height: auto;
    width: 100%
}

.original-image[data-v-ab8fa342] {
    left: 0;
    position: absolute;
    top: 0;
    z-index: 2
}

.compare-button[data-v-ab8fa342] {
    background: #000000b3;
    border: none;
    border-radius: 4px;
    bottom: 20px;
    color: #fff;
    cursor: pointer;
    padding: 8px 16px;
    position: absolute;
    right: 20px;
    z-index: 3
}

.header[data-v-ab8fa342] {
    border-bottom: 1px solid #eee;
    height: 50px;
    position: absolute;
    width: 100%
}

.title[data-v-ab8fa342] {
    font-weight: 600;
    left: 40px;
    top: 50%;
    transform: translateY(-50%)
}

.close-button[data-v-ab8fa342],
.title[data-v-ab8fa342] {
    color: #000;
    position: absolute;
    z-index: 1001
}

.close-button[data-v-ab8fa342] {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 24px;
    font-weight: 700;
    height: 20px;
    left: 10px;
    top: 10px;
    width: 20px
}

.download-button[data-v-ab8fa342],
.save-to-ai-drive-button[data-v-ab8fa342] {
    align-items: center;
    background: #232425;
    border-radius: 12px;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 6px 16px;
    position: absolute;
    right: 10px;
    top: 10px;
    transition: all .3s;
    z-index: 1001
}

.save-to-ai-drive-button[data-v-ab8fa342] {
    right: 140px
}

.save-to-ai-drive-button[data-v-ab8fa342] svg {
    height: 20px;
    width: 20px
}

.download-button[data-v-ab8fa342]:hover:not(:disabled),
.save-to-ai-drive-button[data-v-ab8fa342]:hover:not(:disabled) {
    background: #000
}

button.process-button[data-v-ab8fa342] {
    background: #232425;
    border-radius: 12px;
    color: #fff;
    cursor: pointer;
    font-size: 14px;
    font-weight: 700;
    height: 40px;
    padding: 0 8px;
    width: 201px
}

.download-button[data-v-ab8fa342]:disabled,
.save-to-ai-drive-button[data-v-ab8fa342]:disabled {
    background-color: #f5f5f5;
    color: #000
}

.download-button[data-v-ab8fa342]:disabled,
.process-button[data-v-ab8fa342]:disabled,
.save-to-ai-drive-button[data-v-ab8fa342]:disabled {
    cursor: not-allowed;
    opacity: .5
}

.tip[data-v-ab8fa342] {
    color: red;
    font-size: 14px;
    padding-bottom: 10px
}

@media (max-width:768px) {
    button.process-button[data-v-ab8fa342] {
        margin-bottom: 20px;
        width: 100%
    }
    .controls[data-v-ab8fa342] {
        background: #fff;
        bottom: 0;
        box-sizing: border-box;
        position: fixed;
        width: 100%;
        z-index: 3
    }
    .show-text[data-v-ab8fa342] {
        display: none
    }
    .save-to-ai-drive-button[data-v-ab8fa342] {
        right: 70px
    }
    .preview-container[data-v-ab8fa342] {
        align-items: unset;
        padding-top: 60px;
        z-index: 2
    }
}

.tool-callback-mode .header[data-v-ab8fa342]>* {
    display: none
}

.tool-callback-mode .header>.compare-button[data-v-ab8fa342],
.tool-callback-mode .header>.title[data-v-ab8fa342] {
    display: block
}

.tool-callback-mode .drawable-canvas-container[data-v-ab8fa342],
.tool-callback-mode .editor-container[data-v-ab8fa342],
.tool-callback-mode .editor-inner-container[data-v-ab8fa342],
.tool-callback-mode .preview-container[data-v-ab8fa342] {
    height: 100%
}

.tool-callback-mode .preview-container[data-v-ab8fa342] {
    width: 100%
}