.spark_result_card[data-v-4ae48e24] {
    background: #f5f5f5;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
    padding: 16px
}

.spark_result_card .logo[data-v-4ae48e24] {
    display: flex;
    height: 22px;
    width: 71px
}

.spark_result_card .logo img[data-v-4ae48e24] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.spark_result_card .title[data-v-4ae48e24] {
    color: #094c99;
    cursor: pointer;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 27px
}

.spark_result_card .content[data-v-4ae48e24] {
    color: #232425;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px
}

.spark_result_card .see-more[data-v-4ae48e24] {
    align-items: center;
    background: #0f7fff;
    border-radius: 39px;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    gap: 4px;
    justify-content: center;
    padding: 4px 16px
}

.spark_result_card .see-more .icon[data-v-4ae48e24] {
    color: #fff;
    height: 16px;
    width: 16px
}

@media (prefers-color-scheme:dark) {
    .spark_result_card[data-v-4ae48e24] {
        background: #232425
    }
    .spark_result_card .title[data-v-4ae48e24] {
        color: #0f7fff
    }
    .spark_result_card .content[data-v-4ae48e24] {
        color: #ccc
    }
}

.gallery[data-v-4ae48e24] {
    align-items: center;
    display: flex;
    flex-direction: row;
    height: 184px;
    justify-content: space-between;
    overflow: hidden
}

.gallery .image[data-v-4ae48e24] {
    cursor: pointer;
    display: flex;
    height: 100%;
    position: relative;
    width: 100%
}

.gallery .image img[data-v-4ae48e24] {
    border-radius: 8px;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.gallery .image .youtube_icon[data-v-4ae48e24] {
    height: 32px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 45px
}

.gallery .gallery__item[data-v-4ae48e24] {
    height: 100%
}

.gallery_2 .gallery__item[data-v-4ae48e24] {
    width: 49%
}

.gallery_3 .gallery__item[data-v-4ae48e24] {
    width: 24%
}

.gallery_3 .gallery__item[data-v-4ae48e24]:first-child {
    width: 48%
}

.gallery_4 .gallery__item[data-v-4ae48e24] {
    width: 24%
}

.slide-style-modify-model[data-v-e2af6a1e] {
    min-width: var(--container-width, 760px);
    transition: width .3s ease-in-out, min-width .3s ease-in-out;
    width: var(--container-width, 760px)
}

@media (max-width:1880px) and (min-width:1220px) {
    .slide-style-modify-model[data-v-e2af6a1e] {
        min-width: var(--container-width, 760px);
        width: var(--container-width, 760px)
    }
}

@media (min-width:1880px) {
    .slide-style-modify-model[data-v-e2af6a1e] {
        min-width: var(--container-width, 760px);
        width: var(--container-width, 760px)
    }
}

@media (max-width:768px) {
    .slide-style-modify-model[data-v-e2af6a1e] {
        min-width: auto;
        width: 100%
    }
}

.black-loading-animation[data-v-e2af6a1e] {
    animation: rotate-e2af6a1e 1.5s linear infinite
}

@keyframes rotate-e2af6a1e {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.black-loading-animation[data-v-068ece1c] {
    animation: rotate-068ece1c 1.5s linear infinite
}

@keyframes rotate-068ece1c {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.html-canvas-bar[data-v-bc65088d] {
    background: #f3f9ff;
    border: 1px solid #0f7fff;
    border-radius: 8px;
    box-sizing: border-box;
    margin-bottom: 12px;
    overflow: hidden;
    width: var(--container-width, 100%)
}

@media (max-width:1220px) {
    .html-canvas-bar[data-v-bc65088d] {
        width: 100%
    }
}

.bar-content[data-v-bc65088d] {
    display: flex;
    flex-direction: column;
    gap: 8px;
    overflow: hidden;
    padding: 8px 12px
}

.first-row[data-v-bc65088d] {
    color: #232425;
    gap: 8px;
    justify-content: space-between
}

.first-row[data-v-bc65088d],
.selection-types[data-v-bc65088d] {
    align-items: center;
    display: flex
}

.selection-types[data-v-bc65088d] {
    gap: 12px;
    overflow: hidden
}

.selection-type[data-v-bc65088d] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    gap: 4px
}

.icon[data-v-bc65088d],
.type-text[data-v-bc65088d] {
    font-size: 12px
}

.type-text[data-v-bc65088d] {
    font-weight: 600
}

.close-button[data-v-bc65088d] {
    align-items: center;
    background: none;
    border: none;
    border-radius: 4px;
    color: #232425;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    justify-content: center;
    opacity: .6;
    padding: 0;
    transition: all .2s ease;
    width: 20px
}

@media (hover:hover) {
    .close-button[data-v-bc65088d]:hover {
        opacity: 1
    }
}

.close-button svg[data-v-bc65088d] {
    height: 16px;
    width: 16px
}

.content-row[data-v-bc65088d] {
    border-radius: 8px;
    box-sizing: border-box;
    flex: 1;
    overflow: hidden;
    padding: 6px 8px
}

.selection-content[data-v-bc65088d] {
    display: -webkit-box;
    line-height: 1.5;
    word-break: break-word;
    -webkit-line-clamp: 6;
    -webkit-box-orient: vertical;
    color: #606366;
    font-family: Arial;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    margin-top: 2px;
    overflow: hidden;
    padding: 0;
    text-overflow: ellipsis
}

.image-edit-content[data-v-bc65088d] {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 12px 0
}

.image-url-section[data-v-bc65088d] {
    display: flex;
    flex-direction: column;
    gap: 6px
}

.input-label[data-v-bc65088d] {
    color: #232425;
    font-size: 12px;
    font-weight: 600
}

.image-url-input[data-v-bc65088d] {
    background: #fff;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    box-sizing: border-box;
    color: #374151;
    font-size: 14px;
    padding: 8px 12px;
    transition: border-color .2s ease;
    width: 100%
}

.image-url-input[data-v-bc65088d]:focus {
    border-color: #0f7fff;
    box-shadow: 0 0 0 3px #0f7fff1a;
    outline: none
}

.image-url-input[data-v-bc65088d]::-moz-placeholder {
    color: #9ca3af
}

.image-url-input[data-v-bc65088d]::placeholder {
    color: #9ca3af
}

.upload-section[data-v-bc65088d] {
    align-items: center;
    background: #fff;
    border: 1px dashed #d9eaff;
    border-radius: 12px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    font-size: 14px;
    font-weight: 500;
    gap: 8px;
    justify-content: center;
    padding: 40px 16px;
    transition: all .2s ease
}

.upload-section[data-v-bc65088d]:hover {
    background: #f8fbff;
    border-color: #0f7fff
}

.upload-section svg[data-v-bc65088d] {
    height: 20px;
    width: 20px
}

@media (max-width:767px) {
    .image-edit-content[data-v-bc65088d] {
        gap: 10px;
        padding: 10px 0
    }
    .upload-section[data-v-bc65088d] {
        font-size: 13px;
        padding: 30px 12px
    }
    .upload-section svg[data-v-bc65088d] {
        height: 18px;
        width: 18px
    }
    .image-url-input[data-v-bc65088d] {
        font-size: 13px;
        padding: 8px 10px
    }
}

.editor-switch-bar[data-v-9d8a3f95] {
    background: #f6ecec;
    border: 1px solid #dec6c5;
    border-bottom: none;
    border-radius: 12px 12px 0 0;
    box-sizing: border-box;
    margin-bottom: -20px;
    margin-left: 12px;
    overflow: hidden;
    padding: 0 1px;
    transition: all .3s ease;
    width: calc(var(--container-width, 100%) - 24px)
}

@media (max-width:1220px) {
    .editor-switch-bar[data-v-9d8a3f95] {
        margin-top: 12px;
        width: calc(100% - 24px)
    }
}

.bar-content[data-v-9d8a3f95] {
    align-items: center;
    display: flex;
    margin-bottom: 12px;
    padding: 6px 12px 0;
    transition: all .3s ease
}

.switch-content[data-v-9d8a3f95] {
    flex: 1;
    overflow: hidden
}

.editor-info[data-v-9d8a3f95] {
    gap: 2px;
    justify-content: space-between
}

.editor-info[data-v-9d8a3f95],
.editor-type[data-v-9d8a3f95] {
    align-items: center;
    display: flex
}

.editor-type[data-v-9d8a3f95] {
    flex-shrink: 0;
    gap: 4px
}

.icon[data-v-9d8a3f95] {
    font-size: 12px
}

.type-text[data-v-9d8a3f95] {
    color: #500724;
    font-size: 14px;
    font-weight: 600
}

.editor-options[data-v-9d8a3f95] {
    max-height: none;
    opacity: 1;
    overflow: visible
}

.option-group[data-v-9d8a3f95] {
    border-radius: 8px;
    gap: 2px;
    margin-right: 12px;
    padding: 2px
}

.option-button[data-v-9d8a3f95],
.option-group[data-v-9d8a3f95] {
    background: #fff;
    display: flex;
    flex-wrap: wrap
}

.option-button[data-v-9d8a3f95] {
    align-content: center;
    align-items: center;
    border: none;
    border-radius: 6px;
    color: #551c19;
    cursor: pointer;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    justify-content: center;
    line-height: 150%;
    padding: 1px 10px;
    transition: all .2s ease
}

.option-button[data-v-9d8a3f95]:hover {
    opacity: .8
}

.option-button.active[data-v-9d8a3f95] {
    background: #551c19;
    color: #fff
}

.check-icon[data-v-9d8a3f95] {
    flex-shrink: 0;
    height: 12px;
    width: 12px
}

.option-icon[data-v-9d8a3f95] {
    font-size: 10px;
    text-align: center;
    width: 12px
}

.option-label[data-v-9d8a3f95] {
    font-weight: 500;
    white-space: nowrap
}

.toggle-button[data-v-9d8a3f95] {
    align-items: center;
    background: #ebe1e1;
    border: none;
    border-radius: 30px;
    color: #6c757d;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    justify-content: center;
    padding: 0;
    transform: rotate(180deg);
    transition: all .3s ease;
    width: 20px
}

.toggle-button[data-v-9d8a3f95]:hover {
    opacity: .8
}

.toggle-button.rotated[data-v-9d8a3f95] {
    transform: rotate(0)
}

.format-description[data-v-9d8a3f95] {
    animation: slideUp-9d8a3f95 .3s ease-out;
    background: #fffbfb;
    padding: 12px;
    transform-origin: bottom
}

@media (prefers-color-scheme:dark) {
    .format-description[data-v-9d8a3f95] {
        background: #333
    }
}

@keyframes slideUp-9d8a3f95 {
    0% {
        opacity: 0;
        transform: translateY(10px)
    }
    to {
        opacity: 1;
        transform: translateY(0)
    }
}

.description-content[data-v-9d8a3f95] {
    max-width: 100%
}

.description-title[data-v-9d8a3f95] {
    color: #495057;
    font-size: 13px;
    font-weight: 600;
    margin: 0 0 6px
}

@media (prefers-color-scheme:dark) {
    .description-title[data-v-9d8a3f95] {
        color: #fff
    }
}

.description-list[data-v-9d8a3f95] {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 0;
    padding: 0 0 4px
}

.description-item[data-v-9d8a3f95] {
    align-items: center;
    display: flex;
    gap: 6px;
    justify-content: flex-start
}

.description-icon[data-v-9d8a3f95] {
    color: #5cd4a1;
    flex-shrink: 0;
    height: 18px;
    width: 18px
}

.description-text[data-v-9d8a3f95] {
    color: #000;
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    margin: 0
}

@media (prefers-color-scheme:dark) {
    .description-text[data-v-9d8a3f95] {
        color: #fff
    }
}

.user .content[data-v-b1c443cb] pre {
    background: none;
    margin: 0;
    padding: 0
}

.user .content[data-v-b1c443cb] pre code {
    font-family: sans-serif;
    white-space: pre-wrap
}

.conversation-content[data-v-b1c443cb] {
    background: transparent;
    color: #666;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding-bottom: 180px;
    position: relative
}

.conversation .role[data-v-b1c443cb] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.conversation .avatar[data-v-b1c443cb] {
    height: 36px;
    width: 36px
}

.conversation .avatar img[data-v-b1c443cb] {
    width: 100%
}

.conversation .name[data-v-b1c443cb] {
    color: #252525;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal
}

.conversation-item-desc .bubble[data-v-b1c443cb] {
    align-items: flex-start;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    justify-content: flex-start;
    line-height: 20px;
    padding: 12px 16px
}

.conversation-item-desc .bubble code[data-v-b1c443cb],
.conversation-item-desc .bubble pre[data-v-b1c443cb] {
    font-size: 16px
}

.conversation-item-desc .button[data-v-b1c443cb] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 2px;
    line-height: normal;
    padding: 4px 8px
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc .button[data-v-b1c443cb] {
        border-color: #252525;
        color: #ddd
    }
}

.conversation-item-desc .button .icon[data-v-b1c443cb] {
    display: flex
}

@keyframes blink-animation-b1c443cb {
    0%,
    to {
        color: #000
    }
    50% {
        color: transparent
    }
}

@media (prefers-color-scheme:dark) {
    @keyframes blink-animation-b1c443cb {
        0%,
        to {
            color: #fff
        }
        50% {
            color: transparent
        }
    }
}

.conversation-item-desc.assistant .bubble[data-v-b1c443cb] .cursor {
    animation: blink-animation-b1c443cb .5s infinite;
    color: #606366;
    font-size: 12px
}

.conversation-item-desc[data-v-b1c443cb] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-width: 260px;
    width: 100%
}

.conversation-item-desc.user[data-v-b1c443cb] {
    align-items: flex-end;
    margin-left: 30px
}

.conversation-item-desc.assistant .bubble[data-v-b1c443cb] {
    border: 1px solid transparent;
    border-radius: 16px;
    box-sizing: border-box;
    min-width: 260px;
    padding-left: 0;
    padding-right: 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.retry[data-v-b1c443cb],
.conversation-item-desc.assistant .bubble.try_moa[data-v-b1c443cb] {
    align-items: center;
    background: #fafafa;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 18px 16px
}

.conversation-item-desc .retry .button[data-v-b1c443cb],
.conversation-item-desc .try_moa .button[data-v-b1c443cb] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 8px 16px
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.retry[data-v-b1c443cb],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-b1c443cb] {
        flex-direction: column
    }
}

.models-list[data-v-b1c443cb] {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px 8px
}

.models-list[data-v-b1c443cb],
.models-list .model[data-v-b1c443cb] {
    display: flex;
    flex-direction: column
}

.models-list .model[data-v-b1c443cb] {
    color: #232425;
    cursor: pointer;
    margin-top: 4px;
    padding: 10px 6px 10px 10px
}

.models-list .model .row[data-v-b1c443cb] {
    border-radius: 8px;
    color: #232425;
    gap: 4px;
    justify-content: space-between
}

.models-list .model .row[data-v-b1c443cb],
.models-selected[data-v-b1c443cb] {
    display: flex;
    flex-direction: row;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.models-selected[data-v-b1c443cb] {
    align-items: center;
    background-color: #f4f4f4;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 150%;
    padding: 6px 12px
}

@media (prefers-color-scheme:dark) {
    .models-list[data-v-b1c443cb] {
        background-color: #252525
    }
    .models-list .model .row[data-v-b1c443cb] {
        color: #ddd
    }
    .conversation-item-desc.assistant .bubble[data-v-b1c443cb] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble.retry[data-v-b1c443cb],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-b1c443cb] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .retry .button[data-v-b1c443cb],
    .conversation-item-desc .try_moa .button[data-v-b1c443cb] {
        background: #fafafa;
        color: #232425
    }
}

.controls[data-v-b1c443cb] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden
}

.controls[data-v-b1c443cb]::-webkit-scrollbar {
    display: none
}

.model-container[data-v-b1c443cb] {
    display: contents
}

.model[data-v-b1c443cb] {
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 12px 10px;
    transition: background-color .2s ease
}

.model[data-v-b1c443cb]:first-child {
    margin-top: 0
}

@media (hover:hover) {
    .model[data-v-b1c443cb]:hover {
        background-color: #f5f5f5
    }
}

.divider[data-v-b1c443cb] {
    background-color: #efefef;
    height: 1px;
    margin: 6px 0;
    width: 100%
}

.model .row[data-v-b1c443cb] {
    box-sizing: border-box;
    color: #606366;
    justify-content: space-between;
    min-height: 24px;
    width: 100%
}

.model .left[data-v-b1c443cb],
.model .row[data-v-b1c443cb] {
    align-items: flex-start;
    display: flex
}

.model .left[data-v-b1c443cb] {
    flex: 1;
    flex-direction: row;
    gap: 12px;
    min-width: 0;
    position: relative
}

.model .left .icon[data-v-b1c443cb] {
    align-items: center;
    background-color: transparent;
    border-radius: 50%;
    display: flex;
    flex-shrink: 0;
    height: 32px;
    justify-content: center;
    margin-top: 1px;
    overflow: hidden;
    width: 32px
}

.model .left .icon img[data-v-b1c443cb] {
    height: 32px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 32px
}

.model .left .text[data-v-b1c443cb] {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 6px;
    min-width: 0
}

.model-name[data-v-b1c443cb] {
    color: #606366;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2
}

.model-tags[data-v-b1c443cb] {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    gap: 4px
}

.price-tag[data-v-b1c443cb],
.quality-tag[data-v-b1c443cb],
.speed-tag[data-v-b1c443cb] {
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    line-height: 1.4;
    min-width: -moz-fit-content;
    min-width: fit-content;
    padding: 2px 8px;
    text-align: center;
    white-space: nowrap
}

.price-tag.price-budget[data-v-b1c443cb] {
    background-color: #e8f5e9;
    color: #2e7d32
}

.price-tag.price-moderate[data-v-b1c443cb] {
    background-color: #fff3e0;
    color: #f57c00
}

.price-tag.price-expensive[data-v-b1c443cb],
.price-tag.price-premium[data-v-b1c443cb] {
    background-color: #fce4ec;
    color: #c2185b
}

.speed-tag.speed-very-fast[data-v-b1c443cb] {
    background-color: #e3f2fd;
    color: #1976d2
}

.speed-tag.speed-fast[data-v-b1c443cb] {
    background-color: #e8f5e9;
    color: #388e3c
}

.speed-tag.speed-medium[data-v-b1c443cb] {
    background-color: #fff3e0;
    color: #f57c00
}

.speed-tag.speed-slow[data-v-b1c443cb] {
    background-color: #fce4ec;
    color: #c2185b
}

.quality-tag.quality-excellent[data-v-b1c443cb] {
    background-color: #e8f5e9;
    color: #2e7d32
}

.quality-tag.quality-high[data-v-b1c443cb] {
    background-color: #e3f2fd;
    color: #1976d2
}

.quality-tag.quality-medium[data-v-b1c443cb] {
    background-color: #fff3e0;
    color: #f57c00
}

.model .description[data-v-b1c443cb] {
    color: #909499;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 1.4;
    margin-top: 6px
}

.features[data-v-b1c443cb] {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-top: 8px
}

.feature-label[data-v-b1c443cb] {
    color: #909499;
    font-size: 11px;
    font-weight: 500;
    line-height: 1.2;
    margin: 0
}

.feature-tags[data-v-b1c443cb] {
    align-items: flex-start;
    display: flex;
    flex-wrap: wrap;
    gap: 4px
}

.feature-tag[data-v-b1c443cb] {
    background-color: #f5f5f5;
    border-radius: 12px;
    color: #666;
    font-size: 10px;
    line-height: 1.2;
    padding: 3px 8px;
    text-align: center;
    white-space: nowrap
}

.model-requirement-warning[data-v-b1c443cb] {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 8px
}

.requirement-label[data-v-b1c443cb] {
    align-items: center;
    color: #f57c00;
    display: flex;
    font-size: 11px;
    gap: 6px;
    line-height: 1.2;
    padding: 4px 0
}

.requirement-icon[data-v-b1c443cb] {
    flex-shrink: 0;
    font-size: 10px
}

.model .right[data-v-b1c443cb] {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    margin-left: 12px;
    margin-top: 3px
}

.model .right input[type=radio][data-v-b1c443cb] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    cursor: pointer;
    height: 16px;
    margin: 0;
    position: relative;
    width: 16px
}

.model .right input[type=radio][data-v-b1c443cb]:checked {
    background-color: #fff;
    border-color: #0f7fff
}

.model .right input[type=radio][data-v-b1c443cb]:checked:after {
    background: #0f7fff;
    border-radius: 50%;
    content: "";
    height: 10px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 10px
}

@media (prefers-color-scheme:dark) {
    @media (hover: hover) {
        .model[data-v-b1c443cb]:hover {
            background-color:#333
        }
    }
    .model .row[data-v-b1c443cb],
    .model-name[data-v-b1c443cb] {
        color: #ddd
    }
    .model .left .icon[data-v-b1c443cb] {
        background-color: #efefef
    }
    .model .description[data-v-b1c443cb] {
        color: #909499
    }
    .divider[data-v-b1c443cb] {
        background-color: #333
    }
    .feature-label[data-v-b1c443cb] {
        color: #999
    }
    .feature-tag[data-v-b1c443cb] {
        background-color: #333;
        color: #ccc
    }
    .price-tag.price-budget[data-v-b1c443cb] {
        background-color: #1b4332;
        color: #52c41a
    }
    .price-tag.price-moderate[data-v-b1c443cb] {
        background-color: #3d2914;
        color: #fa8c16
    }
    .price-tag.price-expensive[data-v-b1c443cb],
    .price-tag.price-premium[data-v-b1c443cb] {
        background-color: #3d1a27;
        color: #f759ab
    }
    .speed-tag.speed-very-fast[data-v-b1c443cb] {
        background-color: #0d2a4d;
        color: #40a9ff
    }
    .speed-tag.speed-fast[data-v-b1c443cb] {
        background-color: #1b4332;
        color: #52c41a
    }
    .speed-tag.speed-medium[data-v-b1c443cb] {
        background-color: #3d2914;
        color: #fa8c16
    }
    .speed-tag.speed-slow[data-v-b1c443cb] {
        background-color: #3d1a27;
        color: #f759ab
    }
    .quality-tag.quality-excellent[data-v-b1c443cb] {
        background-color: #1b4332;
        color: #52c41a
    }
    .quality-tag.quality-high[data-v-b1c443cb] {
        background-color: #0d2a4d;
        color: #40a9ff
    }
    .quality-tag.quality-medium[data-v-b1c443cb] {
        background-color: #3d2914;
        color: #fa8c16
    }
    .requirement-label[data-v-b1c443cb] {
        color: #fa8c16
    }
    .model .right input[type=radio][data-v-b1c443cb] {
        background-color: #fff;
        border-color: #666
    }
    .model .right input[type=radio][data-v-b1c443cb]:checked {
        background-color: #fff;
        border-color: #0f7fff
    }
}

@media (max-width:320px) {
    .price-tag[data-v-b1c443cb],
    .quality-tag[data-v-b1c443cb],
    .speed-tag[data-v-b1c443cb] {
        font-size: 8px;
        padding: 1px 4px
    }
}

.user .content[data-v-6c36b9b2] pre {
    background: none;
    margin: 0;
    padding: 0
}

.user .content[data-v-6c36b9b2] pre code {
    font-family: sans-serif;
    white-space: pre-wrap
}

.conversation-content[data-v-6c36b9b2] {
    background: transparent;
    color: #666;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding-bottom: 180px;
    position: relative
}

.conversation .role[data-v-6c36b9b2] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.conversation .avatar[data-v-6c36b9b2] {
    height: 36px;
    width: 36px
}

.conversation .avatar img[data-v-6c36b9b2] {
    width: 100%
}

.conversation .name[data-v-6c36b9b2] {
    color: #252525;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal
}

.conversation-item-desc .bubble[data-v-6c36b9b2] {
    align-items: flex-start;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    justify-content: flex-start;
    line-height: 20px;
    padding: 12px 16px
}

.conversation-item-desc .bubble code[data-v-6c36b9b2],
.conversation-item-desc .bubble pre[data-v-6c36b9b2] {
    font-size: 16px
}

.conversation-item-desc .button[data-v-6c36b9b2] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 2px;
    line-height: normal;
    padding: 4px 8px
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc .button[data-v-6c36b9b2] {
        border-color: #252525;
        color: #ddd
    }
}

.conversation-item-desc .button .icon[data-v-6c36b9b2] {
    display: flex
}

@keyframes blink-animation-6c36b9b2 {
    0%,
    to {
        color: #000
    }
    50% {
        color: transparent
    }
}

@media (prefers-color-scheme:dark) {
    @keyframes blink-animation-6c36b9b2 {
        0%,
        to {
            color: #fff
        }
        50% {
            color: transparent
        }
    }
}

.conversation-item-desc.assistant .bubble[data-v-6c36b9b2] .cursor {
    animation: blink-animation-6c36b9b2 .5s infinite;
    color: #606366;
    font-size: 12px
}

.conversation-item-desc[data-v-6c36b9b2] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-width: 260px;
    width: 100%
}

.conversation-item-desc.user[data-v-6c36b9b2] {
    align-items: flex-end;
    margin-left: 30px
}

.conversation-item-desc.assistant .bubble[data-v-6c36b9b2] {
    border: 1px solid transparent;
    border-radius: 16px;
    box-sizing: border-box;
    min-width: 260px;
    padding-left: 0;
    padding-right: 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.retry[data-v-6c36b9b2],
.conversation-item-desc.assistant .bubble.try_moa[data-v-6c36b9b2] {
    align-items: center;
    background: #fafafa;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 18px 16px
}

.conversation-item-desc .retry .button[data-v-6c36b9b2],
.conversation-item-desc .try_moa .button[data-v-6c36b9b2] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 8px 16px
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.retry[data-v-6c36b9b2],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-6c36b9b2] {
        flex-direction: column
    }
}

.models-list[data-v-6c36b9b2] {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px 8px
}

.models-list[data-v-6c36b9b2],
.models-list .model[data-v-6c36b9b2] {
    display: flex;
    flex-direction: column
}

.models-list .model[data-v-6c36b9b2] {
    color: #232425;
    cursor: pointer;
    margin-top: 4px;
    padding: 10px 6px 10px 10px
}

.models-list .model .row[data-v-6c36b9b2] {
    border-radius: 8px;
    color: #232425;
    gap: 4px;
    justify-content: space-between
}

.models-list .model .row[data-v-6c36b9b2],
.models-selected[data-v-6c36b9b2] {
    display: flex;
    flex-direction: row;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.models-selected[data-v-6c36b9b2] {
    align-items: center;
    background-color: #f4f4f4;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 150%;
    padding: 6px 12px
}

@media (prefers-color-scheme:dark) {
    .models-list[data-v-6c36b9b2] {
        background-color: #252525
    }
    .models-list .model .row[data-v-6c36b9b2] {
        color: #ddd
    }
    .conversation-item-desc.assistant .bubble[data-v-6c36b9b2] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble.retry[data-v-6c36b9b2],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-6c36b9b2] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .retry .button[data-v-6c36b9b2],
    .conversation-item-desc .try_moa .button[data-v-6c36b9b2] {
        background: #fafafa;
        color: #232425
    }
}

.controls[data-v-6c36b9b2] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden
}

.controls[data-v-6c36b9b2]::-webkit-scrollbar {
    display: none
}

.model-container[data-v-6c36b9b2] {
    display: contents
}

.model[data-v-6c36b9b2] {
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 12px 10px;
    transition: background-color .2s ease
}

.model[data-v-6c36b9b2]:first-child {
    margin-top: 0
}

.model.disabled[data-v-6c36b9b2] {
    cursor: not-allowed;
    opacity: .5
}

.model.disabled[data-v-6c36b9b2]:hover {
    background-color: transparent
}

@media (hover:hover) {
    .model[data-v-6c36b9b2]:hover:not(.disabled) {
        background-color: #f5f5f5
    }
}

.divider[data-v-6c36b9b2] {
    background-color: #efefef;
    height: 1px;
    margin: 6px 0;
    width: 100%
}

.model .row[data-v-6c36b9b2] {
    box-sizing: border-box;
    color: #606366;
    justify-content: space-between;
    min-height: 24px;
    width: 100%
}

.model .left[data-v-6c36b9b2],
.model .row[data-v-6c36b9b2] {
    align-items: center;
    display: flex
}

.model .left[data-v-6c36b9b2] {
    flex-direction: row;
    gap: 10px;
    position: relative
}

.model .left .icon[data-v-6c36b9b2] {
    align-items: center;
    background-color: transparent;
    border-radius: 50%;
    display: flex;
    flex-shrink: 0;
    height: 32px;
    justify-content: center;
    margin-top: 1px;
    overflow: hidden;
    width: 32px
}

.model .left .icon img[data-v-6c36b9b2] {
    height: 32px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 32px
}

.model .left .text[data-v-6c36b9b2] {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 6px;
    min-width: 0
}

.model-name[data-v-6c36b9b2] {
    color: #606366;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2
}

.model-tags[data-v-6c36b9b2] {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    gap: 4px
}

.price-tag[data-v-6c36b9b2],
.quality-tag[data-v-6c36b9b2],
.speed-tag[data-v-6c36b9b2] {
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    line-height: 1.4;
    min-width: -moz-fit-content;
    min-width: fit-content;
    padding: 2px 8px;
    text-align: center;
    white-space: nowrap
}

.price-tag.price-budget[data-v-6c36b9b2] {
    background-color: #e8f5e9;
    color: #2e7d32
}

.price-tag.price-moderate[data-v-6c36b9b2] {
    background-color: #fff3e0;
    color: #f57c00
}

.price-tag.price-expensive[data-v-6c36b9b2],
.price-tag.price-premium[data-v-6c36b9b2] {
    background-color: #fce4ec;
    color: #c2185b
}

.speed-tag.speed-very-fast[data-v-6c36b9b2] {
    background-color: #e3f2fd;
    color: #1976d2
}

.speed-tag.speed-fast[data-v-6c36b9b2] {
    background-color: #e8f5e9;
    color: #388e3c
}

.speed-tag.speed-medium[data-v-6c36b9b2] {
    background-color: #fff3e0;
    color: #f57c00
}

.speed-tag.speed-slow[data-v-6c36b9b2] {
    background-color: #fce4ec;
    color: #c2185b
}

.quality-tag.quality-excellent[data-v-6c36b9b2] {
    background-color: #e8f5e9;
    color: #2e7d32
}

.quality-tag.quality-high[data-v-6c36b9b2] {
    background-color: #e3f2fd;
    color: #1976d2
}

.quality-tag.quality-medium[data-v-6c36b9b2] {
    background-color: #fff3e0;
    color: #f57c00
}

.model .description[data-v-6c36b9b2],
.model .tip[data-v-6c36b9b2] {
    color: #909499;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px
}

.features[data-v-6c36b9b2] {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-top: 8px
}

.feature-label[data-v-6c36b9b2] {
    color: #909499;
    font-size: 11px;
    font-weight: 500;
    line-height: 1.2;
    margin: 0
}

.feature-tags[data-v-6c36b9b2] {
    align-items: flex-start;
    display: flex;
    flex-wrap: wrap;
    gap: 4px
}

.feature-tag[data-v-6c36b9b2] {
    background-color: #f5f5f5;
    border-radius: 12px;
    color: #666;
    font-size: 10px;
    line-height: 1.2;
    padding: 3px 8px;
    text-align: center;
    white-space: nowrap
}

.model-requirement-warning[data-v-6c36b9b2] {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 8px
}

.requirement-label[data-v-6c36b9b2] {
    align-items: center;
    color: #f57c00;
    display: flex;
    font-size: 11px;
    gap: 6px;
    line-height: 1.2;
    padding: 4px 0
}

.requirement-icon[data-v-6c36b9b2] {
    flex-shrink: 0;
    font-size: 10px
}

.model .right[data-v-6c36b9b2] {
    align-items: center;
    display: flex;
    flex-direction: row
}

.model .right input[type=radio][data-v-6c36b9b2] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    cursor: pointer;
    height: 16px;
    margin: 0;
    position: relative;
    width: 16px
}

.model .right input[type=radio][data-v-6c36b9b2]:checked {
    background-color: #fff;
    border-color: #0f7fff
}

.model .right input[type=radio][data-v-6c36b9b2]:checked:after {
    background: #0f7fff;
    border-radius: 50%;
    content: "";
    height: 10px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 10px
}

.model.model-auto .left .icon[data-v-6c36b9b2] {
    height: 32px;
    margin-top: 2px;
    width: 32px
}

.model.model-auto .left .icon img[data-v-6c36b9b2] {
    height: 32px;
    width: 32px
}

@media (prefers-color-scheme:dark) {
    .model .row[data-v-6c36b9b2] {
        color: #ddd
    }
    @media (hover:hover) {
        .model[data-v-6c36b9b2]:hover:not(.disabled) {
            background-color: #333
        }
    }
    .model .description[data-v-6c36b9b2],
    .model .tip[data-v-6c36b9b2] {
        color: #909499
    }
    .divider[data-v-6c36b9b2] {
        background-color: #333
    }
    .model .left .icon[data-v-6c36b9b2] {
        background-color: #efefef
    }
    .model .right input[type=radio][data-v-6c36b9b2] {
        background-color: #fff;
        border-color: #666
    }
    .model .right input[type=radio][data-v-6c36b9b2]:checked {
        background-color: #fff;
        border-color: #0f7fff
    }
    .model-name[data-v-6c36b9b2] {
        color: #ddd
    }
    .feature-label[data-v-6c36b9b2] {
        color: #999
    }
    .feature-tag[data-v-6c36b9b2] {
        background-color: #333;
        color: #ccc
    }
    .price-tag.price-budget[data-v-6c36b9b2] {
        background-color: #1b4332;
        color: #52c41a
    }
    .price-tag.price-moderate[data-v-6c36b9b2] {
        background-color: #3d2914;
        color: #fa8c16
    }
    .price-tag.price-expensive[data-v-6c36b9b2],
    .price-tag.price-premium[data-v-6c36b9b2] {
        background-color: #3d1a27;
        color: #f759ab
    }
    .speed-tag.speed-very-fast[data-v-6c36b9b2] {
        background-color: #0d2a4d;
        color: #40a9ff
    }
    .speed-tag.speed-fast[data-v-6c36b9b2] {
        background-color: #1b4332;
        color: #52c41a
    }
    .speed-tag.speed-medium[data-v-6c36b9b2] {
        background-color: #3d2914;
        color: #fa8c16
    }
    .speed-tag.speed-slow[data-v-6c36b9b2] {
        background-color: #3d1a27;
        color: #f759ab
    }
    .quality-tag.quality-excellent[data-v-6c36b9b2] {
        background-color: #1b4332;
        color: #52c41a
    }
    .quality-tag.quality-high[data-v-6c36b9b2] {
        background-color: #0d2a4d;
        color: #40a9ff
    }
    .quality-tag.quality-medium[data-v-6c36b9b2] {
        background-color: #3d2914;
        color: #fa8c16
    }
    .requirement-label[data-v-6c36b9b2] {
        color: #fa8c16
    }
}

.model-select-bar[data-v-e52b60af] {
    box-sizing: border-box;
    width: 100%
}

.bar-content[data-v-e52b60af] {
    background: transparent;
    gap: 12px;
    padding: 0
}

.bar-content[data-v-e52b60af],
.model-info[data-v-e52b60af] {
    align-items: center;
    display: flex
}

.model-info[data-v-e52b60af] {
    gap: 8px
}

.model-label[data-v-e52b60af] {
    color: #666;
    font-size: 14px;
    font-weight: 400;
    white-space: nowrap
}

.model-selector[data-v-e52b60af] {
    display: flex
}

.model-button[data-v-e52b60af] {
    align-items: center;
    background-color: #f4f4f4;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 150%;
    padding: 6px 12px;
    transition: background-color .2s;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.model-button[data-v-e52b60af]:hover {
    background-color: #e8e8e8
}

.model-selected[data-v-e52b60af] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.model-selected .icon[data-v-e52b60af] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 16px;
    justify-content: center;
    width: 16px
}

.model-selected .icon img[data-v-e52b60af] {
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100%
}

.trigger-icon[data-v-e52b60af] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 16px;
    justify-content: center;
    transition: transform .3s;
    width: 16px
}

.trigger-icon.active[data-v-e52b60af] {
    transform: rotate(180deg)
}

.models-list[data-v-e52b60af] {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px 8px
}

@media (prefers-color-scheme:dark) {
    .model-label[data-v-e52b60af] {
        color: #bbb
    }
    .model-button[data-v-e52b60af] {
        background-color: #333;
        color: #ddd
    }
    .model-button[data-v-e52b60af]:hover {
        background-color: #444
    }
    .model-selected .icon[data-v-e52b60af] {
        filter: brightness(.9)
    }
    .models-list[data-v-e52b60af] {
        background-color: #1a1a1a;
        box-shadow: 0 4px 15px #0000004d
    }
}

.slide-reference-card[data-v-03b19248] {
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin: 8px 0;
    overflow: hidden
}

.slide-reference-header[data-v-03b19248] {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    padding: 8px 12px
}

.slide-reference-info[data-v-03b19248] {
    display: flex;
    flex-wrap: wrap;
    gap: 8px
}

.page-badge[data-v-03b19248],
.version-badge[data-v-03b19248] {
    background-color: #eaeaea;
    border-radius: 4px;
    color: #909499;
    font-size: 12px;
    font-weight: 500;
    padding: 2px 8px
}

.slide-reference-content[data-v-03b19248] {
    padding: 0
}

.slide-image[data-v-03b19248] {
    max-width: 100%
}

.slide-placeholder[data-v-03b19248] {
    align-items: center;
    background-color: #fff;
    color: #888;
    display: flex;
    flex-direction: column;
    height: 180px;
    justify-content: center
}

.placeholder-icon[data-v-03b19248] {
    color: #aaa;
    margin-bottom: 12px
}

.placeholder-text[data-v-03b19248] {
    font-size: 14px
}

.element-highlight[data-v-03b19248] {
    background-color: #f0f0f0;
    border-radius: 4px;
    color: #555;
    font-family: monospace;
    font-size: 12px;
    margin-top: 8px;
    padding: 6px
}

.template-reference-card[data-v-dca6a30d] {
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin: 8px 0;
    overflow: hidden
}

.template-reference-header[data-v-dca6a30d] {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    padding: 8px 12px
}

.template-reference-info[data-v-dca6a30d] {
    align-items: center;
    display: flex;
    flex-wrap: nowrap;
    gap: 8px;
    overflow: hidden
}

.template-badge[data-v-dca6a30d] {
    background-color: #eaeaea;
    border-radius: 4px;
    color: #909499;
    font-size: 12px;
    font-weight: 400;
    padding: 2px 8px;
    text-transform: uppercase
}

.template-name[data-v-dca6a30d] {
    color: #333;
    flex: 1;
    font-size: 14px;
    font-weight: 400;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.template-reference-content[data-v-dca6a30d] {
    padding: 0
}

.template-image[data-v-dca6a30d] {
    max-width: 100%
}

.template-placeholder[data-v-dca6a30d] {
    align-items: center;
    background-color: #fff;
    color: #888;
    display: flex;
    flex-direction: column;
    height: 180px;
    justify-content: center
}

.placeholder-icon[data-v-dca6a30d] {
    color: #aaa;
    margin-bottom: 12px
}

.placeholder-text[data-v-dca6a30d] {
    font-size: 14px
}

.template-meta[data-v-dca6a30d] {
    background-color: #fafafa;
    border-top: 1px solid #eee;
    padding: 8px 12px
}

.meta-item[data-v-dca6a30d] {
    align-items: center;
    display: flex;
    font-size: 12px;
    gap: 8px;
    margin: 2px 0
}

.meta-label[data-v-dca6a30d] {
    color: #666;
    font-weight: 500;
    min-width: 24px
}

.meta-value[data-v-dca6a30d] {
    background-color: #f0f0f0;
    border-radius: 3px;
    color: #333;
    font-family: monospace;
    font-size: 11px;
    padding: 1px 4px
}

.cooperation-canvas[data-v-45810d93] {
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 16px
}

.canvas-header[data-v-45810d93] {
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 12px
}

.canvas-header h2[data-v-45810d93] {
    color: #333;
    font-size: 18px;
    margin: 0
}

.status-info[data-v-45810d93] {
    color: #666;
    font-size: 14px
}

.pending-count[data-v-45810d93] {
    color: #f39c12;
    font-weight: 700
}

.canvas-content[data-v-45810d93] {
    flex: 1;
    overflow-y: auto
}

.file-system-section[data-v-45810d93],
.task-status-section[data-v-45810d93] {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px #0000001a;
    margin-bottom: 16px;
    padding: 16px
}

.file-system-section h3[data-v-45810d93],
.task-status-section h3[data-v-45810d93] {
    color: #333;
    font-size: 16px;
    margin: 0 0 12px
}

.task-list[data-v-45810d93] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.task-item[data-v-45810d93] {
    background: #fafafa;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 12px
}

.task-item.status-pending[data-v-45810d93] {
    border-left: 4px solid #f39c12
}

.task-item.status-running[data-v-45810d93] {
    border-left: 4px solid #3498db
}

.task-item.status-success[data-v-45810d93] {
    border-left: 4px solid #27ae60
}

.task-item.status-error[data-v-45810d93],
.task-item.status-failure[data-v-45810d93] {
    border-left: 4px solid #e74c3c
}

.task-header[data-v-45810d93] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px
}

.task-id[data-v-45810d93] {
    color: #666;
    font-family: monospace;
    font-size: 12px
}

.task-status[data-v-45810d93] {
    border-radius: 12px;
    color: #fff;
    font-size: 12px;
    font-weight: 700;
    padding: 2px 8px
}

.task-item.status-pending .task-status[data-v-45810d93] {
    background: #f39c12
}

.task-item.status-running .task-status[data-v-45810d93] {
    background: #3498db
}

.task-item.status-success .task-status[data-v-45810d93] {
    background: #27ae60
}

.task-item.status-error .task-status[data-v-45810d93],
.task-item.status-failure .task-status[data-v-45810d93] {
    background: #e74c3c
}

.task-timestamp[data-v-45810d93] {
    color: #888;
    font-size: 12px
}

.task-details[data-v-45810d93] {
    margin-top: 8px
}

.error-info[data-v-45810d93],
.result-preview[data-v-45810d93] {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 8px
}

.error-info pre[data-v-45810d93],
.result-preview pre[data-v-45810d93] {
    color: #555;
    font-size: 12px;
    margin: 4px 0 0;
    white-space: pre-wrap
}

.error-info[data-v-45810d93] {
    background: #fff5f5;
    border: 1px solid #fed7d7
}

.error-info pre[data-v-45810d93] {
    color: #c53030
}

.file-tree .file-item[data-v-45810d93] {
    align-items: center;
    border-radius: 4px;
    display: flex;
    font-size: 14px;
    justify-content: space-between;
    padding: 4px 8px
}

.file-tree .file-item[data-v-45810d93]:hover {
    background: #f0f0f0
}

.file-tree .file-item.empty-folder[data-v-45810d93] {
    opacity: .6
}

.file-path[data-v-45810d93] {
    color: #333;
    font-family: monospace
}

.file-tree .empty-folder .file-path[data-v-45810d93] {
    color: #888
}

.file-type[data-v-45810d93] {
    color: #666;
    font-size: 12px;
    text-transform: uppercase
}

.empty-indicator[data-v-45810d93] {
    color: #aaa;
    font-size: 11px;
    font-style: italic;
    margin-left: 8px
}

.empty-workspace[data-v-45810d93] {
    color: #888;
    font-style: italic;
    padding: 20px;
    text-align: center
}

.video-canvas-enhanced[data-v-e1607ee1] {
    background: #000;
    height: 100%;
    min-height: 600px;
    width: 100%
}

.tab-content[data-v-a45a3b5b] {
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100%;
    overflow: hidden
}

.error-container[data-v-a45a3b5b],
.loading-container[data-v-a45a3b5b] {
    align-items: center;
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 16px;
    justify-content: center;
    padding: 32px
}

.loading-text[data-v-a45a3b5b] {
    color: #6b7280;
    font-size: 14px
}

.error-message[data-v-a45a3b5b] {
    color: #dc2626;
    margin-bottom: 16px;
    text-align: center
}

.file-browser[data-v-a45a3b5b] {
    display: flex;
    flex: 1;
    height: 100%;
    overflow: hidden
}

.file-tree-panel[data-v-a45a3b5b] {
    background: #fff;
    border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    min-width: 280px;
    width: 280px
}

.file-tree[data-v-a45a3b5b] {
    flex: 1;
    overflow-y: auto;
    padding: 8px
}

.tree-item[data-v-a45a3b5b] {
    margin-bottom: 2px
}

.directory-item[data-v-a45a3b5b] {
    cursor: pointer
}

.directory-header[data-v-a45a3b5b] {
    align-items: center;
    border-radius: 4px;
    display: flex;
    gap: 6px;
    padding: 6px 8px;
    transition: background .2s
}

.directory-header[data-v-a45a3b5b]:hover {
    background: #f3f4f6
}

.expand-icon[data-v-a45a3b5b] {
    color: #333;
    flex-shrink: 0;
    height: 10px;
    transition: transform .2s;
    width: 10px
}

.expand-icon.expanded[data-v-a45a3b5b] {
    transform: rotate(90deg)
}

.file-icon[data-v-a45a3b5b] {
    color: #6b7280;
    flex-shrink: 0;
    height: 18px;
    width: 18px
}

.directory-item .file-icon[data-v-a45a3b5b] {
    color: #232425
}

.file-name[data-v-a45a3b5b] {
    color: #1f2937;
    flex: 1;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.directory-item .file-name[data-v-a45a3b5b],
.file-item-content .file-icon[data-v-a45a3b5b],
.file-item-content .file-name[data-v-a45a3b5b] {
    color: #232425
}

.directory-children[data-v-a45a3b5b] {
    margin-left: 12px
}

.child-item[data-v-a45a3b5b] {
    margin-left: 10px;
    position: relative
}

.file-item[data-v-a45a3b5b] {
    cursor: pointer
}

.file-item-content[data-v-a45a3b5b] {
    align-items: center;
    border-radius: 4px;
    display: flex;
    gap: 8px;
    padding: 6px 8px;
    transition: background .2s
}

.file-item-content[data-v-a45a3b5b]:hover {
    background: #f3f4f6
}

.download-section[data-v-a45a3b5b] {
    padding: 12px
}

.download-button[data-v-a45a3b5b] {
    align-items: center;
    background: #232425;
    border: none;
    border-radius: 8px;
    color: #fff;
    display: flex;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 6px;
    justify-content: center;
    line-height: 21px;
    padding: 8px 16px;
    transition: background-color .2s;
    width: 100%;
    --n-ripple-color: #6b7280 !important;
    --n-text-color-hover: #f3f4f6 !important;
    --n-text-color-pressed: #e5e7eb !important;
    --n-text-color-focus: #f3f4f6 !important;
    --n-border-hover: 1px solid #6b7280 !important;
    --n-border-pressed: 1px solid #4b5563 !important;
    --n-border-focus: 1px solid #6b7280 !important
}

.download-button[data-v-a45a3b5b] .n-button__content {
    align-items: center;
    display: flex;
    gap: 6px !important
}

.download-button[data-v-a45a3b5b]:hover:not(:disabled) {
    background: #3a3b3d
}

.download-button[data-v-a45a3b5b]:disabled {
    background: #9ca3af;
    color: #d1d5db;
    cursor: not-allowed
}

.download-icon[data-v-a45a3b5b] {
    height: 12px;
    width: 12px
}

.file-preview-panel[data-v-a45a3b5b] {
    background: #fff;
    overflow: hidden
}

.file-preview[data-v-a45a3b5b],
.file-preview-panel[data-v-a45a3b5b] {
    display: flex;
    flex: 1;
    flex-direction: column
}

.file-preview[data-v-a45a3b5b] {
    height: 100%
}

.file-header[data-v-a45a3b5b] {
    border-bottom: 1px solid #e5e7eb;
    justify-content: space-between;
    padding: 12px 16px
}

.file-header[data-v-a45a3b5b],
.file-header-left[data-v-a45a3b5b] {
    align-items: center;
    display: flex
}

.file-header-left[data-v-a45a3b5b] {
    flex: 1;
    gap: 16px
}

.file-title[data-v-a45a3b5b] {
    align-items: center;
    display: flex;
    gap: 8px
}

.file-title .file-name[data-v-a45a3b5b] {
    color: #000;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

.file-meta[data-v-a45a3b5b] {
    color: #6b7280;
    font-size: 14px;
    gap: 16px
}

.file-actions[data-v-a45a3b5b],
.file-meta[data-v-a45a3b5b] {
    align-items: center;
    display: flex
}

.file-actions[data-v-a45a3b5b] {
    gap: 4px
}

.file-actions[data-v-a45a3b5b] .n-button {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    min-width: auto !important;
    padding: 4px !important;
    --n-color: transparent !important;
    --n-color-hover: transparent !important;
    --n-color-pressed: transparent !important;
    --n-color-focus: transparent !important;
    --n-border: none !important;
    --n-border-hover: none !important;
    --n-border-pressed: none !important;
    --n-border-focus: none !important;
    --n-ripple-color: transparent !important
}

.file-actions[data-v-a45a3b5b] .n-button:hover {
    background: transparent !important
}

.file-actions .action-icon[data-v-a45a3b5b] {
    color: #6b7280;
    height: 20px;
    transition: color .2s;
    width: 20px
}

.file-actions[data-v-a45a3b5b] .n-button:hover .action-icon,
.file-actions[data-v-a45a3b5b] .n-button:hover svg {
    color: #374151
}

.file-content[data-v-a45a3b5b] {
    display: flex;
    flex: 1;
    flex-direction: column
}

.file-content[data-v-a45a3b5b],
.text-content[data-v-a45a3b5b] {
    min-height: 0;
    overflow-y: auto
}

.text-content[data-v-a45a3b5b] {
    flex: 1;
    overflow-x: auto;
    padding: 16px
}

.text-content pre[data-v-a45a3b5b] {
    font-family: Monaco, Menlo, Ubuntu Mono, monospace;
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word
}

.text-content code[data-v-a45a3b5b] {
    color: #1f2937
}

.image-content[data-v-a45a3b5b] {
    align-items: center;
    display: flex;
    flex: 1;
    justify-content: center;
    padding: 16px
}

.image-content img[data-v-a45a3b5b] {
    max-height: 100%;
    max-width: 100%;
    -o-object-fit: contain;
    object-fit: contain
}

.binary-content[data-v-a45a3b5b] {
    align-items: center;
    display: flex;
    flex: 1;
    justify-content: center
}

.binary-message[data-v-a45a3b5b] {
    color: #6b7280;
    text-align: center
}

.binary-icon[data-v-a45a3b5b] {
    color: #9ca3af;
    display: block;
    height: 48px;
    margin: 0 auto 16px;
    width: 48px
}

.no-preview[data-v-a45a3b5b] {
    align-items: center;
    display: flex;
    flex: 1;
    justify-content: center
}

.no-preview-content[data-v-a45a3b5b] {
    color: #6b7280;
    text-align: center
}

.preview-icon[data-v-a45a3b5b] {
    color: #9ca3af;
    display: block;
    height: 48px;
    margin: 0 auto 16px;
    width: 48px
}

.empty-directory[data-v-a45a3b5b] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: center;
    padding: 32px;
    text-align: center
}

.empty-icon[data-v-a45a3b5b] {
    color: #9ca3af;
    height: 32px;
    opacity: .6;
    width: 32px
}

.empty-text[data-v-a45a3b5b] {
    color: #9ca3af;
    font-size: 14px
}

@media (prefers-color-scheme:dark) {
    .file-preview-panel[data-v-a45a3b5b],
    .file-tree-panel[data-v-a45a3b5b] {
        background: #393939;
        border-color: #666
    }
    .file-header[data-v-a45a3b5b] {
        background: #111827;
        border-color: #374151
    }
    .file-name[data-v-a45a3b5b] {
        color: #f9fafb
    }
    .directory-item .file-icon[data-v-a45a3b5b],
    .directory-item .file-name[data-v-a45a3b5b],
    .file-item-content .file-icon[data-v-a45a3b5b],
    .file-item-content .file-name[data-v-a45a3b5b] {
        color: #e5e7eb
    }
    .expand-icon[data-v-a45a3b5b] {
        color: #d1d5db
    }
    .file-title .file-name[data-v-a45a3b5b] {
        color: #f9fafb
    }
    .directory-header[data-v-a45a3b5b]:hover,
    .file-item-content[data-v-a45a3b5b]:hover {
        background: #374151
    }
    .file-actions .action-icon[data-v-a45a3b5b],
    .file-meta[data-v-a45a3b5b] {
        color: #9ca3af
    }
    .file-actions[data-v-a45a3b5b] .n-button:hover .action-icon,
    .file-actions[data-v-a45a3b5b] .n-button:hover svg {
        color: #f3f4f6
    }
    .text-content code[data-v-a45a3b5b] {
        color: #f9fafb
    }
    .download-button[data-v-a45a3b5b] {
        background: #374151;
        color: #f9fafb;
        --n-ripple-color: #9ca3af !important;
        --n-text-color-hover: #f9fafb !important;
        --n-text-color-pressed: #e5e7eb !important;
        --n-text-color-focus: #f9fafb !important;
        --n-border-hover: 1px solid #9ca3af !important;
        --n-border-pressed: 1px solid #6b7280 !important;
        --n-border-focus: 1px solid #9ca3af !important
    }
    .download-button[data-v-a45a3b5b]:hover:not(:disabled) {
        background: #4b5563
    }
    .download-button[data-v-a45a3b5b]:disabled {
        background: #4b5563;
        color: #6b7280
    }
    .child-item[data-v-a45a3b5b]:before {
        background: #374151
    }
}

@media (max-width:768px) {
    .file-browser[data-v-a45a3b5b] {
        flex-direction: column
    }
    .file-tree-panel[data-v-a45a3b5b] {
        border-bottom: 1px solid #e5e7eb;
        border-right: none;
        height: 200px;
        width: 100%
    }
    .file-meta[data-v-a45a3b5b] {
        display: none
    }
}

.preview-tab[data-v-35c90f22] {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden
}

.preview-container[data-v-35c90f22] {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden
}

.empty-state[data-v-35c90f22] {
    color: #6b7280;
    flex-direction: column;
    gap: 12px;
    height: 100%
}

.empty-icon[data-v-35c90f22],
.empty-state[data-v-35c90f22] {
    align-items: center;
    display: flex;
    justify-content: center
}

.empty-icon[data-v-35c90f22] {
    color: #909499
}

.empty-icon[data-v-35c90f22],
.empty-icon svg[data-v-35c90f22] {
    height: 24px;
    width: 24px
}

.empty-default[data-v-35c90f22] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 12px;
    justify-content: center
}

.empty-text[data-v-35c90f22] {
    color: #909499;
    font-size: 14px;
    font-weight: 400
}

.carousel-container[data-v-35c90f22] {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%
}

.carousel-wrapper[data-v-35c90f22] {
    align-items: flex-start;
    display: flex;
    height: 300px;
    justify-content: center;
    max-width: 100%;
    overflow: hidden;
    padding-top: 90px;
    position: relative;
    width: 500px
}

@media (max-width:768px) {
    .carousel-wrapper[data-v-35c90f22] {
        height: 300px
    }
}

.carousel-track[data-v-35c90f22] {
    display: flex;
    flex-direction: column;
    position: relative;
    top: -64px;
    transition: transform .5s cubic-bezier(.25, .46, .45, .94);
    will-change: transform
}

@media (max-width:768px) {
    .carousel-track[data-v-35c90f22] {
        top: -96px
    }
}

.carousel-item[data-v-35c90f22] {
    align-items: center;
    box-sizing: border-box;
    color: #909499;
    display: flex;
    flex-shrink: 0;
    font-weight: 500;
    height: 64px;
    justify-content: center;
    line-height: 1.4;
    opacity: .4;
    padding: 12px 20px;
    text-align: center;
    transform: scale(.92);
    transition: opacity .5s cubic-bezier(.25, .46, .45, .94), transform .5s cubic-bezier(.25, .46, .45, .94), color .5s cubic-bezier(.25, .46, .45, .94);
    will-change: transform, opacity, color
}

@media (max-width:768px) {
    .carousel-item[data-v-35c90f22] {
        height: 96px;
        padding: 16px 20px
    }
}

.carousel-item.active[data-v-35c90f22] {
    color: #232425;
    opacity: 1;
    transform: scale(1.02)
}

.carousel-item.next[data-v-35c90f22],
.carousel-item.prev[data-v-35c90f22] {
    color: #909499;
    opacity: .7;
    transform: scale(.97)
}

.carousel-fade-bottom[data-v-35c90f22],
.carousel-fade-top[data-v-35c90f22] {
    height: 20px;
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    z-index: 10
}

.carousel-fade-top[data-v-35c90f22] {
    background: linear-gradient(180deg, #fff, #fff0);
    top: 72px
}

.carousel-fade-bottom[data-v-35c90f22] {
    background: linear-gradient(0deg, #fff, #fff0);
    bottom: 0
}

.preview-content[data-v-35c90f22] {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden
}

.preview-header[data-v-35c90f22] {
    align-items: center;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    padding: 12px 16px
}

.preview-info[data-v-35c90f22] {
    flex: 1;
    margin-right: 12px;
    min-width: 0
}

.preview-controls[data-v-35c90f22],
.preview-info[data-v-35c90f22] {
    align-items: flex-start;
    display: flex
}

.preview-controls[data-v-35c90f22] {
    width: 100%
}

.file-tabs[data-v-35c90f22] {
    align-items: flex-start;
    display: flex;
    flex-wrap: wrap;
    gap: 6px 4px
}

.file-tab[data-v-35c90f22] {
    background: #f3f4f6;
    border: none;
    border-radius: 8px;
    color: #6b7280;
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
    outline: none;
    padding: 8px 16px;
    transition: all .2s;
    white-space: nowrap
}

.file-tab[data-v-35c90f22]:hover {
    background: #e5e7eb;
    color: #374151
}

.file-tab-active[data-v-35c90f22] {
    background: #393939;
    color: #fff
}

.file-tab-active[data-v-35c90f22]:hover {
    background: #374151;
    color: #fff
}

.preview-actions[data-v-35c90f22] {
    display: flex;
    gap: 4px
}

.action-button svg[data-v-35c90f22] {
    height: 100%;
    width: 100%
}

.action-button[data-v-35c90f22] {
    align-items: center;
    border-radius: 4px;
    color: #6b7280;
    cursor: pointer;
    display: flex;
    height: 28px;
    justify-content: center;
    transition: all .2s;
    width: 28px
}

.action-button[data-v-35c90f22]:hover {
    background: #e5e7eb;
    color: #374151
}

.width-toggle[data-v-35c90f22] {
    position: relative
}

.iframe-container[data-v-35c90f22] {
    align-items: stretch;
    display: flex;
    flex: 1;
    justify-content: center;
    overflow: hidden;
    position: relative
}

.iframe-wrapper[data-v-35c90f22] {
    height: 100%;
    position: relative;
    transition: all .3s ease;
    width: 100%
}

.iframe-wrapper.width-full[data-v-35c90f22] {
    width: 100%
}

.iframe-wrapper.width-tablet[data-v-35c90f22] {
    border-radius: 8px;
    box-shadow: 0 0 20px #0000001a;
    max-width: 100%;
    overflow: hidden;
    width: 1024px
}

.iframe-wrapper.width-mobile[data-v-35c90f22] {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 0 20px #00000026;
    max-width: 100%;
    overflow: hidden;
    width: 390px
}

.iframe-loading[data-v-35c90f22] {
    align-items: center;
    background: #fff;
    bottom: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
    justify-content: center;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 10
}

.loading-text[data-v-35c90f22] {
    color: #6b7280;
    font-size: 14px
}

.preview-iframe[data-v-35c90f22] {
    background: #fff;
    border: none;
    height: 100%;
    transition: opacity .2s;
    width: 100%
}

.iframe-hidden[data-v-35c90f22] {
    opacity: 0
}

@media (prefers-color-scheme:dark) {
    .carousel-item[data-v-35c90f22],
    .empty-icon[data-v-35c90f22],
    .empty-text[data-v-35c90f22] {
        color: #a1a1aa
    }
    .carousel-item.active[data-v-35c90f22] {
        color: #fff
    }
    .carousel-item.next[data-v-35c90f22],
    .carousel-item.prev[data-v-35c90f22] {
        color: #71717a
    }
    .carousel-fade-top[data-v-35c90f22] {
        background: linear-gradient(180deg, #1f2937, #1f293700);
        top: 72px
    }
    .carousel-fade-bottom[data-v-35c90f22] {
        background: linear-gradient(0deg, #1f2937, #1f293700)
    }
    .file-tab[data-v-35c90f22] {
        background: #374151;
        color: #9ca3af
    }
    .file-tab[data-v-35c90f22]:hover {
        background: #4b5563;
        color: #f9fafb
    }
    .file-tab-active[data-v-35c90f22] {
        background: #f9fafb;
        color: #1f2937
    }
    .file-tab-active[data-v-35c90f22]:hover {
        background: #e5e7eb;
        color: #1f2937
    }
    .iframe-loading[data-v-35c90f22] {
        background: #393939
    }
    .loading-text[data-v-35c90f22] {
        color: #9ca3af
    }
    .preview-header[data-v-35c90f22] {
        background: #111827;
        border-bottom-color: #374151
    }
    .preview-title[data-v-35c90f22] {
        color: #f9fafb
    }
    .action-button[data-v-35c90f22],
    .preview-file[data-v-35c90f22] {
        color: #9ca3af
    }
    .action-button[data-v-35c90f22]:hover {
        background: #374151;
        color: #f9fafb
    }
    .preview-iframe[data-v-35c90f22] {
        background: #393939
    }
    .iframe-wrapper.width-tablet[data-v-35c90f22] {
        box-shadow: 0 0 20px #0000004d
    }
    .iframe-wrapper.width-mobile[data-v-35c90f22] {
        border-color: #374151;
        box-shadow: 0 0 20px #0006
    }
}

.border[data-v-101ea230] {
    border-top: 1px solid #e5e7eb;
    margin: 20px 0
}

@media (prefers-color-scheme:dark) {
    .border[data-v-101ea230] {
        border-color: #374151
    }
}

.tab-content[data-v-101ea230] {
    height: 100%;
    overflow-y: auto;
    padding: 16px
}

.deploy-container[data-v-101ea230] {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin: 0 auto
}

.card[data-v-101ea230] {
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 12px;
    padding: 24px
}

.vertical-space[data-v-101ea230] {
    display: flex;
    flex-direction: column;
    gap: 10px
}

h3[data-v-101ea230] {
    color: #232425;
    font-size: 16px
}

h3[data-v-101ea230],
h4[data-v-101ea230] {
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

h4[data-v-101ea230] {
    color: #000;
    font-size: 14px;
    margin: 0 0 12px
}

.setup-header[data-v-101ea230] {
    align-items: center;
    display: flex;
    justify-content: space-between
}

.setup-header h3[data-v-101ea230] {
    margin: 0
}

.api-key-status-card[data-v-101ea230] {
    border: none;
    padding: 0
}

.api-key-status-header[data-v-101ea230] {
    align-items: center;
    display: flex;
    justify-content: space-between
}

.masked-key-display[data-v-101ea230],
.validation-message[data-v-101ea230] {
    align-items: center;
    display: flex;
    gap: 8px
}

.masked-key-display .label[data-v-101ea230],
.validation-message .label[data-v-101ea230] {
    color: #6b7280;
    font-weight: 500;
    min-width: 80px
}

.masked-key[data-v-101ea230] {
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    color: #1f2937;
    font-family: Monaco, Menlo, Ubuntu Mono, monospace;
    font-size: 14px;
    padding: 4px 8px
}

.success-text[data-v-101ea230] {
    color: #10b981;
    font-weight: 500
}

.setup-instructions[data-v-101ea230] {
    margin-bottom: 24px
}

.setup-instructions h4[data-v-101ea230] {
    color: #374151;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px
}

.setup-instructions ol[data-v-101ea230] {
    color: #6b7280;
    counter-reset: item;
    list-style-position: outside !important;
    list-style-type: decimal !important;
    margin-bottom: 16px;
    margin-left: 20px
}

.setup-instructions li[data-v-101ea230] {
    display: list-item !important;
    line-height: 1.5;
    list-style: decimal !important;
    margin-bottom: 8px
}

.setup-instructions a[data-v-101ea230] {
    color: #3b82f6;
    font-weight: 500;
    text-decoration: none
}

.setup-instructions a[data-v-101ea230]:hover {
    text-decoration: underline
}

.checkbox-description[data-v-101ea230] {
    color: #6b7280;
    margin-top: 4px
}

.loading-container[data-v-101ea230] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: center;
    padding: 32px
}

.loading-text[data-v-101ea230] {
    color: #6b7280;
    font-size: 14px
}

.cloudflare-form[data-v-101ea230],
.form-group[data-v-101ea230] {
    display: flex;
    flex-direction: column;
    gap: 10px
}

.form-label[data-v-101ea230] {
    color: #000;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

.required[data-v-101ea230] {
    color: #ef4444
}

.form-input[data-v-101ea230] {
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 12px;
    font-size: 14px;
    padding: 8px 12px;
    transition: border-color .2s, box-shadow .2s
}

.form-input[data-v-101ea230]:focus {
    border-color: #0f7fff;
    box-shadow: 0 0 0 3px #0f7fff1a;
    outline: none
}

.form-input[data-v-101ea230]:disabled {
    background: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed
}

.form-input.input-error[data-v-101ea230] {
    border-color: #ef4444
}

.form-input.input-error[data-v-101ea230]:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px #ef44441a
}

.input-help[data-v-101ea230] {
    margin-top: 4px
}

.help-text[data-v-101ea230] {
    color: #6b7280;
    font-size: 12px;
    line-height: 1.4
}

.error-text[data-v-101ea230] {
    color: #ef4444;
    font-size: 12px;
    font-weight: 500;
    margin-top: 2px
}

.checkbox-group[data-v-101ea230] {
    align-items: center;
    cursor: pointer;
    display: flex;
    gap: 8px
}

.checkbox-input[data-v-101ea230] {
    accent-color: #0f7fff;
    cursor: pointer;
    height: 16px;
    width: 16px
}

.checkbox-input[data-v-101ea230]:disabled {
    cursor: not-allowed;
    opacity: .5
}

.checkbox-label[data-v-101ea230] {
    color: #374151;
    cursor: pointer;
    font-size: 14px
}

.cloudflare-deploy-button[data-v-101ea230] {
    align-items: center;
    align-self: flex-start;
    background: #232425;
    border: none;
    border-radius: 31px;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    height: 36px;
    justify-content: center;
    line-height: 21px;
    padding: 6px 20px;
    transition: all .2s
}

.cloudflare-deploy-button[data-v-101ea230]:hover:not(:disabled) {
    background: #3a3b3c
}

.cloudflare-deploy-button[data-v-101ea230]:active:not(:disabled) {
    background: #1a1b1c
}

.cloudflare-deploy-button[data-v-101ea230]:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    opacity: .6
}

.button-icon[data-v-101ea230] {
    height: 20px;
    width: 20px
}

.api-key-form[data-v-101ea230] {
    display: flex;
    flex-direction: column;
    gap: 16px
}

.password-input-wrapper[data-v-101ea230] {
    align-items: center;
    display: flex;
    position: relative
}

.password-input[data-v-101ea230] {
    padding-right: 40px
}

.password-toggle[data-v-101ea230] {
    align-items: center;
    background: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding: 4px;
    position: absolute;
    right: 8px;
    transition: background-color .2s
}

.password-toggle[data-v-101ea230]:hover:not(:disabled) {
    background: #f3f4f6
}

.password-toggle[data-v-101ea230]:disabled {
    cursor: not-allowed;
    opacity: .5
}

.eye-icon[data-v-101ea230] {
    color: #6b7280;
    height: 16px;
    width: 16px
}

.api-key-save-button[data-v-101ea230] {
    align-items: center;
    align-self: flex-start;
    background: #232425;
    border: none;
    border-radius: 31px;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    height: 36px;
    justify-content: center;
    line-height: 21px;
    padding: 6px 20px;
    transition: all .2s
}

.api-key-save-button[data-v-101ea230]:hover:not(:disabled) {
    background: #3a3b3c
}

.api-key-save-button[data-v-101ea230]:active:not(:disabled) {
    background: #1a1b1c
}

.api-key-save-button[data-v-101ea230]:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    opacity: .6
}

.api-key-form .form-input[data-v-101ea230],
.api-key-form .password-input-wrapper[data-v-101ea230] {
    width: 100%
}

.api-key-form .api-key-save-button[data-v-101ea230] {
    align-self: flex-start
}

.deploy-button[data-v-101ea230] {
    align-items: center;
    background: #232425;
    border: none;
    border-radius: 31px;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    height: 36px;
    justify-content: center;
    line-height: 21px;
    padding: 6px 20px;
    transition: all .2s
}

.deploy-button[data-v-101ea230]:hover:not(:disabled) {
    background: #3a3b3c
}

.deploy-button[data-v-101ea230]:active:not(:disabled) {
    background: #1a1b1c
}

.deploy-button[data-v-101ea230]:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    opacity: .6
}

.remove-api-key-button[data-v-101ea230] {
    align-items: center;
    background: #232425;
    border: none;
    border-radius: 31px;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    height: 36px;
    justify-content: center;
    line-height: 21px;
    padding: 6px 20px;
    transition: all .2s
}

.remove-api-key-button[data-v-101ea230]:hover:not(:disabled) {
    background: #3a3b3c
}

.remove-api-key-button[data-v-101ea230]:active:not(:disabled) {
    background: #1a1b1c
}

.remove-api-key-button[data-v-101ea230]:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    opacity: .6
}

.deployment-link[data-v-101ea230] {
    align-items: center;
    background: none;
    border: none;
    color: #0f7fff;
    cursor: pointer;
    display: inline-flex;
    gap: 4px;
    text-decoration: none;
    transition: color .2s
}

.deployment-link[data-v-101ea230]:hover {
    color: #338dff;
    text-decoration: underline
}

.external-icon[data-v-101ea230] {
    flex-shrink: 0;
    height: 12px;
    width: 12px
}

.deployment-output[data-v-101ea230] {
    margin: 16px 0
}

.deployment-output h4[data-v-101ea230] {
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 12px
}

.output-container[data-v-101ea230] {
    background: #393939;
    border: 1px solid #374151;
    border-radius: 8px;
    overflow: hidden
}

.output-container pre[data-v-101ea230] {
    background: transparent;
    font-family: SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, Courier New, monospace;
    font-size: 13px;
    height: 400px;
    line-height: 1.5;
    margin: 0;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 16px
}

.output-container code[data-v-101ea230] {
    background: transparent;
    color: #f3f4f6;
    font-family: inherit;
    font-size: inherit;
    padding: 0;
    white-space: pre-wrap;
    width: 100%
}

.deploy-header[data-v-101ea230] {
    align-items: center;
    display: flex;
    gap: 8px
}

.deploy-header h3[data-v-101ea230] {
    margin: 0
}

.deploy-section[data-v-101ea230] {
    align-items: center;
    display: flex;
    justify-content: space-between
}

.deploy-info h4[data-v-101ea230] {
    color: #1f2937;
    font-size: 16px;
    margin: 0 0 8px
}

.deploy-info p[data-v-101ea230] {
    color: #6b7280;
    font-size: 14px;
    margin: 0
}

.no-files-warning[data-v-101ea230] {
    margin-top: 16px
}

.deployment-result[data-v-101ea230] {
    border-top: 1px solid #e5e7eb;
    padding-top: 16px
}

.deployment-result h4[data-v-101ea230] {
    color: #1f2937;
    margin: 0 0 16px
}

.deployment-info[data-v-101ea230],
.deployment-time[data-v-101ea230],
.deployment-url[data-v-101ea230] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.label[data-v-101ea230] {
    color: #374151;
    font-weight: 500;
    min-width: 80px
}

.deployment-url .label[data-v-101ea230] {
    color: #000;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

@media (prefers-color-scheme:dark) {
    .deployment-url .label[data-v-101ea230] {
        color: #fff
    }
}

.deployment-link[data-v-101ea230] {
    font-family: SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, Courier New, monospace;
    font-size: 14px;
    padding: 0
}

.files-to-deploy[data-v-101ea230] {
    border-top: 1px solid #e5e7eb;
    padding-top: 16px
}

.files-to-deploy h4[data-v-101ea230] {
    color: #1f2937;
    margin: 0 0 16px
}

.files-list[data-v-101ea230] {
    display: grid;
    gap: 12px;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr))
}

.file-item[data-v-101ea230] {
    align-items: center;
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    display: flex;
    gap: 8px;
    padding: 8px
}

.file-icon[data-v-101ea230] {
    color: #6b7280;
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.file-info[data-v-101ea230] {
    flex: 1;
    min-width: 0
}

.file-name[data-v-101ea230] {
    color: #1f2937;
    font-size: 13px;
    font-weight: 500;
    word-break: break-all
}

.file-size[data-v-101ea230] {
    color: #6b7280;
    font-family: SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, Courier New, monospace;
    font-size: 11px
}

@media (prefers-color-scheme:dark) {
    .deploy-header h3[data-v-101ea230],
    .setup-header h3[data-v-101ea230],
    h3[data-v-101ea230],
    h4[data-v-101ea230] {
        color: #f9fafb
    }
    .checkbox-description[data-v-101ea230],
    .loading-text[data-v-101ea230] {
        color: #9ca3af
    }
    .form-input[data-v-101ea230],
    .form-label[data-v-101ea230] {
        color: #f9fafb
    }
    .form-input[data-v-101ea230] {
        background: #393939;
        border-color: #4a4b4c
    }
    .form-input[data-v-101ea230]:focus {
        border-color: #0f7fff;
        box-shadow: 0 0 0 3px #0f7fff1a
    }
    .form-input[data-v-101ea230]:disabled {
        background: #111827;
        color: #6b7280
    }
    .form-input.input-error[data-v-101ea230] {
        border-color: #f87171
    }
    .form-input.input-error[data-v-101ea230]:focus {
        border-color: #f87171;
        box-shadow: 0 0 0 3px #f872721a
    }
    .help-text[data-v-101ea230] {
        color: #9ca3af
    }
    .checkbox-label[data-v-101ea230] {
        color: #f9fafb
    }
    .password-toggle[data-v-101ea230]:hover:not(:disabled) {
        background: #374151
    }
    .eye-icon[data-v-101ea230] {
        color: #9ca3af
    }
    .deployment-link[data-v-101ea230] {
        color: #60a5fa
    }
    .deployment-link[data-v-101ea230]:hover {
        color: #93c5fd
    }
    .deploy-info h4[data-v-101ea230] {
        color: #f9fafb
    }
    .deploy-info p[data-v-101ea230] {
        color: #9ca3af
    }
    .deployment-result h4[data-v-101ea230],
    .files-to-deploy h4[data-v-101ea230] {
        color: #f9fafb
    }
    .label[data-v-101ea230] {
        color: #d1d5db
    }
    .file-item[data-v-101ea230] {
        background: #393939;
        border-color: #374151
    }
    .file-icon[data-v-101ea230] {
        color: #9ca3af
    }
    .file-name[data-v-101ea230] {
        color: #f9fafb
    }
    .file-size[data-v-101ea230] {
        color: #9ca3af
    }
    .deployment-result[data-v-101ea230],
    .files-to-deploy[data-v-101ea230] {
        border-top-color: #374151
    }
    .setup-instructions h4[data-v-101ea230] {
        color: #f9fafb
    }
    .setup-instructions ol[data-v-101ea230] {
        color: #9ca3af
    }
    .setup-instructions a[data-v-101ea230] {
        color: #60a5fa
    }
    .masked-key-display .label[data-v-101ea230],
    .validation-message .label[data-v-101ea230] {
        color: #9ca3af
    }
    .masked-key[data-v-101ea230] {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb
    }
    .success-text[data-v-101ea230] {
        color: #34d399
    }
    .error-text[data-v-101ea230] {
        color: #f87171
    }
    .deployment-output h4[data-v-101ea230] {
        color: #f9fafb
    }
    .output-container[data-v-101ea230] {
        background: #111827;
        border-color: #4b5563
    }
    .output-container code[data-v-101ea230] {
        color: #f3f4f6
    }
    .deploy-button[data-v-101ea230] {
        background: #3a3b3c
    }
    .deploy-button[data-v-101ea230]:hover:not(:disabled) {
        background: #4a4b4c
    }
    .card[data-v-101ea230],
    .deploy-button[data-v-101ea230]:active:not(:disabled) {
        background: #2a2b2c
    }
    .card[data-v-101ea230] {
        border-color: #3a3b3c
    }
    .api-key-save-button[data-v-101ea230] {
        background: #3a3b3c
    }
    .api-key-save-button[data-v-101ea230]:hover:not(:disabled) {
        background: #4a4b4c
    }
    .api-key-save-button[data-v-101ea230]:active:not(:disabled) {
        background: #2a2b2c
    }
    .cloudflare-deploy-button[data-v-101ea230] {
        background: #3a3b3c
    }
    .cloudflare-deploy-button[data-v-101ea230]:hover:not(:disabled) {
        background: #4a4b4c
    }
    .cloudflare-deploy-button[data-v-101ea230]:active:not(:disabled) {
        background: #2a2b2c
    }
    .remove-api-key-button[data-v-101ea230] {
        background: #3a3b3c
    }
    .remove-api-key-button[data-v-101ea230]:hover:not(:disabled) {
        background: #4a4b4c
    }
    .remove-api-key-button[data-v-101ea230]:active:not(:disabled) {
        background: #2a2b2c
    }
}

.file-viewer[data-v-5b3c95be] {
    height: 100%;
    overflow: hidden
}

.empty-state[data-v-5b3c95be],
.file-viewer[data-v-5b3c95be] {
    display: flex;
    flex-direction: column
}

.empty-state[data-v-5b3c95be] {
    align-items: center;
    color: #6b7280;
    gap: 12px;
    height: 200px;
    justify-content: center
}

.empty-icon[data-v-5b3c95be] {
    height: 48px;
    opacity: .5;
    width: 48px
}

.empty-text[data-v-5b3c95be] {
    color: #374151;
    font-size: 16px;
    font-weight: 500
}

.file-header[data-v-5b3c95be] {
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0;
    padding: 16px
}

.file-path[data-v-5b3c95be] {
    color: #1f2937;
    font-size: 14px;
    font-weight: 600;
    word-break: break-all
}

.file-meta[data-v-5b3c95be] {
    color: #6b7280;
    display: flex;
    font-size: 12px;
    gap: 12px;
    margin-top: 4px
}

.file-size[data-v-5b3c95be] {
    font-family: SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, Courier New, monospace
}

.text-content[data-v-5b3c95be] {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden
}

.code-content[data-v-5b3c95be] {
    background: #fff;
    flex: 1;
    overflow: auto
}

.code-content pre[data-v-5b3c95be] {
    background: #f8fafc;
    border: none;
    font-family: SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, Courier New, monospace;
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
    overflow: auto;
    padding: 16px;
    white-space: pre-wrap;
    word-wrap: break-word;
    min-height: 100%
}

.code-content code[data-v-5b3c95be] {
    color: #1f2937
}

.image-content[data-v-5b3c95be] {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden
}

.image-container[data-v-5b3c95be] {
    align-items: center;
    display: flex;
    flex: 1;
    justify-content: center;
    overflow: auto;
    padding: 20px
}

.preview-image[data-v-5b3c95be] {
    border-radius: 8px;
    box-shadow: 0 4px 12px #0000001a;
    max-height: 100%;
    max-width: 100%
}

.image-error[data-v-5b3c95be] {
    align-items: center;
    color: #6b7280;
    display: flex;
    flex-direction: column;
    gap: 12px
}

.image-error svg[data-v-5b3c95be] {
    height: 48px;
    opacity: .5;
    width: 48px
}

.binary-content[data-v-5b3c95be] {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden
}

.binary-info[data-v-5b3c95be] {
    align-items: center;
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 16px;
    justify-content: center;
    padding: 40px
}

.binary-icon[data-v-5b3c95be] {
    color: #6b7280;
    height: 64px;
    opacity: .5;
    width: 64px
}

.binary-text[data-v-5b3c95be] {
    text-align: center
}

.binary-title[data-v-5b3c95be] {
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px
}

.binary-subtitle[data-v-5b3c95be] {
    color: #6b7280;
    font-size: 14px
}

@media (prefers-color-scheme:dark) {
    .binary-title[data-v-5b3c95be],
    .empty-text[data-v-5b3c95be] {
        color: #f9fafb
    }
    .file-header[data-v-5b3c95be] {
        background: #111827;
        border-bottom-color: #374151
    }
    .file-path[data-v-5b3c95be] {
        color: #f9fafb
    }
    .file-meta[data-v-5b3c95be] {
        color: #9ca3af
    }
    .code-content[data-v-5b3c95be] {
        background: #393939
    }
    .code-content pre[data-v-5b3c95be] {
        background: #111827;
        color: #f9fafb
    }
    .code-content code[data-v-5b3c95be] {
        color: #f9fafb
    }
    .binary-icon[data-v-5b3c95be],
    .binary-subtitle[data-v-5b3c95be] {
        color: #9ca3af
    }
}

.database-icon[data-v-b14ebf11] {
    color: #6b7280;
    transition: color .2s
}

.database-icon[data-v-b14ebf11]:hover {
    color: #374151
}

@media (prefers-color-scheme:dark) {
    .database-icon[data-v-b14ebf11] {
        color: #9ca3af
    }
    .database-icon[data-v-b14ebf11]:hover {
        color: #f9fafb
    }
}

.data-tab[data-v-03d1aebf] {
    flex-direction: column;
    height: 100%;
    padding: 16px
}

.controls[data-v-03d1aebf],
.data-tab[data-v-03d1aebf] {
    display: flex;
    gap: 16px
}

.controls[data-v-03d1aebf] {
    align-items: center;
    flex-shrink: 0
}

.schema-selector[data-v-03d1aebf],
.schema-tabs[data-v-03d1aebf] {
    align-items: flex-start;
    display: flex;
    width: 100%
}

.schema-tabs[data-v-03d1aebf] {
    flex-wrap: wrap;
    gap: 6px 4px
}

.schema-tab[data-v-03d1aebf] {
    background: #f3f4f6;
    border: none;
    border-radius: 8px;
    color: #6b7280;
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
    outline: none;
    padding: 8px 16px;
    transition: all .2s;
    white-space: nowrap
}

.schema-tab[data-v-03d1aebf]:hover:not(:disabled) {
    background: #e5e7eb;
    color: #374151
}

.schema-tab[data-v-03d1aebf]:disabled {
    cursor: not-allowed;
    opacity: .5
}

.schema-tab-active[data-v-03d1aebf] {
    background: #393939;
    color: #fff
}

.schema-tab-active[data-v-03d1aebf]:hover:not(:disabled) {
    background: #374151;
    color: #fff
}

.data-content[data-v-03d1aebf] {
    gap: 16px
}

.data-content[data-v-03d1aebf],
.table-wrapper[data-v-03d1aebf] {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 0
}

.table-wrapper[data-v-03d1aebf] {
    overflow: auto;
    padding-bottom: 64px
}

[data-v-03d1aebf] .n-pagination {
    --n-item-border-active: 1px solid #393939 !important;
    --n-item-text-color-active: #393939 !important;
    --n-item-text-color-hover: #393939 !important
}

[data-v-03d1aebf] .n-select .n-base-selection {
    --n-border-active: 1px solid #393939 !important;
    --n-border-focus: 1px solid #374151 !important;
    --n-border-hover: 1px solid #374151 !important;
    --n-box-shadow-active: 0 0 0 2px rgba(31, 41, 55, .2) !important;
    --n-box-shadow-focus: 0 0 0 2px rgba(31, 41, 55, .2) !important;
    --n-loading-color: #393939 !important
}

.n-base-select-option {
    --n-option-text-color-active: #393939 !important
}

[data-v-03d1aebf] .n-input {
    --n-item-text-color-hover: #374151 !important;
    --n-item-text-color-active: #393939 !important;
    --n-item-text-color-pressed: #111827 !important;
    --n-item-border-active: 1px solid #393939 !important;
    --n-border-hover: 1px solid #374151 !important;
    --n-border-focus: 1px solid #374151 !important;
    --n-box-shadow-focus: 0 0 0 2px rgba(31, 41, 55, .2) !important
}

@media (prefers-color-scheme:dark) {
    [data-v-03d1aebf] .n-pagination {
        --n-item-border-active: 1px solid #f9fafb !important;
        --n-item-text-color-active: #f9fafb !important;
        --n-item-text-color-hover: #f9fafb !important
    }
    [data-v-03d1aebf] .n-select .n-base-selection {
        --n-border-active: 1px solid #f9fafb !important;
        --n-border-focus: 1px solid #e5e7eb !important;
        --n-border-hover: 1px solid #e5e7eb !important;
        --n-box-shadow-active: 0 0 0 2px rgba(249, 250, 251, .2) !important;
        --n-box-shadow-focus: 0 0 0 2px rgba(249, 250, 251, .2) !important;
        --n-loading-color: #f9fafb !important
    }
    .n-base-select-option {
        --n-option-text-color-active: #f9fafb !important
    }
    [data-v-03d1aebf] .n-input {
        --n-item-text-color-hover: #e5e7eb !important;
        --n-item-text-color-active: #f9fafb !important;
        --n-item-text-color-pressed: #d1d5db !important;
        --n-item-border-active: 1px solid #f9fafb !important;
        --n-border-hover: 1px solid #e5e7eb !important;
        --n-border-focus: 1px solid #e5e7eb !important;
        --n-box-shadow-focus: 0 0 0 2px rgba(249, 250, 251, .2) !important
    }
}

.table-wrapper[data-v-03d1aebf] .n-data-table {
    min-width: 100%
}

.table-wrapper[data-v-03d1aebf] .n-data-table-wrapper {
    overflow-x: auto
}

.table-wrapper[data-v-03d1aebf] .n-data-table-table {
    min-width: -moz-max-content;
    min-width: max-content;
    table-layout: fixed
}

.table-info[data-v-03d1aebf] {
    align-items: center;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    padding: 12px 16px
}

.table-info h3[data-v-03d1aebf] {
    color: #374151;
    font-size: 16px;
    font-weight: 600;
    margin: 0
}

.table-info p[data-v-03d1aebf] {
    color: #6b7280;
    font-size: 14px;
    margin: 0
}

.loading-placeholder[data-v-03d1aebf] {
    align-items: center;
    color: #6b7280;
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 16px;
    justify-content: center;
    min-height: 200px
}

.loading-placeholder p[data-v-03d1aebf] {
    font-size: 14px;
    margin: 0
}

.empty-state[data-v-03d1aebf] {
    color: #6b7280;
    font-style: italic;
    min-height: 200px
}

.empty-state[data-v-03d1aebf],
.empty-state-centered[data-v-03d1aebf] {
    align-items: center;
    display: flex;
    flex: 1;
    justify-content: center
}

.empty-state-centered[data-v-03d1aebf] {
    flex-direction: column;
    gap: 16px;
    margin: 0 auto;
    max-width: 600px;
    min-height: 300px;
    padding: 32px;
    text-align: center
}

.empty-icon[data-v-03d1aebf] {
    align-items: center;
    color: #9ca3af;
    display: flex;
    height: 50px;
    justify-content: center;
    margin-bottom: 8px;
    opacity: .4;
    width: 50px
}

.empty-title[data-v-03d1aebf] {
    color: #909499;
    font-size: 18px;
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 8px
}

.empty-description[data-v-03d1aebf] {
    color: #bdbdbd;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    max-width: 480px
}

[data-v-03d1aebf] .scroll-icon-animate {
    animation: moveAround-03d1aebf 1s infinite
}

@keyframes moveAround-03d1aebf {
    0%,
    to {
        transform: translate(-2px)
    }
    50% {
        transform: translate(2px)
    }
}

@media (prefers-color-scheme:dark) {
    .schema-tab[data-v-03d1aebf] {
        background: #374151;
        color: #9ca3af
    }
    .schema-tab[data-v-03d1aebf]:hover:not(:disabled) {
        background: #4b5563;
        color: #f9fafb
    }
    .schema-tab-active[data-v-03d1aebf] {
        background: #f9fafb;
        color: #1f2937
    }
    .schema-tab-active[data-v-03d1aebf]:hover:not(:disabled) {
        background: #e5e7eb;
        color: #1f2937
    }
    .table-info[data-v-03d1aebf] {
        background: #393939;
        border-color: #374151
    }
    .table-info h3[data-v-03d1aebf] {
        color: #f9fafb
    }
    .empty-state[data-v-03d1aebf],
    .loading-placeholder[data-v-03d1aebf],
    .table-info p[data-v-03d1aebf] {
        color: #9ca3af
    }
    .empty-icon[data-v-03d1aebf] {
        color: #6b7280
    }
    .empty-title[data-v-03d1aebf] {
        color: #a1a1aa
    }
    .empty-description[data-v-03d1aebf] {
        color: #71717a
    }
}

.project-summary[data-v-6b03369c] {
    height: 100%;
    overflow-y: auto;
    padding: 16px
}

.loading-state[data-v-6b03369c] {
    align-items: center;
    color: #6b7280;
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 200px;
    justify-content: center
}

.loading-spinner[data-v-6b03369c] {
    animation: spin-6b03369c 1s linear infinite;
    border: 2px solid #e5e7eb;
    border-radius: 50%;
    border-top-color: #3b82f6;
    height: 24px;
    width: 24px
}

@keyframes spin-6b03369c {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.error-state[data-v-6b03369c] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 200px;
    justify-content: center
}

.error-message[data-v-6b03369c] {
    color: #dc2626;
    font-size: 14px;
    text-align: center
}

.empty-summary[data-v-6b03369c] {
    align-items: center;
    color: #6b7280;
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 300px;
    justify-content: center
}

.empty-icon[data-v-6b03369c] {
    height: 48px;
    opacity: .5;
    width: 48px
}

.empty-message[data-v-6b03369c] {
    font-size: 16px;
    font-weight: 500
}

.summary-content[data-v-6b03369c] {
    display: flex;
    flex-direction: column;
    gap: 24px
}

.summary-section[data-v-6b03369c] {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px
}

.section-header[data-v-6b03369c] {
    align-items: center;
    display: flex;
    gap: 8px;
    margin-bottom: 16px
}

.section-icon[data-v-6b03369c] {
    height: 20px;
    width: 20px
}

.section-icon.overview[data-v-6b03369c] {
    color: #8b5cf6
}

.section-icon.completed[data-v-6b03369c] {
    color: #059669
}

.section-icon.pending[data-v-6b03369c] {
    color: #d97706
}

.section-icon.functional[data-v-6b03369c] {
    color: #2563eb
}

.section-icon.recommended[data-v-6b03369c] {
    color: #7c3aed
}

.section-icon.urls[data-v-6b03369c] {
    color: #0891b2
}

.section-icon.data[data-v-6b03369c] {
    color: #dc2626
}

.section-header h3[data-v-6b03369c] {
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
    margin: 0
}

.project-info[data-v-6b03369c] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.project-name[data-v-6b03369c] {
    color: #1f2937;
    font-size: 20px;
    font-weight: 700
}

.project-goals[data-v-6b03369c] {
    color: #6b7280;
    font-size: 14px;
    line-height: 1.5
}

.data-models h4[data-v-6b03369c],
.main-features h4[data-v-6b03369c],
.storage-services h4[data-v-6b03369c] {
    color: #374151;
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 8px
}

.data-models ul[data-v-6b03369c],
.main-features ul[data-v-6b03369c],
.storage-services ul[data-v-6b03369c] {
    margin: 0;
    padding-left: 20px
}

.data-models li[data-v-6b03369c],
.main-features li[data-v-6b03369c],
.storage-services li[data-v-6b03369c] {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 4px
}

.feature-list[data-v-6b03369c] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.feature-item[data-v-6b03369c] {
    border: 1px solid;
    border-radius: 6px;
    font-size: 14px;
    padding: 12px
}

.feature-item.completed[data-v-6b03369c] {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534
}

.feature-item.pending[data-v-6b03369c] {
    background: #fffbeb;
    border-color: #fde68a;
    color: #92400e
}

.uri-list[data-v-6b03369c] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.uri-item[data-v-6b03369c] {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px
}

.uri-path[data-v-6b03369c] {
    background: #f3f4f6;
    border-radius: 4px;
    color: #374151;
    display: inline-block;
    font-family: SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, Courier New, monospace;
    font-size: 13px;
    margin-bottom: 8px;
    padding: 4px 8px
}

.uri-description[data-v-6b03369c] {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 8px
}

.uri-params[data-v-6b03369c] {
    font-size: 12px
}

.params-label[data-v-6b03369c] {
    color: #6b7280;
    font-weight: 600
}

.params-list[data-v-6b03369c] {
    color: #374151;
    font-family: SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, Courier New, monospace
}

.steps-list[data-v-6b03369c] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.step-item[data-v-6b03369c] {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    display: flex;
    gap: 12px;
    padding: 12px
}

.step-number[data-v-6b03369c] {
    align-items: center;
    background: #7c3aed;
    border-radius: 50%;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 12px;
    font-weight: 600;
    height: 24px;
    justify-content: center;
    width: 24px
}

.step-content[data-v-6b03369c] {
    color: #374151;
    flex: 1;
    font-size: 14px
}

.url-list[data-v-6b03369c] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.url-item[data-v-6b03369c] {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px
}

.url-name[data-v-6b03369c] {
    color: #374151;
    font-weight: 600;
    margin-bottom: 4px
}

.url-link[data-v-6b03369c] {
    color: #2563eb;
    font-family: SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, Courier New, monospace;
    font-size: 13px;
    text-decoration: none;
    word-break: break-all
}

.url-link[data-v-6b03369c]:hover {
    text-decoration: underline
}

.data-info[data-v-6b03369c] {
    display: flex;
    flex-direction: column;
    gap: 16px
}

.empty-state[data-v-6b03369c] {
    color: #9ca3af;
    font-size: 14px;
    font-style: italic;
    padding: 24px;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .project-summary[data-v-6b03369c] {
        background: #393939
    }
    .loading-state[data-v-6b03369c] {
        color: #9ca3af
    }
    .loading-spinner[data-v-6b03369c] {
        border-color: #60a5fa #374151 #374151
    }
    .empty-summary[data-v-6b03369c] {
        color: #9ca3af
    }
    .summary-section[data-v-6b03369c] {
        background: #111827;
        border-color: #374151
    }
    .project-name[data-v-6b03369c],
    .section-header h3[data-v-6b03369c] {
        color: #f9fafb
    }
    .project-goals[data-v-6b03369c] {
        color: #9ca3af
    }
    .data-models h4[data-v-6b03369c],
    .main-features h4[data-v-6b03369c],
    .storage-services h4[data-v-6b03369c] {
        color: #d1d5db
    }
    .data-models li[data-v-6b03369c],
    .main-features li[data-v-6b03369c],
    .storage-services li[data-v-6b03369c] {
        color: #9ca3af
    }
    .feature-item.completed[data-v-6b03369c] {
        background: #064e3b;
        border-color: #065f46;
        color: #34d399
    }
    .feature-item.pending[data-v-6b03369c] {
        background: #451a03;
        border-color: #92400e;
        color: #fbbf24
    }
    .step-item[data-v-6b03369c],
    .uri-item[data-v-6b03369c],
    .url-item[data-v-6b03369c] {
        background: #393939;
        border-color: #374151
    }
    .uri-path[data-v-6b03369c] {
        background: #374151;
        color: #d1d5db
    }
    .params-label[data-v-6b03369c],
    .uri-description[data-v-6b03369c] {
        color: #9ca3af
    }
    .params-list[data-v-6b03369c],
    .step-content[data-v-6b03369c],
    .url-name[data-v-6b03369c] {
        color: #d1d5db
    }
    .url-link[data-v-6b03369c] {
        color: #60a5fa
    }
    .empty-state[data-v-6b03369c] {
        color: #6b7280
    }
}

[data-v-ede1da14] .mermaid-wrapper .mermaid-copy-code {
    display: none
}

.tutorial-tab[data-v-ede1da14] {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    position: relative
}

.tutorial-layout[data-v-ede1da14] {
    display: flex;
    gap: 0;
    height: 100%
}

.toc-sidebar[data-v-ede1da14] {
    background: #f5f5f5;
    border-right: 1px solid #e0e0e0;
    box-sizing: border-box;
    flex-shrink: 0;
    overflow-y: auto;
    padding: 16px;
    width: 250px
}

.toc-header[data-v-ede1da14] {
    color: #333;
    font-size: 16px;
    font-weight: 600;
    justify-content: space-between;
    margin-bottom: 16px
}

.toc-close-btn[data-v-ede1da14],
.toc-header[data-v-ede1da14] {
    align-items: center;
    display: flex
}

.toc-close-btn[data-v-ede1da14] {
    background: none;
    border: none;
    border-radius: 4px;
    color: #666;
    cursor: pointer;
    justify-content: center;
    padding: 4px;
    transition: background-color .2s, color .2s
}

.toc-close-btn[data-v-ede1da14]:hover {
    background-color: #f0f0f0;
    color: #333
}

.toc-toggle-btn[data-v-ede1da14] {
    align-items: center;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 8px #0000001a;
    color: #666;
    cursor: pointer;
    display: flex;
    height: 40px;
    justify-content: center;
    left: 16px;
    position: absolute;
    top: 16px;
    transition: all .2s;
    width: 40px;
    z-index: 100
}

.toc-toggle-btn[data-v-ede1da14]:hover {
    background: #f8f8f8;
    box-shadow: 0 4px 12px #00000026;
    color: #333
}

.tutorial-container[data-v-ede1da14] {
    background: #fafafa;
    flex: 1;
    overflow-y: auto;
    padding: 24px
}

.loading-container[data-v-ede1da14] {
    align-items: center;
    color: #6b7280;
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 100%;
    justify-content: center
}

.loading-container p[data-v-ede1da14] {
    font-size: 14px;
    margin: 0
}

@media (prefers-color-scheme:dark) {
    .toc-sidebar[data-v-ede1da14] {
        background: #2a2a2a;
        border-right-color: #444
    }
    .toc-header[data-v-ede1da14] {
        color: #fff
    }
    .toc-close-btn[data-v-ede1da14] {
        color: #ccc
    }
    .toc-close-btn[data-v-ede1da14]:hover {
        background-color: #444;
        color: #fff
    }
    .toc-toggle-btn[data-v-ede1da14] {
        background: #2a2a2a;
        border-color: #444;
        color: #ccc
    }
    .toc-toggle-btn[data-v-ede1da14]:hover {
        background: #333;
        box-shadow: 0 4px 12px #0000004d;
        color: #fff
    }
    .tutorial-container[data-v-ede1da14] {
        background: #1a1a1a
    }
}

@media (max-width:768px) {
    .tutorial-layout[data-v-ede1da14] {
        flex-direction: column
    }
    .toc-sidebar[data-v-ede1da14] {
        border-bottom: 1px solid #e0e0e0;
        border-right: none;
        box-sizing: border-box;
        max-height: 200px;
        max-width: 100%;
        width: 100%
    }
    @media (prefers-color-scheme:dark) {
        .toc-sidebar[data-v-ede1da14] {
            border-bottom-color: #444
        }
    }
}

[data-v-ede1da14] .toc-content {
    margin-top: 6px
}

[data-v-ede1da14] .toc-content ol>li>ol>li {
    list-style-type: disc;
    margin-left: 20px
}

[data-v-ede1da14] .toc-content ol>li>ol>li>a {
    font-weight: 400
}

[data-v-ede1da14] .toc-content li {
    margin: 10px 0
}

[data-v-ede1da14] .toc-content a {
    color: #232425;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    text-decoration: none
}

[data-v-ede1da14] .toc-content a:hover {
    text-decoration: underline
}

@media (prefers-color-scheme:dark) {
    [data-v-ede1da14] .toc-content {
        background: #2a2a2a
    }
    [data-v-ede1da14] .toc-content a {
        color: #e0e0e0
    }
    [data-v-ede1da14] .toc-content a:hover {
        text-decoration: underline
    }
}

[data-v-11bd8f81] .n-tabs-wrapper {
    padding: 0 16px !important
}

.wrapper[data-v-11bd8f81] {
    background: #fff;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0;
    overflow: hidden
}

@media (min-width:1220px) {
    .wrapper[data-v-11bd8f81] {
        display: flex
    }
}

@media (max-width:1219px) {
    .wrapper[data-v-11bd8f81] {
        display: none
    }
    .wrapper.mobile-visible[data-v-11bd8f81] {
        display: flex;
        height: calc(100vh - 70px);
        max-height: calc(100vh - 70px)
    }
}

.controls[data-v-11bd8f81] {
    background: #fff;
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0;
    height: 48px;
    justify-content: space-between;
    padding: 0 16px
}

.canvas_header[data-v-11bd8f81],
.controls[data-v-11bd8f81] {
    align-items: center;
    display: flex
}

.canvas_header[data-v-11bd8f81] {
    gap: 12px
}

.current-canvas-header[data-v-11bd8f81] {
    gap: 8px
}

.current-canvas-header[data-v-11bd8f81],
.icon[data-v-11bd8f81] {
    align-items: center;
    display: flex
}

.icon[data-v-11bd8f81] {
    height: 16px;
    justify-content: center;
    width: 16px
}

.icon[data-v-11bd8f81],
.label[data-v-11bd8f81] {
    color: #606366
}

.label[data-v-11bd8f81] {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%
}

.action-buttons[data-v-11bd8f81] {
    gap: 8px
}

.action-button[data-v-11bd8f81],
.action-buttons[data-v-11bd8f81] {
    align-items: center;
    display: flex
}

.action-button[data-v-11bd8f81] {
    border-radius: 6px;
    color: #6b7280;
    cursor: pointer;
    height: 20px;
    justify-content: center;
    padding: 4px;
    transition: all .2s;
    width: 20px
}

.action-button[data-v-11bd8f81]:hover {
    background: #e5e7eb;
    color: #374151
}

.action-button.loading[data-v-11bd8f81] {
    animation: spin-11bd8f81 1s linear infinite;
    background: transparent !important
}

@keyframes spin-11bd8f81 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.mobile-close-button[data-v-11bd8f81] {
    display: none
}

@media (max-width:1220px) {
    .mobile-close-button[data-v-11bd8f81] {
        display: flex;
        flex-direction: row;
        justify-content: flex-end
    }
}

.mobile-close-button .icon[data-v-11bd8f81] {
    color: #6b7280;
    cursor: pointer;
    height: 24px;
    transition: all .2s;
    width: 24px
}

.mobile-close-button .icon[data-v-11bd8f81]:hover {
    color: #374151
}

@media (prefers-color-scheme:dark) {
    .wrapper[data-v-11bd8f81] {
        background: #393939;
        border-left-color: #666
    }
    .controls[data-v-11bd8f81] {
        background: #393939;
        border-bottom-color: #666
    }
    .icon[data-v-11bd8f81],
    .label[data-v-11bd8f81] {
        color: #d1d5db
    }
    .action-button[data-v-11bd8f81] {
        color: #9ca3af
    }
    .action-button[data-v-11bd8f81]:hover {
        background: #374151;
        color: #f9fafb
    }
    .mobile-close-button .icon[data-v-11bd8f81] {
        color: #9ca3af
    }
    .mobile-close-button .icon[data-v-11bd8f81]:hover {
        color: #f9fafb
    }
}

.sandbox-tabs[data-v-11bd8f81] {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
    --n-tab-text-color-hover: #0f7fff !important;
    --n-tab-text-color-active: #0f7fff !important;
    --n-bar-color: #0f7fff !important;
    --n-pane-padding-top: 0 !important
}

.sandbox-tabs[data-v-11bd8f81] .n-tabs-tab span {
    color: #232425 !important
}

@media (prefers-color-scheme:dark) {
    .sandbox-tabs[data-v-11bd8f81] .n-tabs-tab span {
        color: #d1d5db !important
    }
}

.sandbox-tabs[data-v-11bd8f81] .n-tabs-tab--active,
.sandbox-tabs[data-v-11bd8f81] .n-tabs-tab--active span {
    color: #0f7fff !important
}

.sandbox-tabs[data-v-11bd8f81] .n-tabs-tab .tab-icon {
    color: #232425 !important
}

@media (prefers-color-scheme:dark) {
    .sandbox-tabs[data-v-11bd8f81] .n-tabs-tab .tab-icon {
        color: #d1d5db !important
    }
}

.sandbox-tabs[data-v-11bd8f81] .n-tabs-tab--active .tab-icon {
    color: #0f7fff !important
}

.sandbox-tabs[data-v-11bd8f81] .n-tabs-content,
.sandbox-tabs[data-v-11bd8f81] .n-tab-pane {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden
}

.tab-header[data-v-11bd8f81] {
    align-items: center;
    display: flex;
    gap: 4px
}

.tab-icon[data-v-11bd8f81] {
    height: 16px;
    width: 16px
}

@media (prefers-color-scheme:dark) {
    .tab-icon[data-v-11bd8f81] {
        color: #9ca3af
    }
}

.file-tab-header[data-v-11bd8f81] {
    padding-right: 24px;
    position: relative
}

.close-icon[data-v-11bd8f81] {
    cursor: pointer;
    height: 12px;
    opacity: .6;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    transition: opacity .2s;
    width: 12px
}

.close-icon[data-v-11bd8f81]:hover {
    opacity: 1
}

.version-button[data-v-11bd8f81],
.version-button-label[data-v-11bd8f81] {
    color: #606366;
    font-size: 14px
}

@media (prefers-color-scheme:dark) {
    .version-button[data-v-11bd8f81],
    .version-button-label[data-v-11bd8f81] {
        color: #999
    }
}

.icon[data-v-20bb0cce] {
    height: 24px;
    width: 24px
}

.black-loading-animation[data-v-20bb0cce] {
    animation: rotate-20bb0cce 1.5s linear infinite
}

@keyframes rotate-20bb0cce {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.icon[data-v-0410dbe7] {
    height: 24px;
    width: 24px
}

.black-loading-animation[data-v-0410dbe7] {
    animation: rotate-0410dbe7 1.5s linear infinite
}

@keyframes rotate-0410dbe7 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.mobile-canvas-entry[data-v-4a5008de] {
    align-items: center;
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    padding: 16px;
    transition: all .2s
}

.mobile-canvas-entry[data-v-4a5008de]:hover {
    background: #f9fafb;
    border-color: #d1d5db
}

.entry-content[data-v-4a5008de] {
    align-items: center;
    display: flex;
    gap: 12px
}

.icon[data-v-4a5008de] {
    color: #3b82f6;
    height: 24px;
    width: 24px
}

.text[data-v-4a5008de] {
    display: flex;
    flex-direction: column;
    gap: 2px
}

.title[data-v-4a5008de] {
    color: #1f2937;
    font-size: 14px;
    font-weight: 500
}

@media (prefers-color-scheme:dark) {
    .mobile-canvas-entry[data-v-4a5008de] {
        background: #1f2937;
        border-color: #374151
    }
    .mobile-canvas-entry[data-v-4a5008de]:hover {
        background: #111827;
        border-color: #4b5563
    }
    .title[data-v-4a5008de] {
        color: #f9fafb
    }
}

.icon[data-v-14ce59ec] {
    height: 24px;
    width: 24px
}

.black-loading-animation[data-v-14ce59ec] {
    animation: rotate-14ce59ec 1.5s linear infinite
}

@keyframes rotate-14ce59ec {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.notion-save-modal-overlay[data-v-dec65e19] {
    align-items: center;
    background: #0000004d;
    bottom: 0;
    display: flex;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Helvetica, Apple Color Emoji, Arial, sans-serif, Segoe UI Symbol;
    justify-content: center;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 9999
}

.notion-save-modal-dialog[data-v-dec65e19] {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 30px #0000001f;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 240px;
    max-width: 95%;
    width: 480px
}

.notion-save-modal-content[data-v-dec65e19] {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%
}

.notion-save-modal-header[data-v-dec65e19] {
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    padding: 16px 20px
}

.notion-save-modal-header h3[data-v-dec65e19] {
    color: #232425;
    font-size: 16px;
    font-weight: 600;
    margin: 0
}

.close-button[data-v-dec65e19] {
    align-items: center;
    background: none;
    border: none;
    border-radius: 4px;
    color: #999;
    cursor: pointer;
    display: flex;
    font-size: 20px;
    height: 24px;
    justify-content: center;
    margin: 0;
    padding: 0;
    transition: all .2s ease;
    width: 24px
}

.close-button[data-v-dec65e19]:hover {
    background: #0000000d;
    color: #333
}

.notion-save-modal-body[data-v-dec65e19] {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
    min-height: 120px;
    padding: 12px 20px
}

.notion-save-modal-loading[data-v-dec65e19] {
    align-items: center;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    min-height: 80px;
    width: 100%
}

.loading-spinner[data-v-dec65e19] {
    animation: spin-dec65e19 1s linear infinite;
    border: 2px solid rgba(0, 0, 0, .1);
    border-radius: 50%;
    border-top-color: #000;
    height: 24px;
    margin-bottom: 16px;
    width: 24px
}

.notion-save-modal-error[data-v-dec65e19] {
    color: #e53935;
    font-size: 14px;
    text-align: center
}

.notion-save-title[data-v-dec65e19] {
    color: #232425;
    font-size: 16px;
    font-style: italic;
    margin-top: 12px;
    max-width: 100%;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap
}

.page-selection-content[data-v-dec65e19] {
    display: flex;
    flex-direction: column;
    height: 100%
}

.custom-select-container[data-v-dec65e19] {
    flex: 1;
    margin-bottom: 12px;
    max-height: 48px;
    position: relative;
    width: 100%
}

.custom-select-header[data-v-dec65e19] {
    align-items: center;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    height: 100%;
    justify-content: space-between;
    min-height: 36px;
    padding: 10px 16px;
    transition: all .2s ease;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.custom-select-header[data-v-dec65e19]:hover {
    border-color: #bbb
}

.custom-select-header.active[data-v-dec65e19] {
    border-color: #0f7fff;
    box-shadow: 0 0 0 2px #0f7fff1a
}

.selected-option[data-v-dec65e19] {
    align-items: center;
    display: flex;
    flex: 1;
    overflow: hidden
}

.selected-option-icon[data-v-dec65e19] {
    font-size: 16px;
    margin-right: 10px
}

.selected-option-text[data-v-dec65e19] {
    font-size: 15px;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.select-arrow[data-v-dec65e19] {
    color: #999;
    margin-left: 8px;
    padding: 4px;
    transition: transform .2s ease
}

.custom-select-header.active .select-arrow[data-v-dec65e19] {
    transform: rotate(180deg)
}

.custom-select-dropdown[data-v-dec65e19] {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px #0000001a;
    left: 0;
    max-height: 200px;
    overflow-y: auto;
    position: absolute;
    right: 0;
    scrollbar-width: thin;
    top: calc(100% + 4px);
    z-index: 10
}

.custom-select-dropdown[data-v-dec65e19]::-webkit-scrollbar {
    width: 6px
}

.custom-select-dropdown[data-v-dec65e19]::-webkit-scrollbar-track {
    background: transparent
}

.custom-select-dropdown[data-v-dec65e19]::-webkit-scrollbar-thumb {
    background-color: #0003;
    border-radius: 3px
}

.custom-select-option[data-v-dec65e19] {
    align-items: center;
    cursor: pointer;
    display: flex;
    padding: 8px 16px;
    transition: background .2s ease
}

.custom-select-option[data-v-dec65e19]:hover {
    background: #f5f5f5
}

.custom-select-option.selected[data-v-dec65e19] {
    background: #f0f7ff
}

.option-icon[data-v-dec65e19] {
    font-size: 16px;
    margin-right: 10px
}

.option-text[data-v-dec65e19] {
    font-size: 15px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.notion-save-modal-buttons[data-v-dec65e19] {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px
}

.notion-save-modal-button[data-v-dec65e19] {
    align-items: center;
    border-radius: 6px;
    cursor: pointer;
    display: inline-flex;
    font-size: 14px;
    font-weight: 500;
    height: 36px;
    justify-content: center;
    min-width: 80px;
    padding: 0 16px;
    transition: all .2s ease;
    white-space: nowrap
}

.notion-save-modal-button-cancel[data-v-dec65e19] {
    background: #f5f5f5;
    border: 1px solid #e0e0e0;
    color: #444
}

.notion-save-modal-button-cancel[data-v-dec65e19]:hover {
    background: #e8e8e8
}

.notion-save-modal-button-primary[data-v-dec65e19] {
    background: #000;
    border: 1px solid #000;
    color: #fff
}

.notion-save-modal-button-primary[data-v-dec65e19]:hover {
    background: #333
}

.notion-save-modal-button-primary[data-v-dec65e19]:disabled {
    background: #ccc;
    border-color: #ccc;
    cursor: not-allowed
}

.notion-save-modal-button-primary[data-v-dec65e19]:focus {
    box-shadow: 0 0 0 2px #0000001a;
    outline: none
}

@keyframes spin-dec65e19 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

@media (prefers-color-scheme:dark) {
    .notion-save-modal-dialog[data-v-dec65e19] {
        background: #232425;
        box-shadow: 0 8px 30px #0003
    }
    .notion-save-modal-header[data-v-dec65e19] {
        border-color: #333
    }
    .notion-save-modal-header h3[data-v-dec65e19] {
        color: #fff
    }
    .loading-spinner[data-v-dec65e19] {
        border-color: #fff hsla(0, 0%, 100%, .1) hsla(0, 0%, 100%, .1)
    }
    .close-button[data-v-dec65e19] {
        color: #888
    }
    .close-button[data-v-dec65e19]:hover {
        background: #ffffff1a;
        color: #ddd
    }
    .custom-select-header[data-v-dec65e19] {
        background: #2c2c2c;
        border-color: #444;
        color: #fff
    }
    .custom-select-header[data-v-dec65e19]:hover {
        border-color: #666
    }
    .custom-select-header.active[data-v-dec65e19] {
        border-color: #0f7fff;
        box-shadow: 0 0 0 2px #0f7fff26
    }
    .select-arrow[data-v-dec65e19] {
        color: #999
    }
    .custom-select-dropdown[data-v-dec65e19] {
        background: #2c2c2c;
        border-color: #444;
        box-shadow: 0 4px 12px #0003
    }
    .custom-select-option[data-v-dec65e19]:hover {
        background: #383838
    }
    .custom-select-option.selected[data-v-dec65e19] {
        background: #383f4a
    }
    .notion-save-modal-button-cancel[data-v-dec65e19] {
        background: #333;
        border-color: #444;
        color: #eee
    }
    .notion-save-modal-button-cancel[data-v-dec65e19]:hover {
        background: #3a3a3a
    }
    .notion-save-modal-button-primary[data-v-dec65e19] {
        background: #000;
        border-color: #555;
        color: #fff
    }
    .notion-save-modal-button-primary[data-v-dec65e19]:hover {
        background: #222
    }
    .notion-save-modal-button-primary[data-v-dec65e19]:focus {
        box-shadow: 0 0 0 2px #ffffff1a
    }
    .notion-save-title[data-v-dec65e19] {
        color: #eee
    }
}

.user .content[data-v-7ef5b0ff] pre {
    background: none;
    margin: 0;
    padding: 0
}

.user .content[data-v-7ef5b0ff] pre code {
    font-family: sans-serif;
    white-space: pre-wrap
}

.conversation-content[data-v-7ef5b0ff] {
    background: transparent;
    color: #666;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding-bottom: 180px;
    position: relative
}

.conversation .role[data-v-7ef5b0ff] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.conversation .avatar[data-v-7ef5b0ff] {
    height: 36px;
    width: 36px
}

.conversation .avatar img[data-v-7ef5b0ff] {
    width: 100%
}

.conversation .name[data-v-7ef5b0ff] {
    color: #252525;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal
}

.conversation-item-desc .bubble[data-v-7ef5b0ff] {
    align-items: flex-start;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    justify-content: flex-start;
    line-height: 20px;
    padding: 12px 16px
}

.conversation-item-desc .bubble code[data-v-7ef5b0ff],
.conversation-item-desc .bubble pre[data-v-7ef5b0ff] {
    font-size: 16px
}

.conversation-item-desc .button[data-v-7ef5b0ff] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 2px;
    line-height: normal;
    padding: 4px 8px
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc .button[data-v-7ef5b0ff] {
        border-color: #252525;
        color: #ddd
    }
}

.conversation-item-desc .button .icon[data-v-7ef5b0ff] {
    display: flex
}

@keyframes blink-animation-7ef5b0ff {
    0%,
    to {
        color: #000
    }
    50% {
        color: transparent
    }
}

@media (prefers-color-scheme:dark) {
    @keyframes blink-animation-7ef5b0ff {
        0%,
        to {
            color: #fff
        }
        50% {
            color: transparent
        }
    }
}

.conversation-item-desc.assistant .bubble[data-v-7ef5b0ff] .cursor {
    animation: blink-animation-7ef5b0ff .5s infinite;
    color: #606366;
    font-size: 12px
}

.conversation-item-desc[data-v-7ef5b0ff] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-width: 260px;
    width: 100%
}

.conversation-item-desc.user[data-v-7ef5b0ff] {
    align-items: flex-end;
    margin-left: 30px
}

.conversation-item-desc.assistant .bubble[data-v-7ef5b0ff] {
    border: 1px solid transparent;
    border-radius: 16px;
    box-sizing: border-box;
    min-width: 260px;
    padding-left: 0;
    padding-right: 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.retry[data-v-7ef5b0ff],
.conversation-item-desc.assistant .bubble.try_moa[data-v-7ef5b0ff] {
    align-items: center;
    background: #fafafa;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 18px 16px
}

.conversation-item-desc .retry .button[data-v-7ef5b0ff],
.conversation-item-desc .try_moa .button[data-v-7ef5b0ff] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 8px 16px
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.retry[data-v-7ef5b0ff],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-7ef5b0ff] {
        flex-direction: column
    }
}

.models-list[data-v-7ef5b0ff] {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px 8px
}

.models-list[data-v-7ef5b0ff],
.models-list .model[data-v-7ef5b0ff] {
    display: flex;
    flex-direction: column
}

.models-list .model[data-v-7ef5b0ff] {
    color: #232425;
    cursor: pointer;
    margin-top: 4px;
    padding: 10px 6px 10px 10px
}

.models-list .model .row[data-v-7ef5b0ff] {
    border-radius: 8px;
    color: #232425;
    gap: 4px;
    justify-content: space-between
}

.models-list .model .row[data-v-7ef5b0ff],
.models-selected[data-v-7ef5b0ff] {
    display: flex;
    flex-direction: row;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.models-selected[data-v-7ef5b0ff] {
    align-items: center;
    background-color: #f4f4f4;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 150%;
    padding: 6px 12px
}

@media (prefers-color-scheme:dark) {
    .models-list[data-v-7ef5b0ff] {
        background-color: #252525
    }
    .models-list .model .row[data-v-7ef5b0ff] {
        color: #ddd
    }
    .conversation-item-desc.assistant .bubble[data-v-7ef5b0ff] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble.retry[data-v-7ef5b0ff],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-7ef5b0ff] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .retry .button[data-v-7ef5b0ff],
    .conversation-item-desc .try_moa .button[data-v-7ef5b0ff] {
        background: #fafafa;
        color: #232425
    }
}

.controls[data-v-7ef5b0ff] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden
}

.controls[data-v-7ef5b0ff]::-webkit-scrollbar {
    display: none
}

.sandbox_status[data-v-7ef5b0ff] {
    color: #999;
    font-size: 16px;
    margin: 12px 0
}

@media (prefers-color-scheme:dark) {
    .sandbox_status[data-v-7ef5b0ff] {
        color: #666
    }
}

.loading-dots[data-v-7ef5b0ff]:after {
    animation: loading-dots-7ef5b0ff 1.5s infinite;
    content: "";
    display: inline-block
}

@keyframes loading-dots-7ef5b0ff {
    0% {
        content: ""
    }
    25% {
        content: "."
    }
    50% {
        content: ".."
    }
    75% {
        content: "..."
    }
    to {
        content: ""
    }
}

@media (max-width:1220px) {
    .docs-agent-welcome-title[data-v-7ef5b0ff] {
        display: none
    }
}

.compact-toggle[data-v-7ef5b0ff] {
    align-items: center;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    gap: 10px;
    justify-content: flex-end;
    margin-bottom: 10px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.compact-toggle-text[data-v-7ef5b0ff] {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    text-align: center
}

.compact-toggle-button .icon[data-v-7ef5b0ff] {
    height: 16px;
    width: 16px
}

.compact-toggle-button-expanded .icon[data-v-7ef5b0ff] {
    transform: rotate(180deg)
}

.html-editor-selection[data-v-7ef5b0ff] {
    border-radius: 8px;
    display: -webkit-box;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 10px;
    opacity: .5;
    padding: 10px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    max-height: 2.2em;
    overflow: hidden;
    text-overflow: ellipsis
}

.chat-sessions-wrapper[data-v-7ef5b0ff] {
    background-color: transparent;
    display: flex;
    flex-direction: column;
    transition: width .3s ease-in-out;
    width: var(--container-width, 680px)
}

@media (max-width:768px) {
    .chat-sessions-wrapper[data-v-7ef5b0ff] {
        width: 100%
    }
}

.load-chat-session[data-v-7ef5b0ff] {
    align-items: center;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    font-size: 14px;
    gap: 12px;
    height: 30px;
    justify-content: center
}

@media (hover:hover) {
    .load-chat-session[data-v-7ef5b0ff]:hover {
        background: #f5f5f5
    }
    @media (prefers-color-scheme:dark) {
        .load-chat-session[data-v-7ef5b0ff] {
            color: #999
        }
        .load-chat-session[data-v-7ef5b0ff]:hover {
            background: #333
        }
    }
}

.chat-session-time[data-v-7ef5b0ff] {
    color: #999
}

.chat-session-time[data-v-7ef5b0ff],
.loading-chat-session[data-v-7ef5b0ff] {
    align-items: center;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    height: 30px;
    justify-content: center
}

.is_asking[data-v-7ef5b0ff] {
    padding-left: 16px
}

@media (max-width:1880px) and (min-width:1220px) {
    .general-chat-wrapper.with-tool-call-result-sidebar[data-v-7ef5b0ff] {
        --container-width: 480px
    }
}

.recommend-queries[data-v-7ef5b0ff] {
    margin-bottom: -12px;
    width: var(--container-width, 680px)
}

.recommend-queries .recommend-queries-inner[data-v-7ef5b0ff] {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 10px;
    overflow-y: auto;
    padding: 0 0 14px;
    width: 100%
}

.recommend-query[data-v-7ef5b0ff] {
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    cursor: pointer;
    flex-direction: row;
    gap: 5px;
    padding: 2px 6px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.recommend-query[data-v-7ef5b0ff],
.recommend-query .icon[data-v-7ef5b0ff] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    justify-content: center
}

.recommend-query .icon[data-v-7ef5b0ff] {
    height: 20px;
    width: 20px
}

.conversation-statement.plain-text[data-v-7ef5b0ff] {
    position: relative
}

.message-actions-user[data-v-7ef5b0ff] {
    align-items: center;
    display: none;
    font-size: 12px;
    gap: 7px;
    height: 20px;
    justify-content: flex-end;
    line-height: 20px;
    min-width: 120px;
    padding-top: 5px;
    position: absolute;
    right: 0;
    top: 100%
}

.conversation-statement.user .message-actions-user.content-editable[data-v-7ef5b0ff],
.conversation-statement.user:hover .message-actions-user[data-v-7ef5b0ff] {
    display: flex
}

.message-action .icon[data-v-7ef5b0ff] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    width: 16px
}

.message-action .icon[data-v-7ef5b0ff] svg {
    height: 100%;
    width: 100%
}

.message-action[data-v-7ef5b0ff] {
    align-items: center;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    gap: 4px
}

[data-v-7ef5b0ff] .thinking_prompt {
    color: #666;
    display: inline;
    position: relative;
    -webkit-text-fill-color: transparent;
    animation: loading-shimmer-7ef5b0ff 1.2s linear infinite alternate;
    background: #666 -webkit-gradient(linear, 100% 0, 0 0, from(#666), color-stop(.5, #d9d9d9), to(#666));
    background-clip: text;
    -webkit-background-clip: text;
    background-repeat: no-repeat;
    background-size: 70% 100%
}

@keyframes loading-shimmer-7ef5b0ff {
    0% {
        background-position: -100% top
    }
    to {
        background-position: 250% top
    }
}

@keyframes shine-7ef5b0ff {
    0% {
        transform: translate(-100%)
    }
    50% {
        transform: translate(200%)
    }
    to {
        transform: translate(-100%)
    }
}

@media (prefers-color-scheme:dark) {
    [data-v-7ef5b0ff] .thinking_prompt {
        color: #999
    }
}

.to-bottom-icon[data-v-7ef5b0ff] {
    cursor: pointer;
    left: 50%;
    position: absolute;
    top: -35px;
    transform: translate(-50%)
}

.empty-placeholder[data-v-7ef5b0ff] {
    color: #232425;
    font-family: Arial;
    font-size: 30px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin-top: 30vh;
    text-align: center
}

@media (max-width:1220px) {
    .empty-placeholder[data-v-7ef5b0ff] {
        margin-top: 10vh
    }
}

.sheets-agent-empty-placeholder[data-v-7ef5b0ff] {
    margin-top: 180px
}

.empty-placeholder ul.datasets-agent-tips[data-v-7ef5b0ff] {
    font-size: 16px;
    font-weight: 500;
    margin-left: 20px;
    margin-top: 10px
}

.empty-placeholder ul.datasets-agent-tips li[data-v-7ef5b0ff] {
    line-height: 2;
    list-style: disc;
    text-align: left
}

.loading-placeholder[data-v-7ef5b0ff] {
    align-items: center;
    display: flex;
    justify-content: center;
    margin-top: 30vh
}

@media (prefers-color-scheme:dark) {
    .empty-placeholder[data-v-7ef5b0ff] {
        color: #fff
    }
}

.file-wrapper[data-v-7ef5b0ff] {
    align-items: center;
    background: #fafafa;
    border: 1px solid #efefef;
    border-radius: 12px;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    gap: 8px;
    height: 48px;
    justify-content: flex-start;
    width: 152px
}

.file-wrapper .file-icon[data-v-7ef5b0ff] {
    background-color: #fff;
    border-radius: 8px;
    box-sizing: border-box;
    flex-shrink: 0;
    height: 36px;
    padding: 6px;
    width: 36px
}

.file-wrapper .file-icon[data-v-7ef5b0ff] svg {
    height: 100%;
    width: 100%
}

.file-wrapper .file-info[data-v-7ef5b0ff] {
    flex-grow: 1;
    max-width: 100px
}

.file-wrapper .file-info .file-name[data-v-7ef5b0ff] {
    color: #232425;
    display: block;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

.file-wrapper .file-info .file-size[data-v-7ef5b0ff] {
    color: #909499;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    width: 100%
}

.private-file-wrappers[data-v-7ef5b0ff] {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px
}

.content[data-v-7ef5b0ff] pre {
    background-color: #fafafa;
    border-radius: 10px;
    padding: 10px
}

.general-chat-wrapper[data-v-7ef5b0ff] {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
    padding: 0 0 0 20px;
    position: relative
}

@media (max-width:1220px) {
    .general-chat-wrapper[data-v-7ef5b0ff] {
        padding: 0;
        width: 100%
    }
}

.general-chat-wrapper[data-v-7ef5b0ff]:last-child {
    flex-grow: 1
}

.chat-wrapper[data-v-7ef5b0ff] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 20px;
    justify-content: flex-start;
    overflow-x: hidden;
    overflow-y: auto;
    width: 100%
}

.tool-call-result-sidebar[data-v-7ef5b0ff] {
    overflow: hidden;
    padding: 0 20px 0 10px
}

.global-canvas[data-v-7ef5b0ff],
.tool-call-result-sidebar[data-v-7ef5b0ff] {
    box-sizing: border-box;
    flex-shrink: 1;
    height: calc(100% - 4px);
    position: sticky;
    right: 0;
    top: 0;
    width: 100%
}

.global-canvas[data-v-7ef5b0ff] {
    contain: layout size;
    flex: 1;
    padding: 0 20px
}

@media (max-width:1220px) {
    .global-canvas[data-v-7ef5b0ff] {
        display: none
    }
    .global-canvas.mobile-visible[data-v-7ef5b0ff] {
        display: block
    }
    .global-canvas-mask[data-v-7ef5b0ff] {
        display: none
    }
    .global-canvas-mask.mobile-visible[data-v-7ef5b0ff] {
        display: block
    }
}

.mobile-canvas-entry[data-v-7ef5b0ff] {
    display: none
}

@media (max-width:1220px) {
    .mobile-canvas-entry[data-v-7ef5b0ff] {
        display: block
    }
}

.tool-call-result-sidebar-inner[data-v-7ef5b0ff] {
    background: #fff;
    border: 1px solid #eaeaea;
    border-radius: 12px;
    box-shadow: 0 4px 15px #00000014;
    box-sizing: border-box;
    height: 100%;
    min-height: 300px;
    min-width: 300px;
    overflow-y: auto;
    position: relative
}

.tool-call-result-sidebar-mask[data-v-7ef5b0ff] {
    display: none
}

@media (max-width:1220px) {
    .tool-call-result-sidebar[data-v-7ef5b0ff] {
        height: calc(100vh - 45px);
        left: 0;
        padding: 0;
        position: fixed;
        top: 45px;
        width: 100vw;
        z-index: 1000
    }
    .tool-call-result-sidebar .tool-call-result-sidebar-inner[data-v-7ef5b0ff] {
        border: none;
        border-radius: 12px 12px 0 0;
        box-shadow: none;
        width: 100vw
    }
    .tool-call-result-sidebar-mask[data-v-7ef5b0ff] {
        background-color: #00000080;
        display: block;
        height: 100vh;
        left: 0;
        position: fixed;
        top: 0;
        width: 100vw;
        z-index: 999
    }
    .global-canvas[data-v-7ef5b0ff] {
        height: auto;
        left: 0;
        position: fixed;
        top: 70px;
        width: 100vw;
        z-index: 1000
    }
    .global-canvas.sheets-global-canvas[data-v-7ef5b0ff] {
        height: calc(100% - 100px)
    }
    .global-canvas-mask[data-v-7ef5b0ff] {
        background-color: #00000080;
        height: 100%;
        left: 0;
        position: fixed;
        top: 0;
        width: 100vw;
        z-index: 999
    }
}

@media (prefers-color-scheme:dark) {
    .tool-call-result-sidebar-inner[data-v-7ef5b0ff] {
        background: #393939;
        border-color: #333
    }
    .global-canvas-mask[data-v-7ef5b0ff] {
        background-color: #000000e6
    }
}

@media (max-width:1220px) and (prefers-color-scheme:dark) {
    .tool-call-result-sidebar-mask[data-v-7ef5b0ff] {
        background-color: #000000e6
    }
}

.tool-call-result-sidebar-inner .close-button[data-v-7ef5b0ff] {
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    height: 24px;
    justify-content: center;
    position: absolute;
    right: 12px;
    top: 12px;
    transition: all .3s ease;
    width: 24px;
    z-index: 9999
}

.tool-call-result-sidebar-inner .close-button .icon[data-v-7ef5b0ff] {
    align-items: center;
    display: flex;
    height: 20px;
    justify-content: center;
    width: 20px
}

.tool-call-result-sidebar-inner .close-button[data-v-7ef5b0ff]:hover {
    background-color: #e6e6e6
}

@media (prefers-color-scheme:dark) {
    .tool-call-result-sidebar-inner .close-button[data-v-7ef5b0ff] {
        background-color: #333
    }
    .tool-call-result-sidebar-inner .close-button[data-v-7ef5b0ff]:hover {
        background-color: #3f3f3f
    }
}

.slide-in-out-enter-active[data-v-7ef5b0ff],
.slide-in-out-leave-active[data-v-7ef5b0ff] {
    transition: all .3s ease-in-out
}

.slide-in-out-enter-from[data-v-7ef5b0ff],
.slide-in-out-leave-to[data-v-7ef5b0ff] {
    max-width: 0
}

@media (max-width:1220px) {
    .slide-in-out-enter-active[data-v-7ef5b0ff],
    .slide-in-out-leave-active[data-v-7ef5b0ff] {
        transition: top .3s ease-in-out
    }
    .slide-in-out-enter-from[data-v-7ef5b0ff],
    .slide-in-out-leave-to[data-v-7ef5b0ff] {
        top: 100%
    }
}

.using-tool-call[data-v-7ef5b0ff] {
    align-items: center;
    background-color: #f5f5f5;
    border: 1px solid #efefef;
    border-radius: 15px;
    box-sizing: border-box;
    color: #232425;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    height: 39px;
    line-height: 150%;
    margin-bottom: 16px;
    padding: 7px 8px 7px 16px
}

.using-tool-call[data-v-7ef5b0ff]:has(+:not(.using-tool-call)) {
    border-radius: 15px 15px 0 0;
    margin-bottom: 0
}

@media (prefers-color-scheme:dark) {
    .using-tool-call[data-v-7ef5b0ff] {
        background-color: #3f3f3f;
        border: 1px solid #efefef30;
        color: #ddd
    }
}

.using-tool-call .icon[data-v-7ef5b0ff] {
    align-items: center;
    color: #909499;
    display: flex;
    height: 16px;
    justify-content: center;
    width: 16px
}

.using-tool-call a[data-v-7ef5b0ff] {
    color: #666
}

@media (prefers-color-scheme:dark) {
    .using-tool-call a[data-v-7ef5b0ff] {
        color: #999
    }
}

.using-tool-call .view-tool-call-result[data-v-7ef5b0ff] {
    display: flex;
    flex-grow: 1;
    justify-content: flex-end
}

.using-tool-call .view-tool-call-result-button[data-v-7ef5b0ff] {
    align-items: center;
    background: #eaeaea;
    border-radius: 20px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    gap: 10px;
    height: 23px;
    justify-content: center;
    min-width: -moz-fit-content;
    min-width: fit-content;
    overflow: visible;
    padding: 4px 10px;
    white-space: normal;
    word-break: keep-all
}

@media (prefers-color-scheme:dark) {
    .using-tool-call .view-tool-call-result-button[data-v-7ef5b0ff] {
        background: #393939
    }
}

.using-tool-call .arguments[data-v-7ef5b0ff] {
    color: #909499;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.using-tool-call .arguments>div[data-v-7ef5b0ff] {
    display: inline
}

.using-tool-call .label[data-v-7ef5b0ff] {
    color: #232425;
    flex-shrink: 0;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

.using-tool-call .name[data-v-7ef5b0ff] {
    white-space: nowrap
}

@media (max-width:768px) {
    .using-tool-call .label[data-v-7ef5b0ff],
    .using-tool-call .separator[data-v-7ef5b0ff] {
        display: none
    }
}

.using-tool-call .icon[data-v-7ef5b0ff] {
    flex-shrink: 0
}

@media (prefers-color-scheme:dark) {
    .using-tool-call .label[data-v-7ef5b0ff] {
        color: #ddd
    }
}

.using-tool-call .name[data-v-7ef5b0ff] {
    flex-shrink: 0
}

.inline-component[data-v-7ef5b0ff] {
    background: #fafafa;
    border: 1px solid #efefef;
    border-radius: 0 0 15px 15px;
    border-top: none;
    margin-bottom: 16px;
    padding: 16px
}

.inline-component[data-v-7ef5b0ff]:has(.create-task-card-container) {
    padding: 0
}

@media (prefers-color-scheme:dark) {
    .inline-component[data-v-7ef5b0ff] {
        background: #393939;
        border: 1px solid #efefef30
    }
}

.inline-component-enter-active[data-v-7ef5b0ff],
.inline-component-leave-active[data-v-7ef5b0ff] {
    transition: all .3s linear
}

.inline-component-enter-from[data-v-7ef5b0ff],
.inline-component-leave-to[data-v-7ef5b0ff] {
    box-sizing: border-box;
    max-height: 0;
    min-height: 16px;
    opacity: 0;
    overflow: hidden;
    padding-bottom: 0;
    padding-top: 0
}

.conversation-wrapper[data-v-7ef5b0ff] {
    background-color: transparent;
    display: flex;
    flex-direction: column;
    transition: width .3s ease-in-out;
    width: var(--container-width, 680px)
}

@media (max-width:768px) {
    .conversation-wrapper[data-v-7ef5b0ff] {
        width: 100%
    }
}

.input-wrapper-wrapper[data-v-7ef5b0ff] {
    align-items: center;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-bottom: 4px;
    padding-top: 16px;
    position: relative;
    width: 100%
}

.prompt-input-wrapper[data-v-7ef5b0ff] {
    align-items: center;
    display: flex;
    gap: 10px
}

.realtime-input-wrapper[data-v-7ef5b0ff] {
    height: 72px;
    position: relative;
    width: 375px;
    z-index: 100
}

.realtime-cover-fixed[data-v-7ef5b0ff] {
    height: 100vh;
    left: 0;
    overflow: hidden;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 10
}

.conversation-item-desc.user .bubble[data-v-7ef5b0ff] {
    background-color: #f5f5f5;
    border-radius: 16px;
    color: #232425;
    margin-top: 12px;
    max-width: 70%
}

.conversation-item-desc.user.content-editable .bubble[data-v-7ef5b0ff] {
    display: none
}

.conversation-item-desc.user .bubble[data-v-7ef5b0ff]:not(:first-child) {
    margin-top: 12px
}

.conversation-item-desc.user .bubble.private_file[data-v-7ef5b0ff] {
    padding: 0
}

.conversation-item-desc.user .bubble.image_url[data-v-7ef5b0ff] {
    max-width: 40%;
    padding: 0
}

.conversation-item-desc.user .bubble.slide_selected[data-v-7ef5b0ff],
.conversation-item-desc.user .bubble.template_card[data-v-7ef5b0ff] {
    background-color: transparent;
    border-radius: 0;
    padding: 0
}

.conversation-item-desc.user .bubble.image_url .image_url_wrapper[data-v-7ef5b0ff] {
    border-radius: 16px;
    box-sizing: border-box;
    display: flex;
    overflow: hidden;
    padding: 0
}

.conversation-item-desc.user .bubble.image_url .image_url_wrapper img[data-v-7ef5b0ff] {
    height: 100%;
    width: 100%
}

.conversation-item-desc.assistant .bubble[data-v-7ef5b0ff]:not(:first-child) {
    margin-top: 12px
}

.conversation-item-desc .bubble[data-v-7ef5b0ff] .cursor {
    display: none
}

.conversation-item-desc.assistant .bubble.thinking[data-v-7ef5b0ff] .cursor {
    display: inline
}

.conversation-item-desc .desc[data-v-7ef5b0ff] {
    display: flex;
    flex-direction: column;
    width: 100%
}

.conversation-item-desc .content[data-v-7ef5b0ff] {
    max-width: 100%
}

.conversation-statement[data-v-7ef5b0ff] {
    display: flex;
    flex-direction: column;
    list-style: none
}

.buttons[data-v-7ef5b0ff] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.border-add .button[data-v-7ef5b0ff] {
    border: 1px solid #efefef;
    border-radius: 8px;
    font-size: 14px;
    padding: 4px 8px
}

.border-add .button .deep-research-icon[data-v-7ef5b0ff] {
    align-items: center;
    background: #000;
    border-radius: 20px;
    color: #fff;
    display: flex;
    height: 20px;
    justify-content: center;
    width: 20px
}

.conversation-statement.user[data-v-7ef5b0ff] {
    align-items: flex-end;
    position: relative
}

.conversation-statement.user+.conversation-statement.user[data-v-7ef5b0ff] {
    margin-top: 30px
}

.conversation-statement.assistant+.conversation-statement.user[data-v-7ef5b0ff],
.conversation-statement.user+.conversation-statement.assistant[data-v-7ef5b0ff] {
    margin-top: 16px
}

.conversation-statement[data-v-7ef5b0ff]:first-child {
    margin-top: 5px
}

.conversation-statement.different-project[data-v-7ef5b0ff] {
    background-color: #e6f3ff1a;
    border-left: 3px solid #e6f3ff;
    padding: 10px 12px;
    position: relative
}

.conversation-statement.different-project .bubble[data-v-7ef5b0ff] {
    opacity: .85
}

.conversation-statement.different-project .msg-label[data-v-7ef5b0ff] {
    border-radius: 4px;
    color: #6ba3d6;
    font-size: 10px;
    font-weight: 500;
    left: 8px;
    padding: 2px 6px;
    position: absolute;
    top: -8px;
    z-index: 1
}

.project-group-toggle[data-v-7ef5b0ff] {
    align-items: center;
    background-color: #fffc;
    border: 1px solid rgba(0, 0, 0, .1);
    border-radius: 4px;
    color: #666;
    cursor: pointer;
    display: flex;
    height: 24px;
    justify-content: center;
    position: absolute;
    right: 8px;
    top: 8px;
    transition: all .3s ease;
    width: 24px;
    z-index: 10
}

.project-group-toggle[data-v-7ef5b0ff]:hover {
    background-color: #fffffff2;
    border-color: #0003;
    color: #333
}

.project-group-toggle svg[data-v-7ef5b0ff] {
    transition: transform .3s ease
}

.project-group-toggle svg.rotated[data-v-7ef5b0ff] {
    transform: rotate(180deg)
}

@media (prefers-color-scheme:dark) {
    .project-group-toggle[data-v-7ef5b0ff] {
        background-color: #323232cc;
        border-color: #ffffff1a;
        color: #ccc
    }
    .project-group-toggle[data-v-7ef5b0ff]:hover {
        background-color: #3c3c3cf2;
        border-color: #fff3;
        color: #fff
    }
}

.collapsed-project-group[data-v-7ef5b0ff] {
    background-color: #e6f3ff0d;
    border-left: 3px solid #e6f3ff;
    border-radius: 8px;
    margin: -10px 0 8px;
    padding: 12px 16px;
    position: relative
}

.collapsed-project-content[data-v-7ef5b0ff] {
    align-items: center;
    display: flex;
    justify-content: space-between
}

.collapsed-project-info[data-v-7ef5b0ff] {
    display: flex;
    flex-direction: column;
    gap: 4px
}

.collapsed-project-label[data-v-7ef5b0ff] {
    color: #666;
    font-size: 14px;
    font-weight: 500
}

.collapsed-project-count[data-v-7ef5b0ff] {
    color: #999;
    font-size: 12px
}

.collapsed-project-expand-btn[data-v-7ef5b0ff] {
    align-items: center;
    background-color: #ffffffb3;
    border: 1px solid rgba(0, 0, 0, .1);
    border-radius: 6px;
    color: #666;
    cursor: pointer;
    display: flex;
    height: 32px;
    justify-content: center;
    transition: all .3s ease;
    width: 32px
}

.collapsed-project-expand-btn[data-v-7ef5b0ff]:hover {
    background-color: #ffffffe6;
    border-color: #0003;
    color: #333
}

@media (prefers-color-scheme:dark) {
    .border-add .button[data-v-7ef5b0ff] {
        border: 1px solid #efefef49
    }
    .border-add .button .deep-research-icon[data-v-7ef5b0ff] {
        background: #ffffff3c
    }
    .collapsed-project-group[data-v-7ef5b0ff] {
        background-color: #e6f3ff08
    }
    .collapsed-project-label[data-v-7ef5b0ff] {
        color: #ccc
    }
    .collapsed-project-count[data-v-7ef5b0ff] {
        color: #999
    }
    .collapsed-project-expand-btn[data-v-7ef5b0ff] {
        background-color: #323232b3;
        border-color: #ffffff1a;
        color: #ccc
    }
    .collapsed-project-expand-btn[data-v-7ef5b0ff]:hover {
        background-color: #3c3c3ce6;
        border-color: #fff3;
        color: #fff
    }
}

.conversation-statement.project-color-0[data-v-7ef5b0ff] {
    background-color: #e6f3ff1a;
    border-left-color: #e6f3ff
}

.conversation-statement.project-color-0 .msg-label[data-v-7ef5b0ff] {
    color: #6ba3d6
}

.conversation-statement.project-color-1[data-v-7ef5b0ff] {
    background-color: #ffe6f31a;
    border-left-color: #ffe6f3
}

.conversation-statement.project-color-1 .msg-label[data-v-7ef5b0ff] {
    color: #d66ba3
}

.conversation-statement.project-color-2[data-v-7ef5b0ff] {
    background-color: #f3ffe61a;
    border-left-color: #f3ffe6
}

.conversation-statement.project-color-2 .msg-label[data-v-7ef5b0ff] {
    color: #a3d66b
}

.conversation-statement.project-color-3[data-v-7ef5b0ff] {
    background-color: #fff3e61a;
    border-left-color: #fff3e6
}

.conversation-statement.project-color-3 .msg-label[data-v-7ef5b0ff] {
    color: #d6a36b
}

.collapsed-project-group.project-color-0[data-v-7ef5b0ff] {
    background-color: #e6f3ff0d;
    border-left-color: #e6f3ff
}

.collapsed-project-group.project-color-1[data-v-7ef5b0ff] {
    background-color: #ffe6f30d;
    border-left-color: #ffe6f3
}

.collapsed-project-group.project-color-2[data-v-7ef5b0ff] {
    background-color: #f3ffe60d;
    border-left-color: #f3ffe6
}

.collapsed-project-group.project-color-3[data-v-7ef5b0ff] {
    background-color: #fff3e60d;
    border-left-color: #fff3e6
}

@media (prefers-color-scheme:dark) {
    .conversation-statement.different-project[data-v-7ef5b0ff] {
        background-color: #1a4d6b26;
        border-left-color: #1a4d6b
    }
    .conversation-statement.different-project .msg-label[data-v-7ef5b0ff] {
        color: #7ab8d4
    }
    .conversation-statement.project-color-0[data-v-7ef5b0ff] {
        background-color: #1a4d6b26;
        border-left-color: #1a4d6b
    }
    .conversation-statement.project-color-0 .msg-label[data-v-7ef5b0ff] {
        color: #7ab8d4
    }
    .conversation-statement.project-color-1[data-v-7ef5b0ff] {
        background-color: #6b1a4d26;
        border-left-color: #6b1a4d
    }
    .conversation-statement.project-color-1 .msg-label[data-v-7ef5b0ff] {
        color: #d47ab8
    }
    .conversation-statement.project-color-2[data-v-7ef5b0ff] {
        background-color: #4d6b1a26;
        border-left-color: #4d6b1a
    }
    .conversation-statement.project-color-2 .msg-label[data-v-7ef5b0ff] {
        color: #b8d47a
    }
    .conversation-statement.project-color-3[data-v-7ef5b0ff] {
        background-color: #6b4d1a26;
        border-left-color: #6b4d1a
    }
    .conversation-statement.project-color-3 .msg-label[data-v-7ef5b0ff] {
        color: #d4b87a
    }
}

@media (max-width:1220px) {
    .chat-wrapper[data-v-7ef5b0ff] {
        box-sizing: border-box;
        overflow-y: hidden;
        padding: 16px 16px 100px
    }
    .docs_agent .chat-wrapper[data-v-7ef5b0ff] {
        padding: 16px 16px 200px
    }
    .chat-wrapper-padding[data-v-7ef5b0ff] {
        padding-bottom: 196px
    }
    .chat-wrapper-padding-sheets[data-v-7ef5b0ff] {
        padding-bottom: 250px
    }
    .chat-wrapper-padding-sheets .sheets-agent-empty-placeholder[data-v-7ef5b0ff] {
        margin-top: 12vh
    }
    .input-wrapper-wrapper[data-v-7ef5b0ff] {
        bottom: 0;
        box-sizing: border-box;
        left: 0;
        padding: 16px 16px 24px;
        position: fixed;
        width: 100%;
        z-index: 100
    }
    .realtime-input-wrapper[data-v-7ef5b0ff] {
        position: relative;
        width: 100%;
        z-index: 100
    }
    .realtime-cover-fixed[data-v-7ef5b0ff] {
        height: 100vh;
        left: 0;
        overflow: hidden;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 5
    }
    .input-wrapper-wrapper-inner[data-v-7ef5b0ff] {
        width: 100%
    }
}

@media (prefers-color-scheme:dark) {
    .content[data-v-7ef5b0ff] pre {
        background-color: #222
    }
    .input-wrapper-wrapper[data-v-7ef5b0ff] {
        background-color: #232425
    }
    .conversation-item-desc.user .bubble[data-v-7ef5b0ff] {
        background: #eee;
        color: #000
    }
}

.mobile-sidebar-page[data-v-7ef5b0ff] {
    background: #fff;
    height: 100%;
    left: 0;
    overflow-y: auto;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 90
}

.slide-enter-active[data-v-7ef5b0ff],
.slide-leave-active[data-v-7ef5b0ff] {
    transition: transform .3s ease
}

.slide-enter-from[data-v-7ef5b0ff],
.slide-leave-to[data-v-7ef5b0ff] {
    transform: translate(100%)
}

.slide-enter-to[data-v-7ef5b0ff],
.slide-leave-from[data-v-7ef5b0ff] {
    transform: translate(0)
}

@media (prefers-color-scheme:dark) {
    .mobile-sidebar-page[data-v-7ef5b0ff] {
        background: #1a1a1a
    }
}

.search-source-sidebar.desktop[data-v-7ef5b0ff] {
    background: #fff;
    box-shadow: -2px 0 8px #00000026;
    height: 100vh;
    position: fixed;
    right: 0;
    top: 0;
    width: 400px;
    z-index: 90
}

.search-source-sidebar.desktop[data-v-7ef5b0ff] .sidebar-content {
    height: 100%;
    overflow-y: auto
}

.slide-desktop-enter-active[data-v-7ef5b0ff],
.slide-desktop-leave-active[data-v-7ef5b0ff] {
    transition: transform .3s ease
}

.slide-desktop-enter-from[data-v-7ef5b0ff],
.slide-desktop-leave-to[data-v-7ef5b0ff] {
    transform: translate(100%)
}

.slide-desktop-enter-to[data-v-7ef5b0ff],
.slide-desktop-leave-from[data-v-7ef5b0ff] {
    transform: translate(0)
}

@media (prefers-color-scheme:dark) {
    .search-source-sidebar.desktop[data-v-7ef5b0ff] {
        background: #1a1a1a;
        box-shadow: -2px 0 8px #0000004d
    }
}

@media (min-width:1220px) {
    .browser-extension-bar[data-v-7ef5b0ff] {
        width: var(--container-width, 680px)
    }
}

.with-infinite-canvas .general-chat-wrapper.with-tool-call-result-sidebar[data-v-7ef5b0ff] {
    padding-right: 16px
}

.with-infinite-canvas .global-canvas[data-v-7ef5b0ff] {
    height: calc(100% - 6px);
    overflow: visible;
    padding: 0 16px 0 0
}

.with-infinite-canvas .global-canvas .tool-call-result-sidebar-inner[data-v-7ef5b0ff] {
    border: none;
    border-radius: 0;
    box-shadow: none;
    overflow: visible
}

.bubble.context_length_exceeded[data-v-7ef5b0ff] {
    align-items: center;
    background: #fff;
    color: #b42318;
    display: flex;
    flex-direction: row;
    gap: 12px;
    justify-content: space-between;
    padding: 16px
}

.bubble.context_length_exceeded .left[data-v-7ef5b0ff] {
    flex: 1
}

.bubble.context_length_exceeded .right[data-v-7ef5b0ff] {
    display: flex
}

.bubble.context_length_exceeded .new-chat-session[data-v-7ef5b0ff] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    gap: 10px;
    justify-content: flex-start;
    width: 100%
}

.bubble.context_length_exceeded .new-chat-session .desc[data-v-7ef5b0ff] {
    color: #232425;
    font-family: Arial;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%
}

.bubble.context_length_exceeded .new-chat-session .button[data-v-7ef5b0ff] {
    align-items: center;
    background: #232425;
    border-radius: 8px;
    box-sizing: border-box;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 6px;
    height: 32px;
    line-height: 150%;
    padding: 5px 16px
}

.bubble.context_length_exceeded .new-chat-session .button .icon[data-v-7ef5b0ff] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    width: 16px
}

@media (prefers-color-scheme:dark) {
    .bubble.context_length_exceeded .new-chat-session .desc[data-v-7ef5b0ff] {
        color: #fff
    }
    .bubble.context_length_exceeded .new-chat-session .button[data-v-7ef5b0ff] {
        background: #fff;
        color: #232425
    }
    .bubble.context_length_exceeded .new-chat-session .button[data-v-7ef5b0ff]:hover {
        background: #e0e0e0;
        transition: background-color .2s
    }
    .bubble.context_length_exceeded[data-v-7ef5b0ff] {
        background: #232425;
        border: 1px solid #444;
        color: #ff6b6b
    }
}

.general-chat-wrapper.with-inbox-canvas[data-v-7ef5b0ff] {
    --container-width: 100%;
    padding: 0
}

.global-canvas.inbox-canvas-fullscreen[data-v-7ef5b0ff] {
    flex-grow: 1;
    padding: 0;
    width: 100%
}

.global-canvas.inbox-canvas-fullscreen .tool-call-result-sidebar-inner[data-v-7ef5b0ff] {
    border: none;
    border-radius: 0;
    box-shadow: none
}

@media (max-width:1220px) {
    .global-canvas.inbox-canvas-fullscreen[data-v-7ef5b0ff] {
        height: 100%;
        top: 0
    }
}