import {
    ar as r,
    as as n,
    F as t
} from "./Cf0SOiw0.js";

function a(r, ...n) {
    if (!Array.isArray(r)) return r(...n);
    r.forEach((r => a(r, ...n)))
}

function u(a) {
    return a.some((a => !r(a) || a.type !== n && !(a.type === t && !u(a.children)))) ? a : null
}

function s(r, n) {
    return r && u(r()) || n()
}

function e(r, n, t) {
    return r && u(r(n)) || t(n)
}

function i(r, n) {
    return n(r && u(r()) || null)
}

function o(r) {
    return !(r && u(r()))
}
export {
    s as a, e as b, a as c, u as e, o as i, i as r
};