const __vite__mapDeps = (i, m = __vite__mapDeps, d = (m.f || (m.f = ["./CM3Hoj-z.js", "./Cf0SOiw0.js", "./entry.CjsqieIi.css", "./gsZIXP6B.js", "./BUCk-Nnr.js", "./CAfqOhBF.js", "./LoadingIcon.DBGzfwQJ.css", "./4N2ByosX.js", "./e-ES_T8J.js", "./B76FSNBo.js", "./CfpQPauw.js", "./HCr483ve.js", "./Bm_HbXT2.js", "./B0db5Fvl.js", "./BUs-AQWo.js", "./BdlGQsae.js", "./DOnko34f.js", "./DyMB-pVc.js", "./DW6cX6jm.js", "./CKd5XOy1.js", "./Cl89jLsD.js", "./DjawIWev.js", "./C38RzRfR.js", "./BLWq1oPC.js", "./CW991W2w.js", "./DpMvtoun.js", "./Jr9eiJio.js", "./9wLWmnxl.js", "./BPQGB51Y.js", "./C-H3edso.js", "./B7VeW_-d.js", "./WZsIN7xM.js", "./pB_XRIgB.js", "./BuhfKjCJ.js", "./BGK9k_mT.js", "./By6xEfKc.js", "./BrPr1fm5.js", "./DAjjhrgi.js", "./MpDLC7up.js", "./DGJMLFjI.js", "./BihyrXkC.js", "./CAmLbDGM.js", "./DQpEsQQa.js", "./BjWUbj3w.js", "./B6noBY_5.js", "./feedback_dialog.DMSfPXgH.css", "./Cu_n4xpI.js", "./ResearchMeModal.CIL8KOGg.css", "./W5AxVKvJ.js", "./DWCxnUK7.js", "./P7LDOl0u.js", "./DT-NG54s.js", "./COYh4g7i.js", "./CaEkZ53E.js", "./DXvAIxvL.js", "./menu.BFjHCDBG.css", "./CQ2glRxo.js", "./Bzg9uoz_.js", "./YoutubeWidget.CYLc38aB.css", "./BspXWmOn.js", "./Dc8Bac8D.js", "./4s4Iy95q.js", "./DrzlY6u5.js", "./index_layout.FoCKiQ9w.css", "./IgM9N0FT.js", "./BffV2qdL.js", "./D0Ouax4K.js", "./DnWGHCrg.js", "./CQjXacSG.js", "./F4fwMVvr.js", "./qk0HrepY.js", "./DqWfLcpp.js", "./CRIx66FB.js", "./CIlzw36e.js", "./D386eQgZ.js", "./D5ao1EUl.js", "./C_XD2eP3.js", "./BJGgZAPd.js", "./DLUhQFIq.js", "./wD492QF4.js", "./Dd3cVSEE.js", "./Chtxu0jj.js", "./FCN43o2W.js", "./DbJ6Dt9m.js", "./Bl-gMEVt.js", "./zH1ZpJ79.js", "./LGmiBiLz.js", "./FileTypeIcon.BQIzFgr3.css", "./nuQnue4a.js", "./D5IxqnO4.js", "./C1lFdfgL.js", "./BThdTUgg.js", "./BN-NNxvY.js", "./D9ll07Bp.js", "./Dflnlfvw.js", "./CrbPJ6Kt.js", "./Di7Ot5aL.js", "./DKpDUEYb.js", "./CAzLTKWw.js", "./CJmWj3ri.js", "./PlanPlusDesc.C8oWeb3m.css", "./9KCDKcmx.js", "./PlanProDesc.DOSRRpAW.css", "./0XKHBXCr.js", "./PricingWindowForPlusPlan.CbG0vZqH.css", "./DPPm6Skg.js", "./lJbCVLd4.js", "./PlanCreditDesc.xiOfDrvw.css", "./PricingWindowForTeamCreditsClaim.SgSSHRIB.css", "./DY44xVYu.js", "./QuotaExceed.DgKuFXXK.css", "./UVj2ej2A.js", "./CVKRwtBu.js", "./Dnth285N.js", "./LoginGuidance.Dm3Ueo8u.css", "./CbfxwEmT.js", "./Ug8thHSu.js", "./BV6guSOS.js", "./BqHcVhvy.js", "./CSefR0kE.js", "./data.Du5y1n5K.css", "./C_QvFyDp.js", "./CfHz9NLm.js", "./ImageGenerating.CYegq0bI.css", "./BuZX8s8J.js", "./image_editor.CgOynLPR.css", "./BXNOMSAZ.js", "./agents_configs.pCdHKEuw.css", "./SearchInputWrapper.PldMiM8q.css", "./DeFGaANp.js", "./D6bQc9d9.js", "./LoadingAnimation.B2xv_2PZ.css", "./DJ-JsGJu.js", "./DZ51nUum.js", "./BMP7zQoC.js", "./DxgY8w7w.js", "./CRmNre8Y.js", "./agent_task_list.Dbv5-9sy.css", "./index.BectADji.css", "./DKfgo1Ia.js", "./CmF_-QGy.js", "./8u4bVPF0.js", "./FontSelector.CzpyUOlP.css", "./ImageMetaForm.BOY4dwcZ.css", "./CbGSSggI.js", "./D3TnIu40.js", "./Cb7t0ry_.js", "./DwlXYv12.js", "./C6zJMIN5.js"]))) => i.map(i => d[i]);
var e = Object.defineProperty,
    t = (t, a, n) => ((t, a, n) => a in t ? e(t, a, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: n
    }) : t[a] = n)(t, "symbol" != typeof a ? a + "" : a, n);
import {
    d as a,
    o as n,
    b as o,
    D as i,
    W as l,
    r,
    c as s,
    s as d,
    A as c,
    ag as u,
    C as g,
    U as h,
    cE as v,
    v as p,
    J as f,
    F as m,
    k as w,
    y,
    a as x,
    x as I,
    h as k,
    q as C,
    f as b,
    T as E,
    t as M,
    _ as S,
    w as _,
    n as N,
    ai as T,
    l as D,
    aJ as P,
    a4 as F,
    m as L,
    e as A,
    H as R,
    L as B,
    a9 as z,
    i as O
} from "./Cf0SOiw0.js";
import {
    f as $,
    l as U,
    h as H
} from "./DKfgo1Ia.js";
import {
    u as j
} from "./B0db5Fvl.js";
import {
    u as W,
    B as X,
    I as V,
    A as G,
    a as Y,
    b as Z
} from "./CmF_-QGy.js";
import {
    C as q
} from "./Bm_HbXT2.js";
import {
    F as J
} from "./8u4bVPF0.js";
import {
    u as K
} from "./gsZIXP6B.js";
import {
    L as Q
} from "./BUCk-Nnr.js";
import {
    u as ee
} from "./B6noBY_5.js";
import {
    f as te
} from "./Bl-gMEVt.js";
const ae = "" + new URL("logo.DopCppMG.png",
    import.meta.url).href;
let ne = null;

function oe() {
    if (!ne) throw new Error("Store getter not initialized. Call setStoreGetter() first.");
    return ne()
}
class ie {
    constructor(e) {
        t(this, "_data"), this._data = e
    }
    get rawData() {
        return this._data
    }
    get id() {
        return this._data.id
    }
    get type() {
        return this._data.type
    }
    get name() {
        return this._data.name || this.type
    }
    get childrenData() {
        return this._data.children || []
    }
    get children() {
        const e = this._data.children || [],
            t = oe();
        return e.map((e => t.getNodeOfData(e))).filter((e => void 0 !== e))
    }
}
class le extends ie {
    constructor(e) {
        super(e)
    }
}
class re extends ie {
    constructor(e) {
        super(e)
    }
    get _canvasData() {
        return this._data
    }
    get rawData() {
        return super.rawData
    }
    get parentNode() {
        const e = oe(),
            t = e.calculateParentId(this.id);
        return t ? e.getNodeById(t) : null
    }
    get left() {
        return this._canvasData.left
    }
    get top() {
        return this._canvasData.top
    }
    get width() {
        return this._canvasData.width || 0
    }
    get height() {
        return this._canvasData.height || 0
    }
    get rotation() {
        return this._canvasData.rotation || 0
    }
    get isPageNode() {
        const e = this.parentNode instanceof le;
        return !e || this.width && this.height || $("isPageNode but width or height is not set. if result from server, should be determined by llm or image w/h. if result from client(drag out from inner frame for example), should be determined by bounding box", this.id, this.name, this.width, this.height), e
    }
}
class se extends re {
    constructor(e) {
        super(e)
    }
    get _frameData() {
        return this._canvasData
    }
    get backgroundColor() {
        return this._frameData.background_color ? this._frameData.background_color : this.isPageNode ? "#FFFFFF" : "transparent"
    }
}
class de extends re {
    constructor(e) {
        super(e)
    }
    get _htmlFragmentData() {
        return this._canvasData
    }
    get html() {
        return this._htmlFragmentData.html
    }
}
const ce = ["SUCCESS", "FAILURE", "NSFW", -2, -3];
class ue extends re {
    constructor(e) {
        super(e)
    }
    get _imageData() {
        return this._canvasData
    }
    get src() {
        return this._imageData.src
    }
    get isBackgroundImage() {
        const e = this.parentNode;
        if (!e) return !1;
        if (!(e instanceof re)) return !1;
        if (!e.isPageNode) return !1;
        const t = this.left ? ? 0,
            a = this.top ? ? 0,
            n = (this.left ? ? 0) + (this.width ? ? 0),
            o = (this.top ? ? 0) + (this.height ? ? 0);
        return t <= 0 && a <= 0 && n >= e.width && o >= e.height
    }
    get generating() {
        var e, t;
        return (null == (t = null == (e = this._imageData.extra_info) ? void 0 : e.task_status) ? void 0 : t.id) && !ce.includes(this._imageData.extra_info.task_status.status ? ? "")
    }
    get generateTaskId() {
        var e, t;
        return (null == (t = null == (e = this._imageData.extra_info) ? void 0 : e.task_status) ? void 0 : t.id) ? ? ""
    }
    get generateMessageId() {
        var e;
        return (null == (e = this._imageData.extra_info) ? void 0 : e.message_id) ? ? ""
    }
    get originalGenerateMessageId() {
        var e;
        return (null == (e = this._imageData.extra_info) ? void 0 : e.original_generated_image_from_message_id) ? ? ""
    }
    get originalFinalTextPrompt() {
        var e;
        return (null == (e = this._imageData.extra_info) ? void 0 : e.original_final_text_prompt) ? ? ""
    }
    get metaInfo() {
        var e;
        return (null == (e = this._imageData.extra_info) ? void 0 : e.meta_info) ? ? null
    }
    get finalTextPrompt() {
        var e;
        return (null == (e = this._imageData.extra_info) ? void 0 : e.final_text_prompt) ? ? null
    }
    get editHistory() {
        var e;
        return (null == (e = this._imageData.extra_info) ? void 0 : e.edit_history) ? ? null
    }
}
const ge = {
    fontSize: 80,
    fontFamily: "Arial, sans-serif",
    color: "#000000",
    fontWeight: "normal",
    fontStyle: "normal",
    textAlign: "left"
};
class he extends re {
    constructor(e) {
        super(e)
    }
    get _textData() {
        return this._canvasData
    }
    get text() {
        return this._textData.text || ""
    }
    get fontSize() {
        return this._textData.fontSize || ge.fontSize
    }
    get fontFamily() {
        return this._textData.fontFamily || ge.fontFamily
    }
    get color() {
        return this._textData.color || ge.color
    }
    get fontWeight() {
        return this._textData.fontWeight || ge.fontWeight
    }
    get fontStyle() {
        return this._textData.fontStyle || ge.fontStyle
    }
    get textAlign() {
        return this._textData.textAlign || ge.textAlign
    }
    get textStyle() {
        return {
            fontSize: this.fontSize,
            fontFamily: this.fontFamily,
            color: this.color,
            fontWeight: this.fontWeight,
            fontStyle: this.fontStyle,
            textAlign: this.textAlign
        }
    }
    get actualWidth() {
        if (!this.text) return 0;
        if ("undefined" != typeof document) {
            const e = document.createElement("canvas").getContext("2d");
            if (e) {
                e.font = `${this.fontStyle} ${this.fontWeight} ${this.fontSize}px ${this.fontFamily}`;
                const t = e.measureText(this.text);
                return Math.ceil(t.width)
            }
        }
        return $("get actualWidth fail, return fallback"), this.text.length * this.fontSize * .6
    }
    get actualHeight() {
        if (!this.text) return 1 * this.fontSize;
        const e = 1 * this.fontSize,
            t = this.text.split("\n").length;
        return Math.max(this.fontSize, Math.ceil(t * e))
    }
    get width() {
        return this._textData.width || this.actualWidth
    }
    get height() {
        return this._textData.height || this.actualHeight
    }
    updateText(e) {
        this._textData.text = e
    }
    updateFontSize(e) {
        this._textData.fontSize = Math.max(8, Math.min(1024, e))
    }
    updateStyle(e) {
        void 0 !== e.fontSize && (this._textData.fontSize = Math.max(8, Math.min(1024, e.fontSize))), void 0 !== e.fontFamily && (this._textData.fontFamily = e.fontFamily), void 0 !== e.color && (this._textData.color = e.color), void 0 !== e.fontWeight && (this._textData.fontWeight = e.fontWeight), void 0 !== e.fontStyle && (this._textData.fontStyle = e.fontStyle), void 0 !== e.textAlign && (this._textData.textAlign = e.textAlign)
    }
    get isEmpty() {
        return !this.text || 0 === this.text.trim().length
    }
}
class ve extends ie {
    constructor(e) {
        super(e)
    }
    get left() {
        return this.rawData.left
    }
    get top() {
        return this.rawData.top
    }
    get width() {
        return this.rawData.width
    }
    get height() {
        return this.rawData.height
    }
    get paths() {
        return this.rawData.paths || []
    }
    get rotation() {
        return this.rawData.rotation || 0
    }
    get isPageNode() {
        return !1
    }
}
var pe = (e => (e.PROJECT = "project", e.FRAME = "frame", e.HTML_FRAGMENT = "html_fragment", e.IMAGE = "image", e.TEXT = "text", e.SVG = "svg", e))(pe || {});
const fe = {
    width: "16",
    height: "17",
    viewBox: "0 0 16 17",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const me = {
        render: function(e, t) {
            return n(), a("svg", fe, t[0] || (t[0] = [o("rect", {
                x: "2.08984",
                y: "3",
                width: "11",
                height: "11",
                rx: "1.1",
                stroke: "#232425",
                "stroke-dasharray": "2.8 2.8"
            }, null, -1)]))
        }
    },
    we = {
        width: "16",
        height: "17",
        viewBox: "0 0 16 17",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const ye = {
        render: function(e, t) {
            return n(), a("svg", we, t[0] || (t[0] = [i('<g clip-path="url(#clip0_842_16017)"><path d="M5.99023 3.83398H13.9902C14.3584 3.83398 14.6569 4.13245 14.6569 4.50065V13.1673C14.6569 13.5355 14.3584 13.834 13.9902 13.834H5.99023" stroke="#232425"></path><path d="M1.32422 4.50065C1.32422 4.13245 1.6227 3.83398 1.99089 3.83398H5.99089V13.834H1.99089C1.6227 13.834 1.32422 13.5355 1.32422 13.1673V4.50065Z" stroke="#232425"></path><path d="M1.32422 5.83398L5.99089 5.83398" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M1.32422 7.83398L5.99089 7.83398" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M1.32422 9.83398L5.99089 9.83398" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M1.32422 11.834L5.99089 11.834" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12.3242 5.83398L8.32422 11.5007" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path></g><defs><clipPath id="clip0_842_16017"><rect width="16" height="16" fill="white" transform="translate(-0.00976562 0.5)"></rect></clipPath></defs>', 2)]))
        }
    },
    xe = {
        width: "16",
        height: "17",
        viewBox: "0 0 16 17",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Ie = {
        render: function(e, t) {
            return n(), a("svg", xe, t[0] || (t[0] = [i('<rect x="1.59473" y="2.5" width="12" height="12" rx="1.6" stroke="#232425"></rect><path d="M8.79492 2.90039L8.79492 14.1004" stroke="#232425" stroke-linecap="round"></path><path d="M5.59473 6.09961H5.99473" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M5.59473 8.5H5.99473" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M7.99512 8.5H8.39512" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M5.59473 10.9004H5.99473" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M7.99512 5.2998H8.39512" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M3.19531 6.90039H3.59531" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M7.99512 11.7002H8.39512" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M3.19531 9.2998H3.59531" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path>', 10)]))
        }
    },
    ke = {
        width: "16",
        height: "17",
        viewBox: "0 0 16 17",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Ce = {
        render: function(e, t) {
            return n(), a("svg", ke, t[0] || (t[0] = [i('<path d="M7.6569 7.83398H2.99023C2.43795 7.83398 1.99023 8.2817 1.99023 8.83398V13.5007C1.99023 14.0529 2.43795 14.5007 2.99023 14.5007H7.6569C8.20919 14.5007 8.6569 14.0529 8.6569 13.5007V8.83398C8.6569 8.2817 8.20919 7.83398 7.6569 7.83398Z" stroke="#232425" stroke-linejoin="round"></path><path d="M9.99023 2.5H13.9902V6.5" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M11.3242 15C11.6004 15 11.8242 14.7761 11.8242 14.5C11.8242 14.2239 11.6004 14 11.3242 14C11.0481 14 10.8242 14.2239 10.8242 14.5C10.8242 14.7761 11.0481 15 11.3242 15Z" fill="#232425" stroke="#232425" stroke-width="0.333333"></path><path d="M1.99023 4.66602C2.26638 4.66602 2.49023 4.88987 2.49023 5.16602C2.49023 5.44216 2.26638 5.66602 1.99023 5.66602C1.71409 5.66602 1.49023 5.44216 1.49023 5.16602C1.49023 4.88987 1.71409 4.66602 1.99023 4.66602Z" fill="#232425" stroke="#232425" stroke-width="0.333333"></path><path d="M13.9902 15C14.2664 15 14.4902 14.7761 14.4902 14.5C14.4902 14.2239 14.2664 14 13.9902 14C13.7141 14 13.4902 14.2239 13.4902 14.5C13.4902 14.7761 13.7141 15 13.9902 15Z" fill="#232425" stroke="#232425" stroke-width="0.333333"></path><path d="M1.99023 2C2.26638 2 2.49023 2.22386 2.49023 2.5C2.49023 2.77614 2.26638 3 1.99023 3C1.71409 3 1.49023 2.77614 1.49023 2.5C1.49023 2.22386 1.71409 2 1.99023 2Z" fill="#232425" stroke="#232425" stroke-width="0.333333"></path><path d="M13.9902 12.334C14.2664 12.334 14.4902 12.1101 14.4902 11.834C14.4902 11.5578 14.2664 11.334 13.9902 11.334C13.7141 11.334 13.4902 11.5578 13.4902 11.834C13.4902 12.1101 13.7141 12.334 13.9902 12.334Z" fill="#232425" stroke="#232425" stroke-width="0.333333"></path><path d="M4.65625 2C4.93239 2 5.15625 2.22386 5.15625 2.5C5.15625 2.77614 4.93239 3 4.65625 3C4.38011 3 4.15625 2.77614 4.15625 2.5C4.15625 2.22386 4.38011 2 4.65625 2Z" fill="#232425" stroke="#232425" stroke-width="0.333333"></path><path d="M13.9902 9.66602C14.2664 9.66602 14.4902 9.44216 14.4902 9.16602C14.4902 8.88987 14.2664 8.66602 13.9902 8.66602C13.7141 8.66602 13.4902 8.88987 13.4902 9.16602C13.4902 9.44216 13.7141 9.66602 13.9902 9.66602Z" fill="#232425" stroke="#232425" stroke-width="0.333333"></path><path d="M7.32422 2C7.60036 2 7.82422 2.22386 7.82422 2.5C7.82422 2.77614 7.60036 3 7.32422 3C7.04808 3 6.82422 2.77614 6.82422 2.5C6.82422 2.22386 7.04808 2 7.32422 2Z" fill="#232425" stroke="#232425" stroke-width="0.333333"></path><path d="M1.99023 11.8334L4.03007 10.3035C4.3982 10.0274 4.9072 10.0382 5.2633 10.3296L8.32357 12.8334" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path><path d="M13.9902 2.5L9.99023 6.5" stroke="#232425" stroke-linecap="round" stroke-linejoin="round"></path>', 12)]))
        }
    },
    be = {
        width: "16",
        height: "17",
        viewBox: "0 0 16 17",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Ee = {
        render: function(e, t) {
            return n(), a("svg", be, t[0] || (t[0] = [o("path", {
                d: "M1.99023 14.5H13.9902",
                stroke: "#232425",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M9.91245 2.5L3.32422 9.43333L4.97128 11.1667H7.16736L12.6576 5.38889L9.91245 2.5Z",
                stroke: "#232425",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    Me = {
        width: "16",
        height: "17",
        viewBox: "0 0 16 17",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Se = {
    render: function(e, t) {
        return n(), a("svg", Me, t[0] || (t[0] = [o("path", {
            d: "M10.7863 3.37307L11.1757 2.98367C11.4856 2.67393 11.9057 2.49994 12.3438 2.5C12.782 2.50006 13.2021 2.67415 13.5118 2.98397C13.8216 3.2938 13.9956 3.71398 13.9955 4.15209C13.9955 4.59019 13.8214 5.01033 13.5115 5.32007L13.1215 5.70947C13.1215 5.70947 12.2947 5.66087 11.5651 4.93067C10.8349 4.20107 10.7863 3.37307 10.7863 3.37307ZM10.7863 3.37307L7.20733 6.95208C6.96493 7.19448 6.84373 7.31568 6.73933 7.44948C6.61613 7.60748 6.51133 7.77708 6.42493 7.95828C6.35233 8.11128 6.29833 8.27388 6.18973 8.59908L5.84233 9.64008M5.84233 9.64008L5.61793 10.3133C5.59154 10.3918 5.58757 10.4761 5.60648 10.5568C5.62539 10.6374 5.66642 10.7112 5.72496 10.7698C5.78349 10.8284 5.85722 10.8695 5.93784 10.8885C6.01847 10.9075 6.10279 10.9036 6.18133 10.8773L6.85513 10.6529M5.84233 9.64008L6.85513 10.6529M13.1221 5.70887L9.54313 9.28788C9.30073 9.53028 9.17953 9.65148 9.04573 9.75588C8.88802 9.8788 8.71742 9.98422 8.53693 10.0703C8.38393 10.1429 8.22133 10.1969 7.89613 10.3055L6.85513 10.6529",
            stroke: "#232425"
        }, null, -1), o("path", {
            d: "M13.9951 8.5C13.9951 11.8138 11.3089 14.5 7.99512 14.5C4.68132 14.5 1.99512 11.8138 1.99512 8.5C1.99512 5.1862 4.68132 2.5 7.99512 2.5",
            stroke: "#232425",
            "stroke-linecap": "round"
        }, null, -1)]))
    }
};
var _e = (e => (e.RECTANGLE_SELECT = "rectangle-select", e.BRUSH_SELECT = "brush-select", e.OBJECT_SELECT = "object-select", e.UPSCALE = "upscale", e.OUTPAINT = "outpaint", e.REMOVE_BG = "remove-bg", e.REMOVER = "remover", e.REDRAW = "redraw", e.SMUDGE = "smudge", e.FAST_EDIT_BY_TEXT = "fast-edit-by-text", e.COPY_AS_TEMPLATE = "copy-as-template", e))(_e || {});
const Ne = [{
        id: "rectangle-select",
        label: "Select",
        svg: me,
        showEditor: !1
    }, {
        id: "remove-bg",
        label: "BG Remover",
        svg: ye,
        showEditor: !0
    }, {
        id: "remover",
        label: "Magic Eraser",
        svg: Ee,
        showEditor: !0
    }, {
        id: "redraw",
        label: "Magic Redraw",
        svg: Se,
        showEditor: !0
    }, {
        id: "upscale",
        label: "Image Unblur",
        svg: Ie,
        showEditor: !0
    }, {
        id: "outpaint",
        label: "Image Expand",
        svg: Ce,
        showEditor: !0
    }],
    Te = [];
var De = (e => (e.NONE = "none", e.FORM_EDIT = "edit", e.TOOL = "tool", e.ADD_SUB_ELEMENTS = "add", e.DOODLE = "doodle", e))(De || {});
const Pe = .05,
    Fe = 4;
var Le = (e => (e.NONE = "none", e.CREATING = "creating", e.MOVING = "moving", e.RESIZING = "resizing", e))(Le || {}),
    Ae = (e => (e.TOP_LEFT = "top-left", e.TOP = "top", e.TOP_RIGHT = "top-right", e.RIGHT = "right", e.BOTTOM_RIGHT = "bottom-right", e.BOTTOM = "bottom", e.BOTTOM_LEFT = "bottom-left", e.LEFT = "left", e))(Ae || {});
const Re = l("infiniteCanvasMaskSelection", (() => {
    const e = r(null),
        t = r(!1),
        a = r("none"),
        n = r(null),
        o = r(null),
        i = r(null),
        l = r(null),
        d = r(!1),
        c = r(null);
    let u = null;
    const g = r(""),
        h = 10;
    let v, p = null;

    function f() {
        U("Cancelling current selection", {
            interactionMode: a.value,
            hasSelection: !!e.value
        }), "creating" === a.value ? (e.value = null, t.value = !1, a.value = "none", n.value = null, o.value = null) : "moving" === a.value ? (o.value && (e.value = { ...o.value
        }), M()) : "resizing" === a.value && (o.value && (e.value = { ...o.value
        }), _()), U("Selection operation cancelled, staying in select tool")
    }

    function m(e) {
        if (!v) throw new Error("Selection store not initialized with context");
        return {
            x: (e.x - v.canvasTranslateX) / v.canvasScale,
            y: (e.y - v.canvasTranslateY) / v.canvasScale
        }
    }

    function w(e) {
        const t = e.target,
            a = t.closest("[data-node-id]"),
            n = (null == a ? void 0 : a.getAttribute("data-node-id")) || null;
        return n ? U("Node found from event", {
            nodeId: n,
            targetElement: t.tagName,
            nodeElement: null == a ? void 0 : a.tagName
        }) : U("No node found from event", {
            targetElement: t.tagName,
            closestNodeElement: a
        }), n
    }

    function y(e) {
        if (!v) throw new Error("Selection store not initialized with context");
        const t = v.getNodeById(e.imageNodeId);
        if (!t || !t.isPageNode) return U("No page node found for constraint", {
            imageNodeId: e.imageNodeId
        }), e;
        const a = t,
            n = {
                x: a.left,
                y: a.top,
                width: a.width || 0,
                height: a.height || 0
            };
        U("Constraining selection", {
            original: e,
            imageBounds: n,
            nodeId: e.imageNodeId
        });
        const o = Math.max(n.x, Math.min(e.x, n.x + n.width - e.width)),
            i = Math.max(n.y, Math.min(e.y, n.y + n.height - e.height)),
            l = Math.min(e.width, n.x + n.width - o),
            r = Math.min(e.height, n.y + n.height - i),
            s = { ...e,
                x: o,
                y: i,
                width: Math.max(h, l),
                height: Math.max(h, r)
            };
        return U("Selection constrained", {
            wasConstrained: s.x !== e.x || s.y !== e.y || s.width !== e.width || s.height !== e.height,
            constrained: s
        }), s
    }
    async function x(t) {
        const a = t || e.value;
        if (!a || !v) return null;
        u && (u.abort(), U("Previous mask upload cancelled")), u = new AbortController;
        const n = u.signal;
        d.value = !0, l.value = null;
        try {
            const e = v.getNodeById(a.imageNodeId);
            if (!e) throw new Error("Image node not found");
            const t = e => new Promise(((t, a) => {
                    const n = new Image;
                    n.crossOrigin = "anonymous";
                    let o = e;
                    try {
                        const t = new URL(e);
                        o = `https://page1.genspark.site${t.pathname}${t.search}${t.hash}`
                    } catch (i) {}
                    n.onload = () => t(n), n.onerror = e => a(e), n.src = o
                })),
                o = await t(e.src || ""),
                i = document.createElement("canvas"),
                r = i.getContext("2d");
            if (!r) throw new Error("Could not create canvas context");
            i.width = o.width, i.height = o.height, r.drawImage(o, 0, 0);
            const s = e,
                d = s.width || 1,
                u = s.height || 1,
                g = (a.x - s.left) * (o.width / d),
                h = (a.y - s.top) * (o.height / u),
                p = a.width * (o.width / d),
                f = a.height * (o.height / u);
            r.clearRect(g, h, p, f);
            const m = await new Promise(((e, t) => {
                i.toBlob((a => {
                    a ? e(a) : t(new Error("Failed to create blob from canvas"))
                }), "image/png")
            }));
            if (c.value && URL.revokeObjectURL(c.value), c.value = URL.createObjectURL(m), U("Mask preview generated during upload:", c.value), n.aborted) throw new Error("Upload cancelled");
            const w = await fetch("/api/get_upload_url", {
                signal: n
            });
            if (!w.ok) throw new Error(`HTTP error! status: ${w.status}`);
            const y = await w.json();
            if (!y || 0 !== y.status) throw new Error(`API error! code: ${y.code}`);
            if (!y.data) throw new Error("API response missing data object");
            const x = y.data.upload_image_url,
                I = y.data.page_url;
            if (n.aborted) throw new Error("Upload cancelled");
            if (!(await fetch(x, {
                    method: "PUT",
                    headers: {
                        "x-ms-blob-type": "BlockBlob"
                    },
                    body: m,
                    signal: n
                })).ok) throw new Error("Upload failed");
            return l.value = I, c.value && URL.revokeObjectURL(c.value), c.value = I, I
        } catch (o) {
            return o instanceof Error && ("AbortError" === o.name || "Upload cancelled" === o.message) ? (U("Mask upload cancelled intentionally"), null) : (l.value = null, c.value && (URL.revokeObjectURL(c.value), c.value = null), null)
        } finally {
            u && u.signal === n ? (d.value = !1, u = null, U("Upload completed and uploading state cleared")) : U("Upload completed but not clearing state (newer upload in progress)")
        }
    }

    function I(n, o) {
        l.value = null, d.value = !1, c.value && (URL.revokeObjectURL(c.value), c.value = null), e.value = {
            x: n.x,
            y: n.y,
            width: 0,
            height: 0,
            imageNodeId: o
        }, t.value = !0, a.value = "creating", U("Selection started", {
            point: n,
            imageNodeId: o,
            interactionMode: a.value
        })
    }

    function k(t) {
        if (!e.value || !n.value) return void U("Update selection skipped - missing data", {
            hasSelection: !!e.value,
            hasDragStart: !!n.value
        });
        const a = Math.min(n.value.x, t.x),
            o = Math.min(n.value.y, t.y),
            i = {
                x: a,
                y: o,
                width: Math.max(n.value.x, t.x) - a,
                height: Math.max(n.value.y, t.y) - o,
                imageNodeId: e.value.imageNodeId
            };
        U("Updating selection", {
            dragStart: n.value,
            currentPoint: t,
            newSelection: i
        }), e.value = y(i)
    }

    function C() {
        a.value, t.value = !1, a.value = "none", e.value && (e.value.width < h || e.value.height < h) ? b() : (e.value && (U("Selection completed, auto-uploading mask"), x(e.value)), U("Selection completed, keeping selection for mask editing"))
    }

    function b() {
        U("Clearing selection", {
            hadSelection: !!e.value,
            previousMode: a.value
        }), e.value = null, t.value = !1, a.value = "none", n.value = null, o.value = null, i.value = null, l.value = null, d.value = !1, c.value && (URL.revokeObjectURL(c.value), c.value = null)
    }

    function E(t) {
        if (!e.value || !o.value || !n.value) return void U("Update move skipped - missing data", {
            hasSelection: !!e.value,
            hasInitialSelection: !!o.value,
            hasDragStart: !!n.value
        });
        const a = t.x - n.value.x,
            i = t.y - n.value.y,
            l = { ...e.value,
                x: o.value.x + a,
                y: o.value.y + i
            };
        U("Updating move", {
            point: t,
            delta: {
                x: a,
                y: i
            },
            newPosition: l
        }), e.value = y(l)
    }

    function M() {
        U("Move ended", {
            finalPosition: e.value
        }), a.value = "none", n.value = null, o.value = null, e.value && (U("Move completed, auto-uploading mask"), x(e.value))
    }

    function S(t) {
        if (!(e.value && o.value && n.value && i.value)) return void U("Update resize skipped - missing data", {
            hasSelection: !!e.value,
            hasInitialSelection: !!o.value,
            hasDragStart: !!n.value,
            hasActiveHandle: !!i.value
        });
        const a = t.x - n.value.x,
            l = t.y - n.value.y;
        U("Resize update calculation", {
            handle: i.value,
            delta: {
                x: a,
                y: l
            },
            initialSelection: o.value
        });
        let r = o.value.x,
            s = o.value.y,
            d = o.value.width,
            c = o.value.height;
        switch (i.value) {
            case "top-left":
                r += a, s += l, d -= a, c -= l;
                break;
            case "top":
                s += l, c -= l;
                break;
            case "top-right":
                s += l, d += a, c -= l;
                break;
            case "right":
                d += a;
                break;
            case "bottom-right":
                d += a, c += l;
                break;
            case "bottom":
                c += l;
                break;
            case "bottom-left":
                r += a, d -= a, c += l;
                break;
            case "left":
                r += a, d -= a
        }
        d < h && (i.value.includes("left") && (r = o.value.x + o.value.width - h), d = h, U("Width constrained to minimum", {
            newWidth: d,
            newX: r
        })), c < h && (i.value.includes("top") && (s = o.value.y + o.value.height - h), c = h, U("Height constrained to minimum", {
            newHeight: c,
            newY: s
        }));
        const u = { ...e.value,
            x: r,
            y: s,
            width: d,
            height: c
        };
        U("Resize result", {
            newSelection: u,
            wasConstrained: d === h || c === h
        }), e.value = y(u)
    }

    function _() {
        U("Resize ended", {
            handle: i.value,
            finalSelection: e.value
        }), a.value = "none", n.value = null, o.value = null, i.value = null, e.value && (U("Resize completed, auto-uploading mask"), x(e.value))
    }
    const N = Ge((t => {
            var a;
            if (!v) return void U("ERROR: Selection store context not initialized!");
            const i = t.target.closest(".j-viewport");
            if (!i) return void U("ERROR: No viewport found", {
                target: t.target,
                targetClass: t.target.className
            });
            const l = i.getBoundingClientRect(),
                r = {
                    x: t.clientX - l.left,
                    y: t.clientY - l.top
                },
                s = m(r);
            U("Mouse down", {
                event: {
                    clientX: t.clientX,
                    clientY: t.clientY
                },
                viewportPoint: r,
                canvasPoint: s,
                hasSelection: !!e.value
            }), b();
            const d = w(t);
            if (!d) return void U("Click outside nodes - exit selection");
            const c = v.getNodeById(d);
            if (!c || c.type !== pe.IMAGE) return;
            const u = c;
            U("Click on image node", {
                nodeId: d,
                nodeType: c.type,
                isPageNode: u.isPageNode,
                isBackgroundImage: u.isBackgroundImage
            }), (u.isPageNode || u.isBackgroundImage) && (U("Click on page image node", {
                nodeId: d,
                currentSelectionImageId: null == (a = e.value) ? void 0 : a.imageNodeId,
                willClearSelection: e.value && e.value.imageNodeId !== d
            }), n.value = s, o.value = {
                x: s.x,
                y: s.y,
                width: 0,
                height: 0,
                imageNodeId: d
            }, U("Ready for selection creation", {
                dragStartPoint: n.value,
                imageNodeId: d
            }))
        }), 16),
        T = Ge((e => {
            const t = e.target.closest(".j-viewport");
            if (!t) return;
            if ((null == v ? void 0 : v.activeMode) !== De.TOOL || (null == v ? void 0 : v.currentToolId) !== _e.RECTANGLE_SELECT) return;
            const i = t.getBoundingClientRect(),
                l = m({
                    x: e.clientX - i.left,
                    y: e.clientY - i.top
                });
            if ("creating" === a.value) k(l);
            else if ("moving" === a.value) E(l);
            else if ("resizing" === a.value) S(l);
            else if (n.value && o.value) {
                const e = Math.sqrt(Math.pow(l.x - n.value.x, 2) + Math.pow(l.y - n.value.y, 2));
                e > 5 && (U("Distance threshold met - starting selection", {
                    distance: e,
                    threshold: 5,
                    dragStart: n.value,
                    currentPoint: l
                }), I(n.value, o.value.imageNodeId))
            }
        }), 16),
        D = () => {
            U("Mouse up", {
                interactionMode: a.value,
                hasSelection: !!e.value,
                selection: e.value
            }), "creating" === a.value ? C() : "moving" === a.value ? M() : "resizing" === a.value && _(), n.value = null, e.value || (o.value = null), U("Mouse up cleanup complete", {
                dragStartPoint: n.value,
                initialSelection: o.value
            })
        };
    return {
        selection: e,
        isSelecting: t,
        isDragging: s((() => !!n.value)),
        interactionMode: a,
        activeResizeHandle: i,
        canvasScale: s((() => (null == v ? void 0 : v.canvasScale) ? ? 1)),
        maskUrl: l,
        isMaskUploading: d,
        maskPreviewUrl: c,
        editInputValue: g,
        initWithContext: function(e) {
            v || (v = e, U("Selection store initialized with context", {
                canvasScale: e.canvasScale,
                canvasTranslateX: e.canvasTranslateX,
                canvasTranslateY: e.canvasTranslateY
            }), function() {
                if ("undefined" == typeof window) return;
                p = e => {
                    "Escape" === e.key && "none" !== a.value && (U("ESC key pressed during selection operation, cancelling selection"), f())
                }, window.addEventListener("keydown", p), U("Keyboard event listener set up for selection cancellation")
            }())
        },
        startSelection: I,
        updateSelection: k,
        endSelection: C,
        clearSelection: b,
        clearSelectionGeometry: function() {
            U("Clearing selection geometry only", {
                hadSelection: !!e.value,
                previousMode: a.value
            }), e.value = null, t.value = !1, a.value = "none", n.value = null, o.value = null, i.value = null
        },
        startMove: function(t) {
            e.value ? (o.value = { ...e.value
            }, n.value = t, a.value = "moving", U("Move started", {
                point: t,
                selection: e.value,
                interactionMode: a.value
            })) : U("Start move skipped - no selection")
        },
        updateMove: E,
        endMove: M,
        startResize: function(t, l) {
            e.value ? (i.value = t, o.value = { ...e.value
            }, n.value = l, a.value = "resizing", U("Resize started", {
                handle: t,
                point: l,
                selection: e.value,
                interactionMode: a.value
            })) : U("Start resize skipped - no selection")
        },
        updateResize: S,
        endResize: _,
        generateMaskBlob: async function(t) {
            const a = t || e.value;
            if (!a || !v) return null;
            try {
                const e = v.getNodeById(a.imageNodeId);
                if (!e) throw new Error("Image node not found");
                const t = e => new Promise(((t, a) => {
                        const n = new Image;
                        n.crossOrigin = "anonymous";
                        let o = e;
                        try {
                            const t = new URL(e);
                            o = `https://page1.genspark.site${t.pathname}${t.search}${t.hash}`
                        } catch (i) {}
                        n.onload = () => t(n), n.onerror = e => a(e), n.src = o
                    })),
                    n = await t(e.src || ""),
                    o = document.createElement("canvas"),
                    i = o.getContext("2d");
                if (!i) throw new Error("Could not create canvas context");
                o.width = n.width, o.height = n.height, i.drawImage(n, 0, 0);
                const l = e,
                    r = l.width || 1,
                    s = l.height || 1,
                    d = (a.x - l.left) * (n.width / r),
                    c = (a.y - l.top) * (n.height / s),
                    u = a.width * (n.width / r),
                    g = a.height * (n.height / s);
                return i.clearRect(d, c, u, g), new Promise(((e, t) => {
                    o.toBlob((a => {
                        a ? e(a) : t(new Error("Failed to create blob from canvas"))
                    }), "image/png")
                }))
            } catch (n) {
                return null
            }
        },
        generateAndUploadMask: x,
        cancelCurrentSelection: f,
        cleanupKeyboardEventListener: function() {
            p && "undefined" != typeof window && (window.removeEventListener("keydown", p), p = null, U("Keyboard event listener cleaned up"))
        },
        viewportToCanvasCoordinates: m,
        getNodeIdFromEvent: w,
        isPointInSelection: function(t) {
            if (!e.value) return !1;
            const a = t.x >= e.value.x && t.x <= e.value.x + e.value.width && t.y >= e.value.y && t.y <= e.value.y + e.value.height;
            return U("Point in selection check", {
                point: t,
                selection: e.value,
                isInside: a
            }), a
        },
        getResizeHandleAtPoint: function(t) {
            if (!e.value) return null;
            if (!v) throw new Error("Selection store not initialized with context");
            const a = e.value,
                n = 8 / v.canvasScale,
                o = n / 2;
            U("Checking resize handles", {
                point: t,
                selection: a,
                handleSize: n,
                scaledHandleSize: n
            });
            const i = [{
                handle: "top-left",
                x: a.x,
                y: a.y
            }, {
                handle: "top",
                x: a.x + a.width / 2,
                y: a.y
            }, {
                handle: "top-right",
                x: a.x + a.width,
                y: a.y
            }, {
                handle: "right",
                x: a.x + a.width,
                y: a.y + a.height / 2
            }, {
                handle: "bottom-right",
                x: a.x + a.width,
                y: a.y + a.height
            }, {
                handle: "bottom",
                x: a.x + a.width / 2,
                y: a.y + a.height
            }, {
                handle: "bottom-left",
                x: a.x,
                y: a.y + a.height
            }, {
                handle: "left",
                x: a.x,
                y: a.y + a.height / 2
            }];
            for (const {
                    handle: e,
                    x: l,
                    y: r
                } of i)
                if (t.x >= l - o && t.x <= l + o && t.y >= r - o && t.y <= r + o) return U("Found resize handle", {
                    handle: e,
                    position: {
                        x: l,
                        y: r
                    }
                }), e;
            return U("No resize handle found at point"), null
        },
        constrainSelectionToImage: y,
        handleMaskSelectionMouseDown: N,
        handleMaskSelectionMouseMove: T,
        handleMaskSelectionMouseUp: D,
        handleMaskSelectionMouseLeave: () => {
            U("Mouse leave viewport", {
                interactionMode: a.value,
                willCancel: "none" !== a.value
            }), "none" !== a.value && D()
        }
    }
}));

function Be(e, t) {
    const a = document.createElement("canvas");
    a.width = e, a.height = t;
    const n = a.getContext("2d", {
        willReadFrequently: !0
    });
    return {
        canvas: a,
        ctx: n
    }
}
async function ze(e) {
    return new Promise(((t, a) => {
        const n = new Image;
        n.crossOrigin = "anonymous", n.onload = () => t(n), n.onerror = a, n.src = e
    }))
}

function Oe(e, t) {
    let a = e.left,
        n = e.top,
        o = e;
    for (; o;) {
        const e = t.calculateParentId(o.id);
        if (!e) break;
        const i = t.getNodeById(e);
        if (!(i && i instanceof re)) break;
        a += i.left, n += i.top, o = i
    }
    return {
        x: a,
        y: n
    }
}
const $e = l("infiniteCanvasCutting", (() => {
        const {
            t: e
        } = d(), t = c(), a = {}, n = r(!1), o = r(null), i = r(null), l = r(null), h = r([]), v = r(-1), p = r(new Map);
        let f = null;
        async function m(n, o) {
            const i = await (async e => {
                const t = await e.arrayBuffer(),
                    a = await crypto.subtle.digest("SHA-256", t);
                return Array.from(new Uint8Array(a)).map((e => e.toString(16).padStart(2, "0"))).join("")
            })(n);
            if (i && a[i]) return g.log("use cache for file: " + n.name), a[i];
            let l = 52428800,
                r = 2097152;
            if (t.query.upload_file_size_limit && (l = 104857600, r = 104857600), n.size) {
                if (!n.type.startsWith("image/") && n.size > r) throw alert(e("components.promptinput.file_size_limit_non_image")), new Error(e("components.promptinput.file_size_limit_non_image"));
                if (n.size > l) throw alert(e("components.promptinput.file_size_limit_image")), new Error(e("components.promptinput.file_size_limit_image"))
            }
            try {
                let e = {
                    name: n.name,
                    type: n.type,
                    size: n.size,
                    ext: o.ext
                };
                const {
                    uploadImageUrl: t,
                    pageUrl: l
                } = await (async () => {
                    const e = await fetch("/api/get_upload_url");
                    if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                    const t = await e.json();
                    if (!t || 0 !== t.status) throw new Error(`API error! code: ${t.code}`);
                    const a = t.data.upload_image_url,
                        n = t.data.private_storage_url;
                    return {
                        uploadImageUrl: a,
                        path: t.data.path,
                        privateStorageUrl: n,
                        pageUrl: t.data.page_url
                    }
                })();
                if (!t || !l) throw new Error("get upload image url failed");
                (new FormData).append("file", n);
                if (!(await fetch(t, {
                        method: "PUT",
                        headers: {
                            "x-ms-blob-type": "BlockBlob"
                        },
                        body: n
                    })).ok) throw new Error("Network response was not ok");
                return g.log("upload file ok: " + t), e.url = l, i && (g.log("cache file: " + n.name), a[i] = e), e
            } catch (s) {
                throw s
            }
        }

        function w() {
            U("Ending cutting operation"), o.value && p.value.delete(o.value), n.value = !1, o.value = null, i.value = null, l.value = null, h.value = [], v.value = -1
        }
        const y = s((() => v.value >= 0 && v.value < h.value.length ? h.value[v.value] : null));
        return {
            isCutting: s((() => n.value)),
            sourceImageNodeId: s((() => o.value)),
            fragment: y,
            fragments: s((() => h.value)),
            activeFragmentIndex: s((() => v.value)),
            modifiedImageData: s((() => l.value)),
            initWithContext: function(e) {
                f || (f = e, U("Cutting store initialized with context"), u((() => {
                    o.value && !e.getNodeById(o.value) && (U("Source image node was deleted, clearing cutting state"), w())
                })))
            },
            processModify: async function(e, t) {
                var a;
                if (!f) throw new Error("Cutting store not initialized");
                const n = f.getNodeById(e);
                if (!(n && n instanceof ue)) throw new Error("Invalid image node");
                U("Processing modify operation", {
                    imageNodeId: e,
                    selection: t
                });
                const o = j();
                o.setPrompt(`Modify image with mask:\n- Mask image: the uploaded attachment image\n- Original image: ${n.src}\n- Instructions: `), o.setImages([""]), null == (a = document.querySelector(".j-search-input")) || a.focus(), U("loading image");
                const i = await ze(n.src);
                U("loading image done", {
                    img: i.src
                });
                const {
                    canvas: l,
                    ctx: r
                } = Be(i.width, i.height);
                r.drawImage(i, 0, 0);
                const s = Oe(n, f),
                    d = n.width ? n.width : 1,
                    c = n.height ? n.height : 1,
                    u = (t.x - s.x) * (i.width / d),
                    g = (t.y - s.y) * (i.height / c),
                    h = t.width * (i.width / d),
                    v = t.height * (i.height / c);
                r.clearRect(u, g, h, v);
                const p = l.toDataURL("image/png"),
                    w = await fetch(p),
                    y = await w.blob();
                U("convert data url to blob done", {
                    blob: y
                });
                const x = new File([y], "mask.png", {
                    type: "image/png"
                });
                U("uploading file", {
                    file: x.name
                });
                const I = await m(x, {
                    ext: "png"
                });
                U("upload file done", I.url), o.setImages([I.url]), U("Modify result added to chat input", {
                    modifiedImage: I.url,
                    originalImage: n.src
                })
            },
            startCutting: async function(e, t) {
                if (!f) throw new Error("Cutting store not initialized");
                const a = f.getNodeById(e);
                if (!(a && a instanceof ue)) throw new Error("Invalid image node");
                U("Starting cutting operation", {
                    imageNodeId: e,
                    selection: t
                });
                const r = l.value || a.src,
                    s = await ze(r),
                    {
                        canvas: d,
                        ctx: c
                    } = Be(s.width, s.height);
                c.drawImage(s, 0, 0);
                const u = Oe(a, f),
                    g = a.width ? a.width : 1,
                    m = a.height ? a.height : 1,
                    w = (t.x - u.x) * (s.width / g),
                    y = (t.y - u.y) * (s.height / m),
                    x = t.width * (s.width / g),
                    I = t.height * (s.height / m),
                    k = c.getImageData(w, y, x, I),
                    {
                        canvas: C,
                        ctx: b
                    } = Be(x, I);
                b.putImageData(k, 0, 0);
                const E = C.toDataURL("image/png");
                c.clearRect(w, y, x, I);
                const M = d.toDataURL("image/png");
                i.value || (i.value = a.src), n.value = !0, o.value = e, l.value = M, p.value.set(e, M);
                const S = {
                    id: `fragment-${Date.now()}`,
                    imageData: E,
                    x: t.x,
                    y: t.y,
                    width: t.width,
                    height: t.height,
                    originalWidth: t.width,
                    originalHeight: t.height,
                    scale: 1,
                    isDragging: !1,
                    dragStartPoint: null,
                    initialPosition: null
                };
                h.value.push(S), v.value = h.value.length - 1, U("Cutting started", {
                    fragment: S,
                    modifiedImageData: l.value,
                    modifiedImageMapSize: p.value.size,
                    fragmentsCount: h.value.length,
                    activeFragmentIndex: v.value
                })
            },
            startReplacing: async function(e) {
                if (!f) throw new Error("Cutting store not initialized");
                const t = f.getNodeById(e);
                if (!(t && t instanceof ue)) throw new Error("Invalid image node");
                U("Starting replacing operation", {
                    imageNodeId: e
                }), i.value || (i.value = t.src), n.value = !0, o.value = e, U("Replacing mode started", {
                    sourceImageNodeId: o.value
                })
            },
            endCutting: w,
            cancelCutting: function() {
                U("Canceling cutting operation"), w()
            },
            applyCutting: async function() {
                if (0 === h.value.length || !o.value || !f) throw new Error("No active cutting operation");
                const e = f.getNodeById(o.value);
                if (!(e && e instanceof ue)) throw new Error("Invalid image node");
                U("Applying cutting operation", {
                    fragments: h.value
                });
                const t = await ze(e.src),
                    {
                        canvas: a,
                        ctx: n
                    } = Be(t.width, t.height);
                if (l.value) {
                    const e = await ze(l.value);
                    n.drawImage(e, 0, 0)
                } else n.drawImage(t, 0, 0);
                const i = Oe(e, f);
                for (const o of h.value) {
                    const a = await ze(o.imageData),
                        l = t.width / e.width,
                        r = (o.x - i.x) * l,
                        s = (o.y - i.y) * l,
                        d = o.width * l,
                        c = o.height * l;
                    n.drawImage(a, r, s, d, c)
                }
                const r = a.toDataURL("image/png"),
                    s = window.open();
                return s && (s.document.write(`<img src="${r}" style="max-width: 100%; height: auto;" />`), s.document.title = "Applied Result"), w(), r
            },
            setActiveFragment: function(e) {
                e >= 0 && e < h.value.length && (v.value = e)
            },
            startFragmentDrag: function(e) {
                const t = y.value;
                if (!t) return;
                const a = v.value;
                h.value[a] = { ...t,
                    isDragging: !0,
                    dragStartPoint: { ...e
                    },
                    initialPosition: {
                        x: t.x,
                        y: t.y
                    }
                }, U("Fragment drag started", {
                    point: e,
                    fragment: h.value[a],
                    index: a
                })
            },
            updateFragmentDrag: function(e) {
                const t = v.value;
                if (t < 0) return;
                const a = h.value[t];
                if (!(a && a.isDragging && a.dragStartPoint && a.initialPosition)) return;
                const n = e.x - a.dragStartPoint.x,
                    o = e.y - a.dragStartPoint.y;
                h.value[t] = { ...a,
                    x: a.initialPosition.x + n,
                    y: a.initialPosition.y + o
                }, U("Fragment drag updated", {
                    point: e,
                    delta: {
                        x: n,
                        y: o
                    },
                    newPosition: {
                        x: h.value[t].x,
                        y: h.value[t].y
                    }
                })
            },
            endFragmentDrag: function() {
                const e = v.value;
                if (e < 0) return;
                const t = h.value[e];
                t && (h.value[e] = { ...t,
                    isDragging: !1,
                    dragStartPoint: null,
                    initialPosition: null
                }, U("Fragment drag ended"))
            },
            scaleFragment: function(e) {
                const t = v.value;
                if (t < 0) return;
                const a = h.value[t];
                if (!a) return;
                const n = a.scale,
                    o = Math.max(.1, Math.min(10, e)),
                    i = a.originalWidth * o,
                    l = a.originalHeight * o,
                    r = o - n,
                    s = a.x - a.originalWidth * r / 2,
                    d = a.y - a.originalHeight * r / 2;
                h.value[t] = { ...a,
                    scale: o,
                    width: i,
                    height: l,
                    x: s,
                    y: d
                }, U("Fragment scaled", {
                    oldScale: n,
                    newScale: o,
                    newDimensions: {
                        width: i,
                        height: l
                    }
                })
            },
            updateFragmentDimensions: function(e, t, a, n) {
                const o = v.value;
                if (o < 0) return;
                const i = h.value[o];
                if (!i) return;
                const l = a / i.originalWidth,
                    r = n / i.originalHeight,
                    s = Math.max(l, r);
                h.value[o] = { ...i,
                    x: e,
                    y: t,
                    width: a,
                    height: n,
                    scale: s
                }, U("Fragment dimensions updated", {
                    position: {
                        x: e,
                        y: t
                    },
                    dimensions: {
                        width: a,
                        height: n
                    },
                    scale: s
                })
            },
            getModifiedImageData: function(e) {
                return p.value.get(e) || null
            },
            addReplaceFragment: async function(e, t) {
                if (!f || !o.value) throw new Error("Cutting store not initialized or no source image");
                const a = f.getNodeById(o.value);
                if (!(a && a instanceof ue)) throw new Error("Invalid image node");
                const l = await ze(e);
                let r, s, d, c, u;
                if (t) r = t.x, s = t.y, d = t.width, c = t.height, u = Math.max(d / l.width, c / l.height);
                else {
                    const e = Oe(a, f),
                        t = e.x + a.width / 2,
                        n = e.y + a.height / 2,
                        o = l.width / l.height;
                    d = .8 * a.width, c = d / o, r = t - d / 2, s = n - c / 2, u = d / l.width
                }
                const g = {
                    id: `replace-fragment-${Date.now()}`,
                    imageData: e,
                    x: r,
                    y: s,
                    width: d,
                    height: c,
                    originalWidth: l.width,
                    originalHeight: l.height,
                    scale: u,
                    isDragging: !1,
                    dragStartPoint: null,
                    initialPosition: null
                };
                n.value || (n.value = !0, o.value = a.id, i.value = a.src), h.value.push(g), v.value = h.value.length - 1, U("Replace fragment added", {
                    fragment: g,
                    fragmentsCount: h.value.length,
                    activeFragmentIndex: v.value
                })
            }
        }
    })),
    Ue = l("infiniteCanvasPositionSync", (() => {
        const e = r(new Set),
            t = r(new Set),
            a = r(!1),
            n = r(0),
            o = r("");
        let i = null;
        const l = Ye(),
            s = e => {
                const t = l.getNodeById(e);
                return t ? t.rawData : ($("Node not found in store:", e), null)
            },
            d = () => {
                i && clearTimeout(i);
                const t = e.value.size;
                U(`Sync scheduled in 1000ms, pending pages: ${t}`), i = setTimeout((async () => {
                    await u()
                }), 1e3)
            },
            c = async () => {
                i && (clearTimeout(i), i = null), await u()
            },
            u = async () => {
                if (a.value) return U("Sync already in progress, scheduling another sync for pending changes"), void d();
                if (0 === e.value.size || !o.value) return;
                const i = new Set(e.value);
                t.value = new Set(i), e.value.clear(), U("Moving pages from pending to syncing:", {
                    syncingPageIds: Array.from(t.value),
                    remainingPendingPages: e.value.size
                });
                const l = [];
                for (const e of i) {
                    const t = s(e);
                    t ? l.push(t) : $("Failed to build page data for pageId:", e)
                }
                if (0 === l.length) return $("No valid page data to sync"), void t.value.clear();
                U("Syncing pages:", {
                    project_id: o.value,
                    pages: l.map((e => {
                        var t;
                        return {
                            id: e.id,
                            type: e.type,
                            childrenCount: (null == (t = e.children) ? void 0 : t.length) || 0
                        }
                    }))
                }), a.value = !0;
                try {
                    const a = await fetch("/api/infinite_canvas/sync_manual_update", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify({
                            project_id: o.value,
                            pages: l
                        })
                    });
                    if (!a.ok) throw new Error(`HTTP error! status: ${a.status}`);
                    const i = await a.json();
                    if (0 === i.status) U("Pages synced successfully, clearing syncing set"), t.value.clear(), n.value = Date.now();
                    else {
                        $("Sync failed, moving pages back to pending:", i.message);
                        for (const a of t.value) e.value.add(a);
                        t.value.clear(), e.value.size > 0 && (U("Scheduling retry for failed sync"), d())
                    }
                } catch (r) {
                    $("Error syncing pages, moving pages back to pending:", r);
                    for (const a of t.value) e.value.add(a);
                    t.value.clear(), e.value.size > 0 && (U("Scheduling retry after sync error"), d())
                } finally {
                    a.value = !1
                }
            };
        return {
            onNodeChanged: t => {
                const a = l.getNodeById(t);
                if (!a) return void $("Node not found for onNodeChanged:", t);
                const n = a.isPageNode;
                let o;
                if (n) o = t;
                else {
                    const e = l.findParentPageId(t);
                    if (!e) return void $("Cannot find parent page for child node:", t);
                    o = e;
                    if (!l.getNodeById(o).isPageNode) return void $("not direct child of page node:", t)
                }
                U("Scheduling sync for node change:", {
                    changedNodeId: t,
                    nodeType: a.type,
                    isPageLevel: n,
                    pageIdToSync: o
                }), e.value.add(o), d()
            },
            forceSyncNow: c,
            setProjectId: a => {
                o.value !== a && (U("Project ID changed:", {
                    oldProjectId: o.value,
                    newProjectId: a
                }), e.value.size && c(), e.value.clear(), t.value.clear(), i && (clearTimeout(i), i = null), U("Cleared all pending and syncing changes"), o.value = a)
            }
        }
    })),
    He = l("infiniteCanvasText", (() => {
        const e = Ue(),
            t = r("default"),
            a = r(null),
            n = r(""),
            o = r("none"),
            i = r(""),
            l = r("none"),
            d = r(!1),
            c = r(16),
            u = r(null);
        let g = null,
            v = null;

        function p(e, t = !1) {
            if (!g) return void U("Cannot enter edit mode - store context not initialized");
            const l = g.getNodeById(e);
            l && l.type === pe.TEXT ? (U("Entering text edit mode", {
                textId: e,
                isNewText: t
            }), a.value = e, o.value = "editing", i.value = l.text, n.value = l.text, g.selectNode(e)) : U("Cannot edit - not a text node", {
                textId: e
            })
        }

        function f() {
            if (!a.value || !g) return;
            const e = a.value;
            U("Cancelling text edit", {
                textId: e,
                willDelete: "" === i.value.trim()
            }), "" === i.value.trim() && g.deleteNode(e), m()
        }

        function m() {
            U("Exiting text edit mode"), a.value = null, n.value = "", i.value = "", o.value = "none", g && g.triggerCanvasUpdate()
        }
        const w = s((() => "editing" === o.value)),
            y = s((() => {
                if (!g) return !1;
                const e = g.getNodeById(g.selectedNodeId || "");
                return (null == e ? void 0 : e.type) === pe.TEXT
            })),
            x = s((() => y.value ? 1 : 0));
        return {
            editingTextId: a,
            editingText: n,
            editingMode: o,
            transformMode: l,
            isTransforming: d,
            cursorStyle: t,
            defaultTextStyle: ge,
            isEditing: w,
            hasSelectedText: y,
            selectedTextCount: x,
            initWithContext: function(e) {
                g ? $("Text store already initialized") : (g = e, U("Text store initialized with context", {
                    canvasScale: e.canvasScale
                }), function() {
                    if ("undefined" == typeof window) return;
                    v = e => {
                        if ("Escape" === e.key && "editing" === o.value && f(), ("Delete" === e.key || "Backspace" === e.key) && g && g.selectedNodeId && "none" === o.value) {
                            const e = g.getNodeById(g.selectedNodeId);
                            e && e.type === pe.TEXT && g.deleteNode(g.selectedNodeId)
                        }
                    }, window.addEventListener("keydown", v), U("Text store keyboard event listener set up")
                }())
            },
            cleanupKeyboardEventListener: function() {
                v && "undefined" != typeof window && (window.removeEventListener("keydown", v), v = null, U("Text store keyboard event listener cleaned up"))
            },
            createTextAtPosition: function(t, a = "") {
                if (!g) return U("Cannot create text - store context not initialized"), null;
                const n = g.selectedPageNodeId;
                if (!n) return U("Cannot create text - no page selected"), null;
                const o = g.getNodeById(n);
                if (!o || o.type !== pe.IMAGE && o.type !== pe.FRAME) return U("Cannot create text - selected node is not an image or frame"), null;
                const i = `text_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,
                    l = o.rawData,
                    r = t.x - l.left,
                    s = t.y - l.top,
                    d = {
                        id: i,
                        type: pe.TEXT,
                        name: "Text",
                        left: r,
                        top: s,
                        width: 0,
                        height: 0,
                        rotation: 0,
                        text: a,
                        fontSize: ge.fontSize,
                        fontFamily: ge.fontFamily,
                        color: ge.color,
                        fontWeight: ge.fontWeight,
                        fontStyle: ge.fontStyle,
                        textAlign: ge.textAlign,
                        children: void 0
                    };
                return U("Creating text node with coordinates", {
                    textNodeData: {
                        id: d.id,
                        left: d.left,
                        top: d.top,
                        text: d.text
                    },
                    parentImageId: n,
                    relativeCoordinates: {
                        x: r,
                        y: s
                    }
                }), l.children || (l.children = []), l.children.push(d), e.onNodeChanged(n), g.triggerCanvasUpdate(), h((() => {
                    p(i, !0)
                })), i
            },
            enterTextEditMode: p,
            commitTextEditing: function() {
                if (!a.value || !g) return;
                const t = a.value,
                    o = n.value.trim();
                U("Committing text edit", {
                    textId: t,
                    newText: o,
                    originalText: i.value
                }), "" === o ? g.deleteNode(t) : (g.updateNode(t, {
                    text: o
                }), e.onNodeChanged(t)), m()
            },
            cancelTextEditing: f,
            exitTextEditMode: m,
            isTextSelected: function(e) {
                return !!g && g.selectedNodeId === e
            },
            startTextTransform: function(e, t, a) {
                if (!g) return;
                const n = g.getNodeById(t);
                n && n.type === pe.TEXT && (U("Starting text transform", {
                    mode: e,
                    textId: t
                }), l.value = e, d.value = !0, u.value = a, c.value = n.fontSize)
            },
            updateTextTransform: function(t) {
                if (!(d.value && u.value && g && g.selectedNodeId)) return;
                const a = g.getNodeById(g.selectedNodeId);
                if (a && a.type === pe.TEXT)
                    if ("resize" === l.value) {
                        const a = t.x - u.value.x,
                            n = Math.max(.5, Math.min(3, 1 + a / 100)),
                            o = Math.max(8, Math.min(72, c.value * n));
                        U("Updating text resize", {
                            scaleRatio: n,
                            newFontSize: o
                        });
                        const i = {
                            fontSize: o
                        };
                        g.updateNode(g.selectedNodeId, i), e.onNodeChanged(g.selectedNodeId)
                    } else if ("move" === l.value) {
                    const n = t.x - u.value.x,
                        o = t.y - u.value.y,
                        i = {
                            left: a.left + n,
                            top: a.top + o
                        };
                    g.updateNode(g.selectedNodeId, i), e.onNodeChanged(g.selectedNodeId), u.value = t
                }
            },
            endTextTransform: function() {
                U("Ending text transform", {
                    mode: l.value
                }), l.value = "none", d.value = !1, u.value = null, c.value = 16, g && g.triggerCanvasUpdate()
            },
            updateTextStyle: function(t, a) {
                g && (U("Updating text style", {
                    textId: t,
                    styleUpdates: a
                }), g.updateNode(t, a), e.onNodeChanged(t))
            },
            handleTextNodeClick: function(e) {
                U("Text node clicked", {
                    textId: e
                }), "none" === o.value && g && g.selectNode(e)
            },
            getTextNode: function(e) {
                if (!g) return null;
                const t = g.getNodeById(e);
                return t && t.type === pe.TEXT ? t : null
            },
            viewportToCanvasCoordinates: function(e) {
                if (!g) throw new Error("Text store not initialized with context");
                return {
                    x: (e.x - g.canvasTranslateX) / g.canvasScale,
                    y: (e.y - g.canvasTranslateY) / g.canvasScale
                }
            }
        }
    })),
    je = l("infiniteCanvasImageUpload", (() => {
        const e = Ue(),
            t = r({
                isUploading: !1,
                uploadProgress: 0,
                placeholderImageId: null,
                file: null,
                error: null
            });
        let a = null;
        async function n(t, n) {
            if (!a) return $("Cannot create placeholder image - store context not initialized"), "";
            const o = a.selectedPageNodeId;
            if (!o) return $("Cannot create placeholder image - no image selected"), "";
            const i = a.getNodeById(o);
            if (!i || i.type !== pe.IMAGE && i.type !== pe.FRAME) return $("Cannot create placeholder image - selected node is not an image or frame"), "";
            try {
                const l = await async function(e) {
                        return new Promise(((t, a) => {
                            const n = new Image,
                                o = URL.createObjectURL(e);
                            n.src = o, n.onload = () => {
                                URL.revokeObjectURL(o), t({
                                    width: n.naturalWidth,
                                    height: n.naturalHeight,
                                    aspectRatio: n.naturalWidth / n.naturalHeight
                                })
                            }, n.onerror = () => {
                                URL.revokeObjectURL(o), a(new Error("Failed to load image"))
                            }
                        }))
                    }(t),
                    r = 400;
                let s = l.width,
                    d = l.height;
                if (s > r) {
                    const e = r / s;
                    s = r, d = Math.round(d * e)
                }
                const c = g.uuid(),
                    u = URL.createObjectURL(t),
                    h = i.rawData,
                    v = n.x - h.left,
                    p = n.y - h.top,
                    f = {
                        id: c,
                        type: pe.IMAGE,
                        name: t.name || "Uploaded Image",
                        left: v,
                        top: p,
                        width: s,
                        height: d,
                        rotation: 0,
                        src: u,
                        children: [],
                        extra_info: {
                            meta_info: null,
                            uploading: !0,
                            original_file_name: t.name,
                            file_size: t.size,
                            preview_url: u
                        }
                    };
                return U("Creating placeholder image node with loading state", {
                    imageNodeId: c,
                    fileName: t.name,
                    fileSize: t.size,
                    dimensions: {
                        width: s,
                        height: d
                    },
                    position: {
                        relativeX: v,
                        relativeY: p
                    },
                    parentImageId: o
                }), h.children || (h.children = []), h.children.push(f), e.onNodeChanged(o), a.triggerCanvasUpdate(), c
            } catch (l) {
                return $("Failed to create placeholder image node", l), ""
            }
        }
        const o = s((() => t.value.isUploading)),
            i = s((() => t.value.uploadProgress)),
            l = s((() => !!t.value.error)),
            d = s((() => t.value.error));
        return {
            uploadState: t,
            isUploading: o,
            uploadProgress: i,
            hasError: l,
            errorMessage: d,
            initWithContext: function(e) {
                a ? $("Image upload store already initialized") : (a = e, U("Image upload store initialized with context"))
            },
            startImageUpload: async function(e) {
                if (a) {
                    t.value = {
                        isUploading: !1,
                        uploadProgress: 0,
                        placeholderImageId: null,
                        file: null,
                        error: null
                    };
                    try {
                        const o = await async function() {
                            return new Promise((e => {
                                const t = document.createElement("input");
                                t.type = "file", t.accept = "image/*", t.onchange = t => {
                                    var a;
                                    const n = (null == (a = t.target.files) ? void 0 : a[0]) || null;
                                    e(n)
                                }, t.oncancel = () => {
                                    e(null)
                                }, t.click()
                            }))
                        }();
                        if (!o) return void U("File selection cancelled");
                        U("File selected", {
                            fileName: o.name,
                            fileSize: o.size
                        }), t.value.isUploading = !0, t.value.file = o, t.value.uploadProgress = 20;
                        const i = await n(o, e);
                        if (!i) throw new Error("Failed to create placeholder image");
                        t.value.placeholderImageId = i, t.value.uploadProgress = 40, U("Preview placeholder created, starting background upload", {
                            placeholderImageId: i,
                            fileName: o.name
                        });
                        const l = await async function(e) {
                            try {
                                const t = await W(e, (() => {}));
                                if (!t) throw new Error("Upload returned empty URL");
                                return t
                            } catch (t) {
                                throw $("Failed to upload image to server", t), t
                            }
                        }(o);
                        t.value.uploadProgress = 80, U("Background upload completed", {
                            fileName: o.name,
                            uploadedUrl: l
                        }), await async function(e, t) {
                            var n;
                            if (!a) return void $("Cannot update placeholder - store context not initialized");
                            try {
                                const o = a.getNodeById(e);
                                if (!o) return void $("Placeholder node not found for update", {
                                    placeholderImageId: e
                                });
                                const i = o.rawData;
                                (null == (n = i.extra_info) ? void 0 : n.preview_url) && URL.revokeObjectURL(i.extra_info.preview_url), a.updateNode(e, {
                                    src: t,
                                    extra_info: { ...i.extra_info,
                                        uploading: !1,
                                        upload_completed: !0,
                                        upload_timestamp: Date.now(),
                                        preview_url: void 0
                                    }
                                }), U("Updated placeholder with uploaded image", {
                                    placeholderImageId: e,
                                    uploadedImageUrl: t
                                })
                            } catch (o) {
                                throw $("Failed to update placeholder with uploaded image", o), o
                            }
                        }(i, l), t.value.uploadProgress = 100, t.value.isUploading = !1, U("Image upload process completed", {
                            imageNodeId: i
                        })
                    } catch (o) {
                        $("Image upload error", o), t.value.error = o instanceof Error ? o.message : "Upload failed", t.value.isUploading = !1, t.value.placeholderImageId && a && await async function(e) {
                            var t;
                            if (!a) return;
                            try {
                                const n = a.getNodeById(e);
                                if (n) {
                                    const e = n.rawData;
                                    (null == (t = e.extra_info) ? void 0 : t.preview_url) && URL.revokeObjectURL(e.extra_info.preview_url)
                                }
                                a.deleteNode(e), U("Cleaned up placeholder image", {
                                    placeholderImageId: e
                                })
                            } catch (o) {
                                $("Failed to clean up placeholder image", o)
                            }
                        }(t.value.placeholderImageId)
                    }
                } else $("Cannot start image upload - store context not initialized")
            },
            cancelUpload: function() {
                t.value.placeholderImageId && a && a.deleteNode(t.value.placeholderImageId), t.value = {
                    isUploading: !1,
                    uploadProgress: 0,
                    placeholderImageId: null,
                    file: null,
                    error: null
                }, U("Image upload cancelled")
            }
        }
    })),
    We = l("doodle", (() => {
        const e = Ue(),
            t = r(null),
            a = r(!1),
            n = r(""),
            o = r("#000000"),
            i = r(10);
        let l = null;
        const d = s((() => !!l && l.activeMode === De.DOODLE));
        return {
            mode: s((() => d.value ? "drawing" : "none")),
            activeSvgNodeId: s((() => t.value)),
            isDrawing: s((() => a.value)),
            currentPath: s((() => n.value)),
            currentStroke: s((() => o.value)),
            currentStrokeWidth: s((() => i.value)),
            isInDoodleMode: d,
            initWithContext: function(e) {
                l = e
            },
            startDoodleMode: function(e) {
                if (!l) return void $("Doodle store not initialized with context");
                const a = l.getNodeById(e);
                if (!a) return void $("Page node not found for doodle mode", {
                    pageNodeId: e
                });
                const n = a.rawData,
                    o = `svg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,
                    i = n.width || a.width || 1024,
                    r = n.height || a.height || 1536;
                U("Creating SVG node with dimensions:", {
                    pageWidth: i,
                    pageHeight: r,
                    pageDataWidth: n.width,
                    pageDataHeight: n.height,
                    pageNodeWidth: a.width,
                    pageNodeHeight: a.height
                });
                const s = {
                    id: o,
                    type: pe.SVG,
                    left: 0,
                    top: 0,
                    width: i,
                    height: r,
                    paths: [],
                    rotation: 0
                };
                n.children || (n.children = []), n.children.push(s);
                const d = l.getNodeOfData(s);
                d || $("Failed to build SVG node immediately after creation", {
                    svgNodeId: o
                }), l.triggerCanvasUpdate(), t.value = o, U("Started doodle mode", {
                    pageNodeId: e,
                    svgNodeId: o
                }, d, l.getNodeById(t.value))
            },
            exitDoodleMode: function() {
                if (!l || !t.value) return void(t.value = null);
                const e = l.getNodeById(t.value);
                if (e) {
                    const a = e.rawData;
                    a.paths && 0 !== a.paths.length ? U("Keeping SVG node with paths", {
                        svgNodeId: t.value,
                        pathCount: a.paths.length
                    }) : (U("Removing empty SVG node", {
                        svgNodeId: t.value
                    }), l.deleteNode(t.value))
                }
                t.value = null, a.value = !1, n.value = "", l && l.cleanActiveMode(), U("Exited doodle mode")
            },
            startPath: function(e, t) {
                d.value && (a.value = !0, n.value = `M ${e.toFixed(2)} ${t.toFixed(2)}`, U("Started drawing path", {
                    x: e,
                    y: t
                }))
            },
            addPointToPath: function(e, t) {
                a.value && d.value && (n.value += ` L ${e.toFixed(2)} ${t.toFixed(2)}`)
            },
            finishPath: function() {
                if (!a.value || !l || !t.value) return a.value = !1, void(n.value = "");
                const r = l.getNodeById(t.value);
                if (!r) return $("SVG node not found when finishing path", {
                    svgNodeId: t.value
                }), a.value = !1, void(n.value = "");
                const s = r.rawData,
                    d = `path_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,
                    c = {
                        id: d,
                        d: n.value,
                        stroke: o.value,
                        strokeWidth: i.value,
                        fill: "none"
                    };
                s.paths || (s.paths = []), s.paths.push(c), l.triggerCanvasUpdate(), U("Finished drawing path", {
                    pathId: d,
                    pathData: c
                }), t.value && e.onNodeChanged(t.value), a.value = !1, n.value = ""
            },
            setStrokeColor: function(e) {
                o.value = e
            },
            setStrokeWidth: function(e) {
                i.value = e
            }
        }
    })),
    Xe = l("infiniteCanvasDragNode", (() => {
        const e = r(null),
            t = Ue();
        let a = null;
        const n = s((() => {
                var t;
                return !!(null == (t = e.value) ? void 0 : t.dragging)
            })),
            o = s((() => !!e.value));
        return {
            mouseDownInfo: e,
            onDragMove: function(t) {
                if (!e.value || !a) return;
                e.value.dragging = !0;
                const {
                    node: n,
                    x: o,
                    y: i
                } = e.value, l = n.rawData, r = t.clientX - o, s = t.clientY - i, d = r / a.canvasScale, c = s / a.canvasScale;
                l.left += d, l.top += c, e.value.x = t.clientX, e.value.y = t.clientY, U("onDragMove", {
                    clientX: t.clientX,
                    clientY: t.clientY,
                    deltaX: r,
                    deltaY: s,
                    canvasDeltaX: d,
                    canvasDeltaY: c,
                    newPosition: {
                        left: l.left,
                        top: l.top
                    }
                })
            },
            onDragEnd: function(n) {
                if (!e.value || !a) return;
                const {
                    node: o,
                    isAlreadySelected: i
                } = e.value, l = o.rawData;
                i || (U("onDragEnd: Image should be selected", o.id), a.selectNode(o.id)), a.updateNode(o.id, {
                    left: l.left,
                    top: l.top
                }), U("onDragEnd", {
                    imageId: o.id,
                    finalPosition: {
                        left: l.left,
                        top: l.top
                    },
                    wasAlreadySelected: i
                }), t.onNodeChanged(o.id), e.value = null
            },
            isDragging: n,
            draggable: o,
            showControlPoints: function(e) {
                return !!a && (a.selectedNodeId === e.id && !n.value)
            },
            initWithContext: function(e) {
                a ? $("Drag image store already initialized") : (a = e, U("Drag image store initialized with context"))
            }
        }
    })),
    Ve = l("infiniteCanvasHoverTracking", (() => {
        const e = r([]);
        let t = null;
        const a = s((() => e.value.length > 0 ? e.value[e.value.length - 1] : null)),
            n = s((() => {
                const e = a.value;
                return e && t ? t.getNodeById(e) : null
            }));

        function o() {
            const t = e.value.length > 0;
            e.value = [], t && U("Hover stack cleared")
        }
        const i = s((() => [...e.value]));
        return {
            hoverStack: s((() => [...e.value])),
            topHoveredNodeId: a,
            topHoveredNode: n,
            hoveredNodeIds: i,
            initWithContext: function(e) {
                t ? H("Hover tracking store already initialized") : (t = e, U("Hover tracking store initialized with context"))
            },
            handleMouseEnter: function(t) {
                t ? (e.value.includes(t) && (e.value = e.value.filter((e => e !== t))), e.value.push(t), U("Node entered hover stack", {
                    nodeId: t,
                    stackDepth: e.value.length,
                    topNode: a.value
                })) : H("handleMouseEnter called with empty nodeId")
            },
            handleMouseLeave: function(t) {
                if (!t) return void H("handleMouseLeave called with empty nodeId");
                const n = e.value.length;
                e.value = e.value.filter((e => e !== t)), e.value.length < n ? U("Node left hover stack", {
                    nodeId: t,
                    stackDepth: e.value.length,
                    topNode: a.value
                }) : H("handleMouseLeave called but node was not in hover stack", {
                    nodeId: t
                })
            },
            clearHoverStack: o,
            getNodeAtDepth: function(t) {
                if (t < 0) {
                    const a = e.value.length + t;
                    return a >= 0 ? e.value[a] : null
                }
                return t < e.value.length ? e.value[t] : null
            },
            isNodeHovered: function(t) {
                return e.value.includes(t)
            },
            getNodeHoverDepth: function(t) {
                const a = e.value.indexOf(t);
                return a >= 0 ? a + 1 : 0
            },
            validateAndCleanStack: function() {
                if (!t) return;
                const a = e.value.filter((e => !!t.getNodeById(e) || (H("Removing invalid node from hover stack", {
                    nodeId: e
                }), !1)));
                a.length !== e.value.length && (e.value = a, U("Cleaned invalid nodes from hover stack", {
                    newStackDepth: e.value.length
                }))
            },
            handleCanvasInteraction: function() {
                e.value.length > 0 && (U("Clearing hover stack due to canvas interaction"), o())
            },
            handleNodeDeletion: function(t) {
                e.value.includes(t) && (e.value = e.value.filter((e => e !== t)), U("Removed deleted node from hover stack", {
                    nodeId: t,
                    newStackDepth: e.value.length
                }))
            }
        }
    }));

function Ge(e, t) {
    let a = null,
        n = 0;
    return (...o) => {
        const i = Date.now();
        i - n > t ? (n = i, e(...o)) : (a && clearTimeout(a), a = setTimeout((() => {
            n = Date.now(), e(...o)
        }), t - (i - n)))
    }
}
const Ye = l("infiniteCanvas", (() => {
        const e = new Map,
            t = r(null);

        function a(t) {
            let n;
            switch (t.type) {
                case pe.PROJECT:
                    n = new le(t);
                    break;
                case pe.FRAME:
                    n = new se(t);
                    break;
                case pe.HTML_FRAGMENT:
                    n = new de(t);
                    break;
                case pe.IMAGE:
                    n = new ue(t);
                    break;
                case pe.TEXT:
                    n = new he(t);
                    break;
                case pe.SVG:
                    n = new ve(t);
                    break;
                default:
                    throw new Error(`Unknown node type: ${t.type}`)
            }
            if (e.has(n.id)) return $("buildSubtree called but nodeMap already has node", n.id, n.type, n.rawData), e.get(n.id);
            if (e.set(n.id, n), t.children)
                for (const e of t.children) a(e);
            return n
        }

        function n(t) {
            const n = e.get(t.id);
            return n || (U("getNodeOfData: build new node for", t.id), a(t))
        }

        function o(a) {
            if (!t.value) return $("calculateParentId called but rootNode is null"), null;
            if (a === t.value.id) return null;
            for (const [t, n] of e) {
                const e = n.rawData;
                if (e.children)
                    for (const t of e.children)
                        if (t.id === a) return n.id
            }
            return null
        }

        function i(e) {
            if (!t.value) return $("findParentPageId called but rootNode is null"), null;
            if (o(e) === t.value.id) return e;
            let a = e;
            for (; a;) {
                const e = o(a);
                if (!e) break;
                if (e === t.value.id) return a;
                a = e
            }
            return null
        }

        function l() {
            e.clear(), t.value = null, ae.clearHoverStack()
        }

        function d(t) {
            return Array.from(e.values()).filter((e => e.type === t))
        }

        function c(t) {
            return e.get(t) || null
        }

        function g(e, a) {
            const n = c(e);
            if (!n) return;
            const o = n.rawData;
            Object.keys(a).forEach((e => {
                o[e] = a[e]
            })), v(t)
        }

        function f(a) {
            if (!t.value) return void $("deleteNodeById called but rootNode is null");
            const n = function e(t) {
                for (let n = 0; n < t.length; n++) {
                    if (t[n].id === a) return t.splice(n, 1), !0;
                    if (t[n].children && e(t[n].children || [])) return !0
                }
                return !1
            }(t.value.rawData.children || []);
            n ? (e.delete(a), ae.handleNodeDeletion(a), v(t), U("deleteNodeById: Deleted node", {
                nodeId: a
            })) : $("deleteNodeById: Node not found in tree", {
                nodeId: a
            })
        }

        function m(e) {
            t.value ? (t.value.rawData.children || (t.value.rawData.children = []), t.value.rawData.children.push(e)) : $("addNodeToCanvas called but rootNode is null")
        }
        const w = r(.3),
            y = r(200),
            x = r(100);
        h((() => {}));
        const I = Ge((() => {
            try {
                const e = {
                    scale: w.value,
                    translateX: y.value,
                    translateY: x.value
                };
                localStorage.setItem("infiniteCanvas.transform.1", JSON.stringify(e))
            } catch (e) {
                $("Failed to save canvas transform to localStorage:", e)
            }
        }), 500);
        u((() => {
            w.value, y.value, x.value, I()
        }));
        const k = Re(),
            C = {
                get canvasScale() {
                    return w.value
                },
                get canvasTranslateX() {
                    return y.value
                },
                get canvasTranslateY() {
                    return x.value
                },
                getNodeById: c,
                getNodeOfData: n,
                cleanActiveMode: () => T.value = De.NONE,
                get currentToolId() {
                    return D.value
                },
                get activeMode() {
                    return T.value
                },
                calculateParentId: o,
                findParentPageId: i,
                get selectedPageImageId() {
                    return B.value
                },
                get selectedPageNodeId() {
                    return z.value
                },
                get selectedPageNodeType() {
                    return O.value
                },
                get selectedNodeId() {
                    return j.value
                },
                createNode: m,
                updateNode: g,
                deleteNode: f,
                selectNode: Z,
                triggerCanvasUpdate: () => v(t)
            };
        k.initWithContext(C);
        const b = $e();
        b.initWithContext(C);
        He().initWithContext(C);
        je().initWithContext(C);
        const E = We();
        E.initWithContext(C);
        const M = r([]),
            S = s((() => !(N.value || !z.value && !W.value || T.value === De.DOODLE))),
            _ = r({
                x: 0,
                y: 0
            }),
            N = r(!1),
            T = r(De.NONE),
            D = r(null),
            P = r(!1),
            F = r(!1),
            L = s((() => T.value === De.TOOL && D.value === _e.RECTANGLE_SELECT)),
            A = s((() => T.value === De.TOOL && D.value === _e.BRUSH_SELECT)),
            R = r(null),
            B = s((() => {
                if (1 != M.value.length) return null;
                const e = c(M.value[0]);
                return (null == e ? void 0 : e.type) !== pe.IMAGE ? null : e.isPageNode ? e.id : (U("!(node as ImageNode).isPageNode"), null)
            })),
            z = s((() => {
                var e;
                if (1 != M.value.length) return null;
                const a = c(M.value[0]);
                if (!a) return null;
                if (a.type === pe.IMAGE) return a.isPageNode ? a.id : null;
                if (a.type === pe.FRAME) {
                    return o(a.id) === (null == (e = t.value) ? void 0 : e.id) ? a.id : null
                }
                return null
            })),
            O = s((() => {
                if (!z.value) return null;
                const e = c(z.value);
                return (null == e ? void 0 : e.type) || null
            })),
            j = s((() => M.value[0] || null)),
            W = s((() => {
                if (!j.value) return null;
                const e = j.value,
                    t = c(e);
                if (!t) return null;
                if (z.value === e) return null;
                if (t.type === pe.IMAGE || t.type === pe.TEXT) {
                    const t = i(e);
                    if (t && t !== e) return e
                }
                return null
            })),
            X = s((() => W.value ? i(W.value) : null)),
            V = s((() => {
                const e = function(e) {
                    if (e) return [...Ne, ...Te].find((t => t.id === e))
                }(D.value);
                return T.value === De.FORM_EDIT || T.value === De.TOOL && (null == e ? void 0 : e.showEditor)
            })),
            G = s((() => {
                if (!j.value) return !1;
                const e = c(j.value);
                return (null == e ? void 0 : e.type) === pe.TEXT
            }));

        function Y() {
            T.value === De.FORM_EDIT && (R.value = null, T.value = De.NONE, k.clearSelection()), T.value === De.TOOL && (D.value = null)
        }

        function Z(e) {
            if (M.value.includes(e)) return;
            M.value = [e], T.value = De.NONE, Y(), D.value = null;
            const t = document.querySelector(".j-search-input");
            t && t.blur()
        }

        function q(e, t) {
            _.value = {
                x: e,
                y: t
            }
        }

        function J(e) {
            D.value = e
        }

        function K(e) {
            if (T.value = e, e === De.FORM_EDIT || e === De.TOOL);
            else {
                R.value = null;
                const e = D.value === _e.RECTANGLE_SELECT;
                D.value = null, e && U("Auto edit disabled when exiting TOOL mode from RECTANGLE_SELECT")
            }
        }

        function Q() {
            N.value = !0
        }

        function ee() {
            R.value = null, M.value = [], N.value = !1, T.value = De.NONE
        }
        p(D, (e => {
            U("currentToolId changed", e), e !== _e.RECTANGLE_SELECT && k.clearSelection()
        })), s((() => k.selection)), s((() => k.isSelecting)), s((() => k.interactionMode)), s((() => k.activeResizeHandle)), s((() => b.isCutting)), s((() => b.fragment)), s((() => b.modifiedImageData)), s((() => b.sourceImageNodeId)), s((() => b.fragments)), s((() => b.activeFragmentIndex));
        const te = Xe();
        te.initWithContext(C);
        const ae = Ve();
        ae.initWithContext(C);
        const oe = r(!1),
            ie = r(!1),
            re = s((() => te.isDragging)),
            ce = {
                rootNode: s((() => t.value)),
                initializeTree: function(n) {
                    e.size > 0 && H("initializeTree called but nodeMap is not empty"), t.value && H("initializeTree called but rootNode is not null"), l();
                    const o = a(n);
                    if (o.type !== pe.PROJECT) throw new Error("initializeTree called but rootNode is not a project node");
                    t.value = o
                },
                clearTree: l,
                getNodeOfData: n,
                getNodeById: c,
                getNodesByType: d,
                getSelectNode: function(e, t) {
                    const a = ae.topHoveredNode;
                    return a || null
                },
                calculateParentId: o,
                findParentPageId: i,
                getNodesByTaskId: function(e) {
                    const t = [];
                    return d(pe.IMAGE).forEach((a => {
                        var n, o;
                        (null == (o = null == (n = a.rawData.extra_info) ? void 0 : n.task_status) ? void 0 : o.id) === e && t.push(a)
                    })), t
                },
                updateNodeData: g,
                addNodeToCanvas: m,
                canvasScale: s((() => w.value)),
                canvasTranslateX: s((() => y.value)),
                canvasTranslateY: s((() => x.value)),
                setCanvasTransform: function(e, t, a) {
                    void 0 !== e && (w.value = Math.max(Pe, Math.min(Fe, e))), void 0 !== t && (y.value = t), void 0 !== a && (x.value = a)
                },
                panCanvas: function(e, t) {
                    Q(), ae.handleCanvasInteraction(), y.value += e, x.value += t
                },
                zoomCanvasAtPoint: function(e, t, a) {
                    Q(), ae.handleCanvasInteraction();
                    const n = Math.max(Pe, Math.min(Fe, e)),
                        o = (t - y.value) / w.value,
                        i = (a - x.value) / w.value;
                    y.value = t - o * n, x.value = a - i * n, w.value = n
                },
                resetCanvasTransform: function() {
                    w.value = 1, y.value = 0, x.value = 0
                },
                selection: s((() => k.selection)),
                isSelecting: s((() => k.isSelecting)),
                interactionMode: s((() => k.interactionMode)),
                activeResizeHandle: s((() => k.activeResizeHandle)),
                handleMaskSelectionMouseDown: e => k.handleMaskSelectionMouseDown(e),
                handleMaskSelectionMouseMove: e => k.handleMaskSelectionMouseMove(e),
                handleMaskSelectionMouseUp: () => k.handleMaskSelectionMouseUp(),
                handleMaskSelectionMouseLeave: () => k.handleMaskSelectionMouseLeave(),
                isCutting: s((() => b.isCutting)),
                cuttingFragment: s((() => b.fragment)),
                modifiedImageData: s((() => b.modifiedImageData)),
                sourceImageNodeId: s((() => b.sourceImageNodeId)),
                cuttingFragments: s((() => b.fragments)),
                activeFragmentIndex: s((() => b.activeFragmentIndex)),
                processModify: (e, t) => b.processModify(e, t),
                startCutting: (e, t) => b.startCutting(e, t),
                startReplacing: e => b.startReplacing(e),
                applyCutting: () => b.applyCutting(),
                cancelCutting: () => b.cancelCutting(),
                setActiveFragment: e => b.setActiveFragment(e),
                addReplaceFragment: (e, t) => b.addReplaceFragment(e, t),
                startFragmentDrag: e => b.startFragmentDrag(e),
                updateFragmentDrag: e => b.updateFragmentDrag(e),
                endFragmentDrag: () => b.endFragmentDrag(),
                updateFragmentDimensions: (e, t, a, n) => b.updateFragmentDimensions(e, t, a, n),
                getModifiedImageData: e => b.getModifiedImageData(e),
                selectedImageMeta: s((() => R.value)),
                selectedPageImageId: B,
                selectedPageNodeId: z,
                selectedPageNodeType: O,
                selectedNodeId: j,
                selectedChildNodeId: W,
                selectedChildNodeParentPageId: X,
                showMetaEditor: s((() => V.value)),
                setSelectedImageMeta: function(e, t) {
                    R.value = e, t && (M.value = [t]), e && (T.value = De.FORM_EDIT, D.value = null)
                },
                closeEditorPanel: Y,
                showTextEditor: s((() => G.value)),
                showFloatingMenu: s((() => S.value)),
                floatingMenuPosition: s((() => _.value)),
                activeMode: s((() => T.value)),
                currentToolId: s((() => D.value)),
                updateToolId: J,
                cleanActiveMode: () => T.value = De.NONE,
                isLeftMouseDragMode: P,
                isLeftMouseDragging: F,
                isMaskSelectionMode: s((() => L.value)),
                isBrushMode: s((() => A.value)),
                selectNode: Z,
                setFloatingMenuPosition: q,
                calculateFloatingMenuPosition: function() {
                    let e = z.value;
                    if (!e && W.value && (e = X.value), !e) return;
                    const t = c(e);
                    if (!t || t.type !== pe.IMAGE && t.type !== pe.FRAME) return;
                    const a = t.rawData,
                        n = a.rotation || 0,
                        o = a.left + a.width / 2,
                        i = a.top + a.height / 2;
                    if (0 === n) {
                        const e = a.left * w.value + y.value,
                            t = a.top * w.value + x.value;
                        q(e + a.width * w.value / 2, t + a.height * w.value + 10)
                    } else {
                        const e = a.width / 2,
                            t = a.height / 2,
                            l = n * Math.PI / 180,
                            r = [{
                                x: -e,
                                y: -t
                            }, {
                                x: e,
                                y: -t
                            }, {
                                x: e,
                                y: t
                            }, {
                                x: -e,
                                y: t
                            }].map((e => ({
                                x: o + e.x * Math.cos(l) - e.y * Math.sin(l),
                                y: i + e.x * Math.sin(l) + e.y * Math.cos(l)
                            }))),
                            s = Math.max(...r.map((e => e.y)));
                        q((Math.min(...r.map((e => e.x))) + Math.max(...r.map((e => e.x)))) / 2 * w.value + y.value, s * w.value + x.value + 10)
                    }
                },
                setActiveMode: K,
                setSelectionMode: function(e) {
                    e ? (K(De.TOOL), J(_e.OBJECT_SELECT)) : J(null)
                },
                setRectangleSelectMode: function(e) {
                    e ? (K(De.TOOL), J(_e.RECTANGLE_SELECT)) : J(null)
                },
                setBrushMode: function(e) {
                    e ? (K(De.TOOL), J(_e.BRUSH_SELECT)) : J(null)
                },
                setEditMode: function(e) {
                    K(e ? De.FORM_EDIT : De.NONE)
                },
                hideFloatingMenuDuringTransform: Q,
                showFloatingMenuAfterTransform: function() {
                    N.value && (N.value = !1)
                },
                clearNodeSelection: ee,
                deleteChildNode: function(e) {
                    const t = i(e);
                    if (!t) return void $("deleteChildNode: Cannot find parent page for child node", {
                        childNodeId: e
                    });
                    U("Deleting child node from parent page", {
                        childNodeId: e,
                        parentPageId: t
                    }), f(e), M.value.includes(e) && ee(), Ue().onNodeChanged(t), U("Child node deleted and sync triggered", {
                        childNodeId: e,
                        parentPageId: t
                    })
                },
                generateAndUploadMask: async function() {
                    return await k.generateAndUploadMask()
                },
                isResizingImage: oe,
                isRotatingImage: ie,
                isDraggingImage: re,
                isOperatingImage: s((() => oe.value || ie.value || re.value)),
                topmostHoveredNodeId: s((() => ae.topHoveredNodeId)),
                topmostHoveredNode: s((() => ae.topHoveredNode)),
                allHoveredNodeIds: s((() => ae.hoveredNodeIds)),
                isNodeHovered: e => ae.isNodeHovered(e),
                getNodeHoverDepth: e => ae.getNodeHoverDepth(e),
                clearHoverStack: () => ae.clearHoverStack(),
                doodleMode: s((() => E.mode)),
                activeSvgNodeId: s((() => E.activeSvgNodeId)),
                isInDoodleMode: s((() => E.isInDoodleMode)),
                isDrawing: s((() => E.isDrawing)),
                startDoodleMode: e => E.startDoodleMode(e),
                exitDoodleMode: () => E.exitDoodleMode(),
                doodleStartPath: (e, t) => E.startPath(e, t),
                doodleAddPointToPath: (e, t) => E.addPointToPath(e, t),
                doodleFinishPath: () => E.finishPath(),
                setDoodleStrokeColor: e => E.setStrokeColor(e),
                setDoodleStrokeWidth: e => E.setStrokeWidth(e)
            };
        return ne = () => ce, ce
    })),
    Ze = f({
        __name: "ProjectNodeComponent",
        props: {
            nodeData: {},
            isPageNode: {
                type: Boolean
            }
        },
        setup(e) {
            const t = e,
                o = Ye(),
                i = s((() => o.getNodeOfData(t.nodeData)));
            return (e, t) => {
                var o;
                return n(), a("div", null, [(n(!0), a(m, null, w((null == (o = y(i)) ? void 0 : o.childrenData) || [], (e => (n(), x(kt, {
                    key: e.id,
                    "node-data": e,
                    "is-page-node": !0
                }, null, 8, ["node-data"])))), 128))])
            }
        }
    }),
    qe = ["data-node-id"],
    Je = {
        class: "page-title-text"
    },
    Ke = ["onMousedown"],
    Qe = S(f({
        __name: "PositionableWrapper",
        props: {
            nodeData: {},
            isPageNode: {
                type: Boolean
            }
        },
        setup(e) {
            const t = e,
                i = Ye(),
                l = s((() => i.getNodeOfData(t.nodeData))),
                d = Xe(),
                c = Ve(),
                u = r(!1);

            function g() {
                u.value = !0, c.handleMouseEnter(l.value.id)
            }

            function h() {
                u.value = !1, c.handleMouseLeave(l.value.id)
            }
            I((() => {
                u.value && c.handleMouseLeave(l.value.id)
            }));
            const v = r(null),
                p = r(null),
                f = s((() => i.selectedNodeId === l.value.id));
            t.isPageNode && k((() => {
                const e = document.querySelector(".j-viewport");
                if (!e) return;
                const t = e.getBoundingClientRect().height,
                    a = i.canvasScale,
                    n = t / 2,
                    o = l.value.width / 2 * a - l.value.left * a,
                    r = n - (l.value.top + l.value.height / 2) * a;
                i.setCanvasTransform(void 0, o, r)
            }));
            const y = s((() => ({
                    position: "absolute",
                    left: `${l.value.left}px`,
                    top: `${l.value.top}px`
                }))),
                x = s((() => {
                    const e = {};
                    l.value.width && l.value.width > 0 && (e.width = `${l.value.width}px`), l.value.height && l.value.height > 0 && (e.height = `${l.value.height}px`), U("contentStyle. after size:", e, l.value, l.value.height, l.value.width);
                    let a = f.value || u.value && !i.isLeftMouseDragMode;
                    if (U("isEmptyText", _.value, l.value.type === pe.TEXT && !l.value.text, l.value, l.value.type, l.value.type === pe.TEXT, l.value.text, !l.value.text), _.value && (a = !1), a) {
                        const t = 2 / i.canvasScale;
                        let a = "#0F7FFF";
                        e.boxShadow = `0 0 0 ${t}px ${a}`, e.transition = "box-shadow 0.2s ease"
                    }
                    const n = t.nodeData.rotation || 0;
                    return 0 !== n && (e.transform = `rotate(${n}deg)`, e.transformOrigin = "center"), e
                })),
                S = s((() => {
                    const e = i.canvasScale,
                        t = 1 / e;
                    return {
                        transform: `scale(${t})`,
                        transformOrigin: "bottom left",
                        bottom: `calc(100% + ${-1*t}px)`,
                        left: "0",
                        maxWidth: l.value.width && l.value.width > 0 ? l.value.width * e + "px" : "100%"
                    }
                })),
                _ = s((() => l.value.type === pe.TEXT && !l.value.text)),
                N = s((() => {
                    if (!d.showControlPoints(l.value)) return [];
                    if (_.value) return [];
                    if (l.value.type !== pe.IMAGE) return [];
                    const e = i.canvasScale,
                        t = 14 / e,
                        a = 2 / e;
                    return [{
                        id: "top-left",
                        x: 0,
                        y: 0,
                        cursor: "nwse-resize"
                    }, {
                        id: "top-right",
                        x: l.value.width,
                        y: 0,
                        cursor: "nesw-resize"
                    }, {
                        id: "bottom-right",
                        x: l.value.width,
                        y: l.value.height,
                        cursor: "nwse-resize"
                    }, {
                        id: "bottom-left",
                        x: 0,
                        y: l.value.height,
                        cursor: "nesw-resize"
                    }].map((e => ({ ...e,
                        style: {
                            position: "absolute",
                            left: e.x - t / 2 + "px",
                            top: e.y - t / 2 + "px",
                            width: `${t}px`,
                            height: `${t}px`,
                            backgroundColor: "#FFFFFF",
                            border: `${a}px solid #0F7FFF`,
                            borderRadius: t / 2 + "px",
                            cursor: e.cursor,
                            pointerEvents: "auto",
                            zIndex: "10"
                        }
                    })))
                })),
                T = s((() => {
                    var e;
                    if (!d.showControlPoints(l.value)) return null;
                    if (_.value) return null;
                    const t = i.canvasScale,
                        a = 14 / t,
                        n = 2 / t,
                        o = 30 / t,
                        r = l.value.width / 2,
                        s = -o;
                    return {
                        style: {
                            position: "absolute",
                            left: r - a / 2 + "px",
                            top: s - a / 2 + "px",
                            width: `${a}px`,
                            height: `${a}px`,
                            backgroundColor: "#FFFFFF",
                            border: `${n}px solid #0F7FFF`,
                            borderRadius: a / 2 + "px",
                            cursor: (null == (e = p.value) ? void 0 : e.isRotating) ? "grabbing" : "grab",
                            pointerEvents: "auto",
                            zIndex: "10"
                        },
                        lineStyle: {
                            position: "absolute",
                            left: r - n / 2 + "px",
                            top: `${s}px`,
                            width: `${n}px`,
                            height: `${o}px`,
                            backgroundColor: "#0F7FFF",
                            pointerEvents: "none",
                            zIndex: "9"
                        }
                    }
                }));

            function D(e) {
                if (!v.value) return;
                e.preventDefault(), e.stopPropagation();
                const a = e.clientX - v.value.startX,
                    n = e.clientY - v.value.startY,
                    o = a / i.canvasScale,
                    l = n / i.canvasScale;
                let r, s;
                switch (v.value.corner) {
                    case "top-left":
                        r = v.value.startLeft, s = v.value.startTop;
                        break;
                    case "top-right":
                        r = v.value.startLeft + v.value.startWidth, s = v.value.startTop;
                        break;
                    case "bottom-right":
                        r = v.value.startLeft + v.value.startWidth, s = v.value.startTop + v.value.startHeight;
                        break;
                    case "bottom-left":
                        r = v.value.startLeft, s = v.value.startTop + v.value.startHeight
                }
                const d = r + o,
                    c = s + l;
                let u;
                const g = v.value.startLeft + v.value.startWidth,
                    h = v.value.startTop + v.value.startHeight;
                switch (v.value.corner) {
                    case "top-left":
                        u = A({
                            x: d,
                            y: c
                        }, {
                            x: g,
                            y: h
                        }, {
                            x: v.value.startLeft,
                            y: v.value.startTop
                        });
                        break;
                    case "top-right":
                        u = A({
                            x: d,
                            y: c
                        }, {
                            x: v.value.startLeft,
                            y: h
                        }, {
                            x: g,
                            y: v.value.startTop
                        });
                        break;
                    case "bottom-right":
                        u = A({
                            x: d,
                            y: c
                        }, {
                            x: v.value.startLeft,
                            y: v.value.startTop
                        }, {
                            x: g,
                            y: h
                        });
                        break;
                    case "bottom-left":
                        u = A({
                            x: d,
                            y: c
                        }, {
                            x: g,
                            y: v.value.startTop
                        }, {
                            x: v.value.startLeft,
                            y: h
                        })
                }
                const p = Math.sqrt(v.value.startWidth * v.value.startWidth + v.value.startHeight * v.value.startHeight);
                p * u < 32 && (u = 32 / p);
                const f = v.value.startWidth * u,
                    m = v.value.startHeight * u;
                switch (t.nodeData.width = f, t.nodeData.height = m, v.value.corner) {
                    case "top-left":
                        t.nodeData.left = g - f, t.nodeData.top = h - m;
                        break;
                    case "top-right":
                        t.nodeData.left = v.value.startLeft, t.nodeData.top = h - m;
                        break;
                    case "bottom-right":
                        t.nodeData.left = v.value.startLeft, t.nodeData.top = v.value.startTop;
                        break;
                    case "bottom-left":
                        t.nodeData.left = g - f, t.nodeData.top = v.value.startTop
                }
            }

            function P(e) {
                if (!v.value) return;
                e.preventDefault(), e.stopPropagation();
                Ue().onNodeChanged(l.value.id), v.value = null, document.removeEventListener("mousemove", D), document.removeEventListener("mouseup", P), i.isResizingImage = !1, i.calculateFloatingMenuPosition()
            }

            function F(e) {
                if (!p.value) return;
                e.preventDefault(), e.stopPropagation();
                const a = document.querySelector(".j-viewport");
                if (!a) return;
                const n = a.getBoundingClientRect(),
                    o = e.clientX - n.left,
                    l = e.clientY - n.top,
                    r = {
                        x: (o - i.canvasTranslateX) / i.canvasScale,
                        y: (l - i.canvasTranslateY) / i.canvasScale
                    },
                    s = function(e, t, a) {
                        const n = {
                                x: t.x - e.x,
                                y: t.y - e.y
                            },
                            o = {
                                x: a.x - e.x,
                                y: a.y - e.y
                            },
                            i = Math.atan2(n.y, n.x);
                        return (Math.atan2(o.y, o.x) - i) / Math.PI * 180
                    }({
                        x: p.value.centerX,
                        y: p.value.centerY
                    }, {
                        x: p.value.startX,
                        y: p.value.startY
                    }, {
                        x: r.x,
                        y: r.y
                    });
                location.hostname;
                const d = function(e) {
                    for (; e < -180;) e += 360;
                    for (; e > 180;) e -= 360;
                    return e
                }(p.value.startRotation + s);
                t.nodeData.rotation = d
            }

            function L(e) {
                if (!p.value) return;
                e.preventDefault(), e.stopPropagation();
                Ue().onNodeChanged(l.value.id), p.value = null, document.removeEventListener("mousemove", F), document.removeEventListener("mouseup", L), i.isRotatingImage = !1, i.calculateFloatingMenuPosition()
            }

            function A(e, t, a) {
                const n = a.x - t.x,
                    o = a.y - t.y,
                    i = e.x - t.x,
                    l = e.y - t.y,
                    r = n * n + o * o;
                if (0 === r) return 1;
                return (i * n + l * o) / r
            }
            return (e, r) => (n(), a("div", {
                style: C(y.value),
                "data-node-id": l.value.id
            }, [o("div", {
                style: C(x.value),
                class: "content-wrapper",
                onMouseenter: g,
                onMouseleave: h
            }, [e.isPageNode ? (n(), a("div", {
                key: 0,
                class: "page-title",
                style: C(S.value)
            }, [o("span", Je, M(l.value.name), 1)], 4)) : b("", !0), E(e.$slots, "default", {}, void 0, !0), (n(!0), a(m, null, w(N.value, (e => (n(), a("div", {
                key: e.id,
                style: C(e.style),
                class: "resize-handle",
                onMousedown: a => {
                    return n = a, o = e.id, void(0 === n.button && (n.preventDefault(), n.stopPropagation(), v.value = {
                        isResizing: !0,
                        startX: n.clientX,
                        startY: n.clientY,
                        startWidth: t.nodeData.width || 0,
                        startHeight: t.nodeData.height || 0,
                        startLeft: t.nodeData.left,
                        startTop: t.nodeData.top,
                        corner: o
                    }, document.addEventListener("mousemove", D), document.addEventListener("mouseup", P), i.isResizingImage = !0));
                    var n, o
                }
            }, null, 44, Ke)))), 128)), T.value ? (n(), a(m, {
                key: 1
            }, [o("div", {
                style: C(T.value.lineStyle),
                class: "rotation-line"
            }, null, 4), o("div", {
                style: C(T.value.style),
                class: "rotation-handle",
                onMousedown: r[0] || (r[0] = e => function(e) {
                    if (0 !== e.button) return;
                    e.preventDefault(), e.stopPropagation();
                    const a = t.nodeData.left + (t.nodeData.width || 0) / 2,
                        n = t.nodeData.top + (t.nodeData.height || 0) / 2,
                        o = document.querySelector(".j-viewport");
                    if (!o) return;
                    const l = o.getBoundingClientRect(),
                        r = e.clientX - l.left,
                        s = e.clientY - l.top,
                        d = {
                            x: (r - i.canvasTranslateX) / i.canvasScale,
                            y: (s - i.canvasTranslateY) / i.canvasScale
                        };
                    p.value = {
                        isRotating: !0,
                        startX: d.x,
                        startY: d.y,
                        startRotation: t.nodeData.rotation || 0,
                        centerX: a,
                        centerY: n,
                        startControlPointX: 0,
                        startControlPointY: 0
                    }, document.addEventListener("mousemove", F), document.addEventListener("mouseup", L), i.isRotatingImage = !0
                }(e))
            }, null, 36)], 64)) : b("", !0)], 36)], 12, qe))
        }
    }), [
        ["__scopeId", "data-v-736324de"]
    ]),
    et = {
        class: "frame-node-content"
    },
    tt = S(f({
        __name: "FrameNodeComponent",
        props: {
            nodeData: {},
            isPageNode: {
                type: Boolean
            }
        },
        setup(e) {
            const t = e,
                i = Ye(),
                l = s((() => i.getNodeOfData(t.nodeData)));
            return (e, t) => (n(), x(Qe, {
                "node-data": e.nodeData,
                "is-page-node": e.isPageNode
            }, {
                default: _((() => [o("div", {
                    class: N(["frame-node", {
                        "is-page-node": e.isPageNode
                    }]),
                    style: C({
                        backgroundColor: y(l).backgroundColor
                    })
                }, [o("div", et, [(n(!0), a(m, null, w(y(l).childrenData, (e => (n(), x(kt, {
                    key: e.id,
                    "node-data": e
                }, null, 8, ["node-data"])))), 128))])], 6)])),
                _: 1
            }, 8, ["node-data", "is-page-node"]))
        }
    }), [
        ["__scopeId", "data-v-fd541656"]
    ]),
    at = {
        class: "html-fragment-node"
    },
    nt = ["innerHTML"],
    ot = S(f({
        __name: "HtmlFragmentNodeComponent",
        props: {
            nodeData: {},
            isPageNode: {
                type: Boolean
            }
        },
        setup(e) {
            const t = e,
                a = Ye(),
                i = s((() => a.getNodeOfData(t.nodeData)));
            return (e, t) => (n(), x(Qe, {
                "node-data": e.nodeData,
                "is-page-node": e.isPageNode
            }, {
                default: _((() => [o("div", at, [o("div", {
                    class: "html-content",
                    innerHTML: y(i).html
                }, null, 8, nt)])])),
                _: 1
            }, 8, ["node-data", "is-page-node"]))
        }
    }), [
        ["__scopeId", "data-v-86daf7e4"]
    ]),
    it = {
        class: "image-node"
    },
    lt = {
        key: 0,
        class: "image-container uploading"
    },
    rt = ["src", "alt"],
    st = {
        key: 1,
        class: "image-container"
    },
    dt = ["src", "alt"],
    ct = {
        key: 2,
        class: "loading-container"
    },
    ut = {
        class: "loading-text"
    },
    gt = {
        key: 0,
        class: "progress"
    },
    ht = {
        key: 3,
        class: "placeholder"
    },
    vt = {
        key: 0,
        class: "status"
    },
    pt = S(f({
        __name: "ImageNodeComponent",
        props: {
            nodeData: {},
            isPageNode: {
                type: Boolean
            }
        },
        setup(e) {
            const t = e,
                i = Ye(),
                l = s((() => i.getNodeOfData(t.nodeData))),
                r = s((() => i.getModifiedImageData(l.value.id) || t.nodeData.src || "")),
                d = s((() => {
                    var e;
                    return null == (e = t.nodeData.extra_info) ? void 0 : e.uploading
                })),
                c = s((() => l.value.generating && !d.value)),
                u = s((() => {
                    var e;
                    return (null == (e = t.nodeData.extra_info) ? void 0 : e.task_status) && t.nodeData.extra_info.task_status.progress || 0
                })),
                g = s((() => {
                    var e;
                    return (null == (e = t.nodeData.extra_info) ? void 0 : e.task_status) && t.nodeData.extra_info.task_status.status || ""
                })),
                h = e => {
                    e.target.src = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="200" height="200"%3E%3Crect width="200" height="200" fill="%23f0f0f0"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="%23999"%3EImage Error%3C/text%3E%3C/svg%3E'
                };
            return (e, t) => (n(), x(Qe, {
                "node-data": e.nodeData,
                "is-page-node": e.isPageNode
            }, {
                default: _((() => [o("div", it, [r.value && d.value ? (n(), a("div", lt, [o("img", {
                    src: r.value,
                    alt: l.value.name,
                    class: "image-content",
                    draggable: "false",
                    onError: h
                }, null, 40, rt)])) : r.value && !c.value ? (n(), a("div", st, [o("img", {
                    src: r.value,
                    alt: l.value.name,
                    class: "image-content",
                    draggable: "false",
                    onError: h
                }, null, 40, dt)])) : c.value ? (n(), a("div", ct, [t[1] || (t[1] = o("div", {
                    class: "loading-animation"
                }, [o("div", {
                    class: "gradient-layer gradient-layer-1"
                }), o("div", {
                    class: "gradient-layer gradient-layer-2"
                }), o("div", {
                    class: "gradient-layer gradient-layer-3"
                }), o("div", {
                    class: "gradient-layer gradient-layer-4"
                })], -1)), o("div", ut, [t[0] || (t[0] = o("div", null, "Generating...", -1)), u.value > 0 ? (n(), a("div", gt, M(Math.round(100 * u.value)) + "% ", 1)) : b("", !0)])])) : (n(), a("div", ht, [t[2] || (t[2] = o("svg", {
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "120",
                    height: "120",
                    viewBox: "0 0 24 24",
                    fill: "none"
                }, [o("path", {
                    stroke: "currentColor",
                    "stroke-linecap": "round",
                    "stroke-linejoin": "round",
                    "stroke-width": "1.5",
                    d: "M21 16V8a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v8m18 0a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2m18 0l-7.5-7.5L3 16"
                }), o("circle", {
                    cx: "8.5",
                    cy: "10.5",
                    r: "1.5",
                    fill: "currentColor"
                })], -1)), g.value && "SUCCESS" !== g.value ? (n(), a("div", vt, M(g.value), 1)) : b("", !0)])), (n(!0), a(m, null, w(l.value.childrenData, (e => (n(), x(kt, {
                    key: e.id,
                    "node-data": e
                }, null, 8, ["node-data"])))), 128))])])),
                _: 1
            }, 8, ["node-data", "is-page-node"]))
        }
    }), [
        ["__scopeId", "data-v-57413c93"]
    ]),
    ft = S(f({
        __name: "TextNodeComponent",
        props: {
            nodeData: {},
            isPageNode: {
                type: Boolean
            }
        },
        setup(e) {
            const t = e,
                i = Ye(),
                l = He(),
                d = r(),
                c = s((() => i.getNodeOfData(t.nodeData))),
                u = s((() => l.editingTextId === c.value.id)),
                g = s((() => l.isTextSelected(c.value.id))),
                v = s({
                    get: () => l.editingText,
                    set: e => {
                        l.editingText = e
                    }
                }),
                f = s((() => c.value.isEmpty ? "Type text here..." : c.value.text)),
                m = s((() => ({
                    cursor: u.value ? "text" : "default",
                    position: "relative"
                }))),
                w = s((() => ({
                    fontSize: `${c.value.fontSize}px`,
                    fontFamily: c.value.fontFamily,
                    color: c.value.isEmpty ? "black" : c.value.color,
                    fontWeight: c.value.fontWeight,
                    fontStyle: c.value.isEmpty ? "normal" : c.value.fontStyle,
                    textAlign: c.value.textAlign,
                    lineHeight: 1,
                    wordBreak: "break-word",
                    whiteSpace: "pre-wrap",
                    userSelect: "none",
                    outline: "none",
                    margin: "0",
                    padding: "0",
                    border: "none",
                    background: "transparent",
                    resize: "none"
                }))),
                y = s((() => ({
                    fontSize: `${c.value.fontSize}px`,
                    fontFamily: c.value.fontFamily,
                    color: c.value.color,
                    fontWeight: c.value.fontWeight,
                    fontStyle: c.value.fontStyle,
                    textAlign: c.value.textAlign,
                    lineHeight: 1,
                    wordBreak: "break-word",
                    whiteSpace: "pre-wrap",
                    outline: "none",
                    margin: "0",
                    padding: "0",
                    border: "none",
                    background: "transparent",
                    width: "100%",
                    height: "100%",
                    resize: "none",
                    overflow: "hidden"
                })));

            function I() {
                U("Text edit complete", {
                    nodeId: c.value.id,
                    text: v.value
                }), d.value && (l.editingText = d.value.textContent || ""), l.commitTextEditing()
            }

            function k(e) {
                U("Text editor keydown", {
                    key: e.key
                }), "Enter" !== e.key || e.shiftKey ? "Escape" === e.key ? (e.preventDefault(), l.cancelTextEditing()) : "Tab" === e.key && (e.preventDefault(), I()) : (e.preventDefault(), I()), e.stopPropagation()
            }

            function b() {}
            return p(u, (e => {
                e && h((() => {
                    var e;
                    if (u.value && d.value && (U("ensureTextEditorFocus"), d.value.focus(), d.value)) try {
                        if (document.execCommand("selectAll", !1, void 0), !(null == (e = window.getSelection()) ? void 0 : e.toString())) {
                            const e = document.createRange(),
                                t = window.getSelection();
                            t && d.value.firstChild ? (e.selectNodeContents(d.value), t.removeAllRanges(), t.addRange(e)) : t && d.value.textContent && (e.selectNode(d.value), t.removeAllRanges(), t.addRange(e))
                        }
                        U("Text editor focused and content selected")
                    } catch (t) {
                        U("Failed to select text content", {
                            error: t
                        })
                    }
                }))
            })), p((() => c.value.text), (() => {
                U("Text content changed, triggering update")
            }), {
                deep: !0
            }), (e, t) => (n(), x(Qe, {
                "node-data": e.nodeData,
                "is-page-node": e.isPageNode
            }, {
                default: _((() => [o("div", {
                    class: N(["text-node", {
                        editing: u.value,
                        selected: g.value,
                        empty: c.value.isEmpty
                    }]),
                    style: C(m.value)
                }, [u.value ? (n(), a("div", {
                    key: 1,
                    ref_key: "textEditor",
                    ref: d,
                    class: "text-editor",
                    style: C(y.value),
                    contenteditable: "true",
                    onBlur: I,
                    onKeydown: k,
                    onInput: b
                }, M(v.value), 37)) : (n(), a("div", {
                    key: 0,
                    class: "text-display",
                    style: C(w.value)
                }, M(f.value), 5))], 6)])),
                _: 1
            }, 8, ["node-data", "is-page-node"]))
        }
    }), [
        ["__scopeId", "data-v-f0693b44"]
    ]),
    mt = ["width", "height", "viewBox"],
    wt = ["d", "stroke", "stroke-width", "fill"],
    yt = ["d", "stroke", "stroke-width"],
    xt = S(f({
        __name: "SvgNodeComponent",
        props: {
            nodeData: {},
            isPageNode: {
                type: Boolean
            }
        },
        setup(e) {
            const t = Ye(),
                o = We(),
                i = s((() => o.currentPath)),
                l = s((() => o.currentStroke)),
                r = s((() => o.currentStrokeWidth));
            return (e, o) => {
                var s, d, c, u, g, h, v, p, f, x;
                return n(), a("div", {
                    class: "svg-node-container",
                    style: C({
                        left: `${(null==(s=e.nodeData)?void 0:s.left)||0}px`,
                        top: `${(null==(d=e.nodeData)?void 0:d.top)||0}px`,
                        width: `${(null==(c=e.nodeData)?void 0:c.width)||0}px`,
                        height: `${(null==(u=e.nodeData)?void 0:u.height)||0}px`,
                        transform: `rotate(${(null==(g=e.nodeData)?void 0:g.rotation)||0}deg)`
                    })
                }, [(n(), a("svg", {
                    width: (null == (h = e.nodeData) ? void 0 : h.width) || 0,
                    height: (null == (v = e.nodeData) ? void 0 : v.height) || 0,
                    viewBox: `0 0 ${(null==(p=e.nodeData)?void 0:p.width)||0} ${(null==(f=e.nodeData)?void 0:f.height)||0}`,
                    class: "svg-canvas"
                }, [(n(!0), a(m, null, w((null == (x = e.nodeData) ? void 0 : x.paths) || [], (e => (n(), a("path", {
                    key: e.id,
                    d: e.d,
                    stroke: e.stroke,
                    "stroke-width": e.strokeWidth,
                    fill: e.fill,
                    "stroke-linecap": "round",
                    "stroke-linejoin": "round"
                }, null, 8, wt)))), 128)), y(t).isInDoodleMode && i.value ? (n(), a("path", {
                    key: 0,
                    d: i.value,
                    stroke: l.value,
                    "stroke-width": r.value,
                    fill: "none",
                    "stroke-linecap": "round",
                    "stroke-linejoin": "round",
                    class: "current-path"
                }, null, 8, yt)) : b("", !0)], 8, mt))], 4)
            }
        }
    }), [
        ["__scopeId", "data-v-335f0658"]
    ]),
    It = {
        key: 1,
        class: "error-node"
    },
    kt = S(f({
        __name: "DynamicNodeRenderer",
        props: {
            nodeData: {
                type: Object,
                required: !0
            },
            isPageNode: {
                type: Boolean,
                default: !1
            }
        },
        setup(e) {
            const t = e,
                o = {
                    [pe.PROJECT]: Ze,
                    [pe.FRAME]: tt,
                    [pe.HTML_FRAGMENT]: ot,
                    [pe.IMAGE]: pt,
                    [pe.TEXT]: ft,
                    [pe.SVG]: xt
                },
                i = s((() => {
                    const e = t.nodeData.type;
                    return o[e] || null
                }));
            return (t, o) => i.value ? (n(), x(T(i.value), {
                key: 0,
                "node-data": e.nodeData,
                "is-page-node": e.isPageNode
            }, null, 8, ["node-data", "is-page-node"])) : (n(), a("div", It, "Unknown node type: " + M(e.nodeData.type), 1))
        }
    }), [
        ["__scopeId", "data-v-f5da5fb0"]
    ]),
    Ct = {
        class: "selection-overlay"
    },
    bt = ["onMousedown"],
    Et = S(f({
        __name: "SelectionOverlay",
        setup(e) {
            Ye();
            const t = Re(),
                i = s((() => {
                    if (!t.selection) return {};
                    const e = t.interactionMode === Le.CREATING,
                        a = 2 / t.canvasScale;
                    return {
                        position: "absolute",
                        left: `${t.selection.x}px`,
                        top: `${t.selection.y}px`,
                        width: `${t.selection.width}px`,
                        height: `${t.selection.height}px`,
                        border: `${a}px ${e?"dashed":"solid"} #0F7FFF`,
                        backgroundColor: "rgba(15, 127, 255, 0.1)",
                        pointerEvents: "none",
                        zIndex: "1"
                    }
                })),
                l = s((() => {
                    if (!t.selection || t.isSelecting) return [];
                    const e = t.selection,
                        a = 14 / t.canvasScale,
                        n = 0 / t.canvasScale,
                        o = a + 2 * n,
                        i = o + 8 / t.canvasScale,
                        l = 2 / t.canvasScale,
                        r = e => e.includes("top") ? "3" : e.includes("bottom") ? "1" : "2";
                    return [{
                        id: Ae.TOP_LEFT,
                        x: e.x,
                        y: e.y,
                        cursor: "nwse-resize"
                    }, {
                        id: Ae.TOP,
                        x: e.x + e.width / 2,
                        y: e.y,
                        cursor: "ns-resize"
                    }, {
                        id: Ae.TOP_RIGHT,
                        x: e.x + e.width,
                        y: e.y,
                        cursor: "nesw-resize"
                    }, {
                        id: Ae.RIGHT,
                        x: e.x + e.width,
                        y: e.y + e.height / 2,
                        cursor: "ew-resize"
                    }, {
                        id: Ae.BOTTOM_RIGHT,
                        x: e.x + e.width,
                        y: e.y + e.height,
                        cursor: "nwse-resize"
                    }, {
                        id: Ae.BOTTOM,
                        x: e.x + e.width / 2,
                        y: e.y + e.height,
                        cursor: "ns-resize"
                    }, {
                        id: Ae.BOTTOM_LEFT,
                        x: e.x,
                        y: e.y + e.height,
                        cursor: "nesw-resize"
                    }, {
                        id: Ae.LEFT,
                        x: e.x,
                        y: e.y + e.height / 2,
                        cursor: "ew-resize"
                    }].map((e => {
                        const t = (s = e.id).includes("left") || s.includes("right") ? o : i;
                        var s;
                        const d = (e => !e.includes("top") && !e.includes("bottom"))(e.id) ? i : o;
                        return { ...e,
                            handleSize: a,
                            totalSize: o,
                            padding: n,
                            style: {
                                position: "absolute",
                                left: e.x - t / 2 + "px",
                                top: e.y - d / 2 + "px",
                                width: `${t}px`,
                                height: `${d}px`,
                                padding: `${n}px`,
                                cursor: e.cursor,
                                pointerEvents: "auto",
                                zIndex: r(e.id)
                            },
                            innerStyle: {
                                width: "100%",
                                height: "100%",
                                backgroundColor: "#FFFFFF",
                                border: `${l}px solid #0F7FFF`,
                                borderRadius: o / 2 + "px"
                            }
                        }
                    }))
                })),
                r = s((() => !t.selection || t.isSelecting ? {} : {
                    position: "absolute",
                    left: `${t.selection.x}px`,
                    top: `${t.selection.y}px`,
                    width: `${t.selection.width}px`,
                    height: `${t.selection.height}px`,
                    cursor: "move",
                    pointerEvents: "auto",
                    zIndex: "1"
                })),
                d = s((() => {
                    if (t.interactionMode === Le.MOVING) return "move";
                    if (t.interactionMode === Le.RESIZING && t.activeResizeHandle) {
                        const e = l.value.find((e => e.id === t.activeResizeHandle));
                        return (null == e ? void 0 : e.cursor) || "default"
                    }
                    return "default"
                }));
            u((() => {
                t.interactionMode !== Le.NONE ? document.body.style.cursor = d.value : document.body.style.cursor = ""
            }));
            const c = e => {
                e.stopPropagation();
                const a = e.target.closest(".j-viewport");
                if (!a) return;
                const n = a.getBoundingClientRect(),
                    o = {
                        x: e.clientX - n.left,
                        y: e.clientY - n.top
                    },
                    i = t.viewportToCanvasCoordinates(o);
                t.startMove(i)
            };
            return (e, s) => (n(), a("div", Ct, [o("div", {
                style: C(i.value)
            }, null, 4), y(t).isSelecting ? b("", !0) : (n(), a("div", {
                key: 0,
                style: C(r.value),
                onMousedown: c
            }, null, 36)), y(t).isSelecting ? b("", !0) : (n(!0), a(m, {
                key: 1
            }, w(l.value, (e => (n(), a("div", {
                key: e.id,
                style: C(e.style),
                class: "resize-handle",
                onMousedown: a => ((e, a) => {
                    e.stopPropagation();
                    const n = e.target.closest(".j-viewport");
                    if (!n) return;
                    const o = n.getBoundingClientRect(),
                        i = {
                            x: e.clientX - o.left,
                            y: e.clientY - o.top
                        },
                        l = t.viewportToCanvasCoordinates(i);
                    t.startResize(a, l)
                })(a, e.id)
            }, [o("div", {
                style: C(e.innerStyle)
            }, null, 4)], 44, bt)))), 128))]))
        }
    }), [
        ["__scopeId", "data-v-253f2ca7"]
    ]),
    Mt = ["src"],
    St = ["onMousedown"],
    _t = S(f({
        __name: "CutFragment",
        props: {
            fragment: {},
            isActive: {
                type: Boolean
            }
        },
        emits: ["activate"],
        setup(e, {
            emit: t
        }) {
            const i = e,
                l = t,
                d = Ye(),
                c = Re(),
                u = s((() => ({
                    position: "absolute",
                    left: `${i.fragment.x}px`,
                    top: `${i.fragment.y}px`,
                    width: `${i.fragment.width}px`,
                    height: `${i.fragment.height}px`,
                    cursor: i.fragment.isDragging ? "grabbing" : "grab",
                    zIndex: i.isActive ? 100 : 90,
                    userSelect: "none",
                    opacity: i.isActive ? 1 : .7
                }))),
                g = s((() => ({
                    position: "absolute",
                    left: 0,
                    top: 0,
                    width: "100%",
                    height: "100%",
                    border: `${1/d.canvasScale}px solid ${i.isActive?"#0F7FFF":"#999"}`,
                    backgroundColor: i.isActive ? "rgba(15, 127, 255, 0.1)" : "rgba(153, 153, 153, 0.1)",
                    pointerEvents: "none"
                }))),
                h = s((() => {
                    if (!i.isActive) return [];
                    const e = i.fragment,
                        t = 8 / d.canvasScale,
                        a = 4 / d.canvasScale,
                        n = t + 2 * a,
                        o = 1 / d.canvasScale;
                    return [{
                        id: "top-left",
                        x: 0,
                        y: 0,
                        cursor: "nwse-resize"
                    }, {
                        id: "top",
                        x: e.width / 2,
                        y: 0,
                        cursor: "ns-resize"
                    }, {
                        id: "top-right",
                        x: e.width,
                        y: 0,
                        cursor: "nesw-resize"
                    }, {
                        id: "right",
                        x: e.width,
                        y: e.height / 2,
                        cursor: "ew-resize"
                    }, {
                        id: "bottom-right",
                        x: e.width,
                        y: e.height,
                        cursor: "nwse-resize"
                    }, {
                        id: "bottom",
                        x: e.width / 2,
                        y: e.height,
                        cursor: "ns-resize"
                    }, {
                        id: "bottom-left",
                        x: 0,
                        y: e.height,
                        cursor: "nesw-resize"
                    }, {
                        id: "left",
                        x: 0,
                        y: e.height / 2,
                        cursor: "ew-resize"
                    }].map((e => {
                        return { ...e,
                            style: {
                                position: "absolute",
                                left: e.x - n / 2 + "px",
                                top: e.y - n / 2 + "px",
                                width: `${n}px`,
                                height: `${n}px`,
                                padding: `${a}px`,
                                cursor: e.cursor,
                                pointerEvents: "auto",
                                zIndex: (t = e.id, t.includes("top") ? "2" : t.includes("bottom") ? "0" : "1")
                            },
                            innerStyle: {
                                width: "100%",
                                height: "100%",
                                backgroundColor: "#0F7FFF",
                                border: `${o}px solid white`,
                                borderRadius: "50%"
                            }
                        };
                        var t
                    }))
                })),
                v = e => {
                    if (e.stopPropagation(), !i.isActive) return void l("activate");
                    const t = e.target.closest(".j-viewport");
                    if (!t) return;
                    const a = t.getBoundingClientRect(),
                        n = {
                            x: e.clientX - a.left,
                            y: e.clientY - a.top
                        },
                        o = c.viewportToCanvasCoordinates(n);
                    d.startFragmentDrag(o)
                },
                p = r(!1),
                f = r(null),
                y = r(null),
                x = r(null),
                k = e => {
                    if (!(p.value && f.value && y.value && x.value)) return;
                    const t = document.querySelector(".j-viewport");
                    if (!t) return;
                    const a = t.getBoundingClientRect(),
                        n = {
                            x: e.clientX - a.left,
                            y: e.clientY - a.top
                        },
                        o = c.viewportToCanvasCoordinates(n),
                        i = o.x - f.value.x,
                        l = o.y - f.value.y,
                        r = y.value;
                    let s = r.x,
                        u = r.y,
                        g = r.width,
                        h = r.height;
                    switch (x.value) {
                        case "top-left":
                            s = r.x + i, u = r.y + l, g = r.width - i, h = r.height - l;
                            break;
                        case "top":
                            u = r.y + l, h = r.height - l;
                            break;
                        case "top-right":
                            u = r.y + l, g = r.width + i, h = r.height - l;
                            break;
                        case "right":
                            g = r.width + i;
                            break;
                        case "bottom-right":
                            g = r.width + i, h = r.height + l;
                            break;
                        case "bottom":
                            h = r.height + l;
                            break;
                        case "bottom-left":
                            s = r.x + i, g = r.width - i, h = r.height + l;
                            break;
                        case "left":
                            s = r.x + i, g = r.width - i
                    }
                    const v = 10;
                    g < v && (x.value.includes("left") && (s = r.x + r.width - v), g = v), h < v && (x.value.includes("top") && (u = r.y + r.height - v), h = v), d.updateFragmentDimensions(s, u, g, h)
                },
                b = () => {
                    p.value && (p.value = !1, f.value = null, y.value = null, x.value = null, document.body.style.cursor = "", document.removeEventListener("mousemove", k), document.removeEventListener("mouseup", b))
                };
            return I((() => {
                document.removeEventListener("mousemove", k), document.removeEventListener("mouseup", b)
            })), (e, t) => (n(), a("div", {
                style: C(u.value),
                class: "cut-fragment",
                onMousedown: v
            }, [o("img", {
                src: e.fragment.imageData,
                alt: "Cut fragment",
                class: "fragment-image"
            }, null, 8, Mt), o("div", {
                style: C(g.value)
            }, null, 4), (n(!0), a(m, null, w(h.value, (e => (n(), a("div", {
                key: e.id,
                style: C(e.style),
                class: "resize-handle",
                onMousedown: t => ((e, t) => {
                    var a;
                    if (!i.isActive) return;
                    e.stopPropagation();
                    const n = e.target.closest(".j-viewport");
                    if (!n) return;
                    const o = n.getBoundingClientRect(),
                        l = {
                            x: e.clientX - o.left,
                            y: e.clientY - o.top
                        },
                        r = c.viewportToCanvasCoordinates(l);
                    p.value = !0, f.value = r, x.value = t, y.value = {
                        x: i.fragment.x,
                        y: i.fragment.y,
                        width: i.fragment.width,
                        height: i.fragment.height
                    };
                    const s = (null == (a = h.value.find((e => e.id === t))) ? void 0 : a.cursor) || "default";
                    document.body.style.cursor = s, document.addEventListener("mousemove", k), document.addEventListener("mouseup", b)
                })(t, e.id)
            }, [o("div", {
                style: C(e.innerStyle)
            }, null, 4)], 44, St)))), 128))], 36))
        }
    }), [
        ["__scopeId", "data-v-78f43cde"]
    ]),
    Nt = {
        class: "controls-container"
    },
    Tt = {
        key: 0,
        class: "fragments-info"
    },
    Dt = {
        class: "buttons-container"
    },
    Pt = S(f({
        __name: "CuttingControls",
        setup(e) {
            const t = Ye(),
                i = s((() => {
                    if (!t.sourceImageNodeId) return null;
                    const e = t.getNodeById(t.sourceImageNodeId);
                    return e instanceof ue ? e : null
                })),
                l = r({
                    top: 10,
                    right: 10
                }),
                d = () => {
                    if (l.value = {
                            top: 10,
                            right: 10
                        }, !i.value) return;
                    const e = document.querySelector(".j-viewport");
                    if (!e) return;
                    const a = e.getBoundingClientRect();
                    try {
                        const e = i.value,
                            n = e.top || 0,
                            o = e.left || 0,
                            r = e.width || 0,
                            s = n * t.canvasScale + t.canvasTranslateY,
                            d = (o + r) * t.canvasScale + t.canvasTranslateX;
                        l.value = {
                            top: Math.max(5, s - 40),
                            right: Math.max(5, a.width - d - 10)
                        }
                    } catch (n) {}
                };
            k((() => {
                d(), window.addEventListener("resize", d)
            })), I((() => {
                window.removeEventListener("resize", d)
            })), p([() => t.canvasScale, () => t.canvasTranslateX, () => t.canvasTranslateY, i], d, {
                immediate: !0
            });
            const c = s((() => {
                    var e;
                    return (null == (e = t.cuttingFragments) ? void 0 : e.length) || 0
                })),
                u = async () => {
                    try {
                        await t.applyCutting()
                    } catch (e) {}
                },
                g = () => {
                    t.cancelCutting()
                },
                h = s((() => ({
                    position: "absolute",
                    top: `${l.value.top}px`,
                    right: `${l.value.right}px`,
                    zIndex: 1e3
                })));
            return (e, t) => (n(), a("div", {
                class: "cutting-controls",
                style: C(h.value)
            }, [o("div", Nt, [c.value > 0 ? (n(), a("div", Tt, M(c.value) + " fragment" + M(c.value > 1 ? "s" : ""), 1)) : b("", !0), o("div", Dt, [o("button", {
                class: "control-button apply-button",
                onClick: D(u, ["stop"]),
                onMousedown: t[0] || (t[0] = D((() => {}), ["stop"]))
            }, " Apply ", 32), o("button", {
                class: "control-button cancel-button",
                onClick: D(g, ["stop"]),
                onMousedown: t[1] || (t[1] = D((() => {}), ["stop"]))
            }, " Cancel ", 32)]), t[2] || (t[2] = o("div", {
                class: "pointer-indicator"
            }, null, -1))])], 4))
        }
    }), [
        ["__scopeId", "data-v-037416b8"]
    ]),
    Ft = S(f({
        __name: "ImageEditorPanel",
        props: {
            metaInfo: {},
            isVisible: {
                type: Boolean
            }
        },
        emits: ["close", "apply"],
        setup(e, {
            emit: t
        }) {
            const i = P((() => L((() =>
                        import ("./CM3Hoj-z.js")), __vite__mapDeps([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143]),
                    import.meta.url))),
                l = P((() => L((() =>
                        import ("./CbGSSggI.js")), __vite__mapDeps([144, 96, 97, 8, 1, 2, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 43, 30, 32, 23, 37, 38, 25, 26, 27, 31, 110, 111, 112, 113, 114, 16, 17, 115, 116, 4, 5, 6, 117, 84, 51, 118, 119, 120, 39, 121, 79, 88, 24, 28, 29, 33, 34, 122, 123, 95, 124, 60, 20, 83, 82, 44, 125, 139, 13, 140, 12, 141, 42, 142, 3]),
                    import.meta.url))),
                r = P((() => L((() =>
                        import ("./D3TnIu40.js")), __vite__mapDeps([145, 96, 97, 8, 1, 2, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 43, 30, 32, 23, 37, 38, 25, 26, 27, 31, 110, 111, 112, 113, 114, 16, 17, 115, 116, 4, 5, 6, 117, 84, 51, 118, 119, 120, 39, 121, 79, 88, 24, 28, 29, 33, 34, 122, 123, 95, 124, 60, 20, 83, 82, 44, 125, 139, 13, 140, 12, 141, 42, 142, 3]),
                    import.meta.url))),
                d = P((() => L((() =>
                        import ("./Cb7t0ry_.js")), __vite__mapDeps([146, 96, 97, 8, 1, 2, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 43, 30, 32, 23, 37, 38, 25, 26, 27, 31, 110, 111, 112, 113, 114, 16, 17, 115, 116, 4, 5, 6, 117, 84, 51, 118, 119, 120, 39, 121, 79, 88, 24, 28, 29, 33, 34, 122, 123, 95, 124, 60, 20, 83, 82, 44, 125, 139, 13, 140, 12, 141, 42, 142, 3]),
                    import.meta.url))),
                c = P((() => L((() =>
                        import ("./DwlXYv12.js")), __vite__mapDeps([147, 96, 97, 8, 1, 2, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 43, 30, 32, 23, 37, 38, 25, 26, 27, 31, 110, 111, 112, 113, 114, 16, 17, 115, 116, 4, 5, 6, 117, 84, 51, 118, 119, 120, 39, 121, 79, 88, 24, 28, 29, 33, 34, 122, 123, 95, 124, 60, 20, 83, 82, 44, 125, 139, 13, 140, 12, 141, 42, 142, 3]),
                    import.meta.url))),
                u = P((() => L((() =>
                        import ("./C6zJMIN5.js")), __vite__mapDeps([148, 96, 97, 8, 1, 2, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 43, 30, 32, 23, 37, 38, 25, 26, 27, 31, 110, 111, 112, 113, 114, 16, 17, 115, 116, 4, 5, 6, 117, 84, 51, 118, 119, 120, 39, 121, 79, 88, 24, 28, 29, 33, 34, 122, 123, 95, 124, 60, 20, 83, 82, 44, 125, 139, 13, 140, 12, 141, 42, 142, 3]),
                    import.meta.url))),
                g = e,
                h = t,
                v = Ye(),
                p = {
                    edit: i,
                    upscale: l,
                    outpaint: r,
                    "remove-bg": d,
                    remover: c,
                    redraw: u
                },
                f = s((() => v.activeMode === De.FORM_EDIT ? i : v.activeMode === De.TOOL && v.currentToolId ? p[v.currentToolId] : null)),
                m = s((() => v.activeMode === De.FORM_EDIT)),
                w = s((() => {
                    if (!v.selectedPageImageId) return !1;
                    const e = v.getNodeById(v.selectedPageImageId);
                    if (!e || "image" !== e.type) return !1;
                    return !!e.src
                }));
            s((() => {
                switch (v.currentToolId) {
                    case "upscale":
                        return "Upscale Image";
                    case "outpaint":
                        return "Outpaint";
                    case "remove-bg":
                        return "Remove Background";
                    case "remover":
                        return "Remover";
                    case "redraw":
                        return "Redraw";
                    default:
                        return "Modify Image Content"
                }
            }));
            const I = s((() => v.currentToolId === _e.OUTPAINT || v.currentToolId === _e.UPSCALE || v.currentToolId === _e.REMOVE_BG || v.currentToolId === _e.REMOVER || v.currentToolId === _e.REDRAW)),
                k = () => {
                    h("close")
                },
                C = e => {
                    h("apply", e)
                };
            return (e, t) => (n(), x(F, {
                name: "slide-right"
            }, {
                default: _((() => [e.isVisible && w.value ? (n(), a("div", {
                    key: 0,
                    class: N(["image-meta-editor absolute", m.value ? "right-[12px] top-[39px]" : "inset-0 flex items-start justify-end image-meta-editor "])
                }, [o("div", {
                    class: N(["image-meta-editor-container j-image-meta-editor-container", {
                        "wider-panel": I.value,
                        "meta-form-panel": m.value
                    }])
                }, [I.value ? (n(), x(y(q), {
                    key: 0,
                    onClick: k,
                    class: "close-btn"
                })) : b("", !0), (n(), x(T(f.value), {
                    class: "image-meta-editor-component",
                    "meta-info": g.metaInfo,
                    onClose: k,
                    onApply: C
                }, null, 40, ["meta-info"]))], 2)], 2)) : b("", !0)])),
                _: 1
            }))
        }
    }), [
        ["__scopeId", "data-v-74f3dcb3"]
    ]),
    Lt = {
        class: "content"
    },
    At = {
        class: "section"
    },
    Rt = {
        class: "section"
    },
    Bt = {
        class: "font-size-control"
    },
    zt = {
        class: "section"
    },
    Ot = {
        class: "color-control"
    },
    $t = {
        class: "section"
    },
    Ut = {
        class: "style-controls"
    },
    Ht = {
        class: "style-group"
    },
    jt = {
        class: "section"
    },
    Wt = {
        class: "alignment-controls"
    },
    Xt = S(f({
        __name: "TextNodeForm",
        props: {
            textNode: {}
        },
        setup(e) {
            const t = e;
            Ye();
            const i = He(),
                l = r({
                    fontSize: 16,
                    fontFamily: "Arial, sans-serif",
                    color: "#000000",
                    fontWeight: "normal",
                    fontStyle: "normal",
                    textAlign: "left"
                });
            p((() => t.textNode), (e => {
                e && (l.value = {
                    fontSize: e.fontSize,
                    fontFamily: e.fontFamily,
                    color: e.color,
                    fontWeight: e.fontWeight,
                    fontStyle: e.fontStyle,
                    textAlign: e.textAlign
                })
            }), {
                immediate: !0
            });
            const s = () => {
                    t.textNode && i.updateTextStyle(t.textNode.id, {
                        fontSize: l.value.fontSize
                    })
                },
                d = () => {
                    t.textNode && i.updateTextStyle(t.textNode.id, {
                        color: l.value.color
                    })
                },
                c = e => {
                    l.value.fontFamily = e, t.textNode && i.updateTextStyle(t.textNode.id, {
                        fontFamily: e
                    })
                },
                u = () => {
                    l.value.fontWeight = "bold" === l.value.fontWeight ? "normal" : "bold", t.textNode && i.updateTextStyle(t.textNode.id, {
                        fontWeight: l.value.fontWeight
                    })
                },
                g = () => {
                    l.value.fontStyle = "italic" === l.value.fontStyle ? "normal" : "italic", t.textNode && i.updateTextStyle(t.textNode.id, {
                        fontStyle: l.value.fontStyle
                    })
                },
                h = e => {
                    l.value.textAlign = e, t.textNode && i.updateTextStyle(t.textNode.id, {
                        textAlign: e
                    })
                };
            return (e, t) => (n(), a("div", null, [o("div", Lt, [o("div", At, [t[6] || (t[6] = o("label", {
                class: "label"
            }, "Font Family", -1)), A(J, {
                "current-font": l.value.fontFamily,
                onFontChange: c
            }, null, 8, ["current-font"])]), o("div", Rt, [t[8] || (t[8] = o("label", {
                class: "label"
            }, "Font Size", -1)), o("div", Bt, [R(o("input", {
                "onUpdate:modelValue": t[0] || (t[0] = e => l.value.fontSize = e),
                type: "number",
                min: "8",
                max: "72",
                class: "font-size-input",
                onInput: s
            }, null, 544), [
                [B, l.value.fontSize, void 0, {
                    number: !0
                }]
            ]), t[7] || (t[7] = o("span", {
                class: "font-size-unit"
            }, "px", -1))])]), o("div", zt, [t[9] || (t[9] = o("label", {
                class: "label"
            }, "Text Color", -1)), o("div", Ot, [R(o("input", {
                "onUpdate:modelValue": t[1] || (t[1] = e => l.value.color = e),
                type: "color",
                class: "color-picker",
                onInput: d
            }, null, 544), [
                [B, l.value.color]
            ]), R(o("input", {
                "onUpdate:modelValue": t[2] || (t[2] = e => l.value.color = e),
                type: "text",
                class: "color-input",
                placeholder: "#000000",
                onInput: d
            }, null, 544), [
                [B, l.value.color]
            ])])]), o("div", $t, [t[10] || (t[10] = o("label", {
                class: "label"
            }, "Font Style", -1)), o("div", Ut, [o("div", Ht, [o("button", {
                type: "button",
                class: N(["style-btn", {
                    active: "bold" === l.value.fontWeight
                }]),
                onClick: u
            }, [A(y(X))], 2), o("button", {
                type: "button",
                class: N(["style-btn", {
                    active: "italic" === l.value.fontStyle
                }]),
                onClick: g
            }, [A(y(V))], 2)])])]), o("div", jt, [t[11] || (t[11] = o("label", {
                class: "label"
            }, "Text Alignment", -1)), o("div", Wt, [o("button", {
                type: "button",
                class: N(["alignment-btn", {
                    active: "left" === l.value.textAlign
                }]),
                onClick: t[3] || (t[3] = e => h("left"))
            }, [A(y(G))], 2), o("button", {
                type: "button",
                class: N(["alignment-btn", {
                    active: "center" === l.value.textAlign
                }]),
                onClick: t[4] || (t[4] = e => h("center"))
            }, [A(y(Y))], 2), o("button", {
                type: "button",
                class: N(["alignment-btn", {
                    active: "right" === l.value.textAlign
                }]),
                onClick: t[5] || (t[5] = e => h("right"))
            }, [A(y(Z))], 2)])])])]))
        }
    }), [
        ["__scopeId", "data-v-7cb6fae0"]
    ]),
    Vt = {
        key: 0,
        class: "text-editor-panel absolute right-[12px] top-[39px] z-[101]"
    },
    Gt = {
        class: "text-editor-container"
    },
    Yt = {
        class: "header"
    },
    Zt = S(f({
        __name: "TextEditorPanel",
        props: {
            isVisible: {
                type: Boolean
            }
        },
        emits: ["close"],
        setup(e, {
            emit: t
        }) {
            const i = t,
                l = Ye();
            He();
            const r = s((() => {
                    if (!l.selectedNodeId) return !1;
                    const e = l.getNodeById(l.selectedNodeId);
                    return !(!e || e.type !== pe.TEXT) && !l.isOperatingImage
                })),
                d = s((() => {
                    if (!l.selectedNodeId) return null;
                    const e = l.getNodeById(l.selectedNodeId);
                    return e && e.type === pe.TEXT ? e : null
                })),
                c = () => {
                    i("close")
                };
            return (e, t) => (n(), x(F, {
                name: "slide-right"
            }, {
                default: _((() => [e.isVisible && r.value ? (n(), a("div", Vt, [o("div", Gt, [o("div", Yt, [t[0] || (t[0] = o("h3", {
                    class: "title"
                }, "Text Style", -1)), A(y(q), {
                    onClick: c,
                    class: "close-btn"
                })]), (n(), x(T(Xt), {
                    class: "text-editor-component",
                    "text-node": d.value
                }, null, 8, ["text-node"]))])])) : b("", !0)])),
                _: 1
            }))
        }
    }), [
        ["__scopeId", "data-v-013d87fe"]
    ]),
    qt = async e => {
        const t = e.selectedPageImageId ? e.getNodeById(e.selectedPageImageId) : null;
        if (!t || t.type !== pe.IMAGE) return null;
        try {
            const e = (e => {
                try {
                    if (!e) return e;
                    if (e.startsWith("data:")) return e;
                    const t = new URL(e);
                    return t.hostname = "page1.genspark.site", t.toString()
                } catch (t) {
                    return e
                }
            })(t.src);
            U("Loading image from:", e);
            const a = await fetch(e),
                n = await a.blob();
            return new File([n], "selected-image.jpg", {
                type: n.type
            })
        } catch (a) {
            return null
        }
    },
    Jt = async (e, t) => {
        const {
            operation: a,
            data: n,
            prompt: o = ""
        } = e;
        try {
            const i = t.selectedPageImageId ? t.getNodeById(t.selectedPageImageId) : null;
            if (!i || i.type !== pe.IMAGE) return {
                success: !1,
                message: "No selected image found"
            };
            const l = i.src,
                r = (() => {
                    const e = c();
                    return new URLSearchParams(window.location.search).get("id") || e.query.id || ""
                })(),
                s = await fetch("/api/infinite_canvas/edit_image", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        image_url: e.image_url || l,
                        operation: a,
                        prompt: o,
                        params: { ...n
                        },
                        project_id: r,
                        selected_image_id: t.selectedPageImageId
                    })
                }),
                d = await s.json();
            if (U("edit_image result:", d), 0 === d.status) {
                let e;
                if (U(`${a} task created:`, d.data.task_id), d.data.new_node) {
                    const a = d.data.new_node;
                    e = a.id, U("Adding new node to canvas:", a, JSON.stringify(a)), t.addNodeToCanvas(a)
                }
                return {
                    success: !0,
                    taskId: d.data.task_id,
                    newNodeId: e,
                    message: "Operation completed successfully"
                }
            }
            return {
                success: !1,
                message: d.message || `${a} API failed`
            }
        } catch (i) {
            return {
                success: !1,
                message: `Failed to call ${a} API: ${i}`
            }
        }
    },
    Kt = (e, t, a, n) => {
        let o = !1;
        return async (i, l, r, s) => {
            if (!o) {
                o = !0;
                try {
                    const o = await Jt({
                        image_url: i,
                        operation: e,
                        data: s,
                        prompt: r || ""
                    }, t);
                    o.success ? a() : n(o.message || "Operation failed")
                } catch (d) {
                    n(`Failed to process ${e}: ${d}`)
                } finally {
                    o = !1
                }
            }
        }
    },
    Qt = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const ea = {
        render: function(e, t) {
            return n(), a("svg", Qt, t[0] || (t[0] = [o("g", {
                opacity: "1"
            }, [o("path", {
                d: "M12.1309 19.6982L12.1309 5.69824",
                stroke: "currentColor",
                "stroke-width": "1.75",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }), o("path", {
                d: "M8.00154 8.84844L11.2863 5.73898C11.7182 5.3301 12.4185 5.3301 12.8504 5.73898L16.1352 8.84844",
                stroke: "currentColor",
                "stroke-width": "1.75",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            })], -1)]))
        }
    },
    ta = {
        class: "edit-input-group"
    },
    aa = {
        class: "edit-input-top-row"
    },
    na = {
        key: 0,
        class: "mask-preview-container"
    },
    oa = {
        class: "mask-preview-box"
    },
    ia = ["src"],
    la = {
        key: 1,
        class: "mask-upload-overlay"
    },
    ra = ["placeholder", "disabled", "onKeydown"],
    sa = {
        class: "edit-input-bottom-row"
    },
    da = ["disabled", "title"],
    ca = {
        key: 1
    },
    ua = S(f({
        __name: "EditInput",
        props: {
            placeholder: {
                default: "Enter your prompt..."
            },
            isProcessing: {
                type: Boolean,
                default: !1
            },
            isReady: {
                type: Boolean,
                default: !0
            },
            readyStateMessage: {
                default: "Not ready"
            },
            showMaskPreview: {
                type: Boolean,
                default: !1
            },
            autoFocus: {
                type: Boolean,
                default: !1
            },
            clearOnSubmit: {
                type: Boolean,
                default: !0
            },
            disableInputDuringProcessing: {
                type: Boolean,
                default: !1
            },
            allowEmptySubmit: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["submit"],
        setup(e, {
            emit: t
        }) {
            const i = e,
                l = t,
                d = Re(),
                {
                    isAsking: c
                } = K(),
                u = r(null),
                g = s({
                    get: () => d.editInputValue,
                    set(e) {
                        d.editInputValue = e
                    }
                }),
                v = s((() => d.maskUrl || d.maskPreviewUrl)),
                f = s((() => !c.value && (i.showMaskPreview ? !!d.selection && !d.isMaskUploading && !!d.maskUrl : i.isReady))),
                m = s((() => c.value ? "Processing your request..." : i.showMaskPreview ? d.isMaskUploading ? "Preparing mask from your selection..." : d.selection ? d.maskUrl ? "Ready" : "Mask not ready" : "No selection area" : i.readyStateMessage)),
                w = () => {
                    if (!(g.value.trim().length > 0 || i.allowEmptySubmit) || i.isProcessing || !f.value || c.value) return;
                    const e = g.value.trim();
                    l("submit", e), i.clearOnSubmit && (g.value = "")
                };
            return p((() => i.autoFocus && !i.isProcessing), (e => {
                U("shouldFocus", e), e && h((() => {
                    var e;
                    (() => {
                        const e = u.value;
                        if (!e) return !1;
                        const t = e.closest(".j-infinite-canvas-root-container");
                        if (!t) return !0;
                        const a = e.getBoundingClientRect(),
                            n = t.getBoundingClientRect(),
                            o = window.getComputedStyle(e),
                            i = parseFloat(o.lineHeight) || parseFloat(o.fontSize) || 16;
                        return a.top + i <= n.top + n.height
                    })() && (null == (e = u.value) || e.focus())
                }))
            }), {
                immediate: !0
            }), (e, t) => (n(), a("div", ta, [o("div", {
                class: N(["edit-input-wrapper", {
                    "is-disabled": e.isProcessing
                }])
            }, [o("div", aa, [e.showMaskPreview && (v.value || y(d).isMaskUploading) ? (n(), a("div", na, [o("div", oa, [v.value ? (n(), a("img", {
                key: 0,
                src: v.value,
                alt: "Mask preview",
                class: "mask-preview-image"
            }, null, 8, ia)) : b("", !0), y(d).isMaskUploading ? (n(), a("div", la, [A(Q, {
                class: "mask-upload-overlay-icon"
            })])) : b("", !0)])])) : b("", !0)]), R(o("textarea", {
                ref_key: "editTextarea",
                ref: u,
                "onUpdate:modelValue": t[0] || (t[0] = e => g.value = e),
                placeholder: e.placeholder,
                disabled: i.disableInputDuringProcessing && i.isProcessing,
                class: "edit-textarea",
                onKeydown: z(D(w, ["prevent"]), ["enter"])
            }, null, 40, ra), [
                [B, g.value]
            ]), o("div", sa, [o("button", {
                onClick: w,
                disabled: !g.value.trim() && !i.allowEmptySubmit || e.isProcessing || !f.value,
                class: "menu-btn edit-apply-btn",
                title: f.value ? "Apply" : m.value
            }, [e.isProcessing ? (n(), a("span", ca, "...")) : (n(), x(y(ea), {
                key: 0
            }))], 8, da)])], 2)]))
        }
    }), [
        ["__scopeId", "data-v-bcdbc05b"]
    ]),
    ga = f({
        __name: "MaskEditInput",
        emits: ["askWithMask"],
        setup(e, {
            emit: t
        }) {
            const a = t,
                o = Ye(),
                i = Re(),
                l = ee(),
                s = r(!1),
                d = async e => {
                    var t, n, r, d;
                    if (!e.trim() || s.value || !i.selection || !o.selectedPageImageId || i.isMaskUploading || !i.maskUrl) return;
                    U("handleMaskEditApply called with prompt:", e);
                    const c = o.getNodeById(o.selectedPageImageId);
                    if (!c || "image" !== c.type) return void $("Selected node is not an image");
                    const u = c;
                    s.value = !0;
                    try {
                        const o = i.maskUrl;
                        if (!o) return $("No mask URL available"), void l.warning("Mask not ready, please wait for upload to complete");
                        const s = [];
                        s.push({
                            type: "text",
                            text: "Please regenerate as requested.",
                            hide_in_ui: !1
                        }), s.push({
                            type: "text",
                            text: "<system-reminder>The new meta information modified by user is as follows:",
                            hide_in_ui: !0
                        });
                        const c = u.metaInfo,
                            g = {
                                image_elements: (null == (t = null == c ? void 0 : c.composition) ? void 0 : t.image_elements) || [],
                                text_elements: (null == (n = null == c ? void 0 : c.composition) ? void 0 : n.text_elements) || [],
                                base_image: u.src,
                                user_custom_instructions: e,
                                mask_image: o
                            };
                        s.push({
                            type: "text",
                            text: JSON.stringify(g, null, 2),
                            hide_in_ui: !0
                        });
                        let h = "",
                            v = "",
                            p = "",
                            f = "",
                            m = "";
                        u.width && u.height && (h = `${u.width}x${u.height}`), f = u.originalFinalTextPrompt, m = (null == (d = null == (r = u.rawData) ? void 0 : r.extra_info) ? void 0 : d.model) || "gpt-image-1", u.finalTextPrompt && (v = u.finalTextPrompt);
                        const w = u.editHistory;
                        w && w.length > 0 && w.forEach(((e, t) => {
                            p += `${t+1}. ${e.description} (${e.operation}) with parameters: ${JSON.stringify(e.parameters)}\n`
                        }));
                        let y = "";
                        h && (y += `Image dimensions: ${h}\n`), v && (y += `Final text prompt: ${v}\n`), m && (y += `Generated by model: ${m}\n`), f && (y += `Originally generated with text prompt: ${f}\n`), p && (y += "Edit history: This image has been processed through the following advanced edit operations(from the original generated image):\n", y += p + "\n"), y && s.push({
                            type: "text",
                            text: "The additional info for the base image:\n" + y,
                            hide_in_ui: !0
                        }), s.push({
                            type: "text",
                            text: "</system-reminder>",
                            hide_in_ui: !0
                        }), s.push({
                            type: "text",
                            text: `Using mask image: ${o}\nPlease use this mask image to guide the generation of new images.`,
                            hide_in_ui: !0
                        }), a("askWithMask", s), U("Mask edit message content emitted successfully", s), i.clearSelection(), U("Selection cleared after mask edit apply")
                    } catch (g) {
                        $("Error processing mask edit:", g), l.warning("Failed to process mask edit request")
                    } finally {
                        s.value = !1
                    }
                };
            return (e, t) => (n(), x(ua, {
                placeholder: "Describe changes for the selected area...",
                "is-processing": s.value,
                "show-mask-preview": !0,
                "auto-focus": !0,
                onSubmit: d
            }, null, 8, ["is-processing"]))
        }
    }),
    ha = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const va = {
        render: function(e, t) {
            return n(), a("svg", ha, t[0] || (t[0] = [o("path", {
                d: "M17.4487 15.625H2.78711C2.48292 15.625 2.23633 15.8716 2.23633 16.1758V17.3322C2.23633 17.6365 2.48292 17.8831 2.78711 17.8831H17.4487C17.7528 17.8831 17.9994 17.6365 17.9994 17.3322V16.1758C17.9994 15.8716 17.7528 15.625 17.4487 15.625Z",
                fill: "currentColor"
            }, null, -1), o("path", {
                d: "M7.13547 13.3937C7.04726 13.3937 6.97279 13.329 6.95906 13.2408C6.38475 9.44404 5.9692 9.10886 2.19993 8.54043C2.08428 8.52278 1.99805 8.42282 1.99805 8.30521C1.99805 8.18761 2.08428 8.08765 2.19993 8.07C5.94961 7.50354 6.28282 7.16836 6.8493 3.42065C6.86694 3.305 6.96689 3.21875 7.0845 3.21875C7.20211 3.21875 7.30207 3.305 7.31971 3.42065C7.88619 7.16836 8.22136 7.50354 11.9691 8.07C12.0847 8.08765 12.171 8.18761 12.171 8.30521C12.171 8.42282 12.0847 8.52278 11.9691 8.54043C8.20373 9.10886 7.88226 9.44404 7.31187 13.2408C7.29816 13.327 7.22366 13.3937 7.13547 13.3937Z",
                fill: "currentColor"
            }, null, -1), o("path", {
                d: "M14.2849 8.3742C14.2299 8.3742 14.183 8.33303 14.1751 8.27814C13.8164 5.90643 13.5556 5.69669 11.2016 5.34192C11.129 5.33015 11.0742 5.26939 11.0742 5.19491C11.0742 5.12239 11.1272 5.05966 11.2016 5.0479C13.5439 4.69508 13.7537 4.48535 14.1065 2.14304C14.1183 2.07051 14.179 2.01562 14.2535 2.01562C14.3261 2.01562 14.3888 2.06855 14.4005 2.14304C14.7533 4.48535 14.963 4.69508 17.3054 5.0479C17.3779 5.05966 17.4328 5.12043 17.4328 5.19491C17.4328 5.26743 17.3799 5.33015 17.3054 5.34192C14.9533 5.69669 14.7513 5.90643 14.3946 8.27814C14.3868 8.33303 14.3397 8.3742 14.2849 8.3742Z",
                fill: "currentColor"
            }, null, -1), o("path", {
                d: "M14.2729 14.2957C14.2376 14.2957 14.2082 14.2702 14.2023 14.2348C13.9749 12.7255 13.8083 12.5923 12.3108 12.3669C12.2638 12.359 12.2305 12.3198 12.2305 12.2728C12.2305 12.2257 12.2638 12.1865 12.3108 12.1787C13.8004 11.9532 13.9338 11.82 14.1591 10.3303C14.167 10.2833 14.2062 10.25 14.2533 10.25C14.3002 10.25 14.3395 10.2833 14.3473 10.3303C14.5728 11.82 14.706 11.9532 16.1957 12.1787C16.2428 12.1865 16.2761 12.2257 16.2761 12.2728C16.2761 12.3198 16.2428 12.359 16.1957 12.3669C14.6983 12.5923 14.5708 12.7255 14.3435 14.2348C14.3376 14.2702 14.3081 14.2957 14.2729 14.2957Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    pa = {
        class: "dropdown-content"
    },
    fa = S(f({
        __name: "AddOptionsDropdown",
        props: {
            isVisible: {
                type: Boolean
            },
            isDogfoodUser: {
                type: Boolean
            }
        },
        emits: ["addText", "addImage", "doodle", "copyTemplate", "close"],
        setup(e, {
            emit: t
        }) {
            const i = e,
                l = t,
                r = Ye(),
                d = s((() => r.activeMode === De.DOODLE));

            function c() {
                U("Add text option clicked"), l("addText")
            }

            function u() {
                U("Add image option clicked"), l("addImage"), l("close")
            }

            function g() {
                U("Doodle option clicked"), l("doodle")
            }

            function h() {
                U("Copy template option clicked"), l("copyTemplate"), l("close")
            }

            function v(e) {
                if (i.isVisible) switch (e.key.toLowerCase()) {
                    case "t":
                        e.preventDefault(), c();
                        break;
                    case "i":
                        e.preventDefault(), u();
                        break;
                    case "d":
                        e.preventDefault(), g();
                        break;
                    case "escape":
                        e.preventDefault(), l("close")
                }
            }
            return "undefined" != typeof window && window.addEventListener("keydown", v), I((() => {
                "undefined" != typeof window && window.removeEventListener("keydown", v)
            })), (e, t) => e.isVisible && !d.value ? (n(), a("div", {
                key: 0,
                class: "add-options-dropdown",
                onClick: t[0] || (t[0] = D((() => {}), ["stop"]))
            }, [o("div", pa, [o("button", {
                class: "option-btn",
                onClick: c
            }, t[1] || (t[1] = [o("div", {
                class: "option-icon"
            }, [o("svg", {
                width: "16",
                height: "16",
                viewBox: "0 0 16 16",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg"
            }, [o("path", {
                d: "M3 3H13V5H11V13H9V5H7V13H5V5H3V3Z",
                fill: "currentColor"
            })])], -1), o("span", {
                class: "option-text"
            }, "Add Text", -1)])), o("button", {
                class: "option-btn",
                onClick: u
            }, t[2] || (t[2] = [o("div", {
                class: "option-icon"
            }, [o("svg", {
                width: "16",
                height: "16",
                viewBox: "0 0 16 16",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg"
            }, [o("path", {
                d: "M2 3C2 2.44772 2.44772 2 3 2H13C13.5523 2 14 2.44772 14 3V13C14 13.5523 13.5523 14 13 14H3C2.44772 14 2 13.5523 2 13V3ZM4 4V12H12V8.5L10.5 7L7.5 10L6 8.5L4 10.5V4ZM6.5 7C7.05228 7 7.5 6.55228 7.5 6C7.5 5.44772 7.05228 5 6.5 5C5.94772 5 5.5 5.44772 5.5 6C5.5 6.55228 5.94772 7 6.5 7Z",
                fill: "currentColor"
            })])], -1), o("span", {
                class: "option-text"
            }, "Add Image", -1)])), o("button", {
                class: "option-btn",
                onClick: g
            }, t[3] || (t[3] = [o("div", {
                class: "option-icon"
            }, [o("svg", {
                width: "16",
                height: "16",
                viewBox: "0 0 16 16",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg"
            }, [o("path", {
                d: "M13.5 1.5L14.5 2.5L4.5 12.5L3.5 13.5L1 14L1.5 11.5L2.5 10.5L12.5 0.5L13.5 1.5ZM3 11.5L3.5 11L4 11.5L3.5 12L3 11.5ZM13 2L14 3L13 4L12 3L13 2ZM11.5 3.5L4.5 10.5L5.5 11.5L12.5 4.5L11.5 3.5Z",
                fill: "currentColor"
            })])], -1), o("span", {
                class: "option-text"
            }, "Doodle", -1)])), e.isDogfoodUser ? (n(), a("button", {
                key: 0,
                class: "option-btn",
                onClick: h
            }, t[4] || (t[4] = [o("div", {
                class: "option-icon"
            }, [o("svg", {
                width: "16",
                height: "16",
                viewBox: "0 0 16 16",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg"
            }, [o("path", {
                d: "M6.66667 6.66667V5.83333C6.66667 4.26667 7.93333 3 9.5 3H14.1667C15.7333 3 17 4.26667 17 5.83333V10.5C17 12.0667 15.7333 13.3333 14.1667 13.3333H13.3333M5.83333 7.5H10.5C12.0667 7.5 13.3333 8.76667 13.3333 10.3333V15C13.3333 16.5667 12.0667 17.8333 10.5 17.8333H5.83333C4.26667 17.8333 3 16.5667 3 15V10.3333C3 8.76667 4.26667 7.5 5.83333 7.5Z",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            })])], -1), o("span", {
                class: "option-text"
            }, "Copy Template", -1)]))) : b("", !0)])])) : b("", !0)
        }
    }), [
        ["__scopeId", "data-v-0fff0c82"]
    ]),
    ma = {
        class: "size-button-wrapper"
    },
    wa = {
        key: 0,
        class: "size-dropdown"
    },
    ya = ["onClick"],
    xa = ["innerHTML"],
    Ia = {
        class: "size-label"
    },
    ka = S(f({
        __name: "SizeOptionsDropdown",
        emits: ["size-change"],
        setup(e, {
            emit: t
        }) {
            const i = t,
                l = r(),
                s = r(!1),
                d = [{
                    key: "square",
                    width: 1024,
                    height: 1024,
                    label: "Square",
                    icon: '<rect x="6" y="6" width="12" height="12" rx="1" stroke="currentColor" stroke-width="1.5" fill="none"/>'
                }, {
                    key: "wide",
                    width: 1536,
                    height: 1024,
                    label: "Wide",
                    icon: '<rect x="4" y="8" width="16" height="8" rx="1" stroke="currentColor" stroke-width="1.5" fill="none"/>'
                }, {
                    key: "tall",
                    width: 1024,
                    height: 1536,
                    label: "Tall",
                    icon: '<rect x="8" y="4" width="8" height="16" rx="1" stroke="currentColor" stroke-width="1.5" fill="none"/>'
                }];

            function c() {
                s.value = !s.value
            }

            function u(e) {
                var t;
                s.value && l.value && !(null == (t = l.value.parentElement) ? void 0 : t.contains(e.target)) && (s.value = !1)
            }
            return "undefined" != typeof window && (window.addEventListener("click", u), I((() => {
                window.removeEventListener("click", u)
            }))), (e, t) => (n(), a("div", ma, [o("button", {
                onClick: c,
                class: N(["menu-btn size-btn", {
                    active: s.value
                }]),
                ref_key: "sizeButton",
                ref: l
            }, t[0] || (t[0] = [o("span", {
                class: "btn-text"
            }, "Change Size", -1), o("svg", {
                class: "dropdown-arrow",
                width: "12",
                height: "12",
                viewBox: "0 0 12 12",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg"
            }, [o("path", {
                d: "M3 4.5L6 7.5L9 4.5",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            })], -1)]), 2), s.value ? (n(), a("div", wa, [(n(), a(m, null, w(d, (e => o("button", {
                key: e.key,
                onClick: t => function(e) {
                    U("Size option selected:", e), i("size-change", e), s.value = !1
                }(e),
                class: "size-option"
            }, [(n(), a("svg", {
                class: "size-icon",
                width: "20",
                height: "20",
                viewBox: "0 0 24 24",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg",
                innerHTML: e.icon
            }, null, 8, xa)), o("span", Ia, M(e.label), 1)], 8, ya))), 64))])) : b("", !0)]))
        }
    }), [
        ["__scopeId", "data-v-013f5c9a"]
    ]),
    Ca = {
        key: 3,
        class: "menu-container"
    },
    ba = {
        class: "add-button-wrapper"
    },
    Ea = ["disabled"],
    Ma = {
        key: 0,
        class: "loading-spinner"
    },
    Sa = {
        class: "btn-text"
    },
    _a = ["disabled"],
    Na = {
        key: 0,
        class: "loading-spinner"
    },
    Ta = {
        class: "btn-text"
    },
    Da = S(f({
        __name: "PageFloatingMenu",
        props: {
            isVisible: {
                type: Boolean
            },
            position: {},
            isFormEditActive: {
                type: Boolean
            },
            isAdvancedEditActive: {
                type: Boolean
            },
            isExportActive: {
                type: Boolean
            },
            isExportLoading: {
                type: Boolean
            },
            isRemixLoading: {
                type: Boolean
            },
            selectedChildNodeId: {}
        },
        emits: ["formEdit", "advancedEdit", "askWithMask", "export", "remix", "addText", "addImage", "doodle", "closeForm", "deleteChild", "duplicate"],
        setup(e, {
            emit: t
        }) {
            const i = e,
                l = t,
                d = Ye(),
                c = Ue(),
                u = Re(),
                v = ee(),
                p = O("currentUser"),
                f = s((() => {
                    var e;
                    return (null == (e = null == p ? void 0 : p.value) ? void 0 : e.gk_dogfood) || !1
                })),
                w = r(!1),
                k = r(),
                E = r(!1),
                S = r(!1),
                T = s((() => "frame" === d.selectedPageNodeType)),
                D = s((() => "image" === d.selectedPageNodeType)),
                P = s((() => d.currentToolId === _e.RECTANGLE_SELECT)),
                L = s((() => !!u.selection)),
                R = s((() => d.activeMode === De.TOOL && d.currentToolId === _e.FAST_EDIT_BY_TEXT)),
                B = s((() => {
                    if (d.selectedPageNodeId) {
                        const e = d.getNodeById(d.selectedPageNodeId);
                        if (!e) return !1;
                        if ("image" === e.type) {
                            return !!e.src
                        }
                        if ("frame" === e.type) return !0
                    } else if (d.selectedChildNodeId) return !0;
                    return !1
                })),
                z = s((() => {
                    var e;
                    if (!d.selectedPageNodeId) return !1;
                    const t = d.getNodeById(d.selectedPageNodeId);
                    if (!t) return !1;
                    if ("image" === t.type || "frame" === t.type) {
                        const a = t;
                        return (null == (e = a.rawData) ? void 0 : e.children) && a.rawData.children.length > 0
                    }
                    return !1
                })),
                H = s((() => {
                    var e;
                    if (!d.selectedPageNodeId || !z.value) return !1;
                    const t = d.getNodeById(d.selectedPageNodeId);
                    if (!t) return !1;
                    return ((null == (e = t.rawData) ? void 0 : e.children) || []).some((e => {
                        var t, a, n, o;
                        return !!(null == (t = e.extra_info) ? void 0 : t.uploading) || (!(!(null == (n = null == (a = e.extra_info) ? void 0 : a.task_status) ? void 0 : n.id) || ["SUCCESS", "FAILURE", "NSFW", "-2", "-3"].includes(e.extra_info.task_status.status) || "task not found" == (null == (o = e.extra_info.task_status) ? void 0 : o.message)) || "image" === e.type && !e.src)
                    }))
                })),
                j = s((() => P.value && L.value && u.interactionMode === Le.NONE)),
                W = async e => {
                    if (!e.trim() || w.value || !B.value) return;
                    if (U("handleFastEditApply called with prompt:", e), !d.selectedPageImageId) return void $("No image selected for fast edit");
                    const t = d.getNodeById(d.selectedPageImageId);
                    if (!t || "image" !== t.type) return void $("Selected node is not an image");
                    const a = t,
                        n = a.width || 1024,
                        o = a.height || 1024;
                    w.value = !0;
                    try {
                        const t = await g.getRecaptchaToken("image_studio"),
                            a = await Jt({
                                operation: "fast_edit_by_text",
                                data: {
                                    g_recaptcha_token: t,
                                    width: n,
                                    height: o
                                },
                                prompt: e
                            }, d);
                        a.success ? (U("Fast edit by text completed successfully", a), u.editInputValue = "", d.currentToolId === _e.FAST_EDIT_BY_TEXT && a.newNodeId && (U("Auto-selecting newly created fast_edit_by_text node:", a.newNodeId), d.selectNode(a.newNodeId), d.setActiveMode(De.TOOL), d.updateToolId(_e.FAST_EDIT_BY_TEXT))) : ($("Fast edit by text failed:", a.message), v.warning(`Fast edit failed: ${a.message}`))
                    } catch (i) {
                        $("Error calling fast edit by text API:", i), v.warning("Failed to process fast edit request")
                    } finally {
                        w.value = !1
                    }
                };

            function X() {
                d.activeMode === De.ADD_SUB_ELEMENTS ? d.setActiveMode(De.NONE) : (d.setActiveMode(De.ADD_SUB_ELEMENTS), l("closeForm"))
            }

            function V() {
                U("Add text requested from floating menu"), l("addText")
            }

            function G() {
                U("Add image requested from floating menu"), l("addImage"), d.setActiveMode(De.NONE)
            }

            function Y() {
                U("Doodle requested from floating menu"), l("doodle")
            }

            function Z() {
                U("Blend button clicked, showing input"), U("Current state before blend:", {
                    isBlendEditActive: E.value,
                    isFastEditByTextActive: R.value,
                    isMaskEditByTextActive: j.value,
                    activeMode: d.activeMode,
                    currentToolId: d.currentToolId,
                    isVisible: i.isVisible,
                    isSelectedNodeReady: B.value
                }), E.value = !0, U("isBlendEditActive after setting to true:", E.value), h((() => {
                    U("After nextTick - isBlendEditActive:", E.value), U("Template visibility check:", {
                        isVisible: i.isVisible,
                        isSelectedNodeReady: B.value,
                        templateShouldShow: i.isVisible && B.value
                    })
                }))
            }
            const q = async e => {
                U("handleBlendApply called with prompt:", e), S.value = !0;
                try {
                    e && e.trim() ? l("remix", e.trim()) : l("remix"), E.value = !1
                } catch (t) {
                    $("Error in blend apply:", t)
                } finally {
                    S.value = !1
                }
            };

            function J(e) {
                if (!d.selectedPageNodeId) return;
                const t = d.getNodeById(d.selectedPageNodeId);
                if (!t || "frame" !== t.type) return;
                U("Changing frame size:", {
                    nodeId: d.selectedPageNodeId,
                    newSize: e
                });
                const a = {
                    width: e.width,
                    height: e.height
                };
                d.updateNodeData(d.selectedPageNodeId, a), c.onNodeChanged(d.selectedPageNodeId)
            }

            function K() {
                if (!d.selectedPageNodeId) return;
                const e = d.getNodeById(d.selectedPageNodeId);
                e && "frame" === e.type && (U("Duplicating frame:", {
                    nodeId: d.selectedPageNodeId
                }), l("duplicate"))
            }
            const Q = async () => {
                    if (!d.selectedPageNodeId) return void v.warning("Please select a node first");
                    const e = d.getNodeById(d.selectedPageNodeId);
                    if (!e || e.type !== pe.IMAGE && e.type !== pe.FRAME) return void v.warning("Selected node is not an image or frame");
                    let t = { ...e.rawData
                    };
                    if (e.type === pe.FRAME) try {
                        v.info("Generating frame preview...");
                        const a = await te(e);
                        if (!a) return void v.error("Failed to generate frame preview");
                        const n = await ae(a);
                        if (!n) return void v.error("Failed to upload frame preview");
                        t.preview_image_url = n, window.open(n, "_blank"), v.success("Frame preview generated and opened")
                    } catch (a) {
                        return $("Error generating frame preview:", a), void v.error("Failed to generate frame preview")
                    }
                    try {
                        const e = JSON.stringify(t, null, 2);
                        await navigator.clipboard.writeText(e), v.info("Template copied to clipboard")
                    } catch (a) {
                        $("Failed to copy to clipboard:", a), v.error("Failed to copy template to clipboard")
                    }
                },
                te = async e => {
                    const t = e.rawData;
                    return new Promise(((e, a) => {
                        try {
                            const n = document.createElement("canvas"),
                                o = n.getContext("2d");
                            if (!o) return $("Failed to get canvas context for frame preview"), void a(new Error("Failed to get canvas context"));
                            n.width = t.width || 1024, n.height = t.height || 1024, o.fillStyle = t.background_color || "#FFFFFF", o.fillRect(0, 0, n.width, n.height);
                            (async () => {
                                if (t.children && t.children.length > 0) {
                                    U("Processing children for frame preview");
                                    for (const e of t.children) try {
                                        if (e.type === pe.IMAGE && e.src) {
                                            const t = new Image;
                                            t.crossOrigin = "anonymous", await new Promise((a => {
                                                t.onload = () => {
                                                    const n = e.left || 0,
                                                        i = e.top || 0,
                                                        l = e.width || t.width,
                                                        r = e.height || t.height;
                                                    o.drawImage(t, n, i, l, r), a()
                                                }, t.onerror = () => {
                                                    $("Failed to load child image:", e.src), a()
                                                }, t.src = e.src
                                            }))
                                        } else if (e.type === pe.TEXT) {
                                            const t = e.text || "",
                                                a = e.fontSize || 16,
                                                n = e.fontFamily || "Arial",
                                                i = e.color || "#000000",
                                                l = e.left || 0,
                                                r = (e.top || 0) + a;
                                            o.font = `${a}px ${n}`, o.fillStyle = i, o.fillText(t, l, r)
                                        }
                                    } catch (i) {
                                        $("Error processing child:", i)
                                    }
                                }
                                n.toBlob((t => {
                                    t ? e(t) : a(new Error("Failed to create blob from canvas"))
                                }), "image/png")
                            })()
                        } catch (n) {
                            $("Error in generateFramePreview:", n), a(n)
                        }
                    }))
                },
                ae = async e => {
                    try {
                        const t = await fetch("/api/get_upload_url");
                        if (!t.ok) throw new Error(`HTTP error! status: ${t.status}`);
                        const a = await t.json();
                        if (!a || 0 !== a.status) throw new Error(`API error! code: ${(null==a?void 0:a.code)||"unknown"}`);
                        if (!a.data) throw new Error("API response missing data object");
                        const n = a.data.upload_image_url,
                            o = a.data.page_url,
                            i = await fetch(n, {
                                method: "PUT",
                                body: e,
                                headers: {
                                    "Content-Type": "image/png",
                                    "x-ms-blob-type": "BlockBlob"
                                }
                            });
                        if (!i.ok) throw new Error(`Upload failed! status: ${i.status}`);
                        return U("Frame preview uploaded successfully:", o), o
                    } catch (t) {
                        return $("Error uploading preview image:", t), null
                    }
                };

            function ne(e) {
                var t;
                if (U("handleClickOutside triggered", {
                        target: e.target,
                        isBlendEditActive: E.value
                    }), d.activeMode === De.ADD_SUB_ELEMENTS && k.value && !k.value.contains(e.target) && d.setActiveMode(De.NONE), E.value) {
                    const a = e.target.closest(".floating-menu"),
                        n = e.target.closest("button[data-blend-button]") || (null == (t = e.target.textContent) ? void 0 : t.includes("Blend"));
                    U("Blend outside click check", {
                        hasFloatingMenu: !!a,
                        isBlendButton: !!n,
                        targetText: e.target.textContent,
                        willClose: !a && !n
                    }), a || n || (U("Closing blend edit because click was outside floating menu"), E.value = !1)
                }
            }
            return "undefined" != typeof window && (window.addEventListener("click", ne), I((() => {
                window.removeEventListener("click", ne)
            }))), (e, t) => (n(), x(F, {
                name: "fade"
            }, {
                default: _((() => [e.isVisible && B.value ? (n(), a("div", {
                    key: 0,
                    class: N(["floating-menu", {
                        "only-button-group": !R.value && !j.value && !E.value
                    }]),
                    style: C({
                        left: `${e.position.x}px`,
                        top: `${e.position.y}px`
                    })
                }, [E.value ? (n(), x(ua, {
                    key: 0,
                    placeholder: "Describe how to blend (optional)...",
                    "is-processing": S.value,
                    "is-ready": B.value,
                    "ready-state-message": "Image is not ready",
                    "show-mask-preview": !1,
                    "auto-focus": !0,
                    "clear-on-submit": !1,
                    "disable-input-during-processing": !0,
                    "allow-empty-submit": !0,
                    onSubmit: q
                }, null, 8, ["is-processing", "is-ready"])) : R.value ? (n(), x(ua, {
                    key: 1,
                    placeholder: "Ask Genspark to draft or improve...",
                    "is-processing": w.value,
                    "is-ready": B.value,
                    "ready-state-message": "Image is not ready",
                    "show-mask-preview": !1,
                    "auto-focus": !0,
                    "clear-on-submit": !1,
                    "disable-input-during-processing": !0,
                    onSubmit: W
                }, null, 8, ["is-processing", "is-ready"])) : j.value ? (n(), x(ga, {
                    key: 2,
                    onAskWithMask: t[0] || (t[0] = e => l("askWithMask", e))
                })) : (n(), a("div", Ca, [e.selectedChildNodeId ? (n(), a("button", {
                    key: 0,
                    onClick: t[1] || (t[1] = e => l("deleteChild")),
                    class: "menu-btn delete-btn"
                }, t[6] || (t[6] = [o("svg", {
                    class: "menu-icon",
                    width: "20",
                    height: "20",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, [o("path", {
                    d: "M8.33333 5V4.16667C8.33333 3.24619 9.07953 2.5 10 2.5C10.9205 2.5 11.6667 3.24619 11.6667 4.16667V5M5 5H15M14.1667 5L13.75 14.1667C13.75 15.0871 13.0038 15.8333 12.0833 15.8333H7.91667C6.99619 15.8333 6.25 15.0871 6.25 14.1667L5.83333 5",
                    stroke: "currentColor",
                    "stroke-width": "1.5",
                    "stroke-linecap": "round",
                    "stroke-linejoin": "round"
                })], -1), o("span", {
                    class: "btn-text"
                }, "Delete", -1)]))) : (n(), a(m, {
                    key: 1
                }, [T.value ? (n(), a(m, {
                    key: 0
                }, [A(ka, {
                    onSizeChange: J
                }), t[8] || (t[8] = o("div", {
                    class: "separator"
                }, null, -1)), o("button", {
                    onClick: K,
                    class: "menu-btn"
                }, t[7] || (t[7] = [o("svg", {
                    class: "menu-icon",
                    width: "20",
                    height: "20",
                    viewBox: "0 0 20 20",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, [o("path", {
                    d: "M6.66667 6.66667V5.83333C6.66667 4.26667 7.93333 3 9.5 3H14.1667C15.7333 3 17 4.26667 17 5.83333V10.5C17 12.0667 15.7333 13.3333 14.1667 13.3333H13.3333M5.83333 7.5H10.5C12.0667 7.5 13.3333 8.76667 13.3333 10.3333V15C13.3333 16.5667 12.0667 17.8333 10.5 17.8333H5.83333C4.26667 17.8333 3 16.5667 3 15V10.3333C3 8.76667 4.26667 7.5 5.83333 7.5Z",
                    stroke: "currentColor",
                    "stroke-width": "1.5",
                    "stroke-linecap": "round",
                    "stroke-linejoin": "round"
                })], -1), o("span", {
                    class: "btn-text"
                }, "Duplicate", -1)])), t[9] || (t[9] = o("div", {
                    class: "separator"
                }, null, -1))], 64)) : b("", !0), D.value ? (n(), a(m, {
                    key: 1
                }, [o("button", {
                    onClick: t[2] || (t[2] = e => l("formEdit")),
                    class: N(["menu-btn", {
                        active: e.isFormEditActive
                    }])
                }, [A(y(va), {
                    class: "menu-icon"
                }), t[10] || (t[10] = o("span", {
                    class: "btn-text"
                }, "AI edit", -1))], 2), t[12] || (t[12] = o("div", {
                    class: "separator"
                }, null, -1)), o("button", {
                    onClick: t[3] || (t[3] = e => l("advancedEdit")),
                    class: N(["menu-btn", {
                        active: e.isAdvancedEditActive
                    }])
                }, t[11] || (t[11] = [o("span", {
                    class: "btn-text"
                }, "Quick edit", -1)]), 2), t[13] || (t[13] = o("div", {
                    class: "separator"
                }, null, -1))], 64)) : b("", !0), o("div", ba, [o("button", {
                    onClick: X,
                    class: N(["menu-btn add-btn", {
                        active: y(d).activeMode === y(De).ADD_SUB_ELEMENTS
                    }]),
                    ref_key: "addButton",
                    ref: k
                }, t[14] || (t[14] = [o("svg", {
                    width: "14",
                    height: "14",
                    viewBox: "0 0 14 14",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, [o("path", {
                    d: "M7 0C7.41421 2.71588e-08 7.75 0.335787 7.75 0.75V6.25H13.25C13.6642 6.25 14 6.58579 14 7C14 7.41421 13.6642 7.75 13.25 7.75H7.75V13.25C7.75 13.6642 7.41421 14 7 14C6.58579 14 6.25 13.6642 6.25 13.25V7.75H0.75C0.335786 7.75 0 7.41421 0 7C0 6.58579 0.335786 6.25 0.75 6.25H6.25V0.75C6.25 0.335787 6.58579 2.73333e-07 7 0Z",
                    fill: "#232425"
                })], -1), o("span", {
                    class: "btn-text"
                }, "Add", -1), o("svg", {
                    class: "dropdown-arrow",
                    width: "12",
                    height: "12",
                    viewBox: "0 0 12 12",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, [o("path", {
                    d: "M3 4.5L6 7.5L9 4.5",
                    stroke: "currentColor",
                    "stroke-width": "1.5",
                    "stroke-linecap": "round",
                    "stroke-linejoin": "round"
                })], -1)]), 2), A(fa, {
                    "is-visible": y(d).activeMode === y(De).ADD_SUB_ELEMENTS,
                    "is-dogfood-user": f.value,
                    onAddText: V,
                    onAddImage: G,
                    onDoodle: Y,
                    onCopyTemplate: Q,
                    onClose: t[4] || (t[4] = e => y(d).setActiveMode(y(De).NONE))
                }, null, 8, ["is-visible", "is-dogfood-user"])]), t[16] || (t[16] = o("div", {
                    class: "separator"
                }, null, -1)), o("button", {
                    onClick: t[5] || (t[5] = e => l("export")),
                    class: N(["menu-btn", {
                        active: e.isExportActive,
                        loading: e.isExportLoading
                    }]),
                    disabled: e.isExportLoading
                }, [e.isExportLoading ? (n(), a("div", Ma)) : b("", !0), o("span", Sa, M(e.isExportLoading ? "Exporting..." : "Export"), 1)], 10, Ea), z.value ? (n(), a(m, {
                    key: 2
                }, [t[15] || (t[15] = o("div", {
                    class: "separator"
                }, null, -1)), o("button", {
                    onClick: Z,
                    class: N(["menu-btn", {
                        loading: e.isRemixLoading || H.value
                    }]),
                    disabled: e.isRemixLoading || H.value
                }, [e.isRemixLoading ? (n(), a("div", Na)) : b("", !0), o("span", Ta, M(e.isRemixLoading ? "Blending..." : H.value ? "Uploading..." : "Blend"), 1)], 10, _a)], 64)) : b("", !0)], 64))]))], 6)) : b("", !0)])),
                _: 1
            }))
        }
    }), [
        ["__scopeId", "data-v-a75ae037"]
    ]),
    Pa = {
        class: "toolbar-container"
    },
    Fa = {
        class: "toolbar-content"
    },
    La = ["onClick"],
    Aa = {
        class: "tool-label"
    },
    Ra = {
        key: 0,
        class: "toolbar-separator horizontal"
    },
    Ba = S(f({
        __name: "PageImageToolbar",
        setup(e) {
            const t = Ye(),
                i = Xe(),
                l = ee(),
                d = O("currentUser"),
                c = r({
                    x: 0,
                    y: 0
                }),
                u = r(!1),
                g = s((() => {
                    var e;
                    const t = d;
                    return function(e = !1) {
                        return e ? [...Ne, ...Te] : Ne
                    }((null == (e = null == t ? void 0 : t.value) ? void 0 : e.gk_dogfood) || (null == t ? void 0 : t.gk_dogfood) || !1)
                }));
            p([() => t.canvasScale, () => t.canvasTranslateX, () => t.canvasTranslateY, () => t.selectedPageImageId, () => t.isResizingImage, () => t.isRotatingImage, () => i.isDragging, () => {
                var e, a;
                return t.selectedPageImageId ? null == (a = null == (e = t.getNodeById(t.selectedPageImageId)) ? void 0 : e.rawData) ? void 0 : a.rotation : null
            }], (() => {
                (() => {
                    if (!t.selectedPageImageId) return void(u.value = !1);
                    const e = t.getNodeById(t.selectedPageImageId);
                    if (!e || e.type !== pe.IMAGE) return void(u.value = !1);
                    const a = e.rawData,
                        n = a.rotation || 0,
                        o = a.left + a.width / 2,
                        i = a.top + a.height / 2;
                    if (0 === n) {
                        const e = a.left * t.canvasScale + t.canvasTranslateX,
                            n = a.top * t.canvasScale + t.canvasTranslateY,
                            o = e + a.width * t.canvasScale + 12,
                            i = n + a.height * t.canvasScale / 2 - 9;
                        c.value = {
                            x: o,
                            y: i
                        }, u.value = !0
                    } else {
                        const e = a.width / 2,
                            l = a.height / 2,
                            r = n * Math.PI / 180,
                            s = [{
                                x: -e,
                                y: -l
                            }, {
                                x: e,
                                y: -l
                            }, {
                                x: e,
                                y: l
                            }, {
                                x: -e,
                                y: l
                            }].map((e => ({
                                x: o + e.x * Math.cos(r) - e.y * Math.sin(r),
                                y: i + e.x * Math.sin(r) + e.y * Math.cos(r)
                            }))),
                            d = Math.max(...s.map((e => e.x))),
                            g = (Math.min(...s.map((e => e.y))) + Math.max(...s.map((e => e.y)))) / 2,
                            h = d * t.canvasScale + t.canvasTranslateX + 12,
                            v = g * t.canvasScale + t.canvasTranslateY - 9;
                        c.value = {
                            x: h,
                            y: v
                        }, u.value = !0
                    }
                })()
            }), {
                immediate: !0
            });
            return (e, i) => (n(), x(F, {
                name: "toolbar-slide"
            }, {
                default: _((() => [y(t).selectedPageImageId && y(t).activeMode === y(De).TOOL && u.value && !y(t).isOperatingImage ? (n(), a("div", {
                    key: 0,
                    class: "image-toolbar",
                    style: C({
                        left: `${c.value.x}px`,
                        top: `${c.value.y}px`
                    })
                }, [o("div", Pa, [o("div", Fa, [(n(!0), a(m, null, w(g.value, ((e, i) => (n(), a("div", {
                    key: e.id,
                    class: "tooltip-container"
                }, [o("button", {
                    class: N(["tool-button", {
                        active: "tool" === y(t).activeMode && y(t).currentToolId === e.id
                    }]),
                    onClick: () => (e => {
                        if (t.currentToolId === e && t.activeMode === De.TOOL) t.updateToolId(null);
                        else switch (t.setActiveMode(De.TOOL), t.updateToolId(e), e) {
                            case _e.BRUSH_SELECT:
                            case _e.OBJECT_SELECT:
                                l.info("TODO: " + e);
                                break;
                            case _e.OUTPAINT:
                                t.selectedPageImageId || (l.warning("Please select an image first"), t.updateToolId(null))
                        }
                    })(e.id)
                }, [(n(), x(T(e.svg), {
                    class: "tool-icon"
                })), o("span", Aa, M(e.label), 1)], 10, La), (i + 1) % 2 == 0 && i < g.value.length - 1 ? (n(), a("div", Ra)) : b("", !0)])))), 128)), i[0] || (i[0] = o("div", {
                    class: "toolbar-separator vertical"
                }, null, -1))])])], 4)) : b("", !0)])),
                _: 1
            }))
        }
    }), [
        ["__scopeId", "data-v-f96b886d"]
    ]),
    za = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Oa = {
        render: function(e, t) {
            return n(), a("svg", za, t[0] || (t[0] = [o("path", {
                d: "M2.49992 2.5L18.3333 8.39096L9.99992 10.0035L7.91442 18.3333L2.49992 2.5Z",
                stroke: "currentColor",
                "stroke-width": "1.25",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    $a = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Ua = {
        render: function(e, t) {
            return n(), a("svg", $a, t[0] || (t[0] = [o("path", {
                d: "M14.6875 3.74986C14.3631 3.7494 14.0427 3.82175 13.75 3.96158V3.43736C13.7502 2.91599 13.5641 2.4117 13.2254 2.01535C12.8867 1.619 12.4176 1.35667 11.9025 1.27562C11.3875 1.19457 10.8605 1.30014 10.4164 1.5733C9.97229 1.84646 9.64038 2.26925 9.48044 2.76548C9.14732 2.5848 8.77297 2.49375 8.39407 2.50127C8.01518 2.50879 7.64473 2.61461 7.31904 2.80837C6.99335 3.00214 6.72358 3.27719 6.53617 3.60657C6.34876 3.93596 6.25014 4.30839 6.24997 4.68736V9.68736L5.95154 9.20845C5.66166 8.70821 5.18554 8.34307 4.62724 8.19284C4.06894 8.04261 3.47387 8.11952 2.97211 8.40674C2.47034 8.69397 2.10269 9.16815 1.94951 9.72565C1.79634 10.2831 1.87009 10.8786 2.15466 11.3819C3.40466 14.0202 4.41482 15.8913 5.57029 17.0608C6.73747 18.2452 8.06247 18.7499 9.99997 18.7499C11.8227 18.7478 13.5702 18.0228 14.859 16.7339C16.1479 15.4451 16.8729 13.6976 16.875 11.8749V5.93736C16.875 5.3572 16.6445 4.8008 16.2343 4.39056C15.824 3.98033 15.2676 3.74986 14.6875 3.74986ZM15.625 11.8749C15.6233 13.3662 15.0302 14.796 13.9756 15.8505C12.9211 16.905 11.4913 17.4982 9.99997 17.4999C8.40779 17.4999 7.38201 17.1186 6.45857 16.1835C5.42497 15.1358 4.47341 13.3592 3.27341 10.8264C3.26643 10.8109 3.2586 10.7958 3.24997 10.7811C3.12565 10.5657 3.09199 10.3098 3.15638 10.0696C3.22077 9.82936 3.37795 9.62457 3.59333 9.50025C3.80872 9.37593 4.06467 9.34226 4.30488 9.40665C4.54508 9.47105 4.74987 9.62822 4.87419 9.84361C4.87743 9.84984 4.88108 9.85584 4.88513 9.86158L6.34372 12.2053C6.41538 12.3209 6.52279 12.41 6.64966 12.4591C6.77654 12.5081 6.91595 12.5144 7.04673 12.477C7.17751 12.4396 7.29253 12.3606 7.37433 12.2519C7.45613 12.1433 7.50024 12.0109 7.49997 11.8749V4.68736C7.49997 4.43872 7.59875 4.20026 7.77456 4.02445C7.95038 3.84863 8.18883 3.74986 8.43747 3.74986C8.68611 3.74986 8.92457 3.84863 9.10039 4.02445C9.2762 4.20026 9.37497 4.43872 9.37497 4.68736V9.37486C9.37497 9.54062 9.44082 9.69959 9.55803 9.8168C9.67524 9.93401 9.83421 9.99986 9.99997 9.99986C10.1657 9.99986 10.3247 9.93401 10.4419 9.8168C10.5591 9.69959 10.625 9.54062 10.625 9.37486V3.43736C10.625 3.18872 10.7237 2.95026 10.8996 2.77445C11.0754 2.59863 11.3138 2.49986 11.5625 2.49986C11.8111 2.49986 12.0496 2.59863 12.2254 2.77445C12.4012 2.95026 12.5 3.18872 12.5 3.43736V9.37486C12.5 9.54062 12.5658 9.69959 12.683 9.8168C12.8002 9.93401 12.9592 9.99986 13.125 9.99986C13.2907 9.99986 13.4497 9.93401 13.5669 9.8168C13.6841 9.69959 13.75 9.54062 13.75 9.37486V5.93736C13.75 5.68872 13.8487 5.45026 14.0246 5.27445C14.2004 5.09863 14.4388 4.99986 14.6875 4.99986C14.9361 4.99986 15.1746 5.09863 15.3504 5.27445C15.5262 5.45026 15.625 5.68872 15.625 5.93736V11.8749Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    Ha = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const ja = {
        render: function(e, t) {
            return n(), a("svg", Ha, t[0] || (t[0] = [o("path", {
                d: "M8.75008 15.8337C12.6621 15.8337 15.8334 12.6623 15.8334 8.75033C15.8334 4.83833 12.6621 1.66699 8.75008 1.66699C4.83808 1.66699 1.66675 4.83833 1.66675 8.75033C1.66675 12.6623 4.83808 15.8337 8.75008 15.8337Z",
                stroke: "currentColor",
                "stroke-width": "1.25",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M8.75 6.25V11.25",
                stroke: "currentColor",
                "stroke-width": "1.25",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M6.25659 8.7565L11.2501 8.75",
                stroke: "currentColor",
                "stroke-width": "1.25",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M13.8423 13.8428L17.3778 17.3783",
                stroke: "currentColor",
                "stroke-width": "1.25",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    Wa = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Xa = {
        render: function(e, t) {
            return n(), a("svg", Wa, t[0] || (t[0] = [o("path", {
                d: "M8.75008 15.8337C12.6621 15.8337 15.8334 12.6623 15.8334 8.75033C15.8334 4.83833 12.6621 1.66699 8.75008 1.66699C4.83808 1.66699 1.66675 4.83833 1.66675 8.75033C1.66675 12.6623 4.83808 15.8337 8.75008 15.8337Z",
                stroke: "currentColor",
                "stroke-width": "1.25",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M6.25 8.75H11.25",
                stroke: "currentColor",
                "stroke-width": "1.25",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M13.8423 13.8428L17.3778 17.3783",
                stroke: "currentColor",
                "stroke-width": "1.25",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    Va = {
        class: "canvas-top-toolbar"
    },
    Ga = S(f({
        __name: "CanvasTopToolbar",
        setup(e) {
            const t = Ye(),
                i = Ue(),
                l = s((() => t.isLeftMouseDragMode ? "hand" : "select")),
                r = () => {
                    t.isLeftMouseDragMode = !1, t.selectedNodeId && t.clearNodeSelection()
                },
                d = () => {
                    t.isLeftMouseDragMode = !0, t.selectedNodeId && t.clearNodeSelection()
                },
                c = () => {
                    const e = document.querySelector(".j-viewport");
                    return e ? {
                        centerX: e.clientWidth / 2,
                        centerY: e.clientHeight / 2
                    } : {
                        centerX: window.innerWidth / 2,
                        centerY: window.innerHeight / 2
                    }
                },
                u = () => {
                    const e = 1.2 * t.canvasScale,
                        {
                            centerX: a,
                            centerY: n
                        } = c();
                    t.zoomCanvasAtPoint(e, a, n)
                },
                v = () => {
                    const e = t.canvasScale / 1.2,
                        {
                            centerX: a,
                            centerY: n
                        } = c();
                    t.zoomCanvasAtPoint(e, a, n)
                },
                p = {
                    width: 1024,
                    height: 1024,
                    label: "Square"
                },
                f = {
                    width: 1536,
                    height: 1024,
                    label: "Wide"
                },
                m = {
                    width: 1024,
                    height: 1536,
                    label: "Tall"
                },
                w = async () => {
                    const e = (() => {
                            var e;
                            if (!(null == (e = t.rootNode) ? void 0 : e.rawData.children) || 0 === t.rootNode.rawData.children.length) return p;
                            const a = t.rootNode.rawData.children,
                                n = a[a.length - 1];
                            if (n.width && n.height) {
                                const e = n.width,
                                    t = n.height;
                                if (1024 === e && 1024 === t) return p;
                                if (1536 === e && 1024 === t) return f;
                                if (1024 === e && 1536 === t) return m
                            }
                            return p
                        })(),
                        a = (() => {
                            var e;
                            if (!(null == (e = t.rootNode) ? void 0 : e.rawData.children) || 0 === t.rootNode.rawData.children.length) return {
                                left: 0,
                                top: 0
                            };
                            let a = 0;
                            for (const n of t.rootNode.rawData.children) {
                                const e = (n.top || 0) + (n.height || 0);
                                a = Math.max(a, e)
                            }
                            return {
                                left: 0,
                                top: a + 140
                            }
                        })(e.width, e.height),
                        n = g.uuid(),
                        o = {
                            id: n,
                            type: pe.FRAME,
                            left: a.left,
                            top: a.top,
                            width: e.width,
                            height: e.height,
                            background_color: "#FFFFFF",
                            children: []
                        };
                    U("Adding new Frame node:", o), t.addNodeToCanvas(o), await h(), i.onNodeChanged(n), t.selectNode(n)
                };
            return (e, t) => (n(), a("div", Va, [o("button", {
                class: N(["toolbar-button", {
                    active: "select" === l.value
                }]),
                onClick: r,
                title: "Select Mode"
            }, [A(y(Oa))], 2), o("button", {
                class: N(["toolbar-button", {
                    active: "hand" === l.value
                }]),
                onClick: d,
                title: "Hand Mode"
            }, [A(y(Ua))], 2), o("button", {
                class: "toolbar-button",
                onClick: u,
                title: "Zoom In"
            }, [A(y(ja))]), o("button", {
                class: "toolbar-button",
                onClick: v,
                title: "Zoom Out"
            }, [A(y(Xa))]), o("button", {
                class: "toolbar-button",
                onClick: w,
                title: "Add Frame"
            }, t[0] || (t[0] = [o("svg", {
                width: "20",
                height: "20",
                viewBox: "0 0 20 20",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg"
            }, [o("rect", {
                x: "3",
                y: "3",
                width: "14",
                height: "14",
                rx: "2",
                stroke: "currentColor",
                "stroke-width": "1.5",
                fill: "none"
            }), o("path", {
                d: "M10 7V13M7 10H13",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round"
            })], -1)]))]))
        }
    }), [
        ["__scopeId", "data-v-c8829781"]
    ]),
    Ya = {
        class: "canvas-container infinite-canvas-root-container j-infinite-canvas-root-container"
    },
    Za = {
        key: 0,
        src: ae,
        class: "logo-icon"
    };

function qa(e) {
    let t = e;
    try {
        const a = new URL(e);
        t = `https://page1.genspark.site${a.pathname}${a.search}${a.hash}`
    } catch (a) {}
    return t
}
const Ja = S(f({
    __name: "InfiniteCanvas",
    props: {
        project: {}
    },
    emits: ["ask"],
    setup(e, {
        emit: t
    }) {
        const i = e,
            l = t,
            d = Ye(),
            c = Re(),
            g = He(),
            v = Xe(),
            f = r(),
            E = r(null);
        j();
        const M = Ue();
        k((() => {
            p((() => {
                var e;
                return null == (e = i.project) ? void 0 : e.id
            }), (async e => {
                e && await M.setProjectId(e)
            }), {
                immediate: !0
            })
        }));
        const S = r(!1),
            _ = r(!1),
            N = r(!1),
            T = d.calculateFloatingMenuPosition;
        p([() => d.canvasScale, () => d.canvasTranslateX, () => d.canvasTranslateY, () => d.selectedPageNodeId, () => d.selectedChildNodeId], (() => {
            d.showFloatingMenu && T()
        }));
        let P = null;
        const F = () => {
                P && clearTimeout(P), P = setTimeout((() => {
                    d.showFloatingMenuAfterTransform(), (d.selectedPageNodeId || d.selectedChildNodeId) && d.showFloatingMenu && T()
                }), 200)
            },
            L = r(!1),
            R = r(null),
            B = r([]),
            z = r(!1); {
            let e = !1;
            k((() => {
                u((() => {
                    var t, a;
                    (null == (a = null == (t = i.project) ? void 0 : t.session_state) ? void 0 : a.infinite_canvas) ? e || (d.initializeTree(i.project.session_state.infinite_canvas), e = !0): (e = !1, d.clearTree())
                }))
            }))
        }
        const O = s((() => d.rootNode)),
            H = () => {
                const e = new Set;
                d.getNodesByType(pe.IMAGE).forEach((t => {
                    const a = t;
                    a.generating && e.add(a.generateTaskId)
                }));
                const t = Array.from(e);
                return U("Found unique pending tasks:", t), t
            },
            W = e => {
                U("Updating nodes with task results:", e);
                const t = d.getNodesByType(pe.IMAGE);
                z.value = !0, t.forEach((t => {
                    var a, n, o, i;
                    const l = t.rawData,
                        r = null == (n = null == (a = l.extra_info) ? void 0 : a.task_status) ? void 0 : n.id;
                    if (r && e[r]) {
                        const a = e[r];
                        if (U(`Found task result for node ${t.id}, task ${r}:`, a, l), l.extra_info && l.extra_info.task_status ? (l.extra_info.task_status = { ...l.extra_info.task_status,
                                ...a
                            }, U("task_status updated", l)) : $("!(nodeData.extra_info && nodeData.extra_info.task_status):", l), "SUCCESS" === a.status && (null == (o = a.image_urls_nowatermark) ? void 0 : o.length) > 0) {
                            const e = d.getNodesByTaskId(r).findIndex((e => e.id === t.id));
                            if (U(`Task ${r} has ${a.image_urls_nowatermark.length} images, node index: ${e}`), -1 !== e && a.image_urls_nowatermark[e]) {
                                U(`Updating node ${t.id} with image URL:`, a.image_urls_nowatermark[e]), d.updateNodeData(t.id, {
                                    src: a.image_urls_nowatermark[e]
                                });
                                const n = d.getNodeById(t.id);
                                U(`After update, node ${t.id} src is:`, null == (i = null == n ? void 0 : n.rawData) ? void 0 : i.src)
                            }
                        }
                    }
                })), h((() => {
                    z.value = !1
                }))
            };

        function X() {
            U("resetTaskMonitoring"), B.value = [], R.value = null, L.value = !1
        }
        const V = async e => {
            if (0 !== e.length) {
                B.value = [...e], R.value && R.value.abort(), R.value = new AbortController, L.value = !0;
                try {
                    await te("/api/ig_tasks_status", {
                        task_ids: B.value
                    }, (e => {
                        U("Received SSE event:", e.type, e), "TASKS_STATUS_UPDATE" === e.type ? W(e.tasks) : "TASKS_STATUS_COMPLETE" === e.type && (U("TASKS_STATUS_COMPLETE", e.final_status), W(e.final_status), X())
                    }), R.value.signal)
                } catch (t) {
                    "AbortError" === t.name ? U("task monitoring aborted:", t) : ($("Error monitoring tasks:", t), X())
                } finally {
                    U("Task monitoring finally:", {
                        isMonitoringTasks: L.value,
                        currentlyMonitoredTaskIds: B.value
                    })
                }
            } else $("No pending tasks, do nothing")
        };
        p((() => d.rootNode), (() => {
            const e = H();
            if (0 === e.length) return;
            let t = [],
                a = !1;
            L.value ? (t = e.filter((e => !B.value.includes(e))), a = t.length > 0) : (a = !0, t = e), U("Task monitoring check:", {
                isMonitoring: L.value,
                currentlyMonitored: B.value,
                pendingTasks: e,
                newTasks: t,
                shouldMonitor: a
            }), a && (U("Found new pending tasks, restarting monitoring", t), V(e))
        }), {
            deep: !0,
            flush: "post"
        });
        const G = s((() => ({
                transform: `translate(${d.canvasTranslateX}px, ${d.canvasTranslateY}px) scale(${d.canvasScale})`
            }))),
            Y = s((() => ({
                backgroundPosition: `${d.canvasTranslateX}px ${d.canvasTranslateY}px`,
                backgroundSize: `${50*d.canvasScale}px ${50*d.canvasScale}px`,
                cursor: Z()
            }))),
            Z = () => ee.value || d.isLeftMouseDragging ? "grabbing" : d.isLeftMouseDragMode ? "grab" : d.isMaskSelectionMode || d.isBrushMode ? "crosshair" : "default",
            q = e => {
                e.preventDefault();
                if (e.ctrlKey || e.metaKey) {
                    const t = f.value.getBoundingClientRect(),
                        a = e.clientX - t.left,
                        n = e.clientY - t.top,
                        o = .005 * -e.deltaY,
                        i = d.canvasScale * (1 + o);
                    d.zoomCanvasAtPoint(i, a, n)
                } else {
                    let t = e.deltaX,
                        a = e.deltaY;
                    e.shiftKey && 0 === e.deltaX && (t = e.deltaY, a = 0), d.panCanvas(1 * -t, 1 * -a)
                }
                F()
            };
        let J = 0,
            K = 0,
            Q = 0;
        const ee = r(!1);
        let ae = 0,
            ne = 0,
            oe = 0,
            ie = 0;
        const le = e => {
                if (1 === e.touches.length) K = e.touches[0].clientX, Q = e.touches[0].clientY;
                else if (2 === e.touches.length) {
                    const t = e.touches[1].clientX - e.touches[0].clientX,
                        a = e.touches[1].clientY - e.touches[0].clientY;
                    J = Math.sqrt(t * t + a * a)
                }
            },
            re = e => {
                if (e.preventDefault(), 1 === e.touches.length) {
                    const t = e.touches[0].clientX - K,
                        a = e.touches[0].clientY - Q;
                    d.panCanvas(t, a), K = e.touches[0].clientX, Q = e.touches[0].clientY
                } else if (2 === e.touches.length) {
                    const t = e.touches[1].clientX - e.touches[0].clientX,
                        a = e.touches[1].clientY - e.touches[0].clientY,
                        n = Math.sqrt(t * t + a * a);
                    if (J > 0) {
                        const t = n / J,
                            a = f.value.getBoundingClientRect(),
                            o = (e.touches[0].clientX + e.touches[1].clientX) / 2 - a.left,
                            i = (e.touches[0].clientY + e.touches[1].clientY) / 2 - a.top;
                        d.zoomCanvasAtPoint(d.canvasScale * t, o, i)
                    }
                    J = n
                }
                F()
            },
            se = e => {
                (1 === e.button || ee.value) && e.preventDefault()
            },
            de = async e => {
                U("handleAskWithMask called with messageContent:", e);
                try {
                    d.clearNodeSelection(), l("ask", e), U("Mask edit ask event emitted successfully")
                } catch (t) {
                    $("Error handling ask with mask:", t)
                }
            },
            ce = async (e, t, a = 1, n = 1) => {
                const o = t,
                    i = qa(o.src);
                i && await new Promise((l => {
                    const r = new Image;
                    r.crossOrigin = "anonymous", r.onload = () => {
                        try {
                            const i = t.left * a,
                                s = t.top * n,
                                d = (t.width || r.width) * a,
                                c = (t.height || r.height) * n;
                            o.rotation ? (e.save(), e.translate(i + d / 2, s + c / 2), e.rotate(o.rotation * Math.PI / 180), e.drawImage(r, -d / 2, -c / 2, d, c), e.restore()) : e.drawImage(r, i, s, d, c), l()
                        } catch (i) {
                            $("Error drawing child image:", i), l()
                        }
                    }, r.onerror = () => {
                        $("Failed to load child image:", i), l()
                    }, r.src = i
                }))
            },
            he = (e, t, a = 1, n = 1) => {
                const o = t,
                    i = t.left * a,
                    l = t.top * n,
                    r = (o.fontSize || 16) * Math.min(a, n);
                if (e.font = `${o.fontStyle||"normal"} ${o.fontWeight||"normal"} ${r}px ${o.fontFamily||"Arial, sans-serif"}`, e.fillStyle = o.color || "#000000", e.textAlign = o.textAlign || "left", e.textBaseline = "top", t.rotation) {
                    e.save(), e.translate(i, l), e.rotate(t.rotation * Math.PI / 180);
                    const a = (o.text || "").split("\n"),
                        n = 1 * r;
                    a.forEach(((t, a) => {
                        e.fillText(t, 0, a * n)
                    })), e.restore()
                } else {
                    const t = (o.text || "").split("\n"),
                        a = 1 * r;
                    t.forEach(((t, n) => {
                        e.fillText(t, i, l + n * a)
                    }))
                }
            },
            ve = (e, t, a = 1, n = 1) => {
                var o;
                const i = t;
                U("Drawing child SVG (doodle) on canvas:", {
                    svgId: t.id,
                    pathCount: (null == (o = i.paths) ? void 0 : o.length) || 0
                });
                const l = t.left * a,
                    r = t.top * n,
                    s = (t.width || 1024) * a,
                    d = (t.height || 1536) * n;
                if (i.paths && i.paths.length > 0) {
                    e.save(), e.translate(l, r), t.rotation && e.rotate(t.rotation * Math.PI / 180);
                    const a = s / (i.width || s),
                        n = d / (i.height || d);
                    e.scale(a, n), i.paths.forEach((t => {
                        try {
                            const a = t.d;
                            if (a) {
                                const n = new Path2D(a);
                                e.strokeStyle = t.stroke || "#000000", e.lineWidth = t.strokeWidth || 3, e.lineCap = "round", e.lineJoin = "round", e.stroke(n), U("Drew SVG path on canvas:", {
                                    pathId: t.id,
                                    stroke: t.stroke,
                                    strokeWidth: t.strokeWidth
                                })
                            }
                        } catch (a) {
                            $("Error drawing SVG path on canvas:", a, t)
                        }
                    })), e.restore()
                }
            },
            fe = async (e, t, a = 1, n = 1) => {
                for (const i of t) try {
                    i.type === pe.IMAGE ? await ce(e, i, a, n) : i.type === pe.TEXT ? he(e, i, a, n) : i.type === pe.SVG && ve(e, i, a, n)
                } catch (o) {
                    $("Error processing child element:", o, i)
                }
            },
            me = async () => {
                var e;
                if (U("handleExport called"), _.value) return void U("Export already in progress, ignoring request");
                if (!d.selectedPageNodeId) return void $("No page node selected for export");
                const t = d.getNodeById(d.selectedPageNodeId);
                if (!t || t.type !== pe.IMAGE && t.type !== pe.FRAME) $("Selected node is not an image or frame");
                else {
                    if (t.type === pe.FRAME) return await we(t);
                    if (d.selectedPageImageId) try {
                        _.value = !0;
                        const t = d.getNodeById(d.selectedPageImageId).rawData,
                            a = qa(t.src);
                        if (!a) return $("Image URL not found"), void(_.value = !1);
                        const n = t.children && t.children.length > 0;
                        U("Export image info:", {
                            imageId: d.selectedPageImageId,
                            hasChildren: n,
                            childrenCount: (null == (e = t.children) ? void 0 : e.length) || 0
                        });
                        const o = document.createElement("a");
                        o.download = `image_${d.selectedPageImageId}.png`, o.target = "_blank";
                        const i = new Image;
                        i.crossOrigin = "anonymous", i.onload = async () => {
                            var e;
                            try {
                                const l = document.createElement("canvas"),
                                    r = l.getContext("2d");
                                if (!r) return $("Failed to get canvas context"), void(_.value = !1);
                                if (l.width = i.width, l.height = i.height, r.drawImage(i, 0, 0), n) {
                                    U("Processing children for composite export");
                                    for (const n of t.children) try {
                                        if (n.type === pe.IMAGE) {
                                            const e = n,
                                                a = qa(e.src);
                                            a && await new Promise((o => {
                                                const l = new Image;
                                                l.crossOrigin = "anonymous", l.onload = () => {
                                                    try {
                                                        const a = i.width / (t.width || i.width),
                                                            s = i.height / (t.height || i.height),
                                                            d = n.left * a,
                                                            c = n.top * s,
                                                            u = (n.width || l.width) * a,
                                                            g = (n.height || l.height) * s;
                                                        U("Drawing child image:", {
                                                            childId: n.id,
                                                            originalPos: {
                                                                x: n.left,
                                                                y: n.top
                                                            },
                                                            scaledPos: {
                                                                x: d,
                                                                y: c
                                                            },
                                                            scaledSize: {
                                                                width: u,
                                                                height: g
                                                            }
                                                        }), e.rotation ? (r.save(), r.translate(d + u / 2, c + g / 2), r.rotate(e.rotation * Math.PI / 180), r.drawImage(l, -u / 2, -g / 2, u, g), r.restore()) : r.drawImage(l, d, c, u, g), o()
                                                    } catch (a) {
                                                        $("Error drawing child image:", a), o()
                                                    }
                                                }, l.onerror = () => {
                                                    $("Failed to load child image:", a), o()
                                                }, l.src = a
                                            }))
                                        } else if (n.type === pe.TEXT) {
                                            const e = n,
                                                a = i.width / (t.width || i.width),
                                                o = i.height / (t.height || i.height),
                                                l = n.left * a,
                                                s = n.top * o,
                                                d = (e.fontSize || 16) * Math.min(a, o);
                                            if (U("Drawing child text:", {
                                                    textId: n.id,
                                                    text: e.text,
                                                    originalPos: {
                                                        x: n.left,
                                                        y: n.top
                                                    },
                                                    scaledPos: {
                                                        x: l,
                                                        y: s
                                                    },
                                                    fontSize: d
                                                }), r.font = `${e.fontStyle||"normal"} ${e.fontWeight||"normal"} ${d}px ${e.fontFamily||"Arial, sans-serif"}`, r.fillStyle = e.color || "#000000", r.textAlign = e.textAlign || "left", r.textBaseline = "top", n.rotation) {
                                                r.save(), r.translate(l, s), r.rotate(n.rotation * Math.PI / 180);
                                                const t = (e.text || "").split("\n"),
                                                    a = 1 * d;
                                                t.forEach(((e, t) => {
                                                    r.fillText(e, 0, t * a)
                                                })), r.restore()
                                            } else {
                                                const t = (e.text || "").split("\n"),
                                                    a = 1 * d;
                                                t.forEach(((e, t) => {
                                                    r.fillText(e, l, s + t * a)
                                                }))
                                            }
                                        } else if (n.type === pe.SVG) {
                                            const a = n;
                                            U("Drawing child SVG (doodle):", {
                                                svgId: n.id,
                                                pathCount: (null == (e = a.paths) ? void 0 : e.length) || 0
                                            });
                                            const o = i.width / (t.width || i.width),
                                                l = i.height / (t.height || i.height),
                                                s = n.left * o,
                                                d = n.top * l,
                                                c = (n.width || t.width || 1024) * o,
                                                u = (n.height || t.height || 1536) * l;
                                            if (a.paths && a.paths.length > 0) {
                                                r.save(), r.translate(s, d), n.rotation && r.rotate(n.rotation * Math.PI / 180);
                                                const e = c / (a.width || c),
                                                    t = u / (a.height || u);
                                                r.scale(e, t), a.paths.forEach((e => {
                                                    try {
                                                        const t = e.d;
                                                        if (t) {
                                                            const a = new Path2D(t);
                                                            r.strokeStyle = e.stroke || "#000000", r.lineWidth = e.strokeWidth || 3, r.lineCap = "round", r.lineJoin = "round", r.stroke(a), U("Drew SVG path in export:", {
                                                                pathId: e.id,
                                                                stroke: e.stroke,
                                                                strokeWidth: e.strokeWidth
                                                            })
                                                        }
                                                    } catch (t) {
                                                        $("Error drawing SVG path in export:", t, e)
                                                    }
                                                })), r.restore()
                                            }
                                        }
                                    } catch (a) {
                                        $("Error processing child element:", a, n)
                                    }
                                    U("Finished processing all children")
                                }
                                l.toBlob((e => {
                                    var a;
                                    if (e) {
                                        const i = URL.createObjectURL(e);
                                        o.href = i, o.click(), URL.revokeObjectURL(i), U("Composite image exported successfully", {
                                            hasChildren: n,
                                            childrenCount: (null == (a = t.children) ? void 0 : a.length) || 0
                                        })
                                    } else $("Failed to create image blob");
                                    _.value = !1
                                }), "image/png")
                            } catch (a) {
                                $("Error during composite export:", a), _.value = !1
                            }
                        }, i.onerror = () => {
                            $("Failed to load main image for export"), _.value = !1
                        }, i.src = a
                    } catch (a) {
                        $("Error exporting image:", a), _.value = !1
                    } else $("No image selected for export (fallback check)")
                }
            },
            we = async e => {
                var t;
                try {
                    _.value = !0;
                    const a = e.rawData,
                        n = a.children && a.children.length > 0;
                    U("Export frame info:", {
                        frameId: e.id,
                        hasChildren: n,
                        childrenCount: (null == (t = a.children) ? void 0 : t.length) || 0,
                        dimensions: `${a.width}x${a.height}`
                    });
                    const o = document.createElement("a");
                    o.download = `frame_${e.id}.png`, o.target = "_blank";
                    const i = document.createElement("canvas"),
                        l = i.getContext("2d");
                    if (!l) return $("Failed to get canvas context for frame export"), void(_.value = !1);
                    i.width = a.width || 1024, i.height = a.height || 1024, l.fillStyle = a.background_color || "#FFFFFF", l.fillRect(0, 0, i.width, i.height), n && (U("Processing children for frame export"), await fe(l, a.children, 1, 1), U("Finished processing all children in frame")), i.toBlob((e => {
                        var t;
                        if (e) {
                            const i = URL.createObjectURL(e);
                            o.href = i, o.click(), URL.revokeObjectURL(i), U("Frame exported successfully", {
                                hasChildren: n,
                                childrenCount: (null == (t = a.children) ? void 0 : t.length) || 0
                            })
                        } else $("Failed to create frame blob");
                        _.value = !1
                    }), "image/png")
                } catch (a) {
                    $("Error exporting frame:", a), _.value = !1
                }
            },
            ye = async () => {
                if (!d.selectedPageNodeId) return $("No page node selected for blend"), null;
                const e = d.getNodeById(d.selectedPageNodeId);
                if (!e || e.type !== pe.IMAGE && e.type !== pe.FRAME) return $("Selected node is not an image or frame"), null;
                if (e.type === pe.FRAME) return await (async e => {
                    const t = e.rawData;
                    return new Promise(((e, a) => {
                        try {
                            const n = document.createElement("canvas"),
                                o = n.getContext("2d");
                            if (!o) return $("Failed to get canvas context for frame composite"), void a(new Error("Failed to get canvas context"));
                            n.width = t.width || 1024, n.height = t.height || 1024, o.fillStyle = t.background_color || "#FFFFFF", o.fillRect(0, 0, n.width, n.height), (async () => {
                                t.children && t.children.length > 0 && (U("Processing children for frame composite blend"), await fe(o, t.children, 1, 1), U("Finished processing all children for frame composite blend"))
                            })().then((() => {
                                n.toBlob((t => {
                                    t ? (U("Frame composite image generated successfully for blend"), e(t)) : ($("Failed to create frame composite blob for blend"), a(new Error("Failed to create frame composite blob")))
                                }), "image/png")
                            })).catch((e => {
                                $("Error during frame composite generation:", e), a(e)
                            }))
                        } catch (n) {
                            $("Error setting up frame composite generation:", n), a(n)
                        }
                    }))
                })(e);
                const t = e.rawData,
                    a = qa(t.src);
                return a ? new Promise(((e, n) => {
                    const o = new Image;
                    o.crossOrigin = "anonymous", o.onload = async () => {
                        var a;
                        try {
                            const l = document.createElement("canvas"),
                                r = l.getContext("2d");
                            if (!r) return $("Failed to get canvas context"), void n(new Error("Failed to get canvas context"));
                            l.width = o.width, l.height = o.height, r.drawImage(o, 0, 0);
                            if (t.children && t.children.length > 0) {
                                U("Processing children for composite blend");
                                for (const e of t.children) try {
                                    if (e.type === pe.IMAGE) {
                                        const a = e,
                                            n = qa(a.src);
                                        n && await new Promise((i => {
                                            const l = new Image;
                                            l.crossOrigin = "anonymous", l.onload = () => {
                                                try {
                                                    const n = o.width / (t.width || o.width),
                                                        s = o.height / (t.height || o.height),
                                                        d = e.left * n,
                                                        c = e.top * s,
                                                        u = (e.width || l.width) * n,
                                                        g = (e.height || l.height) * s;
                                                    a.rotation ? (r.save(), r.translate(d + u / 2, c + g / 2), r.rotate(a.rotation * Math.PI / 180), r.drawImage(l, -u / 2, -g / 2, u, g), r.restore()) : r.drawImage(l, d, c, u, g), i()
                                                } catch (n) {
                                                    $("Error drawing child image:", n), i()
                                                }
                                            }, l.onerror = () => {
                                                $("Failed to load child image:", n), i()
                                            }, l.src = n
                                        }))
                                    } else if (e.type === pe.TEXT) {
                                        const a = e,
                                            n = o.width / (t.width || o.width),
                                            i = o.height / (t.height || o.height),
                                            l = e.left * n,
                                            s = e.top * i,
                                            d = (a.fontSize || 16) * Math.min(n, i);
                                        if (r.font = `${a.fontStyle||"normal"} ${a.fontWeight||"normal"} ${d}px ${a.fontFamily||"Arial, sans-serif"}`, r.fillStyle = a.color || "#000000", r.textAlign = a.textAlign || "left", r.textBaseline = "top", e.rotation) {
                                            r.save(), r.translate(l, s), r.rotate(e.rotation * Math.PI / 180);
                                            const t = (a.text || "").split("\n"),
                                                n = 1 * d;
                                            t.forEach(((e, t) => {
                                                r.fillText(e, 0, t * n)
                                            })), r.restore()
                                        } else {
                                            const e = (a.text || "").split("\n"),
                                                t = 1 * d;
                                            e.forEach(((e, a) => {
                                                r.fillText(e, l, s + a * t)
                                            }))
                                        }
                                    } else if (e.type === pe.SVG) {
                                        const n = e;
                                        U("Drawing child SVG (doodle) for blend:", {
                                            svgId: e.id,
                                            pathCount: (null == (a = n.paths) ? void 0 : a.length) || 0
                                        });
                                        const i = o.width / (t.width || o.width),
                                            l = o.height / (t.height || o.height),
                                            s = e.left * i,
                                            d = e.top * l,
                                            c = (e.width || t.width || 1024) * i,
                                            u = (e.height || t.height || 1536) * l;
                                        if (n.paths && n.paths.length > 0) {
                                            r.save(), r.translate(s, d), e.rotation && r.rotate(e.rotation * Math.PI / 180);
                                            const t = c / (n.width || c),
                                                a = u / (n.height || u);
                                            r.scale(t, a), n.paths.forEach((e => {
                                                try {
                                                    const t = e.d;
                                                    if (t) {
                                                        const a = new Path2D(t);
                                                        r.strokeStyle = e.stroke || "#000000", r.lineWidth = e.strokeWidth || 3, r.lineCap = "round", r.lineJoin = "round", r.stroke(a), U("Drew SVG path in blend:", {
                                                            pathId: e.id,
                                                            stroke: e.stroke,
                                                            strokeWidth: e.strokeWidth
                                                        })
                                                    }
                                                } catch (t) {
                                                    $("Error drawing SVG path in blend:", t, e)
                                                }
                                            })), r.restore()
                                        }
                                    }
                                } catch (i) {
                                    $("Error processing child element:", i, e)
                                }
                                U("Finished processing all children for blend")
                            }
                            l.toBlob((t => {
                                t ? (U("Composite image generated for blend successfully"), e(t)) : ($("Failed to create image blob for blend"), n(new Error("Failed to create image blob")))
                            }), "image/png")
                        } catch (i) {
                            $("Error during composite generation:", i), n(i)
                        }
                    }, o.onerror = () => {
                        $("Failed to load main image for blend"), n(new Error("Failed to load main image"))
                    }, o.src = a
                })) : ($("Image URL not found"), null)
            },
            xe = e => e.type === pe.SVG || !!(e.children && e.children.length > 0) && e.children.some((e => xe(e))),
            Ie = async e => {
                if (U("handleBlend called", {
                        prompt: e
                    }), N.value) return void U("blend already in progress, ignoring request");
                if (!d.selectedPageNodeId) return void $("No page node selected for blend");
                const t = d.getNodeById(d.selectedPageNodeId);
                if (!t || t.type !== pe.IMAGE && t.type !== pe.FRAME) return void $("Selected node is not an image or frame");
                const a = t.rawData;
                if (a.children && 0 !== a.children.length) try {
                    N.value = !0, U("Starting blend process for image with children:", {
                        imageId: d.selectedPageImageId,
                        childrenCount: a.children.length
                    });
                    const n = await ye();
                    if (!n) throw new Error("Failed to generate composite image");
                    const o = await (async e => {
                        try {
                            const t = await fetch("/api/get_upload_url");
                            if (!t.ok) throw new Error(`HTTP error! status: ${t.status}`);
                            const a = await t.json();
                            if (!a || 0 !== a.status) throw new Error(`API error! code: ${a.code}`);
                            if (!a.data) throw new Error("API response missing data object");
                            const n = a.data.upload_image_url,
                                o = a.data.page_url;
                            if (!(await fetch(n, {
                                    method: "PUT",
                                    headers: {
                                        "x-ms-blob-type": "BlockBlob"
                                    },
                                    body: e
                                })).ok) throw new Error("Upload failed");
                            return U("Image uploaded successfully for blend:", o), o
                        } catch (t) {
                            return $("Error uploading image blob:", t), null
                        }
                    })(n);
                    if (!o) throw new Error("Failed to upload composite image");
                    const i = (e => {
                        const t = {
                            text_elements: [],
                            image_elements: []
                        };
                        if (e.children && e.children.length > 0)
                            for (const a of e.children)
                                if (a.type === pe.TEXT) {
                                    const e = a;
                                    t.text_elements.push({
                                        text: e.text || "",
                                        position: `left: ${a.left}px, top: ${a.top}px`,
                                        fontSize: e.fontSize || ge.fontSize,
                                        fontFamily: e.fontFamily || ge.fontFamily,
                                        color: e.color || ge.color,
                                        fontWeight: e.fontWeight || ge.fontWeight,
                                        fontStyle: e.fontStyle || ge.fontStyle,
                                        textAlign: e.textAlign || ge.textAlign,
                                        rotation: a.rotation || void 0
                                    })
                                } else if (a.type === pe.IMAGE) {
                            const e = a;
                            t.image_elements.push({
                                url: qa(e.src) || e.src,
                                description: "A material element uploaded by the user",
                                position: `left: ${a.left}px, top: ${a.top}px`,
                                width: a.width || void 0,
                                height: a.height || void 0,
                                rotation: a.rotation || void 0
                            })
                        }
                        return t
                    })(a);
                    let r = "",
                        s = "",
                        c = "",
                        u = "",
                        g = "",
                        h = "";
                    if (t.type === pe.IMAGE) {
                        const e = t;
                        e.width && e.height && (r = `${e.width}x${e.height}`), s = e.generateMessageId, c = e.originalGenerateMessageId, h = e.originalFinalTextPrompt, e.finalTextPrompt && (u = e.finalTextPrompt);
                        const a = e.editHistory;
                        a && a.length > 0 && a.forEach(((e, t) => {
                            g += `${t+1}. ${e.description} (${e.operation}) with parameters: ${JSON.stringify(e.parameters)}\n`
                        }))
                    } else t.type === pe.FRAME && (a.width && a.height && (r = `${a.width}x${a.height}`), s = "", c = "", u = "", g = "", h = "");
                    let v = [];
                    const p = t.type === pe.IMAGE ? qa(a.src) || a.src : null;
                    let f = "";
                    p && u && (f += `Generated by text prompt: ${u}\n`);
                    const m = {
                        base_image: {
                            url: o,
                            description: p ? "This is the user-edited draft image (However, due to editing tool limitations, the elements may not blend seamlessly with the background). It should be used as the base_image in the meta, so that the image generation can be more accurate.\nThe dimensions of the image are: " + r : "This is the user-edited draft image (but the white background is default (not from users), and users lack the tools to enhance it, so please ignore the default white background). It should be used as the base_image in the meta, so that the image generation can be more accurate.\nThe dimensions of the image are: " + r
                        },
                        image_elements: [...i.image_elements, ...p ? [{
                            url: p,
                            description: "The background image.\n" + f
                        }] : []],
                        text_elements: i.text_elements
                    };
                    xe(t) && (m.base_image.description += "\nNote: The image contains doodle elements that are user sketches. Please regenerate realistic content following their shapes."), v.push({
                        type: "text",
                        text: JSON.stringify(m, null, 2),
                        hide_in_ui: !0
                    });
                    let w = p ? `Please regenerate my draft image. The image contains the background image plus ${i.image_elements.length} image material elements and ${i.text_elements.length} text elements.` : `Please regenerate my draft image. The image contains ${i.image_elements.length} image material elements and ${i.text_elements.length} text elements.`;
                    e && e.trim() && (w += `\n\nAdditional requirements: ${e.trim()}`), v.push({
                        type: "text",
                        text: w,
                        hide_in_ui: !1
                    }), l("ask", v), U("blend request sent successfully", v)
                } catch (n) {
                    $("Error during blend:", n)
                } finally {
                    N.value = !1
                } else $("No children found for blend")
            },
            ke = async e => {
                var t, a;
                U("handleApply:", e);
                try {
                    let n = "",
                        o = "",
                        i = "",
                        r = "",
                        s = "",
                        c = "",
                        u = "";
                    if (d.selectedPageImageId) {
                        const e = d.getNodeById(d.selectedPageImageId);
                        if (e && e instanceof ue) {
                            const l = e;
                            l.width && l.height && (n = `${l.width}x${l.height}`), o = l.generateMessageId, i = l.originalGenerateMessageId, c = l.originalFinalTextPrompt, u = (null == (a = null == (t = l.rawData) ? void 0 : t.extra_info) ? void 0 : a.model) || "gpt-image-1", l.finalTextPrompt && (r = l.finalTextPrompt);
                            const d = l.editHistory;
                            d && d.length > 0 && d.forEach(((e, t) => {
                                s += `${t+1}. ${e.description} (${e.operation}) with parameters: ${JSON.stringify(e.parameters)}\n`
                            }))
                        }
                    }
                    let g = [];
                    g.push({
                        type: "text",
                        text: "Please regenerate as requested.",
                        hide_in_ui: !1
                    }), g.push({
                        type: "text",
                        text: "<system-reminder>The new meta information modified by user is as follows:",
                        hide_in_ui: !0
                    }), g.push({
                        type: "text",
                        text: JSON.stringify(e, null, 2),
                        hide_in_ui: !0
                    });
                    let h = "";
                    n && (h += `Image dimensions: ${n}\n`), r && (h += `Final text prompt: ${r}\n`), u && (h += `Generated by model: ${u}\n`), c && (h += `Originally generated with text prompt: ${c}\n`), s && (h += "Edit history: This image has been processed through the following advanced edit operations(from the original generated image):\n", h += s + "\n"), h && g.push({
                        type: "text",
                        text: "The additional info for the base image:\n" + h,
                        hide_in_ui: !0
                    }), g.push({
                        type: "text",
                        text: "</system-reminder>",
                        hide_in_ui: !0
                    }), e.mask_image && g.push({
                        type: "text",
                        text: `Using mask image: ${e.mask_image}\nPlease use this mask image to guide the generation of new images.`,
                        hide_in_ui: !0
                    }), l("ask", g)
                } catch (n) {
                    $("Error applying meta changes:", n), S.value = !1
                }
            },
            Ce = e => {
                if (v.mouseDownInfo = null, 1 === e.button) return void(e => {
                    1 === e.button && (e.preventDefault(), ee.value = !0, ae = e.clientX, ne = e.clientY)
                })(e);
                if (0 !== e.button) return;
                if ((e => !(0 !== e.button || !d.isLeftMouseDragMode || (e.preventDefault(), d.isLeftMouseDragging = !0, oe = e.clientX, ie = e.clientY, 0)))(e)) return;
                const t = f.value.getBoundingClientRect(),
                    a = e.clientX - t.left,
                    n = e.clientY - t.top,
                    o = {
                        x: (a - d.canvasTranslateX) / d.canvasScale,
                        y: (n - d.canvasTranslateY) / d.canvasScale
                    };
                if (g.editingTextId) return;
                if (d.isMaskSelectionMode && d.selectedPageImageId) {
                    const t = d.getNodeById(d.selectedPageImageId);
                    if (t && t.type === pe.IMAGE) {
                        const a = t.rawData;
                        return a.left <= o.x && o.x <= a.left + a.width && a.top <= o.y && o.y <= a.top + a.height ? void d.handleMaskSelectionMouseDown(e) : void c.clearSelection()
                    }
                }
                const i = d.getSelectNode(o.x, o.y);
                if (i) {
                    if (d.isInDoodleMode) {
                        if (d.activeSvgNodeId) {
                            const t = d.getNodeById(d.activeSvgNodeId);
                            if (U("got svgNode in handleMouseDown", t), t) {
                                const t = d.findParentPageId(d.activeSvgNodeId);
                                if (t) {
                                    const a = d.getNodeById(t);
                                    if (a) {
                                        const t = a.rawData,
                                            n = o.x - t.left,
                                            i = o.y - t.top;
                                        return U(`svgX:${n} svgY:${i} canvasPoint:${o} pageData:${t}`), d.doodleStartPath(n, i), e.stopPropagation(), void e.preventDefault()
                                    }
                                }
                            }
                        }
                        return U("no store.activeSvgNodeId in handleMouseDown", d.activeSvgNodeId), d.doodleStartPath(o.x, o.y), e.stopPropagation(), void e.preventDefault()
                    }
                    v.mouseDownInfo = {
                        node: i,
                        isAlreadySelected: d.selectedNodeId === i.id,
                        x: e.clientX,
                        y: e.clientY,
                        dragging: !1
                    }, d.selectNode(i.id), T(), e.stopPropagation(), e.preventDefault()
                } else {
                    if (d.isInDoodleMode) return void d.exitDoodleMode();
                    d.showMetaEditor ? d.closeEditorPanel() : d.activeMode === De.TOOL ? (d.currentToolId === _e.FAST_EDIT_BY_TEXT || d.setActiveMode(De.NONE), d.updateToolId(null)) : d.activeMode === De.ADD_SUB_ELEMENTS ? d.setActiveMode(De.NONE) : d.selectedNodeId && d.clearNodeSelection(), d.handleMaskSelectionMouseDown(e)
                }
            },
            be = e => {
                var t;
                if (ee.value)(e => {
                    if (ee.value) {
                        e.preventDefault();
                        const t = e.clientX - ae,
                            a = e.clientY - ne;
                        d.panCanvas(t, a), ae = e.clientX, ne = e.clientY, F()
                    }
                })(e);
                else if (!(e => {
                        if (d.isLeftMouseDragging) {
                            e.preventDefault();
                            const t = e.clientX - oe,
                                a = e.clientY - ie;
                            return d.panCanvas(t, a), oe = e.clientX, ie = e.clientY, F(), !0
                        }
                        return !1
                    })(e))
                    if (c.isDragging) d.handleMaskSelectionMouseMove(e);
                    else if (d.isInDoodleMode && d.isDrawing) {
                    const t = f.value.getBoundingClientRect(),
                        a = {
                            x: e.clientX - t.left,
                            y: e.clientY - t.top
                        },
                        n = {
                            x: (a.x - d.canvasTranslateX) / d.canvasScale,
                            y: (a.y - d.canvasTranslateY) / d.canvasScale
                        };
                    if (d.activeSvgNodeId) {
                        const e = d.findParentPageId(d.activeSvgNodeId);
                        if (e) {
                            const t = d.getNodeById(e);
                            if (t) {
                                const e = t.rawData,
                                    a = n.x - e.left,
                                    o = n.y - e.top;
                                return void d.doodleAddPointToPath(a, o)
                            }
                        }
                    }
                    d.doodleAddPointToPath(n.x, n.y)
                } else if (null == (t = d.cuttingFragment) ? void 0 : t.isDragging) {
                    const t = f.value.getBoundingClientRect(),
                        a = {
                            x: e.clientX - t.left,
                            y: e.clientY - t.top
                        },
                        n = {
                            x: (a.x - d.canvasTranslateX) / d.canvasScale,
                            y: (a.y - d.canvasTranslateY) / d.canvasScale
                        };
                    d.updateFragmentDrag(n)
                } else v.draggable && v.onDragMove(e)
            },
            Ee = e => {
                var t, a, n, o;
                if (ee.value) return void(e => {
                    1 === e.button && ee.value && (e.preventDefault(), ee.value = !1)
                })(e);
                if ((e => !(0 !== e.button || !d.isLeftMouseDragging || (e.preventDefault(), d.isLeftMouseDragging = !1, 0)))(e)) return;
                if (c.isDragging) return void d.handleMaskSelectionMouseUp();
                if (d.isInDoodleMode && d.isDrawing) return void d.doodleFinishPath();
                if (null == (t = d.cuttingFragment) ? void 0 : t.isDragging) return void d.endFragmentDrag();
                if (!v.mouseDownInfo) return void U("No mouseDownTargetedImageInfo, ignore");
                const i = v.mouseDownInfo.node,
                    l = v.mouseDownInfo.isAlreadySelected;
                if (v.isDragging) return v.onDragEnd(e), void T();
                i.type === pe.TEXT ? l ? g.enterTextEditMode(i.id) : d.selectNode(i.id) : i.type === pe.IMAGE && (null == (n = null == (a = v.mouseDownInfo.node.rawData) ? void 0 : a.extra_info) ? void 0 : n.meta_info) && d.activeMode === De.NONE && d.setSelectedImageMeta({ ...v.mouseDownInfo.node.rawData.extra_info.meta_info,
                    base_image: null == (o = v.mouseDownInfo.node.rawData) ? void 0 : o.src,
                    user_custom_instructions: ""
                }, v.mouseDownInfo.node.id), v.mouseDownInfo = null
            },
            Me = e => {
                var t;
                ee.value && (ee.value = !1), d.isLeftMouseDragging && (d.isLeftMouseDragging = !1), d.handleMaskSelectionMouseLeave(), (null == (t = d.cuttingFragment) ? void 0 : t.isDragging) && d.endFragmentDrag(), v.isDragging && (v.onDragEnd(e), T()), v.mouseDownInfo = null
            },
            Se = () => {
                if (d.activeMode === De.FORM_EDIT) return void d.closeEditorPanel();
                if (!d.selectedPageImageId) return void $("No image selected, cannot toggle form edit");
                const e = d.getNodeById(d.selectedPageImageId);
                if (!e) return void $("Image node not found, cannot toggle form edit");
                if (e.type !== pe.IMAGE) return void $("Image node is not an image, cannot toggle form edit");
                const t = e;
                t.metaInfo ? d.setSelectedImageMeta({ ...t.metaInfo,
                    base_image: t.src,
                    user_custom_instructions: ""
                }, d.selectedPageImageId) : $("Image node has no meta info, cannot toggle form edit")
            },
            Ne = () => {
                U("handleAddText called");
                const e = d.selectedPageNodeId;
                if (!e) return void U("Cannot add text - no page node selected");
                const t = d.getNodeById(e);
                if (!t || t.type !== pe.IMAGE && t.type !== pe.FRAME) return void U("Cannot add text - selected node is not an image or frame");
                const a = t.rawData,
                    n = 9 * ge.fontSize * .6,
                    o = 1 * ge.fontSize,
                    i = {
                        x: a.left + a.width / 2 - n / 2,
                        y: a.top + a.height / 2 - o / 2
                    };
                U("Creating text node at center of selected node", {
                    selectedPageId: e,
                    nodeType: t.type,
                    centerPoint: i,
                    estimatedTextSize: {
                        width: n,
                        height: o
                    },
                    nodeBounds: {
                        left: a.left,
                        top: a.top,
                        width: a.width,
                        height: a.height
                    }
                }), g.createTextAtPosition(i, "YOUR TEXT")
            },
            Te = () => {
                d.selectedChildNodeId ? (U("Deleting child node", {
                    childNodeId: d.selectedChildNodeId
                }), d.deleteChildNode(d.selectedChildNodeId)) : $("No child node selected for deletion")
            },
            Pe = () => {
                U("handleDoodle called"), d.selectedPageNodeId ? (d.setActiveMode(De.DOODLE), d.startDoodleMode(d.selectedPageNodeId)) : $("No page node selected for doodle")
            },
            Fe = () => {
                if (U("handleDuplicate called"), !d.selectedPageNodeId) return void $("No page node selected for duplication");
                const e = d.getNodeById(d.selectedPageNodeId);
                if (!e || "frame" !== e.type) return void $("Selected node is not a frame");
                U("Duplicating frame node", {
                    frameId: d.selectedPageNodeId,
                    frameData: e
                });
                const t = e,
                    a = JSON.parse(JSON.stringify(t.rawData)),
                    n = `frame_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;
                a.id = n, a.left += a.width + 80, U("dumplicated frame:", {
                    originalFrameData: a,
                    newFrame: a
                });
                const o = e => {
                    e.children && Array.isArray(e.children) && e.children.forEach((e => {
                        const t = e.id;
                        e.id = `${e.type}_${Date.now()}_${Math.random().toString(36).substr(2,9)}`, U("Generated new ID for child", {
                            oldId: t,
                            newId: e.id
                        }), o(e)
                    }))
                };
                o(a), d.addNodeToCanvas(a), M.onNodeChanged(n), d.selectNode(n), U("Frame duplicated successfully", {
                    newFrameId: n
                })
            },
            Le = async () => {
                if (U("handleAddImage called"), !d.selectedPageNodeId) return void $("No image selected for adding child image");
                const e = d.getNodeById(d.selectedPageNodeId);
                if (!e || e.type !== pe.IMAGE && e.type !== pe.FRAME) return void $("Selected node is not an image");
                const t = e.rawData,
                    a = {
                        x: t.left + .3 * t.width,
                        y: t.top + .3 * t.height
                    },
                    n = je();
                await n.startImageUpload(a)
            };

        function Ae() {
            d.clearNodeSelection()
        }
        if (k((() => {
                if (!f.value) return void $("viewportRef is not set");
                f.value.addEventListener("wheel", q, {
                    passive: !1
                }), f.value.addEventListener("touchstart", le, {
                    passive: !0
                }), f.value.addEventListener("touchmove", re, {
                    passive: !1
                }), f.value.addEventListener("mousedown", Ce), f.value.addEventListener("mousemove", be), f.value.addEventListener("mouseup", Ee), f.value.addEventListener("mouseleave", Me), f.value.addEventListener("contextmenu", se);
                const e = H();
                e.length > 0 && V(e);
                const t = document.querySelector(".j-search-input");
                t && t.addEventListener("focus", Ae)
            })), I((() => {
                if (!f.value) return void $("viewportRef is not set");
                f.value.removeEventListener("wheel", q), f.value.removeEventListener("touchstart", le), f.value.removeEventListener("touchmove", re), f.value.removeEventListener("mousedown", Ce), f.value.removeEventListener("mousemove", be), f.value.removeEventListener("mouseup", Ee), f.value.removeEventListener("mouseleave", Me), f.value.removeEventListener("contextmenu", se), R.value && R.value.abort(), c.cleanupKeyboardEventListener();
                const e = document.querySelector(".j-search-input");
                e && e.removeEventListener("focus", Ae)
            })), "localhost" === location.hostname || "www.coswift.ai" === location.hostname) {
            const e = () => {
                const e = (t, a = 0, n = "") => {
                    if (!t) return;
                    let o = `${"  ".repeat(a)}${n}${t.type||"unknown"} (id: ${t.id||"no-id"})`;
                    t.type === pe.TEXT && (o += ":" + t.text), U(o), t.children && Array.isArray(t.children) && t.children.forEach(((n, o) => {
                        const i = o === t.children.length - 1;
                        e(n, a + 1, i ? "└─ " : "├─ ")
                    }))
                };
                U("=== Tree Structure ==="), e(O.value), U("=== Selection ==="), U("Current selection:", d.selection), U("Selection details:", {
                    hasSelection: !!d.selection,
                    selectionCoords: d.selection ? {
                        x: d.selection.x,
                        y: d.selection.y,
                        width: d.selection.width,
                        height: d.selection.height,
                        imageNodeId: d.selection.imageNodeId
                    } : null,
                    isSelecting: d.isSelecting,
                    interactionMode: d.interactionMode,
                    activeResizeHandle: d.activeResizeHandle
                }), U("===================")
            };
            k((() => u(e, {
                flush: "post"
            })))
        }
        return (e, t) => {
            var i, l, r, s;
            return n(), a("div", Ya, [A(Ba), o("div", {
                ref_key: "viewportRef",
                ref: f,
                class: "viewport j-viewport",
                style: C(Y.value)
            }, [(null == (l = null == (i = O.value) ? void 0 : i.children) ? void 0 : l.length) ? (n(), x(Ga, {
                key: 0,
                onMousedown: t[0] || (t[0] = D((() => {}), ["stop"]))
            })) : b("", !0), o("div", {
                class: "locating-container",
                style: C([G.value, {
                    "--text-node-default-line-height": `${y(1)}`
                }])
            }, [O.value ? (n(), x(kt, {
                key: 0,
                "node-data": O.value.rawData
            }, null, 8, ["node-data"])) : b("", !0), y(d).selection ? (n(), x(Et, {
                key: 1
            })) : b("", !0), (n(!0), a(m, null, w(y(d).cuttingFragments, ((e, t) => (n(), x(_t, {
                key: e.id,
                fragment: e,
                "is-active": t === y(d).activeFragmentIndex,
                onActivate: e => y(d).setActiveFragment(t)
            }, null, 8, ["fragment", "is-active", "onActivate"])))), 128))], 4), y(d).isCutting ? (n(), x(Pt, {
                key: 1
            })) : b("", !0), b("", !0)], 4), A(Da, {
                ref_key: "floatingMenuRef",
                ref: E,
                "is-visible": y(d).showFloatingMenu && !y(d).isOperatingImage,
                position: y(d).floatingMenuPosition,
                "is-form-edit-active": y(d).activeMode === y(De).FORM_EDIT,
                "is-advanced-edit-active": y(d).activeMode === y(De).TOOL,
                "is-export-loading": _.value,
                "is-remix-loading": N.value,
                "selected-child-node-id": y(d).selectedChildNodeId,
                onFormEdit: Se,
                onAskWithMask: de,
                onExport: me,
                onRemix: Ie,
                onAddText: Ne,
                onAddImage: Le,
                onDoodle: Pe,
                onDeleteChild: Te,
                onDuplicate: Fe,
                onCloseForm: t[1] || (t[1] = () => {
                    y(d).activeMode === y(De).FORM_EDIT && y(d).closeEditorPanel()
                }),
                onAdvancedEdit: t[2] || (t[2] = () => {
                    y(d).activeMode === y(De).TOOL ? (y(d).setActiveMode(y(De).NONE), y(d).updateToolId(null)) : y(d).setActiveMode(y(De).TOOL)
                })
            }, null, 8, ["is-visible", "position", "is-form-edit-active", "is-advanced-edit-active", "is-export-loading", "is-remix-loading", "selected-child-node-id"]), A(Ft, {
                "meta-info": y(d).selectedImageMeta,
                "is-visible": !!y(d).showMetaEditor,
                onClose: y(d).closeEditorPanel,
                onApply: ke
            }, null, 8, ["meta-info", "is-visible", "onClose"]), A(Zt, {
                "is-visible": !!y(d).showTextEditor,
                onClose: y(d).clearNodeSelection
            }, null, 8, ["is-visible", "onClose"]), (null == (s = null == (r = O.value) ? void 0 : r.children) ? void 0 : s.length) ? b("", !0) : (n(), a("img", Za))])
        }
    }
}), [
    ["__scopeId", "data-v-3c0ddd40"]
]);
export {
    Ja as I, va as L, Re as a, Kt as c, qt as l, Ye as u
};