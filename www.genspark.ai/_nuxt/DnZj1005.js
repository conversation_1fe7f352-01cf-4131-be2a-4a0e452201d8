import {
    d as o,
    D as t,
    o as r
} from "./Cf0SOiw0.js";
const i = {
    width: "12",
    height: "13",
    viewBox: "0 0 12 13",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const e = {
    render: function(e, n) {
        return r(), o("svg", i, n[0] || (n[0] = [t('<g clip-path="url(#clip0_4466_13111)"><path d="M10 3H6.66667M10 3L5 8M10 3V6.33333" stroke="var(--iconColor)" stroke-linecap="round" stroke-linejoin="round"></path><path d="M10 9.5V11C10 11.2652 9.89464 11.5196 9.70711 11.7071C9.51957 11.8946 9.26522 12 9 12H2C1.73478 12 1.48043 11.8946 1.29289 11.7071C1.10536 11.5196 1 11.2652 1 11V4C1 3.73478 1.10536 3.48043 1.29289 3.29289C1.48043 3.10536 1.73478 3 2 3H3.5" stroke="var(--iconColor)" stroke-linecap="round"></path></g><defs><clipPath id="clip0_4466_13111"><rect width="12" height="12" fill="var(--iconColor)" transform="translate(0 0.5)"></rect></clipPath></defs>', 2)]))
    }
};
export {
    e as O
};