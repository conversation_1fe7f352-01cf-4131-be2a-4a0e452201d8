import {
    d as r,
    o,
    b as t
} from "./Cf0SOiw0.js";
const e = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const n = {
    render: function(n, s) {
        return o(), r("svg", e, s[0] || (s[0] = [t("path", {
            d: "M16.25 4.58203L15.7336 12.9363C15.6016 15.0707 15.5357 16.1379 15.0007 16.9053C14.7361 17.2846 14.3956 17.6048 14.0006 17.8454C13.2017 18.332 12.1325 18.332 9.99392 18.332C7.8526 18.332 6.78192 18.332 5.98254 17.8444C5.58733 17.6034 5.24667 17.2827 4.98223 16.9027C4.4474 16.1342 4.38287 15.0654 4.25384 12.928L3.75 4.58203",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linecap": "round"
        }, null, -1), t("path", {
            d: "M2.5 4.58464H17.5M13.3797 4.58464L12.8109 3.41108C12.433 2.63152 12.244 2.24174 11.9181 1.99864C11.8458 1.94472 11.7693 1.89675 11.6892 1.85522C11.3283 1.66797 10.8951 1.66797 10.0287 1.66797C9.14067 1.66797 8.69667 1.66797 8.32973 1.86307C8.24842 1.90631 8.17082 1.95622 8.09774 2.01228C7.76803 2.26522 7.58386 2.66926 7.21551 3.47735L6.71077 4.58464",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linecap": "round"
        }, null, -1), t("path", {
            d: "M7.91663 13.75V8.75",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linecap": "round"
        }, null, -1), t("path", {
            d: "M12.0834 13.75V8.75",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linecap": "round"
        }, null, -1)]))
    }
};
export {
    n as D
};