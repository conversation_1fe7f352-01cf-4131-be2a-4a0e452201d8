import {
    J as e,
    X as n,
    aT as o,
    al as r,
    aO as t,
    am as a,
    aP as l,
    ak as i,
    r as s,
    v as u,
    i as c,
    c as d,
    F as h,
    Y as p,
    Z as v,
    d1 as f,
    b7 as g,
    aR as b,
    b9 as x,
    h as m,
    aD as w,
    ag as y,
    K as C,
    aV as z,
    ap as A,
    aq as F,
    U as S,
    b6 as $
} from "./Cf0SOiw0.js";
import {
    a as B,
    u as R
} from "./MpDLC7up.js";
import {
    b as k,
    r as T,
    a as E,
    c as P
} from "./B7VeW_-d.js";
import {
    S as _
} from "./WZsIN7xM.js";
import {
    V as D,
    o as I,
    a as M
} from "./Jr9eiJio.js";
import {
    u as W
} from "./DGJMLFjI.js";
import {
    u as V
} from "./BuhfKjCJ.js";
import {
    a as j
} from "./pB_XRIgB.js";
import {
    N as O,
    a as N
} from "./By6xEfKc.js";
const K = e({
        name: "Eye",
        render: () => n("svg", {
            xmlns: "http://www.w3.org/2000/svg",
            viewBox: "0 0 512 512"
        }, n("path", {
            d: "M255.66 112c-77.94 0-157.89 45.11-220.83 135.33a16 16 0 0 0-.27 17.77C82.92 340.8 161.8 400 255.66 400c92.84 0 173.34-59.38 221.79-135.25a16.14 16.14 0 0 0 0-17.47C428.89 172.28 347.8 112 255.66 112z",
            fill: "none",
            stroke: "currentColor",
            "stroke-linecap": "round",
            "stroke-linejoin": "round",
            "stroke-width": "32"
        }), n("circle", {
            cx: "256",
            cy: "256",
            r: "80",
            fill: "none",
            stroke: "currentColor",
            "stroke-miterlimit": "10",
            "stroke-width": "32"
        }))
    }),
    H = e({
        name: "EyeOff",
        render: () => n("svg", {
            xmlns: "http://www.w3.org/2000/svg",
            viewBox: "0 0 512 512"
        }, n("path", {
            d: "M432 448a15.92 15.92 0 0 1-11.31-4.69l-352-352a16 16 0 0 1 22.62-22.62l352 352A16 16 0 0 1 432 448z",
            fill: "currentColor"
        }), n("path", {
            d: "M255.66 384c-41.49 0-81.5-12.28-118.92-36.5c-34.07-22-64.74-53.51-88.7-91v-.08c19.94-28.57 41.78-52.73 65.24-72.21a2 2 0 0 0 .14-2.94L93.5 161.38a2 2 0 0 0-2.71-.12c-24.92 21-48.05 46.76-69.08 76.92a31.92 31.92 0 0 0-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416a239.13 239.13 0 0 0 75.8-12.58a2 2 0 0 0 .77-3.31l-21.58-21.58a4 4 0 0 0-3.83-1a204.8 204.8 0 0 1-51.16 6.47z",
            fill: "currentColor"
        }), n("path", {
            d: "M490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96a227.34 227.34 0 0 0-74.89 12.83a2 2 0 0 0-.75 3.31l21.55 21.55a4 4 0 0 0 3.88 1a192.82 192.82 0 0 1 50.21-6.69c40.69 0 80.58 12.43 118.55 37c34.71 22.4 65.74 53.88 89.76 91a.13.13 0 0 1 0 .16a310.72 310.72 0 0 1-64.12 72.73a2 2 0 0 0-.15 2.95l19.9 19.89a2 2 0 0 0 2.7.13a343.49 343.49 0 0 0 68.64-78.48a32.2 32.2 0 0 0-.1-34.78z",
            fill: "currentColor"
        }), n("path", {
            d: "M256 160a95.88 95.88 0 0 0-21.37 2.4a2 2 0 0 0-1 3.38l112.59 112.56a2 2 0 0 0 3.38-1A96 96 0 0 0 256 160z",
            fill: "currentColor"
        }), n("path", {
            d: "M165.78 233.66a2 2 0 0 0-3.38 1a96 96 0 0 0 115 115a2 2 0 0 0 1-3.38z",
            fill: "currentColor"
        }))
    }),
    U = o("n-input"),
    L = r("input", "\n max-width: 100%;\n cursor: text;\n line-height: 1.5;\n z-index: auto;\n outline: none;\n box-sizing: border-box;\n position: relative;\n display: inline-flex;\n border-radius: var(--n-border-radius);\n background-color: var(--n-color);\n transition: background-color .3s var(--n-bezier);\n font-size: var(--n-font-size);\n font-weight: var(--n-font-weight);\n --n-padding-vertical: calc((var(--n-height) - 1.5 * var(--n-font-size)) / 2);\n", [t("input, textarea", "\n overflow: hidden;\n flex-grow: 1;\n position: relative;\n "), t("input-el, textarea-el, input-mirror, textarea-mirror, separator, placeholder", "\n box-sizing: border-box;\n font-size: inherit;\n line-height: 1.5;\n font-family: inherit;\n border: none;\n outline: none;\n background-color: #0000;\n text-align: inherit;\n transition:\n -webkit-text-fill-color .3s var(--n-bezier),\n caret-color .3s var(--n-bezier),\n color .3s var(--n-bezier),\n text-decoration-color .3s var(--n-bezier);\n "), t("input-el, textarea-el", "\n -webkit-appearance: none;\n scrollbar-width: none;\n width: 100%;\n min-width: 0;\n text-decoration-color: var(--n-text-decoration-color);\n color: var(--n-text-color);\n caret-color: var(--n-caret-color);\n background-color: transparent;\n ", [i("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb", "\n width: 0;\n height: 0;\n display: none;\n "), i("&::placeholder", "\n color: #0000;\n -webkit-text-fill-color: transparent !important;\n "), i("&:-webkit-autofill ~", [t("placeholder", "display: none;")])]), a("round", [l("textarea", "border-radius: calc(var(--n-height) / 2);")]), t("placeholder", "\n pointer-events: none;\n position: absolute;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n overflow: hidden;\n color: var(--n-placeholder-color);\n ", [i("span", "\n width: 100%;\n display: inline-block;\n ")]), a("textarea", [t("placeholder", "overflow: visible;")]), l("autosize", "width: 100%;"), a("autosize", [t("textarea-el, input-el", "\n position: absolute;\n top: 0;\n left: 0;\n height: 100%;\n ")]), r("input-wrapper", "\n overflow: hidden;\n display: inline-flex;\n flex-grow: 1;\n position: relative;\n padding-left: var(--n-padding-left);\n padding-right: var(--n-padding-right);\n "), t("input-mirror", "\n padding: 0;\n height: var(--n-height);\n line-height: var(--n-height);\n overflow: hidden;\n visibility: hidden;\n position: static;\n white-space: pre;\n pointer-events: none;\n "), t("input-el", "\n padding: 0;\n height: var(--n-height);\n line-height: var(--n-height);\n ", [i("&[type=password]::-ms-reveal", "display: none;"), i("+", [t("placeholder", "\n display: flex;\n align-items: center; \n ")])]), l("textarea", [t("placeholder", "white-space: nowrap;")]), t("eye", "\n display: flex;\n align-items: center;\n justify-content: center;\n transition: color .3s var(--n-bezier);\n "), a("textarea", "width: 100%;", [r("input-word-count", "\n position: absolute;\n right: var(--n-padding-right);\n bottom: var(--n-padding-vertical);\n "), a("resizable", [r("input-wrapper", "\n resize: vertical;\n min-height: var(--n-height);\n ")]), t("textarea-el, textarea-mirror, placeholder", "\n height: 100%;\n padding-left: 0;\n padding-right: 0;\n padding-top: var(--n-padding-vertical);\n padding-bottom: var(--n-padding-vertical);\n word-break: break-word;\n display: inline-block;\n vertical-align: bottom;\n box-sizing: border-box;\n line-height: var(--n-line-height-textarea);\n margin: 0;\n resize: none;\n white-space: pre-wrap;\n scroll-padding-block-end: var(--n-padding-vertical);\n "), t("textarea-mirror", "\n width: 100%;\n pointer-events: none;\n overflow: hidden;\n visibility: hidden;\n position: static;\n white-space: pre-wrap;\n overflow-wrap: break-word;\n ")]), a("pair", [t("input-el, placeholder", "text-align: center;"), t("separator", "\n display: flex;\n align-items: center;\n transition: color .3s var(--n-bezier);\n color: var(--n-text-color);\n white-space: nowrap;\n ", [r("icon", "\n color: var(--n-icon-color);\n "), r("base-icon", "\n color: var(--n-icon-color);\n ")])]), a("disabled", "\n cursor: not-allowed;\n background-color: var(--n-color-disabled);\n ", [t("border", "border: var(--n-border-disabled);"), t("input-el, textarea-el", "\n cursor: not-allowed;\n color: var(--n-text-color-disabled);\n text-decoration-color: var(--n-text-color-disabled);\n "), t("placeholder", "color: var(--n-placeholder-color-disabled);"), t("separator", "color: var(--n-text-color-disabled);", [r("icon", "\n color: var(--n-icon-color-disabled);\n "), r("base-icon", "\n color: var(--n-icon-color-disabled);\n ")]), r("input-word-count", "\n color: var(--n-count-text-color-disabled);\n "), t("suffix, prefix", "color: var(--n-text-color-disabled);", [r("icon", "\n color: var(--n-icon-color-disabled);\n "), r("internal-icon", "\n color: var(--n-icon-color-disabled);\n ")])]), l("disabled", [t("eye", "\n color: var(--n-icon-color);\n cursor: pointer;\n ", [i("&:hover", "\n color: var(--n-icon-color-hover);\n "), i("&:active", "\n color: var(--n-icon-color-pressed);\n ")]), i("&:hover", [t("state-border", "border: var(--n-border-hover);")]), a("focus", "background-color: var(--n-color-focus);", [t("state-border", "\n border: var(--n-border-focus);\n box-shadow: var(--n-box-shadow-focus);\n ")])]), t("border, state-border", "\n box-sizing: border-box;\n position: absolute;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n pointer-events: none;\n border-radius: inherit;\n border: var(--n-border);\n transition:\n box-shadow .3s var(--n-bezier),\n border-color .3s var(--n-bezier);\n "), t("state-border", "\n border-color: #0000;\n z-index: 1;\n "), t("prefix", "margin-right: 4px;"), t("suffix", "\n margin-left: 4px;\n "), t("suffix, prefix", "\n transition: color .3s var(--n-bezier);\n flex-wrap: nowrap;\n flex-shrink: 0;\n line-height: var(--n-height);\n white-space: nowrap;\n display: inline-flex;\n align-items: center;\n justify-content: center;\n color: var(--n-suffix-text-color);\n ", [r("base-loading", "\n font-size: var(--n-icon-size);\n margin: 0 2px;\n color: var(--n-loading-color);\n "), r("base-clear", "\n font-size: var(--n-icon-size);\n ", [t("placeholder", [r("base-icon", "\n transition: color .3s var(--n-bezier);\n color: var(--n-icon-color);\n font-size: var(--n-icon-size);\n ")])]), i(">", [r("icon", "\n transition: color .3s var(--n-bezier);\n color: var(--n-icon-color);\n font-size: var(--n-icon-size);\n ")]), r("base-icon", "\n font-size: var(--n-icon-size);\n ")]), r("input-word-count", "\n pointer-events: none;\n line-height: 1.5;\n font-size: .85em;\n color: var(--n-count-text-color);\n transition: color .3s var(--n-bezier);\n margin-left: 4px;\n font-variant: tabular-nums;\n "), ["warning", "error"].map((e => a(`${e}-status`, [l("disabled", [r("base-loading", `\n color: var(--n-loading-color-${e})\n `), t("input-el, textarea-el", `\n caret-color: var(--n-caret-color-${e});\n `), t("state-border", `\n border: var(--n-border-${e});\n `), i("&:hover", [t("state-border", `\n border: var(--n-border-hover-${e});\n `)]), i("&:focus", `\n background-color: var(--n-color-focus-${e});\n `, [t("state-border", `\n box-shadow: var(--n-box-shadow-focus-${e});\n border: var(--n-border-focus-${e});\n `)]), a("focus", `\n background-color: var(--n-color-focus-${e});\n `, [t("state-border", `\n box-shadow: var(--n-box-shadow-focus-${e});\n border: var(--n-border-focus-${e});\n `)])])])))]),
    G = r("input", [a("disabled", [t("input-el, textarea-el", "\n -webkit-text-fill-color: var(--n-text-color-disabled);\n ")])]);

function X(e) {
    let n = 0;
    for (const o of e) n++;
    return n
}

function Y(e) {
    return "" === e || null == e
}
const q = e({
        name: "InputWordCount",
        setup(e, {
            slots: o
        }) {
            const {
                mergedValueRef: r,
                maxlengthRef: t,
                mergedClsPrefixRef: a,
                countGraphemesRef: l
            } = c(U), i = d((() => {
                const {
                    value: e
                } = r;
                return null === e || Array.isArray(e) ? 0 : (l.value || X)(e)
            }));
            return () => {
                const {
                    value: e
                } = t, {
                    value: l
                } = r;
                return n("span", {
                    class: `${a.value}-input-word-count`
                }, k(o.default, {
                    value: null === l || Array.isArray(l) ? "" : l
                }, (() => [void 0 === e ? i.value : `${i.value} / ${e}`])))
            }
        }
    }),
    J = e({
        name: "Input",
        props: Object.assign(Object.assign({}, v.props), {
            bordered: {
                type: Boolean,
                default: void 0
            },
            type: {
                type: String,
                default: "text"
            },
            placeholder: [Array, String],
            defaultValue: {
                type: [String, Array],
                default: null
            },
            value: [String, Array],
            disabled: {
                type: Boolean,
                default: void 0
            },
            size: String,
            rows: {
                type: [Number, String],
                default: 3
            },
            round: Boolean,
            minlength: [String, Number],
            maxlength: [String, Number],
            clearable: Boolean,
            autosize: {
                type: [Boolean, Object],
                default: !1
            },
            pair: Boolean,
            separator: String,
            readonly: {
                type: [String, Boolean],
                default: !1
            },
            passivelyActivated: Boolean,
            showPasswordOn: String,
            stateful: {
                type: Boolean,
                default: !0
            },
            autofocus: Boolean,
            inputProps: Object,
            resizable: {
                type: Boolean,
                default: !0
            },
            showCount: Boolean,
            loading: {
                type: Boolean,
                default: void 0
            },
            allowInput: Function,
            renderCount: Function,
            onMousedown: Function,
            onKeydown: Function,
            onKeyup: [Function, Array],
            onInput: [Function, Array],
            onFocus: [Function, Array],
            onBlur: [Function, Array],
            onClick: [Function, Array],
            onChange: [Function, Array],
            onClear: [Function, Array],
            countGraphemes: Function,
            status: String,
            "onUpdate:value": [Function, Array],
            onUpdateValue: [Function, Array],
            textDecoration: [String, Array],
            attrSize: {
                type: Number,
                default: 20
            },
            onInputBlur: [Function, Array],
            onInputFocus: [Function, Array],
            onDeactivate: [Function, Array],
            onActivate: [Function, Array],
            onWrapperFocus: [Function, Array],
            onWrapperBlur: [Function, Array],
            internalDeactivateOnEnter: Boolean,
            internalForceFocus: Boolean,
            internalLoadingBeforeSuffix: {
                type: Boolean,
                default: !0
            },
            showPasswordToggle: Boolean
        }),
        slots: Object,
        setup(e) {
            const {
                mergedClsPrefixRef: n,
                mergedBorderedRef: o,
                inlineThemeDisabled: r,
                mergedRtlRef: t
            } = p(e), a = v("Input", "-input", L, f, e, n);
            B && g("-input-safari", G, n);
            const l = s(null),
                i = s(null),
                c = s(null),
                h = s(null),
                $ = s(null),
                k = s(null),
                T = s(null),
                E = function(e) {
                    const n = s(null);

                    function o() {
                        n.value = null
                    }
                    return u(e, o), {
                        recordCursor: function() {
                            const {
                                value: r
                            } = e;
                            if (!(null == r ? void 0 : r.focus)) return void o();
                            const {
                                selectionStart: t,
                                selectionEnd: a,
                                value: l
                            } = r;
                            null != t && null != a ? n.value = {
                                start: t,
                                end: a,
                                beforeText: l.slice(0, t),
                                afterText: l.slice(a)
                            } : o()
                        },
                        restoreCursor: function() {
                            var o;
                            const {
                                value: r
                            } = n, {
                                value: t
                            } = e;
                            if (!r || !t) return;
                            const {
                                value: a
                            } = t, {
                                start: l,
                                beforeText: i,
                                afterText: s
                            } = r;
                            let u = a.length;
                            if (a.endsWith(s)) u = a.length - s.length;
                            else if (a.startsWith(i)) u = i.length;
                            else {
                                const e = i[l - 1],
                                    n = a.indexOf(e, l - 1); - 1 !== n && (u = n + 1)
                            }
                            null === (o = t.setSelectionRange) || void 0 === o || o.call(t, u, u)
                        }
                    }
                }(T),
                _ = s(null),
                {
                    localeRef: D
                } = W("Input"),
                O = s(e.defaultValue),
                N = b(e, "value"),
                K = V(N, O),
                H = R(e),
                {
                    mergedSizeRef: X,
                    mergedDisabledRef: q,
                    mergedStatusRef: J
                } = H,
                Z = s(!1),
                Q = s(!1),
                ee = s(!1),
                ne = s(!1);
            let oe = null;
            const re = d((() => {
                    const {
                        placeholder: n,
                        pair: o
                    } = e;
                    return o ? Array.isArray(n) ? n : void 0 === n ? ["", ""] : [n, n] : void 0 === n ? [D.value.placeholder] : [n]
                })),
                te = d((() => {
                    const {
                        value: e
                    } = ee, {
                        value: n
                    } = K, {
                        value: o
                    } = re;
                    return !e && (Y(n) || Array.isArray(n) && Y(n[0])) && o[0]
                })),
                ae = d((() => {
                    const {
                        value: e
                    } = ee, {
                        value: n
                    } = K, {
                        value: o
                    } = re;
                    return !e && o[1] && (Y(n) || Array.isArray(n) && Y(n[1]))
                })),
                le = x((() => e.internalForceFocus || Z.value)),
                ie = x((() => {
                    if (q.value || e.readonly || !e.clearable || !le.value && !Q.value) return !1;
                    const {
                        value: n
                    } = K, {
                        value: o
                    } = le;
                    return e.pair ? !(!Array.isArray(n) || !n[0] && !n[1]) && (Q.value || o) : !!n && (Q.value || o)
                })),
                se = d((() => {
                    const {
                        showPasswordOn: n
                    } = e;
                    return n || (e.showPasswordToggle ? "click" : void 0)
                })),
                ue = s(!1),
                ce = d((() => {
                    const {
                        textDecoration: n
                    } = e;
                    return n ? Array.isArray(n) ? n.map((e => ({
                        textDecoration: e
                    }))) : [{
                        textDecoration: n
                    }] : ["", ""]
                })),
                de = s(void 0),
                he = d((() => {
                    const {
                        maxlength: n
                    } = e;
                    return void 0 === n ? void 0 : Number(n)
                }));
            m((() => {
                const {
                    value: e
                } = K;
                Array.isArray(e) || ye(e)
            }));
            const pe = w().proxy;

            function ve(n, o) {
                const {
                    onUpdateValue: r,
                    "onUpdate:value": t,
                    onInput: a
                } = e, {
                    nTriggerFormInput: l
                } = H;
                r && P(r, n, o), t && P(t, n, o), a && P(a, n, o), O.value = n, l()
            }

            function fe(n, o) {
                const {
                    onChange: r
                } = e, {
                    nTriggerFormChange: t
                } = H;
                r && P(r, n, o), O.value = n, t()
            }

            function ge(n, o = 0, r = "input") {
                const t = n.target.value;
                if (ye(t), n instanceof InputEvent && !n.isComposing && (ee.value = !1), "textarea" === e.type) {
                    const {
                        value: e
                    } = _;
                    e && e.syncUnifiedContainer()
                }
                if (oe = t, ee.value) return;
                E.recordCursor();
                const a = function(n) {
                    const {
                        countGraphemes: o,
                        maxlength: r,
                        minlength: t
                    } = e;
                    if (o) {
                        let e;
                        if (void 0 !== r && (void 0 === e && (e = o(n)), e > Number(r))) return !1;
                        if (void 0 !== t && (void 0 === e && (e = o(n)), e < Number(r))) return !1
                    }
                    const {
                        allowInput: a
                    } = e;
                    if ("function" == typeof a) return a(n);
                    return !0
                }(t);
                if (a)
                    if (e.pair) {
                        let {
                            value: e
                        } = K;
                        e = Array.isArray(e) ? [e[0], e[1]] : ["", ""], e[o] = t, "input" === r ? ve(e, {
                            source: o
                        }) : fe(e, {
                            source: o
                        })
                    } else "input" === r ? ve(t, {
                        source: o
                    }) : fe(t, {
                        source: o
                    });
                pe.$forceUpdate(), a || S(E.restoreCursor)
            }

            function be(n, o) {
                (null === n.relatedTarget || n.relatedTarget !== $.value && n.relatedTarget !== k.value && n.relatedTarget !== i.value && n.relatedTarget !== l.value) && ("focus" === o ? (! function(n) {
                    const {
                        onFocus: o
                    } = e, {
                        nTriggerFormFocus: r
                    } = H;
                    o && P(o, n), r()
                }(n), Z.value = !0) : "blur" === o && (! function(n) {
                    const {
                        onBlur: o
                    } = e, {
                        nTriggerFormBlur: r
                    } = H;
                    o && P(o, n), r()
                }(n), Z.value = !1))
            }

            function xe() {
                e.pair ? (ve(["", ""], {
                    source: "clear"
                }), fe(["", ""], {
                    source: "clear"
                })) : (ve("", {
                    source: "clear"
                }), fe("", {
                    source: "clear"
                }))
            }

            function me() {
                e.passivelyActivated && (ne.value = !1, S((() => {
                    var e;
                    null === (e = l.value) || void 0 === e || e.focus()
                })))
            }

            function we() {
                var n, o, r;
                q.value || (e.passivelyActivated ? null === (n = l.value) || void 0 === n || n.focus() : (null === (o = i.value) || void 0 === o || o.focus(), null === (r = $.value) || void 0 === r || r.focus()))
            }

            function ye(n) {
                const {
                    type: o,
                    pair: r,
                    autosize: t
                } = e;
                if (!r && t)
                    if ("textarea" === o) {
                        const {
                            value: e
                        } = c;
                        e && (e.textContent = `${null!=n?n:""}\r\n`)
                    } else {
                        const {
                            value: e
                        } = h;
                        e && (n ? e.textContent = n : e.innerHTML = "&nbsp;")
                    }
            }
            const Ce = s({
                top: "0"
            });
            let ze = null;
            y((() => {
                const {
                    autosize: n,
                    type: o
                } = e;
                n && "textarea" === o ? ze = u(K, (e => {
                    Array.isArray(e) || e === oe || ye(e)
                })) : null == ze || ze()
            }));
            let Ae = null;
            y((() => {
                "textarea" === e.type ? Ae = u(K, (e => {
                    var n;
                    Array.isArray(e) || e === oe || null === (n = _.value) || void 0 === n || n.syncUnifiedContainer()
                })) : null == Ae || Ae()
            })), C(U, {
                mergedValueRef: K,
                maxlengthRef: he,
                mergedClsPrefixRef: n,
                countGraphemesRef: b(e, "countGraphemes")
            });
            const Fe = {
                    wrapperElRef: l,
                    inputElRef: $,
                    textareaElRef: i,
                    isCompositing: ee,
                    clear: xe,
                    focus: we,
                    blur: function() {
                        var e;
                        (null === (e = l.value) || void 0 === e ? void 0 : e.contains(document.activeElement)) && document.activeElement.blur()
                    },
                    select: function() {
                        var e, n;
                        null === (e = i.value) || void 0 === e || e.select(), null === (n = $.value) || void 0 === n || n.select()
                    },
                    deactivate: function() {
                        const {
                            value: e
                        } = l;
                        (null == e ? void 0 : e.contains(document.activeElement)) && e !== document.activeElement && me()
                    },
                    activate: function() {
                        q.value || (i.value ? i.value.focus() : $.value && $.value.focus())
                    },
                    scrollTo: function(n) {
                        if ("textarea" === e.type) {
                            const {
                                value: e
                            } = i;
                            null == e || e.scrollTo(n)
                        } else {
                            const {
                                value: e
                            } = $;
                            null == e || e.scrollTo(n)
                        }
                    }
                },
                Se = z("Input", t, n),
                $e = d((() => {
                    const {
                        value: e
                    } = X, {
                        common: {
                            cubicBezierEaseInOut: n
                        },
                        self: {
                            color: o,
                            borderRadius: r,
                            textColor: t,
                            caretColor: l,
                            caretColorError: i,
                            caretColorWarning: s,
                            textDecorationColor: u,
                            border: c,
                            borderDisabled: d,
                            borderHover: h,
                            borderFocus: p,
                            placeholderColor: v,
                            placeholderColorDisabled: f,
                            lineHeightTextarea: g,
                            colorDisabled: b,
                            colorFocus: x,
                            textColorDisabled: m,
                            boxShadowFocus: w,
                            iconSize: y,
                            colorFocusWarning: C,
                            boxShadowFocusWarning: z,
                            borderWarning: F,
                            borderFocusWarning: S,
                            borderHoverWarning: $,
                            colorFocusError: B,
                            boxShadowFocusError: R,
                            borderError: k,
                            borderFocusError: T,
                            borderHoverError: E,
                            clearSize: P,
                            clearColor: _,
                            clearColorHover: D,
                            clearColorPressed: I,
                            iconColor: M,
                            iconColorDisabled: W,
                            suffixTextColor: V,
                            countTextColor: O,
                            countTextColorDisabled: N,
                            iconColorHover: K,
                            iconColorPressed: H,
                            loadingColor: U,
                            loadingColorError: L,
                            loadingColorWarning: G,
                            fontWeight: Y,
                            [A("padding", e)]: q,
                            [A("fontSize", e)]: J,
                            [A("height", e)]: Z
                        }
                    } = a.value, {
                        left: Q,
                        right: ee
                    } = j(q);
                    return {
                        "--n-bezier": n,
                        "--n-count-text-color": O,
                        "--n-count-text-color-disabled": N,
                        "--n-color": o,
                        "--n-font-size": J,
                        "--n-font-weight": Y,
                        "--n-border-radius": r,
                        "--n-height": Z,
                        "--n-padding-left": Q,
                        "--n-padding-right": ee,
                        "--n-text-color": t,
                        "--n-caret-color": l,
                        "--n-text-decoration-color": u,
                        "--n-border": c,
                        "--n-border-disabled": d,
                        "--n-border-hover": h,
                        "--n-border-focus": p,
                        "--n-placeholder-color": v,
                        "--n-placeholder-color-disabled": f,
                        "--n-icon-size": y,
                        "--n-line-height-textarea": g,
                        "--n-color-disabled": b,
                        "--n-color-focus": x,
                        "--n-text-color-disabled": m,
                        "--n-box-shadow-focus": w,
                        "--n-loading-color": U,
                        "--n-caret-color-warning": s,
                        "--n-color-focus-warning": C,
                        "--n-box-shadow-focus-warning": z,
                        "--n-border-warning": F,
                        "--n-border-focus-warning": S,
                        "--n-border-hover-warning": $,
                        "--n-loading-color-warning": G,
                        "--n-caret-color-error": i,
                        "--n-color-focus-error": B,
                        "--n-box-shadow-focus-error": R,
                        "--n-border-error": k,
                        "--n-border-focus-error": T,
                        "--n-border-hover-error": E,
                        "--n-loading-color-error": L,
                        "--n-clear-color": _,
                        "--n-clear-size": P,
                        "--n-clear-color-hover": D,
                        "--n-clear-color-pressed": I,
                        "--n-icon-color": M,
                        "--n-icon-color-hover": K,
                        "--n-icon-color-pressed": H,
                        "--n-icon-color-disabled": W,
                        "--n-suffix-text-color": V
                    }
                })),
                Be = r ? F("input", d((() => {
                    const {
                        value: e
                    } = X;
                    return e[0]
                })), $e, e) : void 0;
            return Object.assign(Object.assign({}, Fe), {
                wrapperElRef: l,
                inputElRef: $,
                inputMirrorElRef: h,
                inputEl2Ref: k,
                textareaElRef: i,
                textareaMirrorElRef: c,
                textareaScrollbarInstRef: _,
                rtlEnabled: Se,
                uncontrolledValue: O,
                mergedValue: K,
                passwordVisible: ue,
                mergedPlaceholder: re,
                showPlaceholder1: te,
                showPlaceholder2: ae,
                mergedFocus: le,
                isComposing: ee,
                activated: ne,
                showClearButton: ie,
                mergedSize: X,
                mergedDisabled: q,
                textDecorationStyle: ce,
                mergedClsPrefix: n,
                mergedBordered: o,
                mergedShowPasswordOn: se,
                placeholderStyle: Ce,
                mergedStatus: J,
                textAreaScrollContainerWidth: de,
                handleTextAreaScroll: function(e) {
                    var n;
                    const {
                        scrollTop: o
                    } = e.target;
                    Ce.value.top = -o + "px", null === (n = _.value) || void 0 === n || n.syncUnifiedContainer()
                },
                handleCompositionStart: function() {
                    ee.value = !0
                },
                handleCompositionEnd: function(e) {
                    ee.value = !1, e.target === k.value ? ge(e, 1) : ge(e, 0)
                },
                handleInput: ge,
                handleInputBlur: function(n) {
                    ! function(n) {
                        const {
                            onInputBlur: o
                        } = e;
                        o && P(o, n)
                    }(n), n.relatedTarget === l.value && function() {
                        const {
                            onDeactivate: n
                        } = e;
                        n && P(n)
                    }(), (null === n.relatedTarget || n.relatedTarget !== $.value && n.relatedTarget !== k.value && n.relatedTarget !== i.value) && (ne.value = !1), be(n, "blur"), T.value = null
                },
                handleInputFocus: function(n, o) {
                    ! function(n) {
                        const {
                            onInputFocus: o
                        } = e;
                        o && P(o, n)
                    }(n), Z.value = !0, ne.value = !0,
                        function() {
                            const {
                                onActivate: n
                            } = e;
                            n && P(n)
                        }(), be(n, "focus"), 0 === o ? T.value = $.value : 1 === o ? T.value = k.value : 2 === o && (T.value = i.value)
                },
                handleWrapperBlur: function(n) {
                    e.passivelyActivated && (! function(n) {
                        const {
                            onWrapperBlur: o
                        } = e;
                        o && P(o, n)
                    }(n), be(n, "blur"))
                },
                handleWrapperFocus: function(n) {
                    e.passivelyActivated && (Z.value = !0, function(n) {
                        const {
                            onWrapperFocus: o
                        } = e;
                        o && P(o, n)
                    }(n), be(n, "focus"))
                },
                handleMouseEnter: function() {
                    var n;
                    Q.value = !0, "textarea" === e.type && (null === (n = _.value) || void 0 === n || n.handleMouseEnterWrapper())
                },
                handleMouseLeave: function() {
                    var n;
                    Q.value = !1, "textarea" === e.type && (null === (n = _.value) || void 0 === n || n.handleMouseLeaveWrapper())
                },
                handleMouseDown: function(n) {
                    const {
                        onMousedown: o
                    } = e;
                    o && o(n);
                    const {
                        tagName: r
                    } = n.target;
                    if ("INPUT" !== r && "TEXTAREA" !== r) {
                        if (e.resizable) {
                            const {
                                value: e
                            } = l;
                            if (e) {
                                const {
                                    left: o,
                                    top: r,
                                    width: t,
                                    height: a
                                } = e.getBoundingClientRect(), l = 14;
                                if (o + t - l < n.clientX && n.clientX < o + t && r + a - l < n.clientY && n.clientY < r + a) return
                            }
                        }
                        n.preventDefault(), Z.value || we()
                    }
                },
                handleChange: function(e, n) {
                    ge(e, n, "change")
                },
                handleClick: function(n) {
                    ! function(n) {
                        const {
                            onClick: o
                        } = e;
                        o && P(o, n)
                    }(n)
                },
                handleClear: function(n) {
                    ! function(n) {
                        const {
                            onClear: o
                        } = e;
                        o && P(o, n)
                    }(n), xe()
                },
                handlePasswordToggleClick: function() {
                    q.value || "click" === se.value && (ue.value = !ue.value)
                },
                handlePasswordToggleMousedown: function(e) {
                    if (q.value) return;
                    e.preventDefault();
                    const n = e => {
                        e.preventDefault(), M("mouseup", document, n)
                    };
                    if (I("mouseup", document, n), "mousedown" !== se.value) return;
                    ue.value = !0;
                    const o = () => {
                        ue.value = !1, M("mouseup", document, o)
                    };
                    I("mouseup", document, o)
                },
                handleWrapperKeydown: function(n) {
                    switch (e.onKeydown && P(e.onKeydown, n), n.key) {
                        case "Escape":
                            me();
                            break;
                        case "Enter":
                            ! function(n) {
                                var o, r;
                                if (e.passivelyActivated) {
                                    const {
                                        value: t
                                    } = ne;
                                    if (t) return void(e.internalDeactivateOnEnter && me());
                                    n.preventDefault(), "textarea" === e.type ? null === (o = i.value) || void 0 === o || o.focus() : null === (r = $.value) || void 0 === r || r.focus()
                                }
                            }(n)
                    }
                },
                handleWrapperKeyup: function(n) {
                    e.onKeyup && P(e.onKeyup, n)
                },
                handleTextAreaMirrorResize: function() {
                    (() => {
                        var n, o;
                        if ("textarea" === e.type) {
                            const {
                                autosize: r
                            } = e;
                            if (r && (de.value = null === (o = null === (n = _.value) || void 0 === n ? void 0 : n.$el) || void 0 === o ? void 0 : o.offsetWidth), !i.value) return;
                            if ("boolean" == typeof r) return;
                            const {
                                paddingTop: t,
                                paddingBottom: a,
                                lineHeight: l
                            } = window.getComputedStyle(i.value), s = Number(t.slice(0, -2)), u = Number(a.slice(0, -2)), d = Number(l.slice(0, -2)), {
                                value: h
                            } = c;
                            if (!h) return;
                            if (r.minRows) {
                                const e = `${s+u+d*Math.max(r.minRows,1)}px`;
                                h.style.minHeight = e
                            }
                            if (r.maxRows) {
                                const e = `${s+u+d*r.maxRows}px`;
                                h.style.maxHeight = e
                            }
                        }
                    })()
                },
                getTextareaScrollContainer: () => i.value,
                mergedTheme: a,
                cssVars: r ? void 0 : $e,
                themeClass: null == Be ? void 0 : Be.themeClass,
                onRender: null == Be ? void 0 : Be.onRender
            })
        },
        render() {
            var e, o;
            const {
                mergedClsPrefix: r,
                mergedStatus: t,
                themeClass: a,
                type: l,
                countGraphemes: i,
                onRender: s
            } = this, u = this.$slots;
            return null == s || s(), n("div", {
                ref: "wrapperElRef",
                class: [`${r}-input`, a, t && `${r}-input--${t}-status`, {
                    [`${r}-input--rtl`]: this.rtlEnabled,
                    [`${r}-input--disabled`]: this.mergedDisabled,
                    [`${r}-input--textarea`]: "textarea" === l,
                    [`${r}-input--resizable`]: this.resizable && !this.autosize,
                    [`${r}-input--autosize`]: this.autosize,
                    [`${r}-input--round`]: this.round && !("textarea" === l),
                    [`${r}-input--pair`]: this.pair,
                    [`${r}-input--focus`]: this.mergedFocus,
                    [`${r}-input--stateful`]: this.stateful
                }],
                style: this.cssVars,
                tabindex: this.mergedDisabled || !this.passivelyActivated || this.activated ? void 0 : 0,
                onFocus: this.handleWrapperFocus,
                onBlur: this.handleWrapperBlur,
                onClick: this.handleClick,
                onMousedown: this.handleMouseDown,
                onMouseenter: this.handleMouseEnter,
                onMouseleave: this.handleMouseLeave,
                onCompositionstart: this.handleCompositionStart,
                onCompositionend: this.handleCompositionEnd,
                onKeyup: this.handleWrapperKeyup,
                onKeydown: this.handleWrapperKeydown
            }, n("div", {
                class: `${r}-input-wrapper`
            }, T(u.prefix, (e => e && n("div", {
                class: `${r}-input__prefix`
            }, e))), "textarea" === l ? n(_, {
                ref: "textareaScrollbarInstRef",
                class: `${r}-input__textarea`,
                container: this.getTextareaScrollContainer,
                triggerDisplayManually: !0,
                useUnifiedContainer: !0,
                internalHoistYRail: !0
            }, {
                default: () => {
                    var e, o;
                    const {
                        textAreaScrollContainerWidth: t
                    } = this, a = {
                        width: this.autosize && t && `${t}px`
                    };
                    return n(h, null, n("textarea", Object.assign({}, this.inputProps, {
                        ref: "textareaElRef",
                        class: [`${r}-input__textarea-el`, null === (e = this.inputProps) || void 0 === e ? void 0 : e.class],
                        autofocus: this.autofocus,
                        rows: Number(this.rows),
                        placeholder: this.placeholder,
                        value: this.mergedValue,
                        disabled: this.mergedDisabled,
                        maxlength: i ? void 0 : this.maxlength,
                        minlength: i ? void 0 : this.minlength,
                        readonly: this.readonly,
                        tabindex: this.passivelyActivated && !this.activated ? -1 : void 0,
                        style: [this.textDecorationStyle[0], null === (o = this.inputProps) || void 0 === o ? void 0 : o.style, a],
                        onBlur: this.handleInputBlur,
                        onFocus: e => {
                            this.handleInputFocus(e, 2)
                        },
                        onInput: this.handleInput,
                        onChange: this.handleChange,
                        onScroll: this.handleTextAreaScroll
                    })), this.showPlaceholder1 ? n("div", {
                        class: `${r}-input__placeholder`,
                        style: [this.placeholderStyle, a],
                        key: "placeholder"
                    }, this.mergedPlaceholder[0]) : null, this.autosize ? n(D, {
                        onResize: this.handleTextAreaMirrorResize
                    }, {
                        default: () => n("div", {
                            ref: "textareaMirrorElRef",
                            class: `${r}-input__textarea-mirror`,
                            key: "mirror"
                        })
                    }) : null)
                }
            }) : n("div", {
                class: `${r}-input__input`
            }, n("input", Object.assign({
                type: "password" === l && this.mergedShowPasswordOn && this.passwordVisible ? "text" : l
            }, this.inputProps, {
                ref: "inputElRef",
                class: [`${r}-input__input-el`, null === (e = this.inputProps) || void 0 === e ? void 0 : e.class],
                style: [this.textDecorationStyle[0], null === (o = this.inputProps) || void 0 === o ? void 0 : o.style],
                tabindex: this.passivelyActivated && !this.activated ? -1 : void 0,
                placeholder: this.mergedPlaceholder[0],
                disabled: this.mergedDisabled,
                maxlength: i ? void 0 : this.maxlength,
                minlength: i ? void 0 : this.minlength,
                value: Array.isArray(this.mergedValue) ? this.mergedValue[0] : this.mergedValue,
                readonly: this.readonly,
                autofocus: this.autofocus,
                size: this.attrSize,
                onBlur: this.handleInputBlur,
                onFocus: e => {
                    this.handleInputFocus(e, 0)
                },
                onInput: e => {
                    this.handleInput(e, 0)
                },
                onChange: e => {
                    this.handleChange(e, 0)
                }
            })), this.showPlaceholder1 ? n("div", {
                class: `${r}-input__placeholder`
            }, n("span", null, this.mergedPlaceholder[0])) : null, this.autosize ? n("div", {
                class: `${r}-input__input-mirror`,
                key: "mirror",
                ref: "inputMirrorElRef"
            }, " ") : null), !this.pair && T(u.suffix, (e => e || this.clearable || this.showCount || this.mergedShowPasswordOn || void 0 !== this.loading ? n("div", {
                class: `${r}-input__suffix`
            }, [T(u["clear-icon-placeholder"], (e => (this.clearable || e) && n(O, {
                clsPrefix: r,
                show: this.showClearButton,
                onClear: this.handleClear
            }, {
                placeholder: () => e,
                icon: () => {
                    var e, n;
                    return null === (n = (e = this.$slots)["clear-icon"]) || void 0 === n ? void 0 : n.call(e)
                }
            }))), this.internalLoadingBeforeSuffix ? null : e, void 0 !== this.loading ? n(N, {
                clsPrefix: r,
                loading: this.loading,
                showArrow: !1,
                showClear: !1,
                style: this.cssVars
            }) : null, this.internalLoadingBeforeSuffix ? e : null, this.showCount && "textarea" !== this.type ? n(q, null, {
                default: e => {
                    var n;
                    const {
                        renderCount: o
                    } = this;
                    return o ? o(e) : null === (n = u.count) || void 0 === n ? void 0 : n.call(u, e)
                }
            }) : null, this.mergedShowPasswordOn && "password" === this.type ? n("div", {
                class: `${r}-input__eye`,
                onMousedown: this.handlePasswordToggleMousedown,
                onClick: this.handlePasswordToggleClick
            }, this.passwordVisible ? E(u["password-visible-icon"], (() => [n($, {
                clsPrefix: r
            }, {
                default: () => n(K, null)
            })])) : E(u["password-invisible-icon"], (() => [n($, {
                clsPrefix: r
            }, {
                default: () => n(H, null)
            })]))) : null]) : null))), this.pair ? n("span", {
                class: `${r}-input__separator`
            }, E(u.separator, (() => [this.separator]))) : null, this.pair ? n("div", {
                class: `${r}-input-wrapper`
            }, n("div", {
                class: `${r}-input__input`
            }, n("input", {
                ref: "inputEl2Ref",
                type: this.type,
                class: `${r}-input__input-el`,
                tabindex: this.passivelyActivated && !this.activated ? -1 : void 0,
                placeholder: this.mergedPlaceholder[1],
                disabled: this.mergedDisabled,
                maxlength: i ? void 0 : this.maxlength,
                minlength: i ? void 0 : this.minlength,
                value: Array.isArray(this.mergedValue) ? this.mergedValue[1] : void 0,
                readonly: this.readonly,
                style: this.textDecorationStyle[1],
                onBlur: this.handleInputBlur,
                onFocus: e => {
                    this.handleInputFocus(e, 1)
                },
                onInput: e => {
                    this.handleInput(e, 1)
                },
                onChange: e => {
                    this.handleChange(e, 1)
                }
            }), this.showPlaceholder2 ? n("div", {
                class: `${r}-input__placeholder`
            }, n("span", null, this.mergedPlaceholder[1])) : null), T(u.suffix, (e => (this.clearable || e) && n("div", {
                class: `${r}-input__suffix`
            }, [this.clearable && n(O, {
                clsPrefix: r,
                show: this.showClearButton,
                onClear: this.handleClear
            }, {
                icon: () => {
                    var e;
                    return null === (e = u["clear-icon"]) || void 0 === e ? void 0 : e.call(u)
                },
                placeholder: () => {
                    var e;
                    return null === (e = u["clear-icon-placeholder"]) || void 0 === e ? void 0 : e.call(u)
                }
            }), e])))) : null, this.mergedBordered ? n("div", {
                class: `${r}-input__border`
            }) : null, this.mergedBordered ? n("div", {
                class: `${r}-input__state-border`
            }) : null, this.showCount && "textarea" === l ? n(q, null, {
                default: e => {
                    var n;
                    const {
                        renderCount: o
                    } = this;
                    return o ? o(e) : null === (n = u.count) || void 0 === n ? void 0 : n.call(u, e)
                }
            }) : null)
        }
    });
export {
    K as E, J as N
};