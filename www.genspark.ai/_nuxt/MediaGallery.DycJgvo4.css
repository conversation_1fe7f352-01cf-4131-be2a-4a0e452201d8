.media-gallery[data-v-e47f9881] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 0 20px;
    width: 100%
}

.input[data-v-e47f9881] {
    width: 100%
}

.images[data-v-e47f9881] {
    flex-wrap: wrap;
    gap: var(--flow_item_column_gap);
    justify-content: flex-start
}

.images[data-v-e47f9881],
.search-filters[data-v-e47f9881] {
    display: flex;
    flex-direction: row;
    width: 100%
}

.search-filters[data-v-e47f9881] {
    align-items: center;
    background-color: #fff;
    box-shadow: 0 10px 10px #fff;
    gap: 16px;
    justify-content: center;
    max-width: 860px;
    padding: 16px;
    position: sticky;
    top: 0;
    z-index: 100
}

.search-filters[data-v-e47f9881] .n-input {
    width: 100%
}

@media (max-width:800px) {
    .section[data-v-e47f9881] {
        padding: 16px 12px
    }
    .search-filters[data-v-e47f9881] .n-input {
        width: 100%
    }
}

.image_column[data-v-e47f9881] {
    gap: 20px;
    width: var(--flow_item_column_width)
}

.image_column[data-v-e47f9881],
.image_item[data-v-e47f9881] {
    display: flex;
    flex-direction: column
}

.image_item[data-v-e47f9881] {
    align-items: center;
    border-radius: 12px;
    cursor: pointer;
    justify-content: center;
    overflow: hidden;
    position: relative
}

.image_item img[data-v-e47f9881] {
    border-radius: 12px;
    transition: transform .3s ease;
    width: 100%
}

.source[data-v-e47f9881] {
    background: #0009;
    border-radius: 28px;
    bottom: 12px;
    color: #fff;
    display: none;
    left: 12px;
    padding: 4px 12px;
    position: absolute
}

.image_item:hover img[data-v-e47f9881] {
    transform: scale(1.1)
}

.image_item:hover .source[data-v-e47f9881] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 4px
}

.source a[data-v-e47f9881] {
    color: #fff;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    text-decoration: none
}

.source .icon[data-v-e47f9881] {
    --iconColor: #fff
}

.loading-wrapper[data-v-e47f9881],
.source .icon[data-v-e47f9881] {
    align-items: center;
    display: flex;
    justify-content: center
}

.loading-wrapper[data-v-e47f9881] {
    border-radius: 8px;
    height: 36px;
    margin: 20px auto;
    width: 76px
}

.empty_page[data-v-e47f9881] {
    color: #999;
    padding: 60px 0;
    text-align: center
}

[data-v-e47f9881] .search-bar {
    background: #f5f5f5;
    border: 1px solid #f5f5f5;
    border-radius: 12px;
    min-height: 42px;
    padding: 2px 2px 2px 8px
}

[data-v-e47f9881] .search-bar .n-input__input-el {
    background: transparent;
    border: none;
    color: #232425;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin: 0;
    outline: none;
    padding: 3px 0
}

[data-v-e47f9881] .search-bar.n-input--focus {
    background: #0f7fff;
    border: 1px solid #0f7fff;
    box-shadow: 0 2px 15px #0f7fff40;
    transition: border-color .3s linear, background-color .3s linear, box-shadow .3s linear
}

[data-v-e47f9881] .search-bar.n-input--focus .n-input__input-el {
    caret-color: #fff;
    color: #fff;
    transition: color .3s linear, caret-color .3s linear
}

[data-v-e47f9881] .search-bar .n-input__placeholder {
    color: #999
}

[data-v-e47f9881] .search-bar.n-input--focus .n-input__placeholder {
    color: #ffffffa6
}

[data-v-e47f9881] .enter-icon {
    align-items: center;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    height: 32px;
    justify-content: center;
    margin-bottom: 2px;
    width: 32px
}

[data-v-e47f9881] .enter-icon.loading svg {
    animation: rotate-e47f9881 1s linear infinite
}

@keyframes rotate-e47f9881 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

@media (hover:hover) {
    [data-v-e47f9881] .enter-icon:hover {
        background: #ffffff1a
    }
}

[data-v-e47f9881] .search-bar .enter-icon * {
    fill: #000;
    transition: all .3s linear
}

[data-v-e47f9881] .search-bar.n-input--focus .enter-icon * {
    fill: #fff
}

#header[data-v-e47f9881] {
    align-items: flex-start;
    border-bottom: 1px solid transparent;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    min-width: 860px;
    position: relative;
    width: 100%
}

#header .logo[data-v-e47f9881] {
    cursor: pointer;
    height: 60px;
    justify-content: center;
    width: 107px
}

#header .logo[data-v-e47f9881],
.search-bar[data-v-e47f9881] {
    align-items: center;
    display: flex
}

.search-bar[data-v-e47f9881] {
    min-height: 36px
}

.search-bar[data-v-e47f9881],
.search-bar .input[data-v-e47f9881] {
    flex-direction: row;
    justify-content: flex-start;
    position: relative
}

.search-bar .input[data-v-e47f9881] {
    align-items: flex-end;
    background: #f5f5f5;
    border: 1px solid #f5f5f5;
    border-radius: 12px;
    min-height: 42px;
    outline: none;
    width: 100%;
    z-index: 5
}

.search-bar .input[data-v-e47f9881],
.user-info[data-v-e47f9881] {
    box-sizing: border-box;
    display: flex
}

.user-info[data-v-e47f9881] {
    align-items: center;
    height: 60px;
    margin-right: 24px;
    position: absolute;
    right: 0
}

.image_item:hover .number-label[data-v-e47f9881] {
    opacity: 1
}

.number-label[data-v-e47f9881] {
    align-items: center;
    background: #00000073;
    border-radius: 30px;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-size: 16px;
    font-weight: 700;
    height: 32px;
    justify-content: center;
    line-height: 1.5em;
    opacity: 0;
    padding: 6px 16px;
    position: absolute;
    right: 8px;
    top: 8px;
    transition: transform .3s, opacity .3s;
    z-index: 2
}

.number-label[data-v-e47f9881]:hover {
    background: #000;
    transform: scale(1.1)
}

.number-label:hover .prompt-tooltip[data-v-e47f9881] {
    display: block
}

.number-label[data-v-e47f9881]:hover {
    background: #000c
}

@media (hover:hover) {
    .image_item:hover .number-label[data-v-e47f9881] {
        opacity: 1
    }
}

@media (hover:none) {
    .number-label[data-v-e47f9881] {
        opacity: 1
    }
}

.remix-count[data-v-e47f9881] {
    align-items: center;
    border-radius: 8px;
    bottom: 8px;
    color: #fff;
    display: flex;
    font-size: 14px;
    gap: 4px;
    justify-content: center;
    left: 8px;
    padding: 0 4px;
    position: absolute
}

.remix-count p[data-v-e47f9881] {
    text-shadow: 0 1px 1px rgba(0, 0, 0, .25)
}

.remix-count .remix-icon[data-v-e47f9881] {
    height: 24px;
    width: 24px
}

.remix-count svg[data-v-e47f9881] {
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, .25))
}

.more[data-v-e47f9881] {
    height: 30px;
    width: 10px
}

.no-image-icon[data-v-e47f9881] {
    color: #00000020;
    cursor: default;
    height: 24px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    z-index: 1
}

.skeleton-wrapper[data-v-e47f9881] {
    border-radius: 12px;
    height: 0;
    overflow: hidden;
    position: relative;
    width: 100%
}

.animate-skeleton[data-v-e47f9881] {
    animation: skeleton-e47f9881 2s cubic-bezier(.36, 0, .64, 1) infinite;
    background-color: #efefef;
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0
}

.animate-skeleton.is-error[data-v-e47f9881] {
    animation: none
}

@keyframes skeleton-e47f9881 {
    0% {
        background-color: #efefef
    }
    50% {
        background-color: #f5f5f5
    }
    to {
        background-color: #efefef
    }
}

.actual-image[data-v-e47f9881] {
    border-radius: 12px;
    height: 100%;
    left: 0;
    -o-object-fit: cover;
    object-fit: cover;
    position: absolute;
    top: 0;
    transition: transform .3s ease;
    width: 100%
}

.actual-image.is-error[data-v-e47f9881] {
    cursor: default;
    opacity: 0
}

@media (max-width:980px) {
    .number-label[data-v-e47f9881] {
        display: none
    }
}

.basic-search-bar[data-v-e47f9881] {
    align-items: center;
    background: #f5f5f5;
    border: 1px solid #fff;
    border-radius: 18px;
    display: flex;
    max-width: 860px;
    overflow: hidden;
    transition: all .3s ease;
    width: 100%
}

.basic-search-bar.is-active[data-v-e47f9881] {
    border: 1px solid #efefef
}

.search-icon[data-v-e47f9881] {
    padding: 10px 8px 10px 12px
}

.basic-search-bar[data-v-e47f9881] svg {
    height: 16px;
    width: 16px;
    stroke: #232425
}

.basic-search-bar[data-v-e47f9881] svg path {
    stroke: #232425
}

.basic-search-bar input[data-v-e47f9881] {
    background: transparent;
    border: none;
    color: #232425;
    flex: 1;
    font-size: 14px;
    line-height: 16px;
    outline: none;
    padding: 10px 10px 10px 0
}

.basic-search-bar input[data-v-e47f9881]::-moz-placeholder {
    color: #23242573
}

.basic-search-bar input[data-v-e47f9881]::placeholder {
    color: #23242573
}

.upload-button[data-v-e47f9881] {
    align-items: center;
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 18px;
    display: flex;
    flex-shrink: 0;
    gap: 8px;
    justify-content: center;
    padding: 10px 20px
}

.upload-button[data-v-e47f9881]:hover {
    background: #f5f5f5;
    cursor: pointer
}

.upload-text[data-v-e47f9881] {
    color: #232425;
    flex-shrink: 0;
    font-size: 14px;
    line-height: 16px
}

.upload-icon[data-v-e47f9881] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    width: 16px
}

.upload-icon[data-v-e47f9881] svg {
    height: 16px;
    width: 16px
}

.remix-button[data-v-e47f9881] {
    align-items: center;
    background: #00000065;
    border-radius: 18px;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 12px;
    gap: 8px;
    justify-content: center;
    line-height: 18px;
    opacity: 0;
    padding: 6px 16px;
    position: absolute;
    right: 8px;
    top: 8px;
    transition: opacity .3s ease
}

.remix-button[data-v-e47f9881] svg {
    height: 18px;
    width: 18px
}

.image_item:hover .hover-show[data-v-e47f9881] {
    opacity: 1
}

@media (prefers-color-scheme:dark) {
    .section[data-v-e47f9881] {
        background: #222;
        border: 1px solid #333
    }
    [data-v-e47f9881] .search-bar {
        background: #333;
        border-color: #333
    }
    .search-filters[data-v-e47f9881] {
        background: #2a2a2a;
        box-shadow: 0 10px 10px #2a2a2a
    }
    .actual-image[data-v-e47f9881],
    .image_item[data-v-e47f9881] {
        background-color: transparent
    }
    .animate-skeleton[data-v-e47f9881],
    .basic-search-bar[data-v-e47f9881] {
        background-color: #333
    }
    .basic-search-bar[data-v-e47f9881] {
        border-color: #333
    }
    .basic-search-bar.is-active[data-v-e47f9881] {
        border-color: #555
    }
    .basic-search-bar[data-v-e47f9881] svg {
        stroke: #fff
    }
    .basic-search-bar[data-v-e47f9881] svg path {
        stroke: #fff
    }
    .basic-search-bar input[data-v-e47f9881] {
        color: #fff
    }
    .basic-search-bar input[data-v-e47f9881]::-moz-placeholder {
        color: #555
    }
    .basic-search-bar input[data-v-e47f9881]::placeholder {
        color: #555
    }
    .upload-button[data-v-e47f9881] {
        background-color: #333;
        border-color: #efefef30
    }
    .upload-text[data-v-e47f9881] {
        color: #fff
    }
    .upload-button[data-v-e47f9881]:hover {
        background-color: #f0f0f010
    }
    .no-image-icon[data-v-e47f9881] {
        color: #ffffff20
    }
    @keyframes skeleton-e47f9881 {
        0% {
            background-color: #333
        }
        50% {
            background-color: #555
        }
        to {
            background-color: #333
        }
    }
}

@media (max-width:1220px) {
    .remix-button[data-v-e47f9881] {
        opacity: 1
    }
}