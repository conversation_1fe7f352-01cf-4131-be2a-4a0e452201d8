import {
    c as r,
    t,
    b as o
} from "./BPQGB51Y.js";
import {
    b1 as n,
    aA as a,
    b2 as u,
    b3 as s
} from "./Cf0SOiw0.js";

function f(r) {
    return "function" == typeof r ? r : n
}

function i(o, n, f, i) {
    if (!a(o)) return o;
    for (var e = -1, c = (n = r(n, o)).length, p = c - 1, b = o; null != b && ++e < c;) {
        var l = t(n[e]),
            v = f;
        if ("__proto__" === l || "constructor" === l || "prototype" === l) return o;
        if (e != p) {
            var m = b[l];
            void 0 === (v = i ? i(m, l, b) : void 0) && (v = a(m) ? m : u(n[e + 1]) ? [] : {})
        }
        s(b, l, v), b = b[l]
    }
    return o
}

function e(r, t, n, a) {
    return i(r, t, n(o(r, t)), a)
}

function c(r, t, o) {
    return null == r ? r : e(r, t, f(o))
}
export {
    e as a, i as b, f as c, c as u
};