import {
    J as e,
    s as n,
    r as a,
    v as l,
    c as o,
    d as t,
    f as i,
    a9 as c,
    b as d,
    t as s,
    F as u,
    H as r,
    L as m,
    n as f,
    a as p,
    p as g,
    o as y,
    U as v,
    _ as x
} from "./Cf0SOiw0.js";
import {
    L as b
} from "./BUCk-Nnr.js";
const L = {
        class: "confirmation-content"
    },
    B = {
        class: "confirmation-header"
    },
    V = ["disabled"],
    k = ["innerHTML"],
    h = ["innerHTML"],
    w = {
        key: 1,
        class: "input-container"
    },
    T = ["placeholder", "maxlength", "readonly"],
    C = {
        class: "confirmation-actions"
    },
    I = ["disabled"],
    _ = ["disabled"],
    H = x(e({
        __name: "GlobalDialog",
        props: {
            modelValue: {
                type: Boolean
            },
            mode: {
                default: "confirm"
            },
            title: {},
            content: {
                default: ""
            },
            warningContent: {},
            cancelButtonText: {},
            confirmButtonText: {},
            isDangerous: {
                type: Boolean,
                default: !1
            },
            initialValue: {
                default: ""
            },
            placeholder: {},
            maxLength: {
                default: 100
            },
            selectBeforeExtension: {
                type: Boolean,
                default: !1
            },
            allowEmptyValue: {
                type: Boolean,
                default: !1
            },
            confirmLoading: {
                type: [Boolean, null],
                default: null
            }
        },
        emits: ["update:modelValue", "confirm", "cancel", "input-confirm"],
        setup(e, {
            emit: x
        }) {
            const {
                t: H
            } = n(), E = e, M = x, j = a(E.initialValue), z = a(null);

            function D() {
                E.confirmLoading || (M("update:modelValue", !1), M("cancel"))
            }

            function G() {
                E.confirmLoading || (E.allowEmptyValue || j.value.trim()) && (null == E.confirmLoading && M("update:modelValue", !1), M("input-confirm", j.value))
            }
            l((() => E.initialValue), (e => {
                j.value = e
            })), l((() => E.modelValue), (e => {
                e && "input" === E.mode && v((() => {
                    if (z.value)
                        if (z.value.focus(), E.selectBeforeExtension && j.value) {
                            const e = j.value.lastIndexOf(".");
                            e > 0 ? z.value.setSelectionRange(0, e) : z.value.select()
                        } else z.value.select()
                }))
            })), l((() => E.modelValue), (e => {
                const n = document.querySelector(".j-input-wrapper-wrapper");
                n && (e ? (n.style.zIndex, n.style.zIndex = "0") : n.style.zIndex = "")
            }));
            const K = o((() => E.cancelButtonText || H("components.aidrive.dialog.cancel"))),
                S = o((() => E.confirmButtonText || H("components.aidrive.dialog.confirm")));
            return (e, n) => e.modelValue ? (y(), t("div", {
                key: 0,
                class: "confirmation-dialog",
                onKeydown: c(D, ["esc"]),
                tabindex: "0"
            }, [d("div", L, [d("div", B, [d("h3", null, s(e.title), 1), d("button", {
                class: "close-btn",
                onClick: D,
                disabled: !!e.confirmLoading
            }, " × ", 8, V)]), "confirm" === e.mode ? (y(), t(u, {
                key: 0
            }, [d("p", {
                innerHTML: e.content
            }, null, 8, k), e.warningContent ? (y(), t("p", {
                key: 0,
                class: "delete-warning",
                innerHTML: e.warningContent
            }, null, 8, h)) : i("", !0)], 64)) : "input" === e.mode ? (y(), t("div", w, [r(d("input", {
                type: "text",
                "onUpdate:modelValue": n[0] || (n[0] = e => j.value = e),
                class: "dialog-input",
                ref_key: "dialogInput",
                ref: z,
                placeholder: e.placeholder || "",
                onKeydown: c(G, ["enter"]),
                maxlength: e.maxLength || 100,
                readonly: !!e.confirmLoading
            }, null, 40, T), [
                [m, j.value]
            ])])) : i("", !0), d("div", C, [e.cancelButtonText ? (y(), t("button", {
                key: 0,
                class: "cancel-btn",
                onClick: D,
                disabled: !!e.confirmLoading
            }, s(K.value), 9, I)) : i("", !0), e.confirmButtonText ? (y(), t("button", {
                key: 1,
                class: f(["confirm-btn", {
                    "delete-btn": e.isDangerous
                }]),
                onClick: n[1] || (n[1] = n => "input" === e.mode ? G() : (M("update:modelValue", !1), void M("confirm"))),
                disabled: !!e.confirmLoading
            }, [e.confirmLoading ? (y(), p(b, {
                key: 0,
                color: "black"
            })) : (y(), t(u, {
                key: 1
            }, [g(s(S.value), 1)], 64))], 10, _)) : i("", !0)])])], 32)) : i("", !0)
        }
    }), [
        ["__scopeId", "data-v-ee822bf8"]
    ]);
export {
    H as G
};