import {
    d as o,
    b as n,
    o as t
} from "./Cf0SOiw0.js";
const r = {
    width: "16",
    height: "16",
    viewBox: "0 0 16 16",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const s = {
    render: function(s, e) {
        return t(), o("svg", r, e[0] || (e[0] = [n("path", {
            d: "M4 7.63365L7 10.794L13 5",
            stroke: "#0F7FFF",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }, null, -1)]))
    }
};
export {
    s as S
};