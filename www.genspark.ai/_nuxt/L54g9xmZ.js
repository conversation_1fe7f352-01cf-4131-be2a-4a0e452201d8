import {
    _ as e
} from "./e-ES_T8J.js";
import {
    _ as a
} from "./V-H-Vsd5.js";
import {
    _ as l,
    r as t,
    C as i,
    h as n,
    x as s,
    c as r,
    d as o,
    F as u,
    k as c,
    o as d,
    q as v,
    f as p,
    b as m,
    e as b,
    t as g,
    s as h,
    a3 as f,
    v as y,
    a as S,
    w,
    U as M,
    a8 as I,
    E as k,
    a9 as C
} from "./Cf0SOiw0.js";
import {
    _
} from "./D6bQc9d9.js";
import {
    _ as R
} from "./CAmLbDGM.js";
import {
    N as E
} from "./DXvAIxvL.js";
import {
    N as A
} from "./B-XpIQkh.js";
import {
    N as P
} from "./C38RzRfR.js";
const x = ["onClick"],
    F = {
        key: 0,
        class: "flow-item-company-logo"
    },
    D = ["src"],
    T = {
        class: "flow-item-content"
    },
    L = {
        key: 1,
        class: "flow-item-title"
    },
    O = {
        class: "flow-item-date"
    },
    G = l({
        __name: "FlowItemsGrid",
        props: {
            flow_items: {
                type: Array,
                required: !0
            },
            columnWidth: {
                type: Number,
                default: 208
            },
            maxRows: {
                type: Number,
                default: void 0
            },
            fixedSize: {
                type: Boolean,
                default: !1
            },
            maxColumns: {
                type: Number,
                default: void 0
            }
        },
        emits: ["item-click"],
        setup(e, {
            emit: l
        }) {
            const h = e;
            t(!1).value = i.isDarkMode();
            const f = e => {
                    if (h.fixedSize) {
                        const a = h.columnWidth * (2 / 3);
                        let l, t = 0;
                        if (e.original_width / e.original_height > h.columnWidth / a) {
                            l = h.columnWidth / e.original_width;
                            return t = (a - e.original_height * l) / 2, {
                                transform: `scale(${l}) translateY(${t/l}px)`,
                                height: `${a}px`,
                                transformOrigin: "left top"
                            }
                        } {
                            l = a / e.original_height;
                            const t = e.original_width * l;
                            return {
                                transform: `scale(${l}) translateX(${(h.columnWidth-t)/2/l}px)`,
                                height: `${a}px`,
                                transformOrigin: "left top"
                            }
                        }
                    }
                    const a = (l = e.original_width, t = e.original_height, i = h.columnWidth, t * (i / l));
                    var l, t, i;
                    return {
                        transform: `scale(${a/e.original_height})`,
                        height: `${a}px`,
                        transformOrigin: "left top"
                    }
                },
                y = t(null),
                S = t(0),
                w = () => {
                    var e;
                    S.value = (null == (e = y.value) ? void 0 : e.clientWidth) || 0
                };
            n((() => {
                w(), window.addEventListener("resize", w)
            })), s((() => {
                window.removeEventListener("resize", w)
            }));
            const M = r((() => h.maxColumns ? h.maxColumns : Math.floor(S.value / h.columnWidth))),
                I = r((() => {
                    if (!h.maxRows) return h.flow_items.length;
                    const e = Math.max(1, M.value || 1);
                    return h.maxRows * e
                })),
                k = r((() => h.flow_items.slice(0, I.value)));
            return (l, t) => {
                const i = a;
                return d(), o("div", {
                    class: "flow-items-grid",
                    ref_key: "gridRef",
                    ref: y
                }, [(d(!0), o(u, null, c(k.value, (a => {
                    return d(), o("div", {
                        key: a.id,
                        class: "flow-item-wrapper",
                        onClick: e => l.$emit("item-click", a),
                        style: v({
                            width: e.columnWidth + "px"
                        })
                    }, [a.company_logo ? (d(), o("div", F, [m("img", {
                        src: a.company_logo,
                        alt: "Company Logo"
                    }, null, 8, D)])) : p("", !0), m("div", T, [m("div", {
                        style: v(f(a))
                    }, [b(i, {
                        id: a.data.id,
                        data: a.data
                    }, null, 8, ["id", "data"])], 4)]), a.title ? (d(), o("div", L, g(a.title), 1)) : p("", !0), m("div", O, g((t = a.date, new Date(t).toLocaleDateString("en-US", {
                        month: "short",
                        day: "numeric",
                        year: "numeric"
                    }))), 1)], 12, x);
                    var t
                })), 128))], 512)
            }
        }
    }, [
        ["__scopeId", "data-v-89a54f8a"]
    ]),
    B = {
        class: "error-container"
    },
    q = {
        class: "error-box"
    },
    W = {
        class: "error-title"
    },
    U = {
        key: 0,
        class: "error-message"
    },
    N = l({
        __name: "NoResults",
        props: {
            title: {
                type: String,
                required: !0
            },
            description: {
                type: String,
                default: ""
            },
            showFeedbackButton: {
                type: Boolean,
                default: !0
            },
            feedbackText: {
                type: String,
                default: "Send Feedback"
            }
        },
        emits: ["feedback"],
        setup(e, {
            emit: a
        }) {
            const l = a,
                t = () => {
                    l("feedback")
                };
            return (a, l) => (d(), o("div", B, [m("div", q, [m("h2", W, g(e.title), 1), e.description ? (d(), o("p", U, g(e.description), 1)) : p("", !0), e.showFeedbackButton ? (d(), o("button", {
                key: 1,
                class: "home-btn",
                onClick: t
            }, g(e.feedbackText), 1)) : p("", !0)])]))
        }
    }, [
        ["__scopeId", "data-v-39491914"]
    ]),
    H = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 512 512"
    };
const j = {
        render: function(e, a) {
            return d(), o("svg", H, a[0] || (a[0] = [m("path", {
                d: "M221.09 64a157.09 157.09 0 1 0 157.09 157.09A157.1 157.1 0 0 0 221.09 64z",
                fill: "none",
                stroke: "currentColor",
                "stroke-miterlimit": "10",
                "stroke-width": "32"
            }, null, -1), m("path", {
                fill: "none",
                stroke: "currentColor",
                "stroke-linecap": "round",
                "stroke-miterlimit": "10",
                "stroke-width": "32",
                d: "M338.29 338.29L448 448"
            }, null, -1)]))
        }
    },
    $ = {
        name: "LatestReports",
        components: {
            FlowItemsGrid: G,
            NInput: A,
            NIcon: E,
            NSelect: P,
            SearchOutline: j,
            LoadingAnimation: _,
            NoResults: N,
            FeedbackDialog: R
        },
        props: {
            title: {
                type: String,
                default: () => h().t("pages.finance.latest-reports")
            },
            showMoreButton: {
                type: Boolean,
                default: !0
            },
            showFilters: {
                type: Boolean,
                default: !0
            },
            queryParams: {
                type: Object,
                default: () => ({})
            },
            componentWidth: {
                type: Number,
                required: !0
            },
            maxColumns: {
                type: Number,
                default: void 0
            },
            maxRows: {
                type: Number,
                default: void 0
            },
            externalLatestItems: {
                type: Array,
                default: null
            },
            maxColumnWidth: {
                type: Number,
                default: 265
            }
        },
        setup(e, {
            emit: a
        }) {
            const l = r((() => null !== e.externalLatestItems ? e.externalLatestItems : s.value)),
                s = t([]),
                o = t(""),
                u = t(!1),
                c = t(e.queryParams.company || ""),
                d = t(""),
                v = t(""),
                p = t(""),
                m = t("");
            t(e.maxColumns);
            const b = t(1),
                g = t(!1),
                S = t(null),
                w = t(null),
                k = t(!1);
            t(!1).value = i.isDarkMode();
            const {
                t: C
            } = h(), _ = t(e.maxRows), R = [{
                label: C("pages.finance.all-industries"),
                value: ""
            }, {
                label: "Steel",
                value: "Steel"
            }, {
                label: "Silver",
                value: "Silver"
            }, {
                label: "Other Precious Metals",
                value: "Other Precious Metals"
            }, {
                label: "Gold",
                value: "Gold"
            }, {
                label: "Copper",
                value: "Copper"
            }, {
                label: "Aluminum",
                value: "Aluminum"
            }, {
                label: "Paper, Lumber & Forest Products",
                value: "Paper, Lumber & Forest Products"
            }, {
                label: "Industrial Materials",
                value: "Industrial Materials"
            }, {
                label: "Construction Materials",
                value: "Construction Materials"
            }, {
                label: "Chemicals - Specialty",
                value: "Chemicals - Specialty"
            }, {
                label: "Chemicals",
                value: "Chemicals"
            }, {
                label: "Agricultural Inputs",
                value: "Agricultural Inputs"
            }, {
                label: "Telecommunications Services",
                value: "Telecommunications Services"
            }, {
                label: "Internet Content & Information",
                value: "Internet Content & Information"
            }, {
                label: "Publishing",
                value: "Publishing"
            }, {
                label: "Broadcasting",
                value: "Broadcasting"
            }, {
                label: "Advertising Agencies",
                value: "Advertising Agencies"
            }, {
                label: "Entertainment",
                value: "Entertainment"
            }, {
                label: "Travel Lodging",
                value: "Travel Lodging"
            }, {
                label: "Travel Services",
                value: "Travel Services"
            }, {
                label: "Specialty Retail",
                value: "Specialty Retail"
            }, {
                label: "Luxury Goods",
                value: "Luxury Goods"
            }, {
                label: "Home Improvement",
                value: "Home Improvement"
            }, {
                label: "Residential Construction",
                value: "Residential Construction"
            }, {
                label: "Department Stores",
                value: "Department Stores"
            }, {
                label: "Personal Products & Services",
                value: "Personal Products & Services"
            }, {
                label: "Leisure",
                value: "Leisure"
            }, {
                label: "Gambling, Resorts & Casinos",
                value: "Gambling, Resorts & Casinos"
            }, {
                label: "Furnishings, Fixtures & Appliances",
                value: "Furnishings, Fixtures & Appliances"
            }, {
                label: "Restaurants",
                value: "Restaurants"
            }, {
                label: "Auto - Parts",
                value: "Auto - Parts"
            }, {
                label: "Auto - Manufacturers",
                value: "Auto - Manufacturers"
            }, {
                label: "Auto - Recreational Vehicles",
                value: "Auto - Recreational Vehicles"
            }, {
                label: "Auto - Dealerships",
                value: "Auto - Dealerships"
            }, {
                label: "Apparel - Retail",
                value: "Apparel - Retail"
            }, {
                label: "Apparel - Manufacturers",
                value: "Apparel - Manufacturers"
            }, {
                label: "Apparel - Footwear & Accessories",
                value: "Apparel - Footwear & Accessories"
            }, {
                label: "Packaging & Containers",
                value: "Packaging & Containers"
            }, {
                label: "Tobacco",
                value: "Tobacco"
            }, {
                label: "Grocery Stores",
                value: "Grocery Stores"
            }, {
                label: "Discount Stores",
                value: "Discount Stores"
            }, {
                label: "Household & Personal Products",
                value: "Household & Personal Products"
            }, {
                label: "Packaged Foods",
                value: "Packaged Foods"
            }, {
                label: "Food Distribution",
                value: "Food Distribution"
            }, {
                label: "Food Confectioners",
                value: "Food Confectioners"
            }, {
                label: "Agricultural Farm Products",
                value: "Agricultural Farm Products"
            }, {
                label: "Education & Training Services",
                value: "Education & Training Services"
            }, {
                label: "Beverages - Wineries & Distilleries",
                value: "Beverages - Wineries & Distilleries"
            }, {
                label: "Beverages - Non-Alcoholic",
                value: "Beverages - Non-Alcoholic"
            }, {
                label: "Beverages - Alcoholic",
                value: "Beverages - Alcoholic"
            }, {
                label: "Uranium",
                value: "Uranium"
            }, {
                label: "Solar",
                value: "Solar"
            }, {
                label: "Oil & Gas Refining & Marketing",
                value: "Oil & Gas Refining & Marketing"
            }, {
                label: "Oil & Gas Midstream",
                value: "Oil & Gas Midstream"
            }, {
                label: "Oil & Gas Integrated",
                value: "Oil & Gas Integrated"
            }, {
                label: "Oil & Gas Exploration & Production",
                value: "Oil & Gas Exploration & Production"
            }, {
                label: "Oil & Gas Equipment & Services",
                value: "Oil & Gas Equipment & Services"
            }, {
                label: "Oil & Gas Energy",
                value: "Oil & Gas Energy"
            }, {
                label: "Oil & Gas Drilling",
                value: "Oil & Gas Drilling"
            }, {
                label: "Coal",
                value: "Coal"
            }, {
                label: "Shell Companies",
                value: "Shell Companies"
            }, {
                label: "Investment - Banking & Investment Services",
                value: "Investment - Banking & Investment Services"
            }, {
                label: "Insurance - Specialty",
                value: "Insurance - Specialty"
            }, {
                label: "Insurance - Reinsurance",
                value: "Insurance - Reinsurance"
            }, {
                label: "Insurance - Property & Casualty",
                value: "Insurance - Property & Casualty"
            }, {
                label: "Insurance - Life",
                value: "Insurance - Life"
            }, {
                label: "Insurance - Diversified",
                value: "Insurance - Diversified"
            }, {
                label: "Insurance - Brokers",
                value: "Insurance - Brokers"
            }, {
                label: "Financial - Mortgages",
                value: "Financial - Mortgages"
            }, {
                label: "Financial - Diversified",
                value: "Financial - Diversified"
            }, {
                label: "Financial - Data & Stock Exchanges",
                value: "Financial - Data & Stock Exchanges"
            }, {
                label: "Financial - Credit Services",
                value: "Financial - Credit Services"
            }, {
                label: "Financial - Conglomerates",
                value: "Financial - Conglomerates"
            }, {
                label: "Financial - Capital Markets",
                value: "Financial - Capital Markets"
            }, {
                label: "Banks - Regional",
                value: "Banks - Regional"
            }, {
                label: "Banks - Diversified",
                value: "Banks - Diversified"
            }, {
                label: "Banks",
                value: "Banks"
            }, {
                label: "Asset Management",
                value: "Asset Management"
            }, {
                label: "Asset Management - Bonds",
                value: "Asset Management - Bonds"
            }, {
                label: "Asset Management - Income",
                value: "Asset Management - Income"
            }, {
                label: "Asset Management - Leveraged",
                value: "Asset Management - Leveraged"
            }, {
                label: "Asset Management - Cryptocurrency",
                value: "Asset Management - Cryptocurrency"
            }, {
                label: "Asset Management - Global",
                value: "Asset Management - Global"
            }, {
                label: "Medical - Specialties",
                value: "Medical - Specialties"
            }, {
                label: "Medical - Pharmaceuticals",
                value: "Medical - Pharmaceuticals"
            }, {
                label: "Medical - Instruments & Supplies",
                value: "Medical - Instruments & Supplies"
            }, {
                label: "Medical - Healthcare Plans",
                value: "Medical - Healthcare Plans"
            }, {
                label: "Medical - Healthcare Information Services",
                value: "Medical - Healthcare Information Services"
            }, {
                label: "Medical - Equipment & Services",
                value: "Medical - Equipment & Services"
            }, {
                label: "Medical - Distribution",
                value: "Medical - Distribution"
            }, {
                label: "Medical - Diagnostics & Research",
                value: "Medical - Diagnostics & Research"
            }, {
                label: "Medical - Devices",
                value: "Medical - Devices"
            }, {
                label: "Medical - Care Facilities",
                value: "Medical - Care Facilities"
            }, {
                label: "Drug Manufacturers - Specialty & Generic",
                value: "Drug Manufacturers - Specialty & Generic"
            }, {
                label: "Drug Manufacturers - General",
                value: "Drug Manufacturers - General"
            }, {
                label: "Biotechnology",
                value: "Biotechnology"
            }, {
                label: "Waste Management",
                value: "Waste Management"
            }, {
                label: "Trucking",
                value: "Trucking"
            }, {
                label: "Railroads",
                value: "Railroads"
            }, {
                label: "Aerospace & Defense",
                value: "Aerospace & Defense"
            }, {
                label: "Marine Shipping",
                value: "Marine Shipping"
            }, {
                label: "Integrated Freight & Logistics",
                value: "Integrated Freight & Logistics"
            }, {
                label: "Airlines, Airports & Air Services",
                value: "Airlines, Airports & Air Services"
            }, {
                label: "General Transportation",
                value: "General Transportation"
            }, {
                label: "Manufacturing - Tools & Accessories",
                value: "Manufacturing - Tools & Accessories"
            }, {
                label: "Manufacturing - Textiles",
                value: "Manufacturing - Textiles"
            }, {
                label: "Manufacturing - Miscellaneous",
                value: "Manufacturing - Miscellaneous"
            }, {
                label: "Manufacturing - Metal Fabrication",
                value: "Manufacturing - Metal Fabrication"
            }, {
                label: "Industrial - Distribution",
                value: "Industrial - Distribution"
            }, {
                label: "Industrial - Specialties",
                value: "Industrial - Specialties"
            }, {
                label: "Industrial - Pollution & Treatment Controls",
                value: "Industrial - Pollution & Treatment Controls"
            }, {
                label: "Environmental Services",
                value: "Environmental Services"
            }, {
                label: "Industrial - Machinery",
                value: "Industrial - Machinery"
            }, {
                label: "Industrial - Infrastructure Operations",
                value: "Industrial - Infrastructure Operations"
            }, {
                label: "Industrial - Capital Goods",
                value: "Industrial - Capital Goods"
            }, {
                label: "Consulting Services",
                value: "Consulting Services"
            }, {
                label: "Business Equipment & Supplies",
                value: "Business Equipment & Supplies"
            }, {
                label: "Staffing & Employment Services",
                value: "Staffing & Employment Services"
            }, {
                label: "Rental & Leasing Services",
                value: "Rental & Leasing Services"
            }, {
                label: "Engineering & Construction",
                value: "Engineering & Construction"
            }, {
                label: "Security & Protection Services",
                value: "Security & Protection Services"
            }, {
                label: "Specialty Business Services",
                value: "Specialty Business Services"
            }, {
                label: "Construction",
                value: "Construction"
            }, {
                label: "Conglomerates",
                value: "Conglomerates"
            }, {
                label: "Electrical Equipment & Parts",
                value: "Electrical Equipment & Parts"
            }, {
                label: "Agricultural - Machinery",
                value: "Agricultural - Machinery"
            }, {
                label: "Agricultural - Commodities/Milling",
                value: "Agricultural - Commodities/Milling"
            }, {
                label: "REIT - Specialty",
                value: "REIT - Specialty"
            }, {
                label: "REIT - Retail",
                value: "REIT - Retail"
            }, {
                label: "REIT - Residential",
                value: "REIT - Residential"
            }, {
                label: "REIT - Office",
                value: "REIT - Office"
            }, {
                label: "REIT - Mortgage",
                value: "REIT - Mortgage"
            }, {
                label: "REIT - Industrial",
                value: "REIT - Industrial"
            }, {
                label: "REIT - Hotel & Motel",
                value: "REIT - Hotel & Motel"
            }, {
                label: "REIT - Healthcare Facilities",
                value: "REIT - Healthcare Facilities"
            }, {
                label: "REIT - Diversified",
                value: "REIT - Diversified"
            }, {
                label: "Real Estate - Services",
                value: "Real Estate - Services"
            }, {
                label: "Real Estate - Diversified",
                value: "Real Estate - Diversified"
            }, {
                label: "Real Estate - Development",
                value: "Real Estate - Development"
            }, {
                label: "Real Estate - General",
                value: "Real Estate - General"
            }, {
                label: "Information Technology Services",
                value: "Information Technology Services"
            }, {
                label: "Hardware, Equipment & Parts",
                value: "Hardware, Equipment & Parts"
            }, {
                label: "Computer Hardware",
                value: "Computer Hardware"
            }, {
                label: "Electronic Gaming & Multimedia",
                value: "Electronic Gaming & Multimedia"
            }, {
                label: "Software - Services",
                value: "Software - Services"
            }, {
                label: "Software - Infrastructure",
                value: "Software - Infrastructure"
            }, {
                label: "Software - Application",
                value: "Software - Application"
            }, {
                label: "Semiconductors",
                value: "Semiconductors"
            }, {
                label: "Media & Entertainment",
                value: "Media & Entertainment"
            }, {
                label: "Communication Equipment",
                value: "Communication Equipment"
            }, {
                label: "Technology Distributors",
                value: "Technology Distributors"
            }, {
                label: "Consumer Electronics",
                value: "Consumer Electronics"
            }, {
                label: "Renewable Utilities",
                value: "Renewable Utilities"
            }, {
                label: "Regulated Water",
                value: "Regulated Water"
            }, {
                label: "Regulated Gas",
                value: "Regulated Gas"
            }, {
                label: "Regulated Electric",
                value: "Regulated Electric"
            }, {
                label: "Independent Power Producers",
                value: "Independent Power Producers"
            }, {
                label: "Diversified Utilities",
                value: "Diversified Utilities"
            }, {
                label: "General Utilities",
                value: "General Utilities"
            }], E = [{
                label: C("pages.finance.all-fiscal-years"),
                value: ""
            }, {
                label: "2025",
                value: "2025"
            }, {
                label: "2024",
                value: "2024"
            }, {
                label: "2023",
                value: "2023"
            }], A = [{
                label: C("pages.finance.all-periods"),
                value: ""
            }, {
                label: "Q1",
                value: "Q1"
            }, {
                label: "Q2",
                value: "Q2"
            }, {
                label: "Q3",
                value: "Q3"
            }, {
                label: "Annual",
                value: "FY"
            }], P = [{
                label: C("pages.finance.all-companies"),
                value: ""
            }, {
                label: C("pages.finance.popular"),
                value: "hot"
            }], x = async (e = {}, a = 1) => {
                var l;
                i.log("Fetching flow items for page:", a);
                const t = ((e = {}) => {
                    const a = new URL("/api/copilot/get_flow_items", window.location.origin);
                    return a.searchParams.set("type", "COPILOT_FINANCE"), Object.entries(e).forEach((([e, l]) => {
                        l && a.searchParams.set(e, l)
                    })), a
                })(e);
                t.searchParams.set("page", a.toString());
                const n = await fetch(t, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        update_flow_data: null
                    })
                });
                if (!n.ok) throw new Error("Server error");
                const s = await n.json();
                return i.log("Fetched data:", s), s && -5 === s.status ? (I("/login"), null) : (null == (l = null == s ? void 0 : s.data) ? void 0 : l.flow_items) || []
            }, F = async (a = !1) => {
                try {
                    i.log("Loading latest items, isLoadMore:", a), u.value = !0;
                    const l = { ...e.queryParams,
                            ...c.value && {
                                company: c.value
                            },
                            ...d.value && {
                                industry: d.value
                            },
                            ...v.value && {
                                fiscal_year: v.value
                            },
                            ...p.value && {
                                period: p.value
                            },
                            ...m.value && {
                                report_type: m.value
                            }
                        },
                        t = await x(l, b.value);
                    s.value = a ? [...s.value, ...t] : t, k.value = !0, i.log("Latest items loaded:", s.value)
                } catch (l) {
                    o.value = "Failed to load data. Please try again later."
                } finally {
                    u.value = !1
                }
            };
            "IntersectionObserver" in window && (w.value = new IntersectionObserver((e => {
                e.forEach((async e => {
                    e.isIntersecting && (i.log("Load more element is intersecting"), await (async () => {
                        u.value || g.value ? i.log("Load more skipped, loading:", u.value, "noMoreData:", g.value) : (b.value++, i.log("Loading more items, new page:", b.value), await F(!0))
                    })())
                }))
            })));
            const D = async () => {
                    s.value = [], b.value = 1, k.value = !1;
                    const e = {
                        company: c.value || void 0,
                        industry: d.value || void 0,
                        fiscal_year: v.value || void 0,
                        period: p.value || void 0,
                        report_type: m.value || void 0
                    };
                    Object.keys(e).forEach((a => {
                        e[a] || delete e[a]
                    })), await F()
                },
                T = r((() => e.maxColumns));
            r((() => e.columnWidth));
            const L = r((() => !g.value && !e.maxRows || u.value));
            n((() => {
                var e;
                i.log("Component mounted, initializing data load"), O(), F(), L.value && S.value && (null == (e = w.value) || e.observe(S.value))
            })), f((() => {
                var e;
                S.value && (null == (e = w.value) || e.unobserve(S.value))
            }));
            const O = () => {
                    c.value = e.queryParams.company || "", d.value = e.queryParams.industry || "", v.value = e.queryParams.fiscal_year || "", p.value = e.queryParams.period || "", m.value = e.queryParams.report_type || ""
                },
                G = r((() => {
                    i.log("LatestReports got componentWidth", e.componentWidth);
                    const a = e.componentWidth > 800 ? 208 : 150,
                        l = e.maxColumnWidth,
                        t = e.componentWidth - 0 - 0;
                    i.log("LatestReports got componentWidth", e.componentWidth), i.log("LatestReports got availableWidth", t);
                    const n = Math.floor((t + 20) / (a + 20));
                    i.log("LatestReports calculatedMaxColumns", n);
                    const s = e.maxColumns || n,
                        r = (t - 20 * (s - 1)) / s;
                    return Math.min(l, Math.max(a, r))
                }));
            y((() => e.queryParams), (e => {
                c.value = e.company || "", d.value = e.industry || "", v.value = e.fiscal_year || "", p.value = e.period || "", m.value = e.report_type || ""
            }), {
                deep: !0
            });
            const B = t(!1);
            return {
                latestItems: l,
                searchQuery: c,
                selectedIndustry: d,
                selectedYear: v,
                selectedPeriod: p,
                industryOptions: R,
                yearOptions: E,
                periodOptions: A,
                latestItemsLoading: u,
                handleSearch: D,
                handleItemClick: async e => {
                    if (e.post_payload) try {
                        if ("action_ask_query" in e.post_payload) {
                            const a = e.post_payload.action_ask_query,
                                l = e.post_payload.action_ask_image ? `![Image](${e.post_payload.action_ask_image})\n\n` : "";
                            i.log("Handle ask action:", {
                                ask_query: a,
                                ask_image: l
                            })
                        } else {
                            const a = await fetch("/api/copilot/create_product_spark", {
                                    method: "POST",
                                    headers: {
                                        "Content-Type": "application/json"
                                    },
                                    body: JSON.stringify({
                                        payload: { ...e.post_payload,
                                            original_width: e.original_width,
                                            original_height: e.original_height
                                        }
                                    })
                                }),
                                l = await a.json();
                            l && "ok" === l.status && window.open(l.data.redirect_url, "_blank")
                        }
                    } catch (a) {} else e.transition_to_spark_url ? window.open(e.transition_to_spark_url, "_blank") : e.open_spark_id ? window.open(`/spark?id=${e.open_spark_id}`, "_blank") : e.link && window.open(e.link, "_blank")
                },
                handleMoreClick: () => {
                    let a = "/finance/report";
                    if (Object.keys(e.queryParams).length > 0) {
                        a += `?${new URLSearchParams(e.queryParams).toString()}`
                    }
                    I(a)
                },
                selectedReportType: m,
                reportTypeOptions: P,
                handleClear: async () => {
                    c.value = "", s.value = [], b.value = 1, k.value = !1, await M(), D()
                },
                maxColumns: T,
                columnWidth: G,
                page: b,
                noMoreData: g,
                loadMoreElem: S,
                dataLoaded: k,
                maxRows: _,
                shouldShowLoadMore: L,
                t: C,
                showFeedbackDialog: B,
                openFeedbackDialog: e => {
                    var a, l;
                    null == (a = null == e ? void 0 : e.preventDefault) || a.call(e), null == (l = null == e ? void 0 : e.stopPropagation) || l.call(e), B.value = !0
                }
            }
        }
    },
    Q = {
        class: "section"
    },
    z = {
        class: "section-header"
    },
    Y = {
        class: "section-title"
    },
    K = {
        key: 0,
        class: "search-container"
    },
    J = {
        class: "search-filters"
    },
    V = {
        key: 2,
        class: "load-more",
        ref: "loadMoreElem"
    };
const X = l($, [
    ["render", function(a, l, t, i, n, s) {
        const r = k("SearchOutline"),
            u = k("n-icon"),
            c = k("n-input"),
            v = k("n-select"),
            h = e,
            f = G,
            y = N,
            M = _,
            I = R;
        return d(), o("div", Q, [m("div", z, [m("h2", Y, g(t.title), 1), t.showMoreButton ? (d(), o("button", {
            key: 0,
            class: "more-button",
            onClick: l[0] || (l[0] = (...e) => i.handleMoreClick && i.handleMoreClick(...e))
        }, g(i.t("pages.finance.more")), 1)) : p("", !0)]), b(h, null, {
            default: w((() => [t.showFilters ? (d(), o("div", K, [m("div", J, [b(c, {
                value: i.searchQuery,
                "onUpdate:value": l[1] || (l[1] = e => i.searchQuery = e),
                placeholder: i.t("pages.finance.search-by-company-name-or-symbol"),
                clearable: "",
                onKeydown: C(i.handleSearch, ["enter"]),
                onClear: i.handleClear
            }, {
                prefix: w((() => [b(u, null, {
                    default: w((() => [b(r)])),
                    _: 1
                })])),
                _: 1
            }, 8, ["value", "placeholder", "onKeydown", "onClear"]), b(v, {
                value: i.selectedIndustry,
                "onUpdate:value": [l[2] || (l[2] = e => i.selectedIndustry = e), i.handleSearch],
                options: i.industryOptions,
                clearable: "",
                filterable: "",
                placeholder: i.t("pages.finance.select-or-search-industry"),
                "default-value": null
            }, null, 8, ["value", "options", "onUpdate:value", "placeholder"]), b(v, {
                value: i.selectedYear,
                "onUpdate:value": [l[3] || (l[3] = e => i.selectedYear = e), i.handleSearch],
                options: i.yearOptions,
                clearable: "",
                placeholder: i.t("pages.finance.select-year"),
                "default-value": null
            }, null, 8, ["value", "options", "onUpdate:value", "placeholder"]), b(v, {
                value: i.selectedPeriod,
                "onUpdate:value": [l[4] || (l[4] = e => i.selectedPeriod = e), i.handleSearch],
                options: i.periodOptions,
                clearable: "",
                placeholder: i.t("pages.finance.select-period"),
                "default-value": null
            }, null, 8, ["value", "options", "onUpdate:value", "placeholder"]), b(v, {
                value: i.selectedReportType,
                "onUpdate:value": [l[5] || (l[5] = e => i.selectedReportType = e), i.handleSearch],
                options: i.reportTypeOptions,
                clearable: "",
                placeholder: i.t("pages.finance.report-type"),
                "default-value": null
            }, null, 8, ["value", "options", "onUpdate:value", "placeholder"])])])) : p("", !0)])),
            _: 1
        }), i.latestItems && i.latestItems.length ? (d(), S(f, {
            key: 0,
            flow_items: i.latestItems,
            "column-width": i.columnWidth,
            "max-columns": i.maxColumns,
            "fixed-size": !0,
            onItemClick: i.handleItemClick,
            "max-rows": i.maxRows
        }, null, 8, ["flow_items", "column-width", "max-columns", "onItemClick", "max-rows"])) : i.dataLoaded && !i.latestItemsLoading ? (d(), S(y, {
            key: 1,
            title: i.t("pages.finance.reports.no-results-found-title"),
            "feedback-text": i.t("components.feedback.provide-feedback"),
            onFeedback: i.openFeedbackDialog
        }, null, 8, ["title", "feedback-text", "onFeedback"])) : p("", !0), i.shouldShowLoadMore ? (d(), o("div", V, [i.latestItemsLoading ? (d(), S(M, {
            key: 0
        })) : p("", !0)], 512)) : p("", !0), b(I, {
            show: i.showFeedbackDialog,
            "onUpdate:show": l[6] || (l[6] = e => i.showFeedbackDialog = e)
        }, null, 8, ["show"])])
    }],
    ["__scopeId", "data-v-d6be05f3"]
]);
export {
    G as F, j as S, X as _, N as a
};