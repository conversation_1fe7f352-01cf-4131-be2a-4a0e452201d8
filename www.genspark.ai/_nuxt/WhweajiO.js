import {
    d as o,
    o as r,
    b as t
} from "./Cf0SOiw0.js";
const n = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const e = {
    render: function(e, s) {
        return r(), o("svg", n, s[0] || (s[0] = [t("path", {
            d: "M3 10.7647V15.3529C3 15.7898 3.18437 16.2087 3.51256 16.5176C3.84075 16.8265 4.28587 17 4.75 17H15.25C15.7141 17 16.1592 16.8265 16.4874 16.5176C16.8156 16.2087 17 15.7898 17 15.3529V10.7647M13.5 10.4118L10 13.7059M10 13.7059L6.5 10.4118M10 13.7059V3",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }, null, -1)]))
    }
};
export {
    e as D
};