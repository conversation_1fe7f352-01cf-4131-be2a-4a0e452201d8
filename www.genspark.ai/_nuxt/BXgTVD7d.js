import {
    M as e
} from "./BYvs8isC.js";
import {
    b as n,
    i as a,
    a as o,
    s as t,
    c as r,
    t as i
} from "./0tM3vo_n.js";
import {
    a2 as l,
    r as s,
    v as _,
    h as c,
    cg as u,
    d,
    o as m
} from "./Cf0SOiw0.js";
import {
    r as f
} from "./Db8eFYek.js";

function h(e) {
    const n = e.utils.escapeRE,
        a = e.utils.arrayReplaceAt,
        o = " \r\n$+<=>^`|~",
        t = e.utils.lib.ucmicro.P.source,
        r = e.utils.lib.ucmicro.Z.source;
    e.block.ruler.before("reference", "abbr_def", (function(e, n, a, o) {
        let t, r = e.bMarks[n] + e.tShift[n];
        const i = e.eMarks[n];
        if (r + 2 >= i) return !1;
        if (42 !== e.src.charCodeAt(r++)) return !1;
        if (91 !== e.src.charCodeAt(r++)) return !1;
        const l = r;
        for (; r < i; r++) {
            const n = e.src.charCodeAt(r);
            if (91 === n) return !1;
            if (93 === n) {
                t = r;
                break
            }
            92 === n && r++
        }
        if (t < 0 || 58 !== e.src.charCodeAt(t + 1)) return !1;
        if (o) return !0;
        const s = e.src.slice(l, t).replace(/\\(.)/g, "$1"),
            _ = e.src.slice(t + 2, i).trim();
        return 0 !== s.length && (0 !== _.length && (e.env.abbreviations || (e.env.abbreviations = {}), void 0 === e.env.abbreviations[":" + s] && (e.env.abbreviations[":" + s] = _), e.line = n + 1, !0))
    }), {
        alt: ["paragraph", "reference"]
    }), e.core.ruler.after("linkify", "abbr_replace", (function(e) {
        const i = e.tokens;
        if (!e.env.abbreviations) return;
        const l = new RegExp("(?:" + Object.keys(e.env.abbreviations).map((function(e) {
                return e.substr(1)
            })).sort((function(e, n) {
                return n.length - e.length
            })).map(n).join("|") + ")"),
            s = "(^|" + t + "|" + r + "|[" + o.split("").map(n).join("") + "])(" + Object.keys(e.env.abbreviations).map((function(e) {
                return e.substr(1)
            })).sort((function(e, n) {
                return n.length - e.length
            })).map(n).join("|") + ")($|" + t + "|" + r + "|[" + o.split("").map(n).join("") + "])",
            _ = new RegExp(s, "g");
        for (let n = 0, o = i.length; n < o; n++) {
            if ("inline" !== i[n].type) continue;
            let o = i[n].children;
            for (let t = o.length - 1; t >= 0; t--) {
                const r = o[t];
                if ("text" !== r.type) continue;
                let s = 0;
                const c = r.content;
                _.lastIndex = 0;
                const u = [];
                if (!l.test(c)) continue;
                let d;
                for (; d = _.exec(c);) {
                    if (d.index > 0 || d[1].length > 0) {
                        const n = new e.Token("text", "", 0);
                        n.content = c.slice(s, d.index + d[1].length), u.push(n)
                    }
                    const n = new e.Token("abbr_open", "abbr", 1);
                    n.attrs = [
                        ["title", e.env.abbreviations[":" + d[2]]]
                    ], u.push(n);
                    const a = new e.Token("text", "", 0);
                    a.content = d[2], u.push(a);
                    const o = new e.Token("abbr_close", "abbr", -1);
                    u.push(o), _.lastIndex -= d[3].length, s = _.lastIndex
                }
                if (u.length) {
                    if (s < c.length) {
                        const n = new e.Token("text", "", 0);
                        n.content = c.slice(s), u.push(n)
                    }
                    i[n].children = o = a(o, t, u)
                }
            }
        }
    }))
}

function p(e) {
    const n = e.utils.isSpace;

    function a(e, n) {
        let a = e.bMarks[n] + e.tShift[n];
        const o = e.eMarks[n];
        if (a >= o) return -1;
        const t = e.src.charCodeAt(a++);
        if (126 !== t && 58 !== t) return -1;
        const r = e.skipSpaces(a);
        return a === r || r >= o ? -1 : a
    }
    e.block.ruler.before("paragraph", "deflist", (function(e, o, t, r) {
        if (r) return !(e.ddIndent < 0) && a(e, o) >= 0;
        let i = o + 1;
        if (i >= t) return !1;
        if (e.isEmpty(i) && (i++, i >= t)) return !1;
        if (e.sCount[i] < e.blkIndent) return !1;
        let l = a(e, i);
        if (l < 0) return !1;
        const s = e.tokens.length;
        let _ = !0;
        const c = [o, 0];
        e.push("dl_open", "dl", 1).map = c;
        let u = o,
            d = i;
        e: for (;;) {
            let o = !1;
            e.push("dt_open", "dt", 1).map = [u, u];
            const r = e.push("inline", "", 0);
            for (r.map = [u, u], r.content = e.getLines(u, u + 1, e.blkIndent, !1).trim(), r.children = [], e.push("dt_close", "dt", -1);;) {
                const r = [i, 0];
                e.push("dd_open", "dd", 1).map = r;
                let s = l;
                const c = e.eMarks[d];
                let u = e.sCount[d] + l - (e.bMarks[d] + e.tShift[d]);
                for (; s < c;) {
                    const a = e.src.charCodeAt(s);
                    if (!n(a)) break;
                    9 === a ? u += 4 - u % 4 : u++, s++
                }
                l = s;
                const m = e.tight,
                    f = e.ddIndent,
                    h = e.blkIndent,
                    p = e.tShift[d],
                    g = e.sCount[d],
                    b = e.parentType;
                if (e.blkIndent = e.ddIndent = e.sCount[d] + 2, e.tShift[d] = l - e.bMarks[d], e.sCount[d] = u, e.tight = !0, e.parentType = "deflist", e.md.block.tokenize(e, d, t, !0), e.tight && !o || (_ = !1), o = e.line - d > 1 && e.isEmpty(e.line - 1), e.tShift[d] = p, e.sCount[d] = g, e.tight = m, e.parentType = b, e.blkIndent = h, e.ddIndent = f, e.push("dd_close", "dd", -1), r[1] = i = e.line, i >= t) break e;
                if (e.sCount[i] < e.blkIndent) break e;
                if (l = a(e, i), l < 0) break;
                d = i
            }
            if (i >= t) break;
            if (u = i, e.isEmpty(u)) break;
            if (e.sCount[u] < e.blkIndent) break;
            if (d = u + 1, d >= t) break;
            if (e.isEmpty(d) && d++, d >= t) break;
            if (e.sCount[d] < e.blkIndent) break;
            if (l = a(e, d), l < 0) break
        }
        return e.push("dl_close", "dl", -1), c[1] = i, e.line = i, _ && function(e, n) {
            const a = e.level + 2;
            for (let o = n + 2, t = e.tokens.length - 2; o < t; o++) e.tokens[o].level === a && "paragraph_open" === e.tokens[o].type && (e.tokens[o + 2].hidden = !0, e.tokens[o].hidden = !0, o += 2)
        }(e, s), !0
    }), {
        alt: ["paragraph", "reference", "blockquote"]
    })
}
const g = {
    100: "💯",
    1234: "🔢",
    grinning: "😀",
    smiley: "😃",
    smile: "😄",
    grin: "😁",
    laughing: "😆",
    satisfied: "😆",
    sweat_smile: "😅",
    rofl: "🤣",
    joy: "😂",
    slightly_smiling_face: "🙂",
    upside_down_face: "🙃",
    wink: "😉",
    blush: "😊",
    innocent: "😇",
    smiling_face_with_three_hearts: "🥰",
    heart_eyes: "😍",
    star_struck: "🤩",
    kissing_heart: "😘",
    kissing: "😗",
    relaxed: "☺️",
    kissing_closed_eyes: "😚",
    kissing_smiling_eyes: "😙",
    smiling_face_with_tear: "🥲",
    yum: "😋",
    stuck_out_tongue: "😛",
    stuck_out_tongue_winking_eye: "😜",
    zany_face: "🤪",
    stuck_out_tongue_closed_eyes: "😝",
    money_mouth_face: "🤑",
    hugs: "🤗",
    hand_over_mouth: "🤭",
    shushing_face: "🤫",
    thinking: "🤔",
    zipper_mouth_face: "🤐",
    raised_eyebrow: "🤨",
    neutral_face: "😐",
    expressionless: "😑",
    no_mouth: "😶",
    smirk: "😏",
    unamused: "😒",
    roll_eyes: "🙄",
    grimacing: "😬",
    lying_face: "🤥",
    relieved: "😌",
    pensive: "😔",
    sleepy: "😪",
    drooling_face: "🤤",
    sleeping: "😴",
    mask: "😷",
    face_with_thermometer: "🤒",
    face_with_head_bandage: "🤕",
    nauseated_face: "🤢",
    vomiting_face: "🤮",
    sneezing_face: "🤧",
    hot_face: "🥵",
    cold_face: "🥶",
    woozy_face: "🥴",
    dizzy_face: "😵",
    exploding_head: "🤯",
    cowboy_hat_face: "🤠",
    partying_face: "🥳",
    disguised_face: "🥸",
    sunglasses: "😎",
    nerd_face: "🤓",
    monocle_face: "🧐",
    confused: "😕",
    worried: "😟",
    slightly_frowning_face: "🙁",
    frowning_face: "☹️",
    open_mouth: "😮",
    hushed: "😯",
    astonished: "😲",
    flushed: "😳",
    pleading_face: "🥺",
    frowning: "😦",
    anguished: "😧",
    fearful: "😨",
    cold_sweat: "😰",
    disappointed_relieved: "😥",
    cry: "😢",
    sob: "😭",
    scream: "😱",
    confounded: "😖",
    persevere: "😣",
    disappointed: "😞",
    sweat: "😓",
    weary: "😩",
    tired_face: "😫",
    yawning_face: "🥱",
    triumph: "😤",
    rage: "😡",
    pout: "😡",
    angry: "😠",
    cursing_face: "🤬",
    smiling_imp: "😈",
    imp: "👿",
    skull: "💀",
    skull_and_crossbones: "☠️",
    hankey: "💩",
    poop: "💩",
    shit: "💩",
    clown_face: "🤡",
    japanese_ogre: "👹",
    japanese_goblin: "👺",
    ghost: "👻",
    alien: "👽",
    space_invader: "👾",
    robot: "🤖",
    smiley_cat: "😺",
    smile_cat: "😸",
    joy_cat: "😹",
    heart_eyes_cat: "😻",
    smirk_cat: "😼",
    kissing_cat: "😽",
    scream_cat: "🙀",
    crying_cat_face: "😿",
    pouting_cat: "😾",
    see_no_evil: "🙈",
    hear_no_evil: "🙉",
    speak_no_evil: "🙊",
    kiss: "💋",
    love_letter: "💌",
    cupid: "💘",
    gift_heart: "💝",
    sparkling_heart: "💖",
    heartpulse: "💗",
    heartbeat: "💓",
    revolving_hearts: "💞",
    two_hearts: "💕",
    heart_decoration: "💟",
    heavy_heart_exclamation: "❣️",
    broken_heart: "💔",
    heart: "❤️",
    orange_heart: "🧡",
    yellow_heart: "💛",
    green_heart: "💚",
    blue_heart: "💙",
    purple_heart: "💜",
    brown_heart: "🤎",
    black_heart: "🖤",
    white_heart: "🤍",
    anger: "💢",
    boom: "💥",
    collision: "💥",
    dizzy: "💫",
    sweat_drops: "💦",
    dash: "💨",
    hole: "🕳️",
    bomb: "💣",
    speech_balloon: "💬",
    eye_speech_bubble: "👁️‍🗨️",
    left_speech_bubble: "🗨️",
    right_anger_bubble: "🗯️",
    thought_balloon: "💭",
    zzz: "💤",
    wave: "👋",
    raised_back_of_hand: "🤚",
    raised_hand_with_fingers_splayed: "🖐️",
    hand: "✋",
    raised_hand: "✋",
    vulcan_salute: "🖖",
    ok_hand: "👌",
    pinched_fingers: "🤌",
    pinching_hand: "🤏",
    v: "✌️",
    crossed_fingers: "🤞",
    love_you_gesture: "🤟",
    metal: "🤘",
    call_me_hand: "🤙",
    point_left: "👈",
    point_right: "👉",
    point_up_2: "👆",
    middle_finger: "🖕",
    fu: "🖕",
    point_down: "👇",
    point_up: "☝️",
    "+1": "👍",
    thumbsup: "👍",
    "-1": "👎",
    thumbsdown: "👎",
    fist_raised: "✊",
    fist: "✊",
    fist_oncoming: "👊",
    facepunch: "👊",
    punch: "👊",
    fist_left: "🤛",
    fist_right: "🤜",
    clap: "👏",
    raised_hands: "🙌",
    open_hands: "👐",
    palms_up_together: "🤲",
    handshake: "🤝",
    pray: "🙏",
    writing_hand: "✍️",
    nail_care: "💅",
    selfie: "🤳",
    muscle: "💪",
    mechanical_arm: "🦾",
    mechanical_leg: "🦿",
    leg: "🦵",
    foot: "🦶",
    ear: "👂",
    ear_with_hearing_aid: "🦻",
    nose: "👃",
    brain: "🧠",
    anatomical_heart: "🫀",
    lungs: "🫁",
    tooth: "🦷",
    bone: "🦴",
    eyes: "👀",
    eye: "👁️",
    tongue: "👅",
    lips: "👄",
    baby: "👶",
    child: "🧒",
    boy: "👦",
    girl: "👧",
    adult: "🧑",
    blond_haired_person: "👱",
    man: "👨",
    bearded_person: "🧔",
    red_haired_man: "👨‍🦰",
    curly_haired_man: "👨‍🦱",
    white_haired_man: "👨‍🦳",
    bald_man: "👨‍🦲",
    woman: "👩",
    red_haired_woman: "👩‍🦰",
    person_red_hair: "🧑‍🦰",
    curly_haired_woman: "👩‍🦱",
    person_curly_hair: "🧑‍🦱",
    white_haired_woman: "👩‍🦳",
    person_white_hair: "🧑‍🦳",
    bald_woman: "👩‍🦲",
    person_bald: "🧑‍🦲",
    blond_haired_woman: "👱‍♀️",
    blonde_woman: "👱‍♀️",
    blond_haired_man: "👱‍♂️",
    older_adult: "🧓",
    older_man: "👴",
    older_woman: "👵",
    frowning_person: "🙍",
    frowning_man: "🙍‍♂️",
    frowning_woman: "🙍‍♀️",
    pouting_face: "🙎",
    pouting_man: "🙎‍♂️",
    pouting_woman: "🙎‍♀️",
    no_good: "🙅",
    no_good_man: "🙅‍♂️",
    ng_man: "🙅‍♂️",
    no_good_woman: "🙅‍♀️",
    ng_woman: "🙅‍♀️",
    ok_person: "🙆",
    ok_man: "🙆‍♂️",
    ok_woman: "🙆‍♀️",
    tipping_hand_person: "💁",
    information_desk_person: "💁",
    tipping_hand_man: "💁‍♂️",
    sassy_man: "💁‍♂️",
    tipping_hand_woman: "💁‍♀️",
    sassy_woman: "💁‍♀️",
    raising_hand: "🙋",
    raising_hand_man: "🙋‍♂️",
    raising_hand_woman: "🙋‍♀️",
    deaf_person: "🧏",
    deaf_man: "🧏‍♂️",
    deaf_woman: "🧏‍♀️",
    bow: "🙇",
    bowing_man: "🙇‍♂️",
    bowing_woman: "🙇‍♀️",
    facepalm: "🤦",
    man_facepalming: "🤦‍♂️",
    woman_facepalming: "🤦‍♀️",
    shrug: "🤷",
    man_shrugging: "🤷‍♂️",
    woman_shrugging: "🤷‍♀️",
    health_worker: "🧑‍⚕️",
    man_health_worker: "👨‍⚕️",
    woman_health_worker: "👩‍⚕️",
    student: "🧑‍🎓",
    man_student: "👨‍🎓",
    woman_student: "👩‍🎓",
    teacher: "🧑‍🏫",
    man_teacher: "👨‍🏫",
    woman_teacher: "👩‍🏫",
    judge: "🧑‍⚖️",
    man_judge: "👨‍⚖️",
    woman_judge: "👩‍⚖️",
    farmer: "🧑‍🌾",
    man_farmer: "👨‍🌾",
    woman_farmer: "👩‍🌾",
    cook: "🧑‍🍳",
    man_cook: "👨‍🍳",
    woman_cook: "👩‍🍳",
    mechanic: "🧑‍🔧",
    man_mechanic: "👨‍🔧",
    woman_mechanic: "👩‍🔧",
    factory_worker: "🧑‍🏭",
    man_factory_worker: "👨‍🏭",
    woman_factory_worker: "👩‍🏭",
    office_worker: "🧑‍💼",
    man_office_worker: "👨‍💼",
    woman_office_worker: "👩‍💼",
    scientist: "🧑‍🔬",
    man_scientist: "👨‍🔬",
    woman_scientist: "👩‍🔬",
    technologist: "🧑‍💻",
    man_technologist: "👨‍💻",
    woman_technologist: "👩‍💻",
    singer: "🧑‍🎤",
    man_singer: "👨‍🎤",
    woman_singer: "👩‍🎤",
    artist: "🧑‍🎨",
    man_artist: "👨‍🎨",
    woman_artist: "👩‍🎨",
    pilot: "🧑‍✈️",
    man_pilot: "👨‍✈️",
    woman_pilot: "👩‍✈️",
    astronaut: "🧑‍🚀",
    man_astronaut: "👨‍🚀",
    woman_astronaut: "👩‍🚀",
    firefighter: "🧑‍🚒",
    man_firefighter: "👨‍🚒",
    woman_firefighter: "👩‍🚒",
    police_officer: "👮",
    cop: "👮",
    policeman: "👮‍♂️",
    policewoman: "👮‍♀️",
    detective: "🕵️",
    male_detective: "🕵️‍♂️",
    female_detective: "🕵️‍♀️",
    guard: "💂",
    guardsman: "💂‍♂️",
    guardswoman: "💂‍♀️",
    ninja: "🥷",
    construction_worker: "👷",
    construction_worker_man: "👷‍♂️",
    construction_worker_woman: "👷‍♀️",
    prince: "🤴",
    princess: "👸",
    person_with_turban: "👳",
    man_with_turban: "👳‍♂️",
    woman_with_turban: "👳‍♀️",
    man_with_gua_pi_mao: "👲",
    woman_with_headscarf: "🧕",
    person_in_tuxedo: "🤵",
    man_in_tuxedo: "🤵‍♂️",
    woman_in_tuxedo: "🤵‍♀️",
    person_with_veil: "👰",
    man_with_veil: "👰‍♂️",
    woman_with_veil: "👰‍♀️",
    bride_with_veil: "👰‍♀️",
    pregnant_woman: "🤰",
    breast_feeding: "🤱",
    woman_feeding_baby: "👩‍🍼",
    man_feeding_baby: "👨‍🍼",
    person_feeding_baby: "🧑‍🍼",
    angel: "👼",
    santa: "🎅",
    mrs_claus: "🤶",
    mx_claus: "🧑‍🎄",
    superhero: "🦸",
    superhero_man: "🦸‍♂️",
    superhero_woman: "🦸‍♀️",
    supervillain: "🦹",
    supervillain_man: "🦹‍♂️",
    supervillain_woman: "🦹‍♀️",
    mage: "🧙",
    mage_man: "🧙‍♂️",
    mage_woman: "🧙‍♀️",
    fairy: "🧚",
    fairy_man: "🧚‍♂️",
    fairy_woman: "🧚‍♀️",
    vampire: "🧛",
    vampire_man: "🧛‍♂️",
    vampire_woman: "🧛‍♀️",
    merperson: "🧜",
    merman: "🧜‍♂️",
    mermaid: "🧜‍♀️",
    elf: "🧝",
    elf_man: "🧝‍♂️",
    elf_woman: "🧝‍♀️",
    genie: "🧞",
    genie_man: "🧞‍♂️",
    genie_woman: "🧞‍♀️",
    zombie: "🧟",
    zombie_man: "🧟‍♂️",
    zombie_woman: "🧟‍♀️",
    massage: "💆",
    massage_man: "💆‍♂️",
    massage_woman: "💆‍♀️",
    haircut: "💇",
    haircut_man: "💇‍♂️",
    haircut_woman: "💇‍♀️",
    walking: "🚶",
    walking_man: "🚶‍♂️",
    walking_woman: "🚶‍♀️",
    standing_person: "🧍",
    standing_man: "🧍‍♂️",
    standing_woman: "🧍‍♀️",
    kneeling_person: "🧎",
    kneeling_man: "🧎‍♂️",
    kneeling_woman: "🧎‍♀️",
    person_with_probing_cane: "🧑‍🦯",
    man_with_probing_cane: "👨‍🦯",
    woman_with_probing_cane: "👩‍🦯",
    person_in_motorized_wheelchair: "🧑‍🦼",
    man_in_motorized_wheelchair: "👨‍🦼",
    woman_in_motorized_wheelchair: "👩‍🦼",
    person_in_manual_wheelchair: "🧑‍🦽",
    man_in_manual_wheelchair: "👨‍🦽",
    woman_in_manual_wheelchair: "👩‍🦽",
    runner: "🏃",
    running: "🏃",
    running_man: "🏃‍♂️",
    running_woman: "🏃‍♀️",
    woman_dancing: "💃",
    dancer: "💃",
    man_dancing: "🕺",
    business_suit_levitating: "🕴️",
    dancers: "👯",
    dancing_men: "👯‍♂️",
    dancing_women: "👯‍♀️",
    sauna_person: "🧖",
    sauna_man: "🧖‍♂️",
    sauna_woman: "🧖‍♀️",
    climbing: "🧗",
    climbing_man: "🧗‍♂️",
    climbing_woman: "🧗‍♀️",
    person_fencing: "🤺",
    horse_racing: "🏇",
    skier: "⛷️",
    snowboarder: "🏂",
    golfing: "🏌️",
    golfing_man: "🏌️‍♂️",
    golfing_woman: "🏌️‍♀️",
    surfer: "🏄",
    surfing_man: "🏄‍♂️",
    surfing_woman: "🏄‍♀️",
    rowboat: "🚣",
    rowing_man: "🚣‍♂️",
    rowing_woman: "🚣‍♀️",
    swimmer: "🏊",
    swimming_man: "🏊‍♂️",
    swimming_woman: "🏊‍♀️",
    bouncing_ball_person: "⛹️",
    bouncing_ball_man: "⛹️‍♂️",
    basketball_man: "⛹️‍♂️",
    bouncing_ball_woman: "⛹️‍♀️",
    basketball_woman: "⛹️‍♀️",
    weight_lifting: "🏋️",
    weight_lifting_man: "🏋️‍♂️",
    weight_lifting_woman: "🏋️‍♀️",
    bicyclist: "🚴",
    biking_man: "🚴‍♂️",
    biking_woman: "🚴‍♀️",
    mountain_bicyclist: "🚵",
    mountain_biking_man: "🚵‍♂️",
    mountain_biking_woman: "🚵‍♀️",
    cartwheeling: "🤸",
    man_cartwheeling: "🤸‍♂️",
    woman_cartwheeling: "🤸‍♀️",
    wrestling: "🤼",
    men_wrestling: "🤼‍♂️",
    women_wrestling: "🤼‍♀️",
    water_polo: "🤽",
    man_playing_water_polo: "🤽‍♂️",
    woman_playing_water_polo: "🤽‍♀️",
    handball_person: "🤾",
    man_playing_handball: "🤾‍♂️",
    woman_playing_handball: "🤾‍♀️",
    juggling_person: "🤹",
    man_juggling: "🤹‍♂️",
    woman_juggling: "🤹‍♀️",
    lotus_position: "🧘",
    lotus_position_man: "🧘‍♂️",
    lotus_position_woman: "🧘‍♀️",
    bath: "🛀",
    sleeping_bed: "🛌",
    people_holding_hands: "🧑‍🤝‍🧑",
    two_women_holding_hands: "👭",
    couple: "👫",
    two_men_holding_hands: "👬",
    couplekiss: "💏",
    couplekiss_man_woman: "👩‍❤️‍💋‍👨",
    couplekiss_man_man: "👨‍❤️‍💋‍👨",
    couplekiss_woman_woman: "👩‍❤️‍💋‍👩",
    couple_with_heart: "💑",
    couple_with_heart_woman_man: "👩‍❤️‍👨",
    couple_with_heart_man_man: "👨‍❤️‍👨",
    couple_with_heart_woman_woman: "👩‍❤️‍👩",
    family: "👪",
    family_man_woman_boy: "👨‍👩‍👦",
    family_man_woman_girl: "👨‍👩‍👧",
    family_man_woman_girl_boy: "👨‍👩‍👧‍👦",
    family_man_woman_boy_boy: "👨‍👩‍👦‍👦",
    family_man_woman_girl_girl: "👨‍👩‍👧‍👧",
    family_man_man_boy: "👨‍👨‍👦",
    family_man_man_girl: "👨‍👨‍👧",
    family_man_man_girl_boy: "👨‍👨‍👧‍👦",
    family_man_man_boy_boy: "👨‍👨‍👦‍👦",
    family_man_man_girl_girl: "👨‍👨‍👧‍👧",
    family_woman_woman_boy: "👩‍👩‍👦",
    family_woman_woman_girl: "👩‍👩‍👧",
    family_woman_woman_girl_boy: "👩‍👩‍👧‍👦",
    family_woman_woman_boy_boy: "👩‍👩‍👦‍👦",
    family_woman_woman_girl_girl: "👩‍👩‍👧‍👧",
    family_man_boy: "👨‍👦",
    family_man_boy_boy: "👨‍👦‍👦",
    family_man_girl: "👨‍👧",
    family_man_girl_boy: "👨‍👧‍👦",
    family_man_girl_girl: "👨‍👧‍👧",
    family_woman_boy: "👩‍👦",
    family_woman_boy_boy: "👩‍👦‍👦",
    family_woman_girl: "👩‍👧",
    family_woman_girl_boy: "👩‍👧‍👦",
    family_woman_girl_girl: "👩‍👧‍👧",
    speaking_head: "🗣️",
    bust_in_silhouette: "👤",
    busts_in_silhouette: "👥",
    people_hugging: "🫂",
    footprints: "👣",
    monkey_face: "🐵",
    monkey: "🐒",
    gorilla: "🦍",
    orangutan: "🦧",
    dog: "🐶",
    dog2: "🐕",
    guide_dog: "🦮",
    service_dog: "🐕‍🦺",
    poodle: "🐩",
    wolf: "🐺",
    fox_face: "🦊",
    raccoon: "🦝",
    cat: "🐱",
    cat2: "🐈",
    black_cat: "🐈‍⬛",
    lion: "🦁",
    tiger: "🐯",
    tiger2: "🐅",
    leopard: "🐆",
    horse: "🐴",
    racehorse: "🐎",
    unicorn: "🦄",
    zebra: "🦓",
    deer: "🦌",
    bison: "🦬",
    cow: "🐮",
    ox: "🐂",
    water_buffalo: "🐃",
    cow2: "🐄",
    pig: "🐷",
    pig2: "🐖",
    boar: "🐗",
    pig_nose: "🐽",
    ram: "🐏",
    sheep: "🐑",
    goat: "🐐",
    dromedary_camel: "🐪",
    camel: "🐫",
    llama: "🦙",
    giraffe: "🦒",
    elephant: "🐘",
    mammoth: "🦣",
    rhinoceros: "🦏",
    hippopotamus: "🦛",
    mouse: "🐭",
    mouse2: "🐁",
    rat: "🐀",
    hamster: "🐹",
    rabbit: "🐰",
    rabbit2: "🐇",
    chipmunk: "🐿️",
    beaver: "🦫",
    hedgehog: "🦔",
    bat: "🦇",
    bear: "🐻",
    polar_bear: "🐻‍❄️",
    koala: "🐨",
    panda_face: "🐼",
    sloth: "🦥",
    otter: "🦦",
    skunk: "🦨",
    kangaroo: "🦘",
    badger: "🦡",
    feet: "🐾",
    paw_prints: "🐾",
    turkey: "🦃",
    chicken: "🐔",
    rooster: "🐓",
    hatching_chick: "🐣",
    baby_chick: "🐤",
    hatched_chick: "🐥",
    bird: "🐦",
    penguin: "🐧",
    dove: "🕊️",
    eagle: "🦅",
    duck: "🦆",
    swan: "🦢",
    owl: "🦉",
    dodo: "🦤",
    feather: "🪶",
    flamingo: "🦩",
    peacock: "🦚",
    parrot: "🦜",
    frog: "🐸",
    crocodile: "🐊",
    turtle: "🐢",
    lizard: "🦎",
    snake: "🐍",
    dragon_face: "🐲",
    dragon: "🐉",
    sauropod: "🦕",
    "t-rex": "🦖",
    whale: "🐳",
    whale2: "🐋",
    dolphin: "🐬",
    flipper: "🐬",
    seal: "🦭",
    fish: "🐟",
    tropical_fish: "🐠",
    blowfish: "🐡",
    shark: "🦈",
    octopus: "🐙",
    shell: "🐚",
    snail: "🐌",
    butterfly: "🦋",
    bug: "🐛",
    ant: "🐜",
    bee: "🐝",
    honeybee: "🐝",
    beetle: "🪲",
    lady_beetle: "🐞",
    cricket: "🦗",
    cockroach: "🪳",
    spider: "🕷️",
    spider_web: "🕸️",
    scorpion: "🦂",
    mosquito: "🦟",
    fly: "🪰",
    worm: "🪱",
    microbe: "🦠",
    bouquet: "💐",
    cherry_blossom: "🌸",
    white_flower: "💮",
    rosette: "🏵️",
    rose: "🌹",
    wilted_flower: "🥀",
    hibiscus: "🌺",
    sunflower: "🌻",
    blossom: "🌼",
    tulip: "🌷",
    seedling: "🌱",
    potted_plant: "🪴",
    evergreen_tree: "🌲",
    deciduous_tree: "🌳",
    palm_tree: "🌴",
    cactus: "🌵",
    ear_of_rice: "🌾",
    herb: "🌿",
    shamrock: "☘️",
    four_leaf_clover: "🍀",
    maple_leaf: "🍁",
    fallen_leaf: "🍂",
    leaves: "🍃",
    grapes: "🍇",
    melon: "🍈",
    watermelon: "🍉",
    tangerine: "🍊",
    orange: "🍊",
    mandarin: "🍊",
    lemon: "🍋",
    banana: "🍌",
    pineapple: "🍍",
    mango: "🥭",
    apple: "🍎",
    green_apple: "🍏",
    pear: "🍐",
    peach: "🍑",
    cherries: "🍒",
    strawberry: "🍓",
    blueberries: "🫐",
    kiwi_fruit: "🥝",
    tomato: "🍅",
    olive: "🫒",
    coconut: "🥥",
    avocado: "🥑",
    eggplant: "🍆",
    potato: "🥔",
    carrot: "🥕",
    corn: "🌽",
    hot_pepper: "🌶️",
    bell_pepper: "🫑",
    cucumber: "🥒",
    leafy_green: "🥬",
    broccoli: "🥦",
    garlic: "🧄",
    onion: "🧅",
    mushroom: "🍄",
    peanuts: "🥜",
    chestnut: "🌰",
    bread: "🍞",
    croissant: "🥐",
    baguette_bread: "🥖",
    flatbread: "🫓",
    pretzel: "🥨",
    bagel: "🥯",
    pancakes: "🥞",
    waffle: "🧇",
    cheese: "🧀",
    meat_on_bone: "🍖",
    poultry_leg: "🍗",
    cut_of_meat: "🥩",
    bacon: "🥓",
    hamburger: "🍔",
    fries: "🍟",
    pizza: "🍕",
    hotdog: "🌭",
    sandwich: "🥪",
    taco: "🌮",
    burrito: "🌯",
    tamale: "🫔",
    stuffed_flatbread: "🥙",
    falafel: "🧆",
    egg: "🥚",
    fried_egg: "🍳",
    shallow_pan_of_food: "🥘",
    stew: "🍲",
    fondue: "🫕",
    bowl_with_spoon: "🥣",
    green_salad: "🥗",
    popcorn: "🍿",
    butter: "🧈",
    salt: "🧂",
    canned_food: "🥫",
    bento: "🍱",
    rice_cracker: "🍘",
    rice_ball: "🍙",
    rice: "🍚",
    curry: "🍛",
    ramen: "🍜",
    spaghetti: "🍝",
    sweet_potato: "🍠",
    oden: "🍢",
    sushi: "🍣",
    fried_shrimp: "🍤",
    fish_cake: "🍥",
    moon_cake: "🥮",
    dango: "🍡",
    dumpling: "🥟",
    fortune_cookie: "🥠",
    takeout_box: "🥡",
    crab: "🦀",
    lobster: "🦞",
    shrimp: "🦐",
    squid: "🦑",
    oyster: "🦪",
    icecream: "🍦",
    shaved_ice: "🍧",
    ice_cream: "🍨",
    doughnut: "🍩",
    cookie: "🍪",
    birthday: "🎂",
    cake: "🍰",
    cupcake: "🧁",
    pie: "🥧",
    chocolate_bar: "🍫",
    candy: "🍬",
    lollipop: "🍭",
    custard: "🍮",
    honey_pot: "🍯",
    baby_bottle: "🍼",
    milk_glass: "🥛",
    coffee: "☕",
    teapot: "🫖",
    tea: "🍵",
    sake: "🍶",
    champagne: "🍾",
    wine_glass: "🍷",
    cocktail: "🍸",
    tropical_drink: "🍹",
    beer: "🍺",
    beers: "🍻",
    clinking_glasses: "🥂",
    tumbler_glass: "🥃",
    cup_with_straw: "🥤",
    bubble_tea: "🧋",
    beverage_box: "🧃",
    mate: "🧉",
    ice_cube: "🧊",
    chopsticks: "🥢",
    plate_with_cutlery: "🍽️",
    fork_and_knife: "🍴",
    spoon: "🥄",
    hocho: "🔪",
    knife: "🔪",
    amphora: "🏺",
    earth_africa: "🌍",
    earth_americas: "🌎",
    earth_asia: "🌏",
    globe_with_meridians: "🌐",
    world_map: "🗺️",
    japan: "🗾",
    compass: "🧭",
    mountain_snow: "🏔️",
    mountain: "⛰️",
    volcano: "🌋",
    mount_fuji: "🗻",
    camping: "🏕️",
    beach_umbrella: "🏖️",
    desert: "🏜️",
    desert_island: "🏝️",
    national_park: "🏞️",
    stadium: "🏟️",
    classical_building: "🏛️",
    building_construction: "🏗️",
    bricks: "🧱",
    rock: "🪨",
    wood: "🪵",
    hut: "🛖",
    houses: "🏘️",
    derelict_house: "🏚️",
    house: "🏠",
    house_with_garden: "🏡",
    office: "🏢",
    post_office: "🏣",
    european_post_office: "🏤",
    hospital: "🏥",
    bank: "🏦",
    hotel: "🏨",
    love_hotel: "🏩",
    convenience_store: "🏪",
    school: "🏫",
    department_store: "🏬",
    factory: "🏭",
    japanese_castle: "🏯",
    european_castle: "🏰",
    wedding: "💒",
    tokyo_tower: "🗼",
    statue_of_liberty: "🗽",
    church: "⛪",
    mosque: "🕌",
    hindu_temple: "🛕",
    synagogue: "🕍",
    shinto_shrine: "⛩️",
    kaaba: "🕋",
    fountain: "⛲",
    tent: "⛺",
    foggy: "🌁",
    night_with_stars: "🌃",
    cityscape: "🏙️",
    sunrise_over_mountains: "🌄",
    sunrise: "🌅",
    city_sunset: "🌆",
    city_sunrise: "🌇",
    bridge_at_night: "🌉",
    hotsprings: "♨️",
    carousel_horse: "🎠",
    ferris_wheel: "🎡",
    roller_coaster: "🎢",
    barber: "💈",
    circus_tent: "🎪",
    steam_locomotive: "🚂",
    railway_car: "🚃",
    bullettrain_side: "🚄",
    bullettrain_front: "🚅",
    train2: "🚆",
    metro: "🚇",
    light_rail: "🚈",
    station: "🚉",
    tram: "🚊",
    monorail: "🚝",
    mountain_railway: "🚞",
    train: "🚋",
    bus: "🚌",
    oncoming_bus: "🚍",
    trolleybus: "🚎",
    minibus: "🚐",
    ambulance: "🚑",
    fire_engine: "🚒",
    police_car: "🚓",
    oncoming_police_car: "🚔",
    taxi: "🚕",
    oncoming_taxi: "🚖",
    car: "🚗",
    red_car: "🚗",
    oncoming_automobile: "🚘",
    blue_car: "🚙",
    pickup_truck: "🛻",
    truck: "🚚",
    articulated_lorry: "🚛",
    tractor: "🚜",
    racing_car: "🏎️",
    motorcycle: "🏍️",
    motor_scooter: "🛵",
    manual_wheelchair: "🦽",
    motorized_wheelchair: "🦼",
    auto_rickshaw: "🛺",
    bike: "🚲",
    kick_scooter: "🛴",
    skateboard: "🛹",
    roller_skate: "🛼",
    busstop: "🚏",
    motorway: "🛣️",
    railway_track: "🛤️",
    oil_drum: "🛢️",
    fuelpump: "⛽",
    rotating_light: "🚨",
    traffic_light: "🚥",
    vertical_traffic_light: "🚦",
    stop_sign: "🛑",
    construction: "🚧",
    anchor: "⚓",
    boat: "⛵",
    sailboat: "⛵",
    canoe: "🛶",
    speedboat: "🚤",
    passenger_ship: "🛳️",
    ferry: "⛴️",
    motor_boat: "🛥️",
    ship: "🚢",
    airplane: "✈️",
    small_airplane: "🛩️",
    flight_departure: "🛫",
    flight_arrival: "🛬",
    parachute: "🪂",
    seat: "💺",
    helicopter: "🚁",
    suspension_railway: "🚟",
    mountain_cableway: "🚠",
    aerial_tramway: "🚡",
    artificial_satellite: "🛰️",
    rocket: "🚀",
    flying_saucer: "🛸",
    bellhop_bell: "🛎️",
    luggage: "🧳",
    hourglass: "⌛",
    hourglass_flowing_sand: "⏳",
    watch: "⌚",
    alarm_clock: "⏰",
    stopwatch: "⏱️",
    timer_clock: "⏲️",
    mantelpiece_clock: "🕰️",
    clock12: "🕛",
    clock1230: "🕧",
    clock1: "🕐",
    clock130: "🕜",
    clock2: "🕑",
    clock230: "🕝",
    clock3: "🕒",
    clock330: "🕞",
    clock4: "🕓",
    clock430: "🕟",
    clock5: "🕔",
    clock530: "🕠",
    clock6: "🕕",
    clock630: "🕡",
    clock7: "🕖",
    clock730: "🕢",
    clock8: "🕗",
    clock830: "🕣",
    clock9: "🕘",
    clock930: "🕤",
    clock10: "🕙",
    clock1030: "🕥",
    clock11: "🕚",
    clock1130: "🕦",
    new_moon: "🌑",
    waxing_crescent_moon: "🌒",
    first_quarter_moon: "🌓",
    moon: "🌔",
    waxing_gibbous_moon: "🌔",
    full_moon: "🌕",
    waning_gibbous_moon: "🌖",
    last_quarter_moon: "🌗",
    waning_crescent_moon: "🌘",
    crescent_moon: "🌙",
    new_moon_with_face: "🌚",
    first_quarter_moon_with_face: "🌛",
    last_quarter_moon_with_face: "🌜",
    thermometer: "🌡️",
    sunny: "☀️",
    full_moon_with_face: "🌝",
    sun_with_face: "🌞",
    ringed_planet: "🪐",
    star: "⭐",
    star2: "🌟",
    stars: "🌠",
    milky_way: "🌌",
    cloud: "☁️",
    partly_sunny: "⛅",
    cloud_with_lightning_and_rain: "⛈️",
    sun_behind_small_cloud: "🌤️",
    sun_behind_large_cloud: "🌥️",
    sun_behind_rain_cloud: "🌦️",
    cloud_with_rain: "🌧️",
    cloud_with_snow: "🌨️",
    cloud_with_lightning: "🌩️",
    tornado: "🌪️",
    fog: "🌫️",
    wind_face: "🌬️",
    cyclone: "🌀",
    rainbow: "🌈",
    closed_umbrella: "🌂",
    open_umbrella: "☂️",
    umbrella: "☔",
    parasol_on_ground: "⛱️",
    zap: "⚡",
    snowflake: "❄️",
    snowman_with_snow: "☃️",
    snowman: "⛄",
    comet: "☄️",
    fire: "🔥",
    droplet: "💧",
    ocean: "🌊",
    jack_o_lantern: "🎃",
    christmas_tree: "🎄",
    fireworks: "🎆",
    sparkler: "🎇",
    firecracker: "🧨",
    sparkles: "✨",
    balloon: "🎈",
    tada: "🎉",
    confetti_ball: "🎊",
    tanabata_tree: "🎋",
    bamboo: "🎍",
    dolls: "🎎",
    flags: "🎏",
    wind_chime: "🎐",
    rice_scene: "🎑",
    red_envelope: "🧧",
    ribbon: "🎀",
    gift: "🎁",
    reminder_ribbon: "🎗️",
    tickets: "🎟️",
    ticket: "🎫",
    medal_military: "🎖️",
    trophy: "🏆",
    medal_sports: "🏅",
    "1st_place_medal": "🥇",
    "2nd_place_medal": "🥈",
    "3rd_place_medal": "🥉",
    soccer: "⚽",
    baseball: "⚾",
    softball: "🥎",
    basketball: "🏀",
    volleyball: "🏐",
    football: "🏈",
    rugby_football: "🏉",
    tennis: "🎾",
    flying_disc: "🥏",
    bowling: "🎳",
    cricket_game: "🏏",
    field_hockey: "🏑",
    ice_hockey: "🏒",
    lacrosse: "🥍",
    ping_pong: "🏓",
    badminton: "🏸",
    boxing_glove: "🥊",
    martial_arts_uniform: "🥋",
    goal_net: "🥅",
    golf: "⛳",
    ice_skate: "⛸️",
    fishing_pole_and_fish: "🎣",
    diving_mask: "🤿",
    running_shirt_with_sash: "🎽",
    ski: "🎿",
    sled: "🛷",
    curling_stone: "🥌",
    dart: "🎯",
    yo_yo: "🪀",
    kite: "🪁",
    "8ball": "🎱",
    crystal_ball: "🔮",
    magic_wand: "🪄",
    nazar_amulet: "🧿",
    video_game: "🎮",
    joystick: "🕹️",
    slot_machine: "🎰",
    game_die: "🎲",
    jigsaw: "🧩",
    teddy_bear: "🧸",
    pinata: "🪅",
    nesting_dolls: "🪆",
    spades: "♠️",
    hearts: "♥️",
    diamonds: "♦️",
    clubs: "♣️",
    chess_pawn: "♟️",
    black_joker: "🃏",
    mahjong: "🀄",
    flower_playing_cards: "🎴",
    performing_arts: "🎭",
    framed_picture: "🖼️",
    art: "🎨",
    thread: "🧵",
    sewing_needle: "🪡",
    yarn: "🧶",
    knot: "🪢",
    eyeglasses: "👓",
    dark_sunglasses: "🕶️",
    goggles: "🥽",
    lab_coat: "🥼",
    safety_vest: "🦺",
    necktie: "👔",
    shirt: "👕",
    tshirt: "👕",
    jeans: "👖",
    scarf: "🧣",
    gloves: "🧤",
    coat: "🧥",
    socks: "🧦",
    dress: "👗",
    kimono: "👘",
    sari: "🥻",
    one_piece_swimsuit: "🩱",
    swim_brief: "🩲",
    shorts: "🩳",
    bikini: "👙",
    womans_clothes: "👚",
    purse: "👛",
    handbag: "👜",
    pouch: "👝",
    shopping: "🛍️",
    school_satchel: "🎒",
    thong_sandal: "🩴",
    mans_shoe: "👞",
    shoe: "👞",
    athletic_shoe: "👟",
    hiking_boot: "🥾",
    flat_shoe: "🥿",
    high_heel: "👠",
    sandal: "👡",
    ballet_shoes: "🩰",
    boot: "👢",
    crown: "👑",
    womans_hat: "👒",
    tophat: "🎩",
    mortar_board: "🎓",
    billed_cap: "🧢",
    military_helmet: "🪖",
    rescue_worker_helmet: "⛑️",
    prayer_beads: "📿",
    lipstick: "💄",
    ring: "💍",
    gem: "💎",
    mute: "🔇",
    speaker: "🔈",
    sound: "🔉",
    loud_sound: "🔊",
    loudspeaker: "📢",
    mega: "📣",
    postal_horn: "📯",
    bell: "🔔",
    no_bell: "🔕",
    musical_score: "🎼",
    musical_note: "🎵",
    notes: "🎶",
    studio_microphone: "🎙️",
    level_slider: "🎚️",
    control_knobs: "🎛️",
    microphone: "🎤",
    headphones: "🎧",
    radio: "📻",
    saxophone: "🎷",
    accordion: "🪗",
    guitar: "🎸",
    musical_keyboard: "🎹",
    trumpet: "🎺",
    violin: "🎻",
    banjo: "🪕",
    drum: "🥁",
    long_drum: "🪘",
    iphone: "📱",
    calling: "📲",
    phone: "☎️",
    telephone: "☎️",
    telephone_receiver: "📞",
    pager: "📟",
    fax: "📠",
    battery: "🔋",
    electric_plug: "🔌",
    computer: "💻",
    desktop_computer: "🖥️",
    printer: "🖨️",
    keyboard: "⌨️",
    computer_mouse: "🖱️",
    trackball: "🖲️",
    minidisc: "💽",
    floppy_disk: "💾",
    cd: "💿",
    dvd: "📀",
    abacus: "🧮",
    movie_camera: "🎥",
    film_strip: "🎞️",
    film_projector: "📽️",
    clapper: "🎬",
    tv: "📺",
    camera: "📷",
    camera_flash: "📸",
    video_camera: "📹",
    vhs: "📼",
    mag: "🔍",
    mag_right: "🔎",
    candle: "🕯️",
    bulb: "💡",
    flashlight: "🔦",
    izakaya_lantern: "🏮",
    lantern: "🏮",
    diya_lamp: "🪔",
    notebook_with_decorative_cover: "📔",
    closed_book: "📕",
    book: "📖",
    open_book: "📖",
    green_book: "📗",
    blue_book: "📘",
    orange_book: "📙",
    books: "📚",
    notebook: "📓",
    ledger: "📒",
    page_with_curl: "📃",
    scroll: "📜",
    page_facing_up: "📄",
    newspaper: "📰",
    newspaper_roll: "🗞️",
    bookmark_tabs: "📑",
    bookmark: "🔖",
    label: "🏷️",
    moneybag: "💰",
    coin: "🪙",
    yen: "💴",
    dollar: "💵",
    euro: "💶",
    pound: "💷",
    money_with_wings: "💸",
    credit_card: "💳",
    receipt: "🧾",
    chart: "💹",
    envelope: "✉️",
    email: "📧",
    "e-mail": "📧",
    incoming_envelope: "📨",
    envelope_with_arrow: "📩",
    outbox_tray: "📤",
    inbox_tray: "📥",
    package: "📦",
    mailbox: "📫",
    mailbox_closed: "📪",
    mailbox_with_mail: "📬",
    mailbox_with_no_mail: "📭",
    postbox: "📮",
    ballot_box: "🗳️",
    pencil2: "✏️",
    black_nib: "✒️",
    fountain_pen: "🖋️",
    pen: "🖊️",
    paintbrush: "🖌️",
    crayon: "🖍️",
    memo: "📝",
    pencil: "📝",
    briefcase: "💼",
    file_folder: "📁",
    open_file_folder: "📂",
    card_index_dividers: "🗂️",
    date: "📅",
    calendar: "📆",
    spiral_notepad: "🗒️",
    spiral_calendar: "🗓️",
    card_index: "📇",
    chart_with_upwards_trend: "📈",
    chart_with_downwards_trend: "📉",
    bar_chart: "📊",
    clipboard: "📋",
    pushpin: "📌",
    round_pushpin: "📍",
    paperclip: "📎",
    paperclips: "🖇️",
    straight_ruler: "📏",
    triangular_ruler: "📐",
    scissors: "✂️",
    card_file_box: "🗃️",
    file_cabinet: "🗄️",
    wastebasket: "🗑️",
    lock: "🔒",
    unlock: "🔓",
    lock_with_ink_pen: "🔏",
    closed_lock_with_key: "🔐",
    key: "🔑",
    old_key: "🗝️",
    hammer: "🔨",
    axe: "🪓",
    pick: "⛏️",
    hammer_and_pick: "⚒️",
    hammer_and_wrench: "🛠️",
    dagger: "🗡️",
    crossed_swords: "⚔️",
    gun: "🔫",
    boomerang: "🪃",
    bow_and_arrow: "🏹",
    shield: "🛡️",
    carpentry_saw: "🪚",
    wrench: "🔧",
    screwdriver: "🪛",
    nut_and_bolt: "🔩",
    gear: "⚙️",
    clamp: "🗜️",
    balance_scale: "⚖️",
    probing_cane: "🦯",
    link: "🔗",
    chains: "⛓️",
    hook: "🪝",
    toolbox: "🧰",
    magnet: "🧲",
    ladder: "🪜",
    alembic: "⚗️",
    test_tube: "🧪",
    petri_dish: "🧫",
    dna: "🧬",
    microscope: "🔬",
    telescope: "🔭",
    satellite: "📡",
    syringe: "💉",
    drop_of_blood: "🩸",
    pill: "💊",
    adhesive_bandage: "🩹",
    stethoscope: "🩺",
    door: "🚪",
    elevator: "🛗",
    mirror: "🪞",
    window: "🪟",
    bed: "🛏️",
    couch_and_lamp: "🛋️",
    chair: "🪑",
    toilet: "🚽",
    plunger: "🪠",
    shower: "🚿",
    bathtub: "🛁",
    mouse_trap: "🪤",
    razor: "🪒",
    lotion_bottle: "🧴",
    safety_pin: "🧷",
    broom: "🧹",
    basket: "🧺",
    roll_of_paper: "🧻",
    bucket: "🪣",
    soap: "🧼",
    toothbrush: "🪥",
    sponge: "🧽",
    fire_extinguisher: "🧯",
    shopping_cart: "🛒",
    smoking: "🚬",
    coffin: "⚰️",
    headstone: "🪦",
    funeral_urn: "⚱️",
    moyai: "🗿",
    placard: "🪧",
    atm: "🏧",
    put_litter_in_its_place: "🚮",
    potable_water: "🚰",
    wheelchair: "♿",
    mens: "🚹",
    womens: "🚺",
    restroom: "🚻",
    baby_symbol: "🚼",
    wc: "🚾",
    passport_control: "🛂",
    customs: "🛃",
    baggage_claim: "🛄",
    left_luggage: "🛅",
    warning: "⚠️",
    children_crossing: "🚸",
    no_entry: "⛔",
    no_entry_sign: "🚫",
    no_bicycles: "🚳",
    no_smoking: "🚭",
    do_not_litter: "🚯",
    "non-potable_water": "🚱",
    no_pedestrians: "🚷",
    no_mobile_phones: "📵",
    underage: "🔞",
    radioactive: "☢️",
    biohazard: "☣️",
    arrow_up: "⬆️",
    arrow_upper_right: "↗️",
    arrow_right: "➡️",
    arrow_lower_right: "↘️",
    arrow_down: "⬇️",
    arrow_lower_left: "↙️",
    arrow_left: "⬅️",
    arrow_upper_left: "↖️",
    arrow_up_down: "↕️",
    left_right_arrow: "↔️",
    leftwards_arrow_with_hook: "↩️",
    arrow_right_hook: "↪️",
    arrow_heading_up: "⤴️",
    arrow_heading_down: "⤵️",
    arrows_clockwise: "🔃",
    arrows_counterclockwise: "🔄",
    back: "🔙",
    end: "🔚",
    on: "🔛",
    soon: "🔜",
    top: "🔝",
    place_of_worship: "🛐",
    atom_symbol: "⚛️",
    om: "🕉️",
    star_of_david: "✡️",
    wheel_of_dharma: "☸️",
    yin_yang: "☯️",
    latin_cross: "✝️",
    orthodox_cross: "☦️",
    star_and_crescent: "☪️",
    peace_symbol: "☮️",
    menorah: "🕎",
    six_pointed_star: "🔯",
    aries: "♈",
    taurus: "♉",
    gemini: "♊",
    cancer: "♋",
    leo: "♌",
    virgo: "♍",
    libra: "♎",
    scorpius: "♏",
    sagittarius: "♐",
    capricorn: "♑",
    aquarius: "♒",
    pisces: "♓",
    ophiuchus: "⛎",
    twisted_rightwards_arrows: "🔀",
    repeat: "🔁",
    repeat_one: "🔂",
    arrow_forward: "▶️",
    fast_forward: "⏩",
    next_track_button: "⏭️",
    play_or_pause_button: "⏯️",
    arrow_backward: "◀️",
    rewind: "⏪",
    previous_track_button: "⏮️",
    arrow_up_small: "🔼",
    arrow_double_up: "⏫",
    arrow_down_small: "🔽",
    arrow_double_down: "⏬",
    pause_button: "⏸️",
    stop_button: "⏹️",
    record_button: "⏺️",
    eject_button: "⏏️",
    cinema: "🎦",
    low_brightness: "🔅",
    high_brightness: "🔆",
    signal_strength: "📶",
    vibration_mode: "📳",
    mobile_phone_off: "📴",
    female_sign: "♀️",
    male_sign: "♂️",
    transgender_symbol: "⚧️",
    heavy_multiplication_x: "✖️",
    heavy_plus_sign: "➕",
    heavy_minus_sign: "➖",
    heavy_division_sign: "➗",
    infinity: "♾️",
    bangbang: "‼️",
    interrobang: "⁉️",
    question: "❓",
    grey_question: "❔",
    grey_exclamation: "❕",
    exclamation: "❗",
    heavy_exclamation_mark: "❗",
    wavy_dash: "〰️",
    currency_exchange: "💱",
    heavy_dollar_sign: "💲",
    medical_symbol: "⚕️",
    recycle: "♻️",
    fleur_de_lis: "⚜️",
    trident: "🔱",
    name_badge: "📛",
    beginner: "🔰",
    o: "⭕",
    white_check_mark: "✅",
    ballot_box_with_check: "☑️",
    heavy_check_mark: "✔️",
    x: "❌",
    negative_squared_cross_mark: "❎",
    curly_loop: "➰",
    loop: "➿",
    part_alternation_mark: "〽️",
    eight_spoked_asterisk: "✳️",
    eight_pointed_black_star: "✴️",
    sparkle: "❇️",
    copyright: "©️",
    registered: "®️",
    tm: "™️",
    hash: "#️⃣",
    asterisk: "*️⃣",
    zero: "0️⃣",
    one: "1️⃣",
    two: "2️⃣",
    three: "3️⃣",
    four: "4️⃣",
    five: "5️⃣",
    six: "6️⃣",
    seven: "7️⃣",
    eight: "8️⃣",
    nine: "9️⃣",
    keycap_ten: "🔟",
    capital_abcd: "🔠",
    abcd: "🔡",
    symbols: "🔣",
    abc: "🔤",
    a: "🅰️",
    ab: "🆎",
    b: "🅱️",
    cl: "🆑",
    cool: "🆒",
    free: "🆓",
    information_source: "ℹ️",
    id: "🆔",
    m: "Ⓜ️",
    new: "🆕",
    ng: "🆖",
    o2: "🅾️",
    ok: "🆗",
    parking: "🅿️",
    sos: "🆘",
    up: "🆙",
    vs: "🆚",
    koko: "🈁",
    sa: "🈂️",
    ideograph_advantage: "🉐",
    accept: "🉑",
    congratulations: "㊗️",
    secret: "㊙️",
    u6e80: "🈵",
    red_circle: "🔴",
    orange_circle: "🟠",
    yellow_circle: "🟡",
    green_circle: "🟢",
    large_blue_circle: "🔵",
    purple_circle: "🟣",
    brown_circle: "🟤",
    black_circle: "⚫",
    white_circle: "⚪",
    red_square: "🟥",
    orange_square: "🟧",
    yellow_square: "🟨",
    green_square: "🟩",
    blue_square: "🟦",
    purple_square: "🟪",
    brown_square: "🟫",
    black_large_square: "⬛",
    white_large_square: "⬜",
    black_medium_square: "◼️",
    white_medium_square: "◻️",
    black_medium_small_square: "◾",
    white_medium_small_square: "◽",
    black_small_square: "▪️",
    white_small_square: "▫️",
    large_orange_diamond: "🔶",
    large_blue_diamond: "🔷",
    small_orange_diamond: "🔸",
    small_blue_diamond: "🔹",
    small_red_triangle: "🔺",
    small_red_triangle_down: "🔻",
    diamond_shape_with_a_dot_inside: "💠",
    radio_button: "🔘",
    white_square_button: "🔳",
    black_square_button: "🔲",
    checkered_flag: "🏁",
    triangular_flag_on_post: "🚩",
    crossed_flags: "🎌",
    black_flag: "🏴",
    white_flag: "🏳️",
    rainbow_flag: "🏳️‍🌈",
    transgender_flag: "🏳️‍⚧️",
    pirate_flag: "🏴‍☠️",
    ascension_island: "🇦🇨",
    andorra: "🇦🇩",
    united_arab_emirates: "🇦🇪",
    afghanistan: "🇦🇫",
    antigua_barbuda: "🇦🇬",
    anguilla: "🇦🇮",
    albania: "🇦🇱",
    armenia: "🇦🇲",
    angola: "🇦🇴",
    antarctica: "🇦🇶",
    argentina: "🇦🇷",
    american_samoa: "🇦🇸",
    austria: "🇦🇹",
    australia: "🇦🇺",
    aruba: "🇦🇼",
    aland_islands: "🇦🇽",
    azerbaijan: "🇦🇿",
    bosnia_herzegovina: "🇧🇦",
    barbados: "🇧🇧",
    bangladesh: "🇧🇩",
    belgium: "🇧🇪",
    burkina_faso: "🇧🇫",
    bulgaria: "🇧🇬",
    bahrain: "🇧🇭",
    burundi: "🇧🇮",
    benin: "🇧🇯",
    st_barthelemy: "🇧🇱",
    bermuda: "🇧🇲",
    brunei: "🇧🇳",
    bolivia: "🇧🇴",
    caribbean_netherlands: "🇧🇶",
    brazil: "🇧🇷",
    bahamas: "🇧🇸",
    bhutan: "🇧🇹",
    bouvet_island: "🇧🇻",
    botswana: "🇧🇼",
    belarus: "🇧🇾",
    belize: "🇧🇿",
    canada: "🇨🇦",
    cocos_islands: "🇨🇨",
    congo_kinshasa: "🇨🇩",
    central_african_republic: "🇨🇫",
    congo_brazzaville: "🇨🇬",
    switzerland: "🇨🇭",
    cote_divoire: "🇨🇮",
    cook_islands: "🇨🇰",
    chile: "🇨🇱",
    cameroon: "🇨🇲",
    cn: "🇨🇳",
    colombia: "🇨🇴",
    clipperton_island: "🇨🇵",
    costa_rica: "🇨🇷",
    cuba: "🇨🇺",
    cape_verde: "🇨🇻",
    curacao: "🇨🇼",
    christmas_island: "🇨🇽",
    cyprus: "🇨🇾",
    czech_republic: "🇨🇿",
    de: "🇩🇪",
    diego_garcia: "🇩🇬",
    djibouti: "🇩🇯",
    denmark: "🇩🇰",
    dominica: "🇩🇲",
    dominican_republic: "🇩🇴",
    algeria: "🇩🇿",
    ceuta_melilla: "🇪🇦",
    ecuador: "🇪🇨",
    estonia: "🇪🇪",
    egypt: "🇪🇬",
    western_sahara: "🇪🇭",
    eritrea: "🇪🇷",
    es: "🇪🇸",
    ethiopia: "🇪🇹",
    eu: "🇪🇺",
    european_union: "🇪🇺",
    finland: "🇫🇮",
    fiji: "🇫🇯",
    falkland_islands: "🇫🇰",
    micronesia: "🇫🇲",
    faroe_islands: "🇫🇴",
    fr: "🇫🇷",
    gabon: "🇬🇦",
    gb: "🇬🇧",
    uk: "🇬🇧",
    grenada: "🇬🇩",
    georgia: "🇬🇪",
    french_guiana: "🇬🇫",
    guernsey: "🇬🇬",
    ghana: "🇬🇭",
    gibraltar: "🇬🇮",
    greenland: "🇬🇱",
    gambia: "🇬🇲",
    guinea: "🇬🇳",
    guadeloupe: "🇬🇵",
    equatorial_guinea: "🇬🇶",
    greece: "🇬🇷",
    south_georgia_south_sandwich_islands: "🇬🇸",
    guatemala: "🇬🇹",
    guam: "🇬🇺",
    guinea_bissau: "🇬🇼",
    guyana: "🇬🇾",
    hong_kong: "🇭🇰",
    heard_mcdonald_islands: "🇭🇲",
    honduras: "🇭🇳",
    croatia: "🇭🇷",
    haiti: "🇭🇹",
    hungary: "🇭🇺",
    canary_islands: "🇮🇨",
    indonesia: "🇮🇩",
    ireland: "🇮🇪",
    israel: "🇮🇱",
    isle_of_man: "🇮🇲",
    india: "🇮🇳",
    british_indian_ocean_territory: "🇮🇴",
    iraq: "🇮🇶",
    iran: "🇮🇷",
    iceland: "🇮🇸",
    it: "🇮🇹",
    jersey: "🇯🇪",
    jamaica: "🇯🇲",
    jordan: "🇯🇴",
    jp: "🇯🇵",
    kenya: "🇰🇪",
    kyrgyzstan: "🇰🇬",
    cambodia: "🇰🇭",
    kiribati: "🇰🇮",
    comoros: "🇰🇲",
    st_kitts_nevis: "🇰🇳",
    north_korea: "🇰🇵",
    kr: "🇰🇷",
    kuwait: "🇰🇼",
    cayman_islands: "🇰🇾",
    kazakhstan: "🇰🇿",
    laos: "🇱🇦",
    lebanon: "🇱🇧",
    st_lucia: "🇱🇨",
    liechtenstein: "🇱🇮",
    sri_lanka: "🇱🇰",
    liberia: "🇱🇷",
    lesotho: "🇱🇸",
    lithuania: "🇱🇹",
    luxembourg: "🇱🇺",
    latvia: "🇱🇻",
    libya: "🇱🇾",
    morocco: "🇲🇦",
    monaco: "🇲🇨",
    moldova: "🇲🇩",
    montenegro: "🇲🇪",
    st_martin: "🇲🇫",
    madagascar: "🇲🇬",
    marshall_islands: "🇲🇭",
    macedonia: "🇲🇰",
    mali: "🇲🇱",
    myanmar: "🇲🇲",
    mongolia: "🇲🇳",
    macau: "🇲🇴",
    northern_mariana_islands: "🇲🇵",
    martinique: "🇲🇶",
    mauritania: "🇲🇷",
    montserrat: "🇲🇸",
    malta: "🇲🇹",
    mauritius: "🇲🇺",
    maldives: "🇲🇻",
    malawi: "🇲🇼",
    mexico: "🇲🇽",
    malaysia: "🇲🇾",
    mozambique: "🇲🇿",
    namibia: "🇳🇦",
    new_caledonia: "🇳🇨",
    niger: "🇳🇪",
    norfolk_island: "🇳🇫",
    nigeria: "🇳🇬",
    nicaragua: "🇳🇮",
    netherlands: "🇳🇱",
    norway: "🇳🇴",
    nepal: "🇳🇵",
    nauru: "🇳🇷",
    niue: "🇳🇺",
    new_zealand: "🇳🇿",
    oman: "🇴🇲",
    panama: "🇵🇦",
    peru: "🇵🇪",
    french_polynesia: "🇵🇫",
    papua_new_guinea: "🇵🇬",
    philippines: "🇵🇭",
    pakistan: "🇵🇰",
    poland: "🇵🇱",
    st_pierre_miquelon: "🇵🇲",
    pitcairn_islands: "🇵🇳",
    puerto_rico: "🇵🇷",
    palestinian_territories: "🇵🇸",
    portugal: "🇵🇹",
    palau: "🇵🇼",
    paraguay: "🇵🇾",
    qatar: "🇶🇦",
    reunion: "🇷🇪",
    romania: "🇷🇴",
    serbia: "🇷🇸",
    ru: "🇷🇺",
    rwanda: "🇷🇼",
    saudi_arabia: "🇸🇦",
    solomon_islands: "🇸🇧",
    seychelles: "🇸🇨",
    sudan: "🇸🇩",
    sweden: "🇸🇪",
    singapore: "🇸🇬",
    st_helena: "🇸🇭",
    slovenia: "🇸🇮",
    svalbard_jan_mayen: "🇸🇯",
    slovakia: "🇸🇰",
    sierra_leone: "🇸🇱",
    san_marino: "🇸🇲",
    senegal: "🇸🇳",
    somalia: "🇸🇴",
    suriname: "🇸🇷",
    south_sudan: "🇸🇸",
    sao_tome_principe: "🇸🇹",
    el_salvador: "🇸🇻",
    sint_maarten: "🇸🇽",
    syria: "🇸🇾",
    swaziland: "🇸🇿",
    tristan_da_cunha: "🇹🇦",
    turks_caicos_islands: "🇹🇨",
    chad: "🇹🇩",
    french_southern_territories: "🇹🇫",
    togo: "🇹🇬",
    thailand: "🇹🇭",
    tajikistan: "🇹🇯",
    tokelau: "🇹🇰",
    timor_leste: "🇹🇱",
    turkmenistan: "🇹🇲",
    tunisia: "🇹🇳",
    tonga: "🇹🇴",
    tr: "🇹🇷",
    trinidad_tobago: "🇹🇹",
    tuvalu: "🇹🇻",
    taiwan: "🇹🇼",
    tanzania: "🇹🇿",
    ukraine: "🇺🇦",
    uganda: "🇺🇬",
    us_outlying_islands: "🇺🇲",
    united_nations: "🇺🇳",
    us: "🇺🇸",
    uruguay: "🇺🇾",
    uzbekistan: "🇺🇿",
    vatican_city: "🇻🇦",
    st_vincent_grenadines: "🇻🇨",
    venezuela: "🇻🇪",
    british_virgin_islands: "🇻🇬",
    us_virgin_islands: "🇻🇮",
    vietnam: "🇻🇳",
    vanuatu: "🇻🇺",
    wallis_futuna: "🇼🇫",
    samoa: "🇼🇸",
    kosovo: "🇽🇰",
    yemen: "🇾🇪",
    mayotte: "🇾🇹",
    south_africa: "🇿🇦",
    zambia: "🇿🇲",
    zimbabwe: "🇿🇼",
    england: "🏴󠁧󠁢󠁥󠁮󠁧󠁿",
    scotland: "🏴󠁧󠁢󠁳󠁣󠁴󠁿",
    wales: "🏴󠁧󠁢󠁷󠁬󠁳󠁿"
};
var b, w, k, y, v, x, j, C, O, z, A, T;

function I() {
    if (z) return O;
    z = 1;
    var e = y ? k : (y = 1, k = function(e, n) {
            return e[n].content
        }),
        n = x ? v : (x = 1, v = function(e, n, a, o, t) {
            var r = e.utils.arrayReplaceAt,
                i = e.utils.lib.ucmicro,
                l = new RegExp([i.Z.source, i.P.source, i.Cc.source].join("|"));

            function s(e, o, r) {
                var i, s = 0,
                    _ = [];
                return e.replace(t, (function(o, t, c) {
                    var u;
                    if (a.hasOwnProperty(o)) {
                        if (u = a[o], t > 0 && !l.test(c[t - 1])) return;
                        if (t + o.length < c.length && !l.test(c[t + o.length])) return
                    } else u = o.slice(1, -1);
                    t > s && ((i = new r("text", "", 0)).content = e.slice(s, t), _.push(i)), (i = new r("emoji", "", 0)).markup = u, i.content = n[u], _.push(i), s = t + o.length
                })), s < e.length && ((i = new r("text", "", 0)).content = e.slice(s), _.push(i)), _
            }
            return function(e) {
                var n, a, t, i, l, _ = e.tokens,
                    c = 0;
                for (a = 0, t = _.length; a < t; a++)
                    if ("inline" === _[a].type)
                        for (n = (i = _[a].children).length - 1; n >= 0; n--) "link_open" !== (l = i[n]).type && "link_close" !== l.type || "auto" === l.info && (c -= l.nesting), "text" === l.type && 0 === c && o.test(l.content) && (_[a].children = i = r(i, n, s(l.content, l.level, e.Token)))
            }
        }),
        a = C ? j : (C = 1, j = function(e) {
            var n, a = e.defs;
            e.enabled.length && (a = Object.keys(a).reduce((function(n, o) {
                return e.enabled.indexOf(o) >= 0 && (n[o] = a[o]), n
            }), {})), n = Object.keys(e.shortcuts).reduce((function(n, o) {
                return a[o] ? Array.isArray(e.shortcuts[o]) ? (e.shortcuts[o].forEach((function(e) {
                    n[e] = o
                })), n) : (n[e.shortcuts[o]] = o, n) : n
            }), {});
            var o, t = Object.keys(a);
            o = 0 === t.length ? "^$" : t.map((function(e) {
                return ":" + e + ":"
            })).concat(Object.keys(n)).sort().reverse().map((function(e) {
                return e.replace(/[.?*+^$[\]\\(){}|-]/g, "\\$&")
            })).join("|");
            var r = RegExp(o),
                i = RegExp(o, "g");
            return {
                defs: a,
                shortcuts: n,
                scanRE: r,
                replaceRE: i
            }
        });
    return O = function(o, t) {
        var r = a(o.utils.assign({}, {
            defs: {},
            shortcuts: {},
            enabled: []
        }, t || {}));
        o.renderer.rules.emoji = e, o.core.ruler.push("emoji", n(o, r.defs, r.shortcuts, r.scanRE, r.replaceRE))
    }
}
const S = l(function() {
    if (T) return A;
    T = 1;
    var e = g,
        n = w ? b : (w = 1, b = {
            angry: [">:(", ">:-("],
            blush: [':")', ':-")'],
            broken_heart: ["</3", "<\\3"],
            confused: [":/", ":-/"],
            cry: [":'(", ":'-(", ":,(", ":,-("],
            frowning: [":(", ":-("],
            heart: ["<3"],
            imp: ["]:(", "]:-("],
            innocent: ["o:)", "O:)", "o:-)", "O:-)", "0:)", "0:-)"],
            joy: [":')", ":'-)", ":,)", ":,-)", ":'D", ":'-D", ":,D", ":,-D"],
            kissing: [":*", ":-*"],
            laughing: ["x-)", "X-)"],
            neutral_face: [":|", ":-|"],
            open_mouth: [":o", ":-o", ":O", ":-O"],
            rage: [":@", ":-@"],
            smile: [":D", ":-D"],
            smiley: [":)", ":-)"],
            smiling_imp: ["]:)", "]:-)"],
            sob: [":,'(", ":,'-(", ";(", ";-("],
            stuck_out_tongue: [":P", ":-P"],
            sunglasses: ["8-)", "B-)"],
            sweat: [",:(", ",:-("],
            sweat_smile: [",:)", ",:-)"],
            unamused: [":s", ":-S", ":z", ":-Z", ":$", ":-$"],
            wink: [";)", ";-)"]
        }),
        a = I();
    return A = function(o, t) {
        var r = {
                defs: e,
                shortcuts: n,
                enabled: []
            },
            i = o.utils.assign({}, r, t || {});
        a(o, i)
    }
}());

function N(e, n, a, o) {
    const t = Number(e[n].meta.id + 1).toString();
    let r = "";
    return "string" == typeof o.docId && (r = `-${o.docId}-`), r + t
}

function E(e, n) {
    let a = Number(e[n].meta.id + 1).toString();
    return e[n].meta.subId > 0 && (a += `:${e[n].meta.subId}`), `[${a}]`
}

function q(e, n, a, o, t) {
    const r = t.rules.footnote_anchor_name(e, n, a, o, t),
        i = t.rules.footnote_caption(e, n, a, o, t);
    let l = r;
    return e[n].meta.subId > 0 && (l += `:${e[n].meta.subId}`), `<sup class="footnote-ref"><a href="#fn${r}" id="fnref${l}">${i}</a></sup>`
}

function P(e, n, a) {
    return (a.xhtmlOut ? '<hr class="footnotes-sep" />\n' : '<hr class="footnotes-sep">\n') + '<section class="footnotes">\n<ol class="footnotes-list">\n'
}

function $() {
    return "</ol>\n</section>\n"
}

function M(e, n, a, o, t) {
    let r = t.rules.footnote_anchor_name(e, n, a, o, t);
    return e[n].meta.subId > 0 && (r += `:${e[n].meta.subId}`), `<li id="fn${r}" class="footnote-item">`
}

function R() {
    return "</li>\n"
}

function L(e, n, a, o, t) {
    let r = t.rules.footnote_anchor_name(e, n, a, o, t);
    return e[n].meta.subId > 0 && (r += `:${e[n].meta.subId}`), ` <a href="#fnref${r}" class="footnote-backref">↩︎</a>`
}

function U(e) {
    const n = e.helpers.parseLinkLabel,
        a = e.utils.isSpace;
    e.renderer.rules.footnote_ref = q, e.renderer.rules.footnote_block_open = P, e.renderer.rules.footnote_block_close = $, e.renderer.rules.footnote_open = M, e.renderer.rules.footnote_close = R, e.renderer.rules.footnote_anchor = L, e.renderer.rules.footnote_caption = E, e.renderer.rules.footnote_anchor_name = N, e.block.ruler.before("reference", "footnote_def", (function(e, n, o, t) {
        const r = e.bMarks[n] + e.tShift[n],
            i = e.eMarks[n];
        if (r + 4 > i) return !1;
        if (91 !== e.src.charCodeAt(r)) return !1;
        if (94 !== e.src.charCodeAt(r + 1)) return !1;
        let l;
        for (l = r + 2; l < i; l++) {
            if (32 === e.src.charCodeAt(l)) return !1;
            if (93 === e.src.charCodeAt(l)) break
        }
        if (l === r + 2) return !1;
        if (l + 1 >= i || 58 !== e.src.charCodeAt(++l)) return !1;
        if (t) return !0;
        l++, e.env.footnotes || (e.env.footnotes = {}), e.env.footnotes.refs || (e.env.footnotes.refs = {});
        const s = e.src.slice(r + 2, l - 2);
        e.env.footnotes.refs[`:${s}`] = -1;
        const _ = new e.Token("footnote_reference_open", "", 1);
        _.meta = {
            label: s
        }, _.level = e.level++, e.tokens.push(_);
        const c = e.bMarks[n],
            u = e.tShift[n],
            d = e.sCount[n],
            m = e.parentType,
            f = l,
            h = e.sCount[n] + l - (e.bMarks[n] + e.tShift[n]);
        let p = h;
        for (; l < i;) {
            const n = e.src.charCodeAt(l);
            if (!a(n)) break;
            9 === n ? p += 4 - p % 4 : p++, l++
        }
        e.tShift[n] = l - f, e.sCount[n] = p - h, e.bMarks[n] = f, e.blkIndent += 4, e.parentType = "footnote", e.sCount[n] < e.blkIndent && (e.sCount[n] += e.blkIndent), e.md.block.tokenize(e, n, o, !0), e.parentType = m, e.blkIndent -= 4, e.tShift[n] = u, e.sCount[n] = d, e.bMarks[n] = c;
        const g = new e.Token("footnote_reference_close", "", -1);
        return g.level = --e.level, e.tokens.push(g), !0
    }), {
        alt: ["paragraph", "reference"]
    }), e.inline.ruler.after("image", "footnote_inline", (function(e, a) {
        const o = e.posMax,
            t = e.pos;
        if (t + 2 >= o) return !1;
        if (94 !== e.src.charCodeAt(t)) return !1;
        if (91 !== e.src.charCodeAt(t + 1)) return !1;
        const r = t + 2,
            i = n(e, t + 1);
        if (i < 0) return !1;
        if (!a) {
            e.env.footnotes || (e.env.footnotes = {}), e.env.footnotes.list || (e.env.footnotes.list = []);
            const n = e.env.footnotes.list.length,
                a = [];
            e.md.inline.parse(e.src.slice(r, i), e.md, e.env, a);
            e.push("footnote_ref", "", 0).meta = {
                id: n
            }, e.env.footnotes.list[n] = {
                content: e.src.slice(r, i),
                tokens: a
            }
        }
        return e.pos = i + 1, e.posMax = o, !0
    })), e.inline.ruler.after("footnote_inline", "footnote_ref", (function(e, n) {
        const a = e.posMax,
            o = e.pos;
        if (o + 3 > a) return !1;
        if (!e.env.footnotes || !e.env.footnotes.refs) return !1;
        if (91 !== e.src.charCodeAt(o)) return !1;
        if (94 !== e.src.charCodeAt(o + 1)) return !1;
        let t;
        for (t = o + 2; t < a; t++) {
            if (32 === e.src.charCodeAt(t)) return !1;
            if (10 === e.src.charCodeAt(t)) return !1;
            if (93 === e.src.charCodeAt(t)) break
        }
        if (t === o + 2) return !1;
        if (t >= a) return !1;
        t++;
        const r = e.src.slice(o + 2, t - 1);
        if (void 0 === e.env.footnotes.refs[`:${r}`]) return !1;
        if (!n) {
            let n;
            e.env.footnotes.list || (e.env.footnotes.list = []), e.env.footnotes.refs[`:${r}`] < 0 ? (n = e.env.footnotes.list.length, e.env.footnotes.list[n] = {
                label: r,
                count: 0
            }, e.env.footnotes.refs[`:${r}`] = n) : n = e.env.footnotes.refs[`:${r}`];
            const a = e.env.footnotes.list[n].count;
            e.env.footnotes.list[n].count++;
            e.push("footnote_ref", "", 0).meta = {
                id: n,
                subId: a,
                label: r
            }
        }
        return e.pos = t, e.posMax = a, !0
    })), e.core.ruler.after("inline", "footnote_tail", (function(e) {
        let n, a, o, t = !1;
        const r = {};
        if (!e.env.footnotes) return;
        if (e.tokens = e.tokens.filter((function(e) {
                return "footnote_reference_open" === e.type ? (t = !0, a = [], o = e.meta.label, !1) : "footnote_reference_close" === e.type ? (t = !1, r[":" + o] = a, !1) : (t && a.push(e), !t)
            })), !e.env.footnotes.list) return;
        const i = e.env.footnotes.list;
        e.tokens.push(new e.Token("footnote_block_open", "", 1));
        for (let l = 0, s = i.length; l < s; l++) {
            const a = new e.Token("footnote_open", "", 1);
            if (a.meta = {
                    id: l,
                    label: i[l].label
                }, e.tokens.push(a), i[l].tokens) {
                n = [];
                const a = new e.Token("paragraph_open", "p", 1);
                a.block = !0, n.push(a);
                const o = new e.Token("inline", "", 0);
                o.children = i[l].tokens, o.content = i[l].content, n.push(o);
                const t = new e.Token("paragraph_close", "p", -1);
                t.block = !0, n.push(t)
            } else i[l].label && (n = r[`:${i[l].label}`]);
            let o;
            n && (e.tokens = e.tokens.concat(n)), o = "paragraph_close" === e.tokens[e.tokens.length - 1].type ? e.tokens.pop() : null;
            const t = i[l].count > 0 ? i[l].count : 1;
            for (let n = 0; n < t; n++) {
                const a = new e.Token("footnote_anchor", "", 0);
                a.meta = {
                    id: l,
                    subId: n,
                    label: i[l].label
                }, e.tokens.push(a)
            }
            o && e.tokens.push(o), e.tokens.push(new e.Token("footnote_close", "", -1))
        }
        e.tokens.push(new e.Token("footnote_block_close", "", -1))
    }))
}
var B, D, V, H, X, F;

function G() {
    if (D) return B;
    D = 1;
    var e, n = Object.defineProperty,
        a = Object.defineProperties,
        o = Object.getOwnPropertyDescriptor,
        t = Object.getOwnPropertyDescriptors,
        r = Object.getOwnPropertyNames,
        i = Object.getOwnPropertySymbols,
        l = Object.prototype.hasOwnProperty,
        s = Object.prototype.propertyIsEnumerable,
        _ = (e, a, o) => a in e ? n(e, a, {
            enumerable: !0,
            configurable: !0,
            writable: !0,
            value: o
        }) : e[a] = o,
        c = (e, n) => {
            for (var a in n || (n = {})) l.call(n, a) && _(e, a, n[a]);
            if (i)
                for (var a of i(n)) s.call(n, a) && _(e, a, n[a]);
            return e
        },
        u = {};

    function d(e, n, a, o, t) {
        try {
            return n.highlight(o, {
                language: "" !== t ? t : "plaintext",
                ignoreIllegals: a
            }).value
        } catch (r) {
            return e.utils.escapeHtml(o)
        }
    }

    function m(e, n, a, o, t) {
        if ("" !== t) return d(e, n, a, o, t);
        try {
            return n.highlightAuto(o).value
        } catch (r) {
            return e.utils.escapeHtml(o)
        }
    }

    function f(e) {
        return function(...n) {
            return e(...n).replace(/<code class="/g, '<code class="hljs ').replace(/<code>/g, '<code class="hljs">')
        }
    }

    function h(e) {
        var n, o, r;
        for (const i of e.tokens)
            if ("inline" === i.type && null != i.children)
                for (const [l, s] of i.children.entries()) {
                    if ("code_inline" !== s.type) continue;
                    const _ = i.children[l + 1];
                    if (null == _) continue;
                    const u = /^{:?\.([^}]+)}/.exec(_.content);
                    if (null == u) continue;
                    const d = u[1];
                    _.content = _.content.slice(u[0].length);
                    let m = null != (n = s.attrGet("class")) ? n : "";
                    m += `${null!=(o=e.md.options.langPrefix)?o:"language-"}${d}`, s.attrSet("class", m), s.meta = (r = c({}, s.meta), a(r, t({
                        highlightLanguage: d
                    })))
                }
    }

    function p(e, n, a, o, t) {
        var r, i;
        const l = e[n];
        if (null == a.highlight) throw new Error("`options.highlight` was null, this is not supposed to happen");
        const s = a.highlight(l.content, null != (i = null == (r = l.meta) ? void 0 : r.highlightLanguage) ? i : "", "");
        return `<code${t.renderAttrs(l)}>${s}</code>`
    }

    function g(e, n) {
        const a = c(c({}, g.defaults), n);
        if (null == a.hljs) throw new Error("Please pass a highlight.js instance for the required `hljs` option.");
        null != a.register && function(e, n) {
            for (const [a, o] of Object.entries(n)) e.registerLanguage(a, o)
        }(a.hljs, a.register), e.options.highlight = (a.auto ? m : d).bind(null, e, a.hljs, a.ignoreIllegals), null != e.renderer.rules.fence && (e.renderer.rules.fence = f(e.renderer.rules.fence)), a.code && null != e.renderer.rules.code_block && (e.renderer.rules.code_block = f(e.renderer.rules.code_block)), a.inline && (e.core.ruler.before("linkify", "inline_code_language", h), e.renderer.rules.code_inline = f(p))
    }
    return ((e, a) => {
        for (var o in a) n(e, o, {
            get: a[o],
            enumerable: !0
        })
    })(u, {
        default: () => g
    }), e = u, B = ((e, a, t, i) => {
        if (a && "object" == typeof a || "function" == typeof a)
            for (let s of r(a)) l.call(e, s) || s === t || n(e, s, {
                get: () => a[s],
                enumerable: !(i = o(a, s)) || i.enumerable
            });
        return e
    })(n({}, "__esModule", {
        value: !0
    }), e), g.defaults = {
        auto: !1,
        code: !1,
        inline: !1,
        ignoreIllegals: !1
    }, B
}
const Z = l(F ? X : (F = 1, X = function() {
    if (H) return V;
    H = 1;
    var e, n = Object.create,
        a = Object.defineProperty,
        o = Object.getOwnPropertyDescriptor,
        t = Object.getOwnPropertyNames,
        r = Object.getOwnPropertySymbols,
        i = Object.getPrototypeOf,
        l = Object.prototype.hasOwnProperty,
        s = Object.prototype.propertyIsEnumerable,
        _ = (e, n, o) => n in e ? a(e, n, {
            enumerable: !0,
            configurable: !0,
            writable: !0,
            value: o
        }) : e[n] = o,
        c = (e, n) => {
            for (var a in n || (n = {})) l.call(n, a) && _(e, a, n[a]);
            if (r)
                for (var a of r(n)) s.call(n, a) && _(e, a, n[a]);
            return e
        },
        u = (e, n, r, i) => {
            if (n && "object" == typeof n || "function" == typeof n)
                for (let s of t(n)) l.call(e, s) || s === r || a(e, s, {
                    get: () => n[s],
                    enumerable: !(i = o(n, s)) || i.enumerable
                });
            return e
        },
        d = (e, o, t) => (t = null != e ? n(i(e)) : {}, u(e && e.__esModule ? t : a(t, "default", {
            value: e,
            enumerable: !0
        }), e)),
        m = {};
    ((e, n) => {
        for (var o in n) a(e, o, {
            get: n[o],
            enumerable: !0
        })
    })(m, {
        default: () => g
    }), e = m, V = u(a({}, "__esModule", {
        value: !0
    }), e);
    var h = d(f()),
        p = d(G());

    function g(e, n) {
        return null == (n = c(c({}, g.defaults), n)).hljs && (n.hljs = h.default), (0, p.default)(e, n)
    }
    return g.defaults = {
        auto: !0,
        code: !0,
        inline: !1,
        ignoreIllegals: !0
    }, V
}().default));

function J(e) {
    return encodeURIComponent(String(e).trim().toLowerCase().replace(/\s+/g, "-"))
}

function K(e) {
    return String(e).replace(/&/g, "&amp;").replace(/"/g, "&quot;").replace(/'/g, "&#39;").replace(/</g, "&lt;").replace(/>/g, "&gt;")
}

function Y(e, n) {
    var a;
    n = Object.assign({}, {
        placeholder: "(\\$\\{toc\\}|\\[\\[?_?toc_?\\]?\\]|\\$\\<toc(\\{[^}]*\\})\\>)",
        slugify: J,
        uniqueSlugStartIndex: 1,
        containerClass: "table-of-contents",
        containerId: void 0,
        listClass: void 0,
        itemClass: void 0,
        linkClass: void 0,
        level: 1,
        listType: "ol",
        format: void 0,
        callback: void 0
    }, n);
    var o = new RegExp("^" + n.placeholder + "$", "i");
    e.renderer.rules.tocOpen = function(e, a) {
        var o = Object.assign({}, n);
        return e && a >= 0 && (o = Object.assign(o, e[a].inlineOptions)), "<nav" + (o.containerId ? ' id="' + K(o.containerId) + '"' : "") + ' class="' + K(o.containerClass) + '">'
    }, e.renderer.rules.tocClose = function() {
        return "</nav>"
    }, e.renderer.rules.tocBody = function(e, o) {
        var t = Object.assign({}, n);
        e && o >= 0 && (t = Object.assign(t, e[o].inlineOptions));
        var r, i = {},
            l = Array.isArray(t.level) ? (r = t.level, function(e) {
                return r.includes(e)
            }) : function(e) {
                return function(n) {
                    return n >= e
                }
            }(t.level);
        return function e(a) {
            var o = t.listClass ? ' class="' + K(t.listClass) + '"' : "",
                r = t.itemClass ? ' class="' + K(t.itemClass) + '"' : "",
                s = t.linkClass ? ' class="' + K(t.linkClass) + '"' : "";
            if (0 === a.c.length) return "";
            var _ = "";
            return (0 === a.l || l(a.l)) && (_ += "<" + (K(t.listType) + o) + ">"), a.c.forEach((function(a) {
                l(a.l) ? _ += "<li" + r + "><a" + s + ' href="#' + function(e) {
                    for (var n = e, a = t.uniqueSlugStartIndex; Object.prototype.hasOwnProperty.call(i, n);) n = e + "-" + a++;
                    return i[n] = !0, n
                }(n.slugify(a.n)) + '">' + ("function" == typeof t.format ? t.format(a.n, K) : K(a.n)) + "</a>" + e(a) + "</li>" : _ += e(a)
            })), (0 === a.l || l(a.l)) && (_ += "</" + K(t.listType) + ">"), _
        }(a)
    }, e.core.ruler.push("generateTocAst", (function(o) {
        a = function(e) {
            for (var n = {
                    l: 0,
                    n: "",
                    c: []
                }, a = [n], o = 0, t = e.length; o < t; o++) {
                var r = e[o];
                if ("heading_open" === r.type) {
                    var i = e[o + 1].children.filter((function(e) {
                            return "text" === e.type || "code_inline" === e.type
                        })).reduce((function(e, n) {
                            return e + n.content
                        }), ""),
                        l = {
                            l: parseInt(r.tag.substr(1), 10),
                            n: i,
                            c: []
                        };
                    if (l.l > a[0].l) a[0].c.push(l), a.unshift(l);
                    else if (l.l === a[0].l) a[1].c.push(l), a[0] = l;
                    else {
                        for (; l.l <= a[0].l;) a.shift();
                        a[0].c.push(l), a.unshift(l)
                    }
                }
            }
            return n
        }(o.tokens), "function" == typeof n.callback && n.callback(e.renderer.rules.tocOpen() + e.renderer.rules.tocBody() + e.renderer.rules.tocClose(), a)
    })), e.block.ruler.before("heading", "toc", (function(e, n, a, t) {
        var r, i = e.src.slice(e.bMarks[n] + e.tShift[n], e.eMarks[n]).split(" ")[0];
        if (!o.test(i)) return !1;
        if (t) return !0;
        var l = o.exec(i),
            s = {};
        if (null !== l && 3 === l.length) try {
            s = JSON.parse(l[2])
        } catch (_) {}
        return e.line = n + 1, (r = e.push("tocOpen", "nav", 1)).markup = "", r.map = [n, e.line], r.inlineOptions = s, (r = e.push("tocBody", "", 0)).markup = "", r.map = [n, e.line], r.inlineOptions = s, r.children = [], (r = e.push("tocClose", "nav", -1)).markup = "", !0
    }), {
        alt: ["paragraph", "reference", "blockquote"]
    })
}
var Q;
var W = "undefined" == typeof document ? void 0 : document,
    ee = !!W && "content" in W.createElement("template"),
    ne = !!W && W.createRange && "createContextualFragment" in W.createRange();

function ae(e) {
    return e = e.trim(), ee ? function(e) {
        var n = W.createElement("template");
        return n.innerHTML = e, n.content.childNodes[0]
    }(e) : ne ? function(e) {
        return Q || (Q = W.createRange()).selectNode(W.body), Q.createContextualFragment(e).childNodes[0]
    }(e) : function(e) {
        var n = W.createElement("body");
        return n.innerHTML = e, n.childNodes[0]
    }(e)
}

function oe(e, n) {
    var a, o, t = e.nodeName,
        r = n.nodeName;
    return t === r || (a = t.charCodeAt(0), o = r.charCodeAt(0), a <= 90 && o >= 97 ? t === r.toUpperCase() : o <= 90 && a >= 97 && r === t.toUpperCase())
}

function te(e, n, a) {
    e[a] !== n[a] && (e[a] = n[a], e[a] ? e.setAttribute(a, "") : e.removeAttribute(a))
}
var re = {
    OPTION: function(e, n) {
        var a = e.parentNode;
        if (a) {
            var o = a.nodeName.toUpperCase();
            "OPTGROUP" === o && (o = (a = a.parentNode) && a.nodeName.toUpperCase()), "SELECT" !== o || a.hasAttribute("multiple") || (e.hasAttribute("selected") && !n.selected && (e.setAttribute("selected", "selected"), e.removeAttribute("selected")), a.selectedIndex = -1)
        }
        te(e, n, "selected")
    },
    INPUT: function(e, n) {
        te(e, n, "checked"), te(e, n, "disabled"), e.value !== n.value && (e.value = n.value), n.hasAttribute("value") || e.removeAttribute("value")
    },
    TEXTAREA: function(e, n) {
        var a = n.value;
        e.value !== a && (e.value = a);
        var o = e.firstChild;
        if (o) {
            var t = o.nodeValue;
            if (t == a || !a && t == e.placeholder) return;
            o.nodeValue = a
        }
    },
    SELECT: function(e, n) {
        if (!n.hasAttribute("multiple")) {
            for (var a, o, t = -1, r = 0, i = e.firstChild; i;)
                if ("OPTGROUP" === (o = i.nodeName && i.nodeName.toUpperCase())) i = (a = i).firstChild;
                else {
                    if ("OPTION" === o) {
                        if (i.hasAttribute("selected")) {
                            t = r;
                            break
                        }
                        r++
                    }!(i = i.nextSibling) && a && (i = a.nextSibling, a = null)
                }
            e.selectedIndex = t
        }
    }
};

function ie() {}

function le(e) {
    if (e) return e.getAttribute && e.getAttribute("id") || e.id
}
var se, _e = (se = function(e, n) {
    var a, o, t, r, i = n.attributes;
    if (11 !== n.nodeType && 11 !== e.nodeType) {
        for (var l = i.length - 1; l >= 0; l--) o = (a = i[l]).name, t = a.namespaceURI, r = a.value, t ? (o = a.localName || o, e.getAttributeNS(t, o) !== r && ("xmlns" === a.prefix && (o = a.name), e.setAttributeNS(t, o, r))) : e.getAttribute(o) !== r && e.setAttribute(o, r);
        for (var s = e.attributes, _ = s.length - 1; _ >= 0; _--) o = (a = s[_]).name, (t = a.namespaceURI) ? (o = a.localName || o, n.hasAttributeNS(t, o) || e.removeAttributeNS(t, o)) : n.hasAttribute(o) || e.removeAttribute(o)
    }
}, function(e, n, a) {
    if (a || (a = {}), "string" == typeof n)
        if ("#document" === e.nodeName || "HTML" === e.nodeName || "BODY" === e.nodeName) {
            var o = n;
            (n = W.createElement("html")).innerHTML = o
        } else n = ae(n);
    else 11 === n.nodeType && (n = n.firstElementChild);
    var t = a.getNodeKey || le,
        r = a.onBeforeNodeAdded || ie,
        i = a.onNodeAdded || ie,
        l = a.onBeforeElUpdated || ie,
        s = a.onElUpdated || ie,
        _ = a.onBeforeNodeDiscarded || ie,
        c = a.onNodeDiscarded || ie,
        u = a.onBeforeElChildrenUpdated || ie,
        d = a.skipFromChildren || ie,
        m = a.addChild || function(e, n) {
            return e.appendChild(n)
        },
        f = !0 === a.childrenOnly,
        h = Object.create(null),
        p = [];

    function g(e) {
        p.push(e)
    }

    function b(e, n) {
        if (1 === e.nodeType)
            for (var a = e.firstChild; a;) {
                var o = void 0;
                n && (o = t(a)) ? g(o) : (c(a), a.firstChild && b(a, n)), a = a.nextSibling
            }
    }

    function w(e, n, a) {
        !1 !== _(e) && (n && n.removeChild(e), c(e), b(e, a))
    }

    function k(e) {
        if (1 === e.nodeType || 11 === e.nodeType)
            for (var n = e.firstChild; n;) {
                var a = t(n);
                a && (h[a] = n), k(n), n = n.nextSibling
            }
    }

    function y(e) {
        i(e);
        for (var n = e.firstChild; n;) {
            var a = n.nextSibling,
                o = t(n);
            if (o) {
                var r = h[o];
                r && oe(n, r) ? (n.parentNode.replaceChild(r, n), v(r, n)) : y(n)
            } else y(n);
            n = a
        }
    }

    function v(e, n, a) {
        var o = t(n);
        if (o && delete h[o], !a) {
            var i = l(e, n);
            if (!1 === i) return;
            if (i instanceof HTMLElement && k(e = i), se(e, n), s(e), !1 === u(e, n)) return
        }
        "TEXTAREA" !== e.nodeName ? function(e, n) {
            var a, o, i, l, s, _ = d(e, n),
                c = n.firstChild,
                u = e.firstChild;
            e: for (; c;) {
                for (l = c.nextSibling, a = t(c); !_ && u;) {
                    if (i = u.nextSibling, c.isSameNode && c.isSameNode(u)) {
                        c = l, u = i;
                        continue e
                    }
                    o = t(u);
                    var f = u.nodeType,
                        p = void 0;
                    if (f === c.nodeType && (1 === f ? (a ? a !== o && ((s = h[a]) ? i === s ? p = !1 : (e.insertBefore(s, u), o ? g(o) : w(u, e, !0), o = t(u = s)) : p = !1) : o && (p = !1), (p = !1 !== p && oe(u, c)) && v(u, c)) : 3 !== f && 8 != f || (p = !0, u.nodeValue !== c.nodeValue && (u.nodeValue = c.nodeValue))), p) {
                        c = l, u = i;
                        continue e
                    }
                    o ? g(o) : w(u, e, !0), u = i
                }
                if (a && (s = h[a]) && oe(s, c)) _ || m(e, s), v(s, c);
                else {
                    var b = r(c);
                    !1 !== b && (b && (c = b), c.actualize && (c = c.actualize(e.ownerDocument || W)), m(e, c), y(c))
                }
                c = l, u = i
            }! function(e, n, a) {
                for (; n;) {
                    var o = n.nextSibling;
                    (a = t(n)) ? g(a): w(n, e, !0), n = o
                }
            }(e, u, o);
            var k = re[e.nodeName];
            k && k(e, n)
        }(e, n) : re.TEXTAREA(e, n)
    }
    k(e);
    var x, j, C = e,
        O = C.nodeType,
        z = n.nodeType;
    if (!f)
        if (1 === O) 1 === z ? oe(e, n) || (c(e), C = function(e, n) {
            for (var a = e.firstChild; a;) {
                var o = a.nextSibling;
                n.appendChild(a), a = o
            }
            return n
        }(e, (x = n.nodeName, (j = n.namespaceURI) && "http://www.w3.org/1999/xhtml" !== j ? W.createElementNS(j, x) : W.createElement(x)))) : C = n;
        else if (3 === O || 8 === O) {
        if (z === O) return C.nodeValue !== n.nodeValue && (C.nodeValue = n.nodeValue), C;
        C = n
    }
    if (C === n) c(e);
    else {
        if (n.isSameNode && n.isSameNode(C)) return;
        if (v(C, n, f), p)
            for (var A = 0, T = p.length; A < T; A++) {
                var I = h[p[A]];
                I && w(I, I.parentNode, !1)
            }
    }
    return !f && C !== e && e.parentNode && (C.actualize && (C = C.actualize(e.ownerDocument || W)), e.parentNode.replaceChild(C, e)), C
});
const ce = {
    __name: "VueMarkdownIt",
    props: {
        anchor: {
            type: Object,
            default: () => ({})
        },
        breaks: {
            type: Boolean,
            default: !1
        },
        emoji: {
            type: Object,
            default: () => ({})
        },
        highlight: {
            type: Object,
            default: () => ({})
        },
        html: {
            type: Boolean,
            default: !1
        },
        langPrefix: {
            type: String,
            default: "language-"
        },
        linkify: {
            type: Boolean,
            default: !1
        },
        plugins: {
            type: Array,
            default: () => []
        },
        quotes: {
            type: String,
            default: '""'
        },
        source: {
            type: String,
            default: ""
        },
        tasklists: {
            type: Object,
            default: () => ({})
        },
        toc: {
            type: Object,
            default: () => ({})
        },
        typographer: {
            type: Boolean,
            default: !1
        },
        xhtmlOut: {
            type: Boolean,
            default: !1
        }
    },
    setup(l) {
        const f = l,
            g = s(null),
            b = s(null),
            w = s(""),
            k = async () => {
                if (!g.value) return;
                const l = b.value || (() => {
                    let l = (new e).use(h).use(n, { ...f.anchor,
                        level: 3
                    }).use(p).use(S, f.emoji).use(U).use(Z, f.highlight).use(a).use(o).use(t).use(r).use(i, f.tasklists).use(Y, f.toc).set({
                        breaks: f.breaks,
                        html: f.html,
                        langPrefix: f.langPrefix,
                        linkify: f.linkify,
                        quotes: f.quotes,
                        typographer: f.typographer,
                        xhtmlOut: f.xhtmlOut
                    });
                    return l.linkify.set({
                        fuzzyLink: !1,
                        fuzzyEmail: !1,
                        fuzzyIP: !1
                    }), f.plugins.forEach((({
                        plugin: e,
                        options: n = {}
                    }) => {
                        try {
                            l.use(e, n)
                        } catch (a) {}
                    })), l
                })();
                b.value = l;
                const s = l.render(f.source);
                if (s !== w.value) {
                    w.value = s;
                    const e = document.createElement("div");
                    e.innerHTML = s, _e(g.value, e)
                }
            };
        return _((() => f.source), (() => {
            k()
        }), {
            immediate: !0
        }), c((() => {
            k()
        })), u((() => {
            k()
        })), (e, n) => (m(), d("div", {
            ref_key: "markdownContainer",
            ref: g
        }, null, 512))
    }
};
export {
    ce as _, Z as m
};