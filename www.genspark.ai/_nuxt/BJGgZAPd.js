import {
    i as e,
    M as t,
    aT as n,
    K as o,
    J as r,
    X as a,
    Y as l,
    d6 as i,
    r as s,
    ca as u,
    c,
    h as d,
    a3 as v,
    al as f,
    aO as p,
    am as h,
    ak as g,
    bv as m,
    H as x,
    I as b,
    a4 as w,
    aR as y,
    ag as S,
    cg as C,
    v as P,
    Z as k,
    d7 as $,
    aq as z,
    q as D,
    U as I
} from "./Cf0SOiw0.js";
import {
    c as N
} from "./DLUhQFIq.js";
import {
    f as _,
    k as j
} from "./9wLWmnxl.js";
import {
    V as R,
    g as O,
    o as A,
    a as V
} from "./Jr9eiJio.js";
import {
    b as B
} from "./B7VeW_-d.js";
import {
    u as M
} from "./BuhfKjCJ.js";
const E = n("n-carousel-methods");

function X(n = "unknown", o = "component") {
    const r = e(E);
    return r || t(n, `\`${o}\` must be placed inside \`n-carousel\`.`), r
}
const T = r({
        name: "CarouselArrow",
        setup(e) {
            const {
                mergedClsPrefixRef: t
            } = l(e), {
                isVertical: n,
                isPrevDisabled: o,
                isNextDisabled: r,
                prev: a,
                next: i
            } = X();
            return {
                mergedClsPrefix: t,
                isVertical: n,
                isPrevDisabled: o,
                isNextDisabled: r,
                prev: a,
                next: i
            }
        },
        render() {
            const {
                mergedClsPrefix: e
            } = this;
            return a("div", {
                class: `${e}-carousel__arrow-group`
            }, a("div", {
                class: [`${e}-carousel__arrow`, this.isPrevDisabled() && `${e}-carousel__arrow--disabled`],
                role: "button",
                onClick: this.prev
            }, a("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                viewBox: "0 0 16 16"
            }, a("g", {
                fill: "none"
            }, a("path", {
                d: "M10.26 3.2a.75.75 0 0 1 .04 1.06L6.773 8l3.527 3.74a.75.75 0 1 1-1.1 1.02l-4-4.25a.75.75 0 0 1 0-1.02l4-4.25a.75.75 0 0 1 1.06-.04z",
                fill: "currentColor"
            })))), a("div", {
                class: [`${e}-carousel__arrow`, this.isNextDisabled() && `${e}-carousel__arrow--disabled`],
                role: "button",
                onClick: this.next
            }, a("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                viewBox: "0 0 16 16"
            }, a("g", {
                fill: "none"
            }, a("path", {
                d: "M5.74 3.2a.75.75 0 0 0-.04 1.06L9.227 8L5.7 11.74a.75.75 0 1 0 1.1 1.02l4-4.25a.75.75 0 0 0 0-1.02l-4-4.25a.75.75 0 0 0-1.06-.04z",
                fill: "currentColor"
            })))))
        }
    }),
    Y = r({
        name: "CarouselDots",
        props: {
            total: {
                type: Number,
                default: 0
            },
            currentIndex: {
                type: Number,
                default: 0
            },
            dotType: {
                type: String,
                default: "dot"
            },
            trigger: {
                type: String,
                default: "click"
            },
            keyboard: Boolean
        },
        setup(e) {
            const {
                mergedClsPrefixRef: t
            } = l(e), n = s([]), o = X();

            function r(e) {
                var t;
                null === (t = n.value[e]) || void 0 === t || t.focus()
            }
            return u((() => n.value.length = 0)), {
                mergedClsPrefix: t,
                dotEls: n,
                handleKeydown: function(t, n) {
                    switch (t.key) {
                        case "Enter":
                        case " ":
                            return t.preventDefault(), void o.to(n)
                    }
                    e.keyboard && function(e) {
                        var t;
                        if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) return;
                        const n = null === (t = document.activeElement) || void 0 === t ? void 0 : t.nodeName.toLowerCase();
                        if ("input" === n || "textarea" === n) return;
                        const {
                            code: a
                        } = e, l = "PageUp" === a || "ArrowUp" === a, i = "PageDown" === a || "ArrowDown" === a, s = "PageUp" === a || "ArrowRight" === a, u = "PageDown" === a || "ArrowLeft" === a, c = o.isVertical(), d = c ? l : s, v = c ? i : u;
                        if (!d && !v) return;
                        e.preventDefault(), d && !o.isNextDisabled() ? (o.next(), r(o.currentIndexRef.value)) : v && !o.isPrevDisabled() && (o.prev(), r(o.currentIndexRef.value))
                    }(t)
                },
                handleMouseenter: function(t) {
                    "hover" === e.trigger && o.to(t)
                },
                handleClick: function(t) {
                    "click" === e.trigger && o.to(t)
                }
            }
        },
        render() {
            const {
                mergedClsPrefix: e,
                dotEls: t
            } = this;
            return a("div", {
                class: [`${e}-carousel__dots`, `${e}-carousel__dots--${this.dotType}`],
                role: "tablist"
            }, i(this.total, (n => {
                const o = n === this.currentIndex;
                return a("div", {
                    "aria-selected": o,
                    ref: e => t.push(e),
                    role: "button",
                    tabindex: "0",
                    class: [`${e}-carousel__dot`, o && `${e}-carousel__dot--active`],
                    key: n,
                    onClick: () => {
                        this.handleClick(n)
                    },
                    onMouseenter: () => {
                        this.handleMouseenter(n)
                    },
                    onKeydown: e => {
                        this.handleKeydown(e, n)
                    }
                })
            })))
        }
    }),
    F = "CarouselItem";
const K = r({
        name: F,
        setup(e) {
            const {
                mergedClsPrefixRef: t
            } = l(e), n = X(N(F), `n-${N(F)}`), o = s(), r = c((() => {
                const {
                    value: e
                } = o;
                return e ? n.getSlideIndex(e) : -1
            })), a = c((() => n.isPrev(r.value))), i = c((() => n.isNext(r.value))), u = c((() => n.isActive(r.value))), f = c((() => n.getSlideStyle(r.value)));
            return d((() => {
                n.addSlide(o.value)
            })), v((() => {
                n.removeSlide(o.value)
            })), {
                mergedClsPrefix: t,
                selfElRef: o,
                isPrev: a,
                isNext: i,
                isActive: u,
                index: r,
                style: f,
                handleClick: function(e) {
                    const {
                        value: t
                    } = r;
                    void 0 !== t && (null == n || n.onCarouselItemClick(t, e))
                }
            }
        },
        render() {
            var e;
            const {
                $slots: t,
                mergedClsPrefix: n,
                isPrev: o,
                isNext: r,
                isActive: l,
                index: i,
                style: s
            } = this;
            return a("div", {
                ref: "selfElRef",
                class: [`${n}-carousel__slide`, {
                    [`${n}-carousel__slide--current`]: l,
                    [`${n}-carousel__slide--prev`]: o,
                    [`${n}-carousel__slide--next`]: r
                }],
                role: "option",
                tabindex: "-1",
                "data-index": i,
                "aria-hidden": !l,
                style: s,
                onClickCapture: this.handleClick
            }, null === (e = t.default) || void 0 === e ? void 0 : e.call(t, {
                isPrev: o,
                isNext: r,
                isActive: l,
                index: i
            }))
        }
    }),
    L = f("carousel", "\n position: relative;\n width: 100%;\n height: 100%;\n touch-action: pan-y;\n overflow: hidden;\n", [p("slides", "\n display: flex;\n width: 100%;\n height: 100%;\n transition-timing-function: var(--n-bezier);\n transition-property: transform;\n ", [p("slide", "\n flex-shrink: 0;\n position: relative;\n width: 100%;\n height: 100%;\n outline: none;\n overflow: hidden;\n ", [g("> img", "\n display: block;\n ")])]), p("dots", "\n position: absolute;\n display: flex;\n flex-wrap: nowrap;\n ", [h("dot", [p("dot", "\n height: var(--n-dot-size);\n width: var(--n-dot-size);\n background-color: var(--n-dot-color);\n border-radius: 50%;\n cursor: pointer;\n transition:\n box-shadow .3s var(--n-bezier),\n background-color .3s var(--n-bezier);\n outline: none;\n ", [g("&:focus", "\n background-color: var(--n-dot-color-focus);\n "), h("active", "\n background-color: var(--n-dot-color-active);\n ")])]), h("line", [p("dot", "\n border-radius: 9999px;\n width: var(--n-dot-line-width);\n height: 4px;\n background-color: var(--n-dot-color);\n cursor: pointer;\n transition:\n width .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier),\n background-color .3s var(--n-bezier);\n outline: none;\n ", [g("&:focus", "\n background-color: var(--n-dot-color-focus);\n "), h("active", "\n width: var(--n-dot-line-width-active);\n background-color: var(--n-dot-color-active);\n ")])])]), p("arrow", "\n transition: background-color .3s var(--n-bezier);\n cursor: pointer;\n height: 28px;\n width: 28px;\n display: flex;\n align-items: center;\n justify-content: center;\n background-color: rgba(255, 255, 255, .2);\n color: var(--n-arrow-color);\n border-radius: 8px;\n user-select: none;\n -webkit-user-select: none;\n font-size: 18px;\n ", [g("svg", "\n height: 1em;\n width: 1em;\n "), g("&:hover", "\n background-color: rgba(255, 255, 255, .3);\n ")]), h("vertical", "\n touch-action: pan-x;\n ", [p("slides", "\n flex-direction: column;\n "), h("fade", [p("slide", "\n top: 50%;\n left: unset;\n transform: translateY(-50%);\n ")]), h("card", [p("slide", "\n top: 50%;\n left: unset;\n transform: translateY(-50%) translateZ(-400px);\n ", [h("current", "\n transform: translateY(-50%) translateZ(0);\n "), h("prev", "\n transform: translateY(-100%) translateZ(-200px);\n "), h("next", "\n transform: translateY(0%) translateZ(-200px);\n ")])])]), h("usercontrol", [p("slides", [g(">", [g("div", "\n position: absolute;\n top: 50%;\n left: 50%;\n width: 100%;\n height: 100%;\n transform: translate(-50%, -50%);\n ")])])]), h("left", [p("dots", "\n transform: translateY(-50%);\n top: 50%;\n left: 12px;\n flex-direction: column;\n ", [h("line", [p("dot", "\n width: 4px;\n height: var(--n-dot-line-width);\n margin: 4px 0;\n transition:\n height .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier),\n background-color .3s var(--n-bezier);\n outline: none;\n ", [h("active", "\n height: var(--n-dot-line-width-active);\n ")])])]), p("dot", "\n margin: 4px 0;\n ")]), p("arrow-group", "\n position: absolute;\n display: flex;\n flex-wrap: nowrap;\n "), h("vertical", [p("arrow", "\n transform: rotate(90deg);\n ")]), h("show-arrow", [h("bottom", [p("dots", "\n transform: translateX(0);\n bottom: 18px;\n left: 18px;\n ")]), h("top", [p("dots", "\n transform: translateX(0);\n top: 18px;\n left: 18px;\n ")]), h("left", [p("dots", "\n transform: translateX(0);\n top: 18px;\n left: 18px;\n ")]), h("right", [p("dots", "\n transform: translateX(0);\n top: 18px;\n right: 18px;\n ")])]), h("left", [p("arrow-group", "\n bottom: 12px;\n left: 12px;\n flex-direction: column;\n ", [g("> *:first-child", "\n margin-bottom: 12px;\n ")])]), h("right", [p("dots", "\n transform: translateY(-50%);\n top: 50%;\n right: 12px;\n flex-direction: column;\n ", [h("line", [p("dot", "\n width: 4px;\n height: var(--n-dot-line-width);\n margin: 4px 0;\n transition:\n height .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier),\n background-color .3s var(--n-bezier);\n outline: none;\n ", [h("active", "\n height: var(--n-dot-line-width-active);\n ")])])]), p("dot", "\n margin: 4px 0;\n "), p("arrow-group", "\n bottom: 12px;\n right: 12px;\n flex-direction: column;\n ", [g("> *:first-child", "\n margin-bottom: 12px;\n ")])]), h("top", [p("dots", "\n transform: translateX(-50%);\n top: 12px;\n left: 50%;\n ", [h("line", [p("dot", "\n margin: 0 4px;\n ")])]), p("dot", "\n margin: 0 4px;\n "), p("arrow-group", "\n top: 12px;\n right: 12px;\n ", [g("> *:first-child", "\n margin-right: 12px;\n ")])]), h("bottom", [p("dots", "\n transform: translateX(-50%);\n bottom: 12px;\n left: 50%;\n ", [h("line", [p("dot", "\n margin: 0 4px;\n ")])]), p("dot", "\n margin: 0 4px;\n "), p("arrow-group", "\n bottom: 12px;\n right: 12px;\n ", [g("> *:first-child", "\n margin-right: 12px;\n ")])]), h("fade", [p("slide", "\n position: absolute;\n opacity: 0;\n transition-property: opacity;\n pointer-events: none;\n ", [h("current", "\n opacity: 1;\n pointer-events: auto;\n ")])]), h("card", [p("slides", "\n perspective: 1000px;\n "), p("slide", "\n position: absolute;\n left: 50%;\n opacity: 0;\n transform: translateX(-50%) translateZ(-400px);\n transition-property: opacity, transform;\n ", [h("current", "\n opacity: 1;\n transform: translateX(-50%) translateZ(0);\n z-index: 1;\n "), h("prev", "\n opacity: 0.4;\n transform: translateX(-100%) translateZ(-200px);\n "), h("next", "\n opacity: 0.4;\n transform: translateX(0%) translateZ(-200px);\n ")])])]);

function Z(e, t, n) {
    return m(e, {
        key: `carousel-item-duplicate-${t}-${n}`
    })
}

function U(e, t, n) {
    return 1 === t ? 0 : n ? 0 === e ? t - 3 : e === t - 1 ? 0 : e - 1 : e
}

function W(e, t) {
    return t ? e + 1 : e
}

function q(e) {
    return window.TouchEvent && e instanceof window.TouchEvent
}

function H(e, t) {
    let {
        offsetWidth: n,
        offsetHeight: o
    } = e;
    if (t) {
        const t = getComputedStyle(e);
        n = n - Number.parseFloat(t.getPropertyValue("padding-left")) - Number.parseFloat(t.getPropertyValue("padding-right")), o = o - Number.parseFloat(t.getPropertyValue("padding-top")) - Number.parseFloat(t.getPropertyValue("padding-bottom"))
    }
    return {
        width: n,
        height: o
    }
}

function J(e, t, n) {
    return e < t ? t : e > n ? n : e
}
const G = ["transitionDuration", "transitionTimingFunction"],
    Q = Object.assign(Object.assign({}, k.props), {
        defaultIndex: {
            type: Number,
            default: 0
        },
        currentIndex: Number,
        showArrow: Boolean,
        dotType: {
            type: String,
            default: "dot"
        },
        dotPlacement: {
            type: String,
            default: "bottom"
        },
        slidesPerView: {
            type: [Number, String],
            default: 1
        },
        spaceBetween: {
            type: Number,
            default: 0
        },
        centeredSlides: Boolean,
        direction: {
            type: String,
            default: "horizontal"
        },
        autoplay: Boolean,
        interval: {
            type: Number,
            default: 5e3
        },
        loop: {
            type: Boolean,
            default: !0
        },
        effect: {
            type: String,
            default: "slide"
        },
        showDots: {
            type: Boolean,
            default: !0
        },
        trigger: {
            type: String,
            default: "click"
        },
        transitionStyle: {
            type: Object,
            default: () => ({
                transitionDuration: "300ms"
            })
        },
        transitionProps: Object,
        draggable: Boolean,
        prevSlideStyle: [Object, String],
        nextSlideStyle: [Object, String],
        touchable: {
            type: Boolean,
            default: !0
        },
        mousewheel: Boolean,
        keyboard: Boolean,
        "onUpdate:currentIndex": Function,
        onUpdateCurrentIndex: Function
    });
let ee = !1;
const te = r({
    name: "Carousel",
    props: Q,
    slots: Object,
    setup(e) {
        const {
            mergedClsPrefixRef: t,
            inlineThemeDisabled: n
        } = l(e), r = s(null), a = s(null), i = s([]), u = {
            value: []
        }, f = c((() => "vertical" === e.direction)), p = c((() => f.value ? "height" : "width")), h = c((() => f.value ? "bottom" : "right")), g = c((() => "slide" === e.effect)), m = c((() => e.loop && 1 === e.slidesPerView && g.value)), x = c((() => "custom" === e.effect)), b = c((() => !g.value || e.centeredSlides ? 1 : e.slidesPerView)), w = c((() => x.value ? 1 : e.slidesPerView)), N = c((() => "auto" === b.value || "auto" === e.slidesPerView && e.centeredSlides)), _ = s({
            width: 0,
            height: 0
        }), R = s(0), B = c((() => {
            const {
                value: t
            } = i;
            if (!t.length) return [];
            R.value;
            const {
                value: n
            } = N;
            if (n) return t.map((e => H(e)));
            const {
                value: o
            } = w, {
                value: r
            } = _, {
                value: a
            } = p;
            let l = r[a];
            if ("auto" !== o) {
                const {
                    spaceBetween: t
                } = e;
                l = (l - (o - 1) * t) * (1 / Math.max(1, o))
            }
            const s = Object.assign(Object.assign({}, r), {
                [a]: l
            });
            return t.map((() => s))
        })), X = c((() => {
            const {
                value: t
            } = B;
            if (!t.length) return [];
            const {
                centeredSlides: n,
                spaceBetween: o
            } = e, {
                value: r
            } = p, {
                [r]: a
            } = _.value;
            let l = 0;
            return t.map((({
                [r]: e
            }) => {
                let t = l;
                return n && (t += (e - a) / 2), l += e + o, t
            }))
        })), T = s(!1), Y = c((() => {
            const {
                transitionStyle: t
            } = e;
            return t ? j(t, G) : {}
        })), F = c((() => x.value ? 0 : function(e) {
            if (void 0 === e) return 0;
            if ("number" == typeof e) return e;
            const t = e.match(/^((\d+)?\.?\d+?)(ms|s)?$/);
            if (t) {
                const [, e, , n = "ms"] = t;
                return Number(e) * ("ms" === n ? 1 : 1e3)
            }
            return 0
        }(Y.value.transitionDuration))), K = c((() => {
            const {
                value: t
            } = i;
            if (!t.length) return [];
            const n = !(N.value || 1 === w.value),
                o = e => {
                    if (n) {
                        const {
                            value: t
                        } = p;
                        return {
                            [t]: `${B.value[e][t]}px`
                        }
                    }
                };
            if (x.value) return t.map(((e, t) => o(t)));
            const {
                effect: r,
                spaceBetween: a
            } = e, {
                value: l
            } = h;
            return t.reduce(((e, t, n) => {
                const i = Object.assign(Object.assign({}, o(n)), {
                    [`margin-${l}`]: `${a}px`
                });
                return e.push(i), !T.value || "fade" !== r && "card" !== r || Object.assign(i, Y.value), e
            }), [])
        })), Z = c((() => {
            const {
                value: e
            } = b, {
                length: t
            } = i.value;
            if ("auto" !== e) return Math.max(t - e, 0) + 1; {
                const {
                    value: e
                } = B, {
                    length: n
                } = e;
                if (!n) return t;
                const {
                    value: o
                } = X, {
                    value: r
                } = p, a = _.value[r];
                let l = e[e.length - 1][r],
                    i = n;
                for (; i > 1 && l < a;) i--, l += o[i] - o[i - 1];
                return J(i + 1, 1, n)
            }
        })), Q = c((() => {
            return e = Z.value, m.value && e > 3 ? e - 2 : e;
            var e
        })), te = W(e.defaultIndex, m.value), ne = s(U(te, Z.value, m.value)), oe = M(y(e, "currentIndex"), ne), re = c((() => W(oe.value, m.value)));

        function ae(t) {
            var n, o;
            const r = U(t = J(t, 0, Z.value - 1), Z.value, m.value),
                {
                    value: a
                } = oe;
            r !== oe.value && (ne.value = r, null === (n = e["onUpdate:currentIndex"]) || void 0 === n || n.call(e, r, a), null === (o = e.onUpdateCurrentIndex) || void 0 === o || o.call(e, r, a))
        }

        function le(t = re.value) {
            return n = t, o = Z.value, r = e.loop, n < 0 ? null : 0 === n ? r ? o - 1 : null : n - 1;
            var n, o, r
        }

        function ie(t = re.value) {
            return n = t, o = Z.value, r = e.loop, n > o - 1 ? null : n === o - 1 ? r ? 0 : null : n + 1;
            var n, o, r
        }

        function se(e) {
            return re.value === Pe(e)
        }

        function ue() {
            return null === le()
        }

        function ce() {
            return null === ie()
        }
        let de = 0;

        function ve(e) {
            const t = J(W(e, m.value), 0, Z.value);
            e === oe.value && t === re.value || ae(t)
        }

        function fe() {
            const e = le();
            null !== e && (de = -1, ae(e))
        }

        function pe() {
            const e = ie();
            null !== e && (de = 1, ae(e))
        }
        let he = !1;
        let ge = 0;
        const me = s({});

        function xe(e, t = 0) {
            me.value = Object.assign({}, Y.value, {
                transform: f.value ? `translateY(${-e}px)` : `translateX(${-e}px)`,
                transitionDuration: `${t}ms`
            })
        }

        function be(e = 0) {
            g.value ? we(re.value, e) : 0 !== ge && (!he && e > 0 && (he = !0), xe(ge = 0, e))
        }

        function we(e, t) {
            const n = ye(e);
            n !== ge && t > 0 && (he = !0), ge = ye(re.value), xe(n, t)
        }

        function ye(e) {
            let t;
            return t = e >= Z.value - 1 ? Se() : X.value[e] || 0, t
        }

        function Se() {
            if ("auto" === b.value) {
                const {
                    value: e
                } = p, {
                    [e]: t
                } = _.value, {
                    value: n
                } = X, o = n[n.length - 1];
                let r;
                if (void 0 === o) r = t;
                else {
                    const {
                        value: t
                    } = B;
                    r = o + t[t.length - 1][e]
                }
                return r - t
            } {
                const {
                    value: e
                } = X;
                return e[Z.value - 1] || 0
            }
        }
        const Ce = {
            currentIndexRef: oe,
            to: ve,
            prev: function() {
                he && m.value || fe()
            },
            next: function() {
                he && m.value || pe()
            },
            isVertical: () => f.value,
            isHorizontal: () => !f.value,
            isPrev: function(e) {
                const t = Pe(e);
                return null !== t && le() === t
            },
            isNext: function(e) {
                const t = Pe(e);
                return null !== t && ie() === t
            },
            isActive: se,
            isPrevDisabled: ue,
            isNextDisabled: ce,
            getSlideIndex: Pe,
            getSlideStyle: function(t) {
                const n = Pe(t);
                if (-1 !== n) {
                    const t = [K.value[n]],
                        o = Ce.isPrev(n),
                        r = Ce.isNext(n);
                    return o && t.push(e.prevSlideStyle || ""), r && t.push(e.nextSlideStyle || ""), D(t)
                }
            },
            addSlide: function(e) {
                if (!e) return;
                i.value.push(e)
            },
            removeSlide: function(e) {
                if (!e) return;
                const t = Pe(e); - 1 !== t && i.value.splice(t, 1)
            },
            onCarouselItemClick: function(t, n) {
                let o = !he && !Ie && !Ne;
                "card" === e.effect && o && !se(t) && (ve(t), o = !1);
                o || (n.preventDefault(), n.stopPropagation())
            }
        };

        function Pe(e) {
            return "number" == typeof e ? e : e ? i.value.indexOf(e) : -1
        }
        o(E, Ce);
        let ke = 0,
            $e = 0,
            ze = 0,
            De = 0,
            Ie = !1,
            Ne = !1;
        let _e = null;

        function je() {
            _e && (clearInterval(_e), _e = null)
        }

        function Re() {
            je();
            !e.autoplay || Q.value < 2 || (_e = window.setInterval(pe, e.interval))
        }

        function Oe(t) {
            var n;
            if (ee) return;
            if (!(null === (n = a.value) || void 0 === n ? void 0 : n.contains(O(t)))) return;
            ee = !0, Ie = !0, Ne = !1, De = Date.now(), je(), "touchstart" === t.type || t.target.isContentEditable || t.preventDefault();
            const o = q(t) ? t.touches[0] : t;
            f.value ? $e = o.clientY : ke = o.clientX, e.touchable && (A("touchmove", document, Ae), A("touchend", document, Ve), A("touchcancel", document, Ve)), e.draggable && (A("mousemove", document, Ae), A("mouseup", document, Ve))
        }

        function Ae(e) {
            const {
                value: t
            } = f, {
                value: n
            } = p, o = q(e) ? e.touches[0] : e, r = t ? o.clientY - $e : o.clientX - ke, a = _.value[n];
            ze = J(r, -a, a), e.cancelable && e.preventDefault(), g.value && xe(ge - ze, 0)
        }

        function Ve() {
            const {
                value: e
            } = re;
            let t = e;
            if (!he && 0 !== ze && g.value) {
                const e = ge - ze,
                    n = [...X.value.slice(0, Z.value - 1), Se()];
                let o = null;
                for (let r = 0; r < n.length; r++) {
                    const a = Math.abs(n[r] - e);
                    if (null !== o && o < a) break;
                    o = a, t = r
                }
            }
            if (t === e) {
                const e = Date.now() - De,
                    {
                        value: t
                    } = p,
                    n = _.value[t];
                ze > n / 2 || ze / e > .4 ? fe() : (ze < -n / 2 || ze / e < -.4) && pe()
            }
            null !== t && t !== e ? (Ne = !0, ae(t), I((() => {
                m.value && ne.value === oe.value || be(F.value)
            }))) : be(F.value), Be(), Re()
        }

        function Be() {
            Ie && (ee = !1), Ie = !1, ke = 0, $e = 0, ze = 0, De = 0, V("touchmove", document, Ae), V("touchend", document, Ve), V("touchcancel", document, Ve), V("mousemove", document, Ae), V("mouseup", document, Ve)
        }

        function Me(e) {
            if (e.preventDefault(), he) return;
            let {
                deltaX: t,
                deltaY: n
            } = e;
            e.shiftKey && !t && (t = n);
            const o = (t || n) > 0 ? 1 : -1;
            let r = 0,
                a = 0;
            f.value ? a = o : r = o;
            (a * n >= 10 || r * t >= 10) && (1 !== o || ce() ? -1 !== o || ue() || fe() : pe())
        }
        d((() => {
            S(Re), requestAnimationFrame((() => T.value = !0))
        })), v((() => {
            Be(), je()
        })), C((() => {
            const {
                value: e
            } = i, {
                value: t
            } = u, n = new Map, o = e => n.has(e) ? n.get(e) : -1;
            let r = !1;
            for (let a = 0; a < e.length; a++) {
                const o = t.findIndex((t => t.el === e[a]));
                o !== a && (r = !0), n.set(e[a], o)
            }
            r && e.sort(((e, t) => o(e) - o(t)))
        })), P(re, ((e, t) => {
            if (e !== t) {
                if (Re(), g.value) {
                    if (m.value) {
                        const {
                            value: n
                        } = Z; - 1 === de && 1 === t && e === n - 2 ? e = 0 : 1 === de && t === n - 2 && 1 === e && (e = n - 1)
                    }
                    we(e, F.value)
                } else be();
                de = 0
            } else de = 0
        }), {
            immediate: !0
        }), P([m, b], (() => {
            I((() => {
                ae(re.value)
            }))
        })), P(X, (() => {
            g.value && be()
        }), {
            deep: !0
        }), P(g, (e => {
            e ? be() : (he = !1, xe(ge = 0))
        }));
        const Ee = c((() => ({
                onTouchstartPassive: e.touchable ? Oe : void 0,
                onMousedown: e.draggable ? Oe : void 0,
                onWheel: e.mousewheel ? Me : void 0
            }))),
            Xe = c((() => Object.assign(Object.assign({}, j(Ce, ["to", "prev", "next", "isPrevDisabled", "isNextDisabled"])), {
                total: Q.value,
                currentIndex: oe.value
            }))),
            Te = c((() => ({
                total: Q.value,
                currentIndex: oe.value,
                to: Ce.to
            }))),
            Ye = {
                getCurrentIndex: () => oe.value,
                to: ve,
                prev: fe,
                next: pe
            },
            Fe = k("Carousel", "-carousel", L, $, e, t),
            Ke = c((() => {
                const {
                    common: {
                        cubicBezierEaseInOut: e
                    },
                    self: {
                        dotSize: t,
                        dotColor: n,
                        dotColorActive: o,
                        dotColorFocus: r,
                        dotLineWidth: a,
                        dotLineWidthActive: l,
                        arrowColor: i
                    }
                } = Fe.value;
                return {
                    "--n-bezier": e,
                    "--n-dot-color": n,
                    "--n-dot-color-focus": r,
                    "--n-dot-color-active": o,
                    "--n-dot-size": t,
                    "--n-dot-line-width": a,
                    "--n-dot-line-width-active": l,
                    "--n-arrow-color": i
                }
            })),
            Le = n ? z("carousel", void 0, Ke, e) : void 0;
        return Object.assign(Object.assign({
            mergedClsPrefix: t,
            selfElRef: r,
            slidesElRef: a,
            slideVNodes: u,
            duplicatedable: m,
            userWantsControl: x,
            autoSlideSize: N,
            realIndex: re,
            slideStyles: K,
            translateStyle: me,
            slidesControlListeners: Ee,
            handleTransitionEnd: function() {
                if (g.value && he) {
                    const {
                        value: e
                    } = re;
                    we(e, 0)
                } else Re();
                g.value && (me.value.transitionDuration = "0ms"), he = !1
            },
            handleResize: function() {
                _.value = H(r.value, !0), Re()
            },
            handleSlideResize: function() {
                N.value && R.value++
            },
            handleMouseenter: function() {
                e.autoplay && je()
            },
            handleMouseleave: function() {
                e.autoplay && Re()
            },
            isActive: function(e) {
                return oe.value === e
            },
            arrowSlotProps: Xe,
            dotSlotProps: Te
        }, Ye), {
            cssVars: n ? void 0 : Ke,
            themeClass: null == Le ? void 0 : Le.themeClass,
            onRender: null == Le ? void 0 : Le.onRender
        })
    },
    render() {
        var e;
        const {
            mergedClsPrefix: t,
            showArrow: n,
            userWantsControl: o,
            slideStyles: r,
            dotType: l,
            dotPlacement: i,
            slidesControlListeners: s,
            transitionProps: u = {},
            arrowSlotProps: c,
            dotSlotProps: d,
            $slots: {
                default: v,
                dots: f,
                arrow: p
            }
        } = this, h = v && _(v()) || [];
        let g = h.reduce(((e, t) => (function(e) {
            var t;
            return (null === (t = e.type) || void 0 === t ? void 0 : t.name) === F
        }(t) && e.push(t), e)), []);
        return g.length || (g = h.map((e => a(K, null, {
            default: () => m(e)
        })))), this.duplicatedable && (g = function(e) {
            const {
                length: t
            } = e;
            return t > 1 ? (e.push(Z(e[0], 0, "append")), e.unshift(Z(e[t - 1], t - 1, "prepend")), e) : e
        }(g)), this.slideVNodes.value = g, this.autoSlideSize && (g = g.map((e => a(R, {
            onResize: this.handleSlideResize
        }, {
            default: () => e
        })))), null === (e = this.onRender) || void 0 === e || e.call(this), a("div", Object.assign({
            ref: "selfElRef",
            class: [this.themeClass, `${t}-carousel`, "vertical" === this.direction && `${t}-carousel--vertical`, this.showArrow && `${t}-carousel--show-arrow`, `${t}-carousel--${i}`, `${t}-carousel--${this.direction}`, `${t}-carousel--${this.effect}`, o && `${t}-carousel--usercontrol`],
            style: this.cssVars
        }, s, {
            onMouseenter: this.handleMouseenter,
            onMouseleave: this.handleMouseleave
        }), a(R, {
            onResize: this.handleResize
        }, {
            default: () => a("div", {
                ref: "slidesElRef",
                class: `${t}-carousel__slides`,
                role: "listbox",
                style: this.translateStyle,
                onTransitionend: this.handleTransitionEnd
            }, o ? g.map(((e, t) => a("div", {
                style: r[t],
                key: t
            }, x(a(w, Object.assign({}, u), {
                default: () => e
            }), [
                [b, this.isActive(t)]
            ])))) : g)
        }), this.showDots && d.total > 1 && B(f, d, (() => [a(Y, {
            key: l + i,
            total: d.total,
            currentIndex: d.currentIndex,
            dotType: l,
            trigger: this.trigger,
            keyboard: this.keyboard
        })])), n && B(p, c, (() => [a(T, null)])))
    }
});
export {
    te as N, K as a
};