import {
    ak as n,
    at as a
} from "./Cf0SOiw0.js";
const t = new WeakSet;

function i(n) {
    t.add(n)
}

function r(n) {
    return !t.has(n)
}

function e(n) {
    return Object.keys(n)
}
const {
    cubicBezierEaseIn: s,
    cubicBezierEaseOut: o
} = a;

function c({
    transformOrigin: a = "inherit",
    duration: t = ".2s",
    enterScale: i = ".9",
    originalTransform: r = "",
    originalTransition: e = ""
} = {}) {
    return [n("&.fade-in-scale-up-transition-leave-active", {
        transformOrigin: a,
        transition: `opacity ${t} ${s}, transform ${t} ${s} ${e&&`,${e}`}`
    }), n("&.fade-in-scale-up-transition-enter-active", {
        transformOrigin: a,
        transition: `opacity ${t} ${o}, transform ${t} ${o} ${e&&`,${e}`}`
    }), n("&.fade-in-scale-up-transition-enter-from, &.fade-in-scale-up-transition-leave-to", {
        opacity: 0,
        transform: `${r} scale(${i})`
    }), n("&.fade-in-scale-up-transition-leave-from, &.fade-in-scale-up-transition-enter-to", {
        opacity: 1,
        transform: `${r} scale(1)`
    })]
}
export {
    r as e, c as f, e as k, i as m
};