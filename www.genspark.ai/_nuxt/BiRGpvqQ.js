import {
    r as t,
    a1 as e,
    dP as i,
    _ as a,
    s,
    c as o,
    v as n,
    a3 as l,
    h as r,
    m as c,
    x as m,
    a as d,
    o as u,
    w as g,
    b as h
} from "./Cf0SOiw0.js";
import {
    _ as f
} from "./e-ES_T8J.js";
import {
    g as p
} from "./CrbPJ6Kt.js";
import {
    a as b
} from "./FCN43o2W.js";
import {
    d as y
} from "./DOnko34f.js";
import {
    m as k
} from "./CqNssBtC.js";
import {
    c as w
} from "./Cp7w48vH.js";
import "./DbJ6Dt9m.js";
import "./Bl-gMEVt.js";
import "./DyMB-pVc.js";
import "./C-H3edso.js";
let v, F;
const B = new WeakMap,
    I = new WeakMap,
    A = new WeakMap,
    S = new WeakMap,
    C = new WeakMap;
let R = {
    get(t, e, i) {
        if (t instanceof IDBTransaction) {
            if ("done" === e) return I.get(t);
            if ("objectStoreNames" === e) return t.objectStoreNames || A.get(t);
            if ("store" === e) return i.objectStoreNames[1] ? void 0 : i.objectStore(i.objectStoreNames[0])
        }
        return M(t[e])
    },
    set: (t, e, i) => (t[e] = i, !0),
    has: (t, e) => t instanceof IDBTransaction && ("done" === e || "store" === e) || e in t
};

function j(t) {
    return t !== IDBDatabase.prototype.transaction || "objectStoreNames" in IDBTransaction.prototype ? (F || (F = [IDBCursor.prototype.advance, IDBCursor.prototype.continue, IDBCursor.prototype.continuePrimaryKey])).includes(t) ? function(...e) {
        return t.apply(T(this), e), M(B.get(this))
    } : function(...e) {
        return M(t.apply(T(this), e))
    } : function(e, ...i) {
        const a = t.call(T(this), e, ...i);
        return A.set(a, e.sort ? e.sort() : [e]), M(a)
    }
}

function x(t) {
    return "function" == typeof t ? j(t) : (t instanceof IDBTransaction && function(t) {
        if (I.has(t)) return;
        const e = new Promise(((e, i) => {
            const a = () => {
                    t.removeEventListener("complete", s), t.removeEventListener("error", o), t.removeEventListener("abort", o)
                },
                s = () => {
                    e(), a()
                },
                o = () => {
                    i(t.error || new DOMException("AbortError", "AbortError")), a()
                };
            t.addEventListener("complete", s), t.addEventListener("error", o), t.addEventListener("abort", o)
        }));
        I.set(t, e)
    }(t), e = t, (v || (v = [IDBDatabase, IDBObjectStore, IDBIndex, IDBCursor, IDBTransaction])).some((t => e instanceof t)) ? new Proxy(t, R) : t);
    var e
}

function M(t) {
    if (t instanceof IDBRequest) return function(t) {
        const e = new Promise(((e, i) => {
            const a = () => {
                    t.removeEventListener("success", s), t.removeEventListener("error", o)
                },
                s = () => {
                    e(M(t.result)), a()
                },
                o = () => {
                    i(t.error), a()
                };
            t.addEventListener("success", s), t.addEventListener("error", o)
        }));
        return e.then((e => {
            e instanceof IDBCursor && B.set(e, t)
        })).catch((() => {})), C.set(e, t), e
    }(t);
    if (S.has(t)) return S.get(t);
    const e = x(t);
    return e !== t && (S.set(t, e), C.set(e, t)), e
}
const T = t => C.get(t);
const E = ["get", "getKey", "getAll", "getAllKeys", "count"],
    _ = ["put", "add", "delete", "clear"],
    P = new Map;

function D(t, e) {
    if (!(t instanceof IDBDatabase) || e in t || "string" != typeof e) return;
    if (P.get(e)) return P.get(e);
    const i = e.replace(/FromIndex$/, ""),
        a = e !== i,
        s = _.includes(i);
    if (!(i in (a ? IDBIndex : IDBObjectStore).prototype) || !s && !E.includes(i)) return;
    const o = async function(t, ...e) {
        const o = this.transaction(t, s ? "readwrite" : "readonly");
        let n = o.store;
        return a && (n = n.index(e.shift())), (await Promise.all([n[i](...e), s && o.done]))[0]
    };
    return P.set(e, o), o
}
R = (t => ({ ...t,
    get: (e, i, a) => D(e, i) || t.get(e, i, a),
    has: (e, i) => !!D(e, i) || t.has(e, i)
}))(R);
const U = new class {
    constructor() {
        this.db = null
    }
    async init() {
        return this.db || (this.db = await
            function(t, e, {
                blocked: i,
                upgrade: a,
                blocking: s,
                terminated: o
            } = {}) {
                const n = indexedDB.open(t, e),
                    l = M(n);
                return a && n.addEventListener("upgradeneeded", (t => {
                    a(M(n.result), t.oldVersion, t.newVersion, M(n.transaction), t)
                })), i && n.addEventListener("blocked", (t => i(t.oldVersion, t.newVersion, t))), l.then((t => {
                    o && t.addEventListener("close", (() => o())), s && t.addEventListener("versionchange", (t => s(t.oldVersion, t.newVersion, t)))
                })).catch((() => {})), l
            }("ai-vstudio-editor-db", 1, {
                upgrade(t) {
                    if (!t.objectStoreNames.contains("projects")) {
                        const e = t.createObjectStore("projects", {
                            keyPath: "id"
                        });
                        e.createIndex("by_createdAt", "createdAt"), e.createIndex("by_updatedAt", "updatedAt")
                    }
                    if (!t.objectStoreNames.contains("scene_data")) {
                        t.createObjectStore("scene_data", {
                            keyPath: "id"
                        }).createIndex("by_projectId", "projectId")
                    }
                    if (!t.objectStoreNames.contains("media_cache")) {
                        const e = t.createObjectStore("media_cache", {
                            keyPath: "id"
                        });
                        e.createIndex("by_url", "url"), e.createIndex("by_assetId", "assetId")
                    }
                    if (!t.objectStoreNames.contains("assets")) {
                        const e = t.createObjectStore("assets", {
                            keyPath: "id"
                        });
                        e.createIndex("by_projectId", "projectId"), e.createIndex("by_type", "type"), e.createIndex("by_projectId_type", ["projectId", "type"]), e.createIndex("by_createdAt", "createdAt")
                    }
                }
            })), this.db
    }
    async createProject(t) {
        const e = await this.init(),
            i = {
                id: t.id || crypto.randomUUID(),
                title: t.title || "Untitled Project",
                description: t.description || "",
                aspectRatio: t.aspectRatio || "16:9",
                width: t.width || 1920,
                height: t.height || 1080,
                fps: t.fps || 30,
                createdAt: Date.now(),
                updatedAt: Date.now(),
                ...t
            };
        return await e.put("projects", i), i
    }
    async getProject(t) {
        const e = await this.init();
        return await e.get("projects", t)
    }
    async updateProject(t, e) {
        const i = await this.init(),
            a = await this.getProject(t);
        if (!a) throw new Error(`Project ${t} not found`);
        const s = { ...a,
            ...e,
            id: t,
            updatedAt: Date.now()
        };
        return await i.put("projects", s), s
    }
    async listProjects(t = 20, e = 0) {
        const i = (await this.init()).transaction("projects", "readonly").store.index("by_updatedAt"),
            a = [];
        let s = await i.openCursor(null, "prev"),
            o = 0;
        for (; s && (o >= e && o < e + t && a.push(s.value), o++, !(o >= e + t));) s = await s.continue();
        return a
    }
    async saveSceneData(t, e) {
        const i = await this.init(),
            a = await i.getAllFromIndex("scene_data", "by_projectId", t);
        let s;
        return s = a.length > 0 ? { ...a[0],
            sceneData: e,
            timestamp: Date.now()
        } : {
            id: crypto.randomUUID(),
            projectId: t,
            sceneData: e,
            timestamp: Date.now()
        }, await i.put("scene_data", s), await this.updateProject(t, {
            lastSceneId: s.id
        }), s
    }
    async getLatestSceneData(t) {
        const e = await this.init(),
            i = await e.getAllFromIndex("scene_data", "by_projectId", t);
        return 0 === i.length ? null : i[0]
    }
    async getSceneHistory(t, e = 10) {
        const i = await this.init();
        return (await i.getAllFromIndex("scene_data", "by_projectId", t)).slice(0, e)
    }
    async cacheMedia(t) {
        const e = await this.init(),
            i = {
                id: t.id || crypto.randomUUID(),
                url: t.url,
                assetId: t.assetId,
                blob: t.blob,
                mimeType: t.mimeType,
                size: t.size,
                metadata: t.metadata || {},
                cachedAt: Date.now()
            };
        return await e.put("media_cache", i), i
    }
    async getCachedMedia(t) {
        const e = await this.init();
        if (t.id) return await e.get("media_cache", t.id);
        if (t.url) {
            return (await e.getAllFromIndex("media_cache", "by_url", t.url))[0] || null
        }
        if (t.assetId) {
            return (await e.getAllFromIndex("media_cache", "by_assetId", t.assetId))[0] || null
        }
        return null
    }
    async clearMediaCache() {
        const t = (await this.init()).transaction("media_cache", "readwrite");
        await t.store.clear(), await t.done
    }
    async deleteProject(t) {
        const e = (await this.init()).transaction(["projects", "scene_data", "assets"], "readwrite");
        await e.objectStore("projects").delete(t);
        const i = await e.objectStore("scene_data").index("by_projectId").getAllKeys(t);
        for (const s of i) await e.objectStore("scene_data").delete(s);
        const a = await e.objectStore("assets").index("by_projectId").getAllKeys(t);
        for (const s of a) await e.objectStore("assets").delete(s);
        await e.done
    }
    async saveAsset(t) {
        var e;
        const i = await this.init(),
            a = {
                id: t.id,
                label: t.label || {
                    en: "Untitled"
                },
                tags: t.tags || {
                    en: []
                },
                meta: t.meta || {},
                projectId: t.projectId,
                type: t.type || this.getAssetType(null == (e = t.meta) ? void 0 : e.mimeType),
                description: t.description || "",
                ai_drive_path: t.ai_drive_path || "",
                clips: t.clips || [],
                createdAt: t.createdAt || Date.now(),
                updatedAt: Date.now()
            };
        return await i.put("assets", a), a
    }
    async getAsset(t) {
        const e = await this.init();
        return await e.get("assets", t)
    }
    async getProjectAssets(t, e = null) {
        const i = await this.init();
        return e ? await i.getAllFromIndex("assets", "by_projectId_type", [t, e]) : await i.getAllFromIndex("assets", "by_projectId", t)
    }
    async updateAsset(t, e) {
        const i = await this.init(),
            a = await this.getAsset(t);
        if (!a) throw new Error(`Asset ${t} not found`);
        a.meta = { ...a.meta,
            ...e.meta
        }, delete e.meta;
        const s = { ...a,
            ...e,
            id: t,
            updatedAt: Date.now()
        };
        return await i.put("assets", s), s
    }
    async deleteAsset(t) {
        const e = await this.init();
        await e.delete("assets", t)
    }
    async deleteProjectAssets(t) {
        const e = await this.init(),
            i = await this.getProjectAssets(t),
            a = e.transaction("assets", "readwrite");
        for (const s of i) await a.store.delete(s.id);
        await a.done
    }
    getAssetType(t) {
        return t ? t.startsWith("image/") ? "image" : t.startsWith("video/") ? "video" : t.startsWith("audio/") ? "audio" : t.startsWith("font/") ? "typeface" : t.startsWith("application/pdf") ? "template" : t.startsWith("text/") ? "text" : t.startsWith("application/json") ? "data" : t.startsWith("application/zip") ? "archive" : t.startsWith("model/") ? "3d" : "other" : "other"
    }
};
async function O(t, e, i = 100) {
    const a = t.block.createFill("video");
    return t.block.setString(a, "fill/video/fileURI", e), new Promise((async (e, s) => {
        try {
            await t.block.forceLoadAVResource(a);
            t.block.generateVideoThumbnailSequence(a, i, 0, 0, 1, (async (i, o) => {
                if (o instanceof Error) s(o);
                else try {
                    const {
                        blob: t,
                        blobUrl: i
                    } = await async function(t) {
                        const e = document.createElement("canvas");
                        e.width = t.width, e.height = t.height;
                        return e.getContext("2d").putImageData(t, 0, 0), new Promise(((t, i) => {
                            e.toBlob((e => {
                                if (e) {
                                    const i = Math.random().toString(36).substring(2, 8),
                                        a = new File([e], `thumb_${i}.jpg`, {
                                            type: "image/jpeg",
                                            lastModified: Date.now()
                                        }),
                                        s = URL.createObjectURL(a);
                                    t({
                                        blob: a,
                                        blobUrl: s
                                    })
                                } else i(new Error("Failed to create blob from canvas"))
                            }), "image/jpeg", .8)
                        }))
                    }(o);
                    e({
                        blobUrl: i,
                        blob: t
                    })
                } catch (n) {
                    s(n)
                }
                t.block.destroy(a)
            }))
        } catch (o) {
            t.block.isValid(a) && t.block.destroy(a), s(o)
        }
    }))
}
const z = t => {
        if (!t.startsWith("aidrive://")) return t;
        const e = t.replace("aidrive://", "");
        return `/api/aidrive/download/files/${L(e)}`
    },
    L = t => t.trim().replace(/^\/+|\/+$/g, "").split("/").map((t => encodeURIComponent(t))).join("/");
const X = new class {
    constructor() {
        this.blobUrlMap = new Map, this.pendingFetches = new Map, this.projectId = null, this.pendingProjectAssets = new Map
    }
    setProjectId(t) {
        this.projectId = t
    }
    createBlobUrl(t, e) {
        this.blobUrlMap.has(t) && URL.revokeObjectURL(this.blobUrlMap.get(t));
        const i = URL.createObjectURL(e);
        return this.blobUrlMap.set(t, i), i
    }
    revokeAsset(t) {
        this.blobUrlMap.has(t) && (URL.revokeObjectURL(this.blobUrlMap.get(t)), this.blobUrlMap.delete(t))
    }
    revokeAllAssets() {
        this.blobUrlMap.forEach((t => URL.revokeObjectURL(t))), this.blobUrlMap.clear()
    }
    async getCachedAsset(t) {
        let e = await U.getCachedMedia({
            assetId: t
        });
        return e || (e = await U.getCachedMedia({
            url: t
        })), e
    }
    async getAllCachedAssets() {
        const t = await U.init();
        return await t.getAll("media_cache")
    }
    async cacheMedia(t) {
        return await U.cacheMedia(t)
    }
    async deleteCachedAsset(t) {
        this.revokeAsset(t);
        const e = await this.getCachedAsset(t);
        if (e) {
            const t = await U.init();
            await t.delete("media_cache", e.id)
        }
    }
    async cleanupExpiredCache(t = 6048e5) {
        const e = Date.now(),
            i = await this.getAllCachedAssets();
        for (const a of i) e - a.cachedAt > t && await this.deleteCachedAsset(a.assetId || a.id)
    }
    async getCacheStats() {
        const t = await this.getAllCachedAssets();
        let e = 0;
        return t.forEach((t => {
            e += t.size || 0
        })), {
            totalAssets: t.length,
            totalSize: e,
            blobUrls: this.blobUrlMap.size,
            pendingFetches: this.pendingFetches.size
        }
    }
    async isValidMediaBlob(t, e = null) {
        if (!t || 0 === t.size) return !1;
        let i = null;
        return e && e.metadata && e.metadata.mimeType && (e.metadata.mimeType.startsWith("image/") ? i = "image" : e.metadata.mimeType.startsWith("video/") ? i = "video" : e.metadata.mimeType.startsWith("audio/") && (i = "audio")), i || (t.type.startsWith("image/") ? i = "image" : t.type.startsWith("video/") ? i = "video" : t.type.startsWith("audio/") && (i = "audio")), "image" === i ? t.type.startsWith("image/") : "video" === i ? t.type.startsWith("video/") : "audio" === i ? !!t.type.startsWith("audio/") && new Promise((i => {
            const a = new Audio;
            a.onloadedmetadata = () => {
                let t = e && e.metadata && e.metadata.duration ? e.metadata.duration : 0;
                if (!t) return void i(!0);
                const s = a.duration > 0 && !isNaN(a.duration) && Math.abs(a.duration - t) < 2;
                i(s)
            }, a.onerror = () => i(!1), a.src = URL.createObjectURL(t)
        })) : t.size > 0
    }
    getUrlWithCacheBuster(t) {
        const e = "_t=buster";
        return t ? t.includes("?") ? `${t}&${e}` : `${t}?${e}` : ""
    }
    async analyzeMedia(t, e = "") {
        const i = {
            url: e,
            fileSize: t.size,
            mimeType: t.type
        };
        return t.type.startsWith("image/") ? await this.analyzeImage(t, i) : t.type.startsWith("video/") ? await this.analyzeVideo(t, i) : t.type.startsWith("audio/") ? await this.analyzeAudio(t, i) : i
    }
    async analyzeImage(t, e) {
        return new Promise((i => {
            const a = new Image,
                s = URL.createObjectURL(t);
            a.onload = () => {
                e.width = a.width, e.height = a.height, e.aspectRatio = a.width / a.height, URL.revokeObjectURL(s), i(e)
            }, a.onerror = () => {
                URL.revokeObjectURL(s), i(e)
            }, a.src = s
        }))
    }
    async analyzeVideo(t, e) {
        return new Promise((i => {
            const a = document.createElement("video"),
                s = URL.createObjectURL(t);
            a.onloadedmetadata = () => {
                e.width = a.videoWidth, e.height = a.videoHeight, e.duration = a.duration, e.aspectRatio = a.videoWidth / a.videoHeight, URL.revokeObjectURL(s), i(e)
            }, a.onerror = () => {
                URL.revokeObjectURL(s), i(e)
            }, a.src = s
        }))
    }
    async analyzeAudio(t, e) {
        return new Promise((i => {
            const a = new Audio,
                s = URL.createObjectURL(t);
            a.onloadedmetadata = () => {
                e.duration = a.duration, URL.revokeObjectURL(s), i(e)
            }, a.onerror = () => {
                URL.revokeObjectURL(s), i(e)
            }, a.src = s
        }))
    }
    getAssetKind(t) {
        return t ? t.startsWith("image/") ? "image" : t.startsWith("video/") ? "video" : t.startsWith("audio/") ? "audio" : t.startsWith("font/") ? "typeface" : t.startsWith("application/pdf") ? "template" : t.startsWith("text/") ? "text" : t.startsWith("application/json") ? "data" : "other" : "other"
    }
    async saveAssetToDatabase(t) {
        this.projectId ? await U.saveAsset({ ...t,
            projectId: this.projectId
        }) : this.pendingProjectAssets.set(t.id, t)
    }
    async updateAssetUrl(t, e) {
        try {
            const i = await this.getCachedAsset(t);
            if (!i) return !1;
            const a = await U.init();
            if (i.url = e, i.metadata = i.metadata || {}, i.metadata.cloudUrl = e, i.metadata.uploadedAt = Date.now(), await a.put("media_cache", i), this.projectId && await U.updateAsset(t, {
                    meta: {
                        uri: e,
                        cloudUrl: e
                    }
                }), this.pendingProjectAssets.has(t)) {
                const i = this.pendingProjectAssets.get(t);
                i.meta.uri = e, i.meta.cloudUrl = e
            }
            const s = this.blobUrlMap.get(t);
            return s && this.blobUrlMap.set(e, s), !0
        } catch (i) {
            return !1
        }
    }
    async updateAssetThumbnail(t, e) {
        try {
            const i = await this.getCachedAsset(t);
            if (!i) return !1;
            const a = await U.init();
            if (i.metadata = i.metadata || {}, i.metadata.thumbUri = e, i.metadata.thumbnailUploadedAt = Date.now(), await a.put("media_cache", i), this.projectId && await U.updateAsset(t, {
                    meta: {
                        thumbUri: e
                    }
                }), this.pendingProjectAssets.has(t)) {
                this.pendingProjectAssets.get(t).meta.thumbUri = e
            }
            return !0
        } catch (i) {
            return !1
        }
    }
    async syncPendingAssetsToDatabase(t) {
        if (t) try {
            if (0 === this.pendingProjectAssets.size) return;
            let i = 0;
            for (const [a, s] of this.pendingProjectAssets) try {
                if (!(await U.getAsset(a))) {
                    const e = await this.getCachedAsset(a);
                    e && e.url && (s.meta.uri = e.url), await U.saveAsset({ ...s,
                        projectId: t
                    }), i++
                }
            } catch (e) {}
            this.pendingProjectAssets.clear()
        } catch (e) {}
    }
    async getProjectAssets(t) {
        return await U.getProjectAssets(t)
    }
    async exportAsset(t, e) {
        var i;
        const a = await this.getCachedAsset(t);
        if (!a || !a.blob) throw new Error(`Asset ${t} not found in cache`);
        const s = URL.createObjectURL(a.blob),
            o = document.createElement("a");
        o.href = s, o.download = e || (null == (i = a.metadata) ? void 0 : i.name) || "download", document.body.appendChild(o), o.click(), document.body.removeChild(o), URL.revokeObjectURL(s)
    }
    async cleanup() {
        this.revokeAllAssets(), this.pendingFetches.clear()
    }
    async getAsset(t) {
        var e;
        const i = await this.getCachedAsset(t);
        return i ? {
            id: i.assetId || i.id,
            meta: {
                uri: i.url,
                name: null == (e = i.metadata) ? void 0 : e.name,
                ...i.metadata
            }
        } : null
    }
    async handleAsyncResource(t) {
        try {
            if (this.pendingFetches.has(t)) return void(await this.pendingFetches.get(t));
            const e = this._fetchAndCacheResource(t);
            this.pendingFetches.set(t, e), await e
        } catch (e) {} finally {
            this.pendingFetches.delete(t)
        }
    }
    async _fetchAndCacheResource(t) {
        try {
            let i = await this.getCachedAsset(t);
            if (i && i.blob && await this.isValidMediaBlob(i.blob, i)) {
                return {
                    blobUrl: this.createBlobUrl(t, i.blob),
                    asset: i
                }
            }
            let a, s = t;
            t.startsWith("aidrive://") && (s = z(t));
            try {
                a = await fetch(s)
            } catch (e) {
                a = await fetch(this.getUrlWithCacheBuster(s))
            }
            if (!a.ok) throw new Error(`HTTP error! status: ${a.status}`);
            const o = await a.blob();
            if (!(await this.isValidMediaBlob(o))) throw new Error(`Invalid media blob for ${t}`);
            const n = await this.analyzeMedia(o, t),
                l = {
                    id: `cache-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,
                    url: t,
                    assetId: t,
                    blob: o,
                    mimeType: o.type,
                    size: o.size,
                    metadata: {
                        name: t.split("/").pop(),
                        ...n
                    }
                };
            await this.cacheMedia(l);
            return {
                blobUrl: this.createBlobUrl(t, o),
                asset: l
            }
        } catch (e) {
            throw e
        }
    }
};
const N = new class {
        constructor() {
            this.engine = null, this.assetCacheManager = X
        }
        async initialize(t, e = null) {
            this.engine = t, this.assetCacheManager.setProjectId(e), await this.preloadCachedUrls(), this.setupURIResolver(), this.setupApplyAssetMiddleware()
        }
        async preloadCachedUrls() {
            try {
                const t = await this.assetCacheManager.getAllCachedAssets();
                for (const e of t)
                    if (e.url && e.blob) {
                        this.assetCacheManager.createBlobUrl(e.url, e.blob)
                    }
            } catch (t) {}
        }
        setupURIResolver() {
            this.engine && this.engine.editor.setURIResolver(((t, e) => {
                if (!t.startsWith("http") && !t.startsWith("aidrive")) return e(t);
                if (this.assetCacheManager.blobUrlMap.has(t)) {
                    return this.assetCacheManager.blobUrlMap.get(t)
                }
                return this.assetCacheManager.handleAsyncResource(t), t.startsWith("aidrive") ? z(t) : t
            }))
        }
        async updateBlocksWithNewUri(t, e) {
            const i = this.engine.block.findAll();
            for (const s of i) try {
                if (this.engine.block.supportsFill(s)) {
                    const i = this.engine.block.getFill(s);
                    if (i && this.engine.block.isValid(i)) {
                        const a = this.engine.block.getType(i);
                        if (a.endsWith("/image")) {
                            this.engine.block.getString(i, "fill/image/imageFileURI") === t && this.engine.block.setString(i, "fill/image/imageFileURI", e)
                        } else if (a.endsWith("/video")) {
                            this.engine.block.getString(i, "fill/video/fileURI") === t && this.engine.block.setString(i, "fill/video/fileURI", e)
                        }
                    }
                }
            } catch (a) {}
        }
        setupApplyAssetMiddleware() {
            this.engine && this.engine.asset.unstable_registerApplyAssetMiddleware((async (t, e, i) => {
                const a = await i(t, e);
                if (a && e.id) try {
                    this.engine.block.setMetadata(a, "sourceAssetId", e.id)
                } catch (s) {}
                return a
            }))
        }
        async addAssetFromFile(t, e = {}) {
            const i = e.id || `file-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,
                a = await this.assetCacheManager.analyzeMedia(t, t.name),
                s = {
                    id: `cache-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,
                    url: i,
                    assetId: i,
                    blob: t,
                    mimeType: t.type,
                    size: t.size,
                    metadata: {
                        name: t.name,
                        ...a,
                        ...e
                    }
                };
            await this.assetCacheManager.cacheMedia(s);
            const o = this.assetCacheManager.createBlobUrl(i, t);
            let n = e.thumbUri || o,
                l = null,
                r = null;
            if (t.type.startsWith("video/") && this.engine) try {
                const {
                    blobUrl: t,
                    blob: e
                } = await O(this.engine, o, 200);
                n = t, l = e, r = t
            } catch (m) {}
            const c = {
                id: i,
                label: {
                    en: e.label || t.name
                },
                tags: {
                    en: e.tags || []
                },
                meta: {
                    uri: o,
                    thumbUri: n,
                    name: t.name,
                    mimeType: t.type,
                    kind: this.assetCacheManager.getAssetKind(t.type),
                    width: a.width,
                    height: a.height,
                    duration: a.duration,
                    ...e
                }
            };
            return await this.assetCacheManager.saveAssetToDatabase({ ...c,
                type: this.assetCacheManager.getAssetKind(t.type),
                description: e.description || "",
                ai_drive_path: e.ai_drive_path || "",
                clips: e.clips || []
            }), {
                assetId: i,
                blobUrl: o,
                assetDefinition: c,
                thumbnailBlob: l,
                thumbnailBlobUrl: r
            }
        }
        async addAssetFromUrl(t, e = {}) {
            var i, a;
            const s = e.id || t;
            this.assetCacheManager.handleAsyncResource(t);
            const o = {
                id: s,
                label: {
                    en: e.label || "Untitled"
                },
                tags: {
                    en: e.tags || []
                },
                meta: {
                    uri: t,
                    thumbUri: "",
                    name: e.file_name || t.split("/").pop(),
                    kind: e.assetType,
                    width: null == (i = e.resolution) ? void 0 : i.width,
                    height: null == (a = e.resolution) ? void 0 : a.height,
                    duration: e.duration,
                    ...e
                }
            };
            if (e.addToLibrary && this.engine) {
                let i = null;
                switch (e.assetType) {
                    case "image":
                        i = "ly.img.image", o.meta.thumbUri = t;
                        break;
                    case "video":
                        i = "ly.img.video";
                        break;
                    case "audio":
                        i = "ly.img.audio";
                        break;
                    case "sticker":
                        i = "ly.img.sticker";
                        break;
                    case "template":
                        i = "ly.img.template";
                        break;
                    case "text":
                        i = "ly.img.text";
                        break;
                    case "vectorpath":
                        i = "ly.img.vectorpath";
                        break;
                    case "typeface":
                        i = "ly.img.typeface"
                }
                if (i) try {
                    this.engine.asset.addAssetToSource(i, o)
                } catch (n) {}
            }
            return await this.assetCacheManager.saveAssetToDatabase({ ...o,
                type: e.assetType,
                description: e.description || "",
                ai_drive_path: e.ai_drive_path || "",
                clips: e.clips || []
            }), {
                assetId: s,
                assetDefinition: o
            }
        }
        async getCachedAsset(t) {
            return this.assetCacheManager.getCachedAsset(t)
        }
        async getAllCachedAssets() {
            return this.assetCacheManager.getAllCachedAssets()
        }
        async deleteCachedAsset(t) {
            return this.assetCacheManager.deleteCachedAsset(t)
        }
        async cleanupExpiredCache(t) {
            return this.assetCacheManager.cleanupExpiredCache(t)
        }
        async getCacheStats() {
            return this.assetCacheManager.getCacheStats()
        }
        async cleanup() {
            await this.assetCacheManager.cleanup()
        }
        async exportAsset(t, e) {
            return this.assetCacheManager.exportAsset(t, e)
        }
        async _updateBlocksUsingResource(t, e) {
            if (this.engine) try {
                const a = this.engine.block.findByType("graphic"),
                    s = this.engine.block.findByType("audio");
                for (const o of s) try {
                    this.engine.block.getString(o, "audio/fileURI") === t && this.engine.block.setString(o, "audio/fileURI", e)
                } catch (i) {}
                for (const o of a) {
                    if (!this.engine.block.supportsFill(o)) continue;
                    const a = this.engine.block.getFill(o);
                    if (!a || !this.engine.block.isValid(a)) continue;
                    const s = this.engine.block.getType(a);
                    if ("//ly.img.ubq/fill/image" === s) {
                        try {
                            this.engine.block.getString(a, "fill/image/imageFileURI") === t && this.engine.block.setString(a, "fill/image/imageFileURI", e)
                        } catch (i) {}
                        try {
                            this.engine.block.getString(a, "fill/image/previewFileURI") === t && this.engine.block.setString(a, "fill/image/previewFileURI", e)
                        } catch (i) {}
                        try {
                            const i = this.engine.block.getSourceSet(a, "fill/image/sourceSet");
                            if (i && i.length > 0) {
                                const s = i.map((i => i.uri === t ? { ...i,
                                    uri: e
                                } : i));
                                JSON.stringify(i) !== JSON.stringify(s) && this.engine.block.setSourceSet(a, "fill/image/sourceSet", s)
                            }
                        } catch (i) {}
                    } else if ("//ly.img.ubq/fill/video" === s) {
                        try {
                            this.engine.block.getString(a, "fill/video/fileURI") === t && this.engine.block.setString(a, "fill/video/fileURI", e)
                        } catch (i) {}
                        try {
                            this.engine.block.getString(a, "fill/video/previewFileURI") === t && this.engine.block.setString(a, "fill/video/previewFileURI", e)
                        } catch (i) {}
                        try {
                            const i = this.engine.block.getSourceSet(a, "fill/video/sourceSet");
                            if (i && i.length > 0) {
                                const s = i.map((i => i.uri === t ? { ...i,
                                    uri: e
                                } : i));
                                JSON.stringify(i) !== JSON.stringify(s) && this.engine.block.setSourceSet(a, "fill/video/sourceSet", s)
                            }
                        } catch (i) {}
                    }
                }
            } catch (a) {}
        }
        _getSourceIdForAsset(t) {
            var e;
            switch (t.type || (null == (e = t.meta) ? void 0 : e.kind) || "other") {
                case "image":
                    return "ly.img.image.upload";
                case "video":
                    return "ly.img.video.upload";
                case "audio":
                    return "ly.img.audio.upload";
                default:
                    return null
            }
        }
        async _updateAssetInEngine(t, e) {
            if (this.engine) try {
                let i = null;
                const a = this.assetCacheManager.projectId;
                if (a) {
                    i = (await this.assetCacheManager.getProjectAssets(a)).find((e => e.id === t))
                }
                if (!i && this.assetCacheManager.pendingProjectAssets.has(t) && (i = this.assetCacheManager.pendingProjectAssets.get(t)), i) {
                    const a = this._getSourceIdForAsset(i);
                    if (!a) return;
                    this.engine.asset.removeAssetFromSource(a, t);
                    const s = e(i);
                    this.engine.asset.addAssetToSource(a, s), this.engine.asset.assetSourceContentsChanged(a)
                }
            } catch (i) {}
        }
        async updateAssetUrl(t, e, i = !1) {
            try {
                if (!(await this.assetCacheManager.updateAssetUrl(t, e))) return !1;
                if (i) {
                    const i = this.assetCacheManager.blobUrlMap.get(t);
                    i && this.engine && (await this._updateBlocksUsingResource(i, e), await this._updateAssetInEngine(t, (t => ({ ...t,
                        meta: { ...t.meta,
                            uri: e,
                            cloudUrl: e
                        }
                    }))))
                }
                return !0
            } catch (a) {
                return !1
            }
        }
        async updateAssetThumbnail(t, e, i = !1) {
            try {
                return !!(await this.assetCacheManager.updateAssetThumbnail(t, e)) && (i && await this._updateAssetInEngine(t, (t => ({ ...t,
                    meta: { ...t.meta,
                        thumbUri: e
                    }
                }))), !0)
            } catch (a) {
                return !1
            }
        }
        async getAsset(t) {
            return this.assetCacheManager.getAsset(t)
        }
        revokeAsset(t) {
            return this.assetCacheManager.revokeAsset(t)
        }
        revokeAllAssets() {
            return this.assetCacheManager.revokeAllAssets()
        }
        setProjectId(t) {
            this.assetCacheManager.setProjectId(t)
        }
        async syncPendingAssetsToDatabase(t) {
            return this.assetCacheManager.syncPendingAssetsToDatabase(t)
        }
        async loadProjectAssets(t) {
            if (this.engine) try {
                const i = await this.assetCacheManager.getProjectAssets(t),
                    a = {};
                for (const t of i) {
                    const e = t.type || "other";
                    let i = null;
                    switch (e) {
                        case "image":
                            i = "ly.img.image";
                            break;
                        case "video":
                            i = "ly.img.video";
                            break;
                        case "audio":
                            i = "ly.img.audio";
                            break;
                        case "sticker":
                            i = "ly.img.sticker";
                            break;
                        case "template":
                            i = "ly.img.template";
                            break;
                        case "text":
                            i = "ly.img.text";
                            break;
                        case "vectorpath":
                            i = "ly.img.vectorpath";
                            break;
                        case "typeface":
                            i = "ly.img.typeface";
                            break;
                        default:
                            continue
                    }
                    a[i] || (a[i] = []), a[i].push(t)
                }
                for (const [t, s] of Object.entries(a))
                    if (0 !== s.length)
                        for (const i of s) try {
                            this.engine.asset.addAssetToSource(t, i)
                        } catch (e) {}
            } catch (e) {}
        }
    },
    V = "pending",
    Y = "uploading",
    q = "success",
    K = "failed",
    G = "cancelled";
class Q {
    constructor(t, e, i = {}) {
        this.id = t, this.file = e, this.fileName = e.name, this.fileSize = e.size, this.fileType = e.type, this.status = V, this.progress = 0, this.error = null, this.cloudUrl = null, this.startTime = null, this.endTime = null, this.retryCount = 0, this.maxRetries = i.maxRetries || 3, this.onProgress = i.onProgress, this.onComplete = i.onComplete, this.onError = i.onError, this.metadata = i.metadata || {}, this.xhr = null, this.toAiDrive = i.toAiDrive || !1, this.projectId = i.metadata && i.metadata.projectId || null, this.promise = null, this._resolve = null, this._reject = null, this._createPromise()
    }
    _createPromise() {
        this.promise = new Promise(((t, e) => {
            this._resolve = t, this._reject = e
        }))
    }
    getSpeed() {
        if (!this.startTime || this.status !== Y) return 0;
        const t = Date.now() - this.startTime;
        return this.fileSize * (this.progress / 100) / (t / 1e3)
    }
    getRemainingTime() {
        const t = this.getSpeed();
        if (0 === t) return 1 / 0;
        return this.fileSize * ((100 - this.progress) / 100) / t
    }
}
class W {
    constructor(t = {}) {
        this.maxConcurrent = t.maxConcurrent || 3, this.autoRetry = !1 !== t.autoRetry, this.retryDelay = t.retryDelay || 1e3, this.queue = [], this.activeUploads = new Map, this.completedUploads = new Map, this.directoryCache = new Set, this.pendingDirectoryRequests = new Map, this.stats = e({
            totalFiles: 0,
            uploadedFiles: 0,
            failedFiles: 0,
            totalBytes: 0,
            uploadedBytes: 0
        }), this.onQueueUpdate = t.onQueueUpdate, this.onStatsUpdate = t.onStatsUpdate
    }
    addFile(t, e = {}) {
        const i = `upload-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,
            a = new Q(i, t, e);
        return this.queue.push(a), this.stats.totalFiles++, this.stats.totalBytes += t.size, this._notifyQueueUpdate(), this._processQueue(), i
    }
    async addFileAsync(t, e = {}) {
        const i = `upload-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,
            a = new Q(i, t, e);
        return this.queue.push(a), this.stats.totalFiles++, this.stats.totalBytes += t.size, this._notifyQueueUpdate(), this._processQueue(), a.promise
    }
    addFiles(t, e = {}) {
        const i = [];
        for (const a of t) i.push(this.addFile(a, e));
        return i
    }
    cancelUpload(t) {
        const e = this.queue.findIndex((e => e.id === t));
        if (e >= 0) {
            const t = this.queue.splice(e, 1)[0];
            return t.status = G, t._reject && t._reject(new Error("Upload cancelled")), this._notifyQueueUpdate(), !0
        }
        const i = this.activeUploads.get(t);
        return !!i && (i.status = G, i.xhr && (i.xhr.abort(), i.xhr = null), i._reject && i._reject(new Error("Upload cancelled")), this.activeUploads.delete(t), this.completedUploads.set(t, i), this._processQueue(), !0)
    }
    retryUpload(t) {
        const e = this.completedUploads.get(t);
        return !(!e || e.status !== K) && (e.status = V, e.progress = 0, e.error = null, e.retryCount++, e._createPromise(), this.completedUploads.delete(t), this.queue.push(e), this.stats.failedFiles--, this._notifyQueueUpdate(), this._processQueue(), !0)
    }
    getTaskStatus(t) {
        const e = this.queue.find((e => e.id === t));
        if (e) return e;
        const i = this.activeUploads.get(t);
        if (i) return i;
        const a = this.completedUploads.get(t);
        return a || null
    }
    getAllTasks() {
        return [...this.queue, ...Array.from(this.activeUploads.values()), ...Array.from(this.completedUploads.values())]
    }
    clearCompleted() {
        Array.from(this.completedUploads.values()).filter((t => t.status === q)).forEach((t => {
            this.completedUploads.delete(t.id)
        })), this._notifyQueueUpdate()
    }
    async _processQueue() {
        for (; this.queue.length > 0 && this.activeUploads.size < this.maxConcurrent;) {
            const t = this.queue.shift();
            t.status === V && this._uploadFile(t)
        }
    }
    async _uploadFile(t) {
        try {
            let e;
            if (t.status = Y, t.startTime = Date.now(), this.activeUploads.set(t.id, t), this._notifyQueueUpdate(), t.toAiDrive) e = await this._uploadToAiDrive(t);
            else {
                const i = await p();
                e = i.split("?")[0];
                const a = await this._uploadWithProgress(i, t, (e => {
                    t.progress = e, this._updateUploadProgress(t), t.onProgress && t.onProgress(e, t)
                }));
                if (!a.success) throw new Error(a.message || "Upload failed")
            }
            t.status = q, t.cloudUrl = e, t.endTime = Date.now(), t.progress = 100, this.stats.uploadedFiles++, this.stats.uploadedBytes += t.fileSize, this.activeUploads.delete(t.id), this.completedUploads.set(t.id, t);
            const i = {
                taskId: t.id,
                cloudUrl: e,
                fileName: t.fileName,
                fileSize: t.fileSize,
                fileType: t.fileType,
                metadata: t.metadata
            };
            t.onComplete && t.onComplete(i), t._resolve && t._resolve(i)
        } catch (e) {
            t.error = e.message, t.endTime = Date.now(), this.autoRetry && t.retryCount < t.maxRetries ? (t.retryCount++, t.status = V, t.progress = 0, t.error = null, setTimeout((() => {
                this.queue.push(t), this._processQueue()
            }), this.retryDelay * t.retryCount)) : (t.status = K, this.stats.failedFiles++, this.activeUploads.delete(t.id), this.completedUploads.set(t.id, t), t.onError && t.onError(e, t), t._reject && t._reject(e))
        }
        this._notifyQueueUpdate(), this._notifyStatsUpdate(), this._processQueue()
    }
    async _uploadWithProgress(t, e, i) {
        return new Promise(((a, s) => {
            const o = new XMLHttpRequest;
            e.xhr = o, o.upload.addEventListener("progress", (t => {
                if (t.lengthComputable) {
                    const e = Math.round(t.loaded / t.total * 100);
                    i(e)
                }
            })), o.addEventListener("load", (() => {
                e.xhr = null, o.status >= 200 && o.status < 300 ? a({
                    success: !0
                }) : s(new Error(`Upload failed with status: ${o.status}`))
            })), o.addEventListener("error", (() => {
                e.xhr = null, s(new Error("Network error during upload"))
            })), o.addEventListener("abort", (() => {
                e.xhr = null, s(new Error("Upload was aborted"))
            })), o.open("PUT", t), o.setRequestHeader("x-ms-blob-type", "BlockBlob"), o.setRequestHeader("Content-Type", e.file.type || "application/octet-stream"), o.send(e.file)
        }))
    }
    async _ensureDirectoryExists(t) {
        if (this.directoryCache.has(t)) return !0;
        if (this.pendingDirectoryRequests.has(t)) return await this.pendingDirectoryRequests.get(t);
        const e = (async () => {
            try {
                return await b.createDirectoryWithParents(t), this.directoryCache.add(t), !0
            } catch (e) {
                throw e
            } finally {
                this.pendingDirectoryRequests.delete(t)
            }
        })();
        return this.pendingDirectoryRequests.set(t, e), await e
    }
    async _uploadToAiDrive(t) {
        const e = t.projectId || "assets",
            i = `/video_project/${e}/${encodeURIComponent(t.fileName)}`,
            a = `/video_project/${e}`;
        await this._ensureDirectoryExists(a);
        const s = await b.getUploadUrl(i);
        if ("success" !== s.status) throw new Error(`Failed to get upload URL: ${s.message}`);
        const {
            upload_url: o,
            token: n
        } = s.data;
        await this._uploadWithProgress(o, t, (e => {
            t.progress = e, this._updateUploadProgress(t), t.onProgress && t.onProgress(e, t)
        }));
        const l = await b.confirmUpload(i, n, t.fileType || "application/octet-stream");
        if ("success" !== l.status) throw new Error(`Failed to confirm upload: ${l.message}`);
        return `aidrive://${i.replace(/^\//,"")}`
    }
    _updateUploadProgress(t) {
        const e = t.fileSize * (t.progress / 100),
            i = t.fileSize * ((t.progress - 1) / 100);
        this.stats.uploadedBytes += e - i, this._notifyQueueUpdate(), this._notifyStatsUpdate()
    }
    _notifyQueueUpdate() {
        this.onQueueUpdate && this.onQueueUpdate(this.getAllTasks())
    }
    _notifyStatsUpdate() {
        this.onStatsUpdate && this.onStatsUpdate({ ...this.stats
        })
    }
    getOverallProgress() {
        return 0 === this.stats.totalBytes ? 0 : Math.round(this.stats.uploadedBytes / this.stats.totalBytes * 100)
    }
    pauseAll() {
        this.activeUploads.forEach((t => {
            t.status = V, this.queue.unshift(t)
        })), this.activeUploads.clear(), this._notifyQueueUpdate()
    }
    resumeAll() {
        this._processQueue()
    }
    clearAll() {
        this.queue = [], this.activeUploads.clear(), this.completedUploads.clear(), this.stats.totalFiles = 0, this.stats.uploadedFiles = 0, this.stats.failedFiles = 0, this.stats.totalBytes = 0, this.stats.uploadedBytes = 0, this._notifyQueueUpdate(), this._notifyStatsUpdate()
    }
    async renameAiDriveProjectFolder(t, e) {
        if (!t || !e) return !1;
        try {
            const i = `/video_project/${t}`,
                a = `/video_project/${e}`;
            return "success" === (await b.moveItem(i, a)).status
        } catch (i) {
            return !1
        }
    }
}
let H = null;

function J(t = {}) {
    return H || (H = new W(t)), H
}
async function Z(t) {
    if (!t || !t.scene) throw new Error("A valid CreativeEngine instance must be provided.");
    const e = t.scene.get();
    if (null == e) return {
        context: null,
        scene: null,
        assetLibrary: []
    };
    const i = await $(t, e);
    return {
        context: {
            editMode: t.editor.getEditMode(),
            selectedBlockIds: t.block.findAllSelected(),
            canUndo: t.editor.canUndo(),
            canRedo: t.editor.canRedo()
        },
        scene: i,
        assetLibrary: await it(t)
    }
}
async function $(t, e) {
    if (!t.block.isValid(e)) return null;
    const i = await async function(t, e) {
        if (!t.block.isValid(e)) return null;
        const i = t.block.getType(e),
            a = {
                id: e,
                type: i,
                name: t.block.getName(e) || void 0,
                uuid: t.block.getUUID(e),
                parentId: t.block.getParent(e),
                childrenIds: t.block.getChildren(e)
            };
        try {
            if (Object.assign(a, {
                    visible: t.block.isVisible(e),
                    selected: t.block.isSelected(e),
                    clipped: t.block.isClipped(e),
                    includedInExport: t.block.isIncludedInExport(e),
                    kind: t.block.getKind(e) || void 0,
                    state: t.block.getState(e),
                    transformLocked: t.block.isTransformLocked(e)
                }), t.block.supportsOpacity(e) && (a.opacity = t.block.getOpacity(e)), t.block.supportsBlendMode(e) && (a.blendMode = t.block.getBlendMode(e)), t.block.isAllowedByScope(e, "layer/rotate") && (a.rotation = t.block.getRotation(e)), t.block.isAllowedByScope(e, "layer/flip") && (a.flipHorizontal = t.block.getFlipHorizontal(e), a.flipVertical = t.block.getFlipVertical(e)), a.position = {
                    x: t.block.getPositionX(e),
                    y: t.block.getPositionY(e),
                    modeX: t.block.getPositionXMode(e),
                    modeY: t.block.getPositionYMode(e)
                }, a.size = {
                    width: t.block.getWidth(e),
                    height: t.block.getHeight(e),
                    modeWidth: t.block.getWidthMode(e),
                    modeHeight: t.block.getHeightMode(e)
                }, a.frame = {
                    x: t.block.getFrameX(e),
                    y: t.block.getFrameY(e),
                    width: t.block.getFrameWidth(e),
                    height: t.block.getFrameHeight(e)
                }, t.block.supportsDuration(e) && (a.duration = t.block.getDuration(e)), t.block.supportsTimeOffset(e) && (a.timeOffset = t.block.getTimeOffset(e)), t.block.supportsFill(e)) {
                const i = t.block.getFill(e);
                i && (a.fill = await async function(t, e, i) {
                    if (!i || !t.block.isValid(i)) return null;
                    const a = et(t, i, ["Color", "String", "Bool", "Float", "SourceSet", "Enum"]),
                        s = a.type;
                    if (s.endsWith("/video") || s.endsWith("/audio")) try {
                        await t.block.forceLoadAVResource(i), a.avResource = {
                            duration: t.block.getAVResourceTotalDuration(i),
                            volume: t.block.getVolume(i),
                            muted: t.block.isMuted(i),
                            looping: t.block.isLooping(i)
                        }, s.endsWith("/video") && (a.avResource.width = t.block.getVideoWidth(i), a.avResource.height = t.block.getVideoHeight(i))
                    } catch (o) {
                        a.avResource = {
                            error: "Failed to load"
                        }
                    }
                    t.block.supportsCrop(e) && (a.crop = {
                        scaleX: t.block.getCropScaleX(e),
                        scaleY: t.block.getCropScaleY(e),
                        rotation: t.block.getCropRotation(e),
                        translationX: t.block.getCropTranslationX(e),
                        translationY: t.block.getCropTranslationY(e)
                    });
                    return a
                }(t, e, i))
            }
            if (t.block.supportsShape(e)) {
                const i = t.block.getShape(e);
                i && (a.shape = et(t, i, ["Bool", "Int", "Float", "String"]))
            }
            if (t.block.supportsStroke(e) && (a.stroke = {
                    enabled: t.block.isStrokeEnabled(e),
                    width: t.block.getStrokeWidth(e),
                    color: t.block.getStrokeColor(e),
                    style: t.block.getStrokeStyle(e),
                    position: t.block.getStrokePosition(e),
                    cornerGeometry: t.block.getStrokeCornerGeometry(e)
                }), t.block.supportsEffects(e) && (a.effects = t.block.getEffects(e).map((e => et(t, e, ["Bool", "Int", "Float", "String", "Color", "Enum"])))), t.block.supportsBlur(e)) {
                const i = t.block.getBlur(e);
                i && (a.blur = et(t, i, ["Float"]))
            }
            t.block.supportsAnimation(e) && (a.inAnimation = tt(t, t.block.getInAnimation(e)), a.loopAnimation = tt(t, t.block.getLoopAnimation(e)), a.outAnimation = tt(t, t.block.getOutAnimation(e))), i.endsWith("/text") && (a.text = {
                content: t.block.getString(e, "text/text")
            }), i.endsWith("/page") && (a.page = {
                duration: t.block.getDuration(e)
            }), t.block.supportsPlaceholderBehavior(e) && (a.placeholder = {
                enabled: t.block.isPlaceholderEnabled(e),
                behaviorEnabled: t.block.isPlaceholderBehaviorEnabled(e)
            });
            const s = t.editor.findAllScopes();
            a.scopes = s.reduce(((i, a) => (i[a] = t.block.isAllowedByScope(e, a), i)), {});
            const o = t.block.findAllMetadata(e);
            if (o.length > 0) {
                a.metadata = {};
                for (const i of o) a.metadata[i] = t.block.getMetadata(e, i)
            }
        } catch (s) {}
        return a
    }(t, e);
    if (!i) return null;
    const a = i.childrenIds || [],
        s = i.type;
    if (s.endsWith("/page")) {
        i.tracks = [], i.otherBlocks = [];
        const e = a.map((async e => {
            const a = await $(t, e);
            a && (t.block.getType(e).endsWith("/track") ? i.tracks.push(a) : i.otherBlocks.push(a))
        }));
        await Promise.all(e)
    } else s.endsWith("/track") ? i.blocks = a.length > 0 ? (await Promise.all(a.map((e => $(t, e))))).filter(Boolean) : [] : a.length > 0 && (i.children = (await Promise.all(a.map((e => $(t, e))))).filter((t => t && "//ly.img.ubq/camera" !== t.type)));
    return delete i.childrenIds, delete i.parentId, i
}

function tt(t, e) {
    return e && t.block.isValid(e) ? et(t, e, ["Float", "Enum"]) : null
}

function et(t, e, i) {
    if (!e || !t.block.isValid(e)) return null;
    const a = {
            id: e,
            type: t.block.getType(e),
            properties: {}
        },
        s = t.block.findAllProperties(e);
    for (const n of s) try {
        if (t.block.isPropertyReadable(n)) {
            const s = t.block.getPropertyType(n);
            if (i.includes(s)) switch (s) {
                case "Bool":
                    a.properties[n] = t.block.getBool(e, n);
                    break;
                case "Int":
                    a.properties[n] = t.block.getInt(e, n);
                    break;
                case "Float":
                case "Double":
                    a.properties[n] = t.block.getFloat(e, n);
                    break;
                case "String":
                    a.properties[n] = t.block.getString(e, n);
                    break;
                case "Color":
                    a.properties[n] = t.block.getColor(e, n);
                    break;
                case "Enum":
                    a.properties[n] = t.block.getEnum(e, n);
                    break;
                case "SourceSet":
                    a.properties[n] = t.block.getSourceSet(e, n)
            }
        }
    } catch (o) {}
    return a
}
async function it(t) {
    const e = [],
        i = t.asset.findAllSources();
    for (const s of i) {
        const i = {
            id: s,
            groups: await t.asset.getGroups(s).catch((() => [])),
            mimeTypes: t.asset.getSupportedMimeTypes(s),
            credits: t.asset.getCredits(s),
            license: t.asset.getLicense(s),
            canManageAssets: t.asset.canManageAssets(s),
            assets: []
        };
        try {
            const e = await t.asset.findAssets(s, {
                page: 0,
                perPage: 5e3
            });
            i.assets = e.assets.map((t => ({
                id: t.id,
                label: t.label,
                tags: t.tags,
                meta: t.meta,
                payload: t.payload,
                context: t.context
            })))
        } catch (a) {}
        e.push(i)
    }
    return e
}
class at {
    constructor(t, e = null) {
        if (!t) throw new Error("CreativeEngine instance is required.");
        this.engine = t, this.project = e
    }
    _validateUrl(t) {
        try {
            return new URL(t), !0
        } catch {
            return !1
        }
    }
    _generateAssetId(t, e) {
        return e || Date.now().toString()
    }
    _getProjectId() {
        if (!this.project || !this.project.id) throw new Error("No active project. Please provide a valid project object with id.");
        return this.project.id
    }
    setProject(t) {
        this.project = t
    }
    async addAsset(t = {}) {
        const {
            url: e
        } = t;
        if (!e || "string" != typeof e) throw new Error("Valid URL is required");
        if (!this._validateUrl(e)) throw new Error("Invalid URL format");
        try {
            this._getProjectId();
            const i = {
                id: this._generateAssetId(e, t.id),
                label: t.label,
                tags: t.tags || [],
                description: t.description || "",
                assetType: t.asset_type,
                addToLibrary: !0,
                ai_drive_path: t.ai_drive_path || "",
                clips: t.clips || [],
                kind: t.kind,
                file_name: t.file_name,
                duration: t.duration,
                generated_model: t.generated_model,
                content_type: t.content_type,
                resolution: t.resolution,
                transcripts: t.transcripts,
                thumbUri: t.thumb_uri,
                last_frame_url: t.last_frame_url,
                speaker_info: t.speaker_info,
                content: t.content
            };
            i.thumbUri || "audio" !== i.assetType || (i.thumbUri = "https://cdn1.genspark.ai/user-upload-image/manual/MusicalNotes.svg");
            return {
                success: !0,
                assetId: (await N.addAssetFromUrl(e, i)).assetId,
                message: "Asset added successfully"
            }
        } catch (i) {
            throw new Error(`Failed to add asset: ${i.message}`)
        }
    }
    async updateAsset(t = {}) {
        const {
            id: e,
            ...i
        } = t;
        if (!e || "string" != typeof e) throw new Error("Valid asset ID is required");
        if (0 === Object.keys(i).length) throw new Error("At least one field must be provided for update");
        try {
            const t = await U.getAsset(e);
            if (!t) throw new Error(`Asset ${e} not found`);
            const a = {};
            void 0 !== i.description && (a.description = i.description), void 0 !== i.ai_drive_path && (a.ai_drive_path = i.ai_drive_path), void 0 !== i.clips && (a.clips = i.clips), void 0 !== i.kind && (a.kind = i.kind), void 0 !== i.file_name && (a.file_name = i.file_name), void 0 !== i.duration && (a.duration = i.duration), void 0 !== i.generated_model && (a.generated_model = i.generated_model), void 0 !== i.content_type && (a.content_type = i.content_type), void 0 !== i.resolution && (a.resolution = i.resolution), void 0 !== i.transcripts && (a.transcripts = i.transcripts), void 0 !== i.thumb_uri && (a.thumbUri = i.thumb_uri), void 0 !== i.last_frame_url && (a.last_frame_url = i.last_frame_url), void 0 !== i.speaker_info && (a.speaker_info = i.speaker_info), void 0 !== i.content && (a.content = i.content), void 0 !== i.label && (a.label = { ...t.label,
                ...i.label
            }), void 0 !== i.tags && (a.tags = { ...t.tags,
                ...i.tags
            }), void 0 !== i.meta && (a.meta = { ...t.meta,
                ...i.meta
            });
            await U.updateAsset(e, a);
            return {
                success: !0,
                message: "Asset updated successfully"
            }
        } catch (a) {
            throw new Error(`Failed to update asset: ${a.message}`)
        }
    }
}
class st {
    constructor(t) {
        if (!t) throw new Error("CreativeEngine instance is required.");
        this.engine = t
    }
    _getPageId() {
        const t = this.engine.scene.getPages();
        return 0 === t.length ? null : t[0]
    }
    _findBlockByIdentifier(t, e) {
        if (!e) return null;
        const i = this.engine.block.getChildren(e);
        if (0 === i.length) return null;
        if ("selected" === t) {
            const t = this.engine.block.findAllSelected();
            return t.length > 0 && i.includes(t[0]) ? t[0] : null
        }
        if ("first" === t) return i[0];
        if ("last" === t) return i[i.length - 1];
        const a = parseInt(t, 10) - 1;
        return !isNaN(a) && a >= 0 && a < i.length ? i[a] : "number" == typeof t && i.includes(t) ? t : null
    }
    _findSelectedOverlay() {
        const t = this.engine.block.findAllSelected(),
            e = this._getPageId();
        return t.length > 0 && this.engine.block.getParent(t[0]) === e ? t[0] : null
    }
    async addClip(t = {}) {
        const {
            trackId: e,
            assetUri: i,
            assetType: a,
            timeOffset: s = 0,
            duration: o = null,
            width: n = null,
            height: l = null,
            positionX: r = null,
            positionY: c = null,
            contentFillMode: m = null,
            trimOffset: d = 0,
            trimLength: u = null,
            fillParent: g = !0
        } = t, h = this.engine.block.create("graphic");
        this.engine.block.setShape(h, this.engine.block.createShape("rect"));
        const f = this.engine.block.createFill(a),
            p = "video" === a ? "fill/video/fileURI" : "fill/image/imageFileURI";
        this.engine.block.setString(f, p, i), this.engine.block.setFill(h, f);
        let b = 5;
        if ("video" === a) try {
            await this.engine.block.forceLoadAVResource(f), b = this.engine.block.getAVResourceTotalDuration(f)
        } catch (k) {}
        const y = null !== o ? o : b;
        return this.engine.block.setDuration(h, y), s > 0 && this.engine.block.setTimeOffset(h, s), "video" === a && (d > 0 && this.engine.block.setTrimOffset(f, d), null !== u && u > 0 && this.engine.block.setTrimLength(f, u)), this.engine.block.appendChild(e, h), g ? this.engine.block.fillParent(h) : (null !== n && this.engine.block.setWidth(h, n), null !== l && this.engine.block.setHeight(h, l), null !== r && this.engine.block.setPositionX(h, r), null !== c && this.engine.block.setPositionY(h, c)), null !== m && this.engine.block.supportsContentFillMode(h) && this.engine.block.setContentFillMode(h, m), h
    }
    async insertClip(t = {}) {
        const {
            trackId: e,
            assetUri: i,
            assetType: a,
            position: s,
            referenceIdentifier: o
        } = t, n = this._findBlockByIdentifier(o, e);
        if (!n) return null;
        const l = await this.addClip({
                trackId: e,
                assetUri: i,
                assetType: a
            }),
            r = this.engine.block.getChildren(e).indexOf(n),
            c = "before" === s ? r : r + 1;
        return this.engine.block.insertChild(e, l, c), l
    }
    async trimClipStart(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            newStartTime: a
        } = t, s = this._findBlockByIdentifier(i, e);
        if (!s) return;
        const o = this.engine.block.getFill(s);
        await this.engine.block.forceLoadAVResource(o);
        const n = this.engine.block.getTrimOffset(o),
            l = this.engine.block.getDuration(s),
            r = a - n;
        r <= 0 || r >= l || (this.engine.block.setTrimOffset(o, a), this.engine.block.setDuration(s, l - r))
    }
    async trimClipEnd(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            newEndTime: a
        } = t, s = this._findBlockByIdentifier(i, e);
        if (!s) return;
        const o = this.engine.block.getFill(s);
        await this.engine.block.forceLoadAVResource(o);
        const n = a - this.engine.block.getTrimOffset(o);
        n <= 0 || this.engine.block.setDuration(s, n)
    }
    async splitClip(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            splitTime: a
        } = t, s = this._findBlockByIdentifier(i, e);
        if (!s) return null;
        const o = this.engine.block.getDuration(s);
        if (a <= 0 || a >= o) return null;
        const n = this.engine.block.getFill(s),
            l = this.engine.block.getTrimOffset(n),
            r = this.engine.block.duplicate(s, !1);
        this.engine.block.setDuration(s, a);
        const c = l + a,
            m = o - a;
        this.engine.block.setTrimOffset(this.engine.block.getFill(r), c), this.engine.block.setDuration(r, m);
        const d = this.engine.block.getChildren(e).indexOf(s);
        return this.engine.block.insertChild(e, r, d + 1), r
    }
    deleteClip(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i
        } = t, a = this._findBlockByIdentifier(i, e);
        a && this.engine.block.destroy(a)
    }
    duplicateClip(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i
        } = t, a = this._findBlockByIdentifier(i, e);
        return a ? this.engine.block.duplicate(a) : null
    }
    changeImageClipDuration(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            newDuration: a
        } = t, s = this._findBlockByIdentifier(i, e);
        s && "//ly.img.ubq/fill/image" === this.engine.block.getType(this.engine.block.getFill(s)) && this.engine.block.setDuration(s, a)
    }
    reorderClip(t = {}) {
        const {
            trackId: e,
            clipToMoveIdentifier: i,
            position: a,
            referenceIdentifier: s
        } = t, o = this._findBlockByIdentifier(i, e), n = this._findBlockByIdentifier(s, e);
        if (!o || !n || o === n) return;
        const l = this.engine.block.getChildren(e).indexOf(n),
            r = "before" === a ? l : l + 1;
        this.engine.block.insertChild(e, o, r)
    }
    createTrack(t = {}) {
        const {
            trackType: e = "overlay"
        } = t, i = this._getPageId();
        if (!i) return null;
        const a = this.engine.block.create("track");
        return this.engine.block.setName(a, `${e} Track`), this.engine.block.appendChild(i, a), a
    }
    deleteTrack(t = {}) {
        const {
            trackId: e
        } = t, i = this._getPageId();
        if (!i) return;
        this.engine.block.getChildren(i).filter((t => "//ly.img.ubq/track" === this.engine.block.getType(t))).length > 1 && this.engine.block.destroy(e)
    }
    moveClipToTrack(t = {}) {
        const {
            clipId: e,
            newTrackId: i
        } = t;
        this.engine.block.isValid(e) && this.engine.block.isValid(i) && this.engine.block.appendChild(i, e)
    }
    addTextOverlay(t = {}) {
        const {
            content: e,
            startTime: i = 0,
            duration: a = 5,
            x: s = 50,
            y: o = 50,
            width: n = null,
            height: l = null,
            color: r = {
                r: 1,
                g: 1,
                b: 1,
                a: 1
            },
            fontSize: c = 72,
            textAlign: m = null,
            lineHeight: d = null,
            letterSpacing: u = null,
            rotation: g = 0,
            opacity: h = 100,
            backgroundColor: f = null,
            padding: p = null
        } = t, b = this._getPageId();
        if (!b) return null;
        const y = this.engine.block.create("text");
        if (this.engine.block.replaceText(y, e), this.engine.block.setTimeOffset(y, i), this.engine.block.setDuration(y, a), null !== n ? (this.engine.block.setWidth(y, n), this.engine.block.setWidthMode(y, "Absolute")) : this.engine.block.setWidthMode(y, "Auto"), null !== l ? (this.engine.block.setHeight(y, l), this.engine.block.setHeightMode(y, "Absolute")) : this.engine.block.setHeightMode(y, "Auto"), this.engine.block.setPositionX(y, s), this.engine.block.setPositionY(y, o), this.engine.block.setColor(y, "fill/solid/color", r), this.engine.block.setFloat(y, "text/fontSize", c), m) {
            const t = {
                left: "Left",
                center: "Center",
                right: "Right",
                justify: "Justify"
            }[m.toLowerCase()] || m;
            this.engine.block.setEnum(y, "text/horizontalAlignment", t)
        }
        if (null !== d && this.engine.block.setFloat(y, "text/lineHeight", d), null !== u && this.engine.block.setFloat(y, "text/letterSpacing", u), 0 !== g && this.engine.block.setRotation(y, g * Math.PI / 180), 100 !== h && this.engine.block.supportsOpacity(y) && this.engine.block.setOpacity(y, Math.max(0, Math.min(1, h / 100))), f && this.engine.block.supportsBackgroundColor(y) && (this.engine.block.setBackgroundColorEnabled(y, !0), this.engine.block.setColor(y, "backgroundColor", f)), null !== p && f) {
            const t = "number" == typeof p ? p : 0;
            this.engine.block.setFloat(y, "backgroundColor/paddingTop", p.top || t), this.engine.block.setFloat(y, "backgroundColor/paddingBottom", p.bottom || t), this.engine.block.setFloat(y, "backgroundColor/paddingLeft", p.left || t), this.engine.block.setFloat(y, "backgroundColor/paddingRight", p.right || t)
        }
        return this.engine.block.appendChild(b, y), y
    }
    updateTextContent(t = {}) {
        const {
            newContent: e
        } = t, i = this._findSelectedOverlay();
        i && "//ly.img.ubq/text" === this.engine.block.getType(i) && this.engine.block.replaceText(i, e)
    }
    updateTextStyle(t = {}) {
        const {
            styles: e
        } = t, i = this._findSelectedOverlay();
        if (i && "//ly.img.ubq/text" === this.engine.block.getType(i) && (e.color && this.engine.block.setColor(i, "fill/solid/color", e.color), e.fontSize && this.engine.block.setFloat(i, "text/fontSize", e.fontSize), e.textAlign)) {
            const t = {
                left: "Left",
                center: "Center",
                right: "Right",
                justify: "Justify"
            }[e.textAlign.toLowerCase()] || e.textAlign;
            this.engine.block.setEnum(i, "text/horizontalAlignment", t)
        }
    }
    animateText(t = {}) {
        const {
            animationType: e,
            duration: i = 1
        } = t, a = this._findSelectedOverlay();
        if (!a) return null;
        const s = this.engine.block.createAnimation(e);
        return this.engine.block.setDuration(s, i), this.engine.block.setInAnimation(a, s), s
    }
    addImageOverlay(t = {}) {
        const {
            assetUri: e,
            startTime: i = 0,
            duration: a = 5,
            x: s = 100,
            y: o = 100,
            width: n = 200,
            height: l = 200,
            rotation: r = 0,
            opacity: c = 100,
            contentFillMode: m = "Crop",
            cropX: d = 0,
            cropY: u = 0,
            cropScaleX: g = 1,
            cropScaleY: h = 1,
            blendMode: f = "PassThrough"
        } = t, p = this._getPageId();
        if (!p) return null;
        const b = this.engine.block.create("graphic");
        this.engine.block.setShape(b, this.engine.block.createShape("rect"));
        const y = this.engine.block.createFill("image");
        return this.engine.block.setString(y, "fill/image/imageFileURI", e), this.engine.block.setFill(b, y), this.engine.block.setTimeOffset(b, i), this.engine.block.setDuration(b, a), this.engine.block.setWidth(b, n), this.engine.block.setHeight(b, l), this.engine.block.setPositionX(b, s), this.engine.block.setPositionY(b, o), 0 !== r && this.engine.block.setRotation(b, r * Math.PI / 180), 100 !== c && this.engine.block.supportsOpacity(b) && this.engine.block.setOpacity(b, Math.max(0, Math.min(1, c / 100))), this.engine.block.supportsContentFillMode(b) && this.engine.block.setContentFillMode(b, m), "Crop" === m && this.engine.block.supportsCrop(b) && (0 !== d && this.engine.block.setCropTranslationX(b, d), 0 !== u && this.engine.block.setCropTranslationY(b, u), 1 !== g && this.engine.block.setCropScaleX(b, g), 1 !== h && this.engine.block.setCropScaleY(b, h)), "PassThrough" !== f && this.engine.block.setBlendMode(b, f), this.engine.block.appendChild(p, b), b
    }
    addStickerOverlay(t = {}) {
        const {
            assetUri: e,
            startTime: i,
            duration: a,
            options: s = {}
        } = t, o = this.addImageOverlay({
            assetUri: e,
            startTime: i,
            duration: a,
            options: s
        });
        return o && this.engine.block.setKind(o, "sticker"), o
    }
    addShapeOverlay(t = {}) {
        const {
            shapeType: e = "rect",
            startTime: i = 0,
            duration: a = 5,
            x: s = 150,
            y: o = 150,
            width: n = 150,
            height: l = 150,
            fillType: r = "color",
            color: c = {
                r: 1,
                g: 0,
                b: 0,
                a: 1
            },
            gradientColorStops: m = null,
            strokeEnabled: d = !1,
            strokeColor: u = {
                r: 0,
                g: 0,
                b: 0,
                a: 1
            },
            strokeWidth: g = 2,
            rotation: h = 0,
            opacity: f = 100,
            blendMode: p = "PassThrough",
            cornerRadius: b = 0
        } = t, y = this._getPageId();
        if (!y) return null;
        const k = this.engine.block.create("graphic"),
            w = this.engine.block.createShape(e);
        if (this.engine.block.setShape(k, w), "gradient" === r && m) {
            const t = this.engine.block.createFill("gradient");
            if (Array.isArray(m)) {
                const e = m.map((t => ({
                    color: t.color,
                    stop: void 0 !== t.stop ? t.stop : t.offset || 0
                })));
                this.engine.block.setGradientColorStops(t, "fill/gradient/colors", e)
            }
            this.engine.block.setFill(k, t)
        } else {
            const t = this.engine.block.createFill("color");
            this.engine.block.setColor(t, "fill/color/value", c), this.engine.block.setFill(k, t)
        }
        return this.engine.block.setTimeOffset(k, i), this.engine.block.setDuration(k, a), this.engine.block.setWidth(k, n), this.engine.block.setHeight(k, l), this.engine.block.setPositionX(k, s), this.engine.block.setPositionY(k, o), d && this.engine.block.supportsStroke(k) && (this.engine.block.setStrokeEnabled(k, !0), this.engine.block.setColor(k, "stroke/color", u), this.engine.block.setFloat(k, "stroke/width", g)), 0 !== h && this.engine.block.setRotation(k, h * Math.PI / 180), 100 !== f && this.engine.block.supportsOpacity(k) && this.engine.block.setOpacity(k, Math.max(0, Math.min(1, f / 100))), "PassThrough" !== p && this.engine.block.setBlendMode(k, p), "rect" === e && b > 0 && this.engine.block.hasProperty(w, "shape/rect/cornerRadius") && this.engine.block.setFloat(w, "shape/rect/cornerRadius", b), this.engine.block.appendChild(y, k), k
    }
    updateOverlayTransform(t = {}) {
        const {
            x: e,
            y: i,
            width: a,
            height: s,
            rotation: o
        } = t, n = this._findSelectedOverlay();
        n && (void 0 !== e && this.engine.block.setPositionX(n, e), void 0 !== i && this.engine.block.setPositionY(n, i), void 0 !== a && this.engine.block.setWidth(n, a), void 0 !== s && this.engine.block.setHeight(n, s), void 0 !== o && this.engine.block.setRotation(n, o * Math.PI / 180))
    }
    setOverlayOpacity(t = {}) {
        const {
            opacity: e
        } = t, i = this._findSelectedOverlay();
        i && this.engine.block.supportsOpacity(i) && this.engine.block.setOpacity(i, Math.max(0, Math.min(1, e / 100)))
    }
    setOverlayTimeline(t = {}) {
        const {
            startTime: e,
            duration: i
        } = t, a = this._findSelectedOverlay();
        a && (void 0 !== e && this.engine.block.setTimeOffset(a, e), void 0 !== i && this.engine.block.setDuration(a, i))
    }
    groupElements(t = {}) {
        const {
            blockIds: e
        } = t;
        if (!e || !Array.isArray(e) || 0 === e.length) return null;
        const i = e.filter((t => this.engine.block.isValid(t)));
        return i.length, e.length, i.length > 0 && this.engine.block.isGroupable(i) ? this.engine.block.group(i) : null
    }
    ungroupElements(t = {}) {
        const {
            groupId: e
        } = t;
        e && this.engine.block.isValid(e) && "//ly.img.ubq/group" === this.engine.block.getType(e) && this.engine.block.ungroup(e)
    }
    alignOverlays(t = {}) {
        const {
            horizontal: e,
            vertical: i
        } = t, a = this.engine.block.findAllSelected();
        a.length < 2 || !this.engine.block.isAlignable(a) || (e && this.engine.block.alignHorizontally(a, e), i && this.engine.block.alignVertically(a, i))
    }
    distributeOverlays(t = {}) {
        const {
            direction: e
        } = t, i = this.engine.block.findAllSelected();
        i.length < 2 || !this.engine.block.isDistributable(i) || ("horizontal" === e ? this.engine.block.distributeHorizontally(i) : "vertical" === e && this.engine.block.distributeVertically(i))
    }
    changeLayerOrder(t = {}) {
        const {
            direction: e
        } = t, i = this._findSelectedOverlay();
        if (i) switch (e) {
            case "front":
                this.engine.block.bringToFront(i);
                break;
            case "back":
                this.engine.block.sendToBack(i);
                break;
            case "forward":
                this.engine.block.bringForward(i);
                break;
            case "backward":
                this.engine.block.sendBackward(i)
        }
    }
    applyFilterToClip(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            filterType: a
        } = t, s = this._findBlockByIdentifier(i, e);
        if (s) switch (this.engine.block.getEffects(s).forEach((t => this.engine.block.destroy(t))), a) {
            case "sepia":
                {
                    const t = this.engine.block.createEffect("duotone_filter");
                    return this.engine.block.setColor(t, "effect/duotone_filter/darkColor", {
                        r: .44,
                        g: .26,
                        b: .08,
                        a: 1
                    }),
                    this.engine.block.setColor(t, "effect/duotone_filter/lightColor", {
                        r: .96,
                        g: .84,
                        b: .64,
                        a: 1
                    }),
                    this.engine.block.setFloat(t, "effect/duotone_filter/intensity", .8),
                    this.engine.block.appendEffect(s, t),
                    t
                }
            case "black_and_white":
            case "grayscale":
                {
                    const t = this.engine.block.createEffect("adjustments");
                    return this.engine.block.setFloat(t, "effect/adjustments/saturation", -1),
                    this.engine.block.appendEffect(s, t),
                    t
                }
            case "vintage":
                {
                    const t = this.engine.block.createEffect("adjustments");this.engine.block.setFloat(t, "effect/adjustments/saturation", -.3),
                    this.engine.block.setFloat(t, "effect/adjustments/contrast", .1),
                    this.engine.block.setFloat(t, "effect/adjustments/brightness", .05),
                    this.engine.block.appendEffect(s, t);
                    const e = this.engine.block.createEffect("vignette");
                    return this.engine.block.setFloat(e, "effect/vignette/intensity", .5),
                    this.engine.block.appendEffect(s, e),
                    t
                }
            default:
                try {
                    const t = this.engine.block.createEffect(a);
                    return this.engine.block.appendEffect(s, t), t
                } catch (o) {
                    throw new Error(`Invalid effect type: ${a}. Valid types are: adjustments, cross_cut, dot_pattern, duotone_filter, extrude_blur, glow, green_screen, half_tone, linocut, liquid, lut_filter, mirror, outliner, pixelize, posterize, radial_pixel, recolor, sharpie, shifter, tilt_shift, tv_glitch, vignette`)
                }
        }
    }
    adjustClipBrightness(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            value: a
        } = t, s = this._findBlockByIdentifier(i, e);
        if (!s) return null;
        let o = this.engine.block.getEffects(s).find((t => "//ly.img.ubq/effect/adjustments" === this.engine.block.getType(t)));
        return o || (o = this.engine.block.createEffect("adjustments"), this.engine.block.appendEffect(s, o)), this.engine.block.setFloat(o, "effect/adjustments/brightness", a), o
    }
    adjustClipContrast(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            value: a
        } = t, s = this._findBlockByIdentifier(i, e);
        if (!s) return null;
        let o = this.engine.block.getEffects(s).find((t => "//ly.img.ubq/effect/adjustments" === this.engine.block.getType(t)));
        return o || (o = this.engine.block.createEffect("adjustments"), this.engine.block.appendEffect(s, o)), this.engine.block.setFloat(o, "effect/adjustments/contrast", a), o
    }
    adjustClipSaturation(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            value: a
        } = t, s = this._findBlockByIdentifier(i, e);
        if (!s) return null;
        let o = this.engine.block.getEffects(s).find((t => "//ly.img.ubq/effect/adjustments" === this.engine.block.getType(t)));
        return o || (o = this.engine.block.createEffect("adjustments"), this.engine.block.appendEffect(s, o)), this.engine.block.setFloat(o, "effect/adjustments/saturation", a), o
    }
    applyTransition(t = {}) {
        const {
            fromClipId: e,
            toClipId: i,
            transitionType: a = "fade",
            duration: s = .5
        } = t;
        if (!e || !i) return null;
        const o = this.engine.block.createAnimation(a);
        this.engine.block.setDuration(o, s), this.engine.block.setOutAnimation(e, o);
        const n = this.engine.block.createAnimation(a);
        return this.engine.block.setDuration(n, s), this.engine.block.setInAnimation(i, n), {
            outAnimation: o,
            inAnimation: n
        }
    }
    changeClipSpeed(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            speedFactor: a
        } = t, s = this._findBlockByIdentifier(i, e);
        if (!s || a <= 0) return;
        const o = this.engine.block.getFill(s);
        if (!o) return;
        if ("//ly.img.ubq/fill/video" !== this.engine.block.getType(o)) throw new Error("changeClipSpeed only works with video clips");
        const n = this.engine.block.getDuration(s) / a;
        if (this.engine.block.supportsTrim(o)) {
            const t = this.engine.block.getTrimLength(o);
            this.engine.block.setTrimLength(o, t / a)
        }
        this.engine.block.setDuration(s, n)
    }
    cropVideoFrame(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            cropRect: a = {}
        } = t, s = this._findBlockByIdentifier(i, e);
        s && this.engine.block.supportsCrop(s) && (void 0 !== a.x && this.engine.block.setCropTranslationX(s, a.x), void 0 !== a.y && this.engine.block.setCropTranslationY(s, a.y), void 0 !== a.scaleX && this.engine.block.setCropScaleX(s, a.scaleX), void 0 !== a.scaleY && this.engine.block.setCropScaleY(s, a.scaleY), void 0 !== a.rotation && this.engine.block.setCropRotation(s, a.rotation))
    }
    rotateVideoFrame(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            angleDegrees: a
        } = t, s = this._findBlockByIdentifier(i, e);
        s && this.engine.block.setRotation(s, a * Math.PI / 180)
    }
    flipVideoFrame(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            direction: a
        } = t, s = this._findBlockByIdentifier(i, e);
        s && ("horizontal" === a && this.engine.block.setFlipHorizontal(s, !this.engine.block.getFlipHorizontal(s)), "vertical" === a && this.engine.block.setFlipVertical(s, !this.engine.block.getFlipVertical(s)))
    }
    applyBlurToClip(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            blurAmount: a
        } = t, s = this._findBlockByIdentifier(i, e);
        if (!s) return null;
        const o = this.engine.block.createBlur("uniform");
        return this.engine.block.setFloat(o, "blur/uniform/intensity", a), this.engine.block.setBlur(s, o), o
    }
    applyKenBurnsEffect(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            animationType: a = "in"
        } = t, s = this._findBlockByIdentifier(i, e);
        if (!s) return null;
        const o = this.engine.block.createAnimation("ken_burns");
        return "out" === a ? this.engine.block.setOutAnimation(s, o) : this.engine.block.setInAnimation(s, o), o
    }
    setBlendMode(t = {}) {
        const {
            targetIdentifier: e,
            trackId: i,
            blendMode: a
        } = t, s = this._findBlockByIdentifier(e, i);
        s && this.engine.block.setBlendMode(s, a)
    }
    async addAudio(t = {}) {
        const {
            audioUri: e
        } = t, i = this._getPageId();
        if (!i) return null;
        const a = this.engine.block.create("audio");
        this.engine.block.setString(a, "audio/fileURI", e);
        try {
            await this.engine.block.forceLoadAVResource(a);
            const t = this.engine.block.getAVResourceTotalDuration(a);
            this.engine.block.setDuration(a, t)
        } catch (s) {}
        return this.engine.block.appendChild(i, a), a
    }
    setClipVolume(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i,
            volumeLevel: a
        } = t, s = !e ? this._getPageId() : e, o = this._findBlockByIdentifier(i, s);
        if (!o) return;
        if ("//ly.img.ubq/audio" !== this.engine.block.getType(o)) {
            if (this.engine.block.hasFill(o)) {
                const t = this.engine.block.getFill(o);
                "//ly.img.ubq/fill/video" === this.engine.block.getType(t) && this.engine.block.setVolume(t, Math.max(0, Math.min(1, a / 100)))
            }
        } else this.engine.block.setVolume(o, Math.max(0, Math.min(1, a / 100)))
    }
    muteAudio(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i
        } = t, a = !e ? this._getPageId() : e, s = this._findBlockByIdentifier(i, a);
        if (!s) return;
        if ("//ly.img.ubq/audio" !== this.engine.block.getType(s)) {
            if (this.engine.block.hasFill(s)) {
                const t = this.engine.block.getFill(s);
                "//ly.img.ubq/fill/video" === this.engine.block.getType(t) && this.engine.block.setMuted(t, !0)
            }
        } else this.engine.block.setMuted(s, !0)
    }
    unmuteAudio(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i
        } = t, a = !e ? this._getPageId() : e, s = this._findBlockByIdentifier(i, a);
        if (!s) return;
        if ("//ly.img.ubq/audio" !== this.engine.block.getType(s)) {
            if (this.engine.block.hasFill(s)) {
                const t = this.engine.block.getFill(s);
                "//ly.img.ubq/fill/video" === this.engine.block.getType(t) && this.engine.block.setMuted(t, !1)
            }
        } else this.engine.block.setMuted(s, !1)
    }
    async trimAudio(t = {}) {
        const {
            targetIdentifier: e,
            startTime: i,
            endTime: a
        } = t, s = this._getPageId();
        if (!s) return;
        const o = this._findBlockByIdentifier(e, s);
        if (o && "//ly.img.ubq/audio" === this.engine.block.getType(o) && (void 0 !== i && this.engine.block.setTimeOffset(o, i), void 0 !== a && void 0 !== i)) {
            const t = a - i;
            t > 0 && this.engine.block.setDuration(o, t)
        }
    }
    setAudioFadeIn(t = {}) {
        const {
            targetIdentifier: e,
            duration: i = 2
        } = t, a = this._getPageId();
        if (!a) return null;
        const s = this._findBlockByIdentifier(e, a);
        if (!s) return null;
        if ("//ly.img.ubq/audio" !== this.engine.block.getType(s)) return null;
        const o = this.engine.block.createAnimation("fade");
        return this.engine.block.setDuration(o, i), this.engine.block.setEnum(o, "animationEasing", "EaseIn"), this.engine.block.setInAnimation(s, o), o
    }
    setAudioFadeOut(t = {}) {
        const {
            targetIdentifier: e,
            duration: i = 2
        } = t, a = this._getPageId();
        if (!a) return null;
        const s = this._findBlockByIdentifier(e, a);
        if (!s) return null;
        if ("//ly.img.ubq/audio" !== this.engine.block.getType(s)) return null;
        const o = this.engine.block.createAnimation("fade");
        return this.engine.block.setDuration(o, i), this.engine.block.setEnum(o, "animationEasing", "EaseOut"), this.engine.block.setOutAnimation(s, o), o
    }
    async detachAudioFromVideo(t = {}) {
        const {
            trackId: e,
            targetIdentifier: i
        } = t, a = this._findBlockByIdentifier(i, e);
        if (!a) return null;
        if (!this.engine.block.hasFill(a)) return null;
        const s = this.engine.block.getFill(a);
        if ("//ly.img.ubq/fill/video" !== this.engine.block.getType(s)) return null;
        this.engine.block.setMuted(s, !0);
        const o = this.engine.block.getString(s, "fill/video/fileURI"),
            n = await this.addAudio({
                audioUri: o
            });
        this.engine.block.setDuration(n, this.engine.block.getDuration(a)), this.engine.block.setTimeOffset(n, this.engine.block.getTimeOffset(a));
        const l = this.engine.block.getTrimOffset(s);
        return l > 0 && this.engine.block.setTrimOffset(n, l), n
    }
    async addSubtitlesFromFile(t = {}) {
        const {
            subtitleUri: e,
            trackId: i = null,
            style: a = {}
        } = t;
        if (!e) throw new Error("Subtitle URI is required");
        const s = this._getPageId();
        if (!s) return null;
        const o = {
            fontSize: 48,
            color: {
                r: 1,
                g: 1,
                b: 1,
                a: 1
            },
            backgroundColor: null,
            dropShadowEnabled: !1,
            dropShadowColor: {
                r: 0,
                g: 0,
                b: 0,
                a: .8
            },
            textAlign: "Center",
            position: {
                x: .05,
                y: .8
            },
            size: {
                width: .9,
                height: .15
            },
            ...a
        };
        try {
            const t = await this.engine.block.createCaptionsFromURI(e);
            let a = i;
            a || (a = this.engine.block.create("captionTrack"), this.engine.block.setName(a, "Subtitle Track"), this.engine.block.appendChild(s, a));
            for (const e of t) this.engine.block.appendChild(a, e);
            return t.length > 0 && this._applyCaptionStyle(t[0], o), {
                trackId: a,
                captionIds: t
            }
        } catch (n) {
            throw n
        }
    }
    addCaption(t = {}) {
        const {
            text: e,
            startTime: i,
            duration: a = 3,
            trackId: s = null,
            style: o = {}
        } = t;
        if (!e) throw new Error("Caption text is required");
        const n = this._getPageId();
        if (!n) return null;
        const l = this.engine.block.create("caption");
        this.engine.block.setString(l, "caption/text", e), this.engine.block.setTimeOffset(l, i), this.engine.block.setDuration(l, a);
        let r = s;
        if (r || (r = this.engine.block.create("captionTrack"), this.engine.block.setName(r, "Caption Track"), this.engine.block.appendChild(n, r)), this.engine.block.appendChild(r, l), o && Object.keys(o).length > 0) {
            const t = { ...{
                    fontSize: 48,
                    color: {
                        r: 1,
                        g: 1,
                        b: 1,
                        a: 1
                    },
                    backgroundColor: null,
                    dropShadowEnabled: !1,
                    dropShadowColor: {
                        r: 0,
                        g: 0,
                        b: 0,
                        a: .8
                    },
                    textAlign: "Center",
                    position: {
                        x: .05,
                        y: .8
                    },
                    size: {
                        width: .9,
                        height: .15
                    }
                },
                ...o
            };
            this._applyCaptionStyle(l, t)
        }
        return l
    }
    updateCaptionTrackStyle(t = {}) {
        const {
            trackId: e,
            style: i
        } = t;
        if (!e || !this.engine.block.isValid(e)) throw new Error("Valid track ID is required");
        const a = this.engine.block.getChildren(e);
        a.length > 0 && this._applyCaptionStyle(a[0], i)
    }
    _applyCaptionStyle(t, e) {
        void 0 !== e.fontSize && this.engine.block.setFloat(t, "text/fontSize", e.fontSize), e.color && this.engine.block.setColor(t, "fill/solid/color", e.color), e.textAlign && this.engine.block.setEnum(t, "text/horizontalAlignment", e.textAlign), e.position && (this.engine.block.setPositionX(t, e.position.x), this.engine.block.setPositionXMode(t, "Percent"), this.engine.block.setPositionY(t, e.position.y), this.engine.block.setPositionYMode(t, "Percent")), e.size && (this.engine.block.setWidth(t, e.size.width), this.engine.block.setWidthMode(t, "Percent"), this.engine.block.setHeight(t, e.size.height), this.engine.block.setHeightMode(t, "Percent")), e.backgroundColor && (this.engine.block.setBackgroundColorEnabled(t, !0), this.engine.block.setColor(t, "backgroundColor", e.backgroundColor)), e.dropShadowEnabled && (this.engine.block.setBool(t, "dropShadow/enabled", !0), e.dropShadowColor && this.engine.block.setColor(t, "dropShadow/color", e.dropShadowColor))
    }
    createAbsoluteTrack(t = {}) {
        const {
            name: e = "Absolute Track"
        } = t, i = this._getPageId();
        if (!i) return null;
        const a = this.engine.block.create("captionTrack");
        return this.engine.block.setName(a, e), this.engine.block.appendChild(i, a), a
    }
    deleteOverlay(t = {}) {
        const {
            overlayId: e
        } = t, i = e || this._findSelectedOverlay();
        return !(!i || !this.engine.block.isValid(i)) && (this.engine.block.destroy(i), !0)
    }
    deleteMultipleBlocks(t = {}) {
        const {
            blockIds: e = []
        } = t, i = [];
        return e.forEach((t => {
            this.engine.block.isValid(t) && (this.engine.block.destroy(t), i.push(t))
        })), i
    }
    deleteSelectedBlocks(t = {}) {
        const e = this.engine.block.findAllSelected();
        return this.deleteMultipleBlocks({
            blockIds: e
        })
    }
    deleteAllClipsFromTrack(t = {}) {
        const {
            trackId: e
        } = t;
        if (!e || !this.engine.block.isValid(e)) return [];
        const i = this.engine.block.getChildren(e);
        return this.deleteMultipleBlocks({
            blockIds: i
        })
    }
    deleteAllOverlays(t = {}) {
        const e = this._getPageId();
        if (!e) return [];
        const i = this.engine.block.getChildren(e).filter((t => {
            const e = this.engine.block.getType(t);
            return "//ly.img.ubq/track" !== e && "//ly.img.ubq/audio" !== e
        }));
        return this.deleteMultipleBlocks({
            blockIds: i
        })
    }
    deleteAudio(t = {}) {
        const {
            audioIdentifier: e
        } = t, i = this._getPageId();
        if (!i) return !1;
        const a = this._findBlockByIdentifier(e, i);
        return !(!a || "//ly.img.ubq/audio" !== this.engine.block.getType(a)) && (this.engine.block.destroy(a), !0)
    }
    deleteAllAudio(t = {}) {
        const e = this._getPageId();
        if (!e) return [];
        const i = this.engine.block.getChildren(e).filter((t => "//ly.img.ubq/audio" === this.engine.block.getType(t)));
        return this.deleteMultipleBlocks({
            blockIds: i
        })
    }
    clearPage(t = {}) {
        const {
            preserveBackgroundTrack: e = !1
        } = t, i = this._getPageId();
        if (!i) return {
            deletedTracks: 0,
            deletedOverlays: 0,
            deletedAudio: 0
        };
        const a = this.engine.block.getChildren(i);
        let s = 0,
            o = 0,
            n = 0;
        return a.forEach((t => {
            const i = this.engine.block.getType(t);
            e && t === this.engine.block.getBackgroundTrack() || ("//ly.img.ubq/track" === i ? (this.deleteAllClipsFromTrack({
                trackId: t
            }), this.engine.block.destroy(t), s++) : "//ly.img.ubq/audio" === i ? (this.engine.block.destroy(t), n++) : (this.engine.block.destroy(t), o++))
        })), {
            deletedTracks: s,
            deletedOverlays: o,
            deletedAudio: n
        }
    }
    clearAllTracks(t = {}) {
        const {
            preserveBackgroundTrack: e = !1
        } = t, i = this._getPageId();
        if (!i) return [];
        const a = this.engine.block.getChildren(i).filter((t => "//ly.img.ubq/track" === this.engine.block.getType(t))).filter((t => !e || t !== this.engine.block.getBackgroundTrack())),
            s = [];
        return a.forEach((t => {
            this.deleteAllClipsFromTrack({
                trackId: t
            }), this.engine.block.destroy(t), s.push(t)
        })), s
    }
    removeAllEffects(t = {}) {
        const {
            blockId: e,
            trackId: i
        } = t, a = e || (i ? this._findBlockByIdentifier("selected", i) : this._findSelectedOverlay());
        if (!a) return [];
        const s = this.engine.block.getEffects(a);
        return s.forEach((t => this.engine.block.destroy(t))), s
    }
    removeEffect(t = {}) {
        const {
            blockId: e,
            effectIndex: i
        } = t;
        if (!e || void 0 === i) return !1;
        const a = this.engine.block.getEffects(e);
        return i >= 0 && i < a.length && (this.engine.block.removeEffect(e, i), !0)
    }
    removeAllAnimations(t = {}) {
        const {
            blockId: e
        } = t, i = e || this._findSelectedOverlay();
        if (!i || !this.engine.block.supportsAnimation(i)) return !1;
        const a = this.engine.block.getInAnimation(i),
            s = this.engine.block.getLoopAnimation(i),
            o = this.engine.block.getOutAnimation(i);
        return a && (this.engine.block.destroy(a), this.engine.block.setInAnimation(i, null)), s && (this.engine.block.destroy(s), this.engine.block.setLoopAnimation(i, null)), o && (this.engine.block.destroy(o), this.engine.block.setOutAnimation(i, null)), !0
    }
    removeBlur(t = {}) {
        const {
            blockId: e
        } = t, i = e || this._findSelectedOverlay();
        if (!i) return !1;
        const a = this.engine.block.getBlur(i);
        return !!a && (this.engine.block.destroy(a), this.engine.block.setBlur(i, null), !0)
    }
    deleteGroupKeepChildren(t = {}) {
        const {
            groupId: e
        } = t, i = e || this._findSelectedOverlay();
        return !(!i || "//ly.img.ubq/group" !== this.engine.block.getType(i)) && (this.engine.block.ungroup(i), !0)
    }
    batchDelete(t = {}) {
        const {
            clips: e = [],
            tracks: i = [],
            overlays: a = [],
            audio: s = [],
            clearAll: o = !1
        } = t, n = {
            deletedClips: [],
            deletedTracks: [],
            deletedOverlays: [],
            deletedAudio: [],
            errors: []
        };
        if (o) {
            const t = this.clearPage({
                preserveBackgroundTrack: !1
            });
            return { ...n,
                cleared: t
            }
        }
        return e.forEach((({
            trackId: t,
            clipIdentifier: e
        }) => {
            try {
                const i = this._findBlockByIdentifier(e, t);
                i && (this.engine.block.destroy(i), n.deletedClips.push(i))
            } catch (i) {
                n.errors.push({
                    type: "clip",
                    identifier: e,
                    error: i.message
                })
            }
        })), i.forEach((t => {
            try {
                this.engine.block.isValid(t) && (this.deleteTrack({
                    trackId: t
                }), n.deletedTracks.push(t))
            } catch (e) {
                n.errors.push({
                    type: "track",
                    id: t,
                    error: e.message
                })
            }
        })), a.forEach((t => {
            try {
                this.engine.block.isValid(t) && (this.engine.block.destroy(t), n.deletedOverlays.push(t))
            } catch (e) {
                n.errors.push({
                    type: "overlay",
                    id: t,
                    error: e.message
                })
            }
        })), s.forEach((t => {
            try {
                this.deleteAudio({
                    audioIdentifier: t
                }) && n.deletedAudio.push(t)
            } catch (e) {
                n.errors.push({
                    type: "audio",
                    identifier: t,
                    error: e.message
                })
            }
        })), n
    }
    async addMediaToBackgroundTrack(t = {}) {
        const {
            assetUri: e,
            assetType: i,
            timeOffset: a = 0,
            duration: s = null,
            contentFillMode: o = "Cover",
            trimOffset: n = 0,
            trimLength: l = null
        } = t, r = this._getPageId();
        if (!r) return null;
        const c = this.engine.block.create("graphic");
        this.engine.block.setShape(c, this.engine.block.createShape("rect"));
        const m = this.engine.block.createFill(i),
            d = "video" === i ? "fill/video/fileURI" : "fill/image/imageFileURI";
        this.engine.block.setString(m, d, e), this.engine.block.setFill(c, m);
        let u = 5;
        if ("video" === i) try {
            await this.engine.block.forceLoadAVResource(m), u = this.engine.block.getAVResourceTotalDuration(m)
        } catch (h) {}
        const g = null !== s ? s : u;
        return this.engine.block.setDuration(c, g), a > 0 && this.engine.block.setTimeOffset(c, a), "video" === i && (n > 0 && this.engine.block.setTrimOffset(m, n), null !== l && l > 0 && this.engine.block.setTrimLength(m, l)), this.engine.block.appendChild(r, c), this.engine.block.supportsContentFillMode(c) && this.engine.block.setContentFillMode(c, o), this.moveToBackgroundTrack({
            blockId: c
        }), this.engine.block.fillParent(c), c
    }
    moveToBackgroundTrack(t = {}) {
        const {
            blockId: e
        } = t;
        if (!e || !this.engine.block.isValid(e)) return !1;
        const i = this._getPageId();
        if (!i) return null;
        let a = this.engine.block.getBackgroundTrack();
        return a || (a = this.engine.block.create("track"), this.engine.block.appendChild(i, a), this.engine.block.setPageDurationSource(i, a)), this.engine.block.moveToBackgroundTrack(e), !0
    }
}
class ot {
    constructor(t, e = null) {
        if (!t) throw new Error("CreativeEngine instance is required.");
        this.engine = t, this.project = e
    }
    setProject(t) {
        this.project = t
    }
    _getProjectId() {
        if (!this.project || !this.project.id) throw new Error("No active project. Please provide a valid project object with id.");
        return this.project.id
    }
    _getPageId() {
        const t = this.engine.scene.getPages();
        return 0 === t.length ? null : t[0]
    }
    _validateBlockId(t) {
        if (!t) throw new Error("Block ID is required.");
        if (!this.engine.block.isValid(t)) throw new Error(`Block with ID ${t} is not valid.`);
        return !0
    }
    _createColorValue(t) {
        if ("string" == typeof t) {
            if (t.startsWith("#")) {
                const e = t.slice(1);
                return {
                    r: parseInt(e.slice(0, 2), 16) / 255,
                    g: parseInt(e.slice(2, 4), 16) / 255,
                    b: parseInt(e.slice(4, 6), 16) / 255,
                    a: 1
                }
            }
            return {
                white: {
                    r: 1,
                    g: 1,
                    b: 1,
                    a: 1
                },
                black: {
                    r: 0,
                    g: 0,
                    b: 0,
                    a: 1
                },
                red: {
                    r: 1,
                    g: 0,
                    b: 0,
                    a: 1
                },
                green: {
                    r: 0,
                    g: 1,
                    b: 0,
                    a: 1
                },
                blue: {
                    r: 0,
                    g: 0,
                    b: 1,
                    a: 1
                },
                teal: {
                    r: .1,
                    g: .4,
                    b: .7,
                    a: 1
                },
                orange: {
                    r: .9,
                    g: .6,
                    b: .3,
                    a: 1
                }
            }[t.toLowerCase()] || {
                r: 1,
                g: 1,
                b: 1,
                a: 1
            }
        }
        return t
    }
    async createGridTransitionBlock(e = {}) {
        const {
            allThumbnails: i,
            highlightedIndex: a,
            trackId: s = null,
            absoluteTrackId: o = null,
            timeOffset: n = 0,
            transitionDuration: l = 4,
            animationDuration: r = 1,
            gridSize: c = 3
        } = e;
        if (!Array.isArray(i) || 0 === i.length) throw new Error("`allThumbnails` array is required and must not be empty.");
        if ("number" != typeof a || a < 0 || a >= i.length) throw new Error("A valid `highlightedIndex` is required.");
        if (o && !this.engine.block.isValid(o)) throw new Error(`Invalid 'absoluteTrackId': ${o}`);
        if (s && !this.engine.block.isValid(s)) throw new Error(`Invalid 'trackId': ${s}`);
        if (l <= r) throw new Error("`transitionDuration` must be greater than `animationDuration`.");
        try {
            const e = this._getPageId();
            if (!e) throw new Error("No pages found in the scene.");
            const d = this.engine.block.getWidth(e),
                u = this.engine.block.getHeight(e);
            let g = o;
            g || (g = this.engine.block.create("captionTrack"), this.engine.block.setName(g, "Grid Transition Track"), this.engine.block.appendChild(e, g));
            const h = d / c,
                f = u / c,
                p = [];
            for (let t = 0; t < Math.min(i.length, c * c); t++) {
                const e = Math.floor(t / c),
                    s = t % c,
                    o = this.engine.block.create("graphic");
                this.engine.block.setShape(o, this.engine.block.createShape("rect")), this.engine.block.setPositionX(o, s * h), this.engine.block.setPositionY(o, e * f), this.engine.block.setWidth(o, h), this.engine.block.setHeight(o, f);
                const n = this.engine.block.createFill("image");
                if (this.engine.block.setString(n, "fill/image/imageFileURI", i[t]), this.engine.block.setFill(o, n), this.engine.block.setEnum(o, "contentFill/mode", "Cover"), t !== a) {
                    const t = this.engine.block.createEffect("adjustments");
                    this.engine.block.setFloat(t, "effect/adjustments/saturation", -1), this.engine.block.setFloat(t, "effect/adjustments/brightness", -.2), this.engine.block.appendEffect(o, t)
                }
                p.push(o)
            }
            const b = this.engine.block.group(p);
            this.engine.block.setName(b, "TempGridGroup"), this.engine.block.appendChild(e, b), this.engine.block.setOpacity(b, 0);
            const y = function() {
                    const e = t(!1),
                        i = J();
                    async function a(t, e, i = {}) {
                        const {
                            mimeType: a = "image/jpeg",
                            quality: s = .9,
                            targetWidth: o = 1920,
                            targetHeight: n = 1080
                        } = i;
                        try {
                            return await t.block.export(e, {
                                mimeType: a,
                                jpegQuality: s,
                                targetWidth: o,
                                targetHeight: n
                            })
                        } catch (l) {
                            throw l
                        }
                    }
                    return {
                        isProcessing: e,
                        exportGroupAsImage: a,
                        renderGroupToImage: async function(t, s, o, n = {}) {
                            const {
                                targetWidth: l = 1920,
                                targetHeight: r = 1080
                            } = n;
                            if (!t.block.isValid(s)) throw new Error(`Invalid group ID: ${s}`);
                            e.value = !0;
                            try {
                                const e = await a(t, s, {
                                        targetWidth: l,
                                        targetHeight: r
                                    }),
                                    n = `grid_render_${Date.now()}.jpg`,
                                    c = new File([e], n, {
                                        type: "image/jpeg"
                                    });
                                return (await i.addFileAsync(c, {
                                    toAiDrive: !0,
                                    metadata: {
                                        projectId: o
                                    },
                                    onProgress: t => {}
                                })).cloudUrl
                            } finally {
                                e.value = !1
                            }
                        }
                    }
                }(),
                k = this._getProjectId(),
                w = await y.renderGroupToImage(this.engine, b, k, {
                    targetWidth: d,
                    targetHeight: u
                });
            this.engine.block.ungroup(b);
            for (const t of p) this.engine.block.isValid(t) && this.engine.block.destroy(t);
            this.engine.block.isValid(b) && this.engine.block.destroy(b);
            const v = this.engine.block.create("graphic");
            if (this.engine.block.setName(v, "GridTransitionBlock"), this.engine.block.setShape(v, this.engine.block.createShape("rect")), s) this.engine.block.appendChild(s, v);
            else {
                this.engine.block.appendChild(e, v);
                new st(this.engine).moveToBackgroundTrack({
                    blockId: v
                })
            }
            this.engine.block.setPositionX(v, 0), this.engine.block.setPositionY(v, 0), this.engine.block.setWidth(v, d), this.engine.block.setHeight(v, u);
            const F = this.engine.block.createFill("image");
            this.engine.block.setString(F, "fill/image/imageFileURI", w), this.engine.block.setFill(v, F), this.engine.block.setEnum(v, "contentFill/mode", "Cover"), this.engine.block.setTimeOffset(v, n), this.engine.block.setDuration(v, l);
            const B = this.engine.block.createAnimation("fade");
            if (this.engine.block.setDuration(B, r), this.engine.block.setEnum(B, "animationEasing", "EaseOutQuint"), this.engine.block.setOutAnimation(v, B), this.project) try {
                const t = new at(this.engine, this.project);
                await t.addAsset({
                    url: w,
                    label: `Grid ${c}x${c}#H${a}`,
                    description: `Grid transition effect with ${i.length} images, highlighted index: ${a}`,
                    asset_type: "image",
                    kind: "generated",
                    file_name: `grid_${c}x${c}_transition_H${a}.jpg`,
                    content_type: "image/jpeg",
                    resolution: {
                        width: d,
                        height: u,
                        ratio: d / u
                    }
                })
            } catch (m) {}
            const I = Math.floor(a / c),
                A = a % c * h,
                S = I * f,
                C = this.engine.block.create("graphic");
            this.engine.block.setName(C, "HighlightZoomBlock"), this.engine.block.setShape(C, this.engine.block.createShape("rect")), this.engine.block.setPositionX(C, A), this.engine.block.setPositionY(C, S), this.engine.block.setWidth(C, h), this.engine.block.setHeight(C, f);
            const R = this.engine.block.createFill("image");
            this.engine.block.setString(R, "fill/image/imageFileURI", i[a]), this.engine.block.setFill(C, R), this.engine.block.setEnum(C, "contentFill/mode", "Cover"), this.engine.block.setTimeOffset(C, n + l - r), this.engine.block.setDuration(C, r);
            const j = this.engine.block.createAnimation("zoom");
            return this.engine.block.setDuration(j, r), this.engine.block.setEnum(j, "animationEasing", "EaseInQuint"), this.engine.block.setOutAnimation(C, j), this.engine.block.appendChild(g, C), {
                gridBlock: v,
                zoomBlock: C,
                absoluteTrackId: g
            }
        } catch (m) {
            throw m
        }
    }
    async applyJLCut(t = {}) {
        const {
            type: e,
            clip1: i,
            clip2: a,
            overlapSeconds: s = 1
        } = t;
        if (!e || !i || !a) throw new Error("Type, clip1, and clip2 are required for J/L-Cut.");
        const o = this._getPageId();
        if (!o) throw new Error("No pages found in the scene.");
        try {
            const t = this.engine.block.create("track");
            this.engine.block.appendChild(o, t), this.engine.block.fillParent(t);
            const n = this.engine.block.create("track");
            this.engine.block.appendChild(o, n);
            const l = await this.engine.block.addVideo(i.videoUri, 1920, 1080, {
                timeline: {
                    duration: i.duration
                }
            });
            this.engine.block.appendChild(t, l);
            const r = await this.engine.block.addAudio(i.audioUri, {
                timeline: {
                    duration: i.duration
                }
            });
            this.engine.block.appendChild(n, r);
            const c = await this.engine.block.addVideo(a.videoUri, 1920, 1080, {
                timeline: {
                    duration: a.duration
                }
            });
            this.engine.block.appendChild(t, c);
            const m = await this.engine.block.addAudio(a.audioUri, {
                timeline: {
                    duration: a.duration
                }
            });
            if (this.engine.block.appendChild(n, m), await this.engine.editor.addUndoStep(), "J-Cut" === e) {
                const t = this.engine.block.getTimeOffset(m);
                this.engine.block.setTimeOffset(m, t - s)
            } else if ("L-Cut" === e) {
                const t = this.engine.block.getDuration(r);
                this.engine.block.setDuration(r, t + s)
            }
            return {
                video1: l,
                audio1: r,
                video2: c,
                audio2: m
            }
        } catch (n) {
            throw n
        }
    }
    async applyCrossDissolve(t = {}) {
        const {
            blockId1: e,
            blockId2: i,
            transitionSeconds: a = 1
        } = t;
        this._validateBlockId(e), this._validateBlockId(i);
        try {
            const t = this.engine.block.getDuration(e),
                s = this.engine.block.getTimeOffset(e);
            this.engine.block.setTimeOffset(i, s + t - a);
            const o = this.engine.block.createAnimation("fade");
            this.engine.block.setDuration(o, a), this.engine.block.setOutAnimation(e, o);
            const n = this.engine.block.createAnimation("fade");
            return this.engine.block.setDuration(n, a), this.engine.block.setInAnimation(i, n), {
                blockId1: e,
                blockId2: i,
                transitionSeconds: a
            }
        } catch (s) {
            throw s
        }
    }
    async applyFade(t = {}) {
        const {
            blockId: e,
            type: i,
            duration: a = 1
        } = t;
        if (!i || !["in", "out"].includes(i)) throw new Error('Type must be either "in" or "out".');
        this._validateBlockId(e);
        try {
            const t = this.engine.block.createAnimation("fade");
            return this.engine.block.setDuration(t, a), "in" === i ? this.engine.block.setInAnimation(e, t) : this.engine.block.setOutAnimation(e, t), {
                blockId: e,
                type: i,
                duration: a
            }
        } catch (s) {
            throw s
        }
    }
    async applyWipeTransition(t = {}) {
        const {
            blockId1: e,
            blockId2: i,
            transitionSeconds: a = 1,
            angleDegrees: s = 90
        } = t;
        this._validateBlockId(e), this._validateBlockId(i);
        try {
            const t = this.engine.block.getDuration(e),
                o = this.engine.block.getTimeOffset(e);
            this.engine.block.setTimeOffset(i, o + t - a);
            const n = this.engine.block.createAnimation("wipe");
            this.engine.block.setDuration(n, a);
            let l = "Right";
            return s >= 45 && s < 135 ? l = "Down" : s >= 135 && s < 225 ? l = "Left" : s >= 225 && s < 315 && (l = "Up"), this.engine.block.setEnum(n, "animation/wipe/direction", l), this.engine.block.setInAnimation(i, n), this.engine.block.setDuration(e, t - a), {
                blockId1: e,
                blockId2: i,
                transitionSeconds: a,
                angleDegrees: s
            }
        } catch (o) {
            throw o
        }
    }
    async applyFlashTransition(t = {}) {
        const {
            cutTime: e = 0,
            flashColor: i = "white",
            flashDuration: a = .1
        } = t;
        if (!this._getPageId()) throw new Error("No pages found in the scene.");
        try {
            const t = this.engine.block.create("graphic"),
                s = this.engine.block.createFill("color"),
                o = this._createColorValue(i);
            return this.engine.block.setColor(s, "fill/color/value", o), this.engine.block.setFill(t, s), this.engine.block.fillParent(t), this.engine.block.setTimeOffset(t, e), this.engine.block.setDuration(t, a), this.engine.block.bringToFront(t), t
        } catch (s) {
            throw s
        }
    }
    async applyFreezeFrame(t = {}) {
        const {
            videoBlockId: e,
            freezeTime: i = 3,
            freezeDuration: a = 2
        } = t;
        this._validateBlockId(e);
        const s = this.engine.block.getParent(e);
        if (!s) throw new Error("Block has no parent page.");
        try {
            const t = await new Promise((t => {
                    this.engine.block.generateVideoThumbnailSequence(e, 1080, i, i, 1, ((e, i) => {
                        if (i instanceof ImageData) {
                            const e = document.createElement("canvas");
                            e.width = i.width, e.height = i.height, e.getContext("2d").putImageData(i, 0, 0), e.toBlob((e => t(e)), "image/png")
                        }
                    }))
                })),
                o = URL.createObjectURL(t),
                n = await this.engine.block.addImage(o, 1920, 1080),
                l = this.engine.block.getTimeOffset(e),
                r = this.engine.block.getDuration(e);
            this.engine.block.setDuration(e, i), this.engine.block.setTimeOffset(n, l + i), this.engine.block.setDuration(n, a);
            const c = this.engine.block.duplicate(e);
            this.engine.block.appendChild(s, c);
            const m = this.engine.block.getFill(c),
                d = this.engine.block.getTrimOffset(m) + i + a;
            return this.engine.block.setTrimOffset(m, d), this.engine.block.setTimeOffset(c, l + i + a), this.engine.block.setDuration(c, r - i - a), {
                videoBlockId: e,
                freezeFrameBlock: n,
                remainingVideoBlock: c
            }
        } catch (o) {
            throw o
        }
    }
    async applyPictureInPicture(t = {}) {
        const {
            backgroundUri: e,
            foregroundUri: i,
            duration: a = 10,
            scale: s = .3,
            position: o = {
                x: .65,
                y: .65
            }
        } = t;
        if (!e || !i) throw new Error("Both background and foreground URIs are required.");
        if (!this._getPageId()) throw new Error("No pages found in the scene.");
        try {
            const t = await this.engine.block.addImage(e, 1920, 1080, {
                timeline: {
                    duration: a
                }
            });
            this.engine.block.fillParent(t), this.engine.block.sendToBack(t);
            const n = await this.engine.block.addImage(i, 1920, 1080, {
                timeline: {
                    duration: a
                }
            });
            return this.engine.block.setWidth(n, 1920 * s), this.engine.block.setHeightMode(n, "Auto"), this.engine.block.setPositionX(n, 1920 * o.x), this.engine.block.setPositionY(n, 1080 * o.y), {
                background: t,
                foreground: n
            }
        } catch (n) {
            throw n
        }
    }
    async applySplitScreen(t = {}) {
        const {
            videoUriLeft: e,
            videoUriRight: i,
            duration: a = 10
        } = t;
        if (!e || !i) throw new Error("Both left and right video URIs are required.");
        if (!this._getPageId()) throw new Error("No pages found in the scene.");
        try {
            const t = await this.engine.block.addVideo(e, 960, 1080, {
                timeline: {
                    duration: a
                }
            });
            this.engine.block.setPositionX(t, 0);
            const s = await this.engine.block.addVideo(i, 960, 1080, {
                timeline: {
                    duration: a
                }
            });
            return this.engine.block.setPositionX(s, 960), {
                leftBlock: t,
                rightBlock: s
            }
        } catch (s) {
            throw s
        }
    }
    async applyDynamicSplitScreen(t = {}) {
        const {
            videoUri1: e,
            duration1: i,
            videoUri2: a,
            duration2: s,
            transitionTime: o,
            transitionDuration: n
        } = t, l = this._getPageId();
        if (!l) return;
        const r = await this.engine.block.addVideo(e, 1920, 1080, {
            timeline: {
                timeOffset: o - i,
                duration: i
            }
        });
        this.engine.block.fillParent(r), this.engine.block.appendChild(l, r);
        const c = this.engine.block.createAnimation("pan");
        this.engine.block.setDuration(c, n), this.engine.block.setFloat(c, "animation/pan/direction", Math.PI), this.engine.block.setFloat(c, "animation/pan/distance", .5), this.engine.block.setBool(c, "animation/pan/fade", !1), this.engine.block.setInAnimation(r, c);
        const m = await this.engine.block.addVideo(a, 1920, 1080, {
            timeline: {
                timeOffset: o,
                duration: s
            }
        });
        this.engine.block.fillParent(m), this.engine.block.setPositionX(m, 1920), this.engine.block.appendChild(l, m);
        const d = this.engine.block.createAnimation("pan");
        this.engine.block.setDuration(d, n), this.engine.block.setFloat(d, "animation/pan/direction", 0), this.engine.block.setFloat(d, "animation/pan/distance", .75), this.engine.block.setBool(d, "animation/pan/fade", !1), this.engine.block.setInAnimation(m, d)
    }
    async applyChromaKey(t = {}) {
        const {
            blockId: e,
            keyColor: i = "green",
            colorMatch: a = .4
        } = t;
        this._validateBlockId(e);
        try {
            const t = this.engine.block.createEffect("green_screen"),
                s = this._createColorValue(i);
            return this.engine.block.setColor(t, "effect/green_screen/fromColor", s), this.engine.block.setFloat(t, "effect/green_screen/colorMatch", a), this.engine.block.appendEffect(e, t), {
                blockId: e,
                keyColor: i,
                colorMatch: a
            }
        } catch (s) {
            throw s
        }
    }
    async applyGlitchEffect(t = {}) {
        const {
            blockId: e,
            distortion: i = 3
        } = t;
        this._validateBlockId(e);
        try {
            const t = this.engine.block.createEffect("tv_glitch");
            return this.engine.block.setFloat(t, "effect/tv_glitch/distortion", i), this.engine.block.appendEffect(e, t), {
                blockId: e,
                distortion: i
            }
        } catch (a) {
            throw a
        }
    }
    async applyLUT(t = {}) {
        const {
            blockId: e,
            lutUri: i,
            intensity: a = 1,
            horizontalTileCount: s = 5,
            verticalTileCount: o = 5
        } = t;
        if (!i) throw new Error("LUT URI is required.");
        this._validateBlockId(e);
        try {
            const t = this.engine.block.createEffect("lut_filter");
            return this.engine.block.setString(t, "effect/lut_filter/lutFileURI", i), this.engine.block.setFloat(t, "effect/lut_filter/intensity", a), this.engine.block.setInt(t, "effect/lut_filter/horizontalTileCount", s), this.engine.block.setInt(t, "effect/lut_filter/verticalTileCount", o), this.engine.block.appendEffect(e, t), {
                blockId: e,
                lutUri: i,
                intensity: a
            }
        } catch (n) {
            throw n
        }
    }
    async applyTealAndOrange(t = {}) {
        const {
            blockId: e,
            lightColor: i = "orange",
            darkColor: a = "teal"
        } = t;
        this._validateBlockId(e);
        try {
            const t = this.engine.block.createEffect("duotone_filter"),
                s = this._createColorValue(i),
                o = this._createColorValue(a);
            return this.engine.block.setColor(t, "effect/duotone_filter/lightColor", s), this.engine.block.setColor(t, "effect/duotone_filter/darkColor", o), this.engine.block.appendEffect(e, t), {
                blockId: e,
                lightColor: i,
                darkColor: a
            }
        } catch (s) {
            throw s
        }
    }
    async applyGlowEffect(t = {}) {
        const {
            blockId: e,
            amount: i = .6,
            size: a = 5
        } = t;
        this._validateBlockId(e);
        try {
            const t = this.engine.block.createEffect("glow");
            return this.engine.block.setFloat(t, "effect/glow/amount", i), this.engine.block.setFloat(t, "effect/glow/size", a), this.engine.block.appendEffect(e, t), {
                blockId: e,
                amount: i,
                size: a
            }
        } catch (s) {
            throw s
        }
    }
    async createAnimatedTitle(t = {}) {
        const {
            textContent: e,
            fontSize: i = 120,
            animationDuration: a = 1,
            position: s = {
                x: 360,
                y: 440
            }
        } = t;
        if (!e) throw new Error("Text content is required.");
        const o = this._getPageId();
        if (!o) throw new Error("No pages found in the scene.");
        try {
            const t = this.engine.block.create("text");
            this.engine.block.replaceText(t, e), this.engine.block.setWidth(t, 1200), this.engine.block.setPositionX(t, s.x), this.engine.block.setPositionY(t, s.y), this.engine.block.setFloat(t, "text/fontSize", i), this.engine.block.appendChild(o, t);
            const n = this.engine.block.createAnimation("slide");
            return this.engine.block.setDuration(n, a), this.engine.block.setFloat(n, "animation/slide/direction", 270), this.engine.block.setBool(n, "animation/slide/fade", !0), this.engine.block.setInAnimation(t, n), t
        } catch (n) {
            throw n
        }
    }
    async applyTypewriterEffect(t = {}) {
        const {
            textContent: e,
            durationSeconds: i = 3,
            fontSize: a = 50,
            position: s = {
                x: 560,
                y: 400
            }
        } = t;
        if (!e) throw new Error("Text content is required.");
        const o = this._getPageId();
        if (!o) throw new Error("No pages found in the scene.");
        try {
            const t = this.engine.block.create("text");
            this.engine.block.replaceText(t, e), this.engine.block.setWidth(t, 800), this.engine.block.setPositionX(t, s.x), this.engine.block.setPositionY(t, s.y), this.engine.block.setFloat(t, "text/fontSize", a), this.engine.block.appendChild(o, t);
            const n = this.engine.block.createAnimation("typewriter_text");
            return this.engine.block.setDuration(n, i), this.engine.block.setInAnimation(t, n), t
        } catch (n) {
            throw n
        }
    }
    applyCaptionAnimation(t = {}) {
        const {
            captionIds: e,
            animationType: i,
            animationPosition: a = "in",
            animationDuration: s = .5,
            animationEasing: o = "EaseInOut",
            animationProperties: n = {},
            textAnimationStyle: l = null,
            textAnimationOverlap: r = .35
        } = t;
        if (!e || !i) throw new Error("Caption IDs and animation type are required");
        const c = Array.isArray(e) ? e : [e],
            m = {
                inAnimations: [],
                outAnimations: []
            };
        try {
            return c.forEach((t => {
                if (this._validateBlockId(t), "in" === a || "both" === a) {
                    const e = this.engine.block.createAnimation(i);
                    this.engine.block.setDuration(e, s), this.engine.block.setEnum(e, "animationEasing", o), l && (this.engine.block.setEnum(e, "textAnimationWritingStyle", l), this.engine.block.setFloat(e, "textAnimationOverlap", r)), this._applyAnimationProperties(e, i, n), this.engine.block.setInAnimation(t, e), m.inAnimations.push(e)
                }
                if ("out" === a || "both" === a) {
                    const e = this.engine.block.createAnimation(i);
                    this.engine.block.setDuration(e, s), this.engine.block.setEnum(e, "animationEasing", o), l && (this.engine.block.setEnum(e, "textAnimationWritingStyle", l), this.engine.block.setFloat(e, "textAnimationOverlap", r)), this._applyAnimationProperties(e, i, n), this.engine.block.setOutAnimation(t, e), m.outAnimations.push(e)
                }
            })), m
        } catch (d) {
            throw d
        }
    }
    applyCaptionTypewriter(t = {}) {
        const {
            captionIds: e,
            duration: i = null,
            textStyle: a = "Character",
            overlap: s = 0
        } = t;
        return this.applyCaptionAnimation({
            captionIds: e,
            animationType: "typewriter_text",
            animationPosition: "in",
            animationDuration: i,
            textAnimationStyle: a,
            textAnimationOverlap: s
        }).inAnimations
    }
    applyCaptionSlide(t = {}) {
        const {
            captionIds: e,
            direction: i = 270,
            fade: a = !0,
            position: s = "in",
            duration: o = .5
        } = t;
        return this.applyCaptionAnimation({
            captionIds: e,
            animationType: "slide",
            animationPosition: s,
            animationDuration: o,
            animationProperties: {
                "animation/slide/direction": i,
                "animation/slide/fade": a
            }
        })
    }
    applyCaptionFade(t = {}) {
        const {
            captionIds: e,
            position: i = "both",
            duration: a = .3
        } = t;
        return this.applyCaptionAnimation({
            captionIds: e,
            animationType: "fade",
            animationPosition: i,
            animationDuration: a
        })
    }
    applyCaptionPop(t = {}) {
        const {
            captionIds: e,
            position: i = "in",
            duration: a = .4
        } = t;
        return this.applyCaptionAnimation({
            captionIds: e,
            animationType: "pop",
            animationPosition: i,
            animationDuration: a,
            animationEasing: "EaseOutBack"
        })
    }
    applyCaptionWipe(t = {}) {
        const {
            captionIds: e,
            direction: i = "Right",
            textStyle: a = "Character",
            duration: s = .6
        } = t;
        return this.applyCaptionAnimation({
            captionIds: e,
            animationType: "wipe",
            animationPosition: "in",
            animationDuration: s,
            animationProperties: {
                "animation/wipe/direction": i
            },
            textAnimationStyle: a
        }).inAnimations
    }
    applyCaptionBaseline(t = {}) {
        const {
            captionIds: e,
            direction: i = "Up",
            textStyle: a = "Word",
            duration: s = .8
        } = t;
        return this.applyCaptionAnimation({
            captionIds: e,
            animationType: "baseline",
            animationPosition: "in",
            animationDuration: s,
            animationProperties: {
                "animation/baseline/direction": i
            },
            textAnimationStyle: a
        }).inAnimations
    }
    removeCaptionAnimations(t = {}) {
        const {
            captionIds: e,
            animationType: i = "all"
        } = t;
        (Array.isArray(e) ? e : [e]).forEach((t => {
            if (this._validateBlockId(t), "in" === i || "all" === i) {
                const e = this.engine.block.getInAnimation(t);
                e && (this.engine.block.destroy(e), this.engine.block.setInAnimation(t, null))
            }
            if ("out" === i || "all" === i) {
                const e = this.engine.block.getOutAnimation(t);
                e && (this.engine.block.destroy(e), this.engine.block.setOutAnimation(t, null))
            }
        }))
    }
    _applyAnimationProperties(t, e, i) {
        Object.entries(i).forEach((([e, i]) => {
            if ("animation/slide/direction" === e && "number" == typeof i) {
                const a = i * (Math.PI / 180);
                this.engine.block.setFloat(t, e, a)
            } else "number" == typeof i ? this.engine.block.setFloat(t, e, i) : "boolean" == typeof i ? this.engine.block.setBool(t, e, i) : "string" == typeof i && this.engine.block.setEnum(t, e, i)
        }))
    }
    async layerSoundEffects(t = {}) {
        const {
            sfxList: e = []
        } = t;
        if (!Array.isArray(e) || 0 === e.length) throw new Error("Sound effects list is required and must not be empty.");
        try {
            const t = [];
            for (const i of e) {
                if (!i.uri) continue;
                const e = await this.engine.block.addAudio(i.uri, {
                    timeline: {
                        timeOffset: i.time || 0
                    }
                });
                t.push(e)
            }
            return t
        } catch (i) {
            throw i
        }
    }
    async addWhooshTransitionSfx(t = {}) {
        const {
            whooshAudioUri: e,
            transitionTime: i = 0
        } = t;
        if (!e) throw new Error("Whoosh audio URI is required.");
        try {
            return (await this.layerSoundEffects({
                sfxList: [{
                    uri: e,
                    time: i
                }]
            }))[0]
        } catch (a) {
            throw a
        }
    }
    async applyPunchIn(t = {}) {
        const {
            videoBlockId: e,
            punchInTime: i = 3,
            punchDuration: a = 1,
            zoomFactor: s = 1.5
        } = t;
        this._validateBlockId(e);
        const o = this.engine.block.getDuration(e),
            n = this.engine.block.getTimeOffset(e),
            l = this.engine.block.getParent(e);
        try {
            this.engine.block.setDuration(e, i);
            const t = this.engine.block.duplicate(e);
            this.engine.block.appendChild(l, t);
            const r = this.engine.block.getFill(t);
            this.engine.block.setTrimOffset(r, this.engine.block.getTrimOffset(r) + i), this.engine.block.setTimeOffset(t, n + i), this.engine.block.setDuration(t, a);
            const c = this.engine.block.createAnimation("zoom");
            this.engine.block.setDuration(c, .5 * a), this.engine.block.setBool(c, "animation/zoom/fade", !1), this.engine.block.setEnum(c, "animationEasing", "EaseInOut");
            const m = this.engine.block.getWidth(t),
                d = this.engine.block.getHeight(t);
            this.engine.block.setWidth(t, m * s), this.engine.block.setHeight(t, d * s);
            const u = this.engine.block.getWidth(l),
                g = this.engine.block.getHeight(l);
            this.engine.block.setPositionX(t, (u - m * s) / 2), this.engine.block.setPositionY(t, (g - d * s) / 2), this.engine.block.setInAnimation(t, c);
            const h = this.engine.block.duplicate(e);
            this.engine.block.appendChild(l, h);
            const f = this.engine.block.getFill(h);
            return this.engine.block.setTrimOffset(f, this.engine.block.getTrimOffset(f) + i + a), this.engine.block.setTimeOffset(h, n + i + a), this.engine.block.setDuration(h, o - i - a), {
                videoBlockId: e,
                punchBlock: t,
                restBlock: h
            }
        } catch (r) {
            throw r
        }
    }
    async addCreativeSubtitle(t = {}) {
        const {
            text: e,
            startTime: i = 0,
            duration: a = 3,
            style: s = {}
        } = t;
        if (!e) throw new Error("Subtitle text is required.");
        const o = this._getPageId();
        if (!o) throw new Error("No pages found in the scene.");
        const {
            fontSize: n = 80,
            backgroundColor: l = {
                r: 1,
                g: .9,
                b: 0,
                a: 1
            },
            position: r = {
                x: 210,
                y: 800
            }
        } = s;
        try {
            const t = this.engine.block.create("text");
            this.engine.block.replaceText(t, e), this.engine.block.setWidth(t, 1500), this.engine.block.setHeightMode(t, "Auto"), this.engine.block.setPositionX(t, r.x), this.engine.block.setPositionY(t, r.y), this.engine.block.setFloat(t, "text/fontSize", n), this.engine.block.setEnum(t, "text/horizontalAlignment", "Center"), this.engine.block.setBool(t, "backgroundColor/enabled", !0), this.engine.block.setColor(t, "backgroundColor/color", l), this.engine.block.setTimeOffset(t, i), this.engine.block.setDuration(t, a), this.engine.block.appendChild(o, t);
            const s = this.engine.block.createAnimation("pop");
            return this.engine.block.setDuration(s, .5), this.engine.block.setInAnimation(t, s), t
        } catch (c) {
            throw c
        }
    }
    async applyKenBurnsEffect(t = {}) {
        const {
            imageUri: e,
            duration: i = 10,
            startTime: a = 0,
            direction: s = "Right",
            zoomIntensity: o = .5,
            travelDistanceRatio: n = 1,
            fade: l = !1
        } = t;
        if (!e) throw new Error("Image URI is required.");
        const r = this._getPageId();
        if (!r) throw new Error("No pages found in the scene.");
        try {
            const t = await this.engine.block.addImage(e, 1920, 1080);
            this.engine.block.setTimeOffset(t, a), this.engine.block.setDuration(t, i);
            const c = this.engine.block.createAnimation("ken_burns");
            return this.engine.block.setDuration(c, i), this.engine.block.setEnum(c, "animation/ken_burns/direction", s), this.engine.block.setFloat(c, "animation/ken_burns/zoomIntensity", o), this.engine.block.setFloat(c, "animation/ken_burns/travelDistanceRatio", n), this.engine.block.setBool(c, "animation/ken_burns/fade", l), this.engine.block.setEnum(c, "animationEasing", "EaseOutQuint"), this.engine.block.setInAnimation(t, c), this.engine.block.appendChild(r, t), t
        } catch (c) {
            throw c
        }
    }
}
class nt {
    constructor(t) {
        if (!t) throw new Error("CreativeEngine instance is required.");
        this.engine = t
    }
    async executeCustomCode(t = {}) {
        const {
            code: e,
            explanation: i,
            warnings: a = [],
            query: s,
            safety_restricted: o = !0
        } = t;
        if (!e) throw new Error("Code is required for execution");
        try {
            const t = new Function("return " + e)();
            if ("function" != typeof t) throw new Error("Generated code is not a valid function");
            const o = await t(this.engine);
            return {
                success: !1 !== o.success,
                message: o.message || "Code executed successfully",
                details: o.details || {},
                explanation: i,
                warnings: a,
                query: s
            }
        } catch (n) {
            return {
                success: !1,
                message: "Code execution failed",
                error: n.message,
                details: {
                    code_snippet: e.substring(0, 200) + "...",
                    query: s,
                    explanation: i
                },
                warnings: a
            }
        }
    }
    _validateCode(t) {
        const e = [/engine\.asset\.add/gi, /engine\.asset\.delete/gi, /engine\.asset\.remove/gi, /fetch\(/gi, /XMLHttpRequest/gi, /eval\(/gi, /Function\(/gi, /import\s/gi, /require\(/gi, /window\./gi, /document\./gi, /process\./gi],
            i = [];
        for (const a of e) a.test(t) && i.push(`Potentially unsafe pattern detected: ${a.source}`);
        return {
            isValid: 0 === i.length,
            warnings: i
        }
    }
    _getSafeContext() {
        return {
            console: {
                log: (...t) => {},
                warn: (...t) => {},
                error: (...t) => {}
            },
            Math: Math,
            Date: Date,
            JSON: JSON,
            parseInt: parseInt,
            parseFloat: parseFloat,
            isNaN: isNaN,
            isFinite: isFinite
        }
    }
}
const lt = {
        videoAssetTools: null,
        videoEditorTools: null,
        videoEffectTools: null,
        codeExecutorTools: null,
        engine: null,
        project: null,
        contextFunc: null,
        isInitialized: !1,
        initialize(t) {
            if (this.isInitialized) return;
            const {
                engine: e,
                project: i,
                contextFunc: a
            } = t;
            if (!e) throw new Error("[RunAction] Engine is required for initialization");
            if (!a) throw new Error("[RunAction] ContextFunc is required for initialization");
            this.engine = e, this.contextFunc = a, i && (this.project = i), this._initializeTools(), this.isInitialized = !0
        },
        setContextFunc(t) {
            this.contextFunc = t
        },
        setEngine(t) {
            this.engine = t, this._initializeTools()
        },
        setProject(t) {
            this.project = t, this.videoAssetTools && this.videoAssetTools.setProject(t), this.videoEffectTools && this.videoEffectTools.setProject(t)
        },
        getInitialized() {
            return this.isInitialized
        },
        _initializeTools() {
            if (this.engine) try {
                this.videoAssetTools = new at(this.engine, this.project), this.videoEditorTools = new st(this.engine), this.videoEffectTools = new ot(this.engine, this.project), this.codeExecutorTools = new nt(this.engine)
            } catch (t) {}
        },
        _getToolInstance(t) {
            switch (t) {
                case "VideoAssetTools":
                    return this.videoAssetTools;
                case "VideoEditorTools":
                    return this.videoEditorTools;
                case "VideoEffectTools":
                    return this.videoEffectTools;
                case "CodeExecutorTools":
                    return this.codeExecutorTools;
                default:
                    return null
            }
        },
        runAction: async function(t) {
            if (!this.isInitialized) return {
                status: "error",
                error_message: "RunAction not initialized. Please call initialize() first."
            };
            let e = {
                    status: "completed"
                },
                a = {};
            try {
                if (t.instructions && Array.isArray(t.instructions))
                    for (const e of t.instructions)
                        if (e.action) {
                            const t = e.action.split(".");
                            if (3 !== t.length || "ly" !== t[0]) continue;
                            const [i, o, n] = t;
                            a[o] || (a[o] = {});
                            let l = null;
                            const r = this._getToolInstance(o);
                            if (r && "function" == typeof r[n]) try {
                                l = e.payload ? await r[n](e.payload) : await r[n]()
                            } catch (s) {
                                l = {
                                    success: !1,
                                    error: s.message
                                }
                            } else l = {
                                success: !1,
                                error: `Method ${n} not found in ${o}`
                            };
                            l && (a[o][n] || (a[o][n] = []), a[o][n].push(l))
                        }
            } catch (o) {
                e.status = "error", e.error_message = o.message
            }
            if (i.emit("runActionCompleted", e), t.callback_url) {
                e.results = a;
                let i = await this.contextFunc();
                e.client_context = {
                    video_studio: i.editorState
                };
                try {
                    await fetch(t.callback_url, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify(e)
                    })
                } catch (n) {}
            }
            return e
        }
    },
    rt = "/api/project";
class ct {
    constructor({
        fetch: t = window.fetch.bind(window)
    } = {}) {
        this.fetch = t
    }
    async saveVideoStudioData(t, e, i) {
        const a = await this.fetch(`${rt}/video_studio/save`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    project_id: t,
                    save_uuid: e,
                    data: i
                })
            }),
            s = await a.json();
        if (0 !== s.status) throw new Error(s.message || "Failed to save video studio data");
        return s.data
    }
    async getVideoStudioData(t, e) {
        var i;
        const a = await this.fetch(`${rt}/video_studio/load`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    project_id: t,
                    save_uuid: e
                })
            }),
            s = await a.json();
        if (0 !== s.status) throw new Error(s.message || "Failed to load video studio data");
        return (null == (i = s.data) ? void 0 : i.data) || null
    }
}
let mt = null;

function dt() {
    return mt || (mt = new ct), mt
}
class ut {
    constructor({
        projectApi: t = dt(),
        debounceDelay: e = 5e3,
        restoreStrategy: i = "merge"
    } = {}) {
        this.projectApi = t, this.debounceDelay = e, this.restoreStrategy = i, this.projectId = null, this.saveUuid = null, this._debouncedSaveFunction = null
    }
    async initialize(t) {
        var e;
        if (!t || !t.id) throw new Error("Project ID is required for auto-save");
        this.projectId = t.id;
        try {
            return (null == (e = null == t ? void 0 : t.session_state) ? void 0 : e.video_studio_data) ? this.saveUuid = t.session_state.video_studio_data : this.saveUuid = crypto.randomUUID(), this.saveUuid
        } catch (i) {
            throw i
        }
    }
    async loadFromCloud() {
        if (!this.projectId || !this.saveUuid) return null;
        try {
            return await this.projectApi.getVideoStudioData(this.projectId, this.saveUuid)
        } catch (t) {
            return null
        }
    }
    async saveToCloud(t = !1) {
        if (!this.projectId || !this.saveUuid) return !1;
        const e = async () => {
            try {
                const [t, e, i] = await Promise.all([U.getProject(this.projectId), U.getProjectAssets(this.projectId), U.getLatestSceneData(this.projectId)]), a = {
                    version: "1.0",
                    timestamp: (new Date).toISOString(),
                    projectId: this.projectId,
                    project: t || null,
                    assets: e || [],
                    sceneData: (null == i ? void 0 : i.sceneData) || null,
                    metadata: {
                        assetCount: (null == e ? void 0 : e.length) || 0,
                        hasSceneData: !!i,
                        hasProject: !!t
                    }
                };
                return await this.projectApi.saveVideoStudioData(this.projectId, this.saveUuid, a), !0
            } catch (t) {
                return !1
            }
        };
        return t ? await e() : (this._debouncedSaveFunction || (this._debouncedSaveFunction = y(e, this.debounceDelay)), new Promise((t => {
            this._debouncedSaveFunction(), t(!0)
        })))
    }
    async restoreFromCloud() {
        try {
            const t = await this.loadFromCloud();
            if (!t) return !1;
            const e = [];
            if (t.project) {
                await U.getProject(this.projectId) ? e.push(U.updateProject(this.projectId, t.project)) : e.push(U.createProject(t.project))
            }
            if (t.assets && t.assets.length > 0)
                if ("replace" === this.restoreStrategy) await U.deleteProjectAssets(this.projectId), e.push(...t.assets.map((t => U.saveAsset(t))));
                else {
                    const i = await U.getProjectAssets(this.projectId),
                        a = new Map(i.map((t => [t.id, t]))),
                        s = [];
                    for (const e of t.assets) {
                        const t = a.get(e.id);
                        if (t) {
                            const i = k(w(t), e, ((t, e) => {
                                if (null == e) return t
                            }));
                            s.push(i), a.delete(e.id)
                        } else s.push(e)
                    }
                    for (const [, t] of a) s.push(t);
                    e.push(...s.map((t => U.saveAsset(t))))
                }
            return t.sceneData && e.push(U.saveSceneData(this.projectId, t.sceneData)), await Promise.all(e), !0
        } catch (t) {
            return !1
        }
    }
    dispose() {
        this._debouncedSaveFunction && (this._debouncedSaveFunction.cancel(), this._debouncedSaveFunction = null), this.projectId = null, this.saveUuid = null
    }
}
let gt = null;
const ht = {
        ja: {
            "common.back": "戻る",
            "common.close": "閉じる",
            "common.done": "完了",
            "common.edit": "編集",
            "common.delete": "削除",
            "common.duplicate": "複製",
            "common.cancel": "キャンセル",
            "common.confirm": "確認",
            "common.save": "保存",
            "common.export": "エクスポート",
            "common.add": "追加",
            "common.remove": "削除",
            "common.search": "検索",
            "common.upload": "アップロード",
            "common.lock": "ロック",
            "common.unlock": "ロック解除",
            "common.placeholder": "プレースホルダー",
            "action.back": "戻る",
            "action.continue": "続ける",
            "action.editText": "テキストを編集",
            "action.undo": "元に戻す",
            "action.redo": "やり直す",
            "action.export": "エクスポート",
            "action.save": "保存",
            "action.download": "ダウンロード",
            "action.align": "整列",
            "action.align.left": "左揃え",
            "action.align.right": "右揃え",
            "action.align.top": "上揃え",
            "action.align.bottom": "下揃え",
            "action.align.horizontalCenter": "中央揃え（横）",
            "action.align.verticalCenter": "中央揃え（縦）",
            "action.align.left.description": "左に整列",
            "action.align.right.description": "右に整列",
            "action.align.top.description": "上に整列",
            "action.align.bottom.description": "下に整列",
            "action.align.horizontalCenter.description": "中央に整列（水平）",
            "action.align.verticalCenter.description": "中央に整列（垂直）",
            "action.align.elements": "要素を整列",
            "action.align.toPage": "ページに整列",
            "action.arrange": "配置",
            "action.arrange.bringForward": "前面へ",
            "action.arrange.sendBackward": "背面へ",
            "action.arrange.toFront": "最前面へ",
            "action.arrange.toBack": "最背面へ",
            "action.arrange.alwaysOnTop": "最前面に固定",
            "action.arrange.alwaysOnBottom": "最背面に固定",
            "action.arrange.moveLeft": "左へ移動",
            "action.arrange.moveRight": "右へ移動",
            "action.block.add": "要素を追加",
            "action.block.delete": "削除",
            "action.block.duplicate": "複製",
            "action.block.rename": "名前を変更",
            "action.block.move": "移動",
            "action.block.resize": "サイズ変更",
            "action.block.rotate": "回転",
            "action.block.lock": "ロック",
            "action.block.unlock": "ロック解除",
            "action.block.copy": "要素をコピー",
            "action.block.paste": "要素を貼り付け",
            "action.block.flipX": "水平に反転",
            "action.block.flipY": "垂直に反転",
            "action.block.toggleVisibility": "表示/非表示",
            "action.block.lock.description": "要素をロック",
            "action.block.unlock.description": "要素のロックを解除",
            "action.page.add": "ページを追加",
            "action.page.delete": "ページを削除",
            "action.page.duplicate": "ページを複製",
            "action.page.changeFormat": "ページ形式を変更",
            "action.pageMove.up": "上へ移動",
            "action.pageMove.down": "下へ移動",
            "action.pageMove.left": "左へ移動",
            "action.pageMove.right": "右へ移動",
            "action.image.replace": "画像を置換",
            "action.image.crop": "画像をトリミング",
            "action.image.filter": "フィルターを適用",
            "action.image.effect": "エフェクトを適用",
            "action.image.blur": "ぼかしを適用",
            "action.image.matting": "背景を削除",
            "action.image.smartCrop": "スマートクロップ",
            "action.image.smartImage": "スマート機能",
            "action.image.smartImage.description": "スマート画像機能",
            "action.image.superResolution": "超解像度",
            "action.image.inpainting": "インペインティング",
            "action.video.replace": "ビデオを置換",
            "action.video.trim": "ビデオをトリム",
            "action.audio.replace": "オーディオを置換",
            "action.audio.delete": "オーディオを削除",
            "action.audio.duplicate": "オーディオを複製",
            "action.effect.add": "エフェクトを追加",
            "action.effect.remove": "エフェクトを削除",
            "action.filter.add": "フィルターを追加",
            "action.filter.remove": "フィルターを削除",
            "action.crop.reset": "クロップをリセット",
            "action.crop.exit": "クロップモードを終了",
            "action.crop.turn": "90度反時計回りに回転",
            "action.crop.mirrorX": "水平に反転",
            "action.crop.mirrorY": "垂直に反転",
            "action.crop.turnAndMirror": "回転と反転",
            "action.crop.contentFillMode": "コンテンツフィルモードを変更",
            "action.zoom.in": "ズームイン",
            "action.zoom.out": "ズームアウト",
            "action.zoom.fitToScreen": "画面に合わせる",
            "action.zoom.reset": "リセット",
            "action.group": "グループ化",
            "action.enterGroup": "グループに入る",
            "action.ungroup": "グループ解除",
            "action.distribute": "分配",
            "action.distribute.horizontally": "水平に分配",
            "action.distribute.vertically": "垂直に分配",
            "action.distribute.horizontally.description": "水平方向に均等配置",
            "action.distribute.vertically.description": "垂直方向に均等配置",
            "action.loop.enable": "ループを有効化",
            "action.loop.disable": "ループを無効化",
            "action.mute": "ミュート",
            "action.unmute": "ミュート解除",
            "action.clip.add": "クリップを追加",
            "action.backgroundClip.add": "背景クリップを追加",
            "navigation.title": "ナビゲーション",
            "navigation.action.back": "戻る",
            "navigation.action.close": "閉じる",
            "navigation.action.export": "エクスポート",
            "navigation.action.save": "保存",
            "navigation.action.undo": "元に戻す",
            "navigation.action.redo": "やり直す",
            "navigation.action.zoom": "ズーム",
            "libraries.title": "アセットライブラリ",
            "library.images": "画像",
            "library.videos": "ビデオ",
            "library.audio": "オーディオ",
            "library.text": "テキスト",
            "library.shapes": "図形",
            "library.elements": "要素",
            "library.stickers": "ステッカー",
            "panel.inspector": "インスペクター",
            "panel.settings": "設定",
            "panel.timeline": "タイムライン",
            "panel.assetLibrary": "アセットライブラリ",
            "panel.layers": "レイヤー",
            "input.text": "テキスト",
            "input.fontSize": "フォントサイズ",
            "input.fontFamily": "フォント",
            "input.lineHeight": "行間",
            "input.letterSpacing": "文字間隔",
            "input.fontWeight": "フォントウェイト",
            "input.fontStyle": "フォントスタイル",
            "input.textAlign": "テキスト配置",
            "input.textDecoration": "テキスト装飾",
            "input.fill": "塗りつぶし",
            "input.stroke": "線",
            "input.color": "色",
            "input.opacity": "不透明度",
            "input.blendMode": "ブレンドモード",
            "input.width": "幅",
            "input.height": "高さ",
            "input.x": "X座標",
            "input.y": "Y座標",
            "input.rotation": "回転",
            "input.search": "検索...",
            "input.upload": "アップロード",
            "input.uploadDescription": "ファイルをドラッグ＆ドロップまたはクリックして選択",
            "input.cutoutOffset": "カットアウトオフセット",
            "input.cutoutSmoothing": "カットアウトスムージング",
            "input.cutoutType": "カットアウトタイプ",
            "error.loadScene.description": "シーンの読み込みに失敗しました",
            "error.upload.description": "アップロードに失敗しました",
            "error.export.description": "エクスポートに失敗しました",
            "error.network.description": "ネットワークエラーが発生しました",
            "format.image": "画像",
            "format.video": "ビデオ",
            "format.pdf": "PDF",
            "meta.currentLanguage": "日本語"
        },
        zh: {
            "common.back": "返回",
            "common.close": "关闭",
            "common.done": "完成",
            "common.edit": "编辑",
            "common.delete": "删除",
            "common.duplicate": "复制",
            "common.cancel": "取消",
            "common.confirm": "确认",
            "common.save": "保存",
            "common.export": "导出",
            "common.add": "添加",
            "common.remove": "移除",
            "common.search": "搜索",
            "common.upload": "上传",
            "common.lock": "锁定",
            "common.unlock": "解锁",
            "common.placeholder": "占位符",
            "action.back": "返回",
            "action.continue": "继续",
            "action.editText": "编辑文本",
            "action.undo": "撤销",
            "action.redo": "重做",
            "action.export": "导出",
            "action.save": "保存",
            "action.download": "下载",
            "action.align": "对齐",
            "action.align.left": "左对齐",
            "action.align.right": "右对齐",
            "action.align.top": "顶部对齐",
            "action.align.bottom": "底部对齐",
            "action.align.horizontalCenter": "水平居中",
            "action.align.verticalCenter": "垂直居中",
            "action.align.left.description": "左对齐",
            "action.align.right.description": "右对齐",
            "action.align.top.description": "顶部对齐",
            "action.align.bottom.description": "底部对齐",
            "action.align.horizontalCenter.description": "水平居中对齐",
            "action.align.verticalCenter.description": "垂直居中对齐",
            "action.align.elements": "对齐元素",
            "action.align.toPage": "对齐到页面",
            "action.arrange": "排列",
            "action.arrange.bringForward": "上移一层",
            "action.arrange.sendBackward": "下移一层",
            "action.arrange.toFront": "置于顶层",
            "action.arrange.toBack": "置于底层",
            "action.arrange.alwaysOnTop": "固定在顶层",
            "action.arrange.alwaysOnBottom": "固定在底层",
            "action.arrange.moveLeft": "向左移动",
            "action.arrange.moveRight": "向右移动",
            "action.block.add": "添加元素",
            "action.block.delete": "删除",
            "action.block.duplicate": "复制",
            "action.block.rename": "重命名",
            "action.block.move": "移动",
            "action.block.resize": "调整大小",
            "action.block.rotate": "旋转",
            "action.block.lock": "锁定",
            "action.block.unlock": "解锁",
            "action.block.copy": "复制元素",
            "action.block.paste": "粘贴元素",
            "action.block.flipX": "水平翻转",
            "action.block.flipY": "垂直翻转",
            "action.block.toggleVisibility": "显示/隐藏",
            "action.block.lock.description": "锁定元素",
            "action.block.unlock.description": "解锁元素",
            "action.page.add": "添加页面",
            "action.page.delete": "删除页面",
            "action.page.duplicate": "复制页面",
            "action.page.changeFormat": "更改页面格式",
            "action.pageMove.up": "向上移动",
            "action.pageMove.down": "向下移动",
            "action.pageMove.left": "向左移动",
            "action.pageMove.right": "向右移动",
            "action.image.replace": "替换图片",
            "action.image.crop": "裁剪图片",
            "action.image.filter": "应用滤镜",
            "action.image.effect": "应用效果",
            "action.image.blur": "应用模糊",
            "action.image.matting": "移除背景",
            "action.image.smartCrop": "智能裁剪",
            "action.image.smartImage": "智能功能",
            "action.image.smartImage.description": "智能图像功能",
            "action.image.superResolution": "超分辨率",
            "action.image.inpainting": "修补",
            "action.video.replace": "替换视频",
            "action.video.trim": "修剪视频",
            "action.audio.replace": "替换音频",
            "action.audio.delete": "删除音频",
            "action.audio.duplicate": "复制音频",
            "action.effect.add": "添加效果",
            "action.effect.remove": "移除效果",
            "action.filter.add": "添加滤镜",
            "action.filter.remove": "移除滤镜",
            "action.crop.reset": "重置裁剪",
            "action.crop.exit": "退出裁剪模式",
            "action.crop.turn": "逆时针旋转90度",
            "action.crop.mirrorX": "水平镜像",
            "action.crop.mirrorY": "垂直镜像",
            "action.crop.turnAndMirror": "旋转和镜像",
            "action.crop.contentFillMode": "更改内容填充模式",
            "action.zoom.in": "放大",
            "action.zoom.out": "缩小",
            "action.zoom.fitToScreen": "适应屏幕",
            "action.zoom.reset": "重置",
            "action.group": "组合",
            "action.enterGroup": "进入组",
            "action.ungroup": "取消组合",
            "action.distribute": "分布",
            "action.distribute.horizontally": "水平分布",
            "action.distribute.vertically": "垂直分布",
            "action.distribute.horizontally.description": "水平均匀分布",
            "action.distribute.vertically.description": "垂直均匀分布",
            "action.loop.enable": "启用循环",
            "action.loop.disable": "禁用循环",
            "action.mute": "静音",
            "action.unmute": "取消静音",
            "action.clip.add": "添加片段",
            "action.backgroundClip.add": "添加背景片段",
            "navigation.title": "导航",
            "navigation.action.back": "返回",
            "navigation.action.close": "关闭",
            "navigation.action.export": "导出",
            "navigation.action.save": "保存",
            "navigation.action.undo": "撤销",
            "navigation.action.redo": "重做",
            "navigation.action.zoom": "缩放",
            "libraries.title": "资源库",
            "library.images": "图片",
            "library.videos": "视频",
            "library.audio": "音频",
            "library.text": "文本",
            "library.shapes": "形状",
            "library.elements": "元素",
            "library.stickers": "贴纸",
            "panel.inspector": "检查器",
            "panel.settings": "设置",
            "panel.timeline": "时间轴",
            "panel.assetLibrary": "资源库",
            "panel.layers": "图层",
            "input.text": "文本",
            "input.fontSize": "字体大小",
            "input.fontFamily": "字体",
            "input.lineHeight": "行高",
            "input.letterSpacing": "字间距",
            "input.fontWeight": "字重",
            "input.fontStyle": "字体样式",
            "input.textAlign": "文本对齐",
            "input.textDecoration": "文本装饰",
            "input.fill": "填充",
            "input.stroke": "描边",
            "input.color": "颜色",
            "input.opacity": "不透明度",
            "input.blendMode": "混合模式",
            "input.width": "宽度",
            "input.height": "高度",
            "input.x": "X坐标",
            "input.y": "Y坐标",
            "input.rotation": "旋转",
            "input.search": "搜索...",
            "input.upload": "上传",
            "input.uploadDescription": "拖放文件或点击选择",
            "input.cutoutOffset": "裁切偏移",
            "input.cutoutSmoothing": "裁切平滑",
            "input.cutoutType": "裁切类型",
            "error.loadScene.description": "加载场景失败",
            "error.upload.description": "上传失败",
            "error.export.description": "导出失败",
            "error.network.description": "网络错误",
            "format.image": "图片",
            "format.video": "视频",
            "format.pdf": "PDF",
            "meta.currentLanguage": "中文"
        }
    },
    ft = {
        "zh-CN": {
            fonts: [{
                id: "noto-sans-sc",
                label: {
                    en: "Noto Sans SC",
                    zh: "思源黑体"
                },
                payload: {
                    typeface: {
                        name: "Noto Sans SC",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/notosanssc/v38/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYw.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/notosanssc/v38/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG-3FnYw.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/notosanssc/v38/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaGzjCnYw.ttf"
                        }]
                    }
                }
            }, {
                id: "noto-serif-sc",
                label: {
                    en: "Noto Serif SC",
                    zh: "思源宋体"
                },
                payload: {
                    typeface: {
                        name: "Noto Serif SC",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/notoserifsc/v33/H4cyBXePl9DZ0Xe7gG9cyOj7uK2-n-D2rd4FY7SCqyWv.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/notoserifsc/v33/H4cyBXePl9DZ0Xe7gG9cyOj7uK2-n-D2rd4FY7RlrCWv.ttf"
                        }]
                    }
                }
            }, {
                id: "zcool-kuaile",
                label: {
                    en: "ZCOOL KuaiLe",
                    zh: "站酷快乐体"
                },
                payload: {
                    typeface: {
                        name: "ZCOOL KuaiLe",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/zcoolkuaile/v19/tssqApdaRQokwFjFJjvM6h2Wpg.ttf"
                        }]
                    }
                }
            }, {
                id: "zcool-xiaowei",
                label: {
                    en: "ZCOOL XiaoWei",
                    zh: "站酷小薇体"
                },
                payload: {
                    typeface: {
                        name: "ZCOOL XiaoWei",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/zcoolxiaowei/v14/i7dMIFFrTRywPpUVX9_RJyM1YFI.ttf"
                        }]
                    }
                }
            }, {
                id: "zcool-qingke-huangyou",
                label: {
                    en: "ZCOOL QingKe HuangYou",
                    zh: "站酷庆科黄油体"
                },
                payload: {
                    typeface: {
                        name: "ZCOOL QingKe HuangYou",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/zcoolqingkehuangyou/v15/2Eb5L_R5IXJEWhD3AOhSvFC554MOOahI4mRIiw.ttf"
                        }]
                    }
                }
            }, {
                id: "zhi-mang-xing",
                label: {
                    en: "Zhi Mang Xing",
                    zh: "志莽行书"
                },
                payload: {
                    typeface: {
                        name: "Zhi Mang Xing",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/zhimangxing/v18/f0Xw0ey79sErYFtWQ9a2rq-g0ac.ttf"
                        }]
                    }
                }
            }, {
                id: "ma-shan-zheng",
                label: {
                    en: "Ma Shan Zheng",
                    zh: "马善政楷书"
                },
                payload: {
                    typeface: {
                        name: "Ma Shan Zheng",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/mashanzheng/v13/NaPecZTRCLxvwo41b4gvzkXaRMQ.ttf"
                        }]
                    }
                }
            }, {
                id: "long-cang",
                label: {
                    en: "Long Cang",
                    zh: "龙藏体"
                },
                payload: {
                    typeface: {
                        name: "Long Cang",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/longcang/v20/LYjAdGP8kkgoTec8zkRgrQ.ttf"
                        }]
                    }
                }
            }, {
                id: "liu-jian-mao-cao",
                label: {
                    en: "Liu Jian Mao Cao",
                    zh: "刘珍袖草书"
                },
                payload: {
                    typeface: {
                        name: "Liu Jian Mao Cao",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/liujianmaocao/v23/845DNN84HJrccNonurqXILGpvCOofeo.ttf"
                        }]
                    }
                }
            }, {
                id: "kirang-haerang",
                label: {
                    en: "Kirang Haerang",
                    zh: "Kirang Haerang"
                },
                payload: {
                    typeface: {
                        name: "Kirang Haerang",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/kiranghaerang/v21/E21-_dn_gvvIjhYON1lpIU4-bcqv.ttf"
                        }]
                    }
                }
            }, {
                id: "cute-font",
                label: {
                    en: "Cute Font",
                    zh: "Cute Font"
                },
                payload: {
                    typeface: {
                        name: "Cute Font",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/cutefont/v27/Noaw6Uny2oWPbSHMrY6vmA.ttf"
                        }]
                    }
                }
            }, {
                id: "mochiy-pop-p-one",
                label: {
                    en: "Mochiy Pop P One",
                    zh: "Mochiy Pop P One"
                },
                payload: {
                    typeface: {
                        name: "Mochiy Pop P One",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/mochiypoppone/v11/Ktk2AKuPeY_td1-h9LayHYWCjAqyN4M.ttf"
                        }]
                    }
                }
            }, {
                id: "mochiy-pop-one",
                label: {
                    en: "Mochiy Pop One",
                    zh: "Mochiy Pop One"
                },
                payload: {
                    typeface: {
                        name: "Mochiy Pop One",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/mochiypopone/v11/QdVPSTA9Jh-gg-5XZP2UmU4O9kww.ttf"
                        }]
                    }
                }
            }, {
                id: "potta-one",
                label: {
                    en: "Potta One",
                    zh: "Potta One"
                },
                payload: {
                    typeface: {
                        name: "Potta One",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/pottaone/v18/FeVSS05Bp6cy7xI-YfxQ3Q.ttf"
                        }]
                    }
                }
            }, {
                id: "reggae-one",
                label: {
                    en: "Reggae One",
                    zh: "Reggae One"
                },
                payload: {
                    typeface: {
                        name: "Reggae One",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/reggaeone/v18/7r3DqX5msMIkeuwJwOJt_a4.ttf"
                        }]
                    }
                }
            }, {
                id: "rocknroll-one",
                label: {
                    en: "RocknRoll One",
                    zh: "RocknRoll One"
                },
                payload: {
                    typeface: {
                        name: "RocknRoll One",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/rocknrollone/v15/kmK7ZqspGAfCeUiW6FFlmEC9guU.ttf"
                        }]
                    }
                }
            }, {
                id: "stick",
                label: {
                    en: "Stick",
                    zh: "Stick"
                },
                payload: {
                    typeface: {
                        name: "Stick",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/stick/v19/Qw3TZQpMCyTtJSvf.ttf"
                        }]
                    }
                }
            }, {
                id: "train-one",
                label: {
                    en: "Train One",
                    zh: "Train One"
                },
                payload: {
                    typeface: {
                        name: "Train One",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/trainone/v15/gyB-hwkiNtc6KnxUVjWHOg.ttf"
                        }]
                    }
                }
            }, {
                id: "new-tegomin",
                label: {
                    en: "New Tegomin",
                    zh: "New Tegomin"
                },
                payload: {
                    typeface: {
                        name: "New Tegomin",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/newtegomin/v12/SLXMc1fV7Gd9USdBAfPlqfN0.ttf"
                        }]
                    }
                }
            }, {
                id: "yusei-magic",
                label: {
                    en: "Yusei Magic",
                    zh: "Yusei Magic"
                },
                payload: {
                    typeface: {
                        name: "Yusei Magic",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/yuseimagic/v15/yYLt0hbAyuCmoo5wlhPkpjHR.ttf"
                        }]
                    }
                }
            }]
        }.fonts || [],
        "zh-TW": {
            fonts: [{
                id: "noto-serif-hk",
                label: {
                    en: "Noto Serif HK",
                    zh: "思源宋體香港"
                },
                payload: {
                    typeface: {
                        name: "Noto Serif HK",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/notoserifhk/v10/BngdUXBETWXI6LwlBZGcqL-B_KuJFcgfwP_9RMd-K2Rm.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/notoserifhk/v10/BngdUXBETWXI6LwlBZGcqL-B_KuJFcgfwP_9RMeZLGRm.ttf"
                        }]
                    }
                }
            }, {
                id: "zen-kaku-gothic-new",
                label: {
                    en: "Zen Kaku Gothic New",
                    zh: "Zen 角黑體 New"
                },
                payload: {
                    typeface: {
                        name: "Zen Kaku Gothic New",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/zenkakugothicnew/v17/gNMYW2drQpDw0GjzrVNFf_valaDBcznOkjs.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/zenkakugothicnew/v17/gNMVW2drQpDw0GjzrVNFf_valaDBcznOqs9LaWQ.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/zenkakugothicnew/v17/gNMVW2drQpDw0GjzrVNFf_valaDBcznOqodNaWQ.ttf"
                        }]
                    }
                }
            }, {
                id: "zen-maru-gothic",
                label: {
                    en: "Zen Maru Gothic",
                    zh: "Zen 圓黑體"
                },
                payload: {
                    typeface: {
                        name: "Zen Maru Gothic",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/zenmarugothic/v19/o-0SIpIxzW5b-RxT-6A8jWAtCp-k7Q.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/zenmarugothic/v19/o-0XIpIxzW5b-RxT-6A8jWAtCp-cGWtCPA.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/zenmarugothic/v19/o-0XIpIxzW5b-RxT-6A8jWAtCp-cUW1CPA.ttf"
                        }]
                    }
                }
            }, {
                id: "yuji-boku",
                label: {
                    en: "Yuji Boku",
                    zh: "佑字 墨"
                },
                payload: {
                    typeface: {
                        name: "Yuji Boku",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/yujiboku/v7/P5sAzZybeNzXsA9xj1FkjQ.ttf"
                        }]
                    }
                }
            }, {
                id: "dotgothic16",
                label: {
                    en: "DotGothic16",
                    zh: "DotGothic16"
                },
                payload: {
                    typeface: {
                        name: "DotGothic16",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/dotgothic16/v20/v6-QGYjBJFKgyw5nSoDAGE7L.ttf"
                        }]
                    }
                }
            }, {
                id: "hachi-maru-pop",
                label: {
                    en: "Hachi Maru Pop",
                    zh: "Hachi Maru Pop"
                },
                payload: {
                    typeface: {
                        name: "Hachi Maru Pop",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/hachimarupop/v22/HI_TiYoRLqpLrEiMAuO9Ysfz7rW1.ttf"
                        }]
                    }
                }
            }, {
                id: "zen-old-mincho",
                label: {
                    en: "Zen Old Mincho",
                    zh: "Zen 古明體"
                },
                payload: {
                    typeface: {
                        name: "Zen Old Mincho",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/zenoldmincho/v12/tss0ApVaYytLwxTqcxfMyBveyYb3.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/zenoldmincho/v12/tss3ApVaYytLwxTqcxfMyBveyb4Dqlla.ttf"
                        }, {
                            style: "normal",
                            weight: "semiBold",
                            subFamily: "SemiBold",
                            uri: "https://fonts.gstatic.com/s/zenoldmincho/v12/tss3ApVaYytLwxTqcxfMyBveyb4vrVla.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/zenoldmincho/v12/tss3ApVaYytLwxTqcxfMyBveyb5LrFla.ttf"
                        }, {
                            style: "normal",
                            weight: "heavy",
                            subFamily: "Heavy",
                            uri: "https://fonts.gstatic.com/s/zenoldmincho/v12/tss3ApVaYytLwxTqcxfMyBveyb5zrlla.ttf"
                        }]
                    }
                }
            }, {
                id: "rampart-one",
                label: {
                    en: "Rampart One",
                    zh: "Rampart One"
                },
                payload: {
                    typeface: {
                        name: "Rampart One",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/rampartone/v12/K2F1fZFGl_JSR1tAWNG9R6qg.ttf"
                        }]
                    }
                }
            }, {
                id: "yomogi",
                label: {
                    en: "Yomogi",
                    zh: "Yomogi 體"
                },
                payload: {
                    typeface: {
                        name: "Yomogi",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/yomogi/v12/VuJwdNrS2ZL7rpoPWA.ttf"
                        }]
                    }
                }
            }, {
                id: "biz-udgothic",
                label: {
                    en: "BIZ UDGothic",
                    zh: "BIZ UD黑體"
                },
                payload: {
                    typeface: {
                        name: "BIZ UDGothic",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/bizudgothic/v12/daafSTouBF7RUjnbt8p3LuKttQ.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/bizudgothic/v12/daaASTouBF7RUjnbt8p3LuKVCSxZ-w.ttf"
                        }]
                    }
                }
            }, {
                id: "biz-udmincho",
                label: {
                    en: "BIZ UDMincho",
                    zh: "BIZ UD明朝"
                },
                payload: {
                    typeface: {
                        name: "BIZ UDMincho",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/bizudmincho/v11/EJRRQgI6eOxFjBdKs38yhtW1dw.ttf"
                        }]
                    }
                }
            }, {
                id: "chiron-hei-hk",
                label: {
                    en: "Chiron Hei HK",
                    zh: "昭源黑體"
                },
                payload: {
                    typeface: {
                        name: "Chiron Hei HK",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/chironheihk/v1/wXK-E3MSr44vpVKPvzqVJaxhp3w7QQhPNY163lJtr18M.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/chironheihk/v1/wXK-E3MSr44vpVKPvzqVJaxhp3w7QQhPNY163lJfr18M.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/chironheihk/v1/wXK-E3MSr44vpVKPvzqVJaxhp3w7QQhPNY163lKKqF8M.ttf"
                        }]
                    }
                }
            }, {
                id: "iansui",
                label: {
                    en: "Iansui",
                    zh: "芫荽體"
                },
                payload: {
                    typeface: {
                        name: "Iansui",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/iansui/v7/w8gbH2UoTuUp5bOajQ.ttf"
                        }]
                    }
                }
            }]
        }.fonts || [],
        "ja-JP": {
            fonts: [{
                id: "noto-sans-jp",
                label: {
                    en: "Noto Sans JP",
                    ja: "Noto Sans 日本語版"
                },
                payload: {
                    typeface: {
                        name: "Noto Sans JP",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/notosansjp/v54/-F6jfjtqLzI2JPCgQBnw7HFyzSD-AsregP8VFBEj75s.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/notosansjp/v54/-F6jfjtqLzI2JPCgQBnw7HFyzSD-AsregP8VFCMj75s.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/notosansjp/v54/-F6jfjtqLzI2JPCgQBnw7HFyzSD-AsregP8VFPYk75s.ttf"
                        }]
                    }
                }
            }, {
                id: "noto-serif-jp",
                label: {
                    en: "Noto Serif JP",
                    ja: "Noto Serif 日本語版"
                },
                payload: {
                    typeface: {
                        name: "Noto Serif JP",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/notoserifjp/v31/xn71YHs72GKoTvER4Gn3b5eMRtWGkp6o7MjQ2bwxOubA.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/notoserifjp/v31/xn71YHs72GKoTvER4Gn3b5eMRtWGkp6o7MjQ2bzWPebA.ttf"
                        }]
                    }
                }
            }, {
                id: "biz-udpgothic",
                label: {
                    en: "BIZ UDPGothic",
                    ja: "BIZ UDPゴシック"
                },
                payload: {
                    typeface: {
                        name: "BIZ UDPGothic",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/bizudpgothic/v14/hES36X5pHAIBjmS84VL0Bue83nU.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/bizudpgothic/v14/hESq6X5pHAIBjmS84VL0Bue85skjZWE.ttf"
                        }]
                    }
                }
            }, {
                id: "biz-udmincho",
                label: {
                    en: "BIZ UDMincho",
                    ja: "BIZ UD明朝"
                },
                payload: {
                    typeface: {
                        name: "BIZ UDMincho",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/bizudmincho/v11/EJRRQgI6eOxFjBdKs38yhtW1dw.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/bizudmincho/v11/EJROQgI6eOxFjBdKs38yhtWNyyvfpQ.ttf"
                        }]
                    }
                }
            }, {
                id: "m-plus-1",
                label: {
                    en: "M PLUS 1",
                    ja: "M PLUS 1"
                },
                payload: {
                    typeface: {
                        name: "M PLUS 1",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/mplus1/v14/R70EjygA28ymD4HgBUGzkN5Eyoj-WpW5VSa78g.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/mplus1/v14/R70EjygA28ymD4HgBUGzkN5Eyoj-WpW5Zya78g.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/mplus1/v14/R70EjygA28ymD4HgBUGzkN5Eyoj-WpW5siG78g.ttf"
                        }]
                    }
                }
            }, {
                id: "m-plus-rounded-1c",
                label: {
                    en: "M PLUS Rounded 1c",
                    ja: "M PLUS Rounded 1c"
                },
                payload: {
                    typeface: {
                        name: "M PLUS Rounded 1c",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/mplusrounded1c/v19/VdGEAYIAV6gnpUpoWwNkYvrugw9RuPWG.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/mplusrounded1c/v19/VdGBAYIAV6gnpUpoWwNkYvrugw9RuM1y55sK.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/mplusrounded1c/v19/VdGBAYIAV6gnpUpoWwNkYvrugw9RuM064ZsK.ttf"
                        }]
                    }
                }
            }, {
                id: "kosugi",
                label: {
                    en: "Kosugi",
                    ja: "小杉"
                },
                payload: {
                    typeface: {
                        name: "Kosugi",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/kosugi/v18/pxiFyp4_v8FCjlI4NA.ttf"
                        }]
                    }
                }
            }, {
                id: "kosugi-maru",
                label: {
                    en: "Kosugi Maru",
                    ja: "小杉丸ゴシック"
                },
                payload: {
                    typeface: {
                        name: "Kosugi Maru",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/kosugimaru/v16/0nksC9PgP_wGh21A2KeqGiTq.ttf"
                        }]
                    }
                }
            }, {
                id: "sawarabi-mincho",
                label: {
                    en: "Sawarabi Mincho",
                    ja: "さわらび明朝"
                },
                payload: {
                    typeface: {
                        name: "Sawarabi Mincho",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/sawarabimincho/v19/8QIRdiDaitzr7brc8ahpxt6GcIJTLQ.ttf"
                        }]
                    }
                }
            }, {
                id: "sawarabi-gothic",
                label: {
                    en: "Sawarabi Gothic",
                    ja: "さわらびゴシック"
                },
                payload: {
                    typeface: {
                        name: "Sawarabi Gothic",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/sawarabigothic/v16/x3d4ckfVaqqa-BEj-I9mE65u3k3NBQ.ttf"
                        }]
                    }
                }
            }, {
                id: "zen-kurenaido",
                label: {
                    en: "Zen Kurenaido",
                    ja: "Zen紅"
                },
                payload: {
                    typeface: {
                        name: "Zen Kurenaido",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/zenkurenaido/v18/3XFsEr0515BK2u6UUptu_gWJZfw.ttf"
                        }]
                    }
                }
            }, {
                id: "zen-antique-soft",
                label: {
                    en: "Zen Antique Soft",
                    ja: "Zen古風ソフト"
                },
                payload: {
                    typeface: {
                        name: "Zen Antique Soft",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/zenantiquesoft/v13/DtV4JwqzSL1q_KwnEWMc_3xfgW6ihwA.ttf"
                        }]
                    }
                }
            }, {
                id: "stick",
                label: {
                    en: "Stick",
                    ja: "スティック"
                },
                payload: {
                    typeface: {
                        name: "Stick",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/stick/v19/Qw3TZQpMCyTtJSvf.ttf"
                        }]
                    }
                }
            }, {
                id: "rock-3d",
                label: {
                    en: "Rock 3D",
                    ja: "ロック3D"
                },
                payload: {
                    typeface: {
                        name: "Rock 3D",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/rock3d/v12/yYLp0hrL0PCo651513Q.ttf"
                        }]
                    }
                }
            }, {
                id: "reggae-one",
                label: {
                    en: "Reggae One",
                    ja: "レゲエ One"
                },
                payload: {
                    typeface: {
                        name: "Reggae One",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/reggaeone/v18/7r3DqX5msMIkeuwJwOJt_a4.ttf"
                        }]
                    }
                }
            }, {
                id: "rocknroll-one",
                label: {
                    en: "RocknRoll One",
                    ja: "ロックンロール One"
                },
                payload: {
                    typeface: {
                        name: "RocknRoll One",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/rocknrollone/v15/kmK7ZqspGAfCeUiW6FFlmEC9guU.ttf"
                        }]
                    }
                }
            }, {
                id: "potta-one",
                label: {
                    en: "Potta One",
                    ja: "ポッタ One"
                },
                payload: {
                    typeface: {
                        name: "Potta One",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/pottaone/v18/FeVSS05Bp6cy7xI-YfxQ3Q.ttf"
                        }]
                    }
                }
            }, {
                id: "new-tegomin",
                label: {
                    en: "New Tegomin",
                    ja: "ニューテゴミン"
                },
                payload: {
                    typeface: {
                        name: "New Tegomin",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/newtegomin/v12/SLXMc1fV7Gd9USdBAfPlqfN0.ttf"
                        }]
                    }
                }
            }, {
                id: "murecho",
                label: {
                    en: "Murecho",
                    ja: "ムレチョ"
                },
                payload: {
                    typeface: {
                        name: "Murecho",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/murecho/v16/q5uYsoq3NOBn_I-ggCJg98TBOoNFCMpr5XWZ.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/murecho/v16/q5uYsoq3NOBn_I-ggCJg98TBOoNFCMpZ5XWZ.ttf"
                        }, {
                            style: "normal",
                            weight: "semiBold",
                            subFamily: "SemiBold",
                            uri: "https://fonts.gstatic.com/s/murecho/v16/q5uYsoq3NOBn_I-ggCJg98TBOoNFCMq14nWZ.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/murecho/v16/q5uYsoq3NOBn_I-ggCJg98TBOoNFCMqM4nWZ.ttf"
                        }, {
                            style: "normal",
                            weight: "extraBold",
                            subFamily: "ExtraBold",
                            uri: "https://fonts.gstatic.com/s/murecho/v16/q5uYsoq3NOBn_I-ggCJg98TBOoNFCMrr4nWZ.ttf"
                        }, {
                            style: "normal",
                            weight: "heavy",
                            subFamily: "Heavy",
                            uri: "https://fonts.gstatic.com/s/murecho/v16/q5uYsoq3NOBn_I-ggCJg98TBOoNFCMrC4nWZ.ttf"
                        }]
                    }
                }
            }, {
                id: "m-plus-1-code",
                label: {
                    en: "M PLUS 1 Code",
                    ja: "M PLUS 1 Code"
                },
                payload: {
                    typeface: {
                        name: "M PLUS 1 Code",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/mplus1code/v15/ypvMbXOOx2xFpzmYJS3N2_J2hBN6RZ5oIp8m_7iN0HHp.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/mplus1code/v15/ypvMbXOOx2xFpzmYJS3N2_J2hBN6RZ5oIp8m_7i_0HHp.ttf"
                        }, {
                            style: "normal",
                            weight: "semiBold",
                            subFamily: "SemiBold",
                            uri: "https://fonts.gstatic.com/s/mplus1code/v15/ypvMbXOOx2xFpzmYJS3N2_J2hBN6RZ5oIp8m_7hT13Hp.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/mplus1code/v15/ypvMbXOOx2xFpzmYJS3N2_J2hBN6RZ5oIp8m_7hq13Hp.ttf"
                        }]
                    }
                }
            }]
        }.fonts || [],
        "en-US": {
            fonts: [{
                id: "inter",
                label: {
                    en: "Inter"
                },
                payload: {
                    typeface: {
                        name: "Inter",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/inter/v19/UcCO3FwrK3iLTeHuS_nVMrMxCp50SjIw2boKoduKmMEVuLyfMZg.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/inter/v19/UcCO3FwrK3iLTeHuS_nVMrMxCp50SjIw2boKoduKmMEVuI6fMZg.ttf"
                        }, {
                            style: "normal",
                            weight: "semiBold",
                            subFamily: "SemiBold",
                            uri: "https://fonts.gstatic.com/s/inter/v19/UcCO3FwrK3iLTeHuS_nVMrMxCp50SjIw2boKoduKmMEVuGKYMZg.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/inter/v19/UcCO3FwrK3iLTeHuS_nVMrMxCp50SjIw2boKoduKmMEVuFuYMZg.ttf"
                        }]
                    }
                }
            }, {
                id: "lato",
                label: {
                    en: "Lato"
                },
                payload: {
                    typeface: {
                        name: "Lato",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/lato/v24/S6uyw4BMUTPHvxk.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/lato/v24/S6u9w4BMUTPHh6UVew8.ttf"
                        }]
                    }
                }
            }, {
                id: "raleway",
                label: {
                    en: "Raleway"
                },
                payload: {
                    typeface: {
                        name: "Raleway",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/raleway/v36/1Ptxg8zYS_SKggPN4iEgvnHyvveLxVvaooCP.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/raleway/v36/1Ptxg8zYS_SKggPN4iEgvnHyvveLxVvoooCP.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/raleway/v36/1Ptxg8zYS_SKggPN4iEgvnHyvveLxVs9pYCP.ttf"
                        }]
                    }
                }
            }, {
                id: "merriweather",
                label: {
                    en: "Merriweather"
                },
                payload: {
                    typeface: {
                        name: "Merriweather",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icqEw.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDrOSAqEw.ttf"
                        }]
                    }
                }
            }, {
                id: "ubuntu",
                label: {
                    en: "Ubuntu"
                },
                payload: {
                    typeface: {
                        name: "Ubuntu",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/ubuntu/v20/4iCs6KVjbNBYlgo6eA.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCjC3Ttw.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/ubuntu/v20/4iCv6KVjbNBYlgoCxCvTtw.ttf"
                        }]
                    }
                }
            }, {
                id: "rubik",
                label: {
                    en: "Rubik"
                },
                payload: {
                    typeface: {
                        name: "Rubik",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4i1UA.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-NYi1UA.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-4I-1UA.ttf"
                        }]
                    }
                }
            }, {
                id: "work-sans",
                label: {
                    en: "Work Sans"
                },
                payload: {
                    typeface: {
                        name: "Work Sans",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K0nXNig.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K3vXNig.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K67QNig.ttf"
                        }]
                    }
                }
            }, {
                id: "barlow",
                label: {
                    en: "Barlow"
                },
                payload: {
                    typeface: {
                        name: "Barlow",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/barlow/v12/7cHpv4kjgoGqM7EPCw.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E3_-gc4A.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E3t-4c4A.ttf"
                        }]
                    }
                }
            }, {
                id: "mukta",
                label: {
                    en: "Mukta"
                },
                payload: {
                    typeface: {
                        name: "Mukta",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/mukta/v16/iJWKBXyXfDDVXYnG.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/mukta/v16/iJWHBXyXfDDVXbF6iFma.ttf"
                        }]
                    }
                }
            }, {
                id: "crimson-text",
                label: {
                    en: "Crimson Text"
                },
                payload: {
                    typeface: {
                        name: "Crimson Text",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/crimsontext/v19/wlp2gwHKFkZgtmSR3NB0oRJvaA.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/crimsontext/v19/wlppgwHKFkZgtmSR3NB0oRJX1C12Cw.ttf"
                        }]
                    }
                }
            }, {
                id: "libre-franklin",
                label: {
                    en: "Libre Franklin"
                },
                payload: {
                    typeface: {
                        name: "Libre Franklin",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhLsWUBw.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhHMWUBw.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhycKUBw.ttf"
                        }]
                    }
                }
            }, {
                id: "bitter",
                label: {
                    en: "Bitter"
                },
                payload: {
                    typeface: {
                        name: "Bitter",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfCL8.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8RHYCL8.ttf"
                        }]
                    }
                }
            }, {
                id: "libre-baskerville",
                label: {
                    en: "Libre Baskerville"
                },
                payload: {
                    typeface: {
                        name: "Libre Baskerville",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/librebaskerville/v16/kmKnZrc3Hgbbcjq75U4uslyuy4kn0pNe.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/librebaskerville/v16/kmKiZrc3Hgbbcjq75U4uslyuy4kn0qviTjYw.ttf"
                        }]
                    }
                }
            }, {
                id: "vollkorn",
                label: {
                    en: "Vollkorn"
                },
                payload: {
                    typeface: {
                        name: "Vollkorn",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df2MHGuGQ.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df213auGQ.ttf"
                        }]
                    }
                }
            }, {
                id: "gentium-book-plus",
                label: {
                    en: "Gentium Book Plus"
                },
                payload: {
                    typeface: {
                        name: "Gentium Book Plus",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/gentiumbookplus/v1/vEFL2-RHBgUK5fbjKxRpbBtJPyRpofKf.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/gentiumbookplus/v1/vEFO2-RHBgUK5fbjKxRpbBtJPyRpocojWpbG.ttf"
                        }]
                    }
                }
            }, {
                id: "pt-sans",
                label: {
                    en: "PT Sans"
                },
                payload: {
                    typeface: {
                        name: "PT Sans",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/ptsans/v17/jizaRExUiTo99u79P0U.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/ptsans/v17/jizfRExUiTo99u79B_mh4Ok.ttf"
                        }]
                    }
                }
            }, {
                id: "droid-sans",
                label: {
                    en: "Droid Sans"
                },
                payload: {
                    typeface: {
                        name: "Droid Sans",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/droidsans/v18/SlGVmQWMvZQIdix7AFxXoHQ.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/droidsans/v18/SlGWmQWMvZQIdix7AFxXmMh3SDw.ttf"
                        }]
                    }
                }
            }, {
                id: "josefin-sans",
                label: {
                    en: "Josefin Sans"
                },
                payload: {
                    typeface: {
                        name: "Josefin Sans",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/josefinsans/v33/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_DjQXME.ttf"
                        }, {
                            style: "normal",
                            weight: "semiBold",
                            subFamily: "SemiBold",
                            uri: "https://fonts.gstatic.com/s/josefinsans/v33/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_ObXXME.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/josefinsans/v33/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_N_XXME.ttf"
                        }]
                    }
                }
            }, {
                id: "cabin",
                label: {
                    en: "Cabin"
                },
                payload: {
                    typeface: {
                        name: "Cabin",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EL7E.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkW-EL7E.ttf"
                        }, {
                            style: "normal",
                            weight: "semiBold",
                            subFamily: "SemiBold",
                            uri: "https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkYODL7E.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkbqDL7E.ttf"
                        }]
                    }
                }
            }, {
                id: "karla",
                label: {
                    en: "Karla"
                },
                payload: {
                    typeface: {
                        name: "Karla",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/karla/v32/qkBIXvYC6trAT55ZBi1ueQVIjQTD-JqqFA.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/karla/v32/qkBIXvYC6trAT55ZBi1ueQVIjQTDH52qFA.ttf"
                        }, {
                            style: "normal",
                            weight: "extraBold",
                            subFamily: "ExtraBold",
                            uri: "https://fonts.gstatic.com/s/karla/v32/qkBIXvYC6trAT55ZBi1ueQVIjQTDeJ2qFA.ttf"
                        }]
                    }
                }
            }]
        }.fonts || [],
        "ko-KR": {
            fonts: [{
                id: "noto-sans-kr",
                label: {
                    en: "Noto Sans KR",
                    ko: "본고딕 KR"
                },
                payload: {
                    typeface: {
                        name: "Noto Sans KR",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/notosanskr/v37/PbyxFmXiEBPT4ITbgNA5Cgms3VYcOA-vvnIzzuoyeLQ.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/notosanskr/v37/PbyxFmXiEBPT4ITbgNA5Cgms3VYcOA-vvnIzztgyeLQ.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/notosanskr/v37/PbyxFmXiEBPT4ITbgNA5Cgms3VYcOA-vvnIzzg01eLQ.ttf"
                        }]
                    }
                }
            }, {
                id: "noto-serif-kr",
                label: {
                    en: "Noto Serif KR",
                    ko: "본명조 KR"
                },
                payload: {
                    typeface: {
                        name: "Noto Serif KR",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/notoserifkr/v29/3JnoSDn90Gmq2mr3blnHaTZXbOtLJDvui3JOncjmeM52.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/notoserifkr/v29/3JnoSDn90Gmq2mr3blnHaTZXbOtLJDvui3JOncgBf852.ttf"
                        }]
                    }
                }
            }, {
                id: "ibm-plex-sans-kr",
                label: {
                    en: "IBM Plex Sans KR",
                    ko: "IBM Plex Sans KR"
                },
                payload: {
                    typeface: {
                        name: "IBM Plex Sans KR",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/ibmplexsanskr/v10/vEFK2-VJISZe3O_rc3ZVYh4aTwNO8tI.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/ibmplexsanskr/v10/vEFN2-VJISZe3O_rc3ZVYh4aTwNOyiacf7Y.ttf"
                        }, {
                            style: "normal",
                            weight: "semiBold",
                            subFamily: "SemiBold",
                            uri: "https://fonts.gstatic.com/s/ibmplexsanskr/v10/vEFN2-VJISZe3O_rc3ZVYh4aTwNOygqbf7Y.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/ibmplexsanskr/v10/vEFN2-VJISZe3O_rc3ZVYh4aTwNOym6af7Y.ttf"
                        }]
                    }
                }
            }, {
                id: "song-myung",
                label: {
                    en: "Song Myung",
                    ko: "송명"
                },
                payload: {
                    typeface: {
                        name: "Song Myung",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/songmyung/v21/1cX2aUDWAJH5-EIC7DIhr1E.ttf"
                        }]
                    }
                }
            }, {
                id: "gowun-dodum",
                label: {
                    en: "Gowun Dodum",
                    ko: "고운도둠"
                },
                payload: {
                    typeface: {
                        name: "Gowun Dodum",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/gowundodum/v11/3Jn5SD_00GqwlBnWc1TUJF0F.ttf"
                        }]
                    }
                }
            }, {
                id: "gowun-batang",
                label: {
                    en: "Gowun Batang",
                    ko: "고운바탕"
                },
                payload: {
                    typeface: {
                        name: "Gowun Batang",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/gowunbatang/v11/ijwSs5nhRMIjYsdSgcMa3wRhXA.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/gowunbatang/v11/ijwNs5nhRMIjYsdSgcMa3wRZ4J7awg.ttf"
                        }]
                    }
                }
            }, {
                id: "nanum-gothic",
                label: {
                    en: "Nanum Gothic",
                    ko: "나눔고딕"
                },
                payload: {
                    typeface: {
                        name: "Nanum Gothic",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/nanumgothic/v26/PN_3Rfi-oW3hYwmKDpxS7F_z_g.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/nanumgothic/v26/PN_oRfi-oW3hYwmKDpxS7F_LQv37zg.ttf"
                        }]
                    }
                }
            }, {
                id: "nanum-myeongjo",
                label: {
                    en: "Nanum Myeongjo",
                    ko: "나눔명조"
                },
                payload: {
                    typeface: {
                        name: "Nanum Myeongjo",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/nanummyeongjo/v30/9Btx3DZF0dXLMZlywRbVRNhxy1Lr.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/nanummyeongjo/v30/9Bty3DZF0dXLMZlywRbVRNhxy2pXV1A0.ttf"
                        }]
                    }
                }
            }, {
                id: "nanum-gothic-coding",
                label: {
                    en: "Nanum Gothic Coding",
                    ko: "나눔고딕코딩"
                },
                payload: {
                    typeface: {
                        name: "Nanum Gothic Coding",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/nanumgothiccoding/v26/8QIVdjzHisX_8vv59_xMxtPFW4IXROwsy6Q.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/nanumgothiccoding/v26/8QIYdjzHisX_8vv59_xMxtPFW4IXROws8xgecsU.ttf"
                        }]
                    }
                }
            }, {
                id: "nanum-brush-script",
                label: {
                    en: "Nanum Brush Script",
                    ko: "나눔브러시스크립트"
                },
                payload: {
                    typeface: {
                        name: "Nanum Brush Script",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/nanumbrushscript/v25/wXK2E2wfpokopxzthSqPbcR5_gVaxazyjg.ttf"
                        }]
                    }
                }
            }, {
                id: "nanum-pen-script",
                label: {
                    en: "Nanum Pen Script",
                    ko: "나눔펜스크립트"
                },
                payload: {
                    typeface: {
                        name: "Nanum Pen Script",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/nanumpenscript/v25/daaDSSYiLGqEal3MvdA_FOL_3FkN2z4.ttf"
                        }]
                    }
                }
            }, {
                id: "black-han-sans",
                label: {
                    en: "Black Han Sans",
                    ko: "검은고딕"
                },
                payload: {
                    typeface: {
                        name: "Black Han Sans",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/blackhansans/v23/ea8Aad44WunzF9a-dL6toA8r8nqV.ttf"
                        }]
                    }
                }
            }, {
                id: "do-hyeon",
                label: {
                    en: "Do Hyeon",
                    ko: "도현체"
                },
                payload: {
                    typeface: {
                        name: "Do Hyeon",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/dohyeon/v20/TwMN-I8CRRU2zM86HFE3.ttf"
                        }]
                    }
                }
            }, {
                id: "jua",
                label: {
                    en: "Jua",
                    ko: "주아체"
                },
                payload: {
                    typeface: {
                        name: "Jua",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/jua/v17/co3KmW9ljjAjcw.ttf"
                        }]
                    }
                }
            }, {
                id: "gothic-a1",
                label: {
                    en: "Gothic A1",
                    ko: "고딕 A1"
                },
                payload: {
                    typeface: {
                        name: "Gothic A1",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/gothica1/v17/CSR94z5ZnPydRjlCCwl6bA.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/gothica1/v17/CSR44z5ZnPydRjlCCwlCmOQKSA.ttf"
                        }, {
                            style: "normal",
                            weight: "semiBold",
                            subFamily: "SemiBold",
                            uri: "https://fonts.gstatic.com/s/gothica1/v17/CSR44z5ZnPydRjlCCwlCtOMKSA.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/gothica1/v17/CSR44z5ZnPydRjlCCwlC0OIKSA.ttf"
                        }, {
                            style: "normal",
                            weight: "extraBold",
                            subFamily: "ExtraBold",
                            uri: "https://fonts.gstatic.com/s/gothica1/v17/CSR44z5ZnPydRjlCCwlCzOEKSA.ttf"
                        }, {
                            style: "normal",
                            weight: "heavy",
                            subFamily: "Heavy",
                            uri: "https://fonts.gstatic.com/s/gothica1/v17/CSR44z5ZnPydRjlCCwlC6OAKSA.ttf"
                        }]
                    }
                }
            }, {
                id: "sunflower",
                label: {
                    en: "Sunflower",
                    ko: "선플라워"
                },
                payload: {
                    typeface: {
                        name: "Sunflower",
                        fonts: [{
                            style: "normal",
                            weight: "light",
                            subFamily: "Light",
                            uri: "https://fonts.gstatic.com/s/sunflower/v17/RWmPoKeF8fUjqIj7Vc-06MfiqQ.ttf"
                        }, {
                            style: "normal",
                            weight: "medium",
                            subFamily: "Medium",
                            uri: "https://fonts.gstatic.com/s/sunflower/v17/RWmPoKeF8fUjqIj7Vc-0sMbiqQ.ttf"
                        }, {
                            style: "normal",
                            weight: "bold",
                            subFamily: "Bold",
                            uri: "https://fonts.gstatic.com/s/sunflower/v17/RWmPoKeF8fUjqIj7Vc-0-MDiqQ.ttf"
                        }]
                    }
                }
            }, {
                id: "gamja-flower",
                label: {
                    en: "Gamja Flower",
                    ko: "감자꽃"
                },
                payload: {
                    typeface: {
                        name: "Gamja Flower",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/gamjaflower/v25/6NUR8FiKJg-Pa0rM6uN40Z4kyQ.ttf"
                        }]
                    }
                }
            }, {
                id: "hi-melody",
                label: {
                    en: "Hi Melody",
                    ko: "하이멜로디"
                },
                payload: {
                    typeface: {
                        name: "Hi Melody",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/himelody/v18/46ktlbP8Vnz0pJcqCTbEfw.ttf"
                        }]
                    }
                }
            }, {
                id: "cute-font",
                label: {
                    en: "Cute Font",
                    ko: "큐트폰트"
                },
                payload: {
                    typeface: {
                        name: "Cute Font",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/cutefont/v27/Noaw6Uny2oWPbSHMrY6vmA.ttf"
                        }]
                    }
                }
            }, {
                id: "gugi",
                label: {
                    en: "Gugi",
                    ko: "구기체"
                },
                payload: {
                    typeface: {
                        name: "Gugi",
                        fonts: [{
                            style: "normal",
                            weight: "normal",
                            subFamily: "Regular",
                            uri: "https://fonts.gstatic.com/s/gugi/v20/A2BVn5dXywshVA4.ttf"
                        }]
                    }
                }
            }]
        }.fonts || [],
        "de-DE": {
            fonts: JSON.parse('[{"id":"jost","label":{"en":"Jost","de":"Jost"},"payload":{"typeface":{"name":"Jost","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/jost/v19/92zPtBhPNqw79Ij1E865zBUv7myjJQVG.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/jost/v19/92zPtBhPNqw79Ij1E865zBUv7myRJQVG.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/jost/v19/92zPtBhPNqw79Ij1E865zBUv7mx9IgVG.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/jost/v19/92zPtBhPNqw79Ij1E865zBUv7mxEIgVG.ttf"}]}}},{"id":"arimo","label":{"en":"Arimo","de":"Arimo"},"payload":{"typeface":{"name":"Arimo","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/arimo/v34/P5sfzZCDf9_T_3cV7NCUECyoxNk37cxsBw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/arimo/v34/P5sfzZCDf9_T_3cV7NCUECyoxNk3CstsBw.ttf"}]}}},{"id":"work-sans","label":{"en":"Work Sans","de":"Work Sans"},"payload":{"typeface":{"name":"Work Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K0nXNig.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K3vXNig.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K5fQNig.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K67QNig.ttf"}]}}},{"id":"cabin","label":{"en":"Cabin","de":"Cabin"},"payload":{"typeface":{"name":"Cabin","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EL7E.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkW-EL7E.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkYODL7E.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkbqDL7E.ttf"}]}}},{"id":"kanit","label":{"en":"Kanit","de":"Kanit"},"payload":{"typeface":{"name":"Kanit","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/kanit/v16/nKKZ-Go6G5tXcoaS.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/kanit/v16/nKKU-Go6G5tXcr5mOCWg.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/kanit/v16/nKKU-Go6G5tXcr5KPyWg.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/kanit/v16/nKKU-Go6G5tXcr4uPiWg.ttf"}]}}},{"id":"exo","label":{"en":"Exo","de":"Exo"},"payload":{"typeface":{"name":"Exo","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/exo/v24/4UaZrEtFpBI4f1ZSIK9d4LjJ4lM3CwM.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/exo/v24/4UaZrEtFpBI4f1ZSIK9d4LjJ4mE3CwM.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/exo/v24/4UaZrEtFpBI4f1ZSIK9d4LjJ4o0wCwM.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/exo/v24/4UaZrEtFpBI4f1ZSIK9d4LjJ4rQwCwM.ttf"}]}}},{"id":"hind","label":{"en":"Hind","de":"Hind"},"payload":{"typeface":{"name":"Hind","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/hind/v17/5aU69_a8oxmIRG4.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/hind/v17/5aU19_a8oxmIfJpbIRs.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/hind/v17/5aU19_a8oxmIfLZcIRs.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/hind/v17/5aU19_a8oxmIfNJdIRs.ttf"}]}}},{"id":"asap","label":{"en":"Asap","de":"Asap"},"payload":{"typeface":{"name":"Asap","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYkqUsLg.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYoKUsLg.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYTKIsLg.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYdaIsLg.ttf"}]}}},{"id":"mukta","label":{"en":"Mukta","de":"Mukta"},"payload":{"typeface":{"name":"Mukta","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/mukta/v16/iJWKBXyXfDDVXYnG.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/mukta/v16/iJWHBXyXfDDVXbEyjlma.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/mukta/v16/iJWHBXyXfDDVXbEeiVma.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/mukta/v16/iJWHBXyXfDDVXbF6iFma.ttf"}]}}},{"id":"barlow","label":{"en":"Barlow","de":"Barlow"},"payload":{"typeface":{"name":"Barlow","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHpv4kjgoGqM7EPCw.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E3_-gc4A.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E30-8c4A.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E3t-4c4A.ttf"}]}}},{"id":"crimson-text","label":{"en":"Crimson Text","de":"Crimson Text"},"payload":{"typeface":{"name":"Crimson Text","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/crimsontext/v19/wlp2gwHKFkZgtmSR3NB0oRJvaA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/crimsontext/v19/wlppgwHKFkZgtmSR3NB0oRJX1C12Cw.ttf"}]}}},{"id":"bitter","label":{"en":"Bitter","de":"Bitter"},"payload":{"typeface":{"name":"Bitter","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfCL8.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8RHYCL8.ttf"}]}}},{"id":"libre-baskerville","label":{"en":"Libre Baskerville","de":"Libre Baskerville"},"payload":{"typeface":{"name":"Libre Baskerville","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/librebaskerville/v16/kmKnZrc3Hgbbcjq75U4uslyuy4kn0pNe.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/librebaskerville/v16/kmKiZrc3Hgbbcjq75U4uslyuy4kn0qviTjYw.ttf"}]}}},{"id":"vollkorn","label":{"en":"Vollkorn","de":"Vollkorn"},"payload":{"typeface":{"name":"Vollkorn","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df2MHGuGQ.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df2AnGuGQ.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df27nauGQ.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df213auGQ.ttf"}]}}},{"id":"gentium-book-plus","label":{"en":"Gentium Book Plus","de":"Gentium Book Plus"},"payload":{"typeface":{"name":"Gentium Book Plus","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/gentiumbookplus/v1/vEFL2-RHBgUK5fbjKxRpbBtJPyRpofKf.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/gentiumbookplus/v1/vEFO2-RHBgUK5fbjKxRpbBtJPyRpocojWpbG.ttf"}]}}},{"id":"eb-garamond","label":{"en":"EB Garamond","de":"EB Garamond"},"payload":{"typeface":{"name":"EB Garamond","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/ebgaramond/v31/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-6_RUAw.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/ebgaramond/v31/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-2fRUAw.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/ebgaramond/v31/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-NfNUAw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/ebgaramond/v31/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-DPNUAw.ttf"}]}}},{"id":"cardo","label":{"en":"Cardo","de":"Cardo"},"payload":{"typeface":{"name":"Cardo","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/cardo/v20/wlp_gwjKBV1pqiv_.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/cardo/v20/wlpygwjKBV1pqhND-aQR.ttf"}]}}},{"id":"spectral","label":{"en":"Spectral","de":"Spectral"},"payload":{"typeface":{"name":"Spectral","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/spectral/v14/rnCr-xNNww_2s0amA-M-.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/spectral/v14/rnCs-xNNww_2s0amA9vKsV3G.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/spectral/v14/rnCs-xNNww_2s0amA9vmtl3G.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/spectral/v14/rnCs-xNNww_2s0amA9uCt13G.ttf"}]}}},{"id":"literata","label":{"en":"Literata","de":"Literata"},"payload":{"typeface":{"name":"Literata","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/literata/v39/or3PQ6P12-iJxAIgLa78DkrbXsDgk0oVDaDPYLanFLHpPf2TbBG_F_Y.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/literata/v39/or3PQ6P12-iJxAIgLa78DkrbXsDgk0oVDaDPYLanFLHpPf2TbCO_F_Y.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/literata/v39/or3PQ6P12-iJxAIgLa78DkrbXsDgk0oVDaDPYLanFLHpPf2TbM-4F_Y.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/literata/v39/or3PQ6P12-iJxAIgLa78DkrbXsDgk0oVDaDPYLanFLHpPf2TbPa4F_Y.ttf"}]}}},{"id":"crimson-pro","label":{"en":"Crimson Pro","de":"Crimson Pro"},"payload":{"typeface":{"name":"Crimson Pro","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/crimsonpro/v27/q5uUsoa5M_tv7IihmnkabC5XiXCAlXGks1WZzm18OA.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/crimsonpro/v27/q5uUsoa5M_tv7IihmnkabC5XiXCAlXGks1WZ_G18OA.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/crimsonpro/v27/q5uUsoa5M_tv7IihmnkabC5XiXCAlXGks1WZEGp8OA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/crimsonpro/v27/q5uUsoa5M_tv7IihmnkabC5XiXCAlXGks1WZKWp8OA.ttf"}]}}}]')
        }.fonts || [],
        "es-ES": {
            fonts: JSON.parse('[{"id":"rubik","label":{"en":"Rubik","es":"Rubik"},"payload":{"typeface":{"name":"Rubik","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4i1UA.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-NYi1UA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-4I-1UA.ttf"}]}}},{"id":"work-sans","label":{"en":"Work Sans","es":"Work Sans"},"payload":{"typeface":{"name":"Work Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K0nXNig.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K3vXNig.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K67QNig.ttf"}]}}},{"id":"libre-franklin","label":{"en":"Libre Franklin","es":"Libre Franklin"},"payload":{"typeface":{"name":"Libre Franklin","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhLsWUBw.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhHMWUBw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhycKUBw.ttf"}]}}},{"id":"libre-baskerville","label":{"en":"Libre Baskerville","es":"Libre Baskerville"},"payload":{"typeface":{"name":"Libre Baskerville","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/librebaskerville/v16/kmKnZrc3Hgbbcjq75U4uslyuy4kn0pNe.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/librebaskerville/v16/kmKiZrc3Hgbbcjq75U4uslyuy4kn0qviTjYw.ttf"}]}}},{"id":"crimson-text","label":{"en":"Crimson Text","es":"Crimson Text"},"payload":{"typeface":{"name":"Crimson Text","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/crimsontext/v19/wlp2gwHKFkZgtmSR3NB0oRJvaA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/crimsontext/v19/wlppgwHKFkZgtmSR3NB0oRJX1C12Cw.ttf"}]}}},{"id":"zilla-slab","label":{"en":"Zilla Slab","es":"Zilla Slab"},"payload":{"typeface":{"name":"Zilla Slab","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/zillaslab/v11/dFa6ZfeM_74wlPZtksIFWj0.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/zillaslab/v11/dFa5ZfeM_74wlPZtksIFYskZ2HQ.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/zillaslab/v11/dFa5ZfeM_74wlPZtksIFYoEf2HQ.ttf"}]}}},{"id":"asap","label":{"en":"Asap","es":"Asap"},"payload":{"typeface":{"name":"Asap","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYkqUsLg.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYoKUsLg.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYTKIsLg.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYdaIsLg.ttf"}]}}},{"id":"barlow","label":{"en":"Barlow","es":"Barlow"},"payload":{"typeface":{"name":"Barlow","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHpv4kjgoGqM7EPCw.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E3_-gc4A.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E30-8c4A.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E3t-4c4A.ttf"}]}}},{"id":"bitter","label":{"en":"Bitter","es":"Bitter"},"payload":{"typeface":{"name":"Bitter","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfCL8.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8RHYCL8.ttf"}]}}},{"id":"cabin","label":{"en":"Cabin","es":"Cabin"},"payload":{"typeface":{"name":"Cabin","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EL7E.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkW-EL7E.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkYODL7E.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkbqDL7E.ttf"}]}}},{"id":"catamaran","label":{"en":"Catamaran","es":"Catamaran"},"payload":{"typeface":{"name":"Catamaran","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/catamaran/v27/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPHjd1ak.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/catamaran/v27/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPErd1ak.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/catamaran/v27/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPKba1ak.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/catamaran/v27/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPJ_a1ak.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/catamaran/v27/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPPja1ak.ttf"},{"style":"normal","weight":"heavy","subFamily":"Heavy","uri":"https://fonts.gstatic.com/s/catamaran/v27/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPNHa1ak.ttf"}]}}},{"id":"dm-sans","label":{"en":"DM Sans","es":"DM Sans"},"payload":{"typeface":{"name":"DM Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/dmsans/v16/rP2tp2ywxg089UriI5-g4vlH9VoD8CmcqZG40F9JadbnoEwAopxhTg.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/dmsans/v16/rP2tp2ywxg089UriI5-g4vlH9VoD8CmcqZG40F9JadbnoEwAkJxhTg.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/dmsans/v16/rP2tp2ywxg089UriI5-g4vlH9VoD8CmcqZG40F9JadbnoEwARZthTg.ttf"}]}}},{"id":"dosis","label":{"en":"Dosis","es":"Dosis"},"payload":{"typeface":{"name":"Dosis","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/dosis/v33/HhyJU5sn9vOmLxNkIwRSjTVNWLEJN7MV3A.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/dosis/v33/HhyJU5sn9vOmLxNkIwRSjTVNWLEJBbMV3A.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/dosis/v33/HhyJU5sn9vOmLxNkIwRSjTVNWLEJ6bQV3A.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/dosis/v33/HhyJU5sn9vOmLxNkIwRSjTVNWLEJ0LQV3A.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/dosis/v33/HhyJU5sn9vOmLxNkIwRSjTVNWLEJt7QV3A.ttf"}]}}},{"id":"exo-2","label":{"en":"Exo 2","es":"Exo 2"},"payload":{"typeface":{"name":"Exo 2","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/exo2/v25/7cH1v4okm5zmbvwkAx_sfcEuiD8jvvKcPg.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/exo2/v25/7cH1v4okm5zmbvwkAx_sfcEuiD8jjPKcPg.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/exo2/v25/7cH1v4okm5zmbvwkAx_sfcEuiD8jYPWcPg.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/exo2/v25/7cH1v4okm5zmbvwkAx_sfcEuiD8jWfWcPg.ttf"}]}}},{"id":"hind","label":{"en":"Hind","es":"Hind"},"payload":{"typeface":{"name":"Hind","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/hind/v17/5aU69_a8oxmIRG4.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/hind/v17/5aU19_a8oxmIfJpbIRs.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/hind/v17/5aU19_a8oxmIfLZcIRs.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/hind/v17/5aU19_a8oxmIfNJdIRs.ttf"}]}}},{"id":"josefin-sans","label":{"en":"Josefin Sans","es":"Josefin Sans"},"payload":{"typeface":{"name":"Josefin Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/josefinsans/v33/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_DjQXME.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/josefinsans/v33/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_ArQXME.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/josefinsans/v33/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_ObXXME.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/josefinsans/v33/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_N_XXME.ttf"}]}}},{"id":"lora","label":{"en":"Lora","es":"Lora"},"payload":{"typeface":{"name":"Lora","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/lora/v36/0QI6MX1D_JOuGQbT0gvTJPa787weuyJG.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/lora/v36/0QI6MX1D_JOuGQbT0gvTJPa787wsuyJG.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/lora/v36/0QI6MX1D_JOuGQbT0gvTJPa787zAvCJG.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/lora/v36/0QI6MX1D_JOuGQbT0gvTJPa787z5vCJG.ttf"}]}}},{"id":"maven-pro","label":{"en":"Maven Pro","es":"Maven Pro"},"payload":{"typeface":{"name":"Maven Pro","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/mavenpro/v39/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8SX25nA.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/mavenpro/v39/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8Rf25nA.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/mavenpro/v39/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8fvx5nA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/mavenpro/v39/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8cLx5nA.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/mavenpro/v39/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8aXx5nA.ttf"},{"style":"normal","weight":"heavy","subFamily":"Heavy","uri":"https://fonts.gstatic.com/s/mavenpro/v39/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8Yzx5nA.ttf"}]}}},{"id":"saira","label":{"en":"Saira","es":"Saira"},"payload":{"typeface":{"name":"Saira","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/saira/v21/memWYa2wxmKQyPMrZX79wwYZQMhsyuShhKMjjbU9uXuA71rCosg.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/saira/v21/memWYa2wxmKQyPMrZX79wwYZQMhsyuShhKMjjbU9uXuA72jCosg.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/saira/v21/memWYa2wxmKQyPMrZX79wwYZQMhsyuShhKMjjbU9uXuA74TFosg.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/saira/v21/memWYa2wxmKQyPMrZX79wwYZQMhsyuShhKMjjbU9uXuA773Fosg.ttf"}]}}},{"id":"titillium-web","label":{"en":"Titillium Web","es":"Titillium Web"},"payload":{"typeface":{"name":"Titillium Web","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/titilliumweb/v18/NaPecZTIAOhVxoMyOr9n_E7fRMQ.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/titilliumweb/v18/NaPDcZTIAOhVxoMyOr9n_E7ffBzCKIw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/titilliumweb/v18/NaPDcZTIAOhVxoMyOr9n_E7ffHjDKIw.ttf"}]}}}]')
        }.fonts || [],
        "fr-FR": {
            fonts: JSON.parse('[{"id":"work-sans","label":{"en":"Work Sans","fr":"Work Sans"},"payload":{"typeface":{"name":"Work Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K0nXNig.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K3vXNig.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K5fQNig.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K67QNig.ttf"}]}}},{"id":"libre-franklin","label":{"en":"Libre Franklin","fr":"Libre Franklin"},"payload":{"typeface":{"name":"Libre Franklin","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhLsWUBw.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhHMWUBw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhycKUBw.ttf"}]}}},{"id":"crimson-text","label":{"en":"Crimson Text","fr":"Crimson Text"},"payload":{"typeface":{"name":"Crimson Text","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/crimsontext/v19/wlp2gwHKFkZgtmSR3NB0oRJvaA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/crimsontext/v19/wlppgwHKFkZgtmSR3NB0oRJX1C12Cw.ttf"}]}}},{"id":"libre-baskerville","label":{"en":"Libre Baskerville","fr":"Libre Baskerville"},"payload":{"typeface":{"name":"Libre Baskerville","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/librebaskerville/v16/kmKnZrc3Hgbbcjq75U4uslyuy4kn0pNe.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/librebaskerville/v16/kmKiZrc3Hgbbcjq75U4uslyuy4kn0qviTjYw.ttf"}]}}},{"id":"eb-garamond","label":{"en":"EB Garamond","fr":"EB Garamond"},"payload":{"typeface":{"name":"EB Garamond","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/ebgaramond/v31/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-6_RUAw.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/ebgaramond/v31/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-2fRUAw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/ebgaramond/v31/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-DPNUAw.ttf"}]}}},{"id":"cormorant-garamond","label":{"en":"Cormorant Garamond","fr":"Cormorant Garamond"},"payload":{"typeface":{"name":"Cormorant Garamond","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/cormorantgaramond/v20/co3umX5slCNuHLi8bLeY9MK7whWMhyjypVO7abI26QOD_v86GnM.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/cormorantgaramond/v20/co3umX5slCNuHLi8bLeY9MK7whWMhyjypVO7abI26QOD_s06GnM.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/cormorantgaramond/v20/co3umX5slCNuHLi8bLeY9MK7whWMhyjypVO7abI26QOD_hg9GnM.ttf"}]}}},{"id":"spectral","label":{"en":"Spectral","fr":"Spectral"},"payload":{"typeface":{"name":"Spectral","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/spectral/v14/rnCr-xNNww_2s0amA-M-.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/spectral/v14/rnCs-xNNww_2s0amA9vKsV3G.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/spectral/v14/rnCs-xNNww_2s0amA9uCt13G.ttf"}]}}},{"id":"bellefair","label":{"en":"Bellefair","fr":"Bellefair"},"payload":{"typeface":{"name":"Bellefair","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/bellefair/v14/kJExBuYY6AAuhiXUxG19_w.ttf"}]}}},{"id":"barlow","label":{"en":"Barlow","fr":"Barlow"},"payload":{"typeface":{"name":"Barlow","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHpv4kjgoGqM7EPCw.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E3_-gc4A.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E30-8c4A.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E3t-4c4A.ttf"}]}}},{"id":"cabin","label":{"en":"Cabin","fr":"Cabin"},"payload":{"typeface":{"name":"Cabin","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EL7E.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkW-EL7E.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkYODL7E.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkbqDL7E.ttf"}]}}},{"id":"karla","label":{"en":"Karla","fr":"Karla"},"payload":{"typeface":{"name":"Karla","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/karla/v32/qkBIXvYC6trAT55ZBi1ueQVIjQTD-JqqFA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/karla/v32/qkBIXvYC6trAT55ZBi1ueQVIjQTDH52qFA.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/karla/v32/qkBIXvYC6trAT55ZBi1ueQVIjQTDeJ2qFA.ttf"}]}}},{"id":"arimo","label":{"en":"Arimo","fr":"Arimo"},"payload":{"typeface":{"name":"Arimo","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/arimo/v34/P5sfzZCDf9_T_3cV7NCUECyoxNk37cxsBw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/arimo/v34/P5sfzZCDf9_T_3cV7NCUECyoxNk3CstsBw.ttf"}]}}},{"id":"droid-sans","label":{"en":"Droid Sans","fr":"Droid Sans"},"payload":{"typeface":{"name":"Droid Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/droidsans/v18/SlGVmQWMvZQIdix7AFxXoHQ.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/droidsans/v18/SlGWmQWMvZQIdix7AFxXmMh3SDw.ttf"}]}}},{"id":"pt-sans","label":{"en":"PT Sans","fr":"PT Sans"},"payload":{"typeface":{"name":"PT Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/ptsans/v17/jizaRExUiTo99u79P0U.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/ptsans/v17/jizfRExUiTo99u79B_mh4Ok.ttf"}]}}},{"id":"jost","label":{"en":"Jost","fr":"Jost"},"payload":{"typeface":{"name":"Jost","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/jost/v19/92zPtBhPNqw79Ij1E865zBUv7myjJQVG.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/jost/v19/92zPtBhPNqw79Ij1E865zBUv7myRJQVG.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/jost/v19/92zPtBhPNqw79Ij1E865zBUv7mx9IgVG.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/jost/v19/92zPtBhPNqw79Ij1E865zBUv7mxEIgVG.ttf"}]}}},{"id":"exo","label":{"en":"Exo","fr":"Exo"},"payload":{"typeface":{"name":"Exo","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/exo/v24/4UaZrEtFpBI4f1ZSIK9d4LjJ4lM3CwM.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/exo/v24/4UaZrEtFpBI4f1ZSIK9d4LjJ4mE3CwM.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/exo/v24/4UaZrEtFpBI4f1ZSIK9d4LjJ4o0wCwM.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/exo/v24/4UaZrEtFpBI4f1ZSIK9d4LjJ4rQwCwM.ttf"}]}}},{"id":"asap","label":{"en":"Asap","fr":"Asap"},"payload":{"typeface":{"name":"Asap","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYkqUsLg.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYoKUsLg.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYTKIsLg.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYdaIsLg.ttf"}]}}},{"id":"bitter","label":{"en":"Bitter","fr":"Bitter"},"payload":{"typeface":{"name":"Bitter","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfCL8.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8RHYCL8.ttf"}]}}},{"id":"vollkorn","label":{"en":"Vollkorn","fr":"Vollkorn"},"payload":{"typeface":{"name":"Vollkorn","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df2MHGuGQ.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df2AnGuGQ.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df27nauGQ.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df213auGQ.ttf"}]}}},{"id":"gentium-book-plus","label":{"en":"Gentium Book Plus","fr":"Gentium Book Plus"},"payload":{"typeface":{"name":"Gentium Book Plus","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/gentiumbookplus/v1/vEFL2-RHBgUK5fbjKxRpbBtJPyRpofKf.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/gentiumbookplus/v1/vEFO2-RHBgUK5fbjKxRpbBtJPyRpocojWpbG.ttf"}]}}}]')
        }.fonts || [],
        "hi-IN": {
            fonts: JSON.parse('[{"id":"noto-sans-devanagari","label":{"en":"Noto Sans Devanagari","hi":"Noto Sans देवनागरी"},"payload":{"typeface":{"name":"Noto Sans Devanagari","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/notosansdevanagari/v29/TuGoUUFzXI5FBtUq5a8bjKYTZjtRU6Sgv3NaV_SNmI0b8QQCQmHn6B2OHjbL_08AlXQly-A.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/notosansdevanagari/v29/TuGoUUFzXI5FBtUq5a8bjKYTZjtRU6Sgv3NaV_SNmI0b8QQCQmHn6B2OHjbL_08AlUYly-A.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/notosansdevanagari/v29/TuGoUUFzXI5FBtUq5a8bjKYTZjtRU6Sgv3NaV_SNmI0b8QQCQmHn6B2OHjbL_08AlZMiy-A.ttf"}]}}},{"id":"mukta","label":{"en":"Mukta","hi":"मुक्ता"},"payload":{"typeface":{"name":"Mukta","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/mukta/v16/iJWKBXyXfDDVXYnG.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/mukta/v16/iJWHBXyXfDDVXbEyjlma.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/mukta/v16/iJWHBXyXfDDVXbEeiVma.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/mukta/v16/iJWHBXyXfDDVXbF6iFma.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/mukta/v16/iJWHBXyXfDDVXbFmi1ma.ttf"}]}}},{"id":"hind","label":{"en":"Hind","hi":"हिंद"},"payload":{"typeface":{"name":"Hind","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/hind/v17/5aU69_a8oxmIRG4.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/hind/v17/5aU19_a8oxmIfJpbIRs.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/hind/v17/5aU19_a8oxmIfLZcIRs.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/hind/v17/5aU19_a8oxmIfNJdIRs.ttf"}]}}},{"id":"teko","label":{"en":"Teko","hi":"टेको"},"payload":{"typeface":{"name":"Teko","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/teko/v22/LYjYdG7kmE0gV69VVPPdFl06VN8XG7Sy.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/teko/v22/LYjYdG7kmE0gV69VVPPdFl06VN8lG7Sy.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/teko/v22/LYjYdG7kmE0gV69VVPPdFl06VN_JHLSy.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/teko/v22/LYjYdG7kmE0gV69VVPPdFl06VN_wHLSy.ttf"}]}}},{"id":"kalam","label":{"en":"Kalam","hi":"कलम"},"payload":{"typeface":{"name":"Kalam","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/kalam/v17/YA9dr0Wd4kDdMuhW.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/kalam/v17/YA9Qr0Wd4kDdMtDqHQLL.ttf"}]}}},{"id":"rajdhani","label":{"en":"Rajdhani","hi":"राजधानी"},"payload":{"typeface":{"name":"Rajdhani","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/rajdhani/v16/LDIxapCSOBg7S-QT7q4A.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/rajdhani/v16/LDI2apCSOBg7S-QT7pb0EMOs.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/rajdhani/v16/LDI2apCSOBg7S-QT7pbYF8Os.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/rajdhani/v16/LDI2apCSOBg7S-QT7pa8FsOs.ttf"}]}}},{"id":"arya","label":{"en":"Arya","hi":"आर्या"},"payload":{"typeface":{"name":"Arya","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/arya/v20/ga6CawNG-HJd9UY.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/arya/v20/ga6NawNG-HJdzfra3b8.ttf"}]}}},{"id":"eczar","label":{"en":"Eczar","hi":"एक्ज़ार"},"payload":{"typeface":{"name":"Eczar","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/eczar/v26/BXR2vF3Pi-DLmxcpJB-qbNTyTMDXHd6WqQ.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/eczar/v26/BXR2vF3Pi-DLmxcpJB-qbNTyTMDXL96WqQ.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/eczar/v26/BXR2vF3Pi-DLmxcpJB-qbNTyTMDXw9mWqQ.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/eczar/v26/BXR2vF3Pi-DLmxcpJB-qbNTyTMDX-tmWqQ.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/eczar/v26/BXR2vF3Pi-DLmxcpJB-qbNTyTMDXndmWqQ.ttf"}]}}},{"id":"martel","label":{"en":"Martel","hi":"मार्टेल"},"payload":{"typeface":{"name":"Martel","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/martel/v11/PN_xRfK9oXHga0XtYQ.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/martel/v11/PN_yRfK9oXHga0XVuewahQ.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/martel/v11/PN_yRfK9oXHga0XV3e0ahQ.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/martel/v11/PN_yRfK9oXHga0XVwe4ahQ.ttf"},{"style":"normal","weight":"heavy","subFamily":"Heavy","uri":"https://fonts.gstatic.com/s/martel/v11/PN_yRfK9oXHga0XV5e8ahQ.ttf"}]}}},{"id":"martel-sans","label":{"en":"Martel Sans","hi":"मार्टेल सैन्स"},"payload":{"typeface":{"name":"Martel Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/martelsans/v13/h0GsssGi7VdzDgKjM-4d8ijf.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/martelsans/v13/h0GxssGi7VdzDgKjM-4d8hAH48uH.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/martelsans/v13/h0GxssGi7VdzDgKjM-4d8hBj4suH.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/martelsans/v13/h0GxssGi7VdzDgKjM-4d8hB_4cuH.ttf"},{"style":"normal","weight":"heavy","subFamily":"Heavy","uri":"https://fonts.gstatic.com/s/martelsans/v13/h0GxssGi7VdzDgKjM-4d8hBb4MuH.ttf"}]}}},{"id":"baloo-2","label":{"en":"Baloo 2","hi":"बालू 2"},"payload":{"typeface":{"name":"Baloo 2","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/baloo2/v22/wXK0E3kTposypRydzVT08TS3JnAmtdgazapv.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/baloo2/v22/wXK0E3kTposypRydzVT08TS3JnAmtdgozapv.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/baloo2/v22/wXK0E3kTposypRydzVT08TS3JnAmtdjEyqpv.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/baloo2/v22/wXK0E3kTposypRydzVT08TS3JnAmtdj9yqpv.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/baloo2/v22/wXK0E3kTposypRydzVT08TS3JnAmtdiayqpv.ttf"}]}}},{"id":"baloo-bhai-2","label":{"en":"Baloo Bhai 2","hi":"बालू भाई 2"},"payload":{"typeface":{"name":"Baloo Bhai 2","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/baloobhai2/v29/sZlWdRSL-z1VEWZ4YNA7Y5ItevYWUOHDE8FvNighMXc.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/baloobhai2/v29/sZlWdRSL-z1VEWZ4YNA7Y5ItevYWUOHDE8FvNhohMXc.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/baloobhai2/v29/sZlWdRSL-z1VEWZ4YNA7Y5ItevYWUOHDE8FvNvYmMXc.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/baloobhai2/v29/sZlWdRSL-z1VEWZ4YNA7Y5ItevYWUOHDE8FvNs8mMXc.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/baloobhai2/v29/sZlWdRSL-z1VEWZ4YNA7Y5ItevYWUOHDE8FvNqgmMXc.ttf"}]}}},{"id":"baloo-tamma-2","label":{"en":"Baloo Tamma 2","hi":"बालू टम्मा 2"},"payload":{"typeface":{"name":"Baloo Tamma 2","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/balootamma2/v19/vEFE2_hCAgcR46PaajtrYlBbVUMUJgIC5LHTrMscPp-0.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/balootamma2/v19/vEFE2_hCAgcR46PaajtrYlBbVUMUJgIC5LHTrMsuPp-0.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/balootamma2/v19/vEFE2_hCAgcR46PaajtrYlBbVUMUJgIC5LHTrMvCOZ-0.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/balootamma2/v19/vEFE2_hCAgcR46PaajtrYlBbVUMUJgIC5LHTrMv7OZ-0.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/balootamma2/v19/vEFE2_hCAgcR46PaajtrYlBbVUMUJgIC5LHTrMucOZ-0.ttf"}]}}},{"id":"glegoo","label":{"en":"Glegoo","hi":"ग्लेगू"},"payload":{"typeface":{"name":"Glegoo","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/glegoo/v17/_Xmt-HQyrTKWaw2Jiw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/glegoo/v17/_Xmu-HQyrTKWaw2xN4a9CA.ttf"}]}}},{"id":"laila","label":{"en":"Laila","hi":"लैला"},"payload":{"typeface":{"name":"Laila","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/laila/v20/LYjMdG_8nE8jDIRd.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/laila/v20/LYjBdG_8nE8jDLypowNA.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/laila/v20/LYjBdG_8nE8jDLyFpANA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/laila/v20/LYjBdG_8nE8jDLzhpQNA.ttf"}]}}},{"id":"karma","label":{"en":"Karma","hi":"कर्मा"},"payload":{"typeface":{"name":"Karma","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/karma/v17/va9I4kzAzMZRGIBv.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/karma/v17/va9F4kzAzMZRGLibYsZ_.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/karma/v17/va9F4kzAzMZRGLi3ZcZ_.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/karma/v17/va9F4kzAzMZRGLjTZMZ_.ttf"}]}}},{"id":"anek-devanagari","label":{"en":"Anek Devanagari","hi":"अनेक देवनागरी"},"payload":{"typeface":{"name":"Anek Devanagari","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/anekdevanagari/v16/jVyo7nP0CGrUsxB-QiRgw0NlLaVt_QUAkYxLRoCL23mlh20ZVHOMAWbgHLDtku9n.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/anekdevanagari/v16/jVyo7nP0CGrUsxB-QiRgw0NlLaVt_QUAkYxLRoCL23mlh20ZVHOMAWbgHLDfku9n.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/anekdevanagari/v16/jVyo7nP0CGrUsxB-QiRgw0NlLaVt_QUAkYxLRoCL23mlh20ZVHOMAWbgHLAzle9n.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/anekdevanagari/v16/jVyo7nP0CGrUsxB-QiRgw0NlLaVt_QUAkYxLRoCL23mlh20ZVHOMAWbgHLAKle9n.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/anekdevanagari/v16/jVyo7nP0CGrUsxB-QiRgw0NlLaVt_QUAkYxLRoCL23mlh20ZVHOMAWbgHLBtle9n.ttf"}]}}},{"id":"yatra-one","label":{"en":"Yatra One","hi":"यात्रा वन"},"payload":{"typeface":{"name":"Yatra One","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/yatraone/v15/C8ch4copsHzj8p7NaF0xww.ttf"}]}}},{"id":"khand","label":{"en":"Khand","hi":"खंड"},"payload":{"typeface":{"name":"Khand","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/khand/v21/TwMA-IINQlQQ0YpV.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/khand/v21/TwMN-IINQlQQ0bKhcVE3.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/khand/v21/TwMN-IINQlQQ0bKNdlE3.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/khand/v21/TwMN-IINQlQQ0bLpd1E3.ttf"}]}}},{"id":"hind-siliguri","label":{"en":"Hind Siliguri","hi":"हिंद सिलीगुड़ी"},"payload":{"typeface":{"name":"Hind Siliguri","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/hindsiliguri/v13/ijwTs5juQtsyLLR5jN4cxBEofJs.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/hindsiliguri/v13/ijwOs5juQtsyLLR5jN4cxBEoRG_54uE.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/hindsiliguri/v13/ijwOs5juQtsyLLR5jN4cxBEoREP-4uE.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/hindsiliguri/v13/ijwOs5juQtsyLLR5jN4cxBEoRCf_4uE.ttf"}]}}}]')
        }.fonts || [],
        "it-IT": {
            fonts: JSON.parse('[{"id":"libre-franklin","label":{"en":"Libre Franklin","it":"Libre Franklin"},"payload":{"typeface":{"name":"Libre Franklin","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhLsWUBw.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhHMWUBw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhycKUBw.ttf"}]}}},{"id":"crimson-text","label":{"en":"Crimson Text","it":"Crimson Text"},"payload":{"typeface":{"name":"Crimson Text","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/crimsontext/v19/wlp2gwHKFkZgtmSR3NB0oRJvaA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/crimsontext/v19/wlppgwHKFkZgtmSR3NB0oRJX1C12Cw.ttf"}]}}},{"id":"libre-baskerville","label":{"en":"Libre Baskerville","it":"Libre Baskerville"},"payload":{"typeface":{"name":"Libre Baskerville","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/librebaskerville/v16/kmKnZrc3Hgbbcjq75U4uslyuy4kn0pNe.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/librebaskerville/v16/kmKiZrc3Hgbbcjq75U4uslyuy4kn0qviTjYw.ttf"}]}}},{"id":"bodoni-moda","label":{"en":"Bodoni Moda","it":"Bodoni Moda"},"payload":{"typeface":{"name":"Bodoni Moda","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/bodonimoda/v27/aFT67PxzY382XsXX63LUYL6GYFcan6NJrKp-VPjfJMShrpsGFUt8oU7awIA.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/bodonimoda/v27/aFT67PxzY382XsXX63LUYL6GYFcan6NJrKp-VPjfJMShrpsGFUt8oXzawIA.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/bodonimoda/v27/aFT67PxzY382XsXX63LUYL6GYFcan6NJrKp-VPjfJMShrpsGFUt8oZDdwIA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/bodonimoda/v27/aFT67PxzY382XsXX63LUYL6GYFcan6NJrKp-VPjfJMShrpsGFUt8oandwIA.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/bodonimoda/v27/aFT67PxzY382XsXX63LUYL6GYFcan6NJrKp-VPjfJMShrpsGFUt8oc7dwIA.ttf"},{"style":"normal","weight":"heavy","subFamily":"Heavy","uri":"https://fonts.gstatic.com/s/bodonimoda/v27/aFT67PxzY382XsXX63LUYL6GYFcan6NJrKp-VPjfJMShrpsGFUt8oefdwIA.ttf"}]}}},{"id":"quattrocento","label":{"en":"Quattrocento","it":"Quattrocento"},"payload":{"typeface":{"name":"Quattrocento","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/quattrocento/v23/OZpEg_xvsDZQL_LKIF7q4jPHxA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/quattrocento/v23/OZpbg_xvsDZQL_LKIF7q4jP_eE3fdw.ttf"}]}}},{"id":"quattrocento-sans","label":{"en":"Quattrocento Sans","it":"Quattrocento Sans"},"payload":{"typeface":{"name":"Quattrocento Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/quattrocentosans/v21/va9c4lja2NVIDdIAAoMR5MfuElaRB3zO.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/quattrocentosans/v21/va9Z4lja2NVIDdIAAoMR5MfuElaRB0RykmrW.ttf"}]}}},{"id":"cinzel","label":{"en":"Cinzel","it":"Cinzel"},"payload":{"typeface":{"name":"Cinzel","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/cinzel/v25/8vIU7ww63mVu7gtR-kwKxNvkNOjw-tbnTYo.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/cinzel/v25/8vIU7ww63mVu7gtR-kwKxNvkNOjw-uTnTYo.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/cinzel/v25/8vIU7ww63mVu7gtR-kwKxNvkNOjw-gjgTYo.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/cinzel/v25/8vIU7ww63mVu7gtR-kwKxNvkNOjw-jHgTYo.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/cinzel/v25/8vIU7ww63mVu7gtR-kwKxNvkNOjw-lbgTYo.ttf"},{"style":"normal","weight":"heavy","subFamily":"Heavy","uri":"https://fonts.gstatic.com/s/cinzel/v25/8vIU7ww63mVu7gtR-kwKxNvkNOjw-n_gTYo.ttf"}]}}},{"id":"cormorant","label":{"en":"Cormorant","it":"Cormorant"},"payload":{"typeface":{"name":"Cormorant","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/cormorant/v23/H4c2BXOCl9bbnla_nHIA47NMUjsNbCVrFhFTQ7Q.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/cormorant/v23/H4c2BXOCl9bbnla_nHIA47NMUjsNbCVrFiNTQ7Q.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/cormorant/v23/H4c2BXOCl9bbnla_nHIA47NMUjsNbCVrFs9UQ7Q.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/cormorant/v23/H4c2BXOCl9bbnla_nHIA47NMUjsNbCVrFvZUQ7Q.ttf"}]}}},{"id":"barlow","label":{"en":"Barlow","it":"Barlow"},"payload":{"typeface":{"name":"Barlow","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHpv4kjgoGqM7EPCw.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E3_-gc4A.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E30-8c4A.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E3t-4c4A.ttf"}]}}},{"id":"bitter","label":{"en":"Bitter","it":"Bitter"},"payload":{"typeface":{"name":"Bitter","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfCL8.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8RHYCL8.ttf"}]}}},{"id":"cardo","label":{"en":"Cardo","it":"Cardo"},"payload":{"typeface":{"name":"Cardo","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/cardo/v20/wlp_gwjKBV1pqiv_.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/cardo/v20/wlpygwjKBV1pqhND-aQR.ttf"}]}}},{"id":"dm-sans","label":{"en":"DM Sans","it":"DM Sans"},"payload":{"typeface":{"name":"DM Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/dmsans/v16/rP2tp2ywxg089UriI5-g4vlH9VoD8CmcqZG40F9JadbnoEwAopxhTg.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/dmsans/v16/rP2tp2ywxg089UriI5-g4vlH9VoD8CmcqZG40F9JadbnoEwAkJxhTg.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/dmsans/v16/rP2tp2ywxg089UriI5-g4vlH9VoD8CmcqZG40F9JadbnoEwARZthTg.ttf"}]}}},{"id":"eb-garamond","label":{"en":"EB Garamond","it":"EB Garamond"},"payload":{"typeface":{"name":"EB Garamond","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/ebgaramond/v31/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-6_RUAw.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/ebgaramond/v31/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-2fRUAw.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/ebgaramond/v31/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-NfNUAw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/ebgaramond/v31/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-DPNUAw.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/ebgaramond/v31/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-a_NUAw.ttf"}]}}},{"id":"gentium-book-basic","label":{"en":"Gentium Book Basic","it":"Gentium Book Basic"},"payload":{"typeface":{"name":"Gentium Book Basic","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/gentiumbookbasic/v17/pe0zMJCbPYBVokB1LHA9bbyaQb8ZGjcIVw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/gentiumbookbasic/v17/pe0wMJCbPYBVokB1LHA9bbyaQb8ZGjcw65Rfyw.ttf"}]}}},{"id":"ibm-plex-sans","label":{"en":"IBM Plex Sans","it":"IBM Plex Sans"},"payload":{"typeface":{"name":"IBM Plex Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/ibmplexsans/v22/zYXGKVElMYYaJe8bpLHnCwDKr932-G7dytD-Dmu1swZSAXcomDVmadSD6llzAA.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/ibmplexsans/v22/zYXGKVElMYYaJe8bpLHnCwDKr932-G7dytD-Dmu1swZSAXcomDVmadSD2FlzAA.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/ibmplexsans/v22/zYXGKVElMYYaJe8bpLHnCwDKr932-G7dytD-Dmu1swZSAXcomDVmadSDNF5zAA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/ibmplexsans/v22/zYXGKVElMYYaJe8bpLHnCwDKr932-G7dytD-Dmu1swZSAXcomDVmadSDDV5zAA.ttf"}]}}},{"id":"lora","label":{"en":"Lora","it":"Lora"},"payload":{"typeface":{"name":"Lora","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/lora/v36/0QI6MX1D_JOuGQbT0gvTJPa787weuyJG.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/lora/v36/0QI6MX1D_JOuGQbT0gvTJPa787wsuyJG.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/lora/v36/0QI6MX1D_JOuGQbT0gvTJPa787zAvCJG.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/lora/v36/0QI6MX1D_JOuGQbT0gvTJPa787z5vCJG.ttf"}]}}},{"id":"lustria","label":{"en":"Lustria","it":"Lustria"},"payload":{"typeface":{"name":"Lustria","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/lustria/v13/9oRONYodvDEyjuhOrCg.ttf"}]}}},{"id":"old-standard-tt","label":{"en":"Old Standard TT","it":"Old Standard TT"},"payload":{"typeface":{"name":"Old Standard TT","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/oldstandardtt/v21/MwQubh3o1vLImiwAVvYawgcf2eVurQ.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/oldstandardtt/v21/MwQrbh3o1vLImiwAVvYawgcf2eVWEX-dTA.ttf"}]}}},{"id":"pt-serif","label":{"en":"PT Serif","it":"PT Serif"},"payload":{"typeface":{"name":"PT Serif","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/ptserif/v18/EJRVQgYoZZY2vCFuvDFR.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/ptserif/v18/EJRSQgYoZZY2vCFuvAnt65qV.ttf"}]}}},{"id":"vollkorn","label":{"en":"Vollkorn","it":"Vollkorn"},"payload":{"typeface":{"name":"Vollkorn","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df2MHGuGQ.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df2AnGuGQ.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df27nauGQ.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df213auGQ.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df2sHauGQ.ttf"},{"style":"normal","weight":"heavy","subFamily":"Heavy","uri":"https://fonts.gstatic.com/s/vollkorn/v29/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df2mXauGQ.ttf"}]}}}]')
        }.fonts || [],
        "pt-BR": {
            fonts: JSON.parse('[{"id":"rubik","label":{"en":"Rubik","pt":"Rubik"},"payload":{"typeface":{"name":"Rubik","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4i1UA.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-NYi1UA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-4I-1UA.ttf"}]}}},{"id":"work-sans","label":{"en":"Work Sans","pt":"Work Sans"},"payload":{"typeface":{"name":"Work Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K0nXNig.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K3vXNig.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/worksans/v23/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K67QNig.ttf"}]}}},{"id":"libre-franklin","label":{"en":"Libre Franklin","pt":"Libre Franklin"},"payload":{"typeface":{"name":"Libre Franklin","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhLsWUBw.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhHMWUBw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/librefranklin/v19/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhycKUBw.ttf"}]}}},{"id":"libre-baskerville","label":{"en":"Libre Baskerville","pt":"Libre Baskerville"},"payload":{"typeface":{"name":"Libre Baskerville","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/librebaskerville/v16/kmKnZrc3Hgbbcjq75U4uslyuy4kn0pNe.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/librebaskerville/v16/kmKiZrc3Hgbbcjq75U4uslyuy4kn0qviTjYw.ttf"}]}}},{"id":"crimson-text","label":{"en":"Crimson Text","pt":"Crimson Text"},"payload":{"typeface":{"name":"Crimson Text","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/crimsontext/v19/wlp2gwHKFkZgtmSR3NB0oRJvaA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/crimsontext/v19/wlppgwHKFkZgtmSR3NB0oRJX1C12Cw.ttf"}]}}},{"id":"asap","label":{"en":"Asap","pt":"Asap"},"payload":{"typeface":{"name":"Asap","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYkqUsLg.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYoKUsLg.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYTKIsLg.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/asap/v33/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYdaIsLg.ttf"}]}}},{"id":"barlow","label":{"en":"Barlow","pt":"Barlow"},"payload":{"typeface":{"name":"Barlow","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHpv4kjgoGqM7EPCw.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E3_-gc4A.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E30-8c4A.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/barlow/v12/7cHqv4kjgoGqM7E3t-4c4A.ttf"}]}}},{"id":"bitter","label":{"en":"Bitter","pt":"Bitter"},"payload":{"typeface":{"name":"Bitter","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfCL8.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8RHYCL8.ttf"}]}}},{"id":"cabin","label":{"en":"Cabin","pt":"Cabin"},"payload":{"typeface":{"name":"Cabin","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EL7E.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkW-EL7E.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkYODL7E.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkbqDL7E.ttf"}]}}},{"id":"catamaran","label":{"en":"Catamaran","pt":"Catamaran"},"payload":{"typeface":{"name":"Catamaran","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/catamaran/v27/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPHjd1ak.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/catamaran/v27/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPErd1ak.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/catamaran/v27/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPKba1ak.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/catamaran/v27/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPJ_a1ak.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/catamaran/v27/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPPja1ak.ttf"},{"style":"normal","weight":"heavy","subFamily":"Heavy","uri":"https://fonts.gstatic.com/s/catamaran/v27/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPNHa1ak.ttf"}]}}},{"id":"dm-sans","label":{"en":"DM Sans","pt":"DM Sans"},"payload":{"typeface":{"name":"DM Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/dmsans/v16/rP2tp2ywxg089UriI5-g4vlH9VoD8CmcqZG40F9JadbnoEwAopxhTg.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/dmsans/v16/rP2tp2ywxg089UriI5-g4vlH9VoD8CmcqZG40F9JadbnoEwAkJxhTg.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/dmsans/v16/rP2tp2ywxg089UriI5-g4vlH9VoD8CmcqZG40F9JadbnoEwARZthTg.ttf"}]}}},{"id":"dosis","label":{"en":"Dosis","pt":"Dosis"},"payload":{"typeface":{"name":"Dosis","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/dosis/v33/HhyJU5sn9vOmLxNkIwRSjTVNWLEJN7MV3A.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/dosis/v33/HhyJU5sn9vOmLxNkIwRSjTVNWLEJBbMV3A.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/dosis/v33/HhyJU5sn9vOmLxNkIwRSjTVNWLEJ6bQV3A.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/dosis/v33/HhyJU5sn9vOmLxNkIwRSjTVNWLEJ0LQV3A.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/dosis/v33/HhyJU5sn9vOmLxNkIwRSjTVNWLEJt7QV3A.ttf"}]}}},{"id":"exo-2","label":{"en":"Exo 2","pt":"Exo 2"},"payload":{"typeface":{"name":"Exo 2","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/exo2/v25/7cH1v4okm5zmbvwkAx_sfcEuiD8jvvKcPg.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/exo2/v25/7cH1v4okm5zmbvwkAx_sfcEuiD8jjPKcPg.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/exo2/v25/7cH1v4okm5zmbvwkAx_sfcEuiD8jYPWcPg.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/exo2/v25/7cH1v4okm5zmbvwkAx_sfcEuiD8jWfWcPg.ttf"}]}}},{"id":"hind","label":{"en":"Hind","pt":"Hind"},"payload":{"typeface":{"name":"Hind","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/hind/v17/5aU69_a8oxmIRG4.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/hind/v17/5aU19_a8oxmIfJpbIRs.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/hind/v17/5aU19_a8oxmIfLZcIRs.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/hind/v17/5aU19_a8oxmIfNJdIRs.ttf"}]}}},{"id":"josefin-sans","label":{"en":"Josefin Sans","pt":"Josefin Sans"},"payload":{"typeface":{"name":"Josefin Sans","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/josefinsans/v33/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_DjQXME.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/josefinsans/v33/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_ArQXME.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/josefinsans/v33/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_ObXXME.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/josefinsans/v33/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_N_XXME.ttf"}]}}},{"id":"lora","label":{"en":"Lora","pt":"Lora"},"payload":{"typeface":{"name":"Lora","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/lora/v36/0QI6MX1D_JOuGQbT0gvTJPa787weuyJG.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/lora/v36/0QI6MX1D_JOuGQbT0gvTJPa787wsuyJG.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/lora/v36/0QI6MX1D_JOuGQbT0gvTJPa787zAvCJG.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/lora/v36/0QI6MX1D_JOuGQbT0gvTJPa787z5vCJG.ttf"}]}}},{"id":"maven-pro","label":{"en":"Maven Pro","pt":"Maven Pro"},"payload":{"typeface":{"name":"Maven Pro","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/mavenpro/v39/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8SX25nA.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/mavenpro/v39/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8Rf25nA.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/mavenpro/v39/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8fvx5nA.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/mavenpro/v39/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8cLx5nA.ttf"},{"style":"normal","weight":"extraBold","subFamily":"ExtraBold","uri":"https://fonts.gstatic.com/s/mavenpro/v39/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8aXx5nA.ttf"},{"style":"normal","weight":"heavy","subFamily":"Heavy","uri":"https://fonts.gstatic.com/s/mavenpro/v39/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8Yzx5nA.ttf"}]}}},{"id":"saira","label":{"en":"Saira","pt":"Saira"},"payload":{"typeface":{"name":"Saira","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/saira/v21/memWYa2wxmKQyPMrZX79wwYZQMhsyuShhKMjjbU9uXuA71rCosg.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/saira/v21/memWYa2wxmKQyPMrZX79wwYZQMhsyuShhKMjjbU9uXuA72jCosg.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/saira/v21/memWYa2wxmKQyPMrZX79wwYZQMhsyuShhKMjjbU9uXuA74TFosg.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/saira/v21/memWYa2wxmKQyPMrZX79wwYZQMhsyuShhKMjjbU9uXuA773Fosg.ttf"}]}}},{"id":"titillium-web","label":{"en":"Titillium Web","pt":"Titillium Web"},"payload":{"typeface":{"name":"Titillium Web","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/titilliumweb/v18/NaPecZTIAOhVxoMyOr9n_E7fRMQ.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/titilliumweb/v18/NaPDcZTIAOhVxoMyOr9n_E7ffBzCKIw.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/titilliumweb/v18/NaPDcZTIAOhVxoMyOr9n_E7ffHjDKIw.ttf"}]}}},{"id":"zilla-slab","label":{"en":"Zilla Slab","pt":"Zilla Slab"},"payload":{"typeface":{"name":"Zilla Slab","fonts":[{"style":"normal","weight":"normal","subFamily":"Regular","uri":"https://fonts.gstatic.com/s/zillaslab/v11/dFa6ZfeM_74wlPZtksIFWj0.ttf"},{"style":"normal","weight":"medium","subFamily":"Medium","uri":"https://fonts.gstatic.com/s/zillaslab/v11/dFa5ZfeM_74wlPZtksIFYskZ2HQ.ttf"},{"style":"normal","weight":"semiBold","subFamily":"SemiBold","uri":"https://fonts.gstatic.com/s/zillaslab/v11/dFa5ZfeM_74wlPZtksIFYuUe2HQ.ttf"},{"style":"normal","weight":"bold","subFamily":"Bold","uri":"https://fonts.gstatic.com/s/zillaslab/v11/dFa5ZfeM_74wlPZtksIFYoEf2HQ.ttf"}]}}}]')
        }.fonts || []
    };

function pt(t) {
    const e = function(t) {
            if (ft[t]) return ft[t];
            const e = t.split("-")[0],
                i = Object.keys(ft).find((t => t.startsWith(e)));
            return i ? ft[i] : ft["en-US"]
        }(t),
        i = ft["en-US"] || [],
        a = [],
        s = new Set;
    return e.forEach((t => {
        s.add(t.id)
    })), i.forEach((t => {
        s.has(t.id) || s.add(t.id)
    })), Object.entries(ft).forEach((([e, i]) => {
        e !== t && "en-US" !== e && i.forEach((t => {
            s.has(t.id) || (s.add(t.id), a.push(t))
        }))
    })), [...e, ...i.filter((t => !e.find((e => e.id === t.id)))), ...a]
}
const bt = {
        class: "video-editor-enhanced"
    },
    yt = "custom.typefaces",
    kt = a({
        __name: "VideoEditor",
        props: {
            project: {
                type: Object,
                default: null
            },
            config: {
                type: Object,
                default: () => ({})
            }
        },
        emits: ["ready", "error", "export", "save"],
        setup(e, {
            expose: a,
            emit: p
        }) {
            const b = e,
                k = p,
                w = ["aidrive", "http", "https", "buffer", "bundle"],
                v = t(""),
                {
                    locale: F
                } = s(),
                B = t => ({
                    "en-US": "en",
                    "de-DE": "de",
                    "fr-FR": "fr",
                    "es-ES": "es",
                    "it-IT": "it",
                    "pt-BR": "pt",
                    "zh-CN": "zh",
                    "zh-TW": "zh",
                    "ja-JP": "ja",
                    "ko-KR": "ko"
                }[t] || t.split("-")[0] || "en"),
                I = t(null);
            let A = null;
            const S = o((() => {
                    var t;
                    return (null == (t = b.project) ? void 0 : t.id) || ""
                })),
                {
                    manager: C,
                    tasks: R,
                    stats: j
                } = function(e = {}) {
                    const i = J(e),
                        a = t([]),
                        s = t({ ...i.stats
                        });
                    return i.onQueueUpdate = t => {
                        a.value = t
                    }, i.onStatsUpdate = t => {
                        s.value = t
                    }, {
                        manager: i,
                        tasks: a,
                        stats: s,
                        addFile: (t, e) => i.addFile(t, e),
                        addFileAsync: (t, e) => i.addFileAsync(t, e),
                        addFiles: (t, e) => i.addFiles(t, e),
                        cancelUpload: t => i.cancelUpload(t),
                        retryUpload: t => i.retryUpload(t),
                        getTaskStatus: t => i.getTaskStatus(t),
                        clearCompleted: () => i.clearCompleted(),
                        pauseAll: () => i.pauseAll(),
                        resumeAll: () => i.resumeAll(),
                        clearAll: () => i.clearAll(),
                        renameAiDriveProjectFolder: (t, e) => i.renameAiDriveProjectFolder(t, e)
                    }
                }({
                    maxConcurrent: 3,
                    onQueueUpdate: t => {},
                    onStatsUpdate: t => {}
                });
            n(F, (async t => {
                if (A && A.i18n) {
                    const e = B(t);
                    A.i18n.setLocale(e), A && await _(A)
                }
            })), n(S, (async (t, e) => {
                if (t && A)
                    if (e) await M(t);
                    else {
                        v.value = t, N.setProjectId(t);
                        try {
                            const e = await async function() {
                                const t = (await A.engine.block.findByType("page"))[0],
                                    e = t ? A.engine.block.getWidth(t) : 1920,
                                    i = t ? A.engine.block.getHeight(t) : 1080;
                                return {
                                    width: e,
                                    height: i,
                                    fps: 30,
                                    aspectRatio: `${e}:${i}`
                                }
                            }();
                            await U.createProject({
                                id: t,
                                title: "New Project",
                                ...e
                            });
                            const i = await A.engine.scene.saveToString(w);
                            await U.saveSceneData(t, i), await N.syncPendingAssetsToDatabase(t), k("save", {
                                projectId: t
                            })
                        } catch (i) {}
                    }
            }));
            let x = null;
            async function M(t) {
                try {
                    try {
                        x || (gt || (gt = new ut(e)), x = gt);
                        await x.initialize(b.project), await x.restoreFromCloud()
                    } catch (i) {}
                    const a = await U.getProject(t);
                    if (!a) throw new Error(`Project ${t} not found`);
                    N.setProjectId(t);
                    const s = await U.getLatestSceneData(t);
                    s && s.sceneData ? await A.engine.scene.loadFromString(s.sceneData) : (await A.createVideoScene(), await async function(t, e) {
                        const i = (await A.engine.block.findByType("page"))[0];
                        i && (A.engine.block.setWidth(i, t), A.engine.block.setHeight(i, e))
                    }(a.width || 1920, a.height || 1080)), await async function(t) {
                        t && await N.loadProjectAssets(t)
                    }(t)
                } catch (i) {
                    k("error", i)
                }
                var e
            }
            l((() => {
                A && A._unsubscribers && A._unsubscribers.forEach((t => {
                    "function" == typeof t && t()
                })), A && (A.dispose(), A = null), x && (x.dispose(), x = null), i.off("runActionCompleted", P), i.off("triggerAction", T)
            })), r((async () => {
                try {
                    const {
                        default: t
                    } = await c((async () => {
                            const {
                                default: t
                            } = await
                            import ("./DVRPSamh.js");
                            return {
                                default: t
                            }
                        }), [],
                        import.meta.url), e = {
                        license: "cUxjrxWpe6odOgo4F27B8zs7sUARIsLcCIsmIeYW9XgrMx_r2r9D7UOYjQLRqDjU",
                        userId: `gen-spark-${S.value}`,
                        baseURL: "https://cdn.img.ly/packages/imgly/cesdk-js/1.56.0/assets",
                        role: "Creator",
                        theme: "dark",
                        locale: B(F.value),
                        fallbackFontUri: "https://fonts.gstatic.com/s/notosanssc/v38/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYw.ttf",
                        useSystemFontFallback: !1,
                        i18n: ht,
                        ui: {
                            elements: {
                                view: "default",
                                navigation: {
                                    show: !0,
                                    action: {
                                        export: {
                                            show: !0,
                                            format: ["video/mp4", "image/png", "application/pdf"]
                                        },
                                        save: {
                                            show: !0
                                        }
                                    }
                                },
                                panels: {
                                    settings: {
                                        show: !0
                                    },
                                    timeline: {
                                        show: !0
                                    },
                                    inspector: {
                                        show: !0,
                                        position: "right"
                                    },
                                    assetLibrary: {
                                        show: !0
                                    }
                                }
                            },
                            typefaceLibraries: ["ly.img.typeface", yt]
                        },
                        callbacks: {
                            onExport: async (t, e) => {
                                b.showCustomControls || k("export", {
                                    blobs: t,
                                    options: e
                                })
                            },
                            onUpload: async (t, e, i) => {
                                v.value = v.value || S.value || "assets";
                                try {
                                    const i = await N.addAssetFromFile(t);
                                    let a = i.assetDefinition;
                                    const s = [],
                                        o = C.addFileAsync(t, {
                                            toAiDrive: !0,
                                            metadata: {
                                                assetId: i.assetId,
                                                projectId: v.value
                                            },
                                            onProgress: t => {
                                                e && e(t / 100)
                                            },
                                            onError: e => {
                                                k("error", {
                                                    type: "upload",
                                                    file: t.name,
                                                    error: e
                                                })
                                            }
                                        });
                                    s.push(o);
                                    let n = null;
                                    t.type.startsWith("video/") && i.thumbnailBlob && (n = C.addFileAsync(i.thumbnailBlob, {
                                        toAiDrive: !0,
                                        metadata: {
                                            assetId: `${i.assetId}-thumb`,
                                            projectId: v.value,
                                            isThumbFor: i.assetId
                                        },
                                        fileName: `${t.name.replace(/\.[^/.]+$/,"")}_thumb.jpg`,
                                        onError: t => {}
                                    }), s.push(n));
                                    const l = await Promise.all(s),
                                        r = l[0];
                                    if (r && r.cloudUrl && (await N.updateAssetUrl(i.assetId, r.cloudUrl, !1), a.meta.uri = r.cloudUrl, a.meta.cloudUrl = r.cloudUrl), n && l[1]) {
                                        const t = l[1];
                                        t && t.cloudUrl && (await N.updateAssetThumbnail(i.assetId, t.cloudUrl, !1), a.meta.thumbUri = t.cloudUrl)
                                    }
                                    return P(), e(1), a
                                } catch (a) {
                                    throw a
                                }
                            }
                        },
                        ...b.config
                    }, i = await t.create(I.value, e);
                    A = i, await i.addDefaultAssetSources(), await async function(t) {
                        const e = t.engine;
                        e.asset.addLocalSource("ly.img.image"), e.asset.addLocalSource("ly.img.video"), e.asset.addLocalSource("ly.img.audio"), e.asset.addLocalSource("ly.img.image.upload", {
                            supportedMimeTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"]
                        }), e.asset.addLocalSource("ly.img.video.upload", {
                            supportedMimeTypes: ["video/mp4", "video/webm", "video/quicktime"]
                        }), e.asset.addLocalSource("ly.img.audio.upload", {
                            supportedMimeTypes: ["audio/mpeg", "audio/wav", "audio/webm"]
                        });
                        const i = t.ui.getAssetLibraryEntry("ly.img.image");
                        i && !i.sourceIds.includes("ly.img.image.upload") && t.ui.updateAssetLibraryEntry("ly.img.image", {
                            sourceIds: [...i.sourceIds, "ly.img.image.upload"]
                        });
                        const a = t.ui.getAssetLibraryEntry("ly.img.video");
                        a && !a.sourceIds.includes("ly.img.video.upload") && t.ui.updateAssetLibraryEntry("ly.img.video", {
                            sourceIds: [...a.sourceIds, "ly.img.video.upload"]
                        });
                        const s = t.ui.getAssetLibraryEntry("ly.img.audio");
                        s && !s.sourceIds.includes("ly.img.audio.upload") && t.ui.updateAssetLibraryEntry("ly.img.audio", {
                            sourceIds: [...s.sourceIds, "ly.img.audio.upload"]
                        })
                    }(i), await _(i), await N.initialize(i.engine, S.value);
                    const a = i.engine.scene.onActiveChanged((() => {
                            P()
                        })),
                        s = i.engine.block.onSelectionChanged((() => {
                            P()
                        }));
                    A._unsubscribers = [a, s], S.value ? await M(S.value) : await i.createVideoScene(), k("ready", i), window.videoEditor = i, window.videoDB = U, window.mediaCacheManager = N, window.uploadManager = C, window.getEditorStateAsJson = Z
                } catch (t) {
                    k("error", t)
                }
            })), m((() => {
                A && A._unsubscribers && A._unsubscribers.forEach((t => {
                    "function" == typeof t && t()
                })), A && (A.dispose(), A = null), N.cleanup()
            }));
            const T = async t => {
                try {
                    if (!lt.getInitialized()) {
                        if (!A) return;
                        if (!S.value) return;
                        const t = await U.getProject(S.value);
                        if (!t) throw new Error(`Project ${S.value} not found`);
                        lt.initialize({
                            engine: A.engine,
                            project: t,
                            contextFunc: async () => await E()
                        })
                    }
                    return await lt.runAction(t)
                } catch (e) {
                    return {
                        status: "error",
                        error_message: e.message
                    }
                }
            };
            i.on("triggerAction", T);
            const E = async () => {
                if (!A || !A.engine) return null;
                try {
                    const t = await Z(A.engine),
                        e = [];
                    t.assetLibrary && t.assetLibrary.forEach((t => {
                        t.assets && t.assets.length > 0 && e.push(...t.assets)
                    }));
                    const i = ["ly.img.audio", "ly.img.image", "ly.img.video", "ly.img.audio.upload", "ly.img.image.upload", "ly.img.video.upload"];
                    if (t.assetLibrary = t.assetLibrary.filter((t => i.includes(t.id))), S.value) {
                        const e = await U.getProjectAssets(S.value),
                            i = new Map;
                        e.forEach((t => {
                            i.set(t.id, t)
                        })), t.assetLibrary.forEach((t => {
                            t.assets && t.assets.length > 0 && (t.assets = t.assets.map((t => {
                                const e = i.get(t.id);
                                return e ? { ...t,
                                    ...e,
                                    projectId: e.projectId,
                                    type: e.type,
                                    description: e.description,
                                    ai_drive_path: e.ai_drive_path,
                                    clips: e.clips,
                                    createdAt: e.createdAt,
                                    updatedAt: e.updatedAt
                                } : t
                            })))
                        }))
                    }
                    return {
                        projectId: S.value,
                        editorState: t
                    }
                } catch (t) {
                    return null
                }
            };
            async function _(t) {
                try {
                    const i = pt(F.value);
                    try {
                        await t.engine.asset.removeSource(yt)
                    } catch (e) {}
                    t.engine.asset.addLocalSource(yt);
                    for (const e of i) await t.engine.asset.addAssetToSource(yt, e)
                } catch (i) {}
            }
            const P = y((async () => {
                if (A && S.value) try {
                    const t = await A.engine.scene.saveToString(w);
                    await U.saveSceneData(S.value, t), await U.updateProject(S.value, {
                        updatedAt: Date.now()
                    }), k("save", {
                        projectId: S.value
                    }), x && x.projectId === S.value && await x.saveToCloud(!1)
                } catch (t) {}
            }), 2e3);
            return i.on("runActionCompleted", P), a({
                getContextDetails: E,
                getEditor: () => A,
                getUploadManager: () => C,
                getUploadTasks: () => R.value,
                getUploadStats: () => j.value,
                loadProject: M,
                doAction: T
            }), (t, e) => {
                const i = f;
                return u(), d(i, null, {
                    default: g((() => [h("div", bt, [h("div", {
                        ref_key: "editorContainer",
                        ref: I,
                        class: "editor-container"
                    }, null, 512)])])),
                    _: 1
                })
            }
        }
    }, [
        ["__scopeId", "data-v-d6f1ca32"]
    ]);
export {
    kt as
    default
};