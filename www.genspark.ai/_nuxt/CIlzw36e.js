import {
    d as C,
    b as t,
    o as H
} from "./Cf0SOiw0.js";
const L = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const i = {
        render: function(i, e) {
            return H(), C("svg", L, e[0] || (e[0] = [t("g", {
                id: "Frame"
            }, [t("path", {
                id: "Vector",
                d: "M20.0297 6.76641C20.1703 6.90703 20.25 7.09687 20.25 7.29609V21.75C20.25 22.1648 19.9148 22.5 19.5 22.5H4.5C4.08516 22.5 3.75 22.1648 3.75 21.75V2.25C3.75 1.83516 4.08516 1.5 4.5 1.5H14.4539C14.6531 1.5 14.8453 1.57969 14.9859 1.72031L20.0297 6.76641ZM18.5203 7.64062L14.1094 3.22969V7.64062H18.5203ZM14.8411 14.9358C14.4853 14.9241 14.107 14.9515 13.6774 15.0052C13.1079 14.6538 12.7245 14.1713 12.4521 13.4623L12.4772 13.3596L12.5062 13.2382C12.607 12.8133 12.6612 12.5032 12.6773 12.1905C12.6895 11.9545 12.6764 11.7368 12.6345 11.535C12.5571 11.0993 12.2489 10.8445 11.8605 10.8288C11.4984 10.8141 11.1656 11.0163 11.0805 11.3297C10.942 11.8364 11.0231 12.5032 11.3168 13.6404C10.9427 14.5322 10.4484 15.5777 10.1168 16.1609C9.67406 16.3891 9.3293 16.5968 9.03961 16.827C8.65758 17.1309 8.41898 17.4434 8.35336 17.7715C8.32148 17.9236 8.36953 18.1223 8.47898 18.2852C8.6032 18.4699 8.79023 18.5899 9.01453 18.6073C9.58055 18.6511 10.2762 18.0675 11.0442 16.7496C11.1213 16.7238 11.2029 16.6966 11.3025 16.6629L11.5814 16.5687C11.7579 16.5091 11.8859 16.4665 12.0124 16.4255C12.5609 16.2469 12.9757 16.1341 13.353 16.0699C14.0088 16.421 14.7668 16.6512 15.2773 16.6512C15.6987 16.6512 15.9834 16.4327 16.0863 16.0889C16.1766 15.787 16.1051 15.4369 15.911 15.2433C15.7104 15.0462 15.3415 14.952 14.8411 14.9358ZM9.02883 17.9456V17.9372L9.03187 17.9292C9.06617 17.8406 9.11019 17.756 9.16313 17.677C9.26344 17.5228 9.40148 17.3606 9.57258 17.1879C9.66445 17.0953 9.76008 17.0051 9.87234 16.9038C9.89742 16.8813 10.0577 16.7386 10.0877 16.7105L10.3495 16.4667L10.1592 16.7698C9.87047 17.2301 9.60938 17.5615 9.38578 17.7776C9.30352 17.8573 9.23109 17.9159 9.1725 17.9536C9.15316 17.9665 9.13268 17.9777 9.11133 17.9869C9.10172 17.9909 9.09328 17.9932 9.08484 17.9939C9.07594 17.995 9.06689 17.9938 9.05859 17.9904C9.04977 17.9867 9.04224 17.9805 9.03694 17.9725C9.03165 17.9645 9.02882 17.9552 9.02883 17.9456ZM11.9805 12.8297L11.9276 12.9234L11.8948 12.8208C11.8221 12.5904 11.7687 12.2433 11.7539 11.9302C11.737 11.5739 11.7654 11.3602 11.8779 11.3602C12.0359 11.3602 12.1083 11.6133 12.1139 11.9941C12.1191 12.3288 12.0663 12.6771 11.9803 12.8297H11.9805ZM11.8444 14.1998L11.8802 14.1049L11.9292 14.194C12.2032 14.6918 12.5588 15.1071 12.9497 15.3966L13.0341 15.4589L12.9312 15.48C12.5484 15.5592 12.192 15.6783 11.7045 15.8749C11.7553 15.8543 11.1977 16.0826 11.0566 16.1367L10.9336 16.1838L10.9992 16.0695C11.2887 15.5655 11.5561 14.9604 11.8441 14.1998H11.8444ZM15.5386 15.9872C15.3544 16.0598 14.9578 15.9949 14.2596 15.6968L14.0824 15.6213L14.2746 15.6073C14.8207 15.5667 15.2074 15.5967 15.4329 15.6792C15.529 15.7144 15.593 15.7587 15.6213 15.8093C15.6363 15.8333 15.6413 15.8621 15.6353 15.8897C15.6294 15.9173 15.6129 15.9415 15.5895 15.9572C15.5746 15.9703 15.5573 15.9805 15.5386 15.9872Z",
                fill: "#A42416"
            })], -1)]))
        }
    },
    e = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const n = {
        render: function(L, i) {
            return H(), C("svg", e, i[0] || (i[0] = [t("g", {
                id: "Frame"
            }, [t("path", {
                id: "Vector",
                d: "M20.0297 6.76641C20.1703 6.90703 20.25 7.09687 20.25 7.29609V21.75C20.25 22.1648 19.9148 22.5 19.5 22.5H4.5C4.08516 22.5 3.75 22.1648 3.75 21.75V2.25C3.75 1.83516 4.08516 1.5 4.5 1.5H14.4539C14.6531 1.5 14.8453 1.57969 14.9859 1.72031L20.0297 6.76641ZM18.5203 7.64062L14.1094 3.22969V7.64062H18.5203ZM12 13.268L13.2377 17.8852C13.2537 17.945 13.2891 17.9979 13.3382 18.0356C13.3874 18.0734 13.4476 18.0938 13.5096 18.0938H14.2542C14.3161 18.0938 14.3763 18.0735 14.4255 18.0358C14.4746 17.9982 14.51 17.9454 14.5261 17.8856L16.2691 11.4169C16.2755 11.393 16.2786 11.3684 16.2785 11.3438C16.2785 11.2692 16.2489 11.1976 16.1961 11.1449C16.1434 11.0921 16.0719 11.0625 15.9973 11.0625H15.1636C15.0999 11.0625 15.0382 11.084 14.9883 11.1237C14.9385 11.1633 14.9036 11.2187 14.8894 11.2807L13.8164 15.9471L12.6502 11.2755C12.6349 11.2147 12.5998 11.1607 12.5504 11.1221C12.501 11.0835 12.4401 11.0625 12.3773 11.0625H11.6227C11.5599 11.0625 11.499 11.0835 11.4495 11.1222C11.4001 11.1608 11.365 11.2149 11.3498 11.2758L10.1859 15.9377L9.10547 11.2805C9.09113 11.2186 9.05626 11.1634 9.00656 11.1239C8.95685 11.0843 8.89523 11.0628 8.83172 11.0627H8.00227C7.97765 11.0627 7.95315 11.066 7.92938 11.0723C7.85737 11.0917 7.79602 11.1389 7.75879 11.2035C7.72157 11.2681 7.71152 11.3449 7.73086 11.4169L9.46922 17.8856C9.48529 17.9453 9.52059 17.9981 9.56965 18.0357C9.61871 18.0733 9.6788 18.0937 9.74063 18.0938H10.4904C10.5523 18.0937 10.6125 18.0733 10.6616 18.0356C10.7107 17.9979 10.746 17.945 10.762 17.8852L11.9998 13.268H12Z",
                fill: "#3276CD"
            })], -1)]))
        }
    },
    r = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const o = {
    render: function(L, i) {
        return H(), C("svg", r, i[0] || (i[0] = [t("g", {
            id: "Frame"
        }, [t("g", {
            id: "Group"
        }, [t("path", {
            id: "Vector",
            d: "M13.5 6.75V1.5H8.25C7.65326 1.5 7.08097 1.73705 6.65901 2.15901C6.23705 2.58097 6 3.15326 6 3.75V13.5H11.25C12.293 13.4998 13.3124 13.8104 14.1782 14.3919C15.0439 14.9735 15.7169 15.7997 16.1112 16.7653C16.5055 17.7309 16.6032 18.7921 16.3919 19.8134C16.1807 20.8348 15.67 21.7701 14.925 22.5H18.75C19.3467 22.5 19.919 22.2629 20.341 21.841C20.7629 21.419 21 20.8467 21 20.25V9H15.75C15.1533 9 14.581 8.76295 14.159 8.34099C13.7371 7.91903 13.5 7.34674 13.5 6.75ZM15 6.75V1.875L20.625 7.5H15.75C15.5511 7.5 15.3603 7.42098 15.2197 7.28033C15.079 7.13968 15 6.94891 15 6.75ZM5.25 15C4.25544 15 3.30161 15.3951 2.59835 16.0983C1.89509 16.8016 1.5 17.7554 1.5 18.75C1.5 19.7446 1.89509 20.6984 2.59835 21.4016C3.30161 22.1049 4.25544 22.5 5.25 22.5H6C6.19891 22.5 6.38968 22.421 6.53033 22.2803C6.67098 22.1397 6.75 21.9489 6.75 21.75C6.75 21.5511 6.67098 21.3603 6.53033 21.2197C6.38968 21.079 6.19891 21 6 21H5.25C4.65326 21 4.08097 20.7629 3.65901 20.341C3.23705 19.919 3 19.3467 3 18.75C3 18.1533 3.23705 17.581 3.65901 17.159C4.08097 16.7371 4.65326 16.5 5.25 16.5H6C6.19891 16.5 6.38968 16.421 6.53033 16.2803C6.67098 16.1397 6.75 15.9489 6.75 15.75C6.75 15.5511 6.67098 15.3603 6.53033 15.2197C6.38968 15.079 6.19891 15 6 15H5.25ZM10.5 15C10.3011 15 10.1103 15.079 9.96967 15.2197C9.82902 15.3603 9.75 15.5511 9.75 15.75C9.75 15.9489 9.82902 16.1397 9.96967 16.2803C10.1103 16.421 10.3011 16.5 10.5 16.5H11.25C11.8467 16.5 12.419 16.7371 12.841 17.159C13.2629 17.581 13.5 18.1533 13.5 18.75C13.5 19.3467 13.2629 19.919 12.841 20.341C12.419 20.7629 11.8467 21 11.25 21H10.5C10.3011 21 10.1103 21.079 9.96967 21.2197C9.82902 21.3603 9.75 21.5511 9.75 21.75C9.75 21.9489 9.82902 22.1397 9.96967 22.2803C10.1103 22.421 10.3011 22.5 10.5 22.5H11.25C12.2446 22.5 13.1984 22.1049 13.9017 21.4016C14.6049 20.6984 15 19.7446 15 18.75C15 17.7554 14.6049 16.8016 13.9017 16.0983C13.1984 15.3951 12.2446 15 11.25 15H10.5ZM5.25 18C5.05109 18 4.86032 18.079 4.71967 18.2197C4.57902 18.3603 4.5 18.5511 4.5 18.75C4.5 18.9489 4.57902 19.1397 4.71967 19.2803C4.86032 19.421 5.05109 19.5 5.25 19.5H11.25C11.4489 19.5 11.6397 19.421 11.7803 19.2803C11.921 19.1397 12 18.9489 12 18.75C12 18.5511 11.921 18.3603 11.7803 18.2197C11.6397 18.079 11.4489 18 11.25 18H5.25Z",
            fill: "#3276CD"
        })])], -1)]))
    }
};
export {
    o as F, i as P, n as W
};