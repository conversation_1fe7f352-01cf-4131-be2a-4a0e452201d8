.cover-section[data-v-6973b163],
.podcast-player[data-v-6973b163] {
    height: 100%;
    margin: 0 auto;
    overflow: hidden;
    width: 100%
}

.cover-section[data-v-6973b163] {
    border-radius: 16px;
    max-width: 100%;
    position: relative
}

.cover-container[data-v-6973b163] {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: flex-end;
    width: 100%
}

.cover-container[data-v-6973b163],
.cover-container.with-image[data-v-6973b163] {
    position: relative
}

.cover-image[data-v-6973b163] {
    border-radius: 16px;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.cover-container.no-image[data-v-6973b163] {
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: flex-end;
    min-height: 100%;
    min-width: 100%;
    position: relative
}

.content-overlay[data-v-6973b163] {
    overflow: hidden;
    padding: 40px 32px 32px
}

.content-overlay[data-v-6973b163],
.content-overlay[data-v-6973b163]:before {
    border-radius: 0 0 15px 15px;
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0
}

.content-overlay[data-v-6973b163]:before {
    -webkit-backdrop-filter: blur(9.15px);
    backdrop-filter: blur(9.15px);
    background: linear-gradient(180deg, transparent, rgba(0, 0, 0, .65));
    content: "";
    top: 0
}

.content-overlay[data-v-6973b163]:before,
.no-cover .content-overlay[data-v-6973b163]:before {
    mask: linear-gradient(180deg, transparent, rgba(0, 0, 0, .8) 20%, #000 80%);
    -webkit-mask: linear-gradient(180deg, transparent, rgba(0, 0, 0, .8) 20%, #000 80%)
}

.no-cover .content-overlay[data-v-6973b163]:before {
    background: linear-gradient(180deg, transparent, rgba(0, 0, 0, .4));
    border-radius: 0 0 15px 15px
}

.title-section[data-v-6973b163] {
    margin-bottom: 24px;
    position: relative;
    z-index: 1
}

.podcast-title[data-v-6973b163] {
    color: #fff;
    font-size: 28px;
    font-weight: 700;
    line-height: 1.2;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, .3)
}

.status-indicator[data-v-6973b163] {
    align-items: center;
    display: flex;
    margin-top: 8px;
    min-height: 20px;
    transition: opacity .2s ease
}

.status-text[data-v-6973b163] {
    color: #ffffffe6;
    font-size: 14px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, .3);
    transition: opacity .2s ease
}

.status-text.error[data-v-6973b163] {
    color: #ff6b6b
}

.player-controls[data-v-6973b163] {
    color: #fff;
    position: relative;
    z-index: 1
}

.progress-section[data-v-6973b163] {
    align-items: center;
    display: flex;
    gap: 16px;
    margin-bottom: 24px
}

.time-display[data-v-6973b163] {
    color: #ffffffe6;
    font-size: 14px;
    font-variant-numeric: tabular-nums;
    font-weight: 500;
    min-width: 45px;
    text-align: center
}

.progress-bar-container[data-v-6973b163] {
    cursor: pointer;
    flex: 1;
    padding: 8px 0
}

.progress-bar[data-v-6973b163] {
    background: #ffffff4d;
    border-radius: 2px;
    height: 4px;
    overflow: hidden;
    position: relative
}

.progress-fill[data-v-6973b163] {
    background: #fff;
    border-radius: 2px;
    height: 100%;
    transition: width .15s ease-out
}

.buffering-indicator[data-v-6973b163] {
    align-items: center;
    display: flex;
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%)
}

.buffering-dots[data-v-6973b163] {
    display: flex;
    gap: 3px
}

.buffering-dots span[data-v-6973b163] {
    animation: buffering-pulse-6973b163 1.2s ease-in-out infinite;
    background: #fffc;
    border-radius: 50%;
    height: 4px;
    width: 4px
}

.buffering-dots span[data-v-6973b163]:first-child {
    animation-delay: 0s
}

.buffering-dots span[data-v-6973b163]:nth-child(2) {
    animation-delay: .2s
}

.buffering-dots span[data-v-6973b163]:nth-child(3) {
    animation-delay: .4s
}

@keyframes buffering-pulse-6973b163 {
    0%,
    60%,
    to {
        opacity: .7;
        transform: scale(1)
    }
    30% {
        opacity: 1;
        transform: scale(1.2)
    }
}

.control-buttons[data-v-6973b163] {
    gap: 20px
}

.control-btn[data-v-6973b163],
.control-buttons[data-v-6973b163] {
    align-items: center;
    display: flex;
    justify-content: center
}

.control-btn[data-v-6973b163] {
    background: none;
    border: none;
    border-radius: 50%;
    color: #ffffffe6;
    cursor: pointer;
    padding: 12px;
    position: relative;
    transition: all .2s ease
}

@media (hover:hover) {
    .control-btn[data-v-6973b163]:hover {
        background: #fff3;
        color: #fff;
        transform: scale(1.05)
    }
}

.control-btn[data-v-6973b163]:active {
    transform: scale(.95)
}

.control-btn[data-v-6973b163]:disabled {
    cursor: not-allowed;
    opacity: .6
}

.control-btn.disabled[data-v-6973b163] {
    cursor: not-allowed;
    opacity: .3;
    pointer-events: none
}

@media (hover:hover) {
    .control-btn.disabled[data-v-6973b163]:hover,
    .control-btn[data-v-6973b163]:disabled:hover {
        background: none;
        transform: none
    }
    .control-btn.disabled[data-v-6973b163]:hover {
        color: #ffffff4d
    }
}

.control-btn.disabled[data-v-6973b163]:active,
.control-btn[data-v-6973b163]:disabled:active {
    transform: none
}

.control-icon[data-v-6973b163] {
    height: 30px;
    width: 30px;
    fill: currentColor
}

.loading-spinner[data-v-6973b163] {
    align-items: center;
    display: flex;
    justify-content: center
}

.spinner[data-v-6973b163] {
    animation: spin-6973b163 1s linear infinite
}

@keyframes spin-6973b163 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

@keyframes loading-dash-6973b163 {
    0% {
        stroke-dasharray: 1, 150;
        stroke-dashoffset: 0
    }
    50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -35
    }
    to {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -124
    }
}

.spinner circle[data-v-6973b163] {
    animation: loading-dash-6973b163 1.5s ease-in-out infinite
}

.play-btn[data-v-6973b163] {
    color: #fff;
    margin: 0 16px
}

@media (hover:hover) {
    .play-btn[data-v-6973b163]:hover {
        background: #fff3;
        border-color: #ffffff80;
        transform: scale(1.02)
    }
}

.play-btn .control-icon[data-v-6973b163] {
    height: 50px;
    width: 50px
}

.speed-btn[data-v-6973b163] {
    border-radius: 20px;
    color: #fffc;
    font-size: 14px;
    font-weight: 600;
    height: 40px;
    width: 40px
}

@media (hover:hover) {
    .speed-btn[data-v-6973b163]:hover {
        background: #ffffff40
    }
}

.forward-text[data-v-6973b163],
.rewind-text[data-v-6973b163] {
    font-size: 8px;
    font-weight: 700;
    fill: currentColor
}

@media (max-width:768px) {
    .podcast-player[data-v-6973b163] {
        border-radius: 20px;
        max-width: 100%
    }
    .cover-section[data-v-6973b163] {
        min-height: 300px
    }
    .content-overlay[data-v-6973b163] {
        padding: 32px 24px 24px
    }
    .podcast-title[data-v-6973b163] {
        font-size: 24px
    }
}

@media (max-width:640px) {
    .cover-section[data-v-6973b163] {
        min-height: 300px
    }
    .podcast-title[data-v-6973b163] {
        font-size: 20px
    }
    .status-text[data-v-6973b163] {
        font-size: 12px
    }
    .content-overlay[data-v-6973b163] {
        padding: 28px 20px 20px
    }
    .progress-section[data-v-6973b163] {
        gap: 12px;
        margin-bottom: 20px
    }
    .time-display[data-v-6973b163] {
        font-size: 12px;
        min-width: 40px
    }
    .control-buttons[data-v-6973b163] {
        gap: 5px
    }
    .control-btn[data-v-6973b163] {
        padding: 10px
    }
    .play-btn[data-v-6973b163] {
        margin: 0 12px
    }
}

@media (max-width:480px) {
    .podcast-player[data-v-6973b163] {
        border-radius: 16px
    }
    .cover-section[data-v-6973b163] {
        min-height: 300px
    }
    .podcast-title[data-v-6973b163] {
        font-size: 18px
    }
    .status-text[data-v-6973b163] {
        font-size: 11px
    }
    .content-overlay[data-v-6973b163] {
        padding: 24px 16px 16px
    }
    .control-buttons[data-v-6973b163] {
        gap: 5px
    }
    .play-btn[data-v-6973b163] {
        margin: 0 10px
    }
}