import {
    _ as a,
    d as e,
    o as t,
    b as r,
    F as s,
    k as l,
    r as d,
    f as n,
    t as i,
    h as o,
    q as c,
    a as u,
    ai as m
} from "./Cf0SOiw0.js";
import {
    _ as p
} from "./mJse5lCL.js";
const g = {
        class: "product-display p13"
    },
    v = {
        class: "image-container"
    },
    y = ["src", "alt"];
const h = a({
        name: "ProductTemplate13",
        props: {
            data: {
                type: Object,
                required: !0
            }
        },
        setup(a) {
            const e = d(a.data.imageUrl);
            e.value = e.value;
            return {
                data: a.data,
                imageList: e,
                imageLoadError: a => {
                    e.value = e.value.filter((e => e !== a.target.src))
                }
            }
        }
    }, [
        ["render", function(a, d, n, i, o, c) {
            return t(), e("div", g, [r("div", v, [(t(!0), e(s, null, l(i.imageList.slice(0, 2), ((a, r) => (t(), e("img", {
                onError: d[0] || (d[0] = (...a) => i.imageLoadError && i.imageLoadError(...a)),
                src: a,
                alt: i.data.title,
                class: "product-image"
            }, null, 40, y)))), 256))])])
        }],
        ["__scopeId", "data-v-883a0e36"]
    ]),
    L = {
        class: "product-display p14"
    },
    f = {
        class: "image-container"
    },
    _ = ["src", "alt"];
const E = a({
        name: "ProductTemplate14",
        props: {
            data: {
                type: Object,
                required: !0
            }
        },
        setup(a) {
            const e = d(a.data.imageUrl);
            e.value = e.value;
            let t = 1;
            return {
                imageLoadError: a => {
                    e.value[t] && (a.target.src = e.value[t], t++)
                }
            }
        }
    }, [
        ["render", function(a, s, l, d, n, i) {
            return t(), e("div", L, [r("div", f, [r("img", {
                onError: s[0] || (s[0] = (...a) => d.imageLoadError && d.imageLoadError(...a)),
                src: l.data.imageUrl[0],
                alt: l.data.title,
                class: "product-image"
            }, null, 40, _)])])
        }],
        ["__scopeId", "data-v-a1e16305"]
    ]),
    U = {
        class: "product-display p15"
    },
    k = {
        class: "image-container"
    },
    O = ["src", "alt"];
const b = a({
        name: "ProductTemplate15",
        props: {
            data: {
                type: Object,
                required: !0
            }
        },
        setup(a) {
            const e = d(a.data.imageUrl);
            e.value = e.value;
            let t = 1;
            return {
                imageLoadError: a => {
                    e.value[t] && (a.target.src = e.value[t], t++)
                }
            }
        }
    }, [
        ["render", function(a, s, l, d, n, i) {
            return t(), e("div", U, [r("div", k, [r("img", {
                onError: s[0] || (s[0] = (...a) => d.imageLoadError && d.imageLoadError(...a)),
                src: l.data.imageUrl[0],
                alt: l.data.title,
                class: "product-image"
            }, null, 40, O)])])
        }],
        ["__scopeId", "data-v-827ab02d"]
    ]),
    w = {
        class: "product-display p16"
    },
    I = {
        class: "image-container"
    },
    q = ["src", "alt"],
    j = {
        key: 1,
        class: "image-container-bottom"
    },
    G = ["src", "alt"];
const P = a({
        name: "ProductTemplate16",
        props: {
            data: {
                type: Object,
                required: !0
            }
        },
        setup(a) {
            const e = d(a.data.imageUrl);
            e.value = e.value;
            return {
                imageList: e,
                imageLoadError: a => {
                    e.value = e.value.filter((e => e !== a.target.src))
                }
            }
        }
    }, [
        ["render", function(a, d, i, o, c, u) {
            return t(), e("div", w, [r("div", I, [o.imageList.length > 0 ? (t(), e("img", {
                key: 0,
                onError: d[0] || (d[0] = (...a) => o.imageLoadError && o.imageLoadError(...a)),
                src: o.imageList[0],
                alt: i.data.title,
                class: "product-image-top"
            }, null, 40, q)) : n("", !0), o.imageList.length > 1 ? (t(), e("div", j, [(t(!0), e(s, null, l(o.imageList.slice(1, 3), ((a, r) => (t(), e("img", {
                onError: d[1] || (d[1] = (...a) => o.imageLoadError && o.imageLoadError(...a)),
                src: a,
                alt: i.data.title,
                class: "product-image-bottom"
            }, null, 40, G)))), 256))])) : n("", !0)])])
        }],
        ["__scopeId", "data-v-0bda16dd"]
    ]),
    T = {
        key: 0,
        class: "product-display"
    },
    x = {
        key: 0,
        class: "recommended-reason"
    },
    $ = {
        key: 1,
        class: "image-container"
    },
    C = ["src", "alt"],
    W = {
        key: 2,
        class: "product-info"
    },
    S = {
        class: "product-name"
    };
const D = a({
        name: "ProductTemplate30",
        props: {
            data: {
                type: Object,
                required: !0,
                default: () => ({
                    imageUrl: [],
                    name: "",
                    intro: "",
                    pros: [],
                    cons: []
                })
            }
        },
        setup(a) {
            const e = d(a.data.imageUrl || []);
            let t = 3;
            return {
                imageLoadError: a => {
                    e.value && e.value[t] && (a.target.src = e.value[t], t++)
                }
            }
        }
    }, [
        ["render", function(a, s, l, d, o, c) {
            return l.data ? (t(), e("div", T, [l.data.recommended_reason ? (t(), e("div", x, "👍 " + i(l.data.recommended_reason), 1)) : n("", !0), l.data.imageUrl ? (t(), e("div", $, [r("img", {
                onError: s[0] || (s[0] = (...a) => d.imageLoadError && d.imageLoadError(...a)),
                src: l.data.imageUrl.length > 0 ? l.data.imageUrl[0] : "",
                alt: l.data.name,
                class: "product-image-top"
            }, null, 40, C)])) : n("", !0), l.data.name ? (t(), e("div", W, [r("h2", S, i(l.data.name), 1), s[1] || (s[1] = r("div", {
                class: "product-source"
            }, "From Web", -1))])) : n("", !0)])) : n("", !0)
        }],
        ["__scopeId", "data-v-9f503396"]
    ]),
    H = {
        key: 0,
        class: "product-display",
        ref: "productDisplay"
    },
    N = {
        key: 0,
        class: "gradient-overlay"
    },
    R = {
        key: 1,
        class: "recommended-reason"
    },
    F = {
        key: 2,
        class: "image-container"
    },
    M = ["src", "alt"],
    z = {
        key: 3,
        class: "product-info"
    },
    A = {
        class: "product-name"
    },
    B = {
        class: "pros-cons-container"
    },
    J = {
        class: "pros-container"
    },
    K = {
        class: "cons-container"
    };
const Q = a({
        name: "ProductTemplate31",
        props: {
            data: {
                type: Object,
                required: !0,
                default: () => ({
                    imageUrl: [],
                    name: "",
                    intro: "",
                    pros: [],
                    cons: []
                })
            }
        },
        setup(a) {
            const e = d([...a.data.imageUrl || [], ...a.data.originImageUrl || []]);
            let t = 1;
            const r = d(null),
                s = d(!1);
            return o((() => {
                r.value.offsetHeight >= 1e3 && (s.value = !0)
            })), {
                imageLoadError: a => {
                    e.value && e.value[t] && (a.target.src = e.value[t], t++)
                },
                productDisplay: r,
                showGradient: s
            }
        }
    }, [
        ["render", function(a, d, o, c, u, m) {
            return o.data ? (t(), e("div", H, [c.showGradient ? (t(), e("div", N)) : n("", !0), o.data.recommended_reason ? (t(), e("div", R, "👍 " + i(o.data.recommended_reason), 1)) : n("", !0), o.data.imageUrl ? (t(), e("div", F, [r("img", {
                onError: d[0] || (d[0] = (...a) => c.imageLoadError && c.imageLoadError(...a)),
                src: o.data.imageUrl.length > 0 ? o.data.imageUrl[0] : "",
                alt: o.data.name,
                class: "product-image-top"
            }, null, 40, M)])) : n("", !0), o.data.name ? (t(), e("div", z, [r("h2", A, i(o.data.name), 1), r("div", B, [r("div", J, [d[1] || (d[1] = r("div", {
                class: "pros-title"
            }, "PROS", -1)), d[2] || (d[2] = r("div", {
                class: "pros-line"
            }, null, -1)), r("ul", null, [(t(!0), e(s, null, l(o.data.pros.slice(0, 3), ((a, r) => (t(), e("li", {
                key: `pro-${r}`
            }, i(a), 1)))), 128))])]), r("div", K, [d[3] || (d[3] = r("div", {
                class: "cons-title"
            }, "CONS", -1)), d[4] || (d[4] = r("div", {
                class: "cons-line"
            }, null, -1)), r("ul", null, [(t(!0), e(s, null, l(o.data.cons.slice(0, 2), ((a, r) => (t(), e("li", {
                key: `con-${r}`
            }, i(a), 1)))), 128))])])])])) : n("", !0)], 512)) : n("", !0)
        }],
        ["__scopeId", "data-v-27386b16"]
    ]),
    V = {
        key: 0,
        class: "product-display",
        ref: "productDisplay"
    },
    X = {
        key: 0,
        class: "gradient-overlay"
    },
    Y = {
        key: 1,
        class: "recommended-reason"
    },
    Z = {
        key: 2,
        class: "image-container"
    },
    aa = ["src", "alt"],
    ea = {
        key: 3,
        class: "product-info"
    },
    ta = {
        class: "product-name"
    },
    ra = {
        class: "pros-cons-container",
        ref: "prosConsContainer"
    },
    sa = {
        class: "pros-container"
    },
    la = {
        class: "cons-container"
    };
const da = a({
        name: "ProductTemplate32",
        props: {
            data: {
                type: Object,
                required: !0,
                default: () => ({
                    imageUrl: [],
                    name: "",
                    intro: "",
                    pros: [],
                    cons: []
                })
            }
        },
        setup(a) {
            const e = d([...a.data.imageUrl || [], ...a.data.originImageUrl || []]);
            let t = 1;
            const r = d(null),
                s = d(!1);
            return o((() => {
                r.value.offsetHeight >= 1e3 && (s.value = !0)
            })), {
                imageLoadError: a => {
                    e.value && e.value[t] && (a.target.src = e.value[t], t++)
                },
                productDisplay: r,
                showGradient: s
            }
        }
    }, [
        ["render", function(a, d, o, c, u, m) {
            return o.data ? (t(), e("div", V, [c.showGradient ? (t(), e("div", X)) : n("", !0), o.data.recommended_reason ? (t(), e("div", Y, "👍 " + i(o.data.recommended_reason), 1)) : n("", !0), o.data.imageUrl ? (t(), e("div", Z, [r("img", {
                onError: d[0] || (d[0] = (...a) => c.imageLoadError && c.imageLoadError(...a)),
                src: o.data.imageUrl.length > 0 ? o.data.imageUrl[0] : "",
                alt: o.data.name,
                class: "product-image-top"
            }, null, 40, aa)])) : n("", !0), o.data.name ? (t(), e("div", ea, [r("h2", ta, i(o.data.name), 1), r("div", ra, [r("div", sa, [d[1] || (d[1] = r("div", {
                class: "pros-title"
            }, "PROS", -1)), d[2] || (d[2] = r("div", {
                class: "pros-line"
            }, null, -1)), r("ul", null, [(t(!0), e(s, null, l(o.data.pros.slice(0, 3), ((a, r) => (t(), e("li", {
                key: `pro-${r}`
            }, i(a), 1)))), 128))])]), r("div", la, [d[3] || (d[3] = r("div", {
                class: "cons-title"
            }, "CONS", -1)), d[4] || (d[4] = r("div", {
                class: "cons-line"
            }, null, -1)), r("ul", null, [(t(!0), e(s, null, l(o.data.cons.slice(0, 2), ((a, r) => (t(), e("li", {
                key: `con-${r}`
            }, i(a), 1)))), 128))])])], 512)])) : n("", !0)], 512)) : n("", !0)
        }],
        ["__scopeId", "data-v-84374b95"]
    ]),
    na = {
        key: 0,
        class: "product-display",
        ref: "productDisplay"
    },
    ia = {
        key: 0,
        class: "gradient-overlay"
    },
    oa = {
        key: 1,
        class: "recommended-reason"
    },
    ca = {
        key: 2,
        class: "image-container"
    },
    ua = ["src", "alt"],
    ma = {
        key: 3,
        class: "product-info"
    },
    pa = {
        class: "product-name"
    },
    ga = {
        class: "pros-cons-container",
        ref: "prosConsContainer"
    },
    va = {
        class: "pros-container"
    },
    ya = {
        class: "cons-container"
    };
const ha = a({
        name: "ProductTemplate33",
        props: {
            data: {
                type: Object,
                required: !0,
                default: () => ({
                    imageUrl: [],
                    name: "",
                    intro: "",
                    pros: [],
                    cons: []
                })
            }
        },
        setup(a) {
            const e = d([...a.data.imageUrl || [], ...a.data.originImageUrl || []]);
            let t = 1;
            const r = d(null),
                s = d(!1);
            return o((() => {
                r.value.offsetHeight >= 1e3 && (s.value = !0)
            })), {
                imageLoadError: a => {
                    e.value && e.value[t] && (a.target.src = e.value[t], t++)
                },
                productDisplay: r,
                showGradient: s
            }
        }
    }, [
        ["render", function(a, d, o, c, u, m) {
            return o.data ? (t(), e("div", na, [c.showGradient ? (t(), e("div", ia)) : n("", !0), o.data.recommended_reason ? (t(), e("div", oa, "👍 " + i(o.data.recommended_reason), 1)) : n("", !0), o.data.imageUrl ? (t(), e("div", ca, [r("img", {
                onError: d[0] || (d[0] = (...a) => c.imageLoadError && c.imageLoadError(...a)),
                src: o.data.imageUrl.length > 0 ? o.data.imageUrl[0] : "",
                alt: o.data.name,
                class: "product-image-top"
            }, null, 40, ua)])) : n("", !0), o.data.name ? (t(), e("div", ma, [r("h2", pa, i(o.data.name), 1), r("div", ga, [r("div", va, [d[1] || (d[1] = r("div", {
                class: "pros-title"
            }, "PROS", -1)), d[2] || (d[2] = r("div", {
                class: "pros-line"
            }, null, -1)), r("ul", null, [(t(!0), e(s, null, l(o.data.pros.slice(0, 3), ((a, r) => (t(), e("li", {
                key: `pro-${r}`
            }, i(a), 1)))), 128))])]), r("div", ya, [d[3] || (d[3] = r("div", {
                class: "cons-title"
            }, "CONS", -1)), d[4] || (d[4] = r("div", {
                class: "cons-line"
            }, null, -1)), r("ul", null, [(t(!0), e(s, null, l(o.data.cons.slice(0, 2), ((a, r) => (t(), e("li", {
                key: `con-${r}`
            }, i(a), 1)))), 128))])])], 512)])) : n("", !0)], 512)) : n("", !0)
        }],
        ["__scopeId", "data-v-d76dd20d"]
    ]),
    La = {
        class: "product-display"
    },
    fa = {
        class: "image-container"
    },
    _a = ["src", "alt", "width", "height"];
const Ea = a({
        name: "ProductTemplate40",
        props: {
            data: {
                type: Object,
                required: !0
            }
        },
        setup(a) {}
    }, [
        ["render", function(a, s, l, d, n, i) {
            return t(), e("div", La, [r("div", fa, [r("img", {
                src: l.data.imageUrl[0],
                alt: l.data.title,
                width: l.data.width,
                height: l.data.height
            }, null, 8, _a)])])
        }],
        ["__scopeId", "data-v-3eb74d57"]
    ]),
    Ua = {
        class: "product-display"
    },
    ka = ["src"],
    Oa = {
        key: 0,
        class: "segments-grid"
    },
    ba = ["src", "alt"];
const wa = a({
        name: "ProductTemplate41",
        props: {
            data: {
                type: Object,
                required: !0
            }
        },
        setup: a => ({
            getLevelOneWidth: a => {
                if (a.length > 0) {
                    const e = a.length;
                    return (160 - 5 * (e - 1)) / e
                }
            },
            getLevelTwoWidth: (a, e) => {
                if (a > 0) {
                    const t = Math.ceil(Math.sqrt(a));
                    return (e - 5 * (t - 1)) / t
                }
            },
            currentIndex: (a, e) => {
                let t = 0;
                for (let r = 0; r < e; r++) t += a[r];
                return t
            }
        })
    }, [
        ["render", function(a, d, i, o, u, m) {
            return t(), e("div", Ua, [r("img", {
                src: i.data.srcset[1024],
                class: "image-container"
            }, null, 8, ka), i.data.segments && i.data.segments.length > 0 ? (t(), e("div", Oa, [(t(!0), e(s, null, l(i.data.segmentGrids, ((a, d) => (t(), e("div", {
                key: d,
                class: "segment-level-one-container",
                style: c({
                    width: `${o.getLevelOneWidth(i.data.segmentGrids)}px`,
                    height: `${o.getLevelOneWidth(i.data.segmentGrids)}px`
                })
            }, [(t(!0), e(s, null, l(i.data.segments.slice(o.currentIndex(i.data.segmentGrids, d), o.currentIndex(i.data.segmentGrids, d) + a), ((s, l) => (t(), e("div", {
                key: l,
                class: "segment-level-two-container",
                style: c({
                    width: `${o.getLevelTwoWidth(a,o.getLevelOneWidth(i.data.segmentGrids))}px`,
                    height: `${o.getLevelTwoWidth(a,o.getLevelOneWidth(i.data.segmentGrids))}px`
                })
            }, [r("img", {
                src: s.srcset[208],
                alt: s.item_type,
                class: "segment-image"
            }, null, 8, ba)], 4)))), 128))], 4)))), 128))])) : n("", !0)])
        }],
        ["__scopeId", "data-v-25ae8bce"]
    ]),
    Ia = ["src", "alt"],
    qa = a({
        __name: "product_template_43",
        props: {
            data: {
                type: Object,
                required: !0
            }
        },
        setup(a) {
            const s = a,
                l = d([]),
                n = d(1);
            o((() => {
                l.value = s.data.imageUrl
            }));
            const i = a => {
                l.value[n.value] && (a.target.src = l.value[n.value], n.value++)
            };
            return (s, l) => (t(), e("div", {
                class: "product-display",
                style: c({
                    width: a.data.original_width + "px",
                    height: a.data.original_height + "px"
                })
            }, [r("img", {
                onError: i,
                src: a.data.imageUrl[0],
                alt: a.data.title,
                class: "product-image"
            }, null, 40, Ia)], 4))
        }
    }, [
        ["__scopeId", "data-v-2254e284"]
    ]);
const ja = a({
    props: {
        id: {
            type: String,
            required: !0
        },
        data: {
            type: Object,
            required: !0
        }
    },
    computed: {
        templateComponent() {
            switch (this.id) {
                case "13":
                default:
                    return h;
                case "14":
                    return E;
                case "15":
                    return b;
                case "16":
                    return P;
                case "30":
                    return D;
                case "31":
                    return Q;
                case "32":
                    return da;
                case "33":
                    return ha;
                case "40":
                    return Ea;
                case "41":
                    return wa;
                case "42":
                    return p;
                case "43":
                    return qa
            }
        }
    }
}, [
    ["render", function(a, e, r, s, l, d) {
        return t(), u(m(d.templateComponent), {
            data: r.data
        }, null, 8, ["data"])
    }]
]);
export {
    ja as _
};