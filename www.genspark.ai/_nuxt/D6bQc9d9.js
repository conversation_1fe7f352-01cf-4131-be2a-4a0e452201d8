import {
    _ as a,
    d as c,
    b as e,
    f as s,
    o as i
} from "./Cf0SOiw0.js";
const l = {
        name: "LoadingAnimation",
        props: {
            hideSecondCircle: {
                type: Boolean,
                default: !1
            }
        }
    },
    n = {
        class: "loading-animation"
    },
    o = {
        key: 0,
        class: "circle"
    };
const d = a(l, [
    ["render", function(a, l, d, r, t, m) {
        return i(), c("div", n, [l[1] || (l[1] = e("div", {
            class: "circle"
        }, [e("div", {
            class: "circle-small"
        })], -1)), d.hideSecondCircle ? s("", !0) : (i(), c("div", o, l[0] || (l[0] = [e("div", {
            class: "circle-large"
        }, null, -1)])))])
    }],
    ["__scopeId", "data-v-aecb818b"]
]);
export {
    d as _
};