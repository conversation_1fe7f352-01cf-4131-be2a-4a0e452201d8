import {
    N as e,
    p as s
} from "./CW991W2w.js";
import {
    J as o,
    X as r,
    Y as t,
    Z as i,
    $ as p,
    r as a,
    c as n
} from "./Cf0SOiw0.js";
const l = o({
    name: "Tooltip",
    props: Object.assign(Object.assign({}, s), i.props),
    slots: Object,
    __popover__: !0,
    setup(e) {
        const {
            mergedClsPrefixRef: s
        } = t(e), o = i("Tooltip", "-tooltip", void 0, p, e, s), r = a(null), l = {
            syncPosition() {
                r.value.syncPosition()
            },
            setShow(e) {
                r.value.setShow(e)
            }
        };
        return Object.assign(Object.assign({}, l), {
            popoverRef: r,
            mergedTheme: o,
            popoverThemeOverrides: n((() => o.value.self))
        })
    },
    render() {
        const {
            mergedTheme: s,
            internalExtraClass: o
        } = this;
        return r(e, Object.assign(Object.assign({}, this.$props), {
            theme: s.peers.Popover,
            themeOverrides: s.peerOverrides.Popover,
            builtinThemeOverrides: this.popoverThemeOverrides,
            internalExtraClass: o.concat("tooltip"),
            ref: "popoverRef"
        }), this.$slots)
    }
});
export {
    l as N
};