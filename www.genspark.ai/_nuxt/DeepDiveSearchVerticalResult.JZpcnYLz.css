.dot-wrapper[data-v-6bd45b9a] {
    align-items: center;
    bottom: 20px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
    position: absolute;
    width: 100%
}

.dot[data-v-6bd45b9a] {
    background: #bbbcbd;
    border-radius: 50%;
    cursor: pointer;
    height: 9px;
    width: 9px
}

.dot.active[data-v-6bd45b9a] {
    background: #0f7fff
}

.dot[data-v-6bd45b9a]:hover {
    background: #0f7fff7f
}

.wrapper[data-v-6bd45b9a] {
    background: #fafafa;
    display: block;
    max-height: 300px;
    min-height: 100px;
    overflow: hidden;
    position: relative;
    width: 100%
}

.arrow[data-v-6bd45b9a],
.wrapper[data-v-6bd45b9a] {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.arrow[data-v-6bd45b9a] {
    align-items: center;
    background: #23242533;
    border-radius: 50%;
    color: #fff;
    cursor: pointer;
    display: flex;
    height: 40px;
    justify-content: center;
    margin-top: -20px;
    position: absolute;
    top: 50%;
    width: 40px;
    z-index: 1
}

.arrow[data-v-6bd45b9a]:hover {
    background: #232425
}

.arrow.left[data-v-6bd45b9a] {
    left: 10px
}

.arrow.right[data-v-6bd45b9a] {
    right: 10px
}

.image[data-v-6bd45b9a] {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    left: 0;
    overflow: hidden;
    position: absolute;
    top: 0;
    transition: all .3s;
    width: 100%
}

@media (hover:hover) {
    .wrapper:hover .image .source-wrapper[data-v-6bd45b9a] {
        display: flex
    }
}

@media (hover:none) {
    .wrapper .image .source-wrapper[data-v-6bd45b9a] {
        display: flex
    }
    .dot-wrapper[data-v-6bd45b9a] {
        display: none
    }
}

.image .source-wrapper[data-v-6bd45b9a] {
    align-items: center;
    background: linear-gradient(0deg, rgba(0, 0, 0, .5), transparent 60px, transparent);
    bottom: 0;
    display: flex;
    display: none;
    flex-direction: row;
    gap: 5px;
    height: 100%;
    height: 60px;
    justify-content: flex-start;
    position: absolute;
    width: 100%
}

.image .source-wrapper .source[data-v-6bd45b9a] {
    background: #0009;
    border-radius: 28px;
    color: #efefef;
    cursor: pointer;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 5px;
    line-height: 18px;
    margin-left: 20px;
    padding: 4px 12px;
    text-decoration: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    z-index: 10
}

.image .source-wrapper .icon[data-v-6bd45b9a],
.image .source-wrapper .source[data-v-6bd45b9a] {
    align-items: center;
    display: flex;
    justify-content: center
}

.image img[data-v-6bd45b9a] {
    width: 100%
}

.image img[data-v-6bd45b9a],
.image.first img[data-v-6bd45b9a] {
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain
}

.image.first img[data-v-6bd45b9a] {
    width: unset
}

.magnifier-animation[data-v-50c10e26] {
    align-items: center;
    display: flex;
    height: 12px;
    justify-content: center;
    position: relative;
    width: 12px
}

.magnifier-icon[data-v-50c10e26] {
    animation: moveAround-50c10e26 1s infinite;
    height: 100%;
    width: 100%
}

@keyframes moveAround-50c10e26 {
    0%,
    to {
        transform: translate(0)
    }
    25% {
        transform: translate(2px)
    }
    50% {
        transform: translate(2px, 2px)
    }
    75% {
        transform: translateY(2px)
    }
}

.popover[data-v-c5f7ce9e] {
    max-width: 300px
}

.partner-data-container[data-v-c5f7ce9e] {
    align-items: center;
    background-color: #e3ebff;
    border-radius: 10px;
    display: flex;
    gap: 4px;
    height: 20px;
    justify-content: center;
    padding: 0 8px
}

.partner-data-text[data-v-c5f7ce9e] {
    font-size: 11px;
    font-weight: 400;
    line-height: 15px;
    white-space: nowrap;
    width: 100%
}

@media (prefers-color-scheme:dark) {
    .partner-data-text[data-v-c5f7ce9e] {
        color: #232425
    }
}

input[type=checkbox][data-v-3c11b75f] {
    flex-shrink: 0;
    height: 16px;
    width: 16px
}

.create_result[data-v-3c11b75f] {
    background: #fff;
    border-radius: 16px;
    box-sizing: border-box;
    color: #232425;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    max-width: calc(100% - 40px);
    padding: 30px 20px 20px;
    text-align: center;
    width: 540px
}

.create_result .content[data-v-3c11b75f] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%
}

.create_result .bold[data-v-3c11b75f] {
    font-weight: 700
}

.create_result .buttons[data-v-3c11b75f] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 36px;
    width: 100%
}

.create_result .button[data-v-3c11b75f] {
    background: #f5f5f5;
    border-radius: 12px;
    box-sizing: border-box;
    cursor: pointer;
    padding: 10px 20px;
    width: 100%
}

.create_result .button.default[data-v-3c11b75f] {
    background: #000;
    color: #fff
}

.cross_check_error_message[data-v-3c11b75f] {
    color: #ff3d3d;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: -20px;
    padding: 6px 21px 4px
}

.cross-check-inner .error textarea[data-v-3c11b75f] {
    border: 1px solid #ff3d3d
}

.expand1-leave-active[data-v-3c11b75f] {
    height: var(--cross-check-inner-height);
    transition: all .5s
}

.expand1-leave-to[data-v-3c11b75f] {
    height: 0;
    opacity: 0
}

.expand1-leave-to[data-v-3c11b75f],
.expand2-enter-active[data-v-3c11b75f] {
    transition: all .5s
}

.expand2-enter-from[data-v-3c11b75f] {
    opacity: 0;
    transition: all .5s
}

.cross-check-wrapper[data-v-3c11b75f] {
    min-height: 40px;
    position: relative;
    width: 100%
}

.cross-check[data-v-3c11b75f] {
    align-items: center;
    background: #efefef;
    border-radius: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start
}

.cross-check .expand-wrapper[data-v-3c11b75f] {
    align-items: center;
    color: #232425;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    gap: 6px;
    justify-content: center;
    line-height: normal;
    padding: 8px 0;
    width: 100%
}

.cross-check.expandable[data-v-3c11b75f] {
    position: absolute;
    width: 100%;
    z-index: 1
}

.cross-check .icon[data-v-3c11b75f] {
    align-items: center;
    display: flex;
    flex-direction: row;
    height: 24px;
    justify-content: center;
    width: 24px
}

.cross-check-inner[data-v-3c11b75f] {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 8px 16px 16px;
    width: 100%
}

.cross-check-inner .header[data-v-3c11b75f] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 8px 0
}

.cross-check-inner .header .left[data-v-3c11b75f] {
    color: #000;
    font-size: 18px;
    font-weight: 700;
    gap: 10px
}

.cross-check-inner .header .left[data-v-3c11b75f],
.cross-check-inner .header .left .button[data-v-3c11b75f] {
    align-items: center;
    display: flex;
    flex-direction: row;
    font-style: normal;
    line-height: normal
}

.cross-check-inner .header .left .button[data-v-3c11b75f] {
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 6px;
    color: #232425;
    cursor: pointer;
    font-size: 12px;
    font-weight: 400;
    gap: 6px
}

.cross-check-inner .header .left .button .reverse[data-v-3c11b75f] {
    transform: rotate(180deg)
}

.cross-check-inner .header .right[data-v-3c11b75f] {
    cursor: pointer
}

.cross-check-inner textarea[data-v-3c11b75f] {
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 12px;
    box-sizing: border-box;
    height: 66px;
    margin: 20px 0;
    min-height: 66px;
    outline: none;
    padding: 11px 21px;
    resize: none;
    width: 100%
}

.cross-check-inner textarea[data-v-3c11b75f]::-moz-placeholder {
    color: #23242566
}

.cross-check-inner textarea[data-v-3c11b75f]::placeholder {
    color: #23242566
}

.cross_check_statement[data-v-3c11b75f] {
    border-bottom: 1px solid #dedede;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    padding: 24px 0
}

.cross_check_statement label[data-v-3c11b75f] {
    display: flex;
    flex-direction: row;
    gap: 27px
}

.cross_check_begin_cross_check[data-v-3c11b75f] {
    align-items: center;
    background: #232425;
    border: 1px solid #efefef;
    border-radius: 12px;
    display: flex;
    flex-shrink: 0;
    gap: 6px;
    justify-content: center
}

.cross_check_begin_cross_check .button[data-v-3c11b75f] {
    align-items: center;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    justify-content: center;
    line-height: normal;
    padding: 12px 16px;
    width: 100%
}

.cross_check_begin_cross_check.disabled[data-v-3c11b75f] {
    opacity: .45
}

.cross_check_begin_cross_check.disabled .button[data-v-3c11b75f] {
    cursor: not-allowed
}

.cross_check_begin_cross_check .button .text[data-v-3c11b75f] {
    align-items: center;
    display: flex;
    justify-content: center;
    position: relative
}

.cross_check_begin_cross_check .button .icon[data-v-3c11b75f] {
    display: none
}

.cross_check_begin_cross_check .button .icon.spin[data-v-3c11b75f] {
    align-items: center;
    animation: spin-3c11b75f 2s linear infinite;
    display: flex;
    height: 24px;
    justify-content: center;
    left: -28px;
    position: absolute;
    top: -4px;
    width: 24px
}

.cross_check_parsing_statement[data-v-3c11b75f] {
    color: #0c66cc;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-weight: 400;
    gap: 7px;
    line-height: 21px;
    padding-top: 10px
}

.cross_check_parsing_statement .anim[data-v-3c11b75f] {
    align-items: center;
    display: flex;
    justify-content: center
}

@keyframes spin-3c11b75f {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.menu[data-v-3c11b75f] {
    display: flex;
    flex-direction: column
}

.menu .icon[data-v-3c11b75f] {
    justify-content: center
}

.menu .button[data-v-3c11b75f],
.menu .icon[data-v-3c11b75f] {
    align-items: center;
    display: flex
}

.menu .button[data-v-3c11b75f] {
    cursor: pointer;
    flex-direction: row;
    gap: 6px;
    padding: 8px 16px
}

@media (hover:hover) {
    .menu .button[data-v-3c11b75f]:hover {
        background: #f5f5f5
    }
}

@media (prefers-color-scheme:dark) {
    .cross-check[data-v-3c11b75f] {
        background-color: #333
    }
    .cross-check-inner .header .left[data-v-3c11b75f] {
        color: #eee
    }
    .cross-check-inner .header .left .button[data-v-3c11b75f] {
        background-color: #444;
        color: #eee
    }
    @media (hover:hover) {
        .menu .button[data-v-3c11b75f]:hover {
            background-color: #555
        }
    }
    .cross-check-inner textarea[data-v-3c11b75f] {
        background-color: #333;
        color: #eee
    }
    .cross-check-inner textarea[data-v-3c11b75f]::-moz-placeholder {
        color: #999
    }
    .cross-check-inner textarea[data-v-3c11b75f]::placeholder {
        color: #999
    }
    .cross-check .expand-wrapper[data-v-3c11b75f] {
        color: #eee
    }
    .create_result[data-v-3c11b75f] {
        background: #444;
        color: #fff
    }
    .create_result .button[data-v-3c11b75f] {
        background: #232425
    }
    .create_result .button.default[data-v-3c11b75f] {
        background: #fff;
        color: #000
    }
}

.status_top_bar[data-v-f301fd12] {
    background: #fafafa;
    border-radius: 12px;
    box-sizing: border-box;
    cursor: pointer;
    gap: 12px;
    height: 41px;
    padding: 10px 12px;
    width: 100%;
    width: -moz-fit-content;
    width: fit-content
}

.status[data-v-f301fd12],
.status_top_bar[data-v-f301fd12] {
    align-items: center;
    display: flex
}

.status[data-v-f301fd12] {
    gap: 6px
}

.status[data-v-f301fd12] svg {
    height: 12px;
    width: 12px
}

.status[data-v-f301fd12] svg circle {
    fill: #5cd4a1
}

.status[data-v-f301fd12] svg path {
    stroke: #fff
}

.status-indicator[data-v-f301fd12] {
    border-radius: 50%;
    height: 8px;
    width: 8px
}

.loading .status-indicator[data-v-f301fd12] {
    animation: pulse-f301fd12 1.5s infinite;
    background: #0f7fff
}

.finished .status-indicator[data-v-f301fd12] {
    animation: scaleIn-f301fd12 .5s ease-in-out;
    background: #4caf50
}

.status-text[data-v-f301fd12] {
    color: #666;
    font-size: 14px
}

.action[data-v-f301fd12] {
    color: #232425;
    font-size: 14px;
    font-weight: 700
}

.message[data-v-f301fd12] {
    color: #666;
    font-size: 14px
}

.icon-array[data-v-f301fd12] {
    align-items: center;
    display: flex;
    gap: 4px
}

.message-icon[data-v-f301fd12] {
    height: 16px;
    width: 16px
}

@keyframes pulse-f301fd12 {
    0% {
        opacity: 1;
        transform: scale(1)
    }
    50% {
        opacity: .5;
        transform: scale(1.2)
    }
    to {
        opacity: 1;
        transform: scale(1)
    }
}

@keyframes scaleIn-f301fd12 {
    0% {
        transform: scale(.5)
    }
    to {
        transform: scale(1)
    }
}

.favicon-container[data-v-f301fd12] {
    min-width: 0;
    overflow: hidden
}

.favicon-list[data-v-f301fd12] {
    align-items: center;
    display: flex;
    gap: 0;
    overflow: hidden;
    width: 100%
}

.favicon-icon[data-v-f301fd12] {
    border: 2px solid #fafafa;
    border-radius: 50%;
    flex-shrink: 0;
    height: 20px;
    margin-left: -8px;
    -o-object-fit: cover;
    object-fit: cover;
    width: 20px
}

.favicon-icon[data-v-f301fd12]:first-child {
    margin-left: 0
}

.action[data-v-f301fd12],
.message[data-v-f301fd12] {
    flex-shrink: 0
}

@media (prefers-color-scheme:dark) {
    .status_top_bar[data-v-f301fd12] {
        background: #232425
    }
    .action[data-v-f301fd12] {
        color: #fff
    }
    .message[data-v-f301fd12] {
        color: #999
    }
    .favicon-icon[data-v-f301fd12] {
        border-color: #232425
    }
}

.carousel-container[data-v-9e559b37] {
    position: relative;
    width: 100%
}

.carousel-wrapper[data-v-9e559b37] {
    margin-top: 12px;
    overflow: hidden;
    position: relative
}

.carousel[data-v-9e559b37] {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.carousel[data-v-9e559b37]::-webkit-scrollbar {
    display: none
}

.sources-overview[data-v-9e559b37] {
    background: #fafafa;
    border-radius: 12px;
    cursor: pointer;
    flex-shrink: 0;
    gap: 8px;
    height: 86px;
    width: 134px
}

.sources-overview[data-v-9e559b37],
.sources-overview-content[data-v-9e559b37] {
    align-items: flex-start;
    display: flex;
    flex-direction: column
}

.sources-overview-content[data-v-9e559b37] {
    gap: 4px;
    padding: 12px
}

.sources-count[data-v-9e559b37] {
    color: #232425;
    font-family: Arial;
    font-size: 14px;
    font-weight: 700;
    line-height: 21px;
    text-overflow: ellipsis
}

.card[data-v-9e559b37] {
    background: #fafafa;
    border-radius: 12px;
    flex-direction: column;
    flex-shrink: 0;
    gap: 8px;
    height: 86px;
    width: 246px
}

.card[data-v-9e559b37],
.card-content[data-v-9e559b37] {
    align-items: flex-start;
    display: flex
}

.card-content[data-v-9e559b37] {
    color: inherit;
    gap: 12px;
    height: 100%;
    text-decoration: none;
    width: 100%
}

.icon-wrapper[data-v-9e559b37] {
    align-items: center;
    border-radius: 30px;
    display: flex;
    flex-shrink: 0;
    height: 36px;
    padding-left: 12px;
    padding-top: 12px;
    width: 36px
}

.source-icon[data-v-9e559b37] {
    background-color: #fff;
    border-radius: 50%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100%
}

.card-info[data-v-9e559b37] {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    gap: 4px;
    justify-content: center;
    margin-right: 12px;
    margin-top: 12px;
    overflow: hidden
}

.card-title[data-v-9e559b37] {
    color: var(--title, #232425);
    display: -webkit-box;
    font-family: Arial;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical
}

.card-link[data-v-9e559b37],
.card-title[data-v-9e559b37] {
    overflow: hidden;
    text-overflow: ellipsis
}

.card-link[data-v-9e559b37] {
    color: #666;
    display: block;
    font-size: 12px;
    padding: 0;
    white-space: nowrap
}

.nav-button[data-v-9e559b37] {
    align-items: center;
    background: #fff;
    border: 1px solid #eee;
    border-radius: 50%;
    box-shadow: 0 2px 4px #0000001a;
    cursor: pointer;
    display: flex;
    height: 36px;
    justify-content: center;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 36px
}

.nav-button.next[data-v-9e559b37] {
    right: 12px
}

.nav-button.prev[data-v-9e559b37] {
    left: 12px
}

.sources-icons[data-v-9e559b37] {
    height: 20px;
    position: relative;
    width: 100%
}

.overview-icon[data-v-9e559b37] {
    background: #fff;
    border: 1px solid #eee;
    border-radius: 50%;
    height: 20px;
    -o-object-fit: contain;
    object-fit: contain;
    position: absolute;
    width: 20px
}

.skeleton[data-v-9e559b37] {
    animation: pulse-9e559b37 1.5s infinite;
    background: #f5f5f5
}

.skeleton-card-content[data-v-9e559b37] {
    height: 100%;
    width: 100%
}

@keyframes pulse-9e559b37 {
    0% {
        background: #f5f5f5
    }
    50% {
        background: #ebebeb
    }
    to {
        background: #f5f5f5
    }
}

.nav-icon[data-v-9e559b37] {
    height: 20px;
    width: 20px
}

@media (prefers-color-scheme:dark) {
    .card[data-v-9e559b37] {
        background: #333
    }
    .card-title[data-v-9e559b37] {
        color: #eee
    }
    .card-link[data-v-9e559b37] {
        color: #909499
    }
    .sources-overview[data-v-9e559b37] {
        background: #232425
    }
    .sources-count[data-v-9e559b37] {
        color: #eee
    }
    .nav-button[data-v-9e559b37],
    .overview-icon[data-v-9e559b37] {
        background: #333;
        border-color: #444
    }
    .skeleton[data-v-9e559b37] {
        animation: pulse-dark-9e559b37 1.5s infinite;
        background: #333
    }
    @keyframes pulse-dark-9e559b37 {
        0% {
            background: #333
        }
        50% {
            background: #3a3a3a
        }
        to {
            background: #333
        }
    }
}

.recommendation-bar[data-v-3323d2dc] {
    background-color: #f5f5f5;
    border-radius: 12px;
    margin: 20px 0;
    overflow: hidden;
    padding: 15px;
    position: relative
}

@media (prefers-color-scheme:dark) {
    .recommendation-bar[data-v-3323d2dc] {
        background-color: #333
    }
}

.recommendation-header[data-v-3323d2dc] {
    align-items: center;
    display: flex;
    gap: 10px;
    padding: 10px
}

.recommendation-header img[data-v-3323d2dc] {
    border-radius: 50%;
    height: 20px;
    width: 20px
}

.carousel-container[data-v-3323d2dc] {
    align-items: center;
    display: flex;
    position: relative
}

.carousel[data-v-3323d2dc] {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
    align-items: flex-start;
    padding: 10px 0 20px;
    width: 100%
}

.carousel[data-v-3323d2dc]::-webkit-scrollbar {
    display: none
}

.item-card[data-v-3323d2dc] {
    background-color: transparent;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    flex: 0 0 auto;
    flex-direction: column;
    height: 300px;
    overflow: visible;
    padding: 8px;
    position: relative;
    transition: width .4s cubic-bezier(.175, .885, .32, 1.275), box-shadow .3s ease, background-color .3s ease;
    width: 160px;
    z-index: 1
}

.item-card[data-v-3323d2dc]:hover {
    background-color: #fffffff2;
    box-shadow: 0 10px 20px #0003;
    padding-bottom: 8px;
    transform: none;
    width: 280px;
    z-index: 10
}

@media (prefers-color-scheme:dark) {
    .item-card[data-v-3323d2dc]:hover {
        background-color: #000000f2
    }
}

.poster-container[data-v-3323d2dc] {
    border-radius: 8px;
    box-shadow: 0 4px 8px #0000001a;
    flex-shrink: 0;
    height: 220px;
    overflow: hidden;
    position: relative;
    transition: height .4s cubic-bezier(.175, .885, .32, 1.275), box-shadow .3s ease;
    width: 100%
}

.item-card:hover .poster-container[data-v-3323d2dc] {
    box-shadow: 0 8px 16px #0003;
    height: 157.5px
}

.poster-image[data-v-3323d2dc] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    transition: all .4s ease;
    width: 100%
}

.item-card:hover .poster-image[data-v-3323d2dc] {
    transform: none
}

.bookmark-icon[data-v-3323d2dc] {
    position: absolute;
    right: 8px;
    top: 8px;
    z-index: 2
}

.item-info[data-v-3323d2dc] {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    justify-content: flex-start;
    margin-top: 10px;
    min-height: 45px;
    overflow: hidden;
    position: relative;
    transition: all .3s ease;
    width: 100%
}

.item-card:hover .item-info[data-v-3323d2dc] {
    margin-top: 10px;
    min-height: 45px;
    transform: none
}

.item-title[data-v-3323d2dc] {
    color: #333;
    display: -webkit-box;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.3;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: color .3s ease, font-weight .3s ease, height .3s ease;
    white-space: normal;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    height: 2.6em;
    padding: 0 2px
}

@media (prefers-color-scheme:dark) {
    .item-title[data-v-3323d2dc] {
        color: #aaa
    }
}

.item-card:hover .item-title[data-v-3323d2dc] {
    color: #06c;
    font-weight: 700;
    max-height: 80px;
    overflow: visible;
    text-overflow: clip;
    white-space: normal;
    word-wrap: break-word;
    display: block;
    font-size: 15px;
    line-height: 1.4;
    overflow-y: auto;
    padding: 0 4px;
    scrollbar-color: #ccc transparent;
    scrollbar-width: thin;
    -webkit-line-clamp: unset;
    height: auto
}

.item-card:hover .item-title[data-v-3323d2dc]::-webkit-scrollbar {
    width: 4px
}

.item-card:hover .item-title[data-v-3323d2dc]::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 4px
}

.item-card:hover .item-title[data-v-3323d2dc]::-webkit-scrollbar-track {
    background-color: transparent
}

.item-year[data-v-3323d2dc] {
    color: #666;
    font-size: 12px;
    margin: 4px 0 0
}

.nav-button[data-v-3323d2dc] {
    align-items: center;
    background: #ffffffe6;
    border: none;
    border-radius: 50%;
    box-shadow: 0 2px 6px #0000001a;
    cursor: pointer;
    display: flex;
    height: 40px;
    justify-content: center;
    position: absolute;
    transition: all .3s ease;
    width: 40px;
    z-index: 2
}

.nav-button[data-v-3323d2dc]:hover {
    background: #fff;
    box-shadow: 0 4px 12px #00000026
}

.nav-button.prev[data-v-3323d2dc] {
    left: -20px
}

.nav-button.next[data-v-3323d2dc] {
    right: -20px
}

.nav-button[data-v-3323d2dc]:disabled {
    cursor: not-allowed;
    opacity: .5
}

.hover-overlay[data-v-3323d2dc] {
    align-items: center;
    background-color: #00000080;
    display: flex;
    height: 100%;
    justify-content: center;
    left: 0;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    transition: opacity .4s ease;
    width: 100%
}

.item-card:hover .hover-overlay[data-v-3323d2dc] {
    opacity: 1
}

.view-details[data-v-3323d2dc] {
    border: 2px solid #fff;
    border-radius: 20px;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 16px;
    transition: all .2s ease
}

.view-details[data-v-3323d2dc]:hover {
    background-color: #fff;
    color: #333
}

.item-index[data-v-3323d2dc] {
    align-items: center;
    background-color: #000000b3;
    border-radius: 50%;
    color: #fff;
    display: flex;
    font-size: 12px;
    font-weight: 700;
    height: 24px;
    justify-content: center;
    position: absolute;
    right: 8px;
    top: 8px;
    width: 24px;
    z-index: 2
}

@media (max-width:768px) {
    .recommendation-bar[data-v-3323d2dc] {
        padding: 0
    }
    .item-card[data-v-3323d2dc] {
        height: 270px;
        width: 140px
    }
    .poster-container[data-v-3323d2dc] {
        height: 190px
    }
    .item-card[data-v-3323d2dc]:hover {
        padding-bottom: 8px;
        width: 240px
    }
    .item-card:hover .poster-container[data-v-3323d2dc] {
        height: 135px
    }
    .item-card:hover .item-title[data-v-3323d2dc] {
        font-size: 14px;
        max-height: 70px
    }
}

.sources-container[data-v-bf5cbdfa] {
    display: flex;
    flex-direction: column;
    gap: 1rem
}

.source-card[data-v-bf5cbdfa] {
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: .5rem;
    padding: 1rem
}

.source-header[data-v-bf5cbdfa] {
    align-items: center;
    display: flex;
    gap: .5rem;
    margin-bottom: .5rem
}

.source-icon[data-v-bf5cbdfa] {
    height: 20px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 20px
}

.source-url[data-v-bf5cbdfa] {
    color: #666;
    font-size: .875rem;
    text-decoration: none
}

.source-title[data-v-bf5cbdfa] {
    color: #1a73e8;
    font-size: 1.125rem;
    margin: .5rem 0
}

.source-description[data-v-bf5cbdfa] {
    color: #333;
    font-size: .875rem;
    line-height: 1.5;
    margin: 0
}

.share-preview[data-v-7410584f] {
    align-items: flex-start;
    display: flex;
    justify-content: center;
    width: 400px
}

.preview-container[data-v-7410584f] {
    background: #fafafa;
    border-radius: 16px;
    box-shadow: 0 5px 20px #0000000d;
    overflow: hidden
}

.content-wrapper[data-v-7410584f],
.preview-container[data-v-7410584f] {
    align-items: center;
    display: flex;
    flex-direction: column
}

.content-wrapper[data-v-7410584f] {
    background: #fff;
    box-sizing: border-box;
    gap: 16px;
    max-width: 100%;
    min-height: 200px;
    padding: 70px 20px 20px;
    position: relative
}

.share-header[data-v-7410584f] {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%
}

.share-header h2[data-v-7410584f] {
    font-size: 26px;
    font-weight: 700;
    line-height: 1.5em;
    margin: 0;
    text-align: left
}

.divider-container[data-v-7410584f] {
    align-items: center;
    display: flex;
    justify-content: flex-start;
    margin: 4px 0;
    width: 100%
}

.divider[data-v-7410584f] {
    background: #232425;
    height: 4px;
    width: 26px
}

.ai-answer-tag-container[data-v-7410584f] {
    align-items: center;
    display: flex;
    justify-content: flex-start;
    width: 100%
}

.ai-answer-tag[data-v-7410584f] {
    color: #a8adb2;
    font-size: 12px;
    font-weight: 700;
    line-height: 1.5em;
    text-transform: uppercase
}

.mind-map-wrapper[data-v-7410584f] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    overflow: hidden;
    position: relative;
    width: 100%
}

.mind-map-title[data-v-7410584f] {
    border-radius: 8px;
    left: 16px;
    position: absolute;
    top: 16px;
    z-index: 1
}

.logo-container[data-v-7410584f],
.mind-map-title[data-v-7410584f] {
    color: #232425;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5em
}

.logo-container[data-v-7410584f] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin: 40px 0 60px
}

.logo-icon[data-v-7410584f] {
    height: 30px
}

.deep_dive_answer[data-v-7410584f] {
    max-width: 100%
}

.deep_dive_answer .markdown-viewer[data-v-7410584f] {
    display: flex;
    flex-direction: column;
    gap: 16px
}

.deep_dive_answer[data-v-7410584f] h1 {
    color: #232425;
    font-size: 21px;
    font-weight: 700;
    line-height: 1.5em
}

.deep_dive_answer[data-v-7410584f] h2 {
    color: #232425;
    font-size: 18px;
    font-weight: 700;
    line-height: 1.5em
}

.deep_dive_answer[data-v-7410584f] h3,
.deep_dive_answer[data-v-7410584f] h4 {
    color: #232425;
    font-size: 16px;
    font-weight: 700;
    line-height: 1.5em
}

.deep_dive_answer[data-v-7410584f] p {
    color: #232425;
    font-size: 16px;
    line-height: 1.5em
}

.deep_dive_answer[data-v-7410584f] ol,
.deep_dive_answer[data-v-7410584f] ul {
    display: flex;
    flex-direction: column;
    gap: 8px;
    list-style-position: inside
}

.deep_dive_answer[data-v-7410584f] li {
    font-size: 16px;
    line-height: 1.5em;
    list-style-type: disc;
    margin-left: 15px;
    padding-left: 10px
}

.deep_dive_answer[data-v-7410584f] table {
    border-collapse: collapse;
    width: 100%
}

.deep_dive_answer[data-v-7410584f] img {
    width: 100%
}

.deep_dive_answer[data-v-7410584f] th {
    background-color: #f5f5f5;
    border: 1px solid #efefef;
    color: #232425;
    font-weight: 700;
    padding: 10px;
    text-align: left
}

.deep_dive_answer[data-v-7410584f] td {
    border: 1px solid #efefef;
    color: #606366;
    padding: 10px;
    text-align: left
}

.deep_dive_answer[data-v-7410584f] tr:nth-child(2n) {
    background-color: #fafafa
}

.deep_dive_answer[data-v-7410584f] tr:hover {
    background-color: #f2f2f2
}

.deep_dive_answer[data-v-7410584f] pre code {
    white-space: pre-wrap;
    word-break: break-word
}

.deep_dive_answer.preview[data-v-7410584f] h1,
.deep_dive_answer.preview[data-v-7410584f] h2,
.deep_dive_answer.preview[data-v-7410584f] h3,
.deep_dive_answer.preview[data-v-7410584f] h4,
.deep_dive_answer.preview[data-v-7410584f] li,
.deep_dive_answer.preview[data-v-7410584f] ol,
.deep_dive_answer.preview[data-v-7410584f] p,
.deep_dive_answer.preview[data-v-7410584f] ul {
    font-size: 14px;
    line-height: 21px
}

.generating-image .deep_dive_answer .markdown-viewer[data-v-7410584f] {
    gap: 16px !important
}

.generating-image .deep_dive_answer[data-v-7410584f] h1,
.generating-image .deep_dive_answer[data-v-7410584f] h2,
.generating-image .deep_dive_answer[data-v-7410584f] h3,
.generating-image .deep_dive_answer[data-v-7410584f] h4,
.generating-image .deep_dive_answer[data-v-7410584f] ol,
.generating-image .deep_dive_answer[data-v-7410584f] p,
.generating-image .deep_dive_answer[data-v-7410584f] ul {
    margin-bottom: 0 !important;
    margin-top: 0 !important
}

@media (prefers-color-scheme:dark) {
    .preview-container[data-v-7410584f] {
        background: #1f1f1f
    }
    .content-wrapper[data-v-7410584f] {
        background: #1a1a1a
    }
    .share-header h2[data-v-7410584f] {
        color: #fff
    }
    .divider[data-v-7410584f] {
        background: #fff
    }
    .mind-map-wrapper[data-v-7410584f] {
        border-color: #333
    }
    .logo-container[data-v-7410584f],
    .mind-map-title[data-v-7410584f],
    .deep_dive_answer[data-v-7410584f] h1,
    .deep_dive_answer[data-v-7410584f] h2,
    .deep_dive_answer[data-v-7410584f] h3,
    .deep_dive_answer[data-v-7410584f] h4,
    .deep_dive_answer[data-v-7410584f] p {
        color: #fff
    }
    .deep_dive_answer[data-v-7410584f] table {
        border-color: #333
    }
    .deep_dive_answer[data-v-7410584f] th {
        background-color: #1c1c1c;
        border-color: #333;
        color: #fff
    }
    .deep_dive_answer[data-v-7410584f] td {
        border-color: #333;
        color: #a8adb2
    }
    .deep_dive_answer[data-v-7410584f] tr:nth-child(2n) {
        background-color: #2a2a2a
    }
    .deep_dive_answer[data-v-7410584f] tr:hover {
        background-color: #333
    }
}

.modal-container[data-v-43894f41] {
    overflow: hidden;
    z-index: 999
}

.modal-container[data-v-43894f41],
.overlay[data-v-43894f41] {
    height: 100vh;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%
}

.image-display[data-v-43894f41] {
    border-radius: 16px;
    display: flex;
    height: calc(100vh - 40px);
    left: 80px;
    overflow: hidden;
    position: fixed;
    top: 20px;
    width: calc(100% - 160px);
    z-index: 1000
}

@media (max-width:1220px) {
    .image-display[data-v-43894f41] {
        align-items: center;
        background-color: #fff;
        border-radius: 0;
        display: flex;
        flex-direction: column;
        height: 100%;
        left: 0;
        position: relative;
        top: 0;
        width: 100%
    }
}

.image-left[data-v-43894f41] {
    align-items: flex-start;
    background-color: #f5f5f5;
    box-sizing: border-box;
    display: flex;
    flex: 1;
    height: 100%;
    justify-content: center;
    min-width: 0;
    overflow-y: auto;
    padding: 20px
}

@media (max-width:1220px) {
    .image-left[data-v-43894f41] {
        height: calc(100% - 170px);
        padding: 100px 0 250px;
        width: 100%
    }
}

.share-preview-container[data-v-43894f41] {
    transition: all .3s ease
}

.image-right[data-v-43894f41] {
    align-items: center;
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-width: 320px;
    padding: 60px 24px;
    width: 30%;
    z-index: 10
}

@media (max-width:1220px) {
    .image-right[data-v-43894f41] {
        background-color: #fff;
        bottom: 0;
        flex: 1;
        gap: 20px;
        padding: 24px 16px 120px;
        position: absolute;
        width: 100%
    }
}

.close-button-wrapper[data-v-43894f41] {
    position: fixed;
    right: 20px;
    top: 20px;
    z-index: 1001
}

@media (max-width:1220px) {
    .close-button-wrapper[data-v-43894f41] {
        display: none;
        right: 10px;
        top: 10px
    }
}

.close-button-wrapper .icon[data-v-43894f41] {
    cursor: pointer;
    height: 32px;
    transition: all .3s ease;
    width: 32px
}

.close-button-wrapper .icon[data-v-43894f41]:hover {
    transform: scale(1.1)
}

.mobile-title-bar[data-v-43894f41] {
    display: none
}

@media (max-width:1220px) {
    .mobile-title-bar[data-v-43894f41] {
        align-items: center;
        background-color: #fff;
        box-sizing: border-box;
        display: flex;
        font-weight: 700;
        height: 50px;
        justify-content: center;
        position: absolute;
        top: 0;
        width: 100%;
        z-index: 10
    }
}

.back-button[data-v-43894f41] {
    align-items: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    left: 16px;
    position: absolute
}

.back-button[data-v-43894f41],
.back-button .icon[data-v-43894f41] {
    height: 20px;
    width: 20px
}

.share-options[data-v-43894f41] {
    flex-shrink: 0;
    gap: 6px
}

.share-options[data-v-43894f41],
.share-options-wrapper[data-v-43894f41] {
    display: flex;
    flex-direction: column;
    width: 100%
}

.share-options-wrapper[data-v-43894f41] {
    align-items: flex-start
}

@media (max-width:1220px) {
    .share-options-wrapper[data-v-43894f41] {
        overflow-x: auto;
        width: 100%;
        -webkit-overflow-scrolling: touch
    }
}

.download-image[data-v-43894f41] {
    align-items: center;
    background-color: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    font-weight: 700;
    gap: 6px;
    justify-content: center;
    line-height: 1.5em;
    padding: 10px;
    transition: all .2s;
    width: 100%
}

.download-image[data-v-43894f41]:hover {
    background-color: #333
}

@media (max-width:1220px) {
    .share-options[data-v-43894f41] {
        flex-direction: row;
        justify-content: flex-start;
        width: 100%
    }
}

.share-header[data-v-43894f41] {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%
}

.share-header h2[data-v-43894f41] {
    font-size: 26px;
    font-weight: 700;
    line-height: 1.5em;
    margin: 0;
    text-align: left
}

.share-url[data-v-43894f41] {
    align-items: flex-start;
    border-radius: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
    padding: 10px 20px;
    width: 100%
}

.share-url[data-v-43894f41]:hover {
    background-color: #f5f5f5;
    cursor: pointer
}

@media (max-width:1220px) {
    .share-url[data-v-43894f41] {
        align-items: center;
        justify-content: flex-start;
        padding: 10px 0;
        width: -moz-fit-content;
        width: fit-content
    }
}

.url-text[data-v-43894f41] {
    color: #666;
    font-size: 14px;
    word-break: break-all
}

.copy-button[data-v-43894f41] {
    align-items: center;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    font-weight: 400;
    gap: 16px;
    justify-content: center;
    line-height: 1.5em;
    transition: all .2s
}

@media (max-width:1220px) {
    .copy-button[data-v-43894f41] {
        flex-direction: column;
        text-align: center;
        width: 80px
    }
}

.preview-image[data-v-43894f41] {
    display: block;
    height: auto;
    margin: 0 auto;
    max-height: none;
    width: 375px
}

.markdown-container[data-v-43894f41] {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px #0000001a;
    margin-top: 16px;
    padding: 16px;
    width: 100%
}

.markdown-container.hidden[data-v-43894f41] {
    opacity: 0;
    pointer-events: none;
    position: absolute
}

.loading-spinner[data-v-43894f41] {
    align-items: center;
    display: flex;
    justify-content: center;
    min-height: 200px
}

.spinner[data-v-43894f41] {
    animation: spin-43894f41 1s linear infinite;
    border: 2px solid #f3f3f3;
    border-radius: 50%;
    border-top-color: #3498db;
    height: 16px;
    width: 16px
}

@keyframes spin-43894f41 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.loading-container[data-v-43894f41] {
    align-items: center;
    background: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 400px;
    justify-content: center;
    width: 100%
}

.loading-text[data-v-43894f41] {
    color: #666;
    font-size: 14px
}

.divider-container[data-v-43894f41] {
    align-items: center;
    display: flex;
    justify-content: flex-start;
    margin: 4px 0;
    width: 100%
}

.divider[data-v-43894f41] {
    background: #232425;
    height: 4px;
    width: 26px
}

.ai-answer-tag-container[data-v-43894f41] {
    align-items: center;
    display: flex;
    justify-content: flex-start;
    width: 100%
}

.ai-answer-tag[data-v-43894f41] {
    color: #a8adb2;
    font-size: 12px;
    font-weight: 700;
    line-height: 1.5em;
    text-transform: uppercase;
    width: -moz-fit-content;
    width: fit-content
}

.mind-map-wrapper[data-v-43894f41] {
    margin-top: 16px;
    overflow: hidden;
    position: relative;
    width: 100%
}

.mind-map-title[data-v-43894f41],
.mind-map-wrapper[data-v-43894f41] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    display: flex;
    justify-content: center
}

.mind-map-title[data-v-43894f41] {
    color: #232425;
    font-size: 14px;
    font-weight: 400;
    gap: 6px;
    left: 16px;
    line-height: 1.5em;
    padding: 4px 8px;
    position: absolute;
    top: 16px;
    z-index: 1
}

.logo-container[data-v-43894f41] {
    margin: 40px 0 60px
}

.logo-icon[data-v-43894f41] {
    height: 30px
}

.spinner-wrapper[data-v-43894f41] {
    align-items: center;
    background-color: #232425;
    border-radius: 50%;
    display: flex;
    height: 32px;
    justify-content: center;
    width: 32px
}

@media (prefers-color-scheme:dark) {
    .image-display[data-v-43894f41] {
        background-color: #1a1a1a
    }
    .image-left[data-v-43894f41] {
        background-color: #242424
    }
    .image-right[data-v-43894f41],
    .mobile-title-bar[data-v-43894f41] {
        background-color: #1a1a1a
    }
    .mobile-title-bar[data-v-43894f41] {
        color: #eee
    }
    .share-url[data-v-43894f41] {
        color: #fff
    }
    .share-url[data-v-43894f41]:hover {
        background-color: #2c2c2c
    }
    .url-text[data-v-43894f41] {
        color: #a0a0a0
    }
    .copy-button[data-v-43894f41] {
        color: #fff
    }
    .loading-container[data-v-43894f41] {
        background-color: #1a1a1a
    }
    .loading-text[data-v-43894f41] {
        color: #a0a0a0
    }
    .divider[data-v-43894f41] {
        background: #fff
    }
    .ai-answer-tag[data-v-43894f41] {
        color: #888
    }
    .mind-map-title[data-v-43894f41] {
        background-color: #1a1a1a;
        color: #fff
    }
    .mind-map-title[data-v-43894f41],
    .mind-map-wrapper[data-v-43894f41] {
        border-color: #333
    }
}

.copy-image-button[data-v-43894f41] {
    align-items: center;
    background-color: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    font-weight: 700;
    gap: 6px;
    justify-content: center;
    line-height: 1.5em;
    margin-bottom: 10px;
    padding: 10px;
    transition: all .2s;
    width: 100%
}

.copy-image-button[data-v-43894f41]:hover {
    background-color: #333
}

@media (prefers-color-scheme:dark) {
    .copy-image-button[data-v-43894f41] {
        background-color: #232425
    }
    .copy-image-button[data-v-43894f41]:hover {
        background-color: #333
    }
}

.result-with-sidebar-wrapper[data-v-3f3b11ec] {
    display: flex;
    gap: 20px;
    position: relative
}

.main-content[data-v-3f3b11ec] {
    flex: 1;
    min-width: 0
}

.search-sidebar[data-v-3f3b11ec] {
    background: #fff;
    border-left: 1px solid #eee;
    flex-shrink: 0;
    height: 100vh;
    overflow-y: auto;
    position: sticky;
    top: 0;
    width: 400px;
    z-index: 10
}

.slide-sidebar-enter-active[data-v-3f3b11ec],
.slide-sidebar-leave-active[data-v-3f3b11ec] {
    transition: transform .3s ease
}

.slide-sidebar-enter-from[data-v-3f3b11ec],
.slide-sidebar-leave-to[data-v-3f3b11ec] {
    transform: translate(100%)
}

@media (max-width:1200px) {
    .search-sidebar[data-v-3f3b11ec] {
        bottom: 0;
        box-shadow: -2px 0 8px #0000001a;
        position: fixed;
        right: 0;
        top: 0
    }
}

.deep_dive_result_container[data-v-3f3b11ec] {
    display: flex;
    flex-direction: column;
    gap: 16px
}

.deep-dive-thinking[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    gap: 8px;
    height: 21px;
    justify-content: flex-start
}

.deep-dive-thinking .loading_icon[data-v-3f3b11ec] {
    display: flex
}

.deep-dive-thinking-text[data-v-3f3b11ec] {
    color: #0f7fff;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px
}

.loaded-progress-bar[data-v-3f3b11ec],
.loading-progress-bar[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 16px 7% 40px
}

.progress-circle-container[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    position: relative;
    width: 100%
}

.step[data-v-3f3b11ec] {
    display: flex;
    justify-content: center;
    position: relative
}

.circle-container[data-v-3f3b11ec] {
    flex-shrink: 0;
    height: 28px;
    position: relative;
    width: 28px
}

.step-number[data-v-3f3b11ec] {
    font-size: 14px;
    left: 0;
    top: 0;
    z-index: 3
}

.active-circle[data-v-3f3b11ec],
.step-number[data-v-3f3b11ec] {
    align-items: center;
    color: #fff;
    display: flex;
    height: 28px;
    justify-content: center;
    position: absolute;
    width: 28px
}

.active-circle[data-v-3f3b11ec] {
    animation: scaleIn-3f3b11ec .5s ease-in-out;
    background-color: #0f7fff;
    border-radius: 50%;
    z-index: 2
}

@keyframes scaleIn-3f3b11ec {
    0% {
        transform: scale(.5)
    }
    to {
        transform: scale(1)
    }
}

.circle[data-v-3f3b11ec] {
    align-items: center;
    background-color: #eee;
    border-radius: 50%;
    color: #fff;
    display: flex;
    height: 28px;
    justify-content: center;
    width: 28px;
    z-index: 1
}

.circle[data-v-3f3b11ec],
.label[data-v-3f3b11ec] {
    position: absolute
}

.label[data-v-3f3b11ec] {
    color: #eee;
    font-size: 12px;
    height: 21px;
    left: 50%;
    line-height: 1.5em;
    margin-top: 8px;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    top: 100%;
    transform: translate(-50%);
    white-space: nowrap;
    width: 240px
}

.active.label[data-v-3f3b11ec] {
    color: #0f7fff
}

.bar-container[data-v-3f3b11ec] {
    flex-grow: 1;
    height: 1px;
    margin: 0 14px;
    position: relative
}

.bar[data-v-3f3b11ec] {
    background-color: #eee;
    height: 100%
}

.active-bar[data-v-3f3b11ec] {
    background-color: #0f7fff;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    transition: width .5s linear
}

.deep_dive_answer .answer-divider[data-v-3f3b11ec] {
    margin: 24px 0
}

.answer-divider[data-v-3f3b11ec] hr,
.markdown-viewer[data-v-3f3b11ec] hr {
    border: 1px solid #efefef;
    margin: 24px 0
}

.deep_dive_answer .markdown-viewer[data-v-3f3b11ec] .digital-citation-link {
    align-items: center;
    background: #eee;
    border-radius: 18px;
    color: #666;
    display: inline-flex;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    height: 18px;
    justify-content: center;
    line-height: 21px;
    margin-right: 4px;
    text-decoration: none;
    width: 18px
}

.deep_dive_answer .markdown-viewer[data-v-3f3b11ec] .digital-citation-link:hover {
    background: #0c66cc;
    color: #fff
}

.skeleton-source[data-v-3f3b11ec] {
    border-radius: 5px;
    margin-top: 16px;
    width: 60%
}

.skeleton-image-row[data-v-3f3b11ec] {
    display: flex;
    gap: 8px;
    margin: 16px 0
}

.skeleton-image[data-v-3f3b11ec] {
    border-radius: 8px;
    flex-grow: 1;
    height: 84px
}

.img[data-v-3f3b11ec] {
    aspect-ratio: 2.86;
    -o-object-fit: auto;
    object-fit: auto;
    -o-object-position: center;
    object-position: center;
    width: 100%
}

@media (max-width:100%) {
    .img[data-v-3f3b11ec] {
        max-width: 100%
    }
}

.deep_dive_container[data-v-3f3b11ec] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.deep_dive_title[data-v-3f3b11ec] {
    color: #232425;
    font-size: 20px;
    font-weight: 700;
    line-height: 1.5em;
    text-decoration: none
}

.deep_dive_title a[data-v-3f3b11ec] {
    color: inherit;
    cursor: pointer;
    text-decoration: none
}

.deep_dive_answer_preview[data-v-3f3b11ec] {
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative
}

.deep_dive_answer_preview.seeMore[data-v-3f3b11ec] {
    max-height: none
}

.deep_dive_answer_preview .more[data-v-3f3b11ec],
.gradient-overlay[data-v-3f3b11ec] {
    bottom: 0;
    position: absolute
}

.gradient-overlay[data-v-3f3b11ec] {
    background: linear-gradient(180deg, #fff0, #fff);
    height: 100%;
    left: 0;
    pointer-events: none;
    width: 100%
}

.deep_dive_answer .markdown-viewer[data-v-3f3b11ec] {
    display: flex;
    flex-direction: column;
    gap: 16px
}

.deep_dive_answer[data-v-3f3b11ec] h1 {
    color: #232425;
    font-size: 21px;
    font-weight: 700;
    line-height: 1.5em
}

.deep_dive_answer[data-v-3f3b11ec] h2 {
    color: #232425;
    font-size: 18px;
    font-weight: 700;
    line-height: 1.5em
}

.deep_dive_answer[data-v-3f3b11ec] h3,
.deep_dive_answer[data-v-3f3b11ec] h4 {
    color: #232425;
    font-size: 16px;
    font-weight: 700;
    line-height: 1.5em
}

.deep_dive_answer[data-v-3f3b11ec] p {
    color: #232425;
    font-size: 16px;
    line-height: 1.5em
}

.deep_dive_answer[data-v-3f3b11ec] ol,
.deep_dive_answer[data-v-3f3b11ec] ul {
    display: flex;
    flex-direction: column;
    gap: 8px;
    list-style-position: inside
}

.deep_dive_answer[data-v-3f3b11ec] li {
    font-size: 16px;
    line-height: 1.5em;
    list-style-type: disc;
    margin-left: 15px;
    padding-left: 10px
}

.deep_dive_answer[data-v-3f3b11ec] table {
    border-collapse: collapse;
    width: 100%
}

.deep_dive_answer[data-v-3f3b11ec] img {
    width: 100%
}

.deep_dive_answer[data-v-3f3b11ec] th {
    background-color: #f5f5f5;
    border: 1px solid #efefef;
    color: #232425;
    font-weight: 700;
    padding: 10px;
    text-align: left
}

.deep_dive_answer[data-v-3f3b11ec] td {
    border: 1px solid #efefef;
    color: #606366;
    padding: 10px;
    text-align: left
}

.deep_dive_answer[data-v-3f3b11ec] tr:nth-child(2n) {
    background-color: #fafafa
}

.deep_dive_answer[data-v-3f3b11ec] tr:hover {
    background-color: #f2f2f2
}

.deep_dive_answer.preview[data-v-3f3b11ec] h1,
.deep_dive_answer.preview[data-v-3f3b11ec] h2,
.deep_dive_answer.preview[data-v-3f3b11ec] h3,
.deep_dive_answer.preview[data-v-3f3b11ec] h4,
.deep_dive_answer.preview[data-v-3f3b11ec] li,
.deep_dive_answer.preview[data-v-3f3b11ec] ol,
.deep_dive_answer.preview[data-v-3f3b11ec] p,
.deep_dive_answer.preview[data-v-3f3b11ec] ul {
    font-size: 14px;
    line-height: 21px
}

.page-source[data-v-3f3b11ec] {
    align-items: center;
    color: #999;
    display: flex;
    font-size: 14px;
    gap: 4px;
    line-height: 18px
}

.shield-on-hover[data-v-3f3b11ec] {
    display: none;
    left: 0;
    margin-bottom: 90px;
    opacity: 0;
    position: absolute;
    transition: opacity .3s ease-in-out;
    width: 308px;
    z-index: 1000
}

.shield-on-hover[data-show=true][data-v-3f3b11ec] {
    display: block;
    opacity: 1;
    transition: opacity .3s ease-in-out
}

.shield-on-hover-inner[data-v-3f3b11ec] {
    background-color: #031933;
    border-radius: 8px;
    color: #fff;
    font-size: 12px;
    line-height: 16px;
    margin-left: -12px;
    padding: 10px;
    width: 308px
}

.shield-on-hover-triangle[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    margin-left: 2px;
    margin-top: -7px;
    width: 16px
}

.shield-on-hover-triangle.search-plan[data-v-3f3b11ec] {
    margin-left: 14px;
    margin-top: -12px
}

.hover-content[data-v-3f3b11ec] {
    display: none;
    left: 0;
    position: absolute;
    top: 100%;
    width: -moz-max-content;
    width: max-content;
    z-index: 1000
}

.hover-content-inner[data-v-3f3b11ec] {
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 4px 10px #00000026;
    font-size: 12px;
    line-height: 22px;
    margin-top: 4px;
    max-height: 437px;
    overflow-y: auto;
    padding: 10px
}

.hover-content-inner-favicon[data-v-3f3b11ec] {
    height: 16px;
    margin-right: 8px;
    width: 16px
}

.hover-content ul[data-v-3f3b11ec] {
    list-style: none;
    margin: 0;
    padding: 0
}

.hover-content li[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    margin-bottom: 12px
}

.hover-content a[data-v-3f3b11ec] {
    color: #0c66cc;
    text-decoration: none
}

.hover-content a[data-v-3f3b11ec]:active,
.hover-content a[data-v-3f3b11ec]:hover {
    color: #0c66cc;
    font-weight: 700
}

.hover-content[data-show=true][data-v-3f3b11ec] {
    display: block
}

.hover-target[data-v-3f3b11ec] {
    cursor: pointer;
    position: relative;
    text-decoration: underline
}

.hover-target[data-v-3f3b11ec]:hover {
    color: #0c66cc
}

.hover-target:hover .hover-content[data-v-3f3b11ec] {
    font-weight: 400
}

.deep_dive_media[data-v-3f3b11ec] {
    margin-top: 16px
}

.page-title[data-v-3f3b11ec] {
    color: #0c66cc;
    font-weight: 700;
    font: 700 20px/30px Arial, sans-serif;
    width: 100%
}

.more[data-v-3f3b11ec] {
    align-items: center;
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 39px;
    color: #666;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    height: 40px;
    justify-content: center;
    line-height: normal;
    margin-top: 16px;
    width: 100%;
    z-index: 10
}

.more img[data-v-3f3b11ec] {
    margin-left: 4px;
    width: 14px
}

.divider[data-v-3f3b11ec] {
    background-color: #efefef;
    height: 1px;
    margin-bottom: 14px;
    margin-top: 14px;
    width: 100%
}

.people-also-ask[data-v-3f3b11ec] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative
}

.ask-query[data-v-3f3b11ec] {
    background-color: #efefef;
    background-position: right 10px center;
    background-repeat: no-repeat;
    border-radius: 8px 2px 8px 8px;
    color: #232425;
    cursor: pointer;
    display: inline-flex;
    font-size: 16px;
    line-height: 1.5em;
    padding: 3px 10px;
    text-decoration: none;
    transition: all .3s ease
}

.ask-query[data-v-3f3b11ec]:hover {
    background-color: #ddd
}

.ask-query .icon[data-v-3f3b11ec] {
    display: flex;
    height: 24px;
    width: 24px
}

.ask-query .icon[data-v-3f3b11ec] svg {
    width: 100%
}

.rhetorical_question_container[data-v-3f3b11ec] {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 100%
}

.horizontal-ask-query[data-v-3f3b11ec] {
    gap: 10px;
    overflow-x: auto;
    white-space: nowrap
}

.horizontal-ask-query[data-v-3f3b11ec],
.input[data-v-3f3b11ec] {
    display: flex;
    flex-direction: row
}

.input[data-v-3f3b11ec] {
    align-items: center;
    background: #efefef;
    border: 1px solid #f5f5f5;
    border-radius: 12px;
    box-sizing: border-box;
    height: 42px;
    justify-content: flex-start;
    margin-top: 10px;
    outline: none;
    padding: 12px 8px 12px 10px;
    width: 100%
}

.input input[data-v-3f3b11ec] {
    background: transparent;
    border: none;
    color: #232425;
    flex: 1;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    outline: none
}

.input input[data-v-3f3b11ec]::-moz-placeholder {
    color: #999
}

.input input[data-v-3f3b11ec]::placeholder {
    color: #999
}

.enter-icon[data-v-3f3b11ec] {
    border-radius: 8px;
    cursor: pointer;
    height: 32px;
    width: 32px
}

.enter-icon[data-v-3f3b11ec],
.login-overlay[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    justify-content: center
}

.login-overlay[data-v-3f3b11ec] {
    background-image: linear-gradient(180deg, #fff0, #fff);
    flex-direction: column;
    height: 100%;
    left: 0;
    padding-top: 20px;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1
}

.login-text[data-v-3f3b11ec] {
    color: #232425;
    font-size: 16px;
    font-weight: 700;
    line-height: 1.5em;
    margin-bottom: 16px
}

.login-button[data-v-3f3b11ec] {
    background-color: #fff;
    border: 1px solid #0f7fff;
    border-radius: 16px;
    color: #0f7fff;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.5em;
    padding: 4px 30px
}

.plan_container[data-v-3f3b11ec] {
    border-radius: 0 0 8px 8px;
    flex-direction: column;
    gap: 12px;
    padding: 0 12px 12px
}

.plan_container[data-v-3f3b11ec],
.plan_container_expander[data-v-3f3b11ec] {
    background-color: #fafafa;
    box-sizing: border-box;
    display: flex;
    width: 100%
}

.plan_container_expander[data-v-3f3b11ec] {
    align-items: center;
    border-radius: 8px 8px 0 0;
    flex-direction: row;
    gap: 8px;
    justify-content: left;
    padding: 12px;
    position: relative
}

.plan_container_expander[data-v-3f3b11ec]:hover {
    cursor: pointer
}

.plan_container_expander.expanded[data-v-3f3b11ec] {
    border-radius: 8px
}

.plan_container_expander.expanded[data-v-3f3b11ec]:hover {
    cursor: pointer
}

.plan_container_expander_title[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-grow: 1;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-start
}

.slide-leave-active[data-v-3f3b11ec] {
    height: 45px;
    overflow: hidden;
    transition: all .5s ease
}

.slide-leave-to[data-v-3f3b11ec] {
    height: 0;
    opacity: 0;
    padding: 0 12px
}

.status-slide-leave-active[data-v-3f3b11ec] {
    height: var(--plan-height);
    overflow: hidden;
    transition: all .5s ease
}

.status-slide-leave-to[data-v-3f3b11ec] {
    height: 0;
    opacity: 0;
    padding: 0 12px
}

.plan_step_container[data-v-3f3b11ec] {
    flex-direction: column
}

.plan_step_container[data-v-3f3b11ec],
.plan_step_title_container[data-v-3f3b11ec] {
    box-sizing: border-box;
    display: flex;
    position: relative;
    width: 100%
}

.plan_step_title_container[data-v-3f3b11ec] {
    background-color: #fff;
    border-radius: 8px 8px 0 0;
    flex-direction: row;
    gap: 12px;
    justify-content: flex-start;
    padding: 7px 16px
}

.plan_step_title_container[data-v-3f3b11ec]:hover {
    cursor: pointer
}

.plan_step_title_container.expanded[data-v-3f3b11ec] {
    border-radius: 8px
}

.plan_step_title[data-v-3f3b11ec] {
    align-items: self-start;
    display: flex;
    flex-direction: row;
    flex-grow: 1;
    gap: 8px;
    padding-right: 48px;
    width: calc(100% - 24px)
}

.plan_step_title_text[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-weight: 700;
    line-height: 30px;
    text-overflow: ellipsis
}

.engine-icon-wrapper[data-v-3f3b11ec] {
    align-items: center;
    display: inline-flex;
    padding: 4px 0 6px;
    position: relative;
    vertical-align: middle
}

.engine-hover[data-v-3f3b11ec] {
    bottom: 30px;
    display: none;
    left: 130px;
    margin-bottom: 10px;
    opacity: 0;
    position: absolute;
    transform: translate(-50%);
    transition: opacity .3s ease-in-out;
    z-index: 1000
}

.engine-hover[data-show=true][data-v-3f3b11ec] {
    display: block;
    opacity: 1
}

.engine-hover-inner[data-v-3f3b11ec] {
    background-color: #031933;
    border-radius: 8px;
    color: #fff;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    padding: 10px;
    text-align: left;
    white-space: normal;
    width: 240px
}

.engine-hover-triangle[data-v-3f3b11ec] {
    bottom: -16px;
    left: 20px;
    position: absolute;
    transform: translate(-50%)
}

.engine-hover-triangle.general[data-v-3f3b11ec] {
    bottom: -12px
}

.plan_step_index_loading_container[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    height: 21px;
    width: 12px
}

.expand_icon[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    justify-content: center;
    position: absolute;
    right: 12px;
    top: 12.5px
}

.expand_icon.query[data-v-3f3b11ec] {
    right: 12px;
    top: 12px
}

.unexpanded[data-v-3f3b11ec] {
    transform: rotate(180deg)
}

.expand-enter-active[data-v-3f3b11ec],
.expand-leave-active[data-v-3f3b11ec] {
    transition: all .3s ease
}

.expand-enter-from[data-v-3f3b11ec],
.expand-leave-to[data-v-3f3b11ec] {
    opacity: 0
}

.expand-enter-to[data-v-3f3b11ec],
.expand-leave-from[data-v-3f3b11ec] {
    opacity: 1
}

.plan_step_content_container[data-v-3f3b11ec] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.plan_step_index_container[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 30px;
    justify-content: center;
    left: 0;
    position: relative;
    top: 0;
    width: 12px
}

.engine-icon[data-v-3f3b11ec],
.plan_step_finished_icon[data-v-3f3b11ec],
.plan_step_index_circle_inline[data-v-3f3b11ec] {
    align-items: center;
    display: inline-flex;
    flex-shrink: 0;
    height: 16px;
    justify-content: center;
    margin-right: 4px;
    margin-top: 7px;
    width: 16px
}

.engine-icon[data-v-3f3b11ec] {
    color: #5941b1;
    height: 12px;
    width: 12px
}

.plan_step_index_circle_inline[data-v-3f3b11ec] {
    background-color: #efefef;
    border-radius: 50%
}

.plan_step_index_text[data-v-3f3b11ec] {
    color: #606366;
    font-size: 12px;
    font-weight: 400
}

.plan_step_title_status[data-v-3f3b11ec] {
    background-color: #fff;
    border-radius: 0 0 8px 8px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    gap: 12px;
    justify-content: flex-start;
    padding: 0 12px 12px 16px;
    width: 100%
}

.plan_step_title_status_container[data-v-3f3b11ec] {
    position: relative
}

.plan_step_title_status_text_left[data-v-3f3b11ec] {
    color: #606366;
    flex-shrink: 0;
    font-size: 14px;
    line-height: 21px;
    white-space: nowrap;
    width: -moz-max-content;
    width: max-content
}

.plan_step_title_source[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-start;
    width: 100%
}

.plan_step_webpages[data-v-3f3b11ec] {
    flex: 1;
    overflow-x: auto;
    scrollbar-width: none
}

.plan_step_webpage_name_preview_container[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    gap: 24px;
    white-space: nowrap
}

.plan_step_webpage_name[data-v-3f3b11ec] {
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 8px;
    color: #606366;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    gap: 6px;
    line-height: 21px;
    padding-right: 12px;
    transition: all .3s ease
}

.plan_step_webpage_name.financial_class[data-v-3f3b11ec],
.plan_step_webpage_name.scholar_class[data-v-3f3b11ec] {
    background-color: #e3ebff;
    color: #232425
}

.plan_step_webpage_name[data-v-3f3b11ec]:hover {
    cursor: pointer;
    transform: scale(1.03)
}

.plan_step_webpage_name_container[data-v-3f3b11ec] {
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 18px
}

.plan_step_webpage_index[data-v-3f3b11ec] {
    align-items: center;
    background-color: #efefef;
    border-radius: 8px;
    color: #0c66cc;
    display: flex;
    font-size: 14px;
    height: 30px;
    justify-content: center;
    line-height: 16px;
    width: 30px
}

.plan_step_webpage_index.financial_class[data-v-3f3b11ec],
.plan_step_webpage_index.scholar_class[data-v-3f3b11ec] {
    background-color: #cedaf8
}

.plan_step_webpage_favicon[data-v-3f3b11ec] {
    border-radius: 2px;
    height: 20px;
    width: 20px
}

.plan_step_webpage_name_title[data-v-3f3b11ec] {
    color: #232425;
    flex-shrink: 0;
    font-size: 16px;
    font-weight: 700;
    line-height: 2em
}

.plan_step_webpage_name_text[data-v-3f3b11ec] {
    box-sizing: border-box;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.plan_step_webpage_name_text.financial_class[data-v-3f3b11ec],
.plan_step_webpage_name_text.scholar_class[data-v-3f3b11ec] {
    color: #232425
}

.plan_step_sub_answer_divider[data-v-3f3b11ec] {
    border-top: 1px solid #efefef
}

.plan_step_sub_answer.preview[data-v-3f3b11ec] {
    position: relative
}

.plan_step_content[data-v-3f3b11ec] {
    display: flex;
    flex-direction: row;
    gap: 6px;
    justify-content: flex-start
}

.plan_step_content_item[data-v-3f3b11ec] {
    color: gray;
    font-size: 14px;
    height: 30px;
    line-height: 1.5em
}

.plan_step_status[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 12px;
    height: 21px;
    justify-content: flex-start
}

.plan_step_title_status_gradient[data-v-3f3b11ec] {
    background: linear-gradient(270deg, #fff, #fff0);
    height: 21px;
    pointer-events: none;
    position: absolute;
    right: 11px;
    top: 0;
    width: 20%
}

.plan_step_sub_answer[data-v-3f3b11ec] {
    background-color: #fff;
    border-radius: 0 0 8px 8px;
    display: flex;
    padding: 0 16px 16px;
    position: relative
}

.plan_step_sub_answer_container[data-v-3f3b11ec] {
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: flex-end;
    overflow: hidden
}

.plan_step_sub_answer_container.preview[data-v-3f3b11ec] {
    height: 21px
}

.plan_step_sub_answer_content[data-v-3f3b11ec] {
    display: flex;
    flex-direction: column;
    gap: 24px
}

.result_buttons[data-v-3f3b11ec] {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    margin-top: 8px
}

.result_operation[data-v-3f3b11ec] {
    border: 1px solid #efefef;
    border-radius: 8px;
    color: #232425;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    gap: 8px;
    line-height: 1.5em;
    padding: 8px 12px;
    width: -moz-fit-content;
    width: fit-content
}

.result_operation_right[data-v-3f3b11ec] {
    display: flex;
    flex-direction: row;
    gap: 4px
}

.result_operation_right .result_operation[data-v-3f3b11ec] {
    border: 1px transparent;
    padding: 8px
}

.result_operation[data-v-3f3b11ec]:hover {
    background-color: #f5f5f5
}

.result_operation-icon[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    height: 20px;
    justify-content: center;
    width: 20px
}

.result_operation-icon[data-v-3f3b11ec] svg {
    height: 100%;
    width: 100%
}

.result_operation-icon.right[data-v-3f3b11ec] {
    color: #909499
}

.page-source .shield-icon[data-v-3f3b11ec] {
    flex-shrink: 0;
    height: 16px;
    width: 16px
}

.streaming-reflection[data-v-3f3b11ec] {
    background-color: #fafafa;
    border-radius: 8px;
    padding: 12px
}

.mindmap-container[data-v-3f3b11ec] {
    border: 1px solid #efefef;
    border-radius: 16px;
    cursor: pointer;
    overflow: hidden;
    transition: all .3s ease
}

.mindmap-container[data-v-3f3b11ec]:hover {
    transform: scale(1.01)
}

.mindmap-control-container[data-v-3f3b11ec] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 16px 16px 0;
    width: 100%;
    z-index: 1000
}

.mindmap-control-left[data-v-3f3b11ec] {
    color: #232425;
    font-size: 16px;
    font-weight: 700;
    justify-content: flex-start;
    line-height: 1.5em
}

.mindmap-control-left[data-v-3f3b11ec],
.mindmap-control-right[data-v-3f3b11ec] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.mindmap-control-right[data-v-3f3b11ec] {
    cursor: pointer;
    justify-content: center;
    transition: all .3s ease
}

.mindmap-control-right-icon-container[data-v-3f3b11ec] {
    align-items: center;
    background-color: #f4f4f4;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    gap: 8px;
    height: 24px;
    justify-content: center;
    width: 24px
}

.mindmap-control-right-icon[data-v-3f3b11ec] {
    height: 20px;
    width: 20px
}

.mindmap-control-right-text[data-v-3f3b11ec] {
    color: #606366;
    font-size: 14px;
    line-height: 1.5em
}

@media (prefers-color-scheme:dark) {
    .people-also-ask .ask-query[data-v-3f3b11ec] {
        background-color: #333;
        color: #eee
    }
    .streaming-reflection[data-v-3f3b11ec] {
        background-color: #232425
    }
    .deep_dive_title[data-v-3f3b11ec] {
        color: #eee
    }
    .deep-dive-thinking[data-v-3f3b11ec],
    .deep_dive_title a[data-v-3f3b11ec] {
        color: #99c3ff
    }
    .deep_dive_answer[data-v-3f3b11ec] th {
        background-color: #333;
        border: 1px solid #efefef;
        color: #eee
    }
    .divider[data-v-3f3b11ec] {
        background-color: #efefef30
    }
    .deep_dive_answer[data-v-3f3b11ec] td,
    .deep_dive_answer[data-v-3f3b11ec] p,
    .deep_dive_answer[data-v-3f3b11ec] h1,
    .deep_dive_answer[data-v-3f3b11ec] h2,
    .deep_dive_answer[data-v-3f3b11ec] h3,
    .deep_dive_answer[data-v-3f3b11ec] h4,
    .deep_dive_answer[data-v-3f3b11ec] li,
    .deep_dive_answer[data-v-3f3b11ec] ol,
    .deep_dive_answer[data-v-3f3b11ec] ul,
    .ask-query[data-v-3f3b11ec],
    .login-text[data-v-3f3b11ec],
    .page-title[data-v-3f3b11ec],
    .plan_step_title_text[data-v-3f3b11ec],
    .plan_step_webpage_name_text[data-v-3f3b11ec],
    .plan_step_webpage_name_text.financial_class[data-v-3f3b11ec],
    .plan_step_webpage_name_text.scholar_class[data-v-3f3b11ec],
    .plan_step_webpage_name_title[data-v-3f3b11ec] {
        color: #e0e0e0
    }
    .deep_dive_answer[data-v-3f3b11ec] tr:nth-child(2n) {
        background-color: #232425
    }
    .deep_dive_answer[data-v-3f3b11ec] tr:hover {
        background-color: #1e1e1e
    }
    .deep_dive_answer[data-v-3f3b11ec] p,
    .deep_dive_answer[data-v-3f3b11ec] h1,
    .deep_dive_answer[data-v-3f3b11ec] h2,
    .deep_dive_answer[data-v-3f3b11ec] h3,
    .deep_dive_answer[data-v-3f3b11ec] h4,
    .deep_dive_answer[data-v-3f3b11ec] h5 {
        color: #eee
    }
    .hover-content-inner[data-v-3f3b11ec] {
        background: #232425;
        color: #eee
    }
    .hover-content a[data-v-3f3b11ec] {
        color: #99c3ff
    }
    .plan_step_sub_answer[data-v-3f3b11ec],
    .plan_step_webpage_name_container[data-v-3f3b11ec] {
        background-color: #121212
    }
    .plan_step_webpage_name_text[data-v-3f3b11ec] {
        color: #eee
    }
    .plan_step_title_status_gradient[data-v-3f3b11ec] {
        background: linear-gradient(270deg, #121212, #12121200)
    }
    .plan_step_title_container[data-v-3f3b11ec],
    .plan_step_title_status[data-v-3f3b11ec] {
        background-color: #121212
    }
    .plan_step_title_status_text_left[data-v-3f3b11ec],
    .plan_step_webpage_name_title[data-v-3f3b11ec] {
        color: #eee
    }
    .login-overlay[data-v-3f3b11ec] {
        background-image: linear-gradient(180deg, #12121200, #121212)
    }
    .plan_container[data-v-3f3b11ec],
    .plan_container_expander[data-v-3f3b11ec] {
        background-color: #232425
    }
    .result_operation[data-v-3f3b11ec] {
        border: 1px solid #333;
        color: #eee
    }
    .result_operation[data-v-3f3b11ec]:hover {
        background-color: #333
    }
    .plan_step_webpage_name[data-v-3f3b11ec] {
        background-color: #2a2a2a
    }
    .plan_step_webpage_name.financial_class[data-v-3f3b11ec],
    .plan_step_webpage_name.scholar_class[data-v-3f3b11ec] {
        background-color: #e3ebff
    }
    .plan_step_webpage_name_text.financial_class[data-v-3f3b11ec],
    .plan_step_webpage_name_text.scholar_class[data-v-3f3b11ec] {
        color: #232425
    }
    .plan_step_webpage_index[data-v-3f3b11ec] {
        background-color: #333
    }
    .mindmap-control-left[data-v-3f3b11ec] {
        color: #eee
    }
    .mindmap-control-right-icon-container[data-v-3f3b11ec] {
        background-color: #333
    }
    .mindmap-container[data-v-3f3b11ec] {
        border: 1px solid #efefef30
    }
    .mindmap-control-container[data-v-3f3b11ec] {
        background-color: #1a1a1a
    }
    .mindmap-control-right-text[data-v-3f3b11ec] {
        color: #909499
    }
}

.bubble.try_moa[data-v-3f3b11ec] {
    align-items: center;
    background: #f8f9fa;
    border-radius: 12px;
    display: flex;
    gap: 10px;
    justify-content: space-between;
    margin-top: 16px;
    padding: 16px 24px
}

.bubble.try_moa .left[data-v-3f3b11ec] {
    color: #232425;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px
}

.bubble.try_moa .right .button[data-v-3f3b11ec] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fafafa;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 8px 16px;
    transition: background-color .3s
}

.bubble.try_moa .right .button[data-v-3f3b11ec]:hover {
    background: #424242
}

@media (max-width:1220px) {
    .bubble.try_moa[data-v-3f3b11ec] {
        flex-direction: column
    }
}

@media (prefers-color-scheme:dark) {
    .bubble.try_moa[data-v-3f3b11ec] {
        background: #333
    }
    .bubble.try_moa .left[data-v-3f3b11ec] {
        color: #eee
    }
    .bubble.try_moa .right .button[data-v-3f3b11ec] {
        background: #fafafa;
        color: #232425
    }
    .bubble.try_moa .right .button[data-v-3f3b11ec]:hover {
        background: #e0e0e0
    }
}