import {
    M as e,
    B as t
} from "./Boc3hm_9.js";
import {
    K as i,
    P as o,
    L as n,
    H as a
} from "./BThdTUgg.js";
import {
    d as r,
    b as s,
    o as l,
    r as _,
    h as c,
    a3 as p
} from "./Cf0SOiw0.js";
import {
    G as d
} from "./BN-NNxvY.js";
const u = "" + new URL("minimax_icon.pN19_fx0.png",
        import.meta.url).href,
    m = "" + new URL("wan_icon.BmJ2phk-.png",
        import.meta.url).href,
    g = "" + new URL("vidu_icon.QdqdcFj8.png",
        import.meta.url).href,
    v = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const b = {
        render: function(e, t) {
            return l(), r("svg", v, t[0] || (t[0] = [s("rect", {
                width: "8",
                height: "16",
                rx: "3",
                transform: "matrix(1.39071e-07 1 1 -1.39071e-07 4 8)",
                stroke: "#232425",
                "stroke-width": "1.75"
            }, null, -1)]))
        }
    },
    h = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const f = {
        render: function(e, t) {
            return l(), r("svg", h, t[0] || (t[0] = [s("rect", {
                x: "-0.875",
                y: "0.875",
                width: "12.25",
                height: "12.25",
                rx: "2.125",
                transform: "matrix(-1 0 0 1 17.25 5)",
                stroke: "#232425",
                "stroke-width": "1.75"
            }, null, -1)]))
        }
    },
    A = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const R = {
        render: function(e, t) {
            return l(), r("svg", A, t[0] || (t[0] = [s("rect", {
                width: "8",
                height: "16",
                rx: "3",
                transform: "matrix(-1 8.74228e-08 8.74228e-08 1 16 4)",
                stroke: "#232425",
                "stroke-width": "1.75"
            }, null, -1)]))
        }
    },
    C = [{
        name: "auto",
        icon: e,
        label: e => e("components.generate_image_content.moa_label"),
        description: e => e("components.generate_image_content.moa_description"),
        divider: !0,
        support_i2v: !0,
        permitted_aspect_ratios: ["16:9", "9:16"],
        tip: e => e("components.generate_video_content.supported-ratios-ratios", {
            ratios: "16:9, 9:16"
        })
    }, {
        name: "gemini/veo3",
        icon: d,
        label: "Gemini Veo 3",
        generating_bg_color: "#d9d0ba",
        permitted_aspect_ratios: ["16:9", "9:16"],
        new: !0,
        support_i2v: !0,
        support_t2v: !0,
        speed: "medium",
        quality: "excellent",
        price_level: "premium",
        best_for: ["premium_video_content", "videos_with_audio", "highest_quality"],
        tip: e => e("components.generate_video_content.supported-durations-8s-ratios-ratios", {
            ratios: "16:9, 9:16"
        })
    }, {
        name: "gemini/veo3/fast",
        icon: d,
        label: "Gemini Veo 3 Fast",
        generating_bg_color: "#d9d0ba",
        permitted_aspect_ratios: ["16:9", "9:16"],
        new: !0,
        support_i2v: !0,
        support_t2v: !0,
        speed: "fast",
        quality: "high",
        price_level: "moderate",
        best_for: ["quick_video_generation", "cost_effective_quality", "text_to_video"],
        tip: e => e("components.generate_video_content.supported-durations-8s-ratios-ratios", {
            ratios: "16:9, 9:16"
        })
    }, {
        name: "gemini/veo3/preview/image-to-video",
        icon: d,
        label: "Gemini Veo 3 I2V",
        generating_bg_color: "#d9d0ba",
        permitted_aspect_ratios: ["16:9"],
        new: !0,
        hidden: !0,
        support_i2v: !0,
        support_t2v: !1,
        speed: "medium",
        quality: "excellent",
        price_level: "premium",
        deprecated: !0,
        best_for: ["image_animation", "single_image_to_video", "high_quality_i2v"],
        tip: e => e("components.generate_video_content.supported-durations-8s-ratios-ratios", {
            ratios: "16:9"
        })
    }, {
        name: "kling/v2.1/master",
        icon: i,
        label: "Kling V2.1 Master",
        generating_bg_color: "#bac9d9",
        permitted_aspect_ratios: ["16:9", "9:16", "1:1"],
        support_i2v: !0,
        new: !0,
        speed: "slow",
        quality: "excellent",
        price_level: "premium",
        best_for: ["premium_video_content", "highest_quality", "professional_projects"],
        tip: e => e("components.generate_video_content.duration-5s-supported-ratios-ratios", {
            ratios: "16:9, 9:16, 1:1"
        })
    }, {
        name: "fal-ai/bytedance/seedance/v1/pro",
        icon: t,
        label: "Seedance Pro",
        generating_bg_color: "#e6f3ff",
        permitted_aspect_ratios: ["16:9", "4:3", "1:1", "3:4", "9:16"],
        support_i2v: !0,
        support_t2v: !0,
        new: !0,
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        best_for: ["general_video_creation", "image_to_video", "versatile_ratios"],
        tip: e => e("components.generate_video_content.durations-5s-10s-supported-ratios-ratios", {
            ratios: "16:9, 4:3, 1:1"
        })
    }, {
        name: "minimax/hailuo-02/standard",
        icon: u,
        label: "MiniMax Hailuo-02 Standard",
        generating_bg_color: "#bad9c7",
        permitted_aspect_ratios: ["16:9"],
        support_i2v: !0,
        support_t2v: !0,
        new: !0,
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        best_for: ["standard_video_generation", "text_to_video", "chinese_content"],
        tip: e => e("components.generate_video_content.durations-5s-10s-supported-ratios-ratios", {
            ratios: "16:9"
        })
    }, {
        name: "pixverse/v4.5/turbo",
        icon: o,
        label: "PixVerse V4.5 Turbo",
        generating_bg_color: "#c2bad9",
        permitted_aspect_ratios: ["16:9", "9:16", "4:3", "1:1", "3:4"],
        support_i2v: !0,
        speed: "fast",
        quality: "medium",
        price_level: "budget",
        best_for: ["quick_video_generation", "fast_iterations", "cost_effective"],
        tip: e => e("components.generate_video_content.duration-5s-supported-ratios-ratios", {
            ratios: "16:9, 4:3, 1:1, 3:4, 9:16"
        })
    }, {
        name: "official/pixverse/v4.5/turbo",
        icon: o,
        label: "PixVerse V4.5 Turbo",
        generating_bg_color: "#c2bad9",
        permitted_aspect_ratios: ["16:9", "9:16", "4:3", "1:1", "3:4"],
        support_i2v: !0,
        hidden: !0,
        multiple_image: !0,
        speed: "fast",
        quality: "medium",
        price_level: "budget",
        best_for: ["multi_image_input", "quick_video_generation", "cost_effective"],
        tip: e => e("components.generate_video_content.duration-5s-supported-ratios-ratios", {
            ratios: "16:9, 4:3, 1:1, 3:4, 9:16"
        })
    }, {
        name: "fal-ai/bytedance/seedance/v1/lite",
        icon: t,
        label: "Seedance Lite",
        generating_bg_color: "#e6f3ff",
        permitted_aspect_ratios: ["16:9", "4:3", "1:1", "9:21"],
        support_i2v: !0,
        support_t2v: !0,
        speed: "fast",
        quality: "medium",
        price_level: "budget",
        best_for: ["quick_video_generation", "cost_effective", "basic_video_needs"],
        tip: e => e("components.generate_video_content.durations-5s-10s-supported-ratios-ratios", {
            ratios: "16:9, 4:3, 1:1"
        })
    }, {
        name: "kling/v2.1/standard",
        icon: i,
        label: "Kling V2.1",
        generating_bg_color: "#bac9d9",
        permitted_aspect_ratios: ["16:9", "9:16", "1:1"],
        support_i2v: !0,
        support_t2v: !1,
        hidden: !0,
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        best_for: ["standard_video_generation", "good_quality", "balance_cost_quality"],
        tip: e => e("components.generate_video_content.duration-5s-supported-ratios-ratios", {
            ratios: "16:9, 9:16, 1:1"
        })
    }, {
        name: "kling/v2/master",
        icon: i,
        label: "Kling V2.0 Master",
        generating_bg_color: "#bac9d9",
        permitted_aspect_ratios: ["16:9", "9:16", "1:1"],
        support_i2v: !0,
        speed: "slow",
        quality: "excellent",
        price_level: "premium",
        best_for: ["high_quality_content", "professional_projects", "premium_results"],
        tip: e => e("components.generate_video_content.duration-5s-supported-ratios-ratios", {
            ratios: "16:9, 9:16, 1:1"
        })
    }, {
        name: "kling/v1.6/standard",
        icon: i,
        label: "Kling V1.6",
        generating_bg_color: "#bac9d9",
        permitted_aspect_ratios: ["16:9", "9:16", "1:1"],
        support_i2v: !0,
        hidden: !0,
        deprecated: !0,
        speed: "medium",
        quality: "medium",
        price_level: "budget",
        best_for: ["basic_video_generation", "cost_effective", "standard_quality"],
        tip: e => e("components.generate_video_content.durations-5s-10s-supported-ratios-ratios", {
            ratios: "16:9, 9:16, 1:1"
        })
    }, {
        name: "kling/v1.6/pro",
        icon: i,
        label: "Kling V1.6 Pro",
        generating_bg_color: "#bac9d9",
        permitted_aspect_ratios: ["16:9", "9:16", "1:1"],
        support_i2v: !0,
        speed: "slow",
        quality: "high",
        price_level: "premium",
        best_for: ["high_quality_content", "professional_results", "detailed_generation"],
        tip: e => e("components.generate_video_content.durations-5s-10s-supported-ratios-ratios", {
            ratios: "16:9, 9:16, 1:1"
        })
    }, {
        name: "kling/v1.6/pro/elements",
        icon: i,
        label: "Kling V1.6 Pro Elements",
        generating_bg_color: "#bac9d9",
        permitted_aspect_ratios: ["16:9", "9:16", "1:1"],
        support_multi_images: !0,
        support_i2v: !1,
        support_t2v: !1,
        max_input_images: 4,
        min_input_images: 1,
        hidden: !0,
        speed: "slow",
        quality: "high",
        price_level: "premium",
        best_for: ["multi_image_videos", "complex_transitions", "professional_editing"],
        tip: e => e("components.generate_video_content.durations-5s-10s-supported-ratios-ratios", {
            ratios: "16:9, 9:16, 1:1"
        })
    }, {
        name: "kling/lipsync",
        icon: i,
        label: "Kling Lipsync",
        generating_bg_color: "#bac9d9",
        permitted_aspect_ratios: ["16:9", "9:16", "1:1"],
        hidden: !0,
        disable_mute: !0,
        speed: "medium",
        quality: "high",
        price_level: "expensive",
        best_for: ["lip_sync_videos", "talking_heads", "audio_video_sync"],
        tip: e => e("components.generate_video_content.durations-5s-10s-supported-ratios-ratios", {
            ratios: "16:9, 9:16, 1:1"
        })
    }, {
        name: "fal-ai/sync-lipsync/v2",
        icon: "data:image/png;base64,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",
        label: "Fal Lipsync V2",
        generating_bg_color: "#bac9d9",
        permitted_aspect_ratios: ["16:9", "9:16", "1:1"],
        hidden: !0,
        speed: "fast",
        quality: "medium",
        price_level: "moderate",
        best_for: ["lip_sync_videos", "quick_lipsync", "cost_effective_sync"],
        tip: e => e("components.generate_video_content.durations-5s-10s-supported-ratios-ratios", {
            ratios: "16:9, 9:16, 1:1"
        })
    }, {
        name: "minimax/video-01-live",
        icon: u,
        label: "MiniMax Video-01-Live",
        generating_bg_color: "#bad9c7",
        permitted_aspect_ratios: ["16:9", "9:16"],
        support_i2v: !0,
        hidden: !0,
        deprecated: !0,
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        best_for: ["live_generation", "real_time_content", "interactive_video"],
        tip: e => e("components.generate_video_content.duration-5s-supported-ratios-ratios", {
            ratios: "16:9, 9:16"
        })
    }, {
        name: "minimax/video-01-subject-reference",
        icon: u,
        label: "MiniMax Subject Reference",
        generating_bg_color: "#bad9c7",
        permitted_aspect_ratios: ["9:16"],
        support_i2v: !0,
        support_t2v: !1,
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        best_for: ["subject_consistency", "character_videos", "portrait_videos"],
        tip: e => e("components.generate_video_content.duration-5s-supported-ratios-ratios", {
            ratios: "9:16"
        })
    }, {
        name: "pixverse/v3.5/turbo",
        icon: o,
        label: "PixVerse V3.5",
        generating_bg_color: "#c2bad9",
        permitted_aspect_ratios: ["16:9", "9:16", "4:3", "1:1", "3:4"],
        support_i2v: !0,
        hidden: !0,
        deprecated: !0,
        speed: "fast",
        quality: "medium",
        price_level: "budget",
        best_for: ["quick_video_generation", "cost_effective", "basic_needs"],
        tip: e => e("components.generate_video_content.duration-5s-supported-ratios-ratios", {
            ratios: "16:9, 4:3, 1:1, 3:4, 9:16"
        })
    }, {
        name: "pixverse/v4/turbo",
        icon: o,
        deprecated: !0,
        label: "PixVerse V4 Turbo",
        generating_bg_color: "#c2bad9",
        permitted_aspect_ratios: ["16:9", "9:16", "4:3", "1:1", "3:4"],
        support_i2v: !0,
        hidden: !0,
        tip: e => e("components.generate_video_content.duration-5s-supported-ratios-ratios", {
            ratios: "16:9, 4:3, 1:1, 3:4, 9:16"
        })
    }, {
        name: "lumadream/ray-2",
        icon: n,
        label: "Lumalabs DreamMachine",
        generating_bg_color: "#c9d9ba",
        permitted_aspect_ratios: ["16:9", "9:16", "4:3", "3:4"],
        support_i2v: !0,
        hidden: !0,
        deprecated: !0,
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        best_for: ["text_to_video", "natural_motion", "general_content"],
        tip: e => e("components.generate_video_content.durations-5s-9s-only-text-to-video-supported-ratios-ratios", {
            ratios: "16:9, 9:16, 4:3, 3:4"
        })
    }, {
        name: "lumadream/ray-2-flash",
        icon: n,
        label: "Lumalabs DreamMachine",
        generating_bg_color: "#c9d9ba",
        permitted_aspect_ratios: ["16:9", "9:16", "4:3", "3:4"],
        support_i2v: !0,
        hidden: !0,
        deprecated: !0,
        speed: "fast",
        quality: "medium",
        price_level: "budget",
        best_for: ["quick_generation", "fast_prototyping", "cost_effective"],
        tip: e => e("components.generate_video_content.durations-5s-9s-only-text-to-video-supported-ratios-ratios", {
            ratios: "16:9, 9:16, 4:3, 3:4"
        })
    }, {
        name: "gemini/veo2",
        icon: d,
        label: "Gemini Veo 2",
        generating_bg_color: "#d9d0ba",
        permitted_aspect_ratios: ["16:9", "9:16"],
        support_i2v: !0,
        speed: "medium",
        quality: "excellent",
        price_level: "premium",
        best_for: ["high_quality_videos", "premium_content", "google_quality"],
        tip: e => e("components.generate_video_content.supported-durations-5s-8s-ratios-ratios", {
            ratios: "16:9, 9:16"
        })
    }, {
        name: "wan/v2.1",
        icon: m,
        label: "Wan V2.1",
        generating_bg_color: "#bad6d9",
        permitted_aspect_ratios: ["16:9", "9:16"],
        support_i2v: !0,
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        best_for: ["general_video_generation", "good_quality", "versatile_content"],
        tip: e => e("components.generate_video_content.duration-5s-supported-ratios-ratios-0", {
            ratios: "16:9, 9:16"
        })
    }, {
        name: "hunyuan",
        icon: a,
        label: "Hunyuan",
        generating_bg_color: "#bad6d9",
        permitted_aspect_ratios: ["16:9", "9:16"],
        support_i2v: !0,
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        best_for: ["chinese_content", "tencent_quality", "versatile_generation"],
        tip: e => e("components.generate_video_content.duration-5s-supported-ratios-ratios-0", {
            ratios: "16:9, 9:16"
        })
    }, {
        name: "vidu/start-end-to-video",
        icon: g,
        label: "Vidu",
        generating_bg_color: "#bad6d9",
        permitted_aspect_ratios: ["16:9", "9:16", "1:1"],
        support_i2v: !0,
        multiple_image: !0,
        speed: "medium",
        quality: "high",
        price_level: "moderate",
        best_for: ["start_end_videos", "transition_videos", "specific_motions"],
        tip: e => e("components.generate_video_content.duration-5s-supported-ratios-ratios-0", {
            ratios: "16:9, 9:16, 1:1"
        })
    }, {
        name: "runway/gen4_turbo",
        icon: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAMAAAAJbSJIAAAAeFBMVEUAAAD///8MDAyUlJSLi4v4+PgtLS1GRka+vr7Y2NhtbW13d3fu7u5ZWVnp6enz8/PIyMjf39+srKw1NTWDg4NmZmZycnLi4uK2trZWVlbS0tI7OztcXFydnZ27u7uurq4mJiYdHR0VFRULCwtAQECcnJxOTk6Pj4/7qwrFAAAEr0lEQVR4nO3c63aiMBQFYFC84v1GLVqtte37v+EstXU8uUBCkslxzf7+IjS7KCGHhCQBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACK/VG2ZN5Ltu7Kab2PQnqYNisYudoNLypeMS72a6/4idQ+f04h7vph07ilru4fz9mixjp1F485fvYh07j2TgN2CarmInEjhdQNWGsTMR3s/gRS92qgeHEAHTUexYf+2CBEzTQ+xgd6NACdMydrIf36ECpv3Y0X5MgyVMedyKr8RmdQ7r7vGczaX2trqVzquZuMc+drgrMcm9VWPx5Oa1x1oWdI9J0JYbOgoxHm+3hG5yZnA4YRcO96fCl3RDNtJT0jE5Hj3xWZA226FDJuHqt6Tx3w2OtyF7vAVpsx36MxS/VXTEYTRgIL3rPECLbVU3aEsS1l9qEuFLweHOjURYSJvJsNho7J6RI3pvr7VWWh2BJJT/AQprcsT4NRurhC8mR6QJW95bbMsqodF95lN/S40SkivN1HuDrflPSLr8wnuDrXlPmJMDmtzoBeY9Ib1r+/TfYlu+Ewp33l8BmmzJKmHtXWZPqEoa3asHZncOy16FbfaaCow60MCsElpj8CUNm3AQIZAkaMJthECSkAkZdIZJ0IQcLqRJ0ITjCHEUwiXkUIS6CJbwO0IYpVAJN4q/FUeYhAWXp05JmIQdLj/BK/8JR2x+gTeeEw72nJ7eX1klfB3md0PF7AZuM0yuHMaHioi8ZpjcuIzxnyOiUxXjKSK61WmeIaJjJeoJIrrW2vhHdK4mso/oXi/lHtFDRZh5RB81b94RvVT1WUf0U9VXRGQz0dtubKFdLsL4LFqOnmwisqgH248PtSVCRUSTKVThWY+AtWdGjshhQlSTMb7FWWRRr2lQxTCPyKKu36ROYx6RwxzhRpUo498ig+k0DWttxmeRQb/fsJpo2i8yeJDftF6qjUhnYzCYyt64Iqz7opb0Y2Fbb6J5zVt3uaETp+M/onGo6mvO4ph8KP79t8tzC/VsmRP5TPxCv9OTGfVTGDJ3L/6Nm9uzJ1VEekSj6f1hkfbId1k1T9cUEc/kAwxesUC+U6/i1q/qgKqIe7Kdwfo8urRJ3LogW1fyij05Ij3r/yZEJbpmUFxRQdOUwv9DFZH+TzgMgtu0vbQfFyaMJuIplyMO6TajNSiB9YT2Po4GhDWh15l4imXRD/2iEJBHNUpscv93/VouriG99t4nRcR7ty4u7OewsEv84VwUh3Z7P5P7idvnT4qV0ZPPXre7ll8ewmGMnyRducFqv2O9o/kMFB71xKRv2Nx711bWf/aGw3XmQlzrXN9c8eqk0TnFC0UZvVSBVAbNIsYfV9xJ6yQU6HW/NPgt8pjm/aP+3R/iKOi99l0TPHqKu7qIihVaintU4vivM9Sobq9yiXP1NZjD2xSoivcMdTR13Uy/y4TNZfTBWPc2rJm2taVuFw6vUlDJVQ0eVC6baCv2SCcMavk6uz7tBUaLc80e79JtbcGoG1Tqtd/mxWg6KgaHzOhq8bF66E7nC3azoP0oh9kq32wZFGUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgP/AHytMzrk/nZ4cAAAAASUVORK5CYII=",
        label: "Runway",
        generating_bg_color: "#bad6d9",
        support_i2v: !0,
        support_t2v: !1,
        speed: "fast",
        quality: "high",
        price_level: "premium",
        best_for: ["creative_videos", "artistic_content", "professional_quality"],
        tip: e => e("components.generate_video_content.durations-5s-10s-supported-ratios-ratios", {
            ratios: "16:9, 9:16"
        })
    }],
    y = [{
        name: "9:16",
        value: "9:16",
        icon: R,
        label: "9:16"
    }, {
        name: "16:9",
        value: "16:9",
        icon: b,
        label: "16:9"
    }, {
        name: "3:4",
        value: "3:4",
        icon: R,
        label: "3:4"
    }, {
        name: "1:1",
        value: "1:1",
        icon: f,
        label: "1:1"
    }, {
        name: "4:3",
        value: "4:3",
        icon: R,
        label: "4:3"
    }],
    E = [{
        name: "5s",
        value: "5",
        label: "3 ~ 5s",
        free: !0
    }, {
        name: "10s",
        value: "10",
        label: "5 ~ 10s",
        free: !1
    }],
    k = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAIAAAAiOjnJAAACaUlEQVR4nOzVsY2jUBhG0d2RW7Fzl+G2Lcduww0gyHhTAgRzB4k5J/719AVXcBlj/IOf9nX0AM5JWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRF4nL0gN/2+Xxut9vm2f1+fz6fm2ePx+P1em2evd/v6/W6e+MZ/LmwxhjTNG2ezfO857VlWfa8tq7rvnXn4VdIQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJIRFQlgkhEVCWCSERUJYJP6PMY7ewAn5YpEQFglhkRAWCWGREBYJYZEQFglhkRAWCWGREBYJYZEQFglhkRAWCWGREBYJYZEQFglhkRAWCWGREBYJYZEQFglhkRAWCWGREBYJYZEQFglhkRAWCWGREBYJYZEQFglhkRAWCWGREBYJYZEQFglhkRAWCWGREBYJYZEQFglhkRAWCWGREBYJYZEQFglhkRAWCWGREBYJYZEQFglhkRAWCWGR+A4AAP//6GglBm/cDUUAAAAASUVORK5CYII=",
    x = [{
        name: "auto",
        icon: e,
        label: e => e("components.generate_image_content.moa_label"),
        description: e => e("components.generate_image_content.moa_description"),
        divider: !0
    }, {
        name: "google/gemini-2.5-pro-preview-tts",
        icon: d,
        label: "Gemini TTS Pro",
        generating_bg_color: "#d9d0ba",
        speed: "slow",
        quality: "excellent",
        price_level: "budget",
        audio_types: ["speech"],
        features: ["multi_speaker", "emotional_cues", "natural_language"],
        best_for: ["conversations", "narration", "emotional_content"],
        tip: e => e("components.generate_audio_content.gemini_tts_description"),
        description: e => e("components.generate_audio_content.gemini_tts_features")
    }, {
        name: "google/gemini-2.5-flash-preview-tts",
        icon: d,
        label: "Gemini TTS Flash",
        generating_bg_color: "#d9d0ba",
        speed: "medium",
        quality: "high",
        price_level: "budget",
        audio_types: ["speech"],
        features: ["multi_speaker", "emotional_cues", "natural_language"],
        best_for: ["quick_generation", "conversations", "prototyping"],
        tip: e => e("components.generate_audio_content.gemini_tts_description"),
        description: e => e("components.generate_audio_content.gemini_tts_features")
    }, {
        name: "fal-ai/elevenlabs/tts/multilingual-v2",
        icon: k,
        label: "ElevenLabs TTS",
        generating_bg_color: "#d9baba",
        speed: "medium",
        quality: "excellent",
        price_level: "budget",
        audio_types: ["speech"],
        features: ["multilingual", "style_control", "speed_control"],
        best_for: ["english_content", "professional_narration", "voice_overs"],
        tip: e => e("components.generate_audio_content.elevenlabs_description"),
        description: e => e("components.generate_audio_content.elevenlabs_features")
    }, {
        name: "fal-ai/minimax/speech-02-hd",
        icon: u,
        label: "MiniMax TTS",
        generating_bg_color: "#bad9c7",
        speed: "medium",
        quality: "excellent",
        price_level: "budget",
        audio_types: ["speech"],
        features: ["chinese_optimized", "multilingual", "parameter_control"],
        best_for: ["chinese_content", "asian_languages", "precise_control"],
        tip: e => e("components.generate_audio_content.minimax_tts_description"),
        description: e => e("components.generate_audio_content.minimax_tts_features")
    }, {
        name: "fal-ai/elevenlabs/sound-effects",
        icon: k,
        label: "ElevenLabs Sound Effects",
        generating_bg_color: "#d9baba",
        speed: "fast",
        quality: "high",
        price_level: "budget",
        audio_types: ["sound_effect"],
        features: ["environmental_audio", "quick_generation"],
        best_for: ["sound_effects", "environmental_audio", "audio_textures"],
        tip: e => e("components.generate_audio_content.elevenlabs_sound_effects_description"),
        description: e => e("components.generate_audio_content.sound_effects_features")
    }, {
        name: "CassetteAI/music-generator",
        icon: "" + new URL("cassetteai_icon.D5jM5Jm2.png",
            import.meta.url).href,
        label: "CassetteAI",
        generating_bg_color: "#c2bad9",
        speed: "slow",
        quality: "high",
        price_level: "budget",
        audio_types: ["music"],
        features: ["background_music", "instrumental", "extended_duration"],
        best_for: ["background_music", "video_soundtracks", "ambient_music"],
        tip: e => e("components.generate_audio_content.cassetteai_music_description"),
        description: e => e("components.generate_audio_content.music_generation_features")
    }, {
        name: "fal-ai/lyria2",
        icon: d,
        label: "Lyria2 Music Generator",
        generating_bg_color: "#d9d0ba",
        speed: "medium",
        quality: "excellent",
        price_level: "moderate",
        audio_types: ["music", "sound_effect"],
        features: ["no_lyrics", "sound_effects", "instrumental"],
        best_for: ["instrumental_music", "sound_effects", "short_clips"],
        tip: e => e("components.generate_audio_content.lyria2_music_description"),
        description: e => e("components.generate_audio_content.lyria2_features")
    }, {
        name: "fal-ai/minimax/voice-clone",
        icon: u,
        label: "MiniMax Voice Clone",
        generating_bg_color: "#bad9c7",
        speed: "slow",
        quality: "high",
        price_level: "premium",
        audio_types: ["speech"],
        features: ["voice_cloning", "custom_voice", "personalized"],
        best_for: ["custom_voice_generation", "voice_replication", "personalized_tts"],
        tip: e => e("components.generate_audio_content.minimax_voice_clone_description"),
        description: e => e("components.generate_audio_content.voice_clone_features")
    }, {
        name: "fal-ai/minimax-tts/text-to-speech",
        icon: u,
        label: "MiniMax TTS (Legacy)",
        hidden: !0,
        generating_bg_color: "#bad9c7",
        speed: "medium",
        quality: "high",
        price_level: "budget",
        audio_types: ["speech"],
        features: ["multilingual", "chinese_optimized"],
        best_for: ["chinese_content", "multilingual_tts"],
        tip: e => e("components.generate_audio_content.minimax_tts_description")
    }, {
        name: "unreal-speech/tts",
        icon: "" + new URL("unreal_speech_icon.DkTduWug.webp",
            import.meta.url).href,
        label: "Unreal Speech TTS",
        generating_bg_color: "#d9d0ba",
        speed: "fast",
        quality: "high",
        price_level: "budget",
        audio_types: ["speech"],
        features: ["cost_effective", "fast_generation"],
        best_for: ["bulk_tts", "cost_effective_projects", "simple_narration"],
        tip: e => e("components.generate_audio_content.unreal_speech_tts_description"),
        description: e => e("components.generate_audio_content.unreal_speech_features")
    }];

function V() {
    const e = _(0),
        t = () => {
            e.value = document.documentElement.clientWidth
        };
    return t(), c((() => window.addEventListener("resize", t))), p((() => window.removeEventListener("resize", t))), {
        screenWidth: e
    }
}
const J = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const w = {
        render: function(e, t) {
            return l(), r("svg", J, t[0] || (t[0] = [s("path", {
                d: "M18.315 1.97712C19.2469 1.72048 20.2002 2.27039 20.444 3.20563L22.0309 9.29157C22.2743 10.2266 21.7163 11.1931 20.7848 11.4498L17.2887 12.4127C16.8897 12.5222 16.4812 12.2866 16.3766 11.8863L16.2037 11.2242L12.7496 11.9283V14.7291L17.5611 20.5045C17.8269 20.8233 17.7913 21.3036 17.4811 21.5767C17.171 21.8495 16.7048 21.8121 16.4391 21.4937L12.7496 17.066V19.9986C12.7496 20.4128 12.4138 20.7486 11.9996 20.7486C11.5856 20.7484 11.2496 20.4127 11.2496 19.9986V17.066L7.56114 21.4937C7.29545 21.8121 6.82926 21.8494 6.51915 21.5767C6.20902 21.3037 6.17264 20.8233 6.43809 20.5045L11.2496 14.73V12.234L4.44981 13.6216C3.5488 13.8051 2.66839 13.2554 2.43614 12.3648L1.97813 10.608C1.7464 9.71744 2.24149 8.789 3.11095 8.48493L14.4566 4.52009L14.2858 3.86481C14.1813 3.46421 14.42 3.04929 14.819 2.93903L18.315 1.97712ZM3.58556 9.90778C3.46192 9.95149 3.39151 10.0836 3.42442 10.2105L3.88243 11.9664C3.91563 12.0934 4.04098 12.1721 4.16954 12.1461L5.97227 11.7779L5.32579 9.29841L3.58556 9.90778ZM6.74571 8.80231L7.44298 11.4771L15.8248 9.76813L14.8356 5.97419L6.74571 8.80231ZM18.694 3.42829L15.9205 4.19196L16.0699 4.76227C16.0738 4.77456 16.0784 4.78685 16.0816 4.79938L17.4752 10.147C17.4784 10.1594 17.4804 10.1727 17.483 10.1851L17.6334 10.7623L20.4059 9.9986C20.5388 9.96198 20.6191 9.82347 20.5846 9.69001L18.9977 3.60407C18.9629 3.4706 18.8269 3.3919 18.694 3.42829Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    L = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 28 28"
    };
const U = {
    render: function(e, t) {
        return l(), r("svg", L, t[0] || (t[0] = [s("g", {
            fill: "none"
        }, [s("path", {
            d: "M2 14C2 7.373 7.373 2 14 2s12 5.373 12 12s-5.373 12-12 12S2 20.627 2 14zm12.22-5.03a.75.75 0 0 0 0 1.06l3.22 3.22H8.75a.75.75 0 0 0 0 1.5h8.69l-3.22 3.22a.75.75 0 1 0 1.06 1.06l4.5-4.5a.75.75 0 0 0 0-1.06l-4.5-4.5a.75.75 0 0 0-1.06 0z",
            fill: "currentColor"
        })], -1)]))
    }
};
export {
    U as A, w as D, R, f as a, b, y as c, E as d, x as e, C as m, V as u
};