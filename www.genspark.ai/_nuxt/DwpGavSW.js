import {
    d as r,
    b as o,
    o as t
} from "./Cf0SOiw0.js";
const C = {
    xmlns: "http://www.w3.org/2000/svg",
    width: "100%",
    height: "100%",
    viewBox: "0 0 37 36",
    fill: "none"
};
const l = {
    render: function(l, n) {
        return t(), r("svg", C, n[0] || (n[0] = [o("path", {
            d: "M32.3737 14.1207C32.7177 15.3542 32.9016 16.6545 32.9016 17.9977C32.9016 25.9506 26.4545 32.3977 18.5016 32.3977C10.5487 32.3977 4.10156 25.9506 4.10156 17.9977C4.10156 10.0448 10.5487 3.59766 18.5016 3.59766C19.8448 3.59766 21.145 3.78156 22.3785 4.12556",
            stroke: "currentColor",
            "stroke-width": "2",
            "stroke-linecap": "round"
        }, null, -1), o("path", {
            d: "M14.4617 24.0558H14.5005L14.5135 24.0192L15.8532 20.2591H21.1469L22.5427 24.0199L22.556 24.0558H22.5942H24.5H24.5791L24.5516 23.9816L19.6512 10.7816L19.6379 10.7458H19.5997H17.2206H17.1819L17.1688 10.7823L12.4482 23.9823L12.4219 24.0558H12.5H14.4617ZM20.5001 18.4837H16.4786L18.4533 12.8177L20.5001 18.4837Z",
            fill: "currentColor",
            stroke: "currentColor",
            "stroke-width": "0.11"
        }, null, -1), o("path", {
            d: "M28.9192 14.6992C28.8021 14.6992 28.7033 14.6134 28.6851 14.4964C27.923 9.4589 27.3715 9.01419 22.3695 8.26C22.216 8.23659 22.1016 8.10396 22.1016 7.94792C22.1016 7.79188 22.216 7.65925 22.3695 7.63584C27.3455 6.88425 27.7877 6.43954 28.5394 1.46709C28.5628 1.31365 28.6955 1.19922 28.8516 1.19922C29.0076 1.19922 29.1403 1.31365 29.1637 1.46709C29.9154 6.43954 30.3602 6.88425 35.3337 7.63584C35.4871 7.65925 35.6016 7.79188 35.6016 7.94792C35.6016 8.10396 35.4871 8.23659 35.3337 8.26C30.3368 9.01419 29.9102 9.4589 29.1533 14.4964C29.1351 14.6108 29.0362 14.6992 28.9192 14.6992Z",
            fill: "currentColor"
        }, null, -1)]))
    }
};
export {
    l as A
};