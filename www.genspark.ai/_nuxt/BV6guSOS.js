import {
    f as t
} from "./Bl-gMEVt.js";
import {
    i as e,
    e as s,
    J as o,
    r as a,
    c9 as r,
    h as n,
    a as i,
    o as l,
    w as c,
    d as u,
    f as p,
    b as d,
    y as h,
    t as f,
    a4 as w,
    _ as k,
    cn as m
} from "./Cf0SOiw0.js";
import {
    i as _
} from "./DT-NG54s.js";
import {
    i as g
} from "./BqHcVhvy.js";
var y = {
    size: "1em",
    strokeWidth: 4,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    rtl: !1,
    theme: "outline",
    colors: {
        outline: {
            fill: "#333",
            background: "transparent"
        },
        filled: {
            fill: "#333",
            background: "#FFF"
        },
        twoTone: {
            fill: "#333",
            twoTone: "#2F88FF"
        },
        multiColor: {
            outStrokeColor: "#333",
            outFillColor: "#2F88FF",
            innerStrokeColor: "#FFF",
            innerFillColor: "#43CCF8"
        }
    },
    prefix: "i"
};
var T = Symbol("icon-context");

function j(t, o, a) {
    return {
        name: "icon-" + t,
        props: ["size", "strokeWidth", "strokeLinecap", "strokeLinejoin", "theme", "fill", "spin"],
        setup: function(r) {
            var n = "icon-" + (4294967296 * (1 + Math.random()) | 0).toString(16).substring(1),
                i = e(T, y);
            return function() {
                var e = r.size,
                    l = r.strokeWidth,
                    c = r.strokeLinecap,
                    u = r.strokeLinejoin,
                    p = r.theme,
                    d = r.fill,
                    h = r.spin,
                    f = function(t, e, s) {
                        var o = "string" == typeof e.fill ? [e.fill] : e.fill || [],
                            a = [];
                        switch (e.theme || s.theme) {
                            case "outline":
                                a.push("string" == typeof o[0] ? o[0] : "currentColor"), a.push("none"), a.push("string" == typeof o[0] ? o[0] : "currentColor"), a.push("none");
                                break;
                            case "filled":
                                a.push("string" == typeof o[0] ? o[0] : "currentColor"), a.push("string" == typeof o[0] ? o[0] : "currentColor"), a.push("#FFF"), a.push("#FFF");
                                break;
                            case "two-tone":
                                a.push("string" == typeof o[0] ? o[0] : "currentColor"), a.push("string" == typeof o[1] ? o[1] : s.colors.twoTone.twoTone), a.push("string" == typeof o[0] ? o[0] : "currentColor"), a.push("string" == typeof o[1] ? o[1] : s.colors.twoTone.twoTone);
                                break;
                            case "multi-color":
                                a.push("string" == typeof o[0] ? o[0] : "currentColor"), a.push("string" == typeof o[1] ? o[1] : s.colors.multiColor.outFillColor), a.push("string" == typeof o[2] ? o[2] : s.colors.multiColor.innerStrokeColor), a.push("string" == typeof o[3] ? o[3] : s.colors.multiColor.innerFillColor)
                        }
                        return {
                            size: e.size || s.size,
                            strokeWidth: e.strokeWidth || s.strokeWidth,
                            strokeLinecap: e.strokeLinecap || s.strokeLinecap,
                            strokeLinejoin: e.strokeLinejoin || s.strokeLinejoin,
                            colors: a,
                            id: t
                        }
                    }(n, {
                        size: e,
                        strokeWidth: l,
                        strokeLinecap: c,
                        strokeLinejoin: u,
                        theme: p,
                        fill: d
                    }, i),
                    w = [i.prefix + "-icon"];
                return w.push(i.prefix + "-icon-" + t), o && i.rtl && w.push(i.prefix + "-icon-rtl"), h && w.push(i.prefix + "-icon-spin"), s("span", {
                    class: w.join(" ")
                }, [a(f)])
            }
        }
    }
}
const C = j("attention", !0, (function(t) {
        return s("svg", {
            width: t.size,
            height: t.size,
            viewBox: "0 0 48 48",
            fill: "none"
        }, [s("path", {
            d: "M24 44C29.5228 44 34.5228 41.7614 38.1421 38.1421C41.7614 34.5228 44 29.5228 44 24C44 18.4772 41.7614 13.4772 38.1421 9.85786C34.5228 6.23858 29.5228 4 24 4C18.4772 4 13.4772 6.23858 9.85786 9.85786C6.23858 13.4772 4 18.4772 4 24C4 29.5228 6.23858 34.5228 9.85786 38.1421C13.4772 41.7614 18.4772 44 24 44Z",
            fill: t.colors[1],
            stroke: t.colors[0],
            "stroke-width": t.strokeWidth,
            "stroke-linejoin": t.strokeLinejoin
        }, null), s("path", {
            "fill-rule": "evenodd",
            "clip-rule": "evenodd",
            d: "M24 37C25.3807 37 26.5 35.8807 26.5 34.5C26.5 33.1193 25.3807 32 24 32C22.6193 32 21.5 33.1193 21.5 34.5C21.5 35.8807 22.6193 37 24 37Z",
            fill: t.colors[2]
        }, null), s("path", {
            d: "M24 12V28",
            stroke: t.colors[2],
            "stroke-width": t.strokeWidth,
            "stroke-linecap": t.strokeLinecap,
            "stroke-linejoin": t.strokeLinejoin
        }, null)])
    })),
    E = j("check-one", !0, (function(t) {
        return s("svg", {
            width: t.size,
            height: t.size,
            viewBox: "0 0 48 48",
            fill: "none"
        }, [s("path", {
            d: "M24 44C29.5228 44 34.5228 41.7614 38.1421 38.1421C41.7614 34.5228 44 29.5228 44 24C44 18.4772 41.7614 13.4772 38.1421 9.85786C34.5228 6.23858 29.5228 4 24 4C18.4772 4 13.4772 6.23858 9.85786 9.85786C6.23858 13.4772 4 18.4772 4 24C4 29.5228 6.23858 34.5228 9.85786 38.1421C13.4772 41.7614 18.4772 44 24 44Z",
            fill: t.colors[1],
            stroke: t.colors[0],
            "stroke-width": t.strokeWidth,
            "stroke-linejoin": t.strokeLinejoin
        }, null), s("path", {
            d: "M16 24L22 30L34 18",
            stroke: t.colors[2],
            "stroke-width": t.strokeWidth,
            "stroke-linecap": t.strokeLinecap,
            "stroke-linejoin": t.strokeLinejoin
        }, null)])
    })),
    P = j("close-one", !1, (function(t) {
        return s("svg", {
            width: t.size,
            height: t.size,
            viewBox: "0 0 48 48",
            fill: "none"
        }, [s("path", {
            d: "M24 44C35.0457 44 44 35.0457 44 24C44 12.9543 35.0457 4 24 4C12.9543 4 4 12.9543 4 24C4 35.0457 12.9543 44 24 44Z",
            fill: t.colors[1],
            stroke: t.colors[0],
            "stroke-width": t.strokeWidth,
            "stroke-linejoin": t.strokeLinejoin
        }, null), s("path", {
            d: "M29.6567 18.3432L18.343 29.6569",
            stroke: t.colors[2],
            "stroke-width": t.strokeWidth,
            "stroke-linecap": t.strokeLinecap,
            "stroke-linejoin": t.strokeLinejoin
        }, null), s("path", {
            d: "M18.3433 18.3432L29.657 29.6569",
            stroke: t.colors[2],
            "stroke-width": t.strokeWidth,
            "stroke-linecap": t.strokeLinecap,
            "stroke-linejoin": t.strokeLinejoin
        }, null)])
    })),
    $ = {
        IconCloseSmall: j("close-small", !1, (function(t) {
            return s("svg", {
                width: t.size,
                height: t.size,
                viewBox: "0 0 48 48",
                fill: "none"
            }, [s("path", {
                d: "M14 14L34 34",
                stroke: t.colors[0],
                "stroke-width": t.strokeWidth,
                "stroke-linecap": t.strokeLinecap,
                "stroke-linejoin": t.strokeLinejoin
            }, null), s("path", {
                d: "M14 34L34 14",
                stroke: t.colors[0],
                "stroke-width": t.strokeWidth,
                "stroke-linecap": t.strokeLinecap,
                "stroke-linejoin": t.strokeLinejoin
            }, null)])
        })),
        IconAttention: C,
        IconCheckOne: E,
        IconCloseOne: P,
        IconInfo: j("info", !0, (function(t) {
            return s("svg", {
                width: t.size,
                height: t.size,
                viewBox: "0 0 48 48",
                fill: "none"
            }, [s("path", {
                d: "M24 44C29.5228 44 34.5228 41.7614 38.1421 38.1421C41.7614 34.5228 44 29.5228 44 24C44 18.4772 41.7614 13.4772 38.1421 9.85786C34.5228 6.23858 29.5228 4 24 4C18.4772 4 13.4772 6.23858 9.85786 9.85786C6.23858 13.4772 4 18.4772 4 24C4 29.5228 6.23858 34.5228 9.85786 38.1421C13.4772 41.7614 18.4772 44 24 44Z",
                fill: t.colors[1],
                stroke: t.colors[0],
                "stroke-width": t.strokeWidth,
                "stroke-linejoin": t.strokeLinejoin
            }, null), s("path", {
                "fill-rule": "evenodd",
                "clip-rule": "evenodd",
                d: "M24 11C25.3807 11 26.5 12.1193 26.5 13.5C26.5 14.8807 25.3807 16 24 16C22.6193 16 21.5 14.8807 21.5 13.5C21.5 12.1193 22.6193 11 24 11Z",
                fill: t.colors[2]
            }, null), s("path", {
                d: "M24.5 34V20H23.5H22.5",
                stroke: t.colors[2],
                "stroke-width": t.strokeWidth,
                "stroke-linecap": t.strokeLinecap,
                "stroke-linejoin": t.strokeLinejoin
            }, null), s("path", {
                d: "M21 34H28",
                stroke: t.colors[2],
                "stroke-width": t.strokeWidth,
                "stroke-linecap": t.strokeLinecap,
                "stroke-linejoin": t.strokeLinejoin
            }, null)])
        }))
    },
    b = ["id"],
    S = {
        class: "icons"
    },
    v = {
        class: "content"
    },
    L = {
        key: 0,
        class: "title"
    },
    O = {
        class: "description"
    },
    H = {
        key: 0,
        class: "control"
    },
    F = k(o({
        __name: "Message",
        props: {
            id: {},
            message: {},
            type: {
                default: "success"
            },
            title: {
                default: ""
            },
            duration: {
                default: 3e3
            },
            closable: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["close", "destroy"],
        setup(t, {
            expose: e,
            emit: o
        }) {
            const {
                IconAttention: k,
                IconCheckOne: m,
                IconCloseOne: _,
                IconInfo: g,
                IconCloseSmall: y
            } = $, T = t, j = o, C = a(!0), E = a(null), P = () => {
                T.duration <= 0 || (E.value = setTimeout(x, T.duration))
            }, F = () => {
                E.value && clearTimeout(E.value)
            }, x = () => C.value = !1;
            return r((() => {
                F()
            })), n((() => {
                P()
            })), e({
                close: x
            }), (t, e) => (l(), i(w, {
                name: "message-fade",
                appear: "",
                mode: "in-out",
                onBeforeLeave: e[3] || (e[3] = t => j("close")),
                onAfterLeave: e[4] || (e[4] = t => j("destroy"))
            }, {
                default: c((() => [C.value ? (l(), u("div", {
                    key: 0,
                    class: "message",
                    id: t.id
                }, [d("div", {
                    class: "message-container",
                    onMouseenter: e[1] || (e[1] = t => F()),
                    onMouseleave: e[2] || (e[2] = t => P())
                }, [d("div", S, ["warning" === t.type ? (l(), i(h(k), {
                    key: 0,
                    theme: "filled",
                    size: "18",
                    fill: "#faad14"
                })) : p("", !0), "success" === t.type ? (l(), i(h(m), {
                    key: 1,
                    theme: "filled",
                    size: "18",
                    fill: "#52c41a"
                })) : p("", !0), "error" === t.type ? (l(), i(h(_), {
                    key: 2,
                    theme: "filled",
                    size: "18",
                    fill: "#ff4d4f"
                })) : p("", !0), "info" === t.type ? (l(), i(h(g), {
                    key: 3,
                    theme: "filled",
                    size: "18",
                    fill: "#1677ff"
                })) : p("", !0)]), d("div", v, [t.title ? (l(), u("div", L, f(t.title), 1)) : p("", !0), d("div", O, f(t.message), 1)]), t.closable ? (l(), u("div", H, [d("span", {
                    class: "close-btn",
                    onClick: e[0] || (e[0] = t => x())
                }, [s(h(y))])])) : p("", !0)], 32)], 8, b)) : p("", !0)])),
                _: 1
            }))
        }
    }), [
        ["__scopeId", "data-v-4cb0feb7"]
    ]),
    x = [];
let I = null,
    z = 0;
const N = {
        duration: 3e3
    },
    A = t => {
        const e = "message-" + z++,
            o = { ...N,
                ...t,
                id: e
            };
        I || (I = document.createElement("div"), I.className = "message-wrap", I.style.cssText = "\n      width: 100%;\n      position: fixed;\n      top: 0;\n      left: 0;\n      z-index: 6000;\n      pointer-events: none;\n      display: flex;\n      flex-direction: column;\n      box-sizing: border-box;\n      padding: 15px;\n      background-color: rgba(255, 255, 255, 0);\n      transition: all 1s ease-in-out;\n      align-items: center;\n    ", document.body.appendChild(I));
        const a = s(F, o, null),
            r = document.createElement("div");
        a.appContext = t.ctx || A._context || null, a.props.onClose = t.onClose, a.props.onDestroy = () => {
            I && I.childNodes.length <= 1 && (I.remove(), I = null), m(null, r)
        }, m(a, r), I.appendChild(r.firstElementChild);
        const n = {
            id: e,
            close: () => {
                var t, e;
                return null == (e = null == (t = null == a ? void 0 : a.component) ? void 0 : t.exposed) ? void 0 : e.close()
            }
        };
        return x.push(n), n
    };
A.success = (t, e) => A({ ...e,
    type: "success",
    message: t
}), A.info = (t, e) => A({ ...e,
    type: "info",
    message: t
}), A.warning = (t, e) => A({ ...e,
    type: "warning",
    message: t
}), A.error = (t, e) => A({ ...e,
    type: "error",
    message: t
}), A.closeAll = function() {
    for (let t = x.length - 1; t >= 0; t--) x[t].close()
};
const M = new Map,
    U = new Map,
    J = new Map;
let W = null,
    R = 0;
const B = async () => {
        const t = await fetch("/api/fashion_profile/list", {
            method: "GET",
            headers: {
                "Content-Type": "application/json"
            }
        });
        if (!t.ok) throw new Error(`HTTP error! status: ${t.status}`);
        const e = await t.json();
        if (0 === e.status) return e.data.profiles || [];
        throw new Error(e.message || "Failed to get fashion profile list")
    },
    G = async (t = 0, e = 30) => {
        const s = await (async (t = 0, e = 30) => {
            const s = await fetch(`/api/spark/get_bookmark_fashion?page=${t}&page_size=${e}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json"
                }
            });
            if (!s.ok) throw new Error(`HTTP error! status: ${s.status}`);
            const o = await s.json();
            if (!o || 0 !== o.status || !o.data) throw new Error(`Request error: ${o.message||"Unknown error"}`);
            return o.data.entities || []
        })(t, e);
        return s && 0 != s.length ? dt(s) : []
    },
    q = async (t = null) => {
        try {
            const e = new URLSearchParams;
            t && e.append("ctime_before", t), e.append("source", "fashion_collection");
            const s = await fetch(`/api/user/bookmark_list?${e.toString()}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json"
                }
            });
            if (!s.ok) throw new Error(`HTTP error! status: ${s.status}`);
            const o = await s.json();
            if (0 === o.status) return o.data.spark_ids;
            throw new Error(o.message || "Failed to get user bookmark list")
        } catch (e) {
            return []
        }
    },
    D = async (t, e, s = !1) => {
        try {
            const o = await fetch("/api/user/bookmark", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    spark_id: t,
                    source: e,
                    is_deleted: s
                })
            });
            return 0 === (await o.json()).status
        } catch (o) {
            return !1
        }
    },
    Z = async t => {
        const e = await fetch(`/api/fashion_profile/model_training_task_profile_data?task_id=${t}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json"
                }
            }),
            s = await e.json();
        if (0 === s.status) return s.data.fashion_profile;
        throw new Error(s.message || "Failed to get model training task profile data")
    },
    V = async t => {
        const e = await fetch("/api/fashion_profile/upsert", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(t)
        });
        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
        const s = await e.json();
        if (s.data) return s.data
    },
    Y = async () => {
        const t = await fetch("/api/fashion_profile/selected");
        if (!t.ok) throw new Error(`HTTP error! status: ${t.status}`);
        const e = await t.json();
        if (0 === e.status) return e.data;
        throw new Error(e.message || "Failed to get selected fashion profile")
    },
    K = async (t = null) => {
        const e = await fetch(`/api/fashion_profile/get?fashion_profile_id=${t}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json"
            }
        });
        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
        const s = await e.json();
        if (0 === s.status) return s.data;
        throw new Error(s.message || "Failed to get fashion profile")
    },
    Q = async (t, e) => {
        let s = {};
        if (t && (s.fashion_profile_id = t), e && (s.task_id = e), 0 === Object.keys(s).length) throw new Error("No request data provided");
        const o = await fetch("/api/fashion_profile/choose", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(s)
        });
        if (!o.ok) throw new Error(`HTTP error! status: ${o.status}`);
        return 0 === (await o.json()).status
    },
    X = async t => {
        const e = await fetch(`/api/spark/image_generation_task_delete?task_id=${t}`, {
            method: "DELETE"
        });
        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
        const s = await e.json();
        if (0 != s.status) throw new Error(s.message || "Failed to delete image generation task");
        return !0
    },
    tt = async (t = 10, e = 1, s = null, o = null, a = null) => {
        const r = new URLSearchParams({
            page_size: t.toString()
        });
        e && r.append("page_num", e.toString()), s && r.append("task_type", s), o && r.append("task_source", o), a && r.append("status", a);
        const n = `/api/spark/image_generation_tasks?${r.toString()}`,
            i = await fetch(n, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json"
                }
            }),
            l = await i.json();
        if (!i.ok || 0 !== l.status) throw new Error(l.message || `HTTP error! status: ${i.status}`);
        return l.data.tasks.sort(((t, e) => new Date(e.ctime) - new Date(t.ctime))), {
            tasks: l.data.tasks,
            total_count: l.data.total_count,
            total_pages: l.data.total_pages
        }
    },
    et = async (t = null, e = null) => {
        if (t) {
            const e = new Date(t);
            e.setSeconds(0), e.setMilliseconds(0), t = e.toISOString()
        }
        const s = JSON.stringify({
                end_time: t,
                task_source: e
            }),
            o = Date.now(),
            a = U.get(s);
        if (a && a.timestamp && o - a.timestamp < 2e4) return a.data;
        const r = new URLSearchParams({});
        t && r.append("end_time", t), e && r.append("task_source", e);
        const n = await fetch(`/api/spark/image_generation_running_task_count?${r.toString()}`),
            i = await n.json();
        if (!n.ok || 0 !== i.status) throw new Error(i.message || `HTTP error! status: ${n.status}`);
        return U.set(s, {
            data: i.data,
            timestamp: o
        }), i.data
    },
    st = async t => {
        const e = await fetch(`/api/spark/image_generation_task_detail?task_id=${t}`),
            s = await e.json();
        if (!e.ok || 0 !== s.status) throw new Error(s.message || `HTTP error! status: ${e.status}`);
        return s.data
    },
    ot = async (e = "/api/ig_tasks_status") => {
        if (W && (W.abort(), W = null), 0 === J.size) return;
        R++;
        const s = Array.from(J.keys());
        W = new AbortController;
        try {
            await t(e, {
                task_ids: s
            }, (t => {
                t && t.tasks && Object.entries(t.tasks).forEach((([t, e]) => {
                    if (!t || !e) return;
                    const s = M.get(t);
                    if (s && s.onStatusUpdate) {
                        if (g(e.status)) return s.onStatusUpdate({
                            status: "FAILURE",
                            message: A.get(e.status)
                        }), void s.stop();
                        s.onStatusUpdate(e), ["SUCCESS", "FAILURE", "TIMEOUT", "NSFW"].includes(e.status) && s.stop()
                    }
                }))
            }), W.signal), R = 0
        } catch (o) {
            if ("AbortError" != o.name) {
                if ("Unauthorized" == o.message) return;
                if (W = null, R < 3 && J.size > 0) {
                    const t = 2e3 * Math.pow(1.5, R - 1);
                    setTimeout((function() {
                        ot(e)
                    }), t)
                }
            }
        }
    },
    at = (t, e, s = "/api/ig_tasks_status") => {
        if (M.has(t)) {
            const s = M.get(t);
            return s.updateCallbacks || (s.updateCallbacks = [s.onStatusUpdate], s.onStatusUpdate = t => {
                s.updateCallbacks.forEach((e => e(t)))
            }, s._stop = s.stop), s.updateCallbacks.push(e), s.stop = () => {
                const t = s.updateCallbacks.indexOf(e);
                t > -1 && s.updateCallbacks.splice(t, 1), 0 === s.updateCallbacks.length && s._stop()
            }, s
        }
        const o = {
            taskId: t,
            stop: () => {
                M.delete(t), J.has(t) && J.delete(t)
            },
            onStatusUpdate: e
        };
        return M.set(t, o), ((t, e = "/api/ig_tasks_status") => {
            J.has(t) || (J.set(t, !0), R = 0, ot(e))
        })(t, s), o
    },
    rt = (t, e, s = 5e3) => at(t, e, "/api/ig_tasks_status"),
    nt = (t, e, s = 5e3) => at(t, e, "/api/vg_tasks_status"),
    it = (t, e, s = 5e3) => at(t, e, "/api/ag_tasks_status"),
    lt = async (t, e = null) => {
        const s = {
            fashion_profile_id: t
        };
        e && (s.task_id = e);
        const o = await fetch("/api/spark/image_generation_train_model", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(s)
        });
        if (!o.ok) throw new Error(`HTTP error! status: ${o.status}`);
        const a = await o.json();
        if (a.data) return a.data
    },
    ct = async (t = "FASHION_TRYON", e = null) => {
        let s = "";
        !1 !== e && !0 !== e || (s = "&pr=" + e);
        const o = await fetch("/api/spark/tryon_running_tasks?task_source=" + t + s);
        if (!o.ok) throw new Error(`HTTP error! status: ${o.status}`);
        const a = await o.json();
        if (!a || 0 !== a.status || !a.data) throw new Error(`request error message: ${a}`);
        return a.data.running_tasks
    },
    ut = async (t = 0, e = null) => {
        let s = {};
        t && (s.page = t), e && (s.access_private = e);
        const o = Object.keys(s).map((t => `${t}=${s[t]}`)).join("&"),
            a = await fetch(`/api/spark/my_fashion_remix_sparks?${o}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json"
                }
            });
        if (!a.ok) throw new Error(`HTTP error! status: ${a.status}`);
        const r = await a.json();
        if (!r || 0 !== r.status || !r.data) {
            if (r && -5 == r.status) throw new Error("Please login");
            throw new Error(`request error message: ${r}`)
        }
        const n = r.data;
        return n.flow_items || n.flow_items && 0 != n.flow_items.length ? n.flow_items : []
    },
    pt = async (t = 0, e = null) => {
        let s = {
            pr: !0
        };
        t && (s.page = t), e && (s.access_private = e);
        const o = Object.keys(s).map((t => `${t}=${s[t]}`)).join("&"),
            a = await fetch(`/api/spark/my_fashion_sparks?${o}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json"
                }
            });
        if (!a.ok) throw new Error(`HTTP error! status: ${a.status}`);
        const r = await a.json();
        if (!r || 0 !== r.status || !r.data) {
            if (r && -5 == r.status) throw {
                message: "Please login",
                status: -5
            };
            throw new Error(`request error message: ${r}`)
        }
        const n = r.data;
        return n.flow_items || n.flow_items && 0 != n.flow_items.length ? n.flow_items : []
    },
    dt = t => t.map((t => {
        var e, s, o, a;
        return { ...t,
            original_width: 180,
            original_height: (null == (e = t.data) ? void 0 : e.width) ? 180 * t.data.height / t.data.width : 180 * t.original_height / t.original_width,
            image_url: (null == (s = null == t ? void 0 : t.data) ? void 0 : s.srcset) ? null == (o = null == t ? void 0 : t.data) ? void 0 : o.srcset[512] : null == (a = null == t ? void 0 : t.data) ? void 0 : a.imageUrl,
            srcset: t.data.srcset,
            title: t.title,
            data: t.data
        }
    })),
    ht = async (t, e = 0, s = null, o = null, a = null, r = null, n = null, i = !1, l = !1, c = (_() ? 10 : 30)) => {
        const u = await (async (t, e = 0, s = null, o = null, a = null, r = null, n = null, i = !1, l = !1, c = 30) => {
            let u = {};
            if (n && (u.id = n), u.type = t, e && (u.page = e), c && (u.page_size = c), s && (u.sort_by = s), o && (u.sort_order = o), a && (u.seed = a), r) {
                const t = Array.isArray(r) ? r.filter((t => null !== t)) : r;
                u.filter_tags = t
            }
            i && !l && (u.pr = !0);
            const p = Object.keys(u).map((t => `${t}=${u[t]}`)).join("&"),
                d = i ? l ? "/api/spark/my_fashion_remix_sparks" : "/api/spark/my_fashion_sparks" : "/api/copilot/get_flow_items",
                h = await fetch(`${d}?${p}`, {
                    method: i ? "GET" : "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: i ? null : JSON.stringify({})
                });
            if (!h.ok) throw new Error(`HTTP error! status: ${h.status}`);
            const f = await h.json();
            if (!f || 0 !== f.status || !f.data) {
                if (f && -5 == f.status) throw new Error("Please login");
                throw new Error(`request error message: ${f}`)
            }
            const w = f.data;
            return w.flow_items || w.flow_items && 0 != w.flow_items.length ? w.flow_items : []
        })(t, e, s, o, a, r, n, i, l, c);
        return u && 0 != u.length ? dt(u) : []
    },
    ft = async t => {
        const e = await fetch("/api/spark/get_product_items", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(t)
        });
        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
        const s = await e.json();
        if (!s || 0 !== s.status || !s.data) throw new Error(`request error message: ${s}`);
        return s.data.entities
    },
    wt = async t => {
        const e = await fetch("/api/spark/get_collection_items", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(t)
        });
        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
        const s = await e.json();
        if (!s || 0 !== s.status || !s.data) throw new Error(`request error message: ${s}`);
        const o = s.data;
        return o.entities ? o.entities : []
    },
    kt = async t => {
        const e = await wt(t);
        return e && 0 != e.length ? dt(e) : []
    },
    mt = async (t, e, s, o = "remix_fashion_collection") => {
        const a = await fetch("/api/spark/generate_by_collection_items_async", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                model_image_url: t.data.imageUrl,
                model_info: t.data.self_segment.model_info,
                width: t.data.width,
                height: t.data.height,
                sub_task_type: o,
                personalize: e,
                spark_id: t.data.self_segment.id,
                collection_items: s.map((t => ({
                    id: t.id,
                    item_type: t.item_type,
                    item_prompt: t.prompt,
                    image_url: t.imageUrl
                })))
            })
        });
        if (!a.ok) throw new Error(`HTTP error! status: ${a.status}`);
        const r = await a.json();
        if (r.data && r.data.task_ids) return r.data.task_ids;
        throw new Error(r.message)
    },
    _t = async (t = null) => {
        const e = await fetch("/api/get_upload_personal_image_url?concept_id=" + t);
        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
        const s = await e.json();
        if (!s || 0 !== s.status) throw new Error(`API error! code: ${s.code}`);
        return {
            uploadImageUrl: s.data.upload_image_url,
            path: s.data.path
        }
    },
    gt = async (t, e, s = 3e4) => {
        const o = new AbortController,
            a = setTimeout((() => o.abort()), s);
        try {
            const s = await fetch(t, {
                method: "PUT",
                headers: {
                    "x-ms-blob-type": "BlockBlob"
                },
                body: e,
                signal: o.signal
            });
            if (!s.ok) throw new Error(`上传失败: ${s.statusText}`);
            return s
        } finally {
            clearTimeout(a)
        }
    },
    yt = async (t, e = null, s = null) => {
        const o = {
            project_id: t
        };
        e && (o.fashion_profile_id = e), s && (o.package_id = s);
        const a = await fetch("/api/spark/generate_by_trendy_project_async", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(o)
        });
        if (!a.ok) throw new Error(`HTTP error! status: ${a.status}`);
        const r = await a.json();
        if (r.data && r.data.task_ids) return r.data.task_ids;
        throw new Error(r.message)
    },
    Tt = async t => {
        const e = await fetch("/api/fashion_profile/check_faces", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                id_images: t
            })
        });
        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
        const s = await e.json();
        if (0 === s.status && s.data) return s.data;
        throw new Error(s.message)
    },
    jt = async (t = null) => {
        const e = await fetch("/api/fashion_profile/delete", {
            method: "PUT",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                fashion_profile_id: t
            })
        });
        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
        const s = await e.json();
        if (0 === s.status) return !0;
        throw new Error(s.message)
    },
    Ct = async (t, e) => {
        const s = await fetch("/api/fashion_profile/del_1pic", {
            method: "PUT",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                profile_id: t,
                path: e
            })
        });
        if (!s.ok) throw new Error(`HTTP error! status: ${s.status}`);
        const o = await s.json();
        if (0 === o.status) return !0;
        throw new Error(o.message)
    },
    Et = async (t, e = !1) => {
        const s = await fetch("/api/spark/del_fashion_spark", {
            method: "PUT",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                spark_id: t,
                pr: e
            })
        });
        if (!s.ok) throw new Error(`HTTP error! status: ${s.status}`);
        const o = await s.json();
        if (0 === o.status) return !0;
        throw new Error(o.message)
    },
    Pt = async (t, e, s, o = null, a = !0) => {
        const r = await fetch("/api/try_on", {
            method: "POST",
            body: JSON.stringify({
                userPhoto: t,
                productImage: e,
                productPrompt: o,
                is_async: a,
                project_id: s
            }),
            headers: {
                Accept: "application/json",
                "Content-Type": "application/json"
            }
        });
        if (!r.ok) throw new Error(`HTTP error! status: ${r.status}`);
        const n = await r.json();
        if (0 !== n.status) throw new Error(`API error! code: ${n.status}`);
        return n.data
    },
    $t = async (t, e, s = null, o = null, a = "female") => {
        const r = await fetch("/api/try_on", {
            method: "POST",
            body: JSON.stringify({
                userPhoto: t,
                segment_items: e,
                sample_image: s,
                remix_from: o,
                gender: a,
                mode: 0
            })
        });
        if (!r.ok) throw new Error(`HTTP error! status: ${r.status}`);
        const n = await r.json();
        if (0 !== n.status) throw {
            status: n.status,
            message: `API error! code: ${n.status}`,
            responseMessage: n.message
        };
        return n.data
    },
    bt = async () => {
        const t = await fetch("/api/tryon_meta_data");
        if (!t.ok) throw new Error(`HTTP error! status: ${t.status}`);
        const e = await t.json();
        if (0 !== e.status) throw new Error(`API error! code: ${e.status}`);
        return e.data
    },
    St = async t => {
        const e = await fetch("/api/create_tryon_project", {
            method: "POST",
            body: JSON.stringify({
                userPhoto: t
            })
        });
        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
        const s = await e.json();
        if (0 !== s.status) throw new Error(`API error! code: ${s.status}`);
        return s.data
    },
    vt = async (t, e) => {
        const s = await fetch("/api/update_tryon_project", {
            method: "POST",
            body: JSON.stringify({
                project_id: t,
                task_ids: e
            })
        });
        if (!s.ok) throw new Error(`HTTP error! status: ${s.status}`);
        const o = await s.json();
        if (0 !== o.status) throw new Error(`API error! code: ${o.status}`);
        return !0
    },
    Lt = async t => {
        const e = await fetch("/api/delete_tryon_project", {
            method: "DELETE",
            body: JSON.stringify({
                project_id: t
            })
        });
        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
        const s = await e.json();
        if (0 !== s.status) throw new Error(`API error! code: ${s.status}`);
        return !0
    },
    Ot = async () => {
        const t = await fetch("/api/get_failed_tryon_projects");
        if (!t.ok) throw new Error(`HTTP error! status: ${t.status}`);
        const e = await t.json();
        if (0 !== e.status) throw new Error(`API error! code: ${e.status}`);
        return e.data
    },
    Ht = async t => {
        const e = await fetch("/api/spark/set_fashion_spark_access_private", {
            method: "PUT",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                spark_id: t
            })
        });
        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
        const s = await e.json();
        return 0 === s.status || s.status
    },
    Ft = async (t = "FASHION_TRYON", e = null) => {
        let s = {};
        t && (s.task_source = t), e && (s.start_time = e);
        const o = await fetch(`/api/get_failed_tryon_tasks?${new URLSearchParams(s)}`);
        if (!o.ok) throw new Error(`HTTP error! status: ${o.status}`);
        const a = await o.json();
        if (0 !== a.status) throw new Error(`API error! code: ${a.status}`);
        return a.data
    },
    xt = async (t, e = null) => {
        let s = {};
        t && (s.link = t), e && (s.details = e);
        const o = await fetch(`/api/spark/get_fashion_product_details?${new URLSearchParams(s)}`);
        if (!o.ok) throw new Error(`HTTP error! status: ${o.status}`);
        const a = await o.json();
        if (-1 === a.status) return null;
        if (0 !== a.status) throw new Error(`API error! code: ${a.status}`);
        return a.data
    };
export {
    Ht as A, Pt as B, xt as C, ft as D, D as E, q as F, Ot as G, et as H, Lt as I, yt as J, mt as K, Y as L, Z as M, lt as N, Tt as O, j as P, nt as Q, it as R, _t as a, V as b, K as c, Ct as d, ut as e, dt as f, bt as g, G as h, ct as i, wt as j, St as k, vt as l, rt as m, Q as n, tt as o, B as p, jt as q, st as r, pt as s, $t as t, gt as u, Ft as v, X as w, kt as x, ht as y, Et as z
};