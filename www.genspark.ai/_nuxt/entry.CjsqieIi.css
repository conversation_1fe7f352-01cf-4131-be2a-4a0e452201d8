@font-face {
    font-family: KaTeX_AMS;
    font-style: normal;
    font-weight: 400;
    src: url(./KaTeX_AMS-Regular.BQhdFMY1.woff2) format("woff2"), url(./KaTeX_AMS-Regular.DMm9YOAa.woff) format("woff"), url(./KaTeX_AMS-Regular.DRggAlZN.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Caligraphic;
    font-style: normal;
    font-weight: 700;
    src: url(./KaTeX_Caligraphic-Bold.Dq_IR9rO.woff2) format("woff2"), url(./KaTeX_Caligraphic-Bold.BEiXGLvX.woff) format("woff"), url(./KaTeX_Caligraphic-Bold.ATXxdsX0.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Caligraphic;
    font-style: normal;
    font-weight: 400;
    src: url(./KaTeX_Caligraphic-Regular.Di6jR-x-.woff2) format("woff2"), url(./KaTeX_Caligraphic-Regular.CTRA-rTL.woff) format("woff"), url(./KaTeX_Caligraphic-Regular.wX97UBjC.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Fraktur;
    font-style: normal;
    font-weight: 700;
    src: url(./KaTeX_Fraktur-Bold.CL6g_b3V.woff2) format("woff2"), url(./KaTeX_Fraktur-Bold.BsDP51OF.woff) format("woff"), url(./KaTeX_Fraktur-Bold.BdnERNNW.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Fraktur;
    font-style: normal;
    font-weight: 400;
    src: url(./KaTeX_Fraktur-Regular.CTYiF6lA.woff2) format("woff2"), url(./KaTeX_Fraktur-Regular.Dxdc4cR9.woff) format("woff"), url(./KaTeX_Fraktur-Regular.CB_wures.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Main;
    font-style: normal;
    font-weight: 700;
    src: url(./KaTeX_Main-Bold.Cx986IdX.woff2) format("woff2"), url(./KaTeX_Main-Bold.Jm3AIy58.woff) format("woff"), url(./KaTeX_Main-Bold.waoOVXN0.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Main;
    font-style: italic;
    font-weight: 700;
    src: url(./KaTeX_Main-BoldItalic.DxDJ3AOS.woff2) format("woff2"), url(./KaTeX_Main-BoldItalic.SpSLRI95.woff) format("woff"), url(./KaTeX_Main-BoldItalic.DzxPMmG6.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Main;
    font-style: italic;
    font-weight: 400;
    src: url(./KaTeX_Main-Italic.NWA7e6Wa.woff2) format("woff2"), url(./KaTeX_Main-Italic.BMLOBm91.woff) format("woff"), url(./KaTeX_Main-Italic.3WenGoN9.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Main;
    font-style: normal;
    font-weight: 400;
    src: url(./KaTeX_Main-Regular.B22Nviop.woff2) format("woff2"), url(./KaTeX_Main-Regular.Dr94JaBh.woff) format("woff"), url(./KaTeX_Main-Regular.ypZvNtVU.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Math;
    font-style: italic;
    font-weight: 700;
    src: url(./KaTeX_Math-BoldItalic.CZnvNsCZ.woff2) format("woff2"), url(./KaTeX_Math-BoldItalic.iY-2wyZ7.woff) format("woff"), url(./KaTeX_Math-BoldItalic.B3XSjfu4.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Math;
    font-style: italic;
    font-weight: 400;
    src: url(./KaTeX_Math-Italic.t53AETM-.woff2) format("woff2"), url(./KaTeX_Math-Italic.DA0__PXp.woff) format("woff"), url(./KaTeX_Math-Italic.flOr_0UB.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_SansSerif;
    font-style: normal;
    font-weight: 700;
    src: url(./KaTeX_SansSerif-Bold.D1sUS0GD.woff2) format("woff2"), url(./KaTeX_SansSerif-Bold.DbIhKOiC.woff) format("woff"), url(./KaTeX_SansSerif-Bold.CFMepnvq.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_SansSerif;
    font-style: italic;
    font-weight: 400;
    src: url(./KaTeX_SansSerif-Italic.C3H0VqGB.woff2) format("woff2"), url(./KaTeX_SansSerif-Italic.DN2j7dab.woff) format("woff"), url(./KaTeX_SansSerif-Italic.YYjJ1zSn.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_SansSerif;
    font-style: normal;
    font-weight: 400;
    src: url(./KaTeX_SansSerif-Regular.DDBCnlJ7.woff2) format("woff2"), url(./KaTeX_SansSerif-Regular.CS6fqUqJ.woff) format("woff"), url(./KaTeX_SansSerif-Regular.BNo7hRIc.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Script;
    font-style: normal;
    font-weight: 400;
    src: url(./KaTeX_Script-Regular.D3wIWfF6.woff2) format("woff2"), url(./KaTeX_Script-Regular.D5yQViql.woff) format("woff"), url(./KaTeX_Script-Regular.C5JkGWo-.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Size1;
    font-style: normal;
    font-weight: 400;
    src: url(./KaTeX_Size1-Regular.mCD8mA8B.woff2) format("woff2"), url(./KaTeX_Size1-Regular.C195tn64.woff) format("woff"), url(./KaTeX_Size1-Regular.Dbsnue_I.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Size2;
    font-style: normal;
    font-weight: 400;
    src: url(./KaTeX_Size2-Regular.Dy4dx90m.woff2) format("woff2"), url(./KaTeX_Size2-Regular.oD1tc_U0.woff) format("woff"), url(./KaTeX_Size2-Regular.B7gKUWhC.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Size3;
    font-style: normal;
    font-weight: 400;
    src: url(data:font/woff2;base64,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) format("woff2"), url(./KaTeX_Size3-Regular.CTq5MqoE.woff) format("woff"), url(./KaTeX_Size3-Regular.DgpXs0kz.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Size4;
    font-style: normal;
    font-weight: 400;
    src: url(./KaTeX_Size4-Regular.Dl5lxZxV.woff2) format("woff2"), url(./KaTeX_Size4-Regular.BF-4gkZK.woff) format("woff"), url(./KaTeX_Size4-Regular.DWFBv043.ttf) format("truetype")
}

@font-face {
    font-family: KaTeX_Typewriter;
    font-style: normal;
    font-weight: 400;
    src: url(./KaTeX_Typewriter-Regular.CO6r4hn1.woff2) format("woff2"), url(./KaTeX_Typewriter-Regular.C0xS9mPB.woff) format("woff"), url(./KaTeX_Typewriter-Regular.D3Ib7_Hf.ttf) format("truetype")
}

.katex {
    font: 1.21em KaTeX_Main, Times New Roman, serif;
    line-height: 1.2;
    text-indent: 0;
    text-rendering: auto
}

.katex * {
    -ms-high-contrast-adjust: none !important;
    border-color: currentColor
}

.katex .katex-version:after {
    content: "0.16.11"
}

.katex .katex-mathml {
    clip: rect(1px, 1px, 1px, 1px);
    border: 0;
    height: 1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

.katex .katex-html>.newline {
    display: block
}

.katex .base {
    position: relative;
    white-space: nowrap;
    width: -moz-min-content;
    width: min-content
}

.katex .base,
.katex .strut {
    display: inline-block
}

.katex .textbf {
    font-weight: 700
}

.katex .textit {
    font-style: italic
}

.katex .textrm {
    font-family: KaTeX_Main
}

.katex .textsf {
    font-family: KaTeX_SansSerif
}

.katex .texttt {
    font-family: KaTeX_Typewriter
}

.katex .mathnormal {
    font-family: KaTeX_Math;
    font-style: italic
}

.katex .mathit {
    font-family: KaTeX_Main;
    font-style: italic
}

.katex .mathrm {
    font-style: normal
}

.katex .mathbf {
    font-family: KaTeX_Main;
    font-weight: 700
}

.katex .boldsymbol {
    font-family: KaTeX_Math;
    font-style: italic;
    font-weight: 700
}

.katex .amsrm,
.katex .mathbb,
.katex .textbb {
    font-family: KaTeX_AMS
}

.katex .mathcal {
    font-family: KaTeX_Caligraphic
}

.katex .mathfrak,
.katex .textfrak {
    font-family: KaTeX_Fraktur
}

.katex .mathboldfrak,
.katex .textboldfrak {
    font-family: KaTeX_Fraktur;
    font-weight: 700
}

.katex .mathtt {
    font-family: KaTeX_Typewriter
}

.katex .mathscr,
.katex .textscr {
    font-family: KaTeX_Script
}

.katex .mathsf,
.katex .textsf {
    font-family: KaTeX_SansSerif
}

.katex .mathboldsf,
.katex .textboldsf {
    font-family: KaTeX_SansSerif;
    font-weight: 700
}

.katex .mathitsf,
.katex .textitsf {
    font-family: KaTeX_SansSerif;
    font-style: italic
}

.katex .mainrm {
    font-family: KaTeX_Main;
    font-style: normal
}

.katex .vlist-t {
    border-collapse: collapse;
    display: inline-table;
    table-layout: fixed
}

.katex .vlist-r {
    display: table-row
}

.katex .vlist {
    display: table-cell;
    position: relative;
    vertical-align: bottom
}

.katex .vlist>span {
    display: block;
    height: 0;
    position: relative
}

.katex .vlist>span>span {
    display: inline-block
}

.katex .vlist>span>.pstrut {
    overflow: hidden;
    width: 0
}

.katex .vlist-t2 {
    margin-right: -2px
}

.katex .vlist-s {
    display: table-cell;
    font-size: 1px;
    min-width: 2px;
    vertical-align: bottom;
    width: 2px
}

.katex .vbox {
    align-items: baseline;
    display: inline-flex;
    flex-direction: column
}

.katex .hbox {
    width: 100%
}

.katex .hbox,
.katex .thinbox {
    display: inline-flex;
    flex-direction: row
}

.katex .thinbox {
    max-width: 0;
    width: 0
}

.katex .msupsub {
    text-align: left
}

.katex .mfrac>span>span {
    text-align: center
}

.katex .mfrac .frac-line {
    border-bottom-style: solid;
    display: inline-block;
    width: 100%
}

.katex .hdashline,
.katex .hline,
.katex .mfrac .frac-line,
.katex .overline .overline-line,
.katex .rule,
.katex .underline .underline-line {
    min-height: 1px
}

.katex .mspace {
    display: inline-block
}

.katex .clap,
.katex .llap,
.katex .rlap {
    position: relative;
    width: 0
}

.katex .clap>.inner,
.katex .llap>.inner,
.katex .rlap>.inner {
    position: absolute
}

.katex .clap>.fix,
.katex .llap>.fix,
.katex .rlap>.fix {
    display: inline-block
}

.katex .llap>.inner {
    right: 0
}

.katex .clap>.inner,
.katex .rlap>.inner {
    left: 0
}

.katex .clap>.inner>span {
    margin-left: -50%;
    margin-right: 50%
}

.katex .rule {
    border: 0 solid;
    display: inline-block;
    position: relative
}

.katex .hline,
.katex .overline .overline-line,
.katex .underline .underline-line {
    border-bottom-style: solid;
    display: inline-block;
    width: 100%
}

.katex .hdashline {
    border-bottom-style: dashed;
    display: inline-block;
    width: 100%
}

.katex .sqrt>.root {
    margin-left: .2777777778em;
    margin-right: -.5555555556em
}

.katex .fontsize-ensurer.reset-size1.size1,
.katex .sizing.reset-size1.size1 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size1.size2,
.katex .sizing.reset-size1.size2 {
    font-size: 1.2em
}

.katex .fontsize-ensurer.reset-size1.size3,
.katex .sizing.reset-size1.size3 {
    font-size: 1.4em
}

.katex .fontsize-ensurer.reset-size1.size4,
.katex .sizing.reset-size1.size4 {
    font-size: 1.6em
}

.katex .fontsize-ensurer.reset-size1.size5,
.katex .sizing.reset-size1.size5 {
    font-size: 1.8em
}

.katex .fontsize-ensurer.reset-size1.size6,
.katex .sizing.reset-size1.size6 {
    font-size: 2em
}

.katex .fontsize-ensurer.reset-size1.size7,
.katex .sizing.reset-size1.size7 {
    font-size: 2.4em
}

.katex .fontsize-ensurer.reset-size1.size8,
.katex .sizing.reset-size1.size8 {
    font-size: 2.88em
}

.katex .fontsize-ensurer.reset-size1.size9,
.katex .sizing.reset-size1.size9 {
    font-size: 3.456em
}

.katex .fontsize-ensurer.reset-size1.size10,
.katex .sizing.reset-size1.size10 {
    font-size: 4.148em
}

.katex .fontsize-ensurer.reset-size1.size11,
.katex .sizing.reset-size1.size11 {
    font-size: 4.976em
}

.katex .fontsize-ensurer.reset-size2.size1,
.katex .sizing.reset-size2.size1 {
    font-size: .8333333333em
}

.katex .fontsize-ensurer.reset-size2.size2,
.katex .sizing.reset-size2.size2 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size2.size3,
.katex .sizing.reset-size2.size3 {
    font-size: 1.1666666667em
}

.katex .fontsize-ensurer.reset-size2.size4,
.katex .sizing.reset-size2.size4 {
    font-size: 1.3333333333em
}

.katex .fontsize-ensurer.reset-size2.size5,
.katex .sizing.reset-size2.size5 {
    font-size: 1.5em
}

.katex .fontsize-ensurer.reset-size2.size6,
.katex .sizing.reset-size2.size6 {
    font-size: 1.6666666667em
}

.katex .fontsize-ensurer.reset-size2.size7,
.katex .sizing.reset-size2.size7 {
    font-size: 2em
}

.katex .fontsize-ensurer.reset-size2.size8,
.katex .sizing.reset-size2.size8 {
    font-size: 2.4em
}

.katex .fontsize-ensurer.reset-size2.size9,
.katex .sizing.reset-size2.size9 {
    font-size: 2.88em
}

.katex .fontsize-ensurer.reset-size2.size10,
.katex .sizing.reset-size2.size10 {
    font-size: 3.4566666667em
}

.katex .fontsize-ensurer.reset-size2.size11,
.katex .sizing.reset-size2.size11 {
    font-size: 4.1466666667em
}

.katex .fontsize-ensurer.reset-size3.size1,
.katex .sizing.reset-size3.size1 {
    font-size: .7142857143em
}

.katex .fontsize-ensurer.reset-size3.size2,
.katex .sizing.reset-size3.size2 {
    font-size: .8571428571em
}

.katex .fontsize-ensurer.reset-size3.size3,
.katex .sizing.reset-size3.size3 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size3.size4,
.katex .sizing.reset-size3.size4 {
    font-size: 1.1428571429em
}

.katex .fontsize-ensurer.reset-size3.size5,
.katex .sizing.reset-size3.size5 {
    font-size: 1.2857142857em
}

.katex .fontsize-ensurer.reset-size3.size6,
.katex .sizing.reset-size3.size6 {
    font-size: 1.4285714286em
}

.katex .fontsize-ensurer.reset-size3.size7,
.katex .sizing.reset-size3.size7 {
    font-size: 1.7142857143em
}

.katex .fontsize-ensurer.reset-size3.size8,
.katex .sizing.reset-size3.size8 {
    font-size: 2.0571428571em
}

.katex .fontsize-ensurer.reset-size3.size9,
.katex .sizing.reset-size3.size9 {
    font-size: 2.4685714286em
}

.katex .fontsize-ensurer.reset-size3.size10,
.katex .sizing.reset-size3.size10 {
    font-size: 2.9628571429em
}

.katex .fontsize-ensurer.reset-size3.size11,
.katex .sizing.reset-size3.size11 {
    font-size: 3.5542857143em
}

.katex .fontsize-ensurer.reset-size4.size1,
.katex .sizing.reset-size4.size1 {
    font-size: .625em
}

.katex .fontsize-ensurer.reset-size4.size2,
.katex .sizing.reset-size4.size2 {
    font-size: .75em
}

.katex .fontsize-ensurer.reset-size4.size3,
.katex .sizing.reset-size4.size3 {
    font-size: .875em
}

.katex .fontsize-ensurer.reset-size4.size4,
.katex .sizing.reset-size4.size4 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size4.size5,
.katex .sizing.reset-size4.size5 {
    font-size: 1.125em
}

.katex .fontsize-ensurer.reset-size4.size6,
.katex .sizing.reset-size4.size6 {
    font-size: 1.25em
}

.katex .fontsize-ensurer.reset-size4.size7,
.katex .sizing.reset-size4.size7 {
    font-size: 1.5em
}

.katex .fontsize-ensurer.reset-size4.size8,
.katex .sizing.reset-size4.size8 {
    font-size: 1.8em
}

.katex .fontsize-ensurer.reset-size4.size9,
.katex .sizing.reset-size4.size9 {
    font-size: 2.16em
}

.katex .fontsize-ensurer.reset-size4.size10,
.katex .sizing.reset-size4.size10 {
    font-size: 2.5925em
}

.katex .fontsize-ensurer.reset-size4.size11,
.katex .sizing.reset-size4.size11 {
    font-size: 3.11em
}

.katex .fontsize-ensurer.reset-size5.size1,
.katex .sizing.reset-size5.size1 {
    font-size: .5555555556em
}

.katex .fontsize-ensurer.reset-size5.size2,
.katex .sizing.reset-size5.size2 {
    font-size: .6666666667em
}

.katex .fontsize-ensurer.reset-size5.size3,
.katex .sizing.reset-size5.size3 {
    font-size: .7777777778em
}

.katex .fontsize-ensurer.reset-size5.size4,
.katex .sizing.reset-size5.size4 {
    font-size: .8888888889em
}

.katex .fontsize-ensurer.reset-size5.size5,
.katex .sizing.reset-size5.size5 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size5.size6,
.katex .sizing.reset-size5.size6 {
    font-size: 1.1111111111em
}

.katex .fontsize-ensurer.reset-size5.size7,
.katex .sizing.reset-size5.size7 {
    font-size: 1.3333333333em
}

.katex .fontsize-ensurer.reset-size5.size8,
.katex .sizing.reset-size5.size8 {
    font-size: 1.6em
}

.katex .fontsize-ensurer.reset-size5.size9,
.katex .sizing.reset-size5.size9 {
    font-size: 1.92em
}

.katex .fontsize-ensurer.reset-size5.size10,
.katex .sizing.reset-size5.size10 {
    font-size: 2.3044444444em
}

.katex .fontsize-ensurer.reset-size5.size11,
.katex .sizing.reset-size5.size11 {
    font-size: 2.7644444444em
}

.katex .fontsize-ensurer.reset-size6.size1,
.katex .sizing.reset-size6.size1 {
    font-size: .5em
}

.katex .fontsize-ensurer.reset-size6.size2,
.katex .sizing.reset-size6.size2 {
    font-size: .6em
}

.katex .fontsize-ensurer.reset-size6.size3,
.katex .sizing.reset-size6.size3 {
    font-size: .7em
}

.katex .fontsize-ensurer.reset-size6.size4,
.katex .sizing.reset-size6.size4 {
    font-size: .8em
}

.katex .fontsize-ensurer.reset-size6.size5,
.katex .sizing.reset-size6.size5 {
    font-size: .9em
}

.katex .fontsize-ensurer.reset-size6.size6,
.katex .sizing.reset-size6.size6 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size6.size7,
.katex .sizing.reset-size6.size7 {
    font-size: 1.2em
}

.katex .fontsize-ensurer.reset-size6.size8,
.katex .sizing.reset-size6.size8 {
    font-size: 1.44em
}

.katex .fontsize-ensurer.reset-size6.size9,
.katex .sizing.reset-size6.size9 {
    font-size: 1.728em
}

.katex .fontsize-ensurer.reset-size6.size10,
.katex .sizing.reset-size6.size10 {
    font-size: 2.074em
}

.katex .fontsize-ensurer.reset-size6.size11,
.katex .sizing.reset-size6.size11 {
    font-size: 2.488em
}

.katex .fontsize-ensurer.reset-size7.size1,
.katex .sizing.reset-size7.size1 {
    font-size: .4166666667em
}

.katex .fontsize-ensurer.reset-size7.size2,
.katex .sizing.reset-size7.size2 {
    font-size: .5em
}

.katex .fontsize-ensurer.reset-size7.size3,
.katex .sizing.reset-size7.size3 {
    font-size: .5833333333em
}

.katex .fontsize-ensurer.reset-size7.size4,
.katex .sizing.reset-size7.size4 {
    font-size: .6666666667em
}

.katex .fontsize-ensurer.reset-size7.size5,
.katex .sizing.reset-size7.size5 {
    font-size: .75em
}

.katex .fontsize-ensurer.reset-size7.size6,
.katex .sizing.reset-size7.size6 {
    font-size: .8333333333em
}

.katex .fontsize-ensurer.reset-size7.size7,
.katex .sizing.reset-size7.size7 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size7.size8,
.katex .sizing.reset-size7.size8 {
    font-size: 1.2em
}

.katex .fontsize-ensurer.reset-size7.size9,
.katex .sizing.reset-size7.size9 {
    font-size: 1.44em
}

.katex .fontsize-ensurer.reset-size7.size10,
.katex .sizing.reset-size7.size10 {
    font-size: 1.7283333333em
}

.katex .fontsize-ensurer.reset-size7.size11,
.katex .sizing.reset-size7.size11 {
    font-size: 2.0733333333em
}

.katex .fontsize-ensurer.reset-size8.size1,
.katex .sizing.reset-size8.size1 {
    font-size: .3472222222em
}

.katex .fontsize-ensurer.reset-size8.size2,
.katex .sizing.reset-size8.size2 {
    font-size: .4166666667em
}

.katex .fontsize-ensurer.reset-size8.size3,
.katex .sizing.reset-size8.size3 {
    font-size: .4861111111em
}

.katex .fontsize-ensurer.reset-size8.size4,
.katex .sizing.reset-size8.size4 {
    font-size: .5555555556em
}

.katex .fontsize-ensurer.reset-size8.size5,
.katex .sizing.reset-size8.size5 {
    font-size: .625em
}

.katex .fontsize-ensurer.reset-size8.size6,
.katex .sizing.reset-size8.size6 {
    font-size: .6944444444em
}

.katex .fontsize-ensurer.reset-size8.size7,
.katex .sizing.reset-size8.size7 {
    font-size: .8333333333em
}

.katex .fontsize-ensurer.reset-size8.size8,
.katex .sizing.reset-size8.size8 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size8.size9,
.katex .sizing.reset-size8.size9 {
    font-size: 1.2em
}

.katex .fontsize-ensurer.reset-size8.size10,
.katex .sizing.reset-size8.size10 {
    font-size: 1.4402777778em
}

.katex .fontsize-ensurer.reset-size8.size11,
.katex .sizing.reset-size8.size11 {
    font-size: 1.7277777778em
}

.katex .fontsize-ensurer.reset-size9.size1,
.katex .sizing.reset-size9.size1 {
    font-size: .2893518519em
}

.katex .fontsize-ensurer.reset-size9.size2,
.katex .sizing.reset-size9.size2 {
    font-size: .3472222222em
}

.katex .fontsize-ensurer.reset-size9.size3,
.katex .sizing.reset-size9.size3 {
    font-size: .4050925926em
}

.katex .fontsize-ensurer.reset-size9.size4,
.katex .sizing.reset-size9.size4 {
    font-size: .462962963em
}

.katex .fontsize-ensurer.reset-size9.size5,
.katex .sizing.reset-size9.size5 {
    font-size: .5208333333em
}

.katex .fontsize-ensurer.reset-size9.size6,
.katex .sizing.reset-size9.size6 {
    font-size: .5787037037em
}

.katex .fontsize-ensurer.reset-size9.size7,
.katex .sizing.reset-size9.size7 {
    font-size: .6944444444em
}

.katex .fontsize-ensurer.reset-size9.size8,
.katex .sizing.reset-size9.size8 {
    font-size: .8333333333em
}

.katex .fontsize-ensurer.reset-size9.size9,
.katex .sizing.reset-size9.size9 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size9.size10,
.katex .sizing.reset-size9.size10 {
    font-size: 1.2002314815em
}

.katex .fontsize-ensurer.reset-size9.size11,
.katex .sizing.reset-size9.size11 {
    font-size: 1.4398148148em
}

.katex .fontsize-ensurer.reset-size10.size1,
.katex .sizing.reset-size10.size1 {
    font-size: .2410800386em
}

.katex .fontsize-ensurer.reset-size10.size2,
.katex .sizing.reset-size10.size2 {
    font-size: .2892960463em
}

.katex .fontsize-ensurer.reset-size10.size3,
.katex .sizing.reset-size10.size3 {
    font-size: .337512054em
}

.katex .fontsize-ensurer.reset-size10.size4,
.katex .sizing.reset-size10.size4 {
    font-size: .3857280617em
}

.katex .fontsize-ensurer.reset-size10.size5,
.katex .sizing.reset-size10.size5 {
    font-size: .4339440694em
}

.katex .fontsize-ensurer.reset-size10.size6,
.katex .sizing.reset-size10.size6 {
    font-size: .4821600771em
}

.katex .fontsize-ensurer.reset-size10.size7,
.katex .sizing.reset-size10.size7 {
    font-size: .5785920926em
}

.katex .fontsize-ensurer.reset-size10.size8,
.katex .sizing.reset-size10.size8 {
    font-size: .6943105111em
}

.katex .fontsize-ensurer.reset-size10.size9,
.katex .sizing.reset-size10.size9 {
    font-size: .8331726133em
}

.katex .fontsize-ensurer.reset-size10.size10,
.katex .sizing.reset-size10.size10 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size10.size11,
.katex .sizing.reset-size10.size11 {
    font-size: 1.1996142719em
}

.katex .fontsize-ensurer.reset-size11.size1,
.katex .sizing.reset-size11.size1 {
    font-size: .2009646302em
}

.katex .fontsize-ensurer.reset-size11.size2,
.katex .sizing.reset-size11.size2 {
    font-size: .2411575563em
}

.katex .fontsize-ensurer.reset-size11.size3,
.katex .sizing.reset-size11.size3 {
    font-size: .2813504823em
}

.katex .fontsize-ensurer.reset-size11.size4,
.katex .sizing.reset-size11.size4 {
    font-size: .3215434084em
}

.katex .fontsize-ensurer.reset-size11.size5,
.katex .sizing.reset-size11.size5 {
    font-size: .3617363344em
}

.katex .fontsize-ensurer.reset-size11.size6,
.katex .sizing.reset-size11.size6 {
    font-size: .4019292605em
}

.katex .fontsize-ensurer.reset-size11.size7,
.katex .sizing.reset-size11.size7 {
    font-size: .4823151125em
}

.katex .fontsize-ensurer.reset-size11.size8,
.katex .sizing.reset-size11.size8 {
    font-size: .578778135em
}

.katex .fontsize-ensurer.reset-size11.size9,
.katex .sizing.reset-size11.size9 {
    font-size: .6945337621em
}

.katex .fontsize-ensurer.reset-size11.size10,
.katex .sizing.reset-size11.size10 {
    font-size: .8336012862em
}

.katex .fontsize-ensurer.reset-size11.size11,
.katex .sizing.reset-size11.size11 {
    font-size: 1em
}

.katex .delimsizing.size1 {
    font-family: KaTeX_Size1
}

.katex .delimsizing.size2 {
    font-family: KaTeX_Size2
}

.katex .delimsizing.size3 {
    font-family: KaTeX_Size3
}

.katex .delimsizing.size4 {
    font-family: KaTeX_Size4
}

.katex .delimsizing.mult .delim-size1>span {
    font-family: KaTeX_Size1
}

.katex .delimsizing.mult .delim-size4>span {
    font-family: KaTeX_Size4
}

.katex .nulldelimiter {
    display: inline-block;
    width: .12em
}

.katex .delimcenter,
.katex .op-symbol {
    position: relative
}

.katex .op-symbol.small-op {
    font-family: KaTeX_Size1
}

.katex .op-symbol.large-op {
    font-family: KaTeX_Size2
}

.katex .accent>.vlist-t,
.katex .op-limits>.vlist-t {
    text-align: center
}

.katex .accent .accent-body {
    position: relative
}

.katex .accent .accent-body:not(.accent-full) {
    width: 0
}

.katex .overlay {
    display: block
}

.katex .mtable .vertical-separator {
    display: inline-block;
    min-width: 1px
}

.katex .mtable .arraycolsep {
    display: inline-block
}

.katex .mtable .col-align-c>.vlist-t {
    text-align: center
}

.katex .mtable .col-align-l>.vlist-t {
    text-align: left
}

.katex .mtable .col-align-r>.vlist-t {
    text-align: right
}

.katex .svg-align {
    text-align: left
}

.katex svg {
    fill: currentColor;
    stroke: currentColor;
    fill-rule: nonzero;
    fill-opacity: 1;
    stroke-width: 1;
    stroke-linecap: butt;
    stroke-linejoin: miter;
    stroke-miterlimit: 4;
    stroke-dasharray: none;
    stroke-dashoffset: 0;
    stroke-opacity: 1;
    display: block;
    height: inherit;
    position: absolute;
    width: 100%
}

.katex svg path {
    stroke: none
}

.katex img {
    border-style: none;
    max-height: none;
    max-width: none;
    min-height: 0;
    min-width: 0
}

.katex .stretchy {
    display: block;
    overflow: hidden;
    position: relative;
    width: 100%
}

.katex .stretchy:after,
.katex .stretchy:before {
    content: ""
}

.katex .hide-tail {
    overflow: hidden;
    position: relative;
    width: 100%
}

.katex .halfarrow-left {
    left: 0;
    overflow: hidden;
    position: absolute;
    width: 50.2%
}

.katex .halfarrow-right {
    overflow: hidden;
    position: absolute;
    right: 0;
    width: 50.2%
}

.katex .brace-left {
    left: 0;
    overflow: hidden;
    position: absolute;
    width: 25.1%
}

.katex .brace-center {
    left: 25%;
    overflow: hidden;
    position: absolute;
    width: 50%
}

.katex .brace-right {
    overflow: hidden;
    position: absolute;
    right: 0;
    width: 25.1%
}

.katex .x-arrow-pad {
    padding: 0 .5em
}

.katex .cd-arrow-pad {
    padding: 0 .55556em 0 .27778em
}

.katex .mover,
.katex .munder,
.katex .x-arrow {
    text-align: center
}

.katex .boxpad {
    padding: 0 .3em
}

.katex .fbox,
.katex .fcolorbox {
    border: .04em solid;
    box-sizing: border-box
}

.katex .cancel-pad {
    padding: 0 .2em
}

.katex .cancel-lap {
    margin-left: -.2em;
    margin-right: -.2em
}

.katex .sout {
    border-bottom-style: solid;
    border-bottom-width: .08em
}

.katex .angl {
    border-right: .049em solid;
    border-top: .049em solid;
    box-sizing: border-box;
    margin-right: .03889em
}

.katex .anglpad {
    padding: 0 .03889em
}

.katex .eqn-num:before {
    content: "(" counter(katexEqnNo) ")";
    counter-increment: katexEqnNo
}

.katex .mml-eqn-num:before {
    content: "(" counter(mmlEqnNo) ")";
    counter-increment: mmlEqnNo
}

.katex .mtr-glue {
    width: 50%
}

.katex .cd-vert-arrow {
    display: inline-block;
    position: relative
}

.katex .cd-label-left {
    display: inline-block;
    position: absolute;
    right: calc(50% + .3em);
    text-align: left
}

.katex .cd-label-right {
    display: inline-block;
    left: calc(50% + .3em);
    position: absolute;
    text-align: right
}

.katex-display {
    display: block;
    margin: 1em 0;
    text-align: center
}

.katex-display>.katex {
    display: block;
    text-align: center;
    white-space: nowrap
}

.katex-display>.katex>.katex-html {
    display: block;
    position: relative
}

.katex-display>.katex>.katex-html>.tag {
    position: absolute;
    right: 0
}

.katex-display.leqno>.katex>.katex-html>.tag {
    left: 0;
    right: auto
}

.katex-display.fleqn>.katex {
    padding-left: 2em;
    text-align: left
}

body {
    counter-reset: katexEqnNo mmlEqnNo
}

.\ !container {
    width: 100% !important
}

.container {
    width: 100%
}

@media (min-width:640px) {
    .\ !container {
        max-width: 640px !important
    }
    .container {
        max-width: 640px
    }
}

@media (min-width:768px) {
    .\ !container {
        max-width: 768px !important
    }
    .container {
        max-width: 768px
    }
}

@media (min-width:1024px) {
    .\ !container {
        max-width: 1024px !important
    }
    .container {
        max-width: 1024px
    }
}

@media (min-width:1280px) {
    .\ !container {
        max-width: 1280px !important
    }
    .container {
        max-width: 1280px
    }
}

@media (min-width:1536px) {
    .\ !container {
        max-width: 1536px !important
    }
    .container {
        max-width: 1536px
    }
}

.sr-only {
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    clip: rect(0, 0, 0, 0);
    border-width: 0;
    white-space: nowrap
}

.pointer-events-none {
    pointer-events: none
}

.pointer-events-auto {
    pointer-events: auto
}

.\ !visible {
    visibility: visible !important
}

.visible {
    visibility: visible
}

.invisible {
    visibility: hidden
}

.collapse {
    visibility: collapse
}

.static {
    position: static
}

.\ !fixed {
    position: fixed !important
}

.fixed {
    position: fixed
}

.absolute {
    position: absolute
}

.relative {
    position: relative
}

.sticky {
    position: sticky
}

.inset-0 {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

.-top-4 {
    top: -1rem
}

.bottom-0 {
    bottom: 0
}

.bottom-\[-72px\] {
    bottom: -72px
}

.bottom-\[130px\] {
    bottom: 130px
}

.bottom-\[16px\] {
    bottom: 16px
}

.bottom-\[6px\] {
    bottom: 6px
}

.left-0 {
    left: 0
}

.left-1\/2 {
    left: 50%
}

.left-2 {
    left: .5rem
}

.left-3 {
    left: .75rem
}

.left-\[-12px\] {
    left: -12px
}

.left-\[-999999px\] {
    left: -999999px
}

.left-\[0px\] {
    left: 0
}

.left-\[10px\] {
    left: 10px
}

.left-\[12px\] {
    left: 12px
}

.left-\[17px\] {
    left: 17px
}

.left-\[3px\] {
    left: 3px
}

.left-\[48\%\] {
    left: 48%
}

.left-\[50\%\] {
    left: 50%
}

.left-\[80px\] {
    left: 80px
}

.left-\[8px\] {
    left: 8px
}

.left-\[9px\] {
    left: 9px
}

.right-0 {
    right: 0
}

.right-\[-1px\] {
    right: -1px
}

.right-\[-5px\] {
    right: -5px
}

.right-\[10px\] {
    right: 10px
}

.right-\[12px\] {
    right: 12px
}

.right-\[14px\] {
    right: 14px
}

.right-\[16px\] {
    right: 16px
}

.right-\[24px\] {
    right: 24px
}

.right-\[28px\] {
    right: 28px
}

.right-\[30px\] {
    right: 30px
}

.right-\[50px\] {
    right: 50px
}

.right-\[5px\] {
    right: 5px
}

.right-\[6px\] {
    right: 6px
}

.right-\[8px\] {
    right: 8px
}

.right-\[9px\] {
    right: 9px
}

.top-0 {
    top: 0
}

.top-20 {
    top: 5rem
}

.top-3 {
    top: .75rem
}

.top-\[-14px\] {
    top: -14px
}

.top-\[-1px\] {
    top: -1px
}

.top-\[10px\] {
    top: 10px
}

.top-\[12px\] {
    top: 12px
}

.top-\[14px\] {
    top: 14px
}

.top-\[16px\] {
    top: 16px
}

.top-\[22px\] {
    top: 22px
}

.top-\[28px\] {
    top: 28px
}

.top-\[29px\] {
    top: 29px
}

.top-\[30px\] {
    top: 30px
}

.top-\[39px\] {
    top: 39px
}

.top-\[48\%\] {
    top: 48%
}

.top-\[5px\] {
    top: 5px
}

.top-\[68px\] {
    top: 68px
}

.top-\[6px\] {
    top: 6px
}

.top-\[8px\] {
    top: 8px
}

.isolate {
    isolation: isolate
}

.-z-10 {
    z-index: -10
}

.z-10 {
    z-index: 10
}

.z-50 {
    z-index: 50
}

.z-\[100\] {
    z-index: 100
}

.z-\[101\] {
    z-index: 101
}

.z-\[102\] {
    z-index: 102
}

.z-\[1\] {
    z-index: 1
}

.z-\[200\] {
    z-index: 200
}

.z-\[2\] {
    z-index: 2
}

.float-right {
    float: right
}

.-m-2 {
    margin: -.5rem
}

.m-0 {
    margin: 0
}

.mx-4 {
    margin-left: 1rem;
    margin-right: 1rem
}

.mx-\[11px\] {
    margin-left: 11px;
    margin-right: 11px
}

.mx-\[12px\] {
    margin-left: 12px;
    margin-right: 12px
}

.mx-\[14px\] {
    margin-left: 14px;
    margin-right: 14px
}

.mx-\[16px\] {
    margin-left: 16px;
    margin-right: 16px
}

.mx-\[18px\] {
    margin-left: 18px;
    margin-right: 18px
}

.mx-\[20px\] {
    margin-left: 20px;
    margin-right: 20px
}

.mx-\[9px\] {
    margin-left: 9px;
    margin-right: 9px
}

.mx-auto {
    margin-left: auto;
    margin-right: auto
}

.my-1 {
    margin-bottom: .25rem;
    margin-top: .25rem
}

.my-4 {
    margin-bottom: 1rem;
    margin-top: 1rem
}

.my-\[10px\] {
    margin-bottom: 10px;
    margin-top: 10px
}

.my-\[180px\] {
    margin-bottom: 180px;
    margin-top: 180px
}

.my-\[22px\] {
    margin-bottom: 22px;
    margin-top: 22px
}

.my-\[30px\] {
    margin-bottom: 30px;
    margin-top: 30px
}

.my-\[40px\] {
    margin-bottom: 40px;
    margin-top: 40px
}

.mb-1 {
    margin-bottom: .25rem
}

.mb-2 {
    margin-bottom: .5rem
}

.mb-3 {
    margin-bottom: .75rem
}

.mb-4 {
    margin-bottom: 1rem
}

.mb-6 {
    margin-bottom: 1.5rem
}

.mb-8 {
    margin-bottom: 2rem
}

.mb-\[-22px\] {
    margin-bottom: -22px
}

.mb-\[10px\] {
    margin-bottom: 10px
}

.mb-\[16px\] {
    margin-bottom: 16px
}

.mb-\[20px\] {
    margin-bottom: 20px
}

.mb-\[24px\] {
    margin-bottom: 24px
}

.mb-\[26px\] {
    margin-bottom: 26px
}

.mb-\[32px\] {
    margin-bottom: 32px
}

.mb-\[40px\] {
    margin-bottom: 40px
}

.mb-\[45px\] {
    margin-bottom: 45px
}

.mb-\[4px\] {
    margin-bottom: 4px
}

.mb-\[8px\] {
    margin-bottom: 8px
}

.ml-1 {
    margin-left: .25rem
}

.ml-1\.5 {
    margin-left: .375rem
}

.ml-2 {
    margin-left: .5rem
}

.ml-\[10px\] {
    margin-left: 10px
}

.ml-\[12px\] {
    margin-left: 12px
}

.ml-\[14px\] {
    margin-left: 14px
}

.ml-\[16px\] {
    margin-left: 16px
}

.ml-\[45\%\] {
    margin-left: 45%
}

.ml-\[8px\] {
    margin-left: 8px
}

.ml-auto {
    margin-left: auto
}

.mr-1 {
    margin-right: .25rem
}

.mr-2 {
    margin-right: .5rem
}

.mr-3 {
    margin-right: .75rem
}

.mr-4 {
    margin-right: 1rem
}

.mr-6 {
    margin-right: 1.5rem
}

.mr-\[11px\] {
    margin-right: 11px
}

.mr-\[12px\] {
    margin-right: 12px
}

.mr-\[18px\] {
    margin-right: 18px
}

.mr-\[32px\] {
    margin-right: 32px
}

.mr-\[5px\] {
    margin-right: 5px
}

.mr-\[6px\] {
    margin-right: 6px
}

.mr-\[8px\] {
    margin-right: 8px
}

.mt-1 {
    margin-top: .25rem
}

.mt-2 {
    margin-top: .5rem
}

.mt-3 {
    margin-top: .75rem
}

.mt-4 {
    margin-top: 1rem
}

.mt-6 {
    margin-top: 1.5rem
}

.mt-8 {
    margin-top: 2rem
}

.mt-\[-100px\] {
    margin-top: -100px
}

.mt-\[-15px\] {
    margin-top: -15px
}

.mt-\[-2px\] {
    margin-top: -2px
}

.mt-\[0px\] {
    margin-top: 0
}

.mt-\[10px\] {
    margin-top: 10px
}

.mt-\[12px\] {
    margin-top: 12px
}

.mt-\[14px\] {
    margin-top: 14px
}

.mt-\[16px\] {
    margin-top: 16px
}

.mt-\[20\%\] {
    margin-top: 20%
}

.mt-\[21px\] {
    margin-top: 21px
}

.mt-\[23px\] {
    margin-top: 23px
}

.mt-\[24px\] {
    margin-top: 24px
}

.mt-\[2px\] {
    margin-top: 2px
}

.mt-\[30px\] {
    margin-top: 30px
}

.mt-\[30vh\] {
    margin-top: 30vh
}

.mt-\[39px\] {
    margin-top: 39px
}

.mt-\[40px\] {
    margin-top: 40px
}

.mt-\[42px\] {
    margin-top: 42px
}

.mt-\[4px\] {
    margin-top: 4px
}

.mt-\[6px\] {
    margin-top: 6px
}

.mt-\[6vh\] {
    margin-top: 6vh
}

.mt-\[70px\] {
    margin-top: 70px
}

.mt-\[71px\] {
    margin-top: 71px
}

.mt-\[80px\] {
    margin-top: 80px
}

.mt-\[8px\] {
    margin-top: 8px
}

.box-border {
    box-sizing: border-box
}

.line-clamp-1 {
    -webkit-line-clamp: 1
}

.line-clamp-1,
.line-clamp-2 {
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical
}

.line-clamp-2 {
    -webkit-line-clamp: 2
}

.line-clamp-3 {
    -webkit-line-clamp: 3
}

.line-clamp-3,
.line-clamp-4 {
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical
}

.line-clamp-4 {
    -webkit-line-clamp: 4
}

.block {
    display: block
}

.inline-block {
    display: inline-block
}

.inline {
    display: inline
}

.flex {
    display: flex
}

.inline-flex {
    display: inline-flex
}

.\ !table {
    display: table !important
}

.table {
    display: table
}

.table-row {
    display: table-row
}

.grid {
    display: grid
}

.contents {
    display: contents
}

.list-item {
    display: list-item
}

.\ !hidden {
    display: none !important
}

.hidden {
    display: none
}

.aspect-square {
    aspect-ratio: 1/1
}

.\ !h-auto {
    height: auto !important
}

.h-0 {
    height: 0
}

.h-1 {
    height: .25rem
}

.h-12 {
    height: 3rem
}

.h-14 {
    height: 3.5rem
}

.h-16 {
    height: 4rem
}

.h-2 {
    height: .5rem
}

.h-3 {
    height: .75rem
}

.h-3\.5 {
    height: .875rem
}

.h-32 {
    height: 8rem
}

.h-4 {
    height: 1rem
}

.h-5 {
    height: 1.25rem
}

.h-6 {
    height: 1.5rem
}

.h-8 {
    height: 2rem
}

.h-9 {
    height: 2.25rem
}

.h-\[100\%\] {
    height: 100%
}

.h-\[100px\] {
    height: 100px
}

.h-\[100vh\] {
    height: 100vh
}

.h-\[103px\] {
    height: 103px
}

.h-\[10px\] {
    height: 10px
}

.h-\[120px\] {
    height: 120px
}

.h-\[12px\] {
    height: 12px
}

.h-\[136px\] {
    height: 136px
}

.h-\[138px\] {
    height: 138px
}

.h-\[14px\] {
    height: 14px
}

.h-\[15px\] {
    height: 15px
}

.h-\[160px\] {
    height: 160px
}

.h-\[16px\] {
    height: 16px
}

.h-\[17px\] {
    height: 17px
}

.h-\[18px\] {
    height: 18px
}

.h-\[1px\] {
    height: 1px
}

.h-\[20px\] {
    height: 20px
}

.h-\[22px\] {
    height: 22px
}

.h-\[24px\] {
    height: 24px
}

.h-\[260px\] {
    height: 260px
}

.h-\[26px\] {
    height: 26px
}

.h-\[280px\] {
    height: 280px
}

.h-\[28px\] {
    height: 28px
}

.h-\[29px\] {
    height: 29px
}

.h-\[30px\] {
    height: 30px
}

.h-\[32px\] {
    height: 32px
}

.h-\[356px\] {
    height: 356px
}

.h-\[35px\] {
    height: 35px
}

.h-\[36px\] {
    height: 36px
}

.h-\[37px\] {
    height: 37px
}

.h-\[3px\] {
    height: 3px
}

.h-\[40px\] {
    height: 40px
}

.h-\[42px\] {
    height: 42px
}

.h-\[44px\] {
    height: 44px
}

.h-\[48px\] {
    height: 48px
}

.h-\[4px\] {
    height: 4px
}

.h-\[50px\] {
    height: 50px
}

.h-\[50vh\] {
    height: 50vh
}

.h-\[60px\] {
    height: 60px
}

.h-\[64px\] {
    height: 64px
}

.h-\[68px\] {
    height: 68px
}

.h-\[6px\] {
    height: 6px
}

.h-\[70vh\] {
    height: 70vh
}

.h-\[78px\] {
    height: 78px
}

.h-\[7px\] {
    height: 7px
}

.h-\[80px\] {
    height: 80px
}

.h-\[99\%\] {
    height: 99%
}

.h-\[9px\] {
    height: 9px
}

.h-auto {
    height: auto
}

.h-full {
    height: 100%
}

.h-screen {
    height: 100vh
}

.max-h-96 {
    max-height: 24rem
}

.max-h-\[100\%\] {
    max-height: 100%
}

.max-h-\[162px\] {
    max-height: 162px
}

.max-h-\[200px\] {
    max-height: 200px
}

.max-h-\[300px\] {
    max-height: 300px
}

.max-h-\[45vh\] {
    max-height: 45vh
}

.max-h-\[63px\] {
    max-height: 63px
}

.max-h-\[calc\(100vh-88px\)\] {
    max-height: calc(100vh - 88px)
}

.min-h-\[100px\] {
    min-height: 100px
}

.min-h-\[200px\] {
    min-height: 200px
}

.min-h-\[300px\] {
    min-height: 300px
}

.min-h-\[46px\] {
    min-height: 46px
}

.min-h-screen {
    min-height: 100vh
}

.\ !w-\[107px\] {
    width: 107px !important
}

.w-0 {
    width: 0
}

.w-1 {
    width: .25rem
}

.w-12 {
    width: 3rem
}

.w-14 {
    width: 3.5rem
}

.w-16 {
    width: 4rem
}

.w-2 {
    width: .5rem
}

.w-20 {
    width: 5rem
}

.w-3 {
    width: .75rem
}

.w-3\.5 {
    width: .875rem
}

.w-3\/4 {
    width: 75%
}

.w-4 {
    width: 1rem
}

.w-44 {
    width: 11rem
}

.w-48 {
    width: 12rem
}

.w-5 {
    width: 1.25rem
}

.w-6 {
    width: 1.5rem
}

.w-8 {
    width: 2rem
}

.w-\[100\%\] {
    width: 100%
}

.w-\[100px\] {
    width: 100px
}

.w-\[100vw\] {
    width: 100vw
}

.w-\[103px\] {
    width: 103px
}

.w-\[111\.19px\] {
    width: 111.19px
}

.w-\[129px\] {
    width: 129px
}

.w-\[12px\] {
    width: 12px
}

.w-\[138px\] {
    width: 138px
}

.w-\[139px\] {
    width: 139px
}

.w-\[14px\] {
    width: 14px
}

.w-\[15px\] {
    width: 15px
}

.w-\[168px\] {
    width: 168px
}

.w-\[16px\] {
    width: 16px
}

.w-\[17px\] {
    width: 17px
}

.w-\[18px\] {
    width: 18px
}

.w-\[1px\] {
    width: 1px
}

.w-\[20px\] {
    width: 20px
}

.w-\[210px\] {
    width: 210px
}

.w-\[22px\] {
    width: 22px
}

.w-\[240px\] {
    width: 240px
}

.w-\[246px\] {
    width: 246px
}

.w-\[24px\] {
    width: 24px
}

.w-\[257px\] {
    width: 257px
}

.w-\[26px\] {
    width: 26px
}

.w-\[281px\] {
    width: 281px
}

.w-\[28px\] {
    width: 28px
}

.w-\[29px\] {
    width: 29px
}

.w-\[30px\] {
    width: 30px
}

.w-\[32px\] {
    width: 32px
}

.w-\[361px\] {
    width: 361px
}

.w-\[36px\] {
    width: 36px
}

.w-\[3px\] {
    width: 3px
}

.w-\[42px\] {
    width: 42px
}

.w-\[44px\] {
    width: 44px
}

.w-\[461px\] {
    width: 461px
}

.w-\[48px\] {
    width: 48px
}

.w-\[4px\] {
    width: 4px
}

.w-\[50\%\] {
    width: 50%
}

.w-\[60px\] {
    width: 60px
}

.w-\[64px\] {
    width: 64px
}

.w-\[66px\] {
    width: 66px
}

.w-\[680px\] {
    width: 680px
}

.w-\[68px\] {
    width: 68px
}

.w-\[6px\] {
    width: 6px
}

.w-\[70\%\] {
    width: 70%
}

.w-\[74px\] {
    width: 74px
}

.w-\[78px\] {
    width: 78px
}

.w-\[7px\] {
    width: 7px
}

.w-\[80px\] {
    width: 80px
}

.w-\[8px\] {
    width: 8px
}

.w-\[90\%\] {
    width: 90%
}

.w-\[90vw\] {
    width: 90vw
}

.w-\[91px\] {
    width: 91px
}

.w-\[9x\] {
    width: 9x
}

.w-\[calc\(100\%-24px\)\] {
    width: calc(100% - 24px)
}

.w-\[calc\(100\%-32px\)\] {
    width: calc(100% - 32px)
}

.w-\[calc\(100\%-48px\)\] {
    width: calc(100% - 48px)
}

.w-\[calc\(100vw-32px\)\] {
    width: calc(100vw - 32px)
}

.w-\[calc\(50\%-6px\)\] {
    width: calc(50% - 6px)
}

.w-fit {
    width: -moz-fit-content;
    width: fit-content
}

.w-full {
    width: 100%
}

.w-px {
    width: 1px
}

.min-w-0 {
    min-width: 0
}

.min-w-\[130px\] {
    min-width: 130px
}

.min-w-\[6px\] {
    min-width: 6px
}

.max-w-2xl {
    max-width: 42rem
}

.max-w-6xl {
    max-width: 72rem
}

.max-w-\[100\%\] {
    max-width: 100%
}

.max-w-\[100vw\] {
    max-width: 100vw
}

.max-w-\[1200px\] {
    max-width: 1200px
}

.max-w-\[1463px\] {
    max-width: 1463px
}

.max-w-\[429px\] {
    max-width: 429px
}

.max-w-\[500px\] {
    max-width: 500px
}

.max-w-\[702px\] {
    max-width: 702px
}

.max-w-\[800px\] {
    max-width: 800px
}

.max-w-full {
    max-width: 100%
}

.flex-1 {
    flex: 1 1 0%
}

.flex-shrink {
    flex-shrink: 1
}

.flex-shrink-0 {
    flex-shrink: 0
}

.shrink {
    flex-shrink: 1
}

.flex-grow,
.grow {
    flex-grow: 1
}

.border-collapse {
    border-collapse: collapse
}

.translate-x-\[-50\%\] {
    --tw-translate-x: -50%
}

.rotate-0,
.translate-x-\[-50\%\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.rotate-0 {
    --tw-rotate: 0deg
}

.rotate-180 {
    --tw-rotate: 180deg
}

.rotate-180,
.scale-0 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.scale-0 {
    --tw-scale-x: 0;
    --tw-scale-y: 0
}

.scale-110 {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1
}

.scale-110,
.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

@keyframes fadeIn {
    0% {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

.animate-fadeIn {
    animation: fadeIn .2s ease-in-out
}

@keyframes loading {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.animate-loading {
    animation: loading 1s linear infinite
}

@keyframes pulse {
    50% {
        opacity: .5
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite
}

@keyframes skeleton {
    0%,
    80% {
        background-color: #f0f3f5
    }
    40%,
    to {
        background-color: #efefef
    }
}

.animate-skeleton {
    animation: skeleton 2s cubic-bezier(.36, 0, .64, 1) infinite
}

@keyframes spin {
    to {
        transform: rotate(1turn)
    }
}

.animate-spin {
    animation: spin 1s linear infinite
}

@keyframes task-bar-create {
    0% {
        transform: scale(1)
    }
    50% {
        transform: scaleX(1.3) scaleY(1.5)
    }
    to {
        transform: scale(1)
    }
}

.animate-task-bar-create {
    animation: task-bar-create 1.6s ease-in-out
}

@keyframes task-img-create {
    0% {
        transform: scale(1)
    }
    to {
        transform: scale(0)
    }
}

.animate-task-img-create {
    animation: task-img-create 1.6s ease-in-out
}

.cursor-ew-resize {
    cursor: ew-resize
}

.cursor-move {
    cursor: move
}

.cursor-nesw-resize {
    cursor: nesw-resize
}

.cursor-not-allowed {
    cursor: not-allowed
}

.cursor-nwse-resize {
    cursor: nwse-resize
}

.cursor-pointer {
    cursor: pointer
}

.cursor-text {
    cursor: text
}

.resize-none {
    resize: none
}

.resize {
    resize: both
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr))
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr))
}

.flex-row {
    flex-direction: row
}

.flex-col {
    flex-direction: column
}

.flex-wrap {
    flex-wrap: wrap
}

.items-start {
    align-items: flex-start
}

.items-end {
    align-items: flex-end
}

.items-center {
    align-items: center
}

.justify-start {
    justify-content: flex-start
}

.justify-end {
    justify-content: flex-end
}

.justify-center {
    justify-content: center
}

.justify-between {
    justify-content: space-between
}

.justify-around {
    justify-content: space-around
}

.gap-1 {
    gap: .25rem
}

.gap-1\.5 {
    gap: .375rem
}

.gap-12 {
    gap: 3rem
}

.gap-14 {
    gap: 3.5rem
}

.gap-16 {
    gap: 4rem
}

.gap-2 {
    gap: .5rem
}

.gap-2\.5 {
    gap: .625rem
}

.gap-3 {
    gap: .75rem
}

.gap-4 {
    gap: 1rem
}

.gap-6 {
    gap: 1.5rem
}

.gap-8 {
    gap: 2rem
}

.gap-\[10px\] {
    gap: 10px
}

.gap-\[11px\] {
    gap: 11px
}

.gap-\[12px\] {
    gap: 12px
}

.gap-\[16px\] {
    gap: 16px
}

.gap-\[20px\] {
    gap: 20px
}

.gap-\[24px\] {
    gap: 24px
}

.gap-\[3px\] {
    gap: 3px
}

.gap-\[4px\] {
    gap: 4px
}

.gap-\[5px\] {
    gap: 5px
}

.gap-\[65px\] {
    gap: 65px
}

.gap-\[6px\] {
    gap: 6px
}

.gap-\[75px\] {
    gap: 75px
}

.gap-\[8px\] {
    gap: 8px
}

.gap-\[9px\] {
    gap: 9px
}

.space-x-2>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-left: calc(.5rem*(1 - var(--tw-space-x-reverse)));
    margin-right: calc(.5rem*var(--tw-space-x-reverse))
}

.space-y-1>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(.25rem*var(--tw-space-y-reverse));
    margin-top: calc(.25rem*(1 - var(--tw-space-y-reverse)))
}

.space-y-4>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(1rem*var(--tw-space-y-reverse));
    margin-top: calc(1rem*(1 - var(--tw-space-y-reverse)))
}

.self-stretch {
    align-self: stretch
}

.overflow-auto {
    overflow: auto
}

.overflow-hidden {
    overflow: hidden
}

.overflow-visible {
    overflow: visible
}

.overflow-x-auto {
    overflow-x: auto
}

.overflow-y-auto {
    overflow-y: auto
}

.overflow-x-hidden {
    overflow-x: hidden
}

.truncate {
    overflow: hidden;
    white-space: nowrap
}

.text-ellipsis,
.truncate {
    text-overflow: ellipsis
}

.whitespace-nowrap {
    white-space: nowrap
}

.whitespace-pre-line {
    white-space: pre-line
}

.text-wrap {
    text-wrap: wrap
}

.break-words {
    overflow-wrap: break-word
}

.break-all {
    word-break: break-all
}

.\ !rounded-lg {
    border-radius: .5rem !important
}

.rounded {
    border-radius: .25rem
}

.rounded-2xl {
    border-radius: 1rem
}

.rounded-3xl {
    border-radius: 1.5rem
}

.rounded-\[10px\] {
    border-radius: 10px
}

.rounded-\[12px\] {
    border-radius: 12px
}

.rounded-\[16px\] {
    border-radius: 16px
}

.rounded-\[17\.40px\] {
    border-radius: 17.4px
}

.rounded-\[20px\] {
    border-radius: 20px
}

.rounded-\[24px\] {
    border-radius: 24px
}

.rounded-\[25\.85px\] {
    border-radius: 25.85px
}

.rounded-\[2px\] {
    border-radius: 2px
}

.rounded-\[30px\] {
    border-radius: 30px
}

.rounded-\[31px\] {
    border-radius: 31px
}

.rounded-\[35\%\] {
    border-radius: 35%
}

.rounded-\[35px\] {
    border-radius: 35px
}

.rounded-\[36px\] {
    border-radius: 36px
}

.rounded-\[3px\] {
    border-radius: 3px
}

.rounded-\[40px\] {
    border-radius: 40px
}

.rounded-\[43px\] {
    border-radius: 43px
}

.rounded-\[50\%\] {
    border-radius: 50%
}

.rounded-\[50px\] {
    border-radius: 50px
}

.rounded-\[57px\] {
    border-radius: 57px
}

.rounded-\[5px\] {
    border-radius: 5px
}

.rounded-\[60px\] {
    border-radius: 60px
}

.rounded-\[6px\] {
    border-radius: 6px
}

.rounded-\[8px\] {
    border-radius: 8px
}

.rounded-full {
    border-radius: 9999px
}

.rounded-lg {
    border-radius: .5rem
}

.rounded-md {
    border-radius: .375rem
}

.rounded-xl {
    border-radius: .75rem
}

.rounded-b-3xl {
    border-bottom-left-radius: 1.5rem;
    border-bottom-right-radius: 1.5rem
}

.rounded-b-\[12px\] {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px
}

.rounded-l-\[27px\] {
    border-bottom-left-radius: 27px;
    border-top-left-radius: 27px
}

.rounded-t-2xl {
    border-top-left-radius: 1rem;
    border-top-right-radius: 1rem
}

.rounded-bl-\[12px\] {
    border-bottom-left-radius: 12px
}

.rounded-bl-\[1px\] {
    border-bottom-left-radius: 1px
}

.rounded-bl-sm {
    border-bottom-left-radius: .125rem
}

.rounded-br {
    border-bottom-right-radius: .25rem
}

.rounded-br-lg {
    border-bottom-right-radius: .5rem
}

.rounded-tl {
    border-top-left-radius: .25rem
}

.rounded-tl-lg {
    border-top-left-radius: .5rem
}

.rounded-tl-xl {
    border-top-left-radius: .75rem
}

.rounded-tr {
    border-top-right-radius: .25rem
}

.rounded-tr-\[12px\] {
    border-top-right-radius: 12px
}

.rounded-tr-lg {
    border-top-right-radius: .5rem
}

.rounded-tr-xl {
    border-top-right-radius: .75rem
}

.border {
    border-width: 1px
}

.border-0 {
    border-width: 0
}

.border-2,
.border-\[2px\] {
    border-width: 2px
}

.border-b {
    border-bottom-width: 1px
}

.border-b-2 {
    border-bottom-width: 2px
}

.border-b-\[8px\] {
    border-bottom-width: 8px
}

.border-l-\[12px\] {
    border-left-width: 12px
}

.border-l-\[6px\] {
    border-left-width: 6px
}

.border-r-\[6px\] {
    border-right-width: 6px
}

.border-t {
    border-top-width: 1px
}

.border-t-\[6px\] {
    border-top-width: 6px
}

.border-t-\[8px\] {
    border-top-width: 8px
}

.border-solid {
    border-style: solid
}

.border-none {
    border-style: none
}

.border-\[\#00000008\] {
    border-color: #00000008
}

.border-\[\#000\] {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 0/var(--tw-border-opacity, 1))
}

.border-\[\#0F7FFF\] {
    --tw-border-opacity: 1;
    border-color: rgb(15 127 255/var(--tw-border-opacity, 1))
}

.border-\[\#232425\] {
    --tw-border-opacity: 1;
    border-color: rgb(35 36 37/var(--tw-border-opacity, 1))
}

.border-\[\#D9EAFF\] {
    --tw-border-opacity: 1;
    border-color: rgb(217 234 255/var(--tw-border-opacity, 1))
}

.border-\[\#DEC6C5\] {
    --tw-border-opacity: 1;
    border-color: rgb(222 198 197/var(--tw-border-opacity, 1))
}

.border-\[\#EAEAEA\] {
    --tw-border-opacity: 1;
    border-color: rgb(234 234 234/var(--tw-border-opacity, 1))
}

.border-\[\#EFEFEF\] {
    --tw-border-opacity: 1;
    border-color: rgb(239 239 239/var(--tw-border-opacity, 1))
}

.border-\[\#eaeaea\] {
    --tw-border-opacity: 1;
    border-color: rgb(234 234 234/var(--tw-border-opacity, 1))
}

.border-\[\#efefef\] {
    --tw-border-opacity: 1;
    border-color: rgb(239 239 239/var(--tw-border-opacity, 1))
}

.border-black {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 0/var(--tw-border-opacity, 1))
}

.border-black\/20 {
    border-color: #0003
}

.border-blue-200 {
    --tw-border-opacity: 1;
    border-color: rgb(191 219 254/var(--tw-border-opacity, 1))
}

.border-blue-500 {
    --tw-border-opacity: 1;
    border-color: rgb(59 130 246/var(--tw-border-opacity, 1))
}

.border-gray-100 {
    --tw-border-opacity: 1;
    border-color: rgb(243 244 246/var(--tw-border-opacity, 1))
}

.border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-border-opacity, 1))
}

.border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-border-opacity, 1))
}

.border-gray-600 {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99/var(--tw-border-opacity, 1))
}

.border-gray-700 {
    --tw-border-opacity: 1;
    border-color: rgb(55 65 81/var(--tw-border-opacity, 1))
}

.border-green-200 {
    --tw-border-opacity: 1;
    border-color: rgb(187 247 208/var(--tw-border-opacity, 1))
}

.border-red-200 {
    --tw-border-opacity: 1;
    border-color: rgb(254 202 202/var(--tw-border-opacity, 1))
}

.border-transparent {
    border-color: transparent
}

.border-white {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255/var(--tw-border-opacity, 1))
}

.border-zinc-100 {
    --tw-border-opacity: 1;
    border-color: rgb(244 244 245/var(--tw-border-opacity, 1))
}

.border-b-transparent {
    border-bottom-color: transparent
}

.border-l-transparent {
    border-left-color: transparent
}

.border-l-white {
    --tw-border-opacity: 1;
    border-left-color: rgb(255 255 255/var(--tw-border-opacity, 1))
}

.border-r-transparent {
    border-right-color: transparent
}

.border-t-\[\#232425\] {
    --tw-border-opacity: 1;
    border-top-color: rgb(35 36 37/var(--tw-border-opacity, 1))
}

.border-t-gray-900 {
    --tw-border-opacity: 1;
    border-top-color: rgb(17 24 39/var(--tw-border-opacity, 1))
}

.border-t-transparent {
    border-top-color: transparent
}

.\ !bg-slate-900 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(15 23 42/var(--tw-bg-opacity, 1)) !important
}

.\ !bg-transparent {
    background-color: transparent !important
}

.bg-\[\#00000015\] {
    background-color: #00000015
}

.bg-\[\#00000066\] {
    background-color: #0006
}

.bg-\[\#000\] {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity, 1))
}

.bg-\[\#0D1216B2\] {
    background-color: #0d1216b2
}

.bg-\[\#0F7FFF\] {
    --tw-bg-opacity: 1;
    background-color: rgb(15 127 255/var(--tw-bg-opacity, 1))
}

.bg-\[\#0f7eff\] {
    --tw-bg-opacity: 1;
    background-color: rgb(15 126 255/var(--tw-bg-opacity, 1))
}

.bg-\[\#0f7fff\] {
    --tw-bg-opacity: 1;
    background-color: rgb(15 127 255/var(--tw-bg-opacity, 1))
}

.bg-\[\#232425\] {
    --tw-bg-opacity: 1;
    background-color: rgb(35 36 37/var(--tw-bg-opacity, 1))
}

.bg-\[\#232425\]\/60 {
    background-color: #23242599
}

.bg-\[\#232425\]\/65 {
    background-color: #232425a6
}

.bg-\[\#262626\] {
    --tw-bg-opacity: 1;
    background-color: rgb(38 38 38/var(--tw-bg-opacity, 1))
}

.bg-\[\#D9D9D9\] {
    --tw-bg-opacity: 1;
    background-color: rgb(217 217 217/var(--tw-bg-opacity, 1))
}

.bg-\[\#D9EAFF\] {
    --tw-bg-opacity: 1;
    background-color: rgb(217 234 255/var(--tw-bg-opacity, 1))
}

.bg-\[\#DEDEDE\] {
    --tw-bg-opacity: 1;
    background-color: rgb(222 222 222/var(--tw-bg-opacity, 1))
}

.bg-\[\#EAEAEA\] {
    --tw-bg-opacity: 1;
    background-color: rgb(234 234 234/var(--tw-bg-opacity, 1))
}

.bg-\[\#EBE1E1\] {
    --tw-bg-opacity: 1;
    background-color: rgb(235 225 225/var(--tw-bg-opacity, 1))
}

.bg-\[\#F3F9FF\] {
    --tw-bg-opacity: 1;
    background-color: rgb(243 249 255/var(--tw-bg-opacity, 1))
}

.bg-\[\#F5F5F5\] {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 245/var(--tw-bg-opacity, 1))
}

.bg-\[\#F6ECEC\] {
    --tw-bg-opacity: 1;
    background-color: rgb(246 236 236/var(--tw-bg-opacity, 1))
}

.bg-\[\#FAFAFA\] {
    --tw-bg-opacity: 1;
    background-color: rgb(250 250 250/var(--tw-bg-opacity, 1))
}

.bg-\[\#FFFBEE\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 251 238/var(--tw-bg-opacity, 1))
}

.bg-\[\#FFFBFB\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 251 251/var(--tw-bg-opacity, 1))
}

.bg-\[\#FFFFFF33\] {
    background-color: #fff3
}

.bg-\[\#FFFFFF\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity, 1))
}

.bg-\[\#ccc\] {
    --tw-bg-opacity: 1;
    background-color: rgb(204 204 204/var(--tw-bg-opacity, 1))
}

.bg-\[\#d9d9d9\]\/0 {
    background-color: #d9d9d900
}

.bg-\[\#eeeeee\] {
    --tw-bg-opacity: 1;
    background-color: rgb(238 238 238/var(--tw-bg-opacity, 1))
}

.bg-\[\#efefef\] {
    --tw-bg-opacity: 1;
    background-color: rgb(239 239 239/var(--tw-bg-opacity, 1))
}

.bg-\[\#f0f3f5\] {
    --tw-bg-opacity: 1;
    background-color: rgb(240 243 245/var(--tw-bg-opacity, 1))
}

.bg-\[\#f4f4f4\] {
    --tw-bg-opacity: 1;
    background-color: rgb(244 244 244/var(--tw-bg-opacity, 1))
}

.bg-\[\#f5f5f5\] {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 245/var(--tw-bg-opacity, 1))
}

.bg-\[\#f7f7f7\] {
    --tw-bg-opacity: 1;
    background-color: rgb(247 247 247/var(--tw-bg-opacity, 1))
}

.bg-\[\#fafafa\] {
    --tw-bg-opacity: 1;
    background-color: rgb(250 250 250/var(--tw-bg-opacity, 1))
}

.bg-\[\#fff\],
.bg-\[\#ffffff\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity, 1))
}

.bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity, 1))
}

.bg-black\/40 {
    background-color: #0006
}

.bg-black\/60 {
    background-color: #0009
}

.bg-black\/90 {
    background-color: #000000e6
}

.bg-blue-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(219 234 254/var(--tw-bg-opacity, 1))
}

.bg-blue-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(239 246 255/var(--tw-bg-opacity, 1))
}

.bg-blue-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(59 130 246/var(--tw-bg-opacity, 1))
}

.bg-blue-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(37 99 235/var(--tw-bg-opacity, 1))
}

.bg-gray-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity, 1))
}

.bg-gray-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235/var(--tw-bg-opacity, 1))
}

.bg-gray-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(209 213 219/var(--tw-bg-opacity, 1))
}

.bg-gray-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251/var(--tw-bg-opacity, 1))
}

.bg-gray-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(75 85 99/var(--tw-bg-opacity, 1))
}

.bg-gray-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55/var(--tw-bg-opacity, 1))
}

.bg-gray-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39/var(--tw-bg-opacity, 1))
}

.bg-green-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(220 252 231/var(--tw-bg-opacity, 1))
}

.bg-green-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(240 253 244/var(--tw-bg-opacity, 1))
}

.bg-green-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(22 163 74/var(--tw-bg-opacity, 1))
}

.bg-indigo-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(224 231 255/var(--tw-bg-opacity, 1))
}

.bg-neutral-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 245/var(--tw-bg-opacity, 1))
}

.bg-neutral-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(250 250 250/var(--tw-bg-opacity, 1))
}

.bg-neutral-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(38 38 38/var(--tw-bg-opacity, 1))
}

.bg-orange-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 237 213/var(--tw-bg-opacity, 1))
}

.bg-pink-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(252 231 243/var(--tw-bg-opacity, 1))
}

.bg-purple-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 232 255/var(--tw-bg-opacity, 1))
}

.bg-red-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 226 226/var(--tw-bg-opacity, 1))
}

.bg-red-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 242 242/var(--tw-bg-opacity, 1))
}

.bg-red-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(239 68 68/var(--tw-bg-opacity, 1))
}

.bg-red-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(220 38 38/var(--tw-bg-opacity, 1))
}

.bg-rose-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 228 230/var(--tw-bg-opacity, 1))
}

.bg-rose-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(244 63 94/var(--tw-bg-opacity, 1))
}

.bg-sky-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(240 249 255/var(--tw-bg-opacity, 1))
}

.bg-teal-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(204 251 241/var(--tw-bg-opacity, 1))
}

.bg-transparent {
    background-color: transparent
}

.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity, 1))
}

.bg-white\/45 {
    background-color: #ffffff73
}

.bg-yellow-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 249 195/var(--tw-bg-opacity, 1))
}

.bg-opacity-50 {
    --tw-bg-opacity: .5
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops))
}

.from-\[\#F5F5FF\] {
    --tw-gradient-from: #f5f5ff var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(245, 245, 255, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.to-\[\#E5EDFB\] {
    --tw-gradient-to: #e5edfb var(--tw-gradient-to-position)
}

.bg-cover {
    background-size: cover
}

.bg-center {
    background-position: 50%
}

.object-contain {
    -o-object-fit: contain;
    object-fit: contain
}

.object-cover {
    -o-object-fit: cover;
    object-fit: cover
}

.\ !p-0 {
    padding: 0 !important
}

.p-0 {
    padding: 0
}

.p-1 {
    padding: .25rem
}

.p-12 {
    padding: 3rem
}

.p-2 {
    padding: .5rem
}

.p-3 {
    padding: .75rem
}

.p-4 {
    padding: 1rem
}

.p-5 {
    padding: 1.25rem
}

.p-6 {
    padding: 1.5rem
}

.p-\[10px\] {
    padding: 10px
}

.p-\[12px\] {
    padding: 12px
}

.p-\[16px\] {
    padding: 16px
}

.p-\[20px\] {
    padding: 20px
}

.p-\[2px\] {
    padding: 2px
}

.p-\[4px\] {
    padding: 4px
}

.p-\[6px\] {
    padding: 6px
}

.p-\[8px\] {
    padding: 8px
}

.px-1\.5 {
    padding-left: .375rem;
    padding-right: .375rem
}

.px-12 {
    padding-left: 3rem;
    padding-right: 3rem
}

.px-2 {
    padding-left: .5rem;
    padding-right: .5rem
}

.px-3 {
    padding-left: .75rem;
    padding-right: .75rem
}

.px-3\.5 {
    padding-left: .875rem;
    padding-right: .875rem
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem
}

.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem
}

.px-\[11px\] {
    padding-left: 11px;
    padding-right: 11px
}

.px-\[12px\] {
    padding-left: 12px;
    padding-right: 12px
}

.px-\[13px\] {
    padding-left: 13px;
    padding-right: 13px
}

.px-\[14px\] {
    padding-left: 14px;
    padding-right: 14px
}

.px-\[16px\] {
    padding-left: 16px;
    padding-right: 16px
}

.px-\[20px\] {
    padding-left: 20px;
    padding-right: 20px
}

.px-\[23px\] {
    padding-left: 23px;
    padding-right: 23px
}

.px-\[30px\] {
    padding-left: 30px;
    padding-right: 30px
}

.px-\[35px\] {
    padding-left: 35px;
    padding-right: 35px
}

.px-\[48px\] {
    padding-left: 48px;
    padding-right: 48px
}

.px-\[4px\] {
    padding-left: 4px;
    padding-right: 4px
}

.px-\[6px\] {
    padding-left: 6px;
    padding-right: 6px
}

.px-\[8px\] {
    padding-left: 8px;
    padding-right: 8px
}

.py-0\.5 {
    padding-bottom: .125rem;
    padding-top: .125rem
}

.py-1 {
    padding-bottom: .25rem;
    padding-top: .25rem
}

.py-1\.5 {
    padding-bottom: .375rem;
    padding-top: .375rem
}

.py-10 {
    padding-bottom: 2.5rem;
    padding-top: 2.5rem
}

.py-12 {
    padding-bottom: 3rem;
    padding-top: 3rem
}

.py-2 {
    padding-bottom: .5rem;
    padding-top: .5rem
}

.py-2\.5 {
    padding-bottom: .625rem;
    padding-top: .625rem
}

.py-3 {
    padding-bottom: .75rem;
    padding-top: .75rem
}

.py-4 {
    padding-bottom: 1rem;
    padding-top: 1rem
}

.py-5 {
    padding-bottom: 1.25rem;
    padding-top: 1.25rem
}

.py-6 {
    padding-bottom: 1.5rem;
    padding-top: 1.5rem
}

.py-8 {
    padding-bottom: 2rem;
    padding-top: 2rem
}

.py-\[10px\] {
    padding-bottom: 10px;
    padding-top: 10px
}

.py-\[120px\] {
    padding-bottom: 120px;
    padding-top: 120px
}

.py-\[15px\] {
    padding-bottom: 15px;
    padding-top: 15px
}

.py-\[16px\] {
    padding-bottom: 16px;
    padding-top: 16px
}

.py-\[18px\] {
    padding-bottom: 18px;
    padding-top: 18px
}

.py-\[20px\] {
    padding-bottom: 20px;
    padding-top: 20px
}

.py-\[23px\] {
    padding-bottom: 23px;
    padding-top: 23px
}

.py-\[26px\] {
    padding-bottom: 26px;
    padding-top: 26px
}

.py-\[3px\] {
    padding-bottom: 3px;
    padding-top: 3px
}

.py-\[4px\] {
    padding-bottom: 4px;
    padding-top: 4px
}

.py-\[50px\] {
    padding-bottom: 50px;
    padding-top: 50px
}

.py-\[5px\] {
    padding-bottom: 5px;
    padding-top: 5px
}

.py-\[6\.52px\] {
    padding-bottom: 6.52px;
    padding-top: 6.52px
}

.py-\[6px\] {
    padding-bottom: 6px;
    padding-top: 6px
}

.py-\[7px\] {
    padding-bottom: 7px;
    padding-top: 7px
}

.py-\[8px\] {
    padding-bottom: 8px;
    padding-top: 8px
}

.py-\[9px\] {
    padding-bottom: 9px;
    padding-top: 9px
}

.pb-2 {
    padding-bottom: .5rem
}

.pb-3 {
    padding-bottom: .75rem
}

.pb-4 {
    padding-bottom: 1rem
}

.pb-5 {
    padding-bottom: 1.25rem
}

.pb-\[100px\] {
    padding-bottom: 100px
}

.pb-\[10px\] {
    padding-bottom: 10px
}

.pb-\[12px\] {
    padding-bottom: 12px
}

.pb-\[16px\] {
    padding-bottom: 16px
}

.pb-\[200px\] {
    padding-bottom: 200px
}

.pb-\[20px\] {
    padding-bottom: 20px
}

.pb-\[24px\] {
    padding-bottom: 24px
}

.pb-\[45px\] {
    padding-bottom: 45px
}

.pb-\[7px\] {
    padding-bottom: 7px
}

.pb-\[8px\] {
    padding-bottom: 8px
}

.pl-\[12px\] {
    padding-left: 12px
}

.pl-\[14px\] {
    padding-left: 14px
}

.pl-\[16px\] {
    padding-left: 16px
}

.pl-\[8px\] {
    padding-left: 8px
}

.pr-10 {
    padding-right: 2.5rem
}

.pr-\[16px\] {
    padding-right: 16px
}

.pr-\[20px\] {
    padding-right: 20px
}

.pr-\[24px\] {
    padding-right: 24px
}

.pr-\[30px\] {
    padding-right: 30px
}

.pr-\[48px\] {
    padding-right: 48px
}

.pr-\[50px\] {
    padding-right: 50px
}

.pr-\[6px\] {
    padding-right: 6px
}

.pt-0 {
    padding-top: 0
}

.pt-4 {
    padding-top: 1rem
}

.pt-\[0px\] {
    padding-top: 0
}

.pt-\[100px\] {
    padding-top: 100px
}

.pt-\[12px\] {
    padding-top: 12px
}

.pt-\[14px\] {
    padding-top: 14px
}

.pt-\[15px\] {
    padding-top: 15px
}

.pt-\[16px\] {
    padding-top: 16px
}

.pt-\[20px\] {
    padding-top: 20px
}

.pt-\[24px\] {
    padding-top: 24px
}

.pt-\[30px\] {
    padding-top: 30px
}

.pt-\[32px\] {
    padding-top: 32px
}

.pt-\[50px\] {
    padding-top: 50px
}

.pt-\[60px\] {
    padding-top: 60px
}

.text-left {
    text-align: left
}

.text-center {
    text-align: center
}

.text-right {
    text-align: right
}

.align-middle {
    vertical-align: middle
}

.font-\[\'Arial\'\]{font-family:Arial}.font-\[\'Cabin\'\]{font-family:Cabin}.font-\[\'Helvetica_Neue\'\]{font-family:Helvetica Neue}.font-\[\'Inter\'\]{font-family:Inter}.font-mono{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.text-2xl{font-size:1.5rem;line-height:2rem}.text-3xl{font-size:1.875rem;line-height:2.25rem}.text-4xl{font-size:2.25rem;line-height:2.5rem}.text-7xl{font-size:4.5rem;line-height:1}.text-\[10px\]{font-size:10px}.text-\[11px\]{font-size:11px}.text-\[12px\]{font-size:12px}.text-\[13px\]{font-size:13px}.text-\[14px\]{font-size:14px}.text-\[16px\]{font-size:16px}.text-\[22px\]{font-size:22px}.text-\[32px\]{font-size:32px}.text-\[48px\]{font-size:48px}.text-\[5px\]{font-size:5px}.text-\[60px\]{font-size:60px}.text-\[7px\]{font-size:7px}.text-base{font-size:1rem;line-height:1.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:.75rem;line-height:1rem}.font-\[700\],.font-bold{font-weight:700}.font-light{font-weight:300}.font-medium{font-weight:500}.font-normal{font-weight:400}.font-semibold{font-weight:600}.uppercase{text-transform:uppercase}.capitalize{text-transform:capitalize}.italic{font-style:italic}.leading-10{line-height:2.5rem}.leading-6{line-height:1.5rem}.leading-7{line-height:1.75rem}.leading-\[1\.2\]{line-height:1.2}.leading-\[1\.5\]{line-height:1.5}.leading-\[1\.5em\]{line-height:1.5em}.leading-\[150\%\]{line-height:150%}.leading-\[15px\]{line-height:15px}.leading-\[16px\]{line-height:16px}.leading-\[18px\]{line-height:18px}.leading-\[1\]{line-height:1}.leading-\[20px\]{line-height:20px}.leading-\[21px\]{line-height:21px}.leading-\[24px\]{line-height:24px}.leading-loose{line-height:2}.leading-none{line-height:1}.leading-normal{line-height:1.5}.leading-tight{line-height:1.25}.tracking-tight{letter-spacing:-.025em}.text-\[\#000\]{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity,1))}.text-\[\#0F7FFF\]{--tw-text-opacity:1;color:rgb(15 127 255/var(--tw-text-opacity,1))}.text-\[\#1a202c\]{--tw-text-opacity:1;color:rgb(26 32 44/var(--tw-text-opacity,1))}.text-\[\#222325\]{--tw-text-opacity:1;color:rgb(34 35 37/var(--tw-text-opacity,1))}.text-\[\#232425\]{--tw-text-opacity:1;color:rgb(35 36 37/var(--tw-text-opacity,1))}.text-\[\#262626\]{--tw-text-opacity:1;color:rgb(38 38 38/var(--tw-text-opacity,1))}.text-\[\#4a5568\]{--tw-text-opacity:1;color:rgb(74 85 104/var(--tw-text-opacity,1))}.text-\[\#4b5563\]{--tw-text-opacity:1;color:rgb(75 85 99/var(--tw-text-opacity,1))}.text-\[\#5CD4A1\]{--tw-text-opacity:1;color:rgb(92 212 161/var(--tw-text-opacity,1))}.text-\[\#606366\]{--tw-text-opacity:1;color:rgb(96 99 102/var(--tw-text-opacity,1))}.text-\[\#909499\]{--tw-text-opacity:1;color:rgb(144 148 153/var(--tw-text-opacity,1))}.text-\[\#FF3D3D\]{--tw-text-opacity:1;color:rgb(255 61 61/var(--tw-text-opacity,1))}.text-\[\#ccc\]{--tw-text-opacity:1;color:rgb(204 204 204/var(--tw-text-opacity,1))}.text-\[\#cdcdcd\]{--tw-text-opacity:1;color:rgb(205 205 205/var(--tw-text-opacity,1))}.text-\[\#ff3d3d\]{--tw-text-opacity:1;color:rgb(255 61 61/var(--tw-text-opacity,1))}.text-\[\#fff\]{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1))}.text-black{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity,1))}.text-blue-500{--tw-text-opacity:1;color:rgb(59 130 246/var(--tw-text-opacity,1))}.text-blue-600{--tw-text-opacity:1;color:rgb(37 99 235/var(--tw-text-opacity,1))}.text-blue-700{--tw-text-opacity:1;color:rgb(29 78 216/var(--tw-text-opacity,1))}.text-blue-800{--tw-text-opacity:1;color:rgb(30 64 175/var(--tw-text-opacity,1))}.text-gray-300{--tw-text-opacity:1;color:rgb(209 213 219/var(--tw-text-opacity,1))}.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175/var(--tw-text-opacity,1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity,1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99/var(--tw-text-opacity,1))}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81/var(--tw-text-opacity,1))}.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55/var(--tw-text-opacity,1))}.text-gray-900{--tw-text-opacity:1;color:rgb(17 24 39/var(--tw-text-opacity,1))}.text-green-600{--tw-text-opacity:1;color:rgb(22 163 74/var(--tw-text-opacity,1))}.text-green-800{--tw-text-opacity:1;color:rgb(22 101 52/var(--tw-text-opacity,1))}.text-indigo-600{--tw-text-opacity:1;color:rgb(79 70 229/var(--tw-text-opacity,1))}.text-neutral-400{--tw-text-opacity:1;color:rgb(163 163 163/var(--tw-text-opacity,1))}.text-neutral-50{--tw-text-opacity:1;color:rgb(250 250 250/var(--tw-text-opacity,1))}.text-neutral-800{--tw-text-opacity:1;color:rgb(38 38 38/var(--tw-text-opacity,1))}.text-orange-600{--tw-text-opacity:1;color:rgb(234 88 12/var(--tw-text-opacity,1))}.text-pink-600{--tw-text-opacity:1;color:rgb(219 39 119/var(--tw-text-opacity,1))}.text-pink-950{--tw-text-opacity:1;color:rgb(80 7 36/var(--tw-text-opacity,1))}.text-purple-600{--tw-text-opacity:1;color:rgb(147 51 234/var(--tw-text-opacity,1))}.text-red-500{--tw-text-opacity:1;color:rgb(239 68 68/var(--tw-text-opacity,1))}.text-red-600{--tw-text-opacity:1;color:rgb(220 38 38/var(--tw-text-opacity,1))}.text-red-700{--tw-text-opacity:1;color:rgb(185 28 28/var(--tw-text-opacity,1))}.text-red-800{--tw-text-opacity:1;color:rgb(153 27 27/var(--tw-text-opacity,1))}.text-rose-600{--tw-text-opacity:1;color:rgb(225 29 72/var(--tw-text-opacity,1))}.text-stone-500{--tw-text-opacity:1;color:rgb(120 113 108/var(--tw-text-opacity,1))}.text-teal-600{--tw-text-opacity:1;color:rgb(13 148 136/var(--tw-text-opacity,1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1))}.text-yellow-600{--tw-text-opacity:1;color:rgb(202 138 4/var(--tw-text-opacity,1))}.text-yellow-800{--tw-text-opacity:1;color:rgb(133 77 14/var(--tw-text-opacity,1))}.text-zinc-600{--tw-text-opacity:1;color:rgb(82 82 91/var(--tw-text-opacity,1))}.underline{text-decoration-line:underline}.line-through{text-decoration-line:line-through}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.opacity-0{opacity:0}.opacity-25{opacity:.25}.opacity-50{opacity:.5}.opacity-75{opacity:.75}.\!shadow{--tw-shadow:0 1px 3px 0 rgba(0,0,0,.1),0 1px 2px -1px rgba(0,0,0,.1)!important;--tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color),0 1px 2px -1px var(--tw-shadow-color)!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}.shadow{--tw-shadow:0 1px 3px 0 rgba(0,0,0,.1),0 1px 2px -1px rgba(0,0,0,.1);--tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color),0 1px 2px -1px var(--tw-shadow-color)}.shadow,.shadow-2xl{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.shadow-2xl{--tw-shadow:0 25px 50px -12px rgba(0,0,0,.25);--tw-shadow-colored:0 25px 50px -12px var(--tw-shadow-color)}.shadow-\[0_0_10px_10px_rgba\(0\,0\,0\,0\.08\)\]{--tw-shadow:0 0 10px 10px rgba(0,0,0,.08);--tw-shadow-colored:0 0 10px 10px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.shadow-\[0px_2px_4px_0px_rgba\(0\,0\,0\,0\.05\)\]{--tw-shadow:0px 2px 4px 0px rgba(0,0,0,.05);--tw-shadow-colored:0px 2px 4px 0px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.shadow-\[0px_3px_30px_0px_rgba\(0\,0\,0\,0\.08\)\]{--tw-shadow:0px 3px 30px 0px rgba(0,0,0,.08);--tw-shadow-colored:0px 3px 30px 0px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.shadow-\[0px_4px_10px_0px_rgba\(0\,0\,0\,0\.15\)\]{--tw-shadow:0px 4px 10px 0px rgba(0,0,0,.15);--tw-shadow-colored:0px 4px 10px 0px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.shadow-\[0px_4px_15px_0px_rgba\(0\,0\,0\,0\.10\)\]{--tw-shadow:0px 4px 15px 0px rgba(0,0,0,.1);--tw-shadow-colored:0px 4px 15px 0px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.shadow-\[0px_4px_20px_0px_rgba\(0\,0\,0\,0\.08\)\]{--tw-shadow:0px 4px 20px 0px rgba(0,0,0,.08);--tw-shadow-colored:0px 4px 20px 0px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.shadow-\[0px_6px_30px_0px_rgba\(0\,0\,0\,0\.08\)\]{--tw-shadow:0px 6px 30px 0px rgba(0,0,0,.08);--tw-shadow-colored:0px 6px 30px 0px var(--tw-shadow-color)}.shadow-\[0px_6px_30px_0px_rgba\(0\,0\,0\,0\.08\)\],.shadow-lg{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px rgba(0,0,0,.1),0 4px 6px -4px rgba(0,0,0,.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color),0 4px 6px -4px var(--tw-shadow-color)}.shadow-sm{--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.outline-none{outline:2px solid transparent;outline-offset:2px}.\!outline{outline-style:solid!important}.outline{outline-style:solid}.outline-1{outline-width:1px}.outline-2{outline-width:2px}.outline-offset-\[-1px\]{outline-offset:-1px}.outline-blue-100{outline-color:#dbeafe}.outline-blue-600{outline-color:#2563eb}.ring{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}.blur{--tw-blur:blur(8px)}.blur,.drop-shadow{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.drop-shadow{--tw-drop-shadow:drop-shadow(0 1px 2px rgba(0,0,0,.1)) drop-shadow(0 1px 1px rgba(0,0,0,.06))}.grayscale{--tw-grayscale:grayscale(100%)}.grayscale,.invert{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.invert{--tw-invert:invert(100%)}.sepia{--tw-sepia:sepia(100%)}.filter,.sepia{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.backdrop-filter{-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.transition{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1)}.transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.transition-transform{transition-duration:.15s;transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1)}.duration-200{transition-duration:.2s}.duration-300{transition-duration:.3s}.duration-500{transition-duration:.5s}.ease-in{transition-timing-function:cubic-bezier(.4,0,1,1)}.ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.ease-out{transition-timing-function:cubic-bezier(0,0,.2,1)}*{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;border-style:solid;border-width:0}img{vertical-align:middle}.line-ellipsis-1{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.placeholder\:text-\[\#909499\]::-moz-placeholder{--tw-text-opacity:1;color:rgb(144 148 153/var(--tw-text-opacity,1))}.placeholder\:text-\[\#909499\]::placeholder{--tw-text-opacity:1;color:rgb(144 148 153/var(--tw-text-opacity,1))}.hover\:translate-y-\[-2px\]:hover{--tw-translate-y:-2px}.hover\:scale-105:hover,.hover\:translate-y-\[-2px\]:hover{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.hover\:scale-105:hover{--tw-scale-x:1.05;--tw-scale-y:1.05}.hover\:scale-110:hover{--tw-scale-x:1.1;--tw-scale-y:1.1}.hover\:scale-110:hover,.hover\:scale-125:hover{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.hover\:scale-125:hover{--tw-scale-x:1.25;--tw-scale-y:1.25}.hover\:scale-\[1\.02\]:hover{--tw-scale-x:1.02;--tw-scale-y:1.02}.hover\:scale-\[1\.02\]:hover,.hover\:scale-\[1\.2\]:hover{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.hover\:scale-\[1\.2\]:hover{--tw-scale-x:1.2;--tw-scale-y:1.2}.hover\:rounded-\[35\%\]:hover{border-radius:35%}.hover\:border-gray-300:hover{--tw-border-opacity:1;border-color:rgb(209 213 219/var(--tw-border-opacity,1))}.hover\:bg-\[\#0000000d\]:hover{background-color:#0000000d}.hover\:bg-\[\#0D1216CC\]:hover{background-color:#0d1216cc}.hover\:bg-\[\#0E72E5\]:hover{--tw-bg-opacity:1;background-color:rgb(14 114 229/var(--tw-bg-opacity,1))}.hover\:bg-\[\#0E72E6\]:hover{--tw-bg-opacity:1;background-color:rgb(14 114 230/var(--tw-bg-opacity,1))}.hover\:bg-\[\#1A1A1A\]:hover{--tw-bg-opacity:1;background-color:rgb(26 26 26/var(--tw-bg-opacity,1))}.hover\:bg-\[\#393A3B\]:hover{--tw-bg-opacity:1;background-color:rgb(57 58 59/var(--tw-bg-opacity,1))}.hover\:bg-\[\#D6D6D6\]:hover{--tw-bg-opacity:1;background-color:rgb(214 214 214/var(--tw-bg-opacity,1))}.hover\:bg-\[\#E5E5E5\]:hover{--tw-bg-opacity:1;background-color:rgb(229 229 229/var(--tw-bg-opacity,1))}.hover\:bg-\[\#E9E9E9\]:hover{--tw-bg-opacity:1;background-color:rgb(233 233 233/var(--tw-bg-opacity,1))}.hover\:bg-\[\#F5F5F5\]:hover{--tw-bg-opacity:1;background-color:rgb(245 245 245/var(--tw-bg-opacity,1))}.hover\:bg-\[\#f0f0f0\]:hover{--tw-bg-opacity:1;background-color:rgb(240 240 240/var(--tw-bg-opacity,1))}.hover\:bg-\[\#f4f4f4\]:hover{--tw-bg-opacity:1;background-color:rgb(244 244 244/var(--tw-bg-opacity,1))}.hover\:bg-blue-200:hover{--tw-bg-opacity:1;background-color:rgb(191 219 254/var(--tw-bg-opacity,1))}.hover\:bg-blue-600:hover{--tw-bg-opacity:1;background-color:rgb(37 99 235/var(--tw-bg-opacity,1))}.hover\:bg-blue-700:hover{--tw-bg-opacity:1;background-color:rgb(29 78 216/var(--tw-bg-opacity,1))}.hover\:bg-gray-100:hover{--tw-bg-opacity:1;background-color:rgb(243 244 246/var(--tw-bg-opacity,1))}.hover\:bg-gray-200:hover{--tw-bg-opacity:1;background-color:rgb(229 231 235/var(--tw-bg-opacity,1))}.hover\:bg-gray-300:hover{--tw-bg-opacity:1;background-color:rgb(209 213 219/var(--tw-bg-opacity,1))}.hover\:bg-gray-50:hover{--tw-bg-opacity:1;background-color:rgb(249 250 251/var(--tw-bg-opacity,1))}.hover\:bg-gray-800:hover{--tw-bg-opacity:1;background-color:rgb(31 41 55/var(--tw-bg-opacity,1))}.hover\:bg-green-200:hover{--tw-bg-opacity:1;background-color:rgb(187 247 208/var(--tw-bg-opacity,1))}.hover\:bg-green-700:hover{--tw-bg-opacity:1;background-color:rgb(21 128 61/var(--tw-bg-opacity,1))}.hover\:bg-indigo-200:hover{--tw-bg-opacity:1;background-color:rgb(199 210 254/var(--tw-bg-opacity,1))}.hover\:bg-orange-200:hover{--tw-bg-opacity:1;background-color:rgb(254 215 170/var(--tw-bg-opacity,1))}.hover\:bg-pink-200:hover{--tw-bg-opacity:1;background-color:rgb(251 207 232/var(--tw-bg-opacity,1))}.hover\:bg-purple-200:hover{--tw-bg-opacity:1;background-color:rgb(233 213 255/var(--tw-bg-opacity,1))}.hover\:bg-red-200:hover{--tw-bg-opacity:1;background-color:rgb(254 202 202/var(--tw-bg-opacity,1))}.hover\:bg-red-50:hover{--tw-bg-opacity:1;background-color:rgb(254 242 242/var(--tw-bg-opacity,1))}.hover\:bg-rose-200:hover{--tw-bg-opacity:1;background-color:rgb(254 205 211/var(--tw-bg-opacity,1))}.hover\:bg-teal-200:hover{--tw-bg-opacity:1;background-color:rgb(153 246 228/var(--tw-bg-opacity,1))}.hover\:bg-white:hover{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1))}.hover\:bg-yellow-200:hover{--tw-bg-opacity:1;background-color:rgb(254 240 138/var(--tw-bg-opacity,1))}.hover\:text-\[\#232425\]:hover{--tw-text-opacity:1;color:rgb(35 36 37/var(--tw-text-opacity,1))}.hover\:text-blue-800:hover{--tw-text-opacity:1;color:rgb(30 64 175/var(--tw-text-opacity,1))}.hover\:text-gray-600:hover{--tw-text-opacity:1;color:rgb(75 85 99/var(--tw-text-opacity,1))}.hover\:text-gray-700:hover{--tw-text-opacity:1;color:rgb(55 65 81/var(--tw-text-opacity,1))}.hover\:text-gray-800:hover{--tw-text-opacity:1;color:rgb(31 41 55/var(--tw-text-opacity,1))}.hover\:opacity-70:hover{opacity:.7}.hover\:opacity-80:hover{opacity:.8}.hover\:shadow-sm:hover{--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color)}.hover\:shadow-sm:hover,.hover\:shadow-xl:hover{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.hover\:shadow-xl:hover{--tw-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 8px 10px -6px rgba(0,0,0,.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color),0 8px 10px -6px var(--tw-shadow-color)}.focus\:border-blue-500:focus{--tw-border-opacity:1;border-color:rgb(59 130 246/var(--tw-border-opacity,1))}.focus\:border-indigo-500:focus{--tw-border-opacity:1;border-color:rgb(99 102 241/var(--tw-border-opacity,1))}.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\:ring-1:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color)}.focus\:ring-1:focus,.focus\:ring-2:focus{box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}.focus\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color)}.focus\:ring-blue-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(59 130 246/var(--tw-ring-opacity,1))}.focus\:ring-indigo-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(99 102 241/var(--tw-ring-opacity,1))}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:bg-gray-100:disabled{--tw-bg-opacity:1;background-color:rgb(243 244 246/var(--tw-bg-opacity,1))}.disabled\:bg-gray-400:disabled{--tw-bg-opacity:1;background-color:rgb(156 163 175/var(--tw-bg-opacity,1))}.disabled\:text-gray-400:disabled{--tw-text-opacity:1;color:rgb(156 163 175/var(--tw-text-opacity,1))}.disabled\:opacity-50:disabled{opacity:.5}.disabled\:opacity-60:disabled{opacity:.6}.group:hover .group-hover\:scale-\[1\.2\]{--tw-scale-x:1.2;--tw-scale-y:1.2;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.group:hover .group-hover\:bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1))}.group:hover .group-hover\:text-\[\#0f7fff\]{--tw-text-opacity:1;color:rgb(15 127 255/var(--tw-text-opacity,1))}@media not all and (min-width:768px){.max-md\:w-\[calc\(100vw\/4\.5\)\]{width:22.22222vw}.max-md\:flex-shrink-0{flex-shrink:0}}@media not all and (min-width:640px){.max-sm\:left-\[16px\]{left:16px}.max-sm\:flex{display:flex}.max-sm\:w-\[calc\(100\%-32px\)\]{width:calc(100% - 32px)}.max-sm\:w-\[calc\(100vw-32px\)\]{width:calc(100vw - 32px)}.max-sm\:justify-center{justify-content:center}.max-sm\:text-\[22px\]{font-size:22px}}@media (min-width:640px){.sm\:block{display:block}.sm\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\:flex-row{flex-direction:row}.sm\:gap-\[120px\]{gap:120px}.sm\:p-8{padding:2rem}.sm\:text-6xl{font-size:3.75rem;line-height:1}.sm\:text-sm{font-size:.875rem;line-height:1.25rem}.sm\:text-xl{font-size:1.25rem;line-height:1.75rem}}@media (min-width:768px){.md\:visible{visibility:visible}.md\:invisible{visibility:hidden}.md\:relative{position:relative}.md\:bottom-auto{bottom:auto}.md\:left-auto{left:auto}.md\:right-\[13px\]{right:13px}.md\:top-\[12px\]{top:12px}.md\:mx-0{margin-left:0;margin-right:0}.md\:mx-auto{margin-left:auto;margin-right:auto}.md\:my-\[80px\]{margin-bottom:80px;margin-top:80px}.md\:mb-0{margin-bottom:0}.md\:mb-\[61px\]{margin-bottom:61px}.md\:mt-0{margin-top:0}.md\:mt-\[10vh\]{margin-top:10vh}.md\:mt-\[14vh\]{margin-top:14vh}.md\:mt-\[21px\]{margin-top:21px}.md\:mt-\[7vh\]{margin-top:7vh}.md\:block{display:block}.md\:flex{display:flex}.md\:hidden{display:none}.md\:h-\[132px\]{height:132px}.md\:h-\[180px\]{height:180px}.md\:h-\[382px\]{height:382px}.md\:h-screen{height:100vh}.md\:max-h-\[588px\]{max-height:588px}.md\:w-\[1028px\]{width:1028px}.md\:w-\[180px\]{width:180px}.md\:w-\[208px\]{width:208px}.md\:w-\[210px\]{width:210px}.md\:w-\[324px\]{width:324px}.md\:w-\[40\%\]{width:40%}.md\:w-\[441px\]{width:441px}.md\:w-\[510px\]{width:510px}.md\:w-\[540px\]{width:540px}.md\:w-\[680px\]{width:680px}.md\:w-\[708px\]{width:708px}.md\:w-\[760px\]{width:760px}.md\:w-auto{width:auto}.md\:min-w-\[100px\]{min-width:100px}.md\:min-w-\[102px\]{min-width:102px}.md\:min-w-\[760px\]{min-width:760px}.md\:max-w-\[760px\]{max-width:760px}.md\:max-w-\[978px\]{max-width:978px}.md\:max-w-full{max-width:100%}.md\:flex-grow{flex-grow:1}.md\:translate-x-\[0\%\]{--tw-translate-x:0%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.md\:items-center{align-items:center}.md\:justify-center{justify-content:center}.md\:overflow-y-auto{overflow-y:auto}.md\:rounded-\[16px\]{border-radius:16px}.md\:rounded-none{border-radius:0}.md\:p-0{padding:0}.md\:p-\[24px\]{padding:24px}.md\:px-\[25px\]{padding-left:25px;padding-right:25px}.md\:px-\[40px\]{padding-left:40px;padding-right:40px}.md\:py-\[20px\]{padding-bottom:20px;padding-top:20px}.md\:pb-\[16px\]{padding-bottom:16px}.md\:pt-\[80px\]{padding-top:80px}.md\:text-left{text-align:left}.md\:text-center{text-align:center}.md\:text-\[14px\]{font-size:14px}.md\:text-\[16px\]{font-size:16px}.md\:shadow-\[0px_3px_30px_0px_rgba\(0\,0\,0\,0\.08\)\]{--tw-shadow:0px 3px 30px 0px rgba(0,0,0,.08);--tw-shadow-colored:0px 3px 30px 0px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.md\:hover\:scale-\[1\.02\]:hover{--tw-scale-x:1.02;--tw-scale-y:1.02}.md\:hover\:scale-\[1\.02\]:hover,.md\:hover\:scale-\[1\.03\]:hover{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.md\:hover\:scale-\[1\.03\]:hover{--tw-scale-x:1.03;--tw-scale-y:1.03}.md\:hover\:scale-\[1\.2\]:hover{--tw-scale-x:1.2;--tw-scale-y:1.2;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.md\:hover\:bg-black\/70:hover{background-color:#000000b3}.md\:hover\:opacity-80:hover{opacity:.8}.md\:hover\:shadow-\[0px_4px_15px_0px_rgba\(0\,0\,0\,0\.08\)\]:hover{--tw-shadow:0px 4px 15px 0px rgba(0,0,0,.08);--tw-shadow-colored:0px 4px 15px 0px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.group:hover .md\:group-hover\:visible{visibility:visible}.group:hover .md\:group-hover\:scale-\[1\.2\]{--tw-scale-x:1.2;--tw-scale-y:1.2;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}}@media (min-width:1024px){.lg\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.lg\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.lg\:flex-row{flex-direction:row}.lg\:gap-4{gap:1rem}}@media (prefers-color-scheme:dark){.dark\:rotate-0{--tw-rotate:0deg}.dark\:rotate-0,.dark\:rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dark\:rotate-180{--tw-rotate:180deg}.dark\:border-\[\#333\]{--tw-border-opacity:1;border-color:rgb(51 51 51/var(--tw-border-opacity,1))}.dark\:border-\[\#e6e9eb40\]{border-color:#e6e9eb40}.dark\:border-\[\#efefef30\]{border-color:#efefef30}.dark\:border-\[\#ffffff20\]{border-color:#ffffff20}.dark\:border-blue-400{--tw-border-opacity:1;border-color:rgb(96 165 250/var(--tw-border-opacity,1))}.dark\:border-gray-700{--tw-border-opacity:1;border-color:rgb(55 65 81/var(--tw-border-opacity,1))}.dark\:border-white{--tw-border-opacity:1;border-color:rgb(255 255 255/var(--tw-border-opacity,1))}.dark\:border-zinc-700{--tw-border-opacity:1;border-color:rgb(63 63 70/var(--tw-border-opacity,1))}.dark\:border-l-\[\#2a2a2a\]{--tw-border-opacity:1;border-left-color:rgb(42 42 42/var(--tw-border-opacity,1))}.dark\:bg-\[\#121212\]{--tw-bg-opacity:1;background-color:rgb(18 18 18/var(--tw-bg-opacity,1))}.dark\:bg-\[\#1A1B1C\]{--tw-bg-opacity:1;background-color:rgb(26 27 28/var(--tw-bg-opacity,1))}.dark\:bg-\[\#1a1a1a\]{--tw-bg-opacity:1;background-color:rgb(26 26 26/var(--tw-bg-opacity,1))}.dark\:bg-\[\#1a1b1c\]{--tw-bg-opacity:1;background-color:rgb(26 27 28/var(--tw-bg-opacity,1))}.dark\:bg-\[\#222\]{--tw-bg-opacity:1;background-color:rgb(34 34 34/var(--tw-bg-opacity,1))}.dark\:bg-\[\#232425\]{--tw-bg-opacity:1;background-color:rgb(35 36 37/var(--tw-bg-opacity,1))}.dark\:bg-\[\#2a2a2a\]{--tw-bg-opacity:1;background-color:rgb(42 42 42/var(--tw-bg-opacity,1))}.dark\:bg-\[\#333\]{--tw-bg-opacity:1;background-color:rgb(51 51 51/var(--tw-bg-opacity,1))}.dark\:bg-\[\#3a3a3a\]{--tw-bg-opacity:1;background-color:rgb(58 58 58/var(--tw-bg-opacity,1))}.dark\:bg-\[\#3f3f3f\]{--tw-bg-opacity:1;background-color:rgb(63 63 63/var(--tw-bg-opacity,1))}.dark\:bg-\[\#444\]{--tw-bg-opacity:1;background-color:rgb(68 68 68/var(--tw-bg-opacity,1))}.dark\:bg-\[\#eeeeee30\]{background-color:#eeeeee30}.dark\:bg-\[\#eeeeee\]{--tw-bg-opacity:1;background-color:rgb(238 238 238/var(--tw-bg-opacity,1))}.dark\:bg-\[\#f4f4f4\]\/20{background-color:#f5f5f533}.dark\:bg-\[\#ffffff15\]{background-color:#ffffff15}.dark\:bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0/var(--tw-bg-opacity,1))}.dark\:bg-black\/30{background-color:#0000004d}.dark\:bg-blue-900{--tw-bg-opacity:1;background-color:rgb(30 58 138/var(--tw-bg-opacity,1))}.dark\:bg-gray-200{--tw-bg-opacity:1;background-color:rgb(229 231 235/var(--tw-bg-opacity,1))}.dark\:bg-gray-700{--tw-bg-opacity:1;background-color:rgb(55 65 81/var(--tw-bg-opacity,1))}.dark\:bg-gray-800{--tw-bg-opacity:1;background-color:rgb(31 41 55/var(--tw-bg-opacity,1))}.dark\:bg-gray-900{--tw-bg-opacity:1;background-color:rgb(17 24 39/var(--tw-bg-opacity,1))}.dark\:bg-neutral-800{--tw-bg-opacity:1;background-color:rgb(38 38 38/var(--tw-bg-opacity,1))}.dark\:bg-transparent{background-color:transparent}.dark\:bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1))}.dark\:bg-white\/10{background-color:#ffffff1a}.dark\:bg-gradient-to-r{background-image:linear-gradient(to right,var(--tw-gradient-stops))}.dark\:from-\[\#333\]{--tw-gradient-from:#333 var(--tw-gradient-from-position);--tw-gradient-to:rgba(51,51,51,0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}.dark\:to-\[\#2a2a2a\]{--tw-gradient-to:#2a2a2a var(--tw-gradient-to-position)}.dark\:fill-black{fill:#000}.dark\:text-\[\#232425\]{--tw-text-opacity:1;color:rgb(35 36 37/var(--tw-text-opacity,1))}.dark\:text-\[\#333\]{--tw-text-opacity:1;color:rgb(51 51 51/var(--tw-text-opacity,1))}.dark\:text-\[\#666666\],.dark\:text-\[\#666\]{--tw-text-opacity:1;color:rgb(102 102 102/var(--tw-text-opacity,1))}.dark\:text-\[\#888\]{--tw-text-opacity:1;color:rgb(136 136 136/var(--tw-text-opacity,1))}.dark\:text-\[\#DADBDC\]{--tw-text-opacity:1;color:rgb(218 219 220/var(--tw-text-opacity,1))}.dark\:text-\[\#b0b0b0\]{--tw-text-opacity:1;color:rgb(176 176 176/var(--tw-text-opacity,1))}.dark\:text-\[\#b4b4b4\]{--tw-text-opacity:1;color:rgb(180 180 180/var(--tw-text-opacity,1))}.dark\:text-\[\#ddd\]{--tw-text-opacity:1;color:rgb(221 221 221/var(--tw-text-opacity,1))}.dark\:text-\[\#e0e0e0\]{--tw-text-opacity:1;color:rgb(224 224 224/var(--tw-text-opacity,1))}.dark\:text-\[\#f5f5f5\]{--tw-text-opacity:1;color:rgb(245 245 245/var(--tw-text-opacity,1))}.dark\:text-\[\#fff\]{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1))}.dark\:text-black{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity,1))}.dark\:text-blue-300{--tw-text-opacity:1;color:rgb(147 197 253/var(--tw-text-opacity,1))}.dark\:text-blue-400{--tw-text-opacity:1;color:rgb(96 165 250/var(--tw-text-opacity,1))}.dark\:text-gray-200{--tw-text-opacity:1;color:rgb(229 231 235/var(--tw-text-opacity,1))}.dark\:text-gray-300{--tw-text-opacity:1;color:rgb(209 213 219/var(--tw-text-opacity,1))}.dark\:text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175/var(--tw-text-opacity,1))}.dark\:text-neutral-200{--tw-text-opacity:1;color:rgb(229 229 229/var(--tw-text-opacity,1))}.dark\:text-neutral-500{--tw-text-opacity:1;color:rgb(115 115 115/var(--tw-text-opacity,1))}.dark\:text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1))}.dark\:text-zinc-300{--tw-text-opacity:1;color:rgb(212 212 216/var(--tw-text-opacity,1))}.dark\:opacity-30{opacity:.3}.dark\:shadow-\[0px_4px_15px_0px_rgba\(255\,255\,255\,0\.10\)\]{--tw-shadow:0px 4px 15px 0px hsla(0,0%,100%,.1);--tw-shadow-colored:0px 4px 15px 0px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.dark\:invert{--tw-invert:invert(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.dark\:hover\:bg-\[\#222222\]:hover{--tw-bg-opacity:1;background-color:rgb(34 34 34/var(--tw-bg-opacity,1))}.dark\:hover\:bg-\[\#333\]:hover{--tw-bg-opacity:1;background-color:rgb(51 51 51/var(--tw-bg-opacity,1))}.dark\:hover\:bg-\[\#3a3a3a\]:hover{--tw-bg-opacity:1;background-color:rgb(58 58 58/var(--tw-bg-opacity,1))}.dark\:hover\:bg-\[\#4448\]:hover{background-color:#4448}.dark\:hover\:bg-\[\#444\]:hover{--tw-bg-opacity:1;background-color:rgb(68 68 68/var(--tw-bg-opacity,1))}.dark\:hover\:bg-\[\#555\]:hover{--tw-bg-opacity:1;background-color:rgb(85 85 85/var(--tw-bg-opacity,1))}.dark\:hover\:bg-\[\#ffffffdd\]:hover{background-color:#fffd}.dark\:hover\:bg-white\/10:hover{background-color:#ffffff1a}.dark\:hover\:text-gray-200:hover{--tw-text-opacity:1;color:rgb(229 231 235/var(--tw-text-opacity,1))}.group:hover .dark\:group-hover\:bg-white\/10{background-color:#ffffff1a}.group:hover .dark\:group-hover\:text-\[\#99c3ff\]{--tw-text-opacity:1;color:rgb(153 195 255/var(--tw-text-opacity,1))}}body,html{color:#232425;font-family:arial,sans-serif;font-size:16px;height:100%;line-height:1.5;margin:0;padding:0}html body{--gs-font-sans:"Arial","Inter",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",sans-serif;--gs-font-cjk:"Noto Sans SC","Noto Sans JP","Noto Sans KR","Source Han Sans JP",system-ui,sans-serif;--gs-font-emoji:"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-family:var(--gs-font-sans),var(--gs-font-emoji)}:lang(ja),:lang(ko){font-family:var(--gs-font-cjk),var(--gs-font-emoji)}h1,h2,h3,h4,h5,h6,li,ol,p,ul{list-style:none;margin:0;padding:0}#app{display:flex;flex-direction:column;height:100%}input,textarea{font-family:arial,sans-serif}@media (prefers-color-scheme:dark){body{background-color:#1a1b1c;color:#e0e0e0}.agree_terms_dialog{background-color:#333;color:#e0e0e0}.buttons a{color:#e0e0e0}}@media (max-width:1220px){body.agents-page .n-message-wrapper{justify-content:center;width:100%}body.agents-page .n-message{max-width:calc(100% - 48px)}}body.agents-page .icon svg{height:100%;width:100%}@media (prefers-color-scheme:dark){body.agents-page{background:#1a1b1c}}body.agents-page pre code{white-space:pre-wrap;word-break:break-word}.hljs{font-size:14px}#__nuxt{height:100%}.n-message{border-radius:12px;font-size:14px;font-weight:500;max-width:520px;padding:12px 16px}.n-message .n-message__icon.n-message__icon--info-type .n-base-icon{color:#006ffd}.n-message .n-message__icon.n-message__icon--warning-type .n-base-icon{color:#ffb37c}.n-message .n-base-loading__container,.n-message .n-message__icon.n-message__icon--success-type .n-base-icon{color:#3ac0a0}.n-message .n-message__icon.n-message__icon--error-type .n-base-icon{color:#ff616d}.n-message--loading-type,.n-message--success-type{background:#fff;box-shadow:0 4px 15px #0000001a}.n-message__content{color:#232425;font-weight:700}@media (prefers-color-scheme:dark){.n-message__content{color:#fff}.n-message--loading-type,.n-message--success-type{background-color:#2a2a2a}}body.genspark-app{overscroll-behavior:none;-webkit-overscroll-behavior:none}.grecaptcha-badge{display:none!important}.agree_terms_dialog_background[data-v-3e366fdb]{align-items:center;background-color:#00000080;display:flex;height:100vh;justify-content:center;left:0;position:fixed;top:0;width:100vw;z-index:2001}.agree_terms_dialog[data-v-3e366fdb]{background-color:#fff;border-radius:5px;box-shadow:0 0 10px #0000001a;margin:0 16px;max-width:400px;padding:20px;position:relative;z-index:2002}.buttons[data-v-3e366fdb]{align-items:center;display:flex;justify-content:center;margin-top:20px;width:100%}.buttons a[data-v-3e366fdb]{border:1px solid #909499;border-radius:5px;color:#000;cursor:pointer;outline:none;padding:5px 10px;text-decoration:none;transition:all .3s;-webkit-user-select:none;-moz-user-select:none;user-select:none}.buttons a[data-v-3e366fdb]:hover{background-color:#0f7fff;color:#fff}.description a[data-v-3e366fdb]{color:#909499}@media (prefers-color-scheme:dark){.agree_terms_dialog[data-v-3e366fdb]{background-color:#333;color:#fff}.buttons a[data-v-3e366fdb]{color:#fff}}