import {
    c as e,
    i as n,
    a3 as r,
    aT as t,
    K as a
} from "./Cf0SOiw0.js";
const i = "undefined" != typeof document && "undefined" != typeof window,
    o = t("n-form-item");

function s(t, {
    defaultSize: i = "medium",
    mergedSize: s,
    mergedDisabled: d
} = {}) {
    const u = n(o, null);
    a(o, null);
    const l = e(s ? () => s(u) : () => {
            const {
                size: e
            } = t;
            if (e) return e;
            if (u) {
                const {
                    mergedSize: e
                } = u;
                if (void 0 !== e.value) return e.value
            }
            return i
        }),
        g = e(d ? () => d(u) : () => {
            const {
                disabled: e
            } = t;
            return void 0 !== e ? e : !!u && u.disabled.value
        }),
        m = e((() => {
            const {
                status: e
            } = t;
            return e || (null == u ? void 0 : u.mergedValidationStatus.value)
        }));
    return r((() => {
        u && u.restoreValidation()
    })), {
        mergedSizeRef: l,
        mergedDisabledRef: g,
        mergedStatusRef: m,
        nTriggerFormBlur() {
            u && u.handleContentBlur()
        },
        nTriggerFormChange() {
            u && u.handleContentChange()
        },
        nTriggerFormFocus() {
            u && u.handleContentFocus()
        },
        nTriggerFormInput() {
            u && u.handleContentInput()
        }
    }
}
const d = i && "chrome" in window;
i && navigator.userAgent.includes("Firefox");
const u = i && navigator.userAgent.includes("Safari") && !d;
export {
    u as a, o as f, i, s as u
};