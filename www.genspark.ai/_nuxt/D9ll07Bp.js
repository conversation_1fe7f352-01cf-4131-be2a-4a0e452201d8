import {
    d as r,
    b as e,
    o as t
} from "./Cf0SOiw0.js";
const o = {
    width: "28",
    height: "28",
    viewBox: "0 0 28 28",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const l = {
    render: function(l, n) {
        return t(), r("svg", o, n[0] || (n[0] = [e("rect", {
            x: "4",
            y: "3",
            width: "20",
            height: "22",
            rx: "3",
            stroke: "currentColor",
            "stroke-width": "2"
        }, null, -1), e("path", {
            "fill-rule": "evenodd",
            "clip-rule": "evenodd",
            d: "M11.0095 20.0312L11.0093 15.0008L9 15.0009L9.00021 20.0313L11.0095 20.0312ZM18.951 20.0351L18.951 8.00302L16.9414 8.00302L16.9414 20.0351L18.951 20.0351ZM14.9546 20.0352L14.955 12.006L12.9455 12.0059L12.945 20.0351L14.9546 20.0352Z",
            fill: "currentColor"
        }, null, -1)]))
    }
};
export {
    l as D
};