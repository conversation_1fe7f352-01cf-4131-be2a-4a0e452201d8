import {
    c as t,
    s as e
} from "./BH52nuY1.js";
import {
    S as a,
    d as n,
    j as o,
    c as r,
    r as i,
    l
} from "./D5LV4gfS.js";
import {
    _ as s,
    r as d,
    C as c,
    c as u,
    v as h,
    h as p,
    d as g,
    f as m,
    b as v,
    t as f,
    q as y,
    o as x
} from "./Cf0SOiw0.js";
const k = {
        key: 0,
        class: "chart-title-container"
    },
    b = {
        class: "title-row"
    },
    w = {
        class: "chart-image"
    },
    $ = ["src", "alt"],
    M = {
        key: 0,
        class: "chart-title-perspective"
    },
    C = {
        key: 0
    },
    j = {
        key: 1
    },
    S = {
        key: 0,
        class: "chart-perspective"
    },
    N = s({
        __name: "SankeyChart",
        props: {
            data: {
                type: Object,
                required: !0
            },
            nodeWidth: {
                type: Number,
                default: 30
            },
            totalHeight: {
                type: Number,
                default: 200
            },
            horizontalNodeSpacing: {
                type: Number,
                default: 250
            },
            nodePadding: {
                type: Number,
                default: 120
            },
            nodeAlign: {
                type: String,
                default: "center"
            },
            textWidth: {
                type: Number,
                default: 200
            },
            color: {
                type: String,
                default: "#000000"
            },
            useBrandColor: {
                type: Boolean,
                default: !0
            },
            horizontalPadding: {
                type: Number,
                default: 40
            },
            verticalPadding: {
                type: Number,
                default: 150
            },
            titleAreaHeight: {
                type: Number,
                default: 300
            },
            bottomTipsHeight: {
                type: Number,
                default: 100
            },
            showTitle: {
                type: Boolean,
                default: !1
            },
            onlyShowChart: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["mounted"],
        setup(s, {
            expose: N,
            emit: z
        }) {
            const A = s,
                E = d(null),
                W = d(new Map),
                P = d(!1);
            P.value = c.isDarkMode();
            const H = u((() => function(e) {
                if (!e || !e.nodes || !e.links) return c.log("Invalid data structure"), {
                    nodes: [],
                    links: []
                };
                const a = A.useBrandColor && e.brand_color ? e.brand_color : "#000000";
                W.value = new Map, e.links.forEach((t => {
                    W.value.set(t.source, 0);
                    const e = W.value.get(t.target) || 0;
                    W.value.set(t.target, Math.max(e, 1))
                }));
                const n = e.nodes.map(((t, e) => {
                        let n;
                        if ("positive" === t.direction && 0 !== e) n = P.value ? "#4FE356" : "#35A039";
                        else if ("negative" === t.direction && 0 !== e) n = P.value ? "#FF4D4D" : "#C90013";
                        else {
                            const e = t.color || a;
                            n = P.value ? function(t, e) {
                                let a = parseInt(t.slice(1, 3), 16),
                                    n = parseInt(t.slice(3, 5), 16),
                                    o = parseInt(t.slice(5, 7), 16),
                                    [r, i, l] = function(t, e, a) {
                                        t /= 255, e /= 255, a /= 255;
                                        const n = Math.max(t, e, a),
                                            o = Math.min(t, e, a);
                                        let r, i, l = (n + o) / 2;
                                        if (n === o) r = i = 0;
                                        else {
                                            const s = n - o;
                                            switch (i = l > .5 ? s / (2 - n - o) : s / (n + o), n) {
                                                case t:
                                                    r = (e - a) / s + (e < a ? 6 : 0);
                                                    break;
                                                case e:
                                                    r = (a - t) / s + 2;
                                                    break;
                                                case a:
                                                    r = (t - e) / s + 4
                                            }
                                            r /= 6
                                        }
                                        return [r, i, l]
                                    }(a, n, o);
                                l = Math.min(1, l + e), [a, n, o] = function(t, e, a) {
                                    let n, o, r;
                                    if (0 === e) n = o = r = a;
                                    else {
                                        const i = (t, e, a) => (a < 0 && (a += 1), a > 1 && (a -= 1), a < 1 / 6 ? t + 6 * (e - t) * a : a < .5 ? e : a < 2 / 3 ? t + (e - t) * (2 / 3 - a) * 6 : t),
                                            l = a < .5 ? a * (1 + e) : a + e - a * e,
                                            s = 2 * a - l;
                                        n = i(s, l, t + 1 / 3), o = i(s, l, t), r = i(s, l, t - 1 / 3)
                                    }
                                    return [255 * n, 255 * o, 255 * r]
                                }(r, i, l);
                                const s = t => {
                                    const e = Math.round(t).toString(16);
                                    return 1 === e.length ? "0" + e : e
                                };
                                return `#${s(a)}${s(n)}${s(o)}`
                            }(e, .4) : e
                        }
                        return { ...t,
                            index: e,
                            color: n,
                            direction: 0 === e ? void 0 : t.direction
                        }
                    })),
                    o = e.links.map((e => {
                        const o = n[e.target];
                        return { ...e,
                            color: o ? t(o.color).toString() : a
                        }
                    }));
                return {
                    nodes: n,
                    links: o
                }
            }(A.data, A.color)));
            h((() => A.data), (() => {
                I()
            }), {
                deep: !0
            }), p((() => {
                I()
            }));
            const _ = u((() => {
                    const t = function(t, e) {
                        if (!t || !e || 0 === t.length || 0 === e.length) return c.log("Invalid nodes or links data"), 0;
                        const a = new Map,
                            n = new Map;
                        t.forEach(((t, e) => {
                            a.set(e, []), n.set(e, [])
                        })), e.forEach((t => {
                            const e = "object" == typeof t.source ? t.source.index : t.source,
                                o = "object" == typeof t.target ? t.target.index : t.target;
                            a.has(e) && n.has(o) && (a.get(e).push(o), n.get(o).push(e))
                        }));
                        const o = t.filter(((t, e) => 0 === n.get(e).length));
                        if (0 === o.length) return c.log("No parent nodes found"), 0;

                        function r(t, e) {
                            return e.has(t) && 0 !== e.get(t).length ? 1 + Math.max(...e.get(t).map((t => r(t, e)))) : 0
                        }
                        let i = o[0],
                            l = r(i.index, a);
                        for (let s = 1; s < o.length; s++) {
                            const t = r(o[s].index, a);
                            t > l && (l = t, i = o[s])
                        }
                        return r(i.index, n) + r(i.index, a)
                    }(H.value.nodes, H.value.links);
                    return c.log("maxWidth: ", t), t * A.horizontalNodeSpacing * 1.2 + 2 * A.horizontalPadding + A.textWidth
                })),
                B = u((() => A.onlyShowChart ? 200 : A.titleAreaHeight)),
                T = u((() => A.onlyShowChart ? 0 : A.verticalPadding)),
                F = u((() => {
                    const {
                        maxCount: t
                    } = function(t) {
                        const e = new Map,
                            a = new Map;
                        let n = 0,
                            o = 0,
                            r = 0;
                        const i = t => "object" == typeof t ? t.index : t,
                            l = new Map;
                        return t.nodes.forEach(((t, e) => l.set(e, { in: [],
                                out: []
                            }))), t.links.forEach((t => {
                                const e = i(t.source),
                                    a = i(t.target);
                                l.get(e).out.push(a), l.get(a).in.push(e)
                            })),
                            function t(i, s) {
                                e.has(i) || (e.set(i, s), a.set(s, (a.get(s) || 0) + 1), n = Math.max(n, a.get(s)), o = Math.min(o, s), r = Math.max(r, s), c.log(`Node ${i} assigned to level ${s}`), l.get(i).in.forEach((e => t(e, s - 1))), l.get(i).out.forEach((e => t(e, s + 1))))
                            }(Array.from(l.entries()).reduce(((t, [e, { in: a,
                                out: n
                            }]) => a.length + n.length > t.connections ? {
                                index: e,
                                connections: a.length + n.length
                            } : t), {
                                index: -1,
                                connections: -1
                            }).index, 0), c.log("Node levels:", Object.fromEntries(e)), c.log("Level counts:", Object.fromEntries(a)), c.log("Max count in any level:", n), c.log("Min level:", o), c.log("Max level:", r), {
                                nodeLevels: e,
                                levelCounts: a,
                                maxCount: n,
                                minLevel: o,
                                maxLevel: r
                            }
                    }(A.data), e = A.totalHeight + A.nodePadding * t, a = 2 * T.value + B.value + A.bottomTipsHeight;
                    return Math.max(e + a, 500)
                }));

            function I() {
                const t = e(E.value);
                t.selectAll("*").remove();
                const {
                    nodeWidth: s,
                    nodePadding: d,
                    nodeAlign: u
                } = A;
                if (!H.value || !H.value.nodes || !H.value.links) return void c.log("Invalid processed data");
                const h = new Map;
                H.value.links.forEach((t => {
                    h.set(t.source, 0);
                    const e = h.get(t.target) || 0;
                    h.set(t.target, Math.max(e, 1))
                }));
                const p = F.value,
                    g = _.value;
                c.log("maxDepth: ", Math.max(...h.values())), c.log("chartHeight: ", p);
                const m = a().nodeWidth(s).nodePadding(d).extent([
                    [A.horizontalPadding + A.textWidth / 2, B.value + T.value],
                    [g - A.horizontalPadding - A.textWidth / 2, p - T.value - A.bottomTipsHeight]
                ]).nodeAlign(function(t) {
                    switch (t) {
                        case "left":
                            return l;
                        case "right":
                            return i;
                        case "center":
                            return r;
                        default:
                            return o
                    }
                }(u));
                c.log("width: ", g), c.log("start point: ", [A.horizontalPadding + A.textWidth / 2, B.value + T.value]), c.log("end point: ", [g - A.horizontalPadding - A.textWidth / 2, p - T.value - A.bottomTipsHeight]);
                const v = m(H.value);
                t.attr("width", g).attr("height", p).append("g").attr("stroke", "#000").attr("stroke-opacity", .5).selectAll("path").data(v.links).join("path").attr("d", n()).attr("fill", "none").attr("stroke", (t => t.color || "#000")).attr("stroke-width", (t => Math.max(1, t.width))).attr("stroke-opacity", .5);
                const f = t.append("g").selectAll("g").data(v.nodes).join("g");
                f.append("rect").attr("x", (t => t.x0)).attr("y", (t => t.y0)).attr("height", (t => t.y1 - t.y0)).attr("width", (t => t.x1 - t.x0)).attr("fill", (t => t.isCalculated ? "none" : t.color || "#6495ED")).attr("stroke", (t => t.color || "#6495ED")).attr("stroke-width", (t => t.isCalculated ? 3 : 0)).attr("stroke-dasharray", (t => t.isCalculated ? "8,4" : "none")), f.append("rect").attr("x", (t => t.x0)).attr("y", (t => t.y0 - 100)).attr("height", (t => t.y1 - t.y0 + 100)).attr("width", (t => t.x1 - t.x0)).attr("fill", "transparent").attr("stroke", "none").style("pointer-events", "all"), f.append("foreignObject").attr("x", (t => (t.x0 + t.x1) / 2 - A.textWidth / 2)).attr("y", (t => t.y0 - 340)).attr("width", A.textWidth).attr("height", 330).append("xhtml:div").style("display", "flex").style("flex-direction", "column").style("justify-content", "flex-end").style("align-items", "center").style("height", "100%").style("gap", "5px").html((t => {
                    let e = "";
                    if (t.image && (e += `<img src="${t.image}" alt="${t.name}" style="max-width: 200px; max-height: 150px; object-fit: contain;">`), t.isCalculated && (e += '<div style="color: #FF9800; font-size: 14px; margin-bottom: 5px;">(Calculated)</div>'), e += `<div style="font-weight: bold; font-size: 18px; color: ${t.color};">${t.name}</div>`, t.displayAmount) {
                        const a = function(t) {
                                if (!t) return "";
                                const e = t.replace(/[($)]/g, ""),
                                    a = e.match(/([\d.]+)([BM])/);
                                if (a) {
                                    return `$${parseFloat(a[1]).toFixed(1)}${a[2]}`
                                }
                                return t
                            }(t.displayAmount),
                            n = "negative" === t.direction ? `(${a})` : a;
                        e += `<div style="font-size: 18px; color: ${t.color};">${n}</div>`
                    }
                    return t.margin && (e += `<div style="font-size: 14px; color: #666;">${t.margin} margin</div>`), t.yoy && (e += `<div style="font-size: 14px; color: #666;">${t.yoy} Y/Y</div>`), e
                })), f.append("title").text((t => {
                    const e = [];
                    return t.name && e.push(t.name), "number" == typeof t.amount && e.push(`$${t.amount.toLocaleString()}`), t.value && e.push(`$${t.value.toLocaleString()}M`), t.margin && e.push(`${t.margin} margin`), t.yoy && e.push(`${t.yoy} Y/Y`), e.join("\n")
                })), t.selectAll("path").append("title").text((t => {
                    const e = [];
                    return e.push(`${t.source.name} → ${t.target.name}`), "number" == typeof t.value && e.push(`$${t.value.toLocaleString()}M`), e.join("\n")
                }))
            }
            N({
                chartHeight: F,
                chartWidth: _
            });
            const L = z;
            return p((() => {
                c.log("SankeyChart mounted"), L("mounted")
            })), (t, e) => (x(), g("div", {
                class: "sankey-chart-container",
                style: y({
                    width: `${_.value}px`
                })
            }, [s.onlyShowChart ? m("", !0) : (x(), g("div", k, [v("div", b, [v("div", w, [s.data.image ? (x(), g("img", {
                key: 0,
                src: s.data.image,
                alt: s.data.title
            }, null, 8, $)) : m("", !0)]), s.data.perspective ? (x(), g("div", M, [s.showTitle && s.data.title ? (x(), g("span", C, f(s.data.title), 1)) : !s.showTitle && s.data.perspective ? (x(), g("span", j, "Revenue By " + f(s.data.perspective), 1)) : m("", !0)])) : m("", !0)]), s.showTitle && s.data.perspective ? (x(), g("div", S, " Revenue By " + f(s.data.perspective), 1)) : m("", !0)])), (x(), g("svg", {
                ref_key: "svgRef",
                ref: E
            }, null, 512))], 4))
        }
    }, [
        ["__scopeId", "data-v-9a701c96"]
    ]);
export {
    N as _
};