import {
    O as e,
    C as t,
    K as a
} from "./C1lFdfgL.js";
import {
    G as n
} from "./BN-NNxvY.js";
import {
    M as l
} from "./Boc3hm_9.js";
import {
    d as o,
    o as s,
    b as i,
    _ as r,
    s as c,
    V as u,
    ay as d,
    r as p,
    C as m,
    v,
    c as h,
    f as _,
    e as g,
    y as f,
    t as C,
    F as b,
    k as y,
    n as k,
    U as w,
    W as S,
    D as M,
    dP as x,
    h as A,
    a3 as F,
    a as T,
    q as L,
    H as R,
    cQ as I,
    aa as O,
    i as j
} from "./Cf0SOiw0.js";
import {
    L as Z
} from "./Bzg9uoz_.js";
import {
    C as B
} from "./CRmNre8Y.js";
import {
    O as V
} from "./Dd3cVSEE.js";
import {
    c as q
} from "./DdaMTYTP.js";
import {
    s as D
} from "./CVrRKK4N.js";
import {
    e as E
} from "./DZBrT1el.js";
const P = "" + new URL("deepseek.DcMKAzsn.png",
        import.meta.url).href,
    $ = {
        width: "35",
        height: "33",
        viewBox: "0 0 35 33",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        class: "opacity-90 hover:opacity-100 fill-black dark:fill-white max-w-full [&>path]:hidden [&>#mark]:block [&>#furigana]:opacity-60 [&>#subtitle]:opacity-60 sm:[&>path]:hidden w-[1.75rem] h-[1.75rem]"
    };
const G = {
    render: function(e, t) {
        return s(), o("svg", $, t[0] || (t[0] = [i("path", {
            d: "M13.2371 21.0407L24.3186 12.8506C24.8619 12.4491 25.6384 12.6057 25.8973 13.2294C27.2597 16.5185 26.651 20.4712 23.9403 23.1851C21.2297 25.8989 17.4581 26.4941 14.0108 25.1386L10.2449 26.8843C15.6463 30.5806 22.2053 29.6665 26.304 25.5601C29.5551 22.3051 30.562 17.8683 29.6205 13.8673L29.629 13.8758C28.2637 7.99809 29.9647 5.64871 33.449 0.844576C33.5314 0.730667 33.6139 0.616757 33.6964 0.5L29.1113 5.09055V5.07631L13.2343 21.0436",
            fill: "currentColor",
            id: "mark"
        }, null, -1), i("path", {
            d: "M10.9503 23.0313C7.07343 19.3235 7.74185 13.5853 11.0498 10.2763C13.4959 7.82722 17.5036 6.82767 21.0021 8.2971L24.7595 6.55998C24.0826 6.07017 23.215 5.54334 22.2195 5.17313C17.7198 3.31926 12.3326 4.24192 8.67479 7.90126C5.15635 11.4239 4.0499 16.8403 5.94992 21.4622C7.36924 24.9165 5.04257 27.3598 2.69884 29.826C1.86829 30.7002 1.0349 31.5745 0.36364 32.5L10.9474 23.0341",
            fill: "currentColor",
            id: "mark"
        }, null, -1)]))
    }
};
const z = r({
        setup() {
            const {
                t: o
            } = c();
            u();
            return {
                modelsConfig: [{
                    name: "gpt-5,claude-sonnet-4,gemini-2.5-flash",
                    search_model_name: "gpt-5,claude-sonnet-4,gemini-2.5-flash",
                    icon: l,
                    label: "Mixture-of-Agents",
                    full_label: "Mixture-of-Agents",
                    description: o("components.general_chat_content.auto_mixes_best_ai_models_for_your_task"),
                    support_images: !0,
                    support_files: !0
                }, {
                    name: "gpt-5",
                    icon: e,
                    label: "GPT-5",
                    full_label: "Open AI GPT-5",
                    support_images: !0,
                    support_files: !0
                }, {
                    name: "gpt-5-high",
                    icon: e,
                    label: "GPT-5-high",
                    full_label: "Open AI GPT-5-high",
                    support_images: !0,
                    support_files: !0
                }, {
                    name: "o3",
                    icon: e,
                    label: "o3",
                    full_label: "Open AI o3",
                    support_images: !0,
                    support_files: !0,
                    hidden: !0
                }, {
                    name: "o3-pro",
                    icon: e,
                    label: "o3-pro",
                    full_label: "Open AI o3-pro",
                    support_images: !0,
                    support_files: !0,
                    disable_search_web: !0
                }, {
                    name: "o4-mini-high",
                    icon: e,
                    label: "o4-mini-high",
                    full_label: "Open AI o4-mini-high",
                    support_images: !1,
                    support_files: !0
                }, {
                    name: "claude-3-7-sonnet-thinking",
                    icon: t,
                    label: "Claude Sonnet 3.7 (Thinking)",
                    full_label: "Anthropic Claude Sonnet 3.7 (Thinking)",
                    support_images: !0,
                    support_files: !0,
                    hidden: !0
                }, {
                    name: "claude-3-7-sonnet",
                    icon: t,
                    label: "Claude Sonnet 3.7",
                    full_label: "Anthropic Claude 3.7 Sonnet",
                    support_images: !0,
                    support_files: !0,
                    hidden: !0
                }, {
                    name: "claude-sonnet-4-thinking",
                    icon: t,
                    label: "Claude Sonnet 4 (Thinking)",
                    full_label: "Anthropic Claude Sonnet 4 (Thinking)",
                    support_images: !0,
                    support_files: !0,
                    hidden: !0
                }, {
                    name: "claude-sonnet-4",
                    icon: t,
                    label: "Claude Sonnet 4",
                    full_label: "Anthropic Claude Sonnet 4",
                    support_images: !0,
                    support_files: !0
                }, {
                    name: "claude-opus-4",
                    icon: t,
                    label: "Claude Opus 4",
                    full_label: "Anthropic Claude Opus 4",
                    support_images: !0,
                    support_files: !0,
                    hidden: !0
                }, {
                    name: "claude-opus-4-1",
                    icon: t,
                    label: "Claude Opus 4.1",
                    full_label: "Anthropic Claude Opus 4.1",
                    support_images: !0,
                    support_files: !0,
                    disable_search_web: !0
                }, {
                    name: "gemini-2.5-flash",
                    icon: n,
                    label: "Gemini 2.5 Flash",
                    full_label: "Google Gemini 2.5 Flash",
                    support_images: !0,
                    support_files: !0
                }, {
                    name: "gemini-2.5-pro",
                    icon: n,
                    label: "Gemini 2.5 Pro",
                    full_label: "Google Gemini 2.5 Pro",
                    support_images: !0,
                    support_files: !0
                }, {
                    name: "deep-seek-v3",
                    icon: P,
                    label: "DeepSeek V3",
                    full_label: "DeepSeek V3",
                    support_images: !1,
                    support_files: !0,
                    hidden: !0
                }, {
                    name: "deep-seek-r1",
                    icon: P,
                    label: "DeepSeek R1",
                    full_label: "DeepSeek R1",
                    support_images: !1,
                    support_files: !0
                }, {
                    name: "kimi-k2-instruct",
                    icon: a,
                    label: "Kimi K2 Instruct",
                    full_label: "Kimi K2 Instruct",
                    support_images: !1,
                    support_files: !0,
                    hidden: !0
                }, {
                    name: "groq-kimi-k2-instruct",
                    icon: a,
                    label: "Groq Kimi K2 Instruct",
                    full_label: "Groq Kimi K2 Instruct",
                    support_images: !1,
                    support_files: !0,
                    hidden: !0
                }, {
                    name: "grok-4-0709",
                    icon: d(G),
                    label: "Grok4 0709",
                    full_label: "Grok4 0709",
                    support_images: !1,
                    support_files: !0
                }]
            }
        }
    }, [
        ["render", function(e, t, a, n, l, o) {
            return null
        }]
    ]),
    N = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "8",
        height: "6",
        viewBox: "0 0 8 6",
        fill: "none"
    };
const H = {
        render: function(e, t) {
            return s(), o("svg", N, t[0] || (t[0] = [i("path", {
                d: "M4 6L0.535899 -3.01142e-07L7.4641 -9.06825e-07L4 6Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    U = {
        class: "sidebar-content"
    },
    J = {
        class: "execution-steps"
    },
    K = {
        class: "header"
    },
    X = {
        class: "search-results"
    },
    Y = {
        class: "step completed"
    },
    W = ["onClick"],
    Q = {
        class: "source-info"
    },
    ee = ["src"],
    te = {
        class: "source-name"
    },
    ae = {
        class: "date"
    },
    ne = {
        class: "result-title"
    },
    le = {
        class: "result-excerpt"
    },
    oe = r({
        __name: "search_source_side_bar",
        props: {
            searchResults: {
                type: Array,
                default: () => []
            },
            keywords: {
                type: Array,
                default: () => []
            },
            jumpToUrl: {
                type: String,
                default: ""
            },
            showFullScreenSidebar: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["closeSearchSideBar"],
        setup(e) {
            const {
                t: t
            } = c(), a = e, n = p({}), l = p(null);
            p(null).value = m.isDarkMode();
            v((() => a.jumpToUrl), (e => {
                e && w((() => {
                    (e => {
                        if (!l.value) return;
                        const t = new ResizeObserver((o => {
                            t.disconnect(), w((() => {
                                const t = r.value.findIndex((t => t.link === e));
                                if (-1 !== t && n.value[t])
                                    if (a.showFullScreenSidebar) {
                                        const e = n.value[t],
                                            a = l.value,
                                            o = e.getBoundingClientRect(),
                                            s = a.getBoundingClientRect(),
                                            i = o.top - s.top;
                                        a.scrollTo({
                                            top: a.scrollTop + i - 100,
                                            behavior: "smooth"
                                        })
                                    } else n.value[t].scrollIntoView({
                                        behavior: "smooth",
                                        block: "center"
                                    })
                            }))
                        }));
                        t.observe(l.value)
                    })(e)
                }))
            }), {
                immediate: !0
            });
            const r = h((() => Array.isArray(a.searchResults) ? a.searchResults.map((e => ({
                sourceIcon: e.favicon,
                sourceName: e.source || "",
                date: e.date || "",
                title: e.title,
                excerpt: e.snippet,
                link: e.link
            }))) : []));
            return (c, u) => (s(), o("div", {
                class: "search-sidebar",
                ref_key: "sidebarRef",
                ref: l
            }, [a.showFullScreenSidebar ? _("", !0) : (s(), o("div", {
                key: 0,
                class: "close-icon-wrapper",
                onClick: u[1] || (u[1] = e => c.$emit("closeSearchSideBar"))
            }, [g(f(B), {
                class: "close-icon",
                onClick: u[0] || (u[0] = e => c.$emit("closeSearchSideBar"))
            })])), i("div", U, [i("div", J, [i("div", K, [a.showFullScreenSidebar ? (s(), o("div", {
                key: 0,
                class: "back-button",
                onClick: u[2] || (u[2] = e => c.$emit("closeSearchSideBar"))
            }, [g(f(Z))])) : _("", !0), i("div", X, [i("div", Y, [i("h3", null, C(f(t)("components.deep_dive_search_component.search_source_side_bar.read_sources", {
                num: e.searchResults.length
            })), 1)]), (s(!0), o(b, null, y(f(r), ((t, a) => (s(), o("div", {
                key: a,
                class: "result-item",
                ref_for: !0,
                ref: l => {
                    t.link === e.jumpToUrl && (n.value[a] = l)
                }
            }, [i("div", {
                class: k(["result-link", {
                    highlighted: t.link === e.jumpToUrl
                }]),
                onClick: e => f(m).windowopen(t.link)
            }, [i("div", Q, [i("img", {
                src: t.sourceIcon,
                class: "source-icon"
            }, null, 8, ee), i("span", te, C(t.sourceName), 1), i("span", ae, C(t.date), 1)]), i("h4", ne, C(t.title), 1), i("p", le, C(t.excerpt), 1)], 10, W)])))), 128))])])])])], 512))
        }
    }, [
        ["__scopeId", "data-v-f6a230d6"]
    ]),
    se = S("realtime", {
        state: () => ({
            status: "Idle"
        }),
        actions: {
            setStatus(e) {
                this.status = e
            }
        }
    }),
    ie = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const re = {
        render: function(e, t) {
            return s(), o("svg", ie, t[0] || (t[0] = [i("path", {
                d: "M15.9912 11.498V5.99805C15.9912 3.78805 14.2012 1.99805 11.9912 1.99805C9.78121 1.99805 7.99121 3.78805 7.99121 5.99805V11.498C7.99121 13.708 9.78121 15.498 11.9912 15.498C14.2012 15.498 15.9912 13.708 15.9912 11.498Z",
                fill: "#0F7FFF"
            }, null, -1), i("path", {
                d: "M4.34863 9.65234V11.3523C4.34863 15.5723 7.77863 19.0023 11.9986 19.0023C16.2186 19.0023 19.6486 15.5723 19.6486 11.3523V9.65234",
                stroke: "#0F7FFF",
                "stroke-width": "1.71429",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), i("path", {
                d: "M11.9961 19.002V22.002",
                stroke: "#0F7FFF",
                "stroke-width": "1.71429",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    ce = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const ue = {
        render: function(e, t) {
            return s(), o("svg", ce, t[0] || (t[0] = [i("path", {
                d: "M11.9961 19.002V22.002",
                stroke: "#FF3838",
                "stroke-width": "1.71429",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), i("path", {
                "fill-rule": "evenodd",
                "clip-rule": "evenodd",
                d: "M15.9922 11.5V6C15.9922 3.79 14.2022 2 11.9922 2C10.4633 2 9.13542 2.85668 8.46215 4.11645L15.9896 11.6439C15.9913 11.5962 15.9922 11.5482 15.9922 11.5ZM13.3709 15.2563L7.99219 9.87751V11.5C7.99219 13.71 9.78219 15.5 11.9922 15.5C12.4768 15.5 12.9412 15.4139 13.3709 15.2563ZM15.367 17.2523L16.614 18.4993C15.2842 19.3607 13.6995 19.8612 11.9993 19.8612C7.30594 19.8612 3.49219 16.0474 3.49219 11.354V9.65402C3.49219 9.18063 3.87594 8.79687 4.34933 8.79687C4.82272 8.79687 5.20647 9.18063 5.20647 9.65402V11.354C5.20647 15.1006 8.25272 18.1469 11.9993 18.1469C13.224 18.1469 14.3739 17.8214 15.367 17.2523ZM19.5734 15.2277L18.2824 13.9367C18.6109 13.1402 18.7922 12.268 18.7922 11.354V9.65402C18.7922 9.18063 19.1759 8.79687 19.6493 8.79687C20.1227 8.79687 20.5065 9.18063 20.5065 9.65402V11.354C20.5065 12.7485 20.1698 14.0654 19.5734 15.2277Z",
                fill: "#FF3838"
            }, null, -1), i("path", {
                d: "M4.8916 3.67969L19.4378 18.2259",
                stroke: "#FF3838",
                "stroke-width": "1.71429",
                "stroke-linecap": "round"
            }, null, -1)]))
        }
    },
    de = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const pe = {
        render: function(e, t) {
            return s(), o("svg", de, t[0] || (t[0] = [M('<path d="M12 22C17.5229 22 22 17.5229 22 12C22 6.47715 17.5229 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5229 6.47715 22 12 22Z" stroke="#333333" stroke-width="2" stroke-linejoin="round"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 9.5C8.19035 9.5 8.75 8.94035 8.75 8.25C8.75 7.55965 8.19035 7 7.5 7C6.80965 7 6.25 7.55965 6.25 8.25C6.25 8.94035 6.80965 9.5 7.5 9.5Z" fill="#333333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 13.5C8.19035 13.5 8.75 12.9404 8.75 12.25C8.75 11.5597 8.19035 11 7.5 11C6.80965 11 6.25 11.5597 6.25 12.25C6.25 12.9404 6.80965 13.5 7.5 13.5Z" fill="#333333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M12 9.5C12.6904 9.5 13.25 8.94035 13.25 8.25C13.25 7.55965 12.6904 7 12 7C11.3097 7 10.75 7.55965 10.75 8.25C10.75 8.94035 11.3097 9.5 12 9.5Z" fill="#333333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M12 13.5C12.6904 13.5 13.25 12.9404 13.25 12.25C13.25 11.5597 12.6904 11 12 11C11.3097 11 10.75 11.5597 10.75 12.25C10.75 12.9404 11.3097 13.5 12 13.5Z" fill="#333333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M16.5 9.5C17.1904 9.5 17.75 8.94035 17.75 8.25C17.75 7.55965 17.1904 7 16.5 7C15.8097 7 15.25 7.55965 15.25 8.25C15.25 8.94035 15.8097 9.5 16.5 9.5Z" fill="#333333"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M16.5 13.5C17.1904 13.5 17.75 12.9404 17.75 12.25C17.75 11.5597 17.1904 11 16.5 11C15.8097 11 15.25 11.5597 15.25 12.25C15.25 12.9404 15.8097 13.5 16.5 13.5Z" fill="#333333"></path><path d="M8.5 16.5H15.5" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>', 8)]))
        }
    },
    me = "" + new URL("connected.sc9CHxRT.mp3",
        import.meta.url).href,
    ve = {
        class: "input-container"
    },
    he = {
        class: "input-field"
    },
    _e = {
        key: 0,
        class: "settings-modal"
    },
    ge = {
        class: "settings-modal-content"
    },
    fe = {
        class: "voice-options"
    },
    Ce = ["id", "value"],
    be = ["for"],
    ye = {
        class: "instant-mode-option"
    },
    ke = {
        class: "settings-buttons"
    },
    we = r({
        __name: "realtime_prompt_input",
        props: {
            searchStatusTopBarData: {
                type: Object,
                default: null
            }
        },
        emits: ["switchToKeyboard", "function-call", "showSearchSideBar"],
        setup(e, {
            emit: t
        }) {
            const a = t,
                n = se(),
                l = e,
                r = p(!1),
                c = p(null),
                u = p(null),
                d = p(null),
                w = p(null),
                S = p(null),
                M = p(null),
                j = p(null),
                Z = p(null),
                B = p(null),
                D = p(0),
                E = p(0),
                P = p(!1),
                $ = p(!1),
                G = p({}),
                z = p(null),
                N = p(0),
                H = p(!1),
                U = p(null);
            v((() => l.searchStatusTopBarData), (e => {
                e && JSON.stringify(e) !== JSON.stringify(U.value) && (H.value = !1, U.value = JSON.parse(JSON.stringify(e)))
            }), {
                deep: !0
            }), v([() => P.value, () => $.value], (([e, t]) => {
                (e || t) && (H.value = !0)
            }));
            const J = p(!1),
                K = p("alloy"),
                X = h((() => n.status));
            x.on("function-call-final-answer", (e => {
                const t = G.value[e.query];
                m.log("ZZH realtime function-call-final-answer:", t, e), e.search_results && J.value && ie({
                    call_id: t,
                    result: e.search_results
                }), e.detail_answer && !J.value && ie({
                    call_id: t,
                    result: e.detail_answer
                })
            })), x.on("function-call-image-result", (e => {
                const t = G.value[e.prompt];
                m.log("ZZH realtime function-call-image-result:", t, e), e.image_url && ie({
                    call_id: t,
                    result: e.prompt + " Image result is done."
                })
            })), x.on("function-call-video-result", (e => {
                const t = G.value[e.prompt];
                m.log("ZZH realtime function-call-video-result:", t, e), e.video_url && ie({
                    call_id: t,
                    result: e.prompt + " Video result is done."
                })
            }));
            const Y = () => {
                    ce(), a("switchToKeyboard")
                },
                W = e => {
                    a("showSearchSideBar", e)
                },
                Q = async () => {
                    try {
                        ce(), n.setStatus("Connecting"), Me();
                        const e = await fetch(`/api/get_realtime_api_ephemeral_key?instant=${J.value}&voice=${K.value}`),
                            t = (await e.json()).data.client_secret.value;
                        c.value = new RTCPeerConnection, d.value = await navigator.mediaDevices.getUserMedia({
                            audio: !0
                        }), ee(d.value), c.value.addTrack(d.value.getTracks()[0]);
                        const a = document.createElement("audio");
                        a.autoplay = !0, c.value.ontrack = e => {
                            a.srcObject = e.streams[0], te(e.streams[0]), $.value = !0
                        }, u.value = c.value.createDataChannel("oai-events"), u.value.addEventListener("message", le);
                        const l = await c.value.createOffer();
                        await c.value.setLocalDescription(l);
                        const o = "https://api.openai.com/v1/realtime",
                            s = "gpt-4o-realtime-preview-2024-12-17",
                            i = await fetch(`${o}?model=${s}`, {
                                method: "POST",
                                body: l.sdp,
                                headers: {
                                    Authorization: `Bearer ${t}`,
                                    "Content-Type": "application/sdp"
                                }
                            }),
                            r = {
                                type: "answer",
                                sdp: await i.text()
                            };
                        await c.value.setRemoteDescription(r), n.setStatus("Connected"), xe(), new Audio(me).play(), de(), Se(), oe(!0)
                    } catch (e) {
                        n.setStatus("Error"), xe()
                    }
                },
                ee = e => {
                    const t = new(window.AudioContext || window.webkitAudioContext),
                        a = t.createMediaStreamSource(e);
                    M.value = t.createAnalyser(), M.value.fftSize = 1024, M.value.smoothingTimeConstant = .3, a.connect(M.value), ae()
                },
                te = e => {
                    const t = new(window.AudioContext || window.webkitAudioContext),
                        a = t.createMediaStreamSource(e);
                    j.value = t.createAnalyser(), j.value.fftSize = 256, a.connect(j.value), ne()
                },
                ae = () => {
                    const e = new Uint8Array(M.value.frequencyBinCount),
                        t = () => {
                            M.value.getByteTimeDomainData(e);
                            let a = 0;
                            for (let t = 0; t < e.length; t++) {
                                a += Math.abs(e[t] - 128) / 128
                            }
                            const n = a / e.length * 100;
                            D.value = n, P.value = n > 1, Z.value = requestAnimationFrame(t)
                        };
                    t()
                },
                ne = () => {
                    const e = new Uint8Array(j.value.frequencyBinCount),
                        t = () => {
                            j.value.getByteFrequencyData(e);
                            const a = e.reduce(((e, t) => e + t)) / e.length;
                            E.value = a, $.value = a > 20, B.value = requestAnimationFrame(t)
                        };
                    t()
                },
                le = e => {
                    Se();
                    const t = JSON.parse(e.data);
                    if ("response.done" === t.type) {
                        for (const n of t.response.output)
                            if ("function_call" === n.type) {
                                const e = n.call_id,
                                    t = n.name,
                                    l = n.arguments;
                                if ("search_web" === t) {
                                    const n = JSON.parse(l).query;
                                    G.value[n] = e, a("function-call", {
                                        call_id: e,
                                        name: t,
                                        args: l
                                    })
                                }
                                if ("generate_image" === t) {
                                    const n = JSON.parse(l).prompt;
                                    G.value[n] = e, a("function-call", {
                                        call_id: e,
                                        name: t,
                                        args: l
                                    })
                                }
                                if ("generate_video" === t) {
                                    const n = JSON.parse(l).prompt;
                                    G.value[n] = e, a("function-call", {
                                        call_id: e,
                                        name: t,
                                        args: l
                                    })
                                }
                            }
                    } else if ("response.audio_transcript.delta" === t.type);
                    else if ("response.audio_transcript.done" === t.type);
                    else if ("conversation.item.input_audio_transcription.completed" === t.type && (m.log("ZZH realtime conversation.item.input_audio_transcription.completed:", t.transcript), t.transcript))
                        if (m.log("ZZH realtime transcript:", t.transcript), J.value) m.log("ZZH instant mode, not sending transcript:", t.transcript);
                        else {
                            m.log("ZZH sending realtime transcript:", t.transcript);
                            const e = {
                                type: "conversation.item.create",
                                item: {
                                    type: "message",
                                    role: "user",
                                    content: [{
                                        type: "input_text",
                                        text: t.transcript
                                    }]
                                }
                            };
                            u.value.send(JSON.stringify(e));
                            const a = {
                                type: "response.create"
                            };
                            u.value.send(JSON.stringify(a))
                        }
                },
                oe = async e => {
                    !e || c.value ? (r.value = e, d.value && d.value.getAudioTracks().forEach((t => t.enabled = e))) : await Q()
                },
                ie = e => {
                    if (!u.value) return;
                    const t = {
                        type: "conversation.item.create",
                        item: {
                            type: "function_call_output",
                            call_id: e.call_id,
                            output: e.result
                        }
                    };
                    m.log("realtime function call output:", t), u.value.send(JSON.stringify(t));
                    const a = "The function call has been completed, please summarize the result and acknowledge the user. If you are in the middle of a response, just simply and briefly acknowledge the user, and then continue your previous response.",
                        n = {
                            type: "response.create",
                            response: {
                                instructions: a
                            }
                        };
                    m.log("ZZH realtime response instructions:", a), u.value.send(JSON.stringify(n))
                },
                ce = () => {
                    m.log("disconnecting webrtc"), xe(), u.value && (m.log("disconnecting dc."), u.value.close(), u.value = null), c.value && (m.log("disconnecting pc."), c.value.getSenders().forEach((e => {
                        e.track && e.track.stop()
                    })), c.value.close(), c.value = null), d.value && (d.value.getTracks().forEach((e => e.stop())), d.value = null), n.setStatus("Disconnected"), r.value = !1, Z.value && cancelAnimationFrame(Z.value), B.value && cancelAnimationFrame(B.value), we(), clearTimeout(S.value)
                },
                de = async () => {
                    try {
                        "wakeLock" in navigator && (w.value = await navigator.wakeLock.request("screen"))
                    } catch (e) {}
                },
                we = () => {
                    if (w.value) try {
                        w.value.release(), w.value = null
                    } catch (e) {}
                },
                Se = () => {
                    clearTimeout(S.value), S.value = setTimeout((() => {
                        ce()
                    }), 12e4)
                },
                Me = () => {
                    N.value = 0;
                    const e = () => {
                        N.value = (N.value + 1) % 10, z.value = setTimeout(e, 200)
                    };
                    e()
                },
                xe = () => {
                    z.value && (clearTimeout(z.value), z.value = null)
                },
                Ae = e => {
                    const t = 4.74;
                    if ("Connecting" === X.value) return e === N.value ? 24 : t;
                    if (r.value && P.value || $.value) {
                        return t + (24 - t) * ($.value ? Math.min(4 * E.value, 15) / 15 : Math.min(5 * D.value, 15) / 15) * (.4 + 1.2 * Math.random())
                    }
                    return t
                },
                Fe = p(!1),
                Te = p(K.value),
                Le = p(J.value),
                Re = [{
                    value: "alloy",
                    label: "Alloy"
                }, {
                    value: "ash",
                    label: "Ash"
                }, {
                    value: "ballad",
                    label: "Ballad"
                }, {
                    value: "coral",
                    label: "Coral"
                }, {
                    value: "echo",
                    label: "Echo"
                }, {
                    value: "sage",
                    label: "Sage"
                }, {
                    value: "shimmer",
                    label: "Shimmer"
                }, {
                    value: "verse",
                    label: "Verse"
                }],
                Ie = () => {
                    const e = K.value !== Te.value,
                        t = J.value !== Le.value;
                    K.value = Te.value, J.value = Le.value, Fe.value = !1, (e || t) && c.value && (ce(), Q())
                };
            return A((() => {
                Q()
            })), F((() => {
                ce(), xe()
            })), (e, t) => (s(), o(b, null, [i("div", ve, [i("button", {
                class: "icon-button",
                onClick: Y
            }, [g(f(pe))]), i("div", he, [i("div", {
                class: k(["dots-container", {
                    "mic-disabled": !r.value && !l.searchStatusTopBarData,
                    connecting: "Connecting" === X.value
                }])
            }, [H.value ? (s(), o(b, {
                key: 0
            }, y(10, (e => i("div", {
                class: k(["dot", {
                    "remote-active": $.value,
                    "mic-disabled": !r.value,
                    connecting: "Connecting" === X.value,
                    "active-dot": "Connecting" === X.value && e - 1 === N.value
                }]),
                key: e,
                style: L({
                    height: Ae(e - 1) + "px"
                })
            }, null, 6))), 64)) : _("", !0), !l.searchStatusTopBarData || P.value || $.value || H.value ? _("", !0) : (s(), T(q, {
                key: 1,
                class: "search-status-top-bar",
                data: l.searchStatusTopBarData,
                onClick: W
            }, null, 8, ["data"]))], 2)]), i("button", {
                class: k(["icon-button mic-button", {
                    "mic-enabled": r.value
                }]),
                onClick: t[0] || (t[0] = e => oe(!r.value))
            }, [r.value ? (s(), T(f(re), {
                key: 0
            })) : (s(), T(f(ue), {
                key: 1
            }))], 2), Fe.value ? (s(), o("div", _e, [i("div", ge, [t[7] || (t[7] = i("h3", null, "Voice Settings", -1)), i("div", fe, [t[5] || (t[5] = i("h4", null, "Voice", -1)), (s(), o(b, null, y(Re, (e => i("div", {
                key: e.value,
                class: "voice-option"
            }, [R(i("input", {
                type: "radio",
                id: e.value,
                value: e.value,
                "onUpdate:modelValue": t[1] || (t[1] = e => Te.value = e),
                name: "voice"
            }, null, 8, Ce), [
                [I, Te.value]
            ]), i("label", {
                for: e.value
            }, C(e.label), 9, be)]))), 64))]), i("div", ye, [R(i("input", {
                type: "checkbox",
                id: "instant-mode",
                "onUpdate:modelValue": t[2] || (t[2] = e => Le.value = e)
            }, null, 512), [
                [O, Le.value]
            ]), t[6] || (t[6] = i("label", {
                for: "instant-mode"
            }, "Instant Mode", -1))]), i("div", ke, [i("button", {
                onClick: Ie
            }, "Save"), i("button", {
                onClick: t[3] || (t[3] = e => Fe.value = !1)
            }, "Cancel")])])])) : _("", !0)]), i("button", {
                class: "icon-button settings-button",
                onClick: t[4] || (t[4] = e => Fe.value = !0)
            }, [g(f(V))])], 64))
        }
    }, [
        ["__scopeId", "data-v-2011914b"]
    ]),
    Se = {
        class: "realtime-cover"
    },
    Me = {
        ref: "particleCanvas",
        class: "particle-canvas"
    },
    xe = {
        class: "media-wrapper"
    },
    Ae = ["src"],
    Fe = ["src"];
const Te = r({
        name: "RealtimeCover",
        props: {
            speed: {
                type: Number,
                default: 10,
                validator: e => e > 0
            }
        },
        data: () => ({
            animationFrame: null,
            canvas: null,
            ctx: null,
            time: 0,
            gradientPoints: [],
            numPoints: 3,
            mediaContent: null,
            mediaType: null
        }),
        mounted() {
            this.initEffect(), window.addEventListener("resize", this.handleResize), this.setupEventListeners()
        },
        beforeDestroy() {
            window.removeEventListener("resize", this.handleResize), this.animationFrame && cancelAnimationFrame(this.animationFrame), this.removeEventListeners()
        },
        methods: {
            initEffect() {
                this.canvas = this.$refs.particleCanvas, this.ctx = this.canvas.getContext("2d"), this.setCanvasSize(), this.initGradientPoints(), this.animate()
            },
            setupEventListeners() {
                x.on("function-call-image-result", this.handleImageResult), x.on("function-call-video-result", this.handleVideoResult)
            },
            removeEventListeners() {
                x.off("function-call-image-result", this.handleImageResult), x.off("function-call-video-result", this.handleVideoResult)
            },
            handleImageResult(e) {
                e.image_url && this.showMedia("image", e.image_url)
            },
            handleVideoResult(e) {
                e.video_url && this.showMedia("video", e.video_url)
            },
            showMedia(e, t) {
                this.clearMedia(), this.mediaType = e, this.mediaContent = {
                    src: t,
                    opacity: 0
                };
                let a = D((() => {
                    this.mediaContent && this.mediaContent.opacity < 1 ? (this.mediaContent.opacity += .01, this.mediaContent.opacity >= 1 && (clearInterval(a), setTimeout((() => {
                        this.fadeOutMedia()
                    }), 1e4))) : clearInterval(a)
                }), 50);
                this.animationFrame && (cancelAnimationFrame(this.animationFrame), this.animationFrame = null)
            },
            fadeOutMedia() {
                if (!this.mediaContent) return;
                let e = D((() => {
                    this.mediaContent && this.mediaContent.opacity > 0 ? (this.mediaContent.opacity -= .01, this.mediaContent.opacity <= 0 && (clearInterval(e), this.clearMedia())) : clearInterval(e)
                }), 50)
            },
            clearMedia() {
                this.mediaContent = null, this.mediaType = null, this.animationFrame || this.animate()
            },
            setCanvasSize() {
                const e = this.canvas.parentElement;
                this.canvas.width = e.offsetWidth, this.canvas.height = e.offsetHeight
            },
            handleResize() {
                this.setCanvasSize(), this.initGradientPoints()
            },
            initGradientPoints() {
                this.gradientPoints = [];
                const e = this.canvas.width,
                    t = this.canvas.height;
                for (let a = 0; a < this.numPoints; a++) {
                    const a = e / 2,
                        n = t / 2,
                        l = .6 * e,
                        o = .3 * t,
                        s = a + (Math.random() - .5) * l,
                        i = n + (Math.random() - .5) * o,
                        r = (.003 + .002 * Math.random()) * this.speed;
                    this.gradientPoints.push({
                        x: s,
                        y: i,
                        targetX: a + (Math.random() - .5) * l,
                        targetY: n + (Math.random() - .5) * o,
                        radius: Math.min(e, t) * (.3 + .3 * Math.random()),
                        speed: r,
                        color: {
                            h: 180 + 30 * Math.random(),
                            s: 60 + 20 * Math.random(),
                            l: 60 + 20 * Math.random()
                        }
                    })
                }
            },
            animate() {
                this.time += .02, this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height), this.gradientPoints.forEach((e => {
                    const t = e.targetX - e.x,
                        a = e.targetY - e.y;
                    if (Math.sqrt(t * t + a * a) < 20) {
                        const t = this.canvas.width / 2,
                            a = this.canvas.height / 2,
                            n = .6 * this.canvas.width,
                            l = .3 * this.canvas.height;
                        e.targetX = t + (Math.random() - .5) * n, e.targetY = a + (Math.random() - .5) * l
                    } else e.x += t * e.speed, e.y += a * e.speed
                })), this.gradientPoints.forEach((e => {
                    const t = this.ctx.createRadialGradient(e.x, e.y, 0, e.x, e.y, e.radius);
                    t.addColorStop(0, `hsla(${e.color.h}, ${e.color.s}%, ${e.color.l}%, 0.4)`), t.addColorStop(.5, `hsla(${e.color.h}, ${e.color.s}%, ${e.color.l-10}%, 0.2)`), t.addColorStop(1, `hsla(${e.color.h}, ${e.color.s}%, ${e.color.l-20}%, 0)`), this.ctx.globalCompositeOperation = "screen", this.ctx.fillStyle = t, this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height)
                })), this.animationFrame = requestAnimationFrame(this.animate)
            }
        }
    }, [
        ["render", function(e, t, a, n, l, r) {
            return s(), o("div", Se, [i("canvas", Me, null, 512), l.mediaContent ? (s(), o("div", {
                key: 0,
                class: "media-container",
                style: L({
                    opacity: l.mediaContent.opacity
                })
            }, [i("div", xe, ["image" === l.mediaType ? (s(), o("img", {
                key: 0,
                src: l.mediaContent.src,
                class: "media-content",
                alt: "Media Content"
            }, null, 8, Ae)) : _("", !0), "video" === l.mediaType ? (s(), o("video", {
                key: 1,
                src: l.mediaContent.src,
                class: "media-content",
                controls: "",
                autoplay: ""
            }, null, 8, Fe)) : _("", !0)])], 4)) : _("", !0)])
        }],
        ["__scopeId", "data-v-7353c748"]
    ]),
    Le = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "12",
        height: "12",
        viewBox: "0 0 12 12",
        fill: "none"
    };
const Re = {
        render: function(e, t) {
            return s(), o("svg", Le, t[0] || (t[0] = [i("path", {
                d: "M1.20312 10.1992L10.8031 10.1992",
                stroke: "currentColor",
                "stroke-width": "0.9",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), i("path", {
                d: "M2.39844 6.52719V8.39922H4.28L9.59844 3.07844L7.72004 1.19922L2.39844 6.52719Z",
                stroke: "currentColor",
                "stroke-width": "0.9",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    Ie = S("image-select", (() => {
        const e = p("auto"),
            t = p("auto"),
            a = p("none"),
            n = p(!1),
            l = p(!1),
            o = p("auto"),
            s = p([]),
            i = p({});
        return {
            modelsSelected: e,
            setModelsSelected: t => {
                e.value = t
            },
            selectedAspectRatio: t,
            setSelectedAspectRatio: e => {
                t.value = e
            },
            personalizeMode: a,
            setPersonalizeMode: e => {
                a.value = e
            },
            hdEnabled: n,
            setHdEnabled: e => {
                n.value = e
            },
            reflectionEnabled: l,
            setReflectionEnabled: e => {
                l.value = e
            },
            selectedStyle: o,
            setSelectedStyle: e => {
                o.value = e
            },
            promptImages: s,
            setPromptImages: e => {
                s.value = e
            },
            imageModelMap: i,
            setImageModelMap: e => {
                i.value = e
            }
        }
    })),
    Oe = S("video-select", (() => {
        const e = p("kling/v1.6/standard"),
            t = p("9:16"),
            a = p("5"),
            n = p(!0),
            l = p([]);
        return {
            modelsSelected: e,
            selectedAspectRatio: t,
            selectedDuration: a,
            reflectionEnabled: n,
            promptImages: l,
            setModelsSelected: t => {
                e.value = t
            },
            setSelectedAspectRatio: e => {
                t.value = e
            },
            setSelectedDuration: e => {
                a.value = e
            },
            setReflectionEnabled: e => {
                n.value = e
            },
            setPromptImages: e => {
                l.value = e
            }
        }
    })),
    je = S("audio-select", (() => {
        const e = p("auto");
        return {
            modelsSelected: e,
            setModelsSelected: t => {
                e.value = t
            }
        }
    })),
    Ze = {
        width: "16",
        height: "16",
        viewBox: "0 0 16 16",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Be = {
        render: function(e, t) {
            return s(), o("svg", Ze, t[0] || (t[0] = [i("path", {
                d: "M4 7.63365L7 10.794L13 5",
                stroke: "currentcolor",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    Ve = "" + new URL("spark_page.DjIr8NF0.png",
        import.meta.url).href,
    qe = ["onClick"],
    De = {
        class: "row"
    },
    Ee = {
        class: "left"
    },
    Pe = {
        key: 0,
        class: "absolute -top-4 left-2 px-2 py-0.5 bg-red-500 rounded-tl-lg rounded-tr-lg rounded-bl-sm rounded-br-lg inline-flex justify-center items-center gap-2.5 overflow-hidden text-white text-xs font-bold"
    },
    $e = {
        class: "icon"
    },
    Ge = ["src"],
    ze = {
        class: "text"
    },
    Ne = {
        class: "model-name"
    },
    He = {
        key: 0,
        class: "model-tags"
    },
    Ue = {
        class: "right"
    },
    Je = ["checked"],
    Ke = {
        key: 0,
        class: "description"
    },
    Xe = {
        key: 1,
        class: "features"
    },
    Ye = {
        class: "feature-label"
    },
    We = {
        class: "feature-tags"
    },
    Qe = {
        key: 0,
        class: "divider"
    },
    et = r({
        __name: "AudioSelectModel",
        props: {
            toggleModel: {
                type: Function,
                default: () => {}
            },
            modelsSelected: {
                type: String,
                default: ""
            }
        },
        setup(e) {
            const {
                t: t
            } = c();
            j("currentUser");
            const a = h((() => E.filter((e => !e.hidden)))),
                n = e => e.description ? e.description(t) : "";
            return (l, r) => (s(), o("div", null, [(s(!0), o(b, null, y(a.value, (a => {
                var r, c, u, d, p;
                return s(), o("div", {
                    key: a.name,
                    class: "model-container"
                }, [i("div", {
                    class: k(["model", {
                        disabled: a.disabled
                    }]),
                    onClick: t => e.toggleModel(a.name)
                }, [i("div", De, [i("div", Ee, [a.new ? (s(), o("div", Pe, " New ")) : _("", !0), i("div", $e, [i("img", {
                    src: a.icon
                }, null, 8, Ge)]), i("div", ze, [i("div", Ne, C((p = a, "function" == typeof p.label ? p.label(t) : p.label)), 1), a.price_level || a.speed || a.quality ? (s(), o("div", He, [a.price_level ? (s(), o("span", {
                    key: 0,
                    class: k(["price-tag", `price-${a.price_level}`])
                }, C((d = a.price_level, {
                    budget: t("components.generate_audio_content.price_budget"),
                    moderate: t("components.generate_audio_content.price_moderate"),
                    premium: t("components.generate_audio_content.price_premium"),
                    expensive: t("components.generate_audio_content.price_expensive")
                }[d] || d)), 3)) : _("", !0), a.speed ? (s(), o("span", {
                    key: 1,
                    class: k(["speed-tag", `speed-${null==(r=a.speed)?void 0:r.replace("_","-")}`])
                }, C((u = a.speed, {
                    very_fast: t("components.generate_audio_content.speed_very_fast"),
                    fast: t("components.generate_audio_content.speed_fast"),
                    medium: t("components.generate_audio_content.speed_medium"),
                    slow: t("components.generate_audio_content.speed_slow")
                }[null == u ? void 0 : u.replace("-", "_")] || u)), 3)) : _("", !0), a.quality ? (s(), o("span", {
                    key: 2,
                    class: k(["quality-tag", `quality-${a.quality}`])
                }, C((c = a.quality, {
                    excellent: t("components.generate_audio_content.quality_excellent"),
                    high: t("components.generate_audio_content.quality_high"),
                    medium: t("components.generate_audio_content.quality_medium")
                }[c] || c)), 3)) : _("", !0)])) : _("", !0)])]), i("div", Ue, [i("input", {
                    type: "radio",
                    name: "audioModel",
                    checked: e.modelsSelected === a.name
                }, null, 8, Je)])]), a.description ? (s(), o("div", Ke, C(n(a)), 1)) : _("", !0), a.best_for && a.best_for.length > 0 ? (s(), o("div", Xe, [i("div", Ye, C(l.$t("components.generate_audio_content.best_for")) + ": ", 1), i("div", We, [(s(!0), o(b, null, y(a.best_for.slice(0, 3), (e => (s(), o("span", {
                    key: e,
                    class: "feature-tag"
                }, C((e => ({
                    conversations: t("components.generate_audio_content.feature_conversations"),
                    narration: t("components.generate_audio_content.feature_narration"),
                    emotional_content: t("components.generate_audio_content.feature_emotional_content"),
                    quick_generation: t("components.generate_audio_content.feature_quick_generation"),
                    prototyping: t("components.generate_audio_content.feature_prototyping"),
                    english_content: t("components.generate_audio_content.feature_english_content"),
                    professional_narration: t("components.generate_audio_content.feature_professional_narration"),
                    voice_overs: t("components.generate_audio_content.feature_voice_overs"),
                    chinese_content: t("components.generate_audio_content.feature_chinese_content"),
                    asian_languages: t("components.generate_audio_content.feature_asian_languages"),
                    precise_control: t("components.generate_audio_content.feature_precise_control"),
                    sound_effects: t("components.generate_audio_content.feature_sound_effects"),
                    environmental_audio: t("components.generate_audio_content.feature_environmental_audio"),
                    audio_textures: t("components.generate_audio_content.feature_audio_textures"),
                    background_music: t("components.generate_audio_content.feature_background_music"),
                    video_soundtracks: t("components.generate_audio_content.feature_video_soundtracks"),
                    ambient_music: t("components.generate_audio_content.feature_ambient_music"),
                    instrumental_music: t("components.generate_audio_content.feature_instrumental_music"),
                    short_clips: t("components.generate_audio_content.feature_short_clips"),
                    custom_voice_generation: t("components.generate_audio_content.feature_custom_voice_generation"),
                    voice_replication: t("components.generate_audio_content.feature_voice_replication"),
                    personalized_tts: t("components.generate_audio_content.feature_personalized_tts"),
                    bulk_tts: t("components.generate_audio_content.feature_bulk_tts"),
                    cost_effective_projects: t("components.generate_audio_content.feature_cost_effective_projects"),
                    simple_narration: t("components.generate_audio_content.feature_simple_narration")
                }[e] || e))(e)), 1)))), 128))])])) : _("", !0)], 10, qe), a.divider ? (s(), o("div", Qe)) : _("", !0)])
            })), 128))]))
        }
    }, [
        ["__scopeId", "data-v-3bbf2a2c"]
    ]);
export {
    et as A, P as D, Re as E, z as M, H as P, Ve as S, we as _, Oe as a, je as b, Be as c, oe as d, Te as e, Ie as u
};