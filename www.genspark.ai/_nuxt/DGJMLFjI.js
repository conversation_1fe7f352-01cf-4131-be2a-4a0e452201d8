import {
    i as e,
    c as t,
    d8 as a
} from "./Cf0SOiw0.js";
const n = {
    name: "en-US",
    global: {
        undo: "Undo",
        redo: "Redo",
        confirm: "Confirm",
        clear: "Clear"
    },
    Popconfirm: {
        positiveText: "Confirm",
        negativeText: "Cancel"
    },
    Cascader: {
        placeholder: "Please Select",
        loading: "Loading",
        loadingRequiredMessage: e => `Please load all ${e}'s descendants before checking it.`
    },
    Time: {
        dateFormat: "yyyy-MM-dd",
        dateTimeFormat: "yyyy-MM-dd HH:mm:ss"
    },
    DatePicker: {
        yearFormat: "yyyy",
        monthFormat: "MMM",
        dayFormat: "eeeeee",
        yearTypeFormat: "yyyy",
        monthTypeFormat: "yyyy-MM",
        dateFormat: "yyyy-MM-dd",
        dateTimeFormat: "yyyy-MM-dd HH:mm:ss",
        quarterFormat: "yyyy-qqq",
        weekFormat: "YYYY-w",
        clear: "Clear",
        now: "Now",
        confirm: "Confirm",
        selectTime: "Select Time",
        selectDate: "Select Date",
        datePlaceholder: "Select Date",
        datetimePlaceholder: "Select Date and Time",
        monthPlaceholder: "Select Month",
        yearPlaceholder: "Select Year",
        quarterPlaceholder: "Select Quarter",
        weekPlaceholder: "Select Week",
        startDatePlaceholder: "Start Date",
        endDatePlaceholder: "End Date",
        startDatetimePlaceholder: "Start Date and Time",
        endDatetimePlaceholder: "End Date and Time",
        startMonthPlaceholder: "Start Month",
        endMonthPlaceholder: "End Month",
        monthBeforeYear: !0,
        firstDayOfWeek: 6,
        today: "Today"
    },
    DataTable: {
        checkTableAll: "Select all in the table",
        uncheckTableAll: "Unselect all in the table",
        confirm: "Confirm",
        clear: "Clear"
    },
    LegacyTransfer: {
        sourceTitle: "Source",
        targetTitle: "Target"
    },
    Transfer: {
        selectAll: "Select all",
        unselectAll: "Unselect all",
        clearAll: "Clear",
        total: e => `Total ${e} items`,
        selected: e => `${e} items selected`
    },
    Empty: {
        description: "No Data"
    },
    Select: {
        placeholder: "Please Select"
    },
    TimePicker: {
        placeholder: "Select Time",
        positiveText: "OK",
        negativeText: "Cancel",
        now: "Now",
        clear: "Clear"
    },
    Pagination: {
        goto: "Goto",
        selectionSuffix: "page"
    },
    DynamicTags: {
        add: "Add"
    },
    Log: {
        loading: "Loading"
    },
    Input: {
        placeholder: "Please Input"
    },
    InputNumber: {
        placeholder: "Please Input"
    },
    DynamicInput: {
        create: "Create"
    },
    ThemeEditor: {
        title: "Theme Editor",
        clearAllVars: "Clear All Variables",
        clearSearch: "Clear Search",
        filterCompName: "Filter Component Name",
        filterVarName: "Filter Variable Name",
        import: "Import",
        export: "Export",
        restore: "Reset to Default"
    },
    Image: {
        tipPrevious: "Previous picture (←)",
        tipNext: "Next picture (→)",
        tipCounterclockwise: "Counterclockwise",
        tipClockwise: "Clockwise",
        tipZoomOut: "Zoom out",
        tipZoomIn: "Zoom in",
        tipDownload: "Download",
        tipClose: "Close (Esc)",
        tipOriginalSize: "Zoom to original size"
    }
};

function r(e) {
    return (t = {}) => {
        const a = t.width ? String(t.width) : e.defaultWidth;
        return e.formats[a] || e.formats[e.defaultWidth]
    }
}

function o(e) {
    return (t, a) => {
        let n;
        if ("formatting" === ((null == a ? void 0 : a.context) ? String(a.context) : "standalone") && e.formattingValues) {
            const t = e.defaultFormattingWidth || e.defaultWidth,
                r = (null == a ? void 0 : a.width) ? String(a.width) : t;
            n = e.formattingValues[r] || e.formattingValues[t]
        } else {
            const t = e.defaultWidth,
                r = (null == a ? void 0 : a.width) ? String(a.width) : e.defaultWidth;
            n = e.values[r] || e.values[t]
        }
        return n[e.argumentCallback ? e.argumentCallback(t) : t]
    }
}

function i(e) {
    return (t, a = {}) => {
        const n = a.width,
            r = n && e.matchPatterns[n] || e.matchPatterns[e.defaultMatchWidth],
            o = t.match(r);
        if (!o) return null;
        const i = o[0],
            l = n && e.parsePatterns[n] || e.parsePatterns[e.defaultParseWidth],
            d = Array.isArray(l) ? function(e, t) {
                for (let a = 0; a < e.length; a++)
                    if (t(e[a])) return a;
                return
            }(l, (e => e.test(i))) : function(e, t) {
                for (const a in e)
                    if (Object.prototype.hasOwnProperty.call(e, a) && t(e[a])) return a;
                return
            }(l, (e => e.test(i)));
        let s;
        s = e.valueCallback ? e.valueCallback(d) : d, s = a.valueCallback ? a.valueCallback(s) : s;
        return {
            value: s,
            rest: t.slice(i.length)
        }
    }
}
const l = {
        lessThanXSeconds: {
            one: "less than a second",
            other: "less than {{count}} seconds"
        },
        xSeconds: {
            one: "1 second",
            other: "{{count}} seconds"
        },
        halfAMinute: "half a minute",
        lessThanXMinutes: {
            one: "less than a minute",
            other: "less than {{count}} minutes"
        },
        xMinutes: {
            one: "1 minute",
            other: "{{count}} minutes"
        },
        aboutXHours: {
            one: "about 1 hour",
            other: "about {{count}} hours"
        },
        xHours: {
            one: "1 hour",
            other: "{{count}} hours"
        },
        xDays: {
            one: "1 day",
            other: "{{count}} days"
        },
        aboutXWeeks: {
            one: "about 1 week",
            other: "about {{count}} weeks"
        },
        xWeeks: {
            one: "1 week",
            other: "{{count}} weeks"
        },
        aboutXMonths: {
            one: "about 1 month",
            other: "about {{count}} months"
        },
        xMonths: {
            one: "1 month",
            other: "{{count}} months"
        },
        aboutXYears: {
            one: "about 1 year",
            other: "about {{count}} years"
        },
        xYears: {
            one: "1 year",
            other: "{{count}} years"
        },
        overXYears: {
            one: "over 1 year",
            other: "over {{count}} years"
        },
        almostXYears: {
            one: "almost 1 year",
            other: "almost {{count}} years"
        }
    },
    d = {
        lastWeek: "'last' eeee 'at' p",
        yesterday: "'yesterday at' p",
        today: "'today at' p",
        tomorrow: "'tomorrow at' p",
        nextWeek: "eeee 'at' p",
        other: "P"
    },
    s = {
        ordinalNumber: (e, t) => {
            const a = Number(e),
                n = a % 100;
            if (n > 20 || n < 10) switch (n % 10) {
                case 1:
                    return a + "st";
                case 2:
                    return a + "nd";
                case 3:
                    return a + "rd"
            }
            return a + "th"
        },
        era: o({
            values: {
                narrow: ["B", "A"],
                abbreviated: ["BC", "AD"],
                wide: ["Before Christ", "Anno Domini"]
            },
            defaultWidth: "wide"
        }),
        quarter: o({
            values: {
                narrow: ["1", "2", "3", "4"],
                abbreviated: ["Q1", "Q2", "Q3", "Q4"],
                wide: ["1st quarter", "2nd quarter", "3rd quarter", "4th quarter"]
            },
            defaultWidth: "wide",
            argumentCallback: e => e - 1
        }),
        month: o({
            values: {
                narrow: ["J", "F", "M", "A", "M", "J", "J", "A", "S", "O", "N", "D"],
                abbreviated: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                wide: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]
            },
            defaultWidth: "wide"
        }),
        day: o({
            values: {
                narrow: ["S", "M", "T", "W", "T", "F", "S"],
                short: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
                abbreviated: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
                wide: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
            },
            defaultWidth: "wide"
        }),
        dayPeriod: o({
            values: {
                narrow: {
                    am: "a",
                    pm: "p",
                    midnight: "mi",
                    noon: "n",
                    morning: "morning",
                    afternoon: "afternoon",
                    evening: "evening",
                    night: "night"
                },
                abbreviated: {
                    am: "AM",
                    pm: "PM",
                    midnight: "midnight",
                    noon: "noon",
                    morning: "morning",
                    afternoon: "afternoon",
                    evening: "evening",
                    night: "night"
                },
                wide: {
                    am: "a.m.",
                    pm: "p.m.",
                    midnight: "midnight",
                    noon: "noon",
                    morning: "morning",
                    afternoon: "afternoon",
                    evening: "evening",
                    night: "night"
                }
            },
            defaultWidth: "wide",
            formattingValues: {
                narrow: {
                    am: "a",
                    pm: "p",
                    midnight: "mi",
                    noon: "n",
                    morning: "in the morning",
                    afternoon: "in the afternoon",
                    evening: "in the evening",
                    night: "at night"
                },
                abbreviated: {
                    am: "AM",
                    pm: "PM",
                    midnight: "midnight",
                    noon: "noon",
                    morning: "in the morning",
                    afternoon: "in the afternoon",
                    evening: "in the evening",
                    night: "at night"
                },
                wide: {
                    am: "a.m.",
                    pm: "p.m.",
                    midnight: "midnight",
                    noon: "noon",
                    morning: "in the morning",
                    afternoon: "in the afternoon",
                    evening: "in the evening",
                    night: "at night"
                }
            },
            defaultFormattingWidth: "wide"
        })
    },
    u = {
        ordinalNumber: (m = {
            matchPattern: /^(\d+)(th|st|nd|rd)?/i,
            parsePattern: /\d+/i,
            valueCallback: e => parseInt(e, 10)
        }, (e, t = {}) => {
            const a = e.match(m.matchPattern);
            if (!a) return null;
            const n = a[0],
                r = e.match(m.parsePattern);
            if (!r) return null;
            let o = m.valueCallback ? m.valueCallback(r[0]) : r[0];
            return o = t.valueCallback ? t.valueCallback(o) : o, {
                value: o,
                rest: e.slice(n.length)
            }
        }),
        era: i({
            matchPatterns: {
                narrow: /^(b|a)/i,
                abbreviated: /^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,
                wide: /^(before christ|before common era|anno domini|common era)/i
            },
            defaultMatchWidth: "wide",
            parsePatterns: {
                any: [/^b/i, /^(a|c)/i]
            },
            defaultParseWidth: "any"
        }),
        quarter: i({
            matchPatterns: {
                narrow: /^[1234]/i,
                abbreviated: /^q[1234]/i,
                wide: /^[1234](th|st|nd|rd)? quarter/i
            },
            defaultMatchWidth: "wide",
            parsePatterns: {
                any: [/1/i, /2/i, /3/i, /4/i]
            },
            defaultParseWidth: "any",
            valueCallback: e => e + 1
        }),
        month: i({
            matchPatterns: {
                narrow: /^[jfmasond]/i,
                abbreviated: /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,
                wide: /^(january|february|march|april|may|june|july|august|september|october|november|december)/i
            },
            defaultMatchWidth: "wide",
            parsePatterns: {
                narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],
                any: [/^ja/i, /^f/i, /^mar/i, /^ap/i, /^may/i, /^jun/i, /^jul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]
            },
            defaultParseWidth: "any"
        }),
        day: i({
            matchPatterns: {
                narrow: /^[smtwf]/i,
                short: /^(su|mo|tu|we|th|fr|sa)/i,
                abbreviated: /^(sun|mon|tue|wed|thu|fri|sat)/i,
                wide: /^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i
            },
            defaultMatchWidth: "wide",
            parsePatterns: {
                narrow: [/^s/i, /^m/i, /^t/i, /^w/i, /^t/i, /^f/i, /^s/i],
                any: [/^su/i, /^m/i, /^tu/i, /^w/i, /^th/i, /^f/i, /^sa/i]
            },
            defaultParseWidth: "any"
        }),
        dayPeriod: i({
            matchPatterns: {
                narrow: /^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,
                any: /^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i
            },
            defaultMatchWidth: "any",
            parsePatterns: {
                any: {
                    am: /^a/i,
                    pm: /^p/i,
                    midnight: /^mi/i,
                    noon: /^no/i,
                    morning: /morning/i,
                    afternoon: /afternoon/i,
                    evening: /evening/i,
                    night: /night/i
                }
            },
            defaultParseWidth: "any"
        })
    };
var m;
const c = {
    name: "en-US",
    locale: {
        code: "en-US",
        formatDistance: (e, t, a) => {
            let n;
            const r = l[e];
            return n = "string" == typeof r ? r : 1 === t ? r.one : r.other.replace("{{count}}", t.toString()), (null == a ? void 0 : a.addSuffix) ? a.comparison && a.comparison > 0 ? "in " + n : n + " ago" : n
        },
        formatLong: {
            date: r({
                formats: {
                    full: "EEEE, MMMM do, y",
                    long: "MMMM do, y",
                    medium: "MMM d, y",
                    short: "MM/dd/yyyy"
                },
                defaultWidth: "full"
            }),
            time: r({
                formats: {
                    full: "h:mm:ss a zzzz",
                    long: "h:mm:ss a z",
                    medium: "h:mm:ss a",
                    short: "h:mm a"
                },
                defaultWidth: "full"
            }),
            dateTime: r({
                formats: {
                    full: "{{date}} 'at' {{time}}",
                    long: "{{date}} 'at' {{time}}",
                    medium: "{{date}}, {{time}}",
                    short: "{{date}}, {{time}}"
                },
                defaultWidth: "full"
            })
        },
        formatRelative: (e, t, a, n) => d[e],
        localize: s,
        match: u,
        options: {
            weekStartsOn: 0,
            firstWeekContainsDate: 1
        }
    }
};

function h(r) {
    const {
        mergedLocaleRef: o,
        mergedDateLocaleRef: i
    } = e(a, null) || {}, l = t((() => {
        var e, t;
        return null !== (t = null === (e = null == o ? void 0 : o.value) || void 0 === e ? void 0 : e[r]) && void 0 !== t ? t : n[r]
    }));
    return {
        dateLocaleRef: t((() => {
            var e;
            return null !== (e = null == i ? void 0 : i.value) && void 0 !== e ? e : c
        })),
        localeRef: l
    }
}
export {
    h as u
};