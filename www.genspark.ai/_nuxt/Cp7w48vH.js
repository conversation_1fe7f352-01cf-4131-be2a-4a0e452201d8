import {
    dB as t,
    dC as e,
    dD as r,
    dE as a,
    dF as n,
    dG as o,
    dH as c,
    dI as s,
    dJ as b,
    aA as u,
    dK as i,
    dL as j,
    dM as f,
    dN as y,
    dh as d,
    dO as l,
    b3 as A
} from "./Cf0SOiw0.js";
import {
    k as p,
    g as v,
    s as g,
    a as w,
    b as m,
    c as x,
    d as I
} from "./C-H3edso.js";

function O(t, e) {
    for (var r = -1, a = null == t ? 0 : t.length; ++r < a && !1 !== e(t[r], r, t););
    return t
}

function S(e, r) {
    return e && t(r, p(r), e)
}
var h = Object.getOwnPropertySymbols ? function(t) {
    for (var e = []; t;) w(e, v(t)), t = r(t);
    return e
} : g;

function U(t) {
    return m(t, e, h)
}
var F = Object.prototype.hasOwnProperty;
var E = /\w*$/;
var M = n ? n.prototype : void 0,
    B = M ? M.valueOf : void 0;

function D(t, e, r) {
    var n, c, s, b = t.constructor;
    switch (e) {
        case "[object ArrayBuffer]":
            return a(t);
        case "[object Boolean]":
        case "[object Date]":
            return new b(+t);
        case "[object DataView]":
            return function(t, e) {
                var r = e ? a(t.buffer) : t.buffer;
                return new t.constructor(r, t.byteOffset, t.byteLength)
            }(t, r);
        case "[object Float32Array]":
        case "[object Float64Array]":
        case "[object Int8Array]":
        case "[object Int16Array]":
        case "[object Int32Array]":
        case "[object Uint8Array]":
        case "[object Uint8ClampedArray]":
        case "[object Uint16Array]":
        case "[object Uint32Array]":
            return o(t, r);
        case "[object Map]":
        case "[object Set]":
            return new b;
        case "[object Number]":
        case "[object String]":
            return new b(t);
        case "[object RegExp]":
            return (s = new(c = t).constructor(c.source, E.exec(c))).lastIndex = c.lastIndex, s;
        case "[object Symbol]":
            return n = t, B ? Object(B.call(n)) : {}
    }
}
var C = b && b.isMap,
    N = C ? s(C) : function(t) {
        return c(t) && "[object Map]" == x(t)
    };
var k = b && b.isSet,
    G = k ? s(k) : function(t) {
        return c(t) && "[object Set]" == x(t)
    },
    L = "[object Arguments]",
    P = "[object Function]",
    R = "[object Object]",
    V = {};

function H(r, a, n, o, c, s) {
    var b, g = 1 & a,
        w = 2 & a,
        m = 4 & a;
    if (n && (b = c ? n(r, o, c, s) : n(r)), void 0 !== b) return b;
    if (!u(r)) return r;
    var E = d(r);
    if (E) {
        if (b = function(t) {
                var e = t.length,
                    r = new t.constructor(e);
                return e && "string" == typeof t[0] && F.call(t, "index") && (r.index = t.index, r.input = t.input), r
            }(r), !g) return l(r, b)
    } else {
        var M = x(r),
            B = M == P || "[object GeneratorFunction]" == M;
        if (i(r)) return j(r, g);
        if (M == R || M == L || B && !c) {
            if (b = w || B ? {} : f(r), !g) return w ? function(e, r) {
                return t(e, h(e), r)
            }(r, function(r, a) {
                return r && t(a, e(a), r)
            }(b, r)) : function(e, r) {
                return t(e, v(e), r)
            }(r, S(b, r))
        } else {
            if (!V[M]) return c ? r : {};
            b = D(r, M, g)
        }
    }
    s || (s = new y);
    var C = s.get(r);
    if (C) return C;
    s.set(r, b), G(r) ? r.forEach((function(t) {
        b.add(H(t, a, n, t, r, s))
    })) : N(r) && r.forEach((function(t, e) {
        b.set(e, H(t, a, n, e, r, s))
    }));
    var k = E ? void 0 : (m ? w ? U : I : w ? e : p)(r);
    return O(k || r, (function(t, e) {
        k && (t = r[e = t]), A(b, e, H(t, a, n, e, r, s))
    })), b
}
V[L] = V["[object Array]"] = V["[object ArrayBuffer]"] = V["[object DataView]"] = V["[object Boolean]"] = V["[object Date]"] = V["[object Float32Array]"] = V["[object Float64Array]"] = V["[object Int8Array]"] = V["[object Int16Array]"] = V["[object Int32Array]"] = V["[object Map]"] = V["[object Number]"] = V[R] = V["[object RegExp]"] = V["[object Set]"] = V["[object String]"] = V["[object Symbol]"] = V["[object Uint8Array]"] = V["[object Uint8ClampedArray]"] = V["[object Uint16Array]"] = V["[object Uint32Array]"] = !0, V["[object Error]"] = V[P] = V["[object WeakMap]"] = !1;

function J(t) {
    return H(t, 5)
}
export {
    O as a, H as b, J as c, S as d, G as e, U as g, N as i
};