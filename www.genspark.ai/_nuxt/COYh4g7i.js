import {
    aL as e,
    aM as n,
    aN as t,
    al as a,
    aO as o,
    ak as i,
    am as r,
    aP as l,
    aQ as s,
    J as d,
    X as c,
    Y as u,
    Z as h,
    r as b,
    aR as v,
    c as p,
    ap as g,
    aq as f,
    aS as w,
    an as m
} from "./Cf0SOiw0.js";
import {
    i as x,
    r as y,
    c as k
} from "./B7VeW_-d.js";
import {
    u as C
} from "./MpDLC7up.js";
import {
    u as $
} from "./BuhfKjCJ.js";
import {
    p as B,
    d as S
} from "./pB_XRIgB.js";
const _ = {
        common: e,
        self: function(e) {
            const {
                primaryColor: a,
                opacityDisabled: o,
                borderRadius: i,
                textColor3: r
            } = e;
            return Object.assign(Object.assign({}, n), {
                iconColor: r,
                textColor: "white",
                loadingColor: a,
                opacityDisabled: o,
                railColor: "rgba(0, 0, 0, .14)",
                railColorActive: a,
                buttonBoxShadow: "0 1px 4px 0 rgba(0, 0, 0, 0.3), inset 0 0 1px 0 rgba(0, 0, 0, 0.05)",
                buttonColor: "#FFF",
                railBorderRadiusSmall: i,
                railBorderRadiusMedium: i,
                railBorderRadiusLarge: i,
                buttonBorderRadiusSmall: i,
                buttonBorderRadiusMedium: i,
                buttonBorderRadiusLarge: i,
                boxShadowFocus: `0 0 0 2px ${t(a,{alpha:.2})}`
            })
        }
    },
    z = a("switch", "\n height: var(--n-height);\n min-width: var(--n-width);\n vertical-align: middle;\n user-select: none;\n -webkit-user-select: none;\n display: inline-flex;\n outline: none;\n justify-content: center;\n align-items: center;\n", [o("children-placeholder", "\n height: var(--n-rail-height);\n display: flex;\n flex-direction: column;\n overflow: hidden;\n pointer-events: none;\n visibility: hidden;\n "), o("rail-placeholder", "\n display: flex;\n flex-wrap: none;\n "), o("button-placeholder", "\n width: calc(1.75 * var(--n-rail-height));\n height: var(--n-rail-height);\n "), a("base-loading", "\n position: absolute;\n top: 50%;\n left: 50%;\n transform: translateX(-50%) translateY(-50%);\n font-size: calc(var(--n-button-width) - 4px);\n color: var(--n-loading-color);\n transition: color .3s var(--n-bezier);\n ", [s({
        left: "50%",
        top: "50%",
        originalTransform: "translateX(-50%) translateY(-50%)"
    })]), o("checked, unchecked", "\n transition: color .3s var(--n-bezier);\n color: var(--n-text-color);\n box-sizing: border-box;\n position: absolute;\n white-space: nowrap;\n top: 0;\n bottom: 0;\n display: flex;\n align-items: center;\n line-height: 1;\n "), o("checked", "\n right: 0;\n padding-right: calc(1.25 * var(--n-rail-height) - var(--n-offset));\n "), o("unchecked", "\n left: 0;\n justify-content: flex-end;\n padding-left: calc(1.25 * var(--n-rail-height) - var(--n-offset));\n "), i("&:focus", [o("rail", "\n box-shadow: var(--n-box-shadow-focus);\n ")]), r("round", [o("rail", "border-radius: calc(var(--n-rail-height) / 2);", [o("button", "border-radius: calc(var(--n-button-height) / 2);")])]), l("disabled", [l("icon", [r("rubber-band", [r("pressed", [o("rail", [o("button", "max-width: var(--n-button-width-pressed);")])]), o("rail", [i("&:active", [o("button", "max-width: var(--n-button-width-pressed);")])]), r("active", [r("pressed", [o("rail", [o("button", "left: calc(100% - var(--n-offset) - var(--n-button-width-pressed));")])]), o("rail", [i("&:active", [o("button", "left: calc(100% - var(--n-offset) - var(--n-button-width-pressed));")])])])])])]), r("active", [o("rail", [o("button", "left: calc(100% - var(--n-button-width) - var(--n-offset))")])]), o("rail", "\n overflow: hidden;\n height: var(--n-rail-height);\n min-width: var(--n-rail-width);\n border-radius: var(--n-rail-border-radius);\n cursor: pointer;\n position: relative;\n transition:\n opacity .3s var(--n-bezier),\n background .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier);\n background-color: var(--n-rail-color);\n ", [o("button-icon", "\n color: var(--n-icon-color);\n transition: color .3s var(--n-bezier);\n font-size: calc(var(--n-button-height) - 4px);\n position: absolute;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n display: flex;\n justify-content: center;\n align-items: center;\n line-height: 1;\n ", [s()]), o("button", '\n align-items: center; \n top: var(--n-offset);\n left: var(--n-offset);\n height: var(--n-button-height);\n width: var(--n-button-width-pressed);\n max-width: var(--n-button-width);\n border-radius: var(--n-button-border-radius);\n background-color: var(--n-button-color);\n box-shadow: var(--n-button-box-shadow);\n box-sizing: border-box;\n cursor: inherit;\n content: "";\n position: absolute;\n transition:\n background-color .3s var(--n-bezier),\n left .3s var(--n-bezier),\n opacity .3s var(--n-bezier),\n max-width .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier);\n ')]), r("active", [o("rail", "background-color: var(--n-rail-color-active);")]), r("loading", [o("rail", "\n cursor: wait;\n ")]), r("disabled", [o("rail", "\n cursor: not-allowed;\n opacity: .5;\n ")])]);
let R;
const F = d({
    name: "Switch",
    props: Object.assign(Object.assign({}, h.props), {
        size: {
            type: String,
            default: "medium"
        },
        value: {
            type: [String, Number, Boolean],
            default: void 0
        },
        loading: Boolean,
        defaultValue: {
            type: [String, Number, Boolean],
            default: !1
        },
        disabled: {
            type: Boolean,
            default: void 0
        },
        round: {
            type: Boolean,
            default: !0
        },
        "onUpdate:value": [Function, Array],
        onUpdateValue: [Function, Array],
        checkedValue: {
            type: [String, Number, Boolean],
            default: !0
        },
        uncheckedValue: {
            type: [String, Number, Boolean],
            default: !1
        },
        railStyle: Function,
        rubberBand: {
            type: Boolean,
            default: !0
        },
        onChange: [Function, Array]
    }),
    slots: Object,
    setup(e) {
        void 0 === R && (R = "undefined" == typeof CSS || void 0 !== CSS.supports && CSS.supports("width", "max(1px)"));
        const {
            mergedClsPrefixRef: n,
            inlineThemeDisabled: t
        } = u(e), a = h("Switch", "-switch", z, _, e, n), o = C(e), {
            mergedSizeRef: i,
            mergedDisabledRef: r
        } = o, l = b(e.defaultValue), s = v(e, "value"), d = $(s, l), c = p((() => d.value === e.checkedValue)), w = b(!1), m = b(!1), x = p((() => {
            const {
                railStyle: n
            } = e;
            if (n) return n({
                focused: m.value,
                checked: c.value
            })
        }));

        function y(n) {
            const {
                "onUpdate:value": t,
                onChange: a,
                onUpdateValue: i
            } = e, {
                nTriggerFormInput: r,
                nTriggerFormChange: s
            } = o;
            t && k(t, n), i && k(i, n), a && k(a, n), l.value = n, r(), s()
        }
        const F = p((() => {
                const {
                    value: e
                } = i, {
                    self: {
                        opacityDisabled: n,
                        railColor: t,
                        railColorActive: o,
                        buttonBoxShadow: r,
                        buttonColor: l,
                        boxShadowFocus: s,
                        loadingColor: d,
                        textColor: c,
                        iconColor: u,
                        [g("buttonHeight", e)]: h,
                        [g("buttonWidth", e)]: b,
                        [g("buttonWidthPressed", e)]: v,
                        [g("railHeight", e)]: p,
                        [g("railWidth", e)]: f,
                        [g("railBorderRadius", e)]: w,
                        [g("buttonBorderRadius", e)]: m
                    },
                    common: {
                        cubicBezierEaseInOut: x
                    }
                } = a.value;
                let y, k, C;
                return R ? (y = `calc((${p} - ${h}) / 2)`, k = `max(${p}, ${h})`, C = `max(${f}, calc(${f} + ${h} - ${p}))`) : (y = B((S(p) - S(h)) / 2), k = B(Math.max(S(p), S(h))), C = S(p) > S(h) ? f : B(S(f) + S(h) - S(p))), {
                    "--n-bezier": x,
                    "--n-button-border-radius": m,
                    "--n-button-box-shadow": r,
                    "--n-button-color": l,
                    "--n-button-width": b,
                    "--n-button-width-pressed": v,
                    "--n-button-height": h,
                    "--n-height": k,
                    "--n-offset": y,
                    "--n-opacity-disabled": n,
                    "--n-rail-border-radius": w,
                    "--n-rail-color": t,
                    "--n-rail-color-active": o,
                    "--n-rail-height": p,
                    "--n-rail-width": f,
                    "--n-width": C,
                    "--n-box-shadow-focus": s,
                    "--n-loading-color": d,
                    "--n-text-color": c,
                    "--n-icon-color": u
                }
            })),
            V = t ? f("switch", p((() => i.value[0])), F, e) : void 0;
        return {
            handleClick: function() {
                e.loading || r.value || (d.value !== e.checkedValue ? y(e.checkedValue) : y(e.uncheckedValue))
            },
            handleBlur: function() {
                m.value = !1,
                    function() {
                        const {
                            nTriggerFormBlur: e
                        } = o;
                        e()
                    }(), w.value = !1
            },
            handleFocus: function() {
                m.value = !0,
                    function() {
                        const {
                            nTriggerFormFocus: e
                        } = o;
                        e()
                    }()
            },
            handleKeyup: function(n) {
                e.loading || r.value || " " === n.key && (d.value !== e.checkedValue ? y(e.checkedValue) : y(e.uncheckedValue), w.value = !1)
            },
            handleKeydown: function(n) {
                e.loading || r.value || " " === n.key && (n.preventDefault(), w.value = !0)
            },
            mergedRailStyle: x,
            pressed: w,
            mergedClsPrefix: n,
            mergedValue: d,
            checked: c,
            mergedDisabled: r,
            cssVars: t ? void 0 : F,
            themeClass: null == V ? void 0 : V.themeClass,
            onRender: null == V ? void 0 : V.onRender
        }
    },
    render() {
        const {
            mergedClsPrefix: e,
            mergedDisabled: n,
            checked: t,
            mergedRailStyle: a,
            onRender: o,
            $slots: i
        } = this;
        null == o || o();
        const {
            checked: r,
            unchecked: l,
            icon: s,
            "checked-icon": d,
            "unchecked-icon": u
        } = i, h = !(x(s) && x(d) && x(u));
        return c("div", {
            role: "switch",
            "aria-checked": t,
            class: [`${e}-switch`, this.themeClass, h && `${e}-switch--icon`, t && `${e}-switch--active`, n && `${e}-switch--disabled`, this.round && `${e}-switch--round`, this.loading && `${e}-switch--loading`, this.pressed && `${e}-switch--pressed`, this.rubberBand && `${e}-switch--rubber-band`],
            tabindex: this.mergedDisabled ? void 0 : 0,
            style: this.cssVars,
            onClick: this.handleClick,
            onFocus: this.handleFocus,
            onBlur: this.handleBlur,
            onKeyup: this.handleKeyup,
            onKeydown: this.handleKeydown
        }, c("div", {
            class: `${e}-switch__rail`,
            "aria-hidden": "true",
            style: a
        }, y(r, (n => y(l, (t => n || t ? c("div", {
            "aria-hidden": !0,
            class: `${e}-switch__children-placeholder`
        }, c("div", {
            class: `${e}-switch__rail-placeholder`
        }, c("div", {
            class: `${e}-switch__button-placeholder`
        }), n), c("div", {
            class: `${e}-switch__rail-placeholder`
        }, c("div", {
            class: `${e}-switch__button-placeholder`
        }), t)) : null)))), c("div", {
            class: `${e}-switch__button`
        }, y(s, (n => y(d, (t => y(u, (a => c(w, null, {
            default: () => this.loading ? c(m, {
                key: "loading",
                clsPrefix: e,
                strokeWidth: 20
            }) : this.checked && (t || n) ? c("div", {
                class: `${e}-switch__button-icon`,
                key: t ? "checked-icon" : "icon"
            }, t || n) : this.checked || !a && !n ? null : c("div", {
                class: `${e}-switch__button-icon`,
                key: a ? "unchecked-icon" : "icon"
            }, a || n)
        }))))))), y(r, (n => n && c("div", {
            key: "checked",
            class: `${e}-switch__checked`
        }, n))), y(l, (n => n && c("div", {
            key: "unchecked",
            class: `${e}-switch__unchecked`
        }, n))))))
    }
});
export {
    F as N
};