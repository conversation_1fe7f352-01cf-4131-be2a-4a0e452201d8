import {
    _ as e,
    s as a,
    r as s,
    v as t,
    c as o,
    a as n,
    w as c,
    E as i,
    o as l,
    b as d,
    e as r,
    t as u,
    d as p,
    f as m,
    H as b,
    L as f,
    F as h,
    k,
    n as g
} from "./Cf0SOiw0.js";
import {
    C as v
} from "./Bm_HbXT2.js";
import {
    N as w
} from "./DQpEsQQa.js";
import {
    N as D
} from "./BjWUbj3w.js";
import {
    u as I
} from "./B6noBY_5.js";
const y = {
        name: "FeedbackDialog",
        components: {
            CloseIcon: v,
            NModal: D,
            NSpin: w
        },
        props: {
            showFeedbackDialog: {
                type: Boolean,
                default: !1
            }
        },
        emit: ["update:show"],
        setup(e, {
            emit: n
        }) {
            const {
                t: c
            } = a(), i = s(e.showFeedbackDialog);
            t((() => e.showFeedbackDialog), (e => {
                i.value = e
            }));
            const l = o((() => c("components.menu.support_description", ['<a href="mailto:<EMAIL>"><EMAIL></a>']))),
                d = s([]),
                r = s(!1),
                u = s(""),
                p = I();
            const m = e => {
                const a = new FileReader;
                a.onload = function(e) {
                    const a = new Image;
                    a.onload = () => {
                        (function(e, a, s) {
                            return new Promise(((t, o) => {
                                const n = document.createElement("canvas"),
                                    c = n.getContext("2d");
                                let i = e.width,
                                    l = e.height;
                                i > l ? i > a && (l = Math.round(l * (a / i)), i = a) : l > a && (i = Math.round(i * (a / l)), l = a), n.width = i, n.height = l, c.drawImage(e, 0, 0, i, l), t(n.toDataURL("image/jpeg", s))
                            }))
                        })(a, 1024, .8).then((e => {
                            d.value.push(e)
                        })).catch((e => {}))
                    }, a.src = e.target.result
                }, a.readAsDataURL(e)
            };
            return {
                supportDescription: l,
                close: () => {
                    n("update:show", !1)
                },
                emit: n,
                feedbackImages: d,
                feedbackSubmiting: r,
                showFeedbackDialog: i,
                feedbackInput: u,
                submitFeedback: async () => {
                    r.value = !0;
                    try {
                        const e = await fetch("/api/feedback", {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json"
                                },
                                body: JSON.stringify({
                                    content: u.value,
                                    images: d.value,
                                    url: location.href
                                })
                            }),
                            a = await e.json();
                        if (a && 0 === a.status) return p.success(c("components.menu.feedback-submitted")), u.value = "", void(d.value = [])
                    } catch (e) {} finally {
                        r.value = !1, n("update:show", !1)
                    }
                    p.error(c("components.menu.error-submit-feedback"))
                },
                onPaste: e => {
                    const a = (e.clipboardData || e.originalEvent.clipboardData).items;
                    for (let s in a) {
                        const t = a[s];
                        if ("file" === t.kind && t.type.startsWith("image/")) {
                            const a = t.getAsFile();
                            m(a), e.preventDefault()
                        }
                    }
                }
            }
        }
    },
    F = {
        class: "feedback_dialog"
    },
    C = {
        class: "close"
    },
    _ = {
        class: "title"
    },
    j = ["innerHTML"],
    N = {
        class: "content"
    },
    P = {
        class: "input"
    },
    S = ["placeholder"],
    L = {
        key: 0,
        class: "images"
    },
    M = ["src"],
    E = ["onClick"],
    T = {
        class: "buttons"
    };
const x = e(y, [
    ["render", function(e, a, s, t, o, v) {
        const w = i("CloseIcon"),
            D = i("NSpin"),
            I = i("n-modal");
        return l(), n(I, {
            show: t.showFeedbackDialog
        }, {
            default: c((() => [d("div", F, [d("div", C, [d("div", {
                class: "icon",
                onClick: a[0] || (a[0] = () => {
                    t.close()
                })
            }, [r(w)])]), d("div", _, u(e.$t("components.menu.support_title")), 1), d("div", {
                class: "description",
                innerHTML: t.supportDescription
            }, null, 8, j), d("div", N, [d("div", P, [b(d("textarea", {
                "onUpdate:modelValue": a[1] || (a[1] = e => t.feedbackInput = e),
                onPaste: a[2] || (a[2] = (...e) => t.onPaste && t.onPaste(...e)),
                placeholder: e.$t("components.menu.support_placeholder")
            }, null, 40, S), [
                [f, t.feedbackInput]
            ])]), t.feedbackImages ? (l(), p("div", L, [(l(!0), p(h, null, k(t.feedbackImages, ((e, a) => (l(), p("div", {
                class: "image",
                key: a
            }, [d("img", {
                src: e
            }, null, 8, M), d("div", {
                class: "close",
                onClick: () => {
                    t.feedbackImages.splice(a, 1)
                }
            }, [r(w)], 8, E)])))), 128))])) : m("", !0)]), d("div", T, [r(D, {
                show: t.feedbackSubmiting,
                style: {
                    width: "100%"
                }
            }, {
                default: c((() => [d("div", {
                    class: g(["button", {
                        disabled: 0 == t.feedbackInput.length
                    }]),
                    onClick: a[3] || (a[3] = e => {
                        t.feedbackInput.length > 0 && t.submitFeedback(e)
                    })
                }, u(e.$t("components.menu.submit")), 3)])),
                _: 1
            }, 8, ["show"])])])])),
            _: 1
        }, 8, ["show"])
    }],
    ["__scopeId", "data-v-7d7d4b21"]
]);
export {
    x as _
};