import {
    h as e,
    a3 as t,
    dl as o,
    aT as r,
    i as n,
    r as a,
    b9 as l,
    J as s,
    K as i,
    aD as d,
    H as u,
    dm as p,
    X as c,
    dn as f,
    v as h,
    aR as v,
    dp as g,
    U as m,
    ak as b,
    al as w,
    aP as y,
    aO as $,
    am as x,
    dq as S,
    Z as M,
    a4 as B,
    Y as C,
    dr as z,
    ag as T,
    c as E,
    I as R,
    aq as k,
    av as O,
    F as j,
    bv as A,
    bm as I
} from "./Cf0SOiw0.js";
import {
    m as P,
    d as W,
    p as N,
    g as F,
    a as _,
    L,
    z as D,
    c as X,
    F as Y,
    b as H
} from "./DpMvtoun.js";
import {
    m as V
} from "./BPQGB51Y.js";
import {
    o as U,
    a as q,
    g as K
} from "./Jr9eiJio.js";
import {
    i as J,
    r as Z,
    c as G
} from "./B7VeW_-d.js";
import {
    X as Q
} from "./WZsIN7xM.js";
import {
    u as ee
} from "./BuhfKjCJ.js";
import {
    u as te
} from "./BGK9k_mT.js";
import {
    k as oe
} from "./9wLWmnxl.js";
let re = [];
const ne = new WeakMap;

function ae() {
    re.forEach((e => e(...ne.get(e)))), re = []
}

function le(e, ...t) {
    ne.set(e, t), re.includes(e) || 1 === re.push(e) && requestAnimationFrame(ae)
}
let se, ie;
var de, ue;

function pe(o) {
    if (ie) return;
    let r = !1;
    e((() => {
        ie || null == se || se.then((() => {
            r || o()
        }))
    })), t((() => {
        r = !0
    }))
}
se = o ? null === (ue = null === (de = document) || void 0 === de ? void 0 : de.fonts) || void 0 === ue ? void 0 : ue.ready : void 0, ie = !1, void 0 !== se ? se.then((() => {
    ie = !0
})) : ie = !0;
const ce = r("n-internal-select-menu"),
    fe = r("n-internal-select-menu-body"),
    he = "__disabled__";

function ve(o) {
    const r = n(P, null),
        s = n(W, null),
        i = n(N, null),
        d = n(fe, null),
        u = a();
    if ("undefined" != typeof document) {
        u.value = document.fullscreenElement;
        const o = () => {
            u.value = document.fullscreenElement
        };
        e((() => {
            U("fullscreenchange", document, o)
        })), t((() => {
            q("fullscreenchange", document, o)
        }))
    }
    return l((() => {
        var e;
        const {
            to: t
        } = o;
        return void 0 !== t ? !1 === t ? he : !0 === t ? u.value || "body" : t : (null == r ? void 0 : r.value) ? null !== (e = r.value.$el) && void 0 !== e ? e : r.value : (null == s ? void 0 : s.value) ? s.value : (null == i ? void 0 : i.value) ? i.value : (null == d ? void 0 : d.value) ? d.value : null != t ? t : u.value || "body"
    }))
}
ve.tdkey = he, ve.propTo = {
    type: [String, Object, Boolean],
    default: void 0
};
let ge = null;

function me() {
    if (null === ge && (ge = document.getElementById("v-binder-view-measurer"), null === ge)) {
        ge = document.createElement("div"), ge.id = "v-binder-view-measurer";
        const {
            style: e
        } = ge;
        e.position = "fixed", e.left = "0", e.right = "0", e.top = "0", e.bottom = "0", e.pointerEvents = "none", e.visibility = "hidden", document.body.appendChild(ge)
    }
    return ge.getBoundingClientRect()
}

function be(e) {
    const t = e.getBoundingClientRect(),
        o = me();
    return {
        left: t.left - o.left,
        top: t.top - o.top,
        bottom: o.height + o.top - t.bottom,
        right: o.width + o.left - t.right,
        width: t.width,
        height: t.height
    }
}

function we(e) {
    if (null === e) return null;
    const t = function(e) {
        return 9 === e.nodeType ? null : e.parentNode
    }(e);
    if (null === t) return null;
    if (9 === t.nodeType) return document;
    if (1 === t.nodeType) {
        const {
            overflow: e,
            overflowX: o,
            overflowY: r
        } = getComputedStyle(t);
        if (/(auto|scroll|overlay)/.test(e + r + o)) return t
    }
    return we(t)
}
const ye = s({
        name: "Binder",
        props: {
            syncTargetWithParent: Boolean,
            syncTarget: {
                type: Boolean,
                default: !0
            }
        },
        setup(e) {
            var o;
            i("VBinder", null === (o = d()) || void 0 === o ? void 0 : o.proxy);
            const r = n("VBinder", null),
                l = a(null);
            let s = [];
            const u = () => {
                    for (const e of s) q("scroll", e, c, !0);
                    s = []
                },
                p = new Set,
                c = () => {
                    le(f)
                },
                f = () => {
                    p.forEach((e => e()))
                },
                h = new Set,
                v = () => {
                    h.forEach((e => e()))
                };
            return t((() => {
                q("resize", window, v), u()
            })), {
                targetRef: l,
                setTargetRef: t => {
                    l.value = t, r && e.syncTargetWithParent && r.setTargetRef(t)
                },
                addScrollListener: e => {
                    0 === p.size && (() => {
                        let e = l.value;
                        for (; e = we(e), null !== e;) s.push(e);
                        for (const t of s) U("scroll", t, c, !0)
                    })(), p.has(e) || p.add(e)
                },
                removeScrollListener: e => {
                    p.has(e) && p.delete(e), 0 === p.size && u()
                },
                addResizeListener: e => {
                    0 === h.size && U("resize", window, v), h.has(e) || h.add(e)
                },
                removeResizeListener: e => {
                    h.has(e) && h.delete(e), 0 === h.size && q("resize", window, v)
                }
            }
        },
        render() {
            return F("binder", this.$slots)
        }
    }),
    $e = s({
        name: "Target",
        setup() {
            const {
                setTargetRef: e,
                syncTarget: t
            } = n("VBinder");
            return {
                syncTarget: t,
                setTargetDirective: {
                    mounted: e,
                    updated: e
                }
            }
        },
        render() {
            const {
                syncTarget: e,
                setTargetDirective: t
            } = this;
            return e ? u(_("follower", this.$slots), [
                [t]
            ]) : _("follower", this.$slots)
        }
    }),
    xe = "@@mmoContext",
    Se = {
        mounted(e, {
            value: t
        }) {
            e[xe] = {
                handler: void 0
            }, "function" == typeof t && (e[xe].handler = t, U("mousemoveoutside", e, t))
        },
        updated(e, {
            value: t
        }) {
            const o = e[xe];
            "function" == typeof t ? o.handler ? o.handler !== t && (q("mousemoveoutside", e, o.handler), o.handler = t, U("mousemoveoutside", e, t)) : (e[xe].handler = t, U("mousemoveoutside", e, t)) : o.handler && (q("mousemoveoutside", e, o.handler), o.handler = void 0)
        },
        unmounted(e) {
            const {
                handler: t
            } = e[xe];
            t && q("mousemoveoutside", e, t), e[xe].handler = void 0
        }
    },
    {
        c: Me
    } = p(),
    Be = "vueuc-style",
    Ce = {
        top: "bottom",
        bottom: "top",
        left: "right",
        right: "left"
    },
    ze = {
        start: "end",
        center: "center",
        end: "start"
    },
    Te = {
        top: "height",
        bottom: "height",
        left: "width",
        right: "width"
    },
    Ee = {
        "bottom-start": "top left",
        bottom: "top center",
        "bottom-end": "top right",
        "top-start": "bottom left",
        top: "bottom center",
        "top-end": "bottom right",
        "right-start": "top left",
        right: "center left",
        "right-end": "bottom left",
        "left-start": "top right",
        left: "center right",
        "left-end": "bottom right"
    },
    Re = {
        "bottom-start": "bottom left",
        bottom: "bottom center",
        "bottom-end": "bottom right",
        "top-start": "top left",
        top: "top center",
        "top-end": "top right",
        "right-start": "top right",
        right: "center right",
        "right-end": "bottom right",
        "left-start": "top left",
        left: "center left",
        "left-end": "bottom left"
    },
    ke = {
        "bottom-start": "right",
        "bottom-end": "left",
        "top-start": "right",
        "top-end": "left",
        "right-start": "bottom",
        "right-end": "top",
        "left-start": "bottom",
        "left-end": "top"
    },
    Oe = {
        top: !0,
        bottom: !1,
        left: !0,
        right: !1
    },
    je = {
        top: "end",
        bottom: "start",
        left: "end",
        right: "start"
    };
const Ae = Me([Me(".v-binder-follower-container", {
        position: "absolute",
        left: "0",
        right: "0",
        top: "0",
        height: "0",
        pointerEvents: "none",
        zIndex: "auto"
    }), Me(".v-binder-follower-content", {
        position: "absolute",
        zIndex: "auto"
    }, [Me("> *", {
        pointerEvents: "all"
    })])]),
    Ie = s({
        name: "Follower",
        inheritAttrs: !1,
        props: {
            show: Boolean,
            enabled: {
                type: Boolean,
                default: void 0
            },
            placement: {
                type: String,
                default: "bottom"
            },
            syncTrigger: {
                type: Array,
                default: ["resize", "scroll"]
            },
            to: [String, Object],
            flip: {
                type: Boolean,
                default: !0
            },
            internalShift: Boolean,
            x: Number,
            y: Number,
            width: String,
            minWidth: String,
            containerClass: String,
            teleportDisabled: Boolean,
            zindexable: {
                type: Boolean,
                default: !0
            },
            zIndex: Number,
            overlap: Boolean
        },
        setup(o) {
            const r = n("VBinder"),
                s = l((() => void 0 !== o.enabled ? o.enabled : o.show)),
                i = a(null),
                d = a(null),
                u = () => {
                    const {
                        syncTrigger: e
                    } = o;
                    e.includes("scroll") && r.addScrollListener(b), e.includes("resize") && r.addResizeListener(b)
                },
                p = () => {
                    r.removeScrollListener(b), r.removeResizeListener(b)
                };
            e((() => {
                s.value && (b(), u())
            }));
            const c = f();
            Ae.mount({
                id: "vueuc/binder",
                head: !0,
                anchorMetaName: Be,
                ssr: c
            }), t((() => {
                p()
            })), pe((() => {
                s.value && b()
            }));
            const b = () => {
                if (!s.value) return;
                const e = i.value;
                if (null === e) return;
                const t = r.targetRef,
                    {
                        x: n,
                        y: a,
                        overlap: l
                    } = o,
                    u = void 0 !== n && void 0 !== a ? function(e, t) {
                        const o = me();
                        return {
                            top: t,
                            left: e,
                            height: 0,
                            width: 0,
                            right: o.width - e,
                            bottom: o.height - t
                        }
                    }(n, a) : be(t);
                e.style.setProperty("--v-target-width", `${Math.round(u.width)}px`), e.style.setProperty("--v-target-height", `${Math.round(u.height)}px`);
                const {
                    width: p,
                    minWidth: c,
                    placement: f,
                    internalShift: h,
                    flip: v
                } = o;
                e.setAttribute("v-placement", f), l ? e.setAttribute("v-overlap", "") : e.removeAttribute("v-overlap");
                const {
                    style: g
                } = e;
                g.width = "target" === p ? `${u.width}px` : void 0 !== p ? p : "", g.minWidth = "target" === c ? `${u.width}px` : void 0 !== c ? c : "";
                const m = be(e),
                    b = be(d.value),
                    {
                        left: w,
                        top: y,
                        placement: $
                    } = function(e, t, o, r, n, a) {
                        if (!n || a) return {
                            placement: e,
                            top: 0,
                            left: 0
                        };
                        const [l, s] = e.split("-");
                        let i = null != s ? s : "center",
                            d = {
                                top: 0,
                                left: 0
                            };
                        const u = (e, n, a) => {
                                let l = 0,
                                    s = 0;
                                const i = o[e] - t[n] - t[e];
                                return i > 0 && r && (a ? s = Oe[n] ? i : -i : l = Oe[n] ? i : -i), {
                                    left: l,
                                    top: s
                                }
                            },
                            p = "left" === l || "right" === l;
                        if ("center" !== i) {
                            const r = ke[e],
                                n = Ce[r],
                                a = Te[r];
                            if (o[a] > t[a]) {
                                if (t[r] + t[a] < o[a]) {
                                    const e = (o[a] - t[a]) / 2;
                                    t[r] < e || t[n] < e ? t[r] < t[n] ? (i = ze[s], d = u(a, n, p)) : d = u(a, r, p) : i = "center"
                                }
                            } else o[a] < t[a] && t[n] < 0 && t[r] > t[n] && (i = ze[s])
                        } else {
                            const e = "bottom" === l || "top" === l ? "left" : "top",
                                r = Ce[e],
                                n = Te[e],
                                a = (o[n] - t[n]) / 2;
                            (t[e] < a || t[r] < a) && (t[e] > t[r] ? (i = je[e], d = u(n, e, p)) : (i = je[r], d = u(n, r, p)))
                        }
                        let c = l;
                        return t[l] < o[Te[l]] && t[l] < t[Ce[l]] && (c = Ce[l]), {
                            placement: "center" !== i ? `${c}-${i}` : c,
                            left: d.left,
                            top: d.top
                        }
                    }(f, u, m, h, v, l),
                    x = function(e, t) {
                        return t ? Re[e] : Ee[e]
                    }($, l),
                    {
                        left: S,
                        top: M,
                        transform: B
                    } = function(e, t, o, r, n, a) {
                        if (a) switch (e) {
                            case "bottom-start":
                            case "left-end":
                                return {
                                    top: `${Math.round(o.top-t.top+o.height)}px`,
                                    left: `${Math.round(o.left-t.left)}px`,
                                    transform: "translateY(-100%)"
                                };
                            case "bottom-end":
                            case "right-end":
                                return {
                                    top: `${Math.round(o.top-t.top+o.height)}px`,
                                    left: `${Math.round(o.left-t.left+o.width)}px`,
                                    transform: "translateX(-100%) translateY(-100%)"
                                };
                            case "top-start":
                            case "left-start":
                                return {
                                    top: `${Math.round(o.top-t.top)}px`,
                                    left: `${Math.round(o.left-t.left)}px`,
                                    transform: ""
                                };
                            case "top-end":
                            case "right-start":
                                return {
                                    top: `${Math.round(o.top-t.top)}px`,
                                    left: `${Math.round(o.left-t.left+o.width)}px`,
                                    transform: "translateX(-100%)"
                                };
                            case "top":
                                return {
                                    top: `${Math.round(o.top-t.top)}px`,
                                    left: `${Math.round(o.left-t.left+o.width/2)}px`,
                                    transform: "translateX(-50%)"
                                };
                            case "right":
                                return {
                                    top: `${Math.round(o.top-t.top+o.height/2)}px`,
                                    left: `${Math.round(o.left-t.left+o.width)}px`,
                                    transform: "translateX(-100%) translateY(-50%)"
                                };
                            case "left":
                                return {
                                    top: `${Math.round(o.top-t.top+o.height/2)}px`,
                                    left: `${Math.round(o.left-t.left)}px`,
                                    transform: "translateY(-50%)"
                                };
                            default:
                                return {
                                    top: `${Math.round(o.top-t.top+o.height)}px`,
                                    left: `${Math.round(o.left-t.left+o.width/2)}px`,
                                    transform: "translateX(-50%) translateY(-100%)"
                                }
                        }
                        switch (e) {
                            case "bottom-start":
                                return {
                                    top: `${Math.round(o.top-t.top+o.height+r)}px`,
                                    left: `${Math.round(o.left-t.left+n)}px`,
                                    transform: ""
                                };
                            case "bottom-end":
                                return {
                                    top: `${Math.round(o.top-t.top+o.height+r)}px`,
                                    left: `${Math.round(o.left-t.left+o.width+n)}px`,
                                    transform: "translateX(-100%)"
                                };
                            case "top-start":
                                return {
                                    top: `${Math.round(o.top-t.top+r)}px`,
                                    left: `${Math.round(o.left-t.left+n)}px`,
                                    transform: "translateY(-100%)"
                                };
                            case "top-end":
                                return {
                                    top: `${Math.round(o.top-t.top+r)}px`,
                                    left: `${Math.round(o.left-t.left+o.width+n)}px`,
                                    transform: "translateX(-100%) translateY(-100%)"
                                };
                            case "right-start":
                                return {
                                    top: `${Math.round(o.top-t.top+r)}px`,
                                    left: `${Math.round(o.left-t.left+o.width+n)}px`,
                                    transform: ""
                                };
                            case "right-end":
                                return {
                                    top: `${Math.round(o.top-t.top+o.height+r)}px`,
                                    left: `${Math.round(o.left-t.left+o.width+n)}px`,
                                    transform: "translateY(-100%)"
                                };
                            case "left-start":
                                return {
                                    top: `${Math.round(o.top-t.top+r)}px`,
                                    left: `${Math.round(o.left-t.left+n)}px`,
                                    transform: "translateX(-100%)"
                                };
                            case "left-end":
                                return {
                                    top: `${Math.round(o.top-t.top+o.height+r)}px`,
                                    left: `${Math.round(o.left-t.left+n)}px`,
                                    transform: "translateX(-100%) translateY(-100%)"
                                };
                            case "top":
                                return {
                                    top: `${Math.round(o.top-t.top+r)}px`,
                                    left: `${Math.round(o.left-t.left+o.width/2+n)}px`,
                                    transform: "translateY(-100%) translateX(-50%)"
                                };
                            case "right":
                                return {
                                    top: `${Math.round(o.top-t.top+o.height/2+r)}px`,
                                    left: `${Math.round(o.left-t.left+o.width+n)}px`,
                                    transform: "translateY(-50%)"
                                };
                            case "left":
                                return {
                                    top: `${Math.round(o.top-t.top+o.height/2+r)}px`,
                                    left: `${Math.round(o.left-t.left+n)}px`,
                                    transform: "translateY(-50%) translateX(-100%)"
                                };
                            default:
                                return {
                                    top: `${Math.round(o.top-t.top+o.height+r)}px`,
                                    left: `${Math.round(o.left-t.left+o.width/2+n)}px`,
                                    transform: "translateX(-50%)"
                                }
                        }
                    }($, b, u, y, w, l);
                e.setAttribute("v-placement", $), e.style.setProperty("--v-offset-left", `${Math.round(w)}px`), e.style.setProperty("--v-offset-top", `${Math.round(y)}px`), e.style.transform = `translateX(${S}) translateY(${M}) ${B}`, e.style.setProperty("--v-transform-origin", x), e.style.transformOrigin = x
            };
            h(s, (e => {
                e ? (u(), w()) : p()
            }));
            const w = () => {
                m().then(b).catch((e => {}))
            };
            ["placement", "x", "y", "internalShift", "flip", "width", "overlap", "minWidth"].forEach((e => {
                h(v(o, e), b)
            })), ["teleportDisabled"].forEach((e => {
                h(v(o, e), w)
            })), h(v(o, "syncTrigger"), (e => {
                e.includes("resize") ? r.addResizeListener(b) : r.removeResizeListener(b), e.includes("scroll") ? r.addScrollListener(b) : r.removeScrollListener(b)
            }));
            const y = g(),
                $ = l((() => {
                    const {
                        to: e
                    } = o;
                    if (void 0 !== e) return e;
                    y.value
                }));
            return {
                VBinder: r,
                mergedEnabled: s,
                offsetContainerRef: d,
                followerRef: i,
                mergedTo: $,
                syncPosition: b
            }
        },
        render() {
            return c(L, {
                show: this.show,
                to: this.mergedTo,
                disabled: this.teleportDisabled
            }, {
                default: () => {
                    var e, t;
                    const o = c("div", {
                        class: ["v-binder-follower-container", this.containerClass],
                        ref: "offsetContainerRef"
                    }, [c("div", {
                        class: "v-binder-follower-content",
                        ref: "followerRef"
                    }, null === (t = (e = this.$slots).default) || void 0 === t ? void 0 : t.call(e))]);
                    return this.zindexable ? u(o, [
                        [D, {
                            enabled: this.mergedEnabled,
                            zIndex: this.zIndex
                        }]
                    ]) : o
                }
            })
        }
    }),
    Pe = /^(\d|\.)+$/,
    We = /(\d|\.)+/;

function Ne(e, {
    c: t = 1,
    offset: o = 0,
    attachPx: r = !0
} = {}) {
    if ("number" == typeof e) {
        const r = (e + o) * t;
        return 0 === r ? "0" : `${r}px`
    }
    if ("string" == typeof e) {
        if (Pe.test(e)) {
            const n = (Number(e) + o) * t;
            return r ? 0 === n ? "0" : `${n}px` : `${n}`
        } {
            const r = We.exec(e);
            return r ? e.replace(We, String((Number(r[0]) + o) * t)) : e
        }
    }
    return e
}
let Fe;
const _e = {
        top: "bottom",
        bottom: "top",
        left: "right",
        right: "left"
    },
    Le = "var(--n-arrow-height) * 1.414",
    De = b([w("popover", "\n transition:\n box-shadow .3s var(--n-bezier),\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier);\n position: relative;\n font-size: var(--n-font-size);\n color: var(--n-text-color);\n box-shadow: var(--n-box-shadow);\n word-break: break-word;\n ", [b(">", [w("scrollbar", "\n height: inherit;\n max-height: inherit;\n ")]), y("raw", "\n background-color: var(--n-color);\n border-radius: var(--n-border-radius);\n ", [y("scrollable", [y("show-header-or-footer", "padding: var(--n-padding);")])]), $("header", "\n padding: var(--n-padding);\n border-bottom: 1px solid var(--n-divider-color);\n transition: border-color .3s var(--n-bezier);\n "), $("footer", "\n padding: var(--n-padding);\n border-top: 1px solid var(--n-divider-color);\n transition: border-color .3s var(--n-bezier);\n "), x("scrollable, show-header-or-footer", [$("content", "\n padding: var(--n-padding);\n ")])]), w("popover-shared", "\n transform-origin: inherit;\n ", [w("popover-arrow-wrapper", "\n position: absolute;\n overflow: hidden;\n pointer-events: none;\n ", [w("popover-arrow", `\n transition: background-color .3s var(--n-bezier);\n position: absolute;\n display: block;\n width: calc(${Le});\n height: calc(${Le});\n box-shadow: 0 0 8px 0 rgba(0, 0, 0, .12);\n transform: rotate(45deg);\n background-color: var(--n-color);\n pointer-events: all;\n `)]), b("&.popover-transition-enter-from, &.popover-transition-leave-to", "\n opacity: 0;\n transform: scale(.85);\n "), b("&.popover-transition-enter-to, &.popover-transition-leave-from", "\n transform: scale(1);\n opacity: 1;\n "), b("&.popover-transition-enter-active", "\n transition:\n box-shadow .3s var(--n-bezier),\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier),\n opacity .15s var(--n-bezier-ease-out),\n transform .15s var(--n-bezier-ease-out);\n "), b("&.popover-transition-leave-active", "\n transition:\n box-shadow .3s var(--n-bezier),\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier),\n opacity .15s var(--n-bezier-ease-in),\n transform .15s var(--n-bezier-ease-in);\n ")]), Ye("top-start", `\n top: calc(${Le} / -2);\n left: calc(${Xe("top-start")} - var(--v-offset-left));\n `), Ye("top", `\n top: calc(${Le} / -2);\n transform: translateX(calc(${Le} / -2)) rotate(45deg);\n left: 50%;\n `), Ye("top-end", `\n top: calc(${Le} / -2);\n right: calc(${Xe("top-end")} + var(--v-offset-left));\n `), Ye("bottom-start", `\n bottom: calc(${Le} / -2);\n left: calc(${Xe("bottom-start")} - var(--v-offset-left));\n `), Ye("bottom", `\n bottom: calc(${Le} / -2);\n transform: translateX(calc(${Le} / -2)) rotate(45deg);\n left: 50%;\n `), Ye("bottom-end", `\n bottom: calc(${Le} / -2);\n right: calc(${Xe("bottom-end")} + var(--v-offset-left));\n `), Ye("left-start", `\n left: calc(${Le} / -2);\n top: calc(${Xe("left-start")} - var(--v-offset-top));\n `), Ye("left", `\n left: calc(${Le} / -2);\n transform: translateY(calc(${Le} / -2)) rotate(45deg);\n top: 50%;\n `), Ye("left-end", `\n left: calc(${Le} / -2);\n bottom: calc(${Xe("left-end")} + var(--v-offset-top));\n `), Ye("right-start", `\n right: calc(${Le} / -2);\n top: calc(${Xe("right-start")} - var(--v-offset-top));\n `), Ye("right", `\n right: calc(${Le} / -2);\n transform: translateY(calc(${Le} / -2)) rotate(45deg);\n top: 50%;\n `), Ye("right-end", `\n right: calc(${Le} / -2);\n bottom: calc(${Xe("right-end")} + var(--v-offset-top));\n `), ...V({
        top: ["right-start", "left-start"],
        right: ["top-end", "bottom-end"],
        bottom: ["right-end", "left-end"],
        left: ["top-start", "bottom-start"]
    }, ((e, t) => {
        const o = ["right", "left"].includes(t),
            r = o ? "width" : "height";
        return e.map((e => {
            const n = "end" === e.split("-")[1],
                a = `calc((${`var(--v-target-${r}, 0px)`} - ${Le}) / 2)`,
                l = Xe(e);
            return b(`[v-placement="${e}"] >`, [w("popover-shared", [x("center-arrow", [w("popover-arrow", `${t}: calc(max(${a}, ${l}) ${n?"+":"-"} var(--v-offset-${o?"left":"top"}));`)])])])
        }))
    }))]);

function Xe(e) {
    return ["top", "bottom"].includes(e.split("-")[0]) ? "var(--n-arrow-offset)" : "var(--n-arrow-offset-vertical)"
}

function Ye(e, t) {
    const o = e.split("-")[0],
        r = ["top", "bottom"].includes(o) ? "height: var(--n-space-arrow);" : "width: var(--n-space-arrow);";
    return b(`[v-placement="${e}"] >`, [w("popover-shared", `\n margin-${_e[o]}: var(--n-space);\n `, [x("show-arrow", `\n margin-${_e[o]}: var(--n-space-arrow);\n `), x("overlap", "\n margin: 0;\n "), S("popover-arrow-wrapper", `\n right: 0;\n left: 0;\n top: 0;\n bottom: 0;\n ${o}: 100%;\n ${_e[o]}: auto;\n ${r}\n `, [w("popover-arrow", t)])])])
}
const He = Object.assign(Object.assign({}, M.props), {
    to: ve.propTo,
    show: Boolean,
    trigger: String,
    showArrow: Boolean,
    delay: Number,
    duration: Number,
    raw: Boolean,
    arrowPointToCenter: Boolean,
    arrowClass: String,
    arrowStyle: [String, Object],
    arrowWrapperClass: String,
    arrowWrapperStyle: [String, Object],
    displayDirective: String,
    x: Number,
    y: Number,
    flip: Boolean,
    overlap: Boolean,
    placement: String,
    width: [Number, String],
    keepAliveOnHover: Boolean,
    scrollable: Boolean,
    contentClass: String,
    contentStyle: [Object, String],
    headerClass: String,
    headerStyle: [Object, String],
    footerClass: String,
    footerStyle: [Object, String],
    internalDeactivateImmediately: Boolean,
    animated: Boolean,
    onClickoutside: Function,
    internalTrapFocus: Boolean,
    internalOnAfterLeave: Function,
    minWidth: Number,
    maxWidth: Number
});

function Ve({
    arrowClass: e,
    arrowStyle: t,
    arrowWrapperClass: o,
    arrowWrapperStyle: r,
    clsPrefix: n
}) {
    return c("div", {
        key: "__popover-arrow__",
        style: r,
        class: [`${n}-popover-arrow-wrapper`, o]
    }, c("div", {
        class: [`${n}-popover-arrow`, e],
        style: t
    }))
}
const Ue = s({
        name: "PopoverBody",
        inheritAttrs: !1,
        props: He,
        setup(e, {
            slots: o,
            attrs: r
        }) {
            const {
                namespaceRef: l,
                mergedClsPrefixRef: s,
                inlineThemeDisabled: d
            } = C(e), p = M("Popover", "-popover", De, z, e, s), f = a(null), g = n("NPopover"), m = a(null), b = a(e.show), w = a(!1);
            T((() => {
                const {
                    show: t
                } = e;
                !t || (void 0 === Fe && (Fe = navigator.userAgent.includes("Node.js") || navigator.userAgent.includes("jsdom")), Fe) || e.internalDeactivateImmediately || (w.value = !0)
            }));
            const y = E((() => {
                    const {
                        trigger: t,
                        onClickoutside: o
                    } = e, r = [], {
                        positionManuallyRef: {
                            value: n
                        }
                    } = g;
                    return n || ("click" !== t || o || r.push([X, F, void 0, {
                        capture: !0
                    }]), "hover" === t && r.push([Se, I])), o && r.push([X, F, void 0, {
                        capture: !0
                    }]), ("show" === e.displayDirective || e.animated && w.value) && r.push([R, e.show]), r
                })),
                $ = E((() => {
                    const {
                        common: {
                            cubicBezierEaseInOut: e,
                            cubicBezierEaseIn: t,
                            cubicBezierEaseOut: o
                        },
                        self: {
                            space: r,
                            spaceArrow: n,
                            padding: a,
                            fontSize: l,
                            textColor: s,
                            dividerColor: i,
                            color: d,
                            boxShadow: u,
                            borderRadius: c,
                            arrowHeight: f,
                            arrowOffset: h,
                            arrowOffsetVertical: v
                        }
                    } = p.value;
                    return {
                        "--n-box-shadow": u,
                        "--n-bezier": e,
                        "--n-bezier-ease-in": t,
                        "--n-bezier-ease-out": o,
                        "--n-font-size": l,
                        "--n-text-color": s,
                        "--n-color": d,
                        "--n-divider-color": i,
                        "--n-border-radius": c,
                        "--n-arrow-height": f,
                        "--n-arrow-offset": h,
                        "--n-arrow-offset-vertical": v,
                        "--n-padding": a,
                        "--n-space": r,
                        "--n-space-arrow": n
                    }
                })),
                x = E((() => {
                    const t = "trigger" === e.width ? void 0 : Ne(e.width),
                        o = [];
                    t && o.push({
                        width: t
                    });
                    const {
                        maxWidth: r,
                        minWidth: n
                    } = e;
                    return r && o.push({
                        maxWidth: Ne(r)
                    }), n && o.push({
                        maxWidth: Ne(n)
                    }), d || o.push($.value), o
                })),
                S = d ? k("popover", void 0, $, e) : void 0;

            function B(t) {
                "hover" === e.trigger && e.keepAliveOnHover && e.show && g.handleMouseEnter(t)
            }

            function A(t) {
                "hover" === e.trigger && e.keepAliveOnHover && g.handleMouseLeave(t)
            }

            function I(t) {
                "hover" !== e.trigger || _().contains(K(t)) || g.handleMouseMoveOutside(t)
            }

            function F(t) {
                ("click" === e.trigger && !_().contains(K(t)) || e.onClickoutside) && g.handleClickOutside(t)
            }

            function _() {
                return g.getTriggerElement()
            }
            return g.setBodyInstance({
                syncPosition: function() {
                    var e;
                    null === (e = f.value) || void 0 === e || e.syncPosition()
                }
            }), t((() => {
                g.setBodyInstance(null)
            })), h(v(e, "show"), (t => {
                e.animated || (b.value = !!t)
            })), i(N, m), i(W, null), i(P, null), {
                displayed: w,
                namespace: l,
                isMounted: g.isMountedRef,
                zIndex: g.zIndexRef,
                followerRef: f,
                adjustedTo: ve(e),
                followerEnabled: b,
                renderContentNode: function() {
                    if (null == S || S.onRender(), !("show" === e.displayDirective || e.show || e.animated && w.value)) return null;
                    let t;
                    const n = g.internalRenderBodyRef.value,
                        {
                            value: a
                        } = s;
                    if (n) t = n([`${a}-popover-shared`, null == S ? void 0 : S.themeClass.value, e.overlap && `${a}-popover-shared--overlap`, e.showArrow && `${a}-popover-shared--show-arrow`, e.arrowPointToCenter && `${a}-popover-shared--center-arrow`], m, x.value, B, A);
                    else {
                        const {
                            value: n
                        } = g.extraClassRef, {
                            internalTrapFocus: l
                        } = e, s = !J(o.header) || !J(o.footer), i = () => {
                            var t, r;
                            const n = s ? c(j, null, Z(o.header, (t => t ? c("div", {
                                class: [`${a}-popover__header`, e.headerClass],
                                style: e.headerStyle
                            }, t) : null)), Z(o.default, (t => t ? c("div", {
                                class: [`${a}-popover__content`, e.contentClass],
                                style: e.contentStyle
                            }, o) : null)), Z(o.footer, (t => t ? c("div", {
                                class: [`${a}-popover__footer`, e.footerClass],
                                style: e.footerStyle
                            }, t) : null))) : e.scrollable ? null === (t = o.default) || void 0 === t ? void 0 : t.call(o) : c("div", {
                                class: [`${a}-popover__content`, e.contentClass],
                                style: e.contentStyle
                            }, o);
                            return [e.scrollable ? c(Q, {
                                contentClass: s ? void 0 : `${a}-popover__content ${null!==(r=e.contentClass)&&void 0!==r?r:""}`,
                                contentStyle: s ? void 0 : e.contentStyle
                            }, {
                                default: () => n
                            }) : n, e.showArrow ? Ve({
                                arrowClass: e.arrowClass,
                                arrowStyle: e.arrowStyle,
                                arrowWrapperClass: e.arrowWrapperClass,
                                arrowWrapperStyle: e.arrowWrapperStyle,
                                clsPrefix: a
                            }) : null]
                        };
                        t = c("div", O({
                            class: [`${a}-popover`, `${a}-popover-shared`, null == S ? void 0 : S.themeClass.value, n.map((e => `${a}-${e}`)), {
                                [`${a}-popover--scrollable`]: e.scrollable,
                                [`${a}-popover--show-header-or-footer`]: s,
                                [`${a}-popover--raw`]: e.raw,
                                [`${a}-popover-shared--overlap`]: e.overlap,
                                [`${a}-popover-shared--show-arrow`]: e.showArrow,
                                [`${a}-popover-shared--center-arrow`]: e.arrowPointToCenter
                            }],
                            ref: m,
                            style: x.value,
                            onKeydown: g.handleKeydown,
                            onMouseenter: B,
                            onMouseleave: A
                        }, r), l ? c(Y, {
                            active: e.show,
                            autoFocus: !0
                        }, {
                            default: i
                        }) : i())
                    }
                    return u(t, y.value)
                }
            }
        },
        render() {
            return c(Ie, {
                ref: "followerRef",
                zIndex: this.zIndex,
                show: this.show,
                enabled: this.followerEnabled,
                to: this.adjustedTo,
                x: this.x,
                y: this.y,
                flip: this.flip,
                placement: this.placement,
                containerClass: this.namespace,
                overlap: this.overlap,
                width: "trigger" === this.width ? "target" : void 0,
                teleportDisabled: this.adjustedTo === ve.tdkey
            }, {
                default: () => this.animated ? c(B, {
                    name: "popover-transition",
                    appear: this.isMounted,
                    onEnter: () => {
                        this.followerEnabled = !0
                    },
                    onAfterLeave: () => {
                        var e;
                        null === (e = this.internalOnAfterLeave) || void 0 === e || e.call(this), this.followerEnabled = !1, this.displayed = !1
                    }
                }, {
                    default: this.renderContentNode
                }) : this.renderContentNode()
            })
        }
    }),
    qe = Object.keys(He),
    Ke = {
        focus: ["onFocus", "onBlur"],
        click: ["onClick"],
        hover: ["onMouseenter", "onMouseleave"],
        manual: [],
        nested: ["onFocus", "onBlur", "onMouseenter", "onMouseleave", "onClick"]
    };
const Je = {
        show: {
            type: Boolean,
            default: void 0
        },
        defaultShow: Boolean,
        showArrow: {
            type: Boolean,
            default: !0
        },
        trigger: {
            type: String,
            default: "hover"
        },
        delay: {
            type: Number,
            default: 100
        },
        duration: {
            type: Number,
            default: 100
        },
        raw: Boolean,
        placement: {
            type: String,
            default: "top"
        },
        x: Number,
        y: Number,
        arrowPointToCenter: Boolean,
        disabled: Boolean,
        getDisabled: Function,
        displayDirective: {
            type: String,
            default: "if"
        },
        arrowClass: String,
        arrowStyle: [String, Object],
        arrowWrapperClass: String,
        arrowWrapperStyle: [String, Object],
        flip: {
            type: Boolean,
            default: !0
        },
        animated: {
            type: Boolean,
            default: !0
        },
        width: {
            type: [Number, String],
            default: void 0
        },
        overlap: Boolean,
        keepAliveOnHover: {
            type: Boolean,
            default: !0
        },
        zIndex: Number,
        to: ve.propTo,
        scrollable: Boolean,
        contentClass: String,
        contentStyle: [Object, String],
        headerClass: String,
        headerStyle: [Object, String],
        footerClass: String,
        footerStyle: [Object, String],
        onClickoutside: Function,
        "onUpdate:show": [Function, Array],
        onUpdateShow: [Function, Array],
        internalDeactivateImmediately: Boolean,
        internalSyncTargetWithParent: Boolean,
        internalInheritedEventHandlers: {
            type: Array,
            default: () => []
        },
        internalTrapFocus: Boolean,
        internalExtraClass: {
            type: Array,
            default: () => []
        },
        onShow: [Function, Array],
        onHide: [Function, Array],
        arrow: {
            type: Boolean,
            default: void 0
        },
        minWidth: Number,
        maxWidth: Number
    },
    Ze = s({
        name: "Popover",
        inheritAttrs: !1,
        props: Object.assign(Object.assign(Object.assign({}, M.props), Je), {
            internalOnAfterLeave: Function,
            internalRenderBody: Function
        }),
        slots: Object,
        __popover__: !0,
        setup(e) {
            const t = g(),
                o = a(null),
                r = E((() => e.show)),
                n = a(e.defaultShow),
                s = ee(r, n),
                d = l((() => !e.disabled && s.value)),
                u = () => {
                    if (e.disabled) return !0;
                    const {
                        getDisabled: t
                    } = e;
                    return !!(null == t ? void 0 : t())
                },
                p = () => !u() && s.value,
                c = te(e, ["arrow", "showArrow"]),
                f = E((() => !e.overlap && c.value));
            let h = null;
            const m = a(null),
                b = a(null),
                w = l((() => void 0 !== e.x && void 0 !== e.y));

            function y(t) {
                const {
                    "onUpdate:show": o,
                    onUpdateShow: r,
                    onShow: a,
                    onHide: l
                } = e;
                n.value = t, o && G(o, t), r && G(r, t), t && a && G(a, !0), t && l && G(l, !1)
            }

            function $() {
                const {
                    value: e
                } = m;
                e && (window.clearTimeout(e), m.value = null)
            }

            function x() {
                const {
                    value: e
                } = b;
                e && (window.clearTimeout(e), b.value = null)
            }

            function S() {
                const t = u();
                if ("hover" === e.trigger && !t) {
                    if (x(), null !== m.value) return;
                    if (p()) return;
                    const t = () => {
                            y(!0), m.value = null
                        },
                        {
                            delay: o
                        } = e;
                    0 === o ? t() : m.value = window.setTimeout(t, o)
                }
            }

            function M() {
                const t = u();
                if ("hover" === e.trigger && !t) {
                    if ($(), null !== b.value) return;
                    if (!p()) return;
                    const t = () => {
                            y(!1), b.value = null
                        },
                        {
                            duration: o
                        } = e;
                    0 === o ? t() : b.value = window.setTimeout(t, o)
                }
            }
            i("NPopover", {
                getTriggerElement: function() {
                    var e;
                    return null === (e = o.value) || void 0 === e ? void 0 : e.targetRef
                },
                handleKeydown: function(t) {
                    e.internalTrapFocus && "Escape" === t.key && ($(), x(), y(!1))
                },
                handleMouseEnter: S,
                handleMouseLeave: M,
                handleClickOutside: function(t) {
                    var o;
                    p() && ("click" === e.trigger && ($(), x(), y(!1)), null === (o = e.onClickoutside) || void 0 === o || o.call(e, t))
                },
                handleMouseMoveOutside: function() {
                    M()
                },
                setBodyInstance: function(e) {
                    h = e
                },
                positionManuallyRef: w,
                isMountedRef: t,
                zIndexRef: v(e, "zIndex"),
                extraClassRef: v(e, "internalExtraClass"),
                internalRenderBodyRef: v(e, "internalRenderBody")
            }), T((() => {
                s.value && u() && y(!1)
            }));
            return {
                binderInstRef: o,
                positionManually: w,
                mergedShowConsideringDisabledProp: d,
                uncontrolledShow: n,
                mergedShowArrow: f,
                getMergedShow: p,
                setShow: function(e) {
                    n.value = e
                },
                handleClick: function() {
                    if ("click" === e.trigger && !u()) {
                        $(), x();
                        y(!p())
                    }
                },
                handleMouseEnter: S,
                handleMouseLeave: M,
                handleFocus: function() {
                    const t = u();
                    if ("focus" === e.trigger && !t) {
                        if (p()) return;
                        y(!0)
                    }
                },
                handleBlur: function() {
                    const t = u();
                    if ("focus" === e.trigger && !t) {
                        if (!p()) return;
                        y(!1)
                    }
                },
                syncPosition: function() {
                    h && h.syncPosition()
                }
            }
        },
        render() {
            var e;
            const {
                positionManually: t,
                $slots: o
            } = this;
            let r, n = !1;
            if (!t && (r = H(o, "trigger"), r)) {
                r = A(r), r = r.type === I ? c("span", [r]) : r;
                const o = {
                    onClick: this.handleClick,
                    onMouseenter: this.handleMouseEnter,
                    onMouseleave: this.handleMouseLeave,
                    onFocus: this.handleFocus,
                    onBlur: this.handleBlur
                };
                if (null === (e = r.type) || void 0 === e ? void 0 : e.__popover__) n = !0, r.props || (r.props = {
                    internalSyncTargetWithParent: !0,
                    internalInheritedEventHandlers: []
                }), r.props.internalSyncTargetWithParent = !0, r.props.internalInheritedEventHandlers ? r.props.internalInheritedEventHandlers = [o, ...r.props.internalInheritedEventHandlers] : r.props.internalInheritedEventHandlers = [o];
                else {
                    const {
                        internalInheritedEventHandlers: e
                    } = this, n = [o, ...e], i = {
                        onBlur: e => {
                            n.forEach((t => {
                                t.onBlur(e)
                            }))
                        },
                        onFocus: e => {
                            n.forEach((t => {
                                t.onFocus(e)
                            }))
                        },
                        onClick: e => {
                            n.forEach((t => {
                                t.onClick(e)
                            }))
                        },
                        onMouseenter: e => {
                            n.forEach((t => {
                                t.onMouseenter(e)
                            }))
                        },
                        onMouseleave: e => {
                            n.forEach((t => {
                                t.onMouseleave(e)
                            }))
                        }
                    };
                    a = r, l = e ? "nested" : t ? "manual" : this.trigger, s = i, Ke[l].forEach((e => {
                        a.props ? a.props = Object.assign({}, a.props) : a.props = {};
                        const t = a.props[e],
                            o = s[e];
                        a.props[e] = t ? (...e) => {
                            t(...e), o(...e)
                        } : o
                    }))
                }
            }
            var a, l, s;
            return c(ye, {
                ref: "binderInstRef",
                syncTarget: !n,
                syncTargetWithParent: this.internalSyncTargetWithParent
            }, {
                default: () => {
                    this.mergedShowConsideringDisabledProp;
                    const e = this.getMergedShow();
                    return [this.internalTrapFocus && e ? u(c("div", {
                        style: {
                            position: "fixed",
                            top: 0,
                            right: 0,
                            bottom: 0,
                            left: 0
                        }
                    }), [
                        [D, {
                            enabled: e,
                            zIndex: this.zIndex
                        }]
                    ]) : null, t ? null : c($e, null, {
                        default: () => r
                    }), c(Ue, oe(this.$props, qe, Object.assign(Object.assign({}, this.$attrs), {
                        showArrow: this.mergedShowArrow,
                        show: e
                    })), {
                        default: () => {
                            var e, t;
                            return null === (t = (e = this.$slots).default) || void 0 === t ? void 0 : t.call(e)
                        },
                        header: () => {
                            var e, t;
                            return null === (t = (e = this.$slots).header) || void 0 === t ? void 0 : t.call(e)
                        },
                        footer: () => {
                            var e, t;
                            return null === (t = (e = this.$slots).footer) || void 0 === t ? void 0 : t.call(e)
                        }
                    })]
                }
            })
        }
    });
export {
    ye as B, Ze as N, $e as V, Ie as a, le as b, Me as c, Be as d, fe as e, Ne as f, ce as i, pe as o, Je as p, Ve as r, ve as u
};