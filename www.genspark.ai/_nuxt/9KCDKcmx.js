import {
    _ as e,
    d as a,
    F as i,
    k as n,
    o as s,
    b as t,
    t as l,
    n as r
} from "./Cf0SOiw0.js";
const p = {
        class: "theme row5"
    },
    o = {
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    },
    d = ["fill"];
const u = e({
    name: "PlanProDesc",
    props: {
        windowType: {
            type: String,
            default: "standard",
            validator: e => ["standard", "fashion", "video", "phone_call", "credit_insufficient", "capacity_exhausted"].includes(e)
        }
    },
    setup: e => ({
        features: (e.windowType, [{
            translationKey: "pages.pricing.pro-plan-feature1",
            isSideNote: !1
        }, {
            translationKey: "pages.pricing.plus-plan-feature2",
            isSideNote: !1
        }, {
            translationKey: "pages.pricing.plus-plan-feature2a",
            isSideNote: !0
        }, {
            translationKey: "pages.pricing.pro-plan-feature3",
            isSideNote: !1
        }, {
            translationKey: "pages.pricing.plus-plan-feature4",
            isSideNote: !1
        }, {
            translationKey: "pages.pricing.plus-plan-feature4a",
            isSideNote: !0
        }, {
            translationKey: "pages.pricing.plus-plan-feature5",
            isSideNote: !1
        }, {
            translationKey: "pages.pricing.plus-plan-feature5a",
            isSideNote: !0
        }, {
            translationKey: "pages.pricing.plus-plan-feature6",
            isSideNote: !1
        }, {
            translationKey: "pages.pricing.plus-plan-feature6a",
            isSideNote: !0
        }])
    })
}, [
    ["render", function(e, u, c, g, f, y) {
        return s(), a("div", p, [(s(!0), a(i, null, n(g.features, ((i, n) => (s(), a("div", {
            key: n,
            class: "row5_line"
        }, [t("div", {
            class: r(i.isSideNote ? "row5_line_bullet_sub" : "row5_line_bullet")
        }, [(s(), a("svg", o, [t("path", {
            "fill-rule": "evenodd",
            "clip-rule": "evenodd",
            d: "M18.0633 5.67387C18.5196 5.98499 18.6374 6.60712 18.3262 7.06343L10.8262 18.0634C10.6585 18.3095 10.3898 18.4679 10.0934 18.4957C9.79688 18.5235 9.50345 18.4178 9.29289 18.2072L4.79289 13.7072C4.40237 13.3167 4.40237 12.6835 4.79289 12.293C5.18342 11.9025 5.81658 11.9025 6.20711 12.293L9.85368 15.9396L16.6738 5.93676C16.9849 5.48045 17.607 5.36275 18.0633 5.67387Z",
            fill: i.isSideNote ? "none" : "currentColor"
        }, null, 8, d)])), t("span", null, l(e.$t(i.translationKey)), 1)], 2)])))), 128))])
    }],
    ["__scopeId", "data-v-3ab150f0"]
]);
export {
    u as P
};