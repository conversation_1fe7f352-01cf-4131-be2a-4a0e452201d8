import {
    B as e,
    V as o,
    a as n,
    r,
    N as t,
    p as i
} from "./CW991W2w.js";
import {
    a1 as d,
    a0 as l,
    v as a,
    d9 as s,
    c9 as p,
    a3 as u,
    r as c,
    J as f,
    X as v,
    aT as h,
    da as m,
    i as b,
    av as w,
    a4 as y,
    c as g,
    b9 as x,
    K as k,
    F as S,
    au as N,
    al as P,
    ak as R,
    aP as C,
    am as O,
    aO as $,
    aR as I,
    Y as j,
    Z as A,
    db as z,
    ap as F,
    aq as K
} from "./Cf0SOiw0.js";
import {
    p as _,
    m as T,
    d as L
} from "./DpMvtoun.js";
import {
    N as D
} from "./DXvAIxvL.js";
import {
    h as M,
    c as B
} from "./BihyrXkC.js";
import {
    X as E
} from "./WZsIN7xM.js";
import {
    f as H
} from "./BLWq1oPC.js";
import {
    k as q
} from "./9wLWmnxl.js";
import {
    u as U
} from "./BuhfKjCJ.js";
import {
    o as W,
    a as G
} from "./Jr9eiJio.js";
import {
    c as V
} from "./B7VeW_-d.js";

function X(e) {
    return o => {
        e.value = o ? o.$el : null
    }
}
const Z = f({
        name: "ChevronRight",
        render: () => v("svg", {
            viewBox: "0 0 16 16",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg"
        }, v("path", {
            d: "M5.64645 3.14645C5.45118 3.34171 5.45118 3.65829 5.64645 3.85355L9.79289 8L5.64645 12.1464C5.45118 12.3417 5.45118 12.6583 5.64645 12.8536C5.84171 13.0488 6.15829 13.0488 6.35355 12.8536L10.8536 8.35355C11.0488 8.15829 11.0488 7.84171 10.8536 7.64645L6.35355 3.14645C6.15829 2.95118 5.84171 2.95118 5.64645 3.14645Z",
            fill: "currentColor"
        }))
    }),
    J = h("n-dropdown-menu"),
    Y = h("n-dropdown"),
    Q = h("n-dropdown-option"),
    ee = f({
        name: "DropdownDivider",
        props: {
            clsPrefix: {
                type: String,
                required: !0
            }
        },
        render() {
            return v("div", {
                class: `${this.clsPrefix}-dropdown-divider`
            })
        }
    }),
    oe = f({
        name: "DropdownGroupHeader",
        props: {
            clsPrefix: {
                type: String,
                required: !0
            },
            tmNode: {
                type: Object,
                required: !0
            }
        },
        setup() {
            const {
                showIconRef: e,
                hasSubmenuRef: o
            } = b(J), {
                renderLabelRef: n,
                labelFieldRef: r,
                nodePropsRef: t,
                renderOptionRef: i
            } = b(Y);
            return {
                labelField: r,
                showIcon: e,
                hasSubmenu: o,
                renderLabel: n,
                nodeProps: t,
                renderOption: i
            }
        },
        render() {
            var e;
            const {
                clsPrefix: o,
                hasSubmenu: n,
                showIcon: r,
                nodeProps: t,
                renderLabel: i,
                renderOption: d
            } = this, {
                rawNode: l
            } = this.tmNode, a = v("div", Object.assign({
                class: `${o}-dropdown-option`
            }, null == t ? void 0 : t(l)), v("div", {
                class: `${o}-dropdown-option-body ${o}-dropdown-option-body--group`
            }, v("div", {
                "data-dropdown-option": !0,
                class: [`${o}-dropdown-option-body__prefix`, r && `${o}-dropdown-option-body__prefix--show-icon`]
            }, m(l.icon)), v("div", {
                class: `${o}-dropdown-option-body__label`,
                "data-dropdown-option": !0
            }, i ? i(l) : m(null !== (e = l.title) && void 0 !== e ? e : l[this.labelField])), v("div", {
                class: [`${o}-dropdown-option-body__suffix`, n && `${o}-dropdown-option-body__suffix--has-submenu`],
                "data-dropdown-option": !0
            })));
            return d ? d({
                node: a,
                option: l
            }) : a
        }
    });

function ne(e, o) {
    return "submenu" === e.type || void 0 === e.type && void 0 !== e[o]
}

function re(e) {
    return "divider" === e.type
}
const te = f({
        name: "DropdownOption",
        props: {
            clsPrefix: {
                type: String,
                required: !0
            },
            tmNode: {
                type: Object,
                required: !0
            },
            parentKey: {
                type: [String, Number],
                default: null
            },
            placement: {
                type: String,
                default: "right-start"
            },
            props: Object,
            scrollable: Boolean
        },
        setup(e) {
            const o = b(Y),
                {
                    hoverKeyRef: n,
                    keyboardKeyRef: r,
                    lastToggledSubmenuKeyRef: t,
                    pendingKeyPathRef: i,
                    activeKeyPathRef: d,
                    animatedRef: l,
                    mergedShowRef: s,
                    renderLabelRef: p,
                    renderIconRef: u,
                    labelFieldRef: f,
                    childrenFieldRef: v,
                    renderOptionRef: h,
                    nodePropsRef: m,
                    menuPropsRef: w
                } = o,
                y = b(Q, null),
                S = b(J),
                N = b(_),
                P = g((() => e.tmNode.rawNode)),
                R = g((() => {
                    const {
                        value: o
                    } = v;
                    return ne(e.tmNode.rawNode, o)
                })),
                C = g((() => {
                    const {
                        disabled: o
                    } = e.tmNode;
                    return o
                })),
                O = function(e, o, n) {
                    const r = c(e.value);
                    let t = null;
                    return a(e, (e => {
                        null !== t && window.clearTimeout(t), !0 === e ? n && !n.value ? r.value = !0 : t = window.setTimeout((() => {
                            r.value = !0
                        }), o) : r.value = !1
                    })), r
                }(g((() => {
                    if (!R.value) return !1;
                    const {
                        key: o,
                        disabled: d
                    } = e.tmNode;
                    if (d) return !1;
                    const {
                        value: l
                    } = n, {
                        value: a
                    } = r, {
                        value: s
                    } = t, {
                        value: p
                    } = i;
                    return null !== l ? p.includes(o) : null !== a ? p.includes(o) && p[p.length - 1] !== o : null !== s && p.includes(o)
                })), 300, g((() => null === r.value && !l.value))),
                $ = g((() => !!(null == y ? void 0 : y.enteringSubmenuRef.value))),
                I = c(!1);

            function j() {
                const {
                    parentKey: o,
                    tmNode: i
                } = e;
                i.disabled || s.value && (t.value = o, r.value = null, n.value = i.key)
            }
            return k(Q, {
                enteringSubmenuRef: I
            }), {
                labelField: f,
                renderLabel: p,
                renderIcon: u,
                siblingHasIcon: S.showIconRef,
                siblingHasSubmenu: S.hasSubmenuRef,
                menuProps: w,
                popoverBody: N,
                animated: l,
                mergedShowSubmenu: g((() => O.value && !$.value)),
                rawNode: P,
                hasSubmenu: R,
                pending: x((() => {
                    const {
                        value: o
                    } = i, {
                        key: n
                    } = e.tmNode;
                    return o.includes(n)
                })),
                childActive: x((() => {
                    const {
                        value: o
                    } = d, {
                        key: n
                    } = e.tmNode, r = o.findIndex((e => n === e));
                    return -1 !== r && r < o.length - 1
                })),
                active: x((() => {
                    const {
                        value: o
                    } = d, {
                        key: n
                    } = e.tmNode, r = o.findIndex((e => n === e));
                    return -1 !== r && r === o.length - 1
                })),
                mergedDisabled: C,
                renderOption: h,
                nodeProps: m,
                handleClick: function() {
                    const {
                        value: n
                    } = R, {
                        tmNode: r
                    } = e;
                    s.value && (n || r.disabled || (o.doSelect(r.key, r.rawNode), o.doUpdateShow(!1)))
                },
                handleMouseMove: function() {
                    const {
                        tmNode: o
                    } = e;
                    o.disabled || s.value && n.value !== o.key && j()
                },
                handleMouseEnter: j,
                handleMouseLeave: function(o) {
                    if (e.tmNode.disabled) return;
                    if (!s.value) return;
                    const {
                        relatedTarget: r
                    } = o;
                    !r || M({
                        target: r
                    }, "dropdownOption") || M({
                        target: r
                    }, "scrollbarRail") || (n.value = null)
                },
                handleSubmenuBeforeEnter: function() {
                    I.value = !0
                },
                handleSubmenuAfterEnter: function() {
                    I.value = !1
                }
            }
        },
        render() {
            var r, t;
            const {
                animated: i,
                rawNode: d,
                mergedShowSubmenu: l,
                clsPrefix: a,
                siblingHasIcon: s,
                siblingHasSubmenu: p,
                renderLabel: u,
                renderIcon: c,
                renderOption: f,
                nodeProps: h,
                props: b,
                scrollable: g
            } = this;
            let x = null;
            if (l) {
                const e = null === (r = this.menuProps) || void 0 === r ? void 0 : r.call(this, d, d.children);
                x = v(le, Object.assign({}, e, {
                    clsPrefix: a,
                    scrollable: this.scrollable,
                    tmNodes: this.tmNode.children,
                    parentKey: this.tmNode.key
                }))
            }
            const k = {
                    class: [`${a}-dropdown-option-body`, this.pending && `${a}-dropdown-option-body--pending`, this.active && `${a}-dropdown-option-body--active`, this.childActive && `${a}-dropdown-option-body--child-active`, this.mergedDisabled && `${a}-dropdown-option-body--disabled`],
                    onMousemove: this.handleMouseMove,
                    onMouseenter: this.handleMouseEnter,
                    onMouseleave: this.handleMouseLeave,
                    onClick: this.handleClick
                },
                S = null == h ? void 0 : h(d),
                N = v("div", Object.assign({
                    class: [`${a}-dropdown-option`, null == S ? void 0 : S.class],
                    "data-dropdown-option": !0
                }, S), v("div", w(k, b), [v("div", {
                    class: [`${a}-dropdown-option-body__prefix`, s && `${a}-dropdown-option-body__prefix--show-icon`]
                }, [c ? c(d) : m(d.icon)]), v("div", {
                    "data-dropdown-option": !0,
                    class: `${a}-dropdown-option-body__label`
                }, u ? u(d) : m(null !== (t = d[this.labelField]) && void 0 !== t ? t : d.title)), v("div", {
                    "data-dropdown-option": !0,
                    class: [`${a}-dropdown-option-body__suffix`, p && `${a}-dropdown-option-body__suffix--has-submenu`]
                }, this.hasSubmenu ? v(D, null, {
                    default: () => v(Z, null)
                }) : null)]), this.hasSubmenu ? v(e, null, {
                    default: () => [v(o, null, {
                        default: () => v("div", {
                            class: `${a}-dropdown-offset-container`
                        }, v(n, {
                            show: this.mergedShowSubmenu,
                            placement: this.placement,
                            to: g && this.popoverBody || void 0,
                            teleportDisabled: !g
                        }, {
                            default: () => v("div", {
                                class: `${a}-dropdown-menu-wrapper`
                            }, i ? v(y, {
                                onBeforeEnter: this.handleSubmenuBeforeEnter,
                                onAfterEnter: this.handleSubmenuAfterEnter,
                                name: "fade-in-scale-up-transition",
                                appear: !0
                            }, {
                                default: () => x
                            }) : x)
                        }))
                    })]
                }) : null);
            return f ? f({
                node: N,
                option: d
            }) : N
        }
    }),
    ie = f({
        name: "NDropdownGroup",
        props: {
            clsPrefix: {
                type: String,
                required: !0
            },
            tmNode: {
                type: Object,
                required: !0
            },
            parentKey: {
                type: [String, Number],
                default: null
            }
        },
        render() {
            const {
                tmNode: e,
                parentKey: o,
                clsPrefix: n
            } = this, {
                children: r
            } = e;
            return v(S, null, v(oe, {
                clsPrefix: n,
                tmNode: e,
                key: e.key
            }), null == r ? void 0 : r.map((e => {
                const {
                    rawNode: r
                } = e;
                return !1 === r.show ? null : re(r) ? v(ee, {
                    clsPrefix: n,
                    key: e.key
                }) : e.isGroup ? (N("dropdown", "`group` node is not allowed to be put in `group` node."), null) : v(te, {
                    clsPrefix: n,
                    tmNode: e,
                    parentKey: o,
                    key: e.key
                })
            })))
        }
    }),
    de = f({
        name: "DropdownRenderOption",
        props: {
            tmNode: {
                type: Object,
                required: !0
            }
        },
        render() {
            const {
                rawNode: {
                    render: e,
                    props: o
                }
            } = this.tmNode;
            return v("div", o, [null == e ? void 0 : e()])
        }
    }),
    le = f({
        name: "DropdownMenu",
        props: {
            scrollable: Boolean,
            showArrow: Boolean,
            arrowStyle: [String, Object],
            clsPrefix: {
                type: String,
                required: !0
            },
            tmNodes: {
                type: Array,
                default: () => []
            },
            parentKey: {
                type: [String, Number],
                default: null
            }
        },
        setup(e) {
            const {
                renderIconRef: o,
                childrenFieldRef: n
            } = b(Y);
            k(J, {
                showIconRef: g((() => {
                    const n = o.value;
                    return e.tmNodes.some((e => {
                        var o;
                        if (e.isGroup) return null === (o = e.children) || void 0 === o ? void 0 : o.some((({
                            rawNode: e
                        }) => n ? n(e) : e.icon));
                        const {
                            rawNode: r
                        } = e;
                        return n ? n(r) : r.icon
                    }))
                })),
                hasSubmenuRef: g((() => {
                    const {
                        value: o
                    } = n;
                    return e.tmNodes.some((e => {
                        var n;
                        if (e.isGroup) return null === (n = e.children) || void 0 === n ? void 0 : n.some((({
                            rawNode: e
                        }) => ne(e, o)));
                        const {
                            rawNode: r
                        } = e;
                        return ne(r, o)
                    }))
                }))
            });
            const r = c(null);
            return k(T, null), k(L, null), k(_, r), {
                bodyRef: r
            }
        },
        render() {
            const {
                parentKey: e,
                clsPrefix: o,
                scrollable: n
            } = this, t = this.tmNodes.map((r => {
                const {
                    rawNode: t
                } = r;
                return !1 === t.show ? null : function(e) {
                    return "render" === e.type
                }(t) ? v(de, {
                    tmNode: r,
                    key: r.key
                }) : re(t) ? v(ee, {
                    clsPrefix: o,
                    key: r.key
                }) : function(e) {
                    return "group" === e.type
                }(t) ? v(ie, {
                    clsPrefix: o,
                    tmNode: r,
                    parentKey: e,
                    key: r.key
                }) : v(te, {
                    clsPrefix: o,
                    tmNode: r,
                    parentKey: e,
                    key: r.key,
                    props: t.props,
                    scrollable: n
                })
            }));
            return v("div", {
                class: [`${o}-dropdown-menu`, n && `${o}-dropdown-menu--scrollable`],
                ref: "bodyRef"
            }, n ? v(E, {
                contentClass: `${o}-dropdown-menu__content`
            }, {
                default: () => t
            }) : t, this.showArrow ? r({
                clsPrefix: o,
                arrowStyle: this.arrowStyle,
                arrowClass: void 0,
                arrowWrapperClass: void 0,
                arrowWrapperStyle: void 0
            }) : null)
        }
    }),
    ae = P("dropdown-menu", "\n transform-origin: var(--v-transform-origin);\n background-color: var(--n-color);\n border-radius: var(--n-border-radius);\n box-shadow: var(--n-box-shadow);\n position: relative;\n transition:\n background-color .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier);\n", [H(), P("dropdown-option", "\n position: relative;\n ", [R("a", "\n text-decoration: none;\n color: inherit;\n outline: none;\n ", [R("&::before", '\n content: "";\n position: absolute;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n ')]), P("dropdown-option-body", "\n display: flex;\n cursor: pointer;\n position: relative;\n height: var(--n-option-height);\n line-height: var(--n-option-height);\n font-size: var(--n-font-size);\n color: var(--n-option-text-color);\n transition: color .3s var(--n-bezier);\n ", [R("&::before", '\n content: "";\n position: absolute;\n top: 0;\n bottom: 0;\n left: 4px;\n right: 4px;\n transition: background-color .3s var(--n-bezier);\n border-radius: var(--n-border-radius);\n '), C("disabled", [O("pending", "\n color: var(--n-option-text-color-hover);\n ", [$("prefix, suffix", "\n color: var(--n-option-text-color-hover);\n "), R("&::before", "background-color: var(--n-option-color-hover);")]), O("active", "\n color: var(--n-option-text-color-active);\n ", [$("prefix, suffix", "\n color: var(--n-option-text-color-active);\n "), R("&::before", "background-color: var(--n-option-color-active);")]), O("child-active", "\n color: var(--n-option-text-color-child-active);\n ", [$("prefix, suffix", "\n color: var(--n-option-text-color-child-active);\n ")])]), O("disabled", "\n cursor: not-allowed;\n opacity: var(--n-option-opacity-disabled);\n "), O("group", "\n font-size: calc(var(--n-font-size) - 1px);\n color: var(--n-group-header-text-color);\n ", [$("prefix", "\n width: calc(var(--n-option-prefix-width) / 2);\n ", [O("show-icon", "\n width: calc(var(--n-option-icon-prefix-width) / 2);\n ")])]), $("prefix", "\n width: var(--n-option-prefix-width);\n display: flex;\n justify-content: center;\n align-items: center;\n color: var(--n-prefix-color);\n transition: color .3s var(--n-bezier);\n z-index: 1;\n ", [O("show-icon", "\n width: var(--n-option-icon-prefix-width);\n "), P("icon", "\n font-size: var(--n-option-icon-size);\n ")]), $("label", "\n white-space: nowrap;\n flex: 1;\n z-index: 1;\n "), $("suffix", "\n box-sizing: border-box;\n flex-grow: 0;\n flex-shrink: 0;\n display: flex;\n justify-content: flex-end;\n align-items: center;\n min-width: var(--n-option-suffix-width);\n padding: 0 8px;\n transition: color .3s var(--n-bezier);\n color: var(--n-suffix-color);\n z-index: 1;\n ", [O("has-submenu", "\n width: var(--n-option-icon-suffix-width);\n "), P("icon", "\n font-size: var(--n-option-icon-size);\n ")]), P("dropdown-menu", "pointer-events: all;")]), P("dropdown-offset-container", "\n pointer-events: none;\n position: absolute;\n left: 0;\n right: 0;\n top: -4px;\n bottom: -4px;\n ")]), P("dropdown-divider", "\n transition: background-color .3s var(--n-bezier);\n background-color: var(--n-divider-color);\n height: 1px;\n margin: 4px 0;\n "), P("dropdown-menu-wrapper", "\n transform-origin: var(--v-transform-origin);\n width: fit-content;\n "), R(">", [P("scrollbar", "\n height: inherit;\n max-height: inherit;\n ")]), C("scrollable", "\n padding: var(--n-padding);\n "), O("scrollable", [$("content", "\n padding: var(--n-padding);\n ")])]),
    se = {
        animated: {
            type: Boolean,
            default: !0
        },
        keyboard: {
            type: Boolean,
            default: !0
        },
        size: {
            type: String,
            default: "medium"
        },
        inverted: Boolean,
        placement: {
            type: String,
            default: "bottom"
        },
        onSelect: [Function, Array],
        options: {
            type: Array,
            default: () => []
        },
        menuProps: Function,
        showArrow: Boolean,
        renderLabel: Function,
        renderIcon: Function,
        renderOption: Function,
        nodeProps: Function,
        labelField: {
            type: String,
            default: "label"
        },
        keyField: {
            type: String,
            default: "key"
        },
        childrenField: {
            type: String,
            default: "children"
        },
        value: [String, Number]
    },
    pe = Object.keys(i),
    ue = f({
        name: "Dropdown",
        inheritAttrs: !1,
        props: Object.assign(Object.assign(Object.assign({}, i), se), A.props),
        setup(e) {
            const o = c(!1),
                n = U(I(e, "show"), o),
                r = g((() => {
                    const {
                        keyField: o,
                        childrenField: n
                    } = e;
                    return B(e.options, {
                        getKey: e => e[o],
                        getDisabled: e => !0 === e.disabled,
                        getIgnored: e => "divider" === e.type || "render" === e.type,
                        getChildren: e => e[n]
                    })
                })),
                t = g((() => r.value.treeNodes)),
                i = c(null),
                f = c(null),
                v = c(null),
                h = g((() => {
                    var e, o, n;
                    return null !== (n = null !== (o = null !== (e = i.value) && void 0 !== e ? e : f.value) && void 0 !== o ? o : v.value) && void 0 !== n ? n : null
                })),
                m = g((() => r.value.getPath(h.value).keyPath)),
                b = g((() => r.value.getPath(e.value).keyPath));
            ! function(e = {}, o) {
                const n = d({
                        ctrl: !1,
                        command: !1,
                        win: !1,
                        shift: !1,
                        tab: !1
                    }),
                    {
                        keydown: r,
                        keyup: t
                    } = e,
                    i = e => {
                        switch (e.key) {
                            case "Control":
                                n.ctrl = !0;
                                break;
                            case "Meta":
                                n.command = !0, n.win = !0;
                                break;
                            case "Shift":
                                n.shift = !0;
                                break;
                            case "Tab":
                                n.tab = !0
                        }
                        void 0 !== r && Object.keys(r).forEach((o => {
                            if (o !== e.key) return;
                            const n = r[o];
                            if ("function" == typeof n) n(e);
                            else {
                                const {
                                    stop: o = !1,
                                    prevent: r = !1
                                } = n;
                                o && e.stopPropagation(), r && e.preventDefault(), n.handler(e)
                            }
                        }))
                    },
                    c = e => {
                        switch (e.key) {
                            case "Control":
                                n.ctrl = !1;
                                break;
                            case "Meta":
                                n.command = !1, n.win = !1;
                                break;
                            case "Shift":
                                n.shift = !1;
                                break;
                            case "Tab":
                                n.tab = !1
                        }
                        void 0 !== t && Object.keys(t).forEach((o => {
                            if (o !== e.key) return;
                            const n = t[o];
                            if ("function" == typeof n) n(e);
                            else {
                                const {
                                    stop: o = !1,
                                    prevent: r = !1
                                } = n;
                                o && e.stopPropagation(), r && e.preventDefault(), n.handler(e)
                            }
                        }))
                    },
                    f = () => {
                        (void 0 === o || o.value) && (W("keydown", document, i), W("keyup", document, c)), void 0 !== o && a(o, (e => {
                            e ? (W("keydown", document, i), W("keyup", document, c)) : (G("keydown", document, i), G("keyup", document, c))
                        }))
                    };
                s() ? (p(f), u((() => {
                    (void 0 === o || o.value) && (G("keydown", document, i), G("keyup", document, c))
                }))) : f(), l(n)
            }({
                keydown: {
                    ArrowUp: {
                        prevent: !0,
                        handler: function() {
                            O("up")
                        }
                    },
                    ArrowRight: {
                        prevent: !0,
                        handler: function() {
                            O("right")
                        }
                    },
                    ArrowDown: {
                        prevent: !0,
                        handler: function() {
                            O("down")
                        }
                    },
                    ArrowLeft: {
                        prevent: !0,
                        handler: function() {
                            O("left")
                        }
                    },
                    Enter: {
                        prevent: !0,
                        handler: function() {
                            const e = C();
                            (null == e ? void 0 : e.isLeaf) && n.value && (N(e.key, e.rawNode), P(!1))
                        }
                    },
                    Escape: function() {
                        P(!1)
                    }
                }
            }, x((() => e.keyboard && n.value)));
            const {
                mergedClsPrefixRef: w,
                inlineThemeDisabled: y
            } = j(e), S = A("Dropdown", "-dropdown", ae, z, e, w);

            function N(o, n) {
                const {
                    onSelect: r
                } = e;
                r && V(r, o, n)
            }

            function P(n) {
                const {
                    "onUpdate:show": r,
                    onUpdateShow: t
                } = e;
                r && V(r, n), t && V(t, n), o.value = n
            }

            function R() {
                i.value = null, f.value = null, v.value = null
            }

            function C() {
                var e;
                const {
                    value: o
                } = r, {
                    value: n
                } = h;
                return o && null !== n && null !== (e = o.getNode(n)) && void 0 !== e ? e : null
            }

            function O(e) {
                const {
                    value: o
                } = h, {
                    value: {
                        getFirstAvailableNode: n
                    }
                } = r;
                let t = null;
                if (null === o) {
                    const e = n();
                    null !== e && (t = e.key)
                } else {
                    const o = C();
                    if (o) {
                        let n;
                        switch (e) {
                            case "down":
                                n = o.getNext();
                                break;
                            case "up":
                                n = o.getPrev();
                                break;
                            case "right":
                                n = o.getChild();
                                break;
                            case "left":
                                n = o.getParent()
                        }
                        n && (t = n.key)
                    }
                }
                null !== t && (i.value = null, f.value = t)
            }
            k(Y, {
                labelFieldRef: I(e, "labelField"),
                childrenFieldRef: I(e, "childrenField"),
                renderLabelRef: I(e, "renderLabel"),
                renderIconRef: I(e, "renderIcon"),
                hoverKeyRef: i,
                keyboardKeyRef: f,
                lastToggledSubmenuKeyRef: v,
                pendingKeyPathRef: m,
                activeKeyPathRef: b,
                animatedRef: I(e, "animated"),
                mergedShowRef: n,
                nodePropsRef: I(e, "nodeProps"),
                renderOptionRef: I(e, "renderOption"),
                menuPropsRef: I(e, "menuProps"),
                doSelect: N,
                doUpdateShow: P
            }), a(n, (o => {
                e.animated || o || R()
            }));
            const $ = g((() => {
                    const {
                        size: o,
                        inverted: n
                    } = e, {
                        common: {
                            cubicBezierEaseInOut: r
                        },
                        self: t
                    } = S.value, {
                        padding: i,
                        dividerColor: d,
                        borderRadius: l,
                        optionOpacityDisabled: a,
                        [F("optionIconSuffixWidth", o)]: s,
                        [F("optionSuffixWidth", o)]: p,
                        [F("optionIconPrefixWidth", o)]: u,
                        [F("optionPrefixWidth", o)]: c,
                        [F("fontSize", o)]: f,
                        [F("optionHeight", o)]: v,
                        [F("optionIconSize", o)]: h
                    } = t, m = {
                        "--n-bezier": r,
                        "--n-font-size": f,
                        "--n-padding": i,
                        "--n-border-radius": l,
                        "--n-option-height": v,
                        "--n-option-prefix-width": c,
                        "--n-option-icon-prefix-width": u,
                        "--n-option-suffix-width": p,
                        "--n-option-icon-suffix-width": s,
                        "--n-option-icon-size": h,
                        "--n-divider-color": d,
                        "--n-option-opacity-disabled": a
                    };
                    return n ? (m["--n-color"] = t.colorInverted, m["--n-option-color-hover"] = t.optionColorHoverInverted, m["--n-option-color-active"] = t.optionColorActiveInverted, m["--n-option-text-color"] = t.optionTextColorInverted, m["--n-option-text-color-hover"] = t.optionTextColorHoverInverted, m["--n-option-text-color-active"] = t.optionTextColorActiveInverted, m["--n-option-text-color-child-active"] = t.optionTextColorChildActiveInverted, m["--n-prefix-color"] = t.prefixColorInverted, m["--n-suffix-color"] = t.suffixColorInverted, m["--n-group-header-text-color"] = t.groupHeaderTextColorInverted) : (m["--n-color"] = t.color, m["--n-option-color-hover"] = t.optionColorHover, m["--n-option-color-active"] = t.optionColorActive, m["--n-option-text-color"] = t.optionTextColor, m["--n-option-text-color-hover"] = t.optionTextColorHover, m["--n-option-text-color-active"] = t.optionTextColorActive, m["--n-option-text-color-child-active"] = t.optionTextColorChildActive, m["--n-prefix-color"] = t.prefixColor, m["--n-suffix-color"] = t.suffixColor, m["--n-group-header-text-color"] = t.groupHeaderTextColor), m
                })),
                _ = y ? K("dropdown", g((() => `${e.size[0]}${e.inverted?"i":""}`)), $, e) : void 0;
            return {
                mergedClsPrefix: w,
                mergedTheme: S,
                tmNodes: t,
                mergedShow: n,
                handleAfterLeave: () => {
                    e.animated && R()
                },
                doUpdateShow: P,
                cssVars: y ? void 0 : $,
                themeClass: null == _ ? void 0 : _.themeClass,
                onRender: null == _ ? void 0 : _.onRender
            }
        },
        render() {
            const {
                mergedTheme: e
            } = this, o = {
                show: this.mergedShow,
                theme: e.peers.Popover,
                themeOverrides: e.peerOverrides.Popover,
                internalOnAfterLeave: this.handleAfterLeave,
                internalRenderBody: (e, o, n, r, t) => {
                    var i;
                    const {
                        mergedClsPrefix: d,
                        menuProps: l
                    } = this;
                    null === (i = this.onRender) || void 0 === i || i.call(this);
                    const a = (null == l ? void 0 : l(void 0, this.tmNodes.map((e => e.rawNode)))) || {},
                        s = {
                            ref: X(o),
                            class: [e, `${d}-dropdown`, this.themeClass],
                            clsPrefix: d,
                            tmNodes: this.tmNodes,
                            style: [...n, this.cssVars],
                            showArrow: this.showArrow,
                            arrowStyle: this.arrowStyle,
                            scrollable: this.scrollable,
                            onMouseenter: r,
                            onMouseleave: t
                        };
                    return v(le, w(this.$attrs, s, a))
                },
                onUpdateShow: this.doUpdateShow,
                "onUpdate:show": void 0
            };
            return v(t, Object.assign({}, q(this.$props, pe), o), {
                trigger: () => {
                    var e, o;
                    return null === (o = (e = this.$slots).default) || void 0 === o ? void 0 : o.call(e)
                }
            })
        }
    });
export {
    Z as C, ue as N, X as c
};