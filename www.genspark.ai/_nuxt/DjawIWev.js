import {
    _ as e,
    s as a,
    r as o,
    P as t,
    i as s,
    d as n,
    b as l,
    e as i,
    t as c,
    n as r,
    C as u,
    E as d,
    o as p,
    S as g,
    c as m,
    h,
    A as v,
    a as w,
    f as k,
    w as y,
    a4 as C,
    F as f,
    g as _,
    p as S,
    T as b,
    H as U,
    L as I
} from "./Cf0SOiw0.js";
import {
    N as L
} from "./C38RzRfR.js";
import {
    _ as M
} from "./CAmLbDGM.js";
import {
    P as D,
    R as T
} from "./Cu_n4xpI.js";
import {
    S as R
} from "./W5AxVKvJ.js";
import {
    E as P
} from "./DWCxnUK7.js";
import {
    i as N
} from "./P7LDOl0u.js";
import {
    C as $
} from "./Bm_HbXT2.js";
import {
    N as A
} from "./COYh4g7i.js";
import {
    N as j
} from "./CaEkZ53E.js";
import {
    N as F
} from "./CW991W2w.js";
import {
    N as O
} from "./BjWUbj3w.js";
const B = {
        class: "content"
    },
    x = {
        class: "label"
    },
    E = {
        class: "news"
    },
    G = {
        class: "tip"
    };
const H = e({
        props: {
            styleClass: {
                type: String,
                default: "index"
            },
            copilotType: {
                type: String,
                default: "COPILOT_SPARK"
            }
        },
        emits: ["selected"],
        components: {
            NSelect: L
        },
        setup(e, {
            emit: n
        }) {
            const {
                t: l,
                locale: i,
                locales: c
            } = a(), r = o(e.styleClass), d = o(e.copilotType), p = s("currentUser"), {
                $i18n: g
            } = t(), m = o(i.value), h = s("jsBridge"), v = [...c.value.map((e => ({
                value: e.code,
                label: e.name
            })))];
            return {
                t: l,
                styleClass: r,
                copilotType: d,
                currentUser: p,
                currentLocale: m,
                locales: c,
                language_options: v,
                changeLocale: async (e, a) => {
                    const o = e;
                    await g.setLocale(o), m.value = o, n("selected", o), h.value && u.isGensparkAppAndroid() && h.value.callHandler("setAppLocale", {
                        locale: o
                    }, (() => {}))
                }
            }
        }
    }, [
        ["render", function(e, a, o, t, s, u) {
            const g = d("NSelect");
            return p(), n("div", {
                class: r(["languageGuide", {
                    index: "index" == t.styleClass,
                    spark: "spark" == t.styleClass,
                    theme_images: "COPILOT_IMAGES" == t.copilotType,
                    theme_spark: "COPILOT_SPARK" == t.copilotType,
                    theme_products: "COPILOT_PRODUCTS" == t.copilotType || "COPILOT_PRODUCTS_SPARK" == t.copilotType,
                    theme_travel: "COPILOT_TRAVEL" == t.copilotType || "COPILOT_SPARK_TRAVEL" == t.copilotType
                }])
            }, [a[1] || (a[1] = l("div", {
                class: "arrow-wrapper"
            }, [l("div", {
                class: "arrow"
            })], -1)), l("div", B, [l("div", x, [l("span", E, c(e.$t("components.language-guidance.new")), 1), l("span", G, c(e.$t("components.language-guidance.explore-genspark-in-more-languages")), 1)]), i(g, {
                options: t.language_options,
                "onUpdate:value": [t.changeLocale, a[0] || (a[0] = e => t.currentLocale = e)],
                value: t.currentLocale
            }, null, 8, ["options", "onUpdate:value", "value"])])], 2)
        }],
        ["__scopeId", "data-v-e918bb1f"]
    ]),
    z = {
        width: "12",
        height: "12",
        viewBox: "0 0 12 12",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const V = {
        name: "Menu",
        components: {
            FeedbackDialog: M,
            PersonalizationDialog: D,
            NPopover: F,
            SettingIcon: R,
            CloseIcon: $,
            NDropdown: j,
            NModal: O,
            NSwitch: A,
            NSelect: L,
            EditIcon: P,
            GoToIcon: {
                render: function(e, a) {
                    return p(), n("svg", z, a[0] || (a[0] = [l("path", {
                        d: "M6.00016 11.8307C2.77841 11.8307 0.166829 9.21915 0.166829 5.9974C0.166829 2.77565 2.77841 0.164062 6.00016 0.164062C9.22191 0.164062 11.8335 2.77565 11.8335 5.9974C11.8335 9.21915 9.22191 11.8307 6.00016 11.8307Z",
                        fill: "#765DFF"
                    }, null, -1), l("path", {
                        d: "M5.91646 8.40001C5.93479 8.44425 5.96165 8.48444 5.99552 8.5183C6.02937 8.55216 6.06957 8.57903 6.11381 8.59735C6.15805 8.61568 6.20546 8.62512 6.25335 8.62512C6.30124 8.62512 6.34865 8.61568 6.39289 8.59735C6.43713 8.57903 6.47733 8.55216 6.51118 8.5183L8.7716 6.25788C8.80547 6.22403 8.83233 6.18383 8.85066 6.13959C8.86898 6.09535 8.87842 6.04793 8.87842 6.00005C8.87842 5.95216 8.86898 5.90475 8.85066 5.86051C8.83233 5.81627 8.80547 5.77607 8.7716 5.74221L6.51118 3.4818C6.4428 3.41342 6.35006 3.375 6.25335 3.375C6.15664 3.375 6.0639 3.41342 5.99552 3.4818C5.92714 3.55018 5.88872 3.64293 5.88872 3.73963C5.88872 3.83634 5.92714 3.92908 5.99552 3.99747L7.63352 5.63546H3.48252C3.38582 5.63546 3.29309 5.67388 3.22472 5.74225C3.15634 5.81062 3.11793 5.90335 3.11793 6.00005C3.11793 6.09674 3.15634 6.18948 3.22472 6.25785C3.29309 6.32622 3.38582 6.36463 3.48252 6.36463H7.63352L5.99552 8.00263C5.96165 8.03649 5.93479 8.07668 5.91646 8.12092C5.89813 8.16516 5.8887 8.21258 5.8887 8.26047C5.8887 8.30835 5.89813 8.35577 5.91646 8.40001Z",
                        fill: "white"
                    }, null, -1)]))
                }
            },
            LanguageGuidance: H
        },
        props: {
            placement: {
                type: String,
                default: "bottom-end"
            },
            triggerIcon: {
                type: Object
            },
            showMySparkpage: {
                type: Boolean,
                default: !0
            },
            showMyBookmarks: {
                type: Boolean,
                default: !0
            },
            showMyTasks: {
                type: Boolean,
                default: !0
            },
            showPricing: {
                type: Boolean,
                default: !1
            },
            showBrowsingHistory: {
                type: Boolean,
                default: !0
            },
            showUserInfo: {
                type: Boolean,
                default: !0
            },
            showLogout: {
                type: Boolean,
                default: !0
            }
        },
        async setup(e, {
            emit: n,
            slots: l
        }) {
            const {
                placement: i,
                showMySparkpage: c,
                showMyBookmarks: r,
                showMyTasks: d,
                showBrowsingHistory: p,
                showPricing: w,
                showUserInfo: k,
                showLogout: y
            } = g(e), C = e.triggerIcon, f = s("currentUser"), S = v(), b = _(), U = o(!1), I = o(!1), L = o(!1), {
                t: M,
                locale: D,
                locales: T
            } = a(), {
                $i18n: R
            } = t(), P = o(D.value), $ = "language-guidance-showed", A = "setting-showed", j = o(!0), F = o(!1), O = m({
                get: () => !f.value || !f.value.disable_data_retention,
                set: e => {
                    f.value && (f.value.disable_data_retention = !e)
                }
            }), B = o(0), x = [...T.value.map((e => ({
                value: e.code,
                label: e.name
            })))], E = e => fetch("/api/user/update", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(e)
            }).then((e => e.json())).then((e => e)).catch((e => {}));
            h((async () => {
                "/" !== S.path || localStorage.getItem($) ? I.value = !1 : I.value = !0, localStorage.getItem(A) || (L.value = !0)
            }));
            const G = o(!1),
                H = o(!1),
                z = o(!1);
            return {
                showUserInfo: k,
                showLogout: y,
                sendDeleteEmail: async () => {
                    if (confirm(M("components.menu.delete-user-confirm-1"))) {
                        const e = await fetch("/api/user/send_delete_email");
                        0 === (await e.json()).status && alert(M("components.menu.delete-user-email-sent"))
                    }
                },
                utils: u,
                showPricing: w,
                showMySparkpage: c,
                showMyBookmarks: r,
                showMyTasks: d,
                showBrowsingHistory: p,
                placement: i,
                triggerIcon: C,
                newSugUi: j,
                railStyle: ({
                    focused: e,
                    checked: a
                }) => {
                    const o = {};
                    return o.background = a ? "#0F7FFF" : "rgba(189, 189, 189, 0.6)", o
                },
                currentLocale: P,
                locales: T,
                language_options: x,
                changeLocale: async (e, a) => {
                    const o = e;
                    await R.setLocale(o), P.value = o, location.reload()
                },
                changeDataRetention: e => {
                    f.value.disable_data_retention = !e, E({
                        disable_data_retention: f.value.disable_data_retention
                    }).then((e => {}))
                },
                onLangGuidanceSelected: e => {
                    localStorage.setItem($, !0), I.value = !1, P.value = e, location.reload()
                },
                login: () => {
                    location.href = "/api/login?redirect_url=" + encodeURIComponent(location.pathname + location.search)
                },
                showFeedbackDialog: G,
                showSettingDialog: H,
                showSettingDialogFn: () => {
                    H.value = !0, L.value = !1, localStorage.setItem(A, !0)
                },
                showResearchMeModal: F,
                showResearchMeModalFn: () => {
                    F.value = !0
                },
                handleManualInput: () => {
                    z.value = !0
                },
                handleAutoResearch: e => {
                    u.log("zzh handleAutoResearch", e), u.windowopen(`/agents?type=super_agent&prompt=${e}`)
                },
                enableDataRetention: O,
                goto: e => {
                    location.href = e
                },
                clickMenuicon: e => {
                    e.preventDefault(), I.value = !1, localStorage.setItem($, !0), (async () => {
                        if (f.value) try {
                            const e = await fetch("/api/payment/get_credit_balance"),
                                a = await e.json();
                            0 === a.status && (B.value = a.data.balance)
                        } catch (e) {}
                    })()
                },
                showEditName: U,
                showLangGuidance: I,
                showSettingsTip: L,
                editName: () => {
                    U.value = !0
                },
                changeAvatar: () => {
                    N((e => {
                        f.value.avatar = e, E({
                            avatar: e
                        }).then((e => {}))
                    }))
                },
                saveName: () => {
                    U.value = !1, E({
                        name: f.value.name
                    }).then((e => {
                        U.value = !1
                    }))
                },
                my_sparkpage: () => {
                    b.push("/mysparkpage")
                },
                logout: () => {
                    location.href = "/api/logout?redirect_url=" + encodeURIComponent(location.href)
                },
                currentUser: f,
                credit_balance: B,
                showPersonalizationDialog: z,
                showPersonalizationDialogFn: () => {
                    z.value = !0
                }
            }
        }
    },
    K = {
        class: "dropdown"
    },
    Z = {
        class: "user"
    },
    J = ["src"],
    q = {
        class: "name"
    },
    Q = {
        class: "user-email"
    },
    W = {
        key: 1,
        class: "item upgrade"
    },
    X = {
        class: "item credit-left"
    },
    Y = {
        class: "item"
    },
    ee = {
        class: "item"
    },
    ae = {
        class: "item"
    },
    oe = {
        class: "item"
    },
    te = {
        key: 4,
        class: "item"
    },
    se = {
        key: 0,
        class: "item"
    },
    ne = {
        key: 1,
        class: "item"
    },
    le = {
        class: "item"
    },
    ie = {
        class: "item"
    },
    ce = {
        class: "item"
    },
    re = {
        class: "edit_name_dialog"
    },
    ue = {
        class: "close"
    },
    de = {
        class: "title"
    },
    pe = {
        class: "content"
    },
    ge = {
        class: "input"
    },
    me = {
        class: "buttons"
    },
    he = {
        class: "setting_dialog"
    },
    ve = {
        class: "close"
    },
    we = {
        class: "title"
    },
    ke = {
        class: "content"
    },
    ye = {
        class: "setting_item lang bt_hr"
    },
    Ce = {
        class: "label"
    },
    fe = {
        for: "language"
    },
    _e = {
        class: "setting_item"
    },
    Se = {
        class: "label"
    },
    be = {
        for: "ai-data-retention"
    },
    Ue = {
        class: "toggle_switch"
    },
    Ie = {
        class: "data-retention-description"
    },
    Le = {
        key: 0,
        class: "delete-user-link"
    };
const Me = e(V, [
    ["render", function(e, a, o, t, s, u) {
        const g = d("SettingIcon"),
            m = d("EditIcon"),
            h = d("GoToIcon"),
            v = d("NPopover"),
            _ = d("CloseIcon"),
            L = d("n-modal"),
            R = d("NSelect"),
            P = d("NSwitch"),
            N = M,
            $ = D,
            A = T;
        return p(), n(f, null, [t.currentUser || !1 === t.currentUser ? (p(), w(v, {
            key: 0,
            style: {
                padding: "0"
            },
            trigger: "click",
            placement: t.placement,
            "show-arrow": !1
        }, {
            trigger: y((() => [l("div", {
                class: r(["flex items-center justify-center trigger w-[20px] h-[20px] hover:bg-white dark:hover:bg-white/10 rounded-[10px] p-[6px] mx-auto mb-[16px]"]),
                onClick: a[0] || (a[0] = (...e) => t.clickMenuicon && t.clickMenuicon(...e))
            }, [b(e.$slots, "triggerIcon", {}, (() => [i(g)]), !0)])])),
            default: y((() => [l("div", K, [t.currentUser ? (p(), n(f, {
                key: 0
            }, [t.showUserInfo ? (p(), n(f, {
                key: 0
            }, [l("div", Z, [l("div", {
                class: "avatar",
                onClick: a[1] || (a[1] = (...e) => t.changeAvatar && t.changeAvatar(...e))
            }, [l("img", {
                src: t.currentUser.avatar
            }, null, 8, J)]), l("div", q, c(t.currentUser.name), 1), l("div", {
                class: "edit-icon",
                onClick: a[2] || (a[2] = (...e) => t.editName && t.editName(...e))
            }, [i(m)])]), l("div", Q, c(t.currentUser.email), 1), a[29] || (a[29] = l("div", {
                class: "divider"
            }, null, -1))], 64)) : k("", !0), t.showPricing && !t.utils.isGensparkAppIos() ? (p(), n("div", W, [l("div", {
                onClick: a[3] || (a[3] = e => t.goto("/pricing")),
                class: "upgrade-item"
            }, [S(c(!t.currentUser || "plus" != t.currentUser.plan && "pro" != t.currentUser.plan ? e.$t("components.menu.pricing") : e.$t("components.menu.view-plan")) + " ", 1), i(h, {
                class: "go-to-icon"
            })])])) : k("", !0), l("div", X, [l("div", {
                onClick: a[4] || (a[4] = e => t.goto("/usage")),
                class: "credit-left-item"
            }, [l("div", null, c(e.$t("components.menu.credits")), 1), l("div", null, c(t.credit_balance), 1)])]), a[30] || (a[30] = l("div", {
                class: "divider"
            }, null, -1)), k("", !0), k("", !0), l("div", Y, [l("div", {
                onClick: a[7] || (a[7] = e => t.goto("/me"))
            }, c(e.$t("components.menu.history")), 1)]), l("div", ee, [l("div", {
                onClick: a[8] || (a[8] = e => t.goto("/me?activeTab=bookmarks"))
            }, c(e.$t("pages.spark.bookmark")), 1)]), a[31] || (a[31] = l("div", {
                class: "divider"
            }, null, -1)), l("div", ae, [l("div", {
                onClick: a[9] || (a[9] = e => t.showFeedbackDialog = !0)
            }, c(e.$t("components.menu.support")), 1)]), l("div", oe, [l("div", {
                onClick: a[10] || (a[10] = (...e) => t.showSettingDialogFn && t.showSettingDialogFn(...e))
            }, c(e.$t("components.menu.settings")), 1)]), t.showLogout ? (p(), n("div", te, [l("div", {
                onClick: a[11] || (a[11] = (...e) => t.logout && t.logout(...e))
            }, c(e.$t("components.menu.sign-out")), 1)])) : k("", !0)], 64)) : !1 === t.currentUser ? (p(), n(f, {
                key: 1
            }, [t.showMySparkpage ? (p(), n("div", se, [l("div", {
                onClick: a[12] || (a[12] = (...e) => t.login && t.login(...e))
            }, c(e.$t("components.menu.my-sparkpages")), 1)])) : k("", !0), t.showMyBookmarks ? (p(), n("div", ne, [l("div", {
                onClick: a[13] || (a[13] = (...e) => t.login && t.login(...e))
            }, c(e.$t("components.menu.my-bookmarks")), 1)])) : k("", !0), l("div", le, [l("div", {
                onClick: a[14] || (a[14] = (...e) => t.login && t.login(...e))
            }, c(e.$t("components.menu.support")), 1)]), l("div", ie, [l("div", {
                onClick: a[15] || (a[15] = (...e) => t.login && t.login(...e))
            }, c(e.$t("components.menu.settings")), 1)]), l("div", ce, [l("div", {
                onClick: a[16] || (a[16] = (...e) => t.login && t.login(...e))
            }, c(e.$t("components.menu.sign-in")), 1)])], 64)) : k("", !0)])])),
            _: 3
        }, 8, ["placement"])) : k("", !0), t.currentUser ? (p(), w(L, {
            key: 1,
            show: t.showEditName,
            "onUpdate:show": a[20] || (a[20] = e => t.showEditName = e)
        }, {
            default: y((() => [l("div", re, [l("div", ue, [l("div", {
                class: "icon",
                onClick: a[17] || (a[17] = e => t.showEditName = !1)
            }, [i(_)])]), l("div", de, c(e.$t("components.menu.change-name")), 1), l("div", pe, [l("div", ge, [U(l("input", {
                type: "text",
                "onUpdate:modelValue": a[18] || (a[18] = e => t.currentUser.name = e)
            }, null, 512), [
                [I, t.currentUser.name]
            ])])]), l("div", me, [l("div", {
                class: "button",
                onClick: a[19] || (a[19] = (...e) => t.saveName && t.saveName(...e))
            }, c(e.$t("components.menu.save")), 1)])])])),
            _: 1
        }, 8, ["show"])) : k("", !0), t.currentUser ? (p(), w(L, {
            key: 2,
            show: t.showSettingDialog,
            "onUpdate:show": a[25] || (a[25] = e => t.showSettingDialog = e)
        }, {
            default: y((() => [l("div", he, [l("div", ve, [l("div", {
                class: "icon",
                onClick: a[21] || (a[21] = e => t.showSettingDialog = !1)
            }, [i(_)])]), l("div", we, c(e.$t("components.menu.settings")), 1), l("div", ke, [l("div", ye, [l("div", Ce, [l("label", fe, c(e.$t("components.menu.display-language")), 1)]), i(R, {
                options: t.language_options,
                "onUpdate:value": [t.changeLocale, a[22] || (a[22] = e => t.currentLocale = e)],
                value: t.currentLocale
            }, null, 8, ["options", "onUpdate:value", "value"])]), l("div", _e, [l("div", Se, [l("label", be, c(e.$t("components.menu.ai-data-retention")), 1)]), l("div", Ue, [i(P, {
                value: t.enableDataRetention,
                "onUpdate:value": [a[23] || (a[23] = e => t.enableDataRetention = e), t.changeDataRetention],
                "rail-style": t.railStyle
            }, null, 8, ["value", "rail-style", "onUpdate:value"])])]), l("p", Ie, c(e.$t("components.menu.ai-data-retention-allows-v2")), 1), t.currentUser ? (p(), n("p", Le, [l("a", {
                href: "javascript:;",
                onClick: a[24] || (a[24] = (...e) => t.sendDeleteEmail && t.sendDeleteEmail(...e))
            }, c(e.$t("components.menu.delete-user")), 1)])) : k("", !0)])])])),
            _: 1
        }, 8, ["show"])) : k("", !0), i(C, {
            name: "fade-shrink"
        }, {
            default: y((() => [k("", !0)])),
            _: 1
        }), i(N, {
            show: t.showFeedbackDialog,
            "onUpdate:show": a[26] || (a[26] = e => t.showFeedbackDialog = e)
        }, null, 8, ["show"]), i($, {
            show: t.showPersonalizationDialog,
            "onUpdate:show": a[27] || (a[27] = e => t.showPersonalizationDialog = e),
            onAutoResearch: t.showResearchMeModalFn
        }, null, 8, ["show", "onAutoResearch"]), i(A, {
            modelValue: t.showResearchMeModal,
            "onUpdate:modelValue": a[28] || (a[28] = e => t.showResearchMeModal = e),
            onManualInput: t.handleManualInput,
            onAutoResearch: t.handleAutoResearch
        }, null, 8, ["modelValue", "onManualInput", "onAutoResearch"])], 64)
    }],
    ["__scopeId", "data-v-988553a4"]
]);
export {
    Me as _
};