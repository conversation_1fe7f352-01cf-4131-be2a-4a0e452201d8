.icon[data-v-0beb4848] {
    display: flex;
    height: 20px;
    width: 20px
}

.head .icon[data-v-0beb4848] {
    height: 40px;
    width: 40px;
    --bg-color: #000;
    --fg-color: #fff
}

.theme_travel .head .icon[data-v-0beb4848] {
    --bg-color: #5cd4a1
}

.theme_travel .button.blue[data-v-0beb4848] {
    background: #5cd4a1
}

.theme_products .head .icon[data-v-0beb4848] {
    --bg-color: #73a0f8
}

.theme_products .button.blue[data-v-0beb4848] {
    background: #73a0f8
}

.theme_images .head .icon[data-v-0beb4848] {
    --bg-color: #8b62e2
}

.theme_images .button.blue[data-v-0beb4848] {
    background: #8b62e2
}

.index .desc[data-v-0beb4848] {
    color: #232425;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    text-align: center
}

.close[data-v-0beb4848] {
    display: none
}

.index .close[data-v-0beb4848] {
    display: flex;
    height: 20px;
    position: absolute;
    right: 15px;
    top: 15px;
    width: 20px
}

.button[data-v-0beb4848] {
    align-items: center;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    gap: 10px;
    height: 40px;
    justify-content: center;
    line-height: 1.5;
    padding: 6px 16px
}

.button.blue .icon[data-v-0beb4848] {
    height: 10px;
    width: 10px
}

.button.blue[data-v-0beb4848] {
    background: #0f7fff;
    color: #fff
}

.button .bold[data-v-0beb4848],
.button.signin[data-v-0beb4848] {
    font-weight: 700
}

.button.signin[data-v-0beb4848],
.button.third[data-v-0beb4848] {
    background: #f5f5f5;
    color: #232425;
    font-size: 14px;
    font-style: normal;
    line-height: normal;
    text-align: center
}

.button.third[data-v-0beb4848] {
    font-weight: 400
}

.button.third .icons[data-v-0beb4848] {
    display: flex;
    flex-direction: row;
    gap: 0
}

.index .buttons[data-v-0beb4848] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.index.loginGuide[data-v-0beb4848] {
    border: 1px solid #efefef;
    border-radius: 16px;
    bottom: 50px;
    filter: drop-shadow(0 0 15px rgba(97, 116, 136, .15));
    right: 50px;
    width: 385px
}

.index .buttons[data-v-0beb4848],
.index .desc[data-v-0beb4848] {
    margin-top: 24px
}

.loginGuide[data-v-0beb4848] {
    align-items: center;
    background-color: #fff;
    bottom: 0;
    box-sizing: border-box;
    display: flex;
    filter: drop-shadow(0 0 15px rgba(97, 116, 136, .15));
    flex-direction: column;
    justify-content: flex-start;
    padding: 30px 24px;
    position: fixed;
    width: 100%;
    z-index: 100
}

.desc[data-v-0beb4848] {
    color: #232425;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    max-width: 500px;
    text-align: center
}

.buttons[data-v-0beb4848],
.desc[data-v-0beb4848] {
    margin-top: 30px
}

.buttons[data-v-0beb4848] {
    display: flex;
    flex-direction: row;
    gap: 16px
}

.loginGuide .close[data-v-0beb4848] {
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 10px
}

.loginGuide .head[data-v-0beb4848] {
    align-items: center;
    background-color: #fff;
    border-radius: 10px;
    display: flex;
    justify-content: center
}

@media (max-width:768px) {
    .index.loginGuide[data-v-0beb4848] {
        bottom: 100px;
        left: 8px;
        right: unset;
        width: calc(100% - 16px)
    }
    .loginGuide[data-v-0beb4848] {
        height: 313px
    }
    .loginGuide .desc[data-v-0beb4848] {
        color: #232425;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 24px;
        text-align: center
    }
    .loginGuide .buttons[data-v-0beb4848] {
        flex-direction: column;
        gap: 12px
    }
}

@media (prefers-color-scheme:dark) {
    .loginGuide[data-v-0beb4848] {
        background: #232425
    }
    .index .desc[data-v-0beb4848],
    .loginGuide .desc[data-v-0beb4848] {
        color: #fff
    }
    .head .icon[data-v-0beb4848] {
        --bg-color: #fff;
        --fg-color: #000
    }
}