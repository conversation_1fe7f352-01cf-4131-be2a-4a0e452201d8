import {
    P as e
} from "./CJmWj3ri.js";
import {
    P as i
} from "./9KCDKcmx.js";
import {
    u as t,
    a as r
} from "./0XKHBXCr.js";
import {
    r as n,
    s as a,
    v as s,
    V as o,
    C as l,
    h as p,
    S as c,
    i as u,
    _ as d,
    d as g,
    b as h,
    t as y,
    f as b,
    n as v,
    q as _,
    e as P,
    F as w,
    l as m,
    E as f,
    o as S
} from "./Cf0SOiw0.js";
const k = {
        class: "theme payment_window"
    },
    I = {
        class: "window_title"
    },
    C = {
        class: "window_subtitle"
    },
    M = {
        class: "window_body"
    },
    O = {
        key: 0,
        class: "page_notice_for_apple_paid_membership"
    },
    T = ["innerHTML"],
    $ = {
        key: 1,
        class: "page_notice_for_apple_paid_membership"
    },
    W = {
        key: 2,
        class: "page_notice_for_apple_paid_membership"
    },
    V = {
        key: 3
    },
    D = {
        class: "plan_tab"
    },
    x = {
        class: "relative grid grid-cols-2"
    },
    j = {
        class: "plan_tab_item"
    },
    Y = ["data-state", "aria-checked"],
    F = {
        class: "plan_tab_item"
    },
    A = ["data-state", "aria-checked"],
    B = {
        class: "plan_item_group"
    },
    q = {
        class: "plan_column"
    },
    H = {
        class: "plan_column_upper"
    },
    N = {
        class: "row1"
    },
    L = {
        class: "row2"
    },
    X = {
        key: 0,
        class: "plan_price"
    },
    E = {
        key: 1,
        class: "plan_price"
    },
    U = {
        key: 0,
        class: "plan_price_amount"
    },
    z = {
        key: 1,
        class: "plan_price_amount"
    },
    G = {
        key: 2,
        class: "plan_price_amount_strikethrough"
    },
    J = {
        class: "plan_price_unit"
    },
    R = {
        key: 0,
        class: "row2_plan_sidenote"
    },
    K = {
        key: 1,
        class: "row2_plan_sidenote"
    },
    Q = {
        class: "plan_desc"
    },
    Z = {
        class: "row4"
    },
    ee = {
        key: 0
    },
    ie = ["disabled"],
    te = {
        key: 1
    },
    re = ["value"],
    ne = ["value"],
    ae = ["value"],
    se = ["value"],
    oe = {
        key: 2,
        class: "row4_button",
        disabled: !0
    },
    le = ["disabled"],
    pe = {
        key: 5,
        class: "row4_button"
    },
    ce = {
        class: "plan_column"
    },
    ue = {
        class: "plan_column_upper"
    },
    de = {
        class: "row1"
    },
    ge = {
        class: "row2"
    },
    he = {
        key: 0,
        class: "plan_price"
    },
    ye = {
        key: 1,
        class: "plan_price"
    },
    be = {
        key: 0,
        class: "plan_price_amount"
    },
    ve = {
        key: 1,
        class: "plan_price_amount"
    },
    _e = {
        key: 2,
        class: "plan_price_amount_strikethrough"
    },
    Pe = {
        class: "plan_price_unit"
    },
    we = {
        key: 0,
        class: "row2_plan_sidenote"
    },
    me = {
        key: 1,
        class: "row2_plan_sidenote"
    },
    fe = {
        class: "plan_desc"
    },
    Se = {
        class: "row4"
    },
    ke = {
        key: 0
    },
    Ie = ["disabled"],
    Ce = {
        key: 2,
        class: "auto-renew-notice"
    },
    Me = {
        key: 1
    },
    Oe = ["value"],
    Te = ["value"],
    $e = ["value"],
    We = ["value"],
    Ve = {
        key: 2,
        class: "row4_button",
        disabled: !0
    },
    De = ["disabled"],
    xe = {
        key: 5,
        class: "row4_button"
    },
    je = {
        key: 4,
        class: "debug_info"
    };
const Ye = d({
    name: "PricingWindowForPlusPlan",
    components: {
        PlanPlusDesc: e,
        PlanProDesc: i
    },
    props: {
        windowType: {
            type: String,
            default: "standard",
            validator: e => ["standard", "video", "fashion", "phone_call", "credit_insufficient", "capacity_exhausted"].includes(e)
        }
    },
    data: () => ({
        iOSProductIdPlusMonth: "ai.genspark.vip.plus.month",
        iOSProductIdPlusYear: "ai.genspark.vip.plus.year",
        iOSProductIdProMonth: "ai.genspark.vip.pro.month",
        iOSProductIdProYear: "ai.genspark.vip.pro.year",
        iOSProductIdCredit10k: "ai.genspark.vip.credits.10k",
        iOSProductIdCredit20k: "ai.genspark.vip.credits.20k"
    }),
    methods: {
        getPriceValueForWeb(e) {
            const i = this.priceConfig;
            return e === i.plusPriceMonthOnly ? i.plusPriceMonthOnlyValue : e === i.plusPriceWithYear ? i.plusPriceYearTotalValue : e === i.proPriceMonthOnly ? i.proPriceMonthOnlyValue : e === i.proPriceWithYear ? i.proPriceYearTotalValue : e === i.creditPrice ? i.creditPriceValue : e === i.credit2xPrice ? i.credit2xPriceValue : 0
        },
        getPriceValueForApple(e) {
            const i = this.priceConfig;
            return e === this.iOSProductIdPlusMonth ? i.plusPriceMonthOnlyValue : e === this.iOSProductIdPlusYear ? i.plusPriceYearTotalValue : e === this.iOSProductIdProMonth ? i.proPriceMonthOnlyValue : e === this.iOSProductIdProYear ? i.proPriceYearTotalValue : e === this.iOSProductIdCredit10k ? i.creditPriceValue : e === this.iOSProductIdCredit20k ? i.credit2xPriceValue : 0
        },
        async handleWebPurchase(e) {
            const i = new FormData(e.target),
                t = Object.fromEntries(i); {
                const e = this.getPriceValueForWeb(t.price_id);
                window.gtag && gtag("event", "begin_checkout", {
                    platform: "web",
                    price_id: t.price_id,
                    entry: t.entry,
                    current_path: "pricingWindowForPlusPlan",
                    currency: "USD",
                    value: e
                }), window.fbq && fbq("track", "InitiateCheckout", {
                    content_ids: [t.price_id],
                    value: e
                })
            }
            try {
                const e = await fetch("/api/payment/create-checkout-session-web", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(t)
                });
                if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                const i = await e.json();
                i && 0 === i.status && i.data && i.data.url && (window.location.href = i.data.url)
            } catch (r) {}
        },
        async jumpToCustomerPortal(e) {
            try {
                const i = await fetch("/api/payment/customer-portal", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(e)
                });
                if (i.ok) {
                    const e = await i.json();
                    if (e.url) return void(window.location.href = e.url)
                }
            } catch (i) {}
        },
        getMembershipTypeName: e => "plus" === e ? "Plus" : "pro" === e ? "Pro" : e,
        getAppStoreLink: () => "https://apps.apple.com/app/id6739554054",
        handleInApplePurchase(e) {
            this.isInIOS && this.jsBridge && this.jsBridge.callHandler("iap", {
                product_id: e
            }, (() => {
                {
                    const i = this.getPriceValueForApple(e);
                    window.gtag && gtag("event", "begin_checkout", {
                        platform: "ios",
                        price_id: e,
                        currency: "USD",
                        value: i
                    }), window.fbq && fbq("track", "InitiateCheckout", {
                        content_ids: [e],
                        currency: "USD",
                        value: i
                    })
                }
            }))
        }
    },
    setup(e, {
        emit: i
    }) {
        const d = n(!1),
            g = n(!1),
            h = n(""),
            y = n(!0),
            b = n(!0),
            {
                t: v
            } = a(),
            {
                getPriceConfigObject: _,
                fetchPriceConfig: P,
                parseDataValue: w
            } = t(),
            {
                getCurrentSubscriptionObject: m,
                fetchCurrentSubscription: f,
                parseCurrentSubscriptionData: S
            } = r(),
            k = n(!0),
            I = n(!1),
            C = u("jsBridge"),
            M = n(!1);
        s((() => C.value), (e => {
            e && C.value.callHandler("support", {
                api: "iap"
            }, (e => {
                I.value = e
            }))
        }), {
            immediate: !0
        });
        const O = o();
        "forceshowios" in O.query && "string" == typeof O.query.forceshowios && "true" === O.query.forceshowios && (M.value = !0), "showdebuginfo" in O.query && "string" == typeof O.query.showdebuginfo && "true" === O.query.showdebuginfo ? d.value = !0 : d.value = !1, h.value = O.fullPath;
        const T = n(l.isGensparkAppIos());
        if (T.value && !k.value) return void(location.href = "/pricing");
        const $ = n(m()),
            W = n(_()),
            V = n(null),
            D = n(null);
        p((async () => {
            V.value = await f(O, null), D.value = await P(O, null)
        })), s((() => V.value), (e => {
            e && (S(e, T.value, M.value, g.value, v), b.value = !1)
        })), s((() => D.value), (e => {
            e && (w(e), y.value = !1)
        }));
        const {
            windowType: x
        } = c(e);
        return {
            t: v,
            showDebugInfo: d,
            isMonthly: g,
            togglePlanInterval: () => {
                g.value = !g.value, (T.value || M.value) && ($.value.plusButtonDisabled = "plus" === $.value.currentPaidMembershipType && ("month" === $.value.appleProductInterval && !0 === g.value || "year" === $.value.appleProductInterval && !1 === g.value), $.value.proButtonDisabled = "pro" === $.value.currentPaidMembershipType && ("month" === $.value.appleProductInterval && !0 === g.value || "year" === $.value.appleProductInterval && !1 === g.value))
            },
            currentSubscription: $,
            priceConfig: W,
            currentPath: h,
            isWaitingForPriceConfig: y,
            isWaitingForCurrentSubscription: b,
            emit: i,
            getWindowTitle: () => {
                switch (x.value) {
                    case "video":
                        return v("pages.pricing-window.title-video");
                    case "fashion":
                        return v("pages.pricing-window.title-fashion");
                    case "phone_call":
                        return v("pages.pricing-window.title-phone-call-join-plus");
                    case "credit_insufficient":
                        return v("pages.pricing-window.title-credit-insufficient");
                    case "capacity_exhausted":
                        return v("pages.pricing-window.title-capacity-exhausted");
                    default:
                        return v("pages.pricing-window.title-exhausted")
                }
            },
            getWindowSubtitle: () => {
                switch (x.value) {
                    case "video":
                        return v("pages.pricing-window.subtitle-video");
                    case "fashion":
                        return v("pages.pricing-window.subtitle-fashion");
                    case "phone_call":
                        return v("pages.pricing-window.subtitle-phone_call");
                    default:
                        return v("pages.pricing-window.subtitle")
                }
            },
            jsBridge: C,
            isInIOS: T,
            supportIAP: I,
            enableIOS: k,
            forceShowIOS: M
        }
    }
}, [
    ["render", function(e, i, t, r, n, a) {
        const s = f("PlanPlusDesc"),
            o = f("PlanProDesc");
        return S(), g("div", k, [i[12] || (i[12] = h("div", {
            class: "ellipse-1"
        }, null, -1)), i[13] || (i[13] = h("div", {
            class: "ellipse-2"
        }, null, -1)), h("button", {
            class: "close_button",
            onClick: i[0] || (i[0] = i => e.$emit("close"))
        }, i[8] || (i[8] = [h("svg", {
            class: "close_icon",
            xmlns: "http://www.w3.org/2000/svg",
            width: "24",
            height: "24",
            viewBox: "0 0 24 24",
            fill: "none",
            stroke: "currentColor",
            "stroke-width": "2.5",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }, [h("line", {
            x1: "18",
            y1: "6",
            x2: "6",
            y2: "18"
        }), h("line", {
            x1: "6",
            y1: "6",
            x2: "18",
            y2: "18"
        })], -1)])), h("div", I, [i[9] || (i[9] = h("div", null, null, -1)), h("span", null, y(r.getWindowTitle()), 1)]), h("div", C, [h("span", null, y(r.getWindowSubtitle()), 1)]), h("div", M, [r.isInIOS && !r.supportIAP ? (S(), g("div", O, [h("span", {
            innerHTML: e.$t("pages.pricing.notice_for_ios_app_upgrade", {
                url_html: `<a href='${a.getAppStoreLink()}' target='_blank' style='color: var(--link);'>Genspark</a>`
            })
        }, null, 8, T)])) : r.isInIOS || r.forceShowIOS || !["plus", "pro"].includes(r.currentSubscription.applePaidMembershipType) || "" != r.currentSubscription.inviteCreditStartDate || ["plus", "pro"].includes(r.currentSubscription.webPaidMembershipType) ? !r.isInIOS && !r.forceShowIOS || "" == r.currentSubscription.inviteCreditStartDate && !["plus", "pro"].includes(r.currentSubscription.webPaidMembershipType) ? (S(), g("div", V, [h("div", D, [h("div", {
            "aria-label": "Toggle for switching between Monthly and Annually plans",
            class: "plan_tab_container rounded-full bg-token-main-surface-tertiary",
            style: {
                outline: "none"
            },
            onClick: i[1] || (i[1] = (...e) => r.togglePlanInterval && r.togglePlanInterval(...e))
        }, [h("div", x, [h("div", j, [h("button", {
            type: "button",
            "data-state": r.isMonthly ? "on" : "off",
            role: "radio",
            "aria-checked": r.isMonthly,
            "aria-label": "Toggle for switching to Monthly plans",
            class: v(r.isMonthly ? "text-token-text-primary" : "text-token-text-tertiary"),
            "data-radix-collection-item": ""
        }, y(e.$t("pages.pricing.plan-monthly")), 11, Y), r.isMonthly ? (S(), g("div", {
            key: 0,
            class: "absolute inset-0 -z-10 rounded-full bg-token-main-surface-primary",
            style: _({
                transform: r.isMonthly ? "translateX(0)" : "translateX(100%)",
                transition: "transform 0.2s ease-in-out"
            })
        }, null, 4)) : b("", !0)]), h("div", F, [h("button", {
            type: "button",
            "data-state": r.isMonthly ? "off" : "on",
            role: "radio",
            "aria-checked": !r.isMonthly,
            "aria-label": "Toggle for switching to Annually plans",
            class: v(r.isMonthly ? "text-token-text-tertiary" : "text-token-text-primary"),
            "data-radix-collection-item": ""
        }, y(e.$t("pages.pricing.plan-annually")), 11, A), r.isMonthly ? b("", !0) : (S(), g("div", {
            key: 0,
            class: "absolute inset-0 -z-10 rounded-full bg-token-main-surface-primary",
            style: _({
                transform: r.isMonthly ? "translateX(100%)" : "translateX(0)",
                transition: "transform 0.2s ease-in-out"
            })
        }, null, 4))])])])]), h("div", B, [h("div", q, [h("div", null, [h("div", H, [h("div", N, [h("p", null, y(e.$t("pages.pricing.plus-plan-title")), 1)]), h("div", L, [h("div", null, [r.isWaitingForPriceConfig || r.isWaitingForCurrentSubscription ? (S(), g("div", X, i[10] || (i[10] = [h("div", {
            class: "plan_price_amount"
        }, "loading...", -1)]))) : (S(), g("div", E, [r.isMonthly ? (S(), g("div", U, " $" + y(r.priceConfig.plusPriceMonthOnlyValue), 1)) : (S(), g("div", z, " $" + y(r.priceConfig.plusPriceWithYearValue), 1)), r.isMonthly ? b("", !0) : (S(), g("div", G, " $" + y(r.priceConfig.plusPriceMonthOnlyValue), 1)), h("div", J, y(r.priceConfig.plusPriceCurrency) + " / " + y(r.priceConfig.plusPriceInterval), 1)]))])]), r.isMonthly ? (S(), g("div", K, y(e.$t("pages.pricing.plan-sidenote-monthly")), 1)) : (S(), g("div", R, " $" + y(r.priceConfig.plusPriceYearTotalValue) + "/" + y(e.$t("pages.pricing.price-year")) + ", " + y(e.$t("pages.pricing.plan-sidenote-annually")), 1))])]), h("div", Q, [P(s, {
            "window-type": t.windowType
        }, null, 8, ["window-type"])]), h("div", Z, [r.enableIOS && (r.isInIOS || r.forceShowIOS) ? (S(), g("div", ee, [r.supportIAP || r.forceShowIOS ? (S(), g(w, {
            key: 0
        }, [r.currentSubscription.plusButtonDisabled ? (S(), g("button", {
            key: 0,
            class: "row4_button",
            disabled: r.currentSubscription.plusButtonDisabled
        }, y(e.$t("pages.pricing.your-current-plan")), 9, ie)) : (S(), g("button", {
            key: 1,
            class: "row4_button",
            onClick: i[2] || (i[2] = e => a.handleInApplePurchase(r.isMonthly ? this.iOSProductIdPlusMonth : this.iOSProductIdPlusYear))
        }, y("pro" === r.currentSubscription.currentPaidMembershipType ? r.isMonthly ? e.$t("pages.pricing.switch-to-plus-monthly") : e.$t("pages.pricing.switch-to-plus-yearly") : r.isMonthly ? e.$t("pages.pricing.upgrade-to-plus-monthly") : e.$t("pages.pricing.upgrade-to-plus-yearly")), 1))], 64)) : b("", !0)])) : (S(), g("div", te, [h("form", {
            onSubmit: i[4] || (i[4] = m(((...e) => a.handleWebPurchase && a.handleWebPurchase(...e)), ["prevent"]))
        }, [r.isMonthly ? (S(), g("input", {
            key: 0,
            type: "hidden",
            id: "plus_price",
            name: "price_id",
            value: r.priceConfig.plusPriceMonthOnly
        }, null, 8, re)) : (S(), g("input", {
            key: 1,
            type: "hidden",
            id: "plus_price",
            name: "price_id",
            value: r.priceConfig.plusPriceWithYear
        }, null, 8, ne)), h("input", {
            type: "hidden",
            id: "testmode",
            name: "testmode",
            value: r.priceConfig.testmode
        }, null, 8, ae), h("input", {
            type: "hidden",
            id: "current_path",
            name: "current_path",
            value: r.currentPath
        }, null, 8, se), r.isWaitingForPriceConfig || r.isWaitingForCurrentSubscription ? (S(), g("button", oe, y(e.$t("pages.pricing.upgrade-to-plus")), 1)) : r.currentSubscription.plusButtonDisabled ? (S(), g("button", {
            key: 3,
            class: "row4_button",
            disabled: r.currentSubscription.plusButtonDisabled
        }, y(e.$t("pages.pricing.your-current-plan")), 9, le)) : "pro" === r.currentSubscription.currentPaidMembershipType ? (S(), g("button", {
            key: 4,
            class: "row4_button",
            onClick: i[3] || (i[3] = async () => {
                a.jumpToCustomerPortal({
                    testmode: r.priceConfig.testmode,
                    subscription_id: r.currentSubscription.currentSubscriptionId
                })
            })
        }, y(e.$t("pages.pricing.switch-plan")), 1)) : (S(), g("button", pe, y(e.$t("pages.pricing.upgrade-to-plus")), 1))], 32)]))])]), h("div", ce, [h("div", null, [h("div", ue, [h("div", de, [h("p", null, y(e.$t("pages.pricing.pro-plan-title")), 1)]), h("div", ge, [h("div", null, [r.isWaitingForPriceConfig || r.isWaitingForCurrentSubscription ? (S(), g("div", he, i[11] || (i[11] = [h("div", {
            class: "plan_price_amount"
        }, "loading...", -1)]))) : (S(), g("div", ye, [r.isMonthly ? (S(), g("div", be, " $" + y(r.priceConfig.proPriceMonthOnlyValue), 1)) : (S(), g("div", ve, " $" + y(r.priceConfig.proPriceWithYearValue), 1)), r.isMonthly ? b("", !0) : (S(), g("div", _e, " $" + y(r.priceConfig.proPriceMonthOnlyValue), 1)), h("div", Pe, y(r.priceConfig.proPriceCurrency) + " / " + y(r.priceConfig.proPriceInterval), 1)]))])]), r.isMonthly ? (S(), g("div", me, y(e.$t("pages.pricing.plan-sidenote-monthly")), 1)) : (S(), g("div", we, " $" + y(r.priceConfig.proPriceYearTotalValue) + "/" + y(e.$t("pages.pricing.price-year")) + ", " + y(e.$t("pages.pricing.plan-sidenote-annually")), 1))])]), h("div", fe, [P(o, {
            "window-type": t.windowType
        }, null, 8, ["window-type"])]), h("div", Se, [r.enableIOS && (r.isInIOS || r.forceShowIOS) ? (S(), g("div", ke, [r.supportIAP || r.forceShowIOS ? (S(), g(w, {
            key: 0
        }, [r.currentSubscription.proButtonDisabled ? (S(), g("button", {
            key: 0,
            class: "row4_button",
            disabled: r.currentSubscription.proButtonDisabled
        }, y(e.$t("pages.pricing.your-current-plan")), 9, Ie)) : (S(), g("button", {
            key: 1,
            class: "row4_button",
            onClick: i[5] || (i[5] = e => a.handleInApplePurchase(r.isMonthly ? this.iOSProductIdProMonth : this.iOSProductIdProYear))
        }, y(r.isMonthly ? e.$t("pages.pricing.upgrade-to-pro-monthly") : e.$t("pages.pricing.upgrade-to-pro-yearly")), 1)), r.currentSubscription.proButtonDisabled ? b("", !0) : (S(), g("span", Ce, y(e.$t("pages.pricing.auto-renew-notice")), 1))], 64)) : b("", !0)])) : (S(), g("div", Me, [h("form", {
            onSubmit: i[7] || (i[7] = m(((...e) => a.handleWebPurchase && a.handleWebPurchase(...e)), ["prevent"]))
        }, [r.isMonthly ? (S(), g("input", {
            key: 0,
            type: "hidden",
            id: "pro_price",
            name: "price_id",
            value: r.priceConfig.proPriceMonthOnly
        }, null, 8, Oe)) : (S(), g("input", {
            key: 1,
            type: "hidden",
            id: "pro_price",
            name: "price_id",
            value: r.priceConfig.proPriceWithYear
        }, null, 8, Te)), h("input", {
            type: "hidden",
            id: "testmode",
            name: "testmode",
            value: r.priceConfig.testmode
        }, null, 8, $e), h("input", {
            type: "hidden",
            id: "current_path",
            name: "current_path",
            value: r.currentPath
        }, null, 8, We), r.isWaitingForPriceConfig || r.isWaitingForCurrentSubscription ? (S(), g("button", Ve, y(e.$t("pages.pricing.upgrade-to-pro")), 1)) : r.currentSubscription.proButtonDisabled ? (S(), g("button", {
            key: 3,
            class: "row4_button",
            disabled: r.currentSubscription.proButtonDisabled
        }, y(e.$t("pages.pricing.your-current-plan")), 9, De)) : "plus" === r.currentSubscription.currentPaidMembershipType ? (S(), g("button", {
            key: 4,
            class: "row4_button",
            onClick: i[6] || (i[6] = async () => {
                a.jumpToCustomerPortal({
                    testmode: r.priceConfig.testmode,
                    subscription_id: r.currentSubscription.currentSubscriptionId
                })
            })
        }, y(e.$t("pages.pricing.upgrade-to-pro")), 1)) : (S(), g("button", xe, y(e.$t("pages.pricing.upgrade-to-pro")), 1))], 32)]))])])])])) : (S(), g("div", W, y(e.$t("pages.pricing.notice_for_web_paid_membership", {
            membership_type: ["plus", "pro"].includes(r.currentSubscription.webPaidMembershipType) ? a.getMembershipTypeName(r.currentSubscription.webPaidMembershipType) : "Plus"
        })), 1)) : (S(), g("div", $, y(e.$t("pages.pricing.notice_for_apple_paid_membership", {
            membership_type: a.getMembershipTypeName(r.currentSubscription.applePaidMembershipType)
        })), 1)), r.showDebugInfo ? (S(), g("div", je, [h("span", null, " isInIOS: [ " + y(r.isInIOS) + " ], supportIAP: [ " + y(r.supportIAP) + " ], forceShowIOS: [ " + y(r.forceShowIOS) + " ] ", 1), h("span", null, "payment region: [ " + y(r.priceConfig.paymentRegion) + " ], mode: [ " + y(r.priceConfig.liveOrTestMode) + " ], price_id: [ month=" + y(r.priceConfig.plusPriceMonthOnly) + ", year=" + y(r.priceConfig.plusPriceWithYear) + " ]", 1), h("pre", null, y(r.currentSubscription.debugInfo), 1)])) : b("", !0)])])
    }],
    ["__scopeId", "data-v-940f7cfe"]
]);
export {
    Ye as P
};