.circle[data-v-aecb818b] {
    align-items: center;
    display: flex;
    justify-content: center;
    width: 12px
}

.loading-animation[data-v-aecb818b] {
    display: flex;
    gap: 2px;
    height: 12px
}

@keyframes grow-aecb818b {
    0%,
    to {
        height: 4px;
        opacity: .45;
        width: 4px
    }
    50% {
        height: 12px;
        opacity: 1;
        width: 12px
    }
}

@keyframes shrink-aecb818b {
    0%,
    to {
        height: 12px;
        opacity: 1;
        width: 12px
    }
    50% {
        height: 4px;
        opacity: .45;
        width: 4px
    }
}

.circle-small[data-v-aecb818b] {
    animation: grow-aecb818b 2s infinite
}

.circle-large[data-v-aecb818b],
.circle-small[data-v-aecb818b] {
    background-color: #0f7fff;
    border-radius: 50%
}

.circle-large[data-v-aecb818b] {
    animation: shrink-aecb818b 2s infinite
}