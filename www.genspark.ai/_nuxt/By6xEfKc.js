import {
    J as e,
    X as l,
    b5 as a,
    al as r,
    ak as o,
    aO as s,
    aQ as n,
    aS as t,
    b6 as i,
    b7 as c,
    aR as d,
    an as f
} from "./Cf0SOiw0.js";
import {
    a as h
} from "./B7VeW_-d.js";
const u = e({
        name: "ChevronDown",
        render: () => l("svg", {
            viewBox: "0 0 16 16",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg"
        }, l("path", {
            d: "M3.14645 5.64645C3.34171 5.45118 3.65829 5.45118 3.85355 5.64645L8 9.79289L12.1464 5.64645C12.3417 5.45118 12.6583 5.45118 12.8536 5.64645C13.0488 5.84171 13.0488 6.15829 12.8536 6.35355L8.35355 10.8536C8.15829 11.0488 7.84171 11.0488 7.64645 10.8536L3.14645 6.35355C2.95118 6.15829 2.95118 5.84171 3.14645 5.64645Z",
            fill: "currentColor"
        }))
    }),
    p = a("clear", (() => l("svg", {
        viewBox: "0 0 16 16",
        version: "1.1",
        xmlns: "http://www.w3.org/2000/svg"
    }, l("g", {
        stroke: "none",
        "stroke-width": "1",
        fill: "none",
        "fill-rule": "evenodd"
    }, l("g", {
        fill: "currentColor",
        "fill-rule": "nonzero"
    }, l("path", {
        d: "M8,2 C11.3137085,2 14,4.6862915 14,8 C14,11.3137085 11.3137085,14 8,14 C4.6862915,14 2,11.3137085 2,8 C2,4.6862915 4.6862915,2 8,2 Z M6.5343055,5.83859116 C6.33943736,5.70359511 6.07001296,5.72288026 5.89644661,5.89644661 L5.89644661,5.89644661 L5.83859116,5.9656945 C5.70359511,6.16056264 5.72288026,6.42998704 5.89644661,6.60355339 L5.89644661,6.60355339 L7.293,8 L5.89644661,9.39644661 L5.83859116,9.4656945 C5.70359511,9.66056264 5.72288026,9.92998704 5.89644661,10.1035534 L5.89644661,10.1035534 L5.9656945,10.1614088 C6.16056264,10.2964049 6.42998704,10.2771197 6.60355339,10.1035534 L6.60355339,10.1035534 L8,8.707 L9.39644661,10.1035534 L9.4656945,10.1614088 C9.66056264,10.2964049 9.92998704,10.2771197 10.1035534,10.1035534 L10.1035534,10.1035534 L10.1614088,10.0343055 C10.2964049,9.83943736 10.2771197,9.57001296 10.1035534,9.39644661 L10.1035534,9.39644661 L8.707,8 L10.1035534,6.60355339 L10.1614088,6.5343055 C10.2964049,6.33943736 10.2771197,6.07001296 10.1035534,5.89644661 L10.1035534,5.89644661 L10.0343055,5.83859116 C9.83943736,5.70359511 9.57001296,5.72288026 9.39644661,5.89644661 L9.39644661,5.89644661 L8,7.293 L6.60355339,5.89644661 Z"
    })))))),
    C = r("base-clear", "\n flex-shrink: 0;\n height: 1em;\n width: 1em;\n position: relative;\n", [o(">", [s("clear", "\n font-size: var(--n-clear-size);\n height: 1em;\n width: 1em;\n cursor: pointer;\n color: var(--n-clear-color);\n transition: color .3s var(--n-bezier);\n display: flex;\n ", [o("&:hover", "\n color: var(--n-clear-color-hover)!important;\n "), o("&:active", "\n color: var(--n-clear-color-pressed)!important;\n ")]), s("placeholder", "\n display: flex;\n "), s("clear, placeholder", "\n position: absolute;\n left: 50%;\n top: 50%;\n transform: translateX(-50%) translateY(-50%);\n ", [n({
        originalTransform: "translateX(-50%) translateY(-50%)",
        left: "50%",
        top: "50%"
    })])])]),
    w = e({
        name: "BaseClear",
        props: {
            clsPrefix: {
                type: String,
                required: !0
            },
            show: Boolean,
            onClear: Function
        },
        setup: e => (c("-base-clear", C, d(e, "clsPrefix")), {
            handleMouseDown(e) {
                e.preventDefault()
            }
        }),
        render() {
            const {
                clsPrefix: e
            } = this;
            return l("div", {
                class: `${e}-base-clear`
            }, l(t, null, {
                default: () => {
                    var a, r;
                    return this.show ? l("div", {
                        key: "dismiss",
                        class: `${e}-base-clear__clear`,
                        onClick: this.onClear,
                        onMousedown: this.handleMouseDown,
                        "data-clear": !0
                    }, h(this.$slots.icon, (() => [l(i, {
                        clsPrefix: e
                    }, {
                        default: () => l(p, null)
                    })]))) : l("div", {
                        key: "icon",
                        class: `${e}-base-clear__placeholder`
                    }, null === (r = (a = this.$slots).placeholder) || void 0 === r ? void 0 : r.call(a))
                }
            }))
        }
    }),
    v = e({
        name: "InternalSelectionSuffix",
        props: {
            clsPrefix: {
                type: String,
                required: !0
            },
            showArrow: {
                type: Boolean,
                default: void 0
            },
            showClear: {
                type: Boolean,
                default: void 0
            },
            loading: {
                type: Boolean,
                default: !1
            },
            onClear: Function
        },
        setup: (e, {
            slots: a
        }) => () => {
            const {
                clsPrefix: r
            } = e;
            return l(f, {
                clsPrefix: r,
                class: `${r}-base-suffix`,
                strokeWidth: 24,
                scale: .85,
                show: e.loading
            }, {
                default: () => e.showArrow ? l(w, {
                    clsPrefix: r,
                    show: e.showClear,
                    onClear: e.onClear
                }, {
                    placeholder: () => l(i, {
                        clsPrefix: r,
                        class: `${r}-base-suffix__arrow`
                    }, {
                        default: () => h(a.default, (() => [l(u, null)]))
                    })
                }) : null
            })
        }
    });
export {
    u as C, w as N, v as a
};