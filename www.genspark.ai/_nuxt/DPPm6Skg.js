import {
    P as e
} from "./lJbCVLd4.js";
import {
    P as i
} from "./9KCDKcmx.js";
import {
    a as t,
    u as n
} from "./0XKHBXCr.js";
import {
    r,
    s as a,
    v as s,
    V as o,
    C as l,
    h as c,
    i as p,
    _ as u,
    d,
    b as g,
    t as h,
    f as m,
    n as y,
    q as b,
    e as w,
    F as v,
    l as _,
    E as P,
    o as f
} from "./Cf0SOiw0.js";
const k = {
        class: "theme payment_window"
    },
    C = {
        class: "window_title"
    },
    S = {
        class: "window_subtitle"
    },
    I = {
        class: "window_body"
    },
    M = {
        key: 0,
        class: "page_notice_for_apple_paid_membership"
    },
    O = ["innerHTML"],
    T = {
        key: 1,
        class: "page_notice_for_apple_paid_membership"
    },
    x = {
        key: 2,
        class: "page_notice_for_apple_paid_membership"
    },
    $ = {
        key: 3
    },
    W = {
        class: "plan_item_group"
    },
    V = {
        key: 0,
        class: "plan_column"
    },
    j = {
        class: "plan_column_upper"
    },
    D = {
        class: "row1"
    },
    F = {
        class: "plan_tab"
    },
    A = {
        class: "relative grid grid-cols-2"
    },
    Y = {
        class: "plan_tab_item"
    },
    B = ["data-state", "aria-checked"],
    q = {
        class: "plan_tab_item"
    },
    L = ["data-state", "aria-checked"],
    H = {
        class: "row2"
    },
    R = {
        key: 0,
        class: "plan_price"
    },
    N = {
        key: 1,
        class: "plan_price"
    },
    E = {
        key: 0,
        class: "plan_price_amount"
    },
    X = {
        key: 1,
        class: "plan_price_amount"
    },
    U = {
        key: 2,
        class: "plan_price_amount_strikethrough"
    },
    z = {
        class: "plan_price_unit"
    },
    G = {
        key: 0,
        class: "row2_plan_sidenote"
    },
    J = {
        key: 1,
        class: "row2_plan_sidenote"
    },
    Z = {
        class: "plan_desc"
    },
    K = {
        class: "row4"
    },
    Q = {
        key: 0
    },
    ee = ["disabled"],
    ie = {
        key: 2,
        class: "auto-renew-notice"
    },
    te = {
        key: 1
    },
    ne = ["value"],
    re = ["value"],
    ae = ["value"],
    se = ["value"],
    oe = {
        key: 2,
        class: "row4_button",
        disabled: !0
    },
    le = ["disabled"],
    ce = {
        key: 5,
        class: "row4_button"
    },
    pe = {
        class: "plan_column"
    },
    ue = {
        class: "plan_column_upper"
    },
    de = {
        class: "row1"
    },
    ge = {
        class: "row2"
    },
    he = {
        key: 0,
        class: "plan_price"
    },
    me = {
        key: 1,
        class: "plan_price"
    },
    ye = {
        class: "plan_price_amount"
    },
    be = {
        class: "plan_price_unit"
    },
    we = {
        class: "plan_desc"
    },
    ve = {
        class: "row4"
    },
    _e = {
        key: 0
    },
    Pe = {
        key: 1
    },
    fe = ["value"],
    ke = ["value"],
    Ce = ["value"],
    Se = {
        key: 0,
        class: "row4_button",
        disabled: !0
    },
    Ie = {
        key: 1,
        class: "row4_button"
    },
    Me = {
        key: 4,
        class: "debug_info"
    };
const Oe = u({
        name: "PricingWindowForCreditPack",
        components: {
            PlanCreditDesc: e,
            PlanProDesc: i
        },
        props: {
            windowType: {
                type: String,
                default: "standard",
                validator: e => ["standard", "video", "fashion", "phone_call", "credit_insufficient", "capacity_exhausted"].includes(e)
            }
        },
        data: () => ({
            iOSProductIdPlusMonth: "ai.genspark.vip.plus.month",
            iOSProductIdPlusYear: "ai.genspark.vip.plus.year",
            iOSProductIdProMonth: "ai.genspark.vip.pro.month",
            iOSProductIdProYear: "ai.genspark.vip.pro.year",
            iOSProductIdCredit10k: "ai.genspark.vip.credits.10k",
            iOSProductIdCredit20k: "ai.genspark.vip.credits.20k"
        }),
        methods: {
            getPriceValueForWeb(e) {
                const i = this.priceConfig;
                return e === i.plusPriceMonthOnly ? i.plusPriceMonthOnlyValue : e === i.plusPriceWithYear ? i.plusPriceYearTotalValue : e === i.proPriceMonthOnly ? i.proPriceMonthOnlyValue : e === i.proPriceWithYear ? i.proPriceYearTotalValue : e === i.creditPrice ? i.creditPriceValue : e === i.credit2xPrice ? i.credit2xPriceValue : 0
            },
            getPriceValueForApple(e) {
                const i = this.priceConfig;
                return e === this.iOSProductIdPlusMonth ? i.plusPriceMonthOnlyValue : e === this.iOSProductIdPlusYear ? i.plusPriceYearTotalValue : e === this.iOSProductIdProMonth ? i.proPriceMonthOnlyValue : e === this.iOSProductIdProYear ? i.proPriceYearTotalValue : e === this.iOSProductIdCredit10k ? i.creditPriceValue : e === this.iOSProductIdCredit20k ? i.credit2xPriceValue : 0
            },
            async handleWebPurchase(e) {
                const i = new FormData(e.target),
                    t = Object.fromEntries(i); {
                    const e = this.getPriceValueForWeb(t.price_id);
                    window.gtag && gtag("event", "begin_checkout", {
                        platform: "web",
                        price_id: t.price_id,
                        entry: t.entry,
                        current_path: "pricingWindowForCreditPack",
                        currency: "USD",
                        value: e
                    }), window.fbq && fbq("track", "InitiateCheckout", {
                        content_ids: [t.price_id],
                        value: e
                    })
                }
                try {
                    const e = await fetch("/api/payment/create-checkout-session-web", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify(t)
                    });
                    if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                    const i = await e.json();
                    i && 0 === i.status && i.data && i.data.url && (window.location.href = i.data.url)
                } catch (n) {}
            },
            async jumpToCustomerPortal(e) {
                try {
                    const i = await fetch("/api/payment/customer-portal", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify(e)
                    });
                    if (i.ok) {
                        const e = await i.json();
                        if (e.url) return void(window.location.href = e.url)
                    }
                } catch (i) {}
            },
            getMembershipTypeName: e => "plus" === e ? "Plus" : "pro" === e ? "Pro" : e,
            getAppStoreLink: () => "https://apps.apple.com/app/id6739554054",
            handleInApplePurchase(e) {
                this.isInIOS && this.jsBridge && this.jsBridge.callHandler("iap", {
                    product_id: e
                }, (() => {
                    {
                        const i = this.getPriceValueForApple(e);
                        window.gtag && gtag("event", "begin_checkout", {
                            platform: "ios",
                            price_id: e,
                            currency: "USD",
                            value: i
                        }), window.fbq && fbq("track", "InitiateCheckout", {
                            content_ids: [e],
                            currency: "USD",
                            value: i
                        })
                    }
                }))
            }
        },
        setup(e, {
            emit: i
        }) {
            const u = r(!1),
                d = r(!0),
                g = r(!1),
                h = r(""),
                m = r(!0),
                y = r(!0),
                {
                    t: b
                } = a(),
                {
                    getCurrentSubscriptionObject: w,
                    fetchCurrentSubscription: v,
                    parseCurrentSubscriptionData: _
                } = t(),
                {
                    getPriceConfigObject: P,
                    fetchPriceConfig: f,
                    parseDataValue: k
                } = n(),
                C = r(!0),
                S = r(!1),
                I = p("jsBridge"),
                M = r(!1);
            s((() => I.value), (e => {
                e && I.value.callHandler("support", {
                    api: "iap"
                }, (e => {
                    S.value = e
                }))
            }), {
                immediate: !0
            });
            const O = o();
            "forceshowios" in O.query && "string" == typeof O.query.forceshowios && "true" === O.query.forceshowios && (M.value = !0), "showdebuginfo" in O.query && "string" == typeof O.query.showdebuginfo && "true" === O.query.showdebuginfo ? u.value = !0 : u.value = !1, h.value = O.fullPath;
            const T = r(l.isGensparkAppIos());
            if (T.value && !C.value) return void(location.href = "/pricing");
            const x = r(w()),
                $ = r(P()),
                W = r(null),
                V = r(null);
            c((async () => {
                W.value = await v(O, null), V.value = await f(O, null)
            })), s((() => W.value), (e => {
                e && (_(e, T.value, M.value, g.value, b), y.value = !1)
            })), s((() => V.value), (e => {
                e && (k(e), m.value = !1)
            }));
            return {
                t: b,
                showDebugInfo: u,
                isMonthly: g,
                togglePlanInterval: () => {
                    g.value = !g.value, (T.value || M.value) && (x.value.proButtonDisabled = "pro" === x.value.currentPaidMembershipType && ("month" === x.value.appleProductInterval && !0 === g.value || "year" === x.value.appleProductInterval && !1 === g.value))
                },
                currentSubscription: x,
                priceConfig: $,
                currentPath: h,
                isWaitingForCurrentSubscription: y,
                isWaitingForPriceConfig: m,
                emit: i,
                getWindowTitle: i => {
                    switch (e.windowType) {
                        case "credit_insufficient":
                            return b("pages.pricing-window.title-credit-insufficient");
                        case "capacity_exhausted":
                            return b("pages.pricing-window.title-capacity-exhausted");
                        default:
                            return b("pages.pricing-window.title-credit", {
                                membership_type: i
                            })
                    }
                },
                getWindowSubtitle: () => b("pages.pricing-window.subtitle-credit"),
                enableProPlan: d,
                jsBridge: I,
                isInIOS: T,
                supportIAP: S,
                enableIOS: C,
                forceShowIOS: M
            }
        }
    }, [
        ["render", function(e, i, t, n, r, a) {
            const s = P("PlanProDesc"),
                o = P("PlanCreditDesc");
            return f(), d("div", k, [i[12] || (i[12] = g("div", {
                class: "ellipse-1"
            }, null, -1)), i[13] || (i[13] = g("div", {
                class: "ellipse-2"
            }, null, -1)), g("button", {
                class: "close_button",
                onClick: i[0] || (i[0] = i => e.$emit("close"))
            }, i[7] || (i[7] = [g("svg", {
                class: "close_icon",
                xmlns: "http://www.w3.org/2000/svg",
                width: "24",
                height: "24",
                viewBox: "0 0 24 24",
                fill: "none",
                stroke: "currentColor",
                "stroke-width": "2.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, [g("line", {
                x1: "18",
                y1: "6",
                x2: "6",
                y2: "18"
            }), g("line", {
                x1: "6",
                y1: "6",
                x2: "18",
                y2: "18"
            })], -1)])), g("div", C, [i[8] || (i[8] = g("div", null, null, -1)), g("span", null, h(n.getWindowTitle(a.getMembershipTypeName(n.currentSubscription.currentPaidMembershipType))), 1)]), g("div", S, [g("span", null, h(n.getWindowSubtitle()), 1)]), g("div", I, [n.isInIOS && !n.supportIAP ? (f(), d("div", M, [g("span", {
                innerHTML: e.$t("pages.pricing.notice_for_ios_app_upgrade", {
                    url_html: `<a href='${a.getAppStoreLink()}' target='_blank' style='color: var(--link);'>Genspark</a>`
                })
            }, null, 8, O)])) : n.isInIOS || n.forceShowIOS || !["plus", "pro"].includes(n.currentSubscription.applePaidMembershipType) || "" != n.currentSubscription.inviteCreditStartDate || ["plus", "pro"].includes(n.currentSubscription.webPaidMembershipType) ? !n.isInIOS && !n.forceShowIOS || "" == n.currentSubscription.inviteCreditStartDate && !["plus", "pro"].includes(n.currentSubscription.webPaidMembershipType) ? (f(), d("div", $, [g("div", W, [!n.isWaitingForCurrentSubscription && n.enableProPlan && "pro" !== n.currentSubscription.currentPaidMembershipType ? (f(), d("div", V, [g("div", null, [g("div", j, [g("div", D, [g("p", null, h(e.$t("pages.pricing.pro-plan-title")), 1), g("div", F, [g("div", {
                "aria-label": "Toggle for switching between Monthly and Annually plans",
                class: "plan_tab_container rounded-full bg-token-main-surface-tertiary",
                style: {
                    outline: "none"
                },
                onClick: i[1] || (i[1] = (...e) => n.togglePlanInterval && n.togglePlanInterval(...e))
            }, [g("div", A, [g("div", Y, [g("button", {
                type: "button",
                "data-state": n.isMonthly ? "on" : "off",
                role: "radio",
                "aria-checked": n.isMonthly,
                "aria-label": "Toggle for switching to Monthly plans",
                class: y(n.isMonthly ? "text-token-text-primary" : "text-token-text-tertiary"),
                "data-radix-collection-item": ""
            }, h(e.$t("pages.pricing.plan-monthly")), 11, B), n.isMonthly ? (f(), d("div", {
                key: 0,
                class: "absolute inset-0 -z-10 rounded-full bg-token-main-surface-primary",
                style: b({
                    transform: n.isMonthly ? "translateX(0)" : "translateX(100%)",
                    transition: "transform 0.2s ease-in-out"
                })
            }, null, 4)) : m("", !0)]), g("div", q, [g("button", {
                type: "button",
                "data-state": n.isMonthly ? "off" : "on",
                role: "radio",
                "aria-checked": !n.isMonthly,
                "aria-label": "Toggle for switching to Annually plans",
                class: y(n.isMonthly ? "text-token-text-tertiary" : "text-token-text-primary"),
                "data-radix-collection-item": ""
            }, h(e.$t("pages.pricing.plan-annually")), 11, L), n.isMonthly ? m("", !0) : (f(), d("div", {
                key: 0,
                class: "absolute inset-0 -z-10 rounded-full bg-token-main-surface-primary",
                style: b({
                    transform: n.isMonthly ? "translateX(100%)" : "translateX(0)",
                    transition: "transform 0.2s ease-in-out"
                })
            }, null, 4))])])])])]), g("div", H, [g("div", null, [n.isWaitingForPriceConfig ? (f(), d("div", R, i[9] || (i[9] = [g("div", {
                class: "plan_price_amount"
            }, "loading...", -1)]))) : (f(), d("div", N, [n.isMonthly ? (f(), d("div", E, " $" + h(n.priceConfig.proPriceMonthOnlyValue), 1)) : (f(), d("div", X, " $" + h(n.priceConfig.proPriceWithYearValue), 1)), n.isMonthly ? m("", !0) : (f(), d("div", U, " $" + h(n.priceConfig.proPriceMonthOnlyValue), 1)), g("div", z, h(n.priceConfig.proPriceCurrency) + " / " + h(n.priceConfig.proPriceInterval), 1)]))])]), n.isMonthly ? (f(), d("div", J, h(e.$t("pages.pricing.plan-sidenote-monthly")), 1)) : (f(), d("div", G, " $" + h(n.priceConfig.proPriceYearTotalValue) + "/" + h(e.$t("pages.pricing.price-year")) + ", " + h(e.$t("pages.pricing.plan-sidenote-annually")), 1))])]), g("div", Z, [w(s, {
                "window-type": t.windowType
            }, null, 8, ["window-type"])]), g("div", K, [n.enableIOS && (n.isInIOS || n.forceShowIOS) ? (f(), d("div", Q, [n.supportIAP || n.forceShowIOS ? (f(), d(v, {
                key: 0
            }, [n.currentSubscription.proButtonDisabled ? (f(), d("button", {
                key: 0,
                class: "row4_button",
                disabled: n.currentSubscription.proButtonDisabled
            }, h(e.$t("pages.pricing.your-current-plan")), 9, ee)) : (f(), d("button", {
                key: 1,
                class: "row4_button",
                onClick: i[2] || (i[2] = e => a.handleInApplePurchase(n.isMonthly ? this.iOSProductIdProMonth : this.iOSProductIdProYear))
            }, h(n.isMonthly ? e.$t("pages.pricing.switch-to-pro-monthly") : e.$t("pages.pricing.switch-to-pro-yearly")), 1)), n.currentSubscription.proButtonDisabled ? m("", !0) : (f(), d("span", ie, h(e.$t("pages.pricing.auto-renew-notice")), 1))], 64)) : m("", !0)])) : (f(), d("div", te, [g("form", {
                onSubmit: i[4] || (i[4] = _(((...e) => a.handleWebPurchase && a.handleWebPurchase(...e)), ["prevent"]))
            }, [n.isMonthly ? (f(), d("input", {
                key: 0,
                type: "hidden",
                id: "pro_price",
                name: "price_id",
                value: n.priceConfig.proPriceMonthOnly
            }, null, 8, ne)) : (f(), d("input", {
                key: 1,
                type: "hidden",
                id: "pro_price",
                name: "price_id",
                value: n.priceConfig.proPriceWithYear
            }, null, 8, re)), g("input", {
                type: "hidden",
                id: "testmode",
                name: "testmode",
                value: n.priceConfig.testmode
            }, null, 8, ae), g("input", {
                type: "hidden",
                id: "current_path",
                name: "current_path",
                value: n.currentPath
            }, null, 8, se), n.isWaitingForPriceConfig || n.isWaitingForCurrentSubscription ? (f(), d("button", oe, h(e.$t("pages.pricing.upgrade-to-pro")), 1)) : n.currentSubscription.proButtonDisabled ? (f(), d("button", {
                key: 3,
                class: "row4_button",
                disabled: n.currentSubscription.proButtonDisabled
            }, h(e.$t("pages.pricing.your-current-plan")), 9, le)) : "plus" === n.currentSubscription.currentPaidMembershipType ? (f(), d("button", {
                key: 4,
                class: "row4_button",
                onClick: i[3] || (i[3] = async () => {
                    a.jumpToCustomerPortal({
                        testmode: n.priceConfig.testmode,
                        subscription_id: n.currentSubscription.currentSubscriptionId
                    })
                })
            }, h(e.$t("pages.pricing.upgrade-to-pro")), 1)) : (f(), d("button", ce, h(e.$t("pages.pricing.upgrade-to-pro")), 1))], 32)]))])])) : m("", !0), g("div", pe, [g("div", null, [g("div", ue, [g("div", de, [g("p", null, h(e.$t("pages.pricing.credit-plan-title")), 1)]), g("div", ge, [g("div", null, [n.isWaitingForPriceConfig ? (f(), d("div", he, i[10] || (i[10] = [g("div", {
                class: "plan_price_amount"
            }, "loading...", -1)]))) : (f(), d("div", me, [g("div", ye, " $" + h(n.priceConfig.creditPriceValue), 1), g("div", be, h(n.priceConfig.creditPriceCurrency), 1)]))])])])]), g("div", we, [w(o, {
                "pack-type": "standard"
            })]), g("div", ve, [n.enableIOS && (n.isInIOS || n.forceShowIOS) ? (f(), d("div", _e, [n.supportIAP || n.forceShowIOS ? (f(), d("button", {
                key: 0,
                class: "row4_button",
                onClick: i[5] || (i[5] = e => a.handleInApplePurchase(this.iOSProductIdCredit10k))
            }, h(e.$t("pages.pricing.button-buy-credit")), 1)) : m("", !0)])) : (f(), d("div", Pe, [g("form", {
                onSubmit: i[6] || (i[6] = _(((...e) => a.handleWebPurchase && a.handleWebPurchase(...e)), ["prevent"]))
            }, [g("input", {
                type: "hidden",
                id: "price_id",
                name: "price_id",
                value: n.priceConfig.creditPrice
            }, null, 8, fe), g("input", {
                type: "hidden",
                id: "testmode",
                name: "testmode",
                value: n.priceConfig.testmode
            }, null, 8, ke), i[11] || (i[11] = g("input", {
                type: "hidden",
                id: "plan_type",
                name: "plan_type",
                value: "credit"
            }, null, -1)), g("input", {
                type: "hidden",
                id: "current_path",
                name: "current_path",
                value: n.currentPath
            }, null, 8, Ce), n.isWaitingForPriceConfig ? (f(), d("button", Se, h(e.$t("pages.pricing.button-buy-credit")), 1)) : (f(), d("button", Ie, h(e.$t("pages.pricing.button-buy-credit")), 1))], 32)]))])])])])) : (f(), d("div", x, h(e.$t("pages.pricing.notice_for_web_paid_membership", {
                membership_type: ["plus", "pro"].includes(n.currentSubscription.webPaidMembershipType) ? a.getMembershipTypeName(n.currentSubscription.webPaidMembershipType) : "Plus"
            })), 1)) : (f(), d("div", T, h(e.$t("pages.pricing.notice_for_apple_paid_membership", {
                membership_type: a.getMembershipTypeName(n.currentSubscription.applePaidMembershipType)
            })), 1)), n.showDebugInfo ? (f(), d("div", Me, [g("span", null, " isInIOS: [ " + h(n.isInIOS) + " ], supportIAP: [ " + h(n.supportIAP) + " ], forceShowIOS: [ " + h(n.forceShowIOS) + " ] ", 1), g("span", null, "payment region: [ " + h(n.priceConfig.paymentRegion) + " ], mode: [ " + h(n.priceConfig.liveOrTestMode) + " ], price_id: [ month=" + h(n.priceConfig.plusPriceMonthOnly) + ", year=" + h(n.priceConfig.plusPriceWithYear) + " ]", 1), g("pre", null, h(n.currentSubscription.debugInfo), 1)])) : m("", !0)])])
        }],
        ["__scopeId", "data-v-5379bfa6"]
    ]),
    Te = {
        class: "theme payment_window"
    },
    xe = {
        class: "window_title"
    },
    $e = {
        class: "window_subtitle"
    },
    We = {
        class: "window_body"
    },
    Ve = {
        key: 0,
        class: "claim_result"
    },
    je = {
        key: 0,
        class: "success_message"
    },
    De = {
        class: "ok_section"
    },
    Fe = {
        key: 1,
        class: "error_message"
    },
    Ae = {
        class: "action_buttons"
    },
    Ye = ["disabled"],
    Be = {
        key: 0
    },
    qe = {
        key: 1
    },
    Le = {
        key: 1
    },
    He = {
        class: "plan_item_group"
    },
    Re = {
        class: "plan_column"
    },
    Ne = {
        class: "plan_column_upper"
    },
    Ee = {
        class: "row1"
    },
    Xe = {
        class: "plan_desc"
    },
    Ue = {
        class: "row4"
    },
    ze = ["disabled"],
    Ge = {
        key: 0
    },
    Je = {
        key: 1
    };
const Ze = u({
    name: "PricingWindowForTeamCreditsClaim",
    components: {
        PlanCreditDesc: e
    },
    methods: {
        async handleCreditsClaim() {
            this.isClaiming = !0;
            try {
                const e = await fetch("/api/payment/claim-team-credits", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    }
                });
                if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                const i = await e.json();
                i && 0 === i.status ? this.claimResult = {
                    success: !0,
                    message: i.message || this.t("pages.pricing-window.team-credits-claim-success-message")
                } : this.claimResult = {
                    success: !1,
                    message: i.message || this.t("pages.pricing-window.team-credits-claim-failed-message")
                }
            } catch (e) {
                this.claimResult = {
                    success: !1,
                    message: this.t("pages.pricing-window.team-credits-claim-error-message")
                }
            } finally {
                this.isClaiming = !1
            }
        }
    },
    data: () => ({
        isClaiming: !1,
        claimResult: null
    }),
    setup(e, {
        emit: i
    }) {
        const t = r(""),
            n = r(!1),
            {
                t: s
            } = a(),
            l = o();
        return "testmode" in l.query && "true" === l.query.testmode ? n.value = !0 : n.value = !1, t.value = l.fullPath, {
            t: s,
            currentPath: t,
            testmode: n,
            emit: i
        }
    }
}, [
    ["render", function(e, i, t, n, r, a) {
        const s = P("PlanCreditDesc");
        return f(), d("div", Te, [i[8] || (i[8] = g("div", {
            class: "ellipse-1"
        }, null, -1)), i[9] || (i[9] = g("div", {
            class: "ellipse-2"
        }, null, -1)), g("button", {
            class: "close_button",
            onClick: i[0] || (i[0] = i => e.$emit("close"))
        }, i[4] || (i[4] = [g("svg", {
            class: "close_icon",
            xmlns: "http://www.w3.org/2000/svg",
            width: "24",
            height: "24",
            viewBox: "0 0 24 24",
            fill: "none",
            stroke: "currentColor",
            "stroke-width": "2.5",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }, [g("line", {
            x1: "18",
            y1: "6",
            x2: "6",
            y2: "18"
        }), g("line", {
            x1: "6",
            y1: "6",
            x2: "18",
            y2: "18"
        })], -1)])), g("div", xe, [i[5] || (i[5] = g("div", null, null, -1)), g("span", null, h(n.t("pages.pricing-window.team-credits-claim-title")), 1)]), g("div", $e, [g("span", null, h(n.t("pages.pricing-window.team-credits-claim-subtitle")), 1)]), g("div", We, [r.claimResult ? (f(), d("div", Ve, [r.claimResult.success ? (f(), d("div", je, [i[6] || (i[6] = g("div", {
            class: "result_icon success_icon"
        }, [g("svg", {
            viewBox: "0 0 24 24",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg"
        }, [g("path", {
            "fill-rule": "evenodd",
            "clip-rule": "evenodd",
            d: "M18.0633 5.67387C18.5196 5.98499 18.6374 6.60712 18.3262 7.06343L10.8262 18.0634C10.6585 18.3095 10.3898 18.4679 10.0934 18.4957C9.79688 18.5235 9.50345 18.4178 9.29289 18.2072L4.79289 13.7072C4.40237 13.3167 4.40237 12.6835 4.79289 12.293C5.18342 11.9025 5.81658 11.9025 6.20711 12.293L9.85368 15.9396L16.6738 5.93676C16.9849 5.48045 17.607 5.36275 18.0633 5.67387Z",
            fill: "currentColor"
        })])], -1)), g("h3", null, h(n.t("pages.pricing-window.team-credits-claim-success-title")), 1), g("div", De, [g("button", {
            class: "row4_button claim_button",
            onClick: i[1] || (i[1] = i => e.$emit("close"))
        }, h(n.t("pages.pricing-window.button-ok")), 1)])])) : (f(), d("div", Fe, [i[7] || (i[7] = g("div", {
            class: "result_icon error_icon"
        }, [g("svg", {
            viewBox: "0 0 24 24",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg"
        }, [g("path", {
            d: "M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
            stroke: "currentColor",
            "stroke-width": "2",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        })])], -1)), g("h3", null, h(n.t("pages.pricing-window.team-credits-claim-failed-title")), 1), g("div", Ae, [g("button", {
            class: "row4_button claim_button",
            onClick: i[2] || (i[2] = (...e) => a.handleCreditsClaim && a.handleCreditsClaim(...e)),
            disabled: r.isClaiming
        }, [r.isClaiming ? (f(), d("span", Be, h(n.t("pages.pricing-window.button-claiming-credits")), 1)) : (f(), d("span", qe, h(n.t("pages.pricing-window.button-claim-team-credit")), 1))], 8, Ye)])]))])) : (f(), d("div", Le, [g("div", He, [g("div", Re, [g("div", null, [g("div", Ne, [g("div", Ee, [g("p", null, h(n.t("pages.pricing-window.credit-plan-title")), 1)])])]), g("div", Xe, [w(s, {
            "pack-type": "standard"
        })]), g("div", Ue, [g("button", {
            class: "row4_button",
            onClick: i[3] || (i[3] = (...e) => a.handleCreditsClaim && a.handleCreditsClaim(...e)),
            disabled: r.isClaiming
        }, [r.isClaiming ? (f(), d("span", Ge, h(n.t("pages.pricing-window.button-claiming-credits")), 1)) : (f(), d("span", Je, h(n.t("pages.pricing-window.button-claim-team-credit")), 1))], 8, ze)])])])]))])])
    }],
    ["__scopeId", "data-v-69e3a850"]
]);
export {
    Ze as P, Oe as a
};