import {
    d as e,
    D as a,
    o as s,
    b as t,
    _ as i,
    S as o,
    r,
    v as n,
    x as l,
    h as c,
    C as p,
    a as d,
    w as v,
    E as u,
    f as w,
    H as m,
    t as g,
    e as h,
    n as f,
    L as _,
    F as k,
    k as x,
    p as y,
    I as b
} from "./Cf0SOiw0.js";
import {
    H as S
} from "./QQkd3sR0.js";
import {
    P as C
} from "./DHyvnBfI.js";
import {
    T as D,
    P as j
} from "./BDUh8PoD.js";
import {
    S as I
} from "./DJt7CPhG.js";
import {
    E as P
} from "./DC84aLnd.js";
import {
    S as L
} from "./C_XD2eP3.js";
import {
    N as M
} from "./COYh4g7i.js";
import {
    N as H
} from "./BjWUbj3w.js";
const $ = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 24 24"
};
const E = {
        render: function(t, i) {
            return s(), e("svg", $, i[0] || (i[0] = [a('<g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 14c.83.642 2.077 1.017 3.5 1c1.423.017 2.67-.358 3.5-1c.83-.642 2.077-1.017 3.5-1c1.423-.017 2.67.358 3.5 1"></path><path d="M8 3a2.4 2.4 0 0 0-1 2a2.4 2.4 0 0 0 1 2"></path><path d="M12 3a2.4 2.4 0 0 0-1 2a2.4 2.4 0 0 0 1 2"></path><path d="M3 10h14v5a6 6 0 0 1-6 6H9a6 6 0 0 1-6-6v-5z"></path><path d="M16.746 16.726a3 3 0 1 0 .252-5.555"></path></g>', 1)]))
        }
    },
    T = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 32 32"
    };
const N = {
        render: function(a, i) {
            return s(), e("svg", T, i[0] || (i[0] = [t("g", {
                fill: "none"
            }, [t("path", {
                d: "M2 16C2 8.268 8.268 2 16 2s14 6.268 14 14s-6.268 14-14 14S2 23.732 2 16zm13.293-6.707a1 1 0 0 0 0 1.414L19.586 15H10a1 1 0 1 0 0 2h9.586l-4.293 4.293a1 1 0 0 0 1.414 1.414l6-6a1 1 0 0 0 0-1.414l-6-6a1 1 0 0 0-1.414 0z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    A = {
        name: "CreateFormAutopilotagent",
        components: {
            HandleArrowRightIcon: S,
            PublicIcon: C,
            PrivateIcon: j,
            SelectedIconSvg: I,
            TriangleIconSvg: D,
            ErrorMessageIcon: P,
            SpinIcon: L,
            NModal: H,
            NSwitch: M
        },
        props: {
            show: {
                type: Boolean,
                default: !1
            },
            agentConfig: {
                type: Object,
                default: {}
            }
        },
        emits: ["created", "quotaExceed", "update:show"],
        setup(e, {
            expose: a,
            emit: s
        }) {
            const {
                show: t,
                agentConfig: i
            } = o(e), d = r(t.value);
            n((() => t.value), (() => {
                d.value = t.value
            })), n((() => d.value), (() => {
                s("update:show", d.value)
            }));
            const v = r("public"),
                u = r(!1),
                w = r(!0),
                m = r(""),
                g = r(""),
                h = r(!1),
                f = () => {
                    u.value = !1
                };
            l((() => {
                document.removeEventListener("click", f)
            })), c((async () => {
                document.addEventListener("click", f)
            })), n(d, (() => {
                m.value = ""
            })), a({
                reset: () => {
                    g.value = "", m.value = "", h.value = !1, v.value = "public", u.value = !1
                }
            });
            return {
                utils: p,
                create_by_article: w,
                permission: v,
                permissionShowDropDownList: u,
                create_form_show: d,
                createProject: async () => {
                    if (g.value.trim()) {
                        m.value = "";
                        try {
                            h.value = !0;
                            const e = await fetch("/api/project/create", {
                                    method: "POST",
                                    headers: {
                                        "Content-Type": "application/json"
                                    },
                                    body: JSON.stringify({
                                        type: i.value.id,
                                        name: g.value.trim().slice(0, 30),
                                        description: "",
                                        article: g.value,
                                        run_in_background: !0
                                    })
                                }),
                                a = await e.json();
                            if (-2 === a.status && a.data) return void(m.value = a.data.analysis);
                            if (-8 === a.status) return void s("quotaExceed");
                            if (0 !== a.status || !a.data) return;
                            s("created", a.data), g.value = "", h.value = !1
                        } catch (e) {} finally {
                            h.value = !1
                        }
                    }
                },
                agentConfig: i,
                create_article: g,
                creatingProject: h,
                create_task_error_message: m
            }
        }
    },
    z = {
        class: "create_form"
    },
    B = {
        class: "create_form_header"
    },
    V = {
        class: "title"
    },
    O = {
        key: 0,
        class: "dropdown"
    },
    R = {
        class: "icon"
    },
    U = {
        class: "text"
    },
    q = {
        key: 1,
        class: "dropdown"
    },
    F = {
        class: "icon"
    },
    J = {
        class: "text"
    },
    K = {
        key: 2,
        class: "dropDownList"
    },
    G = {
        class: "wrapper"
    },
    Q = {
        class: "name"
    },
    W = {
        class: "icon"
    },
    X = {
        class: "text"
    },
    Y = {
        class: "desc"
    },
    Z = {
        key: 0,
        class: "selected"
    },
    ee = {
        class: "wrapper"
    },
    ae = {
        class: "name"
    },
    se = {
        class: "icon"
    },
    te = {
        class: "text"
    },
    ie = {
        class: "desc"
    },
    oe = {
        key: 0,
        class: "selected"
    },
    re = {
        key: 0,
        class: "create_task_error_message"
    },
    ne = {
        class: "icon"
    },
    le = {
        class: "text"
    },
    ce = {
        class: "textarea"
    },
    pe = ["placeholder"],
    de = {
        key: 1,
        class: "popularTasks"
    },
    ve = ["onClick"],
    ue = {
        class: "text"
    },
    we = {
        class: "icon"
    },
    me = {
        class: "checkbox"
    },
    ge = {
        class: "text"
    };
const he = i(A, [
        ["render", function(a, i, o, r, n, l) {
            const c = u("PublicIcon"),
                p = u("TriangleIconSvg"),
                S = u("PrivateIcon"),
                C = u("SelectedIconSvg"),
                D = u("ErrorMessageIcon"),
                j = u("HandleArrowRightIcon"),
                I = u("NSwitch"),
                P = u("SpinIcon"),
                L = u("NModal");
            return s(), d(L, {
                show: r.create_form_show,
                "onUpdate:show": i[6] || (i[6] = e => r.create_form_show = e),
                "auto-focus": !r.utils.isMobile()
            }, {
                default: v((() => {
                    var o, n, l, d, u;
                    return [t("div", z, [t("div", B, [t("div", V, g(null == (o = r.agentConfig) ? void 0 : o.new_label), 1), t("div", {
                        class: "permission",
                        onClick: i[2] || (i[2] = e => {
                            r.permissionShowDropDownList = !r.permissionShowDropDownList, e.stopPropagation(), e.preventDefault()
                        })
                    }, ["public" == r.permission ? (s(), e("div", O, [t("div", R, [h(c)]), t("div", U, g(a.$t("pages.article_verification.public")), 1), t("div", {
                        class: f(["triangle", r.permissionShowDropDownList ? "reverse" : ""])
                    }, [h(p)], 2)])) : "private" == r.permission ? (s(), e("div", q, [t("div", F, [h(S)]), t("div", J, g(a.$t("pages.article_verification.private")), 1), t("div", {
                        class: f(["triangle", r.permissionShowDropDownList ? "reverse" : ""])
                    }, [h(p)], 2)])) : w("", !0), r.permissionShowDropDownList ? (s(), e("ul", K, [t("li", {
                        onClick: i[0] || (i[0] = e => {
                            e.stopPropagation(), e.preventDefault(), r.permission = "public", r.permissionShowDropDownList = !1
                        })
                    }, [t("div", G, [t("div", Q, [t("div", W, [h(c)]), t("div", X, [t("span", null, g(a.$t("pages.article_verification.public")), 1)])]), t("div", Y, g(a.$t("pages.article_verification.anyone-with-the-link-can-view")), 1)]), "public" == r.permission ? (s(), e("div", Z, [h(C)])) : w("", !0)]), t("li", {
                        onClick: i[1] || (i[1] = e => {
                            e.stopPropagation(), e.preventDefault(), r.permission = "private", r.permissionShowDropDownList = !1
                        })
                    }, [t("div", ee, [t("div", ae, [t("div", se, [h(S)]), t("div", te, [t("span", null, g(a.$t("pages.article_verification.private")), 1)])]), t("div", ie, g(a.$t("pages.article_verification.only-you-can-view-this")), 1)]), "private" == r.permission ? (s(), e("div", oe, [h(C)])) : w("", !0)])])) : w("", !0)])]), r.create_task_error_message ? (s(), e("div", re, [t("div", ne, [h(D)]), t("div", le, g(r.create_task_error_message), 1)])) : w("", !0), t("div", ce, [m(t("textarea", {
                        "onUpdate:modelValue": i[3] || (i[3] = e => r.create_article = e),
                        placeholder: null == (n = r.agentConfig) ? void 0 : n.input_placeholder
                    }, null, 8, pe), [
                        [_, r.create_article]
                    ])]), (null == (l = r.agentConfig) ? void 0 : l.popularTasks) ? (s(), e("div", de, [(s(!0), e(k, null, x(null == (d = r.agentConfig) ? void 0 : d.popularTasks, (a => (s(), e("div", {
                        class: "task",
                        onClick: () => {
                            r.utils.windowopen(`/agents?id=${a.id}`)
                        }
                    }, [t("div", ue, g(a.name), 1), t("div", we, [h(j)])], 8, ve)))), 256))])) : w("", !0), m(t("div", me, [h(I, {
                        value: r.create_by_article,
                        "onUpdate:value": i[4] || (i[4] = e => r.create_by_article = e)
                    }, {
                        checked: v((() => [y(g(a.$t("pages.article_verification.create-by-article")), 1)])),
                        unchecked: v((() => [y(g(a.$t("pages.article_verification.create-by-statement")), 1)])),
                        _: 1
                    }, 8, ["value"])], 512), [
                        [b, !1]
                    ]), t("div", {
                        class: f(["button", {
                            disabled: 0 == r.create_article.trim().length
                        }]),
                        onClick: i[5] || (i[5] = () => {
                            r.creatingProject || r.createProject()
                        })
                    }, [t("div", ge, [t("div", {
                        class: f(["icon", {
                            spin: r.creatingProject
                        }])
                    }, [h(P)], 2), y(" " + g(null == (u = r.agentConfig) ? void 0 : u.button_label), 1)])], 2)])]
                })),
                _: 1
            }, 8, ["show", "auto-focus"])
        }],
        ["__scopeId", "data-v-b16dc35f"]
    ]),
    fe = {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        "stroke-width": "1.8",
        "stroke-linecap": "round",
        "stroke-linejoin": "round"
    };
const _e = {
    render: function(a, i) {
        return s(), e("svg", fe, i[0] || (i[0] = [t("circle", {
            cx: "12",
            cy: "12",
            r: "2.8"
        }, null, -1), t("path", {
            d: "M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"
        }, null, -1)]))
    }
};
export {
    N as A, E as C, _e as S, he as a
};