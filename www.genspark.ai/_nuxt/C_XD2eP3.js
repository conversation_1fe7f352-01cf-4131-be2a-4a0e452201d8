import {
    d as t,
    D as o,
    o as e
} from "./Cf0SOiw0.js";
const i = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const r = {
    render: function(r, s) {
        return e(), t("svg", i, s[0] || (s[0] = [o('<path d="M9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346V18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013" stroke="url(#paint0_linear_7656_16813)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346V18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013" stroke="url(#paint1_linear_7656_16813)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><defs><linearGradient id="paint0_linear_7656_16813" x1="13.9666" y1="3.49523" x2="0.954427" y2="8.97591" gradientUnits="userSpaceOnUse"><stop stop-color="white" stop-opacity="0.01"></stop><stop offset="1" stop-color="white"></stop></linearGradient><linearGradient id="paint1_linear_7656_16813" x1="7.72988" y1="2.84177" x2="14.3604" y2="7.37681" gradientUnits="userSpaceOnUse"><stop stop-color="white"></stop><stop offset="1" stop-color="white" stop-opacity="0.01"></stop></linearGradient></defs>', 3)]))
    }
};
export {
    r as S
};