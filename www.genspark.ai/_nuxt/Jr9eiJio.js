import {
    J as e,
    T as t,
    aD as n,
    h as o,
    a3 as i
} from "./Cf0SOiw0.js";

function r(e) {
    return e.composedPath()[0] || null
}

function s(e) {
    return e.composedPath()[0]
}
const a = {
    mousemoveoutside: new WeakMap,
    clickoutside: new WeakMap
};

function c(e, t, n) {
    const o = a[e];
    let i = o.get(t);
    void 0 === i && o.set(t, i = new WeakMap);
    let r = i.get(n);
    return void 0 === r && i.set(n, r = function(e, t, n) {
        if ("mousemoveoutside" === e) {
            const e = e => {
                t.contains(s(e)) || n(e)
            };
            return {
                mousemove: e,
                touchstart: e
            }
        }
        if ("clickoutside" === e) {
            let e = !1;
            const o = n => {
                    e = !t.contains(s(n))
                },
                i = o => {
                    e && (t.contains(s(o)) || n(o))
                };
            return {
                mousedown: o,
                mouseup: i,
                touchstart: o,
                touchend: i
            }
        }
        return {}
    }(e, t, n)), r
}
const {
    on: u,
    off: d
} = function() {
    if ("undefined" == typeof window) return {
        on: () => {},
        off: () => {}
    };
    const e = new WeakMap,
        t = new WeakMap;

    function n() {
        e.set(this, !0)
    }

    function o() {
        e.set(this, !0), t.set(this, !0)
    }

    function i(e, t, n) {
        const o = e[t];
        return e[t] = function() {
            return n.apply(e, arguments), o.apply(e, arguments)
        }, e
    }

    function r(e, t) {
        e[t] = Event.prototype[t]
    }
    const a = new WeakMap,
        f = Object.getOwnPropertyDescriptor(Event.prototype, "currentTarget");

    function h() {
        var e;
        return null !== (e = a.get(this)) && void 0 !== e ? e : null
    }

    function l(e, t) {
        void 0 !== f && Object.defineProperty(e, "currentTarget", {
            configurable: !0,
            enumerable: !0,
            get: null != t ? t : f.get
        })
    }
    const v = {
            bubble: {},
            capture: {}
        },
        p = {},
        b = function() {
            const c = function(c) {
                const {
                    type: u,
                    eventPhase: d,
                    bubbles: f
                } = c, p = s(c);
                if (2 === d) return;
                const b = 1 === d ? "capture" : "bubble";
                let g = p;
                const w = [];
                for (; null === g && (g = window), w.push(g), g !== window;) g = g.parentNode || null;
                const m = v.capture[u],
                    E = v.bubble[u];
                if (i(c, "stopPropagation", n), i(c, "stopImmediatePropagation", o), l(c, h), "capture" === b) {
                    if (void 0 === m) return;
                    for (let n = w.length - 1; n >= 0 && !e.has(c); --n) {
                        const e = w[n],
                            o = m.get(e);
                        if (void 0 !== o) {
                            a.set(c, e);
                            for (const e of o) {
                                if (t.has(c)) break;
                                e(c)
                            }
                        }
                        if (0 === n && !f && void 0 !== E) {
                            const n = E.get(e);
                            if (void 0 !== n)
                                for (const e of n) {
                                    if (t.has(c)) break;
                                    e(c)
                                }
                        }
                    }
                } else if ("bubble" === b) {
                    if (void 0 === E) return;
                    for (let n = 0; n < w.length && !e.has(c); ++n) {
                        const e = w[n],
                            o = E.get(e);
                        if (void 0 !== o) {
                            a.set(c, e);
                            for (const e of o) {
                                if (t.has(c)) break;
                                e(c)
                            }
                        }
                    }
                }
                r(c, "stopPropagation"), r(c, "stopImmediatePropagation"), l(c)
            };
            return c.displayName = "evtdUnifiedHandler", c
        }(),
        g = function() {
            const e = function(e) {
                const {
                    type: t,
                    eventPhase: n
                } = e;
                if (2 !== n) return;
                const o = p[t];
                void 0 !== o && o.forEach((t => t(e)))
            };
            return e.displayName = "evtdUnifiedWindowEventHandler", e
        }();

    function w(e, t) {
        const n = v[e];
        return void 0 === n[t] && (n[t] = new Map, window.addEventListener(t, b, "capture" === e)), n[t]
    }

    function m(e, t) {
        let n = e.get(t);
        return void 0 === n && e.set(t, n = new Set), n
    }

    function E(e, t, n, o) {
        const i = function(e, t, n, o) {
            if ("mousemoveoutside" === e || "clickoutside" === e) {
                const i = c(e, t, n);
                return Object.keys(i).forEach((e => {
                    d(e, document, i[e], o)
                })), !0
            }
            return !1
        }(e, t, n, o);
        if (i) return;
        const r = !0 === o || "object" == typeof o && !0 === o.capture,
            s = r ? "capture" : "bubble",
            a = w(s, e),
            u = m(a, t);
        if (t === window) {
            if (! function(e, t, n, o) {
                    const i = v[t][n];
                    if (void 0 !== i) {
                        const t = i.get(e);
                        if (void 0 !== t && t.has(o)) return !0
                    }
                    return !1
                }(t, r ? "bubble" : "capture", e, n) && function(e, t) {
                    const n = p[e];
                    return !(void 0 === n || !n.has(t))
                }(e, n)) {
                const t = p[e];
                t.delete(n), 0 === t.size && (window.removeEventListener(e, g), p[e] = void 0)
            }
        }
        u.has(n) && u.delete(n), 0 === u.size && a.delete(t), 0 === a.size && (window.removeEventListener(e, b, "capture" === s), v[s][e] = void 0)
    }
    return {
        on: function(e, t, n, o) {
            let i;
            i = "object" == typeof o && !0 === o.once ? r => {
                E(e, t, i, o), n(r)
            } : n;
            if (function(e, t, n, o) {
                    if ("mousemoveoutside" === e || "clickoutside" === e) {
                        const i = c(e, t, n);
                        return Object.keys(i).forEach((e => {
                            u(e, document, i[e], o)
                        })), !0
                    }
                    return !1
                }(e, t, i, o)) return;
            const r = m(w(!0 === o || "object" == typeof o && !0 === o.capture ? "capture" : "bubble", e), t);
            if (r.has(i) || r.add(i), t === window) {
                const t = function(e) {
                    return void 0 === p[e] && (p[e] = new Set, window.addEventListener(e, g)), p[e]
                }(e);
                t.has(i) || t.add(i)
            }
        },
        off: E
    }
}();
var f, h, l = [],
    v = "ResizeObserver loop completed with undelivered notifications.";
(h = f || (f = {})).BORDER_BOX = "border-box", h.CONTENT_BOX = "content-box", h.DEVICE_PIXEL_CONTENT_BOX = "device-pixel-content-box";
var p, b = function(e) {
        return Object.freeze(e)
    },
    g = function() {
        return function(e, t) {
            this.inlineSize = e, this.blockSize = t, b(this)
        }
    }(),
    w = function() {
        function e(e, t, n, o) {
            return this.x = e, this.y = t, this.width = n, this.height = o, this.top = this.y, this.left = this.x, this.bottom = this.top + this.height, this.right = this.left + this.width, b(this)
        }
        return e.prototype.toJSON = function() {
            var e = this;
            return {
                x: e.x,
                y: e.y,
                top: e.top,
                right: e.right,
                bottom: e.bottom,
                left: e.left,
                width: e.width,
                height: e.height
            }
        }, e.fromRect = function(t) {
            return new e(t.x, t.y, t.width, t.height)
        }, e
    }(),
    m = function(e) {
        return e instanceof SVGElement && "getBBox" in e
    },
    E = function(e) {
        if (m(e)) {
            var t = e.getBBox(),
                n = t.width,
                o = t.height;
            return !n && !o
        }
        var i = e,
            r = i.offsetWidth,
            s = i.offsetHeight;
        return !(r || s || e.getClientRects().length)
    },
    y = function(e) {
        var t;
        if (e instanceof Element) return !0;
        var n = null === (t = null == e ? void 0 : e.ownerDocument) || void 0 === t ? void 0 : t.defaultView;
        return !!(n && e instanceof n.Element)
    },
    x = "undefined" != typeof window ? window : {},
    T = new WeakMap,
    z = /auto|scroll/,
    S = /^tb|vertical/,
    B = /msie|trident/i.test(globalThis.navigator && x.navigator.userAgent),
    O = function(e) {
        return parseFloat(e || "0")
    },
    R = function(e, t, n) {
        return void 0 === e && (e = 0), void 0 === t && (t = 0), void 0 === n && (n = !1), new g((n ? t : e) || 0, (n ? e : t) || 0)
    },
    k = b({
        devicePixelContentBoxSize: R(),
        borderBoxSize: R(),
        contentBoxSize: R(),
        contentRect: new w(0, 0, 0, 0)
    }),
    M = function(e, t) {
        if (void 0 === t && (t = !1), T.has(e) && !t) return T.get(e);
        if (E(e)) return T.set(e, k), k;
        var n = getComputedStyle(e),
            o = m(e) && e.ownerSVGElement && e.getBBox(),
            i = !B && "border-box" === n.boxSizing,
            r = S.test(n.writingMode || ""),
            s = !o && z.test(n.overflowY || ""),
            a = !o && z.test(n.overflowX || ""),
            c = o ? 0 : O(n.paddingTop),
            u = o ? 0 : O(n.paddingRight),
            d = o ? 0 : O(n.paddingBottom),
            f = o ? 0 : O(n.paddingLeft),
            h = o ? 0 : O(n.borderTopWidth),
            l = o ? 0 : O(n.borderRightWidth),
            v = o ? 0 : O(n.borderBottomWidth),
            p = f + u,
            g = c + d,
            y = (o ? 0 : O(n.borderLeftWidth)) + l,
            x = h + v,
            M = a ? e.offsetHeight - x - e.clientHeight : 0,
            P = s ? e.offsetWidth - y - e.clientWidth : 0,
            C = i ? p + y : 0,
            N = i ? g + x : 0,
            W = o ? o.width : O(n.width) - C - P,
            D = o ? o.height : O(n.height) - N - M,
            H = W + p + P + y,
            L = D + g + M + x,
            I = b({
                devicePixelContentBoxSize: R(Math.round(W * devicePixelRatio), Math.round(D * devicePixelRatio), r),
                borderBoxSize: R(H, L, r),
                contentBoxSize: R(W, D, r),
                contentRect: new w(f, c, W, D)
            });
        return T.set(e, I), I
    },
    P = function(e, t, n) {
        var o = M(e, n),
            i = o.borderBoxSize,
            r = o.contentBoxSize,
            s = o.devicePixelContentBoxSize;
        switch (t) {
            case f.DEVICE_PIXEL_CONTENT_BOX:
                return s;
            case f.BORDER_BOX:
                return i;
            default:
                return r
        }
    },
    C = function() {
        return function(e) {
            var t = M(e);
            this.target = e, this.contentRect = t.contentRect, this.borderBoxSize = b([t.borderBoxSize]), this.contentBoxSize = b([t.contentBoxSize]), this.devicePixelContentBoxSize = b([t.devicePixelContentBoxSize])
        }
    }(),
    N = function(e) {
        if (E(e)) return 1 / 0;
        for (var t = 0, n = e.parentNode; n;) t += 1, n = n.parentNode;
        return t
    },
    W = function() {
        var e = 1 / 0,
            t = [];
        l.forEach((function(n) {
            if (0 !== n.activeTargets.length) {
                var o = [];
                n.activeTargets.forEach((function(t) {
                    var n = new C(t.target),
                        i = N(t.target);
                    o.push(n), t.lastReportedSize = P(t.target, t.observedBox), i < e && (e = i)
                })), t.push((function() {
                    n.callback.call(n.observer, o, n.observer)
                })), n.activeTargets.splice(0, n.activeTargets.length)
            }
        }));
        for (var n = 0, o = t; n < o.length; n++) {
            (0, o[n])()
        }
        return e
    },
    D = function(e) {
        l.forEach((function(t) {
            t.activeTargets.splice(0, t.activeTargets.length), t.skippedTargets.splice(0, t.skippedTargets.length), t.observationTargets.forEach((function(n) {
                n.isActive() && (N(n.target) > e ? t.activeTargets.push(n) : t.skippedTargets.push(n))
            }))
        }))
    },
    H = function() {
        var e, t = 0;
        for (D(t); l.some((function(e) {
                return e.activeTargets.length > 0
            }));) t = W(), D(t);
        return l.some((function(e) {
            return e.skippedTargets.length > 0
        })) && ("function" == typeof ErrorEvent ? e = new ErrorEvent("error", {
            message: v
        }) : ((e = document.createEvent("Event")).initEvent("error", !1, !1), e.message = v), window.dispatchEvent(e)), t > 0
    },
    L = [],
    I = function(e) {
        if (!p) {
            var t = 0,
                n = document.createTextNode("");
            new MutationObserver((function() {
                return L.splice(0).forEach((function(e) {
                    return e()
                }))
            })).observe(n, {
                characterData: !0
            }), p = function() {
                n.textContent = "".concat(t ? t-- : t++)
            }
        }
        L.push(e), p()
    },
    F = 0,
    _ = {
        attributes: !0,
        characterData: !0,
        childList: !0,
        subtree: !0
    },
    j = ["resize", "load", "transitionend", "animationend", "animationstart", "animationiteration", "keyup", "keydown", "mouseup", "mousedown", "mouseover", "mouseout", "blur", "focus"],
    V = function(e) {
        return void 0 === e && (e = 0), Date.now() + e
    },
    X = !1,
    A = new(function() {
        function e() {
            var e = this;
            this.stopped = !0, this.listener = function() {
                return e.schedule()
            }
        }
        return e.prototype.run = function(e) {
            var t = this;
            if (void 0 === e && (e = 250), !X) {
                X = !0;
                var n, o = V(e);
                n = function() {
                    var n = !1;
                    try {
                        n = H()
                    } finally {
                        if (X = !1, e = o - V(), !F) return;
                        n ? t.run(1e3) : e > 0 ? t.run(e) : t.start()
                    }
                }, I((function() {
                    requestAnimationFrame(n)
                }))
            }
        }, e.prototype.schedule = function() {
            this.stop(), this.run()
        }, e.prototype.observe = function() {
            var e = this,
                t = function() {
                    return e.observer && e.observer.observe(document.body, _)
                };
            document.body ? t() : x.addEventListener("DOMContentLoaded", t)
        }, e.prototype.start = function() {
            var e = this;
            this.stopped && (this.stopped = !1, this.observer = new MutationObserver(this.listener), this.observe(), j.forEach((function(t) {
                return globalThis.addEventListener(t, e.listener, !0)
            })))
        }, e.prototype.stop = function() {
            var e = this;
            this.stopped || (this.observer && this.observer.disconnect(), j.forEach((function(t) {
                return globalThis.removeEventListener(t, e.listener, !0)
            })), this.stopped = !0)
        }, e
    }()),
    q = function(e) {
        !F && e > 0 && A.start(), !(F += e) && A.stop()
    },
    U = function() {
        function e(e, t) {
            this.target = e, this.observedBox = t || f.CONTENT_BOX, this.lastReportedSize = {
                inlineSize: 0,
                blockSize: 0
            }
        }
        return e.prototype.isActive = function() {
            var e, t = P(this.target, this.observedBox, !0);
            return e = this.target, m(e) || function(e) {
                switch (e.tagName) {
                    case "INPUT":
                        if ("image" !== e.type) break;
                    case "VIDEO":
                    case "AUDIO":
                    case "EMBED":
                    case "OBJECT":
                    case "CANVAS":
                    case "IFRAME":
                    case "IMG":
                        return !0
                }
                return !1
            }(e) || "inline" !== getComputedStyle(e).display || (this.lastReportedSize = t), this.lastReportedSize.inlineSize !== t.inlineSize || this.lastReportedSize.blockSize !== t.blockSize
        }, e
    }(),
    G = function() {
        return function(e, t) {
            this.activeTargets = [], this.skippedTargets = [], this.observationTargets = [], this.observer = e, this.callback = t
        }
    }(),
    J = new WeakMap,
    $ = function(e, t) {
        for (var n = 0; n < e.length; n += 1)
            if (e[n].target === t) return n;
        return -1
    },
    Y = function() {
        function e() {}
        return e.connect = function(e, t) {
            var n = new G(e, t);
            J.set(e, n)
        }, e.observe = function(e, t, n) {
            var o = J.get(e),
                i = 0 === o.observationTargets.length;
            $(o.observationTargets, t) < 0 && (i && l.push(o), o.observationTargets.push(new U(t, n && n.box)), q(1), A.schedule())
        }, e.unobserve = function(e, t) {
            var n = J.get(e),
                o = $(n.observationTargets, t),
                i = 1 === n.observationTargets.length;
            o >= 0 && (i && l.splice(l.indexOf(n), 1), n.observationTargets.splice(o, 1), q(-1))
        }, e.disconnect = function(e) {
            var t = this,
                n = J.get(e);
            n.observationTargets.slice().forEach((function(n) {
                return t.unobserve(e, n.target)
            })), n.activeTargets.splice(0, n.activeTargets.length)
        }, e
    }(),
    K = function() {
        function e(e) {
            if (0 === arguments.length) throw new TypeError("Failed to construct 'ResizeObserver': 1 argument required, but only 0 present.");
            if ("function" != typeof e) throw new TypeError("Failed to construct 'ResizeObserver': The callback provided as parameter 1 is not a function.");
            Y.connect(this, e)
        }
        return e.prototype.observe = function(e, t) {
            if (0 === arguments.length) throw new TypeError("Failed to execute 'observe' on 'ResizeObserver': 1 argument required, but only 0 present.");
            if (!y(e)) throw new TypeError("Failed to execute 'observe' on 'ResizeObserver': parameter 1 is not of type 'Element");
            Y.observe(this, e, t)
        }, e.prototype.unobserve = function(e) {
            if (0 === arguments.length) throw new TypeError("Failed to execute 'unobserve' on 'ResizeObserver': 1 argument required, but only 0 present.");
            if (!y(e)) throw new TypeError("Failed to execute 'unobserve' on 'ResizeObserver': parameter 1 is not of type 'Element");
            Y.unobserve(this, e)
        }, e.prototype.disconnect = function() {
            Y.disconnect(this)
        }, e.toString = function() {
            return "function ResizeObserver () { [polyfill code] }"
        }, e
    }();
const Q = new class {
        constructor() {
            this.handleResize = this.handleResize.bind(this), this.observer = new("undefined" != typeof window && window.ResizeObserver || K)(this.handleResize), this.elHandlersMap = new Map
        }
        handleResize(e) {
            for (const t of e) {
                const e = this.elHandlersMap.get(t.target);
                void 0 !== e && e(t)
            }
        }
        registerHandler(e, t) {
            this.elHandlersMap.set(e, t), this.observer.observe(e)
        }
        unregisterHandler(e) {
            this.elHandlersMap.has(e) && (this.elHandlersMap.delete(e), this.observer.unobserve(e))
        }
    },
    Z = e({
        name: "ResizeObserver",
        props: {
            onResize: Function
        },
        setup(e) {
            let t = !1;
            const r = n().proxy;

            function s(t) {
                const {
                    onResize: n
                } = e;
                void 0 !== n && n(t)
            }
            o((() => {
                const e = r.$el;
                void 0 !== e && (e.nextElementSibling !== e.nextSibling && 3 === e.nodeType && "" !== e.nodeValue || null !== e.nextElementSibling && (Q.registerHandler(e.nextElementSibling, s), t = !0))
            })), i((() => {
                t && Q.unregisterHandler(r.$el.nextElementSibling)
            }))
        },
        render() {
            return t(this.$slots, "default")
        }
    });
export {
    Z as V, d as a, r as g, u as o, Q as r
};