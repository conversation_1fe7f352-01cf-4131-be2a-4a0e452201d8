.fade-enter-active[data-v-94412cae],
.fade-leave-active[data-v-94412cae] {
    transition: opacity .5s ease
}

.fade-enter-from[data-v-94412cae],
.fade-leave-to[data-v-94412cae] {
    opacity: 0
}

.permission-popover[data-v-94412cae] {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 30px #00000014;
    min-width: 460px;
    position: absolute;
    z-index: 1000
}

.permission-popover.disabled[data-v-94412cae] {
    pointer-events: none
}

@media (max-width:1220px) {
    .permission-popover[data-v-94412cae] {
        max-width: calc(100vw - 28px);
        position: fixed !important
    }
}

@media (max-width:768px) {
    .permission-popover[data-v-94412cae] {
        left: 14px !important;
        min-width: calc(100% - 28px)
    }
}

@media (prefers-color-scheme:dark) {
    .permission-popover[data-v-94412cae] {
        background-color: #444
    }
    .spinner[data-v-94412cae] {
        border-color: #f0f0f0 #555 #555
    }
}

.permission-popover-header[data-v-94412cae] {
    border-bottom: 1px solid #eaeaea;
    padding: 16px
}

.permission-popover-header-title[data-v-94412cae] {
    align-items: center;
    color: #333;
    display: flex;
    font-size: 18px;
    font-weight: 600;
    gap: 8px
}

.loading-indicator[data-v-94412cae] {
    align-items: center;
    display: flex;
    justify-content: center
}

.spinner[data-v-94412cae] {
    animation: spin-94412cae 1s linear infinite;
    border: 2px solid #f3f3f3;
    border-radius: 50%;
    border-top-color: #333;
    height: 16px;
    width: 16px
}

@keyframes spin-94412cae {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.permission-popover-content[data-v-94412cae] {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px
}

.permission-popover-content-item[data-v-94412cae] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.permission-popover-content-item-title[data-v-94412cae] {
    color: #232425;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%
}

.access-options[data-v-94412cae] {
    display: flex;
    flex-direction: column;
    gap: 4px
}

.access-option[data-v-94412cae] {
    align-items: center;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    gap: 12px;
    padding: 8px 12px 8px 0
}

.access-option.selected[data-v-94412cae] {
    background-color: transparent
}

.access-option input[type=radio][data-v-94412cae] {
    accent-color: #1a73e8;
    cursor: pointer;
    height: 18px;
    width: 18px
}

.access-option label[data-v-94412cae] {
    color: #232425;
    cursor: pointer;
    font-size: 14px
}

.pill-badge[data-v-94412cae] {
    align-items: center;
    background: #f3f9ff;
    border: 1px solid #cfe5ff;
    border-radius: 57px;
    color: #0f7fff;
    display: inline-flex;
    font-size: 10px;
    font-style: normal;
    font-weight: 700;
    gap: 10px;
    justify-content: center;
    line-height: 150%;
    padding: 0 8px;
    text-align: center
}

.people-list[data-v-94412cae] {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-top: 4px
}

.person[data-v-94412cae] {
    gap: 12px
}

.person[data-v-94412cae],
.person-avatar[data-v-94412cae] {
    align-items: center;
    display: flex
}

.person-avatar[data-v-94412cae] {
    background-color: #e0e0e0;
    border-radius: 50%;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    height: 36px;
    justify-content: center;
    outline: 1px solid rgba(0, 0, 0, .1);
    outline-offset: -1px;
    width: 36px
}

.default-avatar[data-v-94412cae] {
    align-items: center;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    position: relative
}

.default-avatar[data-v-94412cae] svg {
    height: 16px;
    width: 16px
}

.view-only-badge[data-v-94412cae] {
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 57px;
    display: inline-flex;
    height: 20px;
    justify-content: center;
    outline: 1px solid #e0e0e0;
    outline-offset: -1px;
    padding: 0 8px
}

.view-only-badge span[data-v-94412cae] {
    color: #666;
    font-size: 10px;
    font-weight: 400;
    line-height: 150%;
    white-space: nowrap
}

.person-avatar img[data-v-94412cae] {
    border-radius: 50%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.person-info[data-v-94412cae] {
    flex-grow: 1
}

.person-name[data-v-94412cae] {
    color: #333;
    font-size: 14px;
    font-weight: 500
}

.person-email[data-v-94412cae] {
    align-items: center;
    color: #666;
    display: flex;
    font-size: 13px;
    gap: 8px
}

.person-role[data-v-94412cae] {
    color: #888;
    font-size: 14px
}

.delete-icon[data-v-94412cae],
.person-role[data-v-94412cae] {
    align-items: center;
    display: flex;
    justify-content: center
}

.delete-icon[data-v-94412cae] {
    background-color: #f5f5f5;
    border-radius: 50%;
    color: #606366;
    cursor: pointer;
    font-size: 16px;
    height: 18px;
    line-height: 1;
    margin-left: 4px;
    padding-bottom: 1px;
    transition: all .2s ease;
    width: 18px
}

.delete-icon[data-v-94412cae]:hover {
    background-color: #e0e0e0;
    color: #404346
}

.invite-form[data-v-94412cae] {
    display: flex;
    gap: 8px;
    margin-top: 12px
}

.invite-input[data-v-94412cae] {
    border: 1px solid #eaeaea;
    border-radius: 8px;
    flex-grow: 1;
    font-size: 14px;
    outline: none;
    padding: 8px 12px
}

.invite-button[data-v-94412cae] {
    align-items: center;
    background: #232425;
    border-radius: 12px;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-family: Arial;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    gap: 10px;
    justify-content: center;
    line-height: normal;
    opacity: .45;
    padding: 12px 20px;
    width: 109px
}

.invite-button.active[data-v-94412cae] {
    opacity: 1
}

.share-link-button[data-v-94412cae] {
    align-items: center;
    align-self: stretch;
    background: #f5f5f5;
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    gap: 10px;
    height: 40px;
    justify-content: center;
    padding: 6px 12px;
    transition: background-color .2s ease
}

.link-icon[data-v-94412cae] {
    font-size: 18px
}

.link-text[data-v-94412cae] {
    color: #333;
    font-size: 14px;
    font-weight: 500
}

.divider[data-v-94412cae] {
    background-color: #efefef;
    border: none;
    height: 1px;
    margin: 0 -16px;
    padding: 0
}

.icon[data-v-94412cae] {
    align-items: center;
    background: #f5f5f5;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    height: 28px;
    justify-content: center;
    padding: 4px;
    width: 28px
}

@media (hover:hover) {
    .icon[data-v-94412cae]:hover {
        background-color: #e5e5e5
    }
    .invite-button[data-v-94412cae]:hover {
        background-color: #2e2f30
    }
    .share-link-button[data-v-94412cae]:hover {
        background-color: #f0f0f0;
        border-color: #ccc
    }
}

@media (prefers-color-scheme:dark) {
    .permission-popover-content-item-title[data-v-94412cae],
    .permission-popover-header-title[data-v-94412cae] {
        color: #f0f0f0
    }
    .permission-popover-header[data-v-94412cae] {
        border-bottom: 1px solid #eaeaea30
    }
    .access-option.selected[data-v-94412cae] {
        background-color: transparent
    }
    .access-option label[data-v-94412cae] {
        color: #e0e0e0
    }
    .pill-badge[data-v-94412cae] {
        background-color: #2d4164;
        color: #8ab4f8
    }
    .person-avatar[data-v-94412cae] {
        background-color: #3a3a3a;
        color: #e0e0e0
    }
    .person-name[data-v-94412cae] {
        color: #f0f0f0
    }
    .person-email[data-v-94412cae] {
        color: #bdbdbd
    }
    .person-role[data-v-94412cae] {
        color: #a0a0a0
    }
    .delete-icon[data-v-94412cae] {
        background-color: #333;
        color: #606366
    }
    .delete-icon[data-v-94412cae]:hover {
        background-color: #444;
        color: #a0a0a0
    }
    .invite-button[data-v-94412cae],
    .invite-input[data-v-94412cae] {
        background-color: #2a2a2a;
        border-color: #444;
        color: #e0e0e0
    }
    .share-link-button[data-v-94412cae] {
        background-color: #2a2a2a;
        border-color: #444;
        cursor: pointer
    }
    .link-text[data-v-94412cae] {
        color: #e0e0e0
    }
    .icon[data-v-94412cae] {
        background-color: #2a2a2a
    }
    .divider[data-v-94412cae] {
        background-color: #efefef30
    }
    .default-avatar[data-v-94412cae] {
        background-color: #333
    }
    .default-avatar[data-v-94412cae] svg {
        color: #999
    }
    .view-only-badge[data-v-94412cae] {
        background-color: #333;
        outline-color: #444
    }
    .view-only-badge span[data-v-94412cae] {
        color: #aaa
    }
    @media (hover:hover) {
        .icon[data-v-94412cae]:hover {
            background-color: #3a3a3a
        }
        .invite-button[data-v-94412cae]:hover {
            background-color: #333
        }
        .share-link-button[data-v-94412cae]:hover {
            background-color: #333;
            border-color: #555
        }
    }
}