import {
    M as e,
    i as s,
    O as o
} from "./Cf0SOiw0.js";

function t() {
    const t = s(o, null);
    return null === t && e("use-message", "No outer <n-message-provider /> founded. See prerequisite in https://www.naiveui.com/en-US/os-theme/components/message for more details. If you want to use `useMessage` outside setup, please check https://www.naiveui.com/zh-CN/os-theme/components/message#Q-&-A."), t
}
export {
    t as u
};