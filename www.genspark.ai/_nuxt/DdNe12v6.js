import {
    aT as n,
    al as t,
    am as e,
    ak as o,
    J as a,
    X as l,
    Y as i,
    Z as r,
    b8 as s,
    r as c,
    K as d,
    d as f,
    b as u,
    o as m
} from "./Cf0SOiw0.js";
import {
    k as h
} from "./BLWq1oPC.js";
const g = n("n-form"),
    v = n("n-form-item-insts"),
    p = t("form", [e("inline", "\n width: 100%;\n display: inline-flex;\n align-items: flex-start;\n align-content: space-around;\n ", [t("form-item", {
        width: "auto",
        marginRight: "18px"
    }, [o("&:last-child", {
        marginRight: 0
    })])])]);
var w = function(n, t, e, o) {
    return new(e || (e = Promise))((function(a, l) {
        function i(n) {
            try {
                s(o.next(n))
            } catch (t) {
                l(t)
            }
        }

        function r(n) {
            try {
                s(o.throw(n))
            } catch (t) {
                l(t)
            }
        }

        function s(n) {
            var t;
            n.done ? a(n.value) : (t = n.value, t instanceof e ? t : new e((function(n) {
                n(t)
            }))).then(i, r)
        }
        s((o = o.apply(n, t || [])).next())
    }))
};
const x = a({
        name: "Form",
        props: Object.assign(Object.assign({}, r.props), {
            inline: Boolean,
            labelWidth: [Number, String],
            labelAlign: String,
            labelPlacement: {
                type: String,
                default: "top"
            },
            model: {
                type: Object,
                default: () => {}
            },
            rules: Object,
            disabled: Boolean,
            size: String,
            showRequireMark: {
                type: Boolean,
                default: void 0
            },
            requireMarkPlacement: String,
            showFeedback: {
                type: Boolean,
                default: !0
            },
            onSubmit: {
                type: Function,
                default: n => {
                    n.preventDefault()
                }
            },
            showLabel: {
                type: Boolean,
                default: void 0
            },
            validateMessages: Object
        }),
        setup(n) {
            const {
                mergedClsPrefixRef: t
            } = i(n);
            r("Form", "-form", p, s, n, t);
            const e = {},
                o = c(void 0);
            d(g, {
                props: n,
                maxChildLabelWidthRef: o,
                deriveMaxChildLabelWidth: n => {
                    const t = o.value;
                    (void 0 === t || n >= t) && (o.value = n)
                }
            }), d(v, {
                formItems: e
            });
            const a = {
                validate: function(n) {
                    return w(this, arguments, void 0, (function*(n, t = () => !0) {
                        return yield new Promise(((o, a) => {
                            const l = [];
                            for (const n of h(e)) {
                                const o = e[n];
                                for (const n of o) n.path && l.push(n.internalValidate(null, t))
                            }
                            Promise.all(l).then((t => {
                                const e = t.some((n => !n.valid)),
                                    l = [],
                                    i = [];
                                t.forEach((n => {
                                    var t, e;
                                    (null === (t = n.errors) || void 0 === t ? void 0 : t.length) && l.push(n.errors), (null === (e = n.warnings) || void 0 === e ? void 0 : e.length) && i.push(n.warnings)
                                })), n && n(l.length ? l : void 0, {
                                    warnings: i.length ? i : void 0
                                }), e ? a(l.length ? l : void 0) : o({
                                    warnings: i.length ? i : void 0
                                })
                            }))
                        }))
                    }))
                },
                restoreValidation: function() {
                    for (const n of h(e)) {
                        const t = e[n];
                        for (const n of t) n.restoreValidation()
                    }
                }
            };
            return Object.assign(a, {
                mergedClsPrefix: t
            })
        },
        render() {
            const {
                mergedClsPrefix: n
            } = this;
            return l("form", {
                class: [`${n}-form`, this.inline && `${n}-form--inline`],
                onSubmit: this.onSubmit
            }, this.$slots)
        }
    }),
    b = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 24 24"
    };
const L = {
        render: function(n, t) {
            return m(), f("svg", b, t[0] || (t[0] = [u("g", {
                fill: "none"
            }, [u("path", {
                d: "M11.883 3.007L12 3a1 1 0 0 1 .993.883L13 4v7h7a1 1 0 0 1 .993.883L21 12a1 1 0 0 1-.883.993L20 13h-7v7a1 1 0 0 1-.883.993L12 21a1 1 0 0 1-.993-.883L11 20v-7H4a1 1 0 0 1-.993-.883L3 12a1 1 0 0 1 .883-.993L4 11h7V4a1 1 0 0 1 .883-.993L12 3l-.117.007z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    y = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 20 20"
    };
const j = {
    render: function(n, t) {
        return m(), f("svg", y, t[0] || (t[0] = [u("g", {
            fill: "none"
        }, [u("path", {
            d: "M13.245 2.817a2.783 2.783 0 0 1 4.066 3.796l-.13.14l-9.606 9.606a2.001 2.001 0 0 1-.723.462l-.165.053l-4.055 1.106a.5.5 0 0 1-.63-.535l.016-.08l1.106-4.054c.076-.28.212-.54.398-.76l.117-.128l9.606-9.606zm-.86 2.275L4.346 13.13a1 1 0 0 0-.215.321l-.042.123l-.877 3.21l3.212-.875a1 1 0 0 0 .239-.1l.107-.072l.098-.085l8.038-8.04l-2.521-2.52zm4.089-1.568a1.783 1.783 0 0 0-2.402-.11l-.12.11l-.86.86l2.52 2.522l.862-.86a1.783 1.783 0 0 0 .11-2.402l-.11-.12z",
            fill: "currentColor"
        })], -1)]))
    }
};
export {
    L as A, j as E, x as N, v as a, g as f
};