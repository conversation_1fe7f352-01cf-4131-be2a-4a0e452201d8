import {
    _ as e
} from "./Ug8thHSu.js";
import {
    b5 as t,
    X as o,
    J as r,
    dA as n,
    $ as i,
    aL as l,
    Z as a,
    aT as s,
    ak as u,
    al as d,
    aP as c,
    b6 as h,
    F as v,
    H as g,
    a4 as p,
    I as m,
    aR as f,
    r as w,
    v as C,
    a3 as b,
    c as x,
    Y as y,
    aq as k,
    i as P,
    dp as S,
    q as I,
    bb as L,
    K as O,
    aD as M,
    h as T,
    ag as z,
    _ as B,
    d as D,
    o as R,
    f as E,
    e as W,
    p as N,
    t as j,
    n as $,
    b as _,
    k as H,
    w as A,
    a as U,
    E as F,
    l as Z,
    x as V,
    C as q
} from "./Cf0SOiw0.js";
import {
    r as X,
    m as Y
} from "./BV6guSOS.js";
import {
    W as G
} from "./BUCk-Nnr.js";
import {
    P as Q
} from "./CAzLTKWw.js";
import {
    f as K
} from "./BLWq1oPC.js";
import {
    f as J
} from "./pB_XRIgB.js";
import {
    L as ee,
    z as te
} from "./DpMvtoun.js";
import {
    u as oe
} from "./DGJMLFjI.js";
import {
    a as re,
    o as ne
} from "./Jr9eiJio.js";
import {
    k as ie
} from "./C_QvFyDp.js";
import {
    N as le
} from "./nuQnue4a.js";
import {
    b as ae
} from "./CW991W2w.js";
import {
    d as se
} from "./CfHz9NLm.js";
import {
    i as ue
} from "./MpDLC7up.js";
import {
    a as de
} from "./B7VeW_-d.js";
import {
    N as ce
} from "./BjWUbj3w.js";
const he = t("download", (() => o("svg", {
        viewBox: "0 0 16 16",
        version: "1.1",
        xmlns: "http://www.w3.org/2000/svg"
    }, o("g", {
        stroke: "none",
        "stroke-width": "1",
        fill: "none",
        "fill-rule": "evenodd"
    }, o("g", {
        fill: "currentColor",
        "fill-rule": "nonzero"
    }, o("path", {
        d: "M3.5,13 L12.5,13 C12.7761424,13 13,13.2238576 13,13.5 C13,13.7454599 12.8231248,13.9496084 12.5898756,13.9919443 L12.5,14 L3.5,14 C3.22385763,14 3,13.7761424 3,13.5 C3,13.2545401 3.17687516,13.0503916 3.41012437,13.0080557 L3.5,13 L12.5,13 L3.5,13 Z M7.91012437,1.00805567 L8,1 C8.24545989,1 8.44960837,1.17687516 8.49194433,1.41012437 L8.5,1.5 L8.5,10.292 L11.1819805,7.6109127 C11.3555469,7.43734635 11.6249713,7.4180612 11.8198394,7.55305725 L11.8890873,7.6109127 C12.0626536,7.78447906 12.0819388,8.05390346 11.9469427,8.2487716 L11.8890873,8.31801948 L8.35355339,11.8535534 C8.17998704,12.0271197 7.91056264,12.0464049 7.7156945,11.9114088 L7.64644661,11.8535534 L4.1109127,8.31801948 C3.91565056,8.12275734 3.91565056,7.80617485 4.1109127,7.6109127 C4.28447906,7.43734635 4.55390346,7.4180612 4.7487716,7.55305725 L4.81801948,7.6109127 L7.5,10.292 L7.5,1.5 C7.5,1.25454011 7.67687516,1.05039163 7.91012437,1.00805567 L8,1 L7.91012437,1.00805567 Z"
    })))))),
    ve = r({
        name: "ResizeSmall",
        render: () => o("svg", {
            xmlns: "http://www.w3.org/2000/svg",
            viewBox: "0 0 20 20"
        }, o("g", {
            fill: "none"
        }, o("path", {
            d: "M5.5 4A1.5 1.5 0 0 0 4 5.5v1a.5.5 0 0 1-1 0v-1A2.5 2.5 0 0 1 5.5 3h1a.5.5 0 0 1 0 1h-1zM16 5.5A1.5 1.5 0 0 0 14.5 4h-1a.5.5 0 0 1 0-1h1A2.5 2.5 0 0 1 17 5.5v1a.5.5 0 0 1-1 0v-1zm0 9a1.5 1.5 0 0 1-1.5 1.5h-1a.5.5 0 0 0 0 1h1a2.5 2.5 0 0 0 2.5-2.5v-1a.5.5 0 0 0-1 0v1zm-12 0A1.5 1.5 0 0 0 5.5 16h1.25a.5.5 0 0 1 0 1H5.5A2.5 2.5 0 0 1 3 14.5v-1.25a.5.5 0 0 1 1 0v1.25zM8.5 7A1.5 1.5 0 0 0 7 8.5v3A1.5 1.5 0 0 0 8.5 13h3a1.5 1.5 0 0 0 1.5-1.5v-3A1.5 1.5 0 0 0 11.5 7h-3zM8 8.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-3z",
            fill: "currentColor"
        })))
    }),
    ge = t("rotateClockwise", (() => o("svg", {
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    }, o("path", {
        d: "M3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10C17 12.7916 15.3658 15.2026 13 16.3265V14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5V17.5C12 17.7761 12.2239 18 12.5 18H15.5C15.7761 18 16 17.7761 16 17.5C16 17.2239 15.7761 17 15.5 17H13.8758C16.3346 15.6357 18 13.0128 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 10.2761 2.22386 10.5 2.5 10.5C2.77614 10.5 3 10.2761 3 10Z",
        fill: "currentColor"
    }), o("path", {
        d: "M10 12C11.1046 12 12 11.1046 12 10C12 8.89543 11.1046 8 10 8C8.89543 8 8 8.89543 8 10C8 11.1046 8.89543 12 10 12ZM10 11C9.44772 11 9 10.5523 9 10C9 9.44772 9.44772 9 10 9C10.5523 9 11 9.44772 11 10C11 10.5523 10.5523 11 10 11Z",
        fill: "currentColor"
    })))),
    pe = t("rotateClockwise", (() => o("svg", {
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    }, o("path", {
        d: "M17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 12.7916 4.63419 15.2026 7 16.3265V14.5C7 14.2239 7.22386 14 7.5 14C7.77614 14 8 14.2239 8 14.5V17.5C8 17.7761 7.77614 18 7.5 18H4.5C4.22386 18 4 17.7761 4 17.5C4 17.2239 4.22386 17 4.5 17H6.12422C3.66539 15.6357 2 13.0128 2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10C18 10.2761 17.7761 10.5 17.5 10.5C17.2239 10.5 17 10.2761 17 10Z",
        fill: "currentColor"
    }), o("path", {
        d: "M10 12C8.89543 12 8 11.1046 8 10C8 8.89543 8.89543 8 10 8C11.1046 8 12 8.89543 12 10C12 11.1046 11.1046 12 10 12ZM10 11C10.5523 11 11 10.5523 11 10C11 9.44772 10.5523 9 10 9C9.44772 9 9 9.44772 9 10C9 10.5523 9.44772 11 10 11Z",
        fill: "currentColor"
    })))),
    me = t("zoomIn", (() => o("svg", {
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    }, o("path", {
        d: "M11.5 8.5C11.5 8.22386 11.2761 8 11 8H9V6C9 5.72386 8.77614 5.5 8.5 5.5C8.22386 5.5 8 5.72386 8 6V8H6C5.72386 8 5.5 8.22386 5.5 8.5C5.5 8.77614 5.72386 9 6 9H8V11C8 11.2761 8.22386 11.5 8.5 11.5C8.77614 11.5 9 11.2761 9 11V9H11C11.2761 9 11.5 8.77614 11.5 8.5Z",
        fill: "currentColor"
    }), o("path", {
        d: "M8.5 3C11.5376 3 14 5.46243 14 8.5C14 9.83879 13.5217 11.0659 12.7266 12.0196L16.8536 16.1464C17.0488 16.3417 17.0488 16.6583 16.8536 16.8536C16.68 17.0271 16.4106 17.0464 16.2157 16.9114L16.1464 16.8536L12.0196 12.7266C11.0659 13.5217 9.83879 14 8.5 14C5.46243 14 3 11.5376 3 8.5C3 5.46243 5.46243 3 8.5 3ZM8.5 4C6.01472 4 4 6.01472 4 8.5C4 10.9853 6.01472 13 8.5 13C10.9853 13 13 10.9853 13 8.5C13 6.01472 10.9853 4 8.5 4Z",
        fill: "currentColor"
    })))),
    fe = t("zoomOut", (() => o("svg", {
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    }, o("path", {
        d: "M11 8C11.2761 8 11.5 8.22386 11.5 8.5C11.5 8.77614 11.2761 9 11 9H6C5.72386 9 5.5 8.77614 5.5 8.5C5.5 8.22386 5.72386 8 6 8H11Z",
        fill: "currentColor"
    }), o("path", {
        d: "M14 8.5C14 5.46243 11.5376 3 8.5 3C5.46243 3 3 5.46243 3 8.5C3 11.5376 5.46243 14 8.5 14C9.83879 14 11.0659 13.5217 12.0196 12.7266L16.1464 16.8536L16.2157 16.9114C16.4106 17.0464 16.68 17.0271 16.8536 16.8536C17.0488 16.6583 17.0488 16.3417 16.8536 16.1464L12.7266 12.0196C13.5217 11.0659 14 9.83879 14 8.5ZM4 8.5C4 6.01472 6.01472 4 8.5 4C10.9853 4 13 6.01472 13 8.5C13 10.9853 10.9853 13 8.5 13C6.01472 13 4 10.9853 4 8.5Z",
        fill: "currentColor"
    })))),
    we = ue && "loading" in document.createElement("img");
const Ce = new WeakMap,
    be = new WeakMap,
    xe = new WeakMap,
    ye = (e, t, o) => {
        if (!e) return () => {};
        const r = function(e = {}) {
                var t;
                const {
                    root: o = null
                } = e;
                return {
                    hash: `${e.rootMargin||"0px 0px 0px 0px"}-${Array.isArray(e.threshold)?e.threshold.join(","):null!==(t=e.threshold)&&void 0!==t?t:"0"}`,
                    options: Object.assign(Object.assign({}, e), {
                        root: ("string" == typeof o ? document.querySelector(o) : o) || document.documentElement
                    })
                }
            }(t),
            {
                root: n
            } = r.options;
        let i;
        const l = Ce.get(n);
        let a, s;
        l ? i = l : (i = new Map, Ce.set(n, i)), i.has(r.hash) ? (s = i.get(r.hash), s[1].has(e) || (a = s[0], s[1].add(e), a.observe(e))) : (a = new IntersectionObserver((e => {
            e.forEach((e => {
                if (e.isIntersecting) {
                    const t = be.get(e.target),
                        o = xe.get(e.target);
                    t && t(), o && (o.value = !0)
                }
            }))
        }), r.options), a.observe(e), s = [a, new Set([e])], i.set(r.hash, s));
        let u = !1;
        const d = () => {
            u || (be.delete(e), xe.delete(e), u = !0, s[1].has(e) && (s[0].unobserve(e), s[1].delete(e)), s[1].size <= 0 && i.delete(r.hash), i.size || Ce.delete(n))
        };
        return be.set(e, d), xe.set(e, o), d
    };
const ke = n({
    name: "Image",
    common: l,
    peers: {
        Tooltip: i
    },
    self: function() {
        return {
            toolbarIconColor: "rgba(255, 255, 255, .9)",
            toolbarColor: "rgba(0, 0, 0, .35)",
            toolbarBoxShadow: "none",
            toolbarBorderRadius: "24px"
        }
    }
});

function Pe() {
    return o("svg", {
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    }, o("path", {
        d: "M6 5C5.75454 5 5.55039 5.17688 5.50806 5.41012L5.5 5.5V14.5C5.5 14.7761 5.72386 15 6 15C6.24546 15 6.44961 14.8231 6.49194 14.5899L6.5 14.5V5.5C6.5 5.22386 6.27614 5 6 5ZM13.8536 5.14645C13.68 4.97288 13.4106 4.9536 13.2157 5.08859L13.1464 5.14645L8.64645 9.64645C8.47288 9.82001 8.4536 10.0894 8.58859 10.2843L8.64645 10.3536L13.1464 14.8536C13.3417 15.0488 13.6583 15.0488 13.8536 14.8536C14.0271 14.68 14.0464 14.4106 13.9114 14.2157L13.8536 14.1464L9.70711 10L13.8536 5.85355C14.0488 5.65829 14.0488 5.34171 13.8536 5.14645Z",
        fill: "currentColor"
    }))
}

function Se() {
    return o("svg", {
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    }, o("path", {
        d: "M13.5 5C13.7455 5 13.9496 5.17688 13.9919 5.41012L14 5.5V14.5C14 14.7761 13.7761 15 13.5 15C13.2545 15 13.0504 14.8231 13.0081 14.5899L13 14.5V5.5C13 5.22386 13.2239 5 13.5 5ZM5.64645 5.14645C5.82001 4.97288 6.08944 4.9536 6.28431 5.08859L6.35355 5.14645L10.8536 9.64645C11.0271 9.82001 11.0464 10.0894 10.9114 10.2843L10.8536 10.3536L6.35355 14.8536C6.15829 15.0488 5.84171 15.0488 5.64645 14.8536C5.47288 14.68 5.4536 14.4106 5.58859 14.2157L5.64645 14.1464L9.79289 10L5.64645 5.85355C5.45118 5.65829 5.45118 5.34171 5.64645 5.14645Z",
        fill: "currentColor"
    }))
}

function Ie() {
    return o("svg", {
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    }, o("path", {
        d: "M4.089 4.216l.057-.07a.5.5 0 0 1 .638-.057l.07.057L10 9.293l5.146-5.147a.5.5 0 0 1 .638-.057l.07.057a.5.5 0 0 1 .057.638l-.057.07L10.707 10l5.147 5.146a.5.5 0 0 1 .057.638l-.057.07a.5.5 0 0 1-.638.057l-.07-.057L10 10.707l-5.146 5.147a.5.5 0 0 1-.638.057l-.07-.057a.5.5 0 0 1-.057-.638l.057-.07L9.293 10L4.146 4.854a.5.5 0 0 1-.057-.638l.057-.07l-.057.07z",
        fill: "currentColor"
    }))
}
const Le = Object.assign(Object.assign({}, a.props), {
        onPreviewPrev: Function,
        onPreviewNext: Function,
        showToolbar: {
            type: Boolean,
            default: !0
        },
        showToolbarTooltip: Boolean,
        renderToolbar: Function
    }),
    Oe = s("n-image"),
    Me = u([u("body >", [d("image-container", "position: fixed;")]), d("image-preview-container", "\n position: fixed;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n display: flex;\n "), d("image-preview-overlay", "\n z-index: -1;\n position: absolute;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n background: rgba(0, 0, 0, .3);\n ", [J()]), d("image-preview-toolbar", "\n z-index: 1;\n position: absolute;\n left: 50%;\n transform: translateX(-50%);\n border-radius: var(--n-toolbar-border-radius);\n height: 48px;\n bottom: 40px;\n padding: 0 12px;\n background: var(--n-toolbar-color);\n box-shadow: var(--n-toolbar-box-shadow);\n color: var(--n-toolbar-icon-color);\n transition: color .3s var(--n-bezier);\n display: flex;\n align-items: center;\n ", [d("base-icon", "\n padding: 0 8px;\n font-size: 28px;\n cursor: pointer;\n "), J()]), d("image-preview-wrapper", "\n position: absolute;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n display: flex;\n pointer-events: none;\n ", [K()]), d("image-preview", "\n user-select: none;\n -webkit-user-select: none;\n pointer-events: all;\n margin: auto;\n max-height: calc(100vh - 32px);\n max-width: calc(100vw - 32px);\n transition: transform .3s var(--n-bezier);\n "), d("image", "\n display: inline-flex;\n max-height: 100%;\n max-width: 100%;\n ", [c("preview-disabled", "\n cursor: pointer;\n "), u("img", "\n border-radius: inherit;\n ")])]),
    Te = r({
        name: "ImagePreview",
        props: Object.assign(Object.assign({}, Le), {
            onNext: Function,
            onPrev: Function,
            clsPrefix: {
                type: String,
                required: !0
            }
        }),
        setup(e) {
            const t = a("Image", "-image", Me, ke, e, f(e, "clsPrefix"));
            let r = null;
            const n = w(null),
                i = w(null),
                l = w(void 0),
                s = w(!1),
                u = w(!1),
                {
                    localeRef: d
                } = oe("Image");

            function c(t) {
                var o, r;
                switch (t.key) {
                    case " ":
                        t.preventDefault();
                        break;
                    case "ArrowLeft":
                        null === (o = e.onPrev) || void 0 === o || o.call(e);
                        break;
                    case "ArrowRight":
                        null === (r = e.onNext) || void 0 === r || r.call(e);
                        break;
                    case "Escape":
                        H()
                }
            }
            C(s, (e => {
                e ? ne("keydown", document, c) : re("keydown", document, c)
            })), b((() => {
                re("keydown", document, c)
            }));
            let h = 0,
                v = 0,
                g = 0,
                p = 0,
                m = 0,
                L = 0,
                O = 0,
                M = 0,
                T = !1;

            function z(e) {
                const {
                    clientX: t,
                    clientY: o
                } = e;
                g = t - h, p = o - v, ae(_)
            }

            function B(e) {
                const {
                    value: t
                } = n;
                if (!t) return {
                    offsetX: 0,
                    offsetY: 0
                };
                const o = t.getBoundingClientRect(),
                    {
                        moveVerticalDirection: r,
                        moveHorizontalDirection: i,
                        deltaHorizontal: l,
                        deltaVertical: a
                    } = e || {};
                let s = 0,
                    u = 0;
                return s = o.width <= window.innerWidth ? 0 : o.left > 0 ? (o.width - window.innerWidth) / 2 : o.right < window.innerWidth ? -(o.width - window.innerWidth) / 2 : "horizontalRight" === i ? Math.min((o.width - window.innerWidth) / 2, m - (null != l ? l : 0)) : Math.max(-(o.width - window.innerWidth) / 2, m - (null != l ? l : 0)), u = o.height <= window.innerHeight ? 0 : o.top > 0 ? (o.height - window.innerHeight) / 2 : o.bottom < window.innerHeight ? -(o.height - window.innerHeight) / 2 : "verticalBottom" === r ? Math.min((o.height - window.innerHeight) / 2, L - (null != a ? a : 0)) : Math.max(-(o.height - window.innerHeight) / 2, L - (null != a ? a : 0)), {
                    offsetX: s,
                    offsetY: u
                }
            }

            function D(e) {
                re("mousemove", document, z), re("mouseup", document, D);
                const {
                    clientX: t,
                    clientY: o
                } = e;
                T = !1;
                const r = function(e) {
                        const {
                            mouseUpClientX: t,
                            mouseUpClientY: o,
                            mouseDownClientX: r,
                            mouseDownClientY: n
                        } = e, i = r - t, l = n - o;
                        return {
                            moveVerticalDirection: "vertical" + (l > 0 ? "Top" : "Bottom"),
                            moveHorizontalDirection: "horizontal" + (i > 0 ? "Left" : "Right"),
                            deltaHorizontal: i,
                            deltaVertical: l
                        }
                    }({
                        mouseUpClientX: t,
                        mouseUpClientY: o,
                        mouseDownClientX: O,
                        mouseDownClientY: M
                    }),
                    n = B(r);
                g = n.offsetX, p = n.offsetY, _()
            }
            const R = P(Oe, null);
            let E = 0,
                W = 1,
                N = 0;

            function j() {
                W = 1, E = 0
            }

            function $() {
                const {
                    value: e
                } = n;
                if (!e) return 1;
                const {
                    innerWidth: t,
                    innerHeight: o
                } = window, r = e.naturalHeight / (o - 32), i = e.naturalWidth / (t - 32);
                return r < 1 && i < 1 ? 1 : Math.max(r, i)
            }

            function _(e = !0) {
                var t;
                const {
                    value: o
                } = n;
                if (!o) return;
                const {
                    style: r
                } = o, i = I(null === (t = null == R ? void 0 : R.previewedImgPropsRef.value) || void 0 === t ? void 0 : t.style);
                let l = "";
                if ("string" == typeof i) l = `${i};`;
                else
                    for (const n in i) l += `${ie(n)}: ${i[n]};`;
                const a = `transform-origin: center; transform: translateX(${g}px) translateY(${p}px) rotate(${N}deg) scale(${W});`;
                r.cssText = T ? `${l}cursor: grabbing; transition: none;${a}` : `${l}cursor: grab;${a}${e?"":"transition: none;"}`, e || o.offsetHeight
            }

            function H() {
                s.value = !s.value, u.value = !0
            }
            const A = {
                setPreviewSrc: e => {
                    l.value = e
                },
                setThumbnailEl: e => {
                    r = e
                },
                toggleShow: H
            };
            const U = x((() => {
                    const {
                        common: {
                            cubicBezierEaseInOut: e
                        },
                        self: {
                            toolbarIconColor: o,
                            toolbarBorderRadius: r,
                            toolbarBoxShadow: n,
                            toolbarColor: i
                        }
                    } = t.value;
                    return {
                        "--n-bezier": e,
                        "--n-toolbar-icon-color": o,
                        "--n-toolbar-color": i,
                        "--n-toolbar-border-radius": r,
                        "--n-toolbar-box-shadow": n
                    }
                })),
                {
                    inlineThemeDisabled: F
                } = y(),
                Z = F ? k("image-preview", void 0, U, e) : void 0;
            return Object.assign({
                previewRef: n,
                previewWrapperRef: i,
                previewSrc: l,
                show: s,
                appear: S(),
                displayed: u,
                previewedImgProps: null == R ? void 0 : R.previewedImgPropsRef,
                handleWheel(e) {
                    e.preventDefault()
                },
                handlePreviewMousedown: function(e) {
                    var t, o;
                    if (null === (o = null === (t = null == R ? void 0 : R.previewedImgPropsRef.value) || void 0 === t ? void 0 : t.onMousedown) || void 0 === o || o.call(t, e), 0 !== e.button) return;
                    const {
                        clientX: r,
                        clientY: n
                    } = e;
                    T = !0, h = r - g, v = n - p, m = g, L = p, O = r, M = n, _(), ne("mousemove", document, z), ne("mouseup", document, D)
                },
                handlePreviewDblclick: function(e) {
                    var t, o;
                    null === (o = null === (t = null == R ? void 0 : R.previewedImgPropsRef.value) || void 0 === t ? void 0 : t.onDblclick) || void 0 === o || o.call(t, e);
                    const r = $();
                    W = W === r ? 1 : r, _()
                },
                syncTransformOrigin: function() {
                    const {
                        value: e
                    } = i;
                    if (!r || !e) return;
                    const {
                        style: t
                    } = e, o = r.getBoundingClientRect(), n = o.left + o.width / 2, l = o.top + o.height / 2;
                    t.transformOrigin = `${n}px ${l}px`
                },
                handleAfterLeave: () => {
                    j(), N = 0, u.value = !1
                },
                handleDragStart: e => {
                    var t, o;
                    null === (o = null === (t = null == R ? void 0 : R.previewedImgPropsRef.value) || void 0 === t ? void 0 : t.onDragstart) || void 0 === o || o.call(t, e), e.preventDefault()
                },
                zoomIn: function() {
                    const e = function() {
                        const {
                            value: e
                        } = n;
                        if (!e) return 1;
                        const {
                            innerWidth: t,
                            innerHeight: o
                        } = window, r = Math.max(1, e.naturalHeight / (o - 32)), i = Math.max(1, e.naturalWidth / (t - 32));
                        return Math.max(3, 2 * r, 2 * i)
                    }();
                    W < e && (E += 1, W = Math.min(e, Math.pow(1.5, E)), _())
                },
                zoomOut: function() {
                    if (W > .5) {
                        const e = W;
                        E -= 1, W = Math.max(.5, Math.pow(1.5, E));
                        const t = e - W;
                        _(!1);
                        const o = B();
                        W += t, _(!1), W -= t, g = o.offsetX, p = o.offsetY, _()
                    }
                },
                handleDownloadClick: function() {
                    const e = l.value;
                    e && se(e, void 0)
                },
                rotateCounterclockwise: function() {
                    N -= 90, _()
                },
                rotateClockwise: function() {
                    N += 90, _()
                },
                handleSwitchPrev: function() {
                    var t;
                    j(), N = 0, null === (t = e.onPrev) || void 0 === t || t.call(e)
                },
                handleSwitchNext: function() {
                    var t;
                    j(), N = 0, null === (t = e.onNext) || void 0 === t || t.call(e)
                },
                withTooltip: function(r, n) {
                    if (e.showToolbarTooltip) {
                        const {
                            value: e
                        } = t;
                        return o(le, {
                            to: !1,
                            theme: e.peers.Tooltip,
                            themeOverrides: e.peerOverrides.Tooltip,
                            keepAliveOnHover: !1
                        }, {
                            default: () => d.value[n],
                            trigger: () => r
                        })
                    }
                    return r
                },
                resizeToOrignalImageSize: function() {
                    W = $(), E = Math.ceil(Math.log(W) / Math.log(1.5)), g = 0, p = 0, _()
                },
                cssVars: F ? void 0 : U,
                themeClass: null == Z ? void 0 : Z.themeClass,
                onRender: null == Z ? void 0 : Z.onRender
            }, A)
        },
        render() {
            var e, t;
            const {
                clsPrefix: r,
                renderToolbar: n,
                withTooltip: i
            } = this, l = i(o(h, {
                clsPrefix: r,
                onClick: this.handleSwitchPrev
            }, {
                default: Pe
            }), "tipPrevious"), a = i(o(h, {
                clsPrefix: r,
                onClick: this.handleSwitchNext
            }, {
                default: Se
            }), "tipNext"), s = i(o(h, {
                clsPrefix: r,
                onClick: this.rotateCounterclockwise
            }, {
                default: () => o(pe, null)
            }), "tipCounterclockwise"), u = i(o(h, {
                clsPrefix: r,
                onClick: this.rotateClockwise
            }, {
                default: () => o(ge, null)
            }), "tipClockwise"), d = i(o(h, {
                clsPrefix: r,
                onClick: this.resizeToOrignalImageSize
            }, {
                default: () => o(ve, null)
            }), "tipOriginalSize"), c = i(o(h, {
                clsPrefix: r,
                onClick: this.zoomOut
            }, {
                default: () => o(fe, null)
            }), "tipZoomOut"), f = i(o(h, {
                clsPrefix: r,
                onClick: this.handleDownloadClick
            }, {
                default: () => o(he, null)
            }), "tipDownload"), w = i(o(h, {
                clsPrefix: r,
                onClick: this.toggleShow
            }, {
                default: Ie
            }), "tipClose"), C = i(o(h, {
                clsPrefix: r,
                onClick: this.zoomIn
            }, {
                default: () => o(me, null)
            }), "tipZoomIn");
            return o(v, null, null === (t = (e = this.$slots).default) || void 0 === t ? void 0 : t.call(e), o(ee, {
                show: this.show
            }, {
                default: () => {
                    var e;
                    return this.show || this.displayed ? (null === (e = this.onRender) || void 0 === e || e.call(this), g(o("div", {
                        class: [`${r}-image-preview-container`, this.themeClass],
                        style: this.cssVars,
                        onWheel: this.handleWheel
                    }, o(p, {
                        name: "fade-in-transition",
                        appear: this.appear
                    }, {
                        default: () => this.show ? o("div", {
                            class: `${r}-image-preview-overlay`,
                            onClick: this.toggleShow
                        }) : null
                    }), this.showToolbar ? o(p, {
                        name: "fade-in-transition",
                        appear: this.appear
                    }, {
                        default: () => this.show ? o("div", {
                            class: `${r}-image-preview-toolbar`
                        }, n ? n({
                            nodes: {
                                prev: l,
                                next: a,
                                rotateCounterclockwise: s,
                                rotateClockwise: u,
                                resizeToOriginalSize: d,
                                zoomOut: c,
                                zoomIn: C,
                                download: f,
                                close: w
                            }
                        }) : o(v, null, this.onPrev ? o(v, null, l, a) : null, s, u, d, c, C, f, w)) : null
                    }) : null, o(p, {
                        name: "fade-in-scale-up-transition",
                        onAfterLeave: this.handleAfterLeave,
                        appear: this.appear,
                        onEnter: this.syncTransformOrigin,
                        onBeforeLeave: this.syncTransformOrigin
                    }, {
                        default: () => {
                            const {
                                previewedImgProps: e = {}
                            } = this;
                            return g(o("div", {
                                class: `${r}-image-preview-wrapper`,
                                ref: "previewWrapperRef"
                            }, o("img", Object.assign({}, e, {
                                draggable: !1,
                                onMousedown: this.handlePreviewMousedown,
                                onDblclick: this.handlePreviewDblclick,
                                class: [`${r}-image-preview`, e.class],
                                key: this.previewSrc,
                                src: this.previewSrc,
                                ref: "previewRef",
                                onDragstart: this.handleDragStart
                            }))), [
                                [m, this.show]
                            ])
                        }
                    })), [
                        [te, {
                            enabled: this.show
                        }]
                    ])) : null
                }
            }))
        }
    }),
    ze = s("n-image-group"),
    Be = r({
        name: "ImageGroup",
        props: Le,
        setup(e) {
            let t;
            const {
                mergedClsPrefixRef: o
            } = y(e), r = `c${L()}`, n = M(), i = w(null), l = e => {
                var o;
                t = e, null === (o = i.value) || void 0 === o || o.setPreviewSrc(e)
            };

            function a(o) {
                var i, a;
                if (!(null == n ? void 0 : n.proxy)) return;
                const s = n.proxy.$el.parentElement.querySelectorAll(`[data-group-id=${r}]:not([data-error=true])`);
                if (!s.length) return;
                const u = Array.from(s).findIndex((e => e.dataset.previewSrc === t));
                l(~u ? s[(u + o + s.length) % s.length].dataset.previewSrc : s[0].dataset.previewSrc), 1 === o ? null === (i = e.onPreviewNext) || void 0 === i || i.call(e) : null === (a = e.onPreviewPrev) || void 0 === a || a.call(e)
            }
            return O(ze, {
                mergedClsPrefixRef: o,
                setPreviewSrc: l,
                setThumbnailEl: e => {
                    var t;
                    null === (t = i.value) || void 0 === t || t.setThumbnailEl(e)
                },
                toggleShow: () => {
                    var e;
                    null === (e = i.value) || void 0 === e || e.toggleShow()
                },
                groupId: r,
                renderToolbarRef: f(e, "renderToolbar")
            }), {
                mergedClsPrefix: o,
                previewInstRef: i,
                next: () => {
                    a(1)
                },
                prev: () => {
                    a(-1)
                }
            }
        },
        render() {
            return o(Te, {
                theme: this.theme,
                themeOverrides: this.themeOverrides,
                clsPrefix: this.mergedClsPrefix,
                ref: "previewInstRef",
                onPrev: this.prev,
                onNext: this.next,
                showToolbar: this.showToolbar,
                showToolbarTooltip: this.showToolbarTooltip,
                renderToolbar: this.renderToolbar
            }, this.$slots)
        }
    }),
    De = r({
        name: "Image",
        props: Object.assign({
            alt: String,
            height: [String, Number],
            imgProps: Object,
            previewedImgProps: Object,
            lazy: Boolean,
            intersectionObserverOptions: Object,
            objectFit: {
                type: String,
                default: "fill"
            },
            previewSrc: String,
            fallbackSrc: String,
            width: [String, Number],
            src: String,
            previewDisabled: Boolean,
            loadDescription: String,
            onError: Function,
            onLoad: Function
        }, Le),
        slots: Object,
        inheritAttrs: !1,
        setup(e) {
            const t = w(null),
                o = w(!1),
                r = w(null),
                n = P(ze, null),
                {
                    mergedClsPrefixRef: i
                } = n || y(e),
                l = {
                    click: () => {
                        if (e.previewDisabled || o.value) return;
                        const i = e.previewSrc || e.src;
                        if (n) return n.setPreviewSrc(i), n.setThumbnailEl(t.value), void n.toggleShow();
                        const {
                            value: l
                        } = r;
                        l && (l.setPreviewSrc(i), l.setThumbnailEl(t.value), l.toggleShow())
                    }
                },
                a = w(!e.lazy);
            T((() => {
                var e;
                null === (e = t.value) || void 0 === e || e.setAttribute("data-group-id", (null == n ? void 0 : n.groupId) || "")
            })), T((() => {
                if (e.lazy && e.intersectionObserverOptions) {
                    let o;
                    const r = z((() => {
                        null == o || o(), o = void 0, o = ye(t.value, e.intersectionObserverOptions, a)
                    }));
                    b((() => {
                        r(), null == o || o()
                    }))
                }
            })), z((() => {
                var t;
                e.src || null === (t = e.imgProps) || void 0 === t || t.src, o.value = !1
            }));
            const s = w(!1);
            return O(Oe, {
                previewedImgPropsRef: f(e, "previewedImgProps")
            }), Object.assign({
                mergedClsPrefix: i,
                groupId: null == n ? void 0 : n.groupId,
                previewInstRef: r,
                imageRef: t,
                showError: o,
                shouldStartLoading: a,
                loaded: s,
                mergedOnClick: t => {
                    var o, r;
                    l.click(), null === (r = null === (o = e.imgProps) || void 0 === o ? void 0 : o.onClick) || void 0 === r || r.call(o, t)
                },
                mergedOnError: t => {
                    if (!a.value) return;
                    o.value = !0;
                    const {
                        onError: r,
                        imgProps: {
                            onError: n
                        } = {}
                    } = e;
                    null == r || r(t), null == n || n(t)
                },
                mergedOnLoad: t => {
                    const {
                        onLoad: o,
                        imgProps: {
                            onLoad: r
                        } = {}
                    } = e;
                    null == o || o(t), null == r || r(t), s.value = !0
                }
            }, l)
        },
        render() {
            var e, t;
            const {
                mergedClsPrefix: r,
                imgProps: n = {},
                loaded: i,
                $attrs: l,
                lazy: a
            } = this, s = de(this.$slots.error, (() => [])), u = null === (t = (e = this.$slots).placeholder) || void 0 === t ? void 0 : t.call(e), d = this.src || n.src, c = this.showError && s.length ? s : o("img", Object.assign(Object.assign({}, n), {
                ref: "imageRef",
                width: this.width || n.width,
                height: this.height || n.height,
                src: this.showError ? this.fallbackSrc : a && this.intersectionObserverOptions ? this.shouldStartLoading ? d : void 0 : d,
                alt: this.alt || n.alt,
                "aria-label": this.alt || n.alt,
                onClick: this.mergedOnClick,
                onError: this.mergedOnError,
                onLoad: this.mergedOnLoad,
                loading: we && a && !this.intersectionObserverOptions ? "lazy" : "eager",
                style: [n.style || "", u && !i ? {
                    height: "0",
                    width: "0",
                    visibility: "hidden"
                } : "", {
                    objectFit: this.objectFit
                }],
                "data-error": this.showError,
                "data-preview-src": this.previewSrc || this.src
            }));
            return o("div", Object.assign({}, l, {
                role: "none",
                class: [l.class, `${r}-image`, (this.previewDisabled || this.showError) && `${r}-image--preview-disabled`]
            }), this.groupId ? c : o(Te, {
                theme: this.theme,
                themeOverrides: this.themeOverrides,
                clsPrefix: r,
                ref: "previewInstRef",
                showToolbar: this.showToolbar,
                showToolbarTooltip: this.showToolbarTooltip,
                renderToolbar: this.renderToolbar
            }, {
                default: () => c
            }), !i && u)
        }
    }),
    Re = {
        name: "ImageGenerating",
        components: {
            WhiteLoadingIcon: G,
            NImage: De,
            NImageGroup: Be,
            PricingWindowForPlusPlan: Q,
            NModal: ce
        },
        props: {
            taskId: {
                type: String,
                required: !1,
                default: ""
            },
            status: {
                type: String,
                required: !1,
                default: ""
            },
            interval: {
                type: Number,
                default: 2e3
            },
            srcSetIndex: {
                type: Number,
                default: 1
            },
            displayOriginalImage: {
                type: Boolean,
                default: !1
            },
            backgroundImageUrl: {
                type: String,
                required: !1,
                default: ""
            },
            hiddenRunning: {
                type: Boolean,
                default: !1
            },
            reverse: {
                type: Boolean,
                default: !1
            },
            imageUrls: {
                type: Array,
                required: !1,
                default: []
            },
            onlyOneImage: {
                type: Boolean,
                default: !1
            },
            enablePreview: {
                type: Boolean,
                default: !0
            },
            aspectRatio: {
                type: [String, Number],
                default: "3/4"
            },
            enableBackgroundColor: {
                type: Boolean,
                default: !0
            },
            enableBreathingOpacity: {
                type: Boolean,
                default: !1
            },
            cndSrcSets: {
                type: [Object, Array],
                default: []
            },
            layoutClass: {
                type: String,
                default: ""
            },
            minWidth: {
                type: Number,
                required: !1,
                default: null
            },
            minHeight: {
                type: Number,
                required: !1,
                default: null
            },
            showSlowQueue: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["updated", "item-click", "finished"],
        setup(e, {
            emit: t
        }) {
            const o = w(null),
                r = w(e.reverse ? [...e.imageUrls].reverse() : e.imageUrls);
            e.onlyOneImage && e.imageUrls && e.imageUrls.length > 0 && (r.value = [r.value[0]]);
            const n = w([]),
                i = w(e.cndSrcSets),
                l = w(0),
                a = w(r.value.length > 0),
                s = w("NSFW" === e.status),
                u = w("FAILURE" === e.status || s.value),
                d = w(null),
                c = w(null),
                h = w(!1),
                v = w(null),
                g = w(new URL("data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20viewBox='0%200%2032%2032'%3e%3cpath%20d='M19%2014a3%203%200%201%200-3-3a3%203%200%200%200%203%203zm0-4a1%201%200%201%201-1%201a1%201%200%200%201%201-1z'%20fill='currentColor'%3e%3c/path%3e%3cpath%20d='M26%204H6a2%202%200%200%200-2%202v20a2%202%200%200%200%202%202h20a2%202%200%200%200%202-2V6a2%202%200%200%200-2-2zm0%2022H6v-6l5-5l5.59%205.59a2%202%200%200%200%202.82%200L21%2019l5%205zm0-4.83l-3.59-3.59a2%202%200%200%200-2.82%200L18%2019.17l-5.59-5.59a2%202%200%200%200-2.82%200L6%2017.17V6h20z'%20fill='currentColor'%3e%3c/path%3e%3c/svg%3e",
                    import.meta.url).href),
                p = w(e.backgroundImageUrl),
                m = w(null),
                f = w(!1),
                b = x((() => 1 === r.value.length)),
                y = x((() => {
                    if (e.layoutClass) return e.layoutClass;
                    const t = r.value.length;
                    return 9 === t ? "grid-3x3" : 1 === t ? "" : "grid-2-cols"
                })),
                k = async () => {
                    var t;
                    if (!e.hiddenRunning && !p.value) try {
                        const o = await X(e.taskId);
                        (null == (t = o.additional_info) ? void 0 : t.model_image_url) && (p.value = o.additional_info.model_image_url), m.value = o
                    } catch (o) {}
                },
                P = x((() => e => {
                    const t = "_t=buster";
                    return e ? e.includes("?") ? `${e}&${t}` : `${e}?${t}` : ""
                })),
                S = async o => {
                    var n, d, g, p;
                    if (o instanceof Error) return void(u.value = !0);
                    const f = o.status;
                    if (c.value = o, o.id && (h.value = !0), "CREATED" === f) l.value = 0;
                    else if ("PENDING" === f) !o.queue_position || 0 === o.queue_position || null !== v.value && void 0 !== v.value || (v.value = o.queue_position), l.value = 10 + (o.queue_position ? Math.floor(39 * (v.value - o.queue_position) / v.value) : 0);
                    else if ("PICKED" === f) l.value = 50;
                    else if ("RUNNING" === f) "EXECUTION_START" === o.status_tip ? l.value = 60 : "DENOISING" === o.status_tip ? l.value = 60 + (o.progress ? Math.floor(20 * o.progress) : 0) : "UPLOADING_IMAGE" === o.status_tip && (l.value = 80 + (o.progress ? Math.floor(10 * o.progress) : 0));
                    else if ("SUCCESS" === f) {
                        let s = e.onlyOneImage ? 1 : 9,
                            c = (null == (n = o.image_urls) ? void 0 : n.slice(0, s)) || [];
                        if (e.reverse && (c = [...c].reverse()), l.value = 98, c.length > 0) {
                            const n = new Image;
                            n.crossOrigin = "anonymous", n.onload = () => {
                                r.value = c, i.value = o.cnd_src_sets || {}, l.value = 100, a.value = !0, o.firstImage = n, t("finished", o)
                            }, n.src = e.displayOriginalImage ? c[0] : (null == (g = null == (d = o.cnd_src_sets) ? void 0 : d[c[0]]) ? void 0 : g[e.srcSetIndex]) || (null == (p = o.cnd_src_sets) ? void 0 : p[c[0]]) || c[0]
                        } else u.value = !0, t("finished", o)
                    } else "FAILURE" !== f && "NSFW" !== f || (u.value = !0, "NSFW" === f && (s.value = !0), t("finished", o));
                    t("updated", o), m.value = o
                },
                I = () => {
                    d.value && d.value.stop(), e.taskId && (d.value = Y(e.taskId, S, e.interval))
                };
            C((() => e.taskId), (async e => {
                e && (await k(), I())
            })), T((async () => {
                if (0 === e.imageUrls.length && e.taskId) {
                    if (await k(), u.value) return;
                    I()
                }
            })), V((() => {
                d.value && d.value.stop()
            }));
            const L = x((() => ({
                    aspectRatio: e.aspectRatio,
                    ...e.minWidth ? {
                        minWidth: e.minWidth + "px"
                    } : {},
                    ...e.minHeight ? {
                        minHeight: e.minHeight + "px"
                    } : {}
                }))),
                O = x((() => e.hiddenRunning));
            return {
                task: c,
                handleItemClick: o => {
                    t("item-click", c.value ? c.value : {
                        id: e.taskId,
                        image_urls: e.imageUrls,
                        cnd_src_sets: i.value,
                        additional_info: {
                            model_image_url: p.value
                        }
                    }, o)
                },
                progress_percent: l,
                isCompleted: a,
                hasError: u,
                isNSFW: s,
                localImageUrls: r,
                localSrcSet: n,
                isSingleImage: b,
                gridClass: y,
                defaultBackgroundUrl: g,
                currentBackgroundUrl: p,
                handlePreview: t => {
                    if (!e.enablePreview) return;
                    q.log("handlePreview", t);
                    const r = o.value.querySelectorAll(".n-image img");
                    r[t] && r[t].click()
                },
                mainImageItem: o,
                maskStyle: L,
                isTaskCreated: h,
                localCndSrcSets: i,
                shouldHideComponent: O,
                taskDetailData: m,
                showPricingWindowForPlusPlan: f,
                showPlusPricingWindow: () => {
                    f.value = !0
                },
                getUrlWithCacheBuster: P
            }
        }
    },
    Ee = {
        key: 0,
        class: "main-image-mask-blur"
    },
    We = {
        key: 2,
        class: "generation-error"
    },
    Ne = {
        key: 0,
        class: "error-text"
    },
    je = {
        key: 1,
        class: "error-text"
    },
    $e = ["src"],
    _e = {
        key: 1,
        class: "image-generated"
    },
    He = ["src", "onClick"],
    Ae = ["src"];
const Ue = B(Re, [
    ["render", function(t, o, r, n, i, l) {
        var a, s, u;
        const d = e,
            c = F("n-image"),
            h = F("n-image-group"),
            g = F("PricingWindowForPlusPlan"),
            p = F("NModal");
        return R(), D("div", {
            class: $(["main-image-item", {
                "error-status": n.hasError,
                "nsfw-status": n.isNSFW
            }]),
            ref: "mainImageItem",
            style: I({
                display: n.shouldHideComponent ? "none" : "flex"
            })
        }, [n.isCompleted ? E("", !0) : (R(), D("div", {
            key: 0,
            class: "main-image-mask",
            style: I(n.maskStyle)
        }, [n.isNSFW ? (R(), D("div", Ee)) : E("", !0), !n.hasError && n.isTaskCreated ? (R(), D("div", {
            key: 1,
            class: "remixing-progress",
            style: I({
                backgroundColor: r.enableBackgroundColor ? "rgba(0, 0, 0, 0.1)" : "transparent"
            })
        }, [W(d), N(" " + j(n.progress_percent) + "% ", 1)], 4)) : E("", !0), n.hasError ? (R(), D("div", We, [n.isNSFW ? (R(), D("span", Ne, "NSFW")) : (R(), D("span", je, "Failure"))])) : E("", !0), r.enableBackgroundColor ? (R(), D("img", {
            key: 3,
            src: n.currentBackgroundUrl || n.defaultBackgroundUrl,
            class: $({
                "breathing-opacity": !n.hasError && r.enableBreathingOpacity,
                "error-blur": n.hasError || !r.enableBreathingOpacity
            })
        }, null, 10, $e)) : E("", !0)], 4)), n.isCompleted && !n.hasError ? (R(), D("div", _e, [_("div", {
            class: $(["image-grid", n.gridClass]),
            onClick: o[0] || (o[0] = (...e) => n.handleItemClick && n.handleItemClick(...e))
        }, [(R(!0), D(v, null, H(n.localImageUrls, ((e, t) => {
            var o, i, l;
            return R(), D("img", {
                key: e,
                src: r.displayOriginalImage ? e : (null == (i = null == (o = n.localCndSrcSets) ? void 0 : o[e]) ? void 0 : i[r.srcSetIndex]) || (null == (l = n.localCndSrcSets) ? void 0 : l[r.srcSetIndex]) || e,
                onClick: () => n.handlePreview(t)
            }, null, 8, He)
        })), 128)), (R(!0), D(v, null, H(n.localImageUrls, ((e, t) => (R(), D("img", {
            key: `cb-${e}-${t}`,
            style: {
                display: "none"
            },
            crossorigin: "anonymous",
            src: n.getUrlWithCacheBuster(e)
        }, null, 8, Ae)))), 128)), W(h, {
            "show-toolbar": !1
        }, {
            default: A((() => [(R(!0), D(v, null, H(n.localImageUrls, ((e, t) => (R(), U(c, {
                key: `preview-${e}-${t}`,
                src: e,
                style: {
                    display: "none"
                },
                crossorigin: "anonymous",
                "show-toolbar": !1,
                "preset-preview-src": e
            }, null, 8, ["src", "preset-preview-src"])))), 128))])),
            _: 1
        })], 2)])) : E("", !0), r.showSlowQueue && !(null == (a = n.taskDetailData) ? void 0 : a.is_locked) && "normal" === (null == (s = n.taskDetailData) ? void 0 : s.queue_priority) && "PENDING" === (null == (u = n.taskDetailData) ? void 0 : u.status) ? (R(), D("div", {
            key: 2,
            onClick: o[1] || (o[1] = Z(((...e) => n.showPlusPricingWindow && n.showPlusPricingWindow(...e)), ["stop"])),
            class: "absolute top-0 bottom-0 left-0 right-0 flex flex-col items-center bg-black/40"
        }, o[4] || (o[4] = [_("div", {
            class: "mt-[71px] text-center text-white text-sm font-normal font-['Arial'] leading-[21px]"
        }, [N(" High Demand,"), _("br"), N(" Generation in Queue. ")], -1), _("div", {
            class: "absolute left-[9px] right-[9px] bottom-[6px] py-[9px] bg-[#0f7fff] rounded-xl border-2 justify-center items-center gap-2 flex text-center text-white text-xs font-normal font-['Arial'] leading-[18px]"
        }, " Fast Generations ", -1)]))) : E("", !0), W(p, {
            show: n.showPricingWindowForPlusPlan,
            "onUpdate:show": o[3] || (o[3] = e => n.showPricingWindowForPlusPlan = e)
        }, {
            default: A((() => [W(g, {
                "window-type": "standard",
                onClose: o[2] || (o[2] = e => n.showPricingWindowForPlusPlan = !1)
            })])),
            _: 1
        }, 8, ["show"])], 6)
    }],
    ["__scopeId", "data-v-baaac5b5"]
]);
export {
    he as D, Ue as I, De as N, Be as a
};