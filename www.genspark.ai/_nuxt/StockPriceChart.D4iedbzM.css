.stock-chart[data-v-bde6a6e6] {
    border-radius: 12px;
    width: 100%
}

.price-summary[data-v-bde6a6e6] {
    margin-bottom: 1rem
}

.current-price[data-v-bde6a6e6] {
    align-items: flex-start;
    flex-direction: column
}

.company-symbol[data-v-bde6a6e6],
.current-price[data-v-bde6a6e6] {
    display: flex;
    justify-content: space-between;
    width: 100%
}

.company-symbol[data-v-bde6a6e6] {
    align-items: center
}

.company-logo-container[data-v-bde6a6e6] {
    align-items: center;
    display: flex;
    flex-direction: row;
    width: 100%
}

.symbol-text[data-v-bde6a6e6] {
    color: #222;
    font-weight: 700
}

.last-updated[data-v-bde6a6e6] {
    color: #777;
    font-size: .85rem;
    position: relative;
    right: 0
}

.logo-container[data-v-bde6a6e6] {
    align-items: center;
    background-color: #fff;
    border: none;
    border-radius: 50%;
    display: flex;
    height: 48px;
    justify-content: center;
    margin-right: 1rem;
    overflow: hidden;
    width: 48px
}

.company-logo[data-v-bde6a6e6] {
    max-height: 80%;
    max-width: 80%;
    -o-object-fit: contain;
    object-fit: contain
}

.price-container[data-v-bde6a6e6] {
    align-items: flex-end;
    display: flex;
    flex-direction: row;
    gap: 20px;
    margin-top: 20px
}

.price[data-v-bde6a6e6] {
    color: #222;
    font-size: 42px;
    line-height: 1
}

.price[data-v-bde6a6e6],
.unit[data-v-bde6a6e6] {
    font-weight: 700
}

.unit[data-v-bde6a6e6] {
    color: #909499
}

.price-change[data-v-bde6a6e6] {
    align-items: center;
    display: flex;
    font-size: 16px;
    font-weight: 700;
    margin-top: 10px
}

.positive[data-v-bde6a6e6] {
    color: #00a651;
    font-weight: 700
}

.negative[data-v-bde6a6e6] {
    color: #e31837;
    font-weight: 700
}

.timeframe[data-v-bde6a6e6] {
    color: #777;
    font-weight: 400;
    margin-left: .5rem
}

.chart-section[data-v-bde6a6e6] {
    position: relative
}

.chart-controls[data-v-bde6a6e6] {
    display: flex;
    gap: 2.5rem;
    justify-content: flex-start;
    margin-bottom: 1.5rem
}

.chart-controls button[data-v-bde6a6e6] {
    background-color: transparent;
    border: none;
    color: #888;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    padding: .25rem 0;
    position: relative
}

.chart-controls button.active[data-v-bde6a6e6] {
    color: #333;
    font-weight: 700
}

.chart-controls button.active[data-v-bde6a6e6]:after {
    background-color: #333;
    border-radius: 1.5px;
    bottom: -4px;
    content: "";
    height: 3px;
    left: 0;
    position: absolute;
    right: 0
}

.chart-container[data-v-bde6a6e6] {
    background-color: transparent;
    margin-bottom: 35px;
    min-height: 300px;
    position: relative;
    width: 100%
}

.section-border[data-v-bde6a6e6] {
    border-bottom: 1px solid #e8e8e8;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem
}

.stock-performance[data-v-bde6a6e6] {
    margin-top: 1rem
}

.performance-grid[data-v-bde6a6e6] {
    display: grid;
    gap: .75rem;
    grid-template-columns: repeat(3, 1fr)
}

.performance-item[data-v-bde6a6e6] {
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 1rem
}

.performance-item .label[data-v-bde6a6e6] {
    color: #909499;
    font-size: 14px;
    font-weight: 400;
    text-align: left
}

.performance-item .value[data-v-bde6a6e6] {
    color: #606366;
    font-size: 14px;
    font-weight: 700;
    text-align: right
}

@media (max-width:768px) {
    .performance-item[data-v-bde6a6e6] {
        flex-direction: column
    }
    .performance-item .label[data-v-bde6a6e6],
    .performance-item .value[data-v-bde6a6e6] {
        text-align: center
    }
    .chart-controls[data-v-bde6a6e6] {
        display: flex;
        gap: 1.2rem;
        justify-content: flex-start;
        margin-bottom: 1.5rem
    }
}

@media (prefers-color-scheme:dark) {
    .stock-chart[data-v-bde6a6e6] {
        background-color: #1a1a1a
    }
    .price[data-v-bde6a6e6],
    .symbol-text[data-v-bde6a6e6] {
        color: #fff
    }
    .performance-item[data-v-bde6a6e6] {
        background-color: #222;
        border-color: #333
    }
    .chart-controls button.active[data-v-bde6a6e6],
    .performance-item .value[data-v-bde6a6e6] {
        color: #fff
    }
    .chart-controls button.active[data-v-bde6a6e6]:after {
        background-color: #fff
    }
    .section-border[data-v-bde6a6e6] {
        border-bottom-color: #333
    }
}

.chart-controls button[data-v-bde6a6e6]:disabled {
    cursor: not-allowed;
    opacity: .5
}

.price-summary .loading[data-v-bde6a6e6] {
    height: 100px
}

.price-summary .loading[data-v-bde6a6e6],
.stock-performance .loading[data-v-bde6a6e6] {
    align-items: center;
    display: flex;
    justify-content: center
}

.stock-performance .loading[data-v-bde6a6e6] {
    height: 200px
}

.skeleton-block[data-v-bde6a6e6] {
    animation: shimmer-bde6a6e6 1.5s infinite;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: 4px;
    width: 100%
}

.price-summary .skeleton-block[data-v-bde6a6e6] {
    height: 100px
}

.chart-container .skeleton-block[data-v-bde6a6e6] {
    height: 400px
}

.stock-performance .skeleton-block[data-v-bde6a6e6] {
    height: 200px
}

@keyframes shimmer-bde6a6e6 {
    0% {
        background-position: 200% 0
    }
    to {
        background-position: -200% 0
    }
}

@media (prefers-color-scheme:dark) {
    .skeleton-block[data-v-bde6a6e6] {
        background: linear-gradient(90deg, #333 25%, #444, #333 75%);
        background-size: 200% 100%
    }
}