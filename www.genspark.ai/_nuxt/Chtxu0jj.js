import {
    d as o,
    o as r,
    b as t
} from "./Cf0SOiw0.js";
const n = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const e = {
        render: function(e, s) {
            return r(), o("svg", n, s[0] || (s[0] = [t("path", {
                d: "M8 21H5C4.44772 21 4 20.5523 4 20V4C4 3.44771 4.44772 3 5 3H19C19.5523 3 20 3.44771 20 4V21H16.5",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), t("path", {
                d: "M12.25 21V14.5",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), t("path", {
                d: "M10 15.25L10.75 14.5L12.25 13L13.75 14.5L14.5 15.25",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), t("path", {
                d: "M8 6H16",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round"
            }, null, -1)]))
        }
    },
    s = {
        width: "11",
        height: "8",
        viewBox: "0 0 11 8",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const i = {
    render: function(n, e) {
        return r(), o("svg", s, e[0] || (e[0] = [t("path", {
            d: "M1 3.63365L4 6.79403L10 1",
            stroke: "white",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }, null, -1)]))
    }
};
export {
    i as C, e as I
};