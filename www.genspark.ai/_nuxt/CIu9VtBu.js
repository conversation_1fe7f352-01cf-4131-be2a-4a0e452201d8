import {
    d as o,
    b as t,
    o as e
} from "./Cf0SOiw0.js";
const r = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const i = {
    render: function(i, n) {
        return e(), o("svg", r, n[0] || (n[0] = [t("g", {
            id: "Frame"
        }, [t("path", {
            id: "Vector",
            d: "M7 5V19",
            stroke: "white",
            "stroke-width": "2.18182",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }), t("path", {
            id: "Vector_2",
            d: "M17 5V19",
            stroke: "white",
            "stroke-width": "2.18182",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        })], -1)]))
    }
};
export {
    i as P
};