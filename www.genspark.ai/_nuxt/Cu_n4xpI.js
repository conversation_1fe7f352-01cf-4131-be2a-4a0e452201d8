import {
    _ as a,
    s as e,
    c as i,
    r as n,
    v as o,
    i as t,
    a as s,
    w as l,
    C as c,
    E as r,
    o as d,
    b as u,
    d as p,
    f as m,
    t as v,
    n as h,
    H as _,
    L as g,
    F as f,
    k,
    e as w,
    l as b,
    aj as y,
    y as C,
    U as z
} from "./Cf0SOiw0.js";
import {
    B as U
} from "./DAjjhrgi.js";
import {
    N as $
} from "./BjWUbj3w.js";
const L = {
        name: "PersonalizationDialog",
        components: {
            NModal: $,
            NButton: U
        },
        props: {
            show: {
                type: Boolean,
                default: !1
            },
            initialNickname: {
                type: String,
                default: null
            },
            initialOccupation: {
                type: String,
                default: null
            },
            initialTraits: {
                type: String,
                default: null
            },
            initialUserProfile: {
                type: String,
                default: null
            },
            initialAdditionalInstructions: {
                type: String,
                default: null
            }
        },
        emits: ["update:show", "personalization_settings_saved", "autoResearch"],
        setup(a, {
            emit: s
        }) {
            const {
                t: l
            } = e(), r = i({
                get: () => a.show,
                set: a => s("update:show", a)
            }), d = n(""), u = n(""), p = n(""), m = n(""), v = n(""), h = n(!1), _ = n(!1), g = t("currentUser"), f = [l("components.personalization_dialog.chatty"), l("components.personalization_dialog.witty"), l("components.personalization_dialog.straight_shooting"), l("components.personalization_dialog.encouraging"), l("components.personalization_dialog.gen_z"), l("components.personalization_dialog.skeptical"), l("components.personalization_dialog.traditional"), l("components.personalization_dialog.forward_thinking"), l("components.personalization_dialog.poetic")], k = async () => {
                _.value = !0;
                let e = {};
                try {
                    const a = await fetch("/api/personalization/get"),
                        i = await a.json();
                    0 === i.status && (e = i)
                } catch (i) {} finally {
                    ((e = {}) => {
                        c.log("initializeFormFields", e);
                        const i = e.custom_instructions || {};
                        d.value = a.initialNickname ? ? (i.nickname || ""), u.value = a.initialOccupation ? ? (i.occupation || ""), p.value = a.initialTraits ? ? (i.traits || ""), m.value = a.initialUserProfile ? ? (i.user_profile || ""), v.value = a.initialAdditionalInstructions ? ? (i.additional_instructions || ""), c.log("Initialized fields. Props:", {
                            initialNickname: a.initialNickname,
                            initialOccupation: a.initialOccupation,
                            initialTraits: a.initialTraits,
                            initialUserProfile: a.initialUserProfile,
                            initialAdditionalInstructions: a.initialAdditionalInstructions
                        }, "Fetched:", i, "Result:", {
                            nickname: d.value,
                            occupation: u.value,
                            traits: p.value,
                            user_profile: m.value,
                            additional_instructions: v.value
                        })
                    })(e), _.value = !1
                }
            };
            o((() => a.show), (a => {
                a && k()
            }));
            return {
                closeDialog: () => {
                    r.value = !1
                },
                showDialog: r,
                nickname: d,
                occupation: u,
                traits: p,
                additional_instructions: v,
                user_profile: m,
                traitOptions: f,
                addTrait: a => {
                    p.value ? p.value.includes(a) || (p.value += p.value.endsWith(".") ? " " : p.value.length > 0 ? ", " : "", p.value += a) : p.value = a
                },
                saveCustomInstructions: () => {
                    h.value = !0;
                    const a = {
                        nickname: d.value,
                        occupation: u.value,
                        traits: p.value,
                        user_profile: m.value,
                        additional_instructions: v.value
                    };
                    var e;
                    (e = {
                        custom_instructions: a
                    }, fetch("/api/personalization/update", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify(e)
                    }).then((a => a.json())).then((a => (c.log("update personalization setting", a), a))).catch((a => {
                        throw a
                    }))).then((a => {
                        c.log("Custom instructions details saved", a), s("personalization_settings_saved"), r.value = !1
                    })).catch((a => {})).finally((() => {
                        h.value = !1
                    }))
                },
                isSaving: h,
                isLoading: _,
                handleAutoResearch: () => {
                    s("autoResearch"), r.value = !1
                },
                currentUser: g,
                handleInstructionSectionClick: a => {
                    var e;
                    a.stopPropagation(), "free" === (null == (e = null == g ? void 0 : g.value) ? void 0 : e.plan) && c.windowopen("/pricing")
                }
            }
        }
    },
    V = {
        class: "setting_dialog"
    },
    x = {
        class: "content max-h-[calc(100vh-88px)] overflow-y-auto p-[20px] md:p-[24px] w-full box-border"
    },
    I = {
        class: "title"
    },
    P = {
        class: "icon-title"
    },
    S = {
        key: 0,
        class: "loading-overlay"
    },
    R = {
        class: "form-content"
    },
    D = {
        class: "instruction-section"
    },
    A = {
        class: "section-title"
    },
    N = ["placeholder", "disabled"],
    O = {
        class: "instruction-section"
    },
    T = {
        class: "section-title"
    },
    j = ["placeholder", "disabled"],
    B = {
        class: "instruction-section"
    },
    F = {
        class: "section-title"
    },
    M = ["placeholder", "disabled"],
    q = {
        class: "instruction-section"
    },
    E = {
        class: "section-title"
    },
    H = ["placeholder", "disabled"],
    J = {
        class: "traits-buttons"
    },
    W = ["onClick"],
    G = {
        class: "instruction-section"
    },
    K = {
        class: "section-title"
    },
    Q = {
        class: "section-title-text"
    },
    X = {
        class: "textarea-container"
    },
    Y = ["placeholder", "disabled"],
    Z = {
        class: "action-buttons w-full pb-[24px] pr-[24px] box-border"
    },
    aa = {
        key: 0
    },
    ea = {
        key: 1,
        class: "loading-spinner"
    };
const ia = a(L, [
        ["render", function(a, e, i, n, o, t) {
            const c = r("n-modal");
            return d(), s(c, {
                show: n.showDialog,
                "onUpdate:show": e[11] || (e[11] = a => n.showDialog = a),
                "mask-closable": !1,
                "close-on-esc": !1
            }, {
                default: l((() => {
                    var i, o, t, s, l;
                    return [u("div", V, [u("div", x, [u("div", I, [u("span", P, v(a.$t("components.menu.personalization")), 1), u("span", {
                        class: h(["click-button", {
                            loading: n.isSaving || n.isLoading,
                            disabled: n.isLoading
                        }]),
                        onClick: e[0] || (e[0] = (...a) => n.handleAutoResearch && n.handleAutoResearch(...a))
                    }, v(a.$t("components.research_me_modal.auto_research")), 3)]), n.isLoading ? (d(), p("div", S, e[12] || (e[12] = [u("div", {
                        class: "loading-spinner"
                    }, null, -1)]))) : m("", !0), u("div", R, [u("div", D, [u("div", A, v(a.$t("components.menu.what-should-genspark-call-you")), 1), _(u("input", {
                        "onUpdate:modelValue": e[1] || (e[1] = a => n.nickname = a),
                        placeholder: a.$t("components.menu.nickname"),
                        class: "input-field",
                        disabled: n.isLoading
                    }, null, 8, N), [
                        [g, n.nickname]
                    ])]), u("div", O, [u("div", T, v(a.$t("components.menu.what-do-you-do")), 1), _(u("input", {
                        "onUpdate:modelValue": e[2] || (e[2] = a => n.occupation = a),
                        placeholder: a.$t("components.menu.occupation"),
                        class: "input-field",
                        disabled: n.isLoading
                    }, null, 8, j), [
                        [g, n.occupation]
                    ])]), u("div", B, [u("div", F, v(a.$t("components.menu.user-profile")), 1), _(u("textarea", {
                        "onUpdate:modelValue": e[3] || (e[3] = a => n.user_profile = a),
                        placeholder: a.$t("components.menu.user-profile-description"),
                        rows: "3",
                        class: "input-field",
                        disabled: n.isLoading
                    }, null, 8, M), [
                        [g, n.user_profile]
                    ])]), u("div", q, [u("div", E, v(a.$t("components.menu.what-traits-should-genspark-have")), 1), _(u("input", {
                        "onUpdate:modelValue": e[4] || (e[4] = a => n.traits = a),
                        placeholder: a.$t("components.menu.trait-placeholder"),
                        class: "input-field",
                        disabled: n.isLoading
                    }, null, 8, H), [
                        [g, n.traits]
                    ]), u("div", J, [(d(!0), p(f, null, k(n.traitOptions, (a => (d(), p("div", {
                        key: a,
                        class: h(["trait-button", {
                            disabled: n.isLoading
                        }]),
                        onClick: e => !n.isLoading && n.addTrait(a)
                    }, " + " + v(a), 11, W)))), 128))])]), u("div", G, [u("div", K, [u("div", Q, v(a.$t("components.menu.additional-instructions")), 1), "plus" == (null == (i = n.currentUser) ? void 0 : i.plan) || "pro" == (null == (o = n.currentUser) ? void 0 : o.plan) ? (d(), p("div", {
                        key: 0,
                        class: "plan",
                        onClick: e[5] || (e[5] = e => a.utils.windowopen("/pricing"))
                    }, " Plus ")) : m("", !0), "free" == (null == (t = n.currentUser) ? void 0 : t.plan) ? (d(), p("div", {
                        key: 1,
                        class: "plan free",
                        onClick: e[6] || (e[6] = (...a) => n.handleInstructionSectionClick && n.handleInstructionSectionClick(...a))
                    }, " Upgrade to Plus ")) : m("", !0)]), u("div", X, [_(u("textarea", {
                        "onUpdate:modelValue": e[7] || (e[7] = a => n.additional_instructions = a),
                        placeholder: a.$t("components.menu.additional-instructions"),
                        rows: "3",
                        class: "input-field",
                        disabled: n.isLoading || "free" == (null == (s = n.currentUser) ? void 0 : s.plan)
                    }, null, 8, Y), [
                        [g, n.additional_instructions]
                    ]), "free" == (null == (l = n.currentUser) ? void 0 : l.plan) ? (d(), p("div", {
                        key: 0,
                        class: "clickable-overlay",
                        onClick: e[8] || (e[8] = (...a) => n.handleInstructionSectionClick && n.handleInstructionSectionClick(...a))
                    })) : m("", !0)])])])]), u("div", Z, [u("div", {
                        class: "cancel-button",
                        onClick: e[9] || (e[9] = (...a) => n.closeDialog && n.closeDialog(...a))
                    }, v(a.$t("components.menu.cancel")), 1), u("div", {
                        class: h(["click-button", {
                            loading: n.isSaving || n.isLoading,
                            disabled: n.isLoading
                        }]),
                        onClick: e[10] || (e[10] = a => !n.isLoading && n.saveCustomInstructions())
                    }, [n.isSaving || n.isLoading ? (d(), p("span", ea)) : (d(), p("span", aa, v(a.$t("components.menu.save")), 1))], 2)])])]
                })),
                _: 1
            }, 8, ["show"])
        }],
        ["__scopeId", "data-v-de72a1c3"]
    ]),
    na = {
        class: "modal-header"
    },
    oa = {
        class: "modal-title"
    },
    ta = {
        class: "modal-body"
    },
    sa = {
        class: "description"
    },
    la = ["placeholder"],
    ca = {
        class: "action-buttons"
    },
    ra = a({
        __name: "ResearchMeModal",
        props: {
            modelValue: {
                type: Boolean,
                required: !0
            },
            showPersonalizationDialog: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["update:modelValue", "manualInput", "autoResearch", "update:showPersonalizationDialog"],
        setup(a, {
            emit: l
        }) {
            const {
                t: c
            } = e(), r = a, h = l, k = t("currentUser", {}), U = n(""), $ = n(null), L = i({
                get: () => r.showPersonalizationDialog,
                set: a => {
                    h("update:showPersonalizationDialog", a)
                }
            });
            o((() => r.modelValue), (a => {
                !0 === a && z((() => {
                    var a;
                    null == (a = $.value) || a.focus()
                }))
            }), {
                immediate: !0
            });
            const V = () => {
                    const a = U.value;
                    h("update:modelValue", !1), z((() => {
                        L.value = !0
                    })), h("manualInput", a)
                },
                x = () => {
                    const a = c("components.research_me_modal.my_information") + " " + U.value + " " + c("components.research_me_modal.research_me_and_create_profile");
                    h("autoResearch", a), h("update:modelValue", !1)
                },
                I = () => {
                    h("update:modelValue", !0)
                },
                P = () => {
                    h("update:modelValue", !1), h("autoResearch", c("components.research_me_modal.my_information_updated"))
                };
            return (e, i) => (d(), p(f, null, [(d(), s(y, {
                to: "body"
            }, [a.modelValue ? (d(), p("div", {
                key: 0,
                class: "research-modal",
                onClick: i[6] || (i[6] = b((a => e.$emit("update:modelValue", !1)), ["self"]))
            }, [u("div", {
                class: "modal-backdrop",
                onClick: i[0] || (i[0] = b((a => e.$emit("update:modelValue", !1)), ["stop"]))
            }), u("div", {
                class: "modal-panel",
                onClick: i[5] || (i[5] = b((() => {}), ["stop"])),
                role: "dialog",
                "aria-modal": "true"
            }, [u("div", na, [u("button", {
                onClick: i[1] || (i[1] = b((a => e.$emit("update:modelValue", !1)), ["stop"])),
                class: "close-btn"
            }, i[8] || (i[8] = [u("svg", {
                width: "24",
                height: "24",
                viewBox: "0 0 24 24",
                fill: "none"
            }, [u("path", {
                d: "M18 6L6 18M6 6L18 18",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round"
            })], -1)]))]), u("div", oa, [u("h2", null, v(e.$t("components.research_me_modal.let_genspark_know_you")), 1)]), u("div", ta, [u("div", sa, v(e.$t("components.research_me_modal.more_personalized_responses")), 1), _(u("textarea", {
                ref_key: "inputAreaRef",
                ref: $,
                "onUpdate:modelValue": i[2] || (i[2] = a => U.value = a),
                class: "input-area",
                placeholder: e.$t("components.research_me_modal.paste_linkedin_url_twitter_url_name_company_or_anything_about_you"),
                onClick: i[3] || (i[3] = b((() => {}), ["stop"])),
                onFocus: i[4] || (i[4] = b((() => {}), ["stop"]))
            }, null, 40, la), [
                [g, U.value]
            ]), u("div", ca, [u("button", {
                onClick: b(V, ["stop"]),
                class: "btn-manual"
            }, v(e.$t("components.research_me_modal.manual_input")), 1), u("button", {
                onClick: b(x, ["stop"]),
                class: "btn-auto"
            }, v(e.$t("components.research_me_modal.auto_research")), 1)])])])])) : m("", !0)])), w(ia, {
                show: L.value,
                "onUpdate:show": i[7] || (i[7] = a => L.value = a),
                currentUser: C(k),
                onPersonalization_settings_saved: P,
                onAutoResearch: I
            }, null, 8, ["show", "currentUser"])], 64))
        }
    }, [
        ["__scopeId", "data-v-8be1f376"]
    ]);
export {
    ia as P, ra as R
};