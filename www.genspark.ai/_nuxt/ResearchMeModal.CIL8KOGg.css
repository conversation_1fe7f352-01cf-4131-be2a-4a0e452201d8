.setting_dialog[data-v-de72a1c3] {
    align-items: center;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 3px 30px #00000014;
    box-sizing: border-box;
    color: #232425;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    justify-content: flex-start;
    max-height: 90vh;
    max-width: 93%;
    overflow-y: auto;
    width: 512px
}

.setting_dialog .close[data-v-de72a1c3] {
    color: #606366;
    cursor: pointer;
    display: flex;
    height: 32px;
    justify-content: flex-end;
    position: absolute;
    right: 8px;
    top: 8px;
    width: 100%;
    width: 32px
}

.setting_dialog .title[data-v-de72a1c3] {
    color: #232425;
    display: flex;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    gap: 12px;
    line-height: 36px;
    margin-bottom: 20px;
    margin-top: 20px;
    width: 100%
}

.setting_dialog .content[data-v-de72a1c3] {
    width: 100%
}

.section-title[data-v-de72a1c3] {
    font-weight: 600;
    margin-bottom: 8px
}

.section-title[data-v-de72a1c3],
.session-title[data-v-de72a1c3] {
    display: flex;
    flex-direction: row;
    font-size: 14px
}

.session-title[data-v-de72a1c3] {
    color: #232425;
    line-height: normal
}

.plan[data-v-de72a1c3],
.session-title[data-v-de72a1c3] {
    font-style: normal;
    font-weight: 700
}

.plan[data-v-de72a1c3] {
    align-items: center;
    background: #0f7fff;
    border-radius: 57px;
    box-sizing: border-box;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-size: 12px;
    gap: 10px;
    height: 20px;
    justify-content: center;
    line-height: 150%;
    margin-left: 12px;
    padding: 0 8px;
    text-align: center
}

.plan.free[data-v-de72a1c3] {
    background: linear-gradient(122deg, #93deff -23.63%, #edf2ff 50.74%, #deddff 115%);
    color: #0b5cb9;
    margin-left: 12px
}

.form-content[data-v-de72a1c3] {
    width: 100%
}

.instruction-section[data-v-de72a1c3] {
    margin-bottom: 24px
}

.input-field[data-v-de72a1c3] {
    border-radius: 8px;
    margin-bottom: 8px;
    width: 100%
}

.traits-buttons[data-v-de72a1c3] {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px
}

.trait-button[data-v-de72a1c3] {
    background-color: #f5f5f5;
    border-radius: 16px;
    cursor: pointer;
    font-size: 13px;
    padding: 4px 12px;
    transition: background-color .2s
}

.trait-button[data-v-de72a1c3]:hover {
    background-color: #e0e0e0
}

.action-buttons[data-v-de72a1c3] {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    margin-top: 24px
}

.title .click-button[data-v-de72a1c3] {
    font-size: 14px;
    width: 120px
}

.click-button[data-v-de72a1c3],
.title .click-button[data-v-de72a1c3] {
    align-items: center;
    cursor: pointer;
    display: flex;
    height: 40px;
    justify-content: center;
    position: relative
}

.click-button[data-v-de72a1c3] {
    background-color: #000;
    border-radius: 12px;
    color: #fff;
    width: 96px
}

.click-button.loading[data-v-de72a1c3] {
    cursor: default;
    opacity: .8
}

input[data-v-de72a1c3] {
    border: 1px solid #efefef;
    border-radius: 12px;
    box-sizing: border-box;
    color: #232425;
    font-size: 14px;
    height: 42px;
    padding: 12px;
    width: 100%
}

input[data-v-de72a1c3]:focus {
    border-color: #0f7fff
}

textarea[data-v-de72a1c3] {
    border: 1px solid #efefef;
    border-radius: 12px;
    box-sizing: border-box;
    color: #232425;
    font-size: 14px;
    padding: 12px;
    resize: none;
    width: 100%
}

textarea[data-v-de72a1c3]:focus {
    border-color: #0f7fff
}

.loading-spinner[data-v-de72a1c3] {
    animation: spin-de72a1c3 .8s linear infinite;
    border: 2px solid hsla(0, 0%, 100%, .3);
    border-radius: 50%;
    border-top-color: #fff;
    display: inline-block;
    height: 16px;
    width: 16px
}

@keyframes spin-de72a1c3 {
    to {
        transform: rotate(1turn)
    }
}

.cancel-button[data-v-de72a1c3] {
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 12px;
    color: #606366;
    cursor: pointer;
    display: flex;
    height: 40px;
    justify-content: center;
    width: 96px
}

@media (max-width:768px) {
    .setting_dialog[data-v-de72a1c3] {
        width: 90%
    }
    .setting_dialog .title[data-v-de72a1c3] {
        font-size: 20px;
        width: 100%
    }
}

@media (prefers-color-scheme:dark) {
    .setting_dialog[data-v-de72a1c3] {
        background: #232425;
        color: #fff
    }
    .setting_dialog .title[data-v-de72a1c3] {
        color: #fff
    }
    .setting_dialog .close[data-v-de72a1c3] {
        color: #606366
    }
    .trait-button[data-v-de72a1c3] {
        background-color: #333
    }
    .trait-button[data-v-de72a1c3]:hover {
        background-color: #444
    }
}

.loading-indicator[data-v-de72a1c3] {
    padding: 20px;
    text-align: center
}

.content[data-v-de72a1c3] {
    position: relative;
    width: 100%
}

.loading-overlay[data-v-de72a1c3] {
    align-items: center;
    background-color: #ffffffb3;
    border-radius: 8px;
    bottom: 0;
    display: flex;
    justify-content: center;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 10
}

.loading-overlay .loading-spinner[data-v-de72a1c3] {
    border: 3px solid rgba(0, 0, 0, .2);
    border-top-color: #000;
    height: 24px;
    width: 24px
}

.input-field[data-v-de72a1c3]:disabled,
textarea[data-v-de72a1c3]:disabled {
    background-color: #f0f0f0;
    cursor: not-allowed
}

.click-button.disabled[data-v-de72a1c3],
.trait-button.disabled[data-v-de72a1c3] {
    cursor: not-allowed;
    opacity: .6
}

@media (prefers-color-scheme:dark) {
    .loading-overlay[data-v-de72a1c3] {
        background-color: #282828b3
    }
    .loading-overlay .loading-spinner[data-v-de72a1c3] {
        border: 3px solid hsla(0, 0%, 100%, .2);
        border-top-color: #fff
    }
    .input-field[data-v-de72a1c3]:disabled,
    textarea[data-v-de72a1c3]:disabled {
        background-color: #444
    }
}

.textarea-container[data-v-de72a1c3] {
    position: relative;
    width: 100%
}

.clickable-overlay[data-v-de72a1c3] {
    cursor: pointer;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 5
}

.research-modal[data-v-8be1f376] {
    align-items: center;
    display: flex;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    justify-content: center;
    position: fixed;
    z-index: 100
}

.modal-backdrop[data-v-8be1f376] {
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    background-color: #0006;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    position: absolute
}

.modal-panel[data-v-8be1f376] {
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 10px 25px #0003;
    max-width: 90vw;
    position: relative;
    transform: scale(1);
    transition: all .3s ease-out;
    width: 600px
}

.modal-header[data-v-8be1f376] {
    display: flex;
    height: 20px;
    justify-content: flex-end;
    padding: 12px 12px 0
}

.modal-title h2[data-v-8be1f376] {
    color: #232425;
    font-family: Arial, sans-serif;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
    text-align: center
}

.close-btn[data-v-8be1f376] {
    background: none;
    border: none;
    color: #909499;
    cursor: pointer;
    transition: color .2s
}

.close-btn[data-v-8be1f376]:hover {
    color: #232425
}

.modal-body[data-v-8be1f376] {
    padding: 6px 16px 24px
}

.description[data-v-8be1f376] {
    color: #909499;
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 16px;
    text-align: center
}

.input-area[data-v-8be1f376] {
    background-color: #fff;
    border: 1px solid #eaeaea;
    border-radius: 12px;
    box-sizing: border-box;
    color: #232425;
    height: 140px;
    outline: none;
    padding: 12px;
    resize: none;
    transition: all .2s;
    width: 100%
}

.input-area[data-v-8be1f376]::-moz-placeholder {
    color: #606366;
    opacity: .6
}

.input-area[data-v-8be1f376]::placeholder {
    color: #606366;
    opacity: .6
}

.input-area[data-v-8be1f376]:focus,
.input-area[data-v-8be1f376]:hover {
    border-color: #eaeaea
}

.action-buttons[data-v-8be1f376] {
    display: flex;
    gap: 16px;
    justify-content: space-between;
    margin-top: 10px
}

.action-buttons button[data-v-8be1f376] {
    border: none;
    border-radius: 12px;
    cursor: pointer;
    flex: 1;
    font-weight: 400;
    padding: 8px 24px;
    transition: background-color .2s
}

.btn-manual[data-v-8be1f376] {
    background-color: #f4f4f4;
    color: #606366
}

.btn-manual[data-v-8be1f376]:hover {
    background-color: #e4e4e4
}

.btn-auto[data-v-8be1f376] {
    background-color: #232425;
    color: #fff
}

.btn-auto[data-v-8be1f376]:hover {
    background-color: #000
}

@media (prefers-color-scheme:dark) {
    .modal-panel[data-v-8be1f376] {
        background-color: #232425
    }
    .modal-header[data-v-8be1f376] {
        border-bottom-color: #333
    }
    .close-btn[data-v-8be1f376]:hover,
    .modal-header h2[data-v-8be1f376],
    .modal-title h2[data-v-8be1f376] {
        color: #fff
    }
    .input-area[data-v-8be1f376] {
        background-color: #232425;
        border-color: #333;
        color: #fff
    }
    .btn-manual[data-v-8be1f376] {
        background-color: #333;
        color: #fff
    }
    .btn-manual[data-v-8be1f376]:hover {
        background-color: #444
    }
    .btn-auto[data-v-8be1f376] {
        background-color: #fff;
        color: #232425
    }
    .btn-auto[data-v-8be1f376]:hover {
        background-color: #e4e4e4
    }
}