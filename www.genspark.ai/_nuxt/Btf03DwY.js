import {
    _ as a,
    r as e,
    c as l,
    v as s,
    h as t,
    a3 as i,
    a as o,
    o as u,
    w as v,
    d as n,
    f as d,
    b as r,
    y as c,
    t as h,
    a4 as p
} from "./Cf0SOiw0.js";
import {
    u as m
} from "./C1MFiWVQ.js";
const g = {
        key: 0,
        class: "global-audio-player"
    },
    y = {
        class: "player-content"
    },
    b = ["disabled"],
    f = {
        key: 0,
        class: "loading-spinner"
    },
    w = {
        key: 1,
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "currentColor"
    },
    k = {
        key: 2,
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "currentColor"
    },
    C = {
        class: "podcast-time"
    },
    P = a({
        __name: "GlobalAudioPlayer",
        setup(a) {
            const {
                togglePlay: P,
                stop: _,
                formatTime: x,
                cleanup: z,
                addListener: B,
                isPlaying: L,
                currentTime: M,
                duration: T,
                currentPodcast: j,
                isLoading: G,
                selectedPlayer: V
            } = m();
            e(null);
            const A = e(!1),
                F = e(!1),
                H = l((() => "native" === (null == V ? void 0 : V.value))),
                I = () => {
                    P()
                },
                q = () => {
                    _(), z(), setTimeout((() => {
                        A.value = !1, F.value = !1
                    }), 100)
                };
            let D;
            return s((() => j.value), (a => {
                a && !F.value && (F.value = !0, A.value = !0)
            }), {
                immediate: !0
            }), s((() => L.value), (a => {
                a && !F.value && (F.value = !0, A.value = !0)
            })), s((() => null == V ? void 0 : V.value), ((a, e) => {})), s((() => A.value), ((a, e) => {})), s((() => H.value), ((a, e) => {})), t((() => {
                D = B((a => {
                    a.currentPodcast && !F.value && (F.value = !0, A.value = !0)
                }))
            })), i((() => {
                D && D()
            })), (a, e) => (u(), o(p, {
                name: "slide-up"
            }, {
                default: v((() => [A.value && !H.value ? (u(), n("div", g, [r("div", y, [r("button", {
                    onClick: I,
                    class: "play-btn",
                    disabled: c(G)
                }, [c(G) ? (u(), n("div", f)) : c(L) ? (u(), n("svg", k, e[1] || (e[1] = [r("path", {
                    d: "M6 19h4V5H6v14zm8-14v14h4V5h-4z"
                }, null, -1)]))) : (u(), n("svg", w, e[0] || (e[0] = [r("path", {
                    d: "M8 5v14l11-7z"
                }, null, -1)])))], 8, b), r("div", C, h(c(x)(c(M))) + "/" + h(c(x)(c(T))), 1), r("button", {
                    onClick: q,
                    class: "close-btn",
                    title: "Close"
                }, e[2] || (e[2] = [r("svg", {
                    width: "16",
                    height: "16",
                    viewBox: "0 0 24 24",
                    fill: "currentColor"
                }, [r("path", {
                    d: "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                })], -1)]))])])) : d("", !0)])),
                _: 1
            }))
        }
    }, [
        ["__scopeId", "data-v-672e600c"]
    ]);
export {
    P as G
};