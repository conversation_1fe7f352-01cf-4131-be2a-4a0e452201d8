import {
    _ as e,
    s,
    d as o,
    b as a,
    t as n,
    y as t,
    o as r
} from "./Cf0SOiw0.js";
const i = {
        class: "company-info"
    },
    l = {
        class: "view-more"
    },
    p = ["href"],
    m = e({
        __name: "ViewCompanyDetail",
        props: {
            symbol: {
                type: String,
                required: !0
            }
        },
        setup(e) {
            const {
                t: m
            } = s();
            return (s, c) => (r(), o("div", i, [a("div", l, [a("a", {
                href: `/finance/company/${e.symbol}`,
                target: "_blank",
                class: "more-info-link"
            }, [a("span", null, n(t(m)("pages.finance.company.view_more")), 1), c[0] || (c[0] = a("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                width: "16",
                height: "16",
                viewBox: "0 0 24 24",
                fill: "none",
                stroke: "currentColor",
                "stroke-width": "2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, [a("path", {
                d: "M5 12h14"
            }), a("path", {
                d: "m12 5 7 7-7 7"
            })], -1))], 8, p)])]))
        }
    }, [
        ["__scopeId", "data-v-8463e375"]
    ]);
export {
    m as
    default
};