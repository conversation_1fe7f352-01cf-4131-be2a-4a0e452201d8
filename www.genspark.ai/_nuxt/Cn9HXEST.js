import {
    d as e,
    b as t,
    o as s,
    _ as a,
    r as n,
    s as l,
    v as o,
    x as i,
    h as r,
    C as u,
    f as c,
    n as p,
    F as d,
    k as m,
    t as g,
    e as v,
    l as y,
    w as h,
    i as f,
    E as x,
    a as w,
    S as k,
    c as C,
    H as S,
    L as b
} from "./Cf0SOiw0.js";
import {
    M as I
} from "./Dc8Bac8D.js";
import {
    a as _,
    G as Q,
    C as E
} from "./DW6cX6jm.js";
import {
    D as A
} from "./D9ll07Bp.js";
import {
    N as j
} from "./CW991W2w.js";
import {
    C as L
} from "./Bm_HbXT2.js";
import {
    S as F,
    a as D,
    U as M,
    E as P
} from "./BffV2qdL.js";
import {
    F as B,
    W as H,
    P as R
} from "./CIlzw36e.js";
import {
    E as U
} from "./D386eQgZ.js";
import {
    E as V
} from "./D5ao1EUl.js";
import {
    N as z
} from "./DQpEsQQa.js";
const T = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 24 24"
};
const W = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 20 20"
};
const q = {
        render: function(a, n) {
            return s(), e("svg", W, n[0] || (n[0] = [t("g", {
                fill: "none"
            }, [t("path", {
                d: "M11.5 4a1.5 1.5 0 0 0-3 0h-1a2.5 2.5 0 0 1 5 0H17a.5.5 0 0 1 0 1h-.554L15.15 16.23A2 2 0 0 1 13.163 18H6.837a2 2 0 0 1-1.987-1.77L3.553 5H3a.5.5 0 0 1-.492-.41L2.5 4.5A.5.5 0 0 1 3 4h8.5zm3.938 1H4.561l1.282 11.115a1 1 0 0 0 .994.885h6.326a1 1 0 0 0 .993-.885L15.438 5zM8.5 7.5c.245 0 .45.155.492.359L9 7.938v6.125c0 .241-.224.437-.5.437c-.245 0-.45-.155-.492-.359L8 14.062V7.939c0-.242.224-.438.5-.438zm3 0c.245 0 .45.155.492.359l.008.079v6.125c0 .241-.224.437-.5.437c-.245 0-.45-.155-.492-.359L11 14.062V7.939c0-.242.224-.438.5-.438z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    K = {
        width: "100%",
        height: "100%",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const $ = {
    xmlns: "http://www.w3.org/2000/svg",
    width: "100%",
    height: "100%",
    viewBox: "0 0 20 20",
    fill: "none"
};
const N = {
        name: "Suggestion",
        props: {
            searchQuery: {
                type: String,
                default: ""
            },
            reverse: {
                type: Boolean,
                default: !1
            },
            num: {
                type: Number,
                default: 10
            },
            useEmptySuggestions: {
                type: Boolean,
                default: !1
            },
            mobileFullScreen: {
                type: Boolean,
                default: !1
            },
            recentQueries: {
                type: Array,
                default: () => []
            },
            newIndex: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["update:searchQuery", "search"],
        components: {
            DeleteIcon: q,
            MoreIcon: I,
            RecentIcon: {
                render: function(a, n) {
                    return s(), e("svg", K, n[0] || (n[0] = [t("g", {
                        id: "Frame"
                    }, [t("g", {
                        id: "Group"
                    }, [t("path", {
                        id: "Vector",
                        d: "M9.37905 2.0228C10.9942 1.89546 12.6099 2.26152 14.0125 3.07255C15.415 3.88357 16.5383 5.10134 17.2336 6.5647C17.9289 8.02807 18.1635 9.66806 17.9064 11.2677C17.6493 12.8673 16.9125 14.3512 15.7937 15.5229C14.6748 16.6947 13.2265 17.4991 11.6404 17.8297C10.0543 18.1603 8.40527 18.0016 6.91139 17.3745C5.41751 16.7474 4.14924 15.6815 3.27439 14.3179C2.39954 12.9542 1.95934 11.3571 2.01205 9.7378C2.015 9.63931 2.03733 9.54236 2.07775 9.4525C2.11817 9.36264 2.17589 9.28161 2.24763 9.21406C2.31936 9.1465 2.4037 9.09374 2.49582 9.05878C2.58795 9.02382 2.68606 9.00734 2.78455 9.0103C2.88304 9.01325 2.97998 9.03558 3.06985 9.076C3.15971 9.11642 3.24073 9.17415 3.30829 9.24588C3.37584 9.31761 3.42861 9.40195 3.46357 9.49407C3.49853 9.5862 3.515 9.68431 3.51205 9.7828C3.46137 11.3157 3.95435 12.8171 4.90381 14.0215C5.85327 15.226 7.19806 16.0559 8.70037 16.3646C10.2027 16.6732 11.7658 16.4407 13.1132 15.7082C14.4606 14.9756 15.5056 13.7902 16.0634 12.3615C16.6212 10.9329 16.6558 9.35297 16.1612 7.90123C15.6665 6.44949 14.6745 5.21941 13.3604 4.42851C12.0464 3.63761 10.495 3.33683 8.98064 3.57936C7.46624 3.82189 6.08638 4.5921 5.08505 5.7538L4.88105 6.0018H7.26005L7.36205 6.0098C7.54156 6.0345 7.70606 6.12335 7.82514 6.25994C7.94422 6.39652 8.00982 6.5716 8.00982 6.7528C8.00982 6.934 7.94422 7.10908 7.82514 7.24566C7.70606 7.38225 7.54156 7.4711 7.36205 7.4958L7.26005 7.5028H3.26005L3.15805 7.4958C2.9959 7.47353 2.84548 7.39884 2.72975 7.2831C2.61401 7.16737 2.53931 7.01695 2.51705 6.8548L2.51005 6.7528V2.7528L2.51705 2.6508C2.53931 2.48865 2.61401 2.33823 2.72975 2.2225C2.84548 2.10676 2.9959 2.03207 3.15805 2.0098L3.26005 2.0028L3.36205 2.0098C3.5242 2.03207 3.67462 2.10676 3.79035 2.2225C3.90608 2.33823 3.98078 2.48865 4.00305 2.6508L4.01005 2.7528L4.00905 4.7058C5.38159 3.14674 7.30813 2.18355 9.37905 2.0228ZM9.75705 6.0018L9.85905 6.0088C10.0212 6.03106 10.1716 6.10576 10.2874 6.2215C10.4031 6.33723 10.4778 6.48765 10.5 6.6498L10.507 6.7518V9.9968L12.257 9.9978L12.359 10.0048C12.5393 10.0287 12.7048 10.1173 12.8246 10.254C12.9445 10.3908 13.0105 10.5665 13.0105 10.7483C13.0105 10.9301 12.9445 11.1058 12.8246 11.2426C12.7048 11.3793 12.5393 11.4679 12.359 11.4918L12.257 11.4978H9.75705L9.65505 11.4918C9.49275 11.4695 9.34222 11.3946 9.22647 11.2787C9.11072 11.1628 9.03611 11.0121 9.01405 10.8498L9.00705 10.7478V6.7518L9.01405 6.6498C9.03631 6.48765 9.11101 6.33723 9.22675 6.2215C9.34248 6.10576 9.4929 6.03106 9.65505 6.0088L9.75705 6.0018Z",
                        fill: "currentColor"
                    })])], -1)]))
                }
            },
            ViewIcon: {
                render: function(a, n) {
                    return s(), e("svg", $, n[0] || (n[0] = [t("path", {
                        d: "M17.1384 17.1433L14.0363 14.0412M14.0363 14.0412C14.5669 13.5105 14.9878 12.8806 15.275 12.1873C15.5622 11.494 15.71 10.7509 15.71 10.0005C15.71 9.25008 15.5622 8.50701 15.275 7.81371C14.9878 7.12041 14.5669 6.49046 14.0363 5.95983C13.5057 5.4292 12.8757 5.00829 12.1824 4.72111C11.4891 4.43394 10.746 4.28613 9.99562 4.28613C9.2452 4.28613 8.50212 4.43394 7.80882 4.72111C7.11552 5.00829 6.48558 5.4292 5.95495 5.95983C4.8833 7.03148 4.28125 8.48496 4.28125 10.0005C4.28125 11.516 4.8833 12.9695 5.95495 14.0412C7.0266 15.1128 8.48007 15.7149 9.99562 15.7149C11.5112 15.7149 12.9646 15.1128 14.0363 14.0412Z",
                        stroke: "currentColor",
                        "stroke-width": "1.71429",
                        "stroke-linecap": "round",
                        "stroke-linejoin": "round"
                    }, null, -1)]))
                }
            },
            DataIcon: A,
            ChecksCircleIcon: E,
            HotIcon: {
                render: function(a, n) {
                    return s(), e("svg", T, n[0] || (n[0] = [t("path", {
                        d: "M19.48 12.35c-1.57-4.08-7.16-4.3-5.81-10.23c.1-.44-.37-.78-.75-.55C9.29 3.71 6.68 8 8.87 13.62c.18.46-.36.89-.75.59c-1.81-1.37-2-3.34-1.84-4.75c.06-.52-.62-.77-.91-.34C4.69 10.16 4 11.84 4 14.37c.38 5.6 5.11 7.32 6.81 7.54c2.43.31 5.06-.14 6.95-1.87c2.08-1.93 2.84-5.01 1.72-7.69zm-9.28 5.03c1.44-.35 2.18-1.39 2.38-2.31c.33-1.43-.96-2.83-.09-5.09c.33 1.87 3.27 3.04 3.27 5.08c.08 2.53-2.66 4.7-5.56 2.32z",
                        fill: "currentColor"
                    }, null, -1)]))
                }
            },
            GeneralChatIcon: Q,
            GenerateImageIcon: _,
            NPopover: j
        },
        setup(e, {
            emit: t,
            expose: s
        }) {
            const a = n([]),
                {
                    t: c
                } = l(),
                p = n(e.useEmptySuggestions),
                d = n(null);
            p.value && (() => {
                d.value = {
                    tags: []
                };
                const e = [c("pages.index.rome-itinerary-with-family"), c("pages.index.london-trip-with-kids"), c("pages.index.paris-trip"), c("pages.index.adventure-in-tokyo"), c("pages.index.beijing-4-days"), c("pages.index.sydney-5-days"), c("pages.index.chicago-3-day-trip"), c("pages.index.berlin-7-days"), c("pages.index.san-francisco-travel-guide"), c("pages.index.new-york-travel-guide"), c("pages.index.budapest-travel-guide"), c("pages.index.explore-yellowstone-national-park"), c("pages.index.bali-5-day-journey"), c("pages.index.glacier-national-park-adventure"), c("pages.index.explore-santorini"), c("pages.index.maldives-honeymoon"), c("pages.index.gaming-pc"), c("pages.index.mechanical-keyboards"), c("pages.index.earbuds-for-running"), c("pages.index.ebikes"), c("pages.index.running-shoes"), c("pages.index.smart-tv")].sort((() => Math.random() - .5)).slice(0, 6).map((e => ({
                    text: e,
                    hot: !0
                })));
                d.value.tags = e
            })();
            const m = n(e.mobileFullScreen),
                g = n(e.recentQueries),
                v = n(!1),
                y = n(!0),
                h = n(null),
                x = [{
                    icon: E,
                    name: c("components.suggestion.cross-check"),
                    disabled: !1
                }, {
                    icon: A,
                    name: c("components.suggestion.data-search"),
                    disabled: !1
                }];
            e.newIndex && (x.map((e => {
                e.link = "/agents"
            })), x.unshift({
                icon: _,
                name: c("pages.agents.generate_image"),
                link: "/agents?type=moa_generate_image",
                disabled: !1
            }), x.unshift({
                icon: Q,
                name: c("pages.agents.ai_chat"),
                link: "/agents?type=moa_chat",
                disabled: !1
            })), o((() => e.recentQueries), (e => {
                g.value = e
            })), o((() => e.mobileFullScreen), (e => {
                m.value = e
            })), o((() => e.emptySuggestions), (e => {
                d.value = e
            }));
            const w = n(-1),
                k = n(!1),
                C = n(e.searchQuery),
                S = n(!0),
                b = n(e.reverse),
                I = n(e.num);
            o((() => e.searchQuery), (() => {
                C.value = e.searchQuery
            }));
            const j = n(C.value);
            let L = null;
            o(w, ((e, s) => {
                -1 == s && (j.value = C.value), C.value = e >= 0 ? B()[e].text : j.value, t("update:searchQuery", C.value)
            }));
            const F = e => {
                    const t = [];
                    return H.value && t.push(...H.value), t
                },
                D = n([]);
            o(C, (async (e, t) => {
                if (S.value && !(w.value >= 0 && k.value || e == j.value)) {
                    if (a.value = [], j.value = e, e || (D.value = []), !e && d.value) return a.value = F(d.value), void(k.value = !0);
                    if (e) {
                        D.value = g.value.filter((t => t.toLowerCase().trim().startsWith(e.toLowerCase().trim()))), null == L || L.abort(), L = new AbortController;
                        try {
                            const t = await fetch("/api/sug?q=" + encodeURIComponent(e.trim()), {
                                    method: "GET",
                                    headers: {
                                        "Content-Type": "application/json"
                                    },
                                    signal: null == L ? void 0 : L.signal
                                }),
                                s = await t.json();
                            C.value.trim() == e.trim() && (s.suggestions ? (a.value = s.suggestions, k.value = !0, j.value = e, w.value = -1) : a.value = [], a.value = a.value.slice(0, I.value))
                        } catch (s) {}
                    }
                }
            }));
            const M = e => {
                    if (event.isComposing) return;
                    if (("ArrowDown" === e.key || "ArrowUp" == e.key) && !k.value) return void(k.value = !0);
                    let s = a.value.length - 1;
                    if (s = B().length - 1, b.value && "ArrowUp" === e.key || !b.value && "ArrowDown" === e.key) e.preventDefault(), w.value = w.value < s ? w.value + 1 : -1;
                    else if (!b.value && "ArrowUp" === e.key || b.value && "ArrowDown" === e.key) e.preventDefault(), -1 === w.value ? w.value = s : w.value = w.value > 0 ? w.value - 1 : -1;
                    else if ("Enter" === e.key) {
                        if (e.metaKey || e.ctrlKey || e.shiftKey) return;
                        const s = d.value && d.value.tags ? d.value.tags.length : 0;
                        if (w.value >= s + H.value.length) {
                            const e = w.value - s - H.value.length,
                                t = x.filter((e => !e.disabled))[e];
                            if (t) return void u.windowopen("/agents?from=sug_" + encodeURIComponent(t.name))
                        }
                        let a = B();
                        a[w.value] && (C.value = a[w.value].text, t("update:searchQuery", C.value)), e.preventDefault(), e.stopPropagation(), k.value = !1, null == L || L.abort(), t("search")
                    } else k.value = !1, j.value = C.value, w.value = -1
                },
                P = e => {
                    S.value || (k.value = !1)
                };
            i((() => {
                null == L || L.abort(), document.removeEventListener("click", P)
            })), r((() => {
                document.addEventListener("click", P)
            })), s({
                onKeydown: M,
                isVisible: () => !(!k.value || "" != j.value || !d.value) || k.value && (a.value.length > 0 || D.value.length > 0),
                onBlur: e => {
                    S.value = !1, null == L || L.abort()
                },
                onFocus: e => {
                    S.value = !0, k.value = !0, !j.value && d.value && (a.value = F(d.value))
                },
                hide: () => {
                    k.value = !1
                }
            });
            const B = () => {
                    let e = D.value.slice(0, 5);
                    const t = [...e.map((e => ({
                        text: e,
                        type: "recent"
                    }))), ...a.value.filter((t => !e.includes(t))).map((e => ({
                        text: e,
                        type: "server"
                    })))].slice(0, I.value);
                    return 0 != t.length && t
                },
                H = n([]),
                R = () => {
                    const e = v.value ? 8 : 1;
                    if (!h.value) return;
                    const t = Array.prototype.slice.call(h.value.querySelectorAll(".recent_query")),
                        s = h.value.querySelector(".more");
                    t.forEach(((e, t) => {
                        e.style.display = "block"
                    })), y.value = !0;
                    const a = [];
                    if (t.forEach(((e, t) => {
                            const s = e.offsetTop; - 1 == a.indexOf(s) && a.push(s)
                        })), a.length <= 1) return y.value = !1, void(H.value = g.value);
                    const n = t.slice(0).reverse();
                    for (let o of n) s.offsetTop >= a[e] && (o.style.display = "none");
                    let l = t.filter((e => "block" == e.style.display)).length;
                    l > 50 && (l = 50), H.value = g.value.slice(0, l)
                };
            o((() => [p.value, "" == j.value, k.value, m.value].join()), ((e, t) => {
                setTimeout((() => {
                    p.value && "" == j.value && k.value && m.value && (R(), a.value = F(p.value))
                }), 0)
            }));
            const U = f("currentUser");
            return {
                deleteRecentQueries: () => {
                    g.value = [], localStorage.removeItem("recentQueries"), U.value && fetch("/api/recent_queries", {
                        method: "DELETE",
                        headers: {
                            "Content-Type": "application/json"
                        }
                    })
                },
                utils: u,
                recent_query_calculator: h,
                calcRecentQueriesForShow: R,
                recentQueriesForShow: H,
                showMoreRecentQueries: v,
                showMoreIcon: y,
                getSuggestionsWithRecentQueries: B,
                filteredRecentQueries: D,
                recentQueries: g,
                autopilots: x,
                mobileFullScreen: m,
                typedQuery: j,
                reverse: b,
                suggestions: a,
                emptySuggestions: d,
                clickHandler: (e, s) => {
                    C.value = s, t("search", C.value), k.value = !1
                },
                suggestionSelected: w,
                suggestionVisible: k,
                inputKeydown: M
            }
        }
    },
    G = ["onClick"],
    O = {
        class: "icon"
    },
    Z = {
        class: "text"
    },
    J = ["onClick"],
    X = {
        key: 2,
        class: "suggestions empty"
    },
    Y = {
        key: 0,
        class: "recent_queries calculator",
        ref: "recent_query_calculator"
    },
    ee = {
        class: "title"
    },
    te = {
        class: "text"
    },
    se = {
        class: "delete icon"
    },
    ae = {
        class: "items"
    },
    ne = {
        class: "recent_query"
    },
    le = {
        class: "more"
    },
    oe = {
        key: 1,
        class: "recent_queries"
    },
    ie = {
        class: "title"
    },
    re = {
        class: "text"
    },
    ue = {
        class: "icon delete"
    },
    ce = {
        class: "menu"
    },
    pe = {
        class: "items"
    },
    de = ["onClick"],
    me = {
        key: 2,
        class: "tags"
    },
    ge = {
        class: "title"
    },
    ve = {
        class: "items"
    },
    ye = ["onClick"],
    he = {
        key: 0,
        class: "hot"
    },
    fe = {
        class: "text"
    };
const xe = a(N, [
        ["render", function(a, n, l, o, i, r) {
            const u = x("RecentIcon"),
                f = x("ViewIcon"),
                k = x("DeleteIcon"),
                C = x("MoreIcon"),
                S = x("NPopover"),
                b = x("HotIcon");
            return s(), e("div", {
                class: p({
                    "mobile-full-screen": o.mobileFullScreen,
                    hidden: !o.suggestionVisible
                })
            }, [o.typedQuery && o.getSuggestionsWithRecentQueries() ? (s(), e("div", {
                key: 0,
                class: p(["suggestions", {
                    reverse: o.reverse
                }])
            }, [(s(!0), e(d, null, m(o.getSuggestionsWithRecentQueries(), ((a, n) => (s(), e("div", {
                class: p(["suggestion", {
                    selected: o.suggestionSelected === n,
                    recent: "recent" == a.type
                }]),
                key: n,
                onClick: e => o.clickHandler(e, a.text)
            }, [t("div", O, ["recent" == a.type ? (s(), w(u, {
                key: 0
            })) : (s(), w(f, {
                key: 1
            }))]), t("div", Z, g(a.text), 1)], 10, G)))), 128))], 2)) : o.typedQuery && o.suggestions && o.suggestions.length > 0 ? (s(), e("div", {
                key: 1,
                class: p(["suggestions", {
                    reverse: o.reverse
                }])
            }, [(s(!0), e(d, null, m(o.suggestions, ((t, a) => (s(), e("div", {
                class: p(["suggestion", o.suggestionSelected === a ? "selected" : ""]),
                key: "suggestion" + a,
                onClick: e => o.clickHandler(e, t)
            }, g(t), 11, J)))), 128))], 2)) : o.emptySuggestions && "" == o.typedQuery ? (s(), e("div", X, [o.recentQueries && o.recentQueries.length > 0 ? (s(), e("div", Y, [t("div", ee, [t("div", te, g(a.$t("components.suggestion.recent")), 1), t("div", se, [v(k)])]), t("div", ae, [(s(!0), e(d, null, m(o.recentQueries, (t => (s(), e("div", ne, g(t), 1)))), 256)), t("div", le, [t("div", {
                class: p(["icon", {
                    less: o.showMoreRecentQueries
                }])
            }, [v(C)], 2)])])], 512)) : c("", !0), o.recentQueries && o.recentQueries.length > 0 ? (s(), e("div", oe, [t("div", ie, [t("div", re, g(a.$t("components.suggestion.recent")), 1), t("div", {
                onClick: y((() => {}), ["stop"])
            }, [v(S, {
                placement: "bottom",
                trigger: "click"
            }, {
                trigger: h((() => [t("div", ue, [v(k)])])),
                default: h((() => [t("div", ce, [t("div", {
                    class: "button delete",
                    onClick: n[0] || (n[0] = y((() => {
                        o.deleteRecentQueries()
                    }), ["stop"]))
                }, g(a.$t("components.suggestion.delete-all-recent")), 1)])])),
                _: 1
            })])]), t("div", pe, [(s(!0), e(d, null, m(o.recentQueriesForShow, ((t, a) => (s(), e("div", {
                class: p(["recent_query", {
                    selected: o.suggestionSelected == a
                }]),
                onClick: e => {
                    o.clickHandler(e, t)
                }
            }, g(t), 11, de)))), 256)), o.showMoreIcon ? (s(), e("div", {
                key: 0,
                class: "more",
                onClick: n[1] || (n[1] = e => {
                    e.stopPropagation(), e.preventDefault(), o.showMoreRecentQueries = !o.showMoreRecentQueries, o.calcRecentQueriesForShow()
                })
            }, [t("div", {
                class: p(["icon", {
                    less: o.showMoreRecentQueries
                }])
            }, [v(C)], 2)])) : c("", !0)])])) : c("", !0), o.emptySuggestions.tags ? (s(), e("div", me, [t("div", ge, g(a.$t("components.suggestion.hot")), 1), t("div", ve, [(s(!0), e(d, null, m(o.emptySuggestions.tags, ((a, n) => (s(), e("div", {
                class: p(["tag", n == o.suggestionSelected - o.recentQueriesForShow.length ? "selected" : ""]),
                onClick: e => {
                    o.clickHandler(e, a.text)
                }
            }, [a.hot ? (s(), e("div", he, [v(b)])) : c("", !0), t("div", fe, g(a.text), 1)], 10, ye)))), 256))])])) : c("", !0), c("", !0)])) : c("", !0)], 2)
        }],
        ["__scopeId", "data-v-48409492"]
    ]),
    we = {
        components: {
            CloseIcon: L,
            ExpandIcon: P,
            UnExpandIcon: M,
            EnterIcon: V,
            Suggestion: xe,
            SendQuery: D,
            StopIcon: F,
            PdfIcon: R,
            WordIcon: H,
            ExcelIcon: U,
            FileIcon: B,
            NSpin: z
        },
        props: {
            expand: {
                type: Boolean,
                default: !1
            },
            disableExpand: {
                type: Boolean,
                default: !1
            },
            styleClass: String,
            modelValue: String,
            placeholder: String,
            useSuggestion: {
                type: Boolean,
                default: !0
            },
            emptySuggestions: {
                type: Object,
                default: null
            },
            supportImages: {
                type: Boolean,
                default: !1
            },
            images: {
                type: Array,
                default: []
            },
            files: {
                type: Array,
                default: []
            },
            showStopAsking: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["update:modelValue", "update:expand", "submitPrompt", "update:images", "update:files", "stopAsking", "errorMessage"],
        methods: {},
        setup(e, {
            emit: t,
            expose: s
        }) {
            const {
                t: a
            } = l(), i = n(e.images), c = n(e.files), p = n(e.showStopAsking);
            o((() => e.showStopAsking), (e => {
                p.value = e
            }));
            o((() => e.images), (e => {
                i.value = e
            })), o((() => i.value.length), (e => {
                t("update:images", i.value)
            })), o((() => c.value.length), (e => {
                t("update:files", c.value)
            }));
            const {
                supportImages: d
            } = k(e), m = n(e.expand), g = n(e.disableExpand), v = n(e.emptySuggestions), y = n(e.useSuggestion), h = n(e.styleClass ? e.styleClass : ""), f = n(!1), x = C((() => e.placeholder ? e.placeholder : "sparkQuestion" == h.value ? a("components.prompt_input.ask-anything-about-this-page") : "index" == h.value ? a("components.prompt_input.page-subject") : a("components.prompt_input.i-want-the-new-page-to-include"))), w = n(null), S = n(""), b = () => {
                setTimeout((() => {
                    w.value && w.value.scrollHeight > 0 && (w.value.style.height = "1px", w.value.style.height = w.value.scrollHeight + "px")
                }), 0)
            };
            e.modelValue && (S.value = e.modelValue), r((() => {
                b()
            })), o(m, (e => {
                t("update:expand", e)
            })), o(S, (e => {
                t("update:modelValue", e), b()
            })), o((() => e.modelValue), (e => {
                S.value = e
            }));
            const I = e => {
                    p.value || (e && (S.value = e), c.value.find((e => null == e.private_storage_url)) ? alert("Please wait for all files to be uploaded.") : ((S.value || i.value.length > 0 || c.value.length > 0) && (t("submitPrompt", S.value, i.value, c.value), w.value.blur()), S.value = "", c.value = [], i.value = []))
                },
                _ = n(null),
                Q = n(null);
            const E = e => {
                    const t = new FileReader;
                    t.onload = function(e) {
                        const t = new Image;
                        t.onload = () => {
                            (function(e, t, s) {
                                return new Promise(((a, n) => {
                                    const l = document.createElement("canvas"),
                                        o = l.getContext("2d");
                                    let i = e.width,
                                        r = e.height;
                                    i > r ? i > t && (r = Math.round(r * (t / i)), i = t) : r > t && (i = Math.round(i * (t / r)), r = t), l.width = i, l.height = r, o.drawImage(e, 0, 0, i, r), a(l.toDataURL("image/jpeg", s))
                                }))
                            })(t, 1024, .8).then((e => {
                                i.value.push(e)
                            })).catch((e => {}))
                        }, t.src = e.target.result
                    }, t.readAsDataURL(e)
                },
                A = {},
                j = async (e, t) => {
                    const s = await (async e => {
                        const t = await e.arrayBuffer(),
                            s = await crypto.subtle.digest("SHA-256", t);
                        return Array.from(new Uint8Array(s)).map((e => e.toString(16).padStart(2, "0"))).join("")
                    })(e);
                    if (s && A[s]) return u.log("use cache for file: " + e.name), void c.value.push(A[s]);
                    if (e.size) {
                        let t, s;
                        if (e.type.startsWith("image/") ? (t = 104857600, s = "components.prompt_input_super.file_type.image") : (t = 104857600, s = "components.prompt_input_super.file_type.file"), e.size > t) {
                            const e = Math.round(t / 1048576),
                                n = a("components.prompt_input_super.file_size_limit_error", {
                                    fileType: a(s),
                                    maxSize: e
                                });
                            return void alert(n)
                        }
                    }
                    try {
                        c.value.push({
                            name: e.name,
                            type: e.type,
                            size: e.size,
                            ext: t.ext
                        });
                        let a = c.value[c.value.length - 1];
                        const {
                            uploadImageUrl: n,
                            privateStorageUrl: l
                        } = await (async () => {
                            const e = await fetch("/api/get_upload_personal_image_url");
                            if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                            const t = await e.json();
                            if (!t || 0 !== t.status) throw new Error(`API error! code: ${t.code}`);
                            const s = t.data.upload_image_url,
                                a = t.data.private_storage_url;
                            return {
                                uploadImageUrl: s,
                                path: t.data.path,
                                privateStorageUrl: a
                            }
                        })();
                        if (!n || !l) return;
                        (new FormData).append("file", e);
                        if (!(await fetch(n, {
                                method: "PUT",
                                headers: {
                                    "x-ms-blob-type": "BlockBlob"
                                },
                                body: e
                            })).ok) throw new Error("Network response was not ok");
                        a.private_storage_url = l, s && (u.log("cache file: " + e.name), A[s] = a)
                    } catch (n) {
                        c.value = c.value.filter((e => e !== fileObj))
                    }
                };
            return s({
                getDom: () => Q.value,
                setInputHeight: b,
                focus: () => {
                    w.value.focus()
                },
                selectImage: (e, t) => new Promise(((e, s) => {
                    const a = (e => {
                        const t = document.createElement("input");
                        return t.type = "file", t.accept = "image/*", t.style.display = "none", "capture" === e && (t.capture = "camera"), t
                    })(t);
                    u.log("selectImage"), a.onchange = async t => {
                        u.log("image input onchange");
                        let n = t.target.files[0];
                        if (n) {
                            u.log("file", n.name, n.type);
                            try {
                                E(n), e(!0)
                            } catch (l) {
                                s(l)
                            } finally {
                                setTimeout((() => {
                                    document.body.contains(a) && document.body.removeChild(a)
                                }), 300)
                            }
                        } else s(new Error("No file selected"))
                    }, a.onerror = e => {
                        s(e)
                    }, document.body.appendChild(a), a.click()
                })),
                selectFile: (e, s) => {
                    const a = [{
                            ext: "pdf",
                            mime: "application/pdf"
                        }, {
                            ext: "epub",
                            mime: "application/epub+zip"
                        }, {
                            ext: "csv",
                            mime: "text/csv"
                        }, {
                            ext: "md",
                            mime: "text/markdown"
                        }, {
                            ext: "xlsx",
                            mime: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        }, {
                            ext: "docx",
                            mime: "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                        }, {
                            ext: "pptx",
                            mime: "application/vnd.openxmlformats-officedocument.presentationml.presentation"
                        }, {
                            ext: "xls",
                            mime: "application/vnd.ms-excel"
                        }, {
                            ext: "doc",
                            mime: "application/msword"
                        }, {
                            ext: "ppt",
                            mime: "application/vnd.ms-powerpoint"
                        }, {
                            ext: "html",
                            mime: "text/html"
                        }, {
                            ext: "json",
                            mime: "application/json"
                        }, {
                            ext: "xml",
                            mime: "application/xml"
                        }, {
                            ext: "yaml",
                            mime: "application/yaml"
                        }, {
                            ext: "sh",
                            mime: "text/x-sh"
                        }, {
                            ext: "py",
                            mime: "text/x-python-script"
                        }, {
                            ext: "js",
                            mime: "application/javascript"
                        }, {
                            ext: "ts",
                            mime: "application/typescript"
                        }, {
                            ext: "css",
                            mime: "text/css"
                        }, {
                            ext: "mjs",
                            mime: "text/javascript"
                        }, {
                            ext: "cjs",
                            mime: "text/javascript"
                        }, {
                            ext: "cpp",
                            mime: "text/x-c"
                        }, {
                            ext: "h",
                            mime: "text/x-chdr"
                        }, {
                            ext: "hpp",
                            mime: "text/x-c++hdr"
                        }, {
                            ext: "c",
                            mime: "text/x-csrc"
                        }, {
                            ext: "cxx",
                            mime: "text/x-c++src"
                        }, {
                            ext: "vue",
                            mime: "text/x-vue"
                        }, {
                            ext: "txt",
                            mime: "text/plain"
                        }],
                        n = (() => {
                            const e = document.createElement("input");
                            e.type = "file", e.style.display = "none", e.multiple = !0;
                            const t = a.map((e => e.mime));
                            return t.splice(0, 0, a.map((e => `.${e.ext}`))), d.value && t.push("image/*"), e.accept = t.join(","), e
                        })();
                    let l = !1;
                    const o = () => {
                        document.body.contains(n) && document.body.removeChild(n)
                    };
                    u.log("selectFile"), n.onchange = async e => {
                        try {
                            if (u.log("file input onchange"), l) return;
                            l = !0;
                            let s = e.target.files;
                            if (!s) return;
                            for (let e of s) {
                                u.log(`name: ${e.name}, type: ${e.type}`);
                                const s = a.find((t => t.mime == e.type)) || a.find((t => t.ext == e.name.split(".").pop()));
                                if (e.type.startsWith("image/")) {
                                    if (!d.value) return void t("errorMessage", "Image is not supported");
                                    E(e)
                                } else if (s)
                                    if ("application/json" === e.type || s.mime && s.mime.startsWith("text/")) {
                                        const t = new FileReader;
                                        t.onload = function(t) {
                                            const a = t.target.result,
                                                n = new File([a], e.name.replace(".json", ".json.txt"), {
                                                    type: "text/plain"
                                                });
                                            s.ext = "txt", s.mime = "text/plain", j(n, s)
                                        }, t.readAsText(e)
                                    } else j(e, s);
                                else alert(`${e.name} is not supported, type is: ${e.type}`)
                            }
                        } catch (s) {} finally {
                            o()
                        }
                    }, n.oncancel = () => {
                        l || o()
                    }, document.body.appendChild(n), n.click()
                }
            }), {
                showStopAsking: p,
                stopAsking: () => {
                    t("stopAsking")
                },
                filesizeString: u.filesizeString,
                files: c,
                onPaste: e => {
                    if (!d.value) return;
                    const t = (e.clipboardData || e.originalEvent.clipboardData).items;
                    if (!Array.from(t).find((e => "string" === e.kind && "text/plain" == e.type)))
                        for (let s in t) {
                            const a = t[s];
                            if ("file" === a.kind && a.type.startsWith("image/")) {
                                const t = a.getAsFile();
                                E(t), e.preventDefault()
                            }
                        }
                },
                images: i,
                supportImages: d,
                expand: m,
                disableExpand: g,
                emptySuggestions: v,
                useSuggestion: y,
                isFocus: n(!1),
                isActive: f,
                inputWrapper: Q,
                suggestion: n(null),
                searchInputWrapperBackground: _,
                styleClass: h,
                placeholder: x,
                prompt: S,
                submitPrompt: I,
                searchInput: w,
                inputKeydown: e => {
                    if (!e.isComposing && "Enter" === e.key)
                        if (e.metaKey || e.ctrlKey || e.shiftKey) {
                            e.preventDefault();
                            const s = e.target,
                                a = s.selectionStart,
                                n = s.value.substring(0, a),
                                l = s.value.substring(a);
                            s.value = n + "\n" + l;
                            const o = parseInt(window.getComputedStyle(s).lineHeight, 10);
                            s.scrollTop += o, s.selectionStart = s.selectionEnd = a + 1;
                            var t = new Event("input", {
                                bubbles: !0,
                                cancelable: !0
                            });
                            s.dispatchEvent(t)
                        } else y.value ? e.preventDefault() : (e.preventDefault(), I())
                },
                emit: t
            }
        }
    },
    ke = {
        class: "search-input-wrapper-background",
        ref: "searchInputWrapperBackground"
    },
    Ce = {
        key: 0,
        class: "prompt-files"
    },
    Se = ["onClick"],
    be = ["src"],
    Ie = {
        class: "prompt-file"
    },
    _e = {
        class: "prompt-file-inner"
    },
    Qe = ["onClick"],
    Ee = {
        class: "file-wrapper"
    },
    Ae = {
        class: "icon file-icon"
    },
    je = {
        class: "file-info"
    },
    Le = {
        class: "file-name"
    },
    Fe = {
        class: "file-size"
    },
    De = {
        class: "textarea-wrapper"
    },
    Me = ["placeholder"],
    Pe = {
        class: "icon-group"
    },
    Be = {
        key: 0,
        class: "stop-icon"
    },
    He = {
        key: 0,
        class: "suggestion-wrapper"
    };
const Re = a(we, [
    ["render", function(a, n, l, o, i, r) {
        var u, y;
        const f = x("CloseIcon"),
            k = x("PdfIcon"),
            C = x("WordIcon"),
            I = x("ExcelIcon"),
            _ = x("FileIcon"),
            Q = x("n-spin"),
            E = x("UnExpandIcon"),
            A = x("ExpandIcon"),
            j = x("StopIcon"),
            L = x("SendQuery"),
            F = x("EnterIcon"),
            D = xe;
        return s(), e(d, null, [t("div", {
            class: p(["input", [o.isActive ? "active" : "", o.styleClass, o.suggestion && o.suggestion.isVisible() ? "has-suggestions" : "", o.isFocus ? "focus" : "", o.prompt.length > 0 || o.showStopAsking ? "" : "empty"]]),
            ref: "inputWrapper"
        }, [t("div", ke, null, 512), o.supportImages && (null == (u = o.images) ? void 0 : u.length) > 0 || (null == (y = o.files) ? void 0 : y.length) > 0 ? (s(), e("div", Ce, [o.supportImages ? (s(!0), e(d, {
            key: 0
        }, m(o.images, (a => (s(), e("div", {
            class: "prompt-file image",
            key: a
        }, [t("div", {
            class: "remove",
            onClick: () => {
                o.images = o.images.filter((e => e != a))
            }
        }, [v(f)], 8, Se), t("img", {
            src: a
        }, null, 8, be)])))), 128)) : c("", !0), (s(!0), e(d, null, m(o.files, (a => (s(), e("div", Ie, [t("div", _e, [t("div", {
            class: "remove",
            onClick: e => o.files = o.files.filter((e => e != a))
        }, [v(f)], 8, Qe), v(Q, {
            show: !a.private_storage_url
        }, {
            default: h((() => [t("div", Ee, [t("div", Ae, ["pdf" == a.ext ? (s(), w(k, {
                key: 0
            })) : "doc" == a.ext || "docx" == a.ext ? (s(), w(C, {
                key: 1
            })) : "xls" == a.ext || "xlsx" == a.ext ? (s(), w(I, {
                key: 2
            })) : (s(), w(_, {
                key: 3
            }))]), t("div", je, [t("div", Le, g(a.name), 1), t("div", Fe, g(o.filesizeString(a.size)), 1)])])])),
            _: 2
        }, 1032, ["show"])])])))), 256))])) : c("", !0), t("div", De, [S(t("textarea", {
            onPaste: n[0] || (n[0] = (...e) => o.onPaste && o.onPaste(...e)),
            name: "query",
            ref: "searchInput",
            "onUpdate:modelValue": n[1] || (n[1] = e => o.prompt = e),
            onKeydown: n[2] || (n[2] = e => {
                var t;
                o.inputKeydown(e), null == (t = o.suggestion) || t.onKeydown(e)
            }),
            onFocus: n[3] || (n[3] = e => {
                var t;
                o.isFocus = !0, null == (t = o.suggestion) || t.onFocus(e)
            }),
            onBlur: n[4] || (n[4] = e => {
                var t;
                o.isFocus = !1, null == (t = o.suggestion) || t.onBlur(e)
            }),
            onInput: n[5] || (n[5] = e => o.isActive = !0),
            class: p([o.isActive ? "active" : "", "search-input"]),
            type: "text",
            placeholder: o.placeholder,
            enterkeyhint: "search"
        }, null, 42, Me), [
            [b, o.prompt]
        ]), t("div", Pe, ["sparkQuestion" != o.styleClass || o.disableExpand ? c("", !0) : (s(), e("div", {
            key: 0,
            onClick: n[6] || (n[6] = e => {
                e.preventDefault(), o.expand = !o.expand
            }),
            class: "input-icon expand"
        }, [o.expand ? (s(), w(E, {
            key: 0
        })) : (s(), w(A, {
            key: 1
        }))])), t("div", {
            onClick: n[7] || (n[7] = e => {
                var t;
                o.showStopAsking ? o.stopAsking() : (e.stopPropagation(), null == (t = o.suggestion) || t.hide(), o.submitPrompt(o.prompt))
            }),
            class: "input-icon"
        }, [o.showStopAsking ? (s(), e("div", Be, [v(j)])) : "searchAskFollowUp" == o.styleClass ? (s(), w(L, {
            key: 1
        })) : (s(), w(F, {
            key: 2
        }))])])])], 2), o.useSuggestion ? (s(), e("div", He, [v(D, {
            reverse: "sparkQuestion" == o.styleClass,
            searchQuery: o.prompt,
            "onUpdate:searchQuery": n[8] || (n[8] = e => o.prompt = e),
            ref: "suggestion",
            onSearch: n[9] || (n[9] = e => {
                o.submitPrompt(e)
            }),
            num: 3,
            emptySuggestions: o.emptySuggestions
        }, null, 8, ["reverse", "searchQuery", "emptySuggestions"])])) : c("", !0)], 64)
    }],
    ["__scopeId", "data-v-4a485c34"]
]);
export {
    q as D, Re as _, xe as a
};