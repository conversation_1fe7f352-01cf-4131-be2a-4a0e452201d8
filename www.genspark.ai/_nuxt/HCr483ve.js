import {
    d as e,
    o,
    b as t,
    D as a,
    W as n,
    r as s,
    e as r,
    y as i,
    t as d,
    c as l,
    C as c,
    s as C,
    ay as p,
    v as g,
    i as E
} from "./Cf0SOiw0.js";
import {
    C as A
} from "./Bm_HbXT2.js";
import {
    S as _
} from "./B0db5Fvl.js";
import {
    S as h
} from "./BUs-AQWo.js";
import {
    S as u,
    D as F,
    a as f,
    G as w,
    b as m,
    C as k,
    c as L,
    I as T,
    T as I,
    A as D
} from "./BdlGQsae.js";
import {
    G as N,
    a as R,
    C as G
} from "./DW6cX6jm.js";
import {
    a as v
} from "./CKd5XOy1.js";
import {
    A as S
} from "./Cl89jLsD.js";
const x = "data:image/png;base64,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",
    B = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "28",
        height: "28",
        viewBox: "0 0 28 28",
        fill: "none"
    };
const H = {
        render: function(a, n) {
            return o(), e("svg", B, n[0] || (n[0] = [t("path", {
                d: "M10.5719 5.63072L3.77896 11.5749C3.19643 12.0847 3.58701 12.99 4.38944 12.99C4.87969 12.99 5.27711 13.357 5.27711 13.8099V17.594C5.27711 20.8495 5.27711 22.4772 6.37205 23.4886C7.467 24.5 9.22929 24.5 12.7539 24.5H15.2461C18.7707 24.5 20.533 24.5 21.6279 23.4886C22.7229 22.4772 22.7229 20.8495 22.7229 17.594V13.8099C22.7229 13.357 23.1203 12.99 23.6105 12.99C24.413 12.99 24.8036 12.0847 24.221 11.5749L17.428 5.63072C15.8047 4.21024 14.9931 3.5 14 3.5C13.0069 3.5 12.1953 4.21024 10.5719 5.63072Z",
                stroke: "currentColor",
                "stroke-width": "1.75",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), t("path", {
                d: "M14 18.668H14.0105",
                stroke: "currentColor",
                "stroke-width": "3",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    O = {
        width: "28",
        height: "28",
        viewBox: "0 0 28 28",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const M = {
        render: function(a, n) {
            return o(), e("svg", O, n[0] || (n[0] = [t("path", {
                d: "M13.999 2.625C14.7325 2.625 15.3538 2.89686 15.9639 3.30566C16.5516 3.69948 17.2088 4.27778 18.0029 4.97266L24.7959 10.916H24.7969C25.3912 11.4362 25.5037 12.2029 25.2373 12.8203C24.9752 13.4276 24.3634 13.8652 23.6094 13.8652C23.6046 13.8653 23.6003 13.8667 23.5967 13.8672V17.5938C23.5967 19.1947 23.5993 20.4909 23.4512 21.5088C23.2982 22.5594 22.9696 23.4401 22.2207 24.1318C21.4818 24.8142 20.5579 25.1052 19.4551 25.2422C18.3706 25.3768 16.9844 25.375 15.2451 25.375H12.7529C11.0137 25.375 9.62744 25.3769 8.54297 25.2422C7.44018 25.1052 6.51625 24.8142 5.77734 24.1318C5.02857 23.4401 4.6998 22.5594 4.54688 21.5088C4.39873 20.4909 4.40137 19.1947 4.40137 17.5938V13.8672C4.3977 13.8666 4.39341 13.8652 4.38867 13.8652C3.63474 13.8651 3.02278 13.4275 2.76074 12.8203C2.49446 12.203 2.60799 11.4361 3.20215 10.916L9.99512 4.97266C10.7892 4.27779 11.4465 3.6995 12.0342 3.30566C12.6443 2.89685 13.2656 2.62507 13.999 2.625ZM13.999 17.168C13.1707 17.1681 12.499 17.8396 12.499 18.668C12.499 19.4963 13.1707 20.1678 13.999 20.168H14.0098L14.1631 20.1602C14.9194 20.0832 15.5098 19.4446 15.5098 18.668C15.5098 17.8914 14.9194 17.2527 14.1631 17.1758L14.0098 17.168H13.999Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    y = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "28",
        height: "28",
        viewBox: "0 0 28 28",
        fill: "none"
    };
const b = {
        render: function(a, n) {
            return o(), e("svg", y, n[0] || (n[0] = [t("path", {
                d: "M19.822 10.5013C19.822 13.7229 17.2065 16.3346 13.9802 16.3346C10.7542 16.3346 8.13867 13.7229 8.13867 10.5013C8.13867 7.27965 10.7542 4.66797 13.9802 4.66797C17.2065 4.66797 19.822 7.27965 19.822 10.5013Z",
                stroke: "currentColor",
                "stroke-width": "1.75",
                "stroke-linecap": "round"
            }, null, -1), t("path", {
                d: "M3.5 23.332C7.58333 25.082 10.7454 25.6654 14 25.6654C17.8419 25.6654 21.5833 24.4987 24.5 23.332",
                stroke: "currentColor",
                "stroke-width": "1.75",
                "stroke-linecap": "round"
            }, null, -1)]))
        }
    },
    U = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "16",
        height: "16",
        viewBox: "0 0 16 16",
        fill: "none"
    };
const j = {
        render: function(t, n) {
            return o(), e("svg", U, n[0] || (n[0] = [a('<path d="M2.66675 7.33398V10.0007C2.66675 12.2005 2.66675 13.3005 3.35017 13.9839C4.03358 14.6673 5.13353 14.6673 7.33341 14.6673H8.66675C10.8666 14.6673 11.9665 14.6673 12.65 13.9839C13.3334 13.3005 13.3334 12.2005 13.3334 10.0007V7.33398" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2 6.00033C2 5.50187 2 5.25263 2.13397 5.06699C2.22174 4.94538 2.34798 4.84439 2.5 4.77417C2.73205 4.66699 3.04359 4.66699 3.66667 4.66699H12.3333C12.9564 4.66699 13.2679 4.66699 13.5 4.77417C13.652 4.84439 13.7783 4.94538 13.866 5.06699C14 5.25263 14 5.50187 14 6.00033C14 6.49879 14 6.74799 13.866 6.93366C13.7783 7.05526 13.652 7.15626 13.5 7.22646C13.2679 7.33366 12.9564 7.33366 12.3333 7.33366H3.66667C3.04359 7.33366 2.73205 7.33366 2.5 7.22646C2.34798 7.15626 2.22174 7.05526 2.13397 6.93366C2 6.74799 2 6.49879 2 6.00033Z" stroke="currentColor" stroke-linejoin="round"></path><path d="M4 2.52446C4 1.86698 4.53299 1.33398 5.19047 1.33398H5.42857C6.84873 1.33398 8 2.48525 8 3.90541V4.66732H6.14286C4.95939 4.66732 4 3.70792 4 2.52446Z" stroke="currentColor" stroke-linejoin="round"></path><path d="M12 2.52446C12 1.86698 11.467 1.33398 10.8095 1.33398H10.5714C9.15127 1.33398 8 2.48525 8 3.90541V4.66732H9.85713C11.0406 4.66732 12 3.70792 12 2.52446Z" stroke="currentColor" stroke-linejoin="round"></path><path d="M8 7.33398V14.6673" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>', 5)]))
        }
    },
    P = n("menu", (() => {
        const e = s(!1);
        return {
            isClickedUpdate: e,
            setIsClickedUpdate: o => {
                e.value = o, localStorage.setItem("isClickedUpdate", o)
            }
        }
    })),
    W = {
        class: "px-[16px]"
    },
    V = {
        class: "px-[20px] pb-[20px] bg-[#FFFFFF] rounded-2xl shadow-[0px_3px_30px_0px_rgba(0,0,0,0.08)]"
    },
    Q = {
        class: "flex justify-center items-center pt-[32px] gap-[10px]"
    },
    X = {
        class: "text-center justify-start text-black text-xl font-bold leading-loose"
    },
    Z = {
        class: "pt-[20px] text-center justify-start text-black text-base font-bold leading-normal md:w-[510px]"
    },
    K = {
        class: "pt-[16px] text-center justify-start text-zinc-600 text-base font-normal leading-normal"
    },
    Y = {
        __name: "ChatUpdateModal",
        emits: ["handlerAiChatUpdateConfirmModal", "handlerUpgradeModal"],
        setup(a, {
            emit: n
        }) {
            const s = n,
                l = () => {
                    s("handlerAiChatUpdateConfirmModal")
                },
                c = () => {
                    s("handlerUpgradeModal")
                };
            return (a, n) => (o(), e("div", W, [t("div", V, [r(i(A), {
                class: "absolute top-[12px] right-[28px] text-[#606366] w-[20px] h-[20px] cursor-pointer",
                onClick: l
            }), t("div", Q, [n[0] || (n[0] = t("div", {
                class: "text-center text-4xl"
            }, "🎁", -1)), t("div", X, d(a.$t("components.menu.chat_update_modal.pro_plus_users")), 1)]), t("div", Z, d(a.$t("components.menu.chat_update_modal.unlimited_access_to_all_advanced_models_in_ai_chat_with_zero_credit_cost")), 1), t("div", K, d(a.$t("components.menu.chat_update_modal.valid_until")), 1), t("div", {
                class: "mt-[24px] justify-start rounded-xl text-center text-white font-bold bg-[#000] py-[10px] px-[20px] cursor-pointer",
                onClick: l
            }, d(a.$t("components.menu.chat_update_modal.ok")), 1), t("div", {
                class: "rounded-xl text-center bg-[#F5F5F5] mt-[12px] py-[10px] justify-start text-neutral-800 text-base font-normal cursor-pointer",
                onClick: c
            }, d(a.$t("components.menu.chat_update_modal.upgrade")), 1)])]))
        }
    },
    z = () => {
        const e = s(!1),
            o = s("");
        return {
            showAiChatUpdateModal: e,
            currentPath: o,
            handlerAiChatUpdateConfirmModal: o => {
                e.value = !1, null == o || o()
            },
            handlerUpgradeModal: o => {
                e.value = !1, window.open("/pricing", "_blank"), null == o || o()
            }
        }
    },
    q = n("downloadAppGuide", (() => {
        const e = s(!1),
            o = "downloadAppGuideLastShown",
            t = l((() => !e.value && c.isMobile() && !c.isGensparkApp() && n() && !a())),
            a = () => {
                if ("undefined" == typeof window) return !1;
                const e = new URLSearchParams(window.location.search),
                    o = e.get("utm_source"),
                    t = e.get("utm_medium");
                return "google" === o && ("cpc-search" === t || "pmax" === t) || "facebook" === o && "YM" === t
            },
            n = () => {
                if ("undefined" == typeof window) return !1;
                const e = (new Date).toDateString(),
                    t = localStorage.getItem(o);
                return !t || t !== e
            },
            r = () => {
                if ("undefined" == typeof window) return;
                const e = (new Date).toDateString();
                localStorage.setItem(o, e)
            };
        return {
            shouldShowGuide: t,
            hideDownloadAppGuide: e,
            shouldShowToday: n,
            markAsShownToday: r,
            initializeShowState: () => {
                n() || (e.value = !0)
            },
            hideGuide: () => {
                e.value = !0, r()
            }
        }
    })),
    J = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const $ = {
        render: function(a, n) {
            return o(), e("svg", J, n[0] || (n[0] = [t("path", {
                d: "M4.5 12H19.5",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round"
            }, null, -1), t("path", {
                d: "M12 19.5L12 4.5",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round"
            }, null, -1)]))
        }
    },
    ee = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const oe = {
        render: function(t, n) {
            return o(), e("svg", ee, n[0] || (n[0] = [a('<path d="M17.5 3L17.7579 3.69703C18.0961 4.61102 18.2652 5.06802 18.5986 5.40139C18.932 5.73477 19.389 5.90387 20.303 6.24208L21 6.5L20.303 6.75792C19.389 7.09613 18.932 7.26524 18.5986 7.59861C18.2652 7.93198 18.0961 8.38898 17.7579 9.30297L17.5 10L17.2421 9.30297C16.9039 8.38898 16.7348 7.93198 16.4014 7.59861C16.068 7.26524 15.611 7.09613 14.697 6.75792L14 6.5L14.697 6.24208C15.611 5.90387 16.068 5.73477 16.4014 5.40139C16.7348 5.06802 16.9039 4.61102 17.2421 3.69703L17.5 3Z" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"></path><path d="M6.00781 7.99805H3.00781" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9.00781 3.99805L9.00781 20.998" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M6.00781 16.998H3.00781" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M19.0078 12.5V20C19.0078 20.5523 18.5601 21 18.0078 21H6.00781C5.45553 21 5.00781 20.5523 5.00781 20V5C5.00781 4.44772 5.45553 4 6.00781 4H12.0078H12.5078" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>', 5)]))
        }
    },
    te = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const ae = {
        render: function(t, n) {
            return o(), e("svg", te, n[0] || (n[0] = [a('<path d="M20.9965 11C20.9988 11.4701 20.9988 11.9693 20.9988 12.5C20.9988 16.9783 20.9988 19.2175 19.6076 20.6088C18.2163 22 15.9771 22 11.4988 22C7.02044 22 4.78127 22 3.39002 20.6088C1.99878 19.2175 1.99878 16.9783 1.99878 12.5C1.99878 8.02166 1.99878 5.78249 3.39002 4.39124C4.78127 3 7.02044 3 11.4988 3C12.0295 3 12.5287 3 12.9988 3.00231" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path d="M14.834 15.001L16.834 12.501L14.834 10.001" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M11.1234 15.3136L12.5469 10.001" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M8.83398 10.001L6.83398 12.501L8.83398 15.001" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M18.5 1.99707L18.7579 2.6941C19.0961 3.60809 19.2652 4.06509 19.5986 4.39846C19.932 4.73184 20.389 4.90094 21.303 5.23915L22 5.49707L21.303 5.75499C20.389 6.0932 19.932 6.26231 19.5986 6.59568C19.2652 6.92905 19.0961 7.38605 18.7579 8.30004L18.5 8.99707L18.2421 8.30004C17.9039 7.38605 17.7348 6.92905 17.4014 6.59568C17.068 6.26231 16.611 6.0932 15.697 5.75499L15 5.49707L15.697 5.23915C16.611 4.90094 17.068 4.73184 17.4014 4.39846C17.7348 4.06509 17.9039 3.60809 18.2421 2.6941L18.5 1.99707Z" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"></path>', 5)]))
        }
    },
    ne = {
        ALL_AGENTS: "all_agents",
        SUPER_AGENT: "super_agent",
        GENERATE_SLIDE: "slides_agent",
        SHEETS_AGENT: "sheets_agent",
        DOCS_AGENT: "docs_agent",
        AI_PODCASTS: "podcasts_agent",
        GENERAL_CHAT: "moa_chat",
        GENERATE_IMAGE: "moa_generate_image",
        GENERATE_VIDEO: "moa_generate_video",
        DEEP_RESEARCH: "agent_deep_research",
        FACT_CHECK: "agentic_cross_check",
        CALL_FOR_ME: "phone_call",
        DOWNLOAD_FOR_ME: "ai_drive",
        AI_INBOX: "ai_inbox",
        TRANSLATION: "moa_translator",
        AI_DRIVE_FILES: "ai_drive_files",
        MEETING_NOTES: "meeting_notes",
        AI_DEVELOPER: "ai_developer"
    },
    se = () => {
        const {
            t: e
        } = C(), o = E("jsBridge"), t = s([{
            id: _.SUPER_AGENT,
            type: ne.SUPER_AGENT,
            icon: p(N),
            iconActive: p(N),
            name: e("pages.agents.super_agent"),
            isNew: !1,
            color: "#0F7FFF",
            bgColor: "#F5F5FF",
            darkColor: "#fff",
            darkBgColor: "#0F7FFF4D",
            path: "/agents?type=super_agent",
            descs: [],
            needLogin: !0
        }, {
            id: _.GENERATE_SLIDE,
            type: ne.GENERATE_SLIDE,
            icon: p(h),
            iconActive: p(h),
            name: e("pages.agents.ai_slides"),
            isNew: !1,
            color: "#FC9219",
            bgColor: "#FFFAF5",
            darkColor: "#fff",
            darkBgColor: "#FC9E324D",
            path: "/agents?type=slides_agent",
            descs: [e("pages.agents.ai_slides.desc1"), e("pages.agents.ai_slides.desc2"), e("pages.agents.ai_slides.desc3")],
            needLogin: !0
        }, {
            id: _.SHEETS_AGENT,
            type: ne.SHEETS_AGENT,
            icon: p(u),
            iconActive: p(u),
            name: e("pages.agents.ai_sheets"),
            isNew: !1,
            color: "#3C7E40",
            bgColor: "#F5FFF5",
            darkColor: "#fff",
            darkBgColor: "#448F494D",
            path: "/agents?type=sheets_agent",
            descs: [e("pages.agents.ai_sheets.desc1"), e("pages.agents.ai_sheets.desc2"), e("pages.agents.ai_sheets.desc3")],
            needLogin: !0
        }, {
            id: _.DOCS_AGENT,
            type: ne.DOCS_AGENT,
            icon: p(F),
            iconActive: p(F),
            name: e("pages.agents.ai_docs"),
            isNew: !1,
            color: "#007AFF",
            bgColor: "#F5F9FF",
            darkColor: "#fff",
            darkBgColor: "#298DFF4D",
            path: "/agents?type=docs_agent",
            descs: [e("pages.agents.ai_docs.desc1"), e("pages.agents.ai_docs.desc2"), e("pages.agents.ai_docs.desc3")],
            needLogin: !0
        }, {
            id: _.AI_DEVELOPER,
            type: ne.AI_DEVELOPER,
            icon: p(ae),
            iconActive: p(ae),
            name: e("components.code_sandbox_light.canvas.ai_developer"),
            isNew: !0,
            color: "#1FA1F9",
            bgColor: "#F4FCFF",
            darkColor: "#fff",
            darkBgColor: "#1FA1F94D",
            path: "/agents?type=ai_developer",
            descs: [e("pages.agents.ai_developer.desc1"), e("pages.agents.ai_developer.desc2")],
            needLogin: !0,
            isDogfood: !1,
            hide: !1
        }, {
            id: _.AI_PODCASTS,
            type: ne.AI_PODCASTS,
            icon: p(v),
            iconActive: p(v),
            name: e("pages.agents.ai_podcasts"),
            isNew: !1,
            color: "#D43ABA",
            bgColor: "#FFF5FD",
            darkColor: "#fff",
            darkBgColor: "#D94FC24D",
            path: "/agents?type=podcasts_agent",
            isDogfood: !1,
            descs: [e("pages.agents.ai_podcasts.desc1"), e("pages.agents.ai_podcasts.desc2"), e("pages.agents.ai_podcasts.desc3")],
            needLogin: !0
        }, {
            id: _.GENERAL_CHAT,
            type: ne.GENERAL_CHAT,
            icon: p(f),
            iconActive: p(f),
            name: e("pages.agents.ai_chat"),
            update: !0,
            color: "#4B70E8",
            bgColor: "#EDF1FF",
            darkColor: "#fff",
            darkBgColor: "#6282EB4D",
            path: "/agents?type=moa_chat",
            descs: [e("pages.agents.ai_chat.desc1"), e("pages.agents.ai_chat.desc2")],
            isNew: !1
        }, {
            id: _.GENERATE_IMAGE,
            type: ne.GENERATE_IMAGE,
            icon: p(R),
            iconActive: p(R),
            name: e("pages.agents.ai_image"),
            color: "#4B50E8",
            bgColor: "#EEF1FF",
            darkColor: "#fff",
            darkBgColor: "#6266EB4D",
            path: "/agents?type=image_studio",
            descs: [e("pages.agents.ai_image.desc1"), e("pages.agents.ai_image.desc2")],
            isNew: !1,
            needLogin: !0
        }, {
            id: _.GENERATE_VIDEO,
            type: ne.GENERATE_VIDEO,
            icon: p(w),
            iconActive: p(w),
            name: e("pages.agents.ai_video"),
            color: "#F1A23C",
            bgColor: "#FFFBF5",
            darkColor: "#fff",
            darkBgColor: "#F3AE544D",
            path: "/agents?type=moa_generate_video",
            descs: [e("pages.agents.ai_video.desc1"), e("pages.agents.ai_video.desc2")],
            needLogin: !0
        }, {
            id: _.DEEP_RESEARCH,
            type: ne.DEEP_RESEARCH,
            icon: p(m),
            iconActive: p(m),
            name: e("components.index_layout.deep_research"),
            color: "#6C4AB3",
            bgColor: "#F8F5FF",
            darkColor: "#fff",
            darkBgColor: "#7A5BBB4D",
            path: "/agents?type=agent_deep_research",
            descs: [e("pages.agents.deep_research.desc1"), e("pages.agents.deep_research.desc2")],
            isNew: !1,
            needLogin: !0
        }, {
            id: _.FACT_CHECK,
            type: ne.FACT_CHECK,
            icon: p(G),
            iconActive: p(G),
            name: e("pages.agents.agentic_cross_check"),
            isNew: !1,
            color: "#29A2FF",
            bgColor: "#F5FBFF",
            darkColor: "#fff",
            darkBgColor: "#29A2FF4D",
            path: "/agents?type=agentic_cross_check",
            needLogin: !0
        }, {
            id: _.CALL_FOR_ME,
            type: ne.CALL_FOR_ME,
            icon: p(k),
            iconActive: p(k),
            name: e("pages.call_assistant.call_for_me"),
            color: "#E88C4B",
            bgColor: "#FFF9F5",
            darkColor: "#fff",
            darkBgColor: "#EB9B624D",
            path: "/agents?type=phone_call",
            descs: [e("pages.agents.call_for_me.desc1"), e("pages.agents.call_for_me.desc2")],
            isNew: !1,
            needLogin: !0,
            onClick: !0,
            onSetting: !0
        }, {
            id: _.DOWNLOAD_FOR_ME,
            type: ne.DOWNLOAD_FOR_ME,
            icon: p(L),
            iconActive: p(L),
            name: e("components.aidrive.download_for_me"),
            isNew: !1,
            color: "#0F7FFF",
            bgColor: "#F4F9FF",
            darkColor: "#fff",
            darkBgColor: "#0F7FFF4D",
            path: "/agents?type=ai_drive",
            descs: [e("pages.agents.download_for_me.desc1"), e("pages.agents.download_for_me.desc2")],
            needLogin: !0,
            onClick: !0
        }, {
            id: _.AI_INBOX,
            type: ne.AI_INBOX,
            icon: p(T),
            iconActive: p(T),
            name: e("pages.agents.ai_inbox"),
            isNew: !1,
            color: "#3862D4",
            bgColor: "#F5F8FF",
            darkColor: "#fff",
            darkBgColor: "#3862D44D",
            path: "/agents?type=ai_inbox",
            isDogfood: !0,
            descs: [e("pages.agents.ai_inbox.desc1"), e("pages.agents.ai_inbox.desc2"), e("pages.agents.ai_inbox.desc3")],
            needLogin: !0
        }, {
            id: _.TRANSLATION,
            type: ne.TRANSLATION,
            icon: p(I),
            iconActive: p(I),
            name: e("pages.agents.translation"),
            isNew: !1,
            color: "#6438D4",
            bgColor: "#F8F5FF",
            darkColor: "#fff",
            darkBgColor: "#8B5CF64D",
            path: "/agents?type=moa_translator",
            needLogin: !0
        }, {
            id: _.MEETING_NOTES,
            type: ne.MEETING_NOTES,
            icon: p(oe),
            iconActive: p(oe),
            name: e("pages.agents.ai_meeting_notes"),
            isNew: !0,
            color: "#1EC3AD",
            bgColor: "#E5FBF2",
            darkColor: "#fff",
            darkBgColor: "#1EC3AD4D",
            path: "/meetingnotes",
            needLogin: !0,
            isDogfood: !1,
            hide: !0
        }, {
            id: _.AIDRIVE,
            type: ne.AI_DRIVE_FILES,
            icon: p(S),
            iconActive: p(S),
            name: e("components.index_layout.aidrive"),
            isNew: !1,
            color: "#232425",
            bgColor: "#F0F0F0",
            darkColor: "#fff",
            darkBgColor: "#FFFFFF4D",
            path: "/aidrive/files",
            needLogin: !0
        }, {
            id: _.ALL_AGENTS,
            type: ne.ALL_AGENTS,
            icon: p(D),
            iconActive: p(D),
            name: e("components.index_layout.all_agents"),
            color: "#606366",
            bgColor: "#F5F5F5",
            darkColor: "#fff",
            darkBgColor: "#60636626",
            path: "/agents",
            isNew: !1,
            needLogin: !0
        }]), a = () => {
            const e = t.value.find((e => e.id === _.MEETING_NOTES));
            e && (e.hide = !1)
        }, n = () => {
            if ("true" === localStorage.getItem("support_meeting_notes")) return a(), !0
        };
        n(), g((() => o.value), (e => {
            if (e) {
                if (n()) return;
                o.value.callHandler("support", {
                    api: "startRecording"
                }, (e => {
                    e && (localStorage.setItem("support_meeting_notes", "true"), a())
                }))
            }
        }), {
            immediate: !0
        });
        return {
            getAgentListPopover: () => [_.SUPER_AGENT, _.GENERATE_SLIDE, _.SHEETS_AGENT, _.DOCS_AGENT, _.AI_DEVELOPER, _.AI_PODCASTS, _.GENERAL_CHAT, _.GENERATE_IMAGE, _.GENERATE_VIDEO, _.DEEP_RESEARCH, _.FACT_CHECK, _.CALL_FOR_ME, _.DOWNLOAD_FOR_ME, _.AI_INBOX].map((e => t.value.find((o => o.id === e && !o.hide)))).filter(Boolean),
            getAgentListIndex: () => [_.GENERATE_SLIDE, _.SHEETS_AGENT, _.DOCS_AGENT, _.AI_DEVELOPER, _.AI_PODCASTS, _.GENERAL_CHAT, _.GENERATE_IMAGE, _.GENERATE_VIDEO, _.DEEP_RESEARCH, _.DOWNLOAD_FOR_ME, _.ALL_AGENTS].map((e => t.value.find((o => o.id === e && !o.hide)))).filter(Boolean),
            appAgentList: l((() => [_.MEETING_NOTES, _.GENERATE_SLIDE, _.SHEETS_AGENT, _.DOCS_AGENT, _.AI_DEVELOPER, _.AI_PODCASTS, _.GENERAL_CHAT, _.GENERATE_IMAGE, _.DEEP_RESEARCH, _.FACT_CHECK, _.CALL_FOR_ME, _.DOWNLOAD_FOR_ME, _.TRANSLATION, _.GENERATE_VIDEO, _.AIDRIVE].map((e => t.value.find((o => o.id === e && !o.hide)))).filter(Boolean))),
            agentsPageList: l((() => [_.SUPER_AGENT, _.MEETING_NOTES, _.GENERATE_SLIDE, _.SHEETS_AGENT, _.DOCS_AGENT, _.AI_DEVELOPER, _.AI_PODCASTS, _.GENERAL_CHAT, _.GENERATE_IMAGE, _.GENERATE_VIDEO, _.DEEP_RESEARCH, _.FACT_CHECK, _.CALL_FOR_ME, _.DOWNLOAD_FOR_ME, _.AI_INBOX, _.TRANSLATION].map((e => t.value.find((o => o.id === e && !o.hide)))).filter(Boolean)))
        }
    };
export {
    $ as A, M as H, j as I, b as M, x as _, P as a, z as b, q as c, H as d, Y as e, ne as f, se as u
};