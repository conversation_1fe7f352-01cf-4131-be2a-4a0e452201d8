import {
    J as a,
    r as l,
    h as e,
    d as r,
    K as t
} from "./Cf0SOiw0.js";
const o = Symbol.for("nuxt:client-only"),
    s = a({
        name: "ClientOnly",
        inheritAttrs: !1,
        props: ["fallback", "placeholder", "placeholderTag", "fallbackTag"],
        setup(a, {
            slots: s,
            attrs: n
        }) {
            const c = l(!1);
            return e((() => {
                c.value = !0
            })), t(o, !0), a => {
                var l;
                if (c.value) return null == (l = s.default) ? void 0 : l.call(s);
                const e = s.fallback || s.placeholder;
                if (e) return e();
                const t = a.fallback || a.placeholder || "",
                    o = a.fallbackTag || a.placeholderTag || "span";
                return r(o, n, t)
            }
        }
    });
export {
    s as _
};