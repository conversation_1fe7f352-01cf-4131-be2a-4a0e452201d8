import {
    d as C,
    b as t,
    o as r
} from "./Cf0SOiw0.js";
const l = {
    width: "17",
    height: "16",
    viewBox: "0 0 17 16",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const o = {
    render: function(o, i) {
        return r(), C("svg", l, i[0] || (i[0] = [t("g", {
            "clip-path": "url(#clip0_227_2842)"
        }, [t("path", {
            d: "M8.55556 3C8.69716 3.00016 8.83335 3.05438 8.93632 3.15158C9.03928 3.24879 9.10124 3.38164 9.10954 3.523C9.11784 3.66435 9.07185 3.80354 8.98096 3.91213C8.89008 4.02071 8.76116 4.0905 8.62056 4.10722L8.55556 4.11111H4.11111V11.8889H11.8889V7.44444C11.889 7.30285 11.9433 7.16665 12.0405 7.06368C12.1377 6.96072 12.2705 6.89876 12.4119 6.89046C12.5532 6.88216 12.6924 6.92815 12.801 7.01904C12.9096 7.10992 12.9794 7.23884 12.9961 7.37945L13 7.44444V11.8889C13.0001 12.1692 12.8942 12.4392 12.7036 12.6447C12.513 12.8503 12.2518 12.9762 11.9722 12.9972L11.8889 13H4.11111C3.83079 13.0001 3.5608 12.8942 3.35525 12.7036C3.14971 12.513 3.0238 12.2518 3.00278 11.9722L3 11.8889V4.11111C2.99991 3.83079 3.10578 3.5608 3.29639 3.35525C3.48699 3.14971 3.74825 3.0238 4.02778 3.00278L4.11111 3H8.55556ZM12.0239 3.19056C12.1239 3.09092 12.258 3.03307 12.3991 3.02876C12.5402 3.02445 12.6776 3.07401 12.7835 3.16736C12.8894 3.26071 12.9557 3.39086 12.9691 3.53137C12.9825 3.67189 12.9419 3.81223 12.8556 3.92389L12.8094 3.97667L7.30944 9.47611C7.20947 9.57575 7.07531 9.6336 6.93423 9.6379C6.79315 9.64221 6.65572 9.59266 6.54985 9.49931C6.44398 9.40595 6.37761 9.2758 6.36422 9.13529C6.35084 8.99478 6.39144 8.85444 6.47778 8.74278L6.52389 8.69056L12.0239 3.19056Z",
            fill: "currentColor"
        })], -1), t("defs", null, [t("clipPath", {
            id: "clip0_227_2842"
        }, [t("rect", {
            width: "16",
            height: "16",
            fill: "--background-color",
            transform: "translate(0.5)"
        })])], -1)]))
    }
};
export {
    o as E
};