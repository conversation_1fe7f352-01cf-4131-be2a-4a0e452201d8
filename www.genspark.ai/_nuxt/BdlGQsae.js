import {
    d as e,
    D as t,
    o as r,
    b as o,
    W as n,
    r as C,
    C as s,
    ax as i
} from "./Cf0SOiw0.js";
import {
    d as l
} from "./DOnko34f.js";
const a = {
    width: "29",
    height: "28",
    viewBox: "0 0 29 28",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const d = {
        render: function(o, n) {
            return r(), e("svg", a, n[0] || (n[0] = [t('<g id="translate"><path id="Vector" d="M15 23L16.3333 20M16.3333 20L19 14L21.6667 20M16.3333 20H21.6667M23 23L21.6667 20" stroke="currentColor" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round"></path><path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M9.47057 3.10294C9.92083 3.27844 10.1436 3.78571 9.96807 4.23596C9.92432 4.34822 9.87895 4.47359 9.83299 4.61084C10.5177 4.49361 11.2291 4.30814 11.9357 4.02815C12.3853 3.84998 12.8906 4.07078 13.0643 4.52132C13.238 4.97186 13.0143 5.48152 12.5646 5.65969C11.4722 6.09258 10.3803 6.33038 9.36987 6.44634C9.33479 6.63788 9.30228 6.836 9.27314 7.0397C9.66208 6.94899 10.0682 6.90736 10.4878 6.92732C10.5603 6.54623 10.8818 6.24657 11.287 6.21847C11.7691 6.18503 12.187 6.54874 12.2204 7.03083C12.2277 7.13582 12.2317 7.24427 12.2326 7.35572C13.1136 7.73462 13.7707 8.27662 14.2103 8.93603C14.7451 9.7383 14.9095 10.6417 14.8438 11.4776C14.7785 12.3073 14.4857 13.099 14.0658 13.728C13.657 14.3403 13.059 14.896 12.3231 15.0855C11.8551 15.206 11.378 14.9243 11.2575 14.4563C11.137 13.9884 11.4187 13.5113 11.8867 13.3908C12.0567 13.347 12.3388 13.163 12.6104 12.7563C12.8707 12.3664 13.0583 11.8602 13.0992 11.3404C13.1396 10.8269 13.0359 10.3293 12.7542 9.90672C12.5914 9.66256 12.3526 9.41842 12.0052 9.20303C11.8159 10.0004 11.5097 10.8244 11.0911 11.5702C10.7774 12.1291 10.3811 12.6751 9.8993 13.1379C9.91511 13.1736 9.93119 13.2093 9.94755 13.2448C10.1497 13.6837 9.95781 14.2034 9.51889 14.4056C9.129 14.5852 8.67536 14.4538 8.43716 14.1146C7.95 14.3174 7.41081 14.4348 6.81826 14.4348C6.26364 14.4348 5.73611 14.2812 5.33415 13.9056C4.93485 13.5324 4.76683 13.0402 4.7294 12.5736C4.65761 11.6786 5.03789 10.6635 5.59307 9.80266C6.03115 9.12335 6.65734 8.42912 7.41995 7.89247C7.45573 7.42325 7.51072 6.96959 7.57893 6.53875C6.88676 6.5321 6.27744 6.47812 5.791 6.41299C5.31372 6.34909 4.98143 5.90797 5.0488 5.42772C5.11617 4.94746 5.5577 4.60994 6.03498 4.67383C6.54892 4.74264 7.20808 4.79554 7.95365 4.78317C8.07875 4.32408 8.21136 3.9242 8.33755 3.60044C8.51304 3.15019 9.02031 2.92745 9.47057 3.10294ZM7.42171 10.2583C7.29369 10.4153 7.17397 10.5802 7.06377 10.7511C6.6124 11.451 6.44521 12.0774 6.47379 12.4337C6.48015 12.5128 6.495 12.5627 6.50681 12.5905C6.51767 12.6161 6.52636 12.6245 6.52905 12.627C6.53147 12.6293 6.54435 12.6413 6.58289 12.6544C6.624 12.6684 6.69835 12.6848 6.81826 12.6848C7.16861 12.6848 7.49992 12.6122 7.8106 12.476C7.60522 11.738 7.48257 10.9908 7.42171 10.2583ZM9.29994 11.1437C9.39215 11.0077 9.48056 10.8642 9.56507 10.7136C9.9301 10.0633 10.1833 9.34486 10.3299 8.67933C9.91826 8.67538 9.512 8.76603 9.12477 8.93052C9.11634 9.66073 9.16726 10.4083 9.29994 11.1437Z" fill="currentColor"></path><path id="Vector_2" d="M5.16602 19.25C5.16602 20.8886 5.16602 21.7078 5.55925 22.2963C5.72949 22.5511 5.94824 22.7698 6.20302 22.9401C6.79154 23.3333 7.61081 23.3333 9.24935 23.3333" stroke="currentColor" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round"></path><path id="Vector_3" d="M23.8333 8.7513C23.8333 7.11277 23.8333 6.2935 23.4401 5.70497C23.2698 5.4502 23.0511 5.23145 22.7963 5.06121C22.2078 4.66797 21.3886 4.66797 19.75 4.66797" stroke="currentColor" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round"></path></g>', 1)]))
        }
    },
    u = {
        width: "28",
        height: "28",
        viewBox: "0 0 28 28",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const h = {
        render: function(t, n) {
            return r(), e("svg", u, n[0] || (n[0] = [o("path", {
                d: "M16.8623 15.0439C16.6855 15.7751 15.8503 16.2918 14.1799 17.3252C12.565 18.3241 11.7576 18.8237 11.1069 18.6229C10.8379 18.5398 10.5928 18.3822 10.3951 18.165C9.91699 17.6398 9.91699 16.6209 9.91699 14.5833C9.91699 12.5457 9.91699 11.5269 10.3951 11.0016C10.5928 10.7844 10.8379 10.6268 11.1069 10.5438C11.7576 10.343 12.565 10.8425 14.1799 11.8414C15.8503 12.8749 16.6855 13.3916 16.8623 14.1227C16.9352 14.4245 16.9352 14.7421 16.8623 15.0439Z",
                stroke: "currentColor",
                "stroke-width": "1.8",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M24.497 12.8333C24.4997 13.3818 24.4997 13.9642 24.4997 14.5833C24.4997 19.808 24.4997 22.4204 22.8766 24.0436C21.2534 25.6667 18.641 25.6667 13.4163 25.6667C8.19161 25.6667 5.57925 25.6667 3.95612 24.0436C2.33301 22.4204 2.33301 19.808 2.33301 14.5833C2.33301 9.3586 2.33301 6.74624 3.95612 5.12311C5.57925 3.5 8.19161 3.5 13.4163 3.5C14.0355 3.5 14.6179 3.5 15.1663 3.5027",
                stroke: "currentColor",
                "stroke-width": "1.8",
                "stroke-linecap": "round"
            }, null, -1), o("path", {
                d: "M21.5833 2.33594L21.8842 3.14914C22.2788 4.21546 22.4761 4.74863 22.865 5.13756C23.254 5.5265 23.7872 5.72379 24.8535 6.11836L25.6667 6.41927L24.8535 6.72018C23.7872 7.11476 23.254 7.31205 22.865 7.70098C22.4761 8.08991 22.2788 8.62308 21.8842 9.6894L21.5833 10.5026L21.2824 9.6894C20.8879 8.62308 20.6906 8.08991 20.3016 7.70098C19.9127 7.31205 19.3795 7.11476 18.3132 6.72018L17.5 6.41927L18.3132 6.11836C19.3795 5.72379 19.9127 5.5265 20.3016 5.13756C20.6906 4.74863 20.8879 4.21546 21.2824 3.14914L21.5833 2.33594Z",
                stroke: "currentColor",
                "stroke-width": "1.8",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    c = {
        width: "24",
        height: "25",
        viewBox: "0 0 24 25",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const p = {
        render: function(t, n) {
            return r(), e("svg", c, n[0] || (n[0] = [o("g", {
                id: "Frame"
            }, [o("path", {
                id: "Vector",
                d: "M20.8921 10.7994C21.0185 9.95149 20.977 9.08344 20.7677 8.24879C20.4913 7.14619 19.9219 6.10199 19.0597 5.23974C18.1974 4.3775 17.1532 3.80814 16.0506 3.53167C15.216 3.32238 14.3479 3.28093 13.5 3.40732",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }), o("path", {
                id: "Vector_2",
                d: "M17.0992 10.4017C17.2346 9.49343 16.9527 8.53493 16.2535 7.83573C15.5542 7.13653 14.5957 6.85458 13.6875 6.98998",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }), o("path", {
                id: "Vector_3",
                d: "M7.1893 4.89453C7.5526 4.89453 7.8873 5.09154 8.0636 5.40916L9.28685 7.61257C9.447 7.90107 9.4545 8.25002 9.30695 8.54517L8.12855 10.902C8.12855 10.902 8.47005 12.6577 9.89925 14.0869C11.3285 15.5161 13.0783 15.8517 13.0783 15.8517L15.4348 14.6735C15.7301 14.5258 16.0793 14.5335 16.3679 14.6939L18.5776 15.9224C18.8949 16.0988 19.0917 16.4334 19.0917 16.7964V19.3332C19.0917 20.625 17.8917 21.5581 16.6677 21.145C14.1537 20.2968 10.2514 18.6817 7.77795 16.2082C5.3045 13.7348 3.68941 9.83247 2.84114 7.31852C2.42813 6.09447 3.36117 4.89453 4.653 4.89453H7.1893Z",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linejoin": "round"
            })], -1)]))
        }
    },
    w = {
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const k = {
        render: function(o, n) {
            return r(), e("svg", w, n[0] || (n[0] = [t('<path d="M20.9965 11C20.9988 11.4701 20.9988 11.9693 20.9988 12.5C20.9988 16.9783 20.9988 19.2175 19.6076 20.6088C18.2163 22 15.9771 22 11.4988 22C7.02044 22 4.78127 22 3.39002 20.6088C1.99878 19.2175 1.99878 16.9783 1.99878 12.5C1.99878 8.02166 1.99878 5.78249 3.39002 4.39124C4.78127 3 7.02044 3 11.4988 3C12.0295 3 12.5287 3 12.9988 3.00231" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path d="M7 13H16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path d="M7 16H12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path d="M7 10H16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path><path d="M18.5 2L18.7579 2.69703C19.0961 3.61102 19.2652 4.06802 19.5986 4.40139C19.932 4.73477 20.389 4.90387 21.303 5.24208L22 5.5L21.303 5.75792C20.389 6.09613 19.932 6.26524 19.5986 6.59861C19.2652 6.93198 19.0961 7.38898 18.7579 8.30297L18.5 9L18.2421 8.30297C17.9039 7.38898 17.7348 6.93198 17.4014 6.59861C17.068 6.26524 16.611 6.09613 15.697 5.75792L15 5.5L15.697 5.24208C16.611 4.90387 17.068 4.73477 17.4014 4.40139C17.7348 4.06802 17.9039 3.61102 18.2421 2.69703L18.5 2Z" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"></path>', 5)]))
        }
    },
    v = {
        width: "28",
        height: "28",
        viewBox: "0 0 28 28",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const L = {
        render: function(o, n) {
            return r(), e("svg", v, n[0] || (n[0] = [t('<g id="SheetIcon"><g id="Sidebar_sheet_icon"><path id="Vector" d="M22.9474 4H5.05324C4.4719 4 4.00064 4.47126 4.00064 5.0526V22.9468C4.00064 23.5281 4.4719 23.9993 5.05324 23.9993H22.9474C23.5287 23.9993 24 23.5281 24 22.9468V5.0526C24 4.47126 23.5287 4 22.9474 4Z" stroke="currentColor" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round"></path><path id="Vector_2" d="M4.00064 10.8359H24" stroke="currentColor" stroke-width="1.75" stroke-linecap="round"></path><path id="Vector_3" d="M4.00064 17.1641H24" stroke="currentColor" stroke-width="1.75" stroke-linecap="round"></path><path id="Vector_4" d="M10.3162 4V23.9993" stroke="currentColor" stroke-width="1.75" stroke-linecap="round"></path><path id="Vector_5" d="M17.1576 4V23.9993" stroke="currentColor" stroke-width="1.75" stroke-linecap="round"></path></g></g>', 1)]))
        }
    },
    g = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const M = {
        render: function(t, n) {
            return r(), e("svg", g, n[0] || (n[0] = [o("path", {
                d: "M20.9957 11C20.998 11.4701 20.998 11.9693 20.998 12.5C20.998 16.9783 20.998 19.2175 19.6068 20.6088C18.2155 22 15.9763 22 11.498 22C7.01971 22 4.78054 22 3.38929 20.6088C1.99805 19.2175 1.99805 16.9783 1.99805 12.5C1.99805 8.02166 1.99805 5.78249 3.38929 4.39124C4.78054 3 7.01971 3 11.498 3C12.0287 3 12.5279 3 12.998 3.00231",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round"
            }, null, -1), o("path", {
                d: "M2 13H5.33784C5.88261 13 6.40506 13.2107 6.79027 13.5858C7.17548 13.9609 7.39189 14.4696 7.39189 15C7.39189 15.5304 7.6083 16.0391 7.99351 16.4142C8.37872 16.7893 8.90118 17 9.44595 17H13.5541C14.0988 17 14.6213 16.7893 15.0065 16.4142C15.3917 16.0391 15.6081 15.5304 15.6081 15C15.6081 14.4696 15.8245 13.9609 16.2097 13.5858C16.5949 13.2107 17.1174 13 17.6622 13H21",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M18.5 2L18.7579 2.69703C19.0961 3.61102 19.2652 4.06802 19.5986 4.40139C19.932 4.73477 20.389 4.90387 21.303 5.24208L22 5.5L21.303 5.75792C20.389 6.09613 19.932 6.26524 19.5986 6.59861C19.2652 6.93198 19.0961 7.38898 18.7579 8.30297L18.5 9L18.2421 8.30297C17.9039 7.38898 17.7348 6.93198 17.4014 6.59861C17.068 6.26524 16.611 6.09613 15.697 5.75792L15 5.5L15.697 5.24208C16.611 4.90387 17.068 4.73477 17.4014 4.40139C17.7348 4.06802 17.9039 3.61102 18.2421 2.69703L18.5 2Z",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    f = {
        width: "28",
        height: "28",
        viewBox: "0 0 28 28",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const m = {
        render: function(t, n) {
            return r(), e("svg", f, n[0] || (n[0] = [o("path", {
                d: "M25.334 13.4962C25.334 19.6598 20.1099 24.6574 13.6673 24.6574C12.9098 24.6582 12.1545 24.5882 11.4103 24.4487C10.8747 24.3479 10.6068 24.2976 10.42 24.3263C10.2331 24.3547 9.96792 24.4958 9.43805 24.7775C7.9392 25.5746 6.19132 25.8561 4.51026 25.5434C5.14916 24.7577 5.5855 23.8148 5.7781 22.8039C5.89477 22.1857 5.60565 21.585 5.17271 21.1453C3.20613 19.1484 2.00065 16.4576 2.00065 13.4962C2.00065 7.33255 7.22477 2.33496 13.6673 2.33496C20.1099 2.33496 25.334 7.33255 25.334 13.4962Z",
                stroke: "currentColor",
                "stroke-width": "1.75",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M8.46484 17.0418L9.56535 14.8407M9.56535 14.8407L11.7665 10.4385L13.9677 14.8407M9.56535 14.8407H13.9677M15.0682 17.0418L13.9677 14.8407",
                stroke: "currentColor",
                "stroke-width": "1.65083",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), o("path", {
                d: "M18.3711 10.4385L18.3711 17.0418",
                stroke: "currentColor",
                "stroke-width": "1.65083",
                "stroke-linecap": "round"
            }, null, -1)]))
        }
    },
    _ = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const S = {
        render: function(t, n) {
            return r(), e("svg", _, n[0] || (n[0] = [o("path", {
                d: "M12.9426 11.2539C13.3209 11.2923 13.6165 11.6116 13.6165 12C13.6165 12.3884 13.3209 12.7077 12.9426 12.7461L12.8665 12.75H12.5754C12.0275 12.7501 11.5834 13.1943 11.5833 13.7422C11.5833 14.2902 12.0275 14.7342 12.5754 14.7344H12.8665L12.9426 14.7383C13.3208 14.7766 13.6162 15.0962 13.6165 15.4844C13.6165 15.8728 13.3209 16.1931 12.9426 16.2314L12.8665 16.2344H12.5754C11.199 16.2342 10.0833 15.1186 10.0833 13.7422C10.0834 12.3659 11.1991 11.2501 12.5754 11.25H12.8665L12.9426 11.2539ZM16.1233 11.25C17.4997 11.25 18.6163 12.3658 18.6165 13.7422C18.6165 15.1187 17.4998 16.2344 16.1233 16.2344H15.8333C15.419 16.2344 15.0833 15.8986 15.0833 15.4844C15.0835 15.0704 15.4192 14.7344 15.8333 14.7344H16.1233C16.6714 14.7344 17.1165 14.2903 17.1165 13.7422C17.1163 13.1942 16.6713 12.75 16.1233 12.75H15.8333C15.419 12.75 15.0833 12.4142 15.0833 12C15.0833 11.5858 15.419 11.25 15.8333 11.25H16.1233ZM9.92407 1.75C11.7063 1.75 13.1554 2.36309 14.1936 3.38867C15.0737 4.25804 15.6251 5.39135 15.8586 6.61719C16.7908 6.7545 17.4919 7.22448 18.012 7.84668C18.6051 8.55635 18.9566 9.45407 19.2112 10.2178C19.342 10.6106 19.1294 11.0351 18.7366 11.166C18.3437 11.2967 17.9193 11.0842 17.7883 10.6914C17.543 9.95538 17.2626 9.28861 16.8616 8.80859C16.4888 8.36261 16.0121 8.0752 15.2854 8.0752H15.2053C14.82 8.0752 14.4965 7.78286 14.4583 7.39941C14.341 6.22562 13.8831 5.19118 13.1389 4.45605C12.403 3.72913 11.3429 3.25 9.92407 3.25C8.50356 3.25002 7.44399 3.72591 6.70923 4.4502C5.96645 5.18245 5.50836 6.21631 5.38989 7.39941C5.35153 7.78268 5.02895 8.07503 4.6438 8.0752H4.56372C2.93575 8.07531 1.58346 9.43688 1.58325 11.1621C1.58325 12.8874 2.93784 14.25 4.56274 14.25H8.50024L8.57642 14.2539C8.95468 14.2922 9.25024 14.6116 9.25024 15C9.25024 15.3884 8.95468 15.7078 8.57642 15.7461L8.50024 15.75H4.56274C2.06951 15.75 0.083252 13.6754 0.083252 11.1621C0.0834417 8.84492 1.77023 6.90116 3.99048 6.6123C4.22484 5.3823 4.77623 4.24955 5.65649 3.38184C6.69527 2.3579 8.14356 1.75002 9.92407 1.75ZM15.7336 12.9961C16.1116 13.0347 16.4065 13.354 16.4065 13.7422C16.4065 14.1304 16.1116 14.4497 15.7336 14.4883L15.6565 14.4922H13.3333C12.919 14.4922 12.5833 14.1564 12.5833 13.7422C12.5833 13.328 12.919 12.9922 13.3333 12.9922H15.6565L15.7336 12.9961Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    H = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const V = {
        render: function(t, n) {
            return r(), e("svg", H, n[0] || (n[0] = [o("path", {
                d: "M18.3151 1.97806C19.2469 1.72143 20.2002 2.27134 20.444 3.20658L22.0309 9.29252C22.2744 10.2276 21.7163 11.1941 20.7848 11.4507L17.2887 12.4136C16.8897 12.5232 16.4812 12.2876 16.3766 11.8872L16.2037 11.2251L12.7496 11.9292V14.73L17.5612 20.5054C17.8269 20.8243 17.7913 21.3045 17.4811 21.5777C17.171 21.8505 16.7049 21.813 16.4391 21.4947L12.7496 17.0669V19.9995C12.7496 20.4138 12.4139 20.7495 11.9996 20.7495C11.5856 20.7494 11.2496 20.4136 11.2496 19.9995V17.0669L7.56117 21.4947C7.29548 21.8131 6.82929 21.8504 6.51918 21.5777C6.20904 21.3046 6.17267 20.8243 6.43812 20.5054L11.2496 14.731V12.2349L4.44984 13.6226C3.54883 13.806 2.66841 13.2564 2.43617 12.3658L1.97816 10.6089C1.74643 9.71839 2.24152 8.78994 3.11097 8.48588L14.4567 4.52103L14.2858 3.86576C14.1813 3.46516 14.42 3.05023 14.819 2.93998L18.3151 1.97806ZM3.58558 9.90873C3.46194 9.95244 3.39154 10.0846 3.42445 10.2115L3.88246 11.9673C3.91566 12.0944 4.04101 12.173 4.16957 12.147L5.9723 11.7788L5.32582 9.29935L3.58558 9.90873ZM6.74574 8.80326L7.44301 11.4781L15.8248 9.76908L14.8356 5.97513L6.74574 8.80326ZM18.694 3.42924L15.9205 4.19291L16.07 4.76322C16.0739 4.7755 16.0784 4.78779 16.0817 4.80033L17.4752 10.148C17.4784 10.1603 17.4805 10.1737 17.483 10.1861L17.6334 10.7632L20.4059 9.99955C20.5389 9.96293 20.6191 9.82441 20.5846 9.69096L18.9977 3.60502C18.9629 3.47155 18.827 3.39284 18.694 3.42924Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    x = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "28",
        height: "28",
        viewBox: "0 0 28 28",
        fill: "none"
    };
const b = {
        render: function(t, n) {
            return r(), e("svg", x, n[0] || (n[0] = [o("path", {
                d: "M13.91 25C13.7193 25 13.5582 24.8601 13.5285 24.6694C12.2865 16.4602 11.3879 15.7355 3.2364 14.5065C2.9863 14.4683 2.7998 14.2522 2.7998 13.9979C2.7998 13.7436 2.9863 13.5275 3.2364 13.4893C11.3455 12.2645 12.0661 11.5398 13.2911 3.43652C13.3293 3.18648 13.5455 3 13.7998 3C14.0541 3 14.2703 3.18648 14.3085 3.43652C15.5335 11.5398 16.2584 12.2645 24.3632 13.4893C24.6133 13.5275 24.7998 13.7436 24.7998 13.9979C24.7998 14.2522 24.6133 14.4683 24.3632 14.5065C16.2202 15.7355 15.525 16.4602 14.2915 24.6694C14.2618 24.8559 14.1008 25 13.91 25Z",
                stroke: "currentColor",
                "stroke-width": "1.75"
            }, null, -1)]))
        }
    },
    j = n("slides-base", (() => {
        const e = C({}),
            t = C(""),
            r = C(0),
            o = C(!s.isMobile()),
            n = C([]),
            i = C(""),
            l = C(!1),
            a = C(null);
        return {
            currentVersion: e,
            domPath: t,
            pageNumber: r,
            slideCanvasVisible: o,
            setCurrentVersion: t => {
                e.value = t
            },
            setDomPath: e => {
                t.value = e
            },
            setPageNumber: e => {
                r.value = e
            },
            setSlideCanvasVisible: e => {
                o.value = e
            },
            localVersionHistory: n,
            setLocalVersionHistory: e => {
                n.value = e
            },
            domBase64: i,
            setDomBase64: e => {
                i.value = e
            },
            isShowRevertVersionModal: l,
            setIsShowRevertVersionModal: e => {
                l.value = e
            },
            selectedTemplate: a,
            setSelectedTemplate: e => {
                a.value = e
            }
        }
    }));
let Z = null;
const y = n("slideEdit", (() => {
    const e = C("normal"),
        t = C(!1),
        r = C(!1),
        o = C(!1),
        n = C({}),
        s = C(""),
        a = C({}),
        d = C(!1),
        u = j(),
        {
            currentVersion: h
        } = i(u);
    let c = new Map;
    const p = e => {
            s.value = e
        },
        w = l((async e => {
            var t, r, o;
            let n = JSON.parse(JSON.stringify(h.value.fileContents));
            n.forEach(((e, t) => {
                c.has(e.cdn_url) && (e.content = c.get(e.cdn_url), e.page_index = t, e.is_edited = !0, e.action = "update")
            })), n = n.filter((e => e.is_edited));
            let C = JSON.parse(JSON.stringify(e));
            C.page_num = n.length, p("saving");
            let s = {
                project_id: new URLSearchParams(window.location.search).get("id") || route.query.id,
                updated_file_contents: n,
                updated_meta_data: C,
                is_manual_edit: !0
            };
            h.value.version_number > 0 && (s.version_number = h.value.version_number);
            try {
                Z && Z.abort(), Z = new AbortController;
                const e = Z.signal,
                    n = await fetch("/api/project/save_slide_data", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify(s),
                        signal: e
                    }),
                    C = await n.json();
                0 === C.status ? (p("saved"), (null == (o = null == (r = null == (t = null == C ? void 0 : C.data) ? void 0 : t.session_state) ? void 0 : r.version_history) ? void 0 : o.length) > 0 && u.setLocalVersionHistory(C.data.session_state.version_history)) : p("error")
            } catch (i) {
                "AbortError" === i.name || p("error")
            }
        }), 1e3);
    return {
        editSlideStatus: e,
        isIframeLoadSuccess: r,
        setEditSlideStatus: t => {
            e.value = t
        },
        setIsIframeLoadSuccess: e => {
            r.value = e
        },
        isDisableInputPrompt: t,
        setIsDisableInputPrompt: e => {
            t.value = e
        },
        isShowRollbackBtn: o,
        setIsShowRollbackBtn: e => {
            o.value = e
        },
        curEventData: n,
        setCurEventData: e => {
            n.value = e
        },
        saveStatus: s,
        setSaveStatus: p,
        handleSaveSlides: e => {
            s.value = "", w(e)
        },
        fileContentsUpdate: c,
        curTextStyle: a,
        setCurTextStyle: e => {
            (null == e ? void 0 : e.fontFamily) && "undefined" === (null == e ? void 0 : e.fontFamily) && (e.fontFamily = null), a.value = e
        },
        isShowPopover: d,
        setIsShowPopover: e => {
            d.value = e
        }
    }
}));
export {
    b as A, p as C, k as D, h as G, M as I, L as S, d as T, m as a, V as b, S as c, j as d, y as u
};