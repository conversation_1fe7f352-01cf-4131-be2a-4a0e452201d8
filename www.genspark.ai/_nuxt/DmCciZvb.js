var e = Object.defineProperty,
    t = (t, i, s) => ((t, i, s) => i in t ? e(t, i, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: s
    }) : t[i] = s)(t, "symbol" != typeof i ? i + "" : i, s);
import {
    W as i,
    r as s,
    c as a,
    d as l,
    o as n,
    b as o,
    J as r,
    s as u,
    g as c,
    i as d,
    A as h,
    v as p,
    f as v,
    a9 as f,
    t as m,
    y as g,
    H as _,
    L as y,
    l as w,
    n as b,
    e as $,
    F as x,
    k,
    a as S,
    U as C,
    _ as T
} from "./Cf0SOiw0.js";
import {
    a as A
} from "./FCN43o2W.js";
import {
    B as z
} from "./CAfqOhBF.js";
import {
    C as O
} from "./Chtxu0jj.js";
import {
    F as P
} from "./LGmiBiLz.js";
import {
    c as D,
    b as L,
    i as E,
    d as M
} from "./zH1ZpJ79.js";
const U = i("aidrive", {
        state: () => ({
            currentPath: "/",
            files: [],
            selected: [],
            isLoading: !1,
            error: null,
            lastNavigationTime: 0,
            lastSelected: -1,
            multipleSelectionActive: !1,
            operations: [],
            operationInProgress: !1,
            pendingHighlightFilePath: null
        }),
        getters: {
            selectedCount: e => e.selected.length,
            selectedFiles: e => e.selected.map((t => e.files[t])),
            hasSelectedDirectories: e => e.selected.some((t => {
                var i;
                return null == (i = e.files[t]) ? void 0 : i.isDir
            })),
            allSelectedAreDirectories: e => e.selected.length > 0 && e.selected.every((t => {
                var i;
                return null == (i = e.files[t]) ? void 0 : i.isDir
            })),
            hasActiveOperations: e => e.operations.some((e => "pending" === e.status || "in-progress" === e.status)),
            activeOperationsCount: e => e.operations.filter((e => "pending" === e.status || "in-progress" === e.status)).length
        },
        actions: {
            setLoading(e) {
                this.isLoading = e
            },
            setError(e) {
                this.error = e
            },
            toggleMultipleSelection() {
                this.multipleSelectionActive ? (this.multipleSelectionActive = !1, this.selected = [], this.lastSelected = -1) : this.multipleSelectionActive = !0
            },
            handleItemClick(e, t) {
                const i = t.shiftKey;
                i || !this.multipleSelectionActive || 1 !== this.selected.length || this.selected[0] !== e ? (this.multipleSelectionActive || (this.multipleSelectionActive = !0), i && this.lastSelected >= 0 ? this.selectRange(this.lastSelected, e) : this.toggleSelect(e)) : this.clearSelection()
            },
            selectOnly(e) {
                e >= 0 && e < this.files.length && (this.selected = [e], this.lastSelected = e)
            },
            toggleSelect(e) {
                if (e < 0 || e >= this.files.length) return;
                const t = this.selected.indexOf(e); - 1 === t ? this.selected.push(e) : this.selected.splice(t, 1), this.lastSelected = e
            },
            selectRange(e, t) {
                if (e < 0 || t < 0 || e >= this.files.length || t >= this.files.length) return;
                const i = Math.min(e, t),
                    s = Math.max(e, t);
                for (let a = i; a <= s; a++) - 1 === this.selected.indexOf(a) && this.selected.push(a)
            },
            handleEmptyAreaClick() {
                1 !== this.selected.length || this.multipleSelectionActive || (this.selected = [], this.lastSelected = -1)
            },
            selectAll() {
                this.selected = this.files.map(((e, t) => t)), this.files.length > 0 && (this.lastSelected = 0, this.multipleSelectionActive || (this.multipleSelectionActive = !0))
            },
            clearSelection() {
                this.selected = [], this.lastSelected = -1, this.multipleSelectionActive && (this.multipleSelectionActive = !1)
            },
            addOperation(e) {
                const t = `op-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;
                return this.operations.push({ ...e,
                    id: t,
                    timestamp: Date.now()
                }), this.operationInProgress = !0, t
            },
            updateOperation(e, t) {
                const i = this.operations.findIndex((t => t.id === e));
                i >= 0 && (this.operations[i] = { ...this.operations[i],
                    ...t
                }, this.operationInProgress = this.hasActiveOperations)
            },
            removeOperation(e) {
                const t = this.operations.findIndex((t => t.id === e));
                t >= 0 && (this.operations.splice(t, 1), this.operationInProgress = this.hasActiveOperations)
            },
            clearCompletedOperations() {
                this.operations = this.operations.filter((e => "pending" === e.status || "in-progress" === e.status))
            },
            async navigateToPath(e) {
                var t, i, s;
                this.currentPath = e, this.isLoading = !0, this.selected = [], this.lastSelected = -1, this.multipleSelectionActive = !1, this.lastNavigationTime = Date.now();
                const a = this.lastNavigationTime;
                this.files = [];
                try {
                    this.setError(null);
                    const t = await A.fetch(e);
                    this.lastNavigationTime === a && (this.files = t || [])
                } catch (l) {
                    if (this.lastNavigationTime === a) {
                        const e = (null == (i = null == (t = null == l ? void 0 : l.response) ? void 0 : t._data) ? void 0 : i.detail) || (null == (s = null == l ? void 0 : l.response) ? void 0 : s.statusText) || String(l);
                        this.setError(new Error(e))
                    }
                } finally {
                    this.lastNavigationTime === a && (this.isLoading = !1)
                }
            },
            async loadFiles(e) {
                var t, i, s;
                if (e === this.currentPath) {
                    this.isLoading = !0;
                    try {
                        this.setError(null);
                        const t = await A.fetch(e);
                        this.files = t || [], this.selected = [], this.lastSelected = -1, this.multipleSelectionActive = !1
                    } catch (a) {
                        const e = (null == (i = null == (t = null == a ? void 0 : a.response) ? void 0 : t._data) ? void 0 : i.detail) || (null == (s = null == a ? void 0 : a.response) ? void 0 : s.statusText) || String(a);
                        this.setError(new Error(e))
                    } finally {
                        this.isLoading = !1
                    }
                } else this.navigateToPath(e)
            },
            async refreshFiles(e = !1, t = "") {
                var i, s, a;
                if (!this.isLoading) {
                    t || (t = this.currentPath), e && (this.isLoading = !0, this.setError(null));
                    try {
                        const e = await A.fetch(t);
                        decodeURIComponent(this.currentPath) === decodeURIComponent(t) && (this.files = e || [], this.selected = this.selected.filter((e => e < this.files.length)), 0 === this.selected.length && (this.lastSelected = -1, this.multipleSelectionActive = !1))
                    } catch (l) {
                        if (e) {
                            const e = (null == (s = null == (i = null == l ? void 0 : l.response) ? void 0 : i._data) ? void 0 : s.detail) || (null == (a = null == l ? void 0 : l.response) ? void 0 : a.statusText) || String(l);
                            this.setError(new Error(e))
                        }
                    } finally {
                        e && (this.isLoading = !1)
                    }
                }
            },
            async deleteSelected() {
                if (0 === this.selected.length) return;
                const e = this.selected.map((e => this.files[e])),
                    t = this.currentPath,
                    i = this.addOperation({
                        type: "delete",
                        status: "pending",
                        items: e.map((e => e.name)),
                        path: t,
                        progress: 0,
                        itemsLeft: e.length,
                        totalItems: e.length
                    });
                let s = 0,
                    a = 0;
                const l = e.length;
                try {
                    this.updateOperation(i, {
                        status: "in-progress"
                    });
                    for (let t = 0; t < e.length; t++) {
                        const o = e[t];
                        try {
                            await A.deleteItem(o.path, o.isDir), s++, this.updateOperation(i, {
                                progress: (t + 1) / l * 100,
                                itemsLeft: l - (t + 1),
                                message: `Deleting... (${l-(t+1)} items left)`
                            })
                        } catch (n) {
                            a++, this.updateOperation(i, {
                                message: `Deleting... (${a} errors so far)`
                            })
                        }
                    }
                    return await this.refreshFiles(!0), this.multipleSelectionActive = !1, this.selected = [], this.updateOperation(i, {
                        status: "completed",
                        progress: 100,
                        message: `Deleted ${s} of ${l} items`,
                        itemsLeft: 0
                    }), setTimeout((() => {
                        this.removeOperation(i)
                    }), 5e3), {
                        success: s,
                        errors: a,
                        total: l
                    }
                } catch (n) {
                    throw this.updateOperation(i, {
                        status: "error",
                        error: n instanceof Error ? n.message : String(n)
                    }), n
                }
            },
            async moveSelected(e) {
                if (0 === this.selected.length) return null;
                const t = this.selected.map((e => this.files[e])),
                    i = this.currentPath,
                    s = this.addOperation({
                        type: "move",
                        status: "pending",
                        items: t.map((e => e.name)),
                        path: i,
                        progress: 0,
                        itemsLeft: t.length,
                        totalItems: t.length,
                        message: `Moving ${t.length} items to ${e}`
                    });
                let a = 0,
                    l = 0,
                    n = [];
                const o = t.length;
                try {
                    this.updateOperation(s, {
                        status: "in-progress"
                    });
                    for (let i = 0; i < t.length; i++) {
                        const u = t[i],
                            c = u.path,
                            d = `${e}/${u.name}`;
                        try {
                            if (c === d) {
                                n.push(`Cannot move ${u.name} to same location`), l++;
                                continue
                            }
                            if (u.isDir && d.startsWith(c + "/")) {
                                n.push(`Cannot move folder ${u.name} into itself`), l++;
                                continue
                            }
                            await A.moveItem(c, d), a++, this.updateOperation(s, {
                                progress: (i + 1) / o * 100,
                                itemsLeft: o - (i + 1),
                                message: `Moving... (${o-(i+1)} items left)`
                            })
                        } catch (r) {
                            l++, n.push(`Failed to move ${u.name}`), this.updateOperation(s, {
                                message: `Moving... (${l} errors so far)`
                            })
                        }
                    }
                    return await this.refreshFiles(!0), this.multipleSelectionActive = !1, this.selected = [], this.updateOperation(s, {
                        status: "completed",
                        progress: 100,
                        message: `Moved ${a} of ${o} items`,
                        itemsLeft: 0
                    }), setTimeout((() => {
                        this.removeOperation(s)
                    }), 5e3), {
                        success: a,
                        errors: l,
                        errorMessages: n,
                        total: o
                    }
                } catch (r) {
                    throw this.updateOperation(s, {
                        status: "error",
                        error: r instanceof Error ? r.message : String(r)
                    }), r
                }
            },
            async renameItem(e, t) {
                if (e < 0 || e >= this.files.length) return null;
                const i = this.files[e];
                if (!i) return null;
                const s = this.currentPath,
                    a = i.path,
                    l = [...a.split("/").slice(0, -1), t].join("/"),
                    n = this.addOperation({
                        type: "move",
                        status: "pending",
                        items: [i.name],
                        path: s,
                        message: `Renaming ${i.name} to ${t}`
                    });
                try {
                    return this.updateOperation(n, {
                        status: "in-progress"
                    }), await A.moveItem(a, l), await this.refreshFiles(!0), this.selected = [], this.lastSelected = -1, this.updateOperation(n, {
                        status: "completed",
                        progress: 100,
                        message: `Renamed ${i.name} to ${t}`
                    }), setTimeout((() => {
                        this.removeOperation(n)
                    }), 5e3), {
                        success: !0
                    }
                } catch (o) {
                    throw this.updateOperation(n, {
                        status: "error",
                        error: o instanceof Error ? o.message : String(o)
                    }), o
                }
            },
            setPendingHighlightFilePath(e) {
                this.pendingHighlightFilePath = e
            },
            clearPendingHighlightFilePath() {
                this.pendingHighlightFilePath = null
            }
        }
    }),
    F = "PENDING",
    I = "STARTED",
    j = "PROGRESS",
    B = "FINISHED",
    H = "FAILED",
    V = [F, I, j],
    R = [B, H];
class N {
    constructor(e) {
        t(this, "id"), t(this, "type"), t(this, "status"), t(this, "error_message"), t(this, "file_info"), t(this, "path"), t(this, "download_type"), t(this, "url"), t(this, "filename"), t(this, "format_id"), t(this, "mime_type"), t(this, "progress"), t(this, "progress_type"), t(this, "downloaded_bytes"), t(this, "total_bytes"), t(this, "action"), t(this, "source_path"), t(this, "destination_path"), t(this, "processed_files"), t(this, "total_files"), t(this, "current_file_name"), t(this, "output_path"), this.id = e.id || "", this.type = e.type || "download", this.status = e.status || F, this.error_message = e.error_message, this.file_info = e.file_info, this.path = e.path, this.download_type = e.download_type, this.url = e.url, this.filename = e.filename, this.format_id = e.format_id, this.mime_type = e.mime_type, this.progress = e.progress, this.progress_type = e.progress_type, this.downloaded_bytes = e.downloaded_bytes, this.total_bytes = e.total_bytes, this.action = e.action, this.source_path = e.source_path, this.destination_path = e.destination_path, this.processed_files = e.processed_files, this.total_files = e.total_files, this.current_file_name = e.current_file_name, this.output_path = e.output_path
    }
    isFinished() {
        return this.status === B
    }
    isFailed() {
        return this.status === H
    }
    isPending() {
        return this.status === F
    }
    isStarted() {
        return this.status === I
    }
    isProcessing() {
        return this.status === j
    }
    isActive() {
        return [I, j].includes(this.status)
    }
    isTerminal() {
        return R.includes(this.status)
    }
    hasProgress() {
        return "download" === this.type ? void 0 !== this.total_bytes && this.total_bytes > 0 && void 0 !== this.downloaded_bytes && this.downloaded_bytes > 0 : void 0 !== this.total_files && this.total_files > 0 && void 0 !== this.processed_files
    }
    getDisplayName(e) {
        var t;
        if ("download" === this.type) return (null == (t = this.file_info) ? void 0 : t.name) || this.filename || "Unknown file"; {
            if ("compress" === this.action && Array.isArray(this.source_path)) {
                const t = this.source_path.length;
                if (0 === t) return "Compress task";
                const i = this.source_path[0];
                if (1 === t) return i.split("/").pop() || "archive";
                const s = i.substring(0, i.lastIndexOf("/")).split("/").pop() || "archive";
                return e ? e("components.aidrive.compress_display_name", {
                    parentName: s,
                    count: t
                }) : `${s} (${t} items)`
            }
            const t = Array.isArray(this.source_path) ? this.source_path[0] : this.source_path;
            return this.filename || (null == t ? void 0 : t.split("/").pop()) || "Archive task"
        }
    }
}
const K = i("asyncTasks", (() => {
        U();
        const e = s([]),
            t = s(!1),
            i = s(!1),
            l = s(""),
            n = s(null),
            o = s(0),
            r = s(null),
            u = s(new Map),
            c = s(!1),
            d = s(null),
            h = s(!1),
            p = s(!1),
            v = a((() => e.value.some((e => V.includes(e.status))))),
            f = a((() => e.value.filter((e => e.status === F)))),
            m = a((() => e.value.filter((e => [I, j].includes(e.status))))),
            g = a((() => e.value.filter((e => e.status === B)))),
            _ = a((() => e.value.filter((e => e.status === H)))),
            y = a((() => e.value.filter((e => "download" === e.type)))),
            w = a((() => e.value.filter((e => "archive" === e.type)))),
            b = a((() => y.value)),
            $ = a((() => y.value.some((e => V.includes(e.status))))),
            x = a((() => y.value.filter((e => e.status === F)))),
            k = a((() => y.value.filter((e => [I, j].includes(e.status))))),
            S = a((() => y.value.filter((e => e.status === B)))),
            C = a((() => y.value.filter((e => e.status === H)))),
            T = async () => {
                if (!p.value) try {
                    p.value = !0, n.value && (n.value.abort(), n.value = null), t.value = !1, c.value = !1;
                    const s = e.value.filter((e => V.includes(e.status))).map((e => ({
                        id: e.id,
                        type: e.type
                    }))).filter((e => null != e.id && "" !== e.id));
                    if (0 === s.length) return void(p.value = !1);
                    o.value++, n.value = new AbortController, c.value = !0;
                    let a = !1;
                    try {
                        t.value = !0, await A.streamTasks(s, (e => {
                            a = !0, e.tasks ? Object.entries(e.tasks).forEach((([e, t]) => {
                                z(e, t)
                            })) : "TASKS_STATUS_COMPLETE" === e.type && e.final_status && Object.entries(e.final_status).forEach((([e, t]) => {
                                z(e, t)
                            })), c.value = !1
                        }), n.value.signal)
                    } catch (i) {
                        t.value = !1, c.value = !1, n.value = null;
                        e.value.filter((e => !e.isTerminal())).length > 0 && o.value < 3 && setTimeout((() => {
                            T()
                        }), 2e3 * Math.pow(1.5, o.value - 1))
                    }
                } finally {
                    p.value = !1
                }
            },
            z = (t, i) => {
                const s = e.value.findIndex((e => e.id === t));
                if (-1 === s) return;
                const a = { ...e.value[s]
                };
                i.status && (a.status = i.status), void 0 !== i.progress && (a.progress = i.progress), void 0 !== i.downloaded_bytes && (a.downloaded_bytes = i.downloaded_bytes), void 0 !== i.total_bytes && (a.total_bytes = i.total_bytes), void 0 !== i.processed_files && (a.processed_files = i.processed_files), void 0 !== i.total_files && (a.total_files = i.total_files), void 0 !== i.current_file_name && (a.current_file_name = i.current_file_name), i.error_message && (a.error_message = i.error_message), void 0 !== i.progress_type && (a.progress_type = i.progress_type), i.output_path && (a.output_path = i.output_path), i.file_info && (a.file_info = i.file_info);
                const l = new N(a);
                e.value[s] = l, l.isTerminal() && r.value && r.value(l);
                const n = u.value.get(t);
                n && l.isTerminal() && (l.isFinished() ? n.resolve(l) : n.reject(l.error_message || "Task failed without an error message."), u.value.delete(t))
            },
            O = () => {
                t.value || c.value || p.value || (d.value && (clearTimeout(d.value), d.value = null), T())
            },
            P = () => {
                d.value && (clearTimeout(d.value), d.value = null), n.value && (n.value.abort(), n.value = null), t.value = !1, c.value = !1, p.value = !1, o.value = 0
            },
            D = async () => {
                if (!h.value) try {
                    h.value = !0;
                    const t = await A.getInitialTasks();
                    200 === t.status_code && t.tasks && (e.value = t.tasks.map((e => new N(e)))), v.value && (P(), O())
                } catch (t) {
                    i.value = !0, l.value = t instanceof Error ? t.message : "Failed to load tasks"
                } finally {
                    h.value = !1
                }
            };
        return {
            tasks: e,
            isConnected: t,
            hasError: i,
            errorMessage: l,
            hasActiveTasks: v,
            pendingTasks: f,
            runningTasks: m,
            completedTasks: g,
            failedTasks: _,
            downloadTasks: y,
            archiveTasks: w,
            downloads: b,
            hasActiveDownloads: $,
            pendingDownloads: x,
            runningDownloads: k,
            completedDownloads: S,
            failedDownloads: C,
            startPolling: O,
            stopPolling: P,
            loadAsyncTasks: D,
            getTasksByStatus: t => {
                const i = Array.isArray(t) ? t : [t];
                return e.value.filter((e => i.includes(e.status)))
            },
            setTaskCompletedCallback: e => {
                r.value = e
            },
            clearError: () => {
                i.value = !1, l.value = ""
            },
            clearCompletedTasks: async () => {
                const t = e.value.filter((e => R.includes(e.status))).map((e => e.id));
                if (0 !== t.length) {
                    e.value = e.value.filter((e => V.includes(e.status)));
                    try {
                        await A.clearCompletedTasks(t)
                    } catch (i) {}
                }
            },
            cancelTask: async t => {
                try {
                    const i = e.value.find((e => e.id === t));
                    if (!i) return !1;
                    const s = e.value.findIndex((e => e.id === t)); - 1 !== s && e.value.splice(s, 1), P();
                    return 200 === (await A.cancelTask(t, i.type)).status_code
                } catch (i) {
                    return !1
                } finally {
                    D()
                }
            },
            waitForTaskCompletion: t => new Promise(((i, s) => {
                const a = e.value.find((e => e.id === t));
                a && a.isTerminal() ? a.isFinished() ? i(a) : s(a.error_message || "Task failed without an error message.") : u.value.set(t, {
                    resolve: i,
                    reject: s
                })
            }))
        }
    })),
    q = i("aidriveUsage", (() => {
        const e = s(0),
            t = s(0),
            i = s(!1),
            l = s(!1),
            n = s(0),
            o = s(null),
            r = s(null);
        async function u(s = null) {
            if (!i.value) {
                i.value = !0, o.value && clearTimeout(o.value);
                try {
                    const i = await A.getStorageUsage();
                    i && i.data && (e.value = i.data.used_bytes || 0, t.value = i.data.quota_bytes, l.value = !0, n.value = 0)
                } catch (a) {
                    if (n.value < 3) {
                        const e = 3e3 * Math.pow(1.5, n.value);
                        n.value++, o.value = setTimeout((() => {
                            l.value || u(s)
                        }), e)
                    }
                } finally {
                    i.value = !1
                }
            }
        }
        return {
            usedBytes: e,
            isLoading: i,
            dataLoaded: l,
            currentUser: r,
            totalBytes: t,
            loadStorageUsage: u,
            setCurrentUser: function(e) {
                r.value = e, e && u()
            },
            availableBytes: a((() => t.value - e.value)),
            progressWidth: a((() => {
                if (0 === t.value) return 0;
                const i = e.value / t.value * 100;
                return Math.min(i, 100)
            })),
            isStorageFull: a((() => e.value >= t.value)),
            shouldShowUpgrade: a((() => {
                var e;
                const t = null == (e = r.value) ? void 0 : e.plan;
                return !t || "free" === t || "plus" === t
            }))
        }
    })),
    G = {
        width: "40",
        height: "40",
        viewBox: "0 0 40 40",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const W = {
        render: function(e, t) {
            return n(), l("svg", G, t[0] || (t[0] = [o("path", {
                d: "M33.3057 9.99996H18.3057L14.9723 6.66663H6.639C4.80566 6.66663 3.30566 8.16663 3.30566 9.99996V16.6666H36.639V13.3333C36.639 11.5 35.139 9.99996 33.3057 9.99996Z",
                fill: "#A8A8A8"
            }, null, -1), o("path", {
                d: "M33.3389 9.99994H6.6722C4.83887 9.99994 3.33887 11.4999 3.33887 13.3333V29.9999C3.33887 31.8333 4.83887 33.3333 6.6722 33.3333H33.3389C35.1722 33.3333 36.6722 31.8333 36.6722 29.9999V13.3333C36.6722 11.4999 35.1722 9.99994 33.3389 9.99994Z",
                fill: "#CBCBCB"
            }, null, -1)]))
        }
    },
    Z = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const J = {
        render: function(e, t) {
            return n(), l("svg", Z, t[0] || (t[0] = [o("rect", {
                x: "2",
                y: "2",
                width: "20",
                height: "20",
                rx: "4",
                fill: "currentColor"
            }, null, -1), o("path", {
                d: "M17.25 9.42188C17.25 8.71875 16.4062 8.39062 15.9375 8.85938L13.5 11.3438V9.5625C13.5 9.04688 13.0781 8.625 12.5625 8.625H7.6875C7.125 8.625 6.75 9.04688 6.75 9.5625V14.4375C6.75 15 7.125 15.375 7.6875 15.375H12.5625C13.0781 15.375 13.5 15 13.5 14.4375V12.7031L15.9375 15.1875C16.4062 15.6562 17.25 15.3281 17.25 14.625V9.42188Z",
                fill: "white"
            }, null, -1)]))
        }
    },
    Q = {
        class: "dialog-header"
    },
    X = {
        key: 0,
        class: "input-container"
    },
    Y = ["placeholder", "onKeydown", "maxlength", "disabled"],
    ee = {
        key: 1,
        class: "results-container"
    },
    te = {
        class: "format-icon"
    },
    ie = {
        class: "format-info"
    },
    se = {
        class: "format-title auto-select-title"
    },
    ae = {
        class: "format-size"
    },
    le = {
        class: "checkbox-area"
    },
    ne = {
        key: 0,
        class: "checkbox-selected"
    },
    oe = {
        key: 1,
        class: "checkbox-empty"
    },
    re = {
        class: "results-list"
    },
    ue = ["onClick"],
    ce = {
        class: "format-icon"
    },
    de = {
        key: 0,
        class: "extract-icon"
    },
    he = {
        key: 0,
        class: "thumbnail-container"
    },
    pe = ["src", "alt", "onError", "onLoad"],
    ve = {
        key: 1
    },
    fe = {
        class: "format-info"
    },
    me = {
        class: "format-title"
    },
    ge = {
        class: "format-size"
    },
    _e = {
        class: "checkbox-area"
    },
    ye = {
        key: 0,
        class: "checkbox-selected"
    },
    we = {
        key: 1,
        class: "checkbox-empty"
    },
    be = {
        class: "destination-section"
    },
    $e = {
        class: "destination-label"
    },
    xe = {
        class: "destination-path"
    },
    ke = {
        class: "path-text"
    },
    Se = {
        key: 2,
        class: "error-message"
    },
    Ce = ["disabled"],
    Te = ["disabled"],
    Ae = {
        key: 1
    },
    ze = T(r({
        __name: "AiDownloaderDialog",
        props: {
            visible: {
                type: Boolean
            },
            title: {},
            placeholder: {},
            cancelButtonText: {},
            maxLength: {
                default: 500
            },
            breadcrumbText: {},
            save_path: {
                default: "/"
            }
        },
        emits: ["update:visible", "cancel", "download-confirmed", "quota-exceeded", "route-to-super-agent"],
        setup(e, {
            emit: t
        }) {
            const {
                t: i
            } = u();
            c();
            const r = d("currentUser"),
                T = q(),
                U = i("components.aidrive.dialog.confirm"),
                F = e,
                I = a((() => F.title || i("components.aidrive.downloader.title"))),
                j = a((() => F.placeholder || i("components.aidrive.downloader.input_placeholder_new"))),
                B = a((() => F.cancelButtonText || i("components.aidrive.dialog.cancel"))),
                H = a((() => F.breadcrumbText || i("components.aidrive.downloader.my_files"))),
                V = t,
                R = K(),
                N = s(""),
                G = s(!1),
                Z = s(!1),
                ze = s({}),
                Oe = s([]),
                Pe = s([]),
                De = s(null),
                Le = s(!1),
                Ee = s("");
            h();
            const Me = s(!1),
                Ue = s(!1);
            p((() => F.visible), (e => {
                e && (N.value = "", G.value = !1, Z.value = !1, ze.value = {}, Oe.value = [], Pe.value = [], Le.value = !1, Ee.value = "", Me.value = !1, Ue.value = !1, C((() => {
                    De.value && De.value.focus()
                })))
            }));
            const Fe = () => {
                    Le.value = N.value.trim().length > 0
                },
                Ie = () => {
                    V("update:visible", !1), V("cancel")
                },
                je = () => {
                    Ue.value = !Ue.value
                },
                Be = async () => {
                    if (Le.value && !G.value) {
                        Ee.value = "";
                        try {
                            G.value = !0;
                            const e = await A.detectUrl(N.value);
                            if (e && 200 === e.status_code) {
                                if (ze.value = e, Ge(e), 0 === Oe.value.length) return G.value = !1, void(Ee.value = i("components.aidrive.downloader.error_detecting_url"));
                                Me.value = "video" === e.type && Oe.value.length > 0 && (null == r ? void 0 : r.value) && (null == r ? void 0 : r.value.gk_aidrive_e2b_download), Me.value ? Ue.value = !0 : Oe.value.length > 0 && (Pe.value = [0]), G.value = !1, Z.value = !0
                            } else if ("try_superagent" === (null == e ? void 0 : e.action)) {
                                const e = i("components.aidrive.downloader_dialog.superagent_prompt", {
                                    url: N.value
                                });
                                V("route-to-super-agent", {
                                    path: "/agents",
                                    query: {
                                        type: "super_agent",
                                        prompt: e
                                    }
                                }), V("update:visible", !1)
                            } else G.value = !1, Ee.value = (null == e ? void 0 : e.message) || "This URL is not supported for downloading"
                        } catch (e) {
                            G.value = !1, Ee.value = i("components.aidrive.downloader.error_detecting_url")
                        }
                    }
                },
                He = e => {
                    if (e._thumbnailError) return !1;
                    return !!("image" === e.type || "video" === e.type && e.poster_url) && ("video" === e.type ? e.poster_url : e.url, !0)
                },
                Ve = e => {
                    let t = e.filename || e.original_filename || Re(e.url) || "unknown",
                        i = e.content_type || Ke(e);
                    return e.content_type && (i = e.content_type), {
                        name: t,
                        mime_type: i,
                        isDir: !1
                    }
                },
                Re = e => {
                    try {
                        const t = new URL(e).pathname;
                        return t.split("/").pop() || "file"
                    } catch {
                        return "file"
                    }
                },
                Ne = (e, t) => {
                    if (e.extension) return e.extension.replace(/^\./, "");
                    if ("image" === t && e.base64_format) return e.base64_format;
                    if (e.url) try {
                        const t = Re(e.url).split(".");
                        if (t.length > 1) return t.pop()
                    } catch {}
                    switch (t) {
                        case "image":
                            return "jpg";
                        case "video":
                            return "mp4";
                        case "document":
                            return "pdf";
                        case "audio":
                            return "mp3";
                        case "archive":
                            return "zip";
                        default:
                            return "file"
                    }
                },
                Ke = e => {
                    if ("image" === e.type) return e.base64_format ? `image/${e.base64_format}` : "image/jpeg";
                    if ("video" === e.type) return "video/mp4";
                    if ("document" === e.type) {
                        const t = e.extension || `.${D(Re(e.url))}`;
                        return L(`file${t}`)
                    }
                    if ("audio" === e.type) {
                        const t = e.extension || `.${D(Re(e.url))}`;
                        return L(`file${t}`)
                    }
                    if ("archive" === e.type) {
                        const t = e.extension || `.${D(Re(e.url))}`;
                        return L(`file${t}`)
                    }
                    return "application/octet-stream"
                },
                qe = (e, t) => {
                    var i, s, a;
                    let l = Re(e.url);
                    if (!l || "file" === l) {
                        const n = Date.now().toString().slice(-6);
                        switch (t) {
                            case "image":
                                l = `image_${n}.${e.base64_format||"jpg"}`;
                                break;
                            case "video":
                                l = `video_${n}.mp4`;
                                break;
                            case "document":
                                l = `document_${n}.${(null==(i=e.extension)?void 0:i.replace(/^\./,""))||"pdf"}`;
                                break;
                            case "audio":
                                l = `audio_${n}.${(null==(s=e.extension)?void 0:s.replace(/^\./,""))||"mp3"}`;
                                break;
                            case "archive":
                                l = `archive_${n}.${(null==(a=e.extension)?void 0:a.replace(/^\./,""))||"zip"}`;
                                break;
                            default:
                                l = `file_${n}`
                        }
                    }
                    return l
                },
                Ge = e => {
                    var t, i, s, a, l;
                    const n = e.formats || [];
                    if ("extract" === e.type) {
                        const n = [];
                        (null == (t = e.data) ? void 0 : t.documents) && n.push(...e.data.documents.map((e => ({ ...e,
                            type: "document",
                            format_id: `document-${Date.now()}-${Math.random()}`,
                            filename: qe(e, "document"),
                            filesize: e.file_size || 0,
                            original_filename: Re(e.url),
                            ext: Ne(e, "document")
                        })))), (null == (i = e.data) ? void 0 : i.videos) && n.push(...e.data.videos.map((e => ({ ...e,
                            type: "video",
                            format_id: `video-${Date.now()}-${Math.random()}`,
                            filename: qe(e, "video"),
                            filesize: e.file_size || 0,
                            original_filename: Re(e.url),
                            ext: Ne(e, "video")
                        })))), (null == (s = e.data) ? void 0 : s.images) && n.push(...e.data.images.map((e => ({ ...e,
                            type: "image",
                            format_id: `image-${Date.now()}-${Math.random()}`,
                            filename: qe(e, "image"),
                            filesize: 0,
                            original_filename: Re(e.url),
                            ext: Ne(e, "image")
                        })))), (null == (a = e.data) ? void 0 : a.audios) && n.push(...e.data.audios.map((e => ({ ...e,
                            type: "audio",
                            format_id: `audio-${Date.now()}-${Math.random()}`,
                            filename: qe(e, "audio"),
                            filesize: e.file_size || 0,
                            original_filename: Re(e.url),
                            ext: Ne(e, "audio")
                        })))), (null == (l = e.data) ? void 0 : l.archives) && n.push(...e.data.archives.map((e => ({ ...e,
                            type: "archive",
                            format_id: `archive-${Date.now()}-${Math.random()}`,
                            filename: qe(e, "archive"),
                            filesize: e.file_size || 0,
                            original_filename: Re(e.url),
                            ext: Ne(e, "archive")
                        })))), Oe.value = n
                    } else if ("video" === e.type) {
                        const t = n.filter((e => {
                                var t;
                                return "none" !== e.vcodec && e.height > 0 && !(null == (t = e.format_note) ? void 0 : t.toLowerCase().includes("storyboard"))
                            })).sort(((e, t) => (t.height || 0) - (e.height || 0))),
                            i = n.filter((e => {
                                var t;
                                return "none" !== e.acodec && "none" === e.vcodec && !(null == (t = e.format_note) ? void 0 : t.toLowerCase().includes("storyboard"))
                            })).sort(((e, t) => (t.abr || 0) - (e.abr || 0))),
                            s = t.filter((e => !!e.format_id)).map((t => {
                                const i = { ...t
                                };
                                return i.original_filename = t.filename || e.filename || "", i.filename = We({ ...i
                                }), i
                            })),
                            a = i.map((t => {
                                const i = { ...t
                                };
                                return i.original_filename = t.filename || e.filename || "", i.filename = We({ ...i
                                }), i
                            }));
                        Oe.value = [...s, ...a]
                    } else if ("file" === e.type) {
                        const t = {
                            ext: e.filename.split(".").pop() || "",
                            filesize: e.filesize,
                            original_filename: e.filename || "",
                            filename: e.filename || "",
                            url: e.url,
                            format_id: "file-" + Date.now(),
                            content_type: e.content_type
                        };
                        Oe.value = [t]
                    } else Oe.value = n.filter((e => !!e.format_id)).map((e => {
                        const t = { ...e
                        };
                        return t.original_filename = e.filename || "", t.filename = We({ ...t
                        }), t
                    })).slice(0, 1)
                },
                We = e => {
                    if (e.original_filename) {
                        const t = e.original_filename,
                            i = e.ext || "file";
                        let s = t;
                        if (t.length > 40) {
                            s = `${t.substring(0,30)}...${t.substring(t.length-10)}`
                        }
                        return /\.[^.]+$/.test(s) && i && "file" !== i ? s = s.replace(/\.[^.]+$/, `.${i}`) : "file" !== i && (s = `${s}.${i}`), s
                    }
                    let t = "File";
                    if (Ye(e)) {
                        t = e.ext ? e.ext.toUpperCase() : "MP4";
                        const i = e.height || "",
                            s = Ze(e);
                        i && s ? t += `_${i}p_${s}` : i ? t += `_${i}p` : s && (t += `_${s}`)
                    } else if (et(e)) {
                        t = e.ext ? e.ext.toUpperCase() : "Audio";
                        const i = e.abr ? Math.round(e.abr) : "",
                            s = Ze(e);
                        i && s ? t += `_${i}kbps_${s}` : i ? t += `_${i}kbps` : s && (t += `_${s}`)
                    } else t = e.ext ? e.ext.toUpperCase() : "File";
                    return `${t}_${(new Date).getTime().toString().slice(-6)}`
                },
                Ze = e => {
                    if (Ye(e)) {
                        const t = e.vcodec.toLowerCase();
                        if ("none" === t) return "";
                        if (t.includes("avc") || t.includes("h264")) return "H.264";
                        if (t.includes("vp9") || t.match(/vp0?9[\d\.]+/)) return "VP9";
                        if (t.includes("vp8") || t.match(/vp0?8[\d\.]+/)) return "VP8";
                        if (t.includes("av1")) return "AV1";
                        if (t.includes("hevc") || t.includes("h265")) return "H.265";
                        const i = t.match(/^([a-z0-9]+)/i);
                        return i ? i[1].toUpperCase() : t.toUpperCase()
                    }
                    if (et(e) && e.acodec) {
                        const t = e.acodec.toLowerCase();
                        if ("none" === t) return "";
                        if (t.includes("mp4a") || t.includes("aac")) return "AAC";
                        if (t.includes("opus")) return "Opus";
                        if (t.includes("mp3")) return "MP3";
                        if (t.includes("vorbis")) return "Vorbis";
                        const i = t.match(/^([a-z0-9]+)/i);
                        return i ? i[1].toUpperCase() : t.toUpperCase()
                    }
                    return ""
                },
                Je = e => {
                    if (!e) return "";
                    const t = ["B", "KB", "MB", "GB"];
                    let i = e,
                        s = 0;
                    for (; i >= 1024 && s < t.length - 1;) i /= 1024, s++;
                    return `${i.toFixed(1)} ${t[s]}`
                },
                Qe = e => {
                    const t = [];
                    if ("extract" === ze.value.type) {
                        if ("image" === e.type) e.width && e.height && t.push(`${e.width}×${e.height}`);
                        else if ("video" === e.type) e.width && e.height && t.push(`${e.width}×${e.height}`), e.file_size && e.file_size > 0 && t.push(Je(e.file_size)), e.duration && e.duration > 0 && t.push((e => {
                            if (!e || e <= 0) return "";
                            const t = Math.floor(e / 3600),
                                i = Math.floor(e % 3600 / 60),
                                s = Math.floor(e % 60);
                            return t > 0 ? `${t}:${i.toString().padStart(2,"0")}:${s.toString().padStart(2,"0")}` : i > 0 ? `${i}:${s.toString().padStart(2,"0")}` : `0:${s.toString().padStart(2,"0")}`
                        })(e.duration)), t.push("Video");
                        else if ("document" === e.type) {
                            if (e.extension) t.push(e.extension.toUpperCase().replace(/^\./, ""));
                            else {
                                const i = D(Re(e.url));
                                i && t.push(i.toUpperCase())
                            }
                            e.file_size && e.file_size > 0 && t.push(Je(e.file_size)), e.title && t.push(e.title), t.push("Document")
                        } else if ("audio" === e.type) {
                            if (e.extension) t.push(e.extension.toUpperCase().replace(/^\./, ""));
                            else {
                                const i = D(Re(e.url));
                                i && t.push(i.toUpperCase())
                            }
                            e.file_size && e.file_size > 0 && t.push(Je(e.file_size)), e.title && t.push(e.title), t.push("Audio")
                        } else if ("archive" === e.type) {
                            if (e.extension) t.push(e.extension.toUpperCase().replace(/^\./, ""));
                            else {
                                const i = D(Re(e.url));
                                i && t.push(i.toUpperCase())
                            }
                            e.file_size && e.file_size > 0 && t.push(Je(e.file_size)), e.title && t.push(e.title), t.push("Archive")
                        }
                        return t.join(" · ")
                    }
                    const s = tt(e) || Ye(e);
                    if (s && e.width && e.height && t.push(`${e.width}x${e.height}`), (it(e) || et(e)) && e.abr && t.push(`${Math.round(e.abr)}kbps`), e.filesize || e.filesize_approx) {
                        const i = e.filesize || e.filesize_approx;
                        t.push(Je(i))
                    }
                    const a = Ze(e);
                    a && t.push(a);
                    const l = st(e);
                    return s && !l && e.vcodec && t.push(i("components.aidrive.downloader.no_audio")), t.join(" · ")
                },
                Xe = async () => {
                    if (!G.value)
                        if (Z.value) {
                            if (Pe.value.length > 0 || Ue.value) try {
                                G.value = !0;
                                const e = (() => {
                                        let e = 0;
                                        for (const t of Pe.value) {
                                            const i = Oe.value[t],
                                                s = i.filesize || i.filesize_approx || i.file_size || 0;
                                            s > 0 && (e += s)
                                        }
                                        return e
                                    })(),
                                    t = T.availableBytes;
                                if (e > 0 && e > t) return G.value = !1, void V("quota-exceeded", e);
                                const i = Pe.value.map((e => Oe.value[e]));
                                let s;
                                const a = decodeURIComponent(F.save_path);
                                if ("file" === ze.value.type || "extract" === ze.value.type) {
                                    const e = i.map((e => nt(e.url, "file", a, e.filename))),
                                        t = await Promise.all(e),
                                        l = t.filter((e => !e || 200 !== e.status_code));
                                    if (l.length > 0) {
                                        const e = l[0];
                                        return Ee.value = (null == e ? void 0 : e.message) || "Failed to start download task", void(G.value = !1)
                                    }
                                    s = t[0]
                                } else {
                                    const e = i.map((e => ({
                                        format_id: e.format_id,
                                        filename: lt(e, i.length > 1),
                                        mime_type: at(e),
                                        filesize: e.filesize || e.filesize_approx || e.file_size || 0
                                    })));
                                    Ue.value && e.push({
                                        format_id: "auto-select-with-ai",
                                        filename: lt(Oe.value[0], !1),
                                        mime_type: "video/mp4",
                                        filesize: 0
                                    }), s = await nt(N.value, "video", a, void 0, e)
                                }
                                if (!s || 200 !== s.status_code) return Ee.value = (null == s ? void 0 : s.message) || "Failed to start download task", void(G.value = !1);
                                await R.loadAsyncTasks(), V("update:visible", !1), V("download-confirmed"), G.value = !1
                            } catch (e) {
                                return Ee.value = "Failed to start download task", void(G.value = !1)
                            }
                        } else Le.value && await Be()
                },
                Ye = e => e && e.vcodec && "none" !== e.vcodec,
                et = e => e && e.acodec && "none" !== e.acodec && "none" === e.vcodec,
                tt = e => {
                    if (Ye(e)) return !0;
                    const t = {
                        mime_type: e.content_type,
                        name: e.filename || e.original_filename || ""
                    };
                    return E(t)
                },
                it = e => {
                    if (et(e)) return !0;
                    const t = {
                        mime_type: e.content_type,
                        name: e.filename || e.original_filename || ""
                    };
                    return M(t)
                },
                st = e => e && e.acodec && "none" !== e.acodec,
                at = e => "video/webm" === e.mime_type && et(e) ? "audio/webm" : e.mime_type,
                lt = (e, t = !1) => {
                    if (!t) return e.filename;
                    let i = "";
                    if (e.ext) i = `.${e.ext}`;
                    else if (e.filename) {
                        const t = e.filename.split(".");
                        t.length > 1 && (i = `.${t.pop()}`)
                    } else i = Ye(e) ? ".mp4" : et(e) ? ".m4a" : "";
                    let s = "";
                    if (e.original_filename) {
                        const t = e.original_filename.split(".");
                        t.length > 1 && t.pop(), s = t.join(".")
                    } else s = `file_${(new Date).getTime().toString().slice(-8)}`;
                    let a = "";
                    if (Ye(e)) {
                        e.width && e.height ? a += `_${e.width}x${e.height}` : e.height && (a += `_${e.height}p`);
                        const t = Ze(e);
                        t && (a += `_${t}`)
                    } else if (et(e)) {
                        e.abr && (a += `_${Math.round(e.abr)}kbps`);
                        const t = Ze(e);
                        t && (a += `_${t}`)
                    }
                    return `${s}${a}${i}`
                };

            function nt(e, t, i = F.save_path, s, a) {
                return new Promise((async (l, n) => {
                    try {
                        const n = {
                            url: e,
                            type: t,
                            destination_path: i
                        };
                        "file" === t && s ? n.filename = s : "video" === t && a && a.length > 0 && (n.format_items = a);
                        l(await A.startAsyncDownload(n))
                    } catch (o) {
                        n(o)
                    }
                }))
            }
            return (e, t) => e.visible ? (n(), l("div", {
                key: 0,
                class: "downloader-dialog",
                onKeydown: f(Ie, ["esc"]),
                tabindex: "0"
            }, [o("div", {
                class: b(["dialog-content", {
                    expanded: Z.value
                }])
            }, [o("div", Q, [o("h3", null, m(Z.value ? g(i)("components.aidrive.downloader.select_files") : I.value), 1), o("button", {
                class: "close-btn",
                onClick: Ie
            }, "×")]), Z.value ? Z.value ? (n(), l("div", ee, [Me.value ? (n(), l("div", {
                key: 0,
                class: b(["result-item auto-select-item", {
                    selected: Ue.value
                }]),
                onClick: je
            }, [o("div", te, [$(g(J), {
                class: "video-icon"
            })]), o("div", ie, [o("div", se, m(g(i)("components.aidrive.downloader.auto_select_title_hd")), 1), o("div", ae, m(g(i)("components.aidrive.downloader.auto_select_subtitle_hd")), 1)]), o("div", le, [Ue.value ? (n(), l("div", ne, [$(g(O), {
                class: "checkbox-icon"
            })])) : (n(), l("div", oe))])], 2)) : v("", !0), o("div", re, [(n(!0), l(x, null, k(Oe.value, ((e, t) => (n(), l("div", {
                key: t,
                class: b(["result-item", {
                    selected: Pe.value.includes(t)
                }]),
                onClick: e => (e => {
                    const t = Pe.value.indexOf(e); - 1 === t ? Pe.value.push(e) : Pe.value.splice(t, 1)
                })(t)
            }, [o("div", ce, ["extract" === ze.value.type ? (n(), l("div", de, [He(e) ? (n(), l("div", he, [o("img", {
                src: e.url,
                alt: e.alt_text || "Preview",
                class: b(["thumbnail-image", {
                    "thumbnail-error": e._thumbnailError
                }]),
                onError: t => ((e, t) => {
                    t._thumbnailError = !0
                })(0, e),
                onLoad: t => (e => {
                    e._thumbnailError = !1
                })(e)
            }, null, 42, pe), e._thumbnailError ? (n(), S(P, {
                key: 0,
                file: Ve(e),
                size: "medium",
                class: "thumbnail-fallback"
            }, null, 8, ["file"])) : v("", !0)])) : (n(), S(P, {
                key: 1,
                file: Ve(e),
                size: "medium"
            }, null, 8, ["file"]))])) : (n(), l("div", ve, [$(P, {
                file: Ve(e),
                size: "medium"
            }, null, 8, ["file"])]))]), o("div", fe, [o("div", me, m(We(e)), 1), o("div", ge, m(Qe(e)), 1)]), o("div", _e, [Pe.value.includes(t) ? (n(), l("div", ye, [$(g(O), {
                class: "checkbox-icon"
            })])) : (n(), l("div", we))])], 10, ue)))), 128))]), o("div", be, [o("p", $e, m(g(i)("components.aidrive.downloader.save_to")), 1), o("div", xe, [$(g(W), {
                class: "folder-icon"
            }), o("span", ke, m(H.value), 1)])])])) : v("", !0) : (n(), l("div", X, [_(o("textarea", {
                "onUpdate:modelValue": t[0] || (t[0] = e => N.value = e),
                class: "url-input",
                ref_key: "urlInput",
                ref: De,
                placeholder: j.value,
                onInput: Fe,
                onKeydown: f(w(Be, ["prevent"]), ["enter"]),
                maxlength: e.maxLength || 500,
                disabled: G.value
            }, null, 40, Y), [
                [y, N.value]
            ])])), Ee.value ? (n(), l("div", Se, m(Ee.value), 1)) : v("", !0), o("div", {
                class: b(["dialog-actions", {
                    "single-button": Z.value
                }])
            }, [Z.value ? v("", !0) : (n(), l("button", {
                key: 0,
                class: "cancel-btn",
                onClick: Ie,
                disabled: G.value
            }, m(B.value), 9, Ce)), o("button", {
                class: b(["confirm-btn", {
                    "full-width": Z.value,
                    loading: G.value
                }]),
                disabled: !Le.value && !Z.value || Z.value && 0 === Pe.value.length && !Ue.value || G.value,
                onClick: Xe
            }, [G.value ? (n(), S(g(z), {
                key: 0,
                class: "button-loading-icon"
            })) : (n(), l("span", Ae, m(Z.value ? g(i)("components.aidrive.downloader.save_button") : g(U)), 1))], 10, Te)], 2)], 2)], 32)) : v("", !0)
        }
    }), [
        ["__scopeId", "data-v-b0588cdf"]
    ]);
export {
    ze as A, U as a, K as b, q as u
};