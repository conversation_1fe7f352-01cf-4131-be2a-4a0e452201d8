import {
    d as n,
    b as o,
    o as l
} from "./Cf0SOiw0.js";
const r = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 24 24"
};
const t = {
    render: function(t, s) {
        return l(), n("svg", r, s[0] || (s[0] = [o("g", {
            fill: "none"
        }, [o("path", {
            d: "M10 2.75a7.25 7.25 0 0 1 5.63 11.819l4.9 4.9a.75.75 0 0 1-.976 1.134l-.084-.073l-4.901-4.9A7.25 7.25 0 1 1 10 2.75zm0 1.5a5.75 5.75 0 1 0 0 11.5a5.75 5.75 0 0 0 0-11.5z",
            fill: "currentColor"
        })], -1)]))
    }
};
export {
    t as S
};