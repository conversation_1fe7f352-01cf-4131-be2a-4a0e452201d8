const t = new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map((t => t.charCodeAt(0)))),
    e = new Uint16Array("Ȁaglq\tɭ\0\0p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map((t => t.charCodeAt(0))));
var r;
const n = new Map([
        [0, 65533],
        [128, 8364],
        [130, 8218],
        [131, 402],
        [132, 8222],
        [133, 8230],
        [134, 8224],
        [135, 8225],
        [136, 710],
        [137, 8240],
        [138, 352],
        [139, 8249],
        [140, 338],
        [142, 381],
        [145, 8216],
        [146, 8217],
        [147, 8220],
        [148, 8221],
        [149, 8226],
        [150, 8211],
        [151, 8212],
        [152, 732],
        [153, 8482],
        [154, 353],
        [155, 8250],
        [156, 339],
        [158, 382],
        [159, 376]
    ]),
    s = null !== (r = String.fromCodePoint) && void 0 !== r ? r : function(t) {
        let e = "";
        return t > 65535 && (t -= 65536, e += String.fromCharCode(t >>> 10 & 1023 | 55296), t = 56320 | 1023 & t), e += String.fromCharCode(t), e
    };
var i, o;
(o = i || (i = {}))[o.NUM = 35] = "NUM", o[o.SEMI = 59] = "SEMI", o[o.EQUALS = 61] = "EQUALS", o[o.ZERO = 48] = "ZERO", o[o.NINE = 57] = "NINE", o[o.LOWER_A = 97] = "LOWER_A", o[o.LOWER_F = 102] = "LOWER_F", o[o.LOWER_X = 120] = "LOWER_X", o[o.LOWER_Z = 122] = "LOWER_Z", o[o.UPPER_A = 65] = "UPPER_A", o[o.UPPER_F = 70] = "UPPER_F", o[o.UPPER_Z = 90] = "UPPER_Z";
var u, c, a, l, h, p;

function f(t) {
    return t >= i.ZERO && t <= i.NINE
}

function d(t) {
    return t === i.EQUALS || function(t) {
        return t >= i.UPPER_A && t <= i.UPPER_Z || t >= i.LOWER_A && t <= i.LOWER_Z || f(t)
    }(t)
}(c = u || (u = {}))[c.VALUE_LENGTH = 49152] = "VALUE_LENGTH", c[c.BRANCH_LENGTH = 16256] = "BRANCH_LENGTH", c[c.JUMP_TABLE = 127] = "JUMP_TABLE", (l = a || (a = {}))[l.EntityStart = 0] = "EntityStart", l[l.NumericStart = 1] = "NumericStart", l[l.NumericDecimal = 2] = "NumericDecimal", l[l.NumericHex = 3] = "NumericHex", l[l.NamedEntity = 4] = "NamedEntity", (p = h || (h = {}))[p.Legacy = 0] = "Legacy", p[p.Strict = 1] = "Strict", p[p.Attribute = 2] = "Attribute";
class _ {
    constructor(t, e, r) {
        this.decodeTree = t, this.emitCodePoint = e, this.errors = r, this.state = a.EntityStart, this.consumed = 1, this.result = 0, this.treeIndex = 0, this.excess = 1, this.decodeMode = h.Strict
    }
    startEntity(t) {
        this.decodeMode = t, this.state = a.EntityStart, this.result = 0, this.treeIndex = 0, this.excess = 1, this.consumed = 1
    }
    write(t, e) {
        switch (this.state) {
            case a.EntityStart:
                return t.charCodeAt(e) === i.NUM ? (this.state = a.NumericStart, this.consumed += 1, this.stateNumericStart(t, e + 1)) : (this.state = a.NamedEntity, this.stateNamedEntity(t, e));
            case a.NumericStart:
                return this.stateNumericStart(t, e);
            case a.NumericDecimal:
                return this.stateNumericDecimal(t, e);
            case a.NumericHex:
                return this.stateNumericHex(t, e);
            case a.NamedEntity:
                return this.stateNamedEntity(t, e)
        }
    }
    stateNumericStart(t, e) {
        return e >= t.length ? -1 : (32 | t.charCodeAt(e)) === i.LOWER_X ? (this.state = a.NumericHex, this.consumed += 1, this.stateNumericHex(t, e + 1)) : (this.state = a.NumericDecimal, this.stateNumericDecimal(t, e))
    }
    addToNumericResult(t, e, r, n) {
        if (e !== r) {
            const s = r - e;
            this.result = this.result * Math.pow(n, s) + parseInt(t.substr(e, s), n), this.consumed += s
        }
    }
    stateNumericHex(t, e) {
        const r = e;
        for (; e < t.length;) {
            const s = t.charCodeAt(e);
            if (!(f(s) || (n = s, n >= i.UPPER_A && n <= i.UPPER_F || n >= i.LOWER_A && n <= i.LOWER_F))) return this.addToNumericResult(t, r, e, 16), this.emitNumericEntity(s, 3);
            e += 1
        }
        var n;
        return this.addToNumericResult(t, r, e, 16), -1
    }
    stateNumericDecimal(t, e) {
        const r = e;
        for (; e < t.length;) {
            const n = t.charCodeAt(e);
            if (!f(n)) return this.addToNumericResult(t, r, e, 10), this.emitNumericEntity(n, 2);
            e += 1
        }
        return this.addToNumericResult(t, r, e, 10), -1
    }
    emitNumericEntity(t, e) {
        var r;
        if (this.consumed <= e) return null === (r = this.errors) || void 0 === r || r.absenceOfDigitsInNumericCharacterReference(this.consumed), 0;
        if (t === i.SEMI) this.consumed += 1;
        else if (this.decodeMode === h.Strict) return 0;
        return this.emitCodePoint(function(t) {
            var e;
            return t >= 55296 && t <= 57343 || t > 1114111 ? 65533 : null !== (e = n.get(t)) && void 0 !== e ? e : t
        }(this.result), this.consumed), this.errors && (t !== i.SEMI && this.errors.missingSemicolonAfterCharacterReference(), this.errors.validateNumericCharacterReference(this.result)), this.consumed
    }
    stateNamedEntity(t, e) {
        const {
            decodeTree: r
        } = this;
        let n = r[this.treeIndex],
            s = (n & u.VALUE_LENGTH) >> 14;
        for (; e < t.length; e++, this.excess++) {
            const o = t.charCodeAt(e);
            if (this.treeIndex = g(r, n, this.treeIndex + Math.max(1, s), o), this.treeIndex < 0) return 0 === this.result || this.decodeMode === h.Attribute && (0 === s || d(o)) ? 0 : this.emitNotTerminatedNamedEntity();
            if (n = r[this.treeIndex], s = (n & u.VALUE_LENGTH) >> 14, 0 !== s) {
                if (o === i.SEMI) return this.emitNamedEntityData(this.treeIndex, s, this.consumed + this.excess);
                this.decodeMode !== h.Strict && (this.result = this.treeIndex, this.consumed += this.excess, this.excess = 0)
            }
        }
        return -1
    }
    emitNotTerminatedNamedEntity() {
        var t;
        const {
            result: e,
            decodeTree: r
        } = this, n = (r[e] & u.VALUE_LENGTH) >> 14;
        return this.emitNamedEntityData(e, n, this.consumed), null === (t = this.errors) || void 0 === t || t.missingSemicolonAfterCharacterReference(), this.consumed
    }
    emitNamedEntityData(t, e, r) {
        const {
            decodeTree: n
        } = this;
        return this.emitCodePoint(1 === e ? n[t] & ~u.VALUE_LENGTH : n[t + 1], r), 3 === e && this.emitCodePoint(n[t + 2], r), r
    }
    end() {
        var t;
        switch (this.state) {
            case a.NamedEntity:
                return 0 === this.result || this.decodeMode === h.Attribute && this.result !== this.treeIndex ? 0 : this.emitNotTerminatedNamedEntity();
            case a.NumericDecimal:
                return this.emitNumericEntity(0, 2);
            case a.NumericHex:
                return this.emitNumericEntity(0, 3);
            case a.NumericStart:
                return null === (t = this.errors) || void 0 === t || t.absenceOfDigitsInNumericCharacterReference(this.consumed), 0;
            case a.EntityStart:
                return 0
        }
    }
}

function m(t) {
    let e = "";
    const r = new _(t, (t => e += s(t)));
    return function(t, n) {
        let s = 0,
            i = 0;
        for (;
            (i = t.indexOf("&", i)) >= 0;) {
            e += t.slice(s, i), r.startEntity(n);
            const o = r.write(t, i + 1);
            if (o < 0) {
                s = i + r.end();
                break
            }
            s = i + o, i = 0 === o ? s + 1 : s
        }
        const o = e + t.slice(s);
        return e = "", o
    }
}

function g(t, e, r, n) {
    const s = (e & u.BRANCH_LENGTH) >> 7,
        i = e & u.JUMP_TABLE;
    if (0 === s) return 0 !== i && n === i ? r : -1;
    if (i) {
        const e = n - i;
        return e < 0 || e >= s ? -1 : t[r + e] - 1
    }
    let o = r,
        c = o + s - 1;
    for (; o <= c;) {
        const e = o + c >>> 1,
            r = t[e];
        if (r < n) o = e + 1;
        else {
            if (!(r > n)) return t[e + s];
            c = e - 1
        }
    }
    return -1
}
const k = m(t);

function D(t, e = h.Legacy) {
    return k(t, e)
}
m(e);
const C = {};

function y(t, e) {
    "string" != typeof e && (e = y.defaultChars);
    const r = function(t) {
        let e = C[t];
        if (e) return e;
        e = C[t] = [];
        for (let r = 0; r < 128; r++) {
            const t = String.fromCharCode(r);
            e.push(t)
        }
        for (let r = 0; r < t.length; r++) {
            const n = t.charCodeAt(r);
            e[n] = "%" + ("0" + n.toString(16).toUpperCase()).slice(-2)
        }
        return e
    }(e);
    return t.replace(/(%[a-f0-9]{2})+/gi, (function(t) {
        let e = "";
        for (let n = 0, s = t.length; n < s; n += 3) {
            const i = parseInt(t.slice(n + 1, n + 3), 16);
            if (i < 128) e += r[i];
            else {
                if (192 == (224 & i) && n + 3 < s) {
                    const r = parseInt(t.slice(n + 4, n + 6), 16);
                    if (128 == (192 & r)) {
                        const t = i << 6 & 1984 | 63 & r;
                        e += t < 128 ? "��" : String.fromCharCode(t), n += 3;
                        continue
                    }
                }
                if (224 == (240 & i) && n + 6 < s) {
                    const r = parseInt(t.slice(n + 4, n + 6), 16),
                        s = parseInt(t.slice(n + 7, n + 9), 16);
                    if (128 == (192 & r) && 128 == (192 & s)) {
                        const t = i << 12 & 61440 | r << 6 & 4032 | 63 & s;
                        e += t < 2048 || t >= 55296 && t <= 57343 ? "���" : String.fromCharCode(t), n += 6;
                        continue
                    }
                }
                if (240 == (248 & i) && n + 9 < s) {
                    const r = parseInt(t.slice(n + 4, n + 6), 16),
                        s = parseInt(t.slice(n + 7, n + 9), 16),
                        o = parseInt(t.slice(n + 10, n + 12), 16);
                    if (128 == (192 & r) && 128 == (192 & s) && 128 == (192 & o)) {
                        let t = i << 18 & 1835008 | r << 12 & 258048 | s << 6 & 4032 | 63 & o;
                        t < 65536 || t > 1114111 ? e += "����" : (t -= 65536, e += String.fromCharCode(55296 + (t >> 10), 56320 + (1023 & t))), n += 9;
                        continue
                    }
                }
                e += "�"
            }
        }
        return e
    }))
}
y.defaultChars = ";/?:@&=+$,#", y.componentChars = "";
const E = {};

function b(t, e, r) {
    "string" != typeof e && (r = e, e = b.defaultChars), void 0 === r && (r = !0);
    const n = function(t) {
        let e = E[t];
        if (e) return e;
        e = E[t] = [];
        for (let r = 0; r < 128; r++) {
            const t = String.fromCharCode(r);
            /^[0-9a-z]$/i.test(t) ? e.push(t) : e.push("%" + ("0" + r.toString(16).toUpperCase()).slice(-2))
        }
        for (let r = 0; r < t.length; r++) e[t.charCodeAt(r)] = t[r];
        return e
    }(e);
    let s = "";
    for (let i = 0, o = t.length; i < o; i++) {
        const e = t.charCodeAt(i);
        if (r && 37 === e && i + 2 < o && /^[0-9a-f]{2}$/i.test(t.slice(i + 1, i + 3))) s += t.slice(i, i + 3), i += 2;
        else if (e < 128) s += n[e];
        else if (e >= 55296 && e <= 57343) {
            if (e >= 55296 && e <= 56319 && i + 1 < o) {
                const e = t.charCodeAt(i + 1);
                if (e >= 56320 && e <= 57343) {
                    s += encodeURIComponent(t[i] + t[i + 1]), i++;
                    continue
                }
            }
            s += "%EF%BF%BD"
        } else s += encodeURIComponent(t[i])
    }
    return s
}

function A(t) {
    let e = "";
    return e += t.protocol || "", e += t.slashes ? "//" : "", e += t.auth ? t.auth + "@" : "", t.hostname && -1 !== t.hostname.indexOf(":") ? e += "[" + t.hostname + "]" : e += t.hostname || "", e += t.port ? ":" + t.port : "", e += t.pathname || "", e += t.search || "", e += t.hash || "", e
}

function F() {
    this.protocol = null, this.slashes = null, this.auth = null, this.port = null, this.hostname = null, this.hash = null, this.search = null, this.pathname = null
}
b.defaultChars = ";/?:@&=+$,-_.!~*'()#", b.componentChars = "-_.!~*'()";
const x = /^([a-z0-9.+-]+:)/i,
    w = /:[0-9]*$/,
    v = /^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,
    S = ["{", "}", "|", "\\", "^", "`"].concat(["<", ">", '"', "`", " ", "\r", "\n", "\t"]),
    z = ["'"].concat(S),
    q = ["%", "/", "?", ";", "#"].concat(z),
    B = ["/", "?", "#"],
    L = /^[+a-z0-9A-Z_-]{0,63}$/,
    M = /^([+a-z0-9A-Z_-]{0,63})(.*)$/,
    I = {
        javascript: !0,
        "javascript:": !0
    },
    T = {
        http: !0,
        https: !0,
        ftp: !0,
        gopher: !0,
        file: !0,
        "http:": !0,
        "https:": !0,
        "ftp:": !0,
        "gopher:": !0,
        "file:": !0
    };

function R(t, e) {
    if (t && t instanceof F) return t;
    const r = new F;
    return r.parse(t, e), r
}
F.prototype.parse = function(t, e) {
    let r, n, s, i = t;
    if (i = i.trim(), !e && 1 === t.split("#").length) {
        const t = v.exec(i);
        if (t) return this.pathname = t[1], t[2] && (this.search = t[2]), this
    }
    let o = x.exec(i);
    if (o && (o = o[0], r = o.toLowerCase(), this.protocol = o, i = i.substr(o.length)), (e || o || i.match(/^\/\/[^@\/]+@[^@\/]+/)) && (s = "//" === i.substr(0, 2), !s || o && I[o] || (i = i.substr(2), this.slashes = !0)), !I[o] && (s || o && !T[o])) {
        let t, e, r = -1;
        for (let u = 0; u < B.length; u++) n = i.indexOf(B[u]), -1 !== n && (-1 === r || n < r) && (r = n);
        e = -1 === r ? i.lastIndexOf("@") : i.lastIndexOf("@", r), -1 !== e && (t = i.slice(0, e), i = i.slice(e + 1), this.auth = t), r = -1;
        for (let u = 0; u < q.length; u++) n = i.indexOf(q[u]), -1 !== n && (-1 === r || n < r) && (r = n); - 1 === r && (r = i.length), ":" === i[r - 1] && r--;
        const s = i.slice(0, r);
        i = i.slice(r), this.parseHost(s), this.hostname = this.hostname || "";
        const o = "[" === this.hostname[0] && "]" === this.hostname[this.hostname.length - 1];
        if (!o) {
            const t = this.hostname.split(/\./);
            for (let e = 0, r = t.length; e < r; e++) {
                const r = t[e];
                if (r && !r.match(L)) {
                    let n = "";
                    for (let t = 0, e = r.length; t < e; t++) r.charCodeAt(t) > 127 ? n += "x" : n += r[t];
                    if (!n.match(L)) {
                        const n = t.slice(0, e),
                            s = t.slice(e + 1),
                            o = r.match(M);
                        o && (n.push(o[1]), s.unshift(o[2])), s.length && (i = s.join(".") + i), this.hostname = n.join(".");
                        break
                    }
                }
            }
        }
        this.hostname.length > 255 && (this.hostname = ""), o && (this.hostname = this.hostname.substr(1, this.hostname.length - 2))
    }
    const u = i.indexOf("#"); - 1 !== u && (this.hash = i.substr(u), i = i.slice(0, u));
    const c = i.indexOf("?");
    return -1 !== c && (this.search = i.substr(c), i = i.slice(0, c)), i && (this.pathname = i), T[r] && this.hostname && !this.pathname && (this.pathname = ""), this
}, F.prototype.parseHost = function(t) {
    let e = w.exec(t);
    e && (e = e[0], ":" !== e && (this.port = e.substr(1)), t = t.substr(0, t.length - e.length)), t && (this.hostname = t)
};
const N = Object.freeze(Object.defineProperty({
        __proto__: null,
        decode: y,
        encode: b,
        format: A,
        parse: R
    }, Symbol.toStringTag, {
        value: "Module"
    })),
    P = /[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,
    O = /[\0-\x1F\x7F-\x9F]/,
    j = /[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDEAD\uDF55-\uDF59\uDF86-\uDF89]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5A\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDEB9\uDF3C-\uDF3E]|\uD806[\uDC3B\uDD44-\uDD46\uDDE2\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2\uDF00-\uDF09]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8\uDF43-\uDF4F\uDFFF]|\uD809[\uDC70-\uDC74]|\uD80B[\uDFF1\uDFF2]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A\uDFE2]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/,
    Z = /[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u0888\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20C0\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u31EF\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC2\uFD40-\uFD4F\uFDCF\uFDFC-\uFDFF\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD833[\uDF50-\uDFC3]|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDEA\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEDC-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF76\uDF7B-\uDFD9\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDE53\uDE60-\uDE6D\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC5\uDECE-\uDEDB\uDEE0-\uDEE8\uDEF0-\uDEF8\uDF00-\uDF92\uDF94-\uDFCA]/,
    $ = /[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,
    U = Object.freeze(Object.defineProperty({
        __proto__: null,
        Any: P,
        Cc: O,
        Cf: /[\xAD\u0600-\u0605\u061C\u06DD\u070F\u0890\u0891\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD80D[\uDC30-\uDC3F]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/,
        P: j,
        S: Z,
        Z: $
    }, Symbol.toStringTag, {
        value: "Module"
    }));

function H(t) {
    return "[object String]" === function(t) {
        return Object.prototype.toString.call(t)
    }(t)
}
const V = Object.prototype.hasOwnProperty;

function G(t) {
    return Array.prototype.slice.call(arguments, 1).forEach((function(e) {
        if (e) {
            if ("object" != typeof e) throw new TypeError(e + "must be object");
            Object.keys(e).forEach((function(r) {
                t[r] = e[r]
            }))
        }
    })), t
}

function W(t, e, r) {
    return [].concat(t.slice(0, e), r, t.slice(e + 1))
}

function J(t) {
    return !(t >= 55296 && t <= 57343) && (!(t >= 64976 && t <= 65007) && (!!(65535 & ~t && 65534 != (65535 & t)) && (!(t >= 0 && t <= 8) && (11 !== t && (!(t >= 14 && t <= 31) && (!(t >= 127 && t <= 159) && !(t > 1114111)))))))
}

function Q(t) {
    if (t > 65535) {
        const e = 55296 + ((t -= 65536) >> 10),
            r = 56320 + (1023 & t);
        return String.fromCharCode(e, r)
    }
    return String.fromCharCode(t)
}
const X = /\\([!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])/g,
    Y = new RegExp(X.source + "|" + /&([a-z#][a-z0-9]{1,31});/gi.source, "gi"),
    K = /^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i;

function tt(t) {
    return t.indexOf("\\") < 0 && t.indexOf("&") < 0 ? t : t.replace(Y, (function(t, e, r) {
        return e || function(t, e) {
            if (35 === e.charCodeAt(0) && K.test(e)) {
                const r = "x" === e[1].toLowerCase() ? parseInt(e.slice(2), 16) : parseInt(e.slice(1), 10);
                return J(r) ? Q(r) : t
            }
            const r = D(t);
            return r !== t ? r : t
        }(t, r)
    }))
}
const et = /[&<>"]/,
    rt = /[&<>"]/g,
    nt = {
        "&": "&amp;",
        "<": "&lt;",
        ">": "&gt;",
        '"': "&quot;"
    };

function st(t) {
    return nt[t]
}

function it(t) {
    return et.test(t) ? t.replace(rt, st) : t
}
const ot = /[.?*+^$[\]\\(){}|-]/g;

function ut(t) {
    switch (t) {
        case 9:
        case 32:
            return !0
    }
    return !1
}

function ct(t) {
    if (t >= 8192 && t <= 8202) return !0;
    switch (t) {
        case 9:
        case 10:
        case 11:
        case 12:
        case 13:
        case 32:
        case 160:
        case 5760:
        case 8239:
        case 8287:
        case 12288:
            return !0
    }
    return !1
}

function at(t) {
    return j.test(t) || Z.test(t)
}

function lt(t) {
    switch (t) {
        case 33:
        case 34:
        case 35:
        case 36:
        case 37:
        case 38:
        case 39:
        case 40:
        case 41:
        case 42:
        case 43:
        case 44:
        case 45:
        case 46:
        case 47:
        case 58:
        case 59:
        case 60:
        case 61:
        case 62:
        case 63:
        case 64:
        case 91:
        case 92:
        case 93:
        case 94:
        case 95:
        case 96:
        case 123:
        case 124:
        case 125:
        case 126:
            return !0;
        default:
            return !1
    }
}

function ht(t) {
    return t = t.trim().replace(/\s+/g, " "), "Ṿ" === "ẞ".toLowerCase() && (t = t.replace(/ẞ/g, "ß")), t.toLowerCase().toUpperCase()
}
const pt = {
        mdurl: N,
        ucmicro: U
    },
    ft = Object.freeze(Object.defineProperty({
        __proto__: null,
        arrayReplaceAt: W,
        assign: G,
        escapeHtml: it,
        escapeRE: function(t) {
            return t.replace(ot, "\\$&")
        },
        fromCodePoint: Q,
        has: function(t, e) {
            return V.call(t, e)
        },
        isMdAsciiPunct: lt,
        isPunctChar: at,
        isSpace: ut,
        isString: H,
        isValidEntityCode: J,
        isWhiteSpace: ct,
        lib: pt,
        normalizeReference: ht,
        unescapeAll: tt,
        unescapeMd: function(t) {
            return t.indexOf("\\") < 0 ? t : t.replace(X, "$1")
        }
    }, Symbol.toStringTag, {
        value: "Module"
    }));
const dt = Object.freeze(Object.defineProperty({
        __proto__: null,
        parseLinkDestination: function(t, e, r) {
            let n, s = e;
            const i = {
                ok: !1,
                pos: 0,
                str: ""
            };
            if (60 === t.charCodeAt(s)) {
                for (s++; s < r;) {
                    if (n = t.charCodeAt(s), 10 === n) return i;
                    if (60 === n) return i;
                    if (62 === n) return i.pos = s + 1, i.str = tt(t.slice(e + 1, s)), i.ok = !0, i;
                    92 === n && s + 1 < r ? s += 2 : s++
                }
                return i
            }
            let o = 0;
            for (; s < r && (n = t.charCodeAt(s), 32 !== n) && !(n < 32 || 127 === n);)
                if (92 === n && s + 1 < r) {
                    if (32 === t.charCodeAt(s + 1)) break;
                    s += 2
                } else {
                    if (40 === n && (o++, o > 32)) return i;
                    if (41 === n) {
                        if (0 === o) break;
                        o--
                    }
                    s++
                }
            return e === s || 0 !== o || (i.str = tt(t.slice(e, s)), i.pos = s, i.ok = !0), i
        },
        parseLinkLabel: function(t, e, r) {
            let n, s, i, o;
            const u = t.posMax,
                c = t.pos;
            for (t.pos = e + 1, n = 1; t.pos < u;) {
                if (i = t.src.charCodeAt(t.pos), 93 === i && (n--, 0 === n)) {
                    s = !0;
                    break
                }
                if (o = t.pos, t.md.inline.skipToken(t), 91 === i)
                    if (o === t.pos - 1) n++;
                    else if (r) return t.pos = c, -1
            }
            let a = -1;
            return s && (a = t.pos), t.pos = c, a
        },
        parseLinkTitle: function(t, e, r, n) {
            let s, i = e;
            const o = {
                ok: !1,
                can_continue: !1,
                pos: 0,
                str: "",
                marker: 0
            };
            if (n) o.str = n.str, o.marker = n.marker;
            else {
                if (i >= r) return o;
                let n = t.charCodeAt(i);
                if (34 !== n && 39 !== n && 40 !== n) return o;
                e++, i++, 40 === n && (n = 41), o.marker = n
            }
            for (; i < r;) {
                if (s = t.charCodeAt(i), s === o.marker) return o.pos = i + 1, o.str += tt(t.slice(e, i)), o.ok = !0, o;
                if (40 === s && 41 === o.marker) return o;
                92 === s && i + 1 < r && i++, i++
            }
            return o.can_continue = !0, o.str += tt(t.slice(e, i)), o
        }
    }, Symbol.toStringTag, {
        value: "Module"
    })),
    _t = {};

function mt() {
    this.rules = G({}, _t)
}

function gt() {
    this.__rules__ = [], this.__cache__ = null
}

function kt(t, e, r) {
    this.type = t, this.tag = e, this.attrs = null, this.map = null, this.nesting = r, this.level = 0, this.children = null, this.content = "", this.markup = "", this.info = "", this.meta = null, this.block = !1, this.hidden = !1
}

function Dt(t, e, r) {
    this.src = t, this.env = r, this.tokens = [], this.inlineMode = !1, this.md = e
}
_t.code_inline = function(t, e, r, n, s) {
    const i = t[e];
    return "<code" + s.renderAttrs(i) + ">" + it(i.content) + "</code>"
}, _t.code_block = function(t, e, r, n, s) {
    const i = t[e];
    return "<pre" + s.renderAttrs(i) + "><code>" + it(t[e].content) + "</code></pre>\n"
}, _t.fence = function(t, e, r, n, s) {
    const i = t[e],
        o = i.info ? tt(i.info).trim() : "";
    let u, c = "",
        a = "";
    if (o) {
        const t = o.split(/(\s+)/g);
        c = t[0], a = t.slice(2).join("")
    }
    if (u = r.highlight && r.highlight(i.content, c, a) || it(i.content), 0 === u.indexOf("<pre")) return u + "\n";
    if (o) {
        const t = i.attrIndex("class"),
            e = i.attrs ? i.attrs.slice() : [];
        t < 0 ? e.push(["class", r.langPrefix + c]) : (e[t] = e[t].slice(), e[t][1] += " " + r.langPrefix + c);
        const n = {
            attrs: e
        };
        return `<pre><code${s.renderAttrs(n)}>${u}</code></pre>\n`
    }
    return `<pre><code${s.renderAttrs(i)}>${u}</code></pre>\n`
}, _t.image = function(t, e, r, n, s) {
    const i = t[e];
    return i.attrs[i.attrIndex("alt")][1] = s.renderInlineAsText(i.children, r, n), s.renderToken(t, e, r)
}, _t.hardbreak = function(t, e, r) {
    return r.xhtmlOut ? "<br />\n" : "<br>\n"
}, _t.softbreak = function(t, e, r) {
    return r.breaks ? r.xhtmlOut ? "<br />\n" : "<br>\n" : "\n"
}, _t.text = function(t, e) {
    return it(t[e].content)
}, _t.html_block = function(t, e) {
    return t[e].content
}, _t.html_inline = function(t, e) {
    return t[e].content
}, mt.prototype.renderAttrs = function(t) {
    let e, r, n;
    if (!t.attrs) return "";
    for (n = "", e = 0, r = t.attrs.length; e < r; e++) n += " " + it(t.attrs[e][0]) + '="' + it(t.attrs[e][1]) + '"';
    return n
}, mt.prototype.renderToken = function(t, e, r) {
    const n = t[e];
    let s = "";
    if (n.hidden) return "";
    n.block && -1 !== n.nesting && e && t[e - 1].hidden && (s += "\n"), s += (-1 === n.nesting ? "</" : "<") + n.tag, s += this.renderAttrs(n), 0 === n.nesting && r.xhtmlOut && (s += " /");
    let i = !1;
    if (n.block && (i = !0, 1 === n.nesting && e + 1 < t.length)) {
        const r = t[e + 1];
        ("inline" === r.type || r.hidden || -1 === r.nesting && r.tag === n.tag) && (i = !1)
    }
    return s += i ? ">\n" : ">", s
}, mt.prototype.renderInline = function(t, e, r) {
    let n = "";
    const s = this.rules;
    for (let i = 0, o = t.length; i < o; i++) {
        const o = t[i].type;
        void 0 !== s[o] ? n += s[o](t, i, e, r, this) : n += this.renderToken(t, i, e)
    }
    return n
}, mt.prototype.renderInlineAsText = function(t, e, r) {
    let n = "";
    for (let s = 0, i = t.length; s < i; s++) switch (t[s].type) {
        case "text":
        case "html_inline":
        case "html_block":
            n += t[s].content;
            break;
        case "image":
            n += this.renderInlineAsText(t[s].children, e, r);
            break;
        case "softbreak":
        case "hardbreak":
            n += "\n"
    }
    return n
}, mt.prototype.render = function(t, e, r) {
    let n = "";
    const s = this.rules;
    for (let i = 0, o = t.length; i < o; i++) {
        const o = t[i].type;
        "inline" === o ? n += this.renderInline(t[i].children, e, r) : void 0 !== s[o] ? n += s[o](t, i, e, r, this) : n += this.renderToken(t, i, e, r)
    }
    return n
}, gt.prototype.__find__ = function(t) {
    for (let e = 0; e < this.__rules__.length; e++)
        if (this.__rules__[e].name === t) return e;
    return -1
}, gt.prototype.__compile__ = function() {
    const t = this,
        e = [""];
    t.__rules__.forEach((function(t) {
        t.enabled && t.alt.forEach((function(t) {
            e.indexOf(t) < 0 && e.push(t)
        }))
    })), t.__cache__ = {}, e.forEach((function(e) {
        t.__cache__[e] = [], t.__rules__.forEach((function(r) {
            r.enabled && (e && r.alt.indexOf(e) < 0 || t.__cache__[e].push(r.fn))
        }))
    }))
}, gt.prototype.at = function(t, e, r) {
    const n = this.__find__(t),
        s = r || {};
    if (-1 === n) throw new Error("Parser rule not found: " + t);
    this.__rules__[n].fn = e, this.__rules__[n].alt = s.alt || [], this.__cache__ = null
}, gt.prototype.before = function(t, e, r, n) {
    const s = this.__find__(t),
        i = n || {};
    if (-1 === s) throw new Error("Parser rule not found: " + t);
    this.__rules__.splice(s, 0, {
        name: e,
        enabled: !0,
        fn: r,
        alt: i.alt || []
    }), this.__cache__ = null
}, gt.prototype.after = function(t, e, r, n) {
    const s = this.__find__(t),
        i = n || {};
    if (-1 === s) throw new Error("Parser rule not found: " + t);
    this.__rules__.splice(s + 1, 0, {
        name: e,
        enabled: !0,
        fn: r,
        alt: i.alt || []
    }), this.__cache__ = null
}, gt.prototype.push = function(t, e, r) {
    const n = r || {};
    this.__rules__.push({
        name: t,
        enabled: !0,
        fn: e,
        alt: n.alt || []
    }), this.__cache__ = null
}, gt.prototype.enable = function(t, e) {
    Array.isArray(t) || (t = [t]);
    const r = [];
    return t.forEach((function(t) {
        const n = this.__find__(t);
        if (n < 0) {
            if (e) return;
            throw new Error("Rules manager: invalid rule name " + t)
        }
        this.__rules__[n].enabled = !0, r.push(t)
    }), this), this.__cache__ = null, r
}, gt.prototype.enableOnly = function(t, e) {
    Array.isArray(t) || (t = [t]), this.__rules__.forEach((function(t) {
        t.enabled = !1
    })), this.enable(t, e)
}, gt.prototype.disable = function(t, e) {
    Array.isArray(t) || (t = [t]);
    const r = [];
    return t.forEach((function(t) {
        const n = this.__find__(t);
        if (n < 0) {
            if (e) return;
            throw new Error("Rules manager: invalid rule name " + t)
        }
        this.__rules__[n].enabled = !1, r.push(t)
    }), this), this.__cache__ = null, r
}, gt.prototype.getRules = function(t) {
    return null === this.__cache__ && this.__compile__(), this.__cache__[t] || []
}, kt.prototype.attrIndex = function(t) {
    if (!this.attrs) return -1;
    const e = this.attrs;
    for (let r = 0, n = e.length; r < n; r++)
        if (e[r][0] === t) return r;
    return -1
}, kt.prototype.attrPush = function(t) {
    this.attrs ? this.attrs.push(t) : this.attrs = [t]
}, kt.prototype.attrSet = function(t, e) {
    const r = this.attrIndex(t),
        n = [t, e];
    r < 0 ? this.attrPush(n) : this.attrs[r] = n
}, kt.prototype.attrGet = function(t) {
    const e = this.attrIndex(t);
    let r = null;
    return e >= 0 && (r = this.attrs[e][1]), r
}, kt.prototype.attrJoin = function(t, e) {
    const r = this.attrIndex(t);
    r < 0 ? this.attrPush([t, e]) : this.attrs[r][1] = this.attrs[r][1] + " " + e
}, Dt.prototype.Token = kt;
const Ct = /\r\n?|\n/g,
    yt = /\0/g;

function Et(t) {
    return /^<\/a\s*>/i.test(t)
}
const bt = /\+-|\.\.|\?\?\?\?|!!!!|,,|--/,
    At = /\((c|tm|r)\)/i,
    Ft = /\((c|tm|r)\)/gi,
    xt = {
        c: "©",
        r: "®",
        tm: "™"
    };

function wt(t, e) {
    return xt[e.toLowerCase()]
}

function vt(t) {
    let e = 0;
    for (let r = t.length - 1; r >= 0; r--) {
        const n = t[r];
        "text" !== n.type || e || (n.content = n.content.replace(Ft, wt)), "link_open" === n.type && "auto" === n.info && e--, "link_close" === n.type && "auto" === n.info && e++
    }
}

function St(t) {
    let e = 0;
    for (let r = t.length - 1; r >= 0; r--) {
        const n = t[r];
        "text" !== n.type || e || bt.test(n.content) && (n.content = n.content.replace(/\+-/g, "±").replace(/\.{2,}/g, "…").replace(/([?!])…/g, "$1..").replace(/([?!]){4,}/g, "$1$1$1").replace(/,{2,}/g, ",").replace(/(^|[^-])---(?=[^-]|$)/gm, "$1—").replace(/(^|\s)--(?=\s|$)/gm, "$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/gm, "$1–")), "link_open" === n.type && "auto" === n.info && e--, "link_close" === n.type && "auto" === n.info && e++
    }
}
const zt = /['"]/,
    qt = /['"]/g;

function Bt(t, e, r) {
    return t.slice(0, e) + r + t.slice(e + 1)
}

function Lt(t, e) {
    let r;
    const n = [];
    for (let s = 0; s < t.length; s++) {
        const i = t[s],
            o = t[s].level;
        for (r = n.length - 1; r >= 0 && !(n[r].level <= o); r--);
        if (n.length = r + 1, "text" !== i.type) continue;
        let u = i.content,
            c = 0,
            a = u.length;
        t: for (; c < a;) {
            qt.lastIndex = c;
            const l = qt.exec(u);
            if (!l) break;
            let h = !0,
                p = !0;
            c = l.index + 1;
            const f = "'" === l[0];
            let d = 32;
            if (l.index - 1 >= 0) d = u.charCodeAt(l.index - 1);
            else
                for (r = s - 1; r >= 0 && ("softbreak" !== t[r].type && "hardbreak" !== t[r].type); r--)
                    if (t[r].content) {
                        d = t[r].content.charCodeAt(t[r].content.length - 1);
                        break
                    } let _ = 32;
            if (c < a) _ = u.charCodeAt(c);
            else
                for (r = s + 1; r < t.length && ("softbreak" !== t[r].type && "hardbreak" !== t[r].type); r++)
                    if (t[r].content) {
                        _ = t[r].content.charCodeAt(0);
                        break
                    } const m = lt(d) || at(String.fromCharCode(d)),
                g = lt(_) || at(String.fromCharCode(_)),
                k = ct(d),
                D = ct(_);
            if (D ? h = !1 : g && (k || m || (h = !1)), k ? p = !1 : m && (D || g || (p = !1)), 34 === _ && '"' === l[0] && d >= 48 && d <= 57 && (p = h = !1), h && p && (h = m, p = g), h || p) {
                if (p)
                    for (r = n.length - 1; r >= 0; r--) {
                        let h = n[r];
                        if (n[r].level < o) break;
                        if (h.single === f && n[r].level === o) {
                            let o, p;
                            h = n[r], f ? (o = e.md.options.quotes[2], p = e.md.options.quotes[3]) : (o = e.md.options.quotes[0], p = e.md.options.quotes[1]), i.content = Bt(i.content, l.index, p), t[h.token].content = Bt(t[h.token].content, h.pos, o), c += p.length - 1, h.token === s && (c += o.length - 1), u = i.content, a = u.length, n.length = r;
                            continue t
                        }
                    }
                h ? n.push({
                    token: s,
                    pos: l.index,
                    single: f,
                    level: o
                }) : p && f && (i.content = Bt(i.content, l.index, "’"))
            } else f && (i.content = Bt(i.content, l.index, "’"))
        }
    }
}
const Mt = [
    ["normalize", function(t) {
        let e;
        e = t.src.replace(Ct, "\n"), e = e.replace(yt, "�"), t.src = e
    }],
    ["block", function(t) {
        let e;
        t.inlineMode ? (e = new t.Token("inline", "", 0), e.content = t.src, e.map = [0, 1], e.children = [], t.tokens.push(e)) : t.md.block.parse(t.src, t.md, t.env, t.tokens)
    }],
    ["inline", function(t) {
        const e = t.tokens;
        for (let r = 0, n = e.length; r < n; r++) {
            const n = e[r];
            "inline" === n.type && t.md.inline.parse(n.content, t.md, t.env, n.children)
        }
    }],
    ["linkify", function(t) {
        const e = t.tokens;
        var r;
        if (t.md.options.linkify)
            for (let n = 0, s = e.length; n < s; n++) {
                if ("inline" !== e[n].type || !t.md.linkify.pretest(e[n].content)) continue;
                let s = e[n].children,
                    i = 0;
                for (let o = s.length - 1; o >= 0; o--) {
                    const u = s[o];
                    if ("link_close" !== u.type) {
                        if ("html_inline" === u.type && (r = u.content, /^<a[>\s]/i.test(r) && i > 0 && i--, Et(u.content) && i++), !(i > 0) && "text" === u.type && t.md.linkify.test(u.content)) {
                            const r = u.content;
                            let i = t.md.linkify.match(r);
                            const c = [];
                            let a = u.level,
                                l = 0;
                            i.length > 0 && 0 === i[0].index && o > 0 && "text_special" === s[o - 1].type && (i = i.slice(1));
                            for (let e = 0; e < i.length; e++) {
                                const n = i[e].url,
                                    s = t.md.normalizeLink(n);
                                if (!t.md.validateLink(s)) continue;
                                let o = i[e].text;
                                o = i[e].schema ? "mailto:" !== i[e].schema || /^mailto:/i.test(o) ? t.md.normalizeLinkText(o) : t.md.normalizeLinkText("mailto:" + o).replace(/^mailto:/, "") : t.md.normalizeLinkText("http://" + o).replace(/^http:\/\//, "");
                                const u = i[e].index;
                                if (u > l) {
                                    const e = new t.Token("text", "", 0);
                                    e.content = r.slice(l, u), e.level = a, c.push(e)
                                }
                                const h = new t.Token("link_open", "a", 1);
                                h.attrs = [
                                    ["href", s]
                                ], h.level = a++, h.markup = "linkify", h.info = "auto", c.push(h);
                                const p = new t.Token("text", "", 0);
                                p.content = o, p.level = a, c.push(p);
                                const f = new t.Token("link_close", "a", -1);
                                f.level = --a, f.markup = "linkify", f.info = "auto", c.push(f), l = i[e].lastIndex
                            }
                            if (l < r.length) {
                                const e = new t.Token("text", "", 0);
                                e.content = r.slice(l), e.level = a, c.push(e)
                            }
                            e[n].children = s = W(s, o, c)
                        }
                    } else
                        for (o--; s[o].level !== u.level && "link_open" !== s[o].type;) o--
                }
            }
    }],
    ["replacements", function(t) {
        let e;
        if (t.md.options.typographer)
            for (e = t.tokens.length - 1; e >= 0; e--) "inline" === t.tokens[e].type && (At.test(t.tokens[e].content) && vt(t.tokens[e].children), bt.test(t.tokens[e].content) && St(t.tokens[e].children))
    }],
    ["smartquotes", function(t) {
        if (t.md.options.typographer)
            for (let e = t.tokens.length - 1; e >= 0; e--) "inline" === t.tokens[e].type && zt.test(t.tokens[e].content) && Lt(t.tokens[e].children, t)
    }],
    ["text_join", function(t) {
        let e, r;
        const n = t.tokens,
            s = n.length;
        for (let i = 0; i < s; i++) {
            if ("inline" !== n[i].type) continue;
            const t = n[i].children,
                s = t.length;
            for (e = 0; e < s; e++) "text_special" === t[e].type && (t[e].type = "text");
            for (e = r = 0; e < s; e++) "text" === t[e].type && e + 1 < s && "text" === t[e + 1].type ? t[e + 1].content = t[e].content + t[e + 1].content : (e !== r && (t[r] = t[e]), r++);
            e !== r && (t.length = r)
        }
    }]
];

function It() {
    this.ruler = new gt;
    for (let t = 0; t < Mt.length; t++) this.ruler.push(Mt[t][0], Mt[t][1])
}

function Tt(t, e, r, n) {
    this.src = t, this.md = e, this.env = r, this.tokens = n, this.bMarks = [], this.eMarks = [], this.tShift = [], this.sCount = [], this.bsCount = [], this.blkIndent = 0, this.line = 0, this.lineMax = 0, this.tight = !1, this.ddIndent = -1, this.listIndent = -1, this.parentType = "root", this.level = 0;
    const s = this.src;
    for (let i = 0, o = 0, u = 0, c = 0, a = s.length, l = !1; o < a; o++) {
        const t = s.charCodeAt(o);
        if (!l) {
            if (ut(t)) {
                u++, 9 === t ? c += 4 - c % 4 : c++;
                continue
            }
            l = !0
        }
        10 !== t && o !== a - 1 || (10 !== t && o++, this.bMarks.push(i), this.eMarks.push(o), this.tShift.push(u), this.sCount.push(c), this.bsCount.push(0), l = !1, u = 0, c = 0, i = o + 1)
    }
    this.bMarks.push(s.length), this.eMarks.push(s.length), this.tShift.push(0), this.sCount.push(0), this.bsCount.push(0), this.lineMax = this.bMarks.length - 1
}
It.prototype.process = function(t) {
    const e = this.ruler.getRules("");
    for (let r = 0, n = e.length; r < n; r++) e[r](t)
}, It.prototype.State = Dt, Tt.prototype.push = function(t, e, r) {
    const n = new kt(t, e, r);
    return n.block = !0, r < 0 && this.level--, n.level = this.level, r > 0 && this.level++, this.tokens.push(n), n
}, Tt.prototype.isEmpty = function(t) {
    return this.bMarks[t] + this.tShift[t] >= this.eMarks[t]
}, Tt.prototype.skipEmptyLines = function(t) {
    for (let e = this.lineMax; t < e && !(this.bMarks[t] + this.tShift[t] < this.eMarks[t]); t++);
    return t
}, Tt.prototype.skipSpaces = function(t) {
    for (let e = this.src.length; t < e; t++) {
        if (!ut(this.src.charCodeAt(t))) break
    }
    return t
}, Tt.prototype.skipSpacesBack = function(t, e) {
    if (t <= e) return t;
    for (; t > e;)
        if (!ut(this.src.charCodeAt(--t))) return t + 1;
    return t
}, Tt.prototype.skipChars = function(t, e) {
    for (let r = this.src.length; t < r && this.src.charCodeAt(t) === e; t++);
    return t
}, Tt.prototype.skipCharsBack = function(t, e, r) {
    if (t <= r) return t;
    for (; t > r;)
        if (e !== this.src.charCodeAt(--t)) return t + 1;
    return t
}, Tt.prototype.getLines = function(t, e, r, n) {
    if (t >= e) return "";
    const s = new Array(e - t);
    for (let i = 0, o = t; o < e; o++, i++) {
        let t = 0;
        const u = this.bMarks[o];
        let c, a = u;
        for (c = o + 1 < e || n ? this.eMarks[o] + 1 : this.eMarks[o]; a < c && t < r;) {
            const e = this.src.charCodeAt(a);
            if (ut(e)) 9 === e ? t += 4 - (t + this.bsCount[o]) % 4 : t++;
            else {
                if (!(a - u < this.tShift[o])) break;
                t++
            }
            a++
        }
        s[i] = t > r ? new Array(t - r + 1).join(" ") + this.src.slice(a, c) : this.src.slice(a, c)
    }
    return s.join("")
}, Tt.prototype.Token = kt;

function Rt(t, e) {
    const r = t.bMarks[e] + t.tShift[e],
        n = t.eMarks[e];
    return t.src.slice(r, n)
}

function Nt(t) {
    const e = [],
        r = t.length;
    let n = 0,
        s = t.charCodeAt(n),
        i = !1,
        o = 0,
        u = "";
    for (; n < r;) 124 === s && (i ? (u += t.substring(o, n - 1), o = n) : (e.push(u + t.substring(o, n)), u = "", o = n + 1)), i = 92 === s, n++, s = t.charCodeAt(n);
    return e.push(u + t.substring(o)), e
}

function Pt(t, e) {
    const r = t.eMarks[e];
    let n = t.bMarks[e] + t.tShift[e];
    const s = t.src.charCodeAt(n++);
    if (42 !== s && 45 !== s && 43 !== s) return -1;
    if (n < r) {
        if (!ut(t.src.charCodeAt(n))) return -1
    }
    return n
}

function Ot(t, e) {
    const r = t.bMarks[e] + t.tShift[e],
        n = t.eMarks[e];
    let s = r;
    if (s + 1 >= n) return -1;
    let i = t.src.charCodeAt(s++);
    if (i < 48 || i > 57) return -1;
    for (;;) {
        if (s >= n) return -1;
        if (i = t.src.charCodeAt(s++), !(i >= 48 && i <= 57)) {
            if (41 === i || 46 === i) break;
            return -1
        }
        if (s - r >= 10) return -1
    }
    return s < n && (i = t.src.charCodeAt(s), !ut(i)) ? -1 : s
}
const jt = "<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",
    Zt = "<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",
    $t = new RegExp("^(?:" + jt + "|" + Zt + "|\x3c!---?>|\x3c!--(?:[^-]|-[^-]|--[^>])*--\x3e|<[?][\\s\\S]*?[?]>|<![A-Za-z][^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)"),
    Ut = new RegExp("^(?:" + jt + "|" + Zt + ")"),
    Ht = [
        [/^<(script|pre|style|textarea)(?=(\s|>|$))/i, /<\/(script|pre|style|textarea)>/i, !0],
        [/^<!--/, /-->/, !0],
        [/^<\?/, /\?>/, !0],
        [/^<![A-Z]/, />/, !0],
        [/^<!\[CDATA\[/, /\]\]>/, !0],
        [new RegExp("^</?(" + ["address", "article", "aside", "base", "basefont", "blockquote", "body", "caption", "center", "col", "colgroup", "dd", "details", "dialog", "dir", "div", "dl", "dt", "fieldset", "figcaption", "figure", "footer", "form", "frame", "frameset", "h1", "h2", "h3", "h4", "h5", "h6", "head", "header", "hr", "html", "iframe", "legend", "li", "link", "main", "menu", "menuitem", "nav", "noframes", "ol", "optgroup", "option", "p", "param", "search", "section", "summary", "table", "tbody", "td", "tfoot", "th", "thead", "title", "tr", "track", "ul"].join("|") + ")(?=(\\s|/?>|$))", "i"), /^$/, !0],
        [new RegExp(Ut.source + "\\s*$"), /^$/, !1]
    ];
const Vt = [
    ["table", function(t, e, r, n) {
            if (e + 2 > r) return !1;
            let s = e + 1;
            if (t.sCount[s] < t.blkIndent) return !1;
            if (t.sCount[s] - t.blkIndent >= 4) return !1;
            let i = t.bMarks[s] + t.tShift[s];
            if (i >= t.eMarks[s]) return !1;
            const o = t.src.charCodeAt(i++);
            if (124 !== o && 45 !== o && 58 !== o) return !1;
            if (i >= t.eMarks[s]) return !1;
            const u = t.src.charCodeAt(i++);
            if (124 !== u && 45 !== u && 58 !== u && !ut(u)) return !1;
            if (45 === o && ut(u)) return !1;
            for (; i < t.eMarks[s];) {
                const e = t.src.charCodeAt(i);
                if (124 !== e && 45 !== e && 58 !== e && !ut(e)) return !1;
                i++
            }
            let c = Rt(t, e + 1),
                a = c.split("|");
            const l = [];
            for (let g = 0; g < a.length; g++) {
                const t = a[g].trim();
                if (!t) {
                    if (0 === g || g === a.length - 1) continue;
                    return !1
                }
                if (!/^:?-+:?$/.test(t)) return !1;
                58 === t.charCodeAt(t.length - 1) ? l.push(58 === t.charCodeAt(0) ? "center" : "right") : 58 === t.charCodeAt(0) ? l.push("left") : l.push("")
            }
            if (c = Rt(t, e).trim(), -1 === c.indexOf("|")) return !1;
            if (t.sCount[e] - t.blkIndent >= 4) return !1;
            a = Nt(c), a.length && "" === a[0] && a.shift(), a.length && "" === a[a.length - 1] && a.pop();
            const h = a.length;
            if (0 === h || h !== l.length) return !1;
            if (n) return !0;
            const p = t.parentType;
            t.parentType = "table";
            const f = t.md.block.ruler.getRules("blockquote"),
                d = [e, 0];
            t.push("table_open", "table", 1).map = d, t.push("thead_open", "thead", 1).map = [e, e + 1], t.push("tr_open", "tr", 1).map = [e, e + 1];
            for (let g = 0; g < a.length; g++) {
                const e = t.push("th_open", "th", 1);
                l[g] && (e.attrs = [
                    ["style", "text-align:" + l[g]]
                ]);
                const r = t.push("inline", "", 0);
                r.content = a[g].trim(), r.children = [], t.push("th_close", "th", -1)
            }
            let _;
            t.push("tr_close", "tr", -1), t.push("thead_close", "thead", -1);
            let m = 0;
            for (s = e + 2; s < r && !(t.sCount[s] < t.blkIndent); s++) {
                let n = !1;
                for (let e = 0, i = f.length; e < i; e++)
                    if (f[e](t, s, r, !0)) {
                        n = !0;
                        break
                    }
                if (n) break;
                if (c = Rt(t, s).trim(), !c) break;
                if (t.sCount[s] - t.blkIndent >= 4) break;
                if (a = Nt(c), a.length && "" === a[0] && a.shift(), a.length && "" === a[a.length - 1] && a.pop(), m += h - a.length, m > 65536) break;
                if (s === e + 2) {
                    t.push("tbody_open", "tbody", 1).map = _ = [e + 2, 0]
                }
                t.push("tr_open", "tr", 1).map = [s, s + 1];
                for (let e = 0; e < h; e++) {
                    const r = t.push("td_open", "td", 1);
                    l[e] && (r.attrs = [
                        ["style", "text-align:" + l[e]]
                    ]);
                    const n = t.push("inline", "", 0);
                    n.content = a[e] ? a[e].trim() : "", n.children = [], t.push("td_close", "td", -1)
                }
                t.push("tr_close", "tr", -1)
            }
            return _ && (t.push("tbody_close", "tbody", -1), _[1] = s), t.push("table_close", "table", -1), d[1] = s, t.parentType = p, t.line = s, !0
        },
        ["paragraph", "reference"]
    ],
    ["code", function(t, e, r) {
        if (t.sCount[e] - t.blkIndent < 4) return !1;
        let n = e + 1,
            s = n;
        for (; n < r;)
            if (t.isEmpty(n)) n++;
            else {
                if (!(t.sCount[n] - t.blkIndent >= 4)) break;
                n++, s = n
            }
        t.line = s;
        const i = t.push("code_block", "code", 0);
        return i.content = t.getLines(e, s, 4 + t.blkIndent, !1) + "\n", i.map = [e, t.line], !0
    }],
    ["fence", function(t, e, r, n) {
            let s = t.bMarks[e] + t.tShift[e],
                i = t.eMarks[e];
            if (t.sCount[e] - t.blkIndent >= 4) return !1;
            if (s + 3 > i) return !1;
            const o = t.src.charCodeAt(s);
            if (126 !== o && 96 !== o) return !1;
            let u = s;
            s = t.skipChars(s, o);
            let c = s - u;
            if (c < 3) return !1;
            const a = t.src.slice(u, s),
                l = t.src.slice(s, i);
            if (96 === o && l.indexOf(String.fromCharCode(o)) >= 0) return !1;
            if (n) return !0;
            let h = e,
                p = !1;
            for (;
                (h++, !(h >= r)) && (s = u = t.bMarks[h] + t.tShift[h], i = t.eMarks[h], !(s < i && t.sCount[h] < t.blkIndent));)
                if (t.src.charCodeAt(s) === o && !(t.sCount[h] - t.blkIndent >= 4 || (s = t.skipChars(s, o), s - u < c || (s = t.skipSpaces(s), s < i)))) {
                    p = !0;
                    break
                }
            c = t.sCount[e], t.line = h + (p ? 1 : 0);
            const f = t.push("fence", "code", 0);
            return f.info = l, f.content = t.getLines(e + 1, h, c, !0), f.markup = a, f.map = [e, t.line], !0
        },
        ["paragraph", "reference", "blockquote", "list"]
    ],
    ["blockquote", function(t, e, r, n) {
            let s = t.bMarks[e] + t.tShift[e],
                i = t.eMarks[e];
            const o = t.lineMax;
            if (t.sCount[e] - t.blkIndent >= 4) return !1;
            if (62 !== t.src.charCodeAt(s)) return !1;
            if (n) return !0;
            const u = [],
                c = [],
                a = [],
                l = [],
                h = t.md.block.ruler.getRules("blockquote"),
                p = t.parentType;
            t.parentType = "blockquote";
            let f, d = !1;
            for (f = e; f < r; f++) {
                const e = t.sCount[f] < t.blkIndent;
                if (s = t.bMarks[f] + t.tShift[f], i = t.eMarks[f], s >= i) break;
                if (62 === t.src.charCodeAt(s++) && !e) {
                    let e, r, n = t.sCount[f] + 1;
                    32 === t.src.charCodeAt(s) ? (s++, n++, r = !1, e = !0) : 9 === t.src.charCodeAt(s) ? (e = !0, (t.bsCount[f] + n) % 4 == 3 ? (s++, n++, r = !1) : r = !0) : e = !1;
                    let o = n;
                    for (u.push(t.bMarks[f]), t.bMarks[f] = s; s < i;) {
                        const e = t.src.charCodeAt(s);
                        if (!ut(e)) break;
                        9 === e ? o += 4 - (o + t.bsCount[f] + (r ? 1 : 0)) % 4 : o++, s++
                    }
                    d = s >= i, c.push(t.bsCount[f]), t.bsCount[f] = t.sCount[f] + 1 + (e ? 1 : 0), a.push(t.sCount[f]), t.sCount[f] = o - n, l.push(t.tShift[f]), t.tShift[f] = s - t.bMarks[f];
                    continue
                }
                if (d) break;
                let n = !1;
                for (let s = 0, i = h.length; s < i; s++)
                    if (h[s](t, f, r, !0)) {
                        n = !0;
                        break
                    }
                if (n) {
                    t.lineMax = f, 0 !== t.blkIndent && (u.push(t.bMarks[f]), c.push(t.bsCount[f]), l.push(t.tShift[f]), a.push(t.sCount[f]), t.sCount[f] -= t.blkIndent);
                    break
                }
                u.push(t.bMarks[f]), c.push(t.bsCount[f]), l.push(t.tShift[f]), a.push(t.sCount[f]), t.sCount[f] = -1
            }
            const _ = t.blkIndent;
            t.blkIndent = 0;
            const m = t.push("blockquote_open", "blockquote", 1);
            m.markup = ">";
            const g = [e, 0];
            m.map = g, t.md.block.tokenize(t, e, f), t.push("blockquote_close", "blockquote", -1).markup = ">", t.lineMax = o, t.parentType = p, g[1] = t.line;
            for (let k = 0; k < l.length; k++) t.bMarks[k + e] = u[k], t.tShift[k + e] = l[k], t.sCount[k + e] = a[k], t.bsCount[k + e] = c[k];
            return t.blkIndent = _, !0
        },
        ["paragraph", "reference", "blockquote", "list"]
    ],
    ["hr", function(t, e, r, n) {
            const s = t.eMarks[e];
            if (t.sCount[e] - t.blkIndent >= 4) return !1;
            let i = t.bMarks[e] + t.tShift[e];
            const o = t.src.charCodeAt(i++);
            if (42 !== o && 45 !== o && 95 !== o) return !1;
            let u = 1;
            for (; i < s;) {
                const e = t.src.charCodeAt(i++);
                if (e !== o && !ut(e)) return !1;
                e === o && u++
            }
            if (u < 3) return !1;
            if (n) return !0;
            t.line = e + 1;
            const c = t.push("hr", "hr", 0);
            return c.map = [e, t.line], c.markup = Array(u + 1).join(String.fromCharCode(o)), !0
        },
        ["paragraph", "reference", "blockquote", "list"]
    ],
    ["list", function(t, e, r, n) {
            let s, i, o, u, c = e,
                a = !0;
            if (t.sCount[c] - t.blkIndent >= 4) return !1;
            if (t.listIndent >= 0 && t.sCount[c] - t.listIndent >= 4 && t.sCount[c] < t.blkIndent) return !1;
            let l, h, p, f = !1;
            if (n && "paragraph" === t.parentType && t.sCount[c] >= t.blkIndent && (f = !0), (p = Ot(t, c)) >= 0) {
                if (l = !0, o = t.bMarks[c] + t.tShift[c], h = Number(t.src.slice(o, p - 1)), f && 1 !== h) return !1
            } else {
                if (!((p = Pt(t, c)) >= 0)) return !1;
                l = !1
            }
            if (f && t.skipSpaces(p) >= t.eMarks[c]) return !1;
            if (n) return !0;
            const d = t.src.charCodeAt(p - 1),
                _ = t.tokens.length;
            l ? (u = t.push("ordered_list_open", "ol", 1), 1 !== h && (u.attrs = [
                ["start", h]
            ])) : u = t.push("bullet_list_open", "ul", 1);
            const m = [c, 0];
            u.map = m, u.markup = String.fromCharCode(d);
            let g = !1;
            const k = t.md.block.ruler.getRules("list"),
                D = t.parentType;
            for (t.parentType = "list"; c < r;) {
                i = p, s = t.eMarks[c];
                const e = t.sCount[c] + p - (t.bMarks[c] + t.tShift[c]);
                let n = e;
                for (; i < s;) {
                    const e = t.src.charCodeAt(i);
                    if (9 === e) n += 4 - (n + t.bsCount[c]) % 4;
                    else {
                        if (32 !== e) break;
                        n++
                    }
                    i++
                }
                const h = i;
                let f;
                f = h >= s ? 1 : n - e, f > 4 && (f = 1);
                const _ = e + f;
                u = t.push("list_item_open", "li", 1), u.markup = String.fromCharCode(d);
                const m = [c, 0];
                u.map = m, l && (u.info = t.src.slice(o, p - 1));
                const D = t.tight,
                    C = t.tShift[c],
                    y = t.sCount[c],
                    E = t.listIndent;
                if (t.listIndent = t.blkIndent, t.blkIndent = _, t.tight = !0, t.tShift[c] = h - t.bMarks[c], t.sCount[c] = n, h >= s && t.isEmpty(c + 1) ? t.line = Math.min(t.line + 2, r) : t.md.block.tokenize(t, c, r, !0), t.tight && !g || (a = !1), g = t.line - c > 1 && t.isEmpty(t.line - 1), t.blkIndent = t.listIndent, t.listIndent = E, t.tShift[c] = C, t.sCount[c] = y, t.tight = D, u = t.push("list_item_close", "li", -1), u.markup = String.fromCharCode(d), c = t.line, m[1] = c, c >= r) break;
                if (t.sCount[c] < t.blkIndent) break;
                if (t.sCount[c] - t.blkIndent >= 4) break;
                let b = !1;
                for (let s = 0, i = k.length; s < i; s++)
                    if (k[s](t, c, r, !0)) {
                        b = !0;
                        break
                    }
                if (b) break;
                if (l) {
                    if (p = Ot(t, c), p < 0) break;
                    o = t.bMarks[c] + t.tShift[c]
                } else if (p = Pt(t, c), p < 0) break;
                if (d !== t.src.charCodeAt(p - 1)) break
            }
            return u = l ? t.push("ordered_list_close", "ol", -1) : t.push("bullet_list_close", "ul", -1), u.markup = String.fromCharCode(d), m[1] = c, t.line = c, t.parentType = D, a && function(t, e) {
                const r = t.level + 2;
                for (let n = e + 2, s = t.tokens.length - 2; n < s; n++) t.tokens[n].level === r && "paragraph_open" === t.tokens[n].type && (t.tokens[n + 2].hidden = !0, t.tokens[n].hidden = !0, n += 2)
            }(t, _), !0
        },
        ["paragraph", "reference", "blockquote"]
    ],
    ["reference", function(t, e, r, n) {
        let s = t.bMarks[e] + t.tShift[e],
            i = t.eMarks[e],
            o = e + 1;
        if (t.sCount[e] - t.blkIndent >= 4) return !1;
        if (91 !== t.src.charCodeAt(s)) return !1;

        function u(e) {
            const r = t.lineMax;
            if (e >= r || t.isEmpty(e)) return null;
            let n = !1;
            if (t.sCount[e] - t.blkIndent > 3 && (n = !0), t.sCount[e] < 0 && (n = !0), !n) {
                const n = t.md.block.ruler.getRules("reference"),
                    s = t.parentType;
                t.parentType = "reference";
                let i = !1;
                for (let o = 0, u = n.length; o < u; o++)
                    if (n[o](t, e, r, !0)) {
                        i = !0;
                        break
                    }
                if (t.parentType = s, i) return null
            }
            const s = t.bMarks[e] + t.tShift[e],
                i = t.eMarks[e];
            return t.src.slice(s, i + 1)
        }
        let c = t.src.slice(s, i + 1);
        i = c.length;
        let a = -1;
        for (s = 1; s < i; s++) {
            const t = c.charCodeAt(s);
            if (91 === t) return !1;
            if (93 === t) {
                a = s;
                break
            }
            if (10 === t) {
                const t = u(o);
                null !== t && (c += t, i = c.length, o++)
            } else if (92 === t && (s++, s < i && 10 === c.charCodeAt(s))) {
                const t = u(o);
                null !== t && (c += t, i = c.length, o++)
            }
        }
        if (a < 0 || 58 !== c.charCodeAt(a + 1)) return !1;
        for (s = a + 2; s < i; s++) {
            const t = c.charCodeAt(s);
            if (10 === t) {
                const t = u(o);
                null !== t && (c += t, i = c.length, o++)
            } else if (!ut(t)) break
        }
        const l = t.md.helpers.parseLinkDestination(c, s, i);
        if (!l.ok) return !1;
        const h = t.md.normalizeLink(l.str);
        if (!t.md.validateLink(h)) return !1;
        s = l.pos;
        const p = s,
            f = o,
            d = s;
        for (; s < i; s++) {
            const t = c.charCodeAt(s);
            if (10 === t) {
                const t = u(o);
                null !== t && (c += t, i = c.length, o++)
            } else if (!ut(t)) break
        }
        let _, m = t.md.helpers.parseLinkTitle(c, s, i);
        for (; m.can_continue;) {
            const e = u(o);
            if (null === e) break;
            c += e, s = i, i = c.length, o++, m = t.md.helpers.parseLinkTitle(c, s, i, m)
        }
        for (s < i && d !== s && m.ok ? (_ = m.str, s = m.pos) : (_ = "", s = p, o = f); s < i;) {
            if (!ut(c.charCodeAt(s))) break;
            s++
        }
        if (s < i && 10 !== c.charCodeAt(s) && _)
            for (_ = "", s = p, o = f; s < i;) {
                if (!ut(c.charCodeAt(s))) break;
                s++
            }
        if (s < i && 10 !== c.charCodeAt(s)) return !1;
        const g = ht(c.slice(1, a));
        return !!g && (n || (void 0 === t.env.references && (t.env.references = {}), void 0 === t.env.references[g] && (t.env.references[g] = {
            title: _,
            href: h
        }), t.line = o), !0)
    }],
    ["html_block", function(t, e, r, n) {
            let s = t.bMarks[e] + t.tShift[e],
                i = t.eMarks[e];
            if (t.sCount[e] - t.blkIndent >= 4) return !1;
            if (!t.md.options.html) return !1;
            if (60 !== t.src.charCodeAt(s)) return !1;
            let o = t.src.slice(s, i),
                u = 0;
            for (; u < Ht.length && !Ht[u][0].test(o); u++);
            if (u === Ht.length) return !1;
            if (n) return Ht[u][2];
            let c = e + 1;
            if (!Ht[u][1].test(o))
                for (; c < r && !(t.sCount[c] < t.blkIndent); c++)
                    if (s = t.bMarks[c] + t.tShift[c], i = t.eMarks[c], o = t.src.slice(s, i), Ht[u][1].test(o)) {
                        0 !== o.length && c++;
                        break
                    }
            t.line = c;
            const a = t.push("html_block", "", 0);
            return a.map = [e, c], a.content = t.getLines(e, c, t.blkIndent, !0), !0
        },
        ["paragraph", "reference", "blockquote"]
    ],
    ["heading", function(t, e, r, n) {
            let s = t.bMarks[e] + t.tShift[e],
                i = t.eMarks[e];
            if (t.sCount[e] - t.blkIndent >= 4) return !1;
            let o = t.src.charCodeAt(s);
            if (35 !== o || s >= i) return !1;
            let u = 1;
            for (o = t.src.charCodeAt(++s); 35 === o && s < i && u <= 6;) u++, o = t.src.charCodeAt(++s);
            if (u > 6 || s < i && !ut(o)) return !1;
            if (n) return !0;
            i = t.skipSpacesBack(i, s);
            const c = t.skipCharsBack(i, 35, s);
            c > s && ut(t.src.charCodeAt(c - 1)) && (i = c), t.line = e + 1;
            const a = t.push("heading_open", "h" + String(u), 1);
            a.markup = "########".slice(0, u), a.map = [e, t.line];
            const l = t.push("inline", "", 0);
            return l.content = t.src.slice(s, i).trim(), l.map = [e, t.line], l.children = [], t.push("heading_close", "h" + String(u), -1).markup = "########".slice(0, u), !0
        },
        ["paragraph", "reference", "blockquote"]
    ],
    ["lheading", function(t, e, r) {
        const n = t.md.block.ruler.getRules("paragraph");
        if (t.sCount[e] - t.blkIndent >= 4) return !1;
        const s = t.parentType;
        t.parentType = "paragraph";
        let i, o = 0,
            u = e + 1;
        for (; u < r && !t.isEmpty(u); u++) {
            if (t.sCount[u] - t.blkIndent > 3) continue;
            if (t.sCount[u] >= t.blkIndent) {
                let e = t.bMarks[u] + t.tShift[u];
                const r = t.eMarks[u];
                if (e < r && (i = t.src.charCodeAt(e), (45 === i || 61 === i) && (e = t.skipChars(e, i), e = t.skipSpaces(e), e >= r))) {
                    o = 61 === i ? 1 : 2;
                    break
                }
            }
            if (t.sCount[u] < 0) continue;
            let e = !1;
            for (let s = 0, i = n.length; s < i; s++)
                if (n[s](t, u, r, !0)) {
                    e = !0;
                    break
                }
            if (e) break
        }
        if (!o) return !1;
        const c = t.getLines(e, u, t.blkIndent, !1).trim();
        t.line = u + 1;
        const a = t.push("heading_open", "h" + String(o), 1);
        a.markup = String.fromCharCode(i), a.map = [e, t.line];
        const l = t.push("inline", "", 0);
        return l.content = c, l.map = [e, t.line - 1], l.children = [], t.push("heading_close", "h" + String(o), -1).markup = String.fromCharCode(i), t.parentType = s, !0
    }],
    ["paragraph", function(t, e, r) {
        const n = t.md.block.ruler.getRules("paragraph"),
            s = t.parentType;
        let i = e + 1;
        for (t.parentType = "paragraph"; i < r && !t.isEmpty(i); i++) {
            if (t.sCount[i] - t.blkIndent > 3) continue;
            if (t.sCount[i] < 0) continue;
            let e = !1;
            for (let s = 0, o = n.length; s < o; s++)
                if (n[s](t, i, r, !0)) {
                    e = !0;
                    break
                }
            if (e) break
        }
        const o = t.getLines(e, i, t.blkIndent, !1).trim();
        t.line = i, t.push("paragraph_open", "p", 1).map = [e, t.line];
        const u = t.push("inline", "", 0);
        return u.content = o, u.map = [e, t.line], u.children = [], t.push("paragraph_close", "p", -1), t.parentType = s, !0
    }]
];

function Gt() {
    this.ruler = new gt;
    for (let t = 0; t < Vt.length; t++) this.ruler.push(Vt[t][0], Vt[t][1], {
        alt: (Vt[t][2] || []).slice()
    })
}

function Wt(t, e, r, n) {
    this.src = t, this.env = r, this.md = e, this.tokens = n, this.tokens_meta = Array(n.length), this.pos = 0, this.posMax = this.src.length, this.level = 0, this.pending = "", this.pendingLevel = 0, this.cache = {}, this.delimiters = [], this._prev_delimiters = [], this.backticks = {}, this.backticksScanned = !1, this.linkLevel = 0
}

function Jt(t) {
    switch (t) {
        case 10:
        case 33:
        case 35:
        case 36:
        case 37:
        case 38:
        case 42:
        case 43:
        case 45:
        case 58:
        case 60:
        case 61:
        case 62:
        case 64:
        case 91:
        case 92:
        case 93:
        case 94:
        case 95:
        case 96:
        case 123:
        case 125:
        case 126:
            return !0;
        default:
            return !1
    }
}
Gt.prototype.tokenize = function(t, e, r) {
    const n = this.ruler.getRules(""),
        s = n.length,
        i = t.md.options.maxNesting;
    let o = e,
        u = !1;
    for (; o < r && (t.line = o = t.skipEmptyLines(o), !(o >= r)) && !(t.sCount[o] < t.blkIndent);) {
        if (t.level >= i) {
            t.line = r;
            break
        }
        const e = t.line;
        let c = !1;
        for (let i = 0; i < s; i++)
            if (c = n[i](t, o, r, !1), c) {
                if (e >= t.line) throw new Error("block rule didn't increment state.line");
                break
            }
        if (!c) throw new Error("none of the block rules matched");
        t.tight = !u, t.isEmpty(t.line - 1) && (u = !0), o = t.line, o < r && t.isEmpty(o) && (u = !0, o++, t.line = o)
    }
}, Gt.prototype.parse = function(t, e, r, n) {
    if (!t) return;
    const s = new this.State(t, e, r, n);
    this.tokenize(s, s.line, s.lineMax)
}, Gt.prototype.State = Tt, Wt.prototype.pushPending = function() {
    const t = new kt("text", "", 0);
    return t.content = this.pending, t.level = this.pendingLevel, this.tokens.push(t), this.pending = "", t
}, Wt.prototype.push = function(t, e, r) {
    this.pending && this.pushPending();
    const n = new kt(t, e, r);
    let s = null;
    return r < 0 && (this.level--, this.delimiters = this._prev_delimiters.pop()), n.level = this.level, r > 0 && (this.level++, this._prev_delimiters.push(this.delimiters), this.delimiters = [], s = {
        delimiters: this.delimiters
    }), this.pendingLevel = this.level, this.tokens.push(n), this.tokens_meta.push(s), n
}, Wt.prototype.scanDelims = function(t, e) {
    const r = this.posMax,
        n = this.src.charCodeAt(t),
        s = t > 0 ? this.src.charCodeAt(t - 1) : 32;
    let i = t;
    for (; i < r && this.src.charCodeAt(i) === n;) i++;
    const o = i - t,
        u = i < r ? this.src.charCodeAt(i) : 32,
        c = lt(s) || at(String.fromCharCode(s)),
        a = lt(u) || at(String.fromCharCode(u)),
        l = ct(s),
        h = ct(u),
        p = !h && (!a || l || c),
        f = !l && (!c || h || a);
    return {
        can_open: p && (e || !f || c),
        can_close: f && (e || !p || a),
        length: o
    }
}, Wt.prototype.Token = kt;
const Qt = /(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;
const Xt = [];
for (let Ve = 0; Ve < 256; Ve++) Xt.push(0);

function Yt(t, e) {
    let r;
    const n = [],
        s = e.length;
    for (let i = 0; i < s; i++) {
        const s = e[i];
        if (126 !== s.marker) continue;
        if (-1 === s.end) continue;
        const o = e[s.end];
        r = t.tokens[s.token], r.type = "s_open", r.tag = "s", r.nesting = 1, r.markup = "~~", r.content = "", r = t.tokens[o.token], r.type = "s_close", r.tag = "s", r.nesting = -1, r.markup = "~~", r.content = "", "text" === t.tokens[o.token - 1].type && "~" === t.tokens[o.token - 1].content && n.push(o.token - 1)
    }
    for (; n.length;) {
        const e = n.pop();
        let s = e + 1;
        for (; s < t.tokens.length && "s_close" === t.tokens[s].type;) s++;
        s--, e !== s && (r = t.tokens[s], t.tokens[s] = t.tokens[e], t.tokens[e] = r)
    }
}
"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach((function(t) {
    Xt[t.charCodeAt(0)] = 1
}));
const Kt = {
    tokenize: function(t, e) {
        const r = t.pos,
            n = t.src.charCodeAt(r);
        if (e) return !1;
        if (126 !== n) return !1;
        const s = t.scanDelims(t.pos, !0);
        let i = s.length;
        const o = String.fromCharCode(n);
        if (i < 2) return !1;
        let u;
        i % 2 && (u = t.push("text", "", 0), u.content = o, i--);
        for (let c = 0; c < i; c += 2) u = t.push("text", "", 0), u.content = o + o, t.delimiters.push({
            marker: n,
            length: 0,
            token: t.tokens.length - 1,
            end: -1,
            open: s.can_open,
            close: s.can_close
        });
        return t.pos += s.length, !0
    },
    postProcess: function(t) {
        const e = t.tokens_meta,
            r = t.tokens_meta.length;
        Yt(t, t.delimiters);
        for (let n = 0; n < r; n++) e[n] && e[n].delimiters && Yt(t, e[n].delimiters)
    }
};

function te(t, e) {
    for (let r = e.length - 1; r >= 0; r--) {
        const n = e[r];
        if (95 !== n.marker && 42 !== n.marker) continue;
        if (-1 === n.end) continue;
        const s = e[n.end],
            i = r > 0 && e[r - 1].end === n.end + 1 && e[r - 1].marker === n.marker && e[r - 1].token === n.token - 1 && e[n.end + 1].token === s.token + 1,
            o = String.fromCharCode(n.marker),
            u = t.tokens[n.token];
        u.type = i ? "strong_open" : "em_open", u.tag = i ? "strong" : "em", u.nesting = 1, u.markup = i ? o + o : o, u.content = "";
        const c = t.tokens[s.token];
        c.type = i ? "strong_close" : "em_close", c.tag = i ? "strong" : "em", c.nesting = -1, c.markup = i ? o + o : o, c.content = "", i && (t.tokens[e[r - 1].token].content = "", t.tokens[e[n.end + 1].token].content = "", r--)
    }
}
const ee = {
    tokenize: function(t, e) {
        const r = t.pos,
            n = t.src.charCodeAt(r);
        if (e) return !1;
        if (95 !== n && 42 !== n) return !1;
        const s = t.scanDelims(t.pos, 42 === n);
        for (let i = 0; i < s.length; i++) {
            t.push("text", "", 0).content = String.fromCharCode(n), t.delimiters.push({
                marker: n,
                length: s.length,
                token: t.tokens.length - 1,
                end: -1,
                open: s.can_open,
                close: s.can_close
            })
        }
        return t.pos += s.length, !0
    },
    postProcess: function(t) {
        const e = t.tokens_meta,
            r = t.tokens_meta.length;
        te(t, t.delimiters);
        for (let n = 0; n < r; n++) e[n] && e[n].delimiters && te(t, e[n].delimiters)
    }
};
const re = /^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,
    ne = /^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\x00-\x20]*)$/;
const se = /^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,
    ie = /^&([a-z][a-z0-9]{1,31});/i;

function oe(t) {
    const e = {},
        r = t.length;
    if (!r) return;
    let n = 0,
        s = -2;
    const i = [];
    for (let o = 0; o < r; o++) {
        const r = t[o];
        if (i.push(0), t[n].marker === r.marker && s === r.token - 1 || (n = o), s = r.token, r.length = r.length || 0, !r.close) continue;
        e.hasOwnProperty(r.marker) || (e[r.marker] = [-1, -1, -1, -1, -1, -1]);
        const u = e[r.marker][(r.open ? 3 : 0) + r.length % 3];
        let c = n - i[n] - 1,
            a = c;
        for (; c > u; c -= i[c] + 1) {
            const e = t[c];
            if (e.marker === r.marker && (e.open && e.end < 0)) {
                let n = !1;
                if ((e.close || r.open) && (e.length + r.length) % 3 == 0 && (e.length % 3 == 0 && r.length % 3 == 0 || (n = !0)), !n) {
                    const n = c > 0 && !t[c - 1].open ? i[c - 1] + 1 : 0;
                    i[o] = o - c + n, i[c] = n, r.open = !1, e.end = o, e.close = !1, a = -1, s = -2;
                    break
                }
            }
        } - 1 !== a && (e[r.marker][(r.open ? 3 : 0) + (r.length || 0) % 3] = a)
    }
}
const ue = [
        ["text", function(t, e) {
            let r = t.pos;
            for (; r < t.posMax && !Jt(t.src.charCodeAt(r));) r++;
            return r !== t.pos && (e || (t.pending += t.src.slice(t.pos, r)), t.pos = r, !0)
        }],
        ["linkify", function(t, e) {
            if (!t.md.options.linkify) return !1;
            if (t.linkLevel > 0) return !1;
            const r = t.pos;
            if (r + 3 > t.posMax) return !1;
            if (58 !== t.src.charCodeAt(r)) return !1;
            if (47 !== t.src.charCodeAt(r + 1)) return !1;
            if (47 !== t.src.charCodeAt(r + 2)) return !1;
            const n = t.pending.match(Qt);
            if (!n) return !1;
            const s = n[1],
                i = t.md.linkify.matchAtStart(t.src.slice(r - s.length));
            if (!i) return !1;
            let o = i.url;
            if (o.length <= s.length) return !1;
            o = o.replace(/\*+$/, "");
            const u = t.md.normalizeLink(o);
            if (!t.md.validateLink(u)) return !1;
            if (!e) {
                t.pending = t.pending.slice(0, -s.length);
                const e = t.push("link_open", "a", 1);
                e.attrs = [
                    ["href", u]
                ], e.markup = "linkify", e.info = "auto";
                t.push("text", "", 0).content = t.md.normalizeLinkText(o);
                const r = t.push("link_close", "a", -1);
                r.markup = "linkify", r.info = "auto"
            }
            return t.pos += o.length - s.length, !0
        }],
        ["newline", function(t, e) {
            let r = t.pos;
            if (10 !== t.src.charCodeAt(r)) return !1;
            const n = t.pending.length - 1,
                s = t.posMax;
            if (!e)
                if (n >= 0 && 32 === t.pending.charCodeAt(n))
                    if (n >= 1 && 32 === t.pending.charCodeAt(n - 1)) {
                        let e = n - 1;
                        for (; e >= 1 && 32 === t.pending.charCodeAt(e - 1);) e--;
                        t.pending = t.pending.slice(0, e), t.push("hardbreak", "br", 0)
                    } else t.pending = t.pending.slice(0, -1), t.push("softbreak", "br", 0);
            else t.push("softbreak", "br", 0);
            for (r++; r < s && ut(t.src.charCodeAt(r));) r++;
            return t.pos = r, !0
        }],
        ["escape", function(t, e) {
            let r = t.pos;
            const n = t.posMax;
            if (92 !== t.src.charCodeAt(r)) return !1;
            if (r++, r >= n) return !1;
            let s = t.src.charCodeAt(r);
            if (10 === s) {
                for (e || t.push("hardbreak", "br", 0), r++; r < n && (s = t.src.charCodeAt(r), ut(s));) r++;
                return t.pos = r, !0
            }
            let i = t.src[r];
            if (s >= 55296 && s <= 56319 && r + 1 < n) {
                const e = t.src.charCodeAt(r + 1);
                e >= 56320 && e <= 57343 && (i += t.src[r + 1], r++)
            }
            const o = "\\" + i;
            if (!e) {
                const e = t.push("text_special", "", 0);
                s < 256 && 0 !== Xt[s] ? e.content = i : e.content = o, e.markup = o, e.info = "escape"
            }
            return t.pos = r + 1, !0
        }],
        ["backticks", function(t, e) {
            let r = t.pos;
            if (96 !== t.src.charCodeAt(r)) return !1;
            const n = r;
            r++;
            const s = t.posMax;
            for (; r < s && 96 === t.src.charCodeAt(r);) r++;
            const i = t.src.slice(n, r),
                o = i.length;
            if (t.backticksScanned && (t.backticks[o] || 0) <= n) return e || (t.pending += i), t.pos += o, !0;
            let u, c = r;
            for (; - 1 !== (u = t.src.indexOf("`", c));) {
                for (c = u + 1; c < s && 96 === t.src.charCodeAt(c);) c++;
                const n = c - u;
                if (n === o) {
                    if (!e) {
                        const e = t.push("code_inline", "code", 0);
                        e.markup = i, e.content = t.src.slice(r, u).replace(/\n/g, " ").replace(/^ (.+) $/, "$1")
                    }
                    return t.pos = c, !0
                }
                t.backticks[n] = u
            }
            return t.backticksScanned = !0, e || (t.pending += i), t.pos += o, !0
        }],
        ["strikethrough", Kt.tokenize],
        ["emphasis", ee.tokenize],
        ["link", function(t, e) {
            let r, n, s, i, o = "",
                u = "",
                c = t.pos,
                a = !0;
            if (91 !== t.src.charCodeAt(t.pos)) return !1;
            const l = t.pos,
                h = t.posMax,
                p = t.pos + 1,
                f = t.md.helpers.parseLinkLabel(t, t.pos, !0);
            if (f < 0) return !1;
            let d = f + 1;
            if (d < h && 40 === t.src.charCodeAt(d)) {
                for (a = !1, d++; d < h && (r = t.src.charCodeAt(d), ut(r) || 10 === r); d++);
                if (d >= h) return !1;
                if (c = d, s = t.md.helpers.parseLinkDestination(t.src, d, t.posMax), s.ok) {
                    for (o = t.md.normalizeLink(s.str), t.md.validateLink(o) ? d = s.pos : o = "", c = d; d < h && (r = t.src.charCodeAt(d), ut(r) || 10 === r); d++);
                    if (s = t.md.helpers.parseLinkTitle(t.src, d, t.posMax), d < h && c !== d && s.ok)
                        for (u = s.str, d = s.pos; d < h && (r = t.src.charCodeAt(d), ut(r) || 10 === r); d++);
                }(d >= h || 41 !== t.src.charCodeAt(d)) && (a = !0), d++
            }
            if (a) {
                if (void 0 === t.env.references) return !1;
                if (d < h && 91 === t.src.charCodeAt(d) ? (c = d + 1, d = t.md.helpers.parseLinkLabel(t, d), d >= 0 ? n = t.src.slice(c, d++) : d = f + 1) : d = f + 1, n || (n = t.src.slice(p, f)), i = t.env.references[ht(n)], !i) return t.pos = l, !1;
                o = i.href, u = i.title
            }
            if (!e) {
                t.pos = p, t.posMax = f;
                const e = [
                    ["href", o]
                ];
                t.push("link_open", "a", 1).attrs = e, u && e.push(["title", u]), t.linkLevel++, t.md.inline.tokenize(t), t.linkLevel--, t.push("link_close", "a", -1)
            }
            return t.pos = d, t.posMax = h, !0
        }],
        ["image", function(t, e) {
            let r, n, s, i, o, u, c, a, l = "";
            const h = t.pos,
                p = t.posMax;
            if (33 !== t.src.charCodeAt(t.pos)) return !1;
            if (91 !== t.src.charCodeAt(t.pos + 1)) return !1;
            const f = t.pos + 2,
                d = t.md.helpers.parseLinkLabel(t, t.pos + 1, !1);
            if (d < 0) return !1;
            if (i = d + 1, i < p && 40 === t.src.charCodeAt(i)) {
                for (i++; i < p && (r = t.src.charCodeAt(i), ut(r) || 10 === r); i++);
                if (i >= p) return !1;
                for (a = i, u = t.md.helpers.parseLinkDestination(t.src, i, t.posMax), u.ok && (l = t.md.normalizeLink(u.str), t.md.validateLink(l) ? i = u.pos : l = ""), a = i; i < p && (r = t.src.charCodeAt(i), ut(r) || 10 === r); i++);
                if (u = t.md.helpers.parseLinkTitle(t.src, i, t.posMax), i < p && a !== i && u.ok)
                    for (c = u.str, i = u.pos; i < p && (r = t.src.charCodeAt(i), ut(r) || 10 === r); i++);
                else c = "";
                if (i >= p || 41 !== t.src.charCodeAt(i)) return t.pos = h, !1;
                i++
            } else {
                if (void 0 === t.env.references) return !1;
                if (i < p && 91 === t.src.charCodeAt(i) ? (a = i + 1, i = t.md.helpers.parseLinkLabel(t, i), i >= 0 ? s = t.src.slice(a, i++) : i = d + 1) : i = d + 1, s || (s = t.src.slice(f, d)), o = t.env.references[ht(s)], !o) return t.pos = h, !1;
                l = o.href, c = o.title
            }
            if (!e) {
                n = t.src.slice(f, d);
                const e = [];
                t.md.inline.parse(n, t.md, t.env, e);
                const r = t.push("image", "img", 0),
                    s = [
                        ["src", l],
                        ["alt", ""]
                    ];
                r.attrs = s, r.children = e, r.content = n, c && s.push(["title", c])
            }
            return t.pos = i, t.posMax = p, !0
        }],
        ["autolink", function(t, e) {
            let r = t.pos;
            if (60 !== t.src.charCodeAt(r)) return !1;
            const n = t.pos,
                s = t.posMax;
            for (;;) {
                if (++r >= s) return !1;
                const e = t.src.charCodeAt(r);
                if (60 === e) return !1;
                if (62 === e) break
            }
            const i = t.src.slice(n + 1, r);
            if (ne.test(i)) {
                const r = t.md.normalizeLink(i);
                if (!t.md.validateLink(r)) return !1;
                if (!e) {
                    const e = t.push("link_open", "a", 1);
                    e.attrs = [
                        ["href", r]
                    ], e.markup = "autolink", e.info = "auto";
                    t.push("text", "", 0).content = t.md.normalizeLinkText(i);
                    const n = t.push("link_close", "a", -1);
                    n.markup = "autolink", n.info = "auto"
                }
                return t.pos += i.length + 2, !0
            }
            if (re.test(i)) {
                const r = t.md.normalizeLink("mailto:" + i);
                if (!t.md.validateLink(r)) return !1;
                if (!e) {
                    const e = t.push("link_open", "a", 1);
                    e.attrs = [
                        ["href", r]
                    ], e.markup = "autolink", e.info = "auto";
                    t.push("text", "", 0).content = t.md.normalizeLinkText(i);
                    const n = t.push("link_close", "a", -1);
                    n.markup = "autolink", n.info = "auto"
                }
                return t.pos += i.length + 2, !0
            }
            return !1
        }],
        ["html_inline", function(t, e) {
            if (!t.md.options.html) return !1;
            const r = t.posMax,
                n = t.pos;
            if (60 !== t.src.charCodeAt(n) || n + 2 >= r) return !1;
            const s = t.src.charCodeAt(n + 1);
            if (33 !== s && 63 !== s && 47 !== s && ! function(t) {
                    const e = 32 | t;
                    return e >= 97 && e <= 122
                }(s)) return !1;
            const i = t.src.slice(n).match($t);
            if (!i) return !1;
            if (!e) {
                const e = t.push("html_inline", "", 0);
                e.content = i[0], o = e.content, /^<a[>\s]/i.test(o) && t.linkLevel++,
                    function(t) {
                        return /^<\/a\s*>/i.test(t)
                    }(e.content) && t.linkLevel--
            }
            var o;
            return t.pos += i[0].length, !0
        }],
        ["entity", function(t, e) {
            const r = t.pos,
                n = t.posMax;
            if (38 !== t.src.charCodeAt(r)) return !1;
            if (r + 1 >= n) return !1;
            if (35 === t.src.charCodeAt(r + 1)) {
                const n = t.src.slice(r).match(se);
                if (n) {
                    if (!e) {
                        const e = "x" === n[1][0].toLowerCase() ? parseInt(n[1].slice(1), 16) : parseInt(n[1], 10),
                            r = t.push("text_special", "", 0);
                        r.content = J(e) ? Q(e) : Q(65533), r.markup = n[0], r.info = "entity"
                    }
                    return t.pos += n[0].length, !0
                }
            } else {
                const n = t.src.slice(r).match(ie);
                if (n) {
                    const r = D(n[0]);
                    if (r !== n[0]) {
                        if (!e) {
                            const e = t.push("text_special", "", 0);
                            e.content = r, e.markup = n[0], e.info = "entity"
                        }
                        return t.pos += n[0].length, !0
                    }
                }
            }
            return !1
        }]
    ],
    ce = [
        ["balance_pairs", function(t) {
            const e = t.tokens_meta,
                r = t.tokens_meta.length;
            oe(t.delimiters);
            for (let n = 0; n < r; n++) e[n] && e[n].delimiters && oe(e[n].delimiters)
        }],
        ["strikethrough", Kt.postProcess],
        ["emphasis", ee.postProcess],
        ["fragments_join", function(t) {
            let e, r, n = 0;
            const s = t.tokens,
                i = t.tokens.length;
            for (e = r = 0; e < i; e++) s[e].nesting < 0 && n--, s[e].level = n, s[e].nesting > 0 && n++, "text" === s[e].type && e + 1 < i && "text" === s[e + 1].type ? s[e + 1].content = s[e].content + s[e + 1].content : (e !== r && (s[r] = s[e]), r++);
            e !== r && (s.length = r)
        }]
    ];

function ae() {
    this.ruler = new gt;
    for (let t = 0; t < ue.length; t++) this.ruler.push(ue[t][0], ue[t][1]);
    this.ruler2 = new gt;
    for (let t = 0; t < ce.length; t++) this.ruler2.push(ce[t][0], ce[t][1])
}

function le(t) {
    return Array.prototype.slice.call(arguments, 1).forEach((function(e) {
        e && Object.keys(e).forEach((function(r) {
            t[r] = e[r]
        }))
    })), t
}

function he(t) {
    return Object.prototype.toString.call(t)
}

function pe(t) {
    return "[object Function]" === he(t)
}

function fe(t) {
    return t.replace(/[.?*+^$[\]\\(){}|-]/g, "\\$&")
}
ae.prototype.skipToken = function(t) {
    const e = t.pos,
        r = this.ruler.getRules(""),
        n = r.length,
        s = t.md.options.maxNesting,
        i = t.cache;
    if (void 0 !== i[e]) return void(t.pos = i[e]);
    let o = !1;
    if (t.level < s) {
        for (let u = 0; u < n; u++)
            if (t.level++, o = r[u](t, !0), t.level--, o) {
                if (e >= t.pos) throw new Error("inline rule didn't increment state.pos");
                break
            }
    } else t.pos = t.posMax;
    o || t.pos++, i[e] = t.pos
}, ae.prototype.tokenize = function(t) {
    const e = this.ruler.getRules(""),
        r = e.length,
        n = t.posMax,
        s = t.md.options.maxNesting;
    for (; t.pos < n;) {
        const i = t.pos;
        let o = !1;
        if (t.level < s)
            for (let n = 0; n < r; n++)
                if (o = e[n](t, !1), o) {
                    if (i >= t.pos) throw new Error("inline rule didn't increment state.pos");
                    break
                }
        if (o) {
            if (t.pos >= n) break
        } else t.pending += t.src[t.pos++]
    }
    t.pending && t.pushPending()
}, ae.prototype.parse = function(t, e, r, n) {
    const s = new this.State(t, e, r, n);
    this.tokenize(s);
    const i = this.ruler2.getRules(""),
        o = i.length;
    for (let u = 0; u < o; u++) i[u](s)
}, ae.prototype.State = Wt;
const de = {
    fuzzyLink: !0,
    fuzzyEmail: !0,
    fuzzyIP: !1
};
const _e = {
        "http:": {
            validate: function(t, e, r) {
                const n = t.slice(e);
                return r.re.http || (r.re.http = new RegExp("^\\/\\/" + r.re.src_auth + r.re.src_host_port_strict + r.re.src_path, "i")), r.re.http.test(n) ? n.match(r.re.http)[0].length : 0
            }
        },
        "https:": "http:",
        "ftp:": "http:",
        "//": {
            validate: function(t, e, r) {
                const n = t.slice(e);
                return r.re.no_http || (r.re.no_http = new RegExp("^" + r.re.src_auth + "(?:localhost|(?:(?:" + r.re.src_domain + ")\\.)+" + r.re.src_domain_root + ")" + r.re.src_port + r.re.src_host_terminator + r.re.src_path, "i")), r.re.no_http.test(n) ? e >= 3 && ":" === t[e - 3] || e >= 3 && "/" === t[e - 3] ? 0 : n.match(r.re.no_http)[0].length : 0
            }
        },
        "mailto:": {
            validate: function(t, e, r) {
                const n = t.slice(e);
                return r.re.mailto || (r.re.mailto = new RegExp("^" + r.re.src_email_name + "@" + r.re.src_host_strict, "i")), r.re.mailto.test(n) ? n.match(r.re.mailto)[0].length : 0
            }
        }
    },
    me = "biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");

function ge(t) {
    const e = t.re = function(t) {
            const e = {};
            t = t || {}, e.src_Any = P.source, e.src_Cc = O.source, e.src_Z = $.source, e.src_P = j.source, e.src_ZPCc = [e.src_Z, e.src_P, e.src_Cc].join("|"), e.src_ZCc = [e.src_Z, e.src_Cc].join("|");
            const r = "[><｜]";
            return e.src_pseudo_letter = "(?:(?![><｜]|" + e.src_ZPCc + ")" + e.src_Any + ")", e.src_ip4 = "(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)", e.src_auth = "(?:(?:(?!" + e.src_ZCc + "|[@/\\[\\]()]).)+@)?", e.src_port = "(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?", e.src_host_terminator = "(?=$|[><｜]|" + e.src_ZPCc + ")(?!" + (t["---"] ? "-(?!--)|" : "-|") + "_|:\\d|\\.-|\\.(?!$|" + e.src_ZPCc + "))", e.src_path = "(?:[/?#](?:(?!" + e.src_ZCc + "|" + r + "|[()[\\]{}.,\"'?!\\-;]).|\\[(?:(?!" + e.src_ZCc + "|\\]).)*\\]|\\((?:(?!" + e.src_ZCc + "|[)]).)*\\)|\\{(?:(?!" + e.src_ZCc + '|[}]).)*\\}|\\"(?:(?!' + e.src_ZCc + '|["]).)+\\"|\\\'(?:(?!' + e.src_ZCc + "|[']).)+\\'|\\'(?=" + e.src_pseudo_letter + "|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!" + e.src_ZCc + "|[.]|$)|" + (t["---"] ? "\\-(?!--(?:[^-]|$))(?:-*)|" : "\\-+|") + ",(?!" + e.src_ZCc + "|$)|;(?!" + e.src_ZCc + "|$)|\\!+(?!" + e.src_ZCc + "|[!]|$)|\\?(?!" + e.src_ZCc + "|[?]|$))+|\\/)?", e.src_email_name = '[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*', e.src_xn = "xn--[a-z0-9\\-]{1,59}", e.src_domain_root = "(?:" + e.src_xn + "|" + e.src_pseudo_letter + "{1,63})", e.src_domain = "(?:" + e.src_xn + "|(?:" + e.src_pseudo_letter + ")|(?:" + e.src_pseudo_letter + "(?:-|" + e.src_pseudo_letter + "){0,61}" + e.src_pseudo_letter + "))", e.src_host = "(?:(?:(?:(?:" + e.src_domain + ")\\.)*" + e.src_domain + "))", e.tpl_host_fuzzy = "(?:" + e.src_ip4 + "|(?:(?:(?:" + e.src_domain + ")\\.)+(?:%TLDS%)))", e.tpl_host_no_ip_fuzzy = "(?:(?:(?:" + e.src_domain + ")\\.)+(?:%TLDS%))", e.src_host_strict = e.src_host + e.src_host_terminator, e.tpl_host_fuzzy_strict = e.tpl_host_fuzzy + e.src_host_terminator, e.src_host_port_strict = e.src_host + e.src_port + e.src_host_terminator, e.tpl_host_port_fuzzy_strict = e.tpl_host_fuzzy + e.src_port + e.src_host_terminator, e.tpl_host_port_no_ip_fuzzy_strict = e.tpl_host_no_ip_fuzzy + e.src_port + e.src_host_terminator, e.tpl_host_fuzzy_test = "localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:" + e.src_ZPCc + "|>|$))", e.tpl_email_fuzzy = '(^|[><｜]|"|\\(|' + e.src_ZCc + ")(" + e.src_email_name + "@" + e.tpl_host_fuzzy_strict + ")", e.tpl_link_fuzzy = "(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|" + e.src_ZPCc + "))((?![$+<=>^`|｜])" + e.tpl_host_port_fuzzy_strict + e.src_path + ")", e.tpl_link_no_ip_fuzzy = "(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|" + e.src_ZPCc + "))((?![$+<=>^`|｜])" + e.tpl_host_port_no_ip_fuzzy_strict + e.src_path + ")", e
        }(t.__opts__),
        r = t.__tlds__.slice();

    function n(t) {
        return t.replace("%TLDS%", e.src_tlds)
    }
    t.onCompile(), t.__tlds_replaced__ || r.push("a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]"), r.push(e.src_xn), e.src_tlds = r.join("|"), e.email_fuzzy = RegExp(n(e.tpl_email_fuzzy), "i"), e.link_fuzzy = RegExp(n(e.tpl_link_fuzzy), "i"), e.link_no_ip_fuzzy = RegExp(n(e.tpl_link_no_ip_fuzzy), "i"), e.host_fuzzy_test = RegExp(n(e.tpl_host_fuzzy_test), "i");
    const s = [];

    function i(t, e) {
        throw new Error('(LinkifyIt) Invalid schema "' + t + '": ' + e)
    }
    t.__compiled__ = {}, Object.keys(t.__schemas__).forEach((function(e) {
        const r = t.__schemas__[e];
        if (null === r) return;
        const n = {
            validate: null,
            link: null
        };
        if (t.__compiled__[e] = n, "[object Object]" === he(r)) return ! function(t) {
            return "[object RegExp]" === he(t)
        }(r.validate) ? pe(r.validate) ? n.validate = r.validate : i(e, r) : n.validate = function(t) {
            return function(e, r) {
                const n = e.slice(r);
                return t.test(n) ? n.match(t)[0].length : 0
            }
        }(r.validate), void(pe(r.normalize) ? n.normalize = r.normalize : r.normalize ? i(e, r) : n.normalize = function(t, e) {
            e.normalize(t)
        });
        ! function(t) {
            return "[object String]" === he(t)
        }(r) ? i(e, r): s.push(e)
    })), s.forEach((function(e) {
        t.__compiled__[t.__schemas__[e]] && (t.__compiled__[e].validate = t.__compiled__[t.__schemas__[e]].validate, t.__compiled__[e].normalize = t.__compiled__[t.__schemas__[e]].normalize)
    })), t.__compiled__[""] = {
        validate: null,
        normalize: function(t, e) {
            e.normalize(t)
        }
    };
    const o = Object.keys(t.__compiled__).filter((function(e) {
        return e.length > 0 && t.__compiled__[e]
    })).map(fe).join("|");
    t.re.schema_test = RegExp("(^|(?!_)(?:[><｜]|" + e.src_ZPCc + "))(" + o + ")", "i"), t.re.schema_search = RegExp("(^|(?!_)(?:[><｜]|" + e.src_ZPCc + "))(" + o + ")", "ig"), t.re.schema_at_start = RegExp("^" + t.re.schema_search.source, "i"), t.re.pretest = RegExp("(" + t.re.schema_test.source + ")|(" + t.re.host_fuzzy_test.source + ")|@", "i"),
        function(t) {
            t.__index__ = -1, t.__text_cache__ = ""
        }(t)
}

function ke(t, e) {
    const r = t.__index__,
        n = t.__last_index__,
        s = t.__text_cache__.slice(r, n);
    this.schema = t.__schema__.toLowerCase(), this.index = r + e, this.lastIndex = n + e, this.raw = s, this.text = s, this.url = s
}

function De(t, e) {
    const r = new ke(t, e);
    return t.__compiled__[r.schema].normalize(r, t), r
}

function Ce(t, e) {
    if (!(this instanceof Ce)) return new Ce(t, e);
    var r;
    e || (r = t, Object.keys(r || {}).reduce((function(t, e) {
        return t || de.hasOwnProperty(e)
    }), !1) && (e = t, t = {})), this.__opts__ = le({}, de, e), this.__index__ = -1, this.__last_index__ = -1, this.__schema__ = "", this.__text_cache__ = "", this.__schemas__ = le({}, _e, t), this.__compiled__ = {}, this.__tlds__ = me, this.__tlds_replaced__ = !1, this.re = {}, ge(this)
}
Ce.prototype.add = function(t, e) {
    return this.__schemas__[t] = e, ge(this), this
}, Ce.prototype.set = function(t) {
    return this.__opts__ = le(this.__opts__, t), this
}, Ce.prototype.test = function(t) {
    if (this.__text_cache__ = t, this.__index__ = -1, !t.length) return !1;
    let e, r, n, s, i, o, u, c, a;
    if (this.re.schema_test.test(t))
        for (u = this.re.schema_search, u.lastIndex = 0; null !== (e = u.exec(t));)
            if (s = this.testSchemaAt(t, e[2], u.lastIndex), s) {
                this.__schema__ = e[2], this.__index__ = e.index + e[1].length, this.__last_index__ = e.index + e[0].length + s;
                break
            }
    return this.__opts__.fuzzyLink && this.__compiled__["http:"] && (c = t.search(this.re.host_fuzzy_test), c >= 0 && (this.__index__ < 0 || c < this.__index__) && null !== (r = t.match(this.__opts__.fuzzyIP ? this.re.link_fuzzy : this.re.link_no_ip_fuzzy)) && (i = r.index + r[1].length, (this.__index__ < 0 || i < this.__index__) && (this.__schema__ = "", this.__index__ = i, this.__last_index__ = r.index + r[0].length))), this.__opts__.fuzzyEmail && this.__compiled__["mailto:"] && (a = t.indexOf("@"), a >= 0 && null !== (n = t.match(this.re.email_fuzzy)) && (i = n.index + n[1].length, o = n.index + n[0].length, (this.__index__ < 0 || i < this.__index__ || i === this.__index__ && o > this.__last_index__) && (this.__schema__ = "mailto:", this.__index__ = i, this.__last_index__ = o))), this.__index__ >= 0
}, Ce.prototype.pretest = function(t) {
    return this.re.pretest.test(t)
}, Ce.prototype.testSchemaAt = function(t, e, r) {
    return this.__compiled__[e.toLowerCase()] ? this.__compiled__[e.toLowerCase()].validate(t, r, this) : 0
}, Ce.prototype.match = function(t) {
    const e = [];
    let r = 0;
    this.__index__ >= 0 && this.__text_cache__ === t && (e.push(De(this, r)), r = this.__last_index__);
    let n = r ? t.slice(r) : t;
    for (; this.test(n);) e.push(De(this, r)), n = n.slice(this.__last_index__), r += this.__last_index__;
    return e.length ? e : null
}, Ce.prototype.matchAtStart = function(t) {
    if (this.__text_cache__ = t, this.__index__ = -1, !t.length) return null;
    const e = this.re.schema_at_start.exec(t);
    if (!e) return null;
    const r = this.testSchemaAt(t, e[2], e[0].length);
    return r ? (this.__schema__ = e[2], this.__index__ = e.index + e[1].length, this.__last_index__ = e.index + e[0].length + r, De(this, 0)) : null
}, Ce.prototype.tlds = function(t, e) {
    return t = Array.isArray(t) ? t : [t], e ? (this.__tlds__ = this.__tlds__.concat(t).sort().filter((function(t, e, r) {
        return t !== r[e - 1]
    })).reverse(), ge(this), this) : (this.__tlds__ = t.slice(), this.__tlds_replaced__ = !0, ge(this), this)
}, Ce.prototype.normalize = function(t) {
    t.schema || (t.url = "http://" + t.url), "mailto:" !== t.schema || /^mailto:/i.test(t.url) || (t.url = "mailto:" + t.url)
}, Ce.prototype.onCompile = function() {};
const ye = 2147483647,
    Ee = 36,
    be = /^xn--/,
    Ae = /[^\0-\x7F]/,
    Fe = /[\x2E\u3002\uFF0E\uFF61]/g,
    xe = {
        overflow: "Overflow: input needs wider integers to process",
        "not-basic": "Illegal input >= 0x80 (not a basic code point)",
        "invalid-input": "Invalid input"
    },
    we = Math.floor,
    ve = String.fromCharCode;

function Se(t) {
    throw new RangeError(xe[t])
}

function ze(t, e) {
    const r = t.split("@");
    let n = "";
    r.length > 1 && (n = r[0] + "@", t = r[1]);
    const s = function(t, e) {
        const r = [];
        let n = t.length;
        for (; n--;) r[n] = e(t[n]);
        return r
    }((t = t.replace(Fe, ".")).split("."), e).join(".");
    return n + s
}

function qe(t) {
    const e = [];
    let r = 0;
    const n = t.length;
    for (; r < n;) {
        const s = t.charCodeAt(r++);
        if (s >= 55296 && s <= 56319 && r < n) {
            const n = t.charCodeAt(r++);
            56320 == (64512 & n) ? e.push(((1023 & s) << 10) + (1023 & n) + 65536) : (e.push(s), r--)
        } else e.push(s)
    }
    return e
}
const Be = function(t, e) {
        return t + 22 + 75 * (t < 26) - ((0 != e) << 5)
    },
    Le = function(t, e, r) {
        let n = 0;
        for (t = r ? we(t / 700) : t >> 1, t += we(t / e); t > 455; n += Ee) t = we(t / 35);
        return we(n + 36 * t / (t + 38))
    },
    Me = function(t) {
        const e = [],
            r = t.length;
        let n = 0,
            s = 128,
            i = 72,
            o = t.lastIndexOf("-");
        o < 0 && (o = 0);
        for (let c = 0; c < o; ++c) t.charCodeAt(c) >= 128 && Se("not-basic"), e.push(t.charCodeAt(c));
        for (let c = o > 0 ? o + 1 : 0; c < r;) {
            const o = n;
            for (let e = 1, s = Ee;; s += Ee) {
                c >= r && Se("invalid-input");
                const o = (u = t.charCodeAt(c++)) >= 48 && u < 58 ? u - 48 + 26 : u >= 65 && u < 91 ? u - 65 : u >= 97 && u < 123 ? u - 97 : Ee;
                o >= Ee && Se("invalid-input"), o > we((ye - n) / e) && Se("overflow"), n += o * e;
                const a = s <= i ? 1 : s >= i + 26 ? 26 : s - i;
                if (o < a) break;
                const l = Ee - a;
                e > we(ye / l) && Se("overflow"), e *= l
            }
            const a = e.length + 1;
            i = Le(n - o, a, 0 == o), we(n / a) > ye - s && Se("overflow"), s += we(n / a), n %= a, e.splice(n++, 0, s)
        }
        var u;
        return String.fromCodePoint(...e)
    },
    Ie = function(t) {
        const e = [],
            r = (t = qe(t)).length;
        let n = 128,
            s = 0,
            i = 72;
        for (const c of t) c < 128 && e.push(ve(c));
        const o = e.length;
        let u = o;
        for (o && e.push("-"); u < r;) {
            let r = ye;
            for (const e of t) e >= n && e < r && (r = e);
            const c = u + 1;
            r - n > we((ye - s) / c) && Se("overflow"), s += (r - n) * c, n = r;
            for (const a of t)
                if (a < n && ++s > ye && Se("overflow"), a === n) {
                    let t = s;
                    for (let r = Ee;; r += Ee) {
                        const n = r <= i ? 1 : r >= i + 26 ? 26 : r - i;
                        if (t < n) break;
                        const s = t - n,
                            o = Ee - n;
                        e.push(ve(Be(n + s % o, 0))), t = we(s / o)
                    }
                    e.push(ve(Be(t, 0))), i = Le(s, c, u === o), s = 0, ++u
                }++s, ++n
        }
        return e.join("")
    },
    Te = function(t) {
        return ze(t, (function(t) {
            return Ae.test(t) ? "xn--" + Ie(t) : t
        }))
    },
    Re = function(t) {
        return ze(t, (function(t) {
            return be.test(t) ? Me(t.slice(4).toLowerCase()) : t
        }))
    },
    Ne = {
        default: {
            options: {
                html: !1,
                xhtmlOut: !1,
                breaks: !1,
                langPrefix: "language-",
                linkify: !1,
                typographer: !1,
                quotes: "“”‘’",
                highlight: null,
                maxNesting: 100
            },
            components: {
                core: {},
                block: {},
                inline: {}
            }
        },
        zero: {
            options: {
                html: !1,
                xhtmlOut: !1,
                breaks: !1,
                langPrefix: "language-",
                linkify: !1,
                typographer: !1,
                quotes: "“”‘’",
                highlight: null,
                maxNesting: 20
            },
            components: {
                core: {
                    rules: ["normalize", "block", "inline", "text_join"]
                },
                block: {
                    rules: ["paragraph"]
                },
                inline: {
                    rules: ["text"],
                    rules2: ["balance_pairs", "fragments_join"]
                }
            }
        },
        commonmark: {
            options: {
                html: !0,
                xhtmlOut: !0,
                breaks: !1,
                langPrefix: "language-",
                linkify: !1,
                typographer: !1,
                quotes: "“”‘’",
                highlight: null,
                maxNesting: 20
            },
            components: {
                core: {
                    rules: ["normalize", "block", "inline", "text_join"]
                },
                block: {
                    rules: ["blockquote", "code", "fence", "heading", "hr", "html_block", "lheading", "list", "reference", "paragraph"]
                },
                inline: {
                    rules: ["autolink", "backticks", "emphasis", "entity", "escape", "html_inline", "image", "link", "newline", "text"],
                    rules2: ["balance_pairs", "emphasis", "fragments_join"]
                }
            }
        }
    },
    Pe = /^(vbscript|javascript|file|data):/,
    Oe = /^data:image\/(gif|png|jpeg|webp);/;

function je(t) {
    const e = t.trim().toLowerCase();
    return !Pe.test(e) || Oe.test(e)
}
const Ze = ["http:", "https:", "mailto:"];

function $e(t) {
    const e = R(t, !0);
    if (e.hostname && (!e.protocol || Ze.indexOf(e.protocol) >= 0)) try {
        e.hostname = Te(e.hostname)
    } catch (r) {}
    return b(A(e))
}

function Ue(t) {
    const e = R(t, !0);
    if (e.hostname && (!e.protocol || Ze.indexOf(e.protocol) >= 0)) try {
        e.hostname = Re(e.hostname)
    } catch (r) {}
    return y(A(e), y.defaultChars + "%")
}

function He(t, e) {
    if (!(this instanceof He)) return new He(t, e);
    e || H(t) || (e = t || {}, t = "default"), this.inline = new ae, this.block = new Gt, this.core = new It, this.renderer = new mt, this.linkify = new Ce, this.validateLink = je, this.normalizeLink = $e, this.normalizeLinkText = Ue, this.utils = ft, this.helpers = G({}, dt), this.options = {}, this.configure(t), e && this.set(e)
}
He.prototype.set = function(t) {
    return G(this.options, t), this
}, He.prototype.configure = function(t) {
    const e = this;
    if (H(t)) {
        const e = t;
        if (!(t = Ne[e])) throw new Error('Wrong `markdown-it` preset "' + e + '", check name')
    }
    if (!t) throw new Error("Wrong `markdown-it` preset, can't be empty");
    return t.options && e.set(t.options), t.components && Object.keys(t.components).forEach((function(r) {
        t.components[r].rules && e[r].ruler.enableOnly(t.components[r].rules), t.components[r].rules2 && e[r].ruler2.enableOnly(t.components[r].rules2)
    })), this
}, He.prototype.enable = function(t, e) {
    let r = [];
    Array.isArray(t) || (t = [t]), ["core", "block", "inline"].forEach((function(e) {
        r = r.concat(this[e].ruler.enable(t, !0))
    }), this), r = r.concat(this.inline.ruler2.enable(t, !0));
    const n = t.filter((function(t) {
        return r.indexOf(t) < 0
    }));
    if (n.length && !e) throw new Error("MarkdownIt. Failed to enable unknown rule(s): " + n);
    return this
}, He.prototype.disable = function(t, e) {
    let r = [];
    Array.isArray(t) || (t = [t]), ["core", "block", "inline"].forEach((function(e) {
        r = r.concat(this[e].ruler.disable(t, !0))
    }), this), r = r.concat(this.inline.ruler2.disable(t, !0));
    const n = t.filter((function(t) {
        return r.indexOf(t) < 0
    }));
    if (n.length && !e) throw new Error("MarkdownIt. Failed to disable unknown rule(s): " + n);
    return this
}, He.prototype.use = function(t) {
    const e = [this].concat(Array.prototype.slice.call(arguments, 1));
    return t.apply(t, e), this
}, He.prototype.parse = function(t, e) {
    if ("string" != typeof t) throw new Error("Input data should be a String");
    const r = new this.core.State(t, this, e);
    return this.core.process(r), r.tokens
}, He.prototype.render = function(t, e) {
    return e = e || {}, this.renderer.render(this.parse(t, e), this.options, e)
}, He.prototype.parseInline = function(t, e) {
    const r = new this.core.State(t, this, e);
    return r.inlineMode = !0, this.core.process(r), r.tokens
}, He.prototype.renderInline = function(t, e) {
    return e = e || {}, this.renderer.render(this.parseInline(t, e), this.options, e)
};
export {
    h as D, _ as E, He as M, s as f, t as h, e as x
};