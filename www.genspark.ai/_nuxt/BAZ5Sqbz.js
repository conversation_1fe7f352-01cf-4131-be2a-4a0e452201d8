import {
    E as e
} from "./Cy7E5O2b.js";
import a from "./BSM9O9PP.js";
import {
    _ as t,
    r as l,
    C as o,
    s as n,
    v as r,
    c as s,
    h as c,
    x as i,
    d as u,
    b as v,
    f as p,
    t as d,
    n as g,
    F as h,
    k as f,
    e as m,
    o as y
} from "./Cf0SOiw0.js";
import "./e-ES_T8J.js";
const k = {
        class: "stock-chart"
    },
    D = {
        key: 0,
        class: "skeleton-block"
    },
    b = {
        key: 1
    },
    w = {
        class: "price-summary"
    },
    x = {
        class: "current-price"
    },
    M = {
        class: "company-symbol"
    },
    S = {
        class: "company-logo-container"
    },
    $ = {
        class: "logo-container"
    },
    F = ["src"],
    L = {
        class: "symbol-text"
    },
    T = {
        class: "last-updated"
    },
    A = {
        class: "price-container"
    },
    Y = {
        class: "price"
    },
    _ = {
        class: "unit"
    },
    j = {
        class: "price-change"
    },
    N = {
        class: "timeframe"
    },
    H = {
        class: "chart-section"
    },
    Z = {
        class: "chart-controls"
    },
    C = ["onClick"],
    O = {
        class: "stock-performance"
    },
    z = {
        class: "performance-grid"
    },
    P = {
        class: "label"
    },
    B = {
        class: "value"
    },
    E = {
        key: 0
    },
    R = t({
        __name: "StockPriceChart",
        props: {
            symbol: String,
            companyLogo: String,
            profile: Object,
            metrics: Object,
            ratios: Object,
            stockData: Object,
            showViewMore: Boolean
        },
        emits: ["finished_loading"],
        setup(t, {
            emit: R
        }) {
            const V = l(null);
            V.value = o.isDarkMode();
            const W = R,
                {
                    t: I
                } = n(),
                J = t,
                K = ["1D", "5D", "1M", "3M", "6M", "YTD", "5Y"],
                q = l("1D"),
                G = l([]),
                Q = l([]),
                U = l(!0),
                X = l(J.profile || null),
                ee = l(J.metrics || null),
                ae = l(J.ratios || null),
                te = l(J.companyLogo || null),
                le = l(J.stockData || null);
            r((() => J.profile), (e => {
                e && (X.value = e)
            })), r((() => J.metrics), (e => {
                e && (ee.value = e)
            })), r((() => J.ratios), (e => {
                e && (ae.value = e)
            })), r((() => J.companyLogo), (e => {
                e && (te.value = e)
            })), r((() => J.stockData), (e => {
                e && (le.value = stockData, ue(stockData))
            }));
            const oe = s((() => {
                    if (0 === ne.value.length) return 0;
                    const e = e => {
                        const a = (e => {
                            if (e.includes("T")) return e.split("T")[0];
                            if (e.includes(" ")) return e.split(" ")[0]; {
                                const a = new Date(e);
                                return `${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")}`
                            }
                        })(e);
                        for (let t = G.value.length - 1; t >= 0; t--)
                            if (G.value[t].date < a) return G.value[t].close;
                        return 0
                    };
                    switch (q.value) {
                        case "1D":
                            return e(ne.value[ne.value.length - 1].date);
                        case "5D":
                            return e(ne.value[0].date);
                        default:
                            return ne.value[0].close
                    }
                })),
                ne = s((() => {
                    if (0 === Q.value.length) return [];
                    const e = [...Q.value].sort(((e, a) => new Date(e.date) - new Date(a.date))),
                        a = new Date(e[e.length - 1].date);
                    let t = new Date(a);
                    const l = e => {
                        if (e.includes("T")) return e.split("T")[0];
                        if (e.includes(" ")) return e.split(" ")[0]; {
                            const a = new Date(e);
                            return `${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")}`
                        }
                    };

                    function n(e, a) {
                        const t = new Date(e),
                            l = e.getDate();
                        return t.setMonth(e.getMonth() - a), t.getDate() !== l && (o.log("ZZH moa chat month ago result", t), t.setDate(0), o.log("ZZH moa chat month ago result reset", t)), t
                    }
                    switch (q.value) {
                        case "1D":
                            const r = e[e.length - 1].date,
                                s = l(r);
                            return e.filter((e => l(e.date) === s));
                        case "5D":
                            const c = new Set;
                            for (let a = e.length - 1; a >= 0 && c.size < 5; a--) c.add(l(e[a].date));
                            return e.filter((e => c.has(l(e.date))));
                        case "1M":
                            t = n(a, 1), o.log("ZZH moa chat 1M startDate", t);
                            break;
                        case "3M":
                            t = n(a, 3);
                            break;
                        case "6M":
                            t = n(a, 6);
                            break;
                        case "YTD":
                            t = new Date(a.getFullYear(), 0, 1);
                            break;
                        case "5Y":
                            t.setFullYear(a.getFullYear() - 5)
                    }
                    return t.setHours(0, 0, 0, 0), G.value.filter((e => new Date(e.date) >= t))
                })),
                re = s((() => {
                    const e = se.value >= 0,
                        a = e ? "#00a651" : "#e31837";
                    return {
                        tooltip: {
                            trigger: "axis",
                            formatter: e => {
                                const a = e[0].data;
                                return `\n            <div style="padding: 5px; color: white; border-radius: 4px;">\n              <div>Date: ${a[0]}</div>\n              <div>Price: $${a[1].toFixed(2)}</div>\n            </div>\n          `
                            },
                            backgroundColor: "rgba(0,0,0,0.7)",
                            borderWidth: 0,
                            textStyle: {
                                color: "#fff"
                            },
                            axisPointer: {
                                show: !0,
                                type: "line",
                                lineStyle: {
                                    color: a,
                                    width: 2,
                                    type: "solid"
                                },
                                snap: !0
                            }
                        },
                        xAxis: {
                            type: "category",
                            data: ne.value.map((e => e.date)),
                            axisLabel: {
                                interval: Math.floor(ne.value.length / 6),
                                formatter: (e, a) => {
                                    const t = new Date(e);
                                    return ["1D", "3D", "5D"].includes(q.value) ? `${t.getHours()}:${t.getMinutes().toString().padStart(2,"0")}` : `${t.getMonth()+1}/${t.getDate()}`
                                },
                                fontSize: 14,
                                color: "#909499"
                            }
                        },
                        yAxis: {
                            type: "value",
                            scale: !0,
                            splitArea: {
                                show: !1
                            },
                            axisLabel: {
                                fontSize: 14,
                                color: "#909499"
                            },
                            splitLine: {
                                show: !0,
                                lineStyle: {
                                    type: "dashed",
                                    color: V.value ? "rgba(255,255,255,0.1)" : "rgba(0,0,0,0.1)"
                                }
                            }
                        },
                        series: [{
                            name: "Stock Price",
                            type: "line",
                            data: ne.value.map((e => [e.date, e.close])),
                            smooth: !0,
                            showSymbol: !1,
                            lineStyle: {
                                width: 2,
                                color: a
                            },
                            itemStyle: {
                                color: a
                            },
                            areaStyle: {
                                color: {
                                    type: "linear",
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [{
                                        offset: 0,
                                        color: e ? "rgba(0, 166, 81, 0.3)" : "rgba(227, 24, 55, 0.3)"
                                    }, {
                                        offset: 1,
                                        color: "rgba(255, 255, 255, 0.1)"
                                    }]
                                }
                            }
                        }],
                        grid: {
                            left: "0%",
                            right: "0%",
                            bottom: "0%",
                            top: "5%",
                            containLabel: !0
                        }
                    }
                })),
                se = l(0),
                ce = l(0),
                ie = s((() => {
                    switch (q.value) {
                        case "1D":
                            return I("pages.finance.company.stockpricechart.1-day");
                        case "5D":
                            return I("pages.finance.company.stockpricechart.5-days");
                        case "1M":
                            return I("pages.finance.company.stockpricechart.1-month");
                        case "3M":
                            return I("pages.finance.company.stockpricechart.3-months");
                        case "6M":
                            return I("pages.finance.company.stockpricechart.6-months");
                        case "YTD":
                            return I("pages.finance.company.stockpricechart.year-to-date");
                        case "5Y":
                            return I("pages.finance.company.stockpricechart.5-years");
                        default:
                            return q.value
                    }
                }));

            function ue(e) {
                e && (e.five_years && e.five_years.historical && (G.value = e.five_years.historical.sort(((e, a) => new Date(e.date) - new Date(a.date)))), e.daily && (Q.value = e.daily.sort(((e, a) => new Date(e.date) - new Date(a.date)))), U.value = !1, W("finished_loading", JSON.stringify(Q.value)))
            }
            async function ve() {
                if (le.value) return o.log("ZZH moa chat stockData defined in props", le.value), void ue(le.value);
                U.value = !0;
                try {
                    const e = await fetch(`/api/spark/finance/stock_price?symbol=${X.value.symbol}`),
                        a = await e.json();
                    le.value = a.data, ue(a.data)
                } catch (e) {} finally {
                    U.value = !1
                }
            }

            function pe() {
                var e;
                return Q.value.length > 0 ? Q.value[Q.value.length - 1].open : (null == (e = X.value) ? void 0 : e.price) || 0
            }

            function de() {
                var e;
                return Q.value.length > 0 ? Q.value[Q.value.length - 1].high : (null == (e = X.value) ? void 0 : e.price) || 0
            }

            function ge() {
                var e;
                return Q.value.length > 0 ? Q.value[Q.value.length - 1].low : (null == (e = X.value) ? void 0 : e.price) || 0
            }

            function he() {
                if (ne.value.length < 1) return;
                const e = X.value.price,
                    a = oe.value;
                if (null == e || null == a || 0 === a) return se.value = 0, void(ce.value = "0.00");
                o.log("ZZH moa chat calculatePriceChange", e, a), se.value = e - a, ce.value = (se.value / a * 100).toFixed(2)
            }
            const fe = s((() => ne.value.length ? function(e) {
                    const a = new Date(e);
                    return `${a.getMonth()+1}/${a.getDate()}/${a.getFullYear()}`
                }(ne.value[ne.value.length - 1].date) : "N/A")),
                me = l(null),
                ye = l({});

            function ke() {
                if (me.value) {
                    const e = me.value.clientWidth,
                        a = Math.max(300, .6 * e);
                    ye.value = { ...ye.value,
                        width: e,
                        height: a
                    }
                }
            }
            c((async () => {
                J.profile && J.metrics && J.ratios && J.companyLogo || await async function() {
                    const e = await fetch(`/api/spark/finance?symbol=${J.symbol}`),
                        a = (await e.json()).data,
                        t = a.outlook;
                    J.profile || (X.value = t.profile), J.metrics || (ee.value = t.metrics), J.ratios || (ae.value = t.ratios), J.companyLogo || (te.value = a.logo[64])
                }(), J.stockData ? ue(J.stockData) : await ve(), window.addEventListener("resize", ke), ke()
            })), i((() => {
                window.removeEventListener("resize", ke)
            })), r((() => J.symbol), ve), r(ne, he);
            const De = s((() => {
                var e, a, t, l, o;
                return X.value && ee.value && ae.value && 0 !== ae.value.length ? [{
                    label: I("pages.finance.company.stockpricechart.open"),
                    value: null !== pe() && void 0 !== pe() ? `${pe().toFixed(2)}` : "N/A"
                }, {
                    label: I("pages.finance.company.stockpricechart.high"),
                    value: null !== de() && void 0 !== de() ? `${de().toFixed(2)}` : "N/A"
                }, {
                    label: I("pages.finance.company.stockpricechart.low"),
                    value: null !== ge() && void 0 !== ge() ? `${ge().toFixed(2)}` : "N/A"
                }, {
                    label: I("pages.finance.company.stockpricechart.market-cap"),
                    value: (o = X.value.mktCap, o ? o >= 1e12 ? (o / 1e12).toFixed(2) + "T" : o >= 1e9 ? (o / 1e9).toFixed(2) + "B" : o >= 1e6 ? (o / 1e6).toFixed(2) + "M" : o.toLocaleString() : "N/A")
                }, {
                    label: I("pages.finance.company.stockpricechart.pe-ratio"),
                    value: null !== (null == (e = ae.value[0]) ? void 0 : e.peRatioTTM) && void 0 !== (null == (a = ae.value[0]) ? void 0 : a.peRatioTTM) ? ae.value[0].peRatioTTM.toFixed(2) : "N/A"
                }, {
                    label: I("pages.finance.company.stockpricechart.dividend-yield"),
                    value: (l = ee.value.dividendYielTTM, null == l ? "N/A" : (100 * l).toFixed(2) + "%")
                }, {
                    label: I("pages.finance.company.stockpricechart.52-week-high"),
                    value: null !== ee.value.yearHigh && void 0 !== ee.value.yearHigh ? `${ee.value.yearHigh.toFixed(2)}` : "N/A"
                }, {
                    label: I("pages.finance.company.stockpricechart.52-week-low"),
                    value: null !== ee.value.yearLow && void 0 !== ee.value.yearLow ? `${ee.value.yearLow.toFixed(2)}` : "N/A"
                }, {
                    label: I("pages.finance.company.stockpricechart.volume"),
                    value: (t = ee.value.volume, t ? t >= 1e9 ? (t / 1e9).toFixed(2) + "B" : t >= 1e6 ? (t / 1e6).toFixed(2) + "M" : t >= 1e3 ? (t / 1e3).toFixed(2) + "K" : t.toLocaleString() : "N/A")
                }] : []
            }));
            return (t, l) => (y(), u("div", k, [U.value ? (y(), u("div", D)) : (y(), u("div", b, [v("section", w, [v("div", x, [v("div", M, [v("div", S, [v("div", $, [v("img", {
                src: te.value,
                alt: "Company Logo",
                class: "company-logo"
            }, null, 8, F)]), v("span", L, d(X.value.symbol), 1)]), v("div", T, d(fe.value), 1)]), v("div", A, [v("span", Y, "$" + d(X.value.price), 1), v("span", _, d(X.value.currency), 1)])]), v("div", j, [v("span", {
                class: g({
                    positive: se.value > 0,
                    negative: se.value < 0
                })
            }, d(se.value > 0 ? "+" : "") + d(se.value.toFixed(2)) + " (" + d(ce.value) + "%) ", 3), v("span", N, d(ie.value), 1)])]), v("section", H, [v("div", Z, [(y(), u(h, null, f(K, (e => v("button", {
                key: e,
                onClick: a => function(e) {
                    q.value = e, he()
                }(e),
                class: g({
                    active: q.value === e
                })
            }, d(e), 11, C))), 64))]), v("div", {
                class: "chart-container",
                ref_key: "chartContainer",
                ref: me
            }, [m(e, {
                chartData: re.value,
                chartOptions: ye.value
            }, null, 8, ["chartData", "chartOptions"])], 512)]), v("section", O, [v("div", z, [(y(!0), u(h, null, f(De.value, ((e, a) => (y(), u("div", {
                class: "performance-item",
                key: a
            }, [v("span", P, d(e.label), 1), v("span", B, d(e.value), 1)])))), 128))])]), J.showViewMore ? (y(), u("section", E, [m(a, {
                symbol: X.value.symbol
            }, null, 8, ["symbol"])])) : p("", !0)]))]))
        }
    }, [
        ["__scopeId", "data-v-bde6a6e6"]
    ]);
export {
    R as
    default
};