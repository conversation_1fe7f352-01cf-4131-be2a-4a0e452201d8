.theme[data-v-3ab150f0] {
    --white: #fff;
    --black: #000;
    --gray-50: #f9f9f9;
    --gray-100: #ececec;
    --gray-200: #e3e3e3;
    --gray-300: #cdcdcd;
    --gray-400: #909499;
    --gray-500: #9b9b9b;
    --gray-600: #676767;
    --gray-700: #424242;
    --gray-750: #2f2f2f;
    --gray-800: #212121;
    --gray-900: #171717;
    --gray-950: #0d0d0d;
    --red-500: #ef4444;
    --red-700: #b91c1c;
    --main-surface-background: hsla(0, 0%, 100%, .95);
    --composer-surface: hsla(0, 0%, 94%, .9);
    --composer-bar_mask-grace-area: 25px;
    --dot-color: var(--black);
    --main-text: var(--gray-950);
    --main-border: #eaeaea;
    --text-primary: var(--gray-950);
    --text-secondary: #7d7d7d;
    --text-tertiary: var(--gray-400);
    --text-quaternary: var(--gray-300);
    --tag-blue: #08f;
    --tag-blue-light: #0af;
    --text-error: #f93a37;
    --text-placeholder: rgba(0, 0, 0, .7);
    --surface-error: 249 58 55;
    --border-xlight: rgb(0 0 0/5%);
    --border-medium: rgba(0, 0, 0, .15);
    --border-heavy: rgba(0, 0, 0, .2);
    --border-xheavy: rgba(0, 0, 0, .25);
    --hint-text: #08f;
    --hint-bg: #b3dbff;
    --border-light: rgba(0, 0, 0, .1);
    --border-sharp: rgb(0 0 0/5%);
    --icon-secondary: #676767;
    --main-surface-primary: var(--white);
    --main-surface-secondary: var(--gray-50);
    --main-surface-tertiary: var(--gray-100);
    --sidebar-surface-primary: var(--gray-50);
    --sidebar-surface-secondary: var(--gray-100);
    --sidebar-surface-tertiary: var(--gray-200);
    --link: #2964aa;
    --link-hover: #749ac8;
    --selection: #007aff;
    --button-blue: #0f7fff;
    --button-blue-hover: #698cf7;
    --button-disabled: #efefef;
    --button-text: #909499
}

.subscription[data-v-3ab150f0] {
    font-family: ui-sans-serif, -apple-system, system-ui, Segoe UI, Helvetica, Apple Color Emoji, Arial, sans-serif, Segoe UI Emoji, Segoe UI Symbol;
    font-variation-settings: normal
}

.subscription p[data-v-3ab150f0] {
    margin: 0
}

.header[data-v-3ab150f0] {
    justify-content: space-between;
    min-width: 120px;
    width: 100%;
    fill: #ffffffa6;
    -webkit-backdrop-filter: blur(25px);
    backdrop-filter: blur(25px);
    border-bottom: 1px solid transparent;
    box-sizing: border-box;
    height: 60px;
    z-index: 22
}

.header[data-v-3ab150f0],
.logo[data-v-3ab150f0] {
    align-items: center;
    display: flex
}

.logo[data-v-3ab150f0] {
    color: #6e6e6e;
    flex-direction: row;
    height: 24px;
    justify-content: flex-start;
    line-height: 54px;
    margin-left: 24px
}

.logo[data-v-3ab150f0]:hover {
    cursor: pointer
}

.logo img[data-v-3ab150f0] {
    flex-shrink: 0;
    height: 100%
}

.user-info[data-v-3ab150f0] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 24px;
    margin-right: 24px
}

.plan_table[data-v-3ab150f0] {
    background-color: var(--main-surface-primary);
    flex-direction: column;
    grid-column: auto;
    grid-column-start: 2;
    grid-row: auto;
    grid-row-start: 2;
    height: 100%;
    inset-inline-start: 0;
    outline: 2px solid transparent;
    outline-offset: 2px;
    text-align: start;
    width: 100%
}

.plan_table[data-v-3ab150f0],
.plan_table .plan_title[data-v-3ab150f0] {
    color: var(--text-primary);
    display: grid
}

.plan_table .plan_title[data-v-3ab150f0] {
    grid-template-columns: 1fr auto 1fr;
    padding-bottom: 2.5rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    position: relative
}

.plan_table .plan_title span[data-v-3ab150f0] {
    font-size: 1.875rem;
    font-weight: 600;
    line-height: 2.25rem;
    margin-bottom: 0;
    margin-top: 0
}

.plan_table .x_button[data-v-3ab150f0] {
    color: var(--text-primary);
    justify-self: end;
    opacity: .5;
    position: absolute;
    right: 1.5rem;
    top: 1.5rem;
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.icon-large[data-v-3ab150f0] {
    flex-shrink: 0;
    height: 1.5rem;
    stroke-width: 1.5;
    width: 1.5rem
}

.plan_tab[data-v-3ab150f0],
.switch_to_memberships[data-v-3ab150f0] {
    display: flex;
    flex-direction: row;
    gap: 5rem;
    justify-content: center;
    margin-bottom: 1.2rem;
    text-align: center
}

.plan_tab_container[data-v-3ab150f0] {
    border-color: var(--border-light);
    border-style: solid;
    border-width: 1px;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.plan_tab_item[data-v-3ab150f0] {
    font-size: .875rem;
    font-weight: 600;
    line-height: 1.25rem;
    margin: 2px;
    padding: .375rem .75rem;
    position: relative;
    z-index: 10
}

.plan_tab button[data-v-3ab150f0] {
    background-color: transparent;
    border: none;
    cursor: pointer
}

.relative[data-v-3ab150f0] {
    position: relative
}

.absolute[data-v-3ab150f0] {
    position: absolute
}

.grid[data-v-3ab150f0] {
    display: grid
}

.grid-cols-2[data-v-3ab150f0] {
    grid-template-columns: repeat(2, minmax(0, 1fr))
}

.rounded-full[data-v-3ab150f0] {
    border-radius: 9999px
}

.hover\:cursor-pointer[data-v-3ab150f0]:hover {
    cursor: pointer
}

.-z-10[data-v-3ab150f0] {
    z-index: -10
}

.inset-0[data-v-3ab150f0] {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

.bg-token-main-surface-primary[data-v-3ab150f0] {
    background-color: var(--main-surface-primary)
}

.bg-token-main-surface-tertiary[data-v-3ab150f0] {
    background-color: var(--main-surface-tertiary)
}

.text-token-text-primary[data-v-3ab150f0] {
    color: var(--text-primary)
}

.text-token-text-tertiary[data-v-3ab150f0] {
    color: var(--text-tertiary)
}

.plan_item_group[data-v-3ab150f0] {
    display: flex;
    flex-direction: row;
    gap: 5rem;
    justify-content: center;
    min-height: 12rem;
    padding: .75rem .75rem 1.75rem
}

.plan_item_group .plan_column[data-v-3ab150f0] {
    border: 0 solid #e3e3e3;
    border-color: var(--border-light);
    border-radius: .75rem;
    border-width: 1px;
    display: flex;
    flex: 1 1 0%;
    flex-direction: column;
    font-size: .875rem;
    gap: 1.25rem;
    justify-content: flex-start;
    line-height: 1.25rem;
    max-width: 20rem;
    min-height: 8rem;
    padding: 1.5rem 1.5rem .5rem
}

.plan_item_group .plan_column .plan_column_upper[data-v-3ab150f0] {
    display: flex;
    flex-direction: column;
    gap: .25rem
}

.row1[data-v-3ab150f0] {
    align-items: center;
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 2rem
}

.row1[data-v-3ab150f0],
.row2[data-v-3ab150f0] {
    margin-left: .1rem
}

.row2[data-v-3ab150f0] {
    align-items: baseline;
    margin-top: .5rem;
    position: relative
}

.row2[data-v-3ab150f0],
.row2 .plan_price[data-v-3ab150f0] {
    display: flex;
    gap: .375rem
}

.row2 .plan_price[data-v-3ab150f0] {
    align-items: flex-start;
    align-items: baseline;
    color: var(--text-tertiary);
    flex-wrap: wrap;
    font-size: .8rem;
    justify-content: left;
    line-height: 1rem
}

.row2 .plan_price .plan_price_amount[data-v-3ab150f0] {
    color: var(--text-primary);
    font-size: 1.5rem;
    line-height: 1
}

.row2 .plan_price .plan_price_amount_strikethrough[data-v-3ab150f0] {
    color: var(--text-tertiary);
    font-size: 1.5rem;
    line-height: 1;
    text-decoration: line-through
}

.row2 .plan_price .plan_price_unit[data-v-3ab150f0] {
    align-items: flex-start;
    color: var(--text-tertiary);
    display: flex;
    flex-direction: column;
    justify-content: center;
    line-height: 1rem
}

.row2_plan_sidenote[data-v-3ab150f0] {
    display: flex;
    margin-top: .5rem
}

.row3[data-v-3ab150f0] {
    color: var(--text-primary);
    font-size: 1rem;
    line-height: 1.5rem;
    margin-right: -.5rem;
    margin-top: .5rem
}

.row4[data-v-3ab150f0],
.row4_button[data-v-3ab150f0] {
    justify-content: center
}

.row4_button[data-v-3ab150f0] {
    align-items: center;
    background-color: var(--button-blue);
    border: 1px transparent;
    border-radius: 120px;
    color: var(--white);
    display: inline-flex;
    flex-shrink: 0;
    font-size: .875rem;
    font-weight: 600;
    line-height: 1.25rem;
    min-height: 46px;
    padding: .5rem .875rem;
    position: relative;
    width: 100%
}

.row4_button[data-v-3ab150f0]:hover {
    background-color: var(--button-blue-hover);
    cursor: pointer
}

.row4_button[data-v-3ab150f0]:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.row4_button[data-v-3ab150f0]:focus-visible {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.row4_button[data-v-3ab150f0]:disabled {
    background-color: var(--button-disabled);
    color: var(--button-text);
    cursor: not-allowed;
    opacity: .9
}

.row4_button[data-v-3ab150f0]:active {
    opacity: .8
}

.row5[data-v-3ab150f0] {
    display: flex;
    flex-direction: column;
    flex-grow: 0;
    gap: .5rem
}

.row5 .row5_line[data-v-3ab150f0] {
    background-color: inherit;
    position: relative
}

.row5 .row5_line .row5_line_bullet[data-v-3ab150f0] {
    display: flex;
    gap: .5rem;
    margin-right: .5rem
}

.row5 .row5_line .row5_line_bullet svg[data-v-3ab150f0] {
    aspect-ratio: 1;
    flex-shrink: 0;
    height: 1rem;
    margin-top: .125rem;
    -o-object-fit: contain;
    object-fit: contain;
    width: 1rem
}

.row5 .row5_line .row5_line_bullet_sub[data-v-3ab150f0] {
    display: flex;
    gap: .5rem
}

.row5 .row5_line .row5_line_bullet_sub svg[data-v-3ab150f0] {
    aspect-ratio: 1;
    flex-shrink: 0;
    height: 1rem;
    margin-top: .125rem;
    -o-object-fit: contain;
    object-fit: contain;
    width: 1rem
}

.row5 .row5_line .row5_line_bullet_sub span[data-v-3ab150f0] {
    color: var(--text-tertiary)
}

.row6[data-v-3ab150f0] {
    color: var(--text-secondary);
    display: flex;
    flex-direction: column;
    flex-grow: 0;
    gap: .5rem
}

.row6 .row6_line[data-v-3ab150f0] {
    background-color: var(--main-surface-primary);
    position: relative
}

.row6 a[data-v-3ab150f0] {
    color: var(--text-secondary)
}

.plan_switch[data-v-3ab150f0] {
    align-items: center;
    border: 1px solid #e3e3e3;
    border-color: var(--border-light);
    border-radius: .75rem;
    display: flex;
    flex: 1 1 0%;
    flex-direction: row;
    font-size: .875rem;
    justify-content: space-between;
    line-height: 1.25rem;
    margin: 20px auto;
    max-width: 46rem;
    min-width: 200px;
    padding: 20px 40px;
    width: calc(100% - 104px)
}

.plan_switch_title[data-v-3ab150f0] {
    font-family: Arial;
    font-size: 20px;
    font-style: normal;
    font-weight: 700
}

.plan_switch_button[data-v-3ab150f0] {
    align-items: center;
    background-color: var(--button-blue);
    border: 1px transparent;
    border-radius: 120px;
    color: var(--white);
    display: inline-flex;
    flex-shrink: 0;
    font-size: .875rem;
    font-weight: 600;
    justify-content: center;
    line-height: 1.25rem;
    min-height: 46px;
    padding: .5rem .875rem
}

.plan_switch_button[data-v-3ab150f0]:hover {
    background-color: var(--button-blue-hover);
    cursor: pointer
}

.plan_switch_button[data-v-3ab150f0]:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.plan_switch_button[data-v-3ab150f0]:focus-visible {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.plan_switch_button[data-v-3ab150f0]:disabled {
    background-color: var(--button-disabled);
    color: var(--button-text);
    cursor: not-allowed;
    opacity: .9
}

.plan_footer[data-v-3ab150f0] {
    align-content: center;
    align-items: center;
    border: 0 transparent;
    color: var(--text-secondary);
    flex: 1 1 0%;
    flex-direction: column;
    font-size: 15px;
    gap: 4px;
    margin: 8px auto;
    max-width: 600px;
    padding: 4px 22px
}

.plan_footer[data-v-3ab150f0],
.plan_footer .row[data-v-3ab150f0] {
    display: flex;
    justify-content: center
}

.plan_footer .row a[data-v-3ab150f0] {
    color: var(--text-primary);
    text-decoration: underline
}

.plan_status_reminder[data-v-3ab150f0] {
    color: var(--text-error);
    font-size: 12px;
    font-style: italic
}

.payment_result[data-v-3ab150f0] {
    background: #fff;
    border-radius: 16px;
    box-sizing: border-box;
    color: #232425;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    max-width: calc(100% - 40px);
    padding: 30px 20px 20px;
    text-align: center;
    width: 540px
}

.payment_result .content[data-v-3ab150f0] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%
}

.payment_result .bold[data-v-3ab150f0] {
    font-weight: 700
}

.payment_result .buttons[data-v-3ab150f0] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 36px;
    width: 100%
}

.payment_result .button[data-v-3ab150f0] {
    background: #f5f5f5;
    border-radius: 12px;
    box-sizing: border-box;
    cursor: pointer;
    padding: 10px 20px;
    width: 100%
}

.payment_result .button.default[data-v-3ab150f0] {
    background: #000;
    color: #fff
}

.payment_result_error_message[data-v-3ab150f0] {
    color: #ff3d3d;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: -20px;
    padding: 6px 21px 4px
}

.payment_result-inner .error textarea[data-v-3ab150f0] {
    border: 1px solid #ff3d3d
}

.debug_info[data-v-3ab150f0] {
    background-color: var(--main-surface-tertiary);
    border-radius: 10px;
    color: var(--text-primary);
    font-family: ui-sans-serif, -apple-system, system-ui, Segoe UI, Helvetica, Apple Color Emoji, Arial, sans-serif, Segoe UI Emoji, Segoe UI Symbol;
    font-size: 14px;
    font-variation-settings: normal;
    line-height: 2rem;
    margin-left: 20px;
    margin-right: 20px;
    margin-top: 30px;
    padding: 10px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    overflow-x: auto;
    white-space: pre-wrap
}

@media (max-width:768px) {
    .plan_item_group[data-v-3ab150f0] {
        display: grid;
        flex-direction: row;
        gap: 5rem;
        justify-content: center;
        min-height: 30rem;
        padding: .75rem
    }
    .plan_switch[data-v-3ab150f0] {
        max-width: 21rem;
        padding-left: 20px;
        padding-right: 20px;
        width: calc(100% - 66px)
    }
}

@media (prefers-color-scheme:dark) {
    .theme[data-v-3ab150f0] {
        --main-surface-background: rgba(33, 33, 33, .9);
        --composer-surface: rgba(50, 50, 50, .8);
        --composer-bar_mask-grace-area: 55px;
        --dot-color: var(--white);
        --main-text: var(--gray-100);
        --text-primary: var(--gray-100);
        --text-secondary: var(--gray-400);
        --text-tertiary: var(--gray-500);
        --text-quaternary: var(--gray-600);
        --text-placeholder: hsla(0, 0%, 100%, .8);
        --text-error: #f93a37;
        --border-xlight: hsla(0, 0%, 100%, .05);
        --border-light: hsla(0, 0%, 100%, .1);
        --border-medium: hsla(0, 0%, 100%, .15);
        --border-heavy: hsla(0, 0%, 100%, .2);
        --border-xheavy: hsla(0, 0%, 100%, .25);
        --border-sharp: hsla(0, 0%, 100%, .05);
        --main-surface-primary: var(--gray-800);
        --main-surface-secondary: var(--gray-750);
        --main-surface-tertiary: var(--gray-700);
        --sidebar-surface-primary: var(--gray-900);
        --sidebar-surface-secondary: var(--gray-800);
        --sidebar-surface-tertiary: var(--gray-750);
        --link: #7ab7ff;
        --link-hover: #5e83b3;
        --surface-error: 249 58 55;
        --button-disabled: #5e5e5e;
        --button-text: #909499
    }
    .logo[data-v-3ab150f0] {
        filter: invert(1)
    }
}