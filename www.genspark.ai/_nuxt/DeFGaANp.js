import {
    _ as e
} from "./D6bQc9d9.js";
import {
    W as t,
    ah as s,
    C as a,
    s as o,
    _ as r,
    r as n,
    d as i,
    o as l,
    b as c,
    a as d,
    ai as u,
    e as p,
    y as h,
    t as j,
    f as m,
    l as g,
    w as f,
    n as v,
    F as _,
    k as y,
    A as x,
    i as k,
    v as P,
    h as w,
    a3 as b,
    c as E,
    x as S,
    a4 as C,
    aj as T,
    H as M,
    a9 as R,
    L,
    U as D
} from "./Cf0SOiw0.js";
import {
    f as A
} from "./Bl-gMEVt.js";
import {
    u as I
} from "./DJ-JsGJu.js";
import {
    M as N
} from "./DZ51nUum.js";
import {
    _ as F
} from "./C1lFdfgL.js";
import {
    C as O,
    G
} from "./DW6cX6jm.js";
import {
    M as H
} from "./BMP7zQoC.js";
import {
    u as $
} from "./B6noBY_5.js";
import {
    N as q
} from "./CW991W2w.js";
import {
    S as U
} from "./DxgY8w7w.js";
import {
    C as Y
} from "./CRmNre8Y.js";
import {
    d as V
} from "./DOnko34f.js";
const W = t("agentProjects", {
        state: () => ({
            projects: [],
            project: null,
            loadingMyProjects: !1,
            loadingProjectId: null,
            runningProject: !1,
            revokingProject: !1,
            myProjectsHasMore: !0,
            myProjectsLoadMoreVisible: !0,
            quotaExceeded: !1,
            createProjectTimes: 0,
            createProjectDate: "",
            maxDayTimes: 5,
            runningController: null,
            loadingController: null,
            loadingMyProjectsController: null,
            hasUpdateProjectsStatusLoop: !1,
            maxRetryRun: 4,
            retryRun: 0
        }),
        getters: {
            getProjectById: e => t => e.projects.find((e => e.id === t)),
            runningProjects: e => e.projects.filter((e => ["STARTED", "PENDING", "PROGRESS"].includes(e.status))),
            hasRunningProjects: e => e.projects.some((e => ["STARTED", "PENDING", "PROGRESS"].includes(e.status))),
            currentProjectName: e => {
                var t, s, a;
                return (null == (s = null == (t = e.project) ? void 0 : t.session_state) ? void 0 : s.name) ? e.project.session_state.name : (null == (a = e.project) ? void 0 : a.name) ? e.project.name : ""
            }
        },
        actions: {
            async loadMyProjects(e = !1) {
                if (!this.loadingMyProjects) {
                    this.loadingMyProjectsController && this.loadingMyProjectsController.abort(), await new Promise((e => setTimeout(e, 0))), this.loadingMyProjectsController = new AbortController;
                    try {
                        this.loadingMyProjects = !0;
                        let t = "/api/project/my?from=agents";
                        if (e) {
                            if (!this.myProjectsHasMore) return void a.log("myProjectsHasMore is false, skip loadMyProjects");
                            t += "&offset=" + this.projects.length
                        }
                        const s = await fetch(t, {
                                signal: this.loadingMyProjectsController.signal
                            }),
                            o = await s.json();
                        if (0 !== o.status || !o.data || !o.data.projects) return void(-5 === o.status && (location.href = "/login"));
                        if (o.status && 0 == o.data.projects.length) return void(this.myProjectsHasMore = !1);
                        e ? this.projects.push(...o.data.projects) : this.projects = o.data.projects, 20 == o.data.projects.length && this.projects.length < 100 ? this.myProjectsHasMore = !0 : o.data.projects.length < 20 && (this.myProjectsHasMore = !1), this.createProjectDate = o.data.create_project_date, this.createProjectTimes = o.data.create_project_times, this.maxDayTimes = o.data.MAX_DAY_TIMES, this.quotaExceeded = o.data.quota_exceeded
                    } finally {
                        this.loadingMyProjects = !1
                    }
                }
            },
            async updateProjectsStatus() {
                if (!this.projects || 0 == this.projects.length) return;
                const e = this.projects.filter((e => ["STARTED", "PENDING", "PROGRESS"].includes(e.status))).map((e => e.id)).join(","),
                    t = await fetch("/api/project/status?from=agents" + (e ? `&ids=${encodeURIComponent(e)}` : "")),
                    s = await t.json();
                0 === s.status && s.data && s.data.projects && (s.data.projects.forEach((e => {
                    this.projects.forEach((t => {
                        t.id == e.id && (t.status = e.status, t.finished_at = e.finished_at)
                    }))
                })), this.createProjectTimes = s.data.create_project_times, this.createProjectDate = s.data.create_project_date)
            },
            async updateProjectsStatusLoop() {
                if (!this.hasUpdateProjectsStatusLoop)
                    for (this.hasUpdateProjectsStatusLoop = !0;;) {
                        a.log("updateProjectsStatusLoop start loop"), await new Promise((e => setTimeout(e, 1e4)));
                        let t = !1;
                        for (let e of this.projects)["STARTED", "PENDING", "PROGRESS"].includes(e.status) && (t = !0);
                        if (!t) {
                            this.hasUpdateProjectsStatusLoop = !1, a.log("updateProjectsStatusLoop stop loop");
                            break
                        }
                        try {
                            await this.updateProjectsStatus()
                        } catch (e) {}
                    }
            },
            async loadProject(e, t = {}) {
                const {
                    no_run: s = !1,
                    silent: a = !1
                } = t;
                this.loadingController && (this.loadingController.abort(), this.loadingController = null), this.runningProject && this.leaveRunning(), this.loadingController = new AbortController;
                try {
                    a || (this.loadingProjectId = e, window.location.href = `/agents?id=${e}`);
                    const t = await fetch(`/api/project?id=${e}`, {
                            signal: this.loadingController.signal
                        }),
                        s = await t.json();
                    if (0 !== s.status || !s.data) return;
                    this.project = s.data
                } catch (o) {} finally {
                    this.loadingProjectId == e && (this.loadingProjectId = null, this.loadingController = null)
                }!s && this.project && ["STARTED", "PROGRESS", "PENDING"].includes(this.project.status) && this.runProject(), ["STARTED", "PROGRESS", "PENDING"].includes(this.project.status), this.projects.length > 0 && this.projects.forEach((e => {
                    e.id == this.project.id && (e.status = this.project.status, e.name = this.project.name, e.finished_at = this.project.finished_at)
                }))
            },
            async runProject(e = {
                force: !1
            }, t = null) {
                if (!(null == t ? void 0 : t.no_run_project) && !this.runningProject) try {
                    if (this.runningController && (this.runningController.abort(), await new Promise((e => setTimeout(e, 0)))), this.runningController = new AbortController, this.runningProject = !0, !this.project || !this.project.id) return;
                    if (this.projects && this.projects.forEach((e => {
                            e.id == this.project.id && (e.status = "PROGRESS")
                        })), this.retryRun++, this.retryRun > this.maxRetryRun) return;
                    await A("/api/project/run", {
                        project_id: this.project.id,
                        ...e
                    }, (e => {
                        e.project_id == this.project.id && (this.project && !this.project.session_state && (this.project.session_state = {}), "SESSION_STATE_FIELD" === e.type && ("" == e.field_name ? s(this.project.session_state, (() => e.field_value)) : I(this.project.session_state, e.field_name, (() => e.field_value))), "SESSION_STATE_FIELD_DELTA" === e.type && I(this.project.session_state, e.field_name, (t => (t || "") + e.delta)), "SESSION_STATE_FIELD_APPEND_ITEM" === e.type && I(this.project.session_state, e.field_name, (t => [...t || [], e.field_value])))
                    }), this.runningController.signal), this.loadProject(this.project.id, {
                        silent: !0
                    })
                } finally {
                    this.runningProject = !1
                }
            },
            async revokeProject(e = null) {
                try {
                    if (this.revokingProject = !0, !e) {
                        if (!this.project || !this.project.id) return;
                        e = this.project.id
                    }
                    const t = await fetch(`/api/project/revoke?project_id=${e}`),
                        s = await t.json();
                    if (0 !== s.status || !s.data) return;
                    this.retryRun = 0, this.projects && this.project && this.projects.forEach((e => {
                        e.id == this.project.id && (e.status = "FAILED")
                    }))
                } finally {
                    this.revokingProject = !1
                }
            },
            async deleteProject(e) {
                this.projects = this.projects.filter((t => t !== e)), this.project && this.project.id == e.id && (this.project = null, this.stopRunning());
                const t = await fetch(`/api/project/delete?project_id=${e.id}`);
                (await t.json()).status
            },
            addOrUpdateProject(e) {
                if (!e || !e.id) return;
                const t = this.projects.findIndex((t => t.id === e.id)); - 1 !== t ? this.projects[t] = { ...this.projects[t],
                    ...e
                } : this.projects.unshift(e)
            },
            stopRunning() {
                this.runningController && this.runningController.abort(), this.runningProject = !1, this.revokeProject()
            },
            leaveRunning() {
                this.runningController && this.runningController.abort(), this.runningProject = !1
            },
            generateEmptyProject(e) {
                const t = {
                    type: e.type || "moa_chat",
                    name: e.name || "AI Chat",
                    session_state: {
                        steps: [],
                        messages: []
                    }
                };
                return t.client_generated_id = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function(e) {
                    const t = 16 * Math.random() | 0;
                    return ("x" == e ? t : 3 & t | 8).toString(16)
                })), t
            },
            setProject(e) {
                this.project = e
            },
            clearProject() {
                this.project = null
            },
            resetRetryRun() {
                this.retryRun = 0
            },
            setMyProjectsLoadMoreVisible(e) {
                this.myProjectsLoadMoreVisible = e
            }
        }
    }),
    B = () => {
        const {
            t: e,
            locale: t
        } = o();
        return {
            formatTime: s => {
                if (!s) return "";
                const a = new Date(s + "Z"),
                    o = new Date,
                    r = new Date;
                r.setDate(o.getDate() - 1);
                const n = new Date(a.getFullYear(), a.getMonth(), a.getDate()),
                    i = new Date(o.getFullYear(), o.getMonth(), o.getDate()),
                    l = new Date(r.getFullYear(), r.getMonth(), r.getDate());
                if (n.getTime() === i.getTime()) return e("components.aidrive.file_item.today");
                if (n.getTime() === l.getTime()) return e("components.aidrive.file_item.yesterday"); {
                    const e = t.value || "en-US";
                    return a.toLocaleDateString(e, {
                        weekday: "short",
                        year: "numeric",
                        month: "short",
                        day: "numeric"
                    })
                }
            }
        }
    },
    z = {
        class: "name"
    },
    K = {
        key: 0,
        class: "icon"
    },
    X = {
        key: 1,
        class: "icon"
    },
    Z = {
        class: "text"
    },
    J = {
        class: "project-info"
    },
    Q = {
        key: 0,
        class: "time-display"
    },
    ee = {
        class: "project-controls"
    },
    te = {
        class: "icon more"
    },
    se = {
        class: "menu"
    },
    ae = r({
        __name: "AgentTaskListItem",
        props: {
            project: {
                type: Object,
                required: !0
            },
            isActive: {
                type: Boolean,
                default: !1
            },
            taskConfig: {
                type: Object,
                default: null
            }
        },
        emits: ["click", "editProject", "deleteProject"],
        setup(e, {
            emit: t
        }) {
            const s = e,
                r = t,
                {
                    t: _,
                    locale: y
                } = o(),
                x = $(),
                {
                    formatTime: k
                } = B(),
                P = n(!1),
                w = () => {
                    r("click", s.project)
                },
                b = e => {
                    P.value = e
                },
                E = async () => {
                    P.value = !1;
                    const e = x.loading("Adding bookmark...");
                    try {
                        const t = await a.postRequest("/api/user/project_bookmark", {
                            project_id: s.project.id,
                            name: s.project.name,
                            is_deleted: !1,
                            source: "agent_task_list"
                        });
                        e.destroy(), 0 === t.status ? x.success("Bookmark added successfully") : x.error("Failed to add bookmark")
                    } catch (t) {
                        e.destroy(), x.error("Failed to add bookmark")
                    }
                };
            return (t, s) => {
                var a;
                return l(), i("div", {
                    class: v(["project", {
                        active: e.isActive,
                        "popover-visible": P.value
                    }]),
                    onClick: w
                }, [c("div", z, [(null == (a = e.taskConfig) ? void 0 : a.icon) ? (l(), i("div", K, [(l(), d(u(e.taskConfig.icon)))])) : (l(), i("div", X, [p(h(O))])), c("div", Z, j(e.project.name), 1)]), c("div", J, [e.project.ctime ? (l(), i("div", Q, j(h(k)(e.project.ctime)), 1)) : m("", !0), c("div", ee, [c("div", {
                    class: "menu-wrapper",
                    onClick: s[2] || (s[2] = g((() => {}), ["stop"]))
                }, [p(h(q), {
                    style: {
                        padding: "0",
                        "border-radius": "8px"
                    },
                    placement: "bottom-end",
                    trigger: "click",
                    show: P.value,
                    "onUpdate:show": b
                }, {
                    trigger: f((() => [c("div", te, [p(h(H))])])),
                    default: f((() => [c("div", se, [c("div", {
                        class: "button bookmark",
                        onClick: g(E, ["stop"])
                    }, j(t.$t("pages.agents.add_bookmark")), 1), c("div", {
                        class: "button edit",
                        onClick: s[0] || (s[0] = g((s => t.$emit("editProject", e.project)), ["stop"]))
                    }, j(t.$t("pages.autopilotagent.edit_name")), 1), c("div", {
                        class: "button delete",
                        onClick: s[1] || (s[1] = g((s => t.$emit("deleteProject", e.project)), ["stop"]))
                    }, j(t.$t("pages.article_verification.delete")), 1)])])),
                    _: 1
                }, 8, ["show"])])])])], 2)
            }
        }
    }, [
        ["__scopeId", "data-v-c88f9155"]
    ]),
    oe = {
        class: "flex flex-col gap-[12px] w-full px-[16px] overflow-y-auto pt-[16px]"
    },
    re = ["onClick"],
    ne = {
        class: "mt-[2px] w-[90%]"
    },
    ie = {
        class: "flex items-center gap-[6px]"
    },
    le = {
        class: "justify-start text-[#232425] dark:text-[#e0e0e0] text-[14px] font-bold font-['Arial'] leading-[21px] w-full truncate"
    },
    ce = {
        class: "mt-[4px] text-[#606366] dark:text-[#888] text-[14px] font-normal font-['Arial'] leading-[1.5]"
    },
    de = ["innerHTML"],
    ue = {
        key: 1,
        class: "description-text"
    },
    pe = {
        key: 2,
        class: "time-display"
    },
    he = r({
        __name: "search_task_list",
        props: {
            searchResults: {
                type: Array,
                required: !0
            },
            findTaskConfig: {
                type: Function,
                required: !0
            }
        },
        emits: ["handleProjectClick"],
        setup(e, {
            emit: t
        }) {
            const {
                t: s,
                locale: a
            } = o(), {
                formatTime: r
            } = B(), n = t, p = e => {
                if (!e) return !0;
                return e.length < 30 || ["super_agent", "slide_agent", "slides_agent", "sheets_agent", "ai_inbox", "offline_task", "docs_agent", "podcasts_agent", "agentic_deep_research", "call_for_me", "agentic_cross_check", "phone_call", "ai_drive", "poster_agent", "image_agent", "markdown_agent", "html_agent", "moa_deep_research", "generate_sparkpage_table", "article_verification", "sheets_to_site_agent", "sheets_agent_new", "clip_genius_agent", "agent_chat", "super_chat", "super_data_set", "agent_deep_research", "moa_chat", "generate_presentation", "moa_generate_image", "image_studio", "moa_generate_video", "moa_translator", "generate_sparkpage_gan", "generate_info_collection", "moa_deep_research_v2", "moa_deep_research_v3", "code_sandbox"].some((t => e.toLowerCase().includes(t.toLowerCase())))
            }, m = e => {
                if (!e || "string" != typeof e) return "";
                const t = document.createElement("div");
                t.innerHTML = e;
                t.querySelectorAll("script").forEach((e => e.remove()));
                const s = ["em", "strong", "b", "i"];
                return t.querySelectorAll("*").forEach((e => {
                    if (s.includes(e.tagName.toLowerCase())) {
                        Array.from(e.attributes).forEach((t => {
                            "class" !== t.name && e.removeAttribute(t.name)
                        }))
                    } else {
                        const t = e.textContent || e.innerText || "";
                        e.replaceWith(document.createTextNode(t))
                    }
                })), t.innerHTML
            };
            return (t, s) => (l(), i("div", oe, [(l(!0), i(_, null, y(e.searchResults, (t => {
                var s, a;
                return l(), i("div", {
                    key: t.id,
                    class: "flex items-center gap-[6px] cursor-pointer hover:bg-[#F5F5F5] dark:hover:bg-[#333] rounded-[12px] p-[12px] flex-shrink-0",
                    onClick: e => (e => {
                        n("handleProjectClick", e)
                    })(t)
                }, [c("div", ne, [c("div", ie, [(null == (s = e.findTaskConfig(t.type)) ? void 0 : s.icon) ? (l(), d(u(null == (a = e.findTaskConfig(t.type)) ? void 0 : a.icon), {
                    key: 0,
                    class: "w-[14px] h-[14px] flex-shrink-0 text-[#909499] dark:text-[#888]"
                })) : (l(), d(h(G), {
                    key: 1,
                    class: "w-[14px] h-[14px] flex-shrink-0 text-[#909499] dark:text-[#888]"
                })), c("div", le, j(t.name), 1)]), c("div", ce, [t.description_highlight && !p(t.description) ? (l(), i("span", {
                    key: 0,
                    innerHTML: m(t.description_highlight),
                    class: "highlight-content description-text"
                }, null, 8, de)) : p(t.description) ? (l(), i("span", pe, j(h(r)(t.ctime)), 1)) : (l(), i("span", ue, j(t.description), 1))])])], 8, re)
            })), 128))]))
        }
    }, [
        ["__scopeId", "data-v-7442e604"]
    ]),
    je = {
        key: 0,
        class: "left-side"
    },
    me = {
        class: "left-head"
    },
    ge = {
        class: "text"
    },
    fe = {
        class: "hover:bg-[#F5F5F5] mb-[16px] rounded-[12px] dark:hover:bg-[#4448]"
    },
    ve = {
        class: "flex items-center gap-[8px]"
    },
    _e = {
        class: "justify-start text-[#232425] text-[14px] font-normal font-['Arial'] leading-none dark:text-[#fff]"
    },
    ye = {
        class: "custom-modal-content"
    },
    xe = {
        class: "flex items-center gap-[11px] mx-[18px] py-[26px] border-b border-[#EFEFEF] dark:border-[#333]"
    },
    ke = ["placeholder"],
    Pe = {
        class: "cursor-pointer hover:bg-[#F5F5F5] dark:hover:bg-[#333] rounded-[35%] w-[24px] h-[24px] flex items-center justify-center"
    },
    we = {
        class: "flex justify-center my-[10px] mb-[26px] h-[356px]"
    },
    be = {
        key: 0,
        class: "search-skeleton-container w-full h-[50vh] mx-[16px] gap-[12px] mt-[16px]"
    },
    Ee = {
        key: 3,
        class: "w-full h-[50vh] flex items-center justify-center text-neutral-400 dark:text-neutral-500 text-xs font-normal font-['Arial'] leading-none"
    },
    Se = r({
        __name: "agent_task_list",
        props: {
            hideLeftSide: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["setShowLeftSide", "editProjectName"],
        setup(t, {
            expose: s,
            emit: r
        }) {
            const u = t,
                v = r,
                A = e => {
                    v("setShowLeftSide", e)
                },
                I = W();
            n(null);
            const O = x(),
                G = k("currentUser"),
                {
                    t: H,
                    locale: $
                } = o(),
                q = n(""),
                B = n(""),
                z = n(""),
                K = n(""),
                X = n(!1),
                Z = n(!1),
                J = e => {
                    v("editProjectName", e)
                },
                Q = async () => {
                    if (!ue.value.trim()) return Se.value = [], void(pe.value = "");
                    Me.value && Me.value.abort(), Me.value = new AbortController, Ce.value = !0, pe.value = ue.value.trim();
                    try {
                        const e = await $fetch("/api/search_user_projects_by_query", {
                            method: "GET",
                            params: {
                                query: pe.value,
                                top_k: 20
                            },
                            signal: Me.value.signal
                        });
                        if (0 === e.status && e.data.projects) {
                            const t = 1.1243947543488866,
                                s = e.data.projects,
                                a = s.filter((e => null != e.score && e.score <= t));
                            a.length < 5 ? Se.value = s.slice(0, 5) : Se.value = a
                        } else Se.value = [];
                        Ce.value = !1
                    } catch (e) {
                        e.name, Se.value = []
                    }
                },
                ee = V((() => {
                    ue.value.trim() ? Q() : te()
                }), 500, {
                    leading: !0
                }),
                te = () => {
                    Me.value && (Me.value.abort(), Me.value = null), ue.value = "", pe.value = "", Se.value = [], Ce.value = !1
                },
                se = () => {
                    Z.value = !1, te()
                },
                oe = () => {
                    Z.value = !0, A(!0), re()
                },
                re = () => {
                    D((() => {
                        Te.value.focus()
                    }))
                },
                ne = e => {
                    Z.value = !1, He() && A(!0), a.isGensparkAppMainTabView() ? a.windowopen(`/agents?id=${e.id}`) : (I.resetRetryRun(), I.loadProject(e.id))
                };
            P((() => G.value), (() => {
                G.value && (q.value = G.value.phone_call_user_phone_number, B.value = G.value.phone_call_user_country_code, z.value = G.value.phone_call_user_name, K.value = G.value.phone_call_voice, X.value = G.value.phone_call_is_not_first_time)
            }), {
                immediate: !0
            });
            const ie = n(null),
                le = e => {
                    var t, s;
                    return null == (s = null == (t = ie.value) ? void 0 : t.agentsPageConfig) ? void 0 : s.agents.find((t => t.id == e))
                },
                ce = n("");
            ce.value = window.location.search;
            w((() => {
                (() => {
                    const e = new URLSearchParams(ce.value).get("type");
                    let t = le(e);
                    !t || t.component || t.is_advanced && !t.is_deep_research || I.setProject(I.generateEmptyProject({
                        type: t.id,
                        name: t.name
                    })), t && t.is_advanced && !t.is_deep_research && t.onClick(t)
                })()
            })), P((() => !1 === G.value && null !== I.project), (() => {
                location.href = "/login"
            })), n(!1), n("article_verification"), n(!1);
            const de = n(!0),
                ue = n(""),
                pe = n(""),
                Se = n([]),
                Ce = n(!1),
                Te = n(null),
                Me = n(null),
                Re = n(!!O.query.show_content_only),
                Le = n(!1),
                De = n(null),
                Ae = n(null),
                Ie = n(!1),
                Ne = n(null),
                Fe = n(null),
                Oe = n(!1),
                Ge = V((() => {
                    a.log("onWindowResize", window.innerWidth), Oe.value = window.innerWidth
                }), 200);
            w((() => {
                window.addEventListener("resize", Ge), Ge()
            })), b((() => {
                window.removeEventListener("resize", Ge)
            }));
            const He = () => !1 === Oe.value ? a.isMobile() : Oe.value <= 1220;
            O.query.show_generate_sparkpage_gan && (Ie.value = !0);
            const $e = n(null);
            "IntersectionObserver" in window && ($e.value = new IntersectionObserver(((e, t) => {
                e.forEach((async e => {
                    I.setMyProjectsLoadMoreVisible(e.isIntersecting), e.isIntersecting && I.loadMyProjects(!0)
                }))
            }))), P((() => Ae.value), ((e, t) => {
                var s, a;
                t && (null == (s = $e.value) || s.unobserve(t)), e && (null == (a = $e.value) || a.observe(e))
            }));
            const qe = E((() => I.currentProjectName));
            P((() => qe.value), (e => {
                e && I.project && (I.project.name = e, I.projects.length > 0 && I.projects.forEach((e => {
                    e.id == I.project.id && (e.name = I.project.name)
                })))
            })), P((() => De.value), (() => {
                De.value && De.value.addEventListener("scroll", (() => {
                    De.value.scrollTop > 140 ? Le.value = !0 : Le.value = !1
                }))
            }));
            const Ue = () => {
                document.documentElement.scrollTop > 140 ? Le.value = !0 : Le.value = !1
            };
            w((() => {
                document.addEventListener("scroll", Ue)
            })), S((() => {
                document.removeEventListener("scroll", Ue)
            }));
            if ((async () => {
                    await I.loadMyProjects(), setTimeout((() => {
                        I.myProjectsLoadMoreVisible && !u.hideLeftSide && I.loadMyProjects(!0)
                    }), 1e3)
                })(), I.project && ["STARTED", "PROGRESS", "PENDING"].includes(I.project.status)) {
                const e = le(I.project.type);
                I.runProject({}, e)
            }
            n(null).value = a.isDarkMode(), a.isMobile() && P((() => Fe.value), (e => {
                e && function(e) {
                    let t = 0;
                    e.addEventListener("touchstart", (function(e) {
                        t = e.touches ? e.touches[0].screenY : e.screenY
                    })), e.addEventListener("touchmove", (function(s) {
                        var a = s.touches ? s.touches[0].screenY - t : s.screenY - t,
                            o = e.scrollTop,
                            r = e.scrollHeight,
                            n = e.offsetHeight;
                        (0 === o && a > 0 || o + n >= r - 2 && a < 0) && s.preventDefault()
                    }))
                }(e)
            })), w((() => {
                setTimeout((() => {
                    document.body.classList.add("agents-page")
                }), 0)
            })), b((() => {
                document.body.classList.remove("agents-page")
            }));
            const Ye = n(!1);
            P((() => Ye.value), (e => {
                I.runningProject
            }));
            const Ve = e => {
                window.open(`/agents?id=${e.id}`, "_blank")
            };
            return s({
                loadMyProjects: I.loadMyProjects,
                addOrUpdateProject: I.addOrUpdateProject
            }), (s, a) => {
                const o = e;
                return l(), i(_, null, [p(F, {
                    ref_key: "agentsConfigsRef",
                    ref: ie
                }, null, 512), p(C, {
                    name: "expand"
                }, {
                    default: f((() => [!de.value || Re.value || t.hideLeftSide ? m("", !0) : (l(), i("div", je, [c("div", me, [c("div", ge, j(s.$t("pages.autopilotagent.task_list")), 1), c("div", {
                        class: "icon",
                        onClick: a[0] || (a[0] = e => A(!t.hideLeftSide))
                    }, [p(h(N))])]), c("div", {
                        class: "left-main",
                        ref_key: "leftMain",
                        ref: Fe
                    }, [c("div", {
                        ref_key: "leftSide",
                        ref: Ne,
                        class: "my-projects"
                    }, [c("div", fe, [c("div", {
                        class: "py-[16px] mx-[14px] border-b border-t border-[#EAEAEA] cursor-pointer dark:border-[#efefef30]",
                        onClick: oe
                    }, [c("div", ve, [p(h(U), {
                        class: "text-[#606366]"
                    }), c("div", _e, j(s.$t("components.agents.search_task_list.search_chats")), 1)])])]), (l(!0), i(_, null, y(h(I).projects, (e => {
                        var t;
                        return l(), d(ae, {
                            key: e.id,
                            project: e,
                            "is-active": (null == (t = h(I).project) ? void 0 : t.id) == e.id || h(I).loadingProjectId === e.id || h(O).query.id == e.id,
                            "task-config": le(e.type),
                            onClick: ne,
                            onEditProject: J,
                            onDeleteProject: h(I).deleteProject
                        }, null, 8, ["project", "is-active", "task-config", "onDeleteProject"])
                    })), 128)), h(I).myProjectsHasMore ? (l(), i("div", {
                        key: 0,
                        class: "my-projects-load-more",
                        ref_key: "myProjectsLoadMore",
                        ref: Ae
                    }, [c("div", null, [p(o)])], 512)) : m("", !0)], 512)], 512)]))])),
                    _: 1
                }), p(C, {
                    name: "fade"
                }, {
                    default: f((() => [!t.hideLeftSide && de.value ? (l(), i("div", {
                        key: 0,
                        class: "main-mask",
                        onClick: a[1] || (a[1] = e => {
                            e.preventDefault(), e.stopPropagation(), A(!0)
                        }),
                        onTouchmove: a[2] || (a[2] = e => {
                            e.preventDefault(), e.stopPropagation()
                        })
                    }, null, 32)) : m("", !0)])),
                    _: 1
                }), p(C, {
                    name: "modal-fade"
                }, {
                    default: f((() => [(l(), d(T, {
                        to: "body"
                    }, [Z.value ? (l(), i("div", {
                        key: 0,
                        class: "custom-modal-overlay",
                        onClick: se
                    }, [c("div", ye, [c("div", {
                        class: "w-[90vw] md:w-[540px] bg-white dark:bg-[#1a1b1c] rounded-2xl shadow-[0px_4px_15px_0px_rgba(0,0,0,0.10)] dark:shadow-[0px_4px_15px_0px_rgba(255,255,255,0.10)]",
                        onClick: a[5] || (a[5] = g((() => {}), ["stop"]))
                    }, [c("div", xe, [p(h(U), {
                        class: "text-[#606366] dark:text-[#888]"
                    }), M(c("input", {
                        class: "flex-grow justify-start text-[#232425] dark:text-[#e0e0e0] text-[14px] font-normal font-['Arial'] leading-none dark:bg-transparent outline-none",
                        placeholder: h(H)("components.agents.search_task_list.search_placeholder"),
                        autofocus: "",
                        "onUpdate:modelValue": a[3] || (a[3] = e => ue.value = e),
                        ref_key: "searchInputRef",
                        ref: Te,
                        onInput: a[4] || (a[4] = (...e) => h(ee) && h(ee)(...e)),
                        onKeydown: R(Q, ["enter"])
                    }, null, 40, ke), [
                        [L, ue.value]
                    ]), c("div", Pe, [p(h(Y), {
                        class: "text-[#000] dark:text-[#e0e0e0]",
                        onClick: se
                    })])]), c("div", we, [Ce.value ? (l(), i("div", be, [(l(), i(_, null, y(4, (e => c("div", {
                        class: "search-skeleton-card h-[68px] rounded-[12px]",
                        key: `modal-skeleton-${e}`
                    }))), 64))])) : ue.value && Se.value.length > 0 ? (l(), d(he, {
                        key: 1,
                        "search-results": Se.value,
                        "find-task-config": le,
                        onHandleProjectClick: Ve
                    }, null, 8, ["search-results"])) : !ue.value && h(I).projects.length > 0 ? (l(), d(he, {
                        key: 2,
                        "search-results": h(I).projects,
                        "find-task-config": le,
                        onHandleProjectClick: Ve
                    }, null, 8, ["search-results"])) : (l(), i("div", Ee, " No results "))])])])])) : m("", !0)]))])),
                    _: 1
                })], 64)
            }
        }
    }, [
        ["__scopeId", "data-v-85465f6c"]
    ]);
export {
    Se as A
};