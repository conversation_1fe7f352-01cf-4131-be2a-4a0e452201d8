function e(e, n) {
    let {
        target: t
    } = e;
    for (; t;) {
        if (t.dataset && void 0 !== t.dataset[n]) return !0;
        t = t.parentElement
    }
    return !1
}

function n(e) {
    return Array.isArray(e) ? e : [e]
}
const t = "STOP";

function r(e, n) {
    const o = n(e);
    void 0 !== e.children && o !== t && e.children.forEach((e => r(e, n)))
}

function o(e) {
    return e.children
}

function i(e) {
    return e.key
}

function a() {
    return !1
}

function l(e) {
    return !0 === e.disabled
}

function s(e) {
    var n;
    return null == e ? [] : Array.isArray(e) ? e : null !== (n = e.checkedKeys) && void 0 !== n ? n : []
}

function c(e) {
    var n;
    return null == e || Array.isArray(e) ? [] : null !== (n = e.indeterminateKeys) && void 0 !== n ? n : []
}

function d(e, n) {
    const t = new Set(e);
    return n.forEach((e => {
        t.has(e) || t.add(e)
    })), Array.from(t)
}

function u(e, n) {
    const t = new Set(e);
    return n.forEach((e => {
        t.has(e) && t.delete(e)
    })), Array.from(t)
}

function f(e) {
    return "group" === (null == e ? void 0 : e.type)
}

function h(e) {
    const n = new Map;
    return e.forEach(((e, t) => {
        n.set(e.key, t)
    })), e => {
        var t;
        return null !== (t = n.get(e)) && void 0 !== t ? t : null
    }
}
class y extends Error {
    constructor() {
        super(), this.message = "SubtreeNotLoadedError: checking a subtree whose required nodes are not fully loaded."
    }
}

function g(e, n, t, r) {
    const o = k(n, t, r, !1),
        i = k(e, t, r, !0),
        a = function(e, n) {
            const t = new Set;
            return e.forEach((e => {
                const r = n.treeNodeMap.get(e);
                if (void 0 !== r) {
                    let e = r.parent;
                    for (; null !== e && !e.disabled && !t.has(e.key);) t.add(e.key), e = e.parent
                }
            })), t
        }(e, t),
        l = [];
    return o.forEach((e => {
        (i.has(e) || a.has(e)) && l.push(e)
    })), l.forEach((e => o.delete(e))), o
}

function p(e, n) {
    const {
        checkedKeys: t,
        keysToCheck: r,
        keysToUncheck: o,
        indeterminateKeys: i,
        cascade: a,
        leafOnly: l,
        checkStrategy: s,
        allowNotLoaded: c
    } = e;
    if (!a) return void 0 !== r ? {
        checkedKeys: d(t, r),
        indeterminateKeys: Array.from(i)
    } : void 0 !== o ? {
        checkedKeys: u(t, o),
        indeterminateKeys: Array.from(i)
    } : {
        checkedKeys: Array.from(t),
        indeterminateKeys: Array.from(i)
    };
    const {
        levelTreeNodeMap: f
    } = n;
    let h;
    h = void 0 !== o ? g(o, t, n, c) : void 0 !== r ? function(e, n, t, r) {
        return k(n.concat(e), t, r, !1)
    }(r, t, n, c) : k(t, n, c, !1);
    const y = "parent" === s,
        p = "child" === s || l,
        v = h,
        N = new Set;
    for (let d = Math.max.apply(null, Array.from(f.keys())); d >= 0; d -= 1) {
        const e = 0 === d,
            n = f.get(d);
        for (const t of n) {
            if (t.isLeaf) continue;
            const {
                key: n,
                shallowLoaded: r
            } = t;
            if (p && r && t.children.forEach((e => {
                    !e.disabled && !e.isLeaf && e.shallowLoaded && v.has(e.key) && v.delete(e.key)
                })), t.disabled || !r) continue;
            let o = !0,
                i = !1,
                a = !0;
            for (const e of t.children) {
                const n = e.key;
                if (!e.disabled)
                    if (a && (a = !1), v.has(n)) i = !0;
                    else {
                        if (N.has(n)) {
                            i = !0, o = !1;
                            break
                        }
                        if (o = !1, i) break
                    }
            }
            o && !a ? (y && t.children.forEach((e => {
                !e.disabled && v.has(e.key) && v.delete(e.key)
            })), v.add(n)) : i && N.add(n), e && p && v.has(n) && v.delete(n)
        }
    }
    return {
        checkedKeys: Array.from(v),
        indeterminateKeys: Array.from(N)
    }
}

function k(e, n, o, i) {
    const {
        treeNodeMap: a,
        getChildren: l
    } = n, s = new Set, c = new Set(e);
    return e.forEach((e => {
        const n = a.get(e);
        void 0 !== n && r(n, (e => {
            if (e.disabled) return t;
            const {
                key: n
            } = e;
            if (!s.has(n) && (s.add(n), c.add(n), function(e, n) {
                    return !1 === e.isLeaf && !Array.isArray(n(e))
                }(e.rawNode, l))) {
                if (i) return t;
                if (!o) throw new y
            }
        }))
    })), c
}

function v(e, n) {
    const t = e.siblings,
        r = t.length,
        {
            index: o
        } = e;
    return n ? t[(o + 1) % r] : o === t.length - 1 ? null : t[o + 1]
}

function N(e, n, {
    loop: t = !1,
    includeDisabled: r = !1
} = {}) {
    const o = "prev" === n ? w : v,
        i = {
            reverse: "prev" === n
        };
    let a = !1,
        l = null;
    return function n(s) {
        if (null !== s) {
            if (s === e)
                if (a) {
                    if (!e.disabled && !e.isGroup) return void(l = e)
                } else a = !0;
            else if ((!s.disabled || r) && !s.ignored && !s.isGroup) return void(l = s);
            if (s.isGroup) {
                const e = m(s, i);
                null !== e ? l = e : n(o(s, t))
            } else {
                const e = o(s, !1);
                if (null !== e) n(e);
                else {
                    const e = function(e) {
                        return e.parent
                    }(s);
                    (null == e ? void 0 : e.isGroup) ? n(o(e, t)): t && n(o(s, !0))
                }
            }
        }
    }(e), l
}

function w(e, n) {
    const t = e.siblings,
        r = t.length,
        {
            index: o
        } = e;
    return n ? t[(o - 1 + r) % r] : 0 === o ? null : t[o - 1]
}

function m(e, n = {}) {
    const {
        reverse: t = !1
    } = n, {
        children: r
    } = e;
    if (r) {
        const {
            length: e
        } = r, o = t ? -1 : e, i = t ? -1 : 1;
        for (let a = t ? e - 1 : 0; a !== o; a += i) {
            const e = r[a];
            if (!e.disabled && !e.ignored) {
                if (!e.isGroup) return e; {
                    const t = m(e, n);
                    if (null !== t) return t
                }
            }
        }
    }
    return null
}
const b = {
    getChild() {
        return this.ignored ? null : m(this)
    },
    getParent() {
        const {
            parent: e
        } = this;
        return (null == e ? void 0 : e.isGroup) ? e.getParent() : e
    },
    getNext(e = {}) {
        return N(this, "next", e)
    },
    getPrev(e = {}) {
        return N(this, "prev", e)
    }
};

function A(e, n) {
    const t = n ? new Set(n) : void 0,
        r = [];
    return function e(n) {
        n.forEach((n => {
            r.push(n), n.isLeaf || !n.children || n.ignored || (n.isGroup || void 0 === t || t.has(n.key)) && e(n.children)
        }))
    }(e), r
}

function L(e, n, t, r, o, i = null, a = 0) {
    const l = [];
    return e.forEach(((s, c) => {
        var d;
        const u = Object.create(r);
        if (u.rawNode = s, u.siblings = l, u.level = a, u.index = c, u.isFirstChild = 0 === c, u.isLastChild = c + 1 === e.length, u.parent = i, !u.ignored) {
            const e = o(s);
            Array.isArray(e) && (u.children = L(e, n, t, r, o, u, a + 1))
        }
        l.push(u), n.set(u.key, u), t.has(a) || t.set(a, []), null === (d = t.get(a)) || void 0 === d || d.push(u)
    })), l
}

function K(e, t = {}) {
    var r;
    const d = new Map,
        u = new Map,
        {
            getDisabled: h = l,
            getIgnored: y = a,
            getIsGroup: g = f,
            getKey: k = i
        } = t,
        v = null !== (r = t.getChildren) && void 0 !== r ? r : o,
        N = t.ignoreEmptyChildren ? e => {
            const n = v(e);
            return Array.isArray(n) ? n.length ? n : null : n
        } : v,
        w = Object.assign({
            get key() {
                return k(this.rawNode)
            },
            get disabled() {
                return h(this.rawNode)
            },
            get isGroup() {
                return g(this.rawNode)
            },
            get isLeaf() {
                return function(e, n) {
                    const {
                        isLeaf: t
                    } = e;
                    return void 0 !== t ? t : !n(e)
                }(this.rawNode, N)
            },
            get shallowLoaded() {
                return function(e, n) {
                    const {
                        isLeaf: t
                    } = e;
                    return !(!1 === t && !Array.isArray(n(e)))
                }(this.rawNode, N)
            },
            get ignored() {
                return y(this.rawNode)
            },
            contains(e) {
                return function(e, n) {
                    const t = e.key;
                    for (; n;) {
                        if (n.key === t) return !0;
                        n = n.parent
                    }
                    return !1
                }(this, e)
            }
        }, b),
        m = L(e, d, u, w, N);

    function K(e) {
        if (null == e) return null;
        const n = d.get(e);
        return n && !n.ignored ? n : null
    }
    const E = {
        treeNodes: m,
        treeNodeMap: d,
        levelTreeNodeMap: u,
        maxLevel: Math.max(...u.keys()),
        getChildren: N,
        getFlattenedNodes: e => A(m, e),
        getNode: function(e) {
            if (null == e) return null;
            const n = d.get(e);
            return !n || n.isGroup || n.ignored ? null : n
        },
        getPrev: function(e, n) {
            const t = K(e);
            return t ? t.getPrev(n) : null
        },
        getNext: function(e, n) {
            const t = K(e);
            return t ? t.getNext(n) : null
        },
        getParent: function(e) {
            const n = K(e);
            return n ? n.getParent() : null
        },
        getChild: function(e) {
            const n = K(e);
            return n ? n.getChild() : null
        },
        getFirstAvailableNode: () => function(e) {
            if (0 === e.length) return null;
            const n = e[0];
            return n.isGroup || n.ignored || n.disabled ? n.getNext() : n
        }(m),
        getPath: (e, n = {}) => function(e, {
            includeGroup: n = !1,
            includeSelf: t = !0
        }, r) {
            var o;
            const i = r.treeNodeMap;
            let a = null == e ? null : null !== (o = i.get(e)) && void 0 !== o ? o : null;
            const l = {
                keyPath: [],
                treeNodePath: [],
                treeNode: a
            };
            if (null == a ? void 0 : a.ignored) return l.treeNode = null, l;
            for (; a;) a.ignored || !n && a.isGroup || l.treeNodePath.push(a), a = a.parent;
            return l.treeNodePath.reverse(), t || l.treeNodePath.pop(), l.keyPath = l.treeNodePath.map((e => e.key)), l
        }(e, n, E),
        getCheckedKeys(e, n = {}) {
            const {
                cascade: t = !0,
                leafOnly: r = !1,
                checkStrategy: o = "all",
                allowNotLoaded: i = !1
            } = n;
            return p({
                checkedKeys: s(e),
                indeterminateKeys: c(e),
                cascade: t,
                leafOnly: r,
                checkStrategy: o,
                allowNotLoaded: i
            }, E)
        },
        check(e, t, r = {}) {
            const {
                cascade: o = !0,
                leafOnly: i = !1,
                checkStrategy: a = "all",
                allowNotLoaded: l = !1
            } = r;
            return p({
                checkedKeys: s(t),
                indeterminateKeys: c(t),
                keysToCheck: null == e ? [] : n(e),
                cascade: o,
                leafOnly: i,
                checkStrategy: a,
                allowNotLoaded: l
            }, E)
        },
        uncheck(e, t, r = {}) {
            const {
                cascade: o = !0,
                leafOnly: i = !1,
                checkStrategy: a = "all",
                allowNotLoaded: l = !1
            } = r;
            return p({
                checkedKeys: s(t),
                indeterminateKeys: c(t),
                keysToUncheck: null == e ? [] : n(e),
                cascade: o,
                leafOnly: i,
                checkStrategy: a,
                allowNotLoaded: l
            }, E)
        },
        getNonLeafKeys: (e = {}) => function(e, n = {}) {
            const {
                preserveGroup: t = !1
            } = n, r = [], o = t ? e => {
                e.isLeaf || (r.push(e.key), i(e.children))
            } : e => {
                e.isLeaf || (e.isGroup || r.push(e.key), i(e.children))
            };

            function i(e) {
                e.forEach(o)
            }
            return i(e), r
        }(m, e)
    };
    return E
}
export {
    h as a, K as c, A as f, e as h
};