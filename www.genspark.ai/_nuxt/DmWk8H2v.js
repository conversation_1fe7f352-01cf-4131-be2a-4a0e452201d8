const __vite__mapDeps = (i, m = __vite__mapDeps, d = (m.f || (m.f = ["./AMuMAA_L.js", "./Cf0SOiw0.js", "./entry.CjsqieIi.css", "./DY44xVYu.js", "./NG4ombpb.js", "./BH52nuY1.js", "./BPQGB51Y.js", "./C-H3edso.js", "./Jx3-I-D7.js", "./Cp7w48vH.js", "./DJ-JsGJu.js", "./CSefR0kE.js", "./DyMB-pVc.js", "./Bzg9uoz_.js", "./CQ2glRxo.js", "./DAjjhrgi.js", "./MpDLC7up.js", "./B7VeW_-d.js", "./YoutubeWidget.CYLc38aB.css", "./B56nEZrv.js", "./BYvs8isC.js", "./0tM3vo_n.js", "./BuZX8s8J.js", "./CRmNre8Y.js", "./Bl-gMEVt.js", "./CVrRKK4N.js", "./DOnko34f.js", "./B6noBY_5.js", "./BjWUbj3w.js", "./pB_XRIgB.js", "./BLWq1oPC.js", "./DpMvtoun.js", "./Jr9eiJio.js", "./9wLWmnxl.js", "./WZsIN7xM.js", "./mindmap_in_markdown.DgMUazV_.css", "./HR06imN1.js", "./github-dark.DGBC0rMY.css"]))) => i.map(i => d[i]);
import {
    aW as t,
    ex as n,
    b1 as e,
    ey as r,
    aA as i,
    az as o,
    dO as a,
    dH as u,
    dh as s,
    ez as l,
    eA as c,
    b2 as f,
    eB as p,
    a5 as h,
    de as v,
    dg as d,
    dB as _,
    b3 as g,
    dC as y,
    eC as m,
    di as b,
    eD as w,
    es as x,
    ep as k,
    aH as j,
    er as E,
    eq as W,
    eb as I,
    e5 as O,
    a6 as B,
    dI as A,
    eE as M,
    ea as R,
    af as L,
    ae as S,
    dJ as C,
    eF as P,
    eG as D,
    eH as T,
    eI as z,
    eJ as $,
    dF as q,
    eK as N,
    eL as F,
    aG as U,
    dK as K,
    e7 as H,
    dD as V,
    et as J,
    eM as G,
    e9 as Y,
    e8 as Q,
    ah as X,
    m as Z,
    _ as tt,
    S as nt,
    r as et,
    v as rt,
    P as it,
    dQ as ot,
    h as at,
    U as ut,
    C as st,
    d as lt,
    b as ct,
    a as ft,
    f as pt,
    e as ht,
    y as vt,
    F as dt,
    bx as _t,
    ew as gt,
    o as yt
} from "./Cf0SOiw0.js";
import "./B56nEZrv.js";
import {
    _ as mt,
    m as bt
} from "./BXgTVD7d.js";
import {
    _ as wt,
    m as xt,
    l as kt,
    p as jt
} from "./CsPFbezH.js";
import {
    b as Et
} from "./0tM3vo_n.js";
import {
    p as Wt,
    I as It,
    J as Ot,
    l as Bt,
    K as At,
    L as Mt,
    N as Rt,
    O as Lt,
    P as St,
    T as Ct,
    U as Pt,
    V as Dt,
    v as Tt,
    D as zt,
    z as $t,
    C as qt,
    s as Nt,
    G as Ft,
    t as Ut,
    E as Kt,
    i as Ht,
    q as Vt,
    m as Jt,
    f as Gt,
    r as Yt,
    A as Qt,
    u as Xt,
    M as Zt,
    B as tn,
    H as nn,
    k as en,
    e as rn
} from "./Jx3-I-D7.js";
import {
    C as on
} from "./CQjXacSG.js";
import {
    a as an,
    b as un,
    d as sn,
    g as ln,
    c as cn,
    i as fn,
    e as pn
} from "./Cp7w48vH.js";
import {
    W as hn,
    k as vn,
    a as dn,
    c as _n,
    f as gn,
    e as yn,
    S as mn,
    s as bn
} from "./C-H3edso.js";
import {
    g as wn,
    t as xn,
    e as kn,
    S as jn,
    k as En,
    d as Wn,
    l as In,
    i as On,
    m as Bn,
    n as An,
    b as Mn,
    c as Rn,
    h as Ln,
    o as Sn,
    p as Cn,
    q as Pn,
    r as Dn,
    s as Tn,
    u as zn,
    v as $n,
    w as qn,
    x as Nn,
    a as Fn,
    y as Un
} from "./BPQGB51Y.js";
import {
    b as Kn,
    c as Hn,
    a as Vn,
    w as Jn,
    d as Gn
} from "./wD492QF4.js";
import {
    t as Yn,
    a as Qn
} from "./CSefR0kE.js";
import {
    n as Xn,
    d as Zn
} from "./DOnko34f.js";
import {
    t as te,
    b as ne,
    a as ee
} from "./DyMB-pVc.js";
import {
    c as re,
    b as ie,
    a as oe,
    u as ae
} from "./DJ-JsGJu.js";
import {
    m as ue
} from "./CqNssBtC.js";
import {
    i as se
} from "./BqHcVhvy.js";
import {
    c as le,
    a as ce
} from "./DLUhQFIq.js";
import {
    k as fe
} from "./C_QvFyDp.js";
import {
    H as pe
} from "./DR_b14-4.js"; /* empty css        */
import {
    Y as he
} from "./CQ2glRxo.js";

function ve(n) {
    return "number" == typeof n ? n : t(n) ? NaN : +n
}

function de(t, e) {
    return function(r, i) {
        var o;
        if (void 0 === r && void 0 === i) return e;
        if (void 0 !== r && (o = r), void 0 !== i) {
            if (void 0 === o) return i;
            "string" == typeof r || "string" == typeof i ? (r = n(r), i = n(i)) : (r = ve(r), i = ve(i)), o = t(r, i)
        }
        return o
    }
}
var _e = de((function(t, n) {
    return t + n
}), 0);
var ge = hn && new hn,
    ye = ge ? function(t, n) {
        return ge.set(t, n), t
    } : e;

function me(t) {
    return function() {
        var n = arguments;
        switch (n.length) {
            case 0:
                return new t;
            case 1:
                return new t(n[0]);
            case 2:
                return new t(n[0], n[1]);
            case 3:
                return new t(n[0], n[1], n[2]);
            case 4:
                return new t(n[0], n[1], n[2], n[3]);
            case 5:
                return new t(n[0], n[1], n[2], n[3], n[4]);
            case 6:
                return new t(n[0], n[1], n[2], n[3], n[4], n[5]);
            case 7:
                return new t(n[0], n[1], n[2], n[3], n[4], n[5], n[6])
        }
        var e = r(t.prototype),
            o = t.apply(e, n);
        return i(o) ? o : e
    }
}
var be = Math.max;

function we(t, n, e, r) {
    for (var i = -1, o = t.length, a = e.length, u = -1, s = n.length, l = be(o - a, 0), c = Array(s + l), f = !r; ++u < s;) c[u] = n[u];
    for (; ++i < a;)(f || i < o) && (c[e[i]] = t[i]);
    for (; l--;) c[u++] = t[i++];
    return c
}
var xe = Math.max;

function ke(t, n, e, r) {
    for (var i = -1, o = t.length, a = -1, u = e.length, s = -1, l = n.length, c = xe(o - u, 0), f = Array(c + l), p = !r; ++i < c;) f[i] = t[i];
    for (var h = i; ++s < l;) f[h + s] = n[s];
    for (; ++a < u;)(p || i < o) && (f[h + e[a]] = t[i++]);
    return f
}

function je() {}

function Ee(t) {
    this.__wrapped__ = t, this.__actions__ = [], this.__dir__ = 1, this.__filtered__ = !1, this.__iteratees__ = [], this.__takeCount__ = 4294967295, this.__views__ = []
}
Ee.prototype = r(je.prototype), Ee.prototype.constructor = Ee;
var We = ge ? function(t) {
        return ge.get(t)
    } : Wt,
    Ie = {},
    Oe = Object.prototype.hasOwnProperty;

function Be(t) {
    for (var n = t.name + "", e = Ie[n], r = Oe.call(Ie, n) ? e.length : 0; r--;) {
        var i = e[r],
            o = i.func;
        if (null == o || o == t) return i.name
    }
    return n
}

function Ae(t, n) {
    this.__wrapped__ = t, this.__actions__ = [], this.__chain__ = !!n, this.__index__ = 0, this.__values__ = void 0
}

function Me(t) {
    if (t instanceof Ee) return t.clone();
    var n = new Ae(t.__wrapped__, t.__chain__);
    return n.__actions__ = a(t.__actions__), n.__index__ = t.__index__, n.__values__ = t.__values__, n
}
Ae.prototype = r(je.prototype), Ae.prototype.constructor = Ae;
var Re = Object.prototype.hasOwnProperty;

function Le(t) {
    if (u(t) && !s(t) && !(t instanceof Ee)) {
        if (t instanceof Ae) return t;
        if (Re.call(t, "__wrapped__")) return Me(t)
    }
    return new Ae(t)
}

function Se(t) {
    var n = Be(t),
        e = Le[n];
    if ("function" != typeof e || !(n in Ee.prototype)) return !1;
    if (t === e) return !0;
    var r = We(e);
    return !!r && t === r[0]
}
Le.prototype = je.prototype, Le.prototype.constructor = Le;
var Ce = l(ye),
    Pe = /\{\n\/\* \[wrapped with (.+)\] \*/,
    De = /,? & /;
var Te = /\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/;

function ze(t, n, e, r) {
    for (var i = t.length, o = e + (r ? 1 : -1); r ? o-- : ++o < i;)
        if (n(t[o], o, t)) return o;
    return -1
}

function $e(t) {
    return t != t
}

function qe(t, n, e) {
    return n == n ? function(t, n, e) {
        for (var r = e - 1, i = t.length; ++r < i;)
            if (t[r] === n) return r;
        return -1
    }(t, n, e) : ze(t, $e, e)
}

function Ne(t, n) {
    return !!(null == t ? 0 : t.length) && qe(t, n, 0) > -1
}
var Fe = [
    ["ary", 128],
    ["bind", 1],
    ["bindKey", 2],
    ["curry", 8],
    ["curryRight", 16],
    ["flip", 512],
    ["partial", 32],
    ["partialRight", 64],
    ["rearg", 256]
];

function Ue(t, n, e) {
    var r = n + "";
    return c(t, function(t, n) {
        var e = n.length;
        if (!e) return t;
        var r = e - 1;
        return n[r] = (e > 1 ? "& " : "") + n[r], n = n.join(e > 2 ? ", " : " "), t.replace(Te, "{\n/* [wrapped with " + n + "] */\n")
    }(r, function(t, n) {
        return an(Fe, (function(e) {
            var r = "_." + e[0];
            n & e[1] && !Ne(t, r) && t.push(r)
        })), t.sort()
    }(function(t) {
        var n = t.match(Pe);
        return n ? n[1].split(De) : []
    }(r), e)))
}

function Ke(t, n, e, r, i, o, a, u, s, l) {
    var c = 8 & n;
    n |= c ? 32 : 64, 4 & (n &= ~(c ? 64 : 32)) || (n &= -4);
    var f = [t, n, i, c ? o : void 0, c ? a : void 0, c ? void 0 : o, c ? void 0 : a, u, s, l],
        p = e.apply(void 0, f);
    return Se(t) && Ce(p, f), p.placeholder = r, Ue(p, t, n)
}

function He(t) {
    return t.placeholder
}
var Ve = Math.min;
var Je = "__lodash_placeholder__";

function Ge(t, n) {
    for (var e = -1, r = t.length, i = 0, o = []; ++e < r;) {
        var a = t[e];
        a !== n && a !== Je || (t[e] = Je, o[i++] = e)
    }
    return o
}

function Ye(t, n, e, r, i, u, s, l, c, p) {
    var h = 128 & n,
        v = 1 & n,
        d = 2 & n,
        _ = 24 & n,
        g = 512 & n,
        y = d ? void 0 : me(t);
    return function m() {
        for (var b = arguments.length, w = Array(b), x = b; x--;) w[x] = arguments[x];
        if (_) var k = He(m),
            j = function(t, n) {
                for (var e = t.length, r = 0; e--;) t[e] === n && ++r;
                return r
            }(w, k);
        if (r && (w = we(w, r, i, _)), u && (w = ke(w, u, s, _)), b -= j, _ && b < p) {
            var E = Ge(w, k);
            return Ke(t, n, Ye, m.placeholder, e, w, E, l, c, p - b)
        }
        var W = v ? e : this,
            I = d ? W[t] : t;
        return b = w.length, l ? w = function(t, n) {
            for (var e = t.length, r = Ve(n.length, e), i = a(t); r--;) {
                var o = n[r];
                t[r] = f(o, e) ? i[o] : void 0
            }
            return t
        }(w, l) : g && b > 1 && w.reverse(), h && c < b && (w.length = c), this && this !== o && this instanceof m && (I = y || me(I)), I.apply(W, w)
    }
}
var Qe = "__lodash_placeholder__",
    Xe = 128,
    Ze = Math.min;
var tr = Math.max;

function nr(t, n, e, r, i, a, u, s) {
    var l = 2 & n;
    if (!l && "function" != typeof t) throw new TypeError("Expected a function");
    var c = r ? r.length : 0;
    if (c || (n &= -97, r = i = void 0), u = void 0 === u ? u : tr(Yn(u), 0), s = void 0 === s ? s : Yn(s), c -= i ? i.length : 0, 64 & n) {
        var f = r,
            h = i;
        r = i = void 0
    }
    var v = l ? void 0 : We(t),
        d = [t, n, e, r, i, f, h, a, u, s];
    if (v && function(t, n) {
            var e = t[1],
                r = n[1],
                i = e | r,
                o = i < 131,
                a = r == Xe && 8 == e || r == Xe && 256 == e && t[7].length <= n[8] || 384 == r && n[7].length <= n[8] && 8 == e;
            if (!o && !a) return t;
            1 & r && (t[2] = n[2], i |= 1 & e ? 0 : 4);
            var u = n[3];
            if (u) {
                var s = t[3];
                t[3] = s ? we(s, u, n[4]) : u, t[4] = s ? Ge(t[3], Qe) : n[4]
            }(u = n[5]) && (s = t[5], t[5] = s ? ke(s, u, n[6]) : u, t[6] = s ? Ge(t[5], Qe) : n[6]), (u = n[7]) && (t[7] = u), r & Xe && (t[8] = null == t[8] ? n[8] : Ze(t[8], n[8])), null == t[9] && (t[9] = n[9]), t[0] = n[0], t[1] = i
        }(d, v), t = d[0], n = d[1], e = d[2], r = d[3], i = d[4], !(s = d[9] = void 0 === d[9] ? l ? 0 : t.length : tr(d[9] - c, 0)) && 24 & n && (n &= -25), n && 1 != n) _ = 8 == n || 16 == n ? function(t, n, e) {
        var r = me(t);
        return function i() {
            for (var a = arguments.length, u = Array(a), s = a, l = He(i); s--;) u[s] = arguments[s];
            var c = a < 3 && u[0] !== l && u[a - 1] !== l ? [] : Ge(u, l);
            return (a -= c.length) < e ? Ke(t, n, Ye, i.placeholder, void 0, u, c, void 0, void 0, e - a) : p(this && this !== o && this instanceof i ? r : t, this, u)
        }
    }(t, n, s) : 32 != n && 33 != n || i.length ? Ye.apply(void 0, d) : function(t, n, e, r) {
        var i = 1 & n,
            a = me(t);
        return function n() {
            for (var u = -1, s = arguments.length, l = -1, c = r.length, f = Array(c + s), h = this && this !== o && this instanceof n ? a : t; ++l < c;) f[l] = r[l];
            for (; s--;) f[l++] = arguments[++u];
            return p(h, i ? e : this, f)
        }
    }(t, n, e, r);
    else var _ = function(t, n, e) {
        var r = 1 & n,
            i = me(t);
        return function n() {
            return (this && this !== o && this instanceof n ? i : t).apply(r ? e : this, arguments)
        }
    }(t, n, e);
    return Ue((v ? ye : Ce)(_, d), t, n)
}

function er(t, n, e) {
    return n = e ? void 0 : n, nr(t, 128, void 0, void 0, void 0, void 0, n = t && null == n ? t.length : n)
}
var rr = Object.prototype.hasOwnProperty,
    ir = h((function(t, n) {
        if (v(n) || d(n)) _(n, vn(n), t);
        else
            for (var e in n) rr.call(n, e) && g(t, e, n[e])
    })),
    or = h((function(t, n) {
        _(n, y(n), t)
    })),
    ar = h((function(t, n, e, r) {
        _(n, y(n), t, r)
    })),
    ur = h((function(t, n, e, r) {
        _(n, vn(n), t, r)
    }));

function sr(t, n) {
    for (var e = -1, r = n.length, i = Array(r), o = null == t; ++e < r;) i[e] = o ? void 0 : wn(t, n[e]);
    return i
}

function lr(t) {
    return (null == t ? 0 : t.length) ? It(t, 1) : []
}

function cr(t) {
    return c(m(t, void 0, lr), t + "")
}
var fr = cr(sr);

function pr(t) {
    if (!u(t)) return !1;
    var n = b(t);
    return "[object Error]" == n || "[object DOMException]" == n || "string" == typeof t.message && "string" == typeof t.name && !w(t)
}
var hr = x((function(t, n) {
    try {
        return p(t, void 0, n)
    } catch (e) {
        return pr(e) ? e : new Error(e)
    }
}));

function vr(t, n) {
    var e;
    if ("function" != typeof n) throw new TypeError("Expected a function");
    return t = Yn(t),
        function() {
            return --t > 0 && (e = n.apply(this, arguments)), t <= 1 && (n = void 0), e
        }
}
var dr = x((function(t, n, e) {
    var r = 1;
    if (e.length) {
        var i = Ge(e, He(dr));
        r |= 32
    }
    return nr(t, r, n, e, i)
}));
dr.placeholder = {};
var _r = cr((function(t, n) {
        return an(n, (function(n) {
            n = xn(n), k(t, n, dr(t[n], t))
        })), t
    })),
    gr = x((function(t, n, e) {
        var r = 3;
        if (e.length) {
            var i = Ge(e, He(gr));
            r |= 32
        }
        return nr(n, r, t, e, i)
    }));
gr.placeholder = {};
var yr = o.isFinite,
    mr = Math.min;

function br(t) {
    var n = Math[t];
    return function(t, e) {
        if (t = te(t), (e = null == e ? 0 : mr(Yn(e), 292)) && yr(t)) {
            var r = (j(t) + "e").split("e"),
                i = n(r[0] + "e" + (+r[1] + e));
            return +((r = (j(i) + "e").split("e"))[0] + "e" + (+r[1] - e))
        }
        return n(t)
    }
}
var wr = br("ceil");

function xr(t) {
    var n = Le(t);
    return n.__chain__ = !0, n
}
var kr = Math.ceil,
    jr = Math.max;

function Er(t, n, e) {
    return t == t && (void 0 !== e && (t = t <= e ? t : e), void 0 !== n && (t = t >= n ? t : n)), t
}

function Wr(t) {
    return un(t, 4)
}

function Ir(t) {
    for (var n = -1, e = null == t ? 0 : t.length, r = 0, i = []; ++n < e;) {
        var o = t[n];
        o && (i[r++] = o)
    }
    return i
}

function Or(t, n, e) {
    var r = e.length;
    if (null == t) return !r;
    for (t = Object(t); r--;) {
        var i = e[r],
            o = n[i],
            a = t[i];
        if (void 0 === a && !(i in t) || !o(a)) return !1
    }
    return !0
}
var Br = Object.prototype.hasOwnProperty,
    Ar = Ot((function(t, n, e) {
        Br.call(t, e) ? ++t[e] : k(t, e, 1)
    }));

function Mr(t, n, e) {
    var r = nr(t, 8, void 0, void 0, void 0, void 0, void 0, n = e ? void 0 : n);
    return r.placeholder = Mr.placeholder, r
}
Mr.placeholder = {};

function Rr(t, n, e) {
    var r = nr(t, 16, void 0, void 0, void 0, void 0, void 0, n = e ? void 0 : n);
    return r.placeholder = Rr.placeholder, r
}
Rr.placeholder = {};
var Lr = Object.prototype,
    Sr = Lr.hasOwnProperty,
    Cr = x((function(t, n) {
        t = Object(t);
        var e = -1,
            r = n.length,
            i = r > 2 ? n[2] : void 0;
        for (i && E(n[0], n[1], i) && (r = 1); ++e < r;)
            for (var o = n[e], a = y(o), u = -1, s = a.length; ++u < s;) {
                var l = a[u],
                    c = t[l];
                (void 0 === c || O(c, Lr[l]) && !Sr.call(t, l)) && (t[l] = o[l])
            }
        return t
    }));

function Pr(t, n, e, r, o, a) {
    return i(t) && i(n) && (a.set(n, t), B(t, n, void 0, Pr, a), a.delete(n)), t
}
var Dr = x((function(t) {
    return t.push(void 0, Pr), p(ue, void 0, t)
}));

function Tr(t, n, e) {
    if ("function" != typeof t) throw new TypeError("Expected a function");
    return setTimeout((function() {
        t.apply(void 0, e)
    }), n)
}
var zr = x((function(t, n) {
        return Tr(t, 1, n)
    })),
    $r = x((function(t, n, e) {
        return Tr(t, te(n) || 0, e)
    }));

function qr(t, n, e) {
    for (var r = -1, i = null == t ? 0 : t.length; ++r < i;)
        if (e(n, t[r])) return !0;
    return !1
}

function Nr(t, n, e, r) {
    var i = -1,
        o = Ne,
        a = !0,
        u = t.length,
        s = [],
        l = n.length;
    if (!u) return s;
    e && (n = I(n, A(e))), r ? (o = qr, a = !1) : n.length >= 200 && (o = En, a = !1, n = new jn(n));
    t: for (; ++i < u;) {
        var c = t[i],
            f = null == e ? c : e(c);
        if (c = r || 0 !== c ? c : 0, a && f == f) {
            for (var p = l; p--;)
                if (n[p] === f) continue t;
            s.push(c)
        } else o(n, f, r) || s.push(c)
    }
    return s
}
var Fr = x((function(t, n) {
        return M(t) ? Nr(t, It(n, 1, M, !0)) : []
    })),
    Ur = x((function(t, n) {
        var e = Bt(n);
        return M(e) && (e = void 0), M(t) ? Nr(t, It(n, 1, M, !0), kn(e)) : []
    })),
    Kr = x((function(t, n) {
        var e = Bt(n);
        return M(e) && (e = void 0), M(t) ? Nr(t, It(n, 1, M, !0), void 0, e) : []
    })),
    Hr = de((function(t, n) {
        return t / n
    }), 1);

function Vr(t, n, e) {
    var r = null == t ? 0 : t.length;
    return r ? (n = e || void 0 === n ? 1 : Yn(n), W(t, n < 0 ? 0 : n, r)) : []
}

function Jr(t, n, e) {
    var r = null == t ? 0 : t.length;
    return r ? (n = e || void 0 === n ? 1 : Yn(n), W(t, 0, (n = r - n) < 0 ? 0 : n)) : []
}

function Gr(t, n, e, r) {
    for (var i = t.length, o = r ? i : -1;
        (r ? o-- : ++o < i) && n(t[o], o, t););
    return e ? W(t, r ? 0 : o, r ? o + 1 : i) : W(t, r ? o + 1 : 0, r ? i : o)
}

function Yr(t, n) {
    return (s(t) ? an : Wn)(t, re(n))
}

function Qr(t) {
    return function(n) {
        var e, r, i, o = _n(n);
        return "[object Map]" == o ? In(n) : "[object Set]" == o ? (e = n, r = -1, i = Array(e.size), e.forEach((function(t) {
            i[++r] = [t, t]
        })), i) : function(t, n) {
            return I(n, (function(n) {
                return [n, t[n]]
            }))
        }(n, t(n))
    }
}
var Xr = Qr(vn),
    Zr = Qr(y),
    ti = Kn({
        "&": "&amp;",
        "<": "&lt;",
        ">": "&gt;",
        '"': "&quot;",
        "'": "&#39;"
    }),
    ni = /[&<>"']/g,
    ei = RegExp(ni.source);

function ri(t) {
    return (t = j(t)) && ei.test(t) ? t.replace(ni, ti) : t
}
var ii = /[\\^$.*+?()[\]{}|]/g,
    oi = RegExp(ii.source);

function ai(t, n) {
    for (var e = -1, r = null == t ? 0 : t.length; ++e < r;)
        if (!n(t[e], e, t)) return !1;
    return !0
}

function ui(t, n) {
    var e = !0;
    return Wn(t, (function(t, r, i) {
        return e = !!n(t, r, i)
    })), e
}

function si(t, n, e) {
    var r = s(t) ? ai : ui;
    return e && E(t, n, e) && (n = void 0), r(t, kn(n))
}

function li(t) {
    return t ? Er(Yn(t), 0, 4294967295) : 0
}

function ci(t, n) {
    var e = [];
    return Wn(t, (function(t, r, i) {
        n(t, r, i) && e.push(t)
    })), e
}

function fi(t, n) {
    return (s(t) ? gn : ci)(t, kn(n))
}

function pi(t) {
    return function(n, e, r) {
        var i = Object(n);
        if (!d(n)) {
            var o = kn(e);
            n = vn(n), e = function(t) {
                return o(i[t], t, i)
            }
        }
        var a = t(n, e, r);
        return a > -1 ? i[o ? n[a] : a] : void 0
    }
}
var hi = Math.max;

function vi(t, n, e) {
    var r = null == t ? 0 : t.length;
    if (!r) return -1;
    var i = null == e ? 0 : Yn(e);
    return i < 0 && (i = hi(r + i, 0)), ze(t, kn(n), i)
}
var di = pi(vi);

function _i(t, n, e) {
    var r;
    return e(t, (function(t, e, i) {
        if (n(t, e, i)) return r = e, !1
    })), r
}
var gi = Math.max,
    yi = Math.min;

function mi(t, n, e) {
    var r = null == t ? 0 : t.length;
    if (!r) return -1;
    var i = r - 1;
    return void 0 !== e && (i = Yn(e), i = e < 0 ? gi(r + i, 0) : yi(i, r - 1)), ze(t, kn(n), i, !0)
}
var bi = pi(mi);

function wi(t, n) {
    return It(Bn(t, n), 1)
}
var xi = 1 / 0;
var ki = 1 / 0;
var ji = br("floor");

function Ei(t) {
    return cr((function(n) {
        var e = n.length,
            r = e,
            i = Ae.prototype.thru;
        for (t && n.reverse(); r--;) {
            var o = n[r];
            if ("function" != typeof o) throw new TypeError("Expected a function");
            if (i && !a && "wrapper" == Be(o)) var a = new Ae([], !0)
        }
        for (r = a ? r : e; ++r < e;) {
            var u = Be(o = n[r]),
                l = "wrapper" == u ? We(o) : void 0;
            a = l && Se(l[0]) && 424 == l[1] && !l[4].length && 1 == l[9] ? a[Be(l[0])].apply(a, l[3]) : 1 == o.length && Se(o) ? a[u]() : a.thru(o)
        }
        return function() {
            var t = arguments,
                r = t[0];
            if (a && 1 == t.length && s(r)) return a.plant(r).value();
            for (var i = 0, o = e ? n[i].apply(this, t) : r; ++i < e;) o = n[i].call(this, o);
            return o
        }
    }))
}
var Wi = Ei(),
    Ii = Ei(!0);

function Oi(t, n) {
    return null == t ? t : R(t, re(n), y)
}

function Bi(t, n) {
    return t && On(t, re(n))
}

function Ai(t, n) {
    return gn(n, (function(n) {
        return L(t[n])
    }))
}

function Mi(t, n) {
    return t > n
}

function Ri(t) {
    return function(n, e) {
        return "string" == typeof n && "string" == typeof e || (n = te(n), e = te(e)), t(n, e)
    }
}
var Li = Ri(Mi),
    Si = Ri((function(t, n) {
        return t >= n
    })),
    Ci = Object.prototype.hasOwnProperty;

function Pi(t, n) {
    return null != t && Ci.call(t, n)
}

function Di(t, n) {
    return null != t && An(t, n, Pi)
}
var Ti = Math.max,
    zi = Math.min;

function $i(t) {
    return "string" == typeof t || !s(t) && u(t) && "[object String]" == b(t)
}

function qi(t, n) {
    return I(n, (function(n) {
        return t[n]
    }))
}

function Ni(t) {
    return null == t ? [] : qi(t, vn(t))
}
var Fi = Math.max;

function Ui(t, n, e, r) {
    t = d(t) ? t : Ni(t), e = e && !r ? Yn(e) : 0;
    var i = t.length;
    return e < 0 && (e = Fi(i + e, 0)), $i(t) ? e <= i && t.indexOf(n, e) > -1 : !!i && qe(t, n, e) > -1
}
var Ki = Math.max;

function Hi(t, n, e) {
    var r = null == t ? 0 : t.length;
    if (!r) return -1;
    var i = null == e ? 0 : Yn(e);
    return i < 0 && (i = Ki(r + i, 0)), qe(t, n, i)
}
var Vi = Math.min;

function Ji(t, n, e) {
    for (var r = e ? qr : Ne, i = t[0].length, o = t.length, a = o, u = Array(o), s = 1 / 0, l = []; a--;) {
        var c = t[a];
        a && n && (c = I(c, A(n))), s = Vi(c.length, s), u[a] = !e && (n || i >= 120 && c.length >= 120) ? new jn(a && c) : void 0
    }
    c = t[0];
    var f = -1,
        p = u[0];
    t: for (; ++f < i && l.length < s;) {
        var h = c[f],
            v = n ? n(h) : h;
        if (h = e || 0 !== h ? h : 0, !(p ? En(p, v) : r(l, v, e))) {
            for (a = o; --a;) {
                var d = u[a];
                if (!(d ? En(d, v) : r(t[a], v, e))) continue t
            }
            p && p.push(v), l.push(h)
        }
    }
    return l
}

function Gi(t) {
    return M(t) ? t : []
}
var Yi = x((function(t) {
        var n = I(t, Gi);
        return n.length && n[0] === t[0] ? Ji(n) : []
    })),
    Qi = x((function(t) {
        var n = Bt(t),
            e = I(t, Gi);
        return n === Bt(e) ? n = void 0 : e.pop(), e.length && e[0] === t[0] ? Ji(e, kn(n)) : []
    })),
    Xi = x((function(t) {
        var n = Bt(t),
            e = I(t, Gi);
        return (n = "function" == typeof n ? n : void 0) && e.pop(), e.length && e[0] === t[0] ? Ji(e, void 0, n) : []
    }));

function Zi(t, n) {
    return function(e, r) {
        return function(t, n, e, r) {
            return On(t, (function(t, i, o) {
                n(r, e(t), i, o)
            })), r
        }(e, t, n(r), {})
    }
}
var to = Object.prototype.toString,
    no = Zi((function(t, n, e) {
        null != n && "function" != typeof n.toString && (n = to.call(n)), t[n] = e
    }), S(e)),
    eo = Object.prototype,
    ro = eo.hasOwnProperty,
    io = eo.toString,
    oo = Zi((function(t, n, e) {
        null != n && "function" != typeof n.toString && (n = io.call(n)), ro.call(t, n) ? t[n].push(e) : t[n] = [e]
    }), kn);

function ao(t, n) {
    return n.length < 2 ? t : Mn(t, W(n, 0, -1))
}

function uo(t, n, e) {
    var r = null == (t = ao(t, n = Rn(n, t))) ? t : t[xn(Bt(n))];
    return null == r ? void 0 : p(r, t, e)
}
var so = x(uo),
    lo = x((function(t, n, e) {
        var r = -1,
            i = "function" == typeof n,
            o = d(t) ? Array(t.length) : [];
        return Wn(t, (function(t) {
            o[++r] = i ? p(n, t, e) : uo(t, n, e)
        })), o
    }));
var co = C && C.isArrayBuffer,
    fo = co ? A(co) : function(t) {
        return u(t) && "[object ArrayBuffer]" == b(t)
    };
var po = C && C.isDate,
    ho = po ? A(po) : function(t) {
        return u(t) && "[object Date]" == b(t)
    };
var vo = o.isFinite;

function _o(t) {
    return "number" == typeof t || u(t) && "[object Number]" == b(t)
}
var go = D ? L : P;
var yo = C && C.isRegExp,
    mo = yo ? A(yo) : function(t) {
        return u(t) && "[object RegExp]" == b(t)
    };

function bo(t) {
    return void 0 === t
}
var wo = Array.prototype.join;
var xo = Ot((function(t, n, e) {
    k(t, e, n)
}));
var ko = Math.max,
    jo = Math.min;
var Eo = Hn((function(t, n, e) {
        return t + (e ? " " : "") + n.toLowerCase()
    })),
    Wo = z("toLowerCase"),
    Io = Ri(Rt),
    Oo = Ri((function(t, n) {
        return t <= n
    }));

function Bo(t) {
    return t && t.length ? Lt(t, e, Mi) : void 0
}

function Ao(t, n) {
    for (var e, r = -1, i = t.length; ++r < i;) {
        var o = n(t[r]);
        void 0 !== o && (e = void 0 === e ? o : e + o)
    }
    return e
}

function Mo(t, n) {
    var e = null == t ? 0 : t.length;
    return e ? Ao(t, n) / e : NaN
}
var Ro = x((function(t, n) {
        return function(e) {
            return uo(e, t, n)
        }
    })),
    Lo = x((function(t, n) {
        return function(e) {
            return uo(t, e, n)
        }
    }));

function So(t) {
    return t && t.length ? Lt(t, e, Rt) : void 0
}

function Co(t, n, e) {
    var r = vn(n),
        o = Ai(n, r),
        u = !(i(e) && "chain" in e && !e.chain),
        s = L(t);
    return an(o, (function(e) {
        var r = n[e];
        t[e] = r, s && (t.prototype[e] = function() {
            var n = this.__chain__;
            if (u || n) {
                var e = t(this.__wrapped__);
                return (e.__actions__ = a(this.__actions__)).push({
                    func: r,
                    args: arguments,
                    thisArg: t
                }), e.__chain__ = n, e
            }
            return r.apply(t, dn([this.value()], arguments))
        })
    })), t
}
var Po = de((function(t, n) {
    return t * n
}), 1);

function Do(t) {
    if ("function" != typeof t) throw new TypeError("Expected a function");
    return function() {
        var n = arguments;
        switch (n.length) {
            case 0:
                return !t.call(this);
            case 1:
                return !t.call(this, n[0]);
            case 2:
                return !t.call(this, n[0], n[1]);
            case 3:
                return !t.call(this, n[0], n[1], n[2])
        }
        return !t.apply(this, n)
    }
}
var To = q ? q.iterator : void 0;

function zo(t) {
    if (!t) return [];
    if (d(t)) return $i(t) ? $(t) : a(t);
    if (To && t[To]) return function(t) {
        for (var n, e = []; !(n = t.next()).done;) e.push(n.value);
        return e
    }(t[To]());
    var n = _n(t);
    return ("[object Map]" == n ? In : "[object Set]" == n ? Tn : Ni)(t)
}

function $o(t, n) {
    var e = t.length;
    if (e) return f(n += n < 0 ? e : 0, e) ? t[n] : void 0
}

function qo(t, n) {
    return null == (t = ao(t, n = Rn(n, t))) || delete t[xn(Bt(n))]
}

function No(t) {
    return w(t) ? void 0 : t
}
var Fo = cr((function(t, n) {
    var e = {};
    if (null == t) return e;
    var r = !1;
    n = I(n, (function(n) {
        return n = Rn(n, t), r || (r = n.length > 1), n
    })), _(t, ln(t), e), r && (e = un(e, 7, No));
    for (var i = n.length; i--;) qo(e, n[i]);
    return e
}));

function Uo(t, n, e) {
    for (var r = -1, i = n.length, o = {}; ++r < i;) {
        var a = n[r],
            u = Mn(t, a);
        e(u, a) && ie(o, Rn(a, t), u)
    }
    return o
}

function Ko(t, n) {
    if (null == t) return {};
    var e = I(ln(t), (function(t) {
        return [t]
    }));
    return n = kn(n), Uo(t, e, (function(t, e) {
        return n(t, e[0])
    }))
}

function Ho(t) {
    return cr((function(n) {
        return n = I(n, A(kn)), x((function(e) {
            var r = this;
            return t(n, (function(t) {
                return p(t, r, e)
            }))
        }))
    }))
}
var Vo = Ho(I),
    Jo = x,
    Go = Math.min,
    Yo = Jo((function(t, n) {
        var e = (n = 1 == n.length && s(n[0]) ? I(n[0], A(kn)) : I(It(n, 1), A(kn))).length;
        return x((function(r) {
            for (var i = -1, o = Go(r.length, e); ++i < o;) r[i] = n[i].call(this, r[i]);
            return p(t, this, r)
        }))
    })),
    Qo = Ho(ai),
    Xo = Ho(zn),
    Zo = Math.floor;

function ta(t, n) {
    var e = "";
    if (!t || n < 1 || n > 9007199254740991) return e;
    do {
        n % 2 && (e += t), (n = Zo(n / 2)) && (t += t)
    } while (n);
    return e
}
var na = $n("length"),
    ea = "\\ud800-\\udfff",
    ra = "[" + ea + "]",
    ia = "[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",
    oa = "\\ud83c[\\udffb-\\udfff]",
    aa = "[^" + ea + "]",
    ua = "(?:\\ud83c[\\udde6-\\uddff]){2}",
    sa = "[\\ud800-\\udbff][\\udc00-\\udfff]",
    la = "(?:" + ia + "|" + oa + ")" + "?",
    ca = "[\\ufe0e\\ufe0f]?",
    fa = ca + la + ("(?:\\u200d(?:" + [aa, ua, sa].join("|") + ")" + ca + la + ")*"),
    pa = "(?:" + [aa + ia + "?", ia, ua, sa, ra].join("|") + ")",
    ha = RegExp(oa + "(?=" + oa + ")|" + pa + fa, "g");

function va(t) {
    return N(t) ? function(t) {
        for (var n = ha.lastIndex = 0; ha.test(t);) ++n;
        return n
    }(t) : na(t)
}
var da = Math.ceil;

function _a(t, e) {
    var r = (e = void 0 === e ? " " : n(e)).length;
    if (r < 2) return r ? ta(e, t) : e;
    var i = ta(e, da(t / va(e)));
    return N(e) ? F($(i), 0, t).join("") : i.slice(0, t)
}
var ga = Math.ceil,
    ya = Math.floor;
var ma = /^\s+/,
    ba = o.parseInt;
var wa = x((function(t, n) {
    return nr(t, 32, void 0, n, Ge(n, He(wa)))
}));
wa.placeholder = {};
var xa = x((function(t, n) {
    return nr(t, 64, void 0, n, Ge(n, He(xa)))
}));
xa.placeholder = {};
var ka = cr((function(t, n) {
    return null == t ? {} : function(t, n) {
        return Uo(t, n, (function(n, e) {
            return qn(t, e)
        }))
    }(t, n)
}));

function ja(t, n, e, r) {
    for (var i = e - 1, o = t.length; ++i < o;)
        if (r(t[i], n)) return i;
    return -1
}
var Ea = Array.prototype.splice;

function Wa(t, n, e, r) {
    var i = r ? ja : qe,
        o = -1,
        u = n.length,
        s = t;
    for (t === n && (n = a(n)), e && (s = I(t, A(e))); ++o < u;)
        for (var l = 0, c = n[o], f = e ? e(c) : c;
            (l = i(s, f, l, r)) > -1;) s !== t && Ea.call(s, l, 1), Ea.call(t, l, 1);
    return t
}

function Ia(t, n) {
    return t && t.length && n && n.length ? Wa(t, n) : t
}
var Oa = x(Ia);
var Ba = Array.prototype.splice;

function Aa(t, n) {
    for (var e = t ? n.length : 0, r = e - 1; e--;) {
        var i = n[e];
        if (e == r || i !== o) {
            var o = i;
            f(i) ? Ba.call(t, i, 1) : qo(t, i)
        }
    }
    return t
}
var Ma = cr((function(t, n) {
        var e = null == t ? 0 : t.length,
            r = sr(t, n);
        return Aa(t, I(n, (function(t) {
            return f(t, e) ? +t : t
        })).sort(Ct)), r
    })),
    Ra = Math.floor,
    La = Math.random;

function Sa(t, n) {
    return t + Ra(La() * (n - t + 1))
}
var Ca = parseFloat,
    Pa = Math.min,
    Da = Math.random;
var Ta = Pt(!0),
    za = cr((function(t, n) {
        return nr(t, 256, void 0, void 0, void 0, n)
    }));

function $a(t, n, e, r, i) {
    return i(t, (function(t, i, o) {
        e = r ? (r = !1, t) : n(e, t, i, o)
    })), e
}

function qa(t, n, e) {
    var r = s(t) ? Vn : $a,
        i = arguments.length < 3;
    return r(t, kn(n), e, i, Wn)
}

function Na(t, n, e, r) {
    var i = null == t ? 0 : t.length;
    for (r && i && (e = t[--i]); i--;) e = n(e, t[i], i, t);
    return e
}

function Fa(t, n) {
    return (s(t) ? gn : ci)(t, Do(kn(n)))
}
var Ua = Array.prototype.reverse;

function Ka(t) {
    return null == t ? t : Ua.call(t)
}
var Ha = br("round");

function Va(t) {
    var n = t.length;
    return n ? t[Sa(0, n - 1)] : void 0
}

function Ja(t) {
    return Va(Ni(t))
}

function Ga(t, n) {
    var e = -1,
        r = t.length,
        i = r - 1;
    for (n = void 0 === n ? r : n; ++e < n;) {
        var o = Sa(e, i),
            a = t[o];
        t[o] = t[e], t[e] = a
    }
    return t.length = n, t
}

function Ya(t, n) {
    return Ga(a(t), Er(n, 0, t.length))
}

function Qa(t, n) {
    var e = Ni(t);
    return Ga(e, Er(n, 0, e.length))
}

function Xa(t) {
    return Ga(a(t))
}

function Za(t) {
    return Ga(Ni(t))
}
var tu = Hn((function(t, n, e) {
    return t + (e ? "_" : "") + n.toLowerCase()
}));

function nu(t, n) {
    var e;
    return Wn(t, (function(t, r, i) {
        return !(e = n(t, r, i))
    })), !!e
}

function eu(t, n, e) {
    var r = s(t) ? zn : nu;
    return e && E(t, n, e) && (n = void 0), r(t, kn(n))
}
var ru = Math.floor,
    iu = Math.min;

function ou(n, e, r, i) {
    var o = 0,
        a = null == n ? 0 : n.length;
    if (0 === a) return 0;
    for (var u = (e = r(e)) != e, s = null === e, l = t(e), c = void 0 === e; o < a;) {
        var f = ru((o + a) / 2),
            p = r(n[f]),
            h = void 0 !== p,
            v = null === p,
            d = p == p,
            _ = t(p);
        if (u) var g = i || d;
        else g = c ? d && (i || h) : s ? d && h && (i || !v) : l ? d && h && !v && (i || !_) : !v && !_ && (i ? p <= e : p < e);
        g ? o = f + 1 : a = f
    }
    return iu(a, 4294967294)
}

function au(n, r, i) {
    var o = 0,
        a = null == n ? o : n.length;
    if ("number" == typeof r && r == r && a <= 2147483647) {
        for (; o < a;) {
            var u = o + a >>> 1,
                s = n[u];
            null !== s && !t(s) && (i ? s <= r : s < r) ? o = u + 1 : a = u
        }
        return a
    }
    return ou(n, r, e, i)
}

function uu(t, n) {
    for (var e = -1, r = t.length, i = 0, o = []; ++e < r;) {
        var a = t[e],
            u = n ? n(a) : a;
        if (!e || !O(u, s)) {
            var s = u;
            o[i++] = 0 === a ? 0 : a
        }
    }
    return o
}
var su = Math.max;
var lu = Hn((function(t, n, e) {
    return t + (e ? " " : "") + U(n)
}));
var cu = de((function(t, n) {
    return t - n
}), 0);
var fu = Object.prototype,
    pu = fu.hasOwnProperty;

function hu(t, n, e, r) {
    return void 0 === t || O(t, fu[e]) && !pu.call(r, e) ? n : t
}
var vu = {
    "\\": "\\",
    "'": "'",
    "\n": "n",
    "\r": "r",
    "\u2028": "u2028",
    "\u2029": "u2029"
};

function du(t) {
    return "\\" + vu[t]
}
var _u = /<%=([\s\S]+?)%>/g,
    gu = {
        escape: /<%-([\s\S]+?)%>/g,
        evaluate: /<%([\s\S]+?)%>/g,
        interpolate: _u,
        variable: "",
        imports: {
            _: {
                escape: ri
            }
        }
    },
    yu = /\b__p \+= '';/g,
    mu = /\b(__p \+=) '' \+/g,
    bu = /(__e\(.*?\)|\b__t\)) \+\n'';/g,
    wu = /[()=,{}\[\]\/\s]/,
    xu = /\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,
    ku = /($^)/,
    ju = /['\n\r\u2028\u2029\\]/g,
    Eu = Object.prototype.hasOwnProperty;

function Wu(t, n) {
    return n(t)
}

function Iu(t, n) {
    var e = t;
    return e instanceof Ee && (e = e.value()), Vn(n, (function(t, n) {
        return n.func.apply(n.thisArg, dn([t], n.args))
    }), e)
}

function Ou(t, n) {
    for (var e = t.length; e-- && qe(n, t[e], 0) > -1;);
    return e
}

function Bu(t, n) {
    for (var e = -1, r = t.length; ++e < r && qe(n, t[e], 0) > -1;);
    return e
}
var Au = /^\s+/;
var Mu = /\w*$/;
var Ru = Kn({
        "&amp;": "&",
        "&lt;": "<",
        "&gt;": ">",
        "&quot;": '"',
        "&#39;": "'"
    }),
    Lu = /&(?:amp|lt|gt|quot|#39);/g,
    Su = RegExp(Lu.source);
var Cu = mn && 1 / Tn(new mn([, -0]))[1] == 1 / 0 ? function(t) {
    return new mn(t)
} : Wt;

function Pu(t, n, e) {
    var r = -1,
        i = Ne,
        o = t.length,
        a = !0,
        u = [],
        s = u;
    if (e) a = !1, i = qr;
    else if (o >= 200) {
        var l = n ? null : Cu(t);
        if (l) return Tn(l);
        a = !1, i = En, s = new jn
    } else s = n ? [] : u;
    t: for (; ++r < o;) {
        var c = t[r],
            f = n ? n(c) : c;
        if (c = e || 0 !== c ? c : 0, a && f == f) {
            for (var p = s.length; p--;)
                if (s[p] === f) continue t;
            n && s.push(f), u.push(c)
        } else i(s, f, e) || (s !== u && s.push(f), u.push(c))
    }
    return u
}
var Du = x((function(t) {
        return Pu(It(t, 1, M, !0))
    })),
    Tu = x((function(t) {
        var n = Bt(t);
        return M(n) && (n = void 0), Pu(It(t, 1, M, !0), kn(n))
    })),
    zu = x((function(t) {
        var n = Bt(t);
        return n = "function" == typeof n ? n : void 0, Pu(It(t, 1, M, !0), void 0, n)
    }));

function $u(t) {
    return t && t.length ? Pu(t) : []
}

function qu(t, n) {
    return t && t.length ? Pu(t, kn(n)) : []
}
var Nu = Math.max;

function Fu(t) {
    if (!t || !t.length) return [];
    var n = 0;
    return t = gn(t, (function(t) {
        if (M(t)) return n = Nu(t.length, n), !0
    })), J(n, (function(n) {
        return I(t, $n(n))
    }))
}

function Uu(t, n) {
    if (!t || !t.length) return [];
    var e = Fu(t);
    return null == n ? e : I(e, (function(t) {
        return p(n, void 0, t)
    }))
}
var Ku = Hn((function(t, n, e) {
    return t + (e ? " " : "") + n.toUpperCase()
}));
var Hu = x((function(t, n) {
    return M(t) ? Nr(t, n) : []
}));
var Vu = cr((function(t) {
    var n = t.length,
        e = n ? t[0] : 0,
        r = this.__wrapped__,
        i = function(n) {
            return sr(n, t)
        };
    return !(n > 1 || this.__actions__.length) && r instanceof Ee && f(e) ? ((r = r.slice(e, +e + (n ? 1 : 0))).__actions__.push({
        func: Wu,
        args: [i],
        thisArg: void 0
    }), new Ae(r, this.__chain__).thru((function(t) {
        return n && !t.length && t.push(void 0), t
    }))) : this.thru(i)
}));

function Ju(t, n, e) {
    var r = t.length;
    if (r < 2) return r ? Pu(t[0]) : [];
    for (var i = -1, o = Array(r); ++i < r;)
        for (var a = t[i], u = -1; ++u < r;) u != i && (o[i] = Nr(o[i] || a, t[u], n, e));
    return Pu(It(o, 1), n, e)
}
var Gu = x((function(t) {
        return Ju(gn(t, M))
    })),
    Yu = x((function(t) {
        var n = Bt(t);
        return M(n) && (n = void 0), Ju(gn(t, M), kn(n))
    })),
    Qu = x((function(t) {
        var n = Bt(t);
        return n = "function" == typeof n ? n : void 0, Ju(gn(t, M), void 0, n)
    })),
    Xu = x(Fu);

function Zu(t, n, e) {
    for (var r = -1, i = t.length, o = n.length, a = {}; ++r < i;) {
        var u = r < o ? n[r] : void 0;
        e(a, t[r], u)
    }
    return a
}

function ts(t, n) {
    return Zu(t || [], n || [], g)
}
var ns = x((function(t) {
    var n = t.length,
        e = n > 1 ? t[n - 1] : void 0;
    return e = "function" == typeof e ? (t.pop(), e) : void 0, Uu(t, e)
}));
const es = {
        chunk: function(t, n, e) {
            n = (e ? E(t, n, e) : void 0 === n) ? 1 : jr(Yn(n), 0);
            var r = null == t ? 0 : t.length;
            if (!r || n < 1) return [];
            for (var i = 0, o = 0, a = Array(kr(r / n)); i < r;) a[o++] = W(t, i, i += n);
            return a
        },
        compact: Ir,
        concat: function() {
            var t = arguments.length;
            if (!t) return [];
            for (var n = Array(t - 1), e = arguments[0], r = t; r--;) n[r - 1] = arguments[r];
            return dn(s(e) ? a(e) : [e], It(n, 1))
        },
        difference: Fr,
        differenceBy: Ur,
        differenceWith: Kr,
        drop: Vr,
        dropRight: Jr,
        dropRightWhile: function(t, n) {
            return t && t.length ? Gr(t, kn(n), !0, !0) : []
        },
        dropWhile: function(t, n) {
            return t && t.length ? Gr(t, kn(n), !0) : []
        },
        fill: function(t, n, e, r) {
            var i = null == t ? 0 : t.length;
            return i ? (e && "number" != typeof e && E(t, n, e) && (e = 0, r = i), function(t, n, e, r) {
                var i = t.length;
                for ((e = Yn(e)) < 0 && (e = -e > i ? 0 : i + e), (r = void 0 === r || r > i ? i : Yn(r)) < 0 && (r += i), r = e > r ? 0 : li(r); e < r;) t[e++] = n;
                return t
            }(t, n, e, r)) : []
        },
        findIndex: vi,
        findLastIndex: mi,
        flatten: lr,
        flattenDeep: function(t) {
            return (null == t ? 0 : t.length) ? It(t, ki) : []
        },
        flattenDepth: function(t, n) {
            return (null == t ? 0 : t.length) ? (n = void 0 === n ? 1 : Yn(n), It(t, n)) : []
        },
        fromPairs: function(t) {
            for (var n = -1, e = null == t ? 0 : t.length, r = {}; ++n < e;) {
                var i = t[n];
                r[i[0]] = i[1]
            }
            return r
        },
        head: zt,
        indexOf: Hi,
        initial: Tt,
        intersection: Yi,
        intersectionBy: Qi,
        intersectionWith: Xi,
        join: function(t, n) {
            return null == t ? "" : wo.call(t, n)
        },
        lastIndexOf: function(t, n, e) {
            var r = null == t ? 0 : t.length;
            if (!r) return -1;
            var i = r;
            return void 0 !== e && (i = (i = Yn(e)) < 0 ? ko(r + i, 0) : jo(i, r - 1)), n == n ? function(t, n, e) {
                for (var r = e + 1; r--;)
                    if (t[r] === n) return r;
                return r
            }(t, n, i) : ze(t, $e, i, !0)
        },
        nth: function(t, n) {
            return t && t.length ? $o(t, Yn(n)) : void 0
        },
        pull: Oa,
        pullAll: Ia,
        pullAllBy: function(t, n, e) {
            return t && t.length && n && n.length ? Wa(t, n, kn(e)) : t
        },
        pullAllWith: function(t, n, e) {
            return t && t.length && n && n.length ? Wa(t, n, void 0, e) : t
        },
        pullAt: Ma,
        remove: function(t, n) {
            var e = [];
            if (!t || !t.length) return e;
            var r = -1,
                i = [],
                o = t.length;
            for (n = kn(n); ++r < o;) {
                var a = t[r];
                n(a, r, t) && (e.push(a), i.push(r))
            }
            return Aa(t, i), e
        },
        reverse: Ka,
        slice: function(t, n, e) {
            var r = null == t ? 0 : t.length;
            return r ? (e && "number" != typeof e && E(t, n, e) ? (n = 0, e = r) : (n = null == n ? 0 : Yn(n), e = void 0 === e ? r : Yn(e)), W(t, n, e)) : []
        },
        sortedIndex: function(t, n) {
            return au(t, n)
        },
        sortedIndexBy: function(t, n, e) {
            return ou(t, n, kn(e))
        },
        sortedIndexOf: function(t, n) {
            var e = null == t ? 0 : t.length;
            if (e) {
                var r = au(t, n);
                if (r < e && O(t[r], n)) return r
            }
            return -1
        },
        sortedLastIndex: function(t, n) {
            return au(t, n, !0)
        },
        sortedLastIndexBy: function(t, n, e) {
            return ou(t, n, kn(e), !0)
        },
        sortedLastIndexOf: function(t, n) {
            if (null == t ? 0 : t.length) {
                var e = au(t, n, !0) - 1;
                if (O(t[e], n)) return e
            }
            return -1
        },
        sortedUniq: function(t) {
            return t && t.length ? uu(t) : []
        },
        sortedUniqBy: function(t, n) {
            return t && t.length ? uu(t, kn(n)) : []
        },
        tail: function(t) {
            var n = null == t ? 0 : t.length;
            return n ? W(t, 1, n) : []
        },
        take: function(t, n, e) {
            return t && t.length ? (n = e || void 0 === n ? 1 : Yn(n), W(t, 0, n < 0 ? 0 : n)) : []
        },
        takeRight: function(t, n, e) {
            var r = null == t ? 0 : t.length;
            return r ? (n = e || void 0 === n ? 1 : Yn(n), W(t, (n = r - n) < 0 ? 0 : n, r)) : []
        },
        takeRightWhile: function(t, n) {
            return t && t.length ? Gr(t, kn(n), !1, !0) : []
        },
        takeWhile: function(t, n) {
            return t && t.length ? Gr(t, kn(n)) : []
        },
        union: Du,
        unionBy: Tu,
        unionWith: zu,
        uniq: $u,
        uniqBy: qu,
        uniqWith: function(t, n) {
            return n = "function" == typeof n ? n : void 0, t && t.length ? Pu(t, void 0, n) : []
        },
        unzip: Fu,
        unzipWith: Uu,
        without: Hu,
        xor: Gu,
        xorBy: Yu,
        xorWith: Qu,
        zip: Xu,
        zipObject: ts,
        zipObjectDeep: function(t, n) {
            return Zu(t || [], n || [], ie)
        },
        zipWith: ns
    },
    rs = {
        countBy: Ar,
        every: si,
        filter: fi,
        find: di,
        findLast: bi,
        flatMap: wi,
        flatMapDeep: function(t, n) {
            return It(Bn(t, n), xi)
        },
        flatMapDepth: function(t, n, e) {
            return e = void 0 === e ? 1 : Yn(e), It(Bn(t, n), e)
        },
        forEach: Yr,
        forEachRight: Ft,
        groupBy: $t,
        includes: Ui,
        invokeMap: lo,
        keyBy: xo,
        map: Bn,
        orderBy: function(t, n, e, r) {
            return null == t ? [] : (s(n) || (n = null == n ? [] : [n]), s(e = r ? void 0 : e) || (e = null == e ? [] : [e]), St(t, n, e))
        },
        partition: qt,
        reduce: qa,
        reduceRight: function(t, n, e) {
            var r = s(t) ? Na : $a,
                i = arguments.length < 3;
            return r(t, kn(n), e, i, Dt)
        },
        reject: Fa,
        sample: function(t) {
            return (s(t) ? Va : Ja)(t)
        },
        sampleSize: function(t, n, e) {
            return n = (e ? E(t, n, e) : void 0 === n) ? 1 : Yn(n), (s(t) ? Ya : Qa)(t, n)
        },
        shuffle: function(t) {
            return (s(t) ? Xa : Za)(t)
        },
        size: function(t) {
            if (null == t) return 0;
            if (d(t)) return $i(t) ? va(t) : t.length;
            var n = _n(t);
            return "[object Map]" == n || "[object Set]" == n ? t.size : yn(t).length
        },
        some: eu,
        sortBy: Nt
    },
    is = {
        now: Xn
    },
    os = {
        after: function(t, n) {
            if ("function" != typeof n) throw new TypeError("Expected a function");
            return t = Yn(t),
                function() {
                    if (--t < 1) return n.apply(this, arguments)
                }
        },
        ary: er,
        before: vr,
        bind: dr,
        bindKey: gr,
        curry: Mr,
        curryRight: Rr,
        debounce: Zn,
        defer: zr,
        delay: $r,
        flip: function(t) {
            return nr(t, 512)
        },
        memoize: Fn,
        once: function(t) {
            return vr(2, t)
        },
        overArgs: Yo,
        partial: wa,
        partialRight: xa,
        rearg: za,
        rest: function(t, n) {
            if ("function" != typeof t) throw new TypeError("Expected a function");
            return n = void 0 === n ? n : Yn(n), x(t, n)
        },
        spread: function(t, n) {
            if ("function" != typeof t) throw new TypeError("Expected a function");
            return n = null == n ? 0 : su(Yn(n), 0), x((function(e) {
                var r = e[n],
                    i = F(e, 0, n);
                return r && dn(i, r), p(t, this, i)
            }))
        },
        throttle: Ut,
        unary: function(t) {
            return er(t, 1)
        },
        wrap: function(t, n) {
            return wa(re(n), t)
        }
    },
    as = {
        castArray: function() {
            if (!arguments.length) return [];
            var t = arguments[0];
            return s(t) ? t : [t]
        },
        clone: Wr,
        cloneDeep: cn,
        cloneDeepWith: Kt,
        cloneWith: function(t, n) {
            return un(t, 4, n = "function" == typeof n ? n : void 0)
        },
        conformsTo: function(t, n) {
            return null == n || Or(t, n, vn(n))
        },
        eq: O,
        gt: Li,
        gte: Si,
        isArguments: Y,
        isArrayBuffer: fo,
        isArrayLike: d,
        isArrayLikeObject: M,
        isBoolean: function(t) {
            return !0 === t || !1 === t || u(t) && "[object Boolean]" == b(t)
        },
        isBuffer: K,
        isDate: ho,
        isElement: function(t) {
            return u(t) && 1 === t.nodeType && !w(t)
        },
        isEmpty: Ht,
        isEqual: Vt,
        isEqualWith: function(t, n, e) {
            var r = (e = "function" == typeof e ? e : void 0) ? e(t, n) : void 0;
            return void 0 === r ? Ln(t, n, void 0, e) : !!r
        },
        isError: pr,
        isFinite: function(t) {
            return "number" == typeof t && vo(t)
        },
        isFunction: L,
        isInteger: se,
        isLength: Q,
        isMap: fn,
        isMatch: function(t, n) {
            return t === n || Sn(t, n, Cn(n))
        },
        isMatchWith: function(t, n, e) {
            return e = "function" == typeof e ? e : void 0, Sn(t, n, Cn(n), e)
        },
        isNaN: function(t) {
            return _o(t) && t != +t
        },
        isNative: function(t) {
            if (go(t)) throw new Error("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");
            return T(t)
        },
        isNil: function(t) {
            return null == t
        },
        isNull: function(t) {
            return null === t
        },
        isNumber: _o,
        isObjectLike: u,
        isPlainObject: w,
        isRegExp: mo,
        isSafeInteger: function(t) {
            return se(t) && t >= -9007199254740991 && t <= 9007199254740991
        },
        isSet: pn,
        isString: $i,
        isSymbol: t,
        isTypedArray: H,
        isUndefined: bo,
        isWeakMap: function(t) {
            return u(t) && "[object WeakMap]" == _n(t)
        },
        isWeakSet: function(t) {
            return u(t) && "[object WeakSet]" == b(t)
        },
        lt: Io,
        lte: Oo,
        toArray: zo,
        toFinite: Qn,
        toLength: li,
        toNumber: te,
        toPlainObject: G,
        toSafeInteger: function(t) {
            return t ? Er(Yn(t), -9007199254740991, 9007199254740991) : 0 === t ? t : 0
        },
        toString: j
    },
    us = {
        add: _e,
        ceil: wr,
        divide: Hr,
        floor: ji,
        max: Bo,
        maxBy: function(t, n) {
            return t && t.length ? Lt(t, kn(n), Mi) : void 0
        },
        mean: function(t) {
            return Mo(t, e)
        },
        meanBy: function(t, n) {
            return Mo(t, kn(n))
        },
        min: So,
        minBy: Jt,
        multiply: Po,
        round: Ha,
        subtract: cu,
        sum: function(t) {
            return t && t.length ? Ao(t, e) : 0
        },
        sumBy: function(t, n) {
            return t && t.length ? Ao(t, kn(n)) : 0
        }
    },
    ss = function(t, n, e) {
        return void 0 === e && (e = n, n = void 0), void 0 !== e && (e = (e = te(e)) == e ? e : 0), void 0 !== n && (n = (n = te(n)) == n ? n : 0), Er(te(t), n, e)
    },
    ls = function(t, n, e) {
        return n = Qn(n), void 0 === e ? (e = n, n = 0) : e = Qn(e),
            function(t, n, e) {
                return t >= zi(n, e) && t < Ti(n, e)
            }(t = te(t), n, e)
    },
    cs = function(t, n, e) {
        if (e && "boolean" != typeof e && E(t, n, e) && (n = e = void 0), void 0 === e && ("boolean" == typeof n ? (e = n, n = void 0) : "boolean" == typeof t && (e = t, t = void 0)), void 0 === t && void 0 === n ? (t = 0, n = 1) : (t = Qn(t), void 0 === n ? (n = t, t = 0) : n = Qn(n)), t > n) {
            var r = t;
            t = n, n = r
        }
        if (e || t % 1 || n % 1) {
            var i = Da();
            return Pa(t + i * (n - t + Ca("1e-" + ((i + "").length - 1))), n)
        }
        return Sa(t, n)
    },
    fs = {
        assign: ir,
        assignIn: or,
        assignInWith: ar,
        assignWith: ur,
        at: fr,
        create: function(t, n) {
            var e = r(t);
            return null == n ? e : sn(e, n)
        },
        defaults: Cr,
        defaultsDeep: Dr,
        findKey: function(t, n) {
            return _i(t, kn(n), On)
        },
        findLastKey: function(t, n) {
            return _i(t, kn(n), At)
        },
        forIn: Oi,
        forInRight: function(t, n) {
            return null == t ? t : Mt(t, re(n), y)
        },
        forOwn: Bi,
        forOwnRight: function(t, n) {
            return t && At(t, re(n))
        },
        functions: function(t) {
            return null == t ? [] : Ai(t, vn(t))
        },
        functionsIn: function(t) {
            return null == t ? [] : Ai(t, y(t))
        },
        get: wn,
        has: Di,
        hasIn: qn,
        invert: no,
        invertBy: oo,
        invoke: so,
        keysIn: y,
        mapKeys: function(t, n) {
            var e = {};
            return n = kn(n), On(t, (function(t, r, i) {
                k(e, n(t, r, i), t)
            })), e
        },
        mapValues: Gt,
        merge: X,
        mergeWith: ue,
        omit: Fo,
        omitBy: function(t, n) {
            return Ko(t, Do(kn(n)))
        },
        pick: ka,
        pickBy: Ko,
        result: function(t, n, e) {
            var r = -1,
                i = (n = Rn(n, t)).length;
            for (i || (i = 1, t = void 0); ++r < i;) {
                var o = null == t ? void 0 : t[xn(n[r])];
                void 0 === o && (r = i, o = e), t = L(o) ? o.call(t) : o
            }
            return t
        },
        set: function(t, n, e) {
            return null == t ? t : ie(t, n, e)
        },
        setWith: function(t, n, e, r) {
            return r = "function" == typeof r ? r : void 0, null == t ? t : ie(t, n, e, r)
        },
        toPairs: Xr,
        toPairsIn: Zr,
        transform: function(t, n, e) {
            var o = s(t),
                a = o || K(t) || H(t);
            if (n = kn(n), null == e) {
                var u = t && t.constructor;
                e = a ? o ? new u : [] : i(t) && L(u) ? r(V(t)) : {}
            }
            return (a ? an : On)(t, (function(t, r, i) {
                return n(e, t, r, i)
            })), e
        },
        unset: function(t, n) {
            return null == t || qo(t, n)
        },
        update: ae,
        updateWith: function(t, n, e, r) {
            return r = "function" == typeof r ? r : void 0, null == t ? t : oe(t, n, re(e), r)
        },
        values: Ni,
        valuesIn: function(t) {
            return null == t ? [] : qi(t, y(t))
        }
    },
    ps = {
        at: Vu,
        chain: xr,
        commit: function() {
            return new Ae(this.value(), this.__chain__)
        },
        next: function() {
            void 0 === this.__values__ && (this.__values__ = zo(this.value()));
            var t = this.__index__ >= this.__values__.length;
            return {
                done: t,
                value: t ? void 0 : this.__values__[this.__index__++]
            }
        },
        plant: function(t) {
            for (var n, e = this; e instanceof je;) {
                var r = Me(e);
                r.__index__ = 0, r.__values__ = void 0, n ? i.__wrapped__ = r : n = r;
                var i = r;
                e = e.__wrapped__
            }
            return i.__wrapped__ = t, n
        },
        reverse: function() {
            var t = this.__wrapped__;
            if (t instanceof Ee) {
                var n = t;
                return this.__actions__.length && (n = new Ee(this)), (n = n.reverse()).__actions__.push({
                    func: Wu,
                    args: [Ka],
                    thisArg: void 0
                }), new Ae(n, this.__chain__)
            }
            return this.thru(Ka)
        },
        tap: function(t, n) {
            return n(t), t
        },
        toIterator: function() {
            return this
        },
        value: function() {
            return Iu(this.__wrapped__, this.__actions__)
        },
        wrapperChain: function() {
            return xr(this)
        }
    },
    hs = {
        camelCase: le,
        capitalize: ce,
        deburr: Gn,
        endsWith: function(t, e, r) {
            t = j(t), e = n(e);
            var i = t.length,
                o = r = void 0 === r ? i : Er(Yn(r), 0, i);
            return (r -= e.length) >= 0 && t.slice(r, o) == e
        },
        escape: ri,
        escapeRegExp: function(t) {
            return (t = j(t)) && oi.test(t) ? t.replace(ii, "\\$&") : t
        },
        kebabCase: fe,
        lowerCase: Eo,
        lowerFirst: Wo,
        pad: function(t, n, e) {
            t = j(t);
            var r = (n = Yn(n)) ? va(t) : 0;
            if (!n || r >= n) return t;
            var i = (n - r) / 2;
            return _a(ya(i), e) + t + _a(ga(i), e)
        },
        padEnd: function(t, n, e) {
            t = j(t);
            var r = (n = Yn(n)) ? va(t) : 0;
            return n && r < n ? t + _a(n - r, e) : t
        },
        padStart: function(t, n, e) {
            t = j(t);
            var r = (n = Yn(n)) ? va(t) : 0;
            return n && r < n ? _a(n - r, e) + t : t
        },
        parseInt: function(t, n, e) {
            return e || null == n ? n = 0 : n && (n = +n), ba(j(t).replace(ma, ""), n || 0)
        },
        repeat: function(t, n, e) {
            return n = (e ? E(t, n, e) : void 0 === n) ? 1 : Yn(n), ta(j(t), n)
        },
        replace: function() {
            var t = arguments,
                n = j(t[0]);
            return t.length < 3 ? n : n.replace(t[1], t[2])
        },
        snakeCase: tu,
        split: function(t, e, r) {
            return r && "number" != typeof r && E(t, e, r) && (e = r = void 0), (r = void 0 === r ? 4294967295 : r >>> 0) ? (t = j(t)) && ("string" == typeof e || null != e && !mo(e)) && !(e = n(e)) && N(t) ? F($(t), 0, r) : t.split(e, r) : []
        },
        startCase: lu,
        startsWith: function(t, e, r) {
            return t = j(t), r = null == r ? 0 : Er(Yn(r), 0, t.length), e = n(e), t.slice(r, r + e.length) == e
        },
        template: function(t, n, e) {
            var r = gu.imports._.templateSettings || gu;
            e && E(t, n, e) && (n = void 0), t = j(t), n = ar({}, n, r, hu);
            var i, o, a = ar({}, n.imports, r.imports, hu),
                u = vn(a),
                s = qi(a, u),
                l = 0,
                c = n.interpolate || ku,
                f = "__p += '",
                p = RegExp((n.escape || ku).source + "|" + c.source + "|" + (c === _u ? xu : ku).source + "|" + (n.evaluate || ku).source + "|$", "g"),
                h = Eu.call(n, "sourceURL") ? "//# sourceURL=" + (n.sourceURL + "").replace(/\s/g, " ") + "\n" : "";
            t.replace(p, (function(n, e, r, a, u, s) {
                return r || (r = a), f += t.slice(l, s).replace(ju, du), e && (i = !0, f += "' +\n__e(" + e + ") +\n'"), u && (o = !0, f += "';\n" + u + ";\n__p += '"), r && (f += "' +\n((__t = (" + r + ")) == null ? '' : __t) +\n'"), l = s + n.length, n
            })), f += "';\n";
            var v = Eu.call(n, "variable") && n.variable;
            if (v) {
                if (wu.test(v)) throw new Error("Invalid `variable` option passed into `_.template`")
            } else f = "with (obj) {\n" + f + "\n}\n";
            f = (o ? f.replace(yu, "") : f).replace(mu, "$1").replace(bu, "$1;"), f = "function(" + (v || "obj") + ") {\n" + (v ? "" : "obj || (obj = {});\n") + "var __t, __p = ''" + (i ? ", __e = _.escape" : "") + (o ? ", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n" : ";\n") + f + "return __p\n}";
            var d = hr((function() {
                return Function(u, h + "return " + f).apply(void 0, s)
            }));
            if (d.source = f, pr(d)) throw d;
            return d
        },
        templateSettings: gu,
        toLower: function(t) {
            return j(t).toLowerCase()
        },
        toUpper: function(t) {
            return j(t).toUpperCase()
        },
        trim: function(t, e, r) {
            if ((t = j(t)) && (r || void 0 === e)) return ne(t);
            if (!t || !(e = n(e))) return t;
            var i = $(t),
                o = $(e),
                a = Bu(i, o),
                u = Ou(i, o) + 1;
            return F(i, a, u).join("")
        },
        trimEnd: function(t, e, r) {
            if ((t = j(t)) && (r || void 0 === e)) return t.slice(0, ee(t) + 1);
            if (!t || !(e = n(e))) return t;
            var i = $(t),
                o = Ou(i, $(e)) + 1;
            return F(i, 0, o).join("")
        },
        trimStart: function(t, e, r) {
            if ((t = j(t)) && (r || void 0 === e)) return t.replace(Au, "");
            if (!t || !(e = n(e))) return t;
            var i = $(t),
                o = Bu(i, $(e));
            return F(i, o).join("")
        },
        truncate: function(t, e) {
            var r = 30,
                o = "...";
            if (i(e)) {
                var a = "separator" in e ? e.separator : a;
                r = "length" in e ? Yn(e.length) : r, o = "omission" in e ? n(e.omission) : o
            }
            var u = (t = j(t)).length;
            if (N(t)) {
                var s = $(t);
                u = s.length
            }
            if (r >= u) return t;
            var l = r - va(o);
            if (l < 1) return o;
            var c = s ? F(s, 0, l).join("") : t.slice(0, l);
            if (void 0 === a) return c + o;
            if (s && (l += c.length - l), mo(a)) {
                if (t.slice(l).search(a)) {
                    var f, p = c;
                    for (a.global || (a = RegExp(a.source, j(Mu.exec(a)) + "g")), a.lastIndex = 0; f = a.exec(p);) var h = f.index;
                    c = c.slice(0, void 0 === h ? l : h)
                }
            } else if (t.indexOf(n(a), l) != l) {
                var v = c.lastIndexOf(a);
                v > -1 && (c = c.slice(0, v))
            }
            return c + o
        },
        unescape: function(t) {
            return (t = j(t)) && Su.test(t) ? t.replace(Lu, Ru) : t
        },
        upperCase: Ku,
        upperFirst: U,
        words: Jn
    },
    vs = {
        attempt: hr,
        bindAll: _r,
        cond: function(t) {
            var n = null == t ? 0 : t.length,
                e = kn;
            return t = n ? I(t, (function(t) {
                if ("function" != typeof t[1]) throw new TypeError("Expected a function");
                return [e(t[0]), t[1]]
            })) : [], x((function(e) {
                for (var r = -1; ++r < n;) {
                    var i = t[r];
                    if (p(i[0], this, e)) return p(i[1], this, e)
                }
            }))
        },
        conforms: function(t) {
            return function(t) {
                var n = vn(t);
                return function(e) {
                    return Or(e, t, n)
                }
            }(un(t, 1))
        },
        constant: S,
        defaultTo: function(t, n) {
            return null == t || t != t ? n : t
        },
        flow: Wi,
        flowRight: Ii,
        iteratee: function(t) {
            return kn("function" == typeof t ? t : un(t, 1))
        },
        matches: function(t) {
            return Pn(un(t, 1))
        },
        matchesProperty: function(t, n) {
            return Dn(t, un(n, 1))
        },
        method: Ro,
        methodOf: Lo,
        noop: Wt,
        nthArg: function(t) {
            return t = Yn(t), x((function(n) {
                return $o(n, t)
            }))
        },
        over: Vo,
        overEvery: Qo,
        overSome: Xo,
        property: Un,
        propertyOf: function(t) {
            return function(n) {
                return null == t ? void 0 : Mn(t, n)
            }
        },
        range: Yt,
        rangeRight: Ta,
        stubArray: bn,
        stubFalse: P,
        stubObject: function() {
            return {}
        },
        stubString: function() {
            return ""
        },
        stubTrue: function() {
            return !0
        },
        times: Qt,
        toPath: function(n) {
            return s(n) ? I(n, xn) : t(n) ? [n] : a(Nn(j(n)))
        },
        uniqueId: Xt
    };
var ds = Math.max,
    _s = Math.min;
var gs = Math.min;
var ys, ms = 4294967295,
    bs = Array.prototype,
    ws = Object.prototype.hasOwnProperty,
    xs = q ? q.iterator : void 0,
    ks = Math.max,
    js = Math.min,
    Es = function(t) {
        return function(n, e, r) {
            if (null == r) {
                var o = i(e),
                    a = o && vn(e),
                    u = a && a.length && Ai(e, a);
                (u ? u.length : o) || (r = e, e = n, n = this)
            }
            return t(n, e, r)
        }
    }(Co);
Le.after = os.after, Le.ary = os.ary, Le.assign = fs.assign, Le.assignIn = fs.assignIn, Le.assignInWith = fs.assignInWith, Le.assignWith = fs.assignWith, Le.at = fs.at, Le.before = os.before, Le.bind = os.bind, Le.bindAll = vs.bindAll, Le.bindKey = os.bindKey, Le.castArray = as.castArray, Le.chain = ps.chain, Le.chunk = es.chunk, Le.compact = es.compact, Le.concat = es.concat, Le.cond = vs.cond, Le.conforms = vs.conforms, Le.constant = vs.constant, Le.countBy = rs.countBy, Le.create = fs.create, Le.curry = os.curry, Le.curryRight = os.curryRight, Le.debounce = os.debounce, Le.defaults = fs.defaults, Le.defaultsDeep = fs.defaultsDeep, Le.defer = os.defer, Le.delay = os.delay, Le.difference = es.difference, Le.differenceBy = es.differenceBy, Le.differenceWith = es.differenceWith, Le.drop = es.drop, Le.dropRight = es.dropRight, Le.dropRightWhile = es.dropRightWhile, Le.dropWhile = es.dropWhile, Le.fill = es.fill, Le.filter = rs.filter, Le.flatMap = rs.flatMap, Le.flatMapDeep = rs.flatMapDeep, Le.flatMapDepth = rs.flatMapDepth, Le.flatten = es.flatten, Le.flattenDeep = es.flattenDeep, Le.flattenDepth = es.flattenDepth, Le.flip = os.flip, Le.flow = vs.flow, Le.flowRight = vs.flowRight, Le.fromPairs = es.fromPairs, Le.functions = fs.functions, Le.functionsIn = fs.functionsIn, Le.groupBy = rs.groupBy, Le.initial = es.initial, Le.intersection = es.intersection, Le.intersectionBy = es.intersectionBy, Le.intersectionWith = es.intersectionWith, Le.invert = fs.invert, Le.invertBy = fs.invertBy, Le.invokeMap = rs.invokeMap, Le.iteratee = vs.iteratee, Le.keyBy = rs.keyBy, Le.keys = vn, Le.keysIn = fs.keysIn, Le.map = rs.map, Le.mapKeys = fs.mapKeys, Le.mapValues = fs.mapValues, Le.matches = vs.matches, Le.matchesProperty = vs.matchesProperty, Le.memoize = os.memoize, Le.merge = fs.merge, Le.mergeWith = fs.mergeWith, Le.method = vs.method, Le.methodOf = vs.methodOf, Le.mixin = Es, Le.negate = Do, Le.nthArg = vs.nthArg, Le.omit = fs.omit, Le.omitBy = fs.omitBy, Le.once = os.once, Le.orderBy = rs.orderBy, Le.over = vs.over, Le.overArgs = os.overArgs, Le.overEvery = vs.overEvery, Le.overSome = vs.overSome, Le.partial = os.partial, Le.partialRight = os.partialRight, Le.partition = rs.partition, Le.pick = fs.pick, Le.pickBy = fs.pickBy, Le.property = vs.property, Le.propertyOf = vs.propertyOf, Le.pull = es.pull, Le.pullAll = es.pullAll, Le.pullAllBy = es.pullAllBy, Le.pullAllWith = es.pullAllWith, Le.pullAt = es.pullAt, Le.range = vs.range, Le.rangeRight = vs.rangeRight, Le.rearg = os.rearg, Le.reject = rs.reject, Le.remove = es.remove, Le.rest = os.rest, Le.reverse = es.reverse, Le.sampleSize = rs.sampleSize, Le.set = fs.set, Le.setWith = fs.setWith, Le.shuffle = rs.shuffle, Le.slice = es.slice, Le.sortBy = rs.sortBy, Le.sortedUniq = es.sortedUniq, Le.sortedUniqBy = es.sortedUniqBy, Le.split = hs.split, Le.spread = os.spread, Le.tail = es.tail, Le.take = es.take, Le.takeRight = es.takeRight, Le.takeRightWhile = es.takeRightWhile, Le.takeWhile = es.takeWhile, Le.tap = ps.tap, Le.throttle = os.throttle, Le.thru = Wu, Le.toArray = as.toArray, Le.toPairs = fs.toPairs, Le.toPairsIn = fs.toPairsIn, Le.toPath = vs.toPath, Le.toPlainObject = as.toPlainObject, Le.transform = fs.transform, Le.unary = os.unary, Le.union = es.union, Le.unionBy = es.unionBy, Le.unionWith = es.unionWith, Le.uniq = es.uniq, Le.uniqBy = es.uniqBy, Le.uniqWith = es.uniqWith, Le.unset = fs.unset, Le.unzip = es.unzip, Le.unzipWith = es.unzipWith, Le.update = fs.update, Le.updateWith = fs.updateWith, Le.values = fs.values, Le.valuesIn = fs.valuesIn, Le.without = es.without, Le.words = hs.words, Le.wrap = os.wrap, Le.xor = es.xor, Le.xorBy = es.xorBy, Le.xorWith = es.xorWith, Le.zip = es.zip, Le.zipObject = es.zipObject, Le.zipObjectDeep = es.zipObjectDeep, Le.zipWith = es.zipWith, Le.entries = fs.toPairs, Le.entriesIn = fs.toPairsIn, Le.extend = fs.assignIn, Le.extendWith = fs.assignInWith, Es(Le, Le), Le.add = us.add, Le.attempt = vs.attempt, Le.camelCase = hs.camelCase, Le.capitalize = hs.capitalize, Le.ceil = us.ceil, Le.clamp = ss, Le.clone = as.clone, Le.cloneDeep = as.cloneDeep, Le.cloneDeepWith = as.cloneDeepWith, Le.cloneWith = as.cloneWith, Le.conformsTo = as.conformsTo, Le.deburr = hs.deburr, Le.defaultTo = vs.defaultTo, Le.divide = us.divide, Le.endsWith = hs.endsWith, Le.eq = as.eq, Le.escape = hs.escape, Le.escapeRegExp = hs.escapeRegExp, Le.every = rs.every, Le.find = rs.find, Le.findIndex = es.findIndex, Le.findKey = fs.findKey, Le.findLast = rs.findLast, Le.findLastIndex = es.findLastIndex, Le.findLastKey = fs.findLastKey, Le.floor = us.floor, Le.forEach = rs.forEach, Le.forEachRight = rs.forEachRight, Le.forIn = fs.forIn, Le.forInRight = fs.forInRight, Le.forOwn = fs.forOwn, Le.forOwnRight = fs.forOwnRight, Le.get = fs.get, Le.gt = as.gt, Le.gte = as.gte, Le.has = fs.has, Le.hasIn = fs.hasIn, Le.head = es.head, Le.identity = e, Le.includes = rs.includes, Le.indexOf = es.indexOf, Le.inRange = ls, Le.invoke = fs.invoke, Le.isArguments = as.isArguments, Le.isArray = s, Le.isArrayBuffer = as.isArrayBuffer, Le.isArrayLike = as.isArrayLike, Le.isArrayLikeObject = as.isArrayLikeObject, Le.isBoolean = as.isBoolean, Le.isBuffer = as.isBuffer, Le.isDate = as.isDate, Le.isElement = as.isElement, Le.isEmpty = as.isEmpty, Le.isEqual = as.isEqual, Le.isEqualWith = as.isEqualWith, Le.isError = as.isError, Le.isFinite = as.isFinite, Le.isFunction = as.isFunction, Le.isInteger = as.isInteger, Le.isLength = as.isLength, Le.isMap = as.isMap, Le.isMatch = as.isMatch, Le.isMatchWith = as.isMatchWith, Le.isNaN = as.isNaN, Le.isNative = as.isNative, Le.isNil = as.isNil, Le.isNull = as.isNull, Le.isNumber = as.isNumber, Le.isObject = i, Le.isObjectLike = as.isObjectLike, Le.isPlainObject = as.isPlainObject, Le.isRegExp = as.isRegExp, Le.isSafeInteger = as.isSafeInteger, Le.isSet = as.isSet, Le.isString = as.isString, Le.isSymbol = as.isSymbol, Le.isTypedArray = as.isTypedArray, Le.isUndefined = as.isUndefined, Le.isWeakMap = as.isWeakMap, Le.isWeakSet = as.isWeakSet, Le.join = es.join, Le.kebabCase = hs.kebabCase, Le.last = Bt, Le.lastIndexOf = es.lastIndexOf, Le.lowerCase = hs.lowerCase, Le.lowerFirst = hs.lowerFirst, Le.lt = as.lt, Le.lte = as.lte, Le.max = us.max, Le.maxBy = us.maxBy, Le.mean = us.mean, Le.meanBy = us.meanBy, Le.min = us.min, Le.minBy = us.minBy, Le.stubArray = vs.stubArray, Le.stubFalse = vs.stubFalse, Le.stubObject = vs.stubObject, Le.stubString = vs.stubString, Le.stubTrue = vs.stubTrue, Le.multiply = us.multiply, Le.nth = es.nth, Le.noop = vs.noop, Le.now = is.now, Le.pad = hs.pad, Le.padEnd = hs.padEnd, Le.padStart = hs.padStart, Le.parseInt = hs.parseInt, Le.random = cs, Le.reduce = rs.reduce, Le.reduceRight = rs.reduceRight, Le.repeat = hs.repeat, Le.replace = hs.replace, Le.result = fs.result, Le.round = us.round, Le.sample = rs.sample, Le.size = rs.size, Le.snakeCase = hs.snakeCase, Le.some = rs.some, Le.sortedIndex = es.sortedIndex, Le.sortedIndexBy = es.sortedIndexBy, Le.sortedIndexOf = es.sortedIndexOf, Le.sortedLastIndex = es.sortedLastIndex, Le.sortedLastIndexBy = es.sortedLastIndexBy, Le.sortedLastIndexOf = es.sortedLastIndexOf, Le.startCase = hs.startCase, Le.startsWith = hs.startsWith, Le.subtract = us.subtract, Le.sum = us.sum, Le.sumBy = us.sumBy, Le.template = hs.template, Le.times = vs.times, Le.toFinite = as.toFinite, Le.toInteger = Yn, Le.toLength = as.toLength, Le.toLower = hs.toLower, Le.toNumber = as.toNumber, Le.toSafeInteger = as.toSafeInteger, Le.toString = as.toString, Le.toUpper = hs.toUpper, Le.trim = hs.trim, Le.trimEnd = hs.trimEnd, Le.trimStart = hs.trimStart, Le.truncate = hs.truncate, Le.unescape = hs.unescape, Le.uniqueId = vs.uniqueId, Le.upperCase = hs.upperCase, Le.upperFirst = hs.upperFirst, Le.each = rs.forEach, Le.eachRight = rs.forEachRight, Le.first = es.head, Es(Le, (ys = {}, On(Le, (function(t, n) {
    ws.call(Le.prototype, n) || (ys[n] = t)
})), ys), {
    chain: !1
}), Le.VERSION = "4.17.21", (Le.templateSettings = hs.templateSettings).imports._ = Le, an(["bind", "bindKey", "curry", "curryRight", "partial", "partialRight"], (function(t) {
    Le[t].placeholder = Le
})), an(["drop", "take"], (function(t, n) {
    Ee.prototype[t] = function(e) {
        e = void 0 === e ? 1 : ks(Yn(e), 0);
        var r = this.__filtered__ && !n ? new Ee(this) : this.clone();
        return r.__filtered__ ? r.__takeCount__ = js(e, r.__takeCount__) : r.__views__.push({
            size: js(e, ms),
            type: t + (r.__dir__ < 0 ? "Right" : "")
        }), r
    }, Ee.prototype[t + "Right"] = function(n) {
        return this.reverse()[t](n).reverse()
    }
})), an(["filter", "map", "takeWhile"], (function(t, n) {
    var e = n + 1,
        r = 1 == e || 3 == e;
    Ee.prototype[t] = function(t) {
        var n = this.clone();
        return n.__iteratees__.push({
            iteratee: kn(t),
            type: e
        }), n.__filtered__ = n.__filtered__ || r, n
    }
})), an(["head", "last"], (function(t, n) {
    var e = "take" + (n ? "Right" : "");
    Ee.prototype[t] = function() {
        return this[e](1).value()[0]
    }
})), an(["initial", "tail"], (function(t, n) {
    var e = "drop" + (n ? "" : "Right");
    Ee.prototype[t] = function() {
        return this.__filtered__ ? new Ee(this) : this[e](1)
    }
})), Ee.prototype.compact = function() {
    return this.filter(e)
}, Ee.prototype.find = function(t) {
    return this.filter(t).head()
}, Ee.prototype.findLast = function(t) {
    return this.reverse().find(t)
}, Ee.prototype.invokeMap = x((function(t, n) {
    return "function" == typeof t ? new Ee(this) : this.map((function(e) {
        return uo(e, t, n)
    }))
})), Ee.prototype.reject = function(t) {
    return this.filter(Do(kn(t)))
}, Ee.prototype.slice = function(t, n) {
    t = Yn(t);
    var e = this;
    return e.__filtered__ && (t > 0 || n < 0) ? new Ee(e) : (t < 0 ? e = e.takeRight(-t) : t && (e = e.drop(t)), void 0 !== n && (e = (n = Yn(n)) < 0 ? e.dropRight(-n) : e.take(n - t)), e)
}, Ee.prototype.takeRightWhile = function(t) {
    return this.reverse().takeWhile(t).reverse()
}, Ee.prototype.toArray = function() {
    return this.take(ms)
}, On(Ee.prototype, (function(t, n) {
    var e = /^(?:filter|find|map|reject)|While$/.test(n),
        r = /^(?:head|last)$/.test(n),
        i = Le[r ? "take" + ("last" == n ? "Right" : "") : n],
        o = r || /^find/.test(n);
    i && (Le.prototype[n] = function() {
        var n = this.__wrapped__,
            a = r ? [1] : arguments,
            u = n instanceof Ee,
            l = a[0],
            c = u || s(n),
            f = function(t) {
                var n = i.apply(Le, dn([t], a));
                return r && p ? n[0] : n
            };
        c && e && "function" == typeof l && 1 != l.length && (u = c = !1);
        var p = this.__chain__,
            h = !!this.__actions__.length,
            v = o && !p,
            d = u && !h;
        if (!o && c) {
            n = d ? n : new Ee(this);
            var _ = t.apply(n, a);
            return _.__actions__.push({
                func: Wu,
                args: [f],
                thisArg: void 0
            }), new Ae(_, p)
        }
        return v && d ? t.apply(this, a) : (_ = this.thru(f), v ? r ? _.value()[0] : _.value() : _)
    })
})), an(["pop", "push", "shift", "sort", "splice", "unshift"], (function(t) {
    var n = bs[t],
        e = /^(?:push|sort|unshift)$/.test(t) ? "tap" : "thru",
        r = /^(?:pop|shift)$/.test(t);
    Le.prototype[t] = function() {
        var t = arguments;
        if (r && !this.__chain__) {
            var i = this.value();
            return n.apply(s(i) ? i : [], t)
        }
        return this[e]((function(e) {
            return n.apply(s(e) ? e : [], t)
        }))
    }
})), On(Ee.prototype, (function(t, n) {
    var e = Le[n];
    if (e) {
        var r = e.name + "";
        ws.call(Ie, r) || (Ie[r] = []), Ie[r].push({
            name: n,
            func: e
        })
    }
})), Ie[Ye(void 0, 2).name] = [{
    name: "wrapper",
    func: void 0
}], Ee.prototype.clone = function() {
    var t = new Ee(this.__wrapped__);
    return t.__actions__ = a(this.__actions__), t.__dir__ = this.__dir__, t.__filtered__ = this.__filtered__, t.__iteratees__ = a(this.__iteratees__), t.__takeCount__ = this.__takeCount__, t.__views__ = a(this.__views__), t
}, Ee.prototype.reverse = function() {
    if (this.__filtered__) {
        var t = new Ee(this);
        t.__dir__ = -1, t.__filtered__ = !0
    } else(t = this.clone()).__dir__ *= -1;
    return t
}, Ee.prototype.value = function() {
    var t = this.__wrapped__.value(),
        n = this.__dir__,
        e = s(t),
        r = n < 0,
        i = e ? t.length : 0,
        o = function(t, n, e) {
            for (var r = -1, i = e.length; ++r < i;) {
                var o = e[r],
                    a = o.size;
                switch (o.type) {
                    case "drop":
                        t += a;
                        break;
                    case "dropRight":
                        n -= a;
                        break;
                    case "take":
                        n = _s(n, t + a);
                        break;
                    case "takeRight":
                        t = ds(t, n - a)
                }
            }
            return {
                start: t,
                end: n
            }
        }(0, i, this.__views__),
        a = o.start,
        u = o.end,
        l = u - a,
        c = r ? u : a - 1,
        f = this.__iteratees__,
        p = f.length,
        h = 0,
        v = gs(l, this.__takeCount__);
    if (!e || !r && i == l && v == l) return Iu(t, this.__actions__);
    var d = [];
    t: for (; l-- && h < v;) {
        for (var _ = -1, g = t[c += n]; ++_ < p;) {
            var y = f[_],
                m = y.iteratee,
                b = y.type,
                w = m(g);
            if (2 == b) g = w;
            else if (!w) {
                if (1 == b) continue t;
                break t
            }
        }
        d[h++] = g
    }
    return d
}, Le.prototype.at = ps.at, Le.prototype.chain = ps.wrapperChain, Le.prototype.commit = ps.commit, Le.prototype.next = ps.next, Le.prototype.plant = ps.plant, Le.prototype.reverse = ps.reverse, Le.prototype.toJSON = Le.prototype.valueOf = Le.prototype.value = ps.value, Le.prototype.first = Le.prototype.head, xs && (Le.prototype[xs] = ps.toIterator);
let Ws = null,
    Is = {};
const Os = Le.debounce((async () => {
        try {
            await (async () => {
                if (!Ws) {
                    const {
                        default: t
                    } = await Z((async () => {
                            const {
                                default: t
                            } = await
                            import ("./AMuMAA_L.js").then((t => t.aA));
                            return {
                                default: t
                            }
                        }), __vite__mapDeps([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36]),
                        import.meta.url);
                    Ws = t, document.addEventListener("click", (t => {
                        if (t.target.classList.contains("mermaid-copy-code")) {
                            const n = Is[t.target.id];
                            navigator.clipboard.writeText(n)
                        }
                    }))
                }
            })(), Ws && await Ws.run({
                nodes: document.querySelectorAll("pre.mermaid"),
                suppressErrors: !0
            })
        } catch (t) {}
    }), 400),
    Bs = t => {
        const n = t.renderer.rules.fence.bind(t.renderer.rules);
        t.renderer.rules.fence = (t, e, r, ...i) => {
            const o = t[e],
                a = o.content.trim();
            return "mermaid" === o.info ? (t => {
                const n = on.MD5(t).toString(),
                    e = `<div class="mermaid-wrapper">\n  <pre class="mermaid">${t}</pre>\n  <div class="mermaid-controls">\n    <div class="mermaid-copy-code" id="${n}">Copy</div>\n  </div>\n  </div>`;
                return Is[n] = t, Os(), e
            })(a) : n(t, e, r, ...i)
        }
    };
class As {
    constructor(t = {}) {
        this.hook = t.hook, this.callback = t.callback, this.lang = t.lang || "undefined" != typeof document && document.documentElement.lang || "en", this.code_cache = {}
    }
    "after:highlight" (t) {
        if ("undefined" == typeof document || "undefined" == typeof window) return;
        if (t.value.includes("presentation-wrapper")) return;
        this.__init_event || (this.__init_event = !0, document.addEventListener("click", (t => {
            if (t.target.classList.contains("hljs-copy-button")) {
                const n = this.code_cache[t.target.id];
                navigator.clipboard.writeText(n).then((() => {
                    t.target.innerText = "Copied!", setTimeout((() => {
                        t.target.innerText = "Copy"
                    }), 2e3)
                }))
            }
        })));
        const n = t.value.match(/<button class="hljs-copy-button"[^>]*id="([^"]*)"[^>]*>[^<]*<\/button>/),
            e = n ? n[1] : on.MD5(t.value).toString();
        t.value = t.value.replace(/<button class="hljs-copy-button"[^>]*>[^<]*<\/button>/, ""), t.value = `<button class="hljs-copy-button" id="${e}">Copy</button>${t.value}`, this.code_cache[e] = t.code
    }
}

function Ms(t) {
    const n = t.renderer.rules.fence;
    t.renderer.rules.fence = function(e, r, i, o, a) {
        const u = e[r];
        if ("presentation" === u.info.trim()) {
            const n = t.parse(u.content, {}),
                e = t.renderer.render(n, i, o),
                r = u.content;
            return `\n        <div class="presentation-wrapper">\n          <div class="presentation-header">\n            <div class="presentation-header-left">\n              修改建议\n            </div>\n            <button class="presentation-button" onclick="window.dispatchEvent(new CustomEvent('applyPresentationChange', { detail: '${encodeURIComponent(r)}' }))">\n              APPLY\n            </button>\n          </div>\n          <div class="presentation-content">${e}</div>\n        </div>\n      `
        }
        return n(e, r, i, o, a)
    }
}

function Rs(t) {
    const n = t.parse;
    return t.parse = function(t, e) {
        let r = t;
        const i = [{
                regex: /!\[\]\(([^\)]*?)$/,
                type: "空描述图片"
            }, {
                regex: /!\[([^\]]+)\]\(([^\)]*?)$/,
                type: "带描述图片"
            }, {
                regex: /\[([^\]]*)\]\(([^\)]*?)$/,
                type: "链接"
            }, {
                regex: /!\[[^\]]*$/,
                type: "简单图片"
            }, {
                regex: /\[[^\]]*$/,
                type: "简单链接"
            }, {
                regex: /!\[\]$/,
                type: "空图片"
            }, {
                regex: /!\[[^\]]+\]$/,
                type: "仅描述图片"
            }, {
                regex: /\[\]$/,
                type: "空链接"
            }, {
                regex: /\[[^\]]+\]$/,
                type: "仅文本链接"
            }],
            o = t.split(/\r?\n/);
        if (o.length > 0) {
            const t = o[o.length - 1];
            for (const n of i) {
                const e = t.match(n.regex);
                if (e) {
                    const n = e.index;
                    if (n >= 0) {
                        const e = t.substring(0, n);
                        o[o.length - 1] = e, r = o.join("\n");
                        break
                    }
                }
            }
        }
        return n.call(this, r, e)
    }, t
}

function Ls(t, n = {}) {
    const e = t.renderer.renderToken.bind(t.renderer);
    t.renderer.renderToken = function(t, n, r) {
        let i = t[n];
        return null !== i.map && i.attrPush(["data-source-line", i.map[0] + 1 + "-" + i.map[1]]), e(t, n, r)
    };
    const r = t.renderer.rules.fence,
        i = t.renderer.rules.code_block;
    t.renderer.rules.fence = function(t, n, e, i, o) {
        const a = t[n],
            u = r(t, n, e, i, o);
        return a.map ? u.replace("<pre", '<pre data-source-line="' + (a.map[0] + 1) + "-" + a.map[1] + '"') : u
    }, t.renderer.rules.code_block = function(t, n, e, r, o) {
        const a = t[n],
            u = i(t, n, e, r, o);
        return a.map ? u.replace("<pre", '<pre data-source-line="' + (a.map[0] + 1) + "-" + a.map[1] + '"') : u
    }
}
const Ss = Object.assign({
        name: "MarkdownWithPlugins"
    }, {
        __name: "MarkdownWithPlugins",
        props: {
            source: String,
            supportCitationLink: {
                type: Boolean,
                default: !1
            },
            is_thinking: {
                type: Boolean,
                default: !1
            },
            enableCompleteLink: {
                type: Boolean,
                default: !1
            },
            linkify: {
                type: Boolean,
                default: !1
            },
            enableHtml: {
                type: Boolean,
                default: !0
            },
            showToc: {
                type: Boolean,
                default: !1
            },
            tocOptions: {
                type: Object,
                default: () => ({})
            },
            tocCallback: {
                type: Function,
                default: null
            }
        },
        emits: ["click"],
        setup(t, {
            expose: n,
            emit: e
        }) {
            const r = t,
                i = e,
                {
                    source: o,
                    supportCitationLink: a,
                    is_thinking: u,
                    enableCompleteLink: s,
                    linkify: l
                } = nt(r),
                c = et(null),
                f = t => {
                    a.value && (g.value = t.target)
                },
                p = t => {
                    a.value && (g.value = null)
                },
                h = t => {
                    i("click", t)
                };
            rt(o, (() => {
                o.value && (c.value = o.value.replaceAll(/(\n\s*)\$\$/g, "\n$1$$$$"), c.value = c.value.replaceAll(/(\n\s*)\\\[/g, "\n$1\\["))
            }), {
                immediate: !0
            });
            const v = it(),
                d = ot({
                    locale: v.$i18n.locale.value,
                    messages: v.$i18n.messages.value,
                    fallbackLocale: v.$i18n.fallbackLocale.value
                });
            pe.addPlugin(new As);
            const _ = et([{
                plugin: Et,
                options: {
                    level: 3
                }
            }, {
                plugin: xt
            }, {
                plugin: kt
            }, {
                plugin: jt
            }, {
                plugin: rn,
                options: {
                    engine: en,
                    delimiters: ["brackets", "kramdown", "gitlab", "dollars"]
                }
            }, {
                plugin: Bs
            }, {
                plugin: bt,
                options: {
                    hljs: pe
                }
            }, {
                plugin: Ms
            }, {
                plugin: Ls
            }]);
            s.value && _.value.push({
                plugin: Rs
            });
            const g = et(null),
                y = et(null),
                m = et([]),
                b = () => {
                    [{
                        class: "-md-ext-gallery",
                        func: (t, n, e) => {
                            n.value && n.value.list || st.log("data.list is null");
                            const r = _t(Zt, {
                                data: n,
                                ...e
                            });
                            return r.use(gt), r
                        }
                    }, {
                        class: "-md-ext-block-quote",
                        func: (t, n, e) => _t(tn, {
                            data: n,
                            ...e
                        })
                    }, {
                        class: "-md-ext-mind-map",
                        func: (t, n, e) => _t(nn, {
                            data: n,
                            ...e
                        })
                    }, {
                        class: "-md-ext-youtube-widget",
                        func: (t, n, e) => _t(he, {
                            data: n,
                            ...e
                        })
                    }].forEach((t => {
                        if (!y.value) return void st.log("markdownElementRef.value is null");
                        y.value.querySelectorAll(`.${t.class}`).forEach((n => {
                            try {
                                if (n.classList.contains("-md-ext-initialized")) return;
                                const e = et(JSON.parse(n.textContent)),
                                    r = t.func(n, e, {});
                                r.use(d), m.value.push({
                                    app: r,
                                    div: n,
                                    data: e,
                                    exposedDict: {}
                                }), r.provide("mdExtExpose", (t => n => {
                                    const e = m.value.findIndex((n => n.div === t));
                                    e >= 0 && (m.value[e].exposedDict = n)
                                })(n)), r.mount(n), n.classList.add("-md-ext-initialized")
                            } catch (e) {
                                st.log("init markdown component error"), st.log(`source: ${n.outerHTML}`)
                            }
                        }))
                    }))
                },
                w = () => {
                    if (!y.value) return;
                    y.value.querySelectorAll("table").forEach((t => {
                        if (t.classList.contains("table-scroll-init")) return;
                        const n = document.createElement("div");
                        n.className = "table-container", n.style.position = "relative", t.parentNode.insertBefore(n, t), n.appendChild(t);
                        const e = document.createElement("div");
                        e.className = "table-scroll-button", e.style.display = "none";
                        const r = document.createElement("div");
                        r.className = "scroll-icon", r.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">\n          <path d="M9.5556 3L12.2812 5.643C13.4271 6.75412 14 7.30962 14 8C14 8.69037 13.4271 9.24587 12.2812 10.357L9.5556 13M2 3L4.72564 5.643C5.87147 6.75412 6.4444 7.30962 6.4444 8C6.4444 8.69037 5.87147 9.24587 4.72565 10.357L2 13" stroke="currentColor" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>\n          </svg>', e.appendChild(r), n.appendChild(e);
                        const i = () => {
                            t.scrollWidth > t.clientWidth ? e.style.display = "flex" : e.style.display = "none"
                        };
                        e.addEventListener("click", (() => {
                            t.scrollTo({
                                left: t.scrollLeft + t.clientWidth,
                                behavior: "smooth"
                            }), i(), e.style.display = "none"
                        })), t.addEventListener("scroll", (() => {
                            e.style.display = "none"
                        })), i(), t.classList.add("table-scroll-init")
                    }))
                };
            return rt((() => y.value), (async t => {
                t && (await new Promise((t => {
                    setTimeout((() => {
                        t()
                    }), 0)
                })), b(), ut((() => {
                    w()
                })))
            })), rt(o, (async () => {
                await ut(), w()
            })), at((() => {
                ut((() => {
                    w()
                }))
            })), n({
                initComponents: b,
                setupTableScrollButtons: w
            }), st.isDarkMode() && Z((() => Promise.resolve({})), __vite__mapDeps([37]),
                import.meta.url), (n, e) => (yt(), lt(dt, null, [ct("div", {
                class: "markdown-viewer",
                ref_key: "markdownElementRef",
                ref: y,
                onMouseover: f,
                onMouseleave: p,
                onClick: h
            }, [ht(vt(mt), {
                plugins: _.value,
                html: t.enableHtml,
                source: c.value || vt(o),
                linkify: vt(l),
                toc: t.tocCallback ? { ...t.tocOptions,
                    callback: t.tocCallback
                } : t.tocOptions
            }, null, 8, ["plugins", "html", "source", "linkify", "toc"])], 544), vt(a) ? (yt(), ft(vt(wt), {
                key: 0,
                citationLink: g.value
            }, null, 8, ["citationLink"])) : pt("", !0)], 64))
        }
    }),
    Cs = tt(Ss, [
        ["__scopeId", "data-v-0d69e5be"]
    ]),
    Ps = Object.freeze(Object.defineProperty({
        __proto__: null,
        default: Cs
    }, Symbol.toStringTag, {
        value: "Module"
    }));
export {
    $u as A, Ir as B, Fa as C, Hi as D, Fr as E, Jr as F, wi as G, qu as H, Ps as M, Cs as _, Yr as a, lr as b, Wr as c, Bo as d, di as e, fi as f, Oi as g, Di as h, bo as i, Bi as j, Cr as k, vi as l, So as m, ir as n, Ko as o, ka as p, $i as q, qa as r, mo as s, eu as t, Du as u, Ni as v, Ui as w, si as x, Vr as y, ts as z
};