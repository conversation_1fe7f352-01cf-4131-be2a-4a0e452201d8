import {
    d as o,
    o as r,
    b as t
} from "./Cf0SOiw0.js";
const n = {
    width: "16",
    height: "16",
    viewBox: "0 0 16 16",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const e = {
    render: function(e, s) {
        return r(), o("svg", n, s[0] || (s[0] = [t("path", {
            d: "M13 3L3 13M3 3L13 13",
            stroke: "currentColor",
            "stroke-width": "1.2",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }, null, -1)]))
    }
};
export {
    e as C
};