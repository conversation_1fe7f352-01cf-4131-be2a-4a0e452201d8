import {
    d as o,
    D as r,
    o as t
} from "./Cf0SOiw0.js";
const e = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const n = {
    render: function(n, s) {
        return t(), o("svg", e, s[0] || (s[0] = [r('<path d="M7 8L7 17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M11 5L11 20" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M15 13L15 17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M3 10L3 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M19 14L19 16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M17.5 4L17.7579 4.69703C18.0961 5.61102 18.2652 6.06802 18.5986 6.40139C18.932 6.73477 19.389 6.90387 20.303 7.24208L21 7.5L20.303 7.75792C19.389 8.09613 18.932 8.26524 18.5986 8.59861C18.2652 8.93198 18.0961 9.38898 17.7579 10.303L17.5 11L17.2421 10.303C16.9039 9.38898 16.7348 8.93198 16.4014 8.59861C16.068 8.26524 15.611 8.09613 14.697 7.75792L14 7.5L14.697 7.24208C15.611 6.90387 16.068 6.73477 16.4014 6.40139C16.7348 6.06802 16.9039 5.61102 17.2421 4.69703L17.5 4Z" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"></path>', 6)]))
    }
};
export {
    n as a
};