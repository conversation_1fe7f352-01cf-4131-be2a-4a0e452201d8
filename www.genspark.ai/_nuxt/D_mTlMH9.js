import {
    d as n,
    b as o,
    o as r
} from "./Cf0SOiw0.js";
const t = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 24 24"
};
const a = {
    render: function(a, l) {
        return r(), n("svg", t, l[0] || (l[0] = [o("g", {
            fill: "none"
        }, [o("path", {
            d: "M10.55 2.533a2.25 2.25 0 0 1 2.9 0l6.75 5.695c.508.427.8 1.056.8 1.72v9.802a1.75 1.75 0 0 1-1.75 1.75h-3a1.75 1.75 0 0 1-1.75-1.75v-5a.75.75 0 0 0-.75-.75h-3.5a.75.75 0 0 0-.75.75v5a1.75 1.75 0 0 1-1.75 1.75h-3A1.75 1.75 0 0 1 3 19.75V9.947c0-.663.292-1.292.8-1.72l6.75-5.694z",
            fill: "currentColor"
        })], -1)]))
    }
};
export {
    a as H
};