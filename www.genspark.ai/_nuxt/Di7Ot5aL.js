import {
    _ as e
} from "./DKpDUEYb.js";
import {
    _ as a
} from "./UVj2ej2A.js";
import {
    d as t,
    o,
    b as i,
    _ as n,
    J as l,
    f as s,
    H as r,
    t as u,
    L as d,
    e as c,
    E as g,
    n as h,
    w as p,
    q as v,
    a9 as m,
    r as w,
    c as f,
    v as b,
    h as k,
    x as y,
    U as x,
    F as C,
    k as I,
    a as D,
    s as M,
    D as L,
    W as U,
    i as S,
    C as P,
    I as _,
    a4 as R,
    p as j,
    ai as E
} from "./Cf0SOiw0.js";
import {
    d as T
} from "./DOnko34f.js";
import {
    I as z
} from "./CbfxwEmT.js";
import {
    a as $,
    b as H,
    e as W,
    Q as F,
    R as N
} from "./CrbPJ6Kt.js";
import {
    D as A
} from "./BuZX8s8J.js";
import {
    M as B
} from "./Dc8Bac8D.js";
import {
    A as O
} from "./Cl89jLsD.js";
import {
    a as V
} from "./DbJ6Dt9m.js";
import {
    a as q
} from "./FCN43o2W.js";
import {
    u as Q
} from "./B6noBY_5.js";
const G = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const Z = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const K = {
    width: "100%",
    height: "100%",
    viewBox: "0 0 26 26",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const X = l({
        name: "DrawableCanvas",
        components: {
            UndoIcon: {
                render: function(e, a) {
                    return o(), t("svg", G, a[0] || (a[0] = [i("path", {
                        d: "M5.41707 3.33203L2.50049 5.83203L5.41707 8.7487",
                        stroke: "currentColor",
                        "stroke-width": "1.5",
                        "stroke-linecap": "round",
                        "stroke-linejoin": "round"
                    }, null, -1), i("path", {
                        d: "M2.50049 5.83203H12.0812C14.9492 5.83203 17.3847 8.17386 17.4965 11.0404C17.6146 14.0694 15.1118 16.6654 12.0812 16.6654H4.99982",
                        stroke: "currentColor",
                        "stroke-width": "1.5",
                        "stroke-linecap": "round",
                        "stroke-linejoin": "round"
                    }, null, -1)]))
                }
            },
            RedoIcon: {
                render: function(e, a) {
                    return o(), t("svg", Z, a[0] || (a[0] = [i("path", {
                        d: "M14.5829 3.33203L17.4995 5.83203L14.5829 8.7487",
                        stroke: "currentColor",
                        "stroke-width": "1.5",
                        "stroke-linecap": "round",
                        "stroke-linejoin": "round"
                    }, null, -1), i("path", {
                        d: "M17.4995 5.83203H7.91876C5.05084 5.83203 2.6153 8.17386 2.50351 11.0404C2.38539 14.0694 4.88822 16.6654 7.91876 16.6654H15.0002",
                        stroke: "currentColor",
                        "stroke-width": "1.5",
                        "stroke-linecap": "round",
                        "stroke-linejoin": "round"
                    }, null, -1)]))
                }
            },
            SubmitPromptIcon: {
                render: function(e, a) {
                    return o(), t("svg", K, a[0] || (a[0] = [i("path", {
                        d: "M13.1201 20.1445L13.1201 7.14453",
                        stroke: "currentColor",
                        "stroke-width": "2",
                        "stroke-linecap": "round",
                        "stroke-linejoin": "round"
                    }, null, -1), i("path", {
                        d: "M9.28568 10.0697L12.3358 7.18236C12.7369 6.80268 13.3872 6.80268 13.7882 7.18236L16.8383 10.0697",
                        stroke: "currentColor",
                        "stroke-width": "2",
                        "stroke-linecap": "round",
                        "stroke-linejoin": "round"
                    }, null, -1)]))
                }
            }
        },
        props: {
            imageSource: {
                type: [File, String],
                required: !0
            },
            strokeColor: {
                type: String,
                default: "#ffffff"
            },
            tool: {
                type: String,
                default: "inpaint"
            },
            promptInput: {
                type: Boolean,
                default: !1
            },
            disablePaint: {
                type: Boolean,
                default: !1
            },
            needPng: {
                type: Boolean,
                default: !1
            },
            toolCallbackMode: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["submit:imageData", "update:imageData", "image-loaded", "image-resized", "error"],
        setup(e, {
            emit: a
        }) {
            const t = w(null),
                o = w(null),
                i = w(null),
                n = w(null),
                l = w(null),
                s = w(!1),
                r = w(null),
                u = w(null),
                d = w(40),
                c = w(null),
                g = w(null),
                h = w({
                    width: 0,
                    height: 0
                }),
                p = w({
                    image: null,
                    width: 0,
                    height: 0
                }),
                v = w([]),
                m = w(-1),
                C = f((() => m.value >= 0)),
                I = f((() => m.value < v.value.length - 1)),
                D = w(!1),
                M = w(""),
                L = w({
                    left: "0px",
                    top: "0px"
                }),
                U = () => {
                    const e = p.value.image,
                        a = document.createElement("canvas"),
                        t = a.getContext("2d");
                    return a.width = e.width, a.height = e.height, t.drawImage(e, 0, 0), a
                },
                S = e => new Promise(((o, i) => {
                    const n = new Image;
                    n.crossOrigin = "anonymous", n.onload = () => {
                        const e = t.value.clientWidth,
                            i = t.value.clientHeight,
                            l = n.width / n.height;
                        let s, r;
                        l > e / i ? (s = e, r = e / l) : (r = i, s = i * l), h.value = {
                            width: s,
                            height: r
                        }, p.value = {
                            image: n,
                            width: s,
                            height: r
                        };
                        const u = U();
                        a("image-loaded", {
                            image: u.toDataURL("image/jpeg"),
                            width: s,
                            height: r
                        }), o()
                    }, n.onerror = () => {
                        const e = new Error("图片加载失败");
                        a("error", e), i(e)
                    }, n.src = e
                })),
                P = async () => {
                    if (e.imageSource) {
                        E(), l.value.getNode().destroyChildren(), l.value.getNode().batchDraw();
                        try {
                            if (e.imageSource instanceof File) {
                                const a = URL.createObjectURL(e.imageSource);
                                await S(a), URL.revokeObjectURL(a)
                            } else "string" == typeof e.imageSource && await S(e.imageSource);
                            u.value = new Konva.Circle({
                                radius: d.value / 2,
                                opacity: .7,
                                stroke: "#ffffff",
                                strokeWidth: 2,
                                visible: !e.disablePaint
                            }), u.value.position({
                                x: -1e3,
                                y: -1e3
                            }), l.value.getNode().add(u.value), l.value.getNode().batchDraw()
                        } catch (a) {}
                    }
                },
                _ = a => {
                    if (e.disablePaint) return;
                    s.value = !0;
                    const t = o.value.getNode().getPointerPosition(),
                        i = e.strokeColor,
                        c = parseInt(i.slice(1, 3), 16),
                        g = parseInt(i.slice(3, 5), 16),
                        h = parseInt(i.slice(5, 7), 16);
                    r.value = new Konva.Line({
                        points: [t.x, t.y],
                        stroke: `rgba(${c}, ${g}, ${h}, 0.5)`,
                        strokeWidth: parseInt(d.value),
                        lineCap: "round",
                        lineJoin: "round",
                        globalCompositeOperation: "xor",
                        tension: .5
                    }), n.value.getNode().add(r.value), u.value && (u.value.hide(), l.value.getNode().batchDraw())
                },
                R = a => {
                    const t = o.value.getNode().getPointerPosition();
                    if (!e.disablePaint && s.value && r.value) {
                        const e = r.value.points().concat([t.x, t.y]);
                        r.value.points(e), n.value.getNode().batchDraw()
                    }!e.disablePaint && u.value && (u.value.show(), u.value.position({
                        x: t.x,
                        y: t.y
                    }), l.value.getNode().batchDraw())
                },
                j = (a = "") => {
                    const t = p.value.image,
                        o = n.value.getNode().toCanvas(),
                        i = U(),
                        l = i.getContext("2d"),
                        s = document.createElement("canvas");
                    s.width = t.width, s.height = t.height;
                    s.getContext("2d").drawImage(o, 0, 0, o.width, o.height, 0, 0, t.width, t.height);
                    const r = s.getContext("2d").getImageData(0, 0, s.width, s.height),
                        u = l.getImageData(0, 0, i.width, i.height),
                        d = r.data,
                        c = u.data;
                    for (let e = 0; e < d.length; e += 4) {
                        const a = d[e + 3] > 0;
                        c[e + 3] = a ? 0 : 255
                    }
                    return l.putImageData(u, 0, 0), {
                        image: i.toDataURL(e.needPng ? "image/png" : "image/jpeg"),
                        prompt: a,
                        hasPaint: m.value >= 0,
                        width: p.value.width,
                        height: p.value.height
                    }
                },
                E = () => {
                    u.value && u.value.hide(), n.value.getNode().destroyChildren(), n.value.getNode().batchDraw(), v.value = [], m.value = -1, D.value = !1, M.value = "", a("update:imageData", {
                        hasPaint: m.value >= 0
                    })
                },
                z = () => {
                    if (!p.value.image) return;
                    const e = p.value.image,
                        i = t.value.clientWidth,
                        n = t.value.clientHeight,
                        l = e.width / e.height;
                    let s, r;
                    l > i / n ? (s = i, r = i / l) : (r = n, s = n * l), h.value = {
                        width: s,
                        height: r
                    }, p.value = { ...p.value,
                        width: s,
                        height: r
                    }, o.value.getNode().batchDraw(), a("image-resized", {
                        width: s,
                        height: r
                    })
                },
                $ = T(z, 50);
            return b((() => e.imageSource), P), b(d, (e => {
                u.value && (u.value.radius(e / 2), u.value.position({
                    x: h.value.width / 2,
                    y: h.value.height / 2
                }), l.value.getNode().batchDraw(), D.value = !1)
            })), b((() => e.strokeColor), (e => {
                u.value && (u.value.fill(e), l.value.getNode().batchDraw())
            })), b((() => e.disablePaint), (e => {
                e ? u.value && (u.value.hide(), l.value.getNode().batchDraw()) : u.value && (u.value.show(), l.value.getNode().batchDraw())
            })), k((() => {
                e.imageSource && P(), window.addEventListener("resize", $)
            })), y((() => {
                window.removeEventListener("resize", $)
            })), {
                container: t,
                stage: o,
                imageLayer: i,
                drawLayer: n,
                previewLayer: l,
                stageConfig: h,
                imageConfig: p,
                startDrawing: _,
                handleMouseMove: R,
                stopDrawing: () => {
                    if (!e.disablePaint && s.value && (s.value = !1, u.value && (u.value.show(), l.value.getNode().batchDraw()), r.value))
                        if (v.value.splice(m.value + 1), v.value.push(r.value), m.value++, e.promptInput) {
                            const a = r.value.points(),
                                t = a[a.length - 2],
                                i = a[a.length - 1],
                                n = o.value.getNode().container().getBoundingClientRect();
                            D.value = !0, x((() => {
                                var a, o;
                                const l = document.querySelector(".prompt-input-overlay");
                                if (l) {
                                    const o = l.offsetHeight;
                                    if (window.innerWidth <= 768) L.value = {
                                        left: `${n.left+h.value.width/2}px`,
                                        top: .3 * window.innerHeight + "px"
                                    };
                                    else {
                                        let l = n.left + t,
                                            s = n.top + i + o / 2 + 10;
                                        if (e.toolCallbackMode) {
                                            const e = (null == (a = g.value) ? void 0 : a.getBoundingClientRect()) ? ? {
                                                left: 0,
                                                top: 0
                                            };
                                            l = n.left + t - e.left, s = n.top + i + o / 2 + 10 - e.top
                                        }
                                        L.value = {
                                            left: `${l}px`,
                                            top: `${s}px`
                                        }
                                    }
                                }
                                null == (o = c.value) || o.focus()
                            }))
                        } else a("update:imageData", {
                            hasPaint: m.value >= 0
                        })
                },
                handleTouchStart: e => {
                    e.evt.preventDefault();
                    const a = e.evt.touches[0];
                    _((a.clientX, a.clientY))
                },
                handleTouchMove: e => {
                    e.evt.preventDefault();
                    const a = e.evt.touches[0];
                    R((a.clientX, a.clientY))
                },
                undo: () => {
                    C.value && (v.value[m.value].remove(), m.value--, n.value.getNode().batchDraw(), D.value = !1, M.value = "", a("update:imageData", {
                        hasPaint: m.value >= 0
                    }))
                },
                redo: () => {
                    if (!I.value) return;
                    m.value++;
                    const e = v.value[m.value];
                    n.value.getNode().add(e), n.value.getNode().batchDraw(), a("update:imageData", {
                        hasPaint: m.value >= 0
                    })
                },
                reset: E,
                canUndo: C,
                canRedo: I,
                localStrokeWidth: d,
                showPromptInput: D,
                promptText: M,
                promptInputPosition: L,
                submitPrompt: () => {
                    M.value.trim() && (a("submit:imageData", j(M.value)), M.value = ""), D.value = !1
                },
                hidePromptInput: () => {
                    setTimeout((() => {
                        D.value = !1, M.value = ""
                    }), 200)
                },
                promptInputNode: c,
                handleResize: z,
                getCanvasData: j,
                drawableCanvasContainer: g
            }
        }
    }),
    Y = {
        class: "drawable-canvas-container",
        ref: "drawableCanvasContainer"
    },
    J = {
        key: 0,
        class: "tools-container"
    },
    ee = {
        class: "stroke-width-control"
    },
    ae = {
        class: "stroke-width-value"
    },
    te = ["disabled"],
    oe = ["disabled"],
    ie = ["disabled"],
    ne = ["placeholder"];
const le = n(X, [
        ["render", function(e, a, n, l, w, f) {
            const b = g("UndoIcon"),
                k = g("RedoIcon"),
                y = g("v-image"),
                x = g("v-layer"),
                C = g("v-stage"),
                I = g("SubmitPromptIcon");
            return o(), t("div", Y, [i("div", {
                class: h(["toolbar", {
                    "toolbar-bottom-10": ["inpaint"].includes(e.tool)
                }])
            }, [
                ["inpaint", "erase"].includes(e.tool) ? (o(), t("div", J, [i("div", ee, [i("span", null, u(e.$t("components.agents.image_studio.brush")), 1), r(i("input", {
                    type: "range",
                    min: 1,
                    max: 100,
                    "onUpdate:modelValue": a[0] || (a[0] = a => e.localStrokeWidth = a),
                    class: "stroke-width-slider"
                }, null, 512), [
                    [d, e.localStrokeWidth]
                ]), i("span", ae, u(e.localStrokeWidth), 1)]), a[8] || (a[8] = i("span", {
                    class: "tool-separator"
                }, "|", -1)), i("button", {
                    onClick: a[1] || (a[1] = (...a) => e.undo && e.undo(...a)),
                    disabled: !e.canUndo,
                    class: "tool-button"
                }, [c(b)], 8, te), i("button", {
                    onClick: a[2] || (a[2] = (...a) => e.redo && e.redo(...a)),
                    disabled: !e.canRedo,
                    class: "tool-button"
                }, [c(k)], 8, oe), i("button", {
                    onClick: a[3] || (a[3] = (...a) => e.reset && e.reset(...a)),
                    disabled: !e.canUndo,
                    class: "tool-button button-reset"
                }, u(e.$t("components.agents.image_studio.reset")), 9, ie)])) : s("", !0)
            ], 2), i("div", {
                class: h(["v-stage-wrapper", {
                    "enable-paint": !e.disablePaint
                }]),
                ref: "container"
            }, [c(C, {
                ref: "stage",
                config: e.stageConfig,
                onMousedown: e.startDrawing,
                onMousemove: e.handleMouseMove,
                onMouseup: e.stopDrawing,
                onMouseleave: e.stopDrawing,
                onTouchstart: e.handleTouchStart,
                onTouchmove: e.handleTouchMove,
                onTouchend: e.stopDrawing
            }, {
                default: p((() => [c(x, {
                    ref: "imageLayer"
                }, {
                    default: p((() => [c(y, {
                        config: e.imageConfig
                    }, null, 8, ["config"])])),
                    _: 1
                }, 512), c(x, {
                    ref: "drawLayer"
                }, null, 512), c(x, {
                    ref: "previewLayer"
                }, null, 512)])),
                _: 1
            }, 8, ["config", "onMousedown", "onMousemove", "onMouseup", "onMouseleave", "onTouchstart", "onTouchmove", "onTouchend"])], 2), e.showPromptInput ? (o(), t("div", {
                key: 0,
                class: "prompt-input-overlay",
                style: v(e.promptInputPosition)
            }, [r(i("textarea", {
                ref: "promptInputNode",
                "onUpdate:modelValue": a[4] || (a[4] = a => e.promptText = a),
                onKeyup: a[5] || (a[5] = m(((...a) => e.submitPrompt && e.submitPrompt(...a)), ["enter"])),
                onBlur: a[6] || (a[6] = (...a) => e.hidePromptInput && e.hidePromptInput(...a)),
                placeholder: e.$t("components.agents.image_studio.describe-what-you-want-to-redraw-here"),
                class: "prompt-input",
                rows: "3",
                autofocus: ""
            }, null, 40, ne), [
                [d, e.promptText]
            ]), i("div", {
                onClick: a[7] || (a[7] = (...a) => e.submitPrompt && e.submitPrompt(...a)),
                class: "submit-button"
            }, [c(I, {
                class: "submit-prompt-icon"
            })])], 4)) : s("", !0)], 512)
        }],
        ["__scopeId", "data-v-bca4e9e1"]
    ]),
    se = l({
        name: "AspectRatioCanvas",
        props: {
            imageSource: {
                type: [File, String],
                required: !0
            },
            maxCanvasWidth: {
                type: Number,
                required: !0
            },
            aspectRatio: {
                type: String,
                default: "original"
            }
        },
        emits: ["update:expandData", "image-loaded", "error"],
        setup(e, {
            emit: a
        }) {
            const {
                t: t
            } = M(), o = w({
                x: 0,
                y: 0,
                width: 0,
                height: 0
            }), i = w(null), n = w(null), l = w(null), s = w(null), r = w(null), u = w(14), d = w(u.value / 2), c = w({
                width: 0,
                height: 0
            }), g = w({
                image: null,
                width: 0,
                height: 0
            }), h = async () => {
                if (e.imageSource) {
                    D("original");
                    try {
                        if (e.imageSource instanceof File) {
                            const a = URL.createObjectURL(e.imageSource);
                            await x(a), URL.revokeObjectURL(a)
                        } else "string" == typeof e.imageSource && await x(e.imageSource)
                    } catch (a) {}
                }
            }, p = (e, a, t) => {
                let o, i;
                switch (t) {
                    case "1:1":
                        o = i = Math.max(e, a);
                        break;
                    case "4:3":
                        e / a > 4 / 3 ? (o = e, i = 3 * o / 4) : (i = a, o = 4 * i / 3);
                        break;
                    case "16:9":
                        e / a > 16 / 9 ? (o = e, i = 9 * o / 16) : (i = a, o = 16 * i / 9);
                        break;
                    case "9:16":
                        e / a > 9 / 16 ? (o = e, i = 16 * o / 9) : (i = a, o = 9 * i / 16);
                        break;
                    case "3:4":
                        e / a > 3 / 4 ? (o = e, i = 4 * o / 3) : (i = a, o = 3 * i / 4);
                        break;
                    default:
                        o = e, i = a
                }
                return {
                    width: o,
                    height: i,
                    x: (e - o) / 2,
                    y: (a - i) / 2
                }
            }, v = (e, a, t) => {
                const o = a,
                    i = t,
                    n = o / 2,
                    l = i / 2,
                    s = e.width / e.height;
                let r, u;
                l * s > n ? (r = n, u = r / s) : (u = l, r = u * s);
                return {
                    canvas: {
                        width: o,
                        height: i
                    },
                    image: {
                        width: r,
                        height: u,
                        x: (o - r) / 2,
                        y: (i - u) / 2
                    }
                }
            }, m = () => {
                const e = g.value.image,
                    a = document.createElement("canvas"),
                    t = a.getContext("2d");
                return a.width = e.width, a.height = e.height, t.drawImage(e, 0, 0), a
            }, x = async e => new Promise(((t, n) => {
                const l = new Image;
                l.crossOrigin = "anonymous", l.onload = () => {
                    const e = v(l, i.value.clientWidth, i.value.clientHeight);
                    c.value = e.canvas, g.value = {
                        image: l,
                        ...e.image
                    }, o.value = { ...e.image
                    }, a("image-loaded", {
                        image: m().toDataURL("image/jpeg"),
                        width: e.image.width,
                        height: e.image.height
                    }), t()
                }, l.onerror = () => {
                    const e = new Error("图片加载失败");
                    a("error", e), n(e)
                }, l.src = e
            })), C = f((() => {
                const {
                    x: e,
                    y: a,
                    width: t,
                    height: i
                } = o.value;
                return [{
                    id: "top-left",
                    x: e - d.value,
                    y: a - d.value
                }, {
                    id: "top-right",
                    x: e + t - d.value,
                    y: a - d.value
                }, {
                    id: "bottom-left",
                    x: e - d.value,
                    y: a + i - d.value
                }, {
                    id: "bottom-right",
                    x: e + t - d.value,
                    y: a + i - d.value
                }]
            })), I = () => {
                const e = g.value.width / g.value.image.width,
                    t = g.value.x,
                    i = g.value.x + g.value.width,
                    n = g.value.y,
                    l = g.value.y + g.value.height,
                    s = {
                        left: Math.round((t - o.value.x) / e),
                        right: Math.round((o.value.x + o.value.width - i) / e),
                        top: Math.round((n - o.value.y) / e),
                        bottom: Math.round((o.value.y + o.value.height - l) / e)
                    };
                a("update:expandData", {
                    expandData: s,
                    width: g.value.width,
                    height: g.value.height,
                    expandConfig: o.value,
                    imageConfig: g.value
                })
            }, D = e => {
                if (g.value.image) {
                    const {
                        width: a,
                        height: t,
                        x: n,
                        y: l
                    } = g.value, s = p(a, t, e), r = i.value.clientWidth, u = i.value.clientHeight;
                    let d = 1;
                    const c = 40;
                    s.width > r - c && (d = (r - c) / s.width), s.height * d > u - c && (d = Math.min(d, (u - c) / s.height));
                    const h = s.width * d,
                        v = s.height * d;
                    o.value = {
                        x: n + (a - h) / 2,
                        y: l + (t - v) / 2,
                        width: h,
                        height: v
                    }, g.value = { ...g.value,
                        width: a * d,
                        height: t * d,
                        x: (r - a * d) / 2,
                        y: (u - t * d) / 2
                    }, I()
                }
            }, L = [{
                label: t("components.agents.image_studio.original"),
                value: "original"
            }, {
                label: "1:1",
                value: "1:1"
            }, {
                label: "4:3",
                value: "4:3"
            }, {
                label: "3:4",
                value: "3:4"
            }, {
                label: "16:9",
                value: "16:9"
            }, {
                label: "9:16",
                value: "9:16"
            }];
            b((() => e.imageSource), h);
            const U = T((() => {
                if (!g.value.image) return;
                const a = v(g.value.image, i.value.clientWidth, i.value.clientHeight);
                c.value = a.canvas, g.value = { ...g.value,
                    ...a.image
                };
                const t = p(a.image.width, a.image.height, e.aspectRatio);
                o.value = {
                    x: a.image.x + (a.image.width - t.width) / 2,
                    y: a.image.y + (a.image.height - t.height) / 2,
                    width: t.width,
                    height: t.height
                }, n.value.getNode().batchDraw(), I()
            }), 50);
            return k((async () => {
                h(), window.addEventListener("resize", U)
            })), y((() => {
                window.removeEventListener("resize", U)
            })), {
                container: i,
                stage: n,
                imageLayer: l,
                expandLayer: s,
                stageConfig: c,
                imageConfig: g,
                expandConfig: o,
                expandRect: r,
                anchors: C,
                handleAnchorDragMove: (e, a) => {
                    const t = e.target.getStage().getPointerPosition();
                    let i = o.value.width,
                        n = o.value.height,
                        l = o.value.x,
                        s = o.value.y;
                    const r = c.value.width - 10,
                        u = c.value.height - 10,
                        h = {
                            x: Math.min(Math.max(t.x + d.value, 10), r),
                            y: Math.min(Math.max(t.y + d.value, 10), u)
                        },
                        p = g.value.x,
                        v = g.value.x + g.value.width,
                        m = g.value.y,
                        w = g.value.y + g.value.height;
                    switch (a) {
                        case "top-left":
                            if (i = o.value.width + (o.value.x - h.x), n = o.value.height + (o.value.y - h.y), l = h.x, s = h.y, l > p || s > m) return;
                            break;
                        case "top-right":
                            if (i = h.x - o.value.x, n = o.value.height + (o.value.y - h.y), s = h.y, h.x < v || s > m) return;
                            break;
                        case "bottom-left":
                            if (i = o.value.width + (o.value.x - h.x), n = h.y - o.value.y, l = h.x, l > p || h.y < w) return;
                            break;
                        case "bottom-right":
                            if (i = h.x - o.value.x, n = h.y - o.value.y, h.x < v || h.y < w) return
                    }
                    i > 20 && n > 20 && (o.value = {
                        x: l,
                        y: s,
                        width: i,
                        height: n
                    }, I())
                },
                getBoundFunc: (e, a) => {
                    const {
                        x: t,
                        y: i,
                        width: n,
                        height: l
                    } = o.value;
                    switch (a) {
                        case "top-left":
                            return {
                                x: t - d.value,
                                y: i - d.value
                            };
                        case "top-right":
                            return {
                                x: t + n - d.value,
                                y: i - d.value
                            };
                        case "bottom-left":
                            return {
                                x: t - d.value,
                                y: i + l - d.value
                            };
                        case "bottom-right":
                            return {
                                x: t + n - d.value,
                                y: i + l - d.value
                            }
                    }
                    return e
                },
                ratios: L,
                handleAspectRatioChange: D,
                anchorWidth: u,
                getCursorStyle: e => {
                    switch (e) {
                        case "top-left":
                        case "bottom-right":
                            return "nwse-resize";
                        case "top-right":
                        case "bottom-left":
                            return "nesw-resize";
                        default:
                            return "pointer"
                    }
                }
            }
        }
    }),
    re = {
        class: "aspect-ratio-canvas-container"
    },
    ue = {
        class: "toolbar"
    },
    de = {
        class: "ratio-controls"
    },
    ce = ["onClick", "data-ratio"],
    ge = {
        class: "v-stage-wrapper",
        ref: "container"
    };
const he = n(se, [
        ["render", function(e, a, n, l, s, r) {
            const d = g("v-rect"),
                v = g("v-layer"),
                m = g("v-image"),
                w = g("v-stage");
            return o(), t("div", re, [i("div", ue, [i("div", de, [(o(!0), t(C, null, I(e.ratios, (a => (o(), t("button", {
                key: a.value,
                class: h([{
                    active: e.aspectRatio === a.value
                }, "tool-button"]),
                onClick: t => e.handleAspectRatioChange(a.value),
                "data-ratio": a.value
            }, u(a.label), 11, ce)))), 128))])]), i("div", ge, [c(w, {
                ref: "stage",
                config: e.stageConfig
            }, {
                default: p((() => [c(v, {
                    ref: "expandLayer"
                }, {
                    default: p((() => [c(d, {
                        config: {
                            x: 0,
                            y: 0,
                            width: e.stageConfig.width,
                            height: e.stageConfig.height,
                            fill: "transparent"
                        }
                    }, null, 8, ["config"]), c(d, {
                        ref: "expandRect",
                        config: {
                            x: e.expandConfig.x,
                            y: e.expandConfig.y,
                            width: e.expandConfig.width,
                            height: e.expandConfig.height,
                            stroke: "#aaa",
                            strokeWidth: 1,
                            fill: "rgba(153, 153, 153, 0.5)"
                        }
                    }, null, 8, ["config"])])),
                    _: 1
                }, 512), c(v, {
                    ref: "imageLayer"
                }, {
                    default: p((() => [c(m, {
                        config: e.imageConfig
                    }, null, 8, ["config"])])),
                    _: 1
                }, 512), c(v, null, {
                    default: p((() => [(o(!0), t(C, null, I(e.anchors, (t => (o(), D(d, {
                        key: t.id,
                        config: {
                            x: t.x,
                            y: t.y,
                            width: e.anchorWidth,
                            height: e.anchorWidth,
                            fill: "#fff",
                            stroke: "rgba(0, 0, 0, 0.45)",
                            strokeWidth: 2,
                            draggable: !0,
                            name: t.id,
                            cursor: e.getCursorStyle(t.id),
                            dragBoundFunc: a => e.getBoundFunc(a, t.id)
                        },
                        onDragmove: a => e.handleAnchorDragMove(a, t.id),
                        onMouseenter: a => a.target.getStage().container().style.cursor = e.getCursorStyle(t.id),
                        onMouseleave: a[0] || (a[0] = e => e.target.getStage().container().style.cursor = "default")
                    }, null, 8, ["config", "onDragmove", "onMouseenter"])))), 128))])),
                    _: 1
                })])),
                _: 1
            }, 8, ["config"])], 512)])
        }],
        ["__scopeId", "data-v-35738fb2"]
    ]),
    pe = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const ve = {
        render: function(e, a) {
            return o(), t("svg", pe, a[0] || (a[0] = [L('<g id="Frame"><path id="Vector" d="M12.5 5H15.6818C16.5389 5 16.9675 5 17.2337 5.23537C17.5 5.47073 17.5 5.84954 17.5 6.60714V14.6429C17.5 15.4005 17.5 15.7793 17.2337 16.0146C16.9675 16.25 16.5389 16.25 15.6818 16.25H12.5" stroke="#232425" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><g id="Vector_2"><path d="M7.5 5H4.31818C3.46108 5 3.03253 5 2.76627 5.23537C2.5 5.47073 2.5 5.84954 2.5 6.60714V14.6429C2.5 15.4005 2.5 15.7793 2.76627 16.0146C3.03253 16.25 3.46108 16.25 4.31818 16.25H7.5" fill="#232425"></path><path d="M7.5 5H4.31818C3.46108 5 3.03253 5 2.76627 5.23537C2.5 5.47073 2.5 5.84954 2.5 6.60714V14.6429C2.5 15.4005 2.5 15.7793 2.76627 16.0146C3.03253 16.25 3.46108 16.25 4.31818 16.25H7.5V5Z" stroke="#232425" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g><path id="Vector_3" d="M10 2.5V17.5" stroke="#232425" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g>', 1)]))
        }
    },
    me = {
        width: "24",
        height: "25",
        viewBox: "0 0 24 25",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const we = {
        render: function(e, a) {
            return o(), t("svg", me, a[0] || (a[0] = [i("path", {
                d: "M16.1868 4.80961L16.7709 4.22551C17.2357 3.76089 17.8659 3.49992 18.5231 3.5C19.1803 3.50008 19.8105 3.76122 20.2751 4.22596C20.7397 4.6907 21.0007 5.32097 21.0006 5.97813C21.0005 6.63528 20.7394 7.26549 20.2746 7.73011L19.6896 8.31421C19.6896 8.31421 18.4494 8.24131 17.355 7.14601C16.2597 6.05161 16.1868 4.80961 16.1868 4.80961ZM16.1868 4.80961L10.8183 10.1781C10.4547 10.5417 10.2729 10.7235 10.1163 10.9242C9.93152 11.1612 9.77432 11.4156 9.64472 11.6874C9.53582 11.9169 9.45482 12.1608 9.29192 12.6486L8.77082 14.2101M8.77082 14.2101L8.43422 15.2199C8.39463 15.3377 8.38868 15.4642 8.41704 15.5851C8.4454 15.7061 8.50695 15.8168 8.59476 15.9047C8.68256 15.9926 8.79315 16.0542 8.91409 16.0827C9.03502 16.1112 9.16151 16.1054 9.27932 16.0659L10.29 15.7293M8.77082 14.2101L10.29 15.7293M19.6905 8.31331L14.322 13.6818C13.9584 14.0454 13.7766 14.2272 13.5759 14.3838C13.3394 14.5682 13.0835 14.7263 12.8127 14.8554C12.5832 14.9643 12.3393 15.0453 11.8515 15.2082L10.29 15.7293",
                stroke: "black",
                "stroke-width": "1.5"
            }, null, -1), i("path", {
                d: "M21 12.5C21 17.4707 16.9707 21.5 12 21.5C7.0293 21.5 3 17.4707 3 12.5C3 7.5293 7.0293 3.5 12 3.5",
                stroke: "black",
                "stroke-width": "1.5",
                "stroke-linecap": "round"
            }, null, -1)]))
        }
    },
    fe = {
        width: "25",
        height: "25",
        viewBox: "0 0 25 25",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const be = {
        render: function(e, a) {
            return o(), t("svg", fe, a[0] || (a[0] = [i("path", {
                d: "M3.5 21.5H21.5",
                stroke: "black",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), i("path", {
                d: "M15.3824 3.5L5.5 13.9L7.97059 16.5H11.2647L19.5 7.83333L15.3824 3.5Z",
                stroke: "black",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    ke = {
        width: "25",
        height: "25",
        viewBox: "0 0 25 25",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const ye = {
        render: function(e, a) {
            return o(), t("svg", ke, a[0] || (a[0] = [L('<path d="M9.5 5.5H21.5C22.0523 5.5 22.5 5.9477 22.5 6.5V19.5C22.5 20.0523 22.0523 20.5 21.5 20.5H9.5" stroke="black" stroke-width="1.5"></path><path d="M2.5 6.5C2.5 5.9477 2.94771 5.5 3.5 5.5H9.5V20.5H3.5C2.94771 20.5 2.5 20.0523 2.5 19.5V6.5Z" stroke="black" stroke-width="1.5"></path><path d="M2.5 8.5L9.5 8.5" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.5 11.5L9.5 11.5" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.5 14.5L9.5 14.5" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.5 17.5L9.5 17.5" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M19 8.5L13 17" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>', 7)]))
        }
    },
    xe = {
        width: "24",
        height: "25",
        viewBox: "0 0 24 25",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Ce = {
        render: function(e, a) {
            return o(), t("svg", xe, a[0] || (a[0] = [L('<path d="M15 3.5H21V9.5" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9 3.5H3V9.5" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M15 21.5H21V15.5" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9 21.5H3V15.5" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M21 3.5L14.5 10" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M9.5 15L3 21.5" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>', 6)]))
        }
    },
    Ie = U("aidriveUpload", {
        state: () => ({
            uploads: [],
            minimized: !1,
            lastCompletedPath: "",
            activeUploads: 0
        }),
        getters: {
            hasUploads: e => e.uploads.length > 0,
            activeCount: e => e.uploads.filter((e => "pending" === e.status || "uploading" === e.status)).length,
            pendingCount: e => e.uploads.filter((e => "pending" === e.status)).length,
            completedCount: e => e.uploads.filter((e => "completed" === e.status)).length,
            errorCount: e => e.uploads.filter((e => "error" === e.status)).length,
            allFinished: e => e.uploads.length > 0 && e.uploads.every((e => "completed" === e.status || "error" === e.status)),
            canStartMoreUploads: e => e.activeUploads < 3
        },
        actions: {
            addUpload(e, a, t = !1) {
                const o = new AbortController;
                this.minimized = !1, this.uploads.push({
                    file: e.file,
                    name: e.name || e.file.name,
                    path: a,
                    size: e.size || e.file.size,
                    progress: 0,
                    status: "pending",
                    abortController: o
                });
                const i = this.uploads.length - 1;
                return this.checkAndProcessQueue(), i
            },
            checkAndProcessQueue() {
                if (this.activeUploads >= 3) return;
                const e = this.uploads.map(((e, a) => ({
                    upload: e,
                    index: a
                }))).filter((e => "pending" === e.upload.status));
                for (const {
                        index: a
                    } of e) {
                    if (!(this.activeUploads < 3)) break;
                    this.processUpload(a)
                }
            },
            updateProgress(e, a) {
                e >= 0 && e < this.uploads.length && (this.uploads[e].progress = a)
            },
            setStatus(e, a, t) {
                if (e >= 0 && e < this.uploads.length) {
                    const o = this.uploads[e],
                        i = o.status;
                    o.status = a, "uploading" === a && "uploading" !== i ? this.activeUploads++ : "uploading" === i && "uploading" !== a && (this.activeUploads--, setTimeout((() => this.checkAndProcessQueue()), 0)), t && (o.error = t), "completed" === a && "completed" !== i && (this.lastCompletedPath = o.path.endsWith("/") ? o.path + o.name : o.path + "/" + o.name)
                }
            },
            cancelUpload(e) {
                if (e >= 0 && e < this.uploads.length) {
                    const a = this.uploads[e];
                    "uploading" === a.status && a.xhr && a.xhr.abort(), this.setStatus(e, "error", "Upload cancelled")
                }
            },
            retryUpload(e) {
                if (e >= 0 && e < this.uploads.length) {
                    const a = this.uploads[e];
                    "error" === a.status && (a.status = "pending", a.progress = 0, a.error = void 0, a.abortController = new AbortController, this.checkAndProcessQueue())
                }
            },
            removeUpload(e) {
                if (e >= 0 && e < this.uploads.length) {
                    const a = this.uploads[e],
                        t = "uploading" === a.status;
                    t && a.xhr && a.xhr.abort(), this.uploads.splice(e, 1), t && setTimeout((() => this.checkAndProcessQueue()), 0)
                }
            },
            clearCompleted() {
                this.uploads = this.uploads.filter((e => "completed" !== e.status))
            },
            dismissAllUploads() {
                this.uploads.forEach(((e, a) => {
                    "uploading" === e.status && this.cancelUpload(a)
                })), this.uploads = [], this.activeUploads = 0
            },
            minimizeUploads() {
                this.minimized = !this.minimized
            },
            async processUpload(e) {
                if (e >= 0 && e < this.uploads.length) {
                    const t = this.uploads[e];
                    if ("pending" !== t.status) return;
                    if (this.activeUploads >= 3) return;
                    this.setStatus(e, "uploading");
                    try {
                        const a = encodeURIComponent(t.name),
                            o = t.path.endsWith("/") ? `${t.path}${a}` : `${t.path}/${a}`;
                        await this.directUpload(t.file, o, e), this.setStatus(e, "completed")
                    } catch (a) {
                        if (a instanceof Error && "Upload aborted" === a.message) return;
                        a instanceof Error ? this.setStatus(e, "error", a.message) : this.setStatus(e, "error", "Unknown error occurred")
                    }
                }
            },
            async directUpload(e, a, t) {
                const o = this.uploads[t];
                if (!o || !o.abortController) return;
                const i = a.startsWith("/") ? a.substring(1) : a,
                    n = await q.getUploadUrl(i);
                if ("success" !== n.status) throw new Error(`Failed to get upload URL: ${n.message}`);
                const {
                    upload_url: l,
                    token: s
                } = n.data;
                return new Promise(((a, n) => {
                    const r = new XMLHttpRequest;
                    o.xhr = r, r.upload.addEventListener("progress", (e => {
                        if (e.lengthComputable) {
                            const a = Math.round(e.loaded / e.total * 100);
                            this.updateProgress(t, a)
                        }
                    })), r.addEventListener("load", (async () => {
                        if (r.status >= 200 && r.status < 300) try {
                            const t = await q.confirmUpload(i, s, e.type || "application/octet-stream");
                            if ("success" !== t.status) throw new Error(`Failed to confirm upload: ${t.message}`);
                            a(t)
                        } catch (t) {
                            n(t)
                        } else n(new Error(`HTTP error ${r.status}: ${r.statusText}`))
                    })), r.addEventListener("error", (() => {
                        n(new Error("Network error occurred during upload"))
                    })), r.addEventListener("abort", (() => {
                        n(new Error("Upload aborted"))
                    })), r.open("PUT", l), r.setRequestHeader("x-ms-blob-type", "BlockBlob"), o.abortController && o.abortController.signal.addEventListener("abort", (() => {
                        r.abort()
                    })), r.send(e)
                }))
            }
        }
    }),
    De = async e => {
        const a = [];
        for (let t = 0; t < e.files.length; t++) {
            const o = e.files[t];
            a.push({
                file: o,
                name: o.name,
                size: o.size,
                isDir: !1
            })
        }
        return a
    },
    Me = (e, a) => {
        if (!a || !Array.isArray(a) || 0 === a.length) return [];
        const t = new Set(a.map((e => e.name)));
        return e.filter((e => t.has(e.name)))
    },
    Le = async (e, a) => {
        const t = Ie();
        for (const o of e) {
            const e = V(a);
            t.addUpload(o, e)
        }
    },
    Ue = l({
        name: "ImageEditor",
        components: {
            DrawableCanvas: le,
            AspectRatioCanvas: he,
            ImageGenerating: z,
            QuotaExceed: e,
            CompareIcon: ve,
            RedrawIcon: we,
            EraserIcon: be,
            RemoveBackgroundIcon: ye,
            ExpandImageIcon: Ce,
            DownloadIcon: A,
            BackIcon: B,
            AIDriveIcon: O
        },
        props: {
            file: {
                type: [File, String],
                default: null
            },
            tool: {
                type: String,
                default: "inpaint",
                validator: e => ["inpaint", "outpaint", "erase", "rmbg", "upscale"].includes(e)
            },
            aiDriveFilePath: {
                type: String,
                default: "/"
            },
            toolModeCallback: {
                type: Function,
                default: null
            }
        },
        setup(e) {
            const {
                t: a
            } = M(), t = S("currentUser"), o = w(e.file), i = w(null), n = w(""), l = w(null), s = w(800), r = w(!0), u = w(!1), d = w(null), c = w("original"), g = w(null), h = w(null), p = w(null), v = w(""), m = w(!1), k = w({
                width: 512,
                height: 512,
                bgPosition: "center",
                left: 0,
                top: 0,
                bgWidth: 512,
                bgHeight: 512,
                imageMarginLeft: 0,
                imageMarginTop: 0
            }), y = w(!1), x = w(null), C = w(null), I = w(!1), D = w(0), L = w(!1), U = w(null), _ = w(!0), R = w(""), j = Q(), E = [{
                name: "inpaint",
                label: a("components.agents.image_studio.magic-redraw"),
                icon: we,
                button: ""
            }, {
                name: "outpaint",
                label: a("components.agents.image_studio.image-expand"),
                icon: Ce,
                button: a("components.agents.image_studio.apply")
            }, {
                name: "erase",
                label: a("components.agents.image_studio.magic-eraser"),
                icon: be,
                button: a("components.agents.image_studio.apply")
            }, {
                name: "rmbg",
                label: a("components.agents.image_studio.background-remover"),
                icon: ye,
                button: a("components.agents.image_studio.remove-background")
            }, {
                name: "upscale",
                label: a("components.agents.image_studio.image-unblur"),
                icon: Ce,
                button: a("components.agents.image_studio.unblur")
            }], T = w(E.find((a => a.name === e.tool))), z = f((() => !!_.value && (!y.value && (!!l.value && (("rmbg" !== T.value.name || !I.value) && ("erase" === T.value.name ? m.value : "outpaint" === T.value.name ? g.value : "inpaint" !== T.value.name || v.value && m.value)))))), A = f((() => k.value.width / k.value.height)), B = f((() => ["erase", "inpaint"].includes(T.value.name))), O = async () => {
                var t, o;
                if (!i.value || (t = i.value.width, o = i.value.height, "upscale" === T.value.name && (t > 2048 || o > 2048) ? (R.value = a("components.agents.image_studio.image-is-already-clear-enough"), _.value = !1, 0) : (_.value = !0, 1)))
                    if (L.value) U.value.trigger();
                    else if (z.value && !y.value) {
                    if ("outpaint" !== T.value.name) {
                        const e = C.value.getCanvasData();
                        l.value = e.image
                    }
                    if (l.value) {
                        let t = {};
                        "outpaint" === T.value.name && (t = {
                            expand_pixels: g.value
                        });
                        try {
                            r.value = !1, y.value = !0;
                            let a = null,
                                o = null,
                                i = 1;
                            const n = await P.getRecaptchaToken("image_studio");
                            if (t.g_recaptcha_token = n, p.value ? ({
                                    maskUrl: o,
                                    scale: i
                                } = await $(l.value, 1024, .95)) : ({
                                    url: a,
                                    scale: i
                                } = await H(l.value, 1024, .95, B.value)), t && t.expand_pixels && (t.expand_pixels = {
                                    left: parseInt(t.expand_pixels.left * i),
                                    top: parseInt(t.expand_pixels.top * i),
                                    right: parseInt(t.expand_pixels.right * i),
                                    bottom: parseInt(t.expand_pixels.bottom * i)
                                }), p.value && (t.mask_url = o, t.ref_task_id = p.value), e.toolModeCallback) return void e.toolModeCallback(a, T.value.name, v.value, t);
                            const s = W(a, T.value.name, v.value, t),
                                u = await s;
                            h.value = u.task_id
                        } catch (n) {
                            y.value = !1, r.value = !0, n instanceof F ? L.value = !0 : n instanceof N && j.error(a("components.agents.image_studio.server-overloaded"))
                        }
                    }
                }
            }, V = f((() => t.value && t.value.gk_dogfood));
            b((() => e.file), (e => {
                e && (o.value = e)
            })), b((() => L.value && null !== t.value), (e => {
                e && U.value.trigger()
            }));
            const q = f((() => o.value ? o.value instanceof File ? URL.createObjectURL(o.value) : "string" == typeof o.value ? o.value : null : null));
            return {
                tip: R,
                canGoOn: _,
                quota_exceeded: L,
                quotaExceed: U,
                aspectRatioCanvas: x,
                drawableCanvas: C,
                imageSource: o,
                imageUrl: n,
                imageData: l,
                handleFinished: e => {
                    if (e.image_urls.length > 0) {
                        const a = document.createElement("canvas"),
                            t = a.getContext("2d");
                        if (a.width = e.firstImage.width, a.height = e.firstImage.height, i.value = e.firstImage, t.drawImage(e.firstImage, 0, 0), o.value = a.toDataURL("rmbg" === T.value.name ? "image/png" : "image/jpeg"), I.value = !0, y.value = !1, p.value = h.value, h.value = null, "rmbg" === T.value.name) return;
                        D.value++, r.value = !0
                    }
                },
                handleFileUpload: e => {
                    const a = e.target.files[0];
                    a && a.type.startsWith("image/") && (o.value = a)
                },
                loadImageUrl: () => {
                    n.value.trim() && (o.value = n.value)
                },
                handleImageDataUpdate: async e => {
                    e.image && (l.value = e.image), m.value = e.hasPaint, e.prompt && (v.value = e.prompt, await O())
                },
                handleImageLoaded: e => {
                    l.value = e.image, k.value = {
                        width: e.width,
                        height: e.height
                    }
                },
                handleImageResized: e => {
                    k.value = { ...k.value,
                        width: e.width,
                        height: e.height
                    }
                },
                handleImageError: e => {
                    o.value = null
                },
                processImage: O,
                resetImage: () => {
                    o.value = null, n.value = "", l.value = null
                },
                canvasWidth: s,
                isEditMode: r,
                currentTool: T,
                tools: E,
                selectTool: e => {
                    T.value = e
                },
                toggleMode: () => {
                    r.value = !r.value
                },
                showOriginal: u,
                processedImage: d,
                selectedRatio: c,
                expandData: g,
                handleExpandData: e => {
                    g.value = e.expandData, k.value = {
                        width: e.width,
                        height: e.height,
                        bgPosition: "absolute",
                        left: e.expandConfig.x,
                        top: e.expandConfig.y,
                        bgWidth: e.expandConfig.width,
                        bgHeight: e.expandConfig.height,
                        imageMarginLeft: e.imageConfig.x - e.expandConfig.x,
                        imageMarginTop: e.imageConfig.y - e.expandConfig.y
                    }
                },
                taskId: h,
                imageSourceDisplay: q,
                canProcessImage: z,
                imageRatio: A,
                imageDisplaySize: k,
                downloadImage: () => {
                    const e = document.createElement("a");
                    if (o.value instanceof File) e.href = URL.createObjectURL(o.value), e.download = o.value.name;
                    else {
                        e.href = o.value;
                        const a = o.value.startsWith("data:image/png") ? "png" : "jpg";
                        e.download = `image.${a}`
                    }
                    e.click(), o.value instanceof File && URL.revokeObjectURL(e.href)
                },
                processingRequest: y,
                hasGenerated: I,
                generatingIndex: D,
                needPng: B,
                showSaveToAiDrive: V,
                saveToAiDrive: async () => {
                    if (o.value && I.value) try {
                        let t;
                        (new Date).getTime();
                        let i = "jpg";
                        if (o.value instanceof File) {
                            t = o.value;
                            const e = t.name.split(".");
                            e.length > 1 && (i = e[e.length - 1])
                        } else if ("string" == typeof o.value) {
                            const e = await fetch(o.value),
                                a = await e.blob();
                            i = o.value.startsWith("data:image/png") ? "png" : "jpg", t = new File([a], `image.${i}`, {
                                type: a.type
                            })
                        }
                        if (t) {
                            const o = new Date,
                                n = o.getFullYear(),
                                l = String(o.getMonth() + 1).padStart(2, "0"),
                                s = `${n}_${l}_${String(o.getDate()).padStart(2,"0")}`,
                                r = e.aiDriveFilePath || "/";
                            let u = r.split("/").slice(0, -1).join("/") || "/",
                                d = r.split("/").pop() || t.name;
                            const c = d.lastIndexOf(".");
                            c > d.lastIndexOf("/") && -1 !== c && (d = d.substring(0, c)), await Le([{
                                file: t,
                                name: `${d}_${e.tool}_edited_${s}.${i}`,
                                size: t.size,
                                isDir: !1
                            }], u), j.success(a("components.agents.image_studio.saved-to-ai-drive"))
                        }
                    } catch (t) {
                        j.error(a("components.agents.image_studio.failed-to-save-to-ai-drive"))
                    }
                }
            }
        }
    }),
    Se = {
        class: "header"
    },
    Pe = {
        class: "title"
    },
    _e = ["disabled"],
    Re = {
        class: "download-button-text show-text"
    },
    je = ["disabled"],
    Ee = {
        class: "download-button-text show-text"
    },
    Te = {
        key: 0,
        class: "upload-area"
    },
    ze = {
        class: "url-input-container"
    },
    $e = {
        key: 1,
        class: "editor-container"
    },
    He = {
        key: 0,
        class: "toolbar"
    },
    We = ["onClick"],
    Fe = {
        class: "editor-inner-container"
    },
    Ne = ["src"],
    Ae = {
        key: 2,
        class: "controls"
    },
    Be = {
        key: 0,
        class: "tip"
    },
    Oe = ["disabled"];
const Ve = n(Ue, [
    ["render", function(n, l, m, w, f, b) {
        const k = g("BackIcon"),
            y = g("DownloadIcon"),
            x = g("AIDriveIcon"),
            M = g("aspect-ratio-canvas"),
            L = g("drawable-canvas"),
            U = g("ImageGenerating"),
            S = e,
            P = a;
        return o(), t("div", {
            class: h(["image-editor", {
                "tool-callback-mode": !!n.toolModeCallback
            }])
        }, [i("div", Se, [i("div", {
            class: "close-button",
            onClick: l[0] || (l[0] = e => n.$emit("close"))
        }, [c(k)]), i("span", Pe, u(n.currentTool.label), 1), i("button", {
            class: "download-button",
            onClick: l[1] || (l[1] = (...e) => n.downloadImage && n.downloadImage(...e)),
            disabled: !n.hasGenerated
        }, [c(y), i("span", Re, u(n.$t("components.agents.image_studio.download")), 1)], 8, _e), i("button", {
            onClick: l[2] || (l[2] = (...e) => n.saveToAiDrive && n.saveToAiDrive(...e)),
            class: "save-to-ai-drive-button",
            disabled: !n.hasGenerated
        }, [c(x), i("span", Ee, u(n.$t("components.agents.image_studio.save-to-ai-drive")), 1)], 8, je), r(i("button", {
            class: "compare-button",
            onMousedown: l[3] || (l[3] = e => n.showOriginal = !0),
            onMouseup: l[4] || (l[4] = e => n.showOriginal = !1),
            onMouseleave: l[5] || (l[5] = e => n.showOriginal = !1)
        }, " Compare Original ", 544), [
            [_, !1]
        ])]), n.imageSource ? s("", !0) : (o(), t("div", Te, [i("input", {
            type: "file",
            accept: "image/*",
            onChange: l[6] || (l[6] = (...e) => n.handleFileUpload && n.handleFileUpload(...e)),
            class: "file-input"
        }, null, 32), l[10] || (l[10] = i("div", {
            class: "upload-hint"
        }, "Click or drag image here", -1)), i("div", ze, [r(i("input", {
            type: "text",
            "onUpdate:modelValue": l[7] || (l[7] = e => n.imageUrl = e),
            placeholder: "Or enter image URL",
            class: "url-input"
        }, null, 512), [
            [d, n.imageUrl]
        ]), i("button", {
            onClick: l[8] || (l[8] = (...e) => n.loadImageUrl && n.loadImageUrl(...e))
        }, "Load URL Image")])])), n.imageSource ? (o(), t("div", $e, [n.isEditMode ? s("", !0) : r((o(), t("div", He, [(o(!0), t(C, null, I(n.tools, (e => (o(), t("button", {
            key: e.name,
            class: h({
                active: n.currentTool.name === e.name
            }),
            onClick: a => n.selectTool(e)
        }, [(o(), D(E(e.icon))), j(" " + u(e.label), 1)], 10, We)))), 128))], 512)), [
            [_, !1]
        ]), i("div", Fe, ["outpaint" === n.currentTool.name ? (o(), D(M, {
            key: 0,
            ref: "aspectRatioCanvas",
            "image-source": n.imageSource,
            "max-canvas-width": n.canvasWidth,
            "aspect-ratio": n.selectedRatio,
            "onUpdate:expandData": n.handleExpandData,
            onImageLoaded: n.handleImageLoaded,
            onError: n.handleImageError
        }, null, 8, ["image-source", "max-canvas-width", "aspect-ratio", "onUpdate:expandData", "onImageLoaded", "onError"])) : (o(), D(L, {
            key: 1,
            ref: "drawableCanvas",
            "image-source": n.imageSource,
            tool: n.currentTool.name,
            "stroke-color": "#ffffff",
            "prompt-input": "inpaint" === n.currentTool.name,
            "disable-paint": ["rmbg", "upscale"].includes(n.currentTool.name),
            "need-png": n.needPng,
            "tool-callback-mode": !!n.toolModeCallback,
            "onSubmit:imageData": n.handleImageDataUpdate,
            "onUpdate:imageData": n.handleImageDataUpdate,
            onImageLoaded: n.handleImageLoaded,
            onImageResized: n.handleImageResized,
            onError: n.handleImageError
        }, null, 8, ["image-source", "tool", "prompt-input", "disable-paint", "need-png", "tool-callback-mode", "onSubmit:imageData", "onUpdate:imageData", "onImageLoaded", "onImageResized", "onError"]))]), r(i("div", {
            class: h(["preview-container", {
                "fade-leave-active": n.isEditMode,
                "background-image-absolute": "absolute" === n.imageDisplaySize.bgPosition
            }])
        }, [i("div", {
            class: h({
                "background-image": !n.processingRequest || "absolute" === n.imageDisplaySize.bgPosition
            }),
            style: v("absolute" === n.imageDisplaySize.bgPosition ? {
                width: `${n.imageDisplaySize.bgWidth}px`,
                height: `${n.imageDisplaySize.bgHeight}px`,
                marginLeft: `${n.imageDisplaySize.left}px`,
                marginTop: `${n.imageDisplaySize.top}px`
            } : {
                width: `${n.imageDisplaySize.width}px`,
                height: `${n.imageDisplaySize.height}px`
            })
        }, [(o(), D(U, {
            key: n.generatingIndex,
            style: v("absolute" === n.imageDisplaySize.bgPosition ? {
                marginLeft: `${n.imageDisplaySize.imageMarginLeft}px`,
                marginTop: `${n.imageDisplaySize.imageMarginTop}px`,
                width: `${n.imageDisplaySize.width}px`,
                height: `${n.imageDisplaySize.height}px`
            } : {}),
            "task-id": n.taskId,
            "display-original-image": !0,
            "background-image-url": n.imageSourceDisplay,
            "enable-preview": !1,
            "min-height": 100,
            "aspect-ratio": n.imageRatio,
            "enable-breathing-opacity": !0,
            onFinished: n.handleFinished
        }, null, 8, ["style", "task-id", "background-image-url", "aspect-ratio", "onFinished"]))], 6), r(i("img", {
            src: n.imageSourceDisplay,
            class: "original-image"
        }, null, 8, Ne), [
            [_, n.showOriginal]
        ])], 2), [
            [_, !n.isEditMode]
        ])])) : s("", !0), n.imageSource ? (o(), t("div", Ae, [n.tip ? (o(), t("div", Be, u(n.tip), 1)) : s("", !0), n.currentTool.button ? (o(), t("button", {
            key: 1,
            class: "process-button",
            onClick: l[9] || (l[9] = (...e) => n.processImage && n.processImage(...e)),
            disabled: !n.canProcessImage || n.processingRequest
        }, u(n.currentTool.button), 9, Oe)) : s("", !0)])) : s("", !0), c(S, {
            ref: "quotaExceed"
        }, null, 512), c(R, {
            name: "fade"
        }, {
            default: p((() => [c(P, {
                styleClass: "index"
            })])),
            _: 1
        })], 2)
    }],
    ["__scopeId", "data-v-ab8fa342"]
]);
export {
    Ve as I, Me as c, Le as h, De as s, Ie as u
};