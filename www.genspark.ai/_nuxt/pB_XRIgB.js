import {
    ak as t,
    at as e
} from "./Cf0SOiw0.js";

function n(t) {
    return "string" == typeof t ? t.endsWith("px") ? Number(t.slice(0, t.length - 2)) : Number(t) : t
}

function i(t) {
    if (null != t) return "number" == typeof t ? `${t}px` : t.endsWith("px") ? t : `${t}px`
}

function r(t, e) {
    const n = t.trim().split(/\s+/g),
        i = {
            top: n[0]
        };
    switch (n.length) {
        case 1:
            i.right = n[0], i.bottom = n[0], i.left = n[0];
            break;
        case 2:
            i.right = n[1], i.left = n[1], i.bottom = n[0];
            break;
        case 3:
            i.right = n[1], i.bottom = n[2], i.left = n[1];
            break;
        case 4:
            i.right = n[1], i.bottom = n[2], i.left = n[3];
            break;
        default:
            throw new Error("[seemly/getMargin]:" + t + " is not a valid value.")
    }
    return void 0 === e ? i : i[e]
}

function a(t, e) {
    const [n, i] = t.split(" ");
    return {
        row: n,
        col: i || n
    }
}
const {
    cubicBezierEaseInOut: o
} = e;

function s({
    name: e = "fade-in",
    enterDuration: n = "0.2s",
    leaveDuration: i = "0.2s",
    enterCubicBezier: r = o,
    leaveCubicBezier: a = o
} = {}) {
    return [t(`&.${e}-transition-enter-active`, {
        transition: `all ${n} ${r}!important`
    }), t(`&.${e}-transition-leave-active`, {
        transition: `all ${i} ${a}!important`
    }), t(`&.${e}-transition-enter-from, &.${e}-transition-leave-to`, {
        opacity: 0
    }), t(`&.${e}-transition-leave-from, &.${e}-transition-enter-to`, {
        opacity: 1
    })]
}
export {
    r as a, n as d, s as f, a as g, i as p
};