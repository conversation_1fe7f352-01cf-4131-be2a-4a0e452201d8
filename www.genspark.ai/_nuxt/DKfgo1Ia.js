var t, n;

function o(t, n, o, r, e, i, a) {
    let u = (r - e - 2 * i) / n;
    return u * t <= o - 2 * a || (u = (o - 2 * a) / t), u
}

function r(t, n) {
    return Math.sqrt((t.x - n.x) ** 2 + (t.y - n.y) ** 2)
}

function e({
    resizeFrom: t,
    left: n,
    top: o,
    right: e,
    bottom: i
}) {
    switch (t) {
        case "top-left":
        case "bottom-right":
            return r({
                x: n,
                y: o
            }, {
                x: e,
                y: i
            });
        case "top-right":
        case "bottom-left":
            return r({
                x: n,
                y: i
            }, {
                x: e,
                y: o
            })
    }
}

function i(t, n, o) {
    const r = o.x - n.x,
        e = o.y - n.y,
        i = t.x - n.x,
        a = t.y - n.y,
        u = r * r + e * e;
    if (0 === u) return 0;
    return (i * r + a * e) / u
}
"localhost" === (null == (t = null == window ? void 0 : window.location) ? void 0 : t.hostname) || null == (n = null == window ? void 0 : window.location) || n.hostname;

function a(...t) {}

function u(...t) {}

function s(...t) {}

function c(t, n, o) {
    const r = {
            x: n.x - t.x,
            y: n.y - t.y
        },
        e = {
            x: o.x - t.x,
            y: o.y - t.y
        },
        i = Math.atan2(r.y, r.x);
    return (Math.atan2(e.y, e.x) - i) / Math.PI * 180
}

function l(t) {
    for (; t < -180;) t += 360;
    for (; t > 180;) t -= 360;
    return t
}

function x(t, n, o) {
    const {
        left: r,
        top: e
    } = o.getBoundingClientRect();
    return {
        x: (t.x - r) / n,
        y: (t.y - e) / n
    }
}

function f(t) {
    const n = t.match(/design_studio\/poster\/([^/]+)\/edit/);
    if (!n) return null;
    return n[1]
}
export {
    i as a, x as b, o as c, e as d, c as e, u as f, f as g, s as h, a as l, l as n
};