import {
    _ as e,
    r as a,
    C as t,
    c as i,
    v as l,
    h as n,
    U as s,
    x as o,
    d as r,
    f as h,
    b as d,
    l as u,
    e as v,
    y as c,
    a as g,
    q as p,
    p as w,
    F as f,
    n as m,
    o as y
} from "./Cf0SOiw0.js";
import {
    _ as k
} from "./mJse5lCL.js";
import {
    I as _
} from "./CHwVUzYg.js";
import {
    L as C
} from "./CRIx66FB.js";
import {
    I as L
} from "./DqWfLcpp.js";
import {
    C as M
} from "./Bm_HbXT2.js";
const b = {
        class: "chart-container"
    },
    j = {
        key: 0,
        class: "logo"
    },
    $ = {
        key: 1,
        class: "footer"
    },
    x = ["src"],
    F = ["src"],
    S = e({
        __name: "MultipleSankeyCharts",
        props: {
            searchFilter: {
                type: Object,
                default: () => ({})
            },
            data: {
                type: Array,
                default: () => []
            },
            width: {
                type: Number,
                default: 600
            },
            height: {
                type: Number,
                required: !1,
                default: null
            },
            showLogoFooter: {
                type: Boolean,
                default: !0
            },
            showLogo: {
                type: Boolean,
                default: !0
            },
            showTitle: {
                type: Boolean,
                default: !0
            },
            showViewMore: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["finished_loading"],
        setup(e, {
            emit: S
        }) {
            const T = S,
                B = e,
                E = a(!1);
            E.value = t.isDarkMode();
            const N = a(0),
                P = a(null),
                W = a([]),
                I = a(400),
                O = a(B.data),
                q = i((() => O.value[N.value] || null)),
                z = a(800),
                A = i((() => {
                    if (W.value[N.value]) {
                        const {
                            width: e,
                            height: a
                        } = W.value[N.value];
                        let t;
                        if (V.value) {
                            const i = .9 * window.innerWidth / e,
                                l = .9 * window.innerHeight / a;
                            t = Math.min(i, l)
                        } else if (t = z.value / e, B.height) {
                            const e = B.height / a;
                            t = Math.min(t, e)
                        }
                        return {
                            transform: `scale(${t})`,
                            transformOrigin: "center center",
                            width: `${e}px`,
                            height: `${a}px`
                        }
                    }
                    return {}
                })),
                H = i((() => {
                    if (0 === W.value.length) return I.value;
                    const e = W.value.map((e => {
                        if (e.width && e.width > 0) {
                            const a = B.width / e.width;
                            if (B.height) {
                                const t = B.height / e.height;
                                return e.height * Math.min(a, t)
                            }
                            return e.height * a
                        }
                        return I.value
                    }));
                    return Math.max(...e, I.value)
                })),
                U = () => {
                    D()
                },
                D = () => {
                    if (P.value) {
                        const {
                            chartHeight: e,
                            chartWidth: a
                        } = P.value;
                        a && e && (W.value[N.value] = {
                            width: a,
                            height: e
                        }, t.log(`Updated dimensions for chart ${N.value}:`, {
                            width: a,
                            height: e
                        }))
                    }
                },
                G = () => {
                    N.value = (N.value + 1) % O.value.length
                },
                J = () => {
                    N.value = (N.value - 1 + O.value.length) % O.value.length
                },
                R = async () => {
                    for (let e = 0; e < O.value.length; e++) N.value = e, await s(), D();
                    N.value = 0
                },
                V = a(!1),
                K = e => {
                    V.value || (e.preventDefault(), e.stopPropagation(), V.value = !0)
                },
                Q = () => {
                    V.value = !1
                },
                X = e => {
                    "Escape" === e.key && V.value && Q()
                };
            return l([() => B.data, () => B.searchFilter], (async () => {
                if (0 === B.data.length && B.searchFilter) try {
                    const e = `/api/spark/finance/financial_report_flow_item?${new URLSearchParams(B.searchFilter)}`,
                        a = await fetch(e, {
                            method: "GET",
                            headers: {
                                "Content-Type": "application/json"
                            }
                        }),
                        i = await a.json();
                    t.log("sankeydata", i), i.data && i.data.length > 0 && (O.value = i.data.map((e => e.data)), t.log("emit sankeydata", JSON.stringify(O.value)), T("finished_loading", JSON.stringify(O.value)))
                } catch (e) {} else O.value = B.data
            }), {
                immediate: !0
            }), n((async () => {
                t.log("MultipleSankeyCharts mounted, initial width:", z.value), W.value = O.value.map((() => ({
                    width: B.width,
                    height: I.value
                }))), await s(), await R(), window.addEventListener("keydown", X), window.addEventListener("resize", (() => {
                    V.value && s((() => {
                        D()
                    }))
                }))
            })), o((() => {
                window.removeEventListener("keydown", X)
            })), l(N, (() => {
                s((() => {
                    D()
                }))
            })), l((() => B.width), (e => {
                e > 0 && (t.log("newWidth", e), z.value = e, s((() => {
                    R()
                })))
            }), {
                immediate: !0
            }), (a, t) => O.value.length > 0 ? (y(), r("div", {
                key: 0,
                class: m(["multiple-sankey-charts", {
                    "fullscreen-mode": V.value
                }]),
                style: p({
                    width: `${z.value}px`,
                    height: `${H.value}px`
                }),
                onClick: K
            }, [V.value ? (y(), r("div", {
                key: 0,
                class: "fullscreen-button pointer-events-auto",
                onClick: u(Q, ["stop"])
            }, [v(c(M))])) : h("", !0), d("div", b, [e.showLogo ? (y(), r("div", j, [E.value ? (y(), g(c(C), {
                key: 0
            })) : (y(), g(c(L), {
                key: 1
            }))])) : h("", !0), d("div", {
                class: "chart-scaler",
                style: p(A.value)
            }, [q.value ? (y(), g(k, {
                key: `chart-${N.value}`,
                ref: e => {
                    e && (P.value = e)
                },
                data: q.value,
                nodeWidth: 30,
                nodePadding: 120,
                nodeAlign: "center",
                horizontalNodeSpacing: 250,
                totalHeight: 200,
                horizontalPadding: 0,
                verticalPadding: 50,
                textWidth: 200,
                useBrandColor: !0,
                showTitle: B.showTitle,
                onMounted: U
            }, null, 8, ["data", "showTitle"])) : h("", !0)], 4)]), e.showLogoFooter ? (y(), r("div", $, t[0] || (t[0] = [d("div", {
                class: "chart-tips"
            }, [w(" Generated by AI based on financial reports."), d("br"), w(" Leave feedback if you identify any discrepancies. ")], -1)]))) : h("", !0), O.value.length > 1 ? (y(), r(f, {
                key: 2
            }, [d("div", {
                class: "arrow left",
                onClick: J
            }, [d("img", {
                src: c(_)
            }, null, 8, x)]), d("div", {
                class: "arrow right",
                onClick: G
            }, [d("img", {
                src: c(_)
            }, null, 8, F)])], 64)) : h("", !0)], 6)) : h("", !0)
        }
    }, [
        ["__scopeId", "data-v-d5ec5171"]
    ]);
export {
    S as M
};