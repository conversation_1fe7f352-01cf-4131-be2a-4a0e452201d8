import {
    d as t,
    b as e,
    o as i,
    _ as n,
    r as o,
    c as s,
    a as r,
    w as a,
    y as h,
    F as c,
    k as l,
    n as u,
    f as d,
    ai as m,
    t as p,
    e as g,
    s as f,
    C as v,
    i as b,
    T as w,
    E as y,
    H as z,
    I as A,
    av as R,
    l as M
} from "./Cf0SOiw0.js";
import {
    N as x
} from "./CmeRl4Ak.js";
import {
    N as S
} from "./CW991W2w.js";
import {
    m as E
} from "./Boc3hm_9.js";
import {
    m as C
} from "./DZBrT1el.js";
const T = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const W = {
        render: function(n, o) {
            return i(), t("svg", T, o[0] || (o[0] = [e("path", {
                d: "M12 6H15C16.6569 6 18 7.34315 18 9V12M12 18H9C7.34315 18 6 16.6569 6 15V12",
                stroke: "#232425",
                "stroke-width": "1.75",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    D = {
        width: "10",
        height: "10",
        viewBox: "0 0 10 10",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const H = {
        render: function(n, o) {
            return i(), t("svg", D, o[0] || (o[0] = [e("path", {
                d: "M1.25 3.53366L4.78553 7.06733L8.32107 3.53366",
                stroke: "#666666",
                "stroke-width": "0.833023",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    O = {
        class: "selected-content"
    },
    k = {
        key: 0,
        class: "selected-icon"
    },
    j = ["src"],
    I = {
        class: "selected-label"
    },
    L = {
        class: "unified-select-dropdown"
    },
    P = ["onClick"],
    $ = {
        key: 0,
        class: "option-icon"
    },
    B = ["src"],
    U = {
        class: "option-label"
    },
    X = {
        key: 1,
        class: "checkmark"
    },
    Y = n({
        __name: "UnifiedSelect",
        props: {
            modelValue: {
                type: String,
                required: !0
            },
            options: {
                type: Array,
                required: !0,
                validator: t => t.every((t => "object" == typeof t && "value" in t && "label" in t))
            },
            config: {
                type: Object,
                default: () => ({})
            },
            size: {
                type: String,
                default: "medium"
            }
        },
        emits: ["update:modelValue", "change"],
        setup(n, {
            emit: f
        }) {
            const v = n,
                b = f,
                w = o(!1),
                y = s((() => v.options.find((t => t.value === v.modelValue)))),
                z = s((() => {
                    var t;
                    return (null == (t = y.value) ? void 0 : t.label) || v.config.placeholder || "Select"
                })),
                A = t => {
                    w.value = t
                };
            return (o, s) => (i(), r(h(S), {
                trigger: "click",
                "show-arrow": !1,
                placement: "bottom-start",
                class: "unified-select-popover",
                show: w.value,
                "onUpdate:show": [s[0] || (s[0] = t => w.value = t), A]
            }, {
                trigger: a((() => {
                    var o;
                    return [e("div", {
                        class: u(["unified-select-trigger", [n.size, {
                            active: w.value
                        }]])
                    }, [e("div", O, [(null == (o = y.value) ? void 0 : o.icon) ? (i(), t("div", k, ["object" == typeof y.value.icon ? (i(), r(m(y.value.icon), {
                        key: 0
                    })) : (i(), t("img", {
                        key: 1,
                        src: y.value.icon,
                        alt: "icon"
                    }, null, 8, j))])) : d("", !0), e("span", I, p(z.value), 1)]), e("div", {
                        class: u(["trigger-icon", {
                            active: w.value
                        }])
                    }, [g(h(H))], 2)], 2)]
                })),
                default: a((() => [e("div", L, [(i(!0), t(c, null, l(n.options, (o => (i(), t("div", {
                    key: o.id,
                    class: u(["select-option", {
                        active: n.modelValue === o.value
                    }]),
                    onClick: t => (t => {
                        t.extra, b("update:modelValue", t.value), b("change", t.value, t), w.value = !1
                    })(o)
                }, [o.icon ? (i(), t("div", $, ["object" == typeof o.icon ? (i(), r(m(o.icon), {
                    key: 0
                })) : (i(), t("img", {
                    key: 1,
                    src: o.icon,
                    alt: "icon"
                }, null, 8, B))])) : d("", !0), e("div", U, p(o.label), 1), n.modelValue === o.value ? (i(), t("div", X, [g(h(x), {
                    checked: !0
                })])) : d("", !0)], 10, P)))), 128))])])),
                _: 1
            }, 8, ["show"]))
        }
    }, [
        ["__scopeId", "data-v-a26b99fd"]
    ]),
    F = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const _ = {
        render: function(n, o) {
            return i(), t("svg", F, o[0] || (o[0] = [e("rect", {
                width: "14",
                height: "10",
                rx: "3",
                transform: "matrix(-1 -8.74228e-08 -8.74228e-08 1 19 7)",
                stroke: "#232425",
                "stroke-width": "1.75"
            }, null, -1)]))
        }
    },
    N = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const V = {
        render: function(n, o) {
            return i(), t("svg", N, o[0] || (o[0] = [e("rect", {
                width: "14",
                height: "12",
                rx: "3",
                transform: "matrix(-1 -8.74228e-08 -8.74228e-08 1 19 6)",
                stroke: "#232425",
                "stroke-width": "1.75"
            }, null, -1)]))
        }
    },
    q = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Z = {
        render: function(n, o) {
            return i(), t("svg", q, o[0] || (o[0] = [e("rect", {
                width: "14",
                height: "12",
                rx: "3",
                transform: "matrix(1.19249e-08 -1 -1 -1.19249e-08 18 19)",
                stroke: "#232425",
                "stroke-width": "1.75"
            }, null, -1)]))
        }
    },
    G = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Q = {
        render: function(n, o) {
            return i(), t("svg", G, o[0] || (o[0] = [e("rect", {
                width: "14",
                height: "10",
                rx: "3",
                transform: "matrix(1.19249e-08 -1 -1 -1.19249e-08 17 19)",
                stroke: "#232425",
                "stroke-width": "1.75"
            }, null, -1)]))
        }
    },
    K = ["onClick"],
    J = {
        class: "row"
    },
    tt = {
        class: "left"
    },
    et = {
        key: 0,
        class: "absolute -top-4 left-2 px-2 py-0.5 bg-red-500 rounded-tl-lg rounded-tr-lg rounded-bl-sm rounded-br-lg inline-flex justify-center items-center gap-2.5 overflow-hidden text-white text-xs font-bold"
    },
    it = {
        class: "icon"
    },
    nt = ["src"],
    ot = {
        class: "text"
    },
    st = {
        class: "right"
    },
    rt = ["checked"],
    at = {
        key: 0,
        class: "description"
    },
    ht = {
        key: 0,
        class: "divider"
    },
    ct = n({
        __name: "ImageSelectModel",
        props: {
            promptImages: {
                type: Array,
                default: []
            },
            toggleModel: {
                type: Function,
                default: () => {}
            },
            modelsSelected: {
                type: String,
                default: ""
            }
        },
        setup(n) {
            const o = n,
                {
                    t: r
                } = f(),
                a = b("currentUser"),
                u = s((() => (v.log("currentUser:", a.value), !v.isGensparkApp() && !(!a.value || !a.value.gk_realtime_dogfood)))),
                m = s((() => E.filter((t => {
                    const e = !t.is_i2i || t.is_i2i && o.promptImages && o.promptImages.length > 0;
                    return "gemini-flash-2.0" === t.name ? u.value && e : e
                })))),
                g = t => t.description ? t.description(r) : "";
            return (o, s) => (i(!0), t(c, null, l(h(a) && h(a).gk_realtime_dogfood ? m.value.filter((t => !t.deprecated)) : m.value.filter((t => !t.hidden)), (o => {
                return i(), t(c, null, [e("div", {
                    class: "model",
                    onClick: t => n.toggleModel(o.name)
                }, [e("div", J, [e("div", tt, [o.new ? (i(), t("div", et, " New ")) : d("", !0), e("div", it, [e("img", {
                    src: o.icon
                }, null, 8, nt)]), e("div", ot, p((s = o, "function" == typeof s.label ? s.label(r) : s.label)), 1)]), e("div", st, [e("input", {
                    type: "radio",
                    name: "model",
                    checked: n.modelsSelected === o.name
                }, null, 8, rt)])]), o.description ? (i(), t("div", at, p(g(o)), 1)) : d("", !0)], 8, K), o.divider ? (i(), t("div", ht)) : d("", !0)], 64);
                var s
            })), 256))
        }
    }, [
        ["__scopeId", "data-v-5617fe9d"]
    ]);

function lt(t, e) {
    var i = Object.keys(t);
    if (Object.getOwnPropertySymbols) {
        var n = Object.getOwnPropertySymbols(t);
        e && (n = n.filter((function(e) {
            return Object.getOwnPropertyDescriptor(t, e).enumerable
        }))), i.push.apply(i, n)
    }
    return i
}

function ut(t) {
    for (var e = 1; e < arguments.length; e++) {
        var i = null != arguments[e] ? arguments[e] : {};
        e % 2 ? lt(Object(i), !0).forEach((function(e) {
            dt(t, e, i[e])
        })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(i)) : lt(Object(i)).forEach((function(e) {
            Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(i, e))
        }))
    }
    return t
}

function dt(t, e, i) {
    return e in t ? Object.defineProperty(t, e, {
        value: i,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) : t[e] = i, t
}

function mt(t) {
    return function(t) {
        if (Array.isArray(t)) return pt(t)
    }(t) || function(t) {
        if ("undefined" != typeof Symbol && null != t[Symbol.iterator] || null != t["@@iterator"]) return Array.from(t)
    }(t) || function(t, e) {
        if (t) {
            if ("string" == typeof t) return pt(t, e);
            var i = Object.prototype.toString.call(t).slice(8, -1);
            return "Object" === i && t.constructor && (i = t.constructor.name), "Map" === i || "Set" === i ? Array.from(t) : "Arguments" === i || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i) ? pt(t, e) : void 0
        }
    }(t) || function() {
        throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
    }()
}

function pt(t, e) {
    (null == e || e > t.length) && (e = t.length);
    for (var i = 0, n = new Array(e); i < e; i++) n[i] = t[i];
    return n
}
var gt, ft = (function(t) {
        ! function() {
            var e = {}.hasOwnProperty;

            function i() {
                for (var t = [], n = 0; n < arguments.length; n++) {
                    var o = arguments[n];
                    if (o) {
                        var s = typeof o;
                        if ("string" === s || "number" === s) t.push(o);
                        else if (Array.isArray(o)) {
                            if (o.length) {
                                var r = i.apply(null, o);
                                r && t.push(r)
                            }
                        } else if ("object" === s)
                            if (o.toString === Object.prototype.toString)
                                for (var a in o) e.call(o, a) && o[a] && t.push(a);
                            else t.push(o.toString())
                    }
                }
                return t.join(" ")
            }
            t.exports ? (i.default = i, t.exports = i) : window.classNames = i
        }()
    }(gt = {
        path: undefined,
        exports: {},
        require: function(t, e) {
            return function() {
                throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")
            }(null == e && gt.path)
        }
    }, gt.exports), gt.exports),
    vt = function(t) {
        return function(e, i) {
            if (!e) return t;
            var n;
            "string" == typeof e ? n = e : i = e;
            var o = t;
            return n && (o += "__" + n), o + (i ? Object.keys(i).reduce((function(t, e) {
                var n = i[e];
                return n && (t += " " + ("boolean" == typeof n ? o + "--" + e : o + "--" + e + "_" + n)), t
            }), "") : "")
        }
    };

function bt(t, e, i) {
    var n, o, s, r, a;

    function h() {
        var c = Date.now() - r;
        c < e && c >= 0 ? n = setTimeout(h, e - c) : (n = null, i || (a = t.apply(s, o), s = o = null))
    }
    null == e && (e = 100);
    var c = function() {
        s = this, o = arguments, r = Date.now();
        var c = i && !n;
        return n || (n = setTimeout(h, e)), c && (a = t.apply(s, o), s = o = null), a
    };
    return c.clear = function() {
        n && (clearTimeout(n), n = null)
    }, c.flush = function() {
        n && (a = t.apply(s, o), s = o = null, clearTimeout(n), n = null)
    }, c
}
bt.debounce = bt;
var wt = bt,
    yt = function() {
        return yt = Object.assign || function(t) {
            for (var e, i = 1, n = arguments.length; i < n; i++)
                for (var o in e = arguments[i]) Object.prototype.hasOwnProperty.call(e, o) && (t[o] = e[o]);
            return t
        }, yt.apply(this, arguments)
    };

function zt(t, e) {
    var i, n;
    return t && e ? (i = "" + t + e[0].toUpperCase() + e.slice(1), n = t + "-" + e) : (i = t || e, n = t || e), {
        name: i,
        classname: n
    }
}

function At(t) {
    return /^blob:/.test(t)
}

function Rt(t) {
    return At(t) || /^data:/.test(t)
}

function Mt(t) {
    return !!(t && t.constructor && t.call && t.apply)
}

function xt(t) {
    return void 0 === t
}

function St(t) {
    return "object" == typeof t && null !== t
}

function Et(t, e, i) {
    var n = {};
    return St(t) ? (Object.keys(e).forEach((function(o) {
        xt(t[o]) ? n[o] = e[o] : St(e[o]) ? St(t[o]) ? n[o] = Et(t[o], e[o], i[o]) : n[o] = t[o] ? e[o] : i[o] : !0 === e[o] || !1 === e[o] ? n[o] = Boolean(t[o]) : n[o] = t[o]
    })), n) : t ? e : i
}

function Ct(t) {
    var e = Number(t);
    return Number.isNaN(e) ? t : e
}

function Tt(t) {
    return typeof("number" == t || function(t) {
        return "object" == typeof t && null !== t
    }(t) && "[object Number]" == toString.call(t)) && !Wt(t)
}

function Wt(t) {
    return t != t
}

function Dt(t, e) {
    return Math.sqrt(Math.pow(t.x - e.x, 2) + Math.pow(t.y - e.y, 2))
}
var Ht = function(t, e) {
        void 0 === t && (t = {}), void 0 === e && (e = {}), this.type = "manipulateImage", this.move = t, this.scale = e
    },
    Ot = function(t, e) {
        void 0 === e && (e = {}), this.type = "resize", this.directions = t, this.params = e
    },
    kt = function(t) {
        this.type = "move", this.directions = t
    },
    jt = function() {
        function t(t, e, i, n, o) {
            this.type = "drag", this.nativeEvent = t, this.position = i, this.previousPosition = n, this.element = e, this.anchor = o
        }
        return t.prototype.shift = function() {
            var t = this,
                e = t.element,
                i = t.anchor,
                n = t.position;
            if (e) {
                var o = e.getBoundingClientRect(),
                    s = o.left,
                    r = o.top;
                return {
                    left: n.left - s - i.left,
                    top: n.top - r - i.top
                }
            }
            return {
                left: 0,
                top: 0
            }
        }, t
    }(),
    It = {
        name: "DraggableElement",
        props: {
            classname: {
                type: String
            }
        },
        beforeMount: function() {
            window.addEventListener("mouseup", this.onMouseUp, {
                passive: !1
            }), window.addEventListener("mousemove", this.onMouseMove, {
                passive: !1
            }), window.addEventListener("touchmove", this.onTouchMove, {
                passive: !1
            }), window.addEventListener("touchend", this.onTouchEnd, {
                passive: !1
            })
        },
        beforeUnmount: function() {
            window.removeEventListener("mouseup", this.onMouseUp), window.removeEventListener("mousemove", this.onMouseMove), window.removeEventListener("touchmove", this.onTouchMove), window.removeEventListener("touchend", this.onTouchEnd)
        },
        mounted: function() {
            if (!this.$refs.draggable) throw new Error('You should add ref "draggable" to your root element to use draggable mixin');
            this.touches = [], this.hovered = !1
        },
        methods: {
            onMouseOver: function() {
                this.hovered || (this.hovered = !0, this.$emit("enter"))
            },
            onMouseLeave: function() {
                this.hovered && !this.touches.length && (this.hovered = !1, this.$emit("leave"))
            },
            onTouchStart: function(t) {
                t.cancelable && !this.disabled && 1 === t.touches.length && (this.touches = mt(t.touches), this.hovered || (this.$emit("enter"), this.hovered = !0), t.touches.length && this.initAnchor(this.touches.reduce((function(e, i) {
                    return {
                        clientX: e.clientX + i.clientX / t.touches.length,
                        clientY: e.clientY + i.clientY / t.touches.length
                    }
                }), {
                    clientX: 0,
                    clientY: 0
                })), t.preventDefault && t.preventDefault(), t.stopPropagation())
            },
            onTouchEnd: function() {
                this.processEnd()
            },
            onTouchMove: function(t) {
                this.touches.length && (this.processMove(t, t.touches), t.preventDefault && t.preventDefault(), t.stopPropagation && t.stopPropagation())
            },
            onMouseDown: function(t) {
                if (!this.disabled) {
                    var e = {
                        fake: !0,
                        clientX: t.clientX,
                        clientY: t.clientY
                    };
                    this.touches = [e], this.initAnchor(e), t.stopPropagation()
                }
            },
            onMouseMove: function(t) {
                this.touches.length && (this.processMove(t, [{
                    fake: !0,
                    clientX: t.clientX,
                    clientY: t.clientY
                }]), t.preventDefault && t.preventDefault())
            },
            onMouseUp: function() {
                this.processEnd()
            },
            initAnchor: function(t) {
                var e = this.$refs.draggable.getBoundingClientRect(),
                    i = e.left,
                    n = e.right,
                    o = e.bottom,
                    s = e.top;
                this.anchor = {
                    left: t.clientX - i,
                    top: t.clientY - s,
                    bottom: o - t.clientY,
                    right: n - t.clientX
                }
            },
            processMove: function(t, e) {
                var i = mt(e);
                if (this.touches.length) {
                    if (1 === this.touches.length && 1 === i.length) {
                        var n = this.$refs.draggable;
                        this.$emit("drag", new jt(t, n, {
                            left: i[0].clientX,
                            top: i[0].clientY
                        }, {
                            left: this.touches[0].clientX,
                            top: this.touches[0].clientY
                        }, this.anchor))
                    }
                    this.touches = i
                }
            },
            processEnd: function() {
                this.touches.length && this.$emit("drag-end"), this.hovered && (this.$emit("leave"), this.hovered = !1), this.touches = []
            }
        },
        emits: ["drag", "drag-end", "leave", "enter"],
        render: function(t, e, n, o, s, a) {
            return i(), r("div", {
                ref: "draggable",
                class: n.classname,
                onTouchstart: e[1] || (e[1] = function() {
                    return a.onTouchStart && a.onTouchStart.apply(a, arguments)
                }),
                onMousedown: e[2] || (e[2] = function() {
                    return a.onMouseDown && a.onMouseDown.apply(a, arguments)
                }),
                onMouseover: e[3] || (e[3] = function() {
                    return a.onMouseOver && a.onMouseOver.apply(a, arguments)
                }),
                onMouseleave: e[4] || (e[4] = function() {
                    return a.onMouseLeave && a.onMouseLeave.apply(a, arguments)
                })
            }, [w(t.$slots, "default")], 34)
        }
    },
    Lt = vt("vue-handler-wrapper"),
    Pt = {
        name: "HandlerWrapper",
        components: {
            DraggableElement: It
        },
        props: {
            horizontalPosition: {
                type: String
            },
            verticalPosition: {
                type: String
            },
            disabled: {
                type: Boolean,
                default: !1
            }
        },
        computed: {
            classes: function() {
                var t;
                if (this.horizontalPosition || this.verticalPosition) {
                    var e, i = zt(this.horizontalPosition, this.verticalPosition);
                    t = Lt((dt(e = {}, i.classname, !0), dt(e, "disabled", this.disabled), e))
                } else t = Lt({
                    disabled: this.disabled
                });
                return {
                    root: t,
                    draggable: Lt("draggable")
                }
            }
        },
        emits: ["leave", "enter", "drag", "drag-end"],
        render: function(t, e, n, o, s, h) {
            var c = y("DraggableElement");
            return i(), r("div", {
                class: h.classes.root
            }, [g(c, {
                class: h.classes.draggable,
                onDrag: e[1] || (e[1] = function(e) {
                    return t.$emit("drag", e)
                }),
                onDragEnd: e[2] || (e[2] = function(e) {
                    return t.$emit("drag-end")
                }),
                onLeave: e[3] || (e[3] = function(e) {
                    return t.$emit("leave")
                }),
                onEnter: e[4] || (e[4] = function(e) {
                    return t.$emit("enter")
                })
            }, {
                default: a((function() {
                    return [w(t.$slots, "default")]
                })),
                _: 3
            }, 8, ["class"])], 2)
        }
    },
    $t = vt("vue-line-wrapper"),
    Bt = {
        name: "LineWrapper",
        components: {
            DraggableElement: It
        },
        props: {
            position: {
                type: String,
                required: !0
            },
            disabled: {
                type: Boolean,
                default: !1
            }
        },
        computed: {
            classname: function() {
                var t;
                return $t((dt(t = {}, this.position, !0), dt(t, "disabled", this.disabled), t))
            }
        },
        emits: ["leave", "enter", "drag", "drag-end"],
        render: function(t, e, n, o, s, h) {
            var c = y("DraggableElement");
            return i(), r(c, {
                class: h.classname,
                onDrag: e[1] || (e[1] = function(e) {
                    return t.$emit("drag", e)
                }),
                onDragEnd: e[2] || (e[2] = function(e) {
                    return t.$emit("drag-end")
                }),
                onLeave: e[3] || (e[3] = function(e) {
                    return t.$emit("leave")
                }),
                onEnter: e[4] || (e[4] = function(e) {
                    return t.$emit("enter")
                })
            }, {
                default: a((function() {
                    return [w(t.$slots, "default")]
                })),
                _: 3
            }, 8, ["class"])
        }
    },
    Ut = ["left", "right", "top", "bottom"],
    Xt = ["left", "right"],
    Yt = ["top", "bottom"],
    Ft = ["left", "top"],
    _t = ["fill-area", "fit-area", "stencil", "none"],
    Nt = {
        left: 0,
        top: 0,
        width: 0,
        height: 0
    };

function Vt(t, e, i) {
    return !(i = i || ["width", "height", "left", "top"]).some((function(i) {
        return t[i] !== e[i]
    }))
}

function qt(t) {
    return {
        left: t.left,
        top: t.top,
        right: t.left + t.width,
        bottom: t.top + t.height
    }
}

function Zt(t, e) {
    return {
        left: t.left - e.left,
        top: t.top - e.top
    }
}

function Gt(t) {
    return {
        left: t.left + t.width / 2,
        top: t.top + t.height / 2
    }
}

function Qt(t, e) {
    var i = {
        left: 0,
        top: 0,
        right: 0,
        bottom: 0
    };
    return Ut.forEach((function(n) {
        var o = e[n],
            s = qt(t)[n];
        i[n] = void 0 !== o && void 0 !== s ? "left" === n || "top" === n ? Math.max(0, o - s) : Math.max(0, s - o) : 0
    })), i
}

function Kt(t, e) {
    return {
        left: t.left - e.left,
        top: t.top - e.top,
        width: t.width + e.left + e.right,
        height: t.height + e.top + e.bottom
    }
}

function Jt(t) {
    return {
        left: -t.left,
        top: -t.top
    }
}

function te(t, e) {
    return yt(yt({}, t), {
        left: t.left + e.left,
        top: t.top + e.top
    })
}

function ee(t, e, i, n) {
    if (1 !== e) {
        if (i) {
            var o = Gt(t);
            return {
                width: t.width * e,
                height: t.height * e,
                left: t.left + t.width * (1 - e) / 2 + (i.left - o.left) * (1 - e),
                top: t.top + t.height * (1 - e) / 2 + (i.top - o.top) * (1 - e)
            }
        }
        return {
            width: t.width * e,
            height: t.height * e,
            left: t.left + t.width * (1 - e) / 2,
            top: t.top + t.height * (1 - e) / 2
        }
    }
    return t
}

function ie(t) {
    return t.width / t.height
}

function ne(t, e) {
    return Math.min(void 0 !== e.right && void 0 !== e.left ? (e.right - e.left) / t.width : 1 / 0, void 0 !== e.bottom && void 0 !== e.top ? (e.bottom - e.top) / t.height : 1 / 0)
}

function oe(t, e) {
    var i = {
            left: 0,
            top: 0
        },
        n = Qt(t, e);
    return n.left && n.left > 0 ? i.left = n.left : n.right && n.right > 0 && (i.left = -n.right), n.top && n.top > 0 ? i.top = n.top : n.bottom && n.bottom > 0 && (i.top = -n.bottom), i
}

function se(t, e) {
    var i;
    return e.minimum && t < e.minimum ? i = e.minimum : e.maximum && t > e.maximum && (i = e.maximum), i
}

function re(t, e) {
    var i = ie(t),
        n = ie(e);
    return e.width < 1 / 0 && e.height < 1 / 0 ? i > n ? {
        width: e.width,
        height: e.width / i
    } : {
        width: e.height * i,
        height: e.height
    } : e.width < 1 / 0 ? {
        width: e.width,
        height: e.width / i
    } : e.height < 1 / 0 ? {
        width: e.height * i,
        height: e.height
    } : t
}

function ae(t, e) {
    var i = e * Math.PI / 180;
    return {
        width: Math.abs(t.width * Math.cos(i)) + Math.abs(t.height * Math.sin(i)),
        height: Math.abs(t.width * Math.sin(i)) + Math.abs(t.height * Math.cos(i))
    }
}

function he(t, e) {
    var i = e * Math.PI / 180;
    return {
        left: t.left * Math.cos(i) - t.top * Math.sin(i),
        top: t.left * Math.sin(i) + t.top * Math.cos(i)
    }
}

function ce(t, e) {
    var i = Qt(le(t, e), e);
    return i.left + i.right + i.top + i.bottom ? i.left + i.right > i.top + i.bottom ? Math.min((t.width + i.left + i.right) / t.width, ne(t, e)) : Math.min((t.height + i.top + i.bottom) / t.height, ne(t, e)) : 1
}

function le(t, e, i) {
    void 0 === i && (i = !1);
    var n = oe(t, e);
    return te(t, i ? Jt(n) : n)
}

function ue(t) {
    return {
        width: void 0 !== t.right && void 0 !== t.left ? t.right - t.left : 1 / 0,
        height: void 0 !== t.bottom && void 0 !== t.top ? t.bottom - t.top : 1 / 0
    }
}

function de(t, e, i) {
    void 0 === i && (i = !0);
    var n = {};
    return Ut.forEach((function(o) {
        var s = t[o],
            r = e[o];
        void 0 !== s && void 0 !== r ? n[o] = "left" === o || "top" === o ? i ? Math.max(s, r) : Math.min(s, r) : i ? Math.min(s, r) : Math.max(s, r) : void 0 !== r ? n[o] = r : void 0 !== s && (n[o] = s)
    })), n
}

function me(t, e) {
    return de(t, e, !0)
}

function pe(t) {
    var e = t.size,
        i = t.aspectRatio,
        n = t.ignoreMinimum,
        o = t.sizeRestrictions;
    return Boolean((e.correctRatio || ie(e) >= i.minimum && ie(e) <= i.maximum) && e.height <= o.maxHeight && e.width <= o.maxWidth && e.width && e.height && (n || e.height >= o.minHeight && e.width >= o.minWidth))
}

function ge(t, e) {
    return Math.pow(t.width - e.width, 2) + Math.pow(t.height - e.height, 2)
}

function fe(t) {
    var e = t.width,
        i = t.height,
        n = t.sizeRestrictions,
        o = {
            minimum: t.aspectRatio && t.aspectRatio.minimum || 0,
            maximum: t.aspectRatio && t.aspectRatio.maximum || 1 / 0
        },
        s = {
            width: Math.max(n.minWidth, Math.min(n.maxWidth, e)),
            height: Math.max(n.minHeight, Math.min(n.maxHeight, i))
        };

    function r(t, s) {
        return void 0 === s && (s = !1), t.reduce((function(t, r) {
            return pe({
                size: r,
                aspectRatio: o,
                sizeRestrictions: n,
                ignoreMinimum: s
            }) && (!t || ge(r, {
                width: e,
                height: i
            }) < ge(t, {
                width: e,
                height: i
            })) ? r : t
        }), null)
    }
    var a = [];
    o && [o.minimum, o.maximum].forEach((function(t) {
        t && a.push({
            width: s.width,
            height: s.width / t,
            correctRatio: !0
        }, {
            width: s.height * t,
            height: s.height,
            correctRatio: !0
        })
    })), pe({
        size: s,
        aspectRatio: o,
        sizeRestrictions: n
    }) && a.push(s);
    var h = r(a) || r(a, !0);
    return h && {
        width: h.width,
        height: h.height
    }
}

function ve(t) {
    var e = t.event,
        i = t.coordinates,
        n = t.positionRestrictions,
        o = void 0 === n ? {} : n,
        s = te(i, e.directions);
    return te(s, oe(s, o))
}

function be(t) {
    t.event;
    var e = t.getAreaRestrictions,
        i = t.boundaries,
        n = t.coordinates,
        o = t.visibleArea;
    t.aspectRatio;
    var s = t.stencilSize,
        r = t.sizeRestrictions,
        a = t.positionRestrictions;
    t.stencilReference;
    var h, c, l, u = yt({}, n),
        d = yt({}, o),
        m = yt({}, s);
    h = ie(m), c = ie(u), void 0 === l && (l = .001), (0 === h || 0 === c ? Math.abs(c - h) < l : Math.abs(c / h) < 1 + l && Math.abs(c / h) > 1 - l) || (u = yt(yt({}, u), fe({
        sizeRestrictions: r,
        width: u.width,
        height: u.height,
        aspectRatio: {
            minimum: ie(m),
            maximum: ie(m)
        }
    })));
    var p = ce(d = ee(d, u.width * i.width / (d.width * m.width)), e({
        visibleArea: d,
        type: "resize"
    }));
    return 1 !== p && (d = ee(d, p), u = ee(u, p)), d = le(d = te(d, Zt(Gt(u), Gt(d))), e({
        visibleArea: d,
        type: "move"
    })), {
        coordinates: u = le(u, me(qt(d), a)),
        visibleArea: d
    }
}

function we(t) {
    var e = t.event,
        i = t.getAreaRestrictions,
        n = t.boundaries,
        o = t.coordinates,
        s = t.visibleArea;
    t.aspectRatio, t.stencilSize, t.sizeRestrictions;
    var r = t.positionRestrictions;
    t.stencilReference;
    var a = yt({}, o),
        h = yt({}, s);
    if (o && s && "manipulateImage" !== e.type) {
        var c = {
            width: 0,
            height: 0
        };
        h.width, n.width, ie(n) > ie(a) ? (c.height = .8 * n.height, c.width = c.height * ie(a)) : (c.width = .8 * n.width, c.height = c.width * ie(a));
        var l = ce(h = ee(h, a.width * n.width / (h.width * c.width)), i({
            visibleArea: h,
            type: "resize"
        }));
        h = ee(h, l), 1 !== l && (c.height /= l, c.width /= l), h = le(h = te(h, Zt(Gt(a), Gt(h))), i({
            visibleArea: h,
            type: "move"
        })), a = le(a, me(qt(h), r))
    }
    return {
        coordinates: a,
        visibleArea: h
    }
}

function ye(t) {
    var e = t.event,
        i = t.coordinates,
        n = t.visibleArea,
        o = t.getAreaRestrictions,
        s = yt({}, n),
        r = yt({}, i);
    if ("setCoordinates" === e.type) {
        var a = Math.max(0, r.width - s.width),
            h = Math.max(0, r.height - s.height);
        a > h ? s = ee(s, Math.min(r.width / s.width, ne(s, o({
            visibleArea: s,
            type: "resize"
        })))) : h > a && (s = ee(s, Math.min(r.height / s.height, ne(s, o({
            visibleArea: s,
            type: "resize"
        }))))), s = le(s = te(s, Jt(oe(r, qt(s)))), o({
            visibleArea: s,
            type: "move"
        }))
    }
    return {
        visibleArea: s,
        coordinates: r
    }
}

function ze(t) {
    var e = t.imageSize,
        i = t.visibleArea,
        n = t.coordinates,
        o = i || e;
    return {
        left: (i ? i.left : 0) + o.width / 2 - n.width / 2,
        top: (i ? i.top : 0) + o.height / 2 - n.height / 2
    }
}

function Ae(t) {
    var e = t.imageSize,
        i = t.visibleArea,
        n = t.aspectRatio,
        o = t.sizeRestrictions,
        s = i || e,
        r = Math.min(n.maximum || 1 / 0, Math.max(n.minimum || 0, ie(s))),
        a = s.width < s.height ? {
            width: .8 * s.width,
            height: .8 * s.width / r
        } : {
            height: .8 * s.height,
            width: .8 * s.height * r
        };
    return fe(yt(yt({}, a), {
        aspectRatio: n,
        sizeRestrictions: o
    }))
}

function Re(t) {
    var e, i, n = t.imageSize,
        o = t.visibleArea,
        s = t.boundaries,
        r = t.aspectRatio,
        a = t.sizeRestrictions,
        h = t.stencilSize,
        c = o || n;
    return ie(c) > ie(s) ? i = (e = h.height * c.height / s.height) * ie(h) : e = (i = h.width * c.width / s.width) / ie(h), fe({
        width: i,
        height: e,
        aspectRatio: r,
        sizeRestrictions: a
    })
}

function Me(t) {
    var e = t.getAreaRestrictions,
        i = t.coordinates,
        n = t.imageSize,
        o = ie(t.boundaries);
    if (i) {
        var s = {
                height: Math.max(i.height, n.height),
                width: Math.max(i.width, n.width)
            },
            r = re({
                width: ie(s) > o ? s.width : s.height * o,
                height: ie(s) > o ? s.width / o : s.height
            }, ue(e())),
            a = {
                left: i.left + i.width / 2 - r.width / 2,
                top: i.top + i.height / 2 - r.height / 2,
                width: r.width,
                height: r.height
            },
            h = Qt(i, qt(yt({
                left: 0,
                top: 0
            }, n))),
            c = {};
        return !h.left && !h.right && a.width <= n.width && (c.left = 0, c.right = n.width), !h.top && !h.bottom && a.height <= n.height && (c.top = 0, c.bottom = n.height), le(a, c)
    }
    var l = ie(n);
    return r = {
        height: l > o ? n.height : n.width / o,
        width: l > o ? n.height * o : n.width
    }, {
        left: n.width / 2 - r.width / 2,
        top: n.height / 2 - r.height / 2,
        width: r.width,
        height: r.height
    }
}

function xe(t, e) {
    return de(t, qt(e))
}

function Se(t) {
    var e = t.directions,
        i = t.coordinates,
        n = t.positionRestrictions,
        o = void 0 === n ? {} : n,
        s = t.sizeRestrictions,
        r = t.preserveRatio,
        a = t.compensate,
        h = yt({}, e),
        c = Kt(i, h).width,
        l = Kt(i, h).height;
    c < 0 && (h.left < 0 && h.right < 0 ? (h.left = -(i.width - s.minWidth) / (h.left / h.right), h.right = -(i.width - s.minWidth) / (h.right / h.left)) : h.left < 0 ? h.left = -(i.width - s.minWidth) : h.right < 0 && (h.right = -(i.width - s.minWidth))), l < 0 && (h.top < 0 && h.bottom < 0 ? (h.top = -(i.height - s.minHeight) / (h.top / h.bottom), h.bottom = -(i.height - s.minHeight) / (h.bottom / h.top)) : h.top < 0 ? h.top = -(i.height - s.minHeight) : h.bottom < 0 && (h.bottom = -(i.height - s.minHeight)));
    var u = Qt(Kt(i, h), o);
    a && (u.left && u.left > 0 && 0 === u.right ? (h.right += u.left, h.left -= u.left) : u.right && u.right > 0 && 0 === u.left && (h.left += u.right, h.right -= u.right), u.top && u.top > 0 && 0 === u.bottom ? (h.bottom += u.top, h.top -= u.top) : u.bottom && u.bottom > 0 && 0 === u.top && (h.top += u.bottom, h.bottom -= u.bottom), u = Qt(Kt(i, h), o));
    var d = {
        width: 1 / 0,
        height: 1 / 0,
        left: 1 / 0,
        right: 1 / 0,
        top: 1 / 0,
        bottom: 1 / 0
    };
    if (Ut.forEach((function(t) {
            var e = u[t];
            e && h[t] && (d[t] = Math.max(0, 1 - e / h[t]))
        })), r) {
        var m = Math.min.apply(null, Ut.map((function(t) {
            return d[t]
        })));
        m !== 1 / 0 && Ut.forEach((function(t) {
            h[t] *= m
        }))
    } else Ut.forEach((function(t) {
        d[t] !== 1 / 0 && (h[t] *= d[t])
    }));
    if (c = Kt(i, h).width, l = Kt(i, h).height, h.right + h.left && (c > s.maxWidth ? d.width = (s.maxWidth - i.width) / (h.right + h.left) : c < s.minWidth && (d.width = (s.minWidth - i.width) / (h.right + h.left))), h.bottom + h.top && (l > s.maxHeight ? d.height = (s.maxHeight - i.height) / (h.bottom + h.top) : l < s.minHeight && (d.height = (s.minHeight - i.height) / (h.bottom + h.top))), r) {
        var p = Math.min(d.width, d.height);
        p !== 1 / 0 && Ut.forEach((function(t) {
            h[t] *= p
        }))
    } else d.width !== 1 / 0 && Xt.forEach((function(t) {
        h[t] *= d.width
    })), d.height !== 1 / 0 && Yt.forEach((function(t) {
        h[t] *= d.height
    }));
    return h
}

function Ee(t, e, i) {
    return 0 == e && 0 == i ? t / 2 : 0 == e ? 0 : 0 == i ? t : t * Math.abs(e / (e + i))
}
var Ce = vt("vue-simple-handler"),
    Te = vt("vue-simple-handler-wrapper"),
    We = {
        name: "SimpleHandler",
        components: {
            HandlerWrapper: Pt
        },
        props: {
            defaultClass: {
                type: String
            },
            hoverClass: {
                type: String
            },
            wrapperClass: {
                type: String
            },
            horizontalPosition: {
                type: String
            },
            verticalPosition: {
                type: String
            },
            disabled: {
                type: Boolean,
                default: !1
            }
        },
        data: function() {
            return {
                hover: !1
            }
        },
        computed: {
            classes: function() {
                var t, e = (dt(t = {}, this.horizontalPosition, Boolean(this.horizontalPosition)), dt(t, this.verticalPosition, Boolean(this.verticalPosition)), dt(t, "".concat(this.horizontalPosition, "-").concat(this.verticalPosition), Boolean(this.verticalPosition && this.horizontalPosition)), dt(t, "hover", this.hover), t);
                return {
                    default: ft(Ce(e), this.defaultClass, this.hover && this.hoverClass),
                    wrapper: ft(Te(e), this.wrapperClass)
                }
            }
        },
        methods: {
            onDrag: function(t) {
                this.$emit("drag", t)
            },
            onEnter: function() {
                this.hover = !0
            },
            onLeave: function() {
                this.hover = !1
            },
            onDragEnd: function() {
                this.$emit("drag-end")
            }
        },
        emits: ["drag", "drag-end"],
        render: function(t, e, n, o, s, h) {
            var c = y("HandlerWrapper");
            return i(), r(c, {
                class: h.classes.wrapper,
                "vertical-position": n.verticalPosition,
                "horizontal-position": n.horizontalPosition,
                disabled: n.disabled,
                onDrag: h.onDrag,
                onDragEnd: h.onDragEnd,
                onEnter: h.onEnter,
                onLeave: h.onLeave
            }, {
                default: a((function() {
                    return [g("div", {
                        class: h.classes.default
                    }, null, 2)]
                })),
                _: 1
            }, 8, ["class", "vertical-position", "horizontal-position", "disabled", "onDrag", "onDragEnd", "onEnter", "onLeave"])
        }
    },
    De = vt("vue-simple-line"),
    He = vt("vue-simple-line-wrapper"),
    Oe = {
        name: "SimpleLine",
        components: {
            LineWrapper: Bt
        },
        props: {
            defaultClass: {
                type: String
            },
            hoverClass: {
                type: String
            },
            wrapperClass: {
                type: String
            },
            position: {
                type: String
            },
            disabled: {
                type: Boolean,
                default: !1
            }
        },
        data: function() {
            return {
                hover: !1
            }
        },
        computed: {
            classes: function() {
                return {
                    root: ft(De(dt({}, this.position, !0)), this.defaultClass, this.hover && this.hoverClass),
                    wrapper: ft(He(dt({}, this.position, !0)), this.wrapperClass)
                }
            }
        },
        methods: {
            onDrag: function(t) {
                this.$emit("drag", t)
            },
            onEnter: function() {
                this.hover = !0
            },
            onLeave: function() {
                this.hover = !1
            },
            onDragEnd: function() {
                this.$emit("drag-end")
            }
        },
        emits: ["drag", "drag-end"],
        render: function(t, e, n, o, s, h) {
            var c = y("LineWrapper");
            return i(), r(c, {
                class: h.classes.wrapper,
                position: n.position,
                disabled: n.disabled,
                onDrag: h.onDrag,
                onDragEnd: h.onDragEnd,
                onEnter: h.onEnter,
                onLeave: h.onLeave
            }, {
                default: a((function() {
                    return [g("div", {
                        class: h.classes.root
                    }, null, 2)]
                })),
                _: 1
            }, 8, ["class", "position", "disabled", "onDrag", "onDragEnd", "onEnter", "onLeave"])
        }
    },
    ke = vt("vue-bounding-box"),
    je = ["east", "west", null],
    Ie = ["south", "north", null],
    Le = {
        name: "BoundingBox",
        props: {
            width: {
                type: Number
            },
            height: {
                type: Number
            },
            transitions: {
                type: Object
            },
            handlers: {
                type: Object,
                default: function() {
                    return {
                        eastNorth: !0,
                        north: !0,
                        westNorth: !0,
                        west: !0,
                        westSouth: !0,
                        south: !0,
                        eastSouth: !0,
                        east: !0
                    }
                }
            },
            handlersComponent: {
                type: [Object, String],
                default: function() {
                    return We
                }
            },
            handlersClasses: {
                type: Object,
                default: function() {
                    return {}
                }
            },
            handlersWrappersClasses: {
                type: Object,
                default: function() {
                    return {}
                }
            },
            lines: {
                type: Object,
                default: function() {
                    return {
                        west: !0,
                        north: !0,
                        east: !0,
                        south: !0
                    }
                }
            },
            linesComponent: {
                type: [Object, String],
                default: function() {
                    return Oe
                }
            },
            linesClasses: {
                type: Object,
                default: function() {
                    return {}
                }
            },
            linesWrappersClasses: {
                type: Object,
                default: function() {
                    return {}
                }
            },
            resizable: {
                type: Boolean,
                default: !0
            }
        },
        data: function() {
            var t = [];
            return je.forEach((function(e) {
                Ie.forEach((function(i) {
                    if (e !== i) {
                        var n = zt(e, i),
                            o = n.name,
                            s = n.classname;
                        t.push({
                            name: o,
                            classname: s,
                            verticalDirection: i,
                            horizontalDirection: e
                        })
                    }
                }))
            })), {
                points: t
            }
        },
        computed: {
            style: function() {
                var t = {};
                return this.width && this.height && (t.width = "".concat(this.width, "px"), t.height = "".concat(this.height, "px"), this.transitions && this.transitions.enabled && (t.transition = "".concat(this.transitions.time, "ms ").concat(this.transitions.timingFunction))), t
            },
            classes: function() {
                var t = this.handlersClasses,
                    e = this.handlersWrappersClasses,
                    i = this.linesClasses,
                    n = this.linesWrappersClasses;
                return {
                    root: ke(),
                    handlers: t,
                    handlersWrappers: e,
                    lines: i,
                    linesWrappers: n
                }
            },
            lineNodes: function() {
                var t = this,
                    e = [];
                return this.points.forEach((function(i) {
                    i.horizontalDirection && i.verticalDirection || !t.lines[i.name] || e.push({
                        name: i.name,
                        component: t.linesComponent,
                        class: ft(t.classes.lines.default, t.classes.lines[i.name], !t.resizable && t.classes.lines.disabled),
                        wrapperClass: ft(t.classes.linesWrappers.default, t.classes.linesWrappers[i.name], !t.resizable && t.classes.linesWrappers.disabled),
                        hoverClass: t.classes.lines.hover,
                        verticalDirection: i.verticalDirection,
                        horizontalDirection: i.horizontalDirection,
                        disabled: !t.resizable
                    })
                })), e
            },
            handlerNodes: function() {
                var t = this,
                    e = [],
                    i = this.width,
                    n = this.height;
                return this.points.forEach((function(o) {
                    if (t.handlers[o.name]) {
                        var s = {
                            name: o.name,
                            component: t.handlersComponent,
                            class: ft(t.classes.handlers.default, t.classes.handlers[o.name]),
                            wrapperClass: ft(t.classes.handlersWrappers.default, t.classes.handlersWrappers[o.name]),
                            hoverClass: t.classes.handlers.hover,
                            verticalDirection: o.verticalDirection,
                            horizontalDirection: o.horizontalDirection,
                            disabled: !t.resizable
                        };
                        if (i && n) {
                            var r = o.horizontalDirection,
                                a = o.verticalDirection,
                                h = "east" === r ? i : "west" === r ? 0 : i / 2,
                                c = "south" === a ? n : "north" === a ? 0 : n / 2;
                            s.wrapperClass = ke("handler"), s.wrapperStyle = {
                                transform: "translate(".concat(h, "px, ").concat(c, "px)")
                            }, t.transitions && t.transitions.enabled && (s.wrapperStyle.transition = "".concat(t.transitions.time, "ms ").concat(t.transitions.timingFunction))
                        } else s.wrapperClass = ke("handler", dt({}, o.classname, !0));
                        e.push(s)
                    }
                })), e
            }
        },
        beforeMount: function() {
            window.addEventListener("mouseup", this.onMouseUp, {
                passive: !1
            }), window.addEventListener("mousemove", this.onMouseMove, {
                passive: !1
            }), window.addEventListener("touchmove", this.onTouchMove, {
                passive: !1
            }), window.addEventListener("touchend", this.onTouchEnd, {
                passive: !1
            })
        },
        beforeUnmount: function() {
            window.removeEventListener("mouseup", this.onMouseUp), window.removeEventListener("mousemove", this.onMouseMove), window.removeEventListener("touchmove", this.onTouchMove), window.removeEventListener("touchend", this.onTouchEnd)
        },
        mounted: function() {
            this.touches = []
        },
        methods: {
            onEnd: function() {
                this.$emit("resize-end")
            },
            onHandlerDrag: function(t, e, i) {
                var n, o = t.shift(),
                    s = o.left,
                    r = o.top,
                    a = {
                        left: 0,
                        right: 0,
                        top: 0,
                        bottom: 0
                    };
                "west" === e ? a.left -= s : "east" === e && (a.right += s), "north" === i ? a.top -= r : "south" === i && (a.bottom += r), !i && e ? n = "width" : i && !e && (n = "height"), this.resizable && this.$emit("resize", new Ot(a, {
                    allowedDirections: {
                        left: "west" === e || !e,
                        right: "east" === e || !e,
                        bottom: "south" === i || !i,
                        top: "north" === i || !i
                    },
                    preserveAspectRatio: t.nativeEvent && t.nativeEvent.shiftKey,
                    respectDirection: n
                }))
            }
        },
        emits: ["resize", "resize-end"],
        render: function(t, e, n, o, s, a) {
            return i(), r("div", {
                ref: "box",
                class: a.classes.root,
                style: a.style
            }, [w(t.$slots, "default"), g("div", null, [(i(!0), r(c, null, l(a.lineNodes, (function(t) {
                return i(), r(m(t.component), {
                    key: t.name,
                    "default-class": t.class,
                    "hover-class": t.hoverClass,
                    "wrapper-class": t.wrapperClass,
                    position: t.name,
                    disabled: t.disabled,
                    onDrag: function(e) {
                        return a.onHandlerDrag(e, t.horizontalDirection, t.verticalDirection)
                    },
                    onDragEnd: e[1] || (e[1] = function(t) {
                        return a.onEnd()
                    })
                }, null, 8, ["default-class", "hover-class", "wrapper-class", "position", "disabled", "onDrag"])
            })), 128))]), (i(!0), r(c, null, l(a.handlerNodes, (function(t) {
                return i(), r("div", {
                    key: t.name,
                    style: t.wrapperStyle,
                    class: t.wrapperClass
                }, [(i(), r(m(t.component), {
                    "default-class": t.class,
                    "hover-class": t.hoverClass,
                    "wrapper-class": t.wrapperClass,
                    "horizontal-position": t.horizontalDirection,
                    "vertical-position": t.verticalDirection,
                    disabled: t.disabled,
                    onDrag: function(e) {
                        return a.onHandlerDrag(e, t.horizontalDirection, t.verticalDirection)
                    },
                    onDragEnd: e[2] || (e[2] = function(t) {
                        return a.onEnd()
                    })
                }, null, 8, ["default-class", "hover-class", "wrapper-class", "horizontal-position", "vertical-position", "disabled", "onDrag"]))], 6)
            })), 128))], 6)
        }
    },
    Pe = vt("vue-draggable-area"),
    $e = {
        name: "DraggableArea",
        props: {
            movable: {
                type: Boolean,
                default: !0
            },
            activationDistance: {
                type: Number,
                default: 20
            }
        },
        computed: {
            classnames: function() {
                return {
                    default: Pe()
                }
            }
        },
        beforeMount: function() {
            window.addEventListener("mouseup", this.onMouseUp, {
                passive: !1
            }), window.addEventListener("mousemove", this.onMouseMove, {
                passive: !1
            }), window.addEventListener("touchmove", this.onTouchMove, {
                passive: !1
            }), window.addEventListener("touchend", this.onTouchEnd, {
                passive: !1
            })
        },
        beforeUnmount: function() {
            window.removeEventListener("mouseup", this.onMouseUp), window.removeEventListener("mousemove", this.onMouseMove), window.removeEventListener("touchmove", this.onTouchMove), window.removeEventListener("touchend", this.onTouchEnd)
        },
        mounted: function() {
            this.touches = [], this.touchStarted = !1
        },
        methods: {
            onTouchStart: function(t) {
                if (t.cancelable) {
                    var e = this.movable && 1 === t.touches.length;
                    e && (this.touches = mt(t.touches)), (this.touchStarted || e) && (t.preventDefault(), t.stopPropagation())
                }
            },
            onTouchEnd: function() {
                this.touchStarted = !1, this.processEnd()
            },
            onTouchMove: function(t) {
                this.touches.length >= 1 && (this.touchStarted ? (this.processMove(t, t.touches), t.preventDefault(), t.stopPropagation()) : Dt({
                    x: this.touches[0].clientX,
                    y: this.touches[0].clientY
                }, {
                    x: t.touches[0].clientX,
                    y: t.touches[0].clientY
                }) > this.activationDistance && (this.initAnchor({
                    clientX: t.touches[0].clientX,
                    clientY: t.touches[0].clientY
                }), this.touchStarted = !0))
            },
            onMouseDown: function(t) {
                if (this.movable && 0 === t.button) {
                    var e = {
                        fake: !0,
                        clientX: t.clientX,
                        clientY: t.clientY
                    };
                    this.touches = [e], this.initAnchor(e), t.stopPropagation()
                }
            },
            onMouseMove: function(t) {
                this.touches.length && (this.processMove(t, [{
                    fake: !0,
                    clientX: t.clientX,
                    clientY: t.clientY
                }]), t.preventDefault && t.cancelable && t.preventDefault(), t.stopPropagation())
            },
            onMouseUp: function() {
                this.processEnd()
            },
            initAnchor: function(t) {
                var e = this.$refs.container.getBoundingClientRect(),
                    i = e.left,
                    n = e.top;
                this.anchor = {
                    x: t.clientX - i,
                    y: t.clientY - n
                }
            },
            processMove: function(t, e) {
                var i = mt(e);
                if (this.touches.length) {
                    var n = this.$refs.container.getBoundingClientRect(),
                        o = n.left,
                        s = n.top;
                    1 === this.touches.length && 1 === i.length && this.$emit("move", new kt({
                        left: i[0].clientX - (o + this.anchor.x),
                        top: i[0].clientY - (s + this.anchor.y)
                    }))
                }
            },
            processEnd: function() {
                this.touches.length && this.$emit("move-end"), this.touches = []
            }
        },
        emits: ["move", "move-end"]
    };

function Be(t) {
    var e, i;
    return {
        rotate: t.rotate || 0,
        flip: {
            horizontal: (null === (e = null == t ? void 0 : t.flip) || void 0 === e ? void 0 : e.horizontal) || !1,
            vertical: (null === (i = null == t ? void 0 : t.flip) || void 0 === i ? void 0 : i.vertical) || !1
        }
    }
}

function Ue(t) {
    var e = t.rotate,
        i = t.flip,
        n = t.scaleX,
        o = t.scaleY,
        s = "";
    return s += " rotate(" + e + "deg) ", (s += " scaleX(" + n * (i.horizontal ? -1 : 1) + ") ") + " scaleY(" + o * (i.vertical ? -1 : 1) + ") "
}

function Xe(t, e) {
    var i = e.getBoundingClientRect(),
        n = i.left,
        o = i.top,
        s = {
            left: 0,
            top: 0
        },
        r = 0;
    return t.forEach((function(e) {
        s.left += (e.clientX - n) / t.length, s.top += (e.clientY - o) / t.length
    })), t.forEach((function(t) {
        r += Dt({
            x: s.left,
            y: s.top
        }, {
            x: t.clientX - n,
            y: t.clientY - o
        })
    })), {
        centerMass: s,
        spread: r,
        count: t.length
    }
}
$e.render = function(t, e, n, o, s, a) {
    return i(), r("div", {
        ref: "container",
        onTouchstart: e[1] || (e[1] = function() {
            return a.onTouchStart && a.onTouchStart.apply(a, arguments)
        }),
        onMousedown: e[2] || (e[2] = function() {
            return a.onMouseDown && a.onMouseDown.apply(a, arguments)
        })
    }, [w(t.$slots, "default")], 544)
};
var Ye = {
        props: {
            touchMove: {
                type: Boolean,
                required: !0
            },
            mouseMove: {
                type: Boolean,
                required: !0
            },
            touchResize: {
                type: Boolean,
                required: !0
            },
            wheelResize: {
                type: [Boolean, Object],
                required: !0
            },
            eventsFilter: {
                type: Function,
                required: !1
            }
        },
        beforeMount: function() {
            window.addEventListener("mouseup", this.onMouseUp, {
                passive: !1
            }), window.addEventListener("mousemove", this.onMouseMove, {
                passive: !1
            }), window.addEventListener("touchmove", this.onTouchMove, {
                passive: !1
            }), window.addEventListener("touchend", this.onTouchEnd, {
                passive: !1
            })
        },
        beforeUnmount: function() {
            window.removeEventListener("mouseup", this.onMouseUp), window.removeEventListener("mousemove", this.onMouseMove), window.removeEventListener("touchmove", this.onTouchMove), window.removeEventListener("touchend", this.onTouchEnd)
        },
        created: function() {
            this.transforming = !1, this.debouncedProcessEnd = wt(this.processEnd), this.touches = []
        },
        methods: {
            processMove: function(t, e) {
                if (this.touches.length) {
                    if (1 === this.touches.length && 1 === e.length) this.$emit("move", new Ht({
                        left: this.touches[0].clientX - e[0].clientX,
                        top: this.touches[0].clientY - e[0].clientY
                    }));
                    else if (this.touches.length > 1 && this.touchResize) {
                        var i = Xe(e, this.$refs.container),
                            n = this.oldGeometricProperties;
                        n.count === i.count && n.count > 1 && this.$emit("resize", new Ht({
                            left: n.centerMass.left - i.centerMass.left,
                            top: n.centerMass.top - i.centerMass.top
                        }, {
                            factor: n.spread / i.spread,
                            center: i.centerMass
                        })), this.oldGeometricProperties = i
                    }
                    this.touches = e
                }
            },
            processEnd: function() {
                this.transforming && (this.transforming = !1, this.$emit("transform-end"))
            },
            processStart: function() {
                this.transforming = !0, this.debouncedProcessEnd.clear()
            },
            processEvent: function(t) {
                return this.eventsFilter ? !1 !== this.eventsFilter(t, this.transforming) : (t.preventDefault(), t.stopPropagation(), !0)
            },
            onTouchStart: function(t) {
                if (t.cancelable && (this.touchMove || this.touchResize && t.touches.length > 1) && this.processEvent(t)) {
                    var e = this.$refs.container,
                        i = e.getBoundingClientRect(),
                        n = i.left,
                        o = i.top,
                        s = i.bottom,
                        r = i.right;
                    this.touches = mt(t.touches).filter((function(t) {
                        return t.clientX > n && t.clientX < r && t.clientY > o && t.clientY < s
                    })), this.oldGeometricProperties = Xe(this.touches, e)
                }
            },
            onTouchEnd: function(t) {
                0 === t.touches.length && (this.touches = [], this.processEnd())
            },
            onTouchMove: function(t) {
                var e = this;
                if (this.touches.length) {
                    var i = mt(t.touches).filter((function(t) {
                        return !t.identifier || e.touches.find((function(e) {
                            return e.identifier === t.identifier
                        }))
                    }));
                    this.processEvent(t) && (this.processMove(t, i), this.processStart())
                }
            },
            onMouseDown: function(t) {
                if (this.mouseMove && "buttons" in t && 1 === t.buttons && this.processEvent(t)) {
                    var e = {
                        fake: !0,
                        clientX: t.clientX,
                        clientY: t.clientY
                    };
                    this.touches = [e], this.processStart()
                }
            },
            onMouseMove: function(t) {
                this.touches.length && this.processEvent(t) && this.processMove(t, [{
                    clientX: t.clientX,
                    clientY: t.clientY
                }])
            },
            onMouseUp: function() {
                this.touches = [], this.processEnd()
            },
            onWheel: function(t) {
                if (this.wheelResize && this.processEvent(t)) {
                    var e = this.$refs.container.getBoundingClientRect(),
                        i = e.left,
                        n = e.top,
                        o = 1 + this.wheelResize.ratio * (0 == (r = +(t.deltaY || t.detail || t.wheelDelta)) || Wt(r) ? r : r > 0 ? 1 : -1),
                        s = {
                            left: t.clientX - i,
                            top: t.clientY - n
                        };
                    this.$emit("resize", new Ht({}, {
                        factor: o,
                        center: s
                    })), this.touches.length || this.debouncedProcessEnd()
                }
                var r
            }
        },
        emits: ["resize", "move", "transform-end"],
        render: function(t, e, n, o, s, a) {
            return i(), r("div", {
                ref: "container",
                onTouchstart: e[1] || (e[1] = function() {
                    return a.onTouchStart && a.onTouchStart.apply(a, arguments)
                }),
                onMousedown: e[2] || (e[2] = function() {
                    return a.onMouseDown && a.onMouseDown.apply(a, arguments)
                }),
                onWheel: e[3] || (e[3] = function() {
                    return a.onWheel && a.onWheel.apply(a, arguments)
                })
            }, [w(t.$slots, "default")], 544)
        }
    },
    Fe = {
        components: {
            TransformableImage: Ye
        },
        props: {
            touchMove: {
                type: Boolean,
                required: !0
            },
            mouseMove: {
                type: Boolean,
                required: !0
            },
            touchResize: {
                type: Boolean,
                required: !0
            },
            wheelResize: {
                type: [Boolean, Object],
                required: !0
            }
        },
        emits: ["resize", "move"],
        render: function(t, e, n, o, s, h) {
            var c = y("transformable-image");
            return i(), r(c, {
                "touch-move": n.touchMove,
                "touch-resize": n.touchResize,
                "mouse-move": n.mouseMove,
                "wheel-resize": n.wheelResize,
                onMove: e[1] || (e[1] = function(e) {
                    return t.$emit("move", e)
                }),
                onResize: e[2] || (e[2] = function(e) {
                    return t.$emit("resize", e)
                })
            }, {
                default: a((function() {
                    return [w(t.$slots, "default")]
                })),
                _: 3
            }, 8, ["touch-move", "touch-resize", "mouse-move", "wheel-resize"])
        }
    },
    _e = vt("vue-preview"),
    Ne = {
        props: {
            coordinates: {
                type: Object
            },
            transitions: {
                type: Object
            },
            image: {
                type: Object,
                default: function() {
                    return {}
                }
            },
            imageClass: {
                type: String
            },
            width: {
                type: Number
            },
            height: {
                type: Number
            },
            fill: {
                type: Boolean
            }
        },
        data: function() {
            return {
                calculatedImageSize: {
                    width: 0,
                    height: 0
                },
                calculatedSize: {
                    width: 0,
                    height: 0
                }
            }
        },
        computed: {
            classes: function() {
                return {
                    root: _e({
                        fill: this.fill
                    }),
                    wrapper: _e("wrapper"),
                    imageWrapper: _e("image-wrapper"),
                    image: ft(_e("image"), this.imageClass)
                }
            },
            style: function() {
                if (this.fill) return {};
                var t = {};
                return this.width && (t.width = "".concat(this.size.width, "px")), this.height && (t.height = "".concat(this.size.height, "px")), this.transitions && this.transitions.enabled && (t.transition = "".concat(this.transitions.time, "ms ").concat(this.transitions.timingFunction)), t
            },
            wrapperStyle: function() {
                var t = {
                    width: "".concat(this.size.width, "px"),
                    height: "".concat(this.size.height, "px"),
                    left: "calc(50% - ".concat(this.size.width / 2, "px)"),
                    top: "calc(50% - ".concat(this.size.height / 2, "px)")
                };
                return this.transitions && this.transitions.enabled && (t.transition = "".concat(this.transitions.time, "ms ").concat(this.transitions.timingFunction)), t
            },
            imageStyle: function() {
                if (this.coordinates && this.image) {
                    var t = this.coordinates.width / this.size.width,
                        e = ut(ut({
                            rotate: 0,
                            flip: {
                                horizontal: !1,
                                vertical: !1
                            }
                        }, this.image.transforms), {}, {
                            scaleX: 1 / t,
                            scaleY: 1 / t
                        }),
                        i = this.imageSize.width,
                        n = this.imageSize.height,
                        o = ae({
                            width: i,
                            height: n
                        }, e.rotate),
                        s = {
                            width: "".concat(i, "px"),
                            height: "".concat(n, "px"),
                            left: "0px",
                            top: "0px"
                        },
                        r = {
                            rotate: {
                                left: (i - o.width) * e.scaleX / 2,
                                top: (n - o.height) * e.scaleY / 2
                            },
                            scale: {
                                left: (1 - e.scaleX) * i / 2,
                                top: (1 - e.scaleY) * n / 2
                            }
                        };
                    return s.transform = "translate(\n\t\t\t\t".concat(-this.coordinates.left / t - r.rotate.left - r.scale.left, "px,").concat(-this.coordinates.top / t - r.rotate.top - r.scale.top, "px) ") + Ue(e), this.transitions && this.transitions.enabled && (s.transition = "".concat(this.transitions.time, "ms ").concat(this.transitions.timingFunction)), s
                }
                return {}
            },
            size: function() {
                return {
                    width: this.width || this.calculatedSize.width,
                    height: this.height || this.calculatedSize.height
                }
            },
            imageSize: function() {
                return {
                    width: this.image.width || this.calculatedImageSize.width,
                    height: this.image.height || this.calculatedImageSize.height
                }
            }
        },
        watch: {
            image: function(t) {
                (t.width || t.height) && this.onChangeImage()
            }
        },
        mounted: function() {
            var t = this;
            this.onChangeImage(), this.$refs.image.addEventListener("load", (function() {
                t.refreshImage()
            })), window.addEventListener("resize", this.refresh), window.addEventListener("orientationchange", this.refresh)
        },
        unmounted: function() {
            window.removeEventListener("resize", this.refresh), window.removeEventListener("orientationchange", this.refresh)
        },
        methods: {
            refreshImage: function() {
                var t = this.$refs.image;
                this.calculatedImageSize.height = t.naturalHeight, this.calculatedImageSize.width = t.naturalWidth
            },
            refresh: function() {
                var t = this.$refs.root;
                this.width || (this.calculatedSize.width = t.clientWidth), this.height || (this.calculatedSize.height = t.clientHeight)
            },
            onChangeImage: function() {
                var t = this.$refs.image;
                t && t.complete && this.refreshImage(), this.refresh()
            }
        },
        render: function(t, e, n, o, s, a) {
            return i(), r("div", {
                ref: "root",
                class: a.classes.root,
                style: a.style
            }, [g("div", {
                ref: "wrapper",
                class: a.classes.wrapper,
                style: a.wrapperStyle
            }, [z(g("img", {
                ref: "image",
                src: n.image && n.image.src,
                class: a.classes.image,
                style: a.imageStyle
            }, null, 14, ["src"]), [
                [A, n.image && n.image.src]
            ])], 6)], 6)
        }
    },
    Ve = {
        components: {
            Preview: Ne
        },
        inheritAttrs: !1,
        render: function(t, e, n, o, s, a) {
            var h = y("preview");
            return i(), r(h, R(t.$attrs, {
                fill: !0
            }), null, 16)
        }
    },
    qe = vt("vue-rectangle-stencil"),
    Ze = {
        name: "RectangleStencil",
        components: {
            StencilPreview: Ve,
            BoundingBox: Le,
            DraggableArea: $e
        },
        props: {
            image: {
                type: Object
            },
            coordinates: {
                type: Object
            },
            stencilCoordinates: {
                type: Object
            },
            handlers: {
                type: Object
            },
            handlersComponent: {
                type: [Object, String],
                default: function() {
                    return We
                }
            },
            lines: {
                type: Object
            },
            linesComponent: {
                type: [Object, String],
                default: function() {
                    return Oe
                }
            },
            aspectRatio: {
                type: [Number, String]
            },
            minAspectRatio: {
                type: [Number, String]
            },
            maxAspectRatio: {
                type: [Number, String]
            },
            movable: {
                type: Boolean,
                default: !0
            },
            resizable: {
                type: Boolean,
                default: !0
            },
            transitions: {
                type: Object
            },
            movingClass: {
                type: String
            },
            resizingClass: {
                type: String
            },
            previewClass: {
                type: String
            },
            boundingBoxClass: {
                type: String
            },
            linesClasses: {
                type: Object,
                default: function() {
                    return {}
                }
            },
            linesWrappersClasses: {
                type: Object,
                default: function() {
                    return {}
                }
            },
            handlersClasses: {
                type: Object,
                default: function() {
                    return {}
                }
            },
            handlersWrappersClasses: {
                type: Object,
                default: function() {
                    return {}
                }
            }
        },
        data: function() {
            return {
                moving: !1,
                resizing: !1
            }
        },
        computed: {
            classes: function() {
                return {
                    stencil: ft(qe({
                        movable: this.movable,
                        moving: this.moving,
                        resizing: this.resizing
                    }), this.moving && this.movingClass, this.resizing && this.resizingClass),
                    preview: ft(qe("preview"), this.previewClass),
                    boundingBox: ft(qe("bounding-box"), this.boundingBoxClass)
                }
            },
            style: function() {
                var t = this.stencilCoordinates,
                    e = t.height,
                    i = t.width,
                    n = t.left,
                    o = t.top,
                    s = {
                        width: "".concat(i, "px"),
                        height: "".concat(e, "px"),
                        transform: "translate(".concat(n, "px, ").concat(o, "px)")
                    };
                return this.transitions && this.transitions.enabled && (s.transition = "".concat(this.transitions.time, "ms ").concat(this.transitions.timingFunction)), s
            }
        },
        methods: {
            onMove: function(t) {
                this.$emit("move", t), this.moving = !0
            },
            onMoveEnd: function() {
                this.$emit("move-end"), this.moving = !1
            },
            onResize: function(t) {
                this.$emit("resize", t), this.resizing = !0
            },
            onResizeEnd: function() {
                this.$emit("resize-end"), this.resizing = !1
            },
            aspectRatios: function() {
                return {
                    minimum: this.aspectRatio || this.minAspectRatio,
                    maximum: this.aspectRatio || this.maxAspectRatio
                }
            }
        },
        emits: ["resize", "resize-end", "move", "move-end"],
        render: function(t, e, n, o, s, h) {
            var c = y("stencil-preview"),
                l = y("draggable-area"),
                u = y("bounding-box");
            return i(), r("div", {
                class: h.classes.stencil,
                style: h.style
            }, [g(u, {
                width: n.stencilCoordinates.width,
                height: n.stencilCoordinates.height,
                transitions: n.transitions,
                class: h.classes.boundingBox,
                handlers: n.handlers,
                "handlers-component": n.handlersComponent,
                "handlers-classes": n.handlersClasses,
                "handlers-wrappers-classes": n.handlersWrappersClasses,
                lines: n.lines,
                "lines-component": n.linesComponent,
                "lines-classes": n.linesClasses,
                "lines-wrappers-classes": n.linesWrappersClasses,
                resizable: n.resizable,
                onResize: h.onResize,
                onResizeEnd: h.onResizeEnd
            }, {
                default: a((function() {
                    return [g(l, {
                        movable: n.movable,
                        onMove: h.onMove,
                        onMoveEnd: h.onMoveEnd
                    }, {
                        default: a((function() {
                            return [g(c, {
                                image: n.image,
                                coordinates: n.coordinates,
                                width: n.stencilCoordinates.width,
                                height: n.stencilCoordinates.height,
                                class: h.classes.preview,
                                transitions: n.transitions
                            }, null, 8, ["image", "coordinates", "width", "height", "class", "transitions"])]
                        })),
                        _: 1
                    }, 8, ["movable", "onMove", "onMoveEnd"])]
                })),
                _: 1
            }, 8, ["width", "height", "transitions", "class", "handlers", "handlers-component", "handlers-classes", "handlers-wrappers-classes", "lines", "lines-component", "lines-classes", "lines-wrappers-classes", "resizable", "onResize", "onResizeEnd"])], 6)
        }
    },
    Ge = ["transitions"],
    Qe = vt("vue-advanced-cropper"),
    Ke = {
        name: "Cropper",
        components: {
            BackgroundWrapper: Fe
        },
        props: {
            src: {
                type: String,
                default: null
            },
            stencilComponent: {
                type: [Object, String],
                default: function() {
                    return Ze
                }
            },
            backgroundWrapperComponent: {
                type: [Object, String],
                default: function() {
                    return Fe
                }
            },
            stencilProps: {
                type: Object,
                default: function() {
                    return {}
                }
            },
            autoZoom: {
                type: Boolean,
                default: !1
            },
            imageClass: {
                type: String
            },
            boundariesClass: {
                type: String
            },
            backgroundClass: {
                type: String
            },
            foregroundClass: {
                type: String
            },
            minWidth: {
                type: [Number, String]
            },
            minHeight: {
                type: [Number, String]
            },
            maxWidth: {
                type: [Number, String]
            },
            maxHeight: {
                type: [Number, String]
            },
            debounce: {
                type: [Boolean, Number],
                default: 500
            },
            transitions: {
                type: Boolean,
                default: !0
            },
            checkOrientation: {
                type: Boolean,
                default: !0
            },
            canvas: {
                type: [Object, Boolean],
                default: !0
            },
            crossOrigin: {
                type: [Boolean, String],
                default: void 0
            },
            transitionTime: {
                type: Number,
                default: 300
            },
            imageRestriction: {
                type: String,
                default: "fit-area",
                validator: function(t) {
                    return -1 !== _t.indexOf(t)
                }
            },
            roundResult: {
                type: Boolean,
                default: !0
            },
            defaultSize: {
                type: [Function, Object]
            },
            defaultPosition: {
                type: [Function, Object]
            },
            defaultVisibleArea: {
                type: [Function, Object]
            },
            defaultTransforms: {
                type: [Function, Object]
            },
            defaultBoundaries: {
                type: [Function, String],
                validator: function(t) {
                    return !("string" == typeof t && "fill" !== t && "fit" !== t)
                }
            },
            priority: {
                type: String,
                default: "coordinates"
            },
            stencilSize: {
                type: [Object, Function]
            },
            resizeImage: {
                type: [Boolean, Object],
                default: !0
            },
            moveImage: {
                type: [Boolean, Object],
                default: !0
            },
            autoZoomAlgorithm: {
                type: Function
            },
            resizeAlgorithm: {
                type: Function,
                default: function(t) {
                    var e = t.event,
                        i = t.coordinates,
                        n = t.aspectRatio,
                        o = t.positionRestrictions,
                        s = t.sizeRestrictions,
                        r = yt(yt({}, i), {
                            right: i.left + i.width,
                            bottom: i.top + i.height
                        }),
                        a = e.params || {},
                        h = yt({}, e.directions),
                        c = a.allowedDirections || {
                            left: !0,
                            right: !0,
                            bottom: !0,
                            top: !0
                        };
                    s.widthFrozen && (h.left = 0, h.right = 0), s.heightFrozen && (h.top = 0, h.bottom = 0), Ut.forEach((function(t) {
                        c[t] || (h[t] = 0)
                    }));
                    var l = Kt(r, h = Se({
                            coordinates: r,
                            directions: h,
                            sizeRestrictions: s,
                            positionRestrictions: o
                        })).width,
                        u = Kt(r, h).height,
                        d = a.preserveRatio ? ie(r) : se(l / u, n);
                    if (d) {
                        var m = a.respectDirection;
                        if (m || (m = r.width >= r.height || 1 === d ? "width" : "height"), "width" === m) {
                            var p = l / d - r.height;
                            if (c.top && c.bottom) {
                                var g = h.top,
                                    f = h.bottom;
                                h.bottom = Ee(p, f, g), h.top = Ee(p, g, f)
                            } else c.bottom ? h.bottom = p : c.top ? h.top = p : c.right ? h.right = 0 : c.left && (h.left = 0)
                        } else if ("height" === m) {
                            var v = r.width - u * d;
                            if (c.left && c.right) {
                                var b = h.left,
                                    w = h.right;
                                h.left = -Ee(v, b, w), h.right = -Ee(v, w, b)
                            } else c.left ? h.left = -v : c.right ? h.right = -v : c.top ? h.top = 0 : c.bottom && (h.bottom = 0)
                        }
                        h = Se({
                            directions: h,
                            coordinates: r,
                            sizeRestrictions: s,
                            positionRestrictions: o,
                            preserveRatio: !0,
                            compensate: a.compensate
                        })
                    }
                    return l = Kt(r, h).width, u = Kt(r, h).height, (d = a.preserveRatio ? ie(r) : se(l / u, n)) && Math.abs(d - l / u) > .001 && Ut.forEach((function(t) {
                        c[t] || (h[t] = 0)
                    })), ve({
                        event: new kt({
                            left: -h.left,
                            top: -h.top
                        }),
                        coordinates: {
                            width: i.width + h.right + h.left,
                            height: i.height + h.top + h.bottom,
                            left: i.left,
                            top: i.top
                        },
                        positionRestrictions: o
                    })
                }
            },
            moveAlgorithm: {
                type: Function,
                default: ve
            },
            initStretcher: {
                type: Function,
                default: function(t) {
                    var e = t.stretcher,
                        i = t.imageSize,
                        n = ie(i);
                    e.style.width = i.width + "px", e.style.height = e.clientWidth / n + "px", e.style.width = e.clientWidth + "px"
                }
            },
            fitCoordinates: {
                type: Function,
                default: function(t) {
                    var e = t.visibleArea,
                        i = t.coordinates,
                        n = t.aspectRatio,
                        o = t.sizeRestrictions,
                        s = t.positionRestrictions,
                        r = yt(yt({}, i), fe({
                            width: i.width,
                            height: i.height,
                            aspectRatio: n,
                            sizeRestrictions: {
                                maxWidth: e.width,
                                maxHeight: e.height,
                                minHeight: Math.min(e.height, o.minHeight),
                                minWidth: Math.min(e.width, o.minWidth)
                            }
                        }));
                    return le(r = te(r, Zt(Gt(i), Gt(r))), me(qt(e), s))
                }
            },
            fitVisibleArea: {
                type: Function,
                default: function(t) {
                    var e = t.visibleArea,
                        i = t.boundaries,
                        n = t.getAreaRestrictions,
                        o = t.coordinates,
                        s = yt({}, e);
                    s.height = s.width / ie(i), s.top += (e.height - s.height) / 2, (o.height - s.height > 0 || o.width - s.width > 0) && (s = ee(s, Math.max(o.height / s.height, o.width / s.width)));
                    var r = Jt(oe(o, qt(s = ee(s, ce(s, n({
                        visibleArea: s,
                        type: "resize"
                    }))))));
                    return s.width < o.width && (r.left = 0), s.height < o.height && (r.top = 0), le(s = te(s, r), n({
                        visibleArea: s,
                        type: "move"
                    }))
                }
            },
            areaRestrictionsAlgorithm: {
                type: Function,
                default: function(t) {
                    var e = t.visibleArea,
                        i = t.boundaries,
                        n = t.imageSize,
                        o = t.imageRestriction,
                        s = t.type,
                        r = {};
                    return "fill-area" === o ? r = {
                        left: 0,
                        top: 0,
                        right: n.width,
                        bottom: n.height
                    } : "fit-area" === o && (ie(i) > ie(n) ? (r = {
                        top: 0,
                        bottom: n.height
                    }, e && "move" === s && (e.width > n.width ? (r.left = -(e.width - n.width) / 2, r.right = n.width - r.left) : (r.left = 0, r.right = n.width))) : (r = {
                        left: 0,
                        right: n.width
                    }, e && "move" === s && (e.height > n.height ? (r.top = -(e.height - n.height) / 2, r.bottom = n.height - r.top) : (r.top = 0, r.bottom = n.height)))), r
                }
            },
            sizeRestrictionsAlgorithm: {
                type: Function,
                default: function(t) {
                    return {
                        minWidth: t.minWidth,
                        minHeight: t.minHeight,
                        maxWidth: t.maxWidth,
                        maxHeight: t.maxHeight
                    }
                }
            },
            positionRestrictionsAlgorithm: {
                type: Function,
                default: function(t) {
                    var e = t.imageSize,
                        i = {};
                    return "none" !== t.imageRestriction && (i = {
                        left: 0,
                        top: 0,
                        right: e.width,
                        bottom: e.height
                    }), i
                }
            }
        },
        data: function() {
            return {
                transitionsActive: !1,
                imageLoaded: !1,
                imageAttributes: {
                    width: null,
                    height: null,
                    crossOrigin: null,
                    src: null
                },
                defaultImageTransforms: {
                    rotate: 0,
                    flip: {
                        horizontal: !1,
                        vertical: !1
                    }
                },
                appliedImageTransforms: {
                    rotate: 0,
                    flip: {
                        horizontal: !1,
                        vertical: !1
                    }
                },
                boundaries: {
                    width: 0,
                    height: 0
                },
                visibleArea: null,
                coordinates: ut({}, Nt)
            }
        },
        computed: {
            image: function() {
                return {
                    src: this.imageAttributes.src,
                    width: this.imageAttributes.width,
                    height: this.imageAttributes.height,
                    transforms: this.imageTransforms
                }
            },
            imageTransforms: function() {
                return {
                    rotate: this.appliedImageTransforms.rotate,
                    flip: {
                        horizontal: this.appliedImageTransforms.flip.horizontal,
                        vertical: this.appliedImageTransforms.flip.vertical
                    },
                    translateX: this.visibleArea ? this.visibleArea.left / this.coefficient : 0,
                    translateY: this.visibleArea ? this.visibleArea.top / this.coefficient : 0,
                    scaleX: 1 / this.coefficient,
                    scaleY: 1 / this.coefficient
                }
            },
            imageSize: function() {
                var t = this.imageTransforms.rotate * Math.PI / 180;
                return {
                    width: Math.abs(this.imageAttributes.width * Math.cos(t)) + Math.abs(this.imageAttributes.height * Math.sin(t)),
                    height: Math.abs(this.imageAttributes.width * Math.sin(t)) + Math.abs(this.imageAttributes.height * Math.cos(t))
                }
            },
            initialized: function() {
                return Boolean(this.visibleArea && this.imageLoaded)
            },
            settings: function() {
                var t = Et(this.resizeImage, {
                    touch: !0,
                    wheel: {
                        ratio: .1
                    },
                    adjustStencil: !0
                }, {
                    touch: !1,
                    wheel: !1,
                    adjustStencil: !1
                });
                return {
                    moveImage: Et(this.moveImage, {
                        touch: !0,
                        mouse: !0
                    }, {
                        touch: !1,
                        mouse: !1
                    }),
                    resizeImage: t
                }
            },
            coefficient: function() {
                return this.visibleArea ? this.visibleArea.width / this.boundaries.width : 0
            },
            areaRestrictions: function() {
                return this.imageLoaded ? this.areaRestrictionsAlgorithm({
                    imageSize: this.imageSize,
                    imageRestriction: this.imageRestriction,
                    boundaries: this.boundaries
                }) : {}
            },
            transitionsOptions: function() {
                return {
                    enabled: this.transitionsActive,
                    timingFunction: "ease-in-out",
                    time: 350
                }
            },
            sizeRestrictions: function() {
                if (this.boundaries.width && this.boundaries.height && this.imageSize.width && this.imageSize.height) {
                    var t = this.sizeRestrictionsAlgorithm({
                        imageSize: this.imageSize,
                        minWidth: xt(this.minWidth) ? 0 : Ct(this.minWidth),
                        minHeight: xt(this.minHeight) ? 0 : Ct(this.minHeight),
                        maxWidth: xt(this.maxWidth) ? 1 / 0 : Ct(this.maxWidth),
                        maxHeight: xt(this.maxHeight) ? 1 / 0 : Ct(this.maxHeight)
                    });
                    if (t = function(t) {
                            var e = t.areaRestrictions,
                                i = t.sizeRestrictions,
                                n = t.boundaries,
                                o = t.positionRestrictions,
                                s = yt(yt({}, i), {
                                    minWidth: void 0 !== i.minWidth ? i.minWidth : 0,
                                    minHeight: void 0 !== i.minHeight ? i.minHeight : 0,
                                    maxWidth: void 0 !== i.maxWidth ? i.maxWidth : 1 / 0,
                                    maxHeight: void 0 !== i.maxHeight ? i.maxHeight : 1 / 0
                                });
                            void 0 !== o.left && void 0 !== o.right && (s.maxWidth = Math.min(s.maxWidth, o.right - o.left)), void 0 !== o.bottom && void 0 !== o.top && (s.maxHeight = Math.min(s.maxHeight, o.bottom - o.top));
                            var r = ue(e),
                                a = re(n, r);
                            return r.width < 1 / 0 && (!s.maxWidth || s.maxWidth > a.width) && (s.maxWidth = Math.min(s.maxWidth, a.width)), r.height < 1 / 0 && (!s.maxHeight || s.maxHeight > a.height) && (s.maxHeight = Math.min(s.maxHeight, a.height)), s.minWidth > s.maxWidth && (s.minWidth = s.maxWidth, s.widthFrozen = !0), s.minHeight > s.maxHeight && (s.minHeight = s.maxHeight, s.heightFrozen = !0), s
                        }({
                            sizeRestrictions: t,
                            areaRestrictions: this.getAreaRestrictions({
                                visibleArea: this.visibleArea,
                                type: "resize"
                            }),
                            imageSize: this.imageSize,
                            boundaries: this.boundaries,
                            positionRestrictions: this.positionRestrictions,
                            imageRestriction: this.imageRestriction,
                            visibleArea: this.visibleArea,
                            stencilSize: this.getStencilSize()
                        }), this.visibleArea && this.stencilSize) {
                        var e = this.getStencilSize(),
                            i = ue(this.getAreaRestrictions({
                                visibleArea: this.visibleArea,
                                type: "resize"
                            }));
                        t.maxWidth = Math.min(t.maxWidth, i.width * e.width / this.boundaries.width), t.maxHeight = Math.min(t.maxHeight, i.height * e.height / this.boundaries.height), t.maxWidth < t.minWidth && (t.minWidth = t.maxWidth), t.maxHeight < t.minHeight && (t.minHeight = t.maxHeight)
                    }
                    return t
                }
                return {
                    minWidth: 0,
                    minHeight: 0,
                    maxWidth: 0,
                    maxHeight: 0
                }
            },
            positionRestrictions: function() {
                return this.positionRestrictionsAlgorithm({
                    imageSize: this.imageSize,
                    imageRestriction: this.imageRestriction
                })
            },
            classes: function() {
                return {
                    cropper: Qe(),
                    image: ft(Qe("image"), this.imageClass),
                    stencil: Qe("stencil"),
                    boundaries: ft(Qe("boundaries"), this.boundariesClass),
                    stretcher: ft(Qe("stretcher")),
                    background: ft(Qe("background"), this.backgroundClass),
                    foreground: ft(Qe("foreground"), this.foregroundClass),
                    imageWrapper: ft(Qe("image-wrapper")),
                    cropperWrapper: ft(Qe("cropper-wrapper"))
                }
            },
            stencilCoordinates: function() {
                if (this.initialized) {
                    var t = this.coordinates,
                        e = t.width,
                        i = t.height,
                        n = t.left,
                        o = t.top;
                    return {
                        width: e / this.coefficient,
                        height: i / this.coefficient,
                        left: (n - this.visibleArea.left) / this.coefficient,
                        top: (o - this.visibleArea.top) / this.coefficient
                    }
                }
                return this.defaultCoordinates()
            },
            boundariesStyle: function() {
                var t = {
                    width: this.boundaries.width ? "".concat(Math.round(this.boundaries.width), "px") : "auto",
                    height: this.boundaries.height ? "".concat(Math.round(this.boundaries.height), "px") : "auto",
                    transition: "opacity ".concat(this.transitionTime, "ms"),
                    pointerEvents: this.imageLoaded ? "all" : "none"
                };
                return this.imageLoaded || (t.opacity = "0"), t
            },
            imageStyle: function() {
                var t = this.imageAttributes.width > this.imageAttributes.height ? {
                        width: Math.min(1024, this.imageAttributes.width),
                        height: Math.min(1024, this.imageAttributes.width) / (this.imageAttributes.width / this.imageAttributes.height)
                    } : {
                        height: Math.min(1024, this.imageAttributes.height),
                        width: Math.min(1024, this.imageAttributes.height) * (this.imageAttributes.width / this.imageAttributes.height)
                    },
                    e = (t.width - this.imageSize.width) / (2 * this.coefficient),
                    i = (t.height - this.imageSize.height) / (2 * this.coefficient),
                    n = (1 - 1 / this.coefficient) * t.width / 2,
                    o = (1 - 1 / this.coefficient) * t.height / 2,
                    s = ut(ut({}, this.imageTransforms), {}, {
                        scaleX: this.imageTransforms.scaleX * (this.imageAttributes.width / t.width),
                        scaleY: this.imageTransforms.scaleY * (this.imageAttributes.height / t.height)
                    }),
                    r = {
                        width: "".concat(t.width, "px"),
                        height: "".concat(t.height, "px"),
                        left: "0px",
                        top: "0px",
                        transform: "translate(".concat(-e - n - this.imageTransforms.translateX, "px, ").concat(-i - o - this.imageTransforms.translateY, "px)") + Ue(s)
                    };
                return this.transitionsOptions.enabled && (r.transition = "".concat(this.transitionsOptions.time, "ms ").concat(this.transitionsOptions.timingFunction)), r
            }
        },
        watch: {
            src: function() {
                this.onChangeImage()
            },
            stencilComponent: function() {
                var t = this;
                this.$nextTick((function() {
                    t.resetCoordinates(), t.runAutoZoom("setCoordinates"), t.onChange()
                }))
            },
            minWidth: function() {
                this.onPropsChange()
            },
            maxWidth: function() {
                this.onPropsChange()
            },
            minHeight: function() {
                this.onPropsChange()
            },
            maxHeight: function() {
                this.onPropsChange()
            },
            imageRestriction: function() {
                this.reset()
            },
            stencilProps: function(t, e) {
                ["aspectRatio", "minAspectRatio", "maxAspectRatio"].find((function(i) {
                    return t[i] !== e[i]
                })) && this.$nextTick(this.onPropsChange)
            }
        },
        created: function() {
            this.debouncedUpdate = wt(this.update, this.debounce), this.debouncedDisableTransitions = wt(this.disableTransitions, this.transitionsOptions.time), this.awaiting = !1
        },
        mounted: function() {
            this.$refs.image.addEventListener("load", this.onSuccessLoadImage), this.$refs.image.addEventListener("error", this.onFailLoadImage), this.onChangeImage(), window.addEventListener("resize", this.refresh), window.addEventListener("orientationchange", this.refresh)
        },
        unmounted: function() {
            window.removeEventListener("resize", this.refresh), window.removeEventListener("orientationchange", this.refresh), this.imageAttributes.revoke && this.imageAttributes.src && URL.revokeObjectURL(this.imageAttributes.src), this.debouncedUpdate.clear(), this.debouncedDisableTransitions.clear()
        },
        methods: {
            getResult: function() {
                var t = this.initialized ? this.prepareResult(ut({}, this.coordinates)) : this.defaultCoordinates(),
                    e = {
                        rotate: this.imageTransforms.rotate % 360,
                        flip: ut({}, this.imageTransforms.flip)
                    };
                if (this.src && this.imageLoaded) {
                    var i = this;
                    return {
                        image: this.image,
                        coordinates: t,
                        visibleArea: this.visibleArea ? ut({}, this.visibleArea) : null,
                        imageTransforms: e,
                        get canvas() {
                            return i.canvas ? i.getCanvas() : void 0
                        }
                    }
                }
                return {
                    image: this.image,
                    coordinates: t,
                    visibleArea: this.visibleArea ? ut({}, this.visibleArea) : null,
                    canvas: void 0,
                    imageTransforms: e
                }
            },
            zoom: function(t, e) {
                var i = (arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}).transitions,
                    n = void 0 === i || i;
                this.onManipulateImage(new Ht({}, {
                    factor: 1 / t,
                    center: e
                }), {
                    normalize: !1,
                    transitions: n
                })
            },
            move: function(t, e) {
                var i = (arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}).transitions,
                    n = void 0 === i || i;
                this.onManipulateImage(new Ht({
                    left: t || 0,
                    top: e || 0
                }), {
                    normalize: !1,
                    transitions: n
                })
            },
            setCoordinates: function(t) {
                var e = this,
                    i = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                    n = i.autoZoom,
                    o = void 0 === n || n,
                    s = i.transitions,
                    r = void 0 === s || s;
                this.$nextTick((function() {
                    e.imageLoaded ? (e.transitionsActive || (r && e.enableTransitions(), e.coordinates = e.applyTransform(t), o && e.runAutoZoom("setCoordinates"), r && e.debouncedDisableTransitions()), e.onChange()) : e.delayedTransforms = t
                }))
            },
            refresh: function() {
                var t = this,
                    e = this.$refs.image;
                if (this.src && e) return this.initialized ? this.updateVisibleArea().then((function() {
                    t.onChange()
                })) : this.resetVisibleArea().then((function() {
                    t.onChange()
                }))
            },
            reset: function() {
                var t = this;
                return this.resetVisibleArea().then((function() {
                    t.onChange(!1)
                }))
            },
            awaitRender: function(t) {
                var e = this;
                this.awaiting || (this.awaiting = !0, this.$nextTick((function() {
                    t(), e.awaiting = !1
                })))
            },
            prepareResult: function(t) {
                return this.roundResult ? (e = ut(ut({}, this.getPublicProperties()), {}, {
                    positionRestrictions: xe(this.positionRestrictions, this.visibleArea),
                    coordinates: t
                }), i = e.coordinates, n = e.sizeRestrictions, o = e.positionRestrictions, (s = {
                    width: Math.round(i.width),
                    height: Math.round(i.height),
                    left: Math.round(i.left),
                    top: Math.round(i.top)
                }).width > n.maxWidth ? s.width = Math.floor(i.width) : s.width < n.minWidth && (s.width = Math.ceil(i.width)), s.height > n.maxHeight ? s.height = Math.floor(i.height) : s.height < n.minHeight && (s.height = Math.ceil(i.height)), le(s, o)) : t;
                var e, i, n, o, s
            },
            processAutoZoom: function(t, e, i, n) {
                var o = this.autoZoomAlgorithm;
                o || (o = this.stencilSize ? be : this.autoZoom ? we : ye);
                var s = o({
                    event: {
                        type: t,
                        params: n
                    },
                    visibleArea: e,
                    coordinates: i,
                    boundaries: this.boundaries,
                    aspectRatio: this.getAspectRatio(),
                    positionRestrictions: this.positionRestrictions,
                    getAreaRestrictions: this.getAreaRestrictions,
                    sizeRestrictions: this.sizeRestrictions,
                    stencilSize: this.getStencilSize()
                });
                return ut(ut({}, s), {}, {
                    changed: !Vt(s.visibleArea, e) || !Vt(s.coordinates, i)
                })
            },
            runAutoZoom: function(t) {
                var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                    i = e.transitions,
                    n = void 0 !== i && i,
                    o = function(t, e) {
                        if (null == t) return {};
                        var i, n, o = function(t, e) {
                            if (null == t) return {};
                            var i, n, o = {},
                                s = Object.keys(t);
                            for (n = 0; n < s.length; n++) i = s[n], e.indexOf(i) >= 0 || (o[i] = t[i]);
                            return o
                        }(t, e);
                        if (Object.getOwnPropertySymbols) {
                            var s = Object.getOwnPropertySymbols(t);
                            for (n = 0; n < s.length; n++) i = s[n], e.indexOf(i) >= 0 || Object.prototype.propertyIsEnumerable.call(t, i) && (o[i] = t[i])
                        }
                        return o
                    }(e, Ge),
                    s = this.processAutoZoom(t, this.visibleArea, this.coordinates, o),
                    r = s.visibleArea,
                    a = s.coordinates,
                    h = s.changed;
                n && h && this.enableTransitions(), this.visibleArea = r, this.coordinates = a, n && h && this.debouncedDisableTransitions()
            },
            normalizeEvent: function(t) {
                return function(t) {
                    var e = t.event,
                        i = t.visibleArea,
                        n = t.coefficient;
                    if ("manipulateImage" === e.type) return yt(yt({}, e), {
                        move: {
                            left: e.move && e.move.left ? n * e.move.left : 0,
                            top: e.move && e.move.top ? n * e.move.top : 0
                        },
                        scale: {
                            factor: e.scale && e.scale.factor ? e.scale.factor : 1,
                            center: e.scale && e.scale.center ? {
                                left: e.scale.center.left * n + i.left,
                                top: e.scale.center.top * n + i.top
                            } : null
                        }
                    });
                    if ("resize" === e.type) {
                        var o = yt(yt({}, e), {
                            directions: yt({}, e.directions)
                        });
                        return Ut.forEach((function(t) {
                            o.directions[t] *= n
                        })), o
                    }
                    if ("move" === e.type) {
                        var s = yt(yt({}, e), {
                            directions: yt({}, e.directions)
                        });
                        return Ft.forEach((function(t) {
                            s.directions[t] *= n
                        })), s
                    }
                    return e
                }(ut(ut({}, this.getPublicProperties()), {}, {
                    event: t
                }))
            },
            getCanvas: function() {
                if (this.$refs.canvas) {
                    var t = this.$refs.canvas,
                        e = this.$refs.image,
                        i = 0 !== this.imageTransforms.rotate || this.imageTransforms.flip.horizontal || this.imageTransforms.flip.vertical ? function(t, e, i) {
                            var n = i.rotate,
                                o = i.flip,
                                s = {
                                    width: e.naturalWidth,
                                    height: e.naturalHeight
                                },
                                r = ae(s, n),
                                a = t.getContext("2d");
                            t.height = r.height, t.width = r.width, a.save();
                            var h = he(Gt(yt({
                                left: 0,
                                top: 0
                            }, s)), n);
                            return a.translate(-(h.left - r.width / 2), -(h.top - r.height / 2)), a.rotate(n * Math.PI / 180), a.translate(o.horizontal ? s.width : 0, o.vertical ? s.height : 0), a.scale(o.horizontal ? -1 : 1, o.vertical ? -1 : 1), a.drawImage(e, 0, 0, s.width, s.height), a.restore(), t
                        }(this.$refs.sourceCanvas, e, this.imageTransforms) : e,
                        n = ut({
                            minWidth: 0,
                            minHeight: 0,
                            maxWidth: 1 / 0,
                            maxHeight: 1 / 0,
                            maxArea: this.maxCanvasSize,
                            imageSmoothingEnabled: !0,
                            imageSmoothingQuality: "high",
                            fillColor: "transparent"
                        }, this.canvas),
                        o = function(t) {
                            return t.find((function(t) {
                                return e = t, !Number.isNaN(parseFloat(e)) && isFinite(e);
                                var e
                            }))
                        },
                        s = fe({
                            sizeRestrictions: {
                                minWidth: o([n.width, n.minWidth]) || 0,
                                minHeight: o([n.height, n.minHeight]) || 0,
                                maxWidth: o([n.width, n.maxWidth]) || 1 / 0,
                                maxHeight: o([n.height, n.maxHeight]) || 1 / 0
                            },
                            width: this.coordinates.width,
                            height: this.coordinates.height,
                            aspectRatio: {
                                minimum: this.coordinates.width / this.coordinates.height,
                                maximum: this.coordinates.width / this.coordinates.height
                            }
                        });
                    if (n.maxArea && s.width * s.height > n.maxArea) {
                        var r = Math.sqrt(n.maxArea / (s.width * s.height));
                        s = {
                            width: Math.round(r * s.width),
                            height: Math.round(r * s.height)
                        }
                    }
                    return function(t, e, i, n, o) {
                        t.width = n ? n.width : i.width, t.height = n ? n.height : i.height;
                        var s = t.getContext("2d");
                        s.clearRect(0, 0, t.width, t.height), o && (o.imageSmoothingEnabled && (s.imageSmoothingEnabled = o.imageSmoothingEnabled), o.imageSmoothingQuality && (s.imageSmoothingQuality = o.imageSmoothingQuality), o.fillColor && (s.fillStyle = o.fillColor, s.fillRect(0, 0, t.width, t.height), s.save()));
                        var r = i.left < 0 ? -i.left : 0,
                            a = i.top < 0 ? -i.top : 0;
                        s.drawImage(e, i.left + r, i.top + a, i.width, i.height, r * (t.width / i.width), a * (t.height / i.height), t.width, t.height)
                    }(t, i, this.coordinates, s, n), t
                }
            },
            update: function() {
                this.$emit("change", this.getResult())
            },
            applyTransform: function(t) {
                var e = arguments.length > 1 && void 0 !== arguments[1] && arguments[1],
                    i = this.visibleArea && e ? function(t, e) {
                        return yt(yt({}, t), {
                            minWidth: Math.min(e.width, t.minWidth),
                            minHeight: Math.min(e.height, t.minHeight),
                            maxWidth: Math.min(e.width, t.maxWidth),
                            maxHeight: Math.min(e.height, t.maxHeight)
                        })
                    }(this.sizeRestrictions, this.visibleArea) : this.sizeRestrictions,
                    n = this.visibleArea && e ? xe(this.positionRestrictions, this.visibleArea) : this.positionRestrictions;
                return function(t) {
                    var e = t.coordinates,
                        i = t.transform,
                        n = t.imageSize,
                        o = t.sizeRestrictions,
                        s = t.positionRestrictions,
                        r = t.aspectRatio,
                        a = t.visibleArea,
                        h = function(t, e) {
                            return ve({
                                coordinates: t,
                                positionRestrictions: s,
                                event: new kt({
                                    left: e.left - t.left,
                                    top: e.top - t.top
                                })
                            })
                        },
                        c = yt({}, e);
                    return (Array.isArray(i) ? i : [i]).forEach((function(t) {
                        var e, i, s, l;
                        xt((e = "function" == typeof t ? t({
                            coordinates: c,
                            imageSize: n,
                            visibleArea: a
                        }) : t).width) && xt(e.height) || (i = c, s = yt(yt({}, c), e), l = yt(yt(yt({}, i), fe({
                            width: s.width,
                            height: s.height,
                            sizeRestrictions: o,
                            aspectRatio: r
                        })), {
                            left: 0,
                            top: 0
                        }), c = h(l, {
                            left: i.left,
                            top: i.top
                        })), xt(e.left) && xt(e.top) || (c = h(c, yt(yt({}, c), e)))
                    })), c
                }({
                    transform: t,
                    coordinates: this.coordinates,
                    imageSize: this.imageSize,
                    sizeRestrictions: i,
                    positionRestrictions: n,
                    aspectRatio: this.getAspectRatio(),
                    visibleArea: this.visibleArea
                })
            },
            resetCoordinates: function() {
                var t = this;
                if (this.$refs.image) {
                    this.$refs.cropper, this.$refs.image;
                    var e = this.defaultSize;
                    e || (e = this.stencilSize ? Re : Ae);
                    var i = this.sizeRestrictions;
                    i.minWidth, i.minHeight, i.maxWidth, i.maxHeight;
                    var n = Mt(e) ? e({
                            boundaries: this.boundaries,
                            imageSize: this.imageSize,
                            aspectRatio: this.getAspectRatio(),
                            sizeRestrictions: this.sizeRestrictions,
                            stencilSize: this.getStencilSize(),
                            visibleArea: this.visibleArea
                        }) : e,
                        o = this.defaultPosition || ze,
                        s = [n, function(e) {
                            var i = e.coordinates;
                            return ut({}, Mt(o) ? o({
                                coordinates: i,
                                imageSize: t.imageSize,
                                visibleArea: t.visibleArea
                            }) : t.defaultPosition)
                        }];
                    this.delayedTransforms && s.push.apply(s, mt(Array.isArray(this.delayedTransforms) ? this.delayedTransforms : [this.delayedTransforms])), this.coordinates = this.applyTransform(s, !0), this.delayedTransforms = null
                }
            },
            clearImage: function() {
                var t = this;
                this.imageLoaded = !1, setTimeout((function() {
                    var e = t.$refs.stretcher;
                    e && (e.style.height = "auto", e.style.width = "auto"), t.coordinates = t.defaultCoordinates(), t.boundaries = {
                        width: 0,
                        height: 0
                    }
                }), this.transitionTime)
            },
            enableTransitions: function() {
                this.transitions && (this.transitionsActive = !0)
            },
            disableTransitions: function() {
                this.transitionsActive = !1
            },
            updateBoundaries: function() {
                var t = this,
                    e = this.$refs.stretcher,
                    i = this.$refs.cropper;
                return this.initStretcher({
                    cropper: i,
                    stretcher: e,
                    imageSize: this.imageSize
                }), this.$nextTick().then((function() {
                    var e, n, o, s, r, a, h, c = {
                        cropper: i,
                        imageSize: t.imageSize
                    };
                    if (Mt(t.defaultBoundaries) ? t.boundaries = t.defaultBoundaries(c) : "fit" === t.defaultBoundaries ? t.boundaries = (o = (e = c).imageSize, s = (n = e.cropper).clientHeight, r = n.clientWidth, a = s, (h = o.width * s / o.height) > r && (h = r, a = o.height * r / o.width), {
                            width: h,
                            height: a
                        }) : t.boundaries = function(t) {
                            var e = t.cropper;
                            return {
                                width: e.clientWidth,
                                height: e.clientHeight
                            }
                        }(c), !t.boundaries.width || !t.boundaries.height) throw new Error("It's impossible to fit the cropper in the current container")
                }))
            },
            resetVisibleArea: function() {
                var t = this;
                return this.appliedImageTransforms = ut(ut({}, this.defaultImageTransforms), {}, {
                    flip: ut({}, this.defaultImageTransforms.flip)
                }), this.updateBoundaries().then((function() {
                    "visible-area" !== t.priority && (t.visibleArea = null, t.resetCoordinates());
                    var e, i, n, o, s, r = t.defaultVisibleArea || Me;
                    t.visibleArea = Mt(r) ? r({
                        imageSize: t.imageSize,
                        boundaries: t.boundaries,
                        coordinates: "visible-area" !== t.priority ? t.coordinates : null,
                        getAreaRestrictions: t.getAreaRestrictions,
                        stencilSize: t.getStencilSize()
                    }) : t.defaultVisibleArea, t.visibleArea = (i = (e = {
                        visibleArea: t.visibleArea,
                        boundaries: t.boundaries,
                        getAreaRestrictions: t.getAreaRestrictions
                    }).boundaries, n = e.getAreaRestrictions, o = yt({}, e.visibleArea), s = ie(i), o.width / o.height !== s && (o.height = o.width / s), le(o, n({
                        visibleArea: o,
                        type: "move"
                    }))), "visible-area" === t.priority ? t.resetCoordinates() : t.coordinates = t.fitCoordinates({
                        visibleArea: t.visibleArea,
                        coordinates: t.coordinates,
                        aspectRatio: t.getAspectRatio(),
                        positionRestrictions: t.positionRestrictions,
                        sizeRestrictions: t.sizeRestrictions
                    }), t.runAutoZoom("resetVisibleArea")
                })).catch((function() {
                    t.visibleArea = null
                }))
            },
            updateVisibleArea: function() {
                var t = this;
                return this.updateBoundaries().then((function() {
                    t.visibleArea = t.fitVisibleArea({
                        imageSize: t.imageSize,
                        boundaries: t.boundaries,
                        visibleArea: t.visibleArea,
                        coordinates: t.coordinates,
                        getAreaRestrictions: t.getAreaRestrictions
                    }), t.coordinates = t.fitCoordinates({
                        visibleArea: t.visibleArea,
                        coordinates: t.coordinates,
                        aspectRatio: t.getAspectRatio(),
                        positionRestrictions: t.positionRestrictions,
                        sizeRestrictions: t.sizeRestrictions
                    }), t.runAutoZoom("updateVisibleArea")
                })).catch((function() {
                    t.visibleArea = null
                }))
            },
            onChange: function() {
                (!(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0]) && this.debounce ? this.debouncedUpdate() : this.update()
            },
            onChangeImage: function() {
                var t, e = this;
                if (this.imageLoaded = !1, this.delayedTransforms = null, this.src) {
                    if (function(t) {
                            if (Rt(t)) return !1;
                            var e = window.location,
                                i = /(\w+:)?(?:\/\/)([\w.-]+)?(?::(\d+))?\/?/.exec(t) || [],
                                n = {
                                    protocol: i[1] || "",
                                    host: i[2] || "",
                                    port: i[3] || ""
                                },
                                o = function(t) {
                                    return t.port || ("http" === (t.protocol || e.protocol) ? 80 : 433)
                                };
                            return !(!n.protocol && !n.host && !n.port || Boolean(n.protocol && n.protocol == e.protocol && n.host && n.host == e.host && n.host && o(n) == o(e)))
                        }(this.src)) {
                        var i = xt(this.crossOrigin) ? this.canvas : this.crossOrigin;
                        !0 === i && (i = "anonymous"), this.imageAttributes.crossOrigin = i || null
                    }
                    if (this.checkOrientation) {
                        var n = (t = this.src, new Promise((function(e) {
                            (function(t) {
                                return new Promise((function(e, i) {
                                    try {
                                        if (t)
                                            if (/^data:/i.test(t)) e(function(t) {
                                                t = t.replace(/^data:([^;]+);base64,/gim, "");
                                                for (var e = atob(t), i = e.length, n = new ArrayBuffer(i), o = new Uint8Array(n), s = 0; s < i; s++) o[s] = e.charCodeAt(s);
                                                return n
                                            }(t));
                                            else if (/^blob:/i.test(t)) {
                                            var n = new FileReader;
                                            n.onload = function(t) {
                                                e(t.target.result)
                                            }, s = t, r = function(t) {
                                                n.readAsArrayBuffer(t)
                                            }, (a = new XMLHttpRequest).open("GET", s, !0), a.responseType = "blob", a.onload = function() {
                                                200 != this.status && 0 !== this.status || r(this.response)
                                            }, a.send()
                                        } else {
                                            var o = new XMLHttpRequest;
                                            o.onreadystatechange = function() {
                                                4 === o.readyState && (200 === o.status || 0 === o.status ? e(o.response) : i("Warning: could not load an image to parse its orientation"), o = null)
                                            }, o.onprogress = function() {
                                                "image/jpeg" !== o.getResponseHeader("content-type") && o.abort()
                                            }, o.withCredentials = !1, o.open("GET", t, !0), o.responseType = "arraybuffer", o.send(null)
                                        } else i("Error: the image is empty")
                                    } catch (h) {
                                        i(h)
                                    }
                                    var s, r, a
                                }))
                            })(t).then((function(i) {
                                var n = function(t) {
                                    try {
                                        var e, i = new DataView(t),
                                            n = void 0,
                                            o = void 0,
                                            s = void 0,
                                            r = void 0;
                                        if (255 === i.getUint8(0) && 216 === i.getUint8(1))
                                            for (var a = i.byteLength, h = 2; h + 1 < a;) {
                                                if (255 === i.getUint8(h) && 225 === i.getUint8(h + 1)) {
                                                    s = h;
                                                    break
                                                }
                                                h++
                                            }
                                        if (s && (n = s + 10, "Exif" === function(t, e, i) {
                                                var n, o = "";
                                                for (n = e, i += e; n < i; n++) o += String.fromCharCode(t.getUint8(n));
                                                return o
                                            }(i, s + 4, 4))) {
                                            var c = i.getUint16(n);
                                            if (((o = 18761 === c) || 19789 === c) && 42 === i.getUint16(n + 2, o)) {
                                                var l = i.getUint32(n + 4, o);
                                                l >= 8 && (r = n + l)
                                            }
                                        }
                                        if (r)
                                            for (var u = i.getUint16(r, o), d = 0; d < u; d++)
                                                if (h = r + 12 * d + 2, 274 === i.getUint16(h, o)) {
                                                    h += 8, e = i.getUint16(h, o), i.setUint16(h, 1, o);
                                                    break
                                                }
                                        return e
                                    } catch (m) {
                                        return null
                                    }
                                }(i);
                                e(i ? {
                                    source: t,
                                    arrayBuffer: i,
                                    orientation: n
                                } : {
                                    source: t,
                                    arrayBuffer: null,
                                    orientation: null
                                })
                            })).catch((function(i) {
                                e({
                                    source: t,
                                    arrayBuffer: null,
                                    orientation: null
                                })
                            }))
                        })));
                        setTimeout((function() {
                            n.then(e.onParseImage)
                        }), this.transitionTime)
                    } else setTimeout((function() {
                        e.onParseImage({
                            source: e.src
                        })
                    }), this.transitionTime)
                } else this.clearImage()
            },
            onFailLoadImage: function() {
                this.imageAttributes.src && (this.clearImage(), this.$emit("error"))
            },
            onSuccessLoadImage: function() {
                var t = this,
                    e = this.$refs.image;
                e && !this.imageLoaded && (this.imageAttributes.height = e.naturalHeight, this.imageAttributes.width = e.naturalWidth, this.imageLoaded = !0, this.resetVisibleArea().then((function() {
                    t.$emit("ready"), t.onChange(!1)
                })))
            },
            onParseImage: function(t) {
                var e = this,
                    i = t.source,
                    n = t.arrayBuffer,
                    o = t.orientation;
                this.imageAttributes.revoke && this.imageAttributes.src && URL.revokeObjectURL(this.imageAttributes.src), this.imageAttributes.revoke = !1, n && o && o > 1 ? At(i) || !Rt(i) ? (this.imageAttributes.src = URL.createObjectURL(new Blob([n])), this.imageAttributes.revoke = !0) : this.imageAttributes.src = function(t) {
                    for (var e = [], i = new Uint8Array(t); i.length > 0;) {
                        var n = i.subarray(0, 8192);
                        e.push(String.fromCharCode.apply(null, Array.from ? Array.from(n) : n.slice())), i = i.subarray(8192)
                    }
                    return "data:image/jpeg;base64," + btoa(e.join(""))
                }(n) : this.imageAttributes.src = i, Mt(this.defaultTransforms) ? this.appliedImageTransforms = Be(this.defaultTransforms()) : St(this.defaultTransforms) ? this.appliedImageTransforms = Be(this.defaultTransforms) : this.appliedImageTransforms = function(t) {
                    var e = Be({});
                    if (t) switch (t) {
                        case 2:
                            e.flip.horizontal = !0;
                            break;
                        case 3:
                            e.rotate = -180;
                            break;
                        case 4:
                            e.flip.vertical = !0;
                            break;
                        case 5:
                            e.rotate = 90, e.flip.vertical = !0;
                            break;
                        case 6:
                            e.rotate = 90;
                            break;
                        case 7:
                            e.rotate = 90, e.flip.horizontal = !0;
                            break;
                        case 8:
                            e.rotate = -90
                    }
                    return e
                }(o), this.defaultImageTransforms = ut(ut({}, this.appliedImageTransforms), {}, {
                    flip: ut({}, this.appliedImageTransforms.flip)
                }), this.$nextTick((function() {
                    var t = e.$refs.image;
                    t && t.complete && (Boolean(t.naturalWidth) ? e.onSuccessLoadImage() : e.onFailLoadImage())
                }))
            },
            onResizeEnd: function() {
                this.runAutoZoom("resize", {
                    transitions: !0
                })
            },
            onMoveEnd: function() {
                this.runAutoZoom("move", {
                    transitions: !0
                })
            },
            onMove: function(t) {
                var e = this;
                this.transitionsOptions.enabled || this.awaitRender((function() {
                    e.coordinates = e.moveAlgorithm(ut(ut({}, e.getPublicProperties()), {}, {
                        positionRestrictions: xe(e.positionRestrictions, e.visibleArea),
                        coordinates: e.coordinates,
                        event: e.normalizeEvent(t)
                    })), e.onChange()
                }))
            },
            onResize: function(t) {
                var e = this;
                this.transitionsOptions.enabled || this.stencilSize && !this.autoZoom || this.awaitRender((function() {
                    var i = e.sizeRestrictions,
                        n = Math.min(e.coordinates.width, e.coordinates.height, 20 * e.coefficient);
                    e.coordinates = e.resizeAlgorithm(ut(ut({}, e.getPublicProperties()), {}, {
                        positionRestrictions: xe(e.positionRestrictions, e.visibleArea),
                        sizeRestrictions: {
                            maxWidth: Math.min(i.maxWidth, e.visibleArea.width),
                            maxHeight: Math.min(i.maxHeight, e.visibleArea.height),
                            minWidth: Math.max(i.minWidth, n),
                            minHeight: Math.max(i.minHeight, n)
                        },
                        event: e.normalizeEvent(t)
                    })), e.onChange(), e.ticking = !1
                }))
            },
            onManipulateImage: function(t) {
                var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                if (!this.transitionsOptions.enabled) {
                    var i = e.transitions,
                        n = void 0 !== i && i,
                        o = e.normalize,
                        s = void 0 === o || o;
                    n && this.enableTransitions();
                    var r = function(t) {
                            var e = t.event,
                                i = t.coordinates,
                                n = t.visibleArea,
                                o = t.sizeRestrictions,
                                s = t.getAreaRestrictions,
                                r = t.positionRestrictions,
                                a = t.adjustStencil,
                                h = e.scale,
                                c = e.move,
                                l = yt({}, n),
                                u = yt({}, i),
                                d = 1,
                                m = 1,
                                p = h.factor && Math.abs(h.factor - 1) > .001;
                            l = te(l, {
                                left: c.left || 0,
                                top: c.top || 0
                            });
                            var g = {
                                stencil: {
                                    minimum: Math.max(o.minWidth ? o.minWidth / u.width : 0, o.minHeight ? o.minHeight / u.height : 0),
                                    maximum: Math.min(o.maxWidth ? o.maxWidth / u.width : 1 / 0, o.maxHeight ? o.maxHeight / u.height : 1 / 0, ne(u, r))
                                },
                                area: {
                                    maximum: ne(l, s({
                                        visibleArea: l,
                                        type: "resize"
                                    }))
                                }
                            };
                            h.factor && p && (h.factor < 1 ? (m = Math.max(h.factor, g.stencil.minimum)) > 1 && (m = 1) : h.factor > 1 && (m = Math.min(h.factor, Math.min(g.area.maximum, g.stencil.maximum))) < 1 && (m = 1)), m && (l = ee(l, m, h.center));
                            var f = i.left - n.left,
                                v = n.width + n.left - (i.width + i.left),
                                b = i.top - n.top,
                                w = n.height + n.top - (i.height + i.top);
                            return l = le(l = te(l, oe(l, {
                                left: void 0 !== r.left ? r.left - f * m : void 0,
                                top: void 0 !== r.top ? r.top - b * m : void 0,
                                bottom: void 0 !== r.bottom ? r.bottom + w * m : void 0,
                                right: void 0 !== r.right ? r.right + v * m : void 0
                            })), s({
                                visibleArea: l,
                                type: "move"
                            })), u.width = u.width * m, u.height = u.height * m, u.left = l.left + f * m, u.top = l.top + b * m, u = le(u, me(qt(l), r)), h.factor && p && a && (h.factor > 1 ? d = Math.min(g.area.maximum, h.factor) / m : h.factor < 1 && (d = Math.max(u.height / l.height, u.width / l.width, h.factor / m)), 1 !== d && (l = te(l = le(l = ee(l, d, h.factor > 1 ? h.center : Gt(u)), s({
                                visibleArea: l,
                                type: "move"
                            })), Jt(oe(u, qt(l)))))), {
                                coordinates: u,
                                visibleArea: l
                            }
                        }(ut(ut({}, this.getPublicProperties()), {}, {
                            event: s ? this.normalizeEvent(t) : t,
                            getAreaRestrictions: this.getAreaRestrictions,
                            imageRestriction: this.imageRestriction,
                            adjustStencil: !this.stencilSize && this.settings.resizeImage.adjustStencil
                        })),
                        a = r.visibleArea,
                        h = r.coordinates;
                    this.visibleArea = a, this.coordinates = h, this.runAutoZoom("manipulateImage"), this.onChange(), n && this.debouncedDisableTransitions()
                }
            },
            onPropsChange: function() {
                this.coordinates = this.applyTransform(this.coordinates, !0), this.onChange(!1)
            },
            getAreaRestrictions: function() {
                var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                    e = t.visibleArea,
                    i = t.type,
                    n = void 0 === i ? "move" : i;
                return this.areaRestrictionsAlgorithm({
                    boundaries: this.boundaries,
                    imageSize: this.imageSize,
                    imageRestriction: this.imageRestriction,
                    visibleArea: e,
                    type: n
                })
            },
            getAspectRatio: function(t) {
                var e, i, n = this.stencilProps,
                    o = n.aspectRatio,
                    s = n.minAspectRatio,
                    r = n.maxAspectRatio;
                if (this.$refs.stencil && this.$refs.stencil.aspectRatios) {
                    var a = this.$refs.stencil.aspectRatios();
                    e = a.minimum, i = a.maximum
                }
                if (xt(e) && (e = xt(o) ? s : o), xt(i) && (i = xt(o) ? r : o), !t && (xt(e) || xt(i))) {
                    var h = this.getStencilSize(),
                        c = h ? ie(h) : null;
                    xt(e) && (e = Tt(c) ? c : void 0), xt(i) && (i = Tt(c) ? c : void 0)
                }
                return {
                    minimum: e,
                    maximum: i
                }
            },
            getStencilSize: function() {
                if (this.stencilSize) return e = (t = {
                    currentStencilSize: {
                        width: this.stencilCoordinates.width,
                        height: this.stencilCoordinates.height
                    },
                    stencilSize: this.stencilSize,
                    boundaries: this.boundaries,
                    coefficient: this.coefficient,
                    coordinates: this.coordinates,
                    aspectRatio: this.getAspectRatio(!0)
                }).boundaries, n = t.aspectRatio, se(ie(o = Mt(i = t.stencilSize) ? i({
                    boundaries: e,
                    aspectRatio: n
                }) : i), n) && (o = fe({
                    sizeRestrictions: {
                        maxWidth: e.width,
                        maxHeight: e.height,
                        minWidth: 0,
                        minHeight: 0
                    },
                    width: o.width,
                    height: o.height,
                    aspectRatio: {
                        minimum: n.minimum,
                        maximum: n.maximum
                    }
                })), (o.width > e.width || o.height > e.height) && (o = fe({
                    sizeRestrictions: {
                        maxWidth: e.width,
                        maxHeight: e.height,
                        minWidth: 0,
                        minHeight: 0
                    },
                    width: o.width,
                    height: o.height,
                    aspectRatio: {
                        minimum: ie(o),
                        maximum: ie(o)
                    }
                })), o;
                var t, e, i, n, o
            },
            getPublicProperties: function() {
                return {
                    coefficient: this.coefficient,
                    visibleArea: this.visibleArea,
                    coordinates: this.coordinates,
                    boundaries: this.boundaries,
                    sizeRestrictions: this.sizeRestrictions,
                    positionRestrictions: this.positionRestrictions,
                    aspectRatio: this.getAspectRatio(),
                    imageRestriction: this.imageRestriction
                }
            },
            defaultCoordinates: function() {
                return ut({}, Nt)
            },
            flip: function(t, e) {
                var i = (arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}).transitions,
                    n = void 0 === i || i;
                if (!this.transitionsActive) {
                    n && this.enableTransitions();
                    var o = ut({}, this.imageTransforms.flip),
                        s = function(t) {
                            var e = t.flip,
                                i = t.previousFlip,
                                n = t.rotate,
                                o = t.getAreaRestrictions,
                                s = t.coordinates,
                                r = t.visibleArea,
                                a = t.imageSize,
                                h = yt({}, s),
                                c = yt({}, r),
                                l = i.horizontal !== e.horizontal,
                                u = i.vertical !== e.vertical;
                            if (l || u) {
                                var d = he({
                                        left: a.width / 2,
                                        top: a.height / 2
                                    }, -n),
                                    m = he(Gt(h), -n),
                                    p = he({
                                        left: l ? d.left - (m.left - d.left) : m.left,
                                        top: u ? d.top - (m.top - d.top) : m.top
                                    }, n);
                                h = te(h, Zt(p, Gt(h))), m = he(Gt(c), -n), c = le(c = te(c, Zt(p = he({
                                    left: l ? d.left - (m.left - d.left) : m.left,
                                    top: u ? d.top - (m.top - d.top) : m.top
                                }, n), Gt(c))), o({
                                    visibleArea: c,
                                    type: "move"
                                }))
                            }
                            return {
                                coordinates: h,
                                visibleArea: c
                            }
                        }({
                            flip: {
                                horizontal: t ? !o.horizontal : o.horizontal,
                                vertical: e ? !o.vertical : o.vertical
                            },
                            previousFlip: o,
                            rotate: this.imageTransforms.rotate,
                            visibleArea: this.visibleArea,
                            coordinates: this.coordinates,
                            imageSize: this.imageSize,
                            positionRestrictions: this.positionRestrictions,
                            sizeRestrictions: this.sizeRestrictions,
                            getAreaRestrictions: this.getAreaRestrictions,
                            aspectRatio: this.getAspectRatio()
                        }),
                        r = s.visibleArea,
                        a = s.coordinates;
                    t && (this.appliedImageTransforms.flip.horizontal = !this.appliedImageTransforms.flip.horizontal), e && (this.appliedImageTransforms.flip.vertical = !this.appliedImageTransforms.flip.vertical), this.visibleArea = r, this.coordinates = a, this.onChange(), n && this.debouncedDisableTransitions()
                }
            },
            rotate: function(t) {
                var e = (arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}).transitions,
                    i = void 0 === e || e;
                if (!this.transitionsActive) {
                    i && this.enableTransitions();
                    var n = ut({}, this.imageSize);
                    this.appliedImageTransforms.rotate += t;
                    var o = function(t) {
                            var e = t.aspectRatio,
                                i = t.getAreaRestrictions,
                                n = t.coordinates,
                                o = t.visibleArea,
                                s = t.sizeRestrictions,
                                r = t.positionRestrictions,
                                a = t.imageSize,
                                h = t.previousImageSize,
                                c = t.angle,
                                l = yt({}, n),
                                u = yt({}, o),
                                d = he(Gt(yt({
                                    left: 0,
                                    top: 0
                                }, h)), c);
                            return (l = yt(yt({}, fe({
                                sizeRestrictions: s,
                                aspectRatio: e,
                                width: l.width,
                                height: l.height
                            })), he(Gt(l), c))).left -= d.left - a.width / 2 + l.width / 2, l.top -= d.top - a.height / 2 + l.height / 2, u = ee(u, ce(u, i({
                                visibleArea: u,
                                type: "resize"
                            }))), {
                                coordinates: l = le(l, r),
                                visibleArea: u = le(u = te(u, Zt(Gt(l), Gt(n))), i({
                                    visibleArea: u,
                                    type: "move"
                                }))
                            }
                        }({
                            visibleArea: this.visibleArea,
                            coordinates: this.coordinates,
                            previousImageSize: n,
                            imageSize: this.imageSize,
                            angle: t,
                            positionRestrictions: this.positionRestrictions,
                            sizeRestrictions: this.sizeRestrictions,
                            getAreaRestrictions: this.getAreaRestrictions,
                            aspectRatio: this.getAspectRatio()
                        }),
                        s = o.visibleArea,
                        r = o.coordinates,
                        a = this.processAutoZoom("rotateImage", s, r);
                    s = a.visibleArea, r = a.coordinates, this.visibleArea = s, this.coordinates = r, this.onChange(), i && this.debouncedDisableTransitions()
                }
            }
        },
        emits: ["change", "error", "ready"]
    },
    Je = {
        key: 0,
        ref: "canvas",
        style: {
            display: "none"
        }
    },
    ti = {
        key: 1,
        ref: "sourceCanvas",
        style: {
            display: "none"
        }
    };
Ke.render = function(t, e, n, o, s, h) {
    return i(), r("div", {
        ref: "cropper",
        class: h.classes.cropper
    }, [g("div", {
        ref: "stretcher",
        class: h.classes.stretcher
    }, null, 2), g("div", {
        class: h.classes.boundaries,
        style: h.boundariesStyle
    }, [(i(), r(m(n.backgroundWrapperComponent), {
        class: h.classes.cropperWrapper,
        "wheel-resize": h.settings.resizeImage.wheel,
        "touch-resize": h.settings.resizeImage.touch,
        "touch-move": h.settings.moveImage.touch,
        "mouse-move": h.settings.moveImage.mouse,
        onMove: h.onManipulateImage,
        onResize: h.onManipulateImage
    }, {
        default: a((function() {
            return [g("div", {
                class: h.classes.background,
                style: h.boundariesStyle
            }, null, 6), g("div", {
                class: h.classes.imageWrapper
            }, [g("img", {
                ref: "image",
                crossorigin: s.imageAttributes.crossOrigin,
                src: s.imageAttributes.src,
                class: h.classes.image,
                style: h.imageStyle,
                onMousedown: e[1] || (e[1] = M((function() {}), ["prevent"]))
            }, null, 46, ["crossorigin", "src"])], 2), g("div", {
                class: h.classes.foreground,
                style: h.boundariesStyle
            }, null, 6), z((i(), r(m(n.stencilComponent), R({
                ref: "stencil",
                image: h.image,
                coordinates: s.coordinates,
                "stencil-coordinates": h.stencilCoordinates,
                transitions: h.transitionsOptions
            }, n.stencilProps, {
                onResize: h.onResize,
                onResizeEnd: h.onResizeEnd,
                onMove: h.onMove,
                onMoveEnd: h.onMoveEnd
            }), null, 16, ["image", "coordinates", "stencil-coordinates", "transitions", "onResize", "onResizeEnd", "onMove", "onMoveEnd"])), [
                [A, s.imageLoaded]
            ]), n.canvas ? (i(), r("canvas", Je, null, 512)) : d("", !0), n.canvas ? (i(), r("canvas", ti, null, 512)) : d("", !0)]
        })),
        _: 1
    }, 8, ["class", "wheel-resize", "touch-resize", "touch-move", "mouse-move", "onMove", "onResize"]))], 6)], 2)
};
const ei = ["onClick"],
    ii = {
        class: "row"
    },
    ni = {
        class: "left"
    },
    oi = {
        key: 0,
        class: "absolute -top-4 left-2 px-2 py-0.5 bg-red-500 rounded-tl-lg rounded-tr-lg rounded-bl-sm rounded-br-lg inline-flex justify-center items-center gap-2.5 overflow-hidden text-white text-xs font-bold"
    },
    si = {
        class: "icon"
    },
    ri = ["src"],
    ai = {
        class: "text"
    },
    hi = {
        class: "right"
    },
    ci = ["checked", "disabled"],
    li = {
        key: 0,
        class: "description"
    },
    ui = {
        key: 1,
        class: "tip"
    },
    di = {
        key: 0,
        class: "divider"
    },
    mi = n({
        __name: "VideoSelectModel",
        props: {
            promptImages: {
                type: Array,
                default: []
            },
            modelsSelected: {
                type: String,
                default: ""
            },
            selectedAspectRatio: {
                type: String,
                default: ""
            },
            toggleModel: {
                type: Function,
                default: () => {}
            }
        },
        emits: ["updateLastUsedModels", "updateModelsSelected"],
        setup(n, {
            emit: o
        }) {
            const s = n,
                r = b("currentUser"),
                {
                    t: a
                } = f(),
                m = t => {
                    var e;
                    const i = C.find((e => e.name === t));
                    return !!i && (!("auto" !== s.selectedAspectRatio && i.permitted_aspect_ratios && !i.permitted_aspect_ratios.includes(s.selectedAspectRatio)) && !((null == (e = s.promptImages) ? void 0 : e.length) > 0 && !i.support_i2v))
                },
                g = t => t.description ? t.description(a) : "",
                v = t => t.tip ? t.tip(a) : "";
            return (o, s) => (i(!0), t(c, null, l(h(r) && h(r).gk_realtime_dogfood ? h(C).filter((t => !t.deprecated)) : h(C).filter((t => !t.hidden)), (o => {
                return i(), t(c, null, [e("div", {
                    class: u(["model", {
                        disabled: !m(o.name)
                    }]),
                    onClick: t => m(o.name) ? n.toggleModel(o.name) : null
                }, [e("div", ii, [e("div", ni, [o.new ? (i(), t("div", oi, " New ")) : d("", !0), e("div", si, [e("img", {
                    src: o.icon
                }, null, 8, ri)]), e("div", ai, p((s = o, "function" == typeof s.label ? s.label(a) : s.label)), 1)]), e("div", hi, [e("input", {
                    type: "radio",
                    name: "model",
                    checked: n.modelsSelected === o.name,
                    disabled: !m(o.name)
                }, null, 8, ci)])]), o.description ? (i(), t("div", li, p(g(o)), 1)) : d("", !0), o.tip ? (i(), t("div", ui, p(v(o)), 1)) : d("", !0)], 10, ei), o.divider ? (i(), t("div", di)) : d("", !0)], 64);
                var s
            })), 256))
        }
    }, [
        ["__scopeId", "data-v-ca03bd6c"]
    ]);
export {
    H as D, ct as I, W as R, Y as U, mi as V, Q as a, Z as b, V as c, _ as d, Ke as f
};