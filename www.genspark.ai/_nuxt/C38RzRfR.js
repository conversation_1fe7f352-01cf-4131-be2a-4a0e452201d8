import {
    r as e,
    c as n,
    b9 as t,
    K as o,
    J as l,
    i,
    X as r,
    av as a,
    dn as s,
    h as d,
    c8 as u,
    cb as c,
    aR as p,
    U as h,
    T as v,
    v as f,
    a3 as b,
    al as g,
    aO as m,
    ak as w,
    b6 as y,
    Y as x,
    Z as C,
    dW as F,
    ap as S,
    aq as R,
    da as z,
    a4 as O,
    am as T,
    aP as k,
    an as P,
    aV as M,
    dX as I,
    F as B,
    dY as $,
    ag as A,
    H as L,
    I as E,
    dZ as j,
    dp as D
} from "./Cf0SOiw0.js";
import {
    f as N,
    m as W
} from "./BLWq1oPC.js";
import {
    c as _,
    d as V,
    b as H,
    i as K,
    e as U,
    N as q,
    u as G,
    B as Y,
    V as X,
    a as Z
} from "./CW991W2w.js";
import {
    S as J,
    W as Q
} from "./WZsIN7xM.js";
import {
    a as ee
} from "./By6xEfKc.js";
import {
    N as ne
} from "./BrPr1fm5.js";
import {
    d as te,
    p as oe,
    a as le
} from "./pB_XRIgB.js";
import {
    V as ie,
    r as re,
    g as ae
} from "./Jr9eiJio.js";
import {
    c as se
} from "./DpMvtoun.js";
import {
    r as de,
    a as ue,
    c as ce
} from "./B7VeW_-d.js";
import {
    u as pe
} from "./DGJMLFjI.js";
import {
    a as he,
    h as ve,
    c as fe
} from "./BihyrXkC.js";
import {
    u as be
} from "./BuhfKjCJ.js";
import {
    u as ge
} from "./BGK9k_mT.js";
import {
    u as me
} from "./MpDLC7up.js";

function we(e) {
    return e & -e
}
class ye {
    constructor(e, n) {
        this.l = e, this.min = n;
        const t = new Array(e + 1);
        for (let o = 0; o < e + 1; ++o) t[o] = 0;
        this.ft = t
    }
    add(e, n) {
        if (0 === n) return;
        const {
            l: t,
            ft: o
        } = this;
        for (e += 1; e <= t;) o[e] += n, e += we(e)
    }
    get(e) {
        return this.sum(e + 1) - this.sum(e)
    }
    sum(e) {
        if (void 0 === e && (e = this.l), e <= 0) return 0;
        const {
            ft: n,
            min: t,
            l: o
        } = this;
        if (e > o) throw new Error("[FinweckTree.sum]: `i` is larger than length.");
        let l = e * t;
        for (; e > 0;) l += n[e], e -= we(e);
        return l
    }
    getBound(e) {
        let n = 0,
            t = this.l;
        for (; t > n;) {
            const o = Math.floor((n + t) / 2),
                l = this.sum(o);
            if (l > e) t = o;
            else {
                if (!(l < e)) return o;
                if (n === o) return this.sum(n + 1) <= e ? n + 1 : o;
                n = o
            }
        }
        return n
    }
}
let xe, Ce;

function Fe() {
    return "undefined" == typeof document ? 1 : (void 0 === Ce && (Ce = "chrome" in window ? window.devicePixelRatio : 1), Ce)
}
const Se = "VVirtualListXScroll";
const Re = l({
        name: "VirtualListRow",
        props: {
            index: {
                type: Number,
                required: !0
            },
            item: {
                type: Object,
                required: !0
            }
        },
        setup() {
            const {
                startIndexRef: e,
                endIndexRef: n,
                columnsRef: t,
                getLeft: o,
                renderColRef: l,
                renderItemWithColsRef: r
            } = i(Se);
            return {
                startIndex: e,
                endIndex: n,
                columns: t,
                renderCol: l,
                renderItemWithCols: r,
                getLeft: o
            }
        },
        render() {
            const {
                startIndex: e,
                endIndex: n,
                columns: t,
                renderCol: o,
                renderItemWithCols: l,
                getLeft: i,
                item: r
            } = this;
            if (null != l) return l({
                itemIndex: this.index,
                startColIndex: e,
                endColIndex: n,
                allColumns: t,
                item: r,
                getLeft: i
            });
            if (null != o) {
                const l = [];
                for (let a = e; a <= n; ++a) {
                    const e = t[a];
                    l.push(o({
                        column: e,
                        left: i(a),
                        item: r
                    }))
                }
                return l
            }
            return null
        }
    }),
    ze = _(".v-vl", {
        maxHeight: "inherit",
        height: "100%",
        overflow: "auto",
        minWidth: "1px"
    }, [_("&:not(.v-vl--show-scrollbar)", {
        scrollbarWidth: "none"
    }, [_("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb", {
        width: 0,
        height: 0,
        display: "none"
    })])]),
    Oe = l({
        name: "VirtualList",
        inheritAttrs: !1,
        props: {
            showScrollbar: {
                type: Boolean,
                default: !0
            },
            columns: {
                type: Array,
                default: () => []
            },
            renderCol: Function,
            renderItemWithCols: Function,
            items: {
                type: Array,
                default: () => []
            },
            itemSize: {
                type: Number,
                required: !0
            },
            itemResizable: Boolean,
            itemsStyle: [String, Object],
            visibleItemsTag: {
                type: [String, Object],
                default: "div"
            },
            visibleItemsProps: Object,
            ignoreItemResize: Boolean,
            onScroll: Function,
            onWheel: Function,
            onResize: Function,
            defaultScrollKey: [Number, String],
            defaultScrollIndex: Number,
            keyField: {
                type: String,
                default: "key"
            },
            paddingTop: {
                type: [Number, String],
                default: 0
            },
            paddingBottom: {
                type: [Number, String],
                default: 0
            }
        },
        setup(l) {
            const i = s();
            ze.mount({
                id: "vueuc/virtual-list",
                head: !0,
                anchorMetaName: V,
                ssr: i
            }), d((() => {
                const {
                    defaultScrollIndex: e,
                    defaultScrollKey: n
                } = l;
                null != e ? R({
                    index: e
                }) : null != n && R({
                    key: n
                })
            }));
            let r = !1,
                a = !1;
            u((() => {
                r = !1, a ? R({
                    top: C.value,
                    left: f.value
                }) : a = !0
            })), c((() => {
                r = !0, a || (a = !0)
            }));
            const h = t((() => {
                    if (null == l.renderCol && null == l.renderItemWithCols) return;
                    if (0 === l.columns.length) return;
                    let e = 0;
                    return l.columns.forEach((n => {
                        e += n.width
                    })), e
                })),
                v = n((() => {
                    const e = new Map,
                        {
                            keyField: n
                        } = l;
                    return l.items.forEach(((t, o) => {
                        e.set(t[n], o)
                    })), e
                })),
                {
                    scrollLeftRef: f,
                    listWidthRef: b
                } = function({
                    columnsRef: l,
                    renderColRef: i,
                    renderItemWithColsRef: r
                }) {
                    const a = e(0),
                        s = e(0),
                        d = n((() => {
                            const e = l.value;
                            if (0 === e.length) return null;
                            const n = new ye(e.length, 0);
                            return e.forEach(((e, t) => {
                                n.add(t, e.width)
                            })), n
                        })),
                        u = t((() => {
                            const e = d.value;
                            return null !== e ? Math.max(e.getBound(s.value) - 1, 0) : 0
                        })),
                        c = t((() => {
                            const e = d.value;
                            return null !== e ? Math.min(e.getBound(s.value + a.value) + 1, l.value.length - 1) : 0
                        }));
                    return o(Se, {
                        startIndexRef: u,
                        endIndexRef: c,
                        columnsRef: l,
                        renderColRef: i,
                        renderItemWithColsRef: r,
                        getLeft: e => {
                            const n = d.value;
                            return null !== n ? n.sum(e) : 0
                        }
                    }), {
                        listWidthRef: a,
                        scrollLeftRef: s
                    }
                }({
                    columnsRef: p(l, "columns"),
                    renderColRef: p(l, "renderCol"),
                    renderItemWithColsRef: p(l, "renderItemWithCols")
                }),
                g = e(null),
                m = e(void 0),
                w = new Map,
                y = n((() => {
                    const {
                        items: e,
                        itemSize: n,
                        keyField: t
                    } = l, o = new ye(e.length, n);
                    return e.forEach(((e, n) => {
                        const l = e[t],
                            i = w.get(l);
                        void 0 !== i && o.add(n, i)
                    })), o
                })),
                x = e(0),
                C = e(0),
                F = t((() => Math.max(y.value.getBound(C.value - te(l.paddingTop)) - 1, 0))),
                S = n((() => {
                    const {
                        value: e
                    } = m;
                    if (void 0 === e) return [];
                    const {
                        items: n,
                        itemSize: t
                    } = l, o = F.value, i = Math.min(o + Math.ceil(e / t + 1), n.length - 1), r = [];
                    for (let l = o; l <= i; ++l) r.push(n[l]);
                    return r
                })),
                R = (e, n) => {
                    if ("number" == typeof e) return void k(e, n, "auto");
                    const {
                        left: t,
                        top: o,
                        index: l,
                        key: i,
                        position: r,
                        behavior: a,
                        debounce: s = !0
                    } = e;
                    if (void 0 !== t || void 0 !== o) k(t, o, a);
                    else if (void 0 !== l) T(l, a, s);
                    else if (void 0 !== i) {
                        const e = v.value.get(i);
                        void 0 !== e && T(e, a, s)
                    } else "bottom" === r ? k(0, Number.MAX_SAFE_INTEGER, a) : "top" === r && k(0, 0, a)
                };
            let z, O = null;

            function T(e, n, t) {
                const {
                    value: o
                } = y, i = o.sum(e) + te(l.paddingTop);
                if (t) {
                    z = e, null !== O && window.clearTimeout(O), O = window.setTimeout((() => {
                        z = void 0, O = null
                    }), 16);
                    const {
                        scrollTop: t,
                        offsetHeight: l
                    } = g.value;
                    if (i > t) {
                        const r = o.get(e);
                        i + r <= t + l || g.value.scrollTo({
                            left: 0,
                            top: i + r - l,
                            behavior: n
                        })
                    } else g.value.scrollTo({
                        left: 0,
                        top: i,
                        behavior: n
                    })
                } else g.value.scrollTo({
                    left: 0,
                    top: i,
                    behavior: n
                })
            }

            function k(e, n, t) {
                g.value.scrollTo({
                    left: e,
                    top: n,
                    behavior: t
                })
            }
            const P = !("undefined" != typeof document && (void 0 === xe && (xe = "matchMedia" in window && window.matchMedia("(pointer:coarse)").matches), xe));
            let M = !1;

            function I() {
                const {
                    value: e
                } = g;
                null != e && (C.value = e.scrollTop, f.value = e.scrollLeft)
            }

            function B(e) {
                let n = e;
                for (; null !== n;) {
                    if ("none" === n.style.display) return !0;
                    n = n.parentElement
                }
                return !1
            }
            return {
                listHeight: m,
                listStyle: {
                    overflow: "auto"
                },
                keyToIndex: v,
                itemsStyle: n((() => {
                    const {
                        itemResizable: e
                    } = l, n = oe(y.value.sum());
                    return x.value, [l.itemsStyle, {
                        boxSizing: "content-box",
                        width: oe(h.value),
                        height: e ? "" : n,
                        minHeight: e ? n : "",
                        paddingTop: oe(l.paddingTop),
                        paddingBottom: oe(l.paddingBottom)
                    }]
                })),
                visibleItemsStyle: n((() => (x.value, {
                    transform: `translateY(${oe(y.value.sum(F.value))})`
                }))),
                viewportItems: S,
                listElRef: g,
                itemsElRef: e(null),
                scrollTo: R,
                handleListResize: function(e) {
                    if (r) return;
                    if (B(e.target)) return;
                    if (null == l.renderCol && null == l.renderItemWithCols) {
                        if (e.contentRect.height === m.value) return
                    } else if (e.contentRect.height === m.value && e.contentRect.width === b.value) return;
                    m.value = e.contentRect.height, b.value = e.contentRect.width;
                    const {
                        onResize: n
                    } = l;
                    void 0 !== n && n(e)
                },
                handleListScroll: function(e) {
                    var n;
                    null === (n = l.onScroll) || void 0 === n || n.call(l, e), P && M || I()
                },
                handleListWheel: function(e) {
                    var n;
                    if (null === (n = l.onWheel) || void 0 === n || n.call(l, e), P) {
                        const n = g.value;
                        if (null != n) {
                            if (0 === e.deltaX) {
                                if (0 === n.scrollTop && e.deltaY <= 0) return;
                                if (n.scrollTop + n.offsetHeight >= n.scrollHeight && e.deltaY >= 0) return
                            }
                            e.preventDefault(), n.scrollTop += e.deltaY / Fe(), n.scrollLeft += e.deltaX / Fe(), I(), M = !0, H((() => {
                                M = !1
                            }))
                        }
                    }
                },
                handleItemResize: function(e, n) {
                    var t, o, i;
                    if (r) return;
                    if (l.ignoreItemResize) return;
                    if (B(n.target)) return;
                    const {
                        value: a
                    } = y, s = v.value.get(e), d = a.get(s), u = null !== (i = null === (o = null === (t = n.borderBoxSize) || void 0 === t ? void 0 : t[0]) || void 0 === o ? void 0 : o.blockSize) && void 0 !== i ? i : n.contentRect.height;
                    if (u === d) return;
                    0 === u - l.itemSize ? w.delete(e) : w.set(e, u - l.itemSize);
                    const c = u - d;
                    if (0 === c) return;
                    a.add(s, c);
                    const p = g.value;
                    if (null != p) {
                        if (void 0 === z) {
                            const e = a.sum(s);
                            p.scrollTop > e && p.scrollBy(0, c)
                        } else if (s < z) p.scrollBy(0, c);
                        else if (s === z) {
                            u + a.sum(s) > p.scrollTop + p.offsetHeight && p.scrollBy(0, c)
                        }
                        I()
                    }
                    x.value++
                }
            }
        },
        render() {
            const {
                itemResizable: e,
                keyField: n,
                keyToIndex: t,
                visibleItemsTag: o
            } = this;
            return r(ie, {
                onResize: this.handleListResize
            }, {
                default: () => {
                    var l, i;
                    return r("div", a(this.$attrs, {
                        class: ["v-vl", this.showScrollbar && "v-vl--show-scrollbar"],
                        onScroll: this.handleListScroll,
                        onWheel: this.handleListWheel,
                        ref: "listElRef"
                    }), [0 !== this.items.length ? r("div", {
                        ref: "itemsElRef",
                        class: "v-vl-items",
                        style: this.itemsStyle
                    }, [r(o, Object.assign({
                        class: "v-vl-visible-items",
                        style: this.visibleItemsStyle
                    }, this.visibleItemsProps), {
                        default: () => {
                            const {
                                renderCol: o,
                                renderItemWithCols: l
                            } = this;
                            return this.viewportItems.map((i => {
                                const a = i[n],
                                    s = t.get(a),
                                    d = null != o ? r(Re, {
                                        index: s,
                                        item: i
                                    }) : void 0,
                                    u = null != l ? r(Re, {
                                        index: s,
                                        item: i
                                    }) : void 0,
                                    c = this.$slots.default({
                                        item: i,
                                        renderedCols: d,
                                        renderedItemWithCols: u,
                                        index: s
                                    })[0];
                                return e ? r(ie, {
                                    key: a,
                                    onResize: e => this.handleItemResize(a, e)
                                }, {
                                    default: () => c
                                }) : (c.key = a, c)
                            }))
                        }
                    })]) : null === (i = (l = this.$slots).empty) || void 0 === i ? void 0 : i.call(l)])
                }
            })
        }
    }),
    Te = "v-hidden",
    ke = _("[v-hidden]", {
        display: "none!important"
    }),
    Pe = l({
        name: "Overflow",
        props: {
            getCounter: Function,
            getTail: Function,
            updateCounter: Function,
            onUpdateCount: Function,
            onUpdateOverflow: Function
        },
        setup(n, {
            slots: t
        }) {
            const o = e(null),
                l = e(null);

            function i(e) {
                const {
                    value: i
                } = o, {
                    getCounter: r,
                    getTail: a
                } = n;
                let s;
                if (s = void 0 !== r ? r() : l.value, !i || !s) return;
                s.hasAttribute(Te) && s.removeAttribute(Te);
                const {
                    children: d
                } = i;
                if (e.showAllItemsBeforeCalculate)
                    for (const n of d) n.hasAttribute(Te) && n.removeAttribute(Te);
                const u = i.offsetWidth,
                    c = [],
                    p = t.tail ? null == a ? void 0 : a() : null;
                let h = p ? p.offsetWidth : 0,
                    v = !1;
                const f = i.children.length - (t.tail ? 1 : 0);
                for (let t = 0; t < f - 1; ++t) {
                    if (t < 0) continue;
                    const e = d[t];
                    if (v) {
                        e.hasAttribute(Te) || e.setAttribute(Te, "");
                        continue
                    }
                    e.hasAttribute(Te) && e.removeAttribute(Te);
                    const o = e.offsetWidth;
                    if (h += o, c[t] = o, h > u) {
                        const {
                            updateCounter: e
                        } = n;
                        for (let o = t; o >= 0; --o) {
                            const l = f - 1 - o;
                            void 0 !== e ? e(l) : s.textContent = `${l}`;
                            const i = s.offsetWidth;
                            if (h -= c[o], h + i <= u || 0 === o) {
                                v = !0, t = o - 1, p && (-1 === t ? (p.style.maxWidth = u - i + "px", p.style.boxSizing = "border-box") : p.style.maxWidth = "");
                                const {
                                    onUpdateCount: e
                                } = n;
                                e && e(l);
                                break
                            }
                        }
                    }
                }
                const {
                    onUpdateOverflow: b
                } = n;
                v ? void 0 !== b && b(!0) : (void 0 !== b && b(!1), s.setAttribute(Te, ""))
            }
            const r = s();
            return ke.mount({
                id: "vueuc/overflow",
                head: !0,
                anchorMetaName: V,
                ssr: r
            }), d((() => i({
                showAllItemsBeforeCalculate: !1
            }))), {
                selfRef: o,
                counterRef: l,
                sync: i
            }
        },
        render() {
            const {
                $slots: e
            } = this;
            return h((() => this.sync({
                showAllItemsBeforeCalculate: !1
            }))), r("div", {
                class: "v-overflow",
                ref: "selfRef"
            }, [v(e, "default"), e.counter ? e.counter() : r("span", {
                style: {
                    display: "inline-block"
                },
                ref: "counterRef"
            }), e.tail ? e.tail() : null])
        }
    });

function Me(e, n) {
    n && (d((() => {
        const {
            value: t
        } = e;
        t && re.registerHandler(t, n)
    })), f(e, ((e, n) => {
        n && re.unregisterHandler(n)
    }), {
        deep: !1
    }), b((() => {
        const {
            value: n
        } = e;
        n && re.unregisterHandler(n)
    })))
}

function Ie(e) {
    switch (typeof e) {
        case "string":
            return e || void 0;
        case "number":
            return String(e);
        default:
            return
    }
}

function Be(e) {
    const n = e.filter((e => void 0 !== e));
    if (0 !== n.length) return 1 === n.length ? n[0] : n => {
        e.forEach((e => {
            e && e(n)
        }))
    }
}
const $e = l({
        name: "Checkmark",
        render: () => r("svg", {
            xmlns: "http://www.w3.org/2000/svg",
            viewBox: "0 0 16 16"
        }, r("g", {
            fill: "none"
        }, r("path", {
            d: "M14.046 3.486a.75.75 0 0 1-.032 1.06l-7.93 7.474a.85.85 0 0 1-1.188-.022l-2.68-2.72a.75.75 0 1 1 1.068-1.053l2.234 2.267l7.468-7.038a.75.75 0 0 1 1.06.032z",
            fill: "currentColor"
        })))
    }),
    Ae = l({
        name: "Empty",
        render: () => r("svg", {
            viewBox: "0 0 28 28",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg"
        }, r("path", {
            d: "M26 7.5C26 11.0899 23.0899 14 19.5 14C15.9101 14 13 11.0899 13 7.5C13 3.91015 15.9101 1 19.5 1C23.0899 1 26 3.91015 26 7.5ZM16.8536 4.14645C16.6583 3.95118 16.3417 3.95118 16.1464 4.14645C15.9512 4.34171 15.9512 4.65829 16.1464 4.85355L18.7929 7.5L16.1464 10.1464C15.9512 10.3417 15.9512 10.6583 16.1464 10.8536C16.3417 11.0488 16.6583 11.0488 16.8536 10.8536L19.5 8.20711L22.1464 10.8536C22.3417 11.0488 22.6583 11.0488 22.8536 10.8536C23.0488 10.6583 23.0488 10.3417 22.8536 10.1464L20.2071 7.5L22.8536 4.85355C23.0488 4.65829 23.0488 4.34171 22.8536 4.14645C22.6583 3.95118 22.3417 3.95118 22.1464 4.14645L19.5 6.79289L16.8536 4.14645Z",
            fill: "currentColor"
        }), r("path", {
            d: "M25 22.75V12.5991C24.5572 13.0765 24.053 13.4961 23.5 13.8454V16H17.5L17.3982 16.0068C17.0322 16.0565 16.75 16.3703 16.75 16.75C16.75 18.2688 15.5188 19.5 14 19.5C12.4812 19.5 11.25 18.2688 11.25 16.75L11.2432 16.6482C11.1935 16.2822 10.8797 16 10.5 16H4.5V7.25C4.5 6.2835 5.2835 5.5 6.25 5.5H12.2696C12.4146 4.97463 12.6153 4.47237 12.865 4H6.25C4.45507 4 3 5.45507 3 7.25V22.75C3 24.5449 4.45507 26 6.25 26H21.75C23.5449 26 25 24.5449 25 22.75ZM4.5 22.75V17.5H9.81597L9.85751 17.7041C10.2905 19.5919 11.9808 21 14 21L14.215 20.9947C16.2095 20.8953 17.842 19.4209 18.184 17.5H23.5V22.75C23.5 23.7165 22.7165 24.5 21.75 24.5H6.25C5.2835 24.5 4.5 23.7165 4.5 22.75Z",
            fill: "currentColor"
        }))
    }),
    Le = l({
        props: {
            onFocus: Function,
            onBlur: Function
        },
        setup: e => () => r("div", {
            style: "width: 0; height: 0",
            tabindex: 0,
            onFocus: e.onFocus,
            onBlur: e.onBlur
        })
    }),
    Ee = g("empty", "\n display: flex;\n flex-direction: column;\n align-items: center;\n font-size: var(--n-font-size);\n", [m("icon", "\n width: var(--n-icon-size);\n height: var(--n-icon-size);\n font-size: var(--n-icon-size);\n line-height: var(--n-icon-size);\n color: var(--n-icon-color);\n transition:\n color .3s var(--n-bezier);\n ", [w("+", [m("description", "\n margin-top: 8px;\n ")])]), m("description", "\n transition: color .3s var(--n-bezier);\n color: var(--n-text-color);\n "), m("extra", "\n text-align: center;\n transition: color .3s var(--n-bezier);\n margin-top: 12px;\n color: var(--n-extra-text-color);\n ")]),
    je = l({
        name: "Empty",
        props: Object.assign(Object.assign({}, C.props), {
            description: String,
            showDescription: {
                type: Boolean,
                default: !0
            },
            showIcon: {
                type: Boolean,
                default: !0
            },
            size: {
                type: String,
                default: "medium"
            },
            renderIcon: Function
        }),
        slots: Object,
        setup(e) {
            const {
                mergedClsPrefixRef: t,
                inlineThemeDisabled: o,
                mergedComponentPropsRef: l
            } = x(e), i = C("Empty", "-empty", Ee, F, e, t), {
                localeRef: a
            } = pe("Empty"), s = n((() => {
                var n, t, o;
                return null !== (n = e.description) && void 0 !== n ? n : null === (o = null === (t = null == l ? void 0 : l.value) || void 0 === t ? void 0 : t.Empty) || void 0 === o ? void 0 : o.description
            })), d = n((() => {
                var e, n;
                return (null === (n = null === (e = null == l ? void 0 : l.value) || void 0 === e ? void 0 : e.Empty) || void 0 === n ? void 0 : n.renderIcon) || (() => r(Ae, null))
            })), u = n((() => {
                const {
                    size: n
                } = e, {
                    common: {
                        cubicBezierEaseInOut: t
                    },
                    self: {
                        [S("iconSize", n)]: o,
                        [S("fontSize", n)]: l,
                        textColor: r,
                        iconColor: a,
                        extraTextColor: s
                    }
                } = i.value;
                return {
                    "--n-icon-size": o,
                    "--n-font-size": l,
                    "--n-bezier": t,
                    "--n-text-color": r,
                    "--n-icon-color": a,
                    "--n-extra-text-color": s
                }
            })), c = o ? R("empty", n((() => {
                let n = "";
                const {
                    size: t
                } = e;
                return n += t[0], n
            })), u, e) : void 0;
            return {
                mergedClsPrefix: t,
                mergedRenderIcon: d,
                localizedDescription: n((() => s.value || a.value.description)),
                cssVars: o ? void 0 : u,
                themeClass: null == c ? void 0 : c.themeClass,
                onRender: null == c ? void 0 : c.onRender
            }
        },
        render() {
            const {
                $slots: e,
                mergedClsPrefix: n,
                onRender: t
            } = this;
            return null == t || t(), r("div", {
                class: [`${n}-empty`, this.themeClass],
                style: this.cssVars
            }, this.showIcon ? r("div", {
                class: `${n}-empty__icon`
            }, e.icon ? e.icon() : r(y, {
                clsPrefix: n
            }, {
                default: this.mergedRenderIcon
            })) : null, this.showDescription ? r("div", {
                class: `${n}-empty__description`
            }, e.default ? e.default() : this.localizedDescription) : null, e.extra ? r("div", {
                class: `${n}-empty__extra`
            }, e.extra()) : null)
        }
    }),
    De = l({
        name: "NBaseSelectGroupHeader",
        props: {
            clsPrefix: {
                type: String,
                required: !0
            },
            tmNode: {
                type: Object,
                required: !0
            }
        },
        setup() {
            const {
                renderLabelRef: e,
                renderOptionRef: n,
                labelFieldRef: t,
                nodePropsRef: o
            } = i(K);
            return {
                labelField: t,
                nodeProps: o,
                renderLabel: e,
                renderOption: n
            }
        },
        render() {
            const {
                clsPrefix: e,
                renderLabel: n,
                renderOption: t,
                nodeProps: o,
                tmNode: {
                    rawNode: l
                }
            } = this, i = null == o ? void 0 : o(l), a = n ? n(l, !1) : z(l[this.labelField], l, !1), s = r("div", Object.assign({}, i, {
                class: [`${e}-base-select-group-header`, null == i ? void 0 : i.class]
            }), a);
            return l.render ? l.render({
                node: s,
                option: l
            }) : t ? t({
                node: s,
                option: l,
                selected: !1
            }) : s
        }
    });
const Ne = l({
        name: "NBaseSelectOption",
        props: {
            clsPrefix: {
                type: String,
                required: !0
            },
            tmNode: {
                type: Object,
                required: !0
            }
        },
        setup(e) {
            const {
                valueRef: n,
                pendingTmNodeRef: o,
                multipleRef: l,
                valueSetRef: r,
                renderLabelRef: a,
                renderOptionRef: s,
                labelFieldRef: d,
                valueFieldRef: u,
                showCheckmarkRef: c,
                nodePropsRef: p,
                handleOptionClick: h,
                handleOptionMouseEnter: v
            } = i(K), f = t((() => {
                const {
                    value: n
                } = o;
                return !!n && e.tmNode.key === n.key
            }));
            return {
                multiple: l,
                isGrouped: t((() => {
                    const {
                        tmNode: n
                    } = e, {
                        parent: t
                    } = n;
                    return t && "group" === t.rawNode.type
                })),
                showCheckmark: c,
                nodeProps: p,
                isPending: f,
                isSelected: t((() => {
                    const {
                        value: t
                    } = n, {
                        value: o
                    } = l;
                    if (null === t) return !1;
                    const i = e.tmNode.rawNode[u.value];
                    if (o) {
                        const {
                            value: e
                        } = r;
                        return e.has(i)
                    }
                    return t === i
                })),
                labelField: d,
                renderLabel: a,
                renderOption: s,
                handleMouseMove: function(n) {
                    const {
                        tmNode: t
                    } = e, {
                        value: o
                    } = f;
                    t.disabled || o || v(n, t)
                },
                handleMouseEnter: function(n) {
                    const {
                        tmNode: t
                    } = e;
                    t.disabled || v(n, t)
                },
                handleClick: function(n) {
                    const {
                        tmNode: t
                    } = e;
                    t.disabled || h(n, t)
                }
            }
        },
        render() {
            const {
                clsPrefix: e,
                tmNode: {
                    rawNode: n
                },
                isSelected: t,
                isPending: o,
                isGrouped: l,
                showCheckmark: i,
                nodeProps: a,
                renderOption: s,
                renderLabel: d,
                handleClick: u,
                handleMouseEnter: c,
                handleMouseMove: p
            } = this, h = function(e, n) {
                return r(O, {
                    name: "fade-in-scale-up-transition"
                }, {
                    default: () => e ? r(y, {
                        clsPrefix: n,
                        class: `${n}-base-select-option__check`
                    }, {
                        default: () => r($e)
                    }) : null
                })
            }(t, e), v = d ? [d(n, t), i && h] : [z(n[this.labelField], n, t), i && h], f = null == a ? void 0 : a(n), b = r("div", Object.assign({}, f, {
                class: [`${e}-base-select-option`, n.class, null == f ? void 0 : f.class, {
                    [`${e}-base-select-option--disabled`]: n.disabled,
                    [`${e}-base-select-option--selected`]: t,
                    [`${e}-base-select-option--grouped`]: l,
                    [`${e}-base-select-option--pending`]: o,
                    [`${e}-base-select-option--show-checkmark`]: i
                }],
                style: [(null == f ? void 0 : f.style) || "", n.style || ""],
                onClick: Be([u, null == f ? void 0 : f.onClick]),
                onMouseenter: Be([c, null == f ? void 0 : f.onMouseenter]),
                onMousemove: Be([p, null == f ? void 0 : f.onMousemove])
            }), r("div", {
                class: `${e}-base-select-option__content`
            }, v));
            return n.render ? n.render({
                node: b,
                option: n,
                selected: t
            }) : s ? s({
                node: b,
                option: n,
                selected: t
            }) : b
        }
    }),
    We = g("base-select-menu", "\n line-height: 1.5;\n outline: none;\n z-index: 0;\n position: relative;\n border-radius: var(--n-border-radius);\n transition:\n background-color .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier);\n background-color: var(--n-color);\n", [g("scrollbar", "\n max-height: var(--n-height);\n "), g("virtual-list", "\n max-height: var(--n-height);\n "), g("base-select-option", "\n min-height: var(--n-option-height);\n font-size: var(--n-option-font-size);\n display: flex;\n align-items: center;\n ", [m("content", "\n z-index: 1;\n white-space: nowrap;\n text-overflow: ellipsis;\n overflow: hidden;\n ")]), g("base-select-group-header", "\n min-height: var(--n-option-height);\n font-size: .93em;\n display: flex;\n align-items: center;\n "), g("base-select-menu-option-wrapper", "\n position: relative;\n width: 100%;\n "), m("loading, empty", "\n display: flex;\n padding: 12px 32px;\n flex: 1;\n justify-content: center;\n "), m("loading", "\n color: var(--n-loading-color);\n font-size: var(--n-loading-size);\n "), m("header", "\n padding: 8px var(--n-option-padding-left);\n font-size: var(--n-option-font-size);\n transition: \n color .3s var(--n-bezier),\n border-color .3s var(--n-bezier);\n border-bottom: 1px solid var(--n-action-divider-color);\n color: var(--n-action-text-color);\n "), m("action", "\n padding: 8px var(--n-option-padding-left);\n font-size: var(--n-option-font-size);\n transition: \n color .3s var(--n-bezier),\n border-color .3s var(--n-bezier);\n border-top: 1px solid var(--n-action-divider-color);\n color: var(--n-action-text-color);\n "), g("base-select-group-header", "\n position: relative;\n cursor: default;\n padding: var(--n-option-padding);\n color: var(--n-group-header-text-color);\n "), g("base-select-option", "\n cursor: pointer;\n position: relative;\n padding: var(--n-option-padding);\n transition:\n color .3s var(--n-bezier),\n opacity .3s var(--n-bezier);\n box-sizing: border-box;\n color: var(--n-option-text-color);\n opacity: 1;\n ", [T("show-checkmark", "\n padding-right: calc(var(--n-option-padding-right) + 20px);\n "), w("&::before", '\n content: "";\n position: absolute;\n left: 4px;\n right: 4px;\n top: 0;\n bottom: 0;\n border-radius: var(--n-border-radius);\n transition: background-color .3s var(--n-bezier);\n '), w("&:active", "\n color: var(--n-option-text-color-pressed);\n "), T("grouped", "\n padding-left: calc(var(--n-option-padding-left) * 1.5);\n "), T("pending", [w("&::before", "\n background-color: var(--n-option-color-pending);\n ")]), T("selected", "\n color: var(--n-option-text-color-active);\n ", [w("&::before", "\n background-color: var(--n-option-color-active);\n "), T("pending", [w("&::before", "\n background-color: var(--n-option-color-active-pending);\n ")])]), T("disabled", "\n cursor: not-allowed;\n ", [k("selected", "\n color: var(--n-option-text-color-disabled);\n "), T("selected", "\n opacity: var(--n-option-opacity-disabled);\n ")]), m("check", "\n font-size: 16px;\n position: absolute;\n right: calc(var(--n-option-padding-right) - 4px);\n top: calc(50% - 7px);\n color: var(--n-option-check-color);\n transition: color .3s var(--n-bezier);\n ", [N({
        enterScale: "0.5"
    })])])]),
    _e = l({
        name: "InternalSelectMenu",
        props: Object.assign(Object.assign({}, C.props), {
            clsPrefix: {
                type: String,
                required: !0
            },
            scrollable: {
                type: Boolean,
                default: !0
            },
            treeMate: {
                type: Object,
                required: !0
            },
            multiple: Boolean,
            size: {
                type: String,
                default: "medium"
            },
            value: {
                type: [String, Number, Array],
                default: null
            },
            autoPending: Boolean,
            virtualScroll: {
                type: Boolean,
                default: !0
            },
            show: {
                type: Boolean,
                default: !0
            },
            labelField: {
                type: String,
                default: "label"
            },
            valueField: {
                type: String,
                default: "value"
            },
            loading: Boolean,
            focusable: Boolean,
            renderLabel: Function,
            renderOption: Function,
            nodeProps: Function,
            showCheckmark: {
                type: Boolean,
                default: !0
            },
            onMousedown: Function,
            onScroll: Function,
            onFocus: Function,
            onBlur: Function,
            onKeyup: Function,
            onKeydown: Function,
            onTabOut: Function,
            onMouseenter: Function,
            onMouseleave: Function,
            onResize: Function,
            resetMenuOnOptionsChange: {
                type: Boolean,
                default: !0
            },
            inlineThemeDisabled: Boolean,
            onToggle: Function
        }),
        setup(t) {
            const {
                mergedClsPrefixRef: l,
                mergedRtlRef: i
            } = x(t), r = M("InternalSelectMenu", i, l), a = C("InternalSelectMenu", "-internal-select-menu", We, I, t, p(t, "clsPrefix")), s = e(null), u = e(null), c = e(null), v = n((() => t.treeMate.getFlattenedNodes())), g = n((() => he(v.value))), m = e(null);

            function w() {
                const {
                    value: e
                } = m;
                e && !t.treeMate.getNode(e.key) && (m.value = null)
            }
            let y;
            f((() => t.show), (e => {
                e ? y = f((() => t.treeMate), (() => {
                    t.resetMenuOnOptionsChange ? (t.autoPending ? function() {
                        const {
                            treeMate: e
                        } = t;
                        let n = null;
                        const {
                            value: o
                        } = t;
                        null === o ? n = e.getFirstAvailableNode() : (n = t.multiple ? e.getNode((o || [])[(o || []).length - 1]) : e.getNode(o), n && !n.disabled || (n = e.getFirstAvailableNode())), P(n || null)
                    }() : w(), h(B)) : w()
                }), {
                    immediate: !0
                }) : null == y || y()
            }), {
                immediate: !0
            }), b((() => {
                null == y || y()
            }));
            const F = n((() => te(a.value.self[S("optionHeight", t.size)]))),
                z = n((() => le(a.value.self[S("padding", t.size)]))),
                O = n((() => t.multiple && Array.isArray(t.value) ? new Set(t.value) : new Set)),
                T = n((() => {
                    const e = v.value;
                    return e && 0 === e.length
                }));

            function k(e) {
                const {
                    onScroll: n
                } = t;
                n && n(e)
            }

            function P(e, n = !1) {
                m.value = e, n && B()
            }

            function B() {
                var e, n;
                const o = m.value;
                if (!o) return;
                const l = g.value(o.key);
                null !== l && (t.virtualScroll ? null === (e = u.value) || void 0 === e || e.scrollTo({
                    index: l
                }) : null === (n = c.value) || void 0 === n || n.scrollTo({
                    index: l,
                    elSize: F.value
                }))
            }
            o(K, {
                handleOptionMouseEnter: function(e, n) {
                    n.disabled || P(n, !1)
                },
                handleOptionClick: function(e, n) {
                    n.disabled || function(e) {
                        const {
                            onToggle: n
                        } = t;
                        n && n(e)
                    }(n)
                },
                valueSetRef: O,
                pendingTmNodeRef: m,
                nodePropsRef: p(t, "nodeProps"),
                showCheckmarkRef: p(t, "showCheckmark"),
                multipleRef: p(t, "multiple"),
                valueRef: p(t, "value"),
                renderLabelRef: p(t, "renderLabel"),
                renderOptionRef: p(t, "renderOption"),
                labelFieldRef: p(t, "labelField"),
                valueFieldRef: p(t, "valueField")
            }), o(U, s), d((() => {
                const {
                    value: e
                } = c;
                e && e.sync()
            }));
            const $ = n((() => {
                    const {
                        size: e
                    } = t, {
                        common: {
                            cubicBezierEaseInOut: n
                        },
                        self: {
                            height: o,
                            borderRadius: l,
                            color: i,
                            groupHeaderTextColor: r,
                            actionDividerColor: s,
                            optionTextColorPressed: d,
                            optionTextColor: u,
                            optionTextColorDisabled: c,
                            optionTextColorActive: p,
                            optionOpacityDisabled: h,
                            optionCheckColor: v,
                            actionTextColor: f,
                            optionColorPending: b,
                            optionColorActive: g,
                            loadingColor: m,
                            loadingSize: w,
                            optionColorActivePending: y,
                            [S("optionFontSize", e)]: x,
                            [S("optionHeight", e)]: C,
                            [S("optionPadding", e)]: F
                        }
                    } = a.value;
                    return {
                        "--n-height": o,
                        "--n-action-divider-color": s,
                        "--n-action-text-color": f,
                        "--n-bezier": n,
                        "--n-border-radius": l,
                        "--n-color": i,
                        "--n-option-font-size": x,
                        "--n-group-header-text-color": r,
                        "--n-option-check-color": v,
                        "--n-option-color-pending": b,
                        "--n-option-color-active": g,
                        "--n-option-color-active-pending": y,
                        "--n-option-height": C,
                        "--n-option-opacity-disabled": h,
                        "--n-option-text-color": u,
                        "--n-option-text-color-active": p,
                        "--n-option-text-color-disabled": c,
                        "--n-option-text-color-pressed": d,
                        "--n-option-padding": F,
                        "--n-option-padding-left": le(F, "left"),
                        "--n-option-padding-right": le(F, "right"),
                        "--n-loading-color": m,
                        "--n-loading-size": w
                    }
                })),
                {
                    inlineThemeDisabled: A
                } = t,
                L = A ? R("internal-select-menu", n((() => t.size[0])), $, t) : void 0,
                E = {
                    selfRef: s,
                    next: function() {
                        const {
                            value: e
                        } = m;
                        e && P(e.getNext({
                            loop: !0
                        }), !0)
                    },
                    prev: function() {
                        const {
                            value: e
                        } = m;
                        e && P(e.getPrev({
                            loop: !0
                        }), !0)
                    },
                    getPendingTmNode: function() {
                        const {
                            value: e
                        } = m;
                        return e || null
                    }
                };
            return Me(s, t.onResize), Object.assign({
                mergedTheme: a,
                mergedClsPrefix: l,
                rtlEnabled: r,
                virtualListRef: u,
                scrollbarRef: c,
                itemSize: F,
                padding: z,
                flattenedNodes: v,
                empty: T,
                virtualListContainer() {
                    const {
                        value: e
                    } = u;
                    return null == e ? void 0 : e.listElRef
                },
                virtualListContent() {
                    const {
                        value: e
                    } = u;
                    return null == e ? void 0 : e.itemsElRef
                },
                doScroll: k,
                handleFocusin: function(e) {
                    var n, o;
                    (null === (n = s.value) || void 0 === n ? void 0 : n.contains(e.target)) && (null === (o = t.onFocus) || void 0 === o || o.call(t, e))
                },
                handleFocusout: function(e) {
                    var n, o;
                    (null === (n = s.value) || void 0 === n ? void 0 : n.contains(e.relatedTarget)) || null === (o = t.onBlur) || void 0 === o || o.call(t, e)
                },
                handleKeyUp: function(e) {
                    var n;
                    ve(e, "action") || null === (n = t.onKeyup) || void 0 === n || n.call(t, e)
                },
                handleKeyDown: function(e) {
                    var n;
                    ve(e, "action") || null === (n = t.onKeydown) || void 0 === n || n.call(t, e)
                },
                handleMouseDown: function(e) {
                    var n;
                    null === (n = t.onMousedown) || void 0 === n || n.call(t, e), t.focusable || e.preventDefault()
                },
                handleVirtualListResize: function() {
                    var e;
                    null === (e = c.value) || void 0 === e || e.sync()
                },
                handleVirtualListScroll: function(e) {
                    var n;
                    null === (n = c.value) || void 0 === n || n.sync(), k(e)
                },
                cssVars: A ? void 0 : $,
                themeClass: null == L ? void 0 : L.themeClass,
                onRender: null == L ? void 0 : L.onRender
            }, E)
        },
        render() {
            const {
                $slots: e,
                virtualScroll: n,
                clsPrefix: t,
                mergedTheme: o,
                themeClass: l,
                onRender: i
            } = this;
            return null == i || i(), r("div", {
                ref: "selfRef",
                tabindex: this.focusable ? 0 : -1,
                class: [`${t}-base-select-menu`, this.rtlEnabled && `${t}-base-select-menu--rtl`, l, this.multiple && `${t}-base-select-menu--multiple`],
                style: this.cssVars,
                onFocusin: this.handleFocusin,
                onFocusout: this.handleFocusout,
                onKeyup: this.handleKeyUp,
                onKeydown: this.handleKeyDown,
                onMousedown: this.handleMouseDown,
                onMouseenter: this.onMouseenter,
                onMouseleave: this.onMouseleave
            }, de(e.header, (e => e && r("div", {
                class: `${t}-base-select-menu__header`,
                "data-header": !0,
                key: "header"
            }, e))), this.loading ? r("div", {
                class: `${t}-base-select-menu__loading`
            }, r(P, {
                clsPrefix: t,
                strokeWidth: 20
            })) : this.empty ? r("div", {
                class: `${t}-base-select-menu__empty`,
                "data-empty": !0
            }, ue(e.empty, (() => [r(je, {
                theme: o.peers.Empty,
                themeOverrides: o.peerOverrides.Empty,
                size: this.size
            })]))) : r(J, {
                ref: "scrollbarRef",
                theme: o.peers.Scrollbar,
                themeOverrides: o.peerOverrides.Scrollbar,
                scrollable: this.scrollable,
                container: n ? this.virtualListContainer : void 0,
                content: n ? this.virtualListContent : void 0,
                onScroll: n ? void 0 : this.doScroll
            }, {
                default: () => n ? r(Oe, {
                    ref: "virtualListRef",
                    class: `${t}-virtual-list`,
                    items: this.flattenedNodes,
                    itemSize: this.itemSize,
                    showScrollbar: !1,
                    paddingTop: this.padding.top,
                    paddingBottom: this.padding.bottom,
                    onResize: this.handleVirtualListResize,
                    onScroll: this.handleVirtualListScroll,
                    itemResizable: !0
                }, {
                    default: ({
                        item: e
                    }) => e.isGroup ? r(De, {
                        key: e.key,
                        clsPrefix: t,
                        tmNode: e
                    }) : e.ignored ? null : r(Ne, {
                        clsPrefix: t,
                        key: e.key,
                        tmNode: e
                    })
                }) : r("div", {
                    class: `${t}-base-select-menu-option-wrapper`,
                    style: {
                        paddingTop: this.padding.top,
                        paddingBottom: this.padding.bottom
                    }
                }, this.flattenedNodes.map((e => e.isGroup ? r(De, {
                    key: e.key,
                    clsPrefix: t,
                    tmNode: e
                }) : r(Ne, {
                    clsPrefix: t,
                    key: e.key,
                    tmNode: e
                }))))
            }), de(e.action, (e => e && [r("div", {
                class: `${t}-base-select-menu__action`,
                "data-action": !0,
                key: "action"
            }, e), r(Le, {
                onFocus: this.onTabOut,
                key: "focus-detector"
            })])))
        }
    }),
    Ve = w([g("base-selection", "\n --n-padding-single: var(--n-padding-single-top) var(--n-padding-single-right) var(--n-padding-single-bottom) var(--n-padding-single-left);\n --n-padding-multiple: var(--n-padding-multiple-top) var(--n-padding-multiple-right) var(--n-padding-multiple-bottom) var(--n-padding-multiple-left);\n position: relative;\n z-index: auto;\n box-shadow: none;\n width: 100%;\n max-width: 100%;\n display: inline-block;\n vertical-align: bottom;\n border-radius: var(--n-border-radius);\n min-height: var(--n-height);\n line-height: 1.5;\n font-size: var(--n-font-size);\n ", [g("base-loading", "\n color: var(--n-loading-color);\n "), g("base-selection-tags", "min-height: var(--n-height);"), m("border, state-border", "\n position: absolute;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n pointer-events: none;\n border: var(--n-border);\n border-radius: inherit;\n transition:\n box-shadow .3s var(--n-bezier),\n border-color .3s var(--n-bezier);\n "), m("state-border", "\n z-index: 1;\n border-color: #0000;\n "), g("base-suffix", "\n cursor: pointer;\n position: absolute;\n top: 50%;\n transform: translateY(-50%);\n right: 10px;\n ", [m("arrow", "\n font-size: var(--n-arrow-size);\n color: var(--n-arrow-color);\n transition: color .3s var(--n-bezier);\n ")]), g("base-selection-overlay", "\n display: flex;\n align-items: center;\n white-space: nowrap;\n pointer-events: none;\n position: absolute;\n top: 0;\n right: 0;\n bottom: 0;\n left: 0;\n padding: var(--n-padding-single);\n transition: color .3s var(--n-bezier);\n ", [m("wrapper", "\n flex-basis: 0;\n flex-grow: 1;\n overflow: hidden;\n text-overflow: ellipsis;\n ")]), g("base-selection-placeholder", "\n color: var(--n-placeholder-color);\n ", [m("inner", "\n max-width: 100%;\n overflow: hidden;\n ")]), g("base-selection-tags", "\n cursor: pointer;\n outline: none;\n box-sizing: border-box;\n position: relative;\n z-index: auto;\n display: flex;\n padding: var(--n-padding-multiple);\n flex-wrap: wrap;\n align-items: center;\n width: 100%;\n vertical-align: bottom;\n background-color: var(--n-color);\n border-radius: inherit;\n transition:\n color .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier),\n background-color .3s var(--n-bezier);\n "), g("base-selection-label", "\n height: var(--n-height);\n display: inline-flex;\n width: 100%;\n vertical-align: bottom;\n cursor: pointer;\n outline: none;\n z-index: auto;\n box-sizing: border-box;\n position: relative;\n transition:\n color .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier),\n background-color .3s var(--n-bezier);\n border-radius: inherit;\n background-color: var(--n-color);\n align-items: center;\n ", [g("base-selection-input", "\n font-size: inherit;\n line-height: inherit;\n outline: none;\n cursor: pointer;\n box-sizing: border-box;\n border:none;\n width: 100%;\n padding: var(--n-padding-single);\n background-color: #0000;\n color: var(--n-text-color);\n transition: color .3s var(--n-bezier);\n caret-color: var(--n-caret-color);\n ", [m("content", "\n text-overflow: ellipsis;\n overflow: hidden;\n white-space: nowrap; \n ")]), m("render-label", "\n color: var(--n-text-color);\n ")]), k("disabled", [w("&:hover", [m("state-border", "\n box-shadow: var(--n-box-shadow-hover);\n border: var(--n-border-hover);\n ")]), T("focus", [m("state-border", "\n box-shadow: var(--n-box-shadow-focus);\n border: var(--n-border-focus);\n ")]), T("active", [m("state-border", "\n box-shadow: var(--n-box-shadow-active);\n border: var(--n-border-active);\n "), g("base-selection-label", "background-color: var(--n-color-active);"), g("base-selection-tags", "background-color: var(--n-color-active);")])]), T("disabled", "cursor: not-allowed;", [m("arrow", "\n color: var(--n-arrow-color-disabled);\n "), g("base-selection-label", "\n cursor: not-allowed;\n background-color: var(--n-color-disabled);\n ", [g("base-selection-input", "\n cursor: not-allowed;\n color: var(--n-text-color-disabled);\n "), m("render-label", "\n color: var(--n-text-color-disabled);\n ")]), g("base-selection-tags", "\n cursor: not-allowed;\n background-color: var(--n-color-disabled);\n "), g("base-selection-placeholder", "\n cursor: not-allowed;\n color: var(--n-placeholder-color-disabled);\n ")]), g("base-selection-input-tag", "\n height: calc(var(--n-height) - 6px);\n line-height: calc(var(--n-height) - 6px);\n outline: none;\n display: none;\n position: relative;\n margin-bottom: 3px;\n max-width: 100%;\n vertical-align: bottom;\n ", [m("input", "\n font-size: inherit;\n font-family: inherit;\n min-width: 1px;\n padding: 0;\n background-color: #0000;\n outline: none;\n border: none;\n max-width: 100%;\n overflow: hidden;\n width: 1em;\n line-height: inherit;\n cursor: pointer;\n color: var(--n-text-color);\n caret-color: var(--n-caret-color);\n "), m("mirror", "\n position: absolute;\n left: 0;\n top: 0;\n white-space: pre;\n visibility: hidden;\n user-select: none;\n -webkit-user-select: none;\n opacity: 0;\n ")]), ["warning", "error"].map((e => T(`${e}-status`, [m("state-border", `border: var(--n-border-${e});`), k("disabled", [w("&:hover", [m("state-border", `\n box-shadow: var(--n-box-shadow-hover-${e});\n border: var(--n-border-hover-${e});\n `)]), T("active", [m("state-border", `\n box-shadow: var(--n-box-shadow-active-${e});\n border: var(--n-border-active-${e});\n `), g("base-selection-label", `background-color: var(--n-color-active-${e});`), g("base-selection-tags", `background-color: var(--n-color-active-${e});`)]), T("focus", [m("state-border", `\n box-shadow: var(--n-box-shadow-focus-${e});\n border: var(--n-border-focus-${e});\n `)])])])))]), g("base-selection-popover", "\n margin-bottom: -3px;\n display: flex;\n flex-wrap: wrap;\n margin-right: -8px;\n "), g("base-selection-tag-wrapper", "\n max-width: 100%;\n display: inline-flex;\n padding: 0 7px 3px 0;\n ", [w("&:last-child", "padding-right: 0;"), g("tag", "\n font-size: 14px;\n max-width: 100%;\n ", [m("content", "\n line-height: 1.25;\n text-overflow: ellipsis;\n overflow: hidden;\n ")])])]),
    He = l({
        name: "InternalSelection",
        props: Object.assign(Object.assign({}, C.props), {
            clsPrefix: {
                type: String,
                required: !0
            },
            bordered: {
                type: Boolean,
                default: void 0
            },
            active: Boolean,
            pattern: {
                type: String,
                default: ""
            },
            placeholder: String,
            selectedOption: {
                type: Object,
                default: null
            },
            selectedOptions: {
                type: Array,
                default: null
            },
            labelField: {
                type: String,
                default: "label"
            },
            valueField: {
                type: String,
                default: "value"
            },
            multiple: Boolean,
            filterable: Boolean,
            clearable: Boolean,
            disabled: Boolean,
            size: {
                type: String,
                default: "medium"
            },
            loading: Boolean,
            autofocus: Boolean,
            showArrow: {
                type: Boolean,
                default: !0
            },
            inputProps: Object,
            focused: Boolean,
            renderTag: Function,
            onKeydown: Function,
            onClick: Function,
            onBlur: Function,
            onFocus: Function,
            onDeleteOption: Function,
            maxTagCount: [String, Number],
            ellipsisTagPopoverProps: Object,
            onClear: Function,
            onPatternInput: Function,
            onPatternFocus: Function,
            onPatternBlur: Function,
            renderLabel: Function,
            status: String,
            inlineThemeDisabled: Boolean,
            ignoreComposition: {
                type: Boolean,
                default: !0
            },
            onResize: Function
        }),
        setup(t) {
            const {
                mergedClsPrefixRef: o,
                mergedRtlRef: l
            } = x(t), i = M("InternalSelection", l, o), r = e(null), a = e(null), s = e(null), u = e(null), c = e(null), v = e(null), b = e(null), g = e(null), m = e(null), w = e(null), y = e(!1), F = e(!1), O = e(!1), T = C("InternalSelection", "-internal-selection", Ve, $, t, p(t, "clsPrefix")), k = n((() => t.clearable && !t.disabled && (O.value || t.active))), P = n((() => t.selectedOption ? t.renderTag ? t.renderTag({
                option: t.selectedOption,
                handleClose: () => {}
            }) : t.renderLabel ? t.renderLabel(t.selectedOption, !0) : z(t.selectedOption[t.labelField], t.selectedOption, !0) : t.placeholder)), I = n((() => {
                const e = t.selectedOption;
                if (e) return e[t.labelField]
            })), B = n((() => t.multiple ? !(!Array.isArray(t.selectedOptions) || !t.selectedOptions.length) : null !== t.selectedOption));

            function L() {
                var e;
                const {
                    value: n
                } = r;
                if (n) {
                    const {
                        value: o
                    } = a;
                    o && (o.style.width = `${n.offsetWidth}px`, "responsive" !== t.maxTagCount && (null === (e = m.value) || void 0 === e || e.sync({
                        showAllItemsBeforeCalculate: !1
                    })))
                }
            }

            function E(e) {
                const {
                    onPatternInput: n
                } = t;
                n && n(e)
            }

            function j(e) {
                ! function(e) {
                    const {
                        onDeleteOption: n
                    } = t;
                    n && n(e)
                }(e)
            }
            f(p(t, "active"), (e => {
                e || function() {
                    const {
                        value: e
                    } = w;
                    e && (e.style.display = "none")
                }()
            })), f(p(t, "pattern"), (() => {
                t.multiple && h(L)
            }));
            const D = e(!1);
            let N = null;
            let W = null;

            function _() {
                null !== W && window.clearTimeout(W)
            }
            f(B, (e => {
                e || (y.value = !1)
            })), d((() => {
                A((() => {
                    const e = v.value;
                    e && (t.disabled ? e.removeAttribute("tabindex") : e.tabIndex = F.value ? -1 : 0)
                }))
            })), Me(s, t.onResize);
            const {
                inlineThemeDisabled: V
            } = t, H = n((() => {
                const {
                    size: e
                } = t, {
                    common: {
                        cubicBezierEaseInOut: n
                    },
                    self: {
                        fontWeight: o,
                        borderRadius: l,
                        color: i,
                        placeholderColor: r,
                        textColor: a,
                        paddingSingle: s,
                        paddingMultiple: d,
                        caretColor: u,
                        colorDisabled: c,
                        textColorDisabled: p,
                        placeholderColorDisabled: h,
                        colorActive: v,
                        boxShadowFocus: f,
                        boxShadowActive: b,
                        boxShadowHover: g,
                        border: m,
                        borderFocus: w,
                        borderHover: y,
                        borderActive: x,
                        arrowColor: C,
                        arrowColorDisabled: F,
                        loadingColor: R,
                        colorActiveWarning: z,
                        boxShadowFocusWarning: O,
                        boxShadowActiveWarning: k,
                        boxShadowHoverWarning: P,
                        borderWarning: M,
                        borderFocusWarning: I,
                        borderHoverWarning: B,
                        borderActiveWarning: $,
                        colorActiveError: A,
                        boxShadowFocusError: L,
                        boxShadowActiveError: E,
                        boxShadowHoverError: j,
                        borderError: D,
                        borderFocusError: N,
                        borderHoverError: W,
                        borderActiveError: _,
                        clearColor: V,
                        clearColorHover: H,
                        clearColorPressed: K,
                        clearSize: U,
                        arrowSize: q,
                        [S("height", e)]: G,
                        [S("fontSize", e)]: Y
                    }
                } = T.value, X = le(s), Z = le(d);
                return {
                    "--n-bezier": n,
                    "--n-border": m,
                    "--n-border-active": x,
                    "--n-border-focus": w,
                    "--n-border-hover": y,
                    "--n-border-radius": l,
                    "--n-box-shadow-active": b,
                    "--n-box-shadow-focus": f,
                    "--n-box-shadow-hover": g,
                    "--n-caret-color": u,
                    "--n-color": i,
                    "--n-color-active": v,
                    "--n-color-disabled": c,
                    "--n-font-size": Y,
                    "--n-height": G,
                    "--n-padding-single-top": X.top,
                    "--n-padding-multiple-top": Z.top,
                    "--n-padding-single-right": X.right,
                    "--n-padding-multiple-right": Z.right,
                    "--n-padding-single-left": X.left,
                    "--n-padding-multiple-left": Z.left,
                    "--n-padding-single-bottom": X.bottom,
                    "--n-padding-multiple-bottom": Z.bottom,
                    "--n-placeholder-color": r,
                    "--n-placeholder-color-disabled": h,
                    "--n-text-color": a,
                    "--n-text-color-disabled": p,
                    "--n-arrow-color": C,
                    "--n-arrow-color-disabled": F,
                    "--n-loading-color": R,
                    "--n-color-active-warning": z,
                    "--n-box-shadow-focus-warning": O,
                    "--n-box-shadow-active-warning": k,
                    "--n-box-shadow-hover-warning": P,
                    "--n-border-warning": M,
                    "--n-border-focus-warning": I,
                    "--n-border-hover-warning": B,
                    "--n-border-active-warning": $,
                    "--n-color-active-error": A,
                    "--n-box-shadow-focus-error": L,
                    "--n-box-shadow-active-error": E,
                    "--n-box-shadow-hover-error": j,
                    "--n-border-error": D,
                    "--n-border-focus-error": N,
                    "--n-border-hover-error": W,
                    "--n-border-active-error": _,
                    "--n-clear-size": U,
                    "--n-clear-color": V,
                    "--n-clear-color-hover": H,
                    "--n-clear-color-pressed": K,
                    "--n-arrow-size": q,
                    "--n-font-weight": o
                }
            })), K = V ? R("internal-selection", n((() => t.size[0])), H, t) : void 0;
            return {
                mergedTheme: T,
                mergedClearable: k,
                mergedClsPrefix: o,
                rtlEnabled: i,
                patternInputFocused: F,
                filterablePlaceholder: P,
                label: I,
                selected: B,
                showTagsPanel: y,
                isComposing: D,
                counterRef: b,
                counterWrapperRef: g,
                patternInputMirrorRef: r,
                patternInputRef: a,
                selfRef: s,
                multipleElRef: u,
                singleElRef: c,
                patternInputWrapperRef: v,
                overflowRef: m,
                inputTagElRef: w,
                handleMouseDown: function(e) {
                    t.active && t.filterable && e.target !== a.value && e.preventDefault()
                },
                handleFocusin: function(e) {
                    var n;
                    e.relatedTarget && (null === (n = s.value) || void 0 === n ? void 0 : n.contains(e.relatedTarget)) || function(e) {
                        const {
                            onFocus: n
                        } = t;
                        n && n(e)
                    }(e)
                },
                handleClear: function(e) {
                    ! function(e) {
                        const {
                            onClear: n
                        } = t;
                        n && n(e)
                    }(e)
                },
                handleMouseEnter: function() {
                    O.value = !0
                },
                handleMouseLeave: function() {
                    O.value = !1
                },
                handleDeleteOption: j,
                handlePatternKeyDown: function(e) {
                    if ("Backspace" === e.key && !D.value && !t.pattern.length) {
                        const {
                            selectedOptions: e
                        } = t;
                        (null == e ? void 0 : e.length) && j(e[e.length - 1])
                    }
                },
                handlePatternInputInput: function(e) {
                    const {
                        value: n
                    } = r;
                    if (n) {
                        const t = e.target.value;
                        n.textContent = t, L()
                    }
                    t.ignoreComposition && D.value ? N = e : E(e)
                },
                handlePatternInputBlur: function(e) {
                    var n;
                    F.value = !1, null === (n = t.onPatternBlur) || void 0 === n || n.call(t, e)
                },
                handlePatternInputFocus: function(e) {
                    var n;
                    F.value = !0, null === (n = t.onPatternFocus) || void 0 === n || n.call(t, e)
                },
                handleMouseEnterCounter: function() {
                    t.active || (_(), W = window.setTimeout((() => {
                        B.value && (y.value = !0)
                    }), 100))
                },
                handleMouseLeaveCounter: function() {
                    _()
                },
                handleFocusout: function(e) {
                    var n;
                    (null === (n = s.value) || void 0 === n ? void 0 : n.contains(e.relatedTarget)) || function(e) {
                        const {
                            onBlur: n
                        } = t;
                        n && n(e)
                    }(e)
                },
                handleCompositionEnd: function() {
                    D.value = !1, t.ignoreComposition && E(N), N = null
                },
                handleCompositionStart: function() {
                    D.value = !0
                },
                onPopoverUpdateShow: function(e) {
                    e || (_(), y.value = !1)
                },
                focus: function() {
                    var e, n, o;
                    t.filterable ? (F.value = !1, null === (e = v.value) || void 0 === e || e.focus()) : t.multiple ? null === (n = u.value) || void 0 === n || n.focus() : null === (o = c.value) || void 0 === o || o.focus()
                },
                focusInput: function() {
                    const {
                        value: e
                    } = a;
                    e && (! function() {
                        const {
                            value: e
                        } = w;
                        e && (e.style.display = "inline-block")
                    }(), e.focus())
                },
                blur: function() {
                    var e, n;
                    if (t.filterable) F.value = !1, null === (e = v.value) || void 0 === e || e.blur(), null === (n = a.value) || void 0 === n || n.blur();
                    else if (t.multiple) {
                        const {
                            value: e
                        } = u;
                        null == e || e.blur()
                    } else {
                        const {
                            value: e
                        } = c;
                        null == e || e.blur()
                    }
                },
                blurInput: function() {
                    const {
                        value: e
                    } = a;
                    e && e.blur()
                },
                updateCounter: function(e) {
                    const {
                        value: n
                    } = b;
                    n && n.setTextContent(`+${e}`)
                },
                getCounter: function() {
                    const {
                        value: e
                    } = g;
                    return e
                },
                getTail: function() {
                    return a.value
                },
                renderLabel: t.renderLabel,
                cssVars: V ? void 0 : H,
                themeClass: null == K ? void 0 : K.themeClass,
                onRender: null == K ? void 0 : K.onRender
            }
        },
        render() {
            const {
                status: e,
                multiple: n,
                size: t,
                disabled: o,
                filterable: l,
                maxTagCount: i,
                bordered: a,
                clsPrefix: s,
                ellipsisTagPopoverProps: d,
                onRender: u,
                renderTag: c,
                renderLabel: p
            } = this;
            null == u || u();
            const h = "responsive" === i,
                v = "number" == typeof i,
                f = h || v,
                b = r(Q, null, {
                    default: () => r(ee, {
                        clsPrefix: s,
                        loading: this.loading,
                        showArrow: this.showArrow,
                        showClear: this.mergedClearable && this.selected,
                        onClear: this.handleClear
                    }, {
                        default: () => {
                            var e, n;
                            return null === (n = (e = this.$slots).arrow) || void 0 === n ? void 0 : n.call(e)
                        }
                    })
                });
            let g;
            if (n) {
                const {
                    labelField: e
                } = this, n = n => r("div", {
                    class: `${s}-base-selection-tag-wrapper`,
                    key: n.value
                }, c ? c({
                    option: n,
                    handleClose: () => {
                        this.handleDeleteOption(n)
                    }
                }) : r(ne, {
                    size: t,
                    closable: !n.disabled,
                    disabled: o,
                    onClose: () => {
                        this.handleDeleteOption(n)
                    },
                    internalCloseIsButtonTag: !1,
                    internalCloseFocusable: !1
                }, {
                    default: () => p ? p(n, !0) : z(n[e], n, !0)
                })), a = () => (v ? this.selectedOptions.slice(0, i) : this.selectedOptions).map(n), u = l ? r("div", {
                    class: `${s}-base-selection-input-tag`,
                    ref: "inputTagElRef",
                    key: "__input-tag__"
                }, r("input", Object.assign({}, this.inputProps, {
                    ref: "patternInputRef",
                    tabindex: -1,
                    disabled: o,
                    value: this.pattern,
                    autofocus: this.autofocus,
                    class: `${s}-base-selection-input-tag__input`,
                    onBlur: this.handlePatternInputBlur,
                    onFocus: this.handlePatternInputFocus,
                    onKeydown: this.handlePatternKeyDown,
                    onInput: this.handlePatternInputInput,
                    onCompositionstart: this.handleCompositionStart,
                    onCompositionend: this.handleCompositionEnd
                })), r("span", {
                    ref: "patternInputMirrorRef",
                    class: `${s}-base-selection-input-tag__mirror`
                }, this.pattern)) : null, m = h ? () => r("div", {
                    class: `${s}-base-selection-tag-wrapper`,
                    ref: "counterWrapperRef"
                }, r(ne, {
                    size: t,
                    ref: "counterRef",
                    onMouseenter: this.handleMouseEnterCounter,
                    onMouseleave: this.handleMouseLeaveCounter,
                    disabled: o
                })) : void 0;
                let w;
                if (v) {
                    const e = this.selectedOptions.length - i;
                    e > 0 && (w = r("div", {
                        class: `${s}-base-selection-tag-wrapper`,
                        key: "__counter__"
                    }, r(ne, {
                        size: t,
                        ref: "counterRef",
                        onMouseenter: this.handleMouseEnterCounter,
                        disabled: o
                    }, {
                        default: () => `+${e}`
                    })))
                }
                const y = h ? l ? r(Pe, {
                        ref: "overflowRef",
                        updateCounter: this.updateCounter,
                        getCounter: this.getCounter,
                        getTail: this.getTail,
                        style: {
                            width: "100%",
                            display: "flex",
                            overflow: "hidden"
                        }
                    }, {
                        default: a,
                        counter: m,
                        tail: () => u
                    }) : r(Pe, {
                        ref: "overflowRef",
                        updateCounter: this.updateCounter,
                        getCounter: this.getCounter,
                        style: {
                            width: "100%",
                            display: "flex",
                            overflow: "hidden"
                        }
                    }, {
                        default: a,
                        counter: m
                    }) : v && w ? a().concat(w) : a(),
                    x = f ? () => r("div", {
                        class: `${s}-base-selection-popover`
                    }, h ? a() : this.selectedOptions.map(n)) : void 0,
                    C = f ? Object.assign({
                        show: this.showTagsPanel,
                        trigger: "hover",
                        overlap: !0,
                        placement: "top",
                        width: "trigger",
                        onUpdateShow: this.onPopoverUpdateShow,
                        theme: this.mergedTheme.peers.Popover,
                        themeOverrides: this.mergedTheme.peerOverrides.Popover
                    }, d) : null,
                    F = !this.selected && (!this.active || !this.pattern && !this.isComposing) ? r("div", {
                        class: `${s}-base-selection-placeholder ${s}-base-selection-overlay`
                    }, r("div", {
                        class: `${s}-base-selection-placeholder__inner`
                    }, this.placeholder)) : null,
                    S = l ? r("div", {
                        ref: "patternInputWrapperRef",
                        class: `${s}-base-selection-tags`
                    }, y, h ? null : u, b) : r("div", {
                        ref: "multipleElRef",
                        class: `${s}-base-selection-tags`,
                        tabindex: o ? void 0 : 0
                    }, y, b);
                g = r(B, null, f ? r(q, Object.assign({}, C, {
                    scrollable: !0,
                    style: "max-height: calc(var(--v-target-height) * 6.6);"
                }), {
                    trigger: () => S,
                    default: x
                }) : S, F)
            } else if (l) {
                const e = this.pattern || this.isComposing,
                    n = this.active ? !e : !this.selected,
                    t = !this.active && this.selected;
                g = r("div", {
                    ref: "patternInputWrapperRef",
                    class: `${s}-base-selection-label`,
                    title: this.patternInputFocused ? void 0 : Ie(this.label)
                }, r("input", Object.assign({}, this.inputProps, {
                    ref: "patternInputRef",
                    class: `${s}-base-selection-input`,
                    value: this.active ? this.pattern : "",
                    placeholder: "",
                    readonly: o,
                    disabled: o,
                    tabindex: -1,
                    autofocus: this.autofocus,
                    onFocus: this.handlePatternInputFocus,
                    onBlur: this.handlePatternInputBlur,
                    onInput: this.handlePatternInputInput,
                    onCompositionstart: this.handleCompositionStart,
                    onCompositionend: this.handleCompositionEnd
                })), t ? r("div", {
                    class: `${s}-base-selection-label__render-label ${s}-base-selection-overlay`,
                    key: "input"
                }, r("div", {
                    class: `${s}-base-selection-overlay__wrapper`
                }, c ? c({
                    option: this.selectedOption,
                    handleClose: () => {}
                }) : p ? p(this.selectedOption, !0) : z(this.label, this.selectedOption, !0))) : null, n ? r("div", {
                    class: `${s}-base-selection-placeholder ${s}-base-selection-overlay`,
                    key: "placeholder"
                }, r("div", {
                    class: `${s}-base-selection-overlay__wrapper`
                }, this.filterablePlaceholder)) : null, b)
            } else g = r("div", {
                ref: "singleElRef",
                class: `${s}-base-selection-label`,
                tabindex: this.disabled ? void 0 : 0
            }, void 0 !== this.label ? r("div", {
                class: `${s}-base-selection-input`,
                title: Ie(this.label),
                key: "input"
            }, r("div", {
                class: `${s}-base-selection-input__content`
            }, c ? c({
                option: this.selectedOption,
                handleClose: () => {}
            }) : p ? p(this.selectedOption, !0) : z(this.label, this.selectedOption, !0))) : r("div", {
                class: `${s}-base-selection-placeholder ${s}-base-selection-overlay`,
                key: "placeholder"
            }, r("div", {
                class: `${s}-base-selection-placeholder__inner`
            }, this.placeholder)), b);
            return r("div", {
                ref: "selfRef",
                class: [`${s}-base-selection`, this.rtlEnabled && `${s}-base-selection--rtl`, this.themeClass, e && `${s}-base-selection--${e}-status`, {
                    [`${s}-base-selection--active`]: this.active,
                    [`${s}-base-selection--selected`]: this.selected || this.active && this.pattern,
                    [`${s}-base-selection--disabled`]: this.disabled,
                    [`${s}-base-selection--multiple`]: this.multiple,
                    [`${s}-base-selection--focus`]: this.focused
                }],
                style: this.cssVars,
                onClick: this.onClick,
                onMouseenter: this.handleMouseEnter,
                onMouseleave: this.handleMouseLeave,
                onKeydown: this.onKeydown,
                onFocusin: this.handleFocusin,
                onFocusout: this.handleFocusout,
                onMousedown: this.handleMouseDown
            }, g, a ? r("div", {
                class: `${s}-base-selection__border`
            }) : null, a ? r("div", {
                class: `${s}-base-selection__state-border`
            }) : null)
        }
    });

function Ke(e) {
    return "group" === e.type
}

function Ue(e) {
    return "ignored" === e.type
}

function qe(e, n) {
    try {
        return !!(1 + n.toString().toLowerCase().indexOf(e.trim().toLowerCase()))
    } catch (t) {
        return !1
    }
}

function Ge(e, n) {
    return {
        getIsGroup: Ke,
        getIgnored: Ue,
        getKey: n => Ke(n) ? n.name || n.key || "key-required" : n[e],
        getChildren: e => e[n]
    }
}
const Ye = w([g("select", "\n z-index: auto;\n outline: none;\n width: 100%;\n position: relative;\n font-weight: var(--n-font-weight);\n "), g("select-menu", "\n margin: 4px 0;\n box-shadow: var(--n-menu-box-shadow);\n ", [N({
        originalTransition: "background-color .3s var(--n-bezier), box-shadow .3s var(--n-bezier)"
    })])]),
    Xe = l({
        name: "Select",
        props: Object.assign(Object.assign({}, C.props), {
            to: G.propTo,
            bordered: {
                type: Boolean,
                default: void 0
            },
            clearable: Boolean,
            clearFilterAfterSelect: {
                type: Boolean,
                default: !0
            },
            options: {
                type: Array,
                default: () => []
            },
            defaultValue: {
                type: [String, Number, Array],
                default: null
            },
            keyboard: {
                type: Boolean,
                default: !0
            },
            value: [String, Number, Array],
            placeholder: String,
            menuProps: Object,
            multiple: Boolean,
            size: String,
            menuSize: {
                type: String
            },
            filterable: Boolean,
            disabled: {
                type: Boolean,
                default: void 0
            },
            remote: Boolean,
            loading: Boolean,
            filter: Function,
            placement: {
                type: String,
                default: "bottom-start"
            },
            widthMode: {
                type: String,
                default: "trigger"
            },
            tag: Boolean,
            onCreate: Function,
            fallbackOption: {
                type: [Function, Boolean],
                default: void 0
            },
            show: {
                type: Boolean,
                default: void 0
            },
            showArrow: {
                type: Boolean,
                default: !0
            },
            maxTagCount: [Number, String],
            ellipsisTagPopoverProps: Object,
            consistentMenuWidth: {
                type: Boolean,
                default: !0
            },
            virtualScroll: {
                type: Boolean,
                default: !0
            },
            labelField: {
                type: String,
                default: "label"
            },
            valueField: {
                type: String,
                default: "value"
            },
            childrenField: {
                type: String,
                default: "children"
            },
            renderLabel: Function,
            renderOption: Function,
            renderTag: Function,
            "onUpdate:value": [Function, Array],
            inputProps: Object,
            nodeProps: Function,
            ignoreComposition: {
                type: Boolean,
                default: !0
            },
            showOnFocus: Boolean,
            onUpdateValue: [Function, Array],
            onBlur: [Function, Array],
            onClear: [Function, Array],
            onFocus: [Function, Array],
            onScroll: [Function, Array],
            onSearch: [Function, Array],
            onUpdateShow: [Function, Array],
            "onUpdate:show": [Function, Array],
            displayDirective: {
                type: String,
                default: "show"
            },
            resetMenuOnOptionsChange: {
                type: Boolean,
                default: !0
            },
            status: String,
            showCheckmark: {
                type: Boolean,
                default: !0
            },
            onChange: [Function, Array],
            items: Array
        }),
        slots: Object,
        setup(t) {
            const {
                mergedClsPrefixRef: o,
                mergedBorderedRef: l,
                namespaceRef: i,
                inlineThemeDisabled: r
            } = x(t), a = C("Select", "-select", Ye, j, t, o), s = e(t.defaultValue), d = p(t, "value"), u = be(d, s), c = e(!1), h = e(""), v = ge(t, ["items", "options"]), b = e([]), g = e([]), m = n((() => g.value.concat(b.value).concat(v.value))), w = n((() => {
                const {
                    filter: e
                } = t;
                if (e) return e;
                const {
                    labelField: n,
                    valueField: o
                } = t;
                return (e, t) => {
                    if (!t) return !1;
                    const l = t[n];
                    if ("string" == typeof l) return qe(e, l);
                    const i = t[o];
                    return "string" == typeof i ? qe(e, i) : "number" == typeof i && qe(e, String(i))
                }
            })), y = n((() => {
                if (t.remote) return v.value; {
                    const {
                        value: e
                    } = m, {
                        value: n
                    } = h;
                    return n.length && t.filterable ? function(e, n, t, o) {
                        return n ? function e(l) {
                            if (!Array.isArray(l)) return [];
                            const i = [];
                            for (const r of l)
                                if (Ke(r)) {
                                    const n = e(r[o]);
                                    n.length && i.push(Object.assign({}, r, {
                                        [o]: n
                                    }))
                                } else {
                                    if (Ue(r)) continue;
                                    n(t, r) && i.push(r)
                                }
                            return i
                        }(e) : e
                    }(e, w.value, n, t.childrenField) : e
                }
            })), F = n((() => {
                const {
                    valueField: e,
                    childrenField: n
                } = t, o = Ge(e, n);
                return fe(y.value, o)
            })), S = n((() => function(e, n, t) {
                const o = new Map;
                return e.forEach((e => {
                    Ke(e) ? e[t].forEach((e => {
                        o.set(e[n], e)
                    })) : o.set(e[n], e)
                })), o
            }(m.value, t.valueField, t.childrenField))), z = e(!1), O = be(p(t, "show"), z), T = e(null), k = e(null), P = e(null), {
                localeRef: M
            } = pe("Select"), I = n((() => {
                var e;
                return null !== (e = t.placeholder) && void 0 !== e ? e : M.value.placeholder
            })), B = [], $ = e(new Map), A = n((() => {
                const {
                    fallbackOption: e
                } = t;
                if (void 0 === e) {
                    const {
                        labelField: e,
                        valueField: n
                    } = t;
                    return t => ({
                        [e]: String(t),
                        [n]: t
                    })
                }
                return !1 !== e && (n => Object.assign(e(n), {
                    value: n
                }))
            }));

            function L(e) {
                const n = t.remote,
                    {
                        value: o
                    } = $,
                    {
                        value: l
                    } = S,
                    {
                        value: i
                    } = A,
                    r = [];
                return e.forEach((e => {
                    if (l.has(e)) r.push(l.get(e));
                    else if (n && o.has(e)) r.push(o.get(e));
                    else if (i) {
                        const n = i(e);
                        n && r.push(n)
                    }
                })), r
            }
            const E = n((() => {
                    if (t.multiple) {
                        const {
                            value: e
                        } = u;
                        return Array.isArray(e) ? L(e) : []
                    }
                    return null
                })),
                N = n((() => {
                    const {
                        value: e
                    } = u;
                    return t.multiple || Array.isArray(e) || null === e ? null : L([e])[0] || null
                })),
                _ = me(t),
                {
                    mergedSizeRef: V,
                    mergedDisabledRef: H,
                    mergedStatusRef: K
                } = _;

            function U(e, n) {
                const {
                    onChange: o,
                    "onUpdate:value": l,
                    onUpdateValue: i
                } = t, {
                    nTriggerFormChange: r,
                    nTriggerFormInput: a
                } = _;
                o && ce(o, e, n), i && ce(i, e, n), l && ce(l, e, n), s.value = e, r(), a()
            }

            function q(e) {
                const {
                    onBlur: n
                } = t, {
                    nTriggerFormBlur: o
                } = _;
                n && ce(n, e), o()
            }

            function Y() {
                var e;
                const {
                    remote: n,
                    multiple: o
                } = t;
                if (n) {
                    const {
                        value: n
                    } = $;
                    if (o) {
                        const {
                            valueField: o
                        } = t;
                        null === (e = E.value) || void 0 === e || e.forEach((e => {
                            n.set(e[o], e)
                        }))
                    } else {
                        const e = N.value;
                        e && n.set(e[t.valueField], e)
                    }
                }
            }

            function X(e) {
                const {
                    onUpdateShow: n,
                    "onUpdate:show": o
                } = t;
                n && ce(n, e), o && ce(o, e), z.value = e
            }

            function Z() {
                H.value || (X(!0), z.value = !0, t.filterable && re())
            }

            function J() {
                X(!1)
            }

            function Q() {
                h.value = "", g.value = B
            }
            const ee = e(!1);

            function ne(e) {
                te(e.rawNode)
            }

            function te(e) {
                if (H.value) return;
                const {
                    tag: n,
                    remote: o,
                    clearFilterAfterSelect: l,
                    valueField: i
                } = t;
                if (n && !o) {
                    const {
                        value: e
                    } = g, n = e[0] || null;
                    if (n) {
                        const e = b.value;
                        e.length ? e.push(n) : b.value = [n], g.value = B
                    }
                }
                if (o && $.value.set(e[i], e), t.multiple) {
                    const r = function(e) {
                            if (!Array.isArray(e)) return [];
                            if (A.value) return Array.from(e); {
                                const {
                                    remote: n
                                } = t, {
                                    value: o
                                } = S;
                                if (n) {
                                    const {
                                        value: n
                                    } = $;
                                    return e.filter((e => o.has(e) || n.has(e)))
                                }
                                return e.filter((e => o.has(e)))
                            }
                        }(u.value),
                        a = r.findIndex((n => n === e[i]));
                    if (~a) {
                        if (r.splice(a, 1), n && !o) {
                            const n = oe(e[i]);
                            ~n && (b.value.splice(n, 1), l && (h.value = ""))
                        }
                    } else r.push(e[i]), l && (h.value = "");
                    U(r, L(r))
                } else {
                    if (n && !o) {
                        const n = oe(e[i]);
                        b.value = ~n ? [b.value[n]] : B
                    }
                    ie(), J(), U(e[i], e)
                }
            }

            function oe(e) {
                return b.value.findIndex((n => n[t.valueField] === e))
            }

            function le(e) {
                var n, o, l, i, r;
                if (t.keyboard) switch (e.key) {
                    case " ":
                        if (t.filterable) break;
                        e.preventDefault();
                    case "Enter":
                        if (!(null === (n = T.value) || void 0 === n ? void 0 : n.isComposing))
                            if (O.value) {
                                const e = null === (o = P.value) || void 0 === o ? void 0 : o.getPendingTmNode();
                                e ? ne(e) : t.filterable || (J(), ie())
                            } else if (Z(), t.tag && ee.value) {
                            const e = g.value[0];
                            if (e) {
                                const n = e[t.valueField],
                                    {
                                        value: o
                                    } = u;
                                t.multiple && Array.isArray(o) && o.includes(n) || te(e)
                            }
                        }
                        e.preventDefault();
                        break;
                    case "ArrowUp":
                        if (e.preventDefault(), t.loading) return;
                        O.value && (null === (l = P.value) || void 0 === l || l.prev());
                        break;
                    case "ArrowDown":
                        if (e.preventDefault(), t.loading) return;
                        O.value ? null === (i = P.value) || void 0 === i || i.next() : Z();
                        break;
                    case "Escape":
                        O.value && (W(e), J()), null === (r = T.value) || void 0 === r || r.focus()
                } else e.preventDefault()
            }

            function ie() {
                var e;
                null === (e = T.value) || void 0 === e || e.focus()
            }

            function re() {
                var e;
                null === (e = T.value) || void 0 === e || e.focusInput()
            }
            Y(), f(p(t, "options"), Y);
            const se = {
                    focus: () => {
                        var e;
                        null === (e = T.value) || void 0 === e || e.focus()
                    },
                    focusInput: () => {
                        var e;
                        null === (e = T.value) || void 0 === e || e.focusInput()
                    },
                    blur: () => {
                        var e;
                        null === (e = T.value) || void 0 === e || e.blur()
                    },
                    blurInput: () => {
                        var e;
                        null === (e = T.value) || void 0 === e || e.blurInput()
                    }
                },
                de = n((() => {
                    const {
                        self: {
                            menuBoxShadow: e
                        }
                    } = a.value;
                    return {
                        "--n-menu-box-shadow": e
                    }
                })),
                ue = r ? R("select", void 0, de, t) : void 0;
            return Object.assign(Object.assign({}, se), {
                mergedStatus: K,
                mergedClsPrefix: o,
                mergedBordered: l,
                namespace: i,
                treeMate: F,
                isMounted: D(),
                triggerRef: T,
                menuRef: P,
                pattern: h,
                uncontrolledShow: z,
                mergedShow: O,
                adjustedTo: G(t),
                uncontrolledValue: s,
                mergedValue: u,
                followerRef: k,
                localizedPlaceholder: I,
                selectedOption: N,
                selectedOptions: E,
                mergedSize: V,
                mergedDisabled: H,
                focused: c,
                activeWithoutMenuOpen: ee,
                inlineThemeDisabled: r,
                onTriggerInputFocus: function() {
                    t.filterable && (ee.value = !0)
                },
                onTriggerInputBlur: function() {
                    t.filterable && (ee.value = !1, O.value || Q())
                },
                handleTriggerOrMenuResize: function() {
                    var e;
                    O.value && (null === (e = k.value) || void 0 === e || e.syncPosition())
                },
                handleMenuFocus: function() {
                    c.value = !0
                },
                handleMenuBlur: function(e) {
                    var n;
                    (null === (n = T.value) || void 0 === n ? void 0 : n.$el.contains(e.relatedTarget)) || (c.value = !1, q(e), J())
                },
                handleMenuTabOut: function() {
                    var e;
                    null === (e = T.value) || void 0 === e || e.focus(), J()
                },
                handleTriggerClick: function() {
                    H.value || (O.value ? t.filterable ? re() : J() : Z())
                },
                handleToggle: ne,
                handleDeleteOption: te,
                handlePatternInput: function(e) {
                    O.value || Z();
                    const {
                        value: n
                    } = e.target;
                    h.value = n;
                    const {
                        tag: o,
                        remote: l
                    } = t;
                    if (function(e) {
                            const {
                                onSearch: n
                            } = t;
                            n && ce(n, e)
                        }(n), o && !l) {
                        if (!n) return void(g.value = B);
                        const {
                            onCreate: e
                        } = t, o = e ? e(n) : {
                            [t.labelField]: n,
                            [t.valueField]: n
                        }, {
                            valueField: l,
                            labelField: i
                        } = t;
                        v.value.some((e => e[l] === o[l] || e[i] === o[i])) || b.value.some((e => e[l] === o[l] || e[i] === o[i])) ? g.value = B : g.value = [o]
                    }
                },
                handleClear: function(e) {
                    e.stopPropagation();
                    const {
                        multiple: n
                    } = t;
                    !n && t.filterable && J(),
                        function() {
                            const {
                                onClear: e
                            } = t;
                            e && ce(e)
                        }(), n ? U([], []) : U(null, null)
                },
                handleTriggerBlur: function(e) {
                    var n, t;
                    (null === (t = null === (n = P.value) || void 0 === n ? void 0 : n.selfRef) || void 0 === t ? void 0 : t.contains(e.relatedTarget)) || (c.value = !1, q(e), J())
                },
                handleTriggerFocus: function(e) {
                    ! function(e) {
                        const {
                            onFocus: n,
                            showOnFocus: o
                        } = t, {
                            nTriggerFormFocus: l
                        } = _;
                        n && ce(n, e), l(), o && Z()
                    }(e), c.value = !0
                },
                handleKeydown: le,
                handleMenuAfterLeave: Q,
                handleMenuClickOutside: function(e) {
                    var n;
                    O.value && ((null === (n = T.value) || void 0 === n ? void 0 : n.$el.contains(ae(e))) || J())
                },
                handleMenuScroll: function(e) {
                    ! function(e) {
                        const {
                            onScroll: n
                        } = t;
                        n && ce(n, e)
                    }(e)
                },
                handleMenuKeydown: le,
                handleMenuMousedown: function(e) {
                    ve(e, "action") || ve(e, "empty") || ve(e, "header") || e.preventDefault()
                },
                mergedTheme: a,
                cssVars: r ? void 0 : de,
                themeClass: null == ue ? void 0 : ue.themeClass,
                onRender: null == ue ? void 0 : ue.onRender
            })
        },
        render() {
            return r("div", {
                class: `${this.mergedClsPrefix}-select`
            }, r(Y, null, {
                default: () => [r(X, null, {
                    default: () => r(He, {
                        ref: "triggerRef",
                        inlineThemeDisabled: this.inlineThemeDisabled,
                        status: this.mergedStatus,
                        inputProps: this.inputProps,
                        clsPrefix: this.mergedClsPrefix,
                        showArrow: this.showArrow,
                        maxTagCount: this.maxTagCount,
                        ellipsisTagPopoverProps: this.ellipsisTagPopoverProps,
                        bordered: this.mergedBordered,
                        active: this.activeWithoutMenuOpen || this.mergedShow,
                        pattern: this.pattern,
                        placeholder: this.localizedPlaceholder,
                        selectedOption: this.selectedOption,
                        selectedOptions: this.selectedOptions,
                        multiple: this.multiple,
                        renderTag: this.renderTag,
                        renderLabel: this.renderLabel,
                        filterable: this.filterable,
                        clearable: this.clearable,
                        disabled: this.mergedDisabled,
                        size: this.mergedSize,
                        theme: this.mergedTheme.peers.InternalSelection,
                        labelField: this.labelField,
                        valueField: this.valueField,
                        themeOverrides: this.mergedTheme.peerOverrides.InternalSelection,
                        loading: this.loading,
                        focused: this.focused,
                        onClick: this.handleTriggerClick,
                        onDeleteOption: this.handleDeleteOption,
                        onPatternInput: this.handlePatternInput,
                        onClear: this.handleClear,
                        onBlur: this.handleTriggerBlur,
                        onFocus: this.handleTriggerFocus,
                        onKeydown: this.handleKeydown,
                        onPatternBlur: this.onTriggerInputBlur,
                        onPatternFocus: this.onTriggerInputFocus,
                        onResize: this.handleTriggerOrMenuResize,
                        ignoreComposition: this.ignoreComposition
                    }, {
                        arrow: () => {
                            var e, n;
                            return [null === (n = (e = this.$slots).arrow) || void 0 === n ? void 0 : n.call(e)]
                        }
                    })
                }), r(Z, {
                    ref: "followerRef",
                    show: this.mergedShow,
                    to: this.adjustedTo,
                    teleportDisabled: this.adjustedTo === G.tdkey,
                    containerClass: this.namespace,
                    width: this.consistentMenuWidth ? "target" : void 0,
                    minWidth: "target",
                    placement: this.placement
                }, {
                    default: () => r(O, {
                        name: "fade-in-scale-up-transition",
                        appear: this.isMounted,
                        onAfterLeave: this.handleMenuAfterLeave
                    }, {
                        default: () => {
                            var e, n, t;
                            return this.mergedShow || "show" === this.displayDirective ? (null === (e = this.onRender) || void 0 === e || e.call(this), L(r(_e, Object.assign({}, this.menuProps, {
                                ref: "menuRef",
                                onResize: this.handleTriggerOrMenuResize,
                                inlineThemeDisabled: this.inlineThemeDisabled,
                                virtualScroll: this.consistentMenuWidth && this.virtualScroll,
                                class: [`${this.mergedClsPrefix}-select-menu`, this.themeClass, null === (n = this.menuProps) || void 0 === n ? void 0 : n.class],
                                clsPrefix: this.mergedClsPrefix,
                                focusable: !0,
                                labelField: this.labelField,
                                valueField: this.valueField,
                                autoPending: !0,
                                nodeProps: this.nodeProps,
                                theme: this.mergedTheme.peers.InternalSelectMenu,
                                themeOverrides: this.mergedTheme.peerOverrides.InternalSelectMenu,
                                treeMate: this.treeMate,
                                multiple: this.multiple,
                                size: this.menuSize,
                                renderOption: this.renderOption,
                                renderLabel: this.renderLabel,
                                value: this.mergedValue,
                                style: [null === (t = this.menuProps) || void 0 === t ? void 0 : t.style, this.cssVars],
                                onToggle: this.handleToggle,
                                onScroll: this.handleMenuScroll,
                                onFocus: this.handleMenuFocus,
                                onBlur: this.handleMenuBlur,
                                onKeydown: this.handleMenuKeydown,
                                onTabOut: this.handleMenuTabOut,
                                onMousedown: this.handleMenuMousedown,
                                show: this.mergedShow,
                                showCheckmark: this.showCheckmark,
                                resetMenuOnOptionsChange: this.resetMenuOnOptionsChange
                            }), {
                                empty: () => {
                                    var e, n;
                                    return [null === (n = (e = this.$slots).empty) || void 0 === n ? void 0 : n.call(e)]
                                },
                                header: () => {
                                    var e, n;
                                    return [null === (n = (e = this.$slots).header) || void 0 === n ? void 0 : n.call(e)]
                                },
                                action: () => {
                                    var e, n;
                                    return [null === (n = (e = this.$slots).action) || void 0 === n ? void 0 : n.call(e)]
                                }
                            }), "show" === this.displayDirective ? [
                                [E, this.mergedShow],
                                [se, this.handleMenuClickOutside, void 0, {
                                    capture: !0
                                }]
                            ] : [
                                [se, this.handleMenuClickOutside, void 0, {
                                    capture: !0
                                }]
                            ])) : null
                        }
                    })
                })]
            }))
        }
    });
export {
    Xe as N, Oe as V, _e as a, je as b, Ge as c, Be as m
};