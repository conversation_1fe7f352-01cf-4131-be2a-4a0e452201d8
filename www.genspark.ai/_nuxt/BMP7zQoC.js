import {
    d as n,
    b as o,
    o as r
} from "./Cf0SOiw0.js";
const t = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 24 24"
};
const a = {
    render: function(a, s) {
        return r(), n("svg", t, s[0] || (s[0] = [o("g", {
            fill: "none"
        }, [o("path", {
            d: "M7.75 12a1.75 1.75 0 1 1-3.5 0a1.75 1.75 0 0 1 3.5 0zm6 0a1.75 1.75 0 1 1-3.5 0a1.75 1.75 0 0 1 3.5 0zM18 13.75a1.75 1.75 0 1 0 0-3.5a1.75 1.75 0 0 0 0 3.5z",
            fill: "currentColor"
        })], -1)]))
    }
};
export {
    a as M
};