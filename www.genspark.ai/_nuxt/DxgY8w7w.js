import {
    d as o,
    b as n,
    o as r
} from "./Cf0SOiw0.js";
const t = {
    width: "16",
    height: "16",
    viewBox: "0 0 16 16",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const s = {
    render: function(s, C) {
        return r(), o("svg", t, C[0] || (C[0] = [n("path", {
            d: "M14.8605 14.8566L11.8273 11.8234M11.8273 11.8234C12.3462 11.3046 12.7577 10.6886 13.0385 10.0107C13.3193 9.33284 13.4638 8.60628 13.4638 7.87254C13.4638 7.13879 13.3193 6.41223 13.0385 5.73434C12.7577 5.05645 12.3462 4.4405 11.8273 3.92166C11.3085 3.40283 10.6925 2.99126 10.0146 2.71047C9.33675 2.42968 8.61019 2.28516 7.87644 2.28516C7.1427 2.28516 6.41614 2.42968 5.73825 2.71047C5.06035 2.99126 4.4444 3.40283 3.92557 3.92166C2.87773 4.9695 2.28906 6.39067 2.28906 7.87254C2.28906 9.3544 2.87773 10.7756 3.92557 11.8234C4.97341 12.8713 6.39458 13.4599 7.87644 13.4599C9.35831 13.4599 10.7795 12.8713 11.8273 11.8234Z",
            stroke: "var(--iconColor,#999)",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }, null, -1)]))
    }
};
export {
    s as S
};