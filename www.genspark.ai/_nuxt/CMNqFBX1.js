import {
    d as t,
    o as e,
    b as o
} from "./Cf0SOiw0.js";
const s = {
    width: "210",
    height: "1",
    viewBox: "0 0 210 1",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const n = {
    render: function(n, r) {
        return e(), t("svg", s, r[0] || (r[0] = [o("path", {
            d: "M210 0.5L0 0.5",
            stroke: "url(#paint0_linear_1864_402)",
            "stroke-opacity": "0.6"
        }, null, -1), o("defs", null, [o("linearGradient", {
            id: "paint0_linear_1864_402",
            x1: "-0.1",
            y1: "-8.16562e+14",
            x2: "189.9",
            y2: "-8.16562e+14",
            gradientUnits: "userSpaceOnUse"
        }, [o("stop", {
            offset: "0.2",
            "stop-color": "white"
        }), o("stop", {
            offset: "1",
            "stop-color": "#909499"
        })])], -1)]))
    }
};
export {
    n as L
};