import {
    d as t,
    o as i,
    b as e
} from "./Cf0SOiw0.js";
const o = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const r = {
    render: function(r, n) {
        return i(), t("svg", o, n[0] || (n[0] = [e("g", {
            id: "ArrowBackIosNewRound 2",
            "clip-path": "url(#clip0_155_1955)"
        }, [e("path", {
            id: "Rectangle 3468630",
            d: "M12.9297 17.1406L5.85862 10.0696L12.9297 2.99849",
            stroke: "#232425",
            "stroke-width": "2",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        })], -1), e("defs", null, [e("clipPath", {
            id: "clip0_155_1955"
        }, [e("rect", {
            width: "20",
            height: "20",
            fill: "white"
        })])], -1)]))
    }
};
export {
    r as B
};