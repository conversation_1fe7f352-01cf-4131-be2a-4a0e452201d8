const t = (t, s = !0) => {
        let n = t.startsWith("/") ? t.substring(1) : t;
        return s && n.length > 0 && !n.endsWith("/") && (n += "/"), n
    },
    s = t => t.startsWith("/") ? t : `/${t}`,
    n = s => `/${t(s,!1).split("/").map((t=>encodeURIComponent(t))).join("/")}`,
    e = t => t.endsWith("/") ? t : `${t}/`,
    a = t => e(t);
export {
    a,
    s as b,
    e as c,
    n as e,
    t as n
};