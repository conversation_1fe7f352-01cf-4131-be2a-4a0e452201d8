import {
    u as e
} from "./C1MFiWVQ.js";
import {
    d as t,
    o as a,
    b as o,
    _ as s,
    s as r,
    r as l,
    c as i,
    h as n,
    v as c,
    x as d,
    t as C,
    y as p,
    f as u,
    q as v,
    e as f,
    a as y,
    n as m,
    C as g,
    U as b
} from "./Cf0SOiw0.js";
import {
    u as h
} from "./B6noBY_5.js";
const w = {
    width: "57",
    height: "57",
    viewBox: "0 0 57 57",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const _ = {
        render: function(e, s) {
            return a(), t("svg", w, s[0] || (s[0] = [o("g", {
                "clip-path": "url(#clip0_4409_47194)"
            }, [o("path", {
                d: "M47.5 24.5359C50.1667 26.0755 50.1667 29.9245 47.5 31.4641L19.75 47.4856C17.0833 49.0252 13.75 47.1007 13.75 44.0215L13.75 11.9785C13.75 8.89934 17.0833 6.97483 19.75 8.51443L47.5 24.5359Z",
                fill: "currentCulor"
            })], -1), o("defs", null, [o("clipPath", {
                id: "clip0_4409_47194"
            }, [o("rect", {
                width: "50",
                height: "50",
                fill: "currentCulor",
                transform: "translate(3.5 3.5)"
            })])], -1)]))
        }
    },
    k = {
        width: "33",
        height: "32",
        viewBox: "0 0 33 32",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const L = {
        render: function(e, s) {
            return a(), t("svg", k, s[0] || (s[0] = [o("path", {
                d: "M12.3431 21.1921C11.7441 21.1921 11.3335 20.7883 11.3335 20.1758V13.2234H11.2932L9.87305 14.2128C9.6846 14.3474 9.54327 14.3945 9.33463 14.3945C8.92408 14.3945 8.62121 14.0983 8.62121 13.6676C8.62121 13.358 8.74236 13.1292 9.05868 12.9071L10.9836 11.5745C11.4547 11.2514 11.7777 11.1908 12.195 11.1908C12.9152 11.1908 13.3459 11.6283 13.3459 12.3283V20.1758C13.3459 20.7883 12.9421 21.1921 12.3431 21.1921ZM19.2619 21.2662C17.7206 21.2662 16.3746 20.546 15.89 19.5163C15.789 19.3009 15.7284 19.0923 15.7284 18.85C15.7284 18.3317 16.0515 18.0221 16.5765 18.0221C16.9803 18.0221 17.236 18.1635 17.4649 18.5269C17.8149 19.2269 18.3869 19.6509 19.2619 19.6509C20.3185 19.6509 21.0791 18.8903 21.0791 17.8404C21.0791 16.8174 20.3253 16.0905 19.2686 16.0905C18.6898 16.0905 18.2052 16.3395 17.862 16.6693C17.4649 17.0395 17.263 17.1405 16.812 17.1405C16.1457 17.1405 15.8294 16.6761 15.8563 16.1309C15.8563 16.104 15.8563 16.0838 15.8631 16.0501L16.1457 12.4898C16.213 11.6687 16.637 11.3389 17.4245 11.3389H21.6848C22.1896 11.3389 22.5194 11.6552 22.5194 12.1465C22.5194 12.6379 22.1963 12.9542 21.6848 12.9542H17.8485L17.6197 15.6531H17.6601C18.0571 15.0271 18.8513 14.6368 19.8407 14.6368C21.6983 14.6368 23.0174 15.9425 23.0174 17.7933C23.0174 19.8662 21.4762 21.2662 19.2619 21.2662Z",
                fill: "currentColor"
            }, null, -1), o("path", {
                d: "M16.3332 29.1016C14.568 29.1016 12.9115 28.7669 11.3638 28.0977C9.81608 27.4367 8.4566 26.5207 7.28536 25.3494C6.11412 24.1782 5.19386 22.8187 4.52458 21.271C3.86366 19.7233 3.5332 18.0668 3.5332 16.3016C3.5332 14.871 3.75072 13.5157 4.18575 12.2357C4.62915 10.9473 5.24824 9.7719 6.04301 8.70942C6.84614 7.63857 7.78732 6.71412 8.86654 5.93609C9.14262 5.71857 9.43543 5.63909 9.74497 5.69765C10.0545 5.75622 10.2888 5.91099 10.4477 6.16197C10.6067 6.42968 10.6485 6.70158 10.5732 6.97765C10.5063 7.24537 10.3431 7.47543 10.0838 7.66785C9.19699 8.30367 8.42314 9.06916 7.76222 9.96432C7.10131 10.8511 6.5868 11.8299 6.21869 12.9008C5.85059 13.9716 5.66654 15.1052 5.66654 16.3016C5.66654 17.774 5.94261 19.1544 6.49477 20.4428C7.04693 21.7311 7.81242 22.8647 8.79124 23.8435C9.77007 24.8224 10.9037 25.5878 12.192 26.14C13.4804 26.6922 14.8608 26.9682 16.3332 26.9682C17.8056 26.9682 19.186 26.6922 20.4744 26.14C21.7627 25.5878 22.8963 24.8224 23.8752 23.8435C24.854 22.8647 25.6195 21.7311 26.1716 20.4428C26.7238 19.1544 26.9999 17.774 26.9999 16.3016C26.9999 15.0299 26.7907 13.8252 26.3724 12.6875C25.9625 11.5497 25.3852 10.5248 24.6407 9.61295C23.8961 8.69269 23.0218 7.91465 22.0179 7.27883C21.0224 6.64301 19.9348 6.18707 18.7552 5.91099V7.71805C18.7552 8.01086 18.6924 8.23256 18.5669 8.38314C18.4498 8.53373 18.2909 8.60903 18.0901 8.60903C17.8976 8.60066 17.6843 8.517 17.4501 8.35805L13.4469 5.54707C13.1625 5.34628 13.0161 5.11203 13.0077 4.84432C13.0077 4.57661 13.1541 4.33818 13.4469 4.12903L17.4375 1.31805C17.6718 1.15909 17.8893 1.07543 18.0901 1.06707C18.2909 1.0587 18.4498 1.12981 18.5669 1.2804C18.6924 1.43099 18.7552 1.65687 18.7552 1.95805V3.74001C20.2192 4.01609 21.5787 4.5306 22.8336 5.28354C24.0969 6.02811 25.2012 6.96092 26.1465 8.08197C27.0919 9.20301 27.8239 10.4621 28.3426 11.8592C28.8697 13.2563 29.1332 14.7371 29.1332 16.3016C29.1332 18.0668 28.7986 19.7233 28.1293 21.271C27.4684 22.8187 26.5523 24.1782 25.381 25.3494C24.2098 26.5207 22.8503 27.4367 21.3026 28.0977C19.7549 28.7669 18.0984 29.1016 16.3332 29.1016Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    j = {
        width: "33",
        height: "32",
        viewBox: "0 0 33 32",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const I = {
        render: function(e, s) {
            return a(), t("svg", j, s[0] || (s[0] = [o("path", {
                d: "M12.6761 21.1921C12.0771 21.1921 11.6665 20.7883 11.6665 20.1758V13.2234H11.6262L10.2061 14.2128C10.0176 14.3474 9.87628 14.3945 9.66763 14.3945C9.25708 14.3945 8.95422 14.0983 8.95422 13.6676C8.95422 13.358 9.07536 13.1292 9.39169 12.9071L11.3166 11.5745C11.7877 11.2514 12.1107 11.1908 12.528 11.1908C13.2482 11.1908 13.6789 11.6283 13.6789 12.3283V20.1758C13.6789 20.7883 13.2751 21.1921 12.6761 21.1921ZM19.5949 21.2662C18.0536 21.2662 16.7076 20.546 16.223 19.5163C16.122 19.3009 16.0615 19.0923 16.0615 18.85C16.0615 18.3317 16.3845 18.0221 16.9095 18.0221C17.3133 18.0221 17.5691 18.1635 17.7979 18.5269C18.1479 19.2269 18.7199 19.6509 19.5949 19.6509C20.6515 19.6509 21.4121 18.8903 21.4121 17.8404C21.4121 16.8174 20.6583 16.0905 19.6016 16.0905C19.0228 16.0905 18.5382 16.3395 18.195 16.6693C17.7979 17.0395 17.596 17.1405 17.145 17.1405C16.4787 17.1405 16.1624 16.6761 16.1893 16.1309C16.1893 16.104 16.1893 16.0838 16.1961 16.0501L16.4787 12.4898C16.546 11.6687 16.9701 11.3389 17.7575 11.3389H22.0178C22.5226 11.3389 22.8524 11.6552 22.8524 12.1465C22.8524 12.6379 22.5293 12.9542 22.0178 12.9542H18.1815L17.9527 15.6531H17.9931C18.3902 15.0271 19.1843 14.6368 20.1737 14.6368C22.0313 14.6368 23.3504 15.9425 23.3504 17.7933C23.3504 19.8662 21.8092 21.2662 19.5949 21.2662Z",
                fill: "currentColor"
            }, null, -1), o("path", {
                d: "M16.6668 29.1016C18.432 29.1016 20.0885 28.7669 21.6362 28.0977C23.1839 27.4367 24.5434 26.5207 25.7146 25.3494C26.8859 24.1782 27.8061 22.8187 28.4754 21.271C29.1363 19.7233 29.4668 18.0668 29.4668 16.3016C29.4668 14.871 29.2493 13.5157 28.8142 12.2357C28.3708 10.9473 27.7518 9.7719 26.957 8.70942C26.1539 7.63857 25.2127 6.71412 24.1335 5.93609C23.8574 5.71857 23.5646 5.63909 23.255 5.69765C22.9455 5.75622 22.7112 5.91099 22.5523 6.16197C22.3933 6.42968 22.3515 6.70158 22.4268 6.97765C22.4937 7.24537 22.6569 7.47543 22.9162 7.66785C23.803 8.30367 24.5769 9.06916 25.2378 9.96432C25.8987 10.8511 26.4132 11.8299 26.7813 12.9008C27.1494 13.9716 27.3335 15.1052 27.3335 16.3016C27.3335 17.774 27.0574 19.1544 26.5052 20.4428C25.9531 21.7311 25.1876 22.8647 24.2088 23.8435C23.2299 24.8224 22.0963 25.5878 20.808 26.14C19.5196 26.6922 18.1392 26.9682 16.6668 26.9682C15.1944 26.9682 13.814 26.6922 12.5256 26.14C11.2373 25.5878 10.1037 24.8224 9.12484 23.8435C8.14601 22.8647 7.38052 21.7311 6.82836 20.4428C6.27621 19.1544 6.00013 17.774 6.00013 16.3016C6.00013 15.0299 6.20928 13.8252 6.62758 12.6875C7.03752 11.5497 7.61477 10.5248 8.35935 9.61295C9.10392 8.69269 9.97817 7.91465 10.9821 7.27883C11.9776 6.64301 13.0652 6.18707 14.2448 5.91099V7.71805C14.2448 8.01086 14.3076 8.23256 14.4331 8.38314C14.5502 8.53373 14.7091 8.60903 14.9099 8.60903C15.1024 8.60066 15.3157 8.517 15.5499 8.35805L19.5531 5.54707C19.8375 5.34628 19.9839 5.11203 19.9923 4.84432C19.9923 4.57661 19.8459 4.33818 19.5531 4.12903L15.5625 1.31805C15.3282 1.15909 15.1107 1.07543 14.9099 1.06707C14.7091 1.0587 14.5502 1.12981 14.4331 1.2804C14.3076 1.43099 14.2448 1.65687 14.2448 1.95805V3.74001C12.7808 4.01609 11.4213 4.5306 10.1664 5.28354C8.90314 6.02811 7.79882 6.96092 6.85346 8.08197C5.9081 9.20301 5.17608 10.4621 4.65738 11.8592C4.13033 13.2563 3.8668 14.7371 3.8668 16.3016C3.8668 18.0668 4.20144 19.7233 4.87072 21.271C5.53163 22.8187 6.44771 24.1782 7.61895 25.3494C8.79019 26.5207 10.1497 27.4367 11.6974 28.0977C13.2451 28.7669 14.9016 29.1016 16.6668 29.1016Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    x = {
        key: 0,
        class: "cover-container with-image"
    },
    F = ["src"],
    H = {
        key: 1,
        class: "cover-container no-image"
    },
    U = {
        class: "content-overlay"
    },
    B = {
        class: "title-section"
    },
    M = {
        class: "podcast-title"
    },
    V = {
        class: "status-indicator"
    },
    P = {
        key: 0,
        class: "status-text"
    },
    Z = {
        key: 1,
        class: "status-text"
    },
    R = {
        key: 2,
        class: "status-text error"
    },
    S = {
        class: "player-controls"
    },
    T = {
        class: "progress-section"
    },
    E = {
        class: "time-display"
    },
    $ = {
        class: "progress-bar"
    },
    q = {
        key: 0,
        class: "buffering-indicator"
    },
    D = {
        class: "time-display"
    },
    N = {
        class: "control-buttons"
    },
    O = ["title"],
    X = ["title"],
    z = ["title", "disabled"],
    A = {
        key: 0,
        class: "loading-spinner"
    },
    G = {
        key: 3,
        viewBox: "0 0 24 24",
        class: "control-icon"
    },
    J = ["title"],
    K = ["title", "disabled"],
    Q = {
        key: 0,
        viewBox: "0 0 25 25",
        class: "control-icon"
    },
    W = {
        key: 1,
        viewBox: "0 0 25 25",
        class: "control-icon"
    },
    Y = s({
        __name: "PodcastPlayer",
        props: {
            audioUrl: {
                type: String,
                required: !0
            },
            title: {
                type: String,
                default: ""
            },
            coverImage: {
                type: String,
                default: null
            },
            aspectRatio: {
                type: String,
                default: "1:1"
            },
            autoplay: {
                type: Boolean,
                default: !1
            },
            projectId: {
                type: String,
                default: null
            },
            initialDuration: {
                type: Number,
                default: 0
            },
            disableFavorite: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["play", "pause", "timeupdate", "ended", "favorite"],
        setup(s, {
            emit: w
        }) {
            const k = s,
                j = w,
                Y = h(),
                {
                    t: ee
                } = r(),
                te = ee,
                {
                    togglePlay: ae,
                    setPlaybackSpeed: oe,
                    rewind: se,
                    forward: re,
                    seekTo: le,
                    formatTime: ie,
                    isCurrentPodcast: ne,
                    addListener: ce,
                    cleanup: de,
                    isPlaying: Ce,
                    currentTime: pe,
                    duration: ue,
                    progress: ve,
                    isLoading: fe,
                    isBuffering: ye,
                    hasError: me,
                    playbackSpeed: ge
                } = e(),
                be = l(!1),
                he = i((() => {
                    if (k.aspectRatio.includes(":")) {
                        const [e, t] = k.aspectRatio.split(":").map(Number);
                        return `${e} / ${t}`
                    }
                    return k.aspectRatio
                })),
                we = i((() => ne(k.projectId || k.audioUrl) && fe.value)),
                _e = i((() => ne(k.projectId || k.audioUrl) && ye.value)),
                ke = i((() => ne(k.projectId || k.audioUrl) && me.value)),
                Le = i((() => ne(k.projectId || k.audioUrl) && Ce.value)),
                je = i((() => ne(k.projectId || k.audioUrl) ? ve.value : 0)),
                Ie = i((() => ne(k.projectId || k.audioUrl) ? pe.value : 0)),
                xe = i((() => je.value)),
                Fe = () => {
                    se(15)
                },
                He = () => {
                    re(15)
                },
                Ue = i((() => ue.value > 0 ? ue.value : k.initialDuration)),
                Be = () => {},
                Me = async () => {
                    var e, t;
                    if (k.projectId && !k.disableFavorite) try {
                        const a = await fetch(`/api/user/project_bookmark?project_id=${k.projectId}`);
                        if (!a.ok) throw new Error(`HTTP error! status: ${a.status}`);
                        const o = await a.json();
                        if (0 !== o.status) throw new Error(o.message || "Failed to fetch bookmark status");
                        (null == (t = null == (e = o.data) ? void 0 : e.project_list) ? void 0 : t.length) > 0 ? be.value = !0 : be.value = !1
                    } catch (a) {
                        be.value = !1
                    }
                },
                Ve = () => {
                    if (ke.value) return;
                    const e = {
                        id: k.projectId || k.audioUrl,
                        audio_url: k.audioUrl,
                        duration: 0,
                        title: k.title,
                        image: k.coverImage
                    };
                    ae(e)
                },
                Pe = () => {
                    const e = [1, 1.25, 1.5, 2],
                        t = (e.indexOf(ge.value) + 1) % e.length;
                    oe(e[t])
                },
                Ze = async () => {
                    if (k.disableFavorite) return;
                    const e = be.value,
                        t = !e;
                    be.value = t;
                    const a = Y.loading(ee(t ? "components.podcast.player.adding_to_favorites" : "components.podcast.player.removing_from_favorites"));
                    try {
                        const e = await g.postRequest("/api/user/project_bookmark", {
                            project_id: k.projectId,
                            name: k.title,
                            is_deleted: !t,
                            source: "podcast_player"
                        });
                        if (a.destroy(), 0 !== e.status) throw new Error("Failed to toggle favorite");
                        Y.success(ee(t ? "components.podcast.player.added_to_favorites_success" : "components.podcast.player.removed_from_favorites_success")), j("favorite", t)
                    } catch (o) {
                        a.destroy(), be.value = e, Y.error(ee(t ? "components.podcast.player.failed_to_add_favorites" : "components.podcast.player.failed_to_remove_favorites"))
                    }
                },
                Re = e => {
                    if (!ne(k.projectId || k.audioUrl) || 0 === Ue.value) return;
                    const t = e.currentTarget.getBoundingClientRect(),
                        a = (e.clientX - t.left) / t.width * 100;
                    le(a)
                };
            let Se;
            return n((() => {
                k.disableFavorite || Me(), Se = ce((e => {
                    ne(k.projectId || k.audioUrl) && (j("timeupdate", e.currentTime), e.isPlaying ? j("play") : j("pause"))
                })), k.autoplay && Ve()
            })), c((() => k.audioUrl), ((e, t) => {
                if (e !== t) {
                    if (t && ne(k.projectId || t)) {
                        if (Le.value) {
                            const e = {
                                id: k.projectId || t,
                                audio_url: t,
                                duration: 0,
                                title: k.title,
                                image: k.coverImage
                            };
                            ae(e)
                        }
                        de()
                    }
                    k.disableFavorite || Me(), k.autoplay && e && b((() => {
                        Ve()
                    }))
                }
            }), {
                immediate: !1
            }), d((() => {
                Se && Se()
            })), (e, r) => (a(), t("div", {
                class: m(["podcast-player", {
                    "no-cover": !s.coverImage
                }])
            }, [o("div", {
                class: "cover-section",
                style: v({
                    aspectRatio: he.value
                })
            }, [s.coverImage ? (a(), t("div", x, [o("img", {
                src: s.coverImage,
                class: "cover-image",
                onError: Be
            }, null, 40, F)])) : (a(), t("div", H)), o("div", U, [o("div", B, [o("h1", M, C(s.title || p(te)("components.podcast.player.ai_pods")), 1), o("div", V, [we.value ? (a(), t("span", P, C(p(te)("components.podcast.player.loading")), 1)) : _e.value ? (a(), t("span", Z, C(p(te)("components.podcast.player.buffering")), 1)) : ke.value ? (a(), t("span", R, C(p(te)("components.podcast.player.playback_error")), 1)) : u("", !0)])]), o("div", S, [o("div", T, [o("span", E, C(p(ie)(Ie.value)), 1), o("div", {
                class: "progress-bar-container",
                onClick: Re
            }, [o("div", $, [o("div", {
                class: "progress-fill",
                style: v({
                    width: xe.value + "%"
                })
            }, null, 4), _e.value ? (a(), t("div", q, r[1] || (r[1] = [o("div", {
                class: "buffering-dots"
            }, [o("span"), o("span"), o("span")], -1)]))) : u("", !0)])]), o("span", D, "-" + C(p(ie)(Ue.value - Ie.value)), 1)]), o("div", N, [o("button", {
                class: "control-btn speed-btn",
                onClick: Pe,
                title: `${p(te)("components.podcast.player.playback_speed")} ${p(ge)}x`
            }, C(p(ge)) + "x ", 9, O), o("button", {
                class: "control-btn rewind-btn",
                onClick: Fe,
                title: p(te)("components.podcast.player.rewind_15s")
            }, [f(p(L), {
                class: "control-icon"
            })], 8, X), o("button", {
                class: "control-btn play-btn",
                onClick: Ve,
                title: Le.value ? p(te)("components.podcast.player.pause") : p(te)("components.podcast.player.play"),
                disabled: we.value || ke.value
            }, [we.value || _e.value ? (a(), t("div", A, r[2] || (r[2] = [o("svg", {
                viewBox: "0 0 24 24",
                class: "control-icon spinner"
            }, [o("circle", {
                cx: "12",
                cy: "12",
                r: "10",
                stroke: "currentColor",
                "stroke-width": "2",
                fill: "none",
                "stroke-linecap": "round",
                "stroke-dasharray": "31.416",
                "stroke-dashoffset": "31.416"
            })], -1)]))) : ke.value ? (a(), y(p(_), {
                key: 1,
                class: "control-icon"
            })) : Le.value ? (a(), t("svg", G, r[3] || (r[3] = [o("path", {
                d: "M14,19H18V5H14M6,19H10V5H6V19Z"
            }, null, -1)]))) : (a(), y(p(_), {
                key: 2,
                class: "control-icon"
            }))], 8, z), o("button", {
                class: "control-btn forward-btn",
                onClick: He,
                title: p(te)("components.podcast.player.forward_15s")
            }, [f(p(I), {
                class: "control-icon"
            })], 8, J), o("button", {
                class: m(["control-btn star-btn", {
                    disabled: s.disableFavorite
                }]),
                onClick: r[0] || (r[0] = e => s.disableFavorite ? void 0 : Ze),
                title: s.disableFavorite ? "" : be.value ? p(te)("components.podcast.player.remove_from_favorites") : p(te)("components.podcast.player.add_to_favorites"),
                disabled: s.disableFavorite
            }, [be.value ? (a(), t("svg", W, r[5] || (r[5] = [o("path", {
                d: "M13.1778 3.65944L14.9296 7.33061C15.2576 8.018 15.9111 8.4928 16.6662 8.59234L20.699 9.12394C21.3245 9.20638 21.5748 9.97714 21.1173 10.4114L18.1671 13.212C17.6148 13.7363 17.3652 14.5046 17.5038 15.2535L18.2445 19.2531C18.3593 19.8735 17.7036 20.3498 17.1492 20.0489L13.5742 18.1085C12.9048 17.7452 12.097 17.7452 11.4276 18.1085L7.8525 20.0489C7.29806 20.3498 6.64241 19.8735 6.75728 19.2531L7.4979 15.2535C7.63658 14.5046 7.38696 13.7363 6.83457 13.212L3.88442 10.4114C3.42689 9.97714 3.67733 9.20638 4.30275 9.12394L8.33559 8.59234C9.09069 8.4928 9.74418 8.018 10.0722 7.33061L11.824 3.65944C12.0957 3.09008 12.9061 3.09008 13.1778 3.65944Z",
                fill: "currentColor"
            }, null, -1)]))) : (a(), t("svg", Q, r[4] || (r[4] = [o("path", {
                d: "M13.1778 3.65944L14.9296 7.33061C15.2576 8.018 15.9111 8.4928 16.6662 8.59234L20.699 9.12394C21.3245 9.20638 21.5748 9.97714 21.1173 10.4114L18.1671 13.212C17.6148 13.7363 17.3652 14.5046 17.5038 15.2535L18.2445 19.2531C18.3593 19.8735 17.7036 20.3498 17.1492 20.0489L13.5742 18.1085C12.9048 17.7452 12.097 17.7452 11.4276 18.1085L7.8525 20.0489C7.29806 20.3498 6.64241 19.8735 6.75728 19.2531L7.4979 15.2535C7.63658 14.5046 7.38696 13.7363 6.83457 13.212L3.88442 10.4114C3.42689 9.97714 3.67733 9.20638 4.30275 9.12394L8.33559 8.59234C9.09069 8.4928 9.74418 8.018 10.0722 7.33061L11.824 3.65944C12.0957 3.09008 12.9061 3.09008 13.1778 3.65944Z",
                stroke: "currentColor",
                "stroke-width": "1.5",
                fill: "none"
            }, null, -1)])))], 10, K)])])])], 4)], 2))
        }
    }, [
        ["__scopeId", "data-v-6973b163"]
    ]);
export {
    L as B, I as F, Y as P, _ as a
};