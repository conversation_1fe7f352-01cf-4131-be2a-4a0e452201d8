.modal-container[data-v-164828a5] {
    overflow: hidden
}

.modal-container[data-v-164828a5],
.overlay[data-v-164828a5] {
    height: 100vh;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 999
}

.overlay[data-v-164828a5] {
    background-color: #000c
}

.image-display[data-v-164828a5] {
    border-radius: 16px;
    display: flex;
    height: calc(100vh - 40px);
    left: 80px;
    overflow: hidden;
    position: fixed;
    top: 20px;
    width: calc(100% - 160px);
    z-index: 1000
}

@media (max-width:1220px) {
    .image-display[data-v-164828a5] {
        align-items: center;
        border-radius: 0;
        flex-direction: column;
        height: 100%;
        left: 0;
        overflow-y: scroll;
        position: fixed;
        top: 0;
        width: 100%;
        -webkit-overflow-scrolling: touch;
        background-color: #fff;
        display: flex
    }
}

.image-left[data-v-164828a5] {
    align-items: center;
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    flex: 1;
    justify-content: center;
    position: relative
}

@media (max-width:1220px) {
    .image-left[data-v-164828a5] {
        background-color: #fafafa;
        flex: none;
        max-height: 50%;
        max-width: 680px;
        padding: 0;
        width: 100%
    }
}

.image-left img[data-v-164828a5] {
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100%
}

.image-right[data-v-164828a5] {
    background-color: #f5f5f5;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-width: 320px;
    padding: 16px;
    width: 30%
}

@media (max-width:1220px) {
    .image-right[data-v-164828a5] {
        background-color: #fff;
        display: flex;
        flex: 1;
        max-width: 680px;
        padding: 24px;
        width: 100%
    }
}

.image-right-top[data-v-164828a5] {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: flex-start
}

@media (max-width:1220px) {
    .image-right-top[data-v-164828a5] {
        flex-direction: column
    }
}

.image-right-bottom[data-v-164828a5] {
    margin-bottom: 60px;
    width: 100%
}

.remix-button[data-v-164828a5] {
    align-items: center;
    background-color: #232425;
    border-radius: 20px;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-size: 16px;
    gap: 8px;
    justify-content: center;
    line-height: 1.5em;
    padding: 6px 0;
    transition: all .3s ease;
    width: 100%
}

.remix-button[data-v-164828a5]:hover {
    transform: scale(1.05)
}

.close-button-wrapper[data-v-164828a5] {
    position: fixed;
    right: 20px;
    top: 20px;
    z-index: 1001
}

@media (max-width:1220px) {
    .close-button-wrapper[data-v-164828a5] {
        display: none;
        right: 10px;
        top: 10px
    }
}

.close-button-wrapper .icon[data-v-164828a5] {
    cursor: pointer;
    height: 32px;
    transition: all .3s ease;
    width: 32px
}

.close-button-wrapper .icon[data-v-164828a5]:hover {
    transform: scale(1.1)
}

.prompt-wrapper[data-v-164828a5] {
    width: 100%
}

.prompt-wrapper p[data-v-164828a5] {
    color: #232425;
    font-size: 14px;
    line-height: 1.5em;
    margin-bottom: 12px;
    margin-top: 60px;
    text-align: left
}

@media (max-width:1220px) {
    .prompt-wrapper p[data-v-164828a5] {
        margin-top: 12px
    }
}

.action-buttons[data-v-164828a5] {
    display: flex;
    flex-direction: row;
    gap: 12px;
    justify-content: flex-start;
    margin-top: 20px;
    width: 100%
}

@media (max-width:1220px) {
    .action-buttons[data-v-164828a5] {
        gap: 24px;
        margin-bottom: 8px;
        margin-top: 0;
        order: -1
    }
}

.button-wrapper[data-v-164828a5] {
    align-items: center;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    height: 24px;
    justify-content: center;
    transition: all .3s ease;
    width: 24px
}

.button-wrapper[data-v-164828a5]:hover {
    background-color: #e5e5e5;
    transform: scale(1.1)
}

.button-wrapper .icon[data-v-164828a5] {
    height: 20px;
    width: 20px
}

.info[data-v-164828a5] {
    color: #909499;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    font-size: 14px;
    gap: 8px;
    line-height: 1.5em;
    margin-bottom: 20px;
    width: 100%
}

.info-wrapper[data-v-164828a5] {
    background-color: #e6e6e6a6;
    border-radius: 8px;
    flex-direction: row;
    gap: 6px;
    height: 24px;
    padding: 0 8px
}

.info-wrapper[data-v-164828a5],
.model-icon[data-v-164828a5] {
    align-items: center;
    display: flex;
    justify-content: center
}

.model-icon[data-v-164828a5] {
    background-color: #fff;
    border-radius: 50%;
    height: 16px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 16px
}

.mobile-title-bar[data-v-164828a5] {
    align-items: center;
    background-color: #fff;
    box-sizing: border-box;
    display: none;
    min-height: 50px;
    padding: 0 16px;
    position: relative;
    width: 100%
}

@media (max-width:1220px) {
    .mobile-title-bar[data-v-164828a5] {
        display: flex
    }
}

.back-button[data-v-164828a5] {
    align-items: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    position: relative
}

.back-button[data-v-164828a5],
.back-button .icon[data-v-164828a5] {
    height: 20px;
    width: 20px
}

.clickable-image[data-v-164828a5] {
    cursor: pointer
}

@media (prefers-color-scheme:dark) {
    .image-display[data-v-164828a5],
    .image-left[data-v-164828a5] {
        background-color: #1a1a1a
    }
    .image-right[data-v-164828a5],
    .image-right-top[data-v-164828a5] {
        background-color: #242424
    }
    .prompt-wrapper p[data-v-164828a5] {
        color: #e5e5e5
    }
    .info[data-v-164828a5] {
        color: #909499
    }
    .info-wrapper[data-v-164828a5] {
        background-color: #ffffff1a
    }
    .button-wrapper[data-v-164828a5]:hover {
        background-color: #333
    }
    .mobile-title-bar[data-v-164828a5] {
        background-color: #1a1a1a
    }
    .button-wrapper .icon[data-v-164828a5] {
        color: #fff
    }
    .remix-button[data-v-164828a5] {
        background-color: #fff;
        color: #232425
    }
    .remix-button[data-v-164828a5] svg path {
        fill: #232425
    }
}

.nav-button[data-v-164828a5] {
    background: #00000080;
    border-radius: 4px;
    color: #fff;
    cursor: pointer;
    padding: 20px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.prev[data-v-164828a5] {
    left: 20px
}

.next[data-v-164828a5] {
    right: 20px
}

.nav-button[data-v-164828a5]:hover {
    background: #000000b3
}