import {
    O as t
} from "./DnZj1005.js";
import {
    _ as e,
    r as n,
    v as o,
    C as r,
    d as i,
    f as s,
    b as l,
    t as a,
    e as c,
    q as u,
    E as p,
    o as f
} from "./Cf0SOiw0.js";
const d = {
        class: "favicon"
    },
    k = ["src"],
    m = {
        class: "title"
    },
    v = {
        class: "outlink"
    };
const g = e({
    name: "CitationPopover",
    components: {
        OutlinkIconSvg: t
    },
    props: {
        citationLink: {
            type: Object,
            required: !1,
            default: null
        }
    },
    setup(t, {
        emit: e,
        expose: i
    }) {
        const s = n(null),
            l = n(null);
        let a = null;
        const c = t => {
            if (clearTimeout(a), !t || "A" != t.tagName || !t.classList.contains("digital-citation-link")) return void(a = setTimeout((() => {
                s.value = null
            }), 300));
            l.value = t, t.getBoundingClientRect();
            let e = t.offsetTop,
                n = t.offsetLeft;
            s.value = {
                href: t.href,
                title: t.title ? t.title : r.getHost(t.href),
                top: e,
                left: n
            }
        };
        o((() => t.citationLink), (t => {
            c(t)
        })), c(t.citationLink);
        return {
            windowopen: r.windowopen,
            mouseenter: () => {
                clearTimeout(a)
            },
            mouseleave: () => {
                clearTimeout(a), a = setTimeout((() => {
                    s.value = null
                }), 300)
            },
            getFavIcon: t => `https://t3.gstatic.com/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&url=${encodeURIComponent(t)}&size=64`,
            link: s,
            linkElem: l
        }
    }
}, [
    ["render", function(t, e, n, o, r, g) {
        const h = p("OutlinkIconSvg");
        return o.link ? (f(), i("div", {
            key: 0,
            class: "citation-popover",
            style: u({
                left: o.link.left + "px",
                top: o.link.top + "px"
            }),
            onMouseenter: e[1] || (e[1] = (...t) => o.mouseenter && o.mouseenter(...t)),
            onMouseleave: e[2] || (e[2] = (...t) => o.mouseleave && o.mouseleave(...t))
        }, [l("div", {
            class: "citation-popover-content",
            onClick: e[0] || (e[0] = t => o.windowopen(o.link.href))
        }, [l("div", d, [l("img", {
            src: o.getFavIcon(o.link.href),
            alt: "favicon"
        }, null, 8, k)]), l("div", m, a(o.link.title), 1), l("div", v, [c(h)])])], 36)) : s("", !0)
    }],
    ["__scopeId", "data-v-b75b20fa"]
]);

function h(t) {
    const e = t.renderer.rules.text || function(t, e, n, o, r) {
        return r.renderToken(t, e, n)
    };
    t.renderer.rules.text = function(t, n, o, r, i) {
        if (t.length >= n + 4 && "link_open" === t[n + 1].type && "text" === t[n + 2].type && "link_close" === t[n + 3].type) {
            const e = t[n + 2].content;
            if (e && /^[0-9]+$/.test(e.trim())) {
                if (t[n].content) {
                    const e = t[n].content;
                    if (e.length > 0) {
                        "[" == e[e.length - 1] && (t[n].content = e.slice(0, -1))
                    }
                }
                if (t[n + 4] && "text" == t[n + 4].type) {
                    const e = t[n + 4].content;
                    if (e.length > 0) {
                        "]" == e[0] && (t[n + 4].content = e.slice(1))
                    }
                }
                t[n + 1].attrPush(["class", "digital-citation-link"])
            }
        }
        return e(t, n, o, r, i)
    };
    const n = t.renderer.rules.link_open || function(t, e, n, o, r) {
        return r.renderToken(t, e, n)
    };
    t.renderer.rules.link_open = function(t, e, o, r, i) {
        if (t.length >= e + 3 && "link_open" === t[e].type && "text" === t[e + 1].type && "link_close" === t[e + 2].type) {
            const n = t[e + 1].content;
            if (n && /^[0-9]+$/.test(n.trim())) {
                const n = t[e].attrIndex("class");
                n < 0 ? t[e].attrPush(["class", "digital-citation-link"]) : t[e].attrs[n][1] = "digital-citation-link"
            }
        }
        return n(t, e, o, r, i)
    }
}

function _(t) {
    const e = t.renderer.rules.link_open || function(t, e, n, o, r) {
        return r.renderToken(t, e, n)
    };
    t.renderer.rules.link_open = function(t, n, o, r, i) {
        const s = t[n].attrIndex("target");
        let l = !1;
        const a = t[n].attrIndex("href");
        if (a >= 0) {
            t[n].attrs[a][1].startsWith("prompt://") && (l = !0)
        }
        return l || (s < 0 ? t[n].attrPush(["target", "_blank"]) : t[n].attrs[s][1] = "_blank", t[n].attrPush(["rel", "noopener noreferrer"])), e(t, n, o, r, i)
    }
}

function x(t) {
    const e = t.renderer.rules.link_open || function(t, e, n, o, r) {
        return r.renderToken(t, e, n)
    };
    t.renderer.rules.link_open = function(t, n, o, r, i) {
        const s = t[n],
            l = s.attrIndex("href");
        if (l >= 0) {
            if (s.attrs[l][1].startsWith("prompt://")) {
                const t = s.attrIndex("class");
                t < 0 ? s.attrPush(["class", "-md-ext-promptlink"]) : s.attrs[t][1] += " -md-ext-promptlink"
            }
        }
        return e(t, n, o, r, i)
    }
}
export {
    g as _, _ as l, h as m, x as p
};