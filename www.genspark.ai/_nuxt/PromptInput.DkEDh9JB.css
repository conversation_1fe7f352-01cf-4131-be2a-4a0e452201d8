.calculator[data-v-48409492] {
    box-sizing: border-box;
    max-height: calc(100vh - 200px);
    overflow: hidden;
    position: absolute;
    visibility: hidden;
    width: 100%
}

.hidden[data-v-48409492] {
    display: none
}

.recent_queries[data-v-48409492] {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px 16px 0
}

.recent_queries .title[data-v-48409492] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between
}

.recent_queries .title .delete[data-v-48409492] {
    cursor: pointer;
    height: 16px;
    width: 16px
}

.menu .delete[data-v-48409492] {
    color: #ff2525
}

.menu .button[data-v-48409492] {
    cursor: pointer
}

.suggestions .items[data-v-48409492] {
    display: flex;
    flex-wrap: wrap;
    width: 100%
}

.recent_queries .items[data-v-48409492] {
    gap: 8px
}

.suggestions .items .recent_query[data-v-48409492] {
    align-items: center;
    background: #fafafa;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    max-width: 40%;
    overflow: hidden;
    padding: 4px 12px;
    text-overflow: ellipsis;
    white-space: nowrap
}

.suggestions .items .recent_query.selected[data-v-48409492] {
    background: #f5f5f5
}

.suggestions .items .more[data-v-48409492] {
    align-items: center;
    background: #fafafa;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    justify-content: center;
    line-height: 1.5;
    max-width: 40%;
    overflow: hidden;
    padding: 4px 12px;
    text-overflow: ellipsis;
    white-space: nowrap
}

.suggestions .items .more .icon[data-v-48409492] {
    display: flex;
    height: 12px;
    transform: rotate(270deg);
    width: 12px
}

.suggestions .items .more .icon.less[data-v-48409492] {
    transform: rotate(90deg)
}

@media (hover:hover) {
    .suggestions .items .recent_query[data-v-48409492]:hover {
        background: #f5f5f5
    }
}

.suggestions .title[data-v-48409492] {
    color: #606366;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5
}

.suggestions .tags[data-v-48409492] {
    padding: 16px 16px 0
}

.suggestions .tags .tag[data-v-48409492] {
    background: #fafafa
}

.suggestions .tags .items[data-v-48409492] {
    gap: 8px;
    max-height: 34px;
    overflow: hidden
}

.suggestions .autopilots[data-v-48409492] {
    box-sizing: border-box;
    -moz-column-gap: 0;
    column-gap: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 16px 10px 0;
    row-gap: 12px
}

.suggestions .autopilots .title[data-v-48409492] {
    padding: 0 6px
}

.suggestions .autopilots .items[data-v-48409492] {
    margin-top: -12px
}

.suggestions .autopilot[data-v-48409492] {
    box-sizing: border-box;
    color: #606366;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 6px;
    line-height: 21px;
    margin-top: 12px;
    padding: 0 6px;
    width: 50%
}

.suggestions .autopilot.disabled[data-v-48409492] {
    color: #606366;
    opacity: .65
}

.suggestions .autopilot-inner[data-v-48409492] {
    align-items: center;
    background: #fafafa;
    border-radius: 12px;
    gap: 8px;
    justify-content: center;
    padding: 16px 0;
    text-align: center;
    width: 100%
}

.suggestions .autopilot-inner[data-v-48409492],
.suggestions .autopilot-inner .text[data-v-48409492] {
    display: flex;
    flex-direction: row
}

.suggestions .autopilot-inner .desc[data-v-48409492] {
    font-weight: 400
}

.suggestions .selected .autopilot-inner[data-v-48409492] {
    background: #f5f5f5
}

@media (hover:hover) {
    .suggestions .autopilot-inner[data-v-48409492]:hover {
        background: #f5f5f5
    }
}

.suggestions .autopilot-inner .icon[data-v-48409492] {
    display: flex;
    height: 24px;
    width: 24px
}

.suggestions .suggestion.recent .text[data-v-48409492] {
    color: #0b5cb9
}

.suggestions .suggestion .icon[data-v-48409492] {
    display: flex;
    flex-shrink: 0;
    height: 14px;
    width: 14px
}

.tags[data-v-48409492] {
    background: #fff;
    color: var(--word, #606366);
    display: flex;
    flex-wrap: wrap;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 24px;
    padding: 16px
}

.tags .hot[data-v-48409492] {
    color: #ff6767;
    display: inline-flex;
    height: 16px;
    width: 16px
}

.tags .tag[data-v-48409492] {
    align-items: center;
    background: #fff;
    border: 1px solid #f5f5f5;
    border-radius: 8px;
    cursor: pointer;
    display: inline-flex;
    gap: 4px;
    justify-content: center;
    padding: 4px 12px
}

.tags .tag.selected[data-v-48409492],
.tags .tag[data-v-48409492]:hover {
    background: #f5f5f5
}

.suggestions.empty[data-v-48409492] {
    background: #fff;
    padding-bottom: 16px
}

.suggestions.empty .suggestion[data-v-48409492]:first-child {
    padding-top: 8px
}

.suggestions.reverse[data-v-48409492] {
    border: 1px solid #dcdfe0;
    border-bottom: none;
    border-radius: 12px 12px 0 0;
    bottom: 100%;
    display: flex;
    flex-direction: column-reverse;
    top: unset
}

.suggestions[data-v-48409492] {
    background: #fff;
    border: 1px solid #0f7fff;
    border-radius: 0 0 12px 12px;
    border-top: none;
    box-sizing: border-box;
    left: 0;
    padding: 8px 0;
    position: absolute;
    top: 100%;
    width: 100%;
    z-index: 5
}

.suggestions .suggestion[data-v-48409492] {
    align-items: center;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    justify-content: flex-start;
    line-height: 21px;
    padding: 8px 18px
}

.suggestions .suggestion.selected[data-v-48409492],
.suggestions .suggestion[data-v-48409492]:hover {
    background: #0f79f214
}

.suggestions.reverse .suggestion[data-v-48409492] {
    font-size: 14px;
    line-height: 20px;
    padding: 8px 12px
}

@media (prefers-color-scheme:dark) {
    .suggestions[data-v-48409492] {
        background: #333;
        color: #fff
    }
    .suggestions .suggestion[data-v-48409492] {
        color: #eee
    }
    .suggestions .suggestion.selected[data-v-48409492],
    .suggestions .suggestion[data-v-48409492]:hover {
        background: #ffffff1a
    }
    .suggestions.empty[data-v-48409492] {
        background: #333
    }
    .suggestions.empty .suggestion[data-v-48409492] {
        color: #fff
    }
    .suggestions.empty .suggestion.selected[data-v-48409492],
    .suggestions.empty .suggestion[data-v-48409492]:hover {
        background: #ffffff1a
    }
    .suggestions[data-v-48409492] {
        border: 1px solid #e6e9eb40
    }
    .tags[data-v-48409492] {
        background: #333;
        color: #fff
    }
    .tags .tag[data-v-48409492] {
        background: #333;
        border: 1px solid #333
    }
    .tags .tag.selected[data-v-48409492],
    .tags .tag[data-v-48409492]:hover {
        background: #ffffff1a
    }
    .suggestions .items .recent_query[data-v-48409492] {
        background: #444;
        color: #eee
    }
    .suggestions .items .recent_query.selected[data-v-48409492] {
        background: #555
    }
    @media (hover:hover) {
        .suggestions .items .recent_query[data-v-48409492]:hover {
            background: #555
        }
    }
    .suggestions .items .more[data-v-48409492] {
        background: #444;
        color: #eee
    }
    .suggestions .title[data-v-48409492] {
        color: #eee
    }
    .suggestions .tags .tag[data-v-48409492] {
        background: #444;
        color: #eee
    }
    @media (hover:hover) {
        .suggestions .tags .tag[data-v-48409492]:hover {
            background: #555
        }
    }
    .suggestions .suggestion.recent .text[data-v-48409492] {
        color: #0f7fff
    }
    .mobile-full-screen .autopilot-inner[data-v-48409492] {
        background: #444;
        color: #eee
    }
}

@media (max-width:800px) {
    .mobile-full-screen.hidden[data-v-48409492] {
        display: block
    }
    .mobile-full-screen .suggestions[data-v-48409492] {
        border: none;
        display: flex;
        flex-direction: column;
        gap: 16px;
        padding-top: 16px;
        position: relative;
        top: 0
    }
    .mobile-full-screen .suggestions .suggestion[data-v-48409492] {
        gap: 16px;
        padding: 0 16px
    }
    .mobile-full-screen .suggestions .suggestion .icon[data-v-48409492] {
        display: flex
    }
    .mobile-full-screen .suggestions .suggestion .text[data-v-48409492] {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap
    }
    .mobile-full-screen .autopilot-inner[data-v-48409492] {
        align-items: center;
        background: #fafafa;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        justify-content: center;
        padding: 16px 0;
        text-align: center;
        width: 100%
    }
    .mobile-full-screen .autopilot-inner .text[data-v-48409492] {
        display: flex;
        flex-direction: column
    }
    .mobile-full-screen .autopilot-inner .desc[data-v-48409492] {
        font-size: 10px
    }
    @media (prefers-color-scheme:dark) {
        .mobile-full-screen .autopilot-inner[data-v-48409492] {
            background: #444;
            color: #eee
        }
    }
}

.stop-icon[data-v-4a485c34] {
    align-items: center;
    display: flex;
    height: 14px;
    justify-content: center;
    width: 14px
}

.remove[data-v-4a485c34] svg {
    height: 100%;
    width: 100%
}

.prompt-files .prompt-file.image[data-v-4a485c34] {
    height: 50px;
    padding: 0;
    width: 50px
}

.prompt-files .prompt-file.image .remove[data-v-4a485c34] {
    right: -8px;
    top: -8px
}

.file-wrapper[data-v-4a485c34] {
    align-items: center;
    background: #fafafa;
    border: 1px solid #efefef;
    border-radius: 12px;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    gap: 8px;
    height: 48px;
    justify-content: flex-start;
    width: 152px
}

.file-wrapper .file-icon[data-v-4a485c34] {
    background-color: #fff;
    border-radius: 8px;
    box-sizing: border-box;
    flex-shrink: 0;
    height: 36px;
    padding: 6px;
    width: 36px
}

.file-wrapper .file-icon[data-v-4a485c34] svg {
    height: 100%;
    width: 100%
}

.file-wrapper .file-info[data-v-4a485c34] {
    flex-grow: 1;
    max-width: 100px
}

.file-wrapper .file-info .file-name[data-v-4a485c34] {
    color: #232425;
    display: block;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

.file-wrapper .file-info .file-size[data-v-4a485c34] {
    color: #909499;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    width: 100%
}

.textarea-wrapper[data-v-4a485c34] {
    align-items: center;
    border-radius: 12px;
    box-sizing: border-box;
    display: flex;
    gap: 10px;
    overflow: hidden;
    position: relative;
    z-index: 1
}

.prompt-files[data-v-4a485c34] {
    display: flex;
    flex-direction: row;
    gap: 8px;
    overflow-x: auto;
    padding: 12px;
    width: 100%
}

.prompt-files .prompt-file[data-v-4a485c34] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    justify-content: center;
    position: relative
}

.prompt-files .prompt-file-inner[data-v-4a485c34] {
    height: 100%;
    padding: 8px;
    position: relative;
    width: 100%
}

.prompt-files .prompt-file .remove[data-v-4a485c34] {
    align-items: center;
    background: #000;
    border-radius: 50%;
    color: #fff;
    cursor: pointer;
    display: flex;
    height: 16px;
    justify-content: center;
    position: absolute;
    right: 0;
    top: 0;
    width: 16px;
    z-index: 1
}

.prompt-files .prompt-file img[data-v-4a485c34] {
    border-radius: 12px;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.search-input-wrapper-background[data-v-4a485c34] {
    background: #0f7fff;
    background: linear-gradient(270deg, #000, #000 45%, #376cd2, #0f7fff 55%, #0f7fff);
    height: 100%;
    margin-left: -170%;
    position: absolute;
    top: 0;
    transition: all 1s ease-in-out;
    width: 300%;
    z-index: -1
}

.searchAskFollowUp .search-input-wrapper-background[data-v-4a485c34],
.sparkQuestion .search-input-wrapper-background[data-v-4a485c34] {
    background: #fff
}

.active .search-input-wrapper-background[data-v-4a485c34] {
    margin-left: -10%
}

.input.sparkQuestion[data-v-4a485c34],
.input.white[data-v-4a485c34] {
    background: #fff;
    color: #d9d9d9
}

.input.sparkQuestion[data-v-4a485c34] {
    padding-right: 4px
}

.input.sparkQuestion textarea[data-v-4a485c34],
.input.white textarea[data-v-4a485c34] {
    background: transparent;
    color: #606366
}

.input.sparkQuestion textarea[data-v-4a485c34]::-moz-placeholder,
.input.white textarea[data-v-4a485c34]::-moz-placeholder {
    color: #606366;
    opacity: .65
}

.input.sparkQuestion textarea[data-v-4a485c34]::placeholder,
.input.white textarea[data-v-4a485c34]::placeholder {
    color: #606366;
    opacity: .65
}

.input.sparkQuestion.active textarea[data-v-4a485c34],
.input.white.active textarea[data-v-4a485c34] {
    caret-color: #fff;
    color: #fff
}

.input.searchAskFollowUp textarea[data-v-4a485c34]::-moz-placeholder {
    color: #606366;
    opacity: .65
}

.input.searchAskFollowUp textarea[data-v-4a485c34]::placeholder {
    color: #606366;
    opacity: .65
}

.input.searchAskFollowUp textarea[data-v-4a485c34],
.input.searchAskFollowUp.active textarea[data-v-4a485c34] {
    caret-color: #232425;
    color: #232425
}

.input.sparkQuestion.active textarea[data-v-4a485c34]::-moz-placeholder {
    color: #606366a6
}

.input.sparkQuestion.active textarea[data-v-4a485c34]::placeholder {
    color: #606366a6
}

.input.sparkQuestion.active textarea[data-v-4a485c34] {
    caret-color: #606366;
    color: #606366
}

.sparkQuestion .input-icon[data-v-4a485c34],
.white .input-icon[data-v-4a485c34] {
    background: #f2f2f2;
    border-radius: 8px;
    color: #232425;
    height: 20px;
    padding: 4px;
    width: 20px
}

.searchAskFollowUp .input-icon[data-v-4a485c34] {
    height: 32px;
    width: 32px
}

.icon-group[data-v-4a485c34] {
    align-items: flex-end;
    display: flex;
    margin-right: 6px
}

.searchAskFollowUp .icon-group[data-v-4a485c34] {
    margin-right: 0
}

.sparkQuestion .input-icon[data-v-4a485c34] {
    background: #fff
}

.sparkQuestion .input-icon[data-v-4a485c34]:hover {
    background: #2324250f
}

.input-icon[data-v-4a485c34] svg {
    align-items: center;
    display: flex;
    justify-content: center;
    width: 100%
}

.white[data-v-4a485c34] .input-icon *,
.white.active[data-v-4a485c34] .input-icon * {
    fill: #232425
}

.sparkQuestion[data-v-4a485c34] .input-icon * {
    fill: #232425;
    opacity: .65
}

.sparkQuestion.active[data-v-4a485c34] .input-icon * {
    fill: #606366;
    opacity: 1
}

.input[data-v-4a485c34] {
    align-items: stretch;
    border-radius: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow: hidden;
    padding: 6px 8px;
    position: relative;
    z-index: 1
}

.input.searchAskFollowUp[data-v-4a485c34] {
    border: 1px solid #efefef
}

.input.searchAskFollowUp.focus[data-v-4a485c34] {
    box-shadow: 0 1px 8px #00000014
}

.input.has-suggestions[data-v-4a485c34] {
    border-radius: 12px 12px 0 0
}

.input.sparkQuestion[data-v-4a485c34] {
    border: 1px solid #fff
}

.input.has-suggestions.sparkQuestion[data-v-4a485c34] {
    border-radius: 0 0 12px 12px
}

.input.focus.sparkQuestion[data-v-4a485c34] {
    border: 1px solid #dcdfe0
}

.input textarea[data-v-4a485c34] {
    background: transparent;
    border: 0;
    box-sizing: border-box;
    color: #fff;
    flex-grow: 1;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    height: 24px;
    line-height: 24px;
    max-height: 500px;
    outline: none;
    padding: 6px 0 6px 2px;
    resize: none;
    transition: color 1s ease-in-out;
    width: 100%
}

.input textarea[data-v-4a485c34]::-moz-placeholder {
    color: #ffffffa6
}

.input textarea[data-v-4a485c34]::placeholder {
    color: #ffffffa6
}

.input-icon[data-v-4a485c34] {
    align-items: center;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    height: 18px;
    justify-content: center;
    width: 18px
}

.active .input-icon[data-v-4a485c34] {
    color: #fff
}

.searchAskFollowUp .input-icon[data-v-4a485c34] {
    color: #232425
}

@media (max-width:1220px) {
    .input-icon.expand[data-v-4a485c34] {
        display: none
    }
}

@media (prefers-color-scheme:dark) {
    .input.sparkQuestion[data-v-4a485c34],
    .searchAskFollowUp .search-input-wrapper-background[data-v-4a485c34],
    .sparkQuestion .search-input-wrapper-background[data-v-4a485c34] {
        background: #232425
    }
    .input textarea[data-v-4a485c34],
    .input.active textarea[data-v-4a485c34],
    .input.active.searchAskFollowUp textarea[data-v-4a485c34],
    .input.active.sparkQuestion textarea[data-v-4a485c34],
    .input.active.white textarea[data-v-4a485c34],
    .input.searchAskFollowUp textarea[data-v-4a485c34],
    .input.sparkQuestion textarea[data-v-4a485c34],
    .input.white textarea[data-v-4a485c34] {
        caret-color: #fff;
        color: #fff
    }
    .input textarea[data-v-4a485c34]::-moz-placeholder,
    .input.active textarea[data-v-4a485c34]::-moz-placeholder,
    .input.active.searchAskFollowUp textarea[data-v-4a485c34]::-moz-placeholder,
    .input.active.sparkQuestion textarea[data-v-4a485c34]::-moz-placeholder,
    .input.active.white textarea[data-v-4a485c34]::-moz-placeholder,
    .input.searchAskFollowUp textarea[data-v-4a485c34]::-moz-placeholder,
    .input.sparkQuestion textarea[data-v-4a485c34]::-moz-placeholder,
    .input.white textarea[data-v-4a485c34]::-moz-placeholder {
        color: #fff;
        opacity: .65
    }
    .input textarea[data-v-4a485c34]::placeholder,
    .input.active textarea[data-v-4a485c34]::placeholder,
    .input.active.searchAskFollowUp textarea[data-v-4a485c34]::placeholder,
    .input.active.sparkQuestion textarea[data-v-4a485c34]::placeholder,
    .input.active.white textarea[data-v-4a485c34]::placeholder,
    .input.searchAskFollowUp textarea[data-v-4a485c34]::placeholder,
    .input.sparkQuestion textarea[data-v-4a485c34]::placeholder,
    .input.white textarea[data-v-4a485c34]::placeholder {
        color: #fff;
        opacity: .65
    }
    .input-icon[data-v-4a485c34],
    .searchAskFollowUp .input-icon[data-v-4a485c34],
    .sparkQuestion .input-icon[data-v-4a485c34],
    .white .input-icon[data-v-4a485c34] {
        background: #232425
    }
    .index .input-icon[data-v-4a485c34] {
        background: transparent
    }
    .input-icon[data-v-4a485c34]:hover,
    .searchAskFollowUp .input-icon[data-v-4a485c34]:hover,
    .sparkQuestion .input-icon[data-v-4a485c34]:hover,
    .white .input-icon[data-v-4a485c34] {
        background: #2324250f
    }
    .input-icon[data-v-4a485c34] svg {
        fill: #fff
    }
    .input-icon[data-v-4a485c34] svg:hover {
        fill: #fff
    }
    .white[data-v-4a485c34] .input-icon *,
    .white.active[data-v-4a485c34] .input-icon * {
        fill: #fff
    }
    .searchAskFollowUp[data-v-4a485c34] .input-icon *,
    .sparkQuestion[data-v-4a485c34] .input-icon * {
        fill: #fff;
        color: #fff;
        opacity: .8
    }
    .searchAskFollowUp.active[data-v-4a485c34] .input-icon *,
    .sparkQuestion.active[data-v-4a485c34] .input-icon * {
        fill: #fff;
        color: #fff;
        opacity: 1
    }
    .index .search-input-wrapper-background[data-v-4a485c34] {
        background: linear-gradient(270deg, #333, #333 45%, #376cd2, #0f7fff 55%, #0f7fff)
    }
    .input.index[data-v-4a485c34] {
        border: 1px solid transparent
    }
    .input.index.has-suggestions[data-v-4a485c34] {
        border: 1px solid #0f7fff
    }
}

.moa .search-input-wrapper-background[data-v-4a485c34] {
    background: #fff
}

.moa.input[data-v-4a485c34] {
    border: 1px solid #e6e9eb;
    border-radius: 16px
}

.moa.input textarea[data-v-4a485c34] {
    color: #232425
}

.moa.input textarea[data-v-4a485c34]::-moz-placeholder {
    color: #909499
}

.moa.input textarea[data-v-4a485c34]::placeholder {
    color: #909499
}

.moa .input-icon[data-v-4a485c34] {
    align-items: center;
    background: #eee;
    border-radius: 35%;
    box-sizing: border-box;
    color: #000;
    display: flex;
    height: 36px;
    justify-content: center;
    padding: 6px;
    width: 36px
}

.moa.empty .input-icon[data-v-4a485c34] {
    background-color: #f4f4f4;
    color: #909499
}

.moa .input-icon[data-v-4a485c34] {
    background-color: #232425;
    color: #fff
}

.moa .input-icon[data-v-4a485c34]:hover {
    opacity: .8
}

.moa .icon-group[data-v-4a485c34] {
    margin-right: 0
}

@media (prefers-color-scheme:dark) {
    .moa .input-icon[data-v-4a485c34] {
        background: transparent;
        color: #909499
    }
    .moa.empty .input-icon[data-v-4a485c34] {
        background-color: #eeeeee30;
        color: #ffffff60
    }
    .moa .input-icon[data-v-4a485c34] {
        background-color: #eee;
        color: #232425
    }
    .moa .search-input-wrapper-background[data-v-4a485c34] {
        background: #333
    }
    .moa.input textarea[data-v-4a485c34] {
        color: #fff
    }
    .moa.input[data-v-4a485c34] {
        border: 1px solid #e6e9eb40
    }
}