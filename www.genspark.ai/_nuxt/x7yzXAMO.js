import {
    _ as t
} from "./L54g9xmZ.js";
import {
    _ as s,
    r as e,
    v as i,
    h as r,
    a3 as o,
    d as a,
    e as m,
    o as p
} from "./Cf0SOiw0.js";
import "./e-ES_T8J.js";
import "./V-H-Vsd5.js";
import "./mJse5lCL.js";
import "./BH52nuY1.js";
import "./D5LV4gfS.js";
import "./D6bQc9d9.js";
import "./CAmLbDGM.js";
import "./Bm_HbXT2.js";
import "./DQpEsQQa.js";
import "./pB_XRIgB.js";
import "./BGK9k_mT.js";
import "./BjWUbj3w.js";
import "./B7VeW_-d.js";
import "./BLWq1oPC.js";
import "./DAjjhrgi.js";
import "./MpDLC7up.js";
import "./DpMvtoun.js";
import "./Jr9eiJio.js";
import "./9wLWmnxl.js";
import "./WZsIN7xM.js";
import "./B6noBY_5.js";
import "./DXvAIxvL.js";
import "./CW991W2w.js";
import "./BPQGB51Y.js";
import "./C-H3edso.js";
import "./BuhfKjCJ.js";
import "./B-XpIQkh.js";
import "./DGJMLFjI.js";
import "./By6xEfKc.js";
import "./C38RzRfR.js";
import "./BrPr1fm5.js";
import "./BihyrXkC.js";
const n = {
        class: "sankey-report"
    },
    j = s({
        __name: "SankeyReport",
        props: {
            searchFilter: {
                type: Object,
                default: () => ({})
            },
            latestItems: {
                type: Array,
                default: () => []
            }
        },
        setup(s) {
            const j = e(0),
                l = e([]),
                c = s;
            i([() => c.latestItems, () => c.searchFilter], (async () => {
                if (0 === c.latestItems.length && c.searchFilter) try {
                    const t = await fetch("/api/spark/finance/financial_report_flow_item", {
                            method: "GET",
                            headers: {
                                "Content-Type": "application/json"
                            }
                        } + "?" + new URLSearchParams(c.searchFilter)),
                        s = await t.json();
                    l.value = s
                } catch (t) {
                    l.value = []
                } else l.value = c.latestItems
            }), {
                immediate: !0
            }), r((() => {
                j.value = window.innerWidth, window.addEventListener("resize", d)
            }));
            const d = () => {
                j.value = window.innerWidth
            };
            return o((() => {
                window.removeEventListener("resize", d)
            })), (s, e) => (p(), a("div", n, [m(t, {
                title: s.$t("pages.finance.earnings-highlights"),
                "component-width": j.value,
                "external-latest-items": l.value,
                "show-filters": !1,
                "show-more-button": !1,
                "max-column-width": 180,
                "max-rows": 2
            }, null, 8, ["title", "component-width", "external-latest-items"])]))
        }
    }, [
        ["__scopeId", "data-v-e7a4d81e"]
    ]);
export {
    j as
    default
};