import {
    d as o,
    D as t,
    o as e,
    b as l,
    a as n,
    w as i,
    ai as s,
    p as a,
    _ as c,
    s as r,
    r as g,
    V as p,
    i as d,
    v as C,
    c as u,
    C as h,
    f as v,
    e as w,
    t as f,
    n as L,
    j as y,
    E as m
} from "./Cf0SOiw0.js";
import {
    A as _
} from "./CVKRwtBu.js";
import {
    C as I
} from "./Dnth285N.js";
const A = {
    width: "100%",
    height: "100%",
    viewBox: "0 0 40 40",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const T = {
    xmlns: "http://www.w3.org/2000/svg",
    width: "21",
    height: "20",
    viewBox: "0 0 21 20",
    fill: "none"
};
const k = {
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 19 19.08"
};
const x = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 24 24"
};
const b = {
        class: "head"
    },
    M = {
        class: "icon"
    },
    G = {
        class: "desc"
    },
    O = {
        class: "buttons"
    },
    P = {
        class: "bold"
    },
    H = {
        class: "icon"
    },
    R = {
        class: "icons"
    },
    V = {
        class: "icon"
    },
    S = {
        class: "icon"
    },
    Z = {
        class: "icon"
    };
const U = c({
    props: {
        styleClass: {
            type: String,
            default: "index"
        },
        copilotType: {
            type: String,
            default: "COPILOT_SPARK"
        }
    },
    components: {
        AppleIcon: _,
        GoogleIcon: {
            render: function(t, n) {
                return e(), o("svg", T, n[0] || (n[0] = [l("path", {
                    d: "M18.6687 8.36727H17.9974V8.33269H10.4974V11.666H15.207C14.5199 13.6064 12.6737 14.9993 10.4974 14.9993C7.73615 14.9993 5.4974 12.7606 5.4974 9.99936C5.4974 7.23811 7.73615 4.99936 10.4974 4.99936C11.772 4.99936 12.9316 5.48019 13.8145 6.26561L16.1716 3.90852C14.6833 2.52144 12.6924 1.66602 10.4974 1.66602C5.89531 1.66602 2.16406 5.39727 2.16406 9.99936C2.16406 14.6014 5.89531 18.3327 10.4974 18.3327C15.0995 18.3327 18.8308 14.6014 18.8308 9.99936C18.8308 9.44061 18.7733 8.89519 18.6687 8.36727Z",
                    fill: "#FFC107"
                }, null, -1), l("path", {
                    d: "M3.125 6.12061L5.86292 8.12852C6.60375 6.29436 8.39792 4.99936 10.4975 4.99936C11.7721 4.99936 12.9317 5.48019 13.8146 6.26561L16.1717 3.90852C14.6834 2.52144 12.6925 1.66602 10.4975 1.66602C7.29667 1.66602 4.52083 3.47311 3.125 6.12061Z",
                    fill: "#FF3D00"
                }, null, -1), l("path", {
                    d: "M10.4969 18.3336C12.6494 18.3336 14.6052 17.5099 16.084 16.1703L13.5048 13.9878C12.64 14.6455 11.5833 15.0012 10.4969 15.0003C8.32937 15.0003 6.48897 13.6182 5.79563 11.6895L3.07812 13.7832C4.45729 16.482 7.25812 18.3336 10.4969 18.3336Z",
                    fill: "#4CAF50"
                }, null, -1), l("path", {
                    d: "M18.6712 8.36856H18V8.33398H10.5V11.6673H15.2096C14.8809 12.5908 14.2889 13.3978 13.5067 13.9886L13.5079 13.9877L16.0871 16.1702C15.9046 16.3361 18.8333 14.1673 18.8333 10.0007C18.8333 9.44189 18.7758 8.89648 18.6712 8.36856Z",
                    fill: "#1976D2"
                }, null, -1)]))
            }
        },
        MicrosoftIcon: {
            render: function(c, r) {
                return e(), o("svg", k, [l("defs", null, [(e(), n(s("style"), null, {
                    default: i((() => r[0] || (r[0] = [a(".cls-1{fill:#f25022;}.cls-2{fill:#7fba00;}.cls-3{fill:#59b4d9;}.cls-4{fill:#ffb900;}")]))),
                    _: 1
                }))]), r[1] || (r[1] = t('<title>Microsoft</title><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><g id="Microsoft_Logo" data-name="Microsoft Logo"><rect class="cls-1" width="9.06" height="9.06"></rect><rect class="cls-2" x="9.94" width="9.06" height="9.06"></rect><rect class="cls-3" y="10.02" width="9.06" height="9.06"></rect><rect class="cls-4" x="9.94" y="10.02" width="9.06" height="9.06"></rect></g></g></g>', 2))])
            }
        },
        GensparkIcon: {
            render: function(l, n) {
                return e(), o("svg", A, n[0] || (n[0] = [t('<path d="M32.4035 0H7.59653C3.40108 0 0 3.40108 0 7.59653V32.4035C0 36.5989 3.40108 40 7.59653 40H32.4035C36.5989 40 40 36.5989 40 32.4035V7.59653C40 3.40108 36.5989 0 32.4035 0Z" style="fill:var(--bg-color, white);"></path><path d="M33.547 30.5703H6.55314C5.99308 30.5703 5.53906 31.0243 5.53906 31.5844V33.7136C5.53906 34.2736 5.99308 34.7277 6.55314 34.7277H33.547C34.107 34.7277 34.5611 34.2736 34.5611 33.7136V31.5844C34.5611 31.0243 34.107 30.5703 33.547 30.5703Z" style="fill:var(--fg-color, black);"></path><path d="M14.5524 26.2255C14.39 26.2255 14.2529 26.1064 14.2276 25.944C13.1703 18.9538 12.4052 18.3366 5.46545 17.2901C5.25253 17.2576 5.09375 17.0736 5.09375 16.857C5.09375 16.6405 5.25253 16.4565 5.46545 16.424C12.3691 15.381 12.9826 14.7639 14.0255 7.86389C14.058 7.65097 14.2421 7.49219 14.4586 7.49219C14.6751 7.49219 14.8592 7.65097 14.8916 7.86389C15.9346 14.7639 16.5517 15.381 23.4517 16.424C23.6647 16.4565 23.8234 16.6405 23.8234 16.857C23.8234 17.0736 23.6647 17.2576 23.4517 17.2901C16.5192 18.3366 15.9274 18.9538 14.8772 25.944C14.852 26.1028 14.7148 26.2255 14.5524 26.2255Z" style="fill:var(--fg-color, black);"></path><path d="M27.7237 16.9804C27.6227 16.9804 27.5361 16.9046 27.5216 16.8036C26.8612 12.4369 26.3813 12.0508 22.0471 11.3976C21.9136 11.3759 21.8125 11.2641 21.8125 11.1269C21.8125 10.9934 21.9099 10.8779 22.0471 10.8563C26.3596 10.2067 26.7457 9.82053 27.3953 5.50801C27.417 5.37448 27.5289 5.27344 27.666 5.27344C27.7995 5.27344 27.915 5.37088 27.9367 5.50801C28.5862 9.82053 28.9724 10.2067 33.2849 10.8563C33.4184 10.8779 33.5195 10.9898 33.5195 11.1269C33.5195 11.2604 33.422 11.3759 33.2849 11.3976C28.9543 12.0508 28.5826 12.4369 27.9258 16.8036C27.9114 16.9046 27.8248 16.9804 27.7237 16.9804Z" style="fill:var(--fg-color, black);"></path><path d="M27.6901 27.8783C27.6251 27.8783 27.571 27.8313 27.5602 27.7664C27.1415 24.9876 26.8348 24.7422 24.0777 24.3272C23.991 24.3128 23.9297 24.2406 23.9297 24.154C23.9297 24.0674 23.991 23.9952 24.0777 23.9808C26.8204 23.5657 27.0657 23.3203 27.4808 20.5776C27.4952 20.491 27.5674 20.4297 27.654 20.4297C27.7406 20.4297 27.8128 20.491 27.8272 20.5776C28.2422 23.3203 28.4876 23.5657 31.2303 23.9808C31.3169 23.9952 31.3783 24.0674 31.3783 24.154C31.3783 24.2406 31.3169 24.3128 31.2303 24.3272C28.4732 24.7422 28.2386 24.9876 27.82 27.7664C27.8092 27.8313 27.755 27.8783 27.6901 27.8783Z" style="fill:var(--fg-color, black);"></path>', 5)]))
            }
        },
        ArrowRightIcon: {
            render: function(t, n) {
                return e(), o("svg", x, n[0] || (n[0] = [l("path", {
                    d: "M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8l-8-8z",
                    fill: "currentColor"
                }, null, -1)]))
            }
        },
        CloseIcon: I
    },
    setup(o) {
        const {
            t: t
        } = r(), e = g(o.styleClass), l = g(o.copilotType), n = d("currentUser"), i = g(!1), s = p();
        !1 === n.value && (i.value = !0), C(n, (o => {
            i.value = !1 === o
        }));
        const a = () => {
                s.query.from === y ? window.parent.postMessage({
                    type: "chrome_extension_login",
                    payload: {
                        redirectUrl: window.location.origin + "/api/login?redirect_url=" + encodeURIComponent(window.location.origin)
                    }
                }, "*") : window.location.href = "/api/login?redirect_url=" + encodeURIComponent(location.href)
            },
            c = d("jsBridge"),
            v = g(!1);
        C((() => c.value), (o => {
            o && c.value.callHandler("support", {
                api: "googleLogin"
            }, (o => {
                v.value = o
            }))
        }), {
            immediate: !0
        });
        return {
            gtag: u((() => window.gtag ? window.gtag : () => {})),
            utils: h,
            t: t,
            styleClass: e,
            copilotType: l,
            gotoLogin: a,
            gotoAndroidAppLogin: () => {
                v.value ? c.value.callHandler("googleLogin", {}, (o => {})) : a()
            },
            currentUser: n,
            showLoginGuide: i
        }
    }
}, [
    ["render", function(t, n, i, s, c, r) {
        m("CloseIcon");
        const g = m("GensparkIcon"),
            p = m("ArrowRightIcon"),
            d = m("AppleIcon"),
            C = m("GoogleIcon"),
            u = m("MicrosoftIcon");
        return s.showLoginGuide ? (e(), o("div", {
            key: 0,
            class: L(["loginGuide", {
                index: "index" == s.styleClass,
                spark: "spark" == s.styleClass,
                theme_images: "COPILOT_IMAGES" == s.copilotType,
                theme_spark: "COPILOT_SPARK" == s.copilotType,
                theme_products: "COPILOT_PRODUCTS" == s.copilotType || "COPILOT_PRODUCTS_SPARK" == s.copilotType,
                theme_travel: "COPILOT_TRAVEL" == s.copilotType || "COPILOT_SPARK_TRAVEL" == s.copilotType
            }])
        }, [v("", !0), l("div", b, [l("div", M, [w(g)])]), l("div", G, [a(f(t.$t("components.LoginGuidance.desc-2")) + " ", 1), n[4] || (n[4] = l("br", null, null, -1)), a(" " + f(t.$t("components.LoginGuidance.desc-3")), 1)]), l("div", O, [l("div", {
            class: "button blue",
            onClick: n[1] || (n[1] = o => {
                o.preventDefault(), o.stopPropagation(), s.gtag("event", "start_login", {
                    type: "signup"
                }), s.gotoLogin()
            })
        }, [l("div", P, f(t.$t("components.LoginGuidance.signup")), 1), l("div", null, f(t.$t("components.LoginGuidance.it-s-free")), 1), l("div", H, [w(p)])]), s.utils.isGensparkAppIos() ? (e(), o("div", {
            key: 0,
            class: "button third",
            onClick: n[2] || (n[2] = () => {
                s.gtag("event", "start_login", {
                    type: "signin"
                }), s.gotoLogin()
            })
        }, [l("div", R, [l("div", V, [w(d)]), l("div", S, [w(C)]), l("div", Z, [w(u)])]), l("div", null, f(t.$t("components.LoginGuidance.signin")), 1)])) : (e(), o("div", {
            key: 1,
            class: "button signin",
            onClick: n[3] || (n[3] = () => {
                s.gtag("event", "start_login", {
                    type: "signin"
                }), s.utils.isGensparkAppAndroid() ? s.gotoAndroidAppLogin() : s.gotoLogin()
            })
        }, [l("div", null, f(t.$t("components.LoginGuidance.signin")), 1)]))])], 2)) : v("", !0)
    }],
    ["__scopeId", "data-v-0beb4848"]
]);
export {
    U as _
};