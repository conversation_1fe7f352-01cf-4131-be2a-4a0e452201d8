import {
    d as C,
    b as e,
    o as l
} from "./Cf0SOiw0.js";
const n = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const r = {
        render: function(r, L) {
            return l(), C("svg", n, L[0] || (L[0] = [e("path", {
                d: "M6 7L6 16",
                stroke: "currentColor"
            }, null, -1), e("path", {
                "fill-rule": "evenodd",
                "clip-rule": "evenodd",
                d: "M16.6921 14.3545C16.4052 15.7491 15.1775 16.75 13.7536 16.75L5.00319 16.75C3.34634 16.75 2.00319 15.4069 2.00319 13.75L2.00319 8.75C2.00319 8.64616 2.00231 8.54572 2.00147 8.45062C1.99974 8.25401 1.99821 8.08023 2.00511 7.94645C2.01445 7.76541 2.03992 7.47071 2.25741 7.25347C2.47478 7.03636 2.76942 7.01107 2.95038 7.00181C3.08379 6.99497 3.2571 6.99651 3.45315 6.99826C3.54865 6.99911 3.64955 7 3.75385 7L6.25319 7L6.45113 7C6.94508 7 7.40736 6.75683 7.68719 6.3498L10.0648 2.89148C10.4485 2.33341 11.0823 2 11.7595 2C13.0975 2 14.0792 3.25739 13.7547 4.55542L13.1436 7L14.525 7C16.4249 7 17.8463 8.74359 17.4635 10.6045L16.6921 14.3545ZM13.7536 15.75C14.7028 15.75 15.5213 15.0828 15.7126 14.153L16.484 10.403C16.7392 9.1624 15.7916 8 14.525 8L12.5032 8C12.3492 8 12.2038 7.92907 12.1091 7.80771C12.0143 7.68635 11.9808 7.5281 12.0181 7.37873L12.7846 4.31288C12.9513 3.64601 12.4469 3 11.7595 3C11.4116 3 11.0859 3.1713 10.8888 3.45801L8.51124 6.91632C8.04485 7.59471 7.27437 8 6.45113 8L6.25319 8L3.75385 8C3.61553 8 3.49893 7.9989 3.39852 7.99795C3.23009 7.99635 3.10719 7.99519 3.00366 8.00039C2.99843 8.104 2.99958 8.22715 3.00117 8.39606C3.0021 8.49615 3.00319 8.61232 3.00319 8.75L3.00319 13.75C3.00319 14.8546 3.89862 15.75 5.00319 15.75L13.7536 15.75Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    L = {
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const o = {
    render: function(n, r) {
        return l(), C("svg", L, r[0] || (r[0] = [e("path", {
            "fill-rule": "evenodd",
            "clip-rule": "evenodd",
            d: "M5.62757 16.25L5.00327 16.25C3.62256 16.25 2.50327 15.1307 2.50327 13.75L2.50327 8.75C2.50327 8.05976 2.47077 7.74724 2.61076 7.6073C2.75081 7.46731 3.06346 7.5 3.75393 7.5L5.62757 7.5L5.62757 16.25ZM6.87757 16.25L13.7537 16.25C14.9402 16.25 15.9633 15.4159 16.2024 14.2537L16.9738 10.5037C17.2928 8.95299 16.1083 7.5 14.5251 7.5L12.5033 7.5L13.2697 4.43415C13.5154 3.4517 12.7723 2.5 11.7596 2.5C11.247 2.5 10.7673 2.75235 10.4769 3.17475L8.09929 6.63306C7.80826 7.05638 7.36921 7.34678 6.87757 7.45406L6.87757 16.25Z",
            fill: "currentColor"
        }, null, -1)]))
    }
};
export {
    o as C, r as a
};