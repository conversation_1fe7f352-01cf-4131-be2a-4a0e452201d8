.custom-tools-modal[data-v-39c05aa8] {
    background-color: #fff;
    border-radius: 8px;
    box-sizing: border-box;
    padding: 16px;
    width: 100%
}

.dropdown-header[data-v-39c05aa8] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px
}

.dropdown-header h3[data-v-39c05aa8] {
    color: #232425;
    font-size: 16px;
    font-weight: 600;
    margin: 0
}

.add-link[data-v-39c05aa8] {
    color: #4285f4;
    cursor: pointer;
    font-size: 13px
}

.arrow[data-v-39c05aa8] {
    font-weight: 700
}

.tools-list[data-v-39c05aa8] {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 240px;
    overflow-x: hidden;
    overflow-y: auto
}

.tool-item[data-v-39c05aa8] {
    flex-shrink: 0;
    min-height: 32px;
    padding: 6px 0
}

.tool-icon[data-v-39c05aa8],
.tool-item[data-v-39c05aa8] {
    align-items: center;
    display: flex
}

.tool-icon[data-v-39c05aa8] {
    height: 20px;
    justify-content: center;
    margin-right: 10px;
    width: 20px
}

.tool-icon img[data-v-39c05aa8] {
    border-radius: 4px;
    max-height: 100%;
    max-width: 100%
}

.default-icon[data-v-39c05aa8] {
    align-items: center;
    background-color: #e0e0e0;
    border-radius: 4px;
    display: flex;
    font-size: 12px;
    font-weight: 500;
    height: 20px;
    justify-content: center;
    width: 20px
}

.tool-name[data-v-39c05aa8] {
    color: #232425;
    flex-grow: 1;
    font-size: 14px
}

.checkbox[data-v-39c05aa8] {
    position: relative
}

.checkbox input[data-v-39c05aa8] {
    cursor: pointer;
    height: 0;
    opacity: 0;
    position: absolute;
    width: 0
}

.checkbox label[data-v-39c05aa8] {
    background-color: #fff;
    border: 2px solid #dcdfe0;
    border-radius: 4px;
    cursor: pointer;
    display: inline-block;
    height: 18px;
    position: relative;
    width: 18px
}

.checkbox input:checked+label[data-v-39c05aa8] {
    background-color: #4285f4;
    border-color: #4285f4
}

.checkbox input:checked+label[data-v-39c05aa8]:after {
    border: solid #fff;
    border-width: 0 2px 2px 0;
    content: "";
    height: 8px;
    left: 5px;
    position: absolute;
    top: 2px;
    transform: rotate(45deg);
    width: 4px
}

.dropdown-footer[data-v-39c05aa8] {
    display: flex;
    gap: 8px;
    justify-content: flex-end
}

.apply-btn[data-v-39c05aa8],
.cancel-btn[data-v-39c05aa8] {
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    padding: 6px 12px
}

.cancel-btn[data-v-39c05aa8] {
    background-color: transparent;
    border: 1px solid #dcdfe0;
    color: #606366
}

.apply-btn[data-v-39c05aa8] {
    background-color: #4285f4;
    border: none;
    color: #fff
}

@media (prefers-color-scheme:dark) {
    .custom-tools-modal[data-v-39c05aa8] {
        background-color: #333
    }
    .dropdown-header h3[data-v-39c05aa8],
    .tool-name[data-v-39c05aa8] {
        color: #fff
    }
    .default-icon[data-v-39c05aa8] {
        background-color: #555;
        color: #fff
    }
    .checkbox label[data-v-39c05aa8] {
        background-color: #333;
        border-color: #555
    }
    .cancel-btn[data-v-39c05aa8] {
        border-color: #555;
        color: #ddd
    }
}

.install-tools-container[data-v-f99eab88] {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif;
    margin: 0 auto;
    max-width: 1200px;
    padding: 20px;
    width: 100%
}

.install-tools-container.modal-mode[data-v-f99eab88] {
    align-items: center;
    bottom: 0;
    display: flex;
    justify-content: center;
    left: 0;
    margin: 0;
    max-height: 100vh;
    padding: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1000
}

.header[data-v-f99eab88] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px
}

.title[data-v-f99eab88] {
    color: #333;
    font-size: 28px;
    font-weight: 600;
    margin: 0
}

.section-header[data-v-f99eab88] {
    border-bottom: 1px solid #eee;
    margin: 40px 0 20px;
    padding-bottom: 10px;
    position: relative
}

.section-header h2[data-v-f99eab88] {
    color: #333;
    font-size: 20px;
    font-weight: 600;
    margin: 0
}

.mcp-section-header[data-v-f99eab88] {
    align-items: center;
    display: flex;
    justify-content: center;
    margin: 24px 0
}

.mcp-section-header h2[data-v-f99eab88] {
    color: #666;
    font-size: 22px;
    font-weight: 500;
    margin: 0 15px
}

.divider-line[data-v-f99eab88] {
    background-color: #eee;
    flex-grow: 1;
    height: 1px
}

.empty-mcp-container[data-v-f99eab88] {
    align-items: center;
    background-color: #f9f9f9;
    border-radius: 8px;
    display: flex;
    grid-column: 1/-1;
    justify-content: center;
    min-height: 120px;
    padding: 40px
}

.empty-mcp-message[data-v-f99eab88] {
    color: #666;
    font-size: 16px;
    text-align: center
}

.logo[data-v-f99eab88] {
    align-items: center;
    color: #6e6e6e;
    display: flex;
    flex-direction: row;
    height: 24px;
    justify-content: flex-start;
    line-height: 54px;
    width: 120px
}

.logo img[data-v-f99eab88] {
    flex-shrink: 0;
    height: 48px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 120px
}

.close[data-v-f99eab88],
.header-actions[data-v-f99eab88] {
    align-items: center;
    display: flex
}

.close[data-v-f99eab88] {
    border-radius: 50%;
    color: #606366;
    cursor: pointer;
    font-size: 20px;
    font-weight: 300;
    height: 32px;
    justify-content: center;
    transition: background-color .2s;
    width: 32px
}

.close[data-v-f99eab88]:hover {
    background-color: #f1f1f1
}

.add-server-btn[data-v-f99eab88] {
    align-items: center;
    background: linear-gradient(0deg, #f3f9ff, #f3f9ff), #f5f5f5;
    border: 1px solid #cfe5ff;
    border-radius: 8px;
    box-sizing: border-box;
    color: #0f7fff;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: 700;
    gap: 10px;
    height: 36px;
    justify-content: center;
    margin-bottom: 20px;
    overflow: hidden;
    padding: 6px 14px;
    width: 100%
}

.add-server-btn[data-v-f99eab88]:hover {
    border-color: #a0c9ff
}

.add-server-btn svg[data-v-f99eab88] {
    flex-shrink: 0;
    height: 20px;
    width: 20px
}

.add-server-btn span[data-v-f99eab88] {
    display: block;
    line-height: 24px;
    text-align: center;
    width: 140px
}

.tools-grid[data-v-f99eab88] {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(2, 1fr)
}

.tool-card[data-v-f99eab88] {
    background-color: #f9f9f9;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    padding: 20px;
    transition: box-shadow .2s
}

.tool-card[data-v-f99eab88]:hover {
    box-shadow: 0 2px 8px #0000000d
}

.clickable-card[data-v-f99eab88] {
    cursor: pointer
}

.tool-card-top[data-v-f99eab88] {
    align-items: flex-start;
    display: flex;
    justify-content: space-between;
    padding-bottom: 16px
}

.tool-top-left[data-v-f99eab88] {
    align-items: center;
    display: flex;
    gap: 12px
}

.tool-card-divider[data-v-f99eab88] {
    background-color: #e0e0e0;
    height: 1px;
    margin: 0
}

.tool-card-bottom[data-v-f99eab88] {
    padding-top: 16px
}

.tool-icon-google-suite[data-v-f99eab88] {
    width: 96px
}

.tool-icon[data-v-f99eab88],
.tool-icon-google-suite[data-v-f99eab88] {
    align-items: center;
    display: flex;
    height: 32px;
    justify-content: center
}

.tool-icon[data-v-f99eab88] {
    width: 32px
}

.tool-icon img[data-v-f99eab88],
.tool-icon svg[data-v-f99eab88] {
    height: 32px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 32px
}

.tool-icon>div[data-v-f99eab88],
.tool-icon>span[data-v-f99eab88] {
    max-height: 32px;
    max-width: 32px
}

.tool-icon>.nuxt-icon[data-v-f99eab88],
.tool-icon>div[data-v-f99eab88],
.tool-icon>span[data-v-f99eab88] {
    align-items: center;
    display: flex;
    justify-content: center
}

.tool-icon>.nuxt-icon[data-v-f99eab88],
.tool-icon>.nuxt-icon svg[data-v-f99eab88] {
    height: 32px !important;
    width: 32px !important
}

.default-icon[data-v-f99eab88] {
    align-items: center;
    background-color: #eaeaea;
    border-radius: 4px;
    color: #333;
    display: flex;
    font-size: 16px;
    font-weight: 600;
    height: 32px;
    justify-content: center;
    width: 32px
}

.tool-title-section[data-v-f99eab88] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px
}

.tool-title[data-v-f99eab88] {
    display: -webkit-box;
    font-size: 16px;
    font-weight: 600;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    -webkit-hyphens: auto;
    hyphens: auto;
    line-height: 1.3;
    max-height: 2.6em;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word
}

.tool-type-badge[data-v-f99eab88] {
    align-items: center;
    background-color: #f0f0f0;
    border-radius: 12px;
    color: #555;
    display: flex;
    font-size: 11px;
    font-weight: 500;
    height: 18px;
    padding: 2px 8px
}

.tool-description[data-v-f99eab88] {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word
}

.tool-action-btn[data-v-f99eab88] {
    border: none;
    border-radius: 44px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    height: 26px;
    min-width: 80px;
    transition: background-color .2s;
    width: 65px
}

.install-btn[data-v-f99eab88] {
    background-color: #4285f4;
    color: #fff
}

.install-btn[data-v-f99eab88]:hover {
    background-color: #3367d6
}

.uninstall-btn[data-v-f99eab88] {
    background-color: #f1f1f1;
    color: #666
}

.uninstall-btn[data-v-f99eab88]:hover {
    background-color: #e5e5e5
}

.installing-btn[data-v-f99eab88] {
    background-color: #4285f4;
    color: #fff;
    cursor: not-allowed
}

.uninstalling-btn[data-v-f99eab88] {
    background-color: #e0e0e0;
    color: #888;
    cursor: not-allowed
}

.tool-card-content[data-v-f99eab88] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    padding: 20px
}

.browser-required-btn[data-v-f99eab88] {
    align-self: center;
    background-color: #1f7ef8;
    border: none;
    border-radius: 44px;
    box-sizing: border-box;
    color: #fff;
    cursor: pointer;
    flex-shrink: 0;
    font-size: 14px;
    font-weight: 500;
    height: 26px;
    line-height: 26px;
    min-width: 190px;
    padding: 0 16px;
    text-align: center;
    white-space: nowrap
}

.browser-required-btn[data-v-f99eab88]:hover {
    background-color: #3367d6
}

.modal-overlay[data-v-f99eab88] {
    background-color: #00000080;
    left: 0;
    position: fixed;
    top: 0;
    z-index: 1000
}

.modal-overlay[data-v-f99eab88],
.modal-wrapper[data-v-f99eab88] {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%
}

.modal-container[data-v-f99eab88] {
    animation: modalFadeIn-f99eab88 .3s ease;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 5px 20px #0003;
    max-height: 90vh;
    max-width: 1000px;
    overflow-y: auto;
    padding: 24px;
    width: 90%
}

.modal-header[data-v-f99eab88] {
    align-items: center;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    padding: 16px 20px
}

.modal-header h2[data-v-f99eab88] {
    font-size: 18px;
    font-weight: 600;
    margin: 0
}

.close-btn[data-v-f99eab88] {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 24px
}

.modal-body[data-v-f99eab88] {
    padding: 20px
}

.form-group[data-v-f99eab88] {
    margin-bottom: 16px
}

.form-group label[data-v-f99eab88] {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 6px
}

.form-group input[data-v-f99eab88],
.form-group textarea[data-v-f99eab88] {
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    padding: 10px;
    width: 100%
}

.radio-group label[data-v-f99eab88] {
    margin-bottom: 0
}

.form-group textarea[data-v-f99eab88] {
    min-height: 80px;
    resize: vertical
}

.modal-footer[data-v-f99eab88] {
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding: 16px 20px
}

.cancel-btn[data-v-f99eab88] {
    background-color: #f1f1f1;
    border: none;
    border-radius: 4px;
    color: #333
}

.add-btn[data-v-f99eab88],
.cancel-btn[data-v-f99eab88] {
    cursor: pointer;
    font-size: 14px;
    min-width: 100px;
    padding: 8px 16px
}

.add-btn[data-v-f99eab88] {
    background-color: #000;
    border: none;
    border-radius: 12px;
    color: #fff;
    font-weight: 500
}

.add-btn[data-v-f99eab88]:hover {
    background-color: #1a1a1a
}

@media (max-width:768px) {
    .tools-grid[data-v-f99eab88] {
        grid-template-columns: 1fr
    }
    .tool-card-top[data-v-f99eab88]:has(.browser-required-btn) {
        align-items: flex-start;
        flex-direction: column;
        gap: 12px
    }
    .browser-required-btn[data-v-f99eab88] {
        align-self: stretch;
        min-width: unset;
        width: 100%
    }
}

.modal-backdrop[data-v-f99eab88] {
    background-color: #00000080;
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: -1
}

.modal-content[data-v-f99eab88] {
    background-color: #fff;
    border-radius: 12px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 24px;
    width: 100%
}

.modal-mode .modal-content[data-v-f99eab88] {
    animation: modalFadeIn-f99eab88 .3s ease;
    box-shadow: 0 5px 20px #0003;
    max-width: 1000px;
    width: 90%
}

@keyframes modalFadeIn-f99eab88 {
    0% {
        opacity: 0;
        transform: translateY(-20px)
    }
    to {
        opacity: 1;
        transform: translateY(0)
    }
}

.loading-container[data-v-f99eab88] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 70vh;
    padding: 40px
}

.loading-spinner[data-v-f99eab88] {
    animation: spin-f99eab88 1s linear infinite;
    border: 3px solid #f3f3f3;
    border-radius: 50%;
    border-top-color: #4285f4;
    height: 40px;
    margin-bottom: 16px;
    width: 40px
}

@keyframes spin-f99eab88 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.loading-spinner-small[data-v-f99eab88] {
    animation: spin-small-f99eab88 .8s linear infinite;
    border: 2px solid hsla(0, 0%, 100%, .2);
    border-radius: 50%;
    border-top-color: #fff;
    display: inline-block;
    height: 14px;
    vertical-align: middle;
    width: 14px
}

.uninstalling-btn .loading-spinner-small[data-v-f99eab88] {
    border: 2px solid hsla(0, 0%, 53%, .2);
    border-top-color: #888
}

@keyframes spin-small-f99eab88 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.default-mcp-icon[data-v-f99eab88] {
    align-items: center;
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-sizing: border-box;
    color: #000;
    display: flex;
    font-size: 10px;
    font-weight: 600;
    height: 32px;
    justify-content: center;
    line-height: 1;
    width: 32px
}

@media (prefers-color-scheme:dark) {
    .default-icon[data-v-f99eab88] {
        background-color: #333;
        color: #fff
    }
    .default-mcp-icon[data-v-f99eab88] {
        border-color: #555
    }
    .default-mcp-icon[data-v-f99eab88],
    .install-tools-container[data-v-f99eab88] {
        background-color: #2d2d2d;
        color: #fff
    }
    .title[data-v-f99eab88] {
        color: #fff
    }
    .section-header[data-v-f99eab88] {
        border-bottom-color: #444
    }
    .section-header h2[data-v-f99eab88] {
        color: #fff
    }
    .mcp-section-header h2[data-v-f99eab88] {
        color: #aaa
    }
    .divider-line[data-v-f99eab88] {
        background-color: #444
    }
    .empty-mcp-container[data-v-f99eab88] {
        background-color: #1a1a1a
    }
    .empty-mcp-message[data-v-f99eab88] {
        color: #aaa
    }
    .tool-card[data-v-f99eab88] {
        background-color: #1a1a1a;
        border: 1px solid #333
    }
    .tool-card-divider[data-v-f99eab88] {
        background-color: #444
    }
    .tool-title[data-v-f99eab88] {
        color: #fff
    }
    .tool-description[data-v-f99eab88] {
        color: #aaa
    }
    .tool-type-badge[data-v-f99eab88] {
        background-color: #333;
        color: #ccc
    }
    .uninstall-btn[data-v-f99eab88] {
        background-color: #333;
        color: #aaa
    }
    .uninstall-btn[data-v-f99eab88]:hover {
        background-color: #444
    }
    .mcp-tool-tag[data-v-f99eab88] {
        background-color: #333;
        color: #ccc
    }
    .browser-required-btn[data-v-f99eab88] {
        background-color: #1f7ef8
    }
    .tool-tooltip .tooltip-content[data-v-f99eab88] {
        background-color: #232425;
        border-color: #444
    }
    .tool-tooltip .tooltip-arrow[data-v-f99eab88] {
        border-bottom-color: #232425
    }
    .tooltip-title[data-v-f99eab88] {
        color: #fff
    }
    .tooltip-description[data-v-f99eab88] {
        color: #ccc
    }
    .modal-container[data-v-f99eab88],
    .modal-overlay .modal-container[data-v-f99eab88] {
        background-color: #232425;
        color: #fff
    }
    .modal-header h2[data-v-f99eab88] {
        color: #fff
    }
    .modal-header .close-btn[data-v-f99eab88] {
        color: #aaa
    }
    .form-group input[data-v-f99eab88],
    .form-group textarea[data-v-f99eab88] {
        background-color: #333;
        border-color: #555;
        color: #fff
    }
    .form-group label[data-v-f99eab88],
    .radio-option label[data-v-f99eab88] {
        color: #ccc
    }
    .cancel-btn[data-v-f99eab88] {
        background-color: #333;
        color: #ccc
    }
    .loading-spinner[data-v-f99eab88] {
        border-color: #fff hsla(0, 0%, 100%, .2) hsla(0, 0%, 100%, .2)
    }
    .loading-container p[data-v-f99eab88] {
        color: #ccc
    }
    .modal-footer[data-v-f99eab88] {
        border-top-color: #444
    }
    .modal-header[data-v-f99eab88] {
        border-bottom-color: #444
    }
    .add-server-btn[data-v-f99eab88] {
        background: #333;
        border-color: #555;
        color: #ccc
    }
    .add-server-btn[data-v-f99eab88]:hover {
        background: #444;
        border-color: #666
    }
    .close[data-v-f99eab88] {
        color: #606366
    }
    .close[data-v-f99eab88]:hover {
        background-color: #444
    }
}

.radio-group[data-v-f99eab88] {
    display: flex;
    flex-direction: row;
    gap: 20px
}

.radio-option[data-v-f99eab88] {
    align-items: center;
    display: flex;
    gap: 4px
}

.radio-option input[type=radio][data-v-f99eab88] {
    accent-color: #000
}

.mcp-info-section[data-v-f99eab88] {
    font-size: 14px;
    margin-top: 16px;
    padding-top: 16px
}

.mcp-tools-tags[data-v-f99eab88] {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 12px
}

.mcp-tool-tag[data-v-f99eab88] {
    align-items: center;
    background-color: #e9f1fd;
    border-radius: 12px;
    color: #4285f4;
    display: flex;
    font-size: 11px;
    font-weight: 500;
    padding: 4px 10px;
    transition: all .2s ease;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.tool-tooltip[data-v-f99eab88] {
    animation: fade-in-f99eab88 .15s ease forwards;
    filter: drop-shadow(0 2px 6px rgba(0, 0, 0, .1));
    opacity: 0;
    pointer-events: none;
    position: absolute;
    z-index: 1001
}

.tooltip-sticky[data-v-f99eab88] {
    pointer-events: auto
}

.tooltip-arrow[data-v-f99eab88] {
    border-bottom: 6px solid #fff;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    filter: drop-shadow(0 -1px 1px rgba(0, 0, 0, .05));
    height: 0;
    left: 20px;
    position: absolute;
    top: -6px;
    width: 0
}

.tooltip-content[data-v-f99eab88] {
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 6px;
    box-shadow: 0 2px 8px #0000001a;
    max-width: 300px;
    padding: 12px
}

.tooltip-title[data-v-f99eab88] {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin: 0
}

.tooltip-description[data-v-f99eab88] {
    color: #555;
    font-size: 12px;
    line-height: 1.4;
    margin-top: 6px;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-line
}

@keyframes fade-in-f99eab88 {
    0% {
        opacity: 0;
        transform: translateY(-8px)
    }
    to {
        opacity: 1;
        transform: translateY(0)
    }
}

.extended-mcp-tools-grid[data-v-f99eab88] {
    margin-bottom: 40px
}

.category-filter-container[data-v-f99eab88] {
    margin: 20px 0;
    overflow-x: auto;
    padding: 0 4px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.category-filter-container[data-v-f99eab88]::-webkit-scrollbar {
    background: transparent;
    height: 0
}

.category-filter-container[data-v-f99eab88]:hover {
    scrollbar-width: thin
}

.category-filter-container[data-v-f99eab88]:hover::-webkit-scrollbar {
    height: 6px
}

.category-filter-container[data-v-f99eab88]:hover::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px
}

.category-filter-container[data-v-f99eab88]:hover::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 10px
}

.category-filter-container[data-v-f99eab88]:hover::-webkit-scrollbar-thumb:hover {
    background: #bbb
}

.category-filter-tags[data-v-f99eab88] {
    align-items: center;
    display: flex;
    flex-wrap: nowrap;
    gap: 8px;
    min-width: -moz-max-content;
    min-width: max-content;
    padding-bottom: 4px
}

.category-filter-tag[data-v-f99eab88] {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    color: #666;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    padding: 6px 14px;
    transition: all .2s ease;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    white-space: nowrap
}

.category-filter-tag[data-v-f99eab88]:hover {
    background-color: #e8f4ff;
    border-color: #b3d9ff;
    color: #4285f4
}

.category-filter-tag.active[data-v-f99eab88] {
    background-color: #4285f4;
    border-color: #4285f4;
    color: #fff
}

.tool-categories[data-v-f99eab88] {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 4px
}

.category-tag[data-v-f99eab88] {
    background-color: #f0f4ff;
    border-radius: 8px;
    color: #4285f4;
    font-size: 10px;
    font-weight: 500;
    letter-spacing: .02em;
    padding: 2px 6px;
    text-transform: uppercase
}

.external-link-icon[data-v-f99eab88] {
    align-items: center;
    color: #666;
    display: flex;
    height: 24px;
    justify-content: center;
    transition: color .2s ease;
    width: 24px
}

.tool-repository-info[data-v-f99eab88] {
    border-top: 1px solid #f0f0f0;
    margin-top: 12px;
    padding-top: 8px
}

.repository-name[data-v-f99eab88] {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    color: #666;
    font-family: SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, Courier New, monospace;
    font-size: 11px;
    padding: 2px 6px
}

@media (prefers-color-scheme:dark) {
    .category-tag[data-v-f99eab88] {
        background-color: #1a3a6c;
        color: #89b4ff
    }
    .external-link-icon[data-v-f99eab88] {
        color: #aaa
    }
    .tool-repository-info[data-v-f99eab88] {
        border-top-color: #333
    }
    .repository-name[data-v-f99eab88] {
        background-color: #2d2d2d;
        border-color: #444;
        color: #ccc
    }
    .category-filter-tag[data-v-f99eab88] {
        background-color: #333;
        border-color: #555;
        color: #aaa
    }
    .category-filter-tag[data-v-f99eab88]:hover {
        background-color: #1a3a6c;
        border-color: #4285f4;
        color: #89b4ff
    }
    .category-filter-tag.active[data-v-f99eab88] {
        background-color: #4285f4;
        border-color: #4285f4;
        color: #fff
    }
    .category-filter-container[data-v-f99eab88]:hover::-webkit-scrollbar-track {
        background: #444
    }
    .category-filter-container[data-v-f99eab88]:hover::-webkit-scrollbar-thumb {
        background: #666
    }
    .category-filter-container[data-v-f99eab88]:hover::-webkit-scrollbar-thumb:hover {
        background: #777
    }
}

.custom-tools-icon-container[data-v-effa800b] {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: visible !important;
    position: relative;
    transition: all .2s ease
}

.icon-wrapper[data-v-effa800b] {
    border-radius: 50%;
    justify-content: center;
    width: 36px
}

.icon-wrapper[data-v-effa800b],
.tools-wrapper[data-v-effa800b] {
    align-items: center;
    border: 1px solid #efefef;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    height: 36px;
    position: relative
}

.tools-wrapper[data-v-effa800b] {
    border-radius: 32px;
    color: #232425;
    gap: 4px;
    justify-content: flex-start;
    padding: 12px;
    transition: all .2s ease
}

.tools-wrapper[data-v-effa800b]:hover {
    background: #f5f5f5;
    border-color: #dcdfe0
}

@media (prefers-color-scheme:dark) {
    .tools-wrapper[data-v-effa800b] {
        border: 1px solid #efefef30;
        color: #efefef
    }
    .tools-wrapper[data-v-effa800b]:hover {
        background: #444
    }
}

.tool-icon[data-v-effa800b] {
    box-sizing: border-box !important;
    display: inline-block !important;
    flex-shrink: 0;
    -o-object-fit: contain;
    object-fit: contain;
    overflow: hidden !important;
    position: relative !important
}

.tool-icon[data-v-effa800b],
.tool-icon svg[data-v-effa800b] {
    height: 20px !important;
    width: 20px !important
}

.tool-icon[data-v-effa800b],
.tool-icon[data-v-effa800b] *,
.tool-icon svg[data-v-effa800b] {
    max-height: 20px !important;
    max-width: 20px !important
}

.tool-count[data-v-effa800b] {
    color: #909499;
    font-size: 12px;
    font-weight: 500;
    padding: 0 4px
}

.promption-tips-wrapper[data-v-effa800b] {
    align-items: center;
    border: 0;
    margin-top: 50px;
    position: absolute;
    z-index: 1000
}

.custom-tools-dropdown[data-v-effa800b] {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px #00000026;
    max-height: 400px;
    min-height: 200px;
    overflow: visible;
    position: fixed;
    width: 300px;
    z-index: 9999
}

.custom-tools-dropdown.dropdown-above[data-v-effa800b] {
    box-shadow: 0 -4px 12px #00000026
}

.custom-tools-dropdown.dropdown-below[data-v-effa800b] {
    box-shadow: 0 4px 12px #00000026
}

.promption-tips-wrapper[data-v-effa800b] svg path {
    fill: #ececec
}

@media (prefers-color-scheme:dark) {
    .tool-count[data-v-effa800b] {
        color: #fff
    }
    .promption-tips-wrapper[data-v-effa800b] svg path {
        fill: #444
    }
    .custom-tools-dropdown[data-v-effa800b] {
        background-color: #333;
        border: 1px solid #444
    }
    .custom-tools-dropdown.dropdown-above[data-v-effa800b] {
        box-shadow: 0 -4px 12px #0000004d
    }
    .custom-tools-dropdown.dropdown-below[data-v-effa800b] {
        box-shadow: 0 4px 12px #0000004d
    }
}

.model-selection-container[data-v-2c74e158] {
    align-items: center;
    display: flex;
    position: relative
}

.model-selection-button[data-v-2c74e158] {
    align-items: center;
    background: #fff;
    border: 1px solid #efefef;
    border-radius: 20px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    gap: 4px;
    height: 36px;
    padding: 8px;
    transition: all .2s ease
}

.model-selection-button[data-v-2c74e158]:hover {
    background: #f5f5f5;
    border-color: #dcdfe0
}

.model-selection-button.active[data-v-2c74e158] {
    background: #f8f9fa;
    border-color: #007bff
}

.model-selection-button.disabled[data-v-2c74e158] {
    cursor: not-allowed;
    opacity: .5;
    pointer-events: none
}

.model-icon[data-v-2c74e158] {
    border-radius: 2px;
    flex-shrink: 0;
    height: 20px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 20px
}

.model-label[data-v-2c74e158] {
    color: #333;
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.dropdown-icon[data-v-2c74e158] {
    color: #666;
    flex-shrink: 0;
    height: 16px;
    transition: transform .2s ease;
    width: 16px
}

.dropdown-icon.rotated[data-v-2c74e158] {
    transform: rotate(180deg)
}

.model-dropdown[data-v-2c74e158] {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 4px 12px #00000026;
    max-height: calc(100vh - 160px);
    max-width: 320px;
    min-width: 280px;
    overflow: hidden;
    position: fixed;
    z-index: 9999
}

.dropdown-content[data-v-2c74e158] {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    padding: 16px
}

.model-option[data-v-2c74e158] {
    border-radius: 12px;
    cursor: pointer;
    margin-bottom: 20px;
    transition: background-color .2s ease
}

.model-option[data-v-2c74e158]:last-child {
    margin-bottom: 0
}

.model-option .row[data-v-2c74e158] {
    align-items: center;
    display: flex;
    justify-content: space-between
}

.model-option .row[data-v-2c74e158]:has(+.description) {
    margin-bottom: 4px
}

.model-option .left[data-v-2c74e158] {
    align-items: center;
    display: flex;
    flex: 1;
    gap: 8px
}

.model-option .left .icon[data-v-2c74e158] {
    display: flex;
    flex-shrink: 0;
    height: 14px;
    width: 14px
}

.model-option-icon[data-v-2c74e158] {
    border-radius: 4px;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100%
}

.model-option .left .text[data-v-2c74e158] {
    color: #333;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4
}

.model-option .right[data-v-2c74e158] {
    align-items: center;
    display: flex;
    justify-content: flex-end
}

.model-option .right input[type=radio][data-v-2c74e158] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    cursor: pointer;
    height: 16px;
    margin: 0;
    position: relative;
    width: 16px
}

.model-option .right input[type=radio][data-v-2c74e158]:checked {
    background-color: #fff;
    border-color: #0f7fff
}

.model-option .right input[type=radio][data-v-2c74e158]:checked:after {
    background: #0f7fff;
    border-radius: 50%;
    content: "";
    height: 10px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 10px
}

.model-option .description[data-v-2c74e158] {
    margin-top: 0
}

.model-option .description .text[data-v-2c74e158] {
    color: #666;
    font-size: 12px;
    line-height: 1.4
}

@media (prefers-color-scheme:dark) {
    .model-selection-button[data-v-2c74e158] {
        background: #333;
        border-color: #444;
        color: #fff
    }
    .model-selection-button[data-v-2c74e158]:hover {
        background: #444;
        border-color: #555
    }
    .model-selection-button.active[data-v-2c74e158] {
        background: #2a2a2a;
        border-color: #09f
    }
    .model-selection-button.disabled[data-v-2c74e158] {
        cursor: not-allowed;
        opacity: .5;
        pointer-events: none
    }
    .model-label[data-v-2c74e158] {
        color: #fff
    }
    .dropdown-icon[data-v-2c74e158] {
        color: #ccc
    }
    .model-dropdown[data-v-2c74e158] {
        background: #333;
        border-color: #444;
        box-shadow: 0 4px 12px #0000004d
    }
    .model-option .left .text[data-v-2c74e158] {
        color: #fff
    }
    .model-option .description .text[data-v-2c74e158] {
        color: #ccc
    }
    .model-option .right input[type=radio][data-v-2c74e158] {
        background-color: #fff;
        border-color: #666
    }
    .model-option .right input[type=radio][data-v-2c74e158]:checked {
        background-color: #fff;
        border-color: #0f7fff
    }
}

.model-dropdown.dropdown-above[data-v-2c74e158] {
    box-shadow: 0 -4px 12px #00000026
}

.model-dropdown.dropdown-below[data-v-2c74e158] {
    box-shadow: 0 4px 12px #00000026
}

@media (prefers-color-scheme:dark) {
    .model-dropdown.dropdown-above[data-v-2c74e158] {
        box-shadow: 0 -4px 12px #0000004d
    }
    .model-dropdown.dropdown-below[data-v-2c74e158] {
        box-shadow: 0 4px 12px #0000004d
    }
}

.picker-wrapper[data-v-9d93498a] {
    left: -9999px;
    position: absolute;
    top: -9999px
}

.loading-popup[data-v-9d93498a] {
    background-color: #fffc;
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 9999
}

.loading-content[data-v-9d93498a],
.loading-popup[data-v-9d93498a] {
    align-items: center;
    display: flex;
    justify-content: center
}

.loading-content[data-v-9d93498a] {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px #0003;
    flex-direction: column;
    min-width: 200px;
    padding: 30px
}

.spinner[data-v-9d93498a] {
    animation: spin-9d93498a 1s linear infinite;
    border: 3px solid #f3f3f3;
    border-radius: 50%;
    border-top-color: #4285f4;
    height: 30px;
    margin-bottom: 10px;
    width: 30px
}

.loading-text[data-v-9d93498a] {
    color: #5f6368;
    font-size: 14px
}

@keyframes spin-9d93498a {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.error-popup[data-v-9d93498a] {
    left: 50%;
    position: fixed;
    top: 20px;
    transform: translate(-50%);
    z-index: 9999
}

.error-content[data-v-9d93498a] {
    background-color: #fff;
    border: 1px solid #fad2cf;
    border-radius: 4px;
    box-shadow: 0 2px 10px #0000001a;
    color: #d93025;
    font-size: 14px;
    max-width: 400px;
    padding: 10px 16px;
    position: relative;
    white-space: pre-wrap
}

.close-btn[data-v-9d93498a] {
    color: #5f6368;
    cursor: pointer;
    font-size: 18px;
    position: absolute;
    right: 8px;
    top: 8px
}

.close-btn[data-v-9d93498a]:hover {
    color: #d93025
}

@media (prefers-color-scheme:dark) {
    .loading-popup[data-v-9d93498a] {
        background-color: #000c
    }
    .loading-content[data-v-9d93498a] {
        background-color: #333
    }
    .loading-text[data-v-9d93498a] {
        color: #e0e0e0
    }
    .error-content[data-v-9d93498a] {
        background-color: #333;
        border-color: #d93025;
        color: #ff8a80
    }
}

.spin[data-v-0946bd67] {
    align-items: center;
    animation: spin-0946bd67 2s linear infinite;
    display: flex;
    justify-content: center;
    left: -24px;
    position: absolute;
    top: 1px
}

@keyframes spin-0946bd67 {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.buttons[data-v-0946bd67] {
    display: flex;
    flex-direction: row;
    justify-content: space-between
}

.button[data-v-0946bd67] {
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding: 6px 0;
    width: calc(50% - 5px)
}

.button .label[data-v-0946bd67] {
    position: relative
}

.cancel[data-v-0946bd67] {
    background: #f5f5f5;
    color: #606366
}

.confirm[data-v-0946bd67] {
    background: #232425;
    color: #fff
}

.wrapper[data-v-0946bd67] {
    background: #fff;
    border-radius: 16px;
    color: #000;
    max-width: calc(100vw - 32px);
    padding: 24px;
    width: 540px
}

.title[data-v-0946bd67] {
    color: #000;
    font-size: 20px
}

.desc[data-v-0946bd67],
.title[data-v-0946bd67] {
    font-style: normal;
    font-weight: 700;
    line-height: normal
}

.desc[data-v-0946bd67] {
    margin-top: 24px
}

.alert[data-v-0946bd67],
.desc[data-v-0946bd67] {
    color: #232425;
    font-size: 14px
}

.alert[data-v-0946bd67] {
    align-items: flex-start;
    align-self: stretch;
    background: #fffbee;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    font-style: normal;
    font-weight: 400;
    gap: 2px;
    line-height: 150%;
    padding: 12px
}

.github-binding-prompt-wrapper[data-v-16528abd] {
    align-items: center;
    background: #fff;
    border-radius: 24px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 11
}

@media (prefers-color-scheme:dark) {
    .github-binding-prompt-wrapper[data-v-16528abd] {
        background: #232425
    }
}

.github-binding-prompt-wrapper .github-binding-prompt-inner[data-v-16528abd] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
    max-width: 400px;
    padding: 24px;
    text-align: center
}

.github-binding-prompt-wrapper .title[data-v-16528abd] {
    color: #232425;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 150%
}

@media (prefers-color-scheme:dark) {
    .github-binding-prompt-wrapper .title[data-v-16528abd] {
        color: #fff
    }
}

.github-binding-prompt-wrapper .description[data-v-16528abd] {
    color: #232425;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%
}

@media (prefers-color-scheme:dark) {
    .github-binding-prompt-wrapper .description[data-v-16528abd] {
        color: #d1d5db
    }
}

.github-binding-prompt-wrapper .buttons[data-v-16528abd] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 12px;
    justify-content: center
}

.github-binding-prompt-wrapper .button[data-v-16528abd] {
    align-items: center;
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-weight: 500;
    gap: 8px;
    justify-content: center;
    min-width: 100px;
    padding: 8px 16px;
    transition: opacity .3s ease
}

.github-binding-prompt-wrapper .button.primary[data-v-16528abd] {
    background: #232425;
    color: #fff
}

.github-binding-prompt-wrapper .button.secondary[data-v-16528abd] {
    background: #f5f5f5;
    color: #606366
}

@media (prefers-color-scheme:dark) {
    .github-binding-prompt-wrapper .button.primary[data-v-16528abd] {
        background: #fff;
        color: #232425
    }
    .github-binding-prompt-wrapper .button.secondary[data-v-16528abd] {
        background: #374151;
        color: #d1d5db
    }
}

.github-binding-prompt-wrapper .button[data-v-16528abd]:hover {
    opacity: .8
}

.github-binding-prompt-wrapper .button .icon[data-v-16528abd] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 16px;
    justify-content: center;
    width: 16px
}

.github-binding-prompt-wrapper .button .label[data-v-16528abd] {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.upload-from-multiple-source-container[data-v-c432c120] {
    display: inline-block;
    position: relative
}

.upload-trigger-button[data-v-c432c120] {
    align-items: center;
    color: #232425;
    cursor: pointer;
    display: flex;
    justify-content: center
}

.upload-trigger-button[data-v-c432c120]:hover {
    opacity: .8
}

.upload-trigger-button.disabled[data-v-c432c120] {
    cursor: not-allowed;
    opacity: .5
}

.upload-trigger-button[data-v-c432c120] svg {
    height: 100%;
    width: 100%
}

.upload-options-popover[data-v-c432c120] {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px #00000026;
    min-width: 240px;
    padding: 8px 0
}

.upload-option-item[data-v-c432c120] {
    align-items: center;
    cursor: pointer;
    display: flex;
    gap: 12px;
    padding: 12px 16px;
    transition: background-color .2s ease
}

.upload-option-item[data-v-c432c120]:hover {
    background-color: #f5f5f5
}

.option-icon[data-v-c432c120] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 20px;
    justify-content: center;
    width: 20px
}

.option-icon[data-v-c432c120] svg {
    color: #232425;
    height: 100%;
    width: 100%
}

.option-label[data-v-c432c120] {
    color: #232425;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.4
}

@media (prefers-color-scheme:dark) {
    .upload-trigger-button[data-v-c432c120] {
        color: #ddd
    }
    .upload-options-popover[data-v-c432c120] {
        background: #333;
        border: 1px solid #444
    }
    .upload-option-item[data-v-c432c120]:hover {
        background-color: #404040
    }
    .option-label[data-v-c432c120] {
        color: #fff
    }
    .invert-icon[data-v-c432c120] {
        filter: invert(1)
    }
}

.select-files-dialog[data-v-a116a03a] {
    align-items: center;
    background-color: #00000080;
    bottom: 0;
    display: flex;
    justify-content: center;
    left: 0;
    padding: 20px;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1000
}

.dialog-content[data-v-a116a03a] {
    animation: zoomIn-a116a03a .15s ease-out forwards;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 25px #00000026;
    display: flex;
    flex-direction: column;
    height: 720px;
    max-height: min(720px, 80vh);
    max-width: 650px;
    min-width: 350px;
    width: 100%
}

@keyframes zoomIn-a116a03a {
    0% {
        opacity: 0;
        transform: scale(.95)
    }
    to {
        opacity: 1;
        transform: scale(1)
    }
}

.dialog-header[data-v-a116a03a] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    padding: 20px 20px 10px
}

.dialog-header h3[data-v-a116a03a] {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    margin: 0
}

.close-btn[data-v-a116a03a] {
    align-items: center;
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    display: flex;
    font-size: 24px;
    height: 24px;
    justify-content: center;
    line-height: 24px;
    padding: 0;
    transition: color .2s ease;
    width: 24px
}

.close-btn[data-v-a116a03a]:hover {
    color: #333
}

.breadcrumb-header-container[data-v-a116a03a] {
    align-items: center;
    display: flex;
    font-size: 14px;
    gap: 16px;
    justify-content: space-between;
    padding: 12px 20px;
    position: relative
}

.breadcrumb-header-container[data-v-a116a03a]:after {
    background-color: #eaeaea;
    bottom: 0;
    content: "";
    height: 1px;
    left: 20px;
    position: absolute;
    right: 20px
}

.breadcrumb-section[data-v-a116a03a] {
    color: #666;
    flex: 1;
    min-width: 0;
    overflow: hidden
}

.breadcrumb-section[data-v-a116a03a] .n-breadcrumb {
    flex-wrap: nowrap;
    font-size: 14px;
    overflow: hidden
}

.breadcrumb-section[data-v-a116a03a] .n-breadcrumb-item {
    color: #666;
    cursor: pointer;
    flex-shrink: 0
}

.breadcrumb-section[data-v-a116a03a] .n-breadcrumb-item__link {
    color: #666
}

.breadcrumb-section[data-v-a116a03a] .n-breadcrumb-item:hover:not(:last-child) .n-breadcrumb-item__link {
    color: #333
}

.breadcrumb-section[data-v-a116a03a] .n-breadcrumb-item:last-child {
    cursor: default;
    flex-shrink: 1;
    min-width: 0;
    overflow: hidden
}

.breadcrumb-section[data-v-a116a03a] .n-breadcrumb-item:last-child .last-crumb {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.breadcrumb-section[data-v-a116a03a] .n-breadcrumb-item__separator {
    align-items: center;
    color: #999;
    display: flex;
    margin: 0 8px
}

.breadcrumb-section[data-v-a116a03a] .n-breadcrumb-item__separator svg {
    height: 12px;
    width: 12px
}

.breadcrumb-ellipsis[data-v-a116a03a] {
    border-radius: 4px;
    cursor: pointer;
    margin: -2px -8px;
    padding: 2px 8px;
    transition: background-color .2s
}

.breadcrumb-ellipsis[data-v-a116a03a]:hover {
    background-color: #f5f5f5
}

.last-crumb[data-v-a116a03a] {
    color: #333;
    cursor: default !important;
    font-weight: 500
}

.modified-header[data-v-a116a03a] {
    color: #999;
    flex-shrink: 0;
    font-size: 12px;
    font-weight: 500;
    margin-right: 20px;
    white-space: nowrap
}

.file-list-container[data-v-a116a03a] {
    flex: 1;
    min-height: 0;
    overflow: hidden;
    position: relative
}

.empty-state[data-v-a116a03a],
.loading-state[data-v-a116a03a] {
    align-items: center;
    color: #999;
    display: flex;
    font-size: 14px;
    height: 100%;
    justify-content: center
}

.loading-icon[data-v-a116a03a] {
    animation: spin-a116a03a 1s linear infinite;
    height: 32px;
    width: 32px
}

@keyframes spin-a116a03a {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.file-list[data-v-a116a03a] {
    height: 100%;
    overflow-y: auto;
    padding: 8px 0;
    scrollbar-color: #d9d9d9 transparent;
    scrollbar-width: thin
}

.file-list[data-v-a116a03a]::-webkit-scrollbar {
    width: 8px
}

.file-list[data-v-a116a03a]::-webkit-scrollbar-track {
    background: transparent
}

.file-list[data-v-a116a03a]::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 4px
}

.file-list[data-v-a116a03a]::-webkit-scrollbar-thumb:hover {
    background-color: #bbb
}

.file-item[data-v-a116a03a] {
    align-items: center;
    border-radius: 12px;
    display: flex;
    margin: 0 20px 1px;
    padding: 12px 20px;
    transition: background-color .2s;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.file-item[data-v-a116a03a],
.file-item.folder[data-v-a116a03a] {
    cursor: pointer
}

.file-item[data-v-a116a03a]:hover:not(.unsupported) {
    background-color: #f5f5f5
}

.file-item.selected[data-v-a116a03a] {
    background-color: #ececec
}

.file-item.unsupported[data-v-a116a03a] {
    cursor: not-allowed
}

.file-item.unsupported .file-name[data-v-a116a03a],
.file-item.unsupported .file-time[data-v-a116a03a] {
    color: #999
}

.checkbox-area[data-v-a116a03a] {
    flex-shrink: 0;
    margin-right: 12px;
    width: 16px
}

.checkbox-selected[data-v-a116a03a] {
    align-items: center;
    background-color: #232425;
    border-radius: 4px;
    display: flex;
    height: 16px;
    justify-content: center;
    width: 16px
}

.checkbox-selected.disabled[data-v-a116a03a] {
    background-color: #d9d9d9
}

.checkbox-icon[data-v-a116a03a] {
    color: #fff;
    height: 10px;
    width: 10px
}

.checkbox-empty[data-v-a116a03a] {
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    height: 16px;
    width: 16px
}

.checkbox-empty.disabled[data-v-a116a03a] {
    opacity: .5
}

.file-icon[data-v-a116a03a] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    justify-content: center;
    margin-right: 12px
}

.file-icon.folder-icon[data-v-a116a03a] {
    margin-left: 12px
}

.file-icon[data-v-a116a03a] .file-type-icon {
    height: 24px;
    width: 24px
}

.file-icon.folder-icon[data-v-a116a03a] .file-type-icon {
    margin-left: 16px
}

.file-name[data-v-a116a03a] {
    color: #333;
    flex: 1;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.file-time[data-v-a116a03a] {
    color: #666;
    flex-shrink: 0;
    font-size: 14px;
    margin-left: 16px
}

.dialog-footer[data-v-a116a03a] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    padding: 16px 20px;
    position: relative
}

.dialog-footer[data-v-a116a03a]:before {
    background-color: #eaeaea;
    content: "";
    height: 1px;
    left: 20px;
    position: absolute;
    right: 20px;
    top: 0
}

.footer-left[data-v-a116a03a] {
    color: #666;
    font-size: 14px
}

.footer-right[data-v-a116a03a] {
    display: flex;
    gap: 16px
}

.cancel-btn[data-v-a116a03a],
.confirm-btn[data-v-a116a03a] {
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 16px;
    transition: all .2s
}

.cancel-btn[data-v-a116a03a] {
    background-color: #f5f5f5;
    color: #333
}

.cancel-btn[data-v-a116a03a]:hover {
    background-color: #eaeaea
}

.confirm-btn[data-v-a116a03a] {
    background-color: #1a1a1a;
    color: #fff
}

.confirm-btn[data-v-a116a03a]:hover:not(:disabled) {
    background-color: #333
}

.confirm-btn[data-v-a116a03a]:disabled {
    cursor: not-allowed;
    opacity: .45
}

.select-all-wrapper[data-v-a116a03a] {
    align-items: center;
    display: flex;
    gap: 8px
}

.select-all-checkbox[data-v-a116a03a] {
    align-items: center;
    cursor: pointer;
    display: flex;
    justify-content: center
}

.select-all-checkbox .checkbox-empty[data-v-a116a03a],
.select-all-checkbox .checkbox-indeterminate[data-v-a116a03a],
.select-all-checkbox .checkbox-selected[data-v-a116a03a] {
    border-radius: 4px;
    height: 16px;
    width: 16px
}

.select-all-checkbox .checkbox-selected[data-v-a116a03a] {
    align-items: center;
    background-color: #232425;
    display: flex;
    justify-content: center
}

.select-all-checkbox .checkbox-empty[data-v-a116a03a] {
    background-color: #fff;
    border: 1px solid #d9d9d9
}

.select-all-checkbox .checkbox-indeterminate[data-v-a116a03a] {
    align-items: center;
    background-color: #232425;
    display: flex;
    justify-content: center
}

.indeterminate-icon[data-v-a116a03a] {
    background-color: #fff;
    height: 2px;
    width: 8px
}

@media (prefers-color-scheme:dark) {
    .dialog-content[data-v-a116a03a] {
        background-color: #282828
    }
    .dialog-header h3[data-v-a116a03a] {
        color: #f0f0f0
    }
    .close-btn[data-v-a116a03a] {
        color: #b4b4b4
    }
    .close-btn[data-v-a116a03a]:hover {
        color: #f0f0f0
    }
    .breadcrumb-header-container[data-v-a116a03a]:after {
        background-color: #505050
    }
    .breadcrumb-section[data-v-a116a03a] {
        color: #e0e0e0
    }
    .breadcrumb-section[data-v-a116a03a] .n-breadcrumb-item,
    .breadcrumb-section[data-v-a116a03a] .n-breadcrumb-item__link {
        color: #e0e0e0 !important
    }
    .breadcrumb-section[data-v-a116a03a] .n-breadcrumb-item:hover:not(:last-child) .n-breadcrumb-item__link {
        color: #fff !important
    }
    .breadcrumb-section[data-v-a116a03a] .n-breadcrumb-item__separator {
        color: #999 !important
    }
    .breadcrumb-section[data-v-a116a03a] .n-breadcrumb-item__separator svg {
        filter: brightness(.8)
    }
    .breadcrumb-ellipsis[data-v-a116a03a]:hover {
        background-color: #3a3a3a
    }
    .last-crumb[data-v-a116a03a] {
        color: #fff !important
    }
    .modified-header[data-v-a116a03a] {
        color: #b4b4b4
    }
    .empty-state[data-v-a116a03a],
    .loading-state[data-v-a116a03a] {
        color: #666
    }
    .file-item[data-v-a116a03a]:hover:not(.unsupported) {
        background-color: #3a3a3a
    }
    .file-item.selected[data-v-a116a03a] {
        background-color: #484848
    }
    .file-item.unsupported .file-name[data-v-a116a03a],
    .file-item.unsupported .file-time[data-v-a116a03a] {
        color: #666
    }
    .checkbox-selected[data-v-a116a03a] {
        background-color: #232425
    }
    .checkbox-selected.disabled[data-v-a116a03a] {
        background-color: #404040
    }
    .checkbox-icon[data-v-a116a03a] {
        color: #fff
    }
    .checkbox-empty[data-v-a116a03a] {
        background-color: #3a3a3a;
        border-color: #505050
    }
    .file-name[data-v-a116a03a] {
        color: #f0f0f0
    }
    .file-time[data-v-a116a03a] {
        color: #b4b4b4
    }
    .dialog-footer[data-v-a116a03a]:before {
        background-color: #505050
    }
    .footer-left[data-v-a116a03a] {
        color: #b4b4b4
    }
    .cancel-btn[data-v-a116a03a] {
        background-color: #3c3c3c;
        color: #f0f0f0
    }
    .cancel-btn[data-v-a116a03a]:hover {
        background-color: #464646
    }
    .loading-icon[data-v-a116a03a] {
        filter: invert(1)
    }
    .file-icon[data-v-a116a03a] .file-type-icon.file-type-folder svg {
        filter: invert(1)
    }
    .file-list-header[data-v-a116a03a] {
        border-bottom-color: #505050;
        color: #666
    }
    .file-list[data-v-a116a03a] {
        scrollbar-color: #505050 transparent
    }
    .file-list[data-v-a116a03a]::-webkit-scrollbar-thumb {
        background-color: #505050
    }
    .file-list[data-v-a116a03a]::-webkit-scrollbar-thumb:hover {
        background-color: #666
    }
    .select-all-checkbox .checkbox-empty[data-v-a116a03a] {
        background-color: #3a3a3a;
        border-color: #505050
    }
    .select-all-checkbox .checkbox-indeterminate[data-v-a116a03a],
    .select-all-checkbox .checkbox-selected[data-v-a116a03a] {
        background-color: #232425
    }
}

.convert-document-type-prompt-wrapper[data-v-35796eef] {
    align-items: center;
    background: #fff;
    border-radius: 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 11
}

@media (prefers-color-scheme:dark) {
    .convert-document-type-prompt-wrapper[data-v-35796eef] {
        background: #232425
    }
}

.convert-document-type-prompt-wrapper .convert-document-type-prompt-inner[data-v-35796eef] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 12px;
    justify-content: center
}

.convert-document-type-prompt-wrapper .text[data-v-35796eef] {
    color: #232425;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .convert-document-type-prompt-wrapper .text[data-v-35796eef] {
        color: #fff
    }
}

.convert-document-type-prompt-wrapper .button[data-v-35796eef] {
    align-items: center;
    background: #232425;
    border-radius: 12px;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    gap: 10px;
    justify-content: center;
    max-width: 200px;
    padding: 6px 14px;
    transition: opacity .3s ease
}

.convert-document-type-prompt-wrapper .button.disabled[data-v-35796eef] {
    cursor: not-allowed;
    opacity: .5
}

@media (prefers-color-scheme:dark) {
    .convert-document-type-prompt-wrapper .button[data-v-35796eef] {
        background: #fff;
        color: #232425
    }
}

.convert-document-type-prompt-wrapper .button .icon[data-v-35796eef] {
    align-items: center;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    height: 16px;
    justify-content: center;
    width: 16px
}

@media (prefers-color-scheme:dark) {
    .convert-document-type-prompt-wrapper .button .icon[data-v-35796eef] {
        color: #232425
    }
}

.convert-document-type-prompt-wrapper .button .label[data-v-35796eef] {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.rollback-prompt-wrapper[data-v-ff21ecef] {
    align-items: center;
    background: #fff;
    border-radius: 24px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 11
}

@media (prefers-color-scheme:dark) {
    .rollback-prompt-wrapper[data-v-ff21ecef] {
        background: #232425
    }
}

.rollback-prompt-wrapper .rollback-prompt-inner[data-v-ff21ecef] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 12px;
    justify-content: center
}

.rollback-prompt-wrapper .text[data-v-ff21ecef] {
    color: #232425;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    text-align: center
}

@media (prefers-color-scheme:dark) {
    .rollback-prompt-wrapper .text[data-v-ff21ecef] {
        color: #fff
    }
}

.rollback-prompt-wrapper .button[data-v-ff21ecef] {
    align-items: center;
    background: #232425;
    border-radius: 12px;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    gap: 10px;
    justify-content: center;
    max-width: 200px;
    padding: 6px 14px
}

@media (prefers-color-scheme:dark) {
    .rollback-prompt-wrapper .button[data-v-ff21ecef] {
        background: #fff;
        color: #232425
    }
}

.rollback-prompt-wrapper .button .icon[data-v-ff21ecef] {
    align-items: center;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    height: 16px;
    justify-content: center;
    width: 16px
}

@media (prefers-color-scheme:dark) {
    .rollback-prompt-wrapper .button .icon[data-v-ff21ecef] {
        color: #232425
    }
}

.rollback-prompt-wrapper .button .label[data-v-ff21ecef] {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.chat-session-wrapper[data-v-ff21ecef] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    height: 36px;
    justify-content: center;
    width: 36px
}

@media (hover:hover) {
    .chat-session-wrapper[data-v-ff21ecef]:hover {
        background: #f5f5f5
    }
}

@media (prefers-color-scheme:dark) {
    .chat-session-wrapper[data-v-ff21ecef] {
        background: #333;
        border-color: #444
    }
    @media (hover:hover) {
        .chat-session-wrapper[data-v-ff21ecef]:hover {
            background: #444;
            border-color: #555
        }
    }
}

.chat-session-wrapper .icon[data-v-ff21ecef] {
    align-items: center;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    height: 24px;
    justify-content: center;
    padding: 4px;
    width: 24px
}

.chat-session-popover[data-v-ff21ecef] {
    max-height: 410px;
    max-width: 380px;
    overflow-y: auto
}

@media (max-width:1220px) {
    .chat-session-popover[data-v-ff21ecef] {
        max-height: calc(100vh - 300px);
        max-width: calc(100vw - 88px)
    }
}

.chat-session-popover .button[data-v-ff21ecef] {
    align-items: center;
    background: #232425;
    border-radius: 8px;
    box-sizing: border-box;
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 6px;
    height: 32px;
    line-height: 150%;
    padding: 5px 16px
}

.chat-session-popover .divider[data-v-ff21ecef] {
    background: #efefef;
    height: 1px;
    margin: 0 0 16px;
    width: 100%
}

.chat-session-popover .new-chat-session[data-v-ff21ecef] {
    align-items: flex-start;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    justify-content: flex-start;
    padding: 16px
}

.chat-session-popover .new-chat-session .desc[data-v-ff21ecef] {
    color: #232425;
    font-family: Arial;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%
}

@media (prefers-color-scheme:dark) {
    .chat-session-popover .new-chat-session .desc[data-v-ff21ecef] {
        color: #fff
    }
}

.chat-session-popover .icon[data-v-ff21ecef] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    width: 16px
}

.chat-session-history-item[data-v-ff21ecef] {
    color: #232425;
    cursor: pointer;
    height: 28px;
    opacity: .5
}

@media (prefers-color-scheme:dark) {
    .chat-session-history-item[data-v-ff21ecef] {
        color: #fff
    }
}

.chat-session-history-item.active[data-v-ff21ecef] {
    opacity: 1
}

@media (hover:hover) {
    .chat-session-history-item[data-v-ff21ecef]:hover {
        opacity: 1
    }
}

.stop-icon[data-v-ff21ecef] {
    align-items: center;
    display: flex;
    height: 14px;
    justify-content: center;
    width: 14px
}

.remove[data-v-ff21ecef] svg {
    height: 100%;
    width: 100%
}

.prompt-files .prompt-file.image[data-v-ff21ecef] {
    height: 50px;
    padding: 0;
    width: 50px
}

.prompt-files .prompt-file.image .remove[data-v-ff21ecef] {
    right: -8px;
    top: -8px
}

.file-wrapper[data-v-ff21ecef] {
    align-items: center;
    background: #fafafa;
    border: 1px solid #efefef;
    border-radius: 12px;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    gap: 8px;
    height: 48px;
    justify-content: flex-start;
    padding: 0 8px;
    width: 152px
}

.file-wrapper .file-icon[data-v-ff21ecef] {
    background-color: #fff;
    border-radius: 8px;
    box-sizing: border-box;
    flex-shrink: 0;
    height: 36px;
    padding: 6px;
    width: 36px
}

.file-wrapper .file-icon[data-v-ff21ecef] svg {
    height: 100%;
    width: 100%
}

.file-wrapper .file-info[data-v-ff21ecef] {
    flex-grow: 1;
    max-width: 100px
}

.file-wrapper .file-info .file-name[data-v-ff21ecef] {
    color: #232425;
    display: block;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

.file-wrapper .file-info .file-size[data-v-ff21ecef] {
    color: #909499;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    width: 100%
}

.textarea-wrapper[data-v-ff21ecef] {
    box-sizing: border-box;
    display: flex;
    gap: 10px;
    overflow: visible;
    position: relative;
    z-index: 10
}

.prompt-files[data-v-ff21ecef] {
    display: flex;
    flex-direction: row;
    gap: 8px;
    overflow-x: auto;
    padding: 12px 0 0;
    width: 100%
}

.prompt-files .prompt-file[data-v-ff21ecef] {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    justify-content: center;
    position: relative
}

.prompt-files .prompt-file-inner[data-v-ff21ecef] {
    height: 100%;
    position: relative;
    width: 100%
}

.prompt-files .prompt-file .remove[data-v-ff21ecef] {
    align-items: center;
    background: #000;
    border-radius: 50%;
    color: #fff;
    cursor: pointer;
    display: flex;
    height: 16px;
    justify-content: center;
    position: absolute;
    right: 0;
    top: -8px;
    width: 16px;
    z-index: 1
}

.prompt-files .prompt-file img[data-v-ff21ecef] {
    border-radius: 12px;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.prompt-files .prompt-file.template-card[data-v-ff21ecef] {
    height: 48px;
    width: auto
}

.template-card-wrapper[data-v-ff21ecef] {
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    height: 48px;
    max-width: 240px;
    min-width: 200px;
    overflow: hidden
}

@media (prefers-color-scheme:dark) {
    .template-card-wrapper[data-v-ff21ecef] {
        background: #2a2a2a;
        border: 1px solid #444
    }
}

.template-preview[data-v-ff21ecef] {
    flex-shrink: 0;
    height: 48px;
    overflow: hidden
}

.template-preview-image[data-v-ff21ecef] {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.prompt-files .prompt-file.template-card img[data-v-ff21ecef] {
    border-radius: 0
}

.template-info[data-v-ff21ecef] {
    flex: 1;
    min-width: 0;
    padding: 0 8px
}

.template-name[data-v-ff21ecef] {
    color: #232425;
    display: block;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

@media (prefers-color-scheme:dark) {
    .template-name[data-v-ff21ecef] {
        color: #fff
    }
}

.template-label[data-v-ff21ecef] {
    color: #909499;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    width: 100%
}

.search-input-wrapper-background[data-v-ff21ecef] {
    background: #0f7fff;
    background: linear-gradient(270deg, #000, #000 45%, #376cd2, #0f7fff 55%, #0f7fff);
    height: 100%;
    margin-left: -170%;
    position: absolute;
    top: 0;
    transition: all 1s ease-in-out;
    width: 300%;
    z-index: -1
}

.searchAskFollowUp .search-input-wrapper-background[data-v-ff21ecef],
.sparkQuestion .search-input-wrapper-background[data-v-ff21ecef] {
    background: #fff
}

.active .search-input-wrapper-background[data-v-ff21ecef] {
    margin-left: -10%
}

.input.sparkQuestion[data-v-ff21ecef],
.input.white[data-v-ff21ecef] {
    background: #fff;
    color: #d9d9d9
}

.input.sparkQuestion[data-v-ff21ecef] {
    padding-right: 4px
}

.input.sparkQuestion textarea[data-v-ff21ecef],
.input.white textarea[data-v-ff21ecef] {
    background: transparent;
    color: #606366
}

.input.searchAskFollowUp textarea[data-v-ff21ecef],
.input.searchAskFollowUp.active textarea[data-v-ff21ecef] {
    caret-color: #232425;
    color: #232425
}

.input.sparkQuestion.active textarea[data-v-ff21ecef] {
    caret-color: #606366;
    color: #606366
}

.sparkQuestion .input-icon[data-v-ff21ecef],
.white .input-icon[data-v-ff21ecef] {
    background: #f2f2f2;
    border-radius: 8px;
    color: #232425;
    height: 20px;
    padding: 4px;
    width: 20px
}

.searchAskFollowUp .input-icon[data-v-ff21ecef] {
    height: 32px;
    width: 32px
}

.icon-group[data-v-ff21ecef] {
    background: #fff;
    display: flex;
    overflow: visible;
    position: relative
}

.icon-group.in-speech[data-v-ff21ecef] {
    z-index: 11
}

@media (prefers-color-scheme:dark) {
    .icon-group[data-v-ff21ecef] {
        background: #333
    }
}

.right-icon-group[data-v-ff21ecef] {
    align-items: center;
    display: flex;
    justify-content: flex-end;
    width: 100%
}

.searchAskFollowUp .icon-group[data-v-ff21ecef] {
    margin-right: 0
}

.sparkQuestion .input-icon[data-v-ff21ecef] {
    background: #fff
}

.sparkQuestion .input-icon[data-v-ff21ecef]:hover {
    background: #2324250f
}

.input-icon[data-v-ff21ecef] svg {
    align-items: center;
    display: flex;
    justify-content: center;
    width: 100%
}

.white[data-v-ff21ecef] .input-icon *,
.white.active[data-v-ff21ecef] .input-icon * {
    fill: #232425
}

.sparkQuestion[data-v-ff21ecef] .input-icon * {
    fill: #232425;
    opacity: .65
}

.sparkQuestion.active[data-v-ff21ecef] .input-icon * {
    fill: #606366;
    opacity: 1
}

.input[data-v-ff21ecef] {
    align-items: stretch;
    border-radius: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: visible;
    position: relative;
    z-index: 1
}

.input.searchAskFollowUp[data-v-ff21ecef] {
    border: 1px solid #efefef
}

.input.searchAskFollowUp.focus[data-v-ff21ecef] {
    box-shadow: 0 1px 8px #00000014
}

.input.has-suggestions[data-v-ff21ecef] {
    border-radius: 12px 12px 0 0
}

.input.sparkQuestion[data-v-ff21ecef] {
    border: 1px solid #fff
}

.input.has-suggestions.sparkQuestion[data-v-ff21ecef] {
    border-radius: 0 0 12px 12px
}

.input.focus.sparkQuestion[data-v-ff21ecef] {
    border: 1px solid #dcdfe0
}

.input textarea[data-v-ff21ecef] {
    background: transparent;
    border: 0;
    box-sizing: border-box;
    font-size: 16px;
    outline: none;
    resize: none;
    transition: color 1s ease-in-out;
    width: 100%
}

.input-icon[data-v-ff21ecef] {
    align-items: center;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    justify-content: center
}

.active .input-icon[data-v-ff21ecef] {
    color: #fff
}

.searchAskFollowUp .input-icon[data-v-ff21ecef] {
    color: #232425
}

.prefix-button[data-v-ff21ecef] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    font-weight: 500;
    justify-content: start;
    padding: 0 12px
}

@media (max-width:1220px) {
    .input-icon.expand[data-v-ff21ecef] {
        display: none
    }
}

@media (prefers-color-scheme:dark) {
    .input.sparkQuestion[data-v-ff21ecef],
    .searchAskFollowUp .search-input-wrapper-background[data-v-ff21ecef],
    .sparkQuestion .search-input-wrapper-background[data-v-ff21ecef] {
        background: #232425
    }
    .input.active textarea[data-v-ff21ecef],
    .input.active.searchAskFollowUp textarea[data-v-ff21ecef],
    .input.active.sparkQuestion textarea[data-v-ff21ecef],
    .input.active.white textarea[data-v-ff21ecef],
    .input.searchAskFollowUp textarea[data-v-ff21ecef],
    .input.sparkQuestion textarea[data-v-ff21ecef],
    .input.white textarea[data-v-ff21ecef] {
        caret-color: #fff;
        color: #fff
    }
    .input-icon[data-v-ff21ecef] svg:hover {
        fill: #fff
    }
    .spin-icon[data-v-ff21ecef] svg:hover {
        fill: #000
    }
    .white[data-v-ff21ecef] .input-icon *,
    .white.active[data-v-ff21ecef] .input-icon * {
        fill: #fff
    }
    .searchAskFollowUp[data-v-ff21ecef] .input-icon *,
    .sparkQuestion[data-v-ff21ecef] .input-icon * {
        fill: #fff;
        color: #fff;
        opacity: .8
    }
    .searchAskFollowUp.active[data-v-ff21ecef] .input-icon *,
    .sparkQuestion.active[data-v-ff21ecef] .input-icon * {
        fill: #fff;
        color: #fff;
        opacity: 1
    }
    .index .search-input-wrapper-background[data-v-ff21ecef] {
        background: linear-gradient(270deg, #333, #333 45%, #376cd2, #0f7fff 55%, #0f7fff)
    }
    .input.index[data-v-ff21ecef] {
        border: 1px solid transparent
    }
    .input.index.has-suggestions[data-v-ff21ecef] {
        border: 1px solid #0f7fff
    }
}

.moa .search-input-wrapper-background[data-v-ff21ecef] {
    background: #fff
}

.moa.input[data-v-ff21ecef] {
    border-radius: 16px
}

.moa.input textarea[data-v-ff21ecef] {
    color: #232425
}

.moa .icon-group[data-v-ff21ecef] {
    margin-right: 0
}

@media (prefers-color-scheme:dark) {
    .moa .input-icon[data-v-ff21ecef] {
        color: #232425
    }
    .moa .search-input-wrapper-background[data-v-ff21ecef] {
        background: #333
    }
    .moa.input textarea[data-v-ff21ecef] {
        color: #fff
    }
    .search-input[data-v-ff21ecef] {
        scrollbar-color: #676767 transparent
    }
}

.enter-icon[data-v-ff21ecef] {
    display: flex;
    flex-direction: column;
    height: 100%
}

.enter-icon-wrapper[data-v-ff21ecef] {
    justify-content: center;
    width: 36px
}

.enter-icon-wrapper[data-v-ff21ecef],
.research-me-btn[data-v-ff21ecef] {
    align-items: center;
    display: flex;
    height: 36px
}

.research-me-btn[data-v-ff21ecef] {
    border: 1px solid #efefef;
    border-radius: 32px;
    box-sizing: border-box;
    color: #232425;
    justify-content: flex-start;
    padding: 12px;
    transition: all .2s ease
}

.research-me-btn[data-v-ff21ecef]:hover {
    background: #f5f5f5;
    border-color: #dcdfe0
}

.research-me-btn-icon[data-v-ff21ecef] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 50%;
    box-sizing: border-box;
    display: flex;
    height: 36px;
    justify-content: center;
    width: 36px
}

@media (prefers-color-scheme:dark) {
    .research-me-btn[data-v-ff21ecef] {
        background: #333;
        color: #fff
    }
    .research-me-btn-icon[data-v-ff21ecef] {
        border: 1px solid #efefef30
    }
    .research-me-btn-icon[data-v-ff21ecef]:hover {
        border: 1px solid #efefef
    }
    .research-me-btn[data-v-ff21ecef] {
        border: 1px solid #efefef2f
    }
    .research-me-btn[data-v-ff21ecef]:hover {
        background: #444
    }
}

.custom-tools-dropdown[data-v-ff21ecef] {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px #00000026;
    position: absolute;
    width: 235px;
    z-index: 9999
}

.icon-group .flex.items-center.gap-2[data-v-ff21ecef] {
    overflow: visible;
    position: relative
}

@media (prefers-color-scheme:dark) {
    .custom-tools-dropdown[data-v-ff21ecef] {
        background-color: #333;
        border: 1px solid #444
    }
}

.model-selection-icon-container[data-v-ff21ecef] {
    align-items: center;
    display: flex;
    justify-content: center;
    overflow: visible;
    position: relative
}

@media (max-width:1880px) and (min-width:1220px) {
    .search-input-wrapper[data-v-e894f63c] {
        min-width: var(--container-width, 760px);
        width: var(--container-width, 760px)
    }
}

@media (min-width:1880px) {
    .search-input-wrapper[data-v-e894f63c] {
        min-width: var(--container-width, 760px);
        width: var(--container-width, 760px)
    }
}