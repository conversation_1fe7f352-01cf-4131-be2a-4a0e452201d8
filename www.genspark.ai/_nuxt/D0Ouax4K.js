import {
    d as t,
    o as c,
    b as l,
    D as a
} from "./Cf0SOiw0.js";
const h = {
    width: "16",
    height: "16",
    viewBox: "0 0 16 16",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const p = {
        render: function(a, p) {
            return c(), t("svg", h, p[0] || (p[0] = [l("g", {
                id: "Group 1171276685"
            }, [l("path", {
                id: "Vector 49",
                d: "M4 8.27273L6.66667 11L12 6",
                stroke: "currentColor",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            })], -1)]))
        }
    },
    e = {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 24 24"
    };
const i = {
        render: function(l, h) {
            return c(), t("svg", e, h[0] || (h[0] = [a('<path fill="#4285f4" d="M1.5 20h3.5V9.5L0 6v12c0 1.1 0.67 2 1.5 2"></path><path fill="#34a853" d="M17 20h3.5c0.83 0 1.5-0.9 1.5-2V6l-5 3.5"></path><path fill="#fbbc04" d="M17 4v6.5l5-3.5V5c0-2.47-2.12-3.88-3.6-2.4"></path><path fill="#ea4335" d="M5 10.5V4l6 4.5L17 4v6.5L11 15"></path><path fill="#c5221f" d="M0 5v2l5 3.5V4L3.6 2.6C2.12 1.12 0 2.53 0 5"></path>', 5)]))
        }
    },
    o = {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 24 24"
    };
const n = {
        render: function(l, h) {
            return c(), t("svg", o, h[0] || (h[0] = [a('<g><path fill="#FFFFFF" d="M17.8 5.25l-5.7-0.63l-6.95 0.63l-0.63 6.32l0.63 6.32l6.32 0.8l6.32-0.8l0.63-6.47L17.8 5.25z"></path><path fill="#1A73E8" d="M7.8 15c-0.47-0.32-0.8-0.78-0.98-1.4l1.1-0.45c0.1 0.38 0.27 0.67 0.52 0.88c0.25 0.21 0.55 0.31 0.9 0.31c0.36 0 0.67-0.11 0.92-0.33s0.39-0.5 0.39-0.83c0-0.34-0.14-0.63-0.41-0.84s-0.61-0.33-1.02-0.33h-0.63v-1.08h0.76c0.35 0 0.65-0.09 0.89-0.28c0.24-0.19 0.36-0.45 0.36-0.78c0-0.29-0.11-0.53-0.32-0.7s-0.49-0.26-0.82-0.26c-0.32 0-0.58 0.09-0.77 0.26s-0.33 0.38-0.41 0.63l-1.08-0.45c0.14-0.41 0.41-0.77 0.79-1.08c0.39-0.31 0.88-0.47 1.48-0.47c0.44 0 0.84 0.09 1.2 0.26c0.35 0.17 0.63 0.41 0.83 0.71c0.2 0.3 0.3 0.65 0.3 1.02c0 0.39-0.09 0.71-0.28 0.98c-0.19 0.27-0.42 0.47-0.69 0.62v0.06c0.36 0.15 0.65 0.38 0.88 0.69c0.23 0.31 0.34 0.68 0.34 1.11s-0.11 0.81-0.33 1.15c-0.22 0.34-0.52 0.6-0.9 0.79c-0.38 0.19-0.82 0.29-1.29 0.29C8.83 15.5 8.27 15.33 7.8 15z"></path><path fill="#1A73E8" d="M14.5 9.58L13.3 10.45l-0.6-0.91l2.16-1.56h0.83v7.34h-1.19V9.58z"></path><path fill="#EA4335" d="M17.8 23.5l5.68-5.68l-2.84-1.26l-2.84 1.26l-1.26 2.84L17.8 23.5z"></path><path fill="#34A853" d="M3.96 20.66l1.26 2.84h12.63v-5.68H5.22L3.96 20.66z"></path><path fill="#4285F4" d="M1.44 0.5C0.39 0.5-0.45 1.4-0.45 2.45v16.42l2.84 1.26l2.84-1.26V5.25h12.63l1.26-2.84L17.8 0.5H1.44z"></path><path fill="#188038" d="M-0.45 17.87v3.79c0 1.05 0.85 1.9 1.9 1.9h3.79v-5.68H-0.45z"></path><path fill="#FBBC04" d="M17.8 5.25v12.63h5.68V5.25l-2.84-1.26L17.8 5.25z"></path><path fill="#1967D2" d="M23.5 5.25V2.45c0-1.05-0.85-1.9-1.9-1.9h-3.79v5.68H23.5z"></path></g>', 1)]))
        }
    },
    r = {
        viewBox: "0 0 87.3 78",
        xmlns: "http://www.w3.org/2000/svg"
    };
const d = {
        render: function(l, h) {
            return c(), t("svg", r, h[0] || (h[0] = [a('<path d="m6.6 66.85 3.85 6.65c.8 1.4 1.95 2.5 3.3 3.3l13.75-23.8h-27.5c0 1.55.4 3.1 1.2 4.5z" fill="#0066da"></path><path d="m43.65 25-13.75-23.8c-1.35.8-2.5 1.9-3.3 3.3l-25.4 44a9.06 9.06 0 0 0 -1.2 4.5h27.5z" fill="#00ac47"></path><path d="m73.55 76.8c1.35-.8 2.5-1.9 3.3-3.3l1.6-2.75 7.65-13.25c.8-1.4 1.2-2.95 1.2-4.5h-27.502l5.852 11.5z" fill="#ea4335"></path><path d="m43.65 25 13.75-23.8c-1.35-.8-2.9-1.2-4.5-1.2h-18.5c-1.6 0-3.15.45-4.5 1.2z" fill="#00832d"></path><path d="m59.8 53h-32.3l-13.75 23.8c1.35.8 2.9 1.2 4.5 1.2h50.8c1.6 0 3.15-.45 4.5-1.2z" fill="#2684fc"></path><path d="m73.4 26.5-12.7-22c-.8-1.4-1.95-2.5-3.3-3.3l-13.75 23.8 16.15 28h27.45c0-1.55-.4-3.1-1.2-4.5z" fill="#ffba00"></path>', 6)]))
        }
    },
    s = {
        width: "100",
        height: "100",
        viewBox: "0 0 100 100",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const f = {
        render: function(a, h) {
            return c(), t("svg", s, h[0] || (h[0] = [l("path", {
                d: "M6.017 4.313l55.333 -4.087c6.797 -0.583 8.543 -0.19 12.817 2.917l17.663 12.443c2.913 2.14 3.883 2.723 3.883 5.053v68.243c0 4.277 -1.553 6.807 -6.99 7.193L24.467 99.967c-4.08 0.193 -6.023 -0.39 -8.16 -3.113L3.3 79.94c-2.333 -3.113 -3.3 -5.443 -3.3 -8.167V11.113c0 -3.497 1.553 -6.413 6.017 -6.8z",
                fill: "#fff"
            }, null, -1), l("path", {
                "fill-rule": "evenodd",
                "clip-rule": "evenodd",
                d: "M61.35 0.227l-55.333 4.087C1.553 4.7 0 7.617 0 11.113v60.66c0 2.723 0.967 5.053 3.3 8.167l13.007 16.913c2.137 2.723 4.08 3.307 8.16 3.113l64.257 -3.89c5.433 -0.387 6.99 -2.917 6.99 -7.193V20.64c0 -2.21 -0.873 -2.847 -3.443 -4.733L74.167 3.143c-4.273 -3.107 -6.02 -3.5 -12.817 -2.917zM25.92 19.523c-5.247 0.353 -6.437 0.433 -9.417 -1.99L8.927 11.507c-0.77 -0.78 -0.383 -1.753 1.557 -1.947l53.193 -3.887c4.467 -0.39 6.793 1.167 8.54 2.527l9.123 6.61c0.39 0.197 1.36 1.36 0.193 1.36l-54.933 3.307 -0.68 0.047zM19.803 88.3V30.367c0 -2.53 0.777 -3.697 3.103 -3.893L86 22.78c2.14 -0.193 3.107 1.167 3.107 3.693v57.547c0 2.53 -0.39 4.67 -3.883 4.863l-60.377 3.5c-3.493 0.193 -5.043 -0.97 -5.043 -4.083zm59.6 -54.827c0.387 1.75 0 3.5 -1.75 3.7l-2.91 0.577v42.773c-2.527 1.36 -4.853 2.137 -6.797 2.137 -3.107 0 -3.883 -0.973 -6.21 -3.887l-19.03 -29.94v28.967l6.02 1.363s0 3.5 -4.857 3.5l-13.39 0.777c-0.39 -0.78 0 -2.723 1.357 -3.11l3.497 -0.97v-38.3L30.48 40.667c-0.39 -1.75 0.58 -4.277 3.3 -4.473l14.367 -0.967 19.8 30.327v-26.83l-5.047 -0.58c-0.39 -2.143 1.163 -3.7 3.103 -3.89l13.4 -0.78z",
                fill: "#000"
            }, null, -1)]))
        }
    },
    v = {
        version: "1.1",
        id: "Livello_1",
        "xmlns:x": "http://ns.adobe.com/Extensibility/1.0/",
        "xmlns:i": "http://ns.adobe.com/AdobeIllustrator/10.0/",
        "xmlns:graph": "http://ns.adobe.com/Graphs/1.0/",
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        x: "0px",
        y: "0px",
        viewBox: "0 0 1831.085 1703.335",
        "enable-background": "new 0 0 1831.085 1703.335",
        "xml:space": "preserve"
    };
const M = {
        render: function(l, h) {
            return c(), t("svg", v, h[0] || (h[0] = [a('<path fill="#0A2767" d="M1831.083,894.25c0.1-14.318-7.298-27.644-19.503-35.131h-0.213l-0.767-0.426l-634.492-375.585  c-2.74-1.851-5.583-3.543-8.517-5.067c-24.498-12.639-53.599-12.639-78.098,0c-2.934,1.525-5.777,3.216-8.517,5.067L446.486,858.693  l-0.766,0.426c-19.392,12.059-25.337,37.556-13.278,56.948c3.553,5.714,8.447,10.474,14.257,13.868l634.492,375.585  c2.749,1.835,5.592,3.527,8.517,5.068c24.498,12.639,53.599,12.639,78.098,0c2.925-1.541,5.767-3.232,8.517-5.068l634.492-375.585  C1823.49,922.545,1831.228,908.923,1831.083,894.25z"></path><path fill="#0364B8" d="M520.453,643.477h416.38v381.674h-416.38V643.477z M1745.917,255.5V80.908  c1-43.652-33.552-79.862-77.203-80.908H588.204C544.552,1.046,510,37.256,511,80.908V255.5l638.75,170.333L1745.917,255.5z"></path><path fill="#0078D4" d="M511,255.5h425.833v383.25H511V255.5z"></path><path fill="#28A8EA" d="M1362.667,255.5H936.833v383.25L1362.667,1022h383.25V638.75L1362.667,255.5z"></path><path fill="#0078D4" d="M936.833,638.75h425.833V1022H936.833V638.75z"></path><path fill="#0364B8" d="M936.833,1022h425.833v383.25H936.833V1022z"></path><path fill="#14447D" d="M520.453,1025.151h416.38v346.969h-416.38V1025.151z"></path><path fill="#0078D4" d="M1362.667,1022h383.25v383.25h-383.25V1022z"></path><linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="1128.4584" y1="811.0833" x2="1128.4584" y2="1.9982" gradientTransform="matrix(1 0 0 -1 0 1705.3334)"><stop offset="0" style="stop-color:#35B8F1;"></stop><stop offset="1" style="stop-color:#28A8EA;"></stop></linearGradient><path fill="url(#SVGID_1_)" d="M1811.58,927.593l-0.809,0.426l-634.492,356.848c-2.768,1.703-5.578,3.321-8.517,4.769  c-10.777,5.132-22.481,8.029-34.407,8.517l-34.663-20.27c-2.929-1.47-5.773-3.105-8.517-4.897L447.167,906.003h-0.298  l-21.036-11.753v722.384c0.328,48.196,39.653,87.006,87.849,86.7h1230.914c0.724,0,1.363-0.341,2.129-0.341  c10.18-0.651,20.216-2.745,29.808-6.217c4.145-1.756,8.146-3.835,11.966-6.217c2.853-1.618,7.75-5.152,7.75-5.152  c21.814-16.142,34.726-41.635,34.833-68.772V894.25C1831.068,908.067,1823.616,920.807,1811.58,927.593z"></path><path opacity="0.5" fill="#0A2767" enable-background="new    " d="M1797.017,891.397v44.287l-663.448,456.791L446.699,906.301  c0-0.235-0.191-0.426-0.426-0.426l0,0l-63.023-37.899v-31.938l25.976-0.426l54.932,31.512l1.277,0.426l4.684,2.981  c0,0,645.563,368.346,647.267,369.197l24.698,14.478c2.129-0.852,4.258-1.703,6.813-2.555  c1.278-0.852,640.879-360.681,640.879-360.681L1797.017,891.397z"></path><path fill="#1490DF" d="M1811.58,927.593l-0.809,0.468l-634.492,356.848c-2.768,1.703-5.578,3.321-8.517,4.769  c-24.641,12.038-53.457,12.038-78.098,0c-2.918-1.445-5.76-3.037-8.517-4.769L446.657,928.061l-0.766-0.468  c-12.25-6.642-19.93-19.409-20.057-33.343v722.384c0.305,48.188,39.616,87.004,87.803,86.7c0.001,0,0.002,0,0.004,0h1229.636  c48.188,0.307,87.5-38.509,87.807-86.696c0-0.001,0-0.002,0-0.004V894.25C1831.068,908.067,1823.616,920.807,1811.58,927.593z"></path><path opacity="0.1" enable-background="new    " d="M1185.52,1279.629l-9.496,5.323c-2.752,1.752-5.595,3.359-8.517,4.812  c-10.462,5.135-21.838,8.146-33.47,8.857l241.405,285.479l421.107,101.476c11.539-8.716,20.717-20.178,26.7-33.343L1185.52,1279.629  z"></path><path opacity="0.05" enable-background="new    " d="M1228.529,1255.442l-52.505,29.51c-2.752,1.752-5.595,3.359-8.517,4.812  c-10.462,5.135-21.838,8.146-33.47,8.857l113.101,311.838l549.538,74.989c21.649-16.254,34.394-41.743,34.407-68.815v-9.326  L1228.529,1255.442z"></path><path fill="#28A8EA" d="M514.833,1703.333h1228.316c18.901,0.096,37.335-5.874,52.59-17.033l-697.089-408.331  c-2.929-1.47-5.773-3.105-8.517-4.897L447.125,906.088h-0.298l-20.993-11.838v719.914  C425.786,1663.364,465.632,1703.286,514.833,1703.333C514.832,1703.333,514.832,1703.333,514.833,1703.333z"></path><path opacity="0.1" enable-background="new    " d="M1022,418.722v908.303c-0.076,31.846-19.44,60.471-48.971,72.392  c-9.148,3.931-19,5.96-28.957,5.962H425.833V383.25H511v-42.583h433.073C987.092,340.83,1021.907,375.702,1022,418.722z"></path><path opacity="0.2" enable-background="new    " d="M979.417,461.305v908.302c0.107,10.287-2.074,20.469-6.388,29.808  c-11.826,29.149-40.083,48.273-71.54,48.417H425.833V383.25h475.656c12.356-0.124,24.533,2.958,35.344,8.943  C962.937,405.344,979.407,432.076,979.417,461.305z"></path><path opacity="0.2" enable-background="new    " d="M979.417,461.305v823.136c-0.208,43-34.928,77.853-77.927,78.225H425.833V383.25  h475.656c12.356-0.124,24.533,2.958,35.344,8.943C962.937,405.344,979.407,432.076,979.417,461.305z"></path><path opacity="0.2" enable-background="new    " d="M936.833,461.305v823.136c-0.046,43.067-34.861,78.015-77.927,78.225H425.833  V383.25h433.072c43.062,0.023,77.951,34.951,77.927,78.013C936.833,461.277,936.833,461.291,936.833,461.305z"></path><linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="162.7469" y1="1383.0741" x2="774.0864" y2="324.2592" gradientTransform="matrix(1 0 0 -1 0 1705.3334)"><stop offset="0" style="stop-color:#1784D9;"></stop><stop offset="0.5" style="stop-color:#107AD5;"></stop><stop offset="1" style="stop-color:#0A63C9;"></stop></linearGradient><path fill="url(#SVGID_2_)" d="M78.055,383.25h780.723c43.109,0,78.055,34.947,78.055,78.055v780.723  c0,43.109-34.946,78.055-78.055,78.055H78.055c-43.109,0-78.055-34.947-78.055-78.055V461.305  C0,418.197,34.947,383.25,78.055,383.25z"></path><path fill="#FFFFFF" d="M243.96,710.631c19.238-40.988,50.29-75.289,89.17-98.495c43.057-24.651,92.081-36.94,141.675-35.515  c45.965-0.997,91.321,10.655,131.114,33.683c37.414,22.312,67.547,55.004,86.742,94.109c20.904,43.09,31.322,90.512,30.405,138.396  c1.013,50.043-9.706,99.628-31.299,144.783c-19.652,40.503-50.741,74.36-89.425,97.388c-41.327,23.734-88.367,35.692-136.011,34.578  c-46.947,1.133-93.303-10.651-134.01-34.067c-37.738-22.341-68.249-55.07-87.892-94.28c-21.028-42.467-31.57-89.355-30.745-136.735  C212.808,804.859,223.158,755.686,243.96,710.631z M339.006,941.858c10.257,25.912,27.651,48.385,50.163,64.812  c22.93,16.026,50.387,24.294,78.353,23.591c29.783,1.178,59.14-7.372,83.634-24.358c22.227-16.375,39.164-38.909,48.715-64.812  c10.677-28.928,15.946-59.572,15.543-90.404c0.33-31.127-4.623-62.084-14.649-91.554c-8.855-26.607-25.246-50.069-47.182-67.537  c-23.88-17.79-53.158-26.813-82.91-25.55c-28.572-0.74-56.644,7.593-80.184,23.804c-22.893,16.496-40.617,39.168-51.1,65.365  c-23.255,60.049-23.376,126.595-0.341,186.728L339.006,941.858z"></path><path fill="#50D9FF" d="M1362.667,255.5h383.25v383.25h-383.25V255.5z"></path>', 23)]))
        }
    },
    z = {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 2228.833 2073.333"
    };
const w = {
        render: function(l, h) {
            return c(), t("svg", z, h[0] || (h[0] = [a('<path fill="#5059C9" d="M1554.637,777.5h575.713c54.391,0,98.483,44.092,98.483,98.483c0,0,0,0,0,0v524.398\tc0,199.901-162.051,361.952-361.952,361.952h0h-1.711c-199.901,0.028-361.975-162-362.004-361.901c0-0.017,0-0.034,0-0.052V828.971\tC1503.167,800.544,1526.211,777.5,1554.637,777.5L1554.637,777.5z"></path><circle fill="#5059C9" cx="1943.75" cy="440.583" r="233.25"></circle><circle fill="#7B83EB" cx="1218.083" cy="336.917" r="336.917"></circle><path fill="#7B83EB" d="M1667.323,777.5H717.01c-53.743,1.33-96.257,45.931-95.01,99.676v598.105\tc-7.505,322.519,247.657,590.16,570.167,598.053c322.51-7.893,577.671-275.534,570.167-598.053V877.176\tC1763.579,823.431,1721.066,778.83,1667.323,777.5z"></path><path opacity=".1" d="M1244,777.5v838.145c-0.258,38.435-23.549,72.964-59.09,87.598\tc-11.316,4.787-23.478,7.254-35.765,7.257H667.613c-6.738-17.105-12.958-34.21-18.142-51.833\tc-18.144-59.477-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1244z"></path><path opacity=".2" d="M1192.167,777.5v889.978c-0.002,12.287-2.47,24.449-7.257,35.765\tc-14.634,35.541-49.163,58.833-87.598,59.09H691.975c-8.812-17.105-17.105-34.21-24.362-51.833\tc-7.257-17.623-12.958-34.21-18.142-51.833c-18.144-59.476-27.402-121.307-27.472-183.49V877.02\tc-1.246-53.659,41.198-98.19,94.855-99.52H1192.167z"></path><path opacity=".2" d="M1192.167,777.5v786.312c-0.395,52.223-42.632,94.46-94.855,94.855h-447.84\tc-18.144-59.476-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1192.167z"></path><path opacity=".2" d="M1140.333,777.5v786.312c-0.395,52.223-42.632,94.46-94.855,94.855H649.472\tc-18.144-59.476-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1140.333z"></path><path opacity=".1" d="M1244,509.522v163.275c-8.812,0.518-17.105,1.037-25.917,1.037\tc-8.812,0-17.105-0.518-25.917-1.037c-17.496-1.161-34.848-3.937-51.833-8.293c-104.963-24.857-191.679-98.469-233.25-198.003\tc-7.153-16.715-12.706-34.071-16.587-51.833h258.648C1201.449,414.866,1243.801,457.217,1244,509.522z"></path><path opacity=".2" d="M1192.167,561.355v111.442c-17.496-1.161-34.848-3.937-51.833-8.293\tc-104.963-24.857-191.679-98.469-233.25-198.003h190.228C1149.616,466.699,1191.968,509.051,1192.167,561.355z"></path><path opacity=".2" d="M1192.167,561.355v111.442c-17.496-1.161-34.848-3.937-51.833-8.293\tc-104.963-24.857-191.679-98.469-233.25-198.003h190.228C1149.616,466.699,1191.968,509.051,1192.167,561.355z"></path><path opacity=".2" d="M1140.333,561.355v103.148c-104.963-24.857-191.679-98.469-233.25-198.003\th138.395C1097.783,466.699,1140.134,509.051,1140.333,561.355z"></path><linearGradient id="a" gradientUnits="userSpaceOnUse" x1="198.099" y1="1683.0726" x2="942.2344" y2="394.2607" gradientTransform="matrix(1 0 0 -1 0 2075.3333)"><stop offset="0" stop-color="#5a62c3"></stop><stop offset=".5" stop-color="#4d55bd"></stop><stop offset="1" stop-color="#3940ab"></stop></linearGradient><path fill="url(#a)" d="M95.01,466.5h950.312c52.473,0,95.01,42.538,95.01,95.01v950.312c0,52.473-42.538,95.01-95.01,95.01\tH95.01c-52.473,0-95.01-42.538-95.01-95.01V561.51C0,509.038,42.538,466.5,95.01,466.5z"></path><path fill="#FFF" d="M820.211,828.193H630.241v517.297H509.211V828.193H320.123V727.844h500.088V828.193z"></path>', 15)]))
        }
    },
    V = {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 5.5 32 20.5"
    };
const x = {
        render: function(l, h) {
            return c(), t("svg", V, h[0] || (h[0] = [a('<title>OfficeCore10_32x_24x_20x_16x_01-22-2019</title><g id="STYLE_COLOR"><path d="M12.20245,11.19292l.00031-.0011,6.71765,4.02379,4.00293-1.68451.00018.00068A6.4768,6.4768,0,0,1,25.5,13c.14764,0,.29358.0067.43878.01639a10.00075,10.00075,0,0,0-18.041-3.01381C7.932,10.00215,7.9657,10,8,10A7.96073,7.96073,0,0,1,12.20245,11.19292Z" fill="#0364b8"></path><path d="M12.20276,11.19182l-.00031.0011A7.96073,7.96073,0,0,0,8,10c-.0343,0-.06805.00215-.10223.00258A7.99676,7.99676,0,0,0,1.43732,22.57277l5.924-2.49292,2.63342-1.10819,5.86353-2.46746,3.06213-1.28859Z" fill="#0078d4"></path><path d="M25.93878,13.01639C25.79358,13.0067,25.64764,13,25.5,13a6.4768,6.4768,0,0,0-2.57648.53178l-.00018-.00068-4.00293,1.68451,1.16077.69528L23.88611,18.19l1.66009.99438,5.67633,3.40007a6.5002,6.5002,0,0,0-5.28375-9.56805Z" fill="#1490df"></path><path d="M25.5462,19.18437,23.88611,18.19l-3.80493-2.2791-1.16077-.69528L15.85828,16.5042,9.99475,18.97166,7.36133,20.07985l-5.924,2.49292A7.98889,7.98889,0,0,0,8,26H25.5a6.49837,6.49837,0,0,0,5.72253-3.41556Z" fill="#28a8ea"></path></g>', 2)]))
        }
    },
    g = {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 32 32"
    };
const u = {
        render: function(l, h) {
            return c(), t("svg", g, h[0] || (h[0] = [a('<title>OfficeCore10_32x_24x_20x_16x_01-22-2019</title><g id="STYLE_COLOR" data-name="STYLE = COLOR"><circle cx="14.99964" cy="9.5" r="9.5" fill="#036c70"></circle><circle cx="23.87464" cy="17.875" r="8.125" fill="#1a9ba1"></circle><circle cx="15.99964" cy="25.5" r="6.5" fill="#37c6d0"></circle><path d="M16.66663,7H5.83276a9.50556,9.50556,0,0,0,9.16687,12c.27729,0,.55164-.01263.82288-.03589l.00537.03815A6.50007,6.50007,0,0,0,9.49963,25.5q0,.25231.019.5h7.148A1.33732,1.33732,0,0,0,18,24.66663V8.33331A1.33727,1.33727,0,0,0,16.66663,7Z" opacity="0.1"></path><path d="M15.66663,8H5.61792a9.50509,9.50509,0,0,0,9.38171,11c.27729,0,.55164-.01263.82288-.03589l.00537.03815A6.50459,6.50459,0,0,0,9.67389,27h5.99274A1.33732,1.33732,0,0,0,17,25.66663V9.33331A1.33727,1.33727,0,0,0,15.66663,8Z" opacity="0.2"></path><path d="M15.66663,8H5.61792a9.50509,9.50509,0,0,0,9.38171,11c.27729,0,.55164-.01263.82288-.03589l.00537.03815A6.50063,6.50063,0,0,0,9.51868,25h6.148A1.33732,1.33732,0,0,0,17,23.66663V9.33331A1.33727,1.33727,0,0,0,15.66663,8Z" opacity="0.2"></path><path d="M14.66663,8H5.61792a9.50509,9.50509,0,0,0,9.38171,11c.27729,0,.55164-.01263.82288-.03589l.00537.03815A6.50063,6.50063,0,0,0,9.51868,25h5.148A1.33732,1.33732,0,0,0,16,23.66663V9.33331A1.33727,1.33727,0,0,0,14.66663,8Z" opacity="0.2"></path><path id="Back_Plate" data-name="Back Plate" d="M1.33333,8H14.66669A1.33334,1.33334,0,0,1,16,9.33334V22.66666A1.33333,1.33333,0,0,1,14.6667,24H1.33334A1.33334,1.33334,0,0,1,0,22.66666V9.33333A1.33333,1.33333,0,0,1,1.33333,8Z" fill="#03787c"></path><path d="M5.67039,15.82457a2.64535,2.64535,0,0,1-.82266-.86964A2.36142,2.36142,0,0,1,4.561,13.76466a2.29061,2.29061,0,0,1,.5327-1.5409,3.14167,3.14167,0,0,1,1.416-.92359A5.98158,5.98158,0,0,1,8.44506,11a7.35414,7.35414,0,0,1,2.54889.35738v1.80041a3.98617,3.98617,0,0,0-1.15307-.472,5.59553,5.59553,0,0,0-1.34862-.16183,2.92625,2.92625,0,0,0-1.38581.29321A.91071.91071,0,0,0,6.557,13.65a.84355.84355,0,0,0,.23275.59013,2.12174,2.12174,0,0,0,.62689.44831q.3948.19576,1.17678.51922a1.23212,1.23212,0,0,1,.16857.06743,9.69661,9.69661,0,0,1,1.48348.73173,2.654,2.654,0,0,1,.87661.88313,2.55808,2.55808,0,0,1,.31692,1.33187,2.48083,2.48083,0,0,1-.499,1.60485,2.78893,2.78893,0,0,1-1.33513.89683A6.04879,6.04879,0,0,1,7.70332,21a10.0284,10.0284,0,0,1-1.72275-.14161,5.91231,5.91231,0,0,1-1.3993-.40458V18.55226a4.50013,4.50013,0,0,0,1.41605.67431,5.51323,5.51323,0,0,0,1.55765.24949,2.68013,2.68013,0,0,0,1.41257-.3.94723.94723,0,0,0,.4755-.84636.90389.90389,0,0,0-.26625-.64734,2.70416,2.70416,0,0,0-.73521-.51248Q7.973,16.934,7.056,16.54956A7.85955,7.85955,0,0,1,5.67039,15.82457Z" fill="#fff"></path><rect x="0.00003" width="32" height="32" fill="none"></rect></g>', 2)]))
        }
    },
    y = {
        version: "1.1",
        id: "Livello_1",
        "xmlns:x": "http://ns.adobe.com/Extensibility/1.0/",
        "xmlns:i": "http://ns.adobe.com/AdobeIllustrator/10.0/",
        "xmlns:graph": "http://ns.adobe.com/Graphs/1.0/",
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        x: "0px",
        y: "0px",
        viewBox: "0 0 1831.085 1703.335",
        "enable-background": "new 0 0 1831.085 1703.335",
        "xml:space": "preserve"
    };
const C = {
        render: function(l, h) {
            return c(), t("svg", y, h[0] || (h[0] = [a('<path fill="#0A2767" d="M1831.083,894.25c0.1-14.318-7.298-27.644-19.503-35.131h-0.213l-0.767-0.426l-634.492-375.585  c-2.74-1.851-5.583-3.543-8.517-5.067c-24.498-12.639-53.599-12.639-78.098,0c-2.934,1.525-5.777,3.216-8.517,5.067L446.486,858.693  l-0.766,0.426c-19.392,12.059-25.337,37.556-13.278,56.948c3.553,5.714,8.447,10.474,14.257,13.868l634.492,375.585  c2.749,1.835,5.592,3.527,8.517,5.068c24.498,12.639,53.599,12.639,78.098,0c2.925-1.541,5.767-3.232,8.517-5.068l634.492-375.585  C1823.49,922.545,1831.228,908.923,1831.083,894.25z"></path><path fill="#0364B8" d="M520.453,643.477h416.38v381.674h-416.38V643.477z M1745.917,255.5V80.908  c1-43.652-33.552-79.862-77.203-80.908H588.204C544.552,1.046,510,37.256,511,80.908V255.5l638.75,170.333L1745.917,255.5z"></path><path fill="#0078D4" d="M511,255.5h425.833v383.25H511V255.5z"></path><path fill="#28A8EA" d="M1362.667,255.5H936.833v383.25L1362.667,1022h383.25V638.75L1362.667,255.5z"></path><path fill="#0078D4" d="M936.833,638.75h425.833V1022H936.833V638.75z"></path><path fill="#0364B8" d="M936.833,1022h425.833v383.25H936.833V1022z"></path><path fill="#14447D" d="M520.453,1025.151h416.38v346.969h-416.38V1025.151z"></path><path fill="#0078D4" d="M1362.667,1022h383.25v383.25h-383.25V1022z"></path><linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="1128.4584" y1="811.0833" x2="1128.4584" y2="1.9982" gradientTransform="matrix(1 0 0 -1 0 1705.3334)"><stop offset="0" style="stop-color:#35B8F1;"></stop><stop offset="1" style="stop-color:#28A8EA;"></stop></linearGradient><path fill="url(#SVGID_1_)" d="M1811.58,927.593l-0.809,0.426l-634.492,356.848c-2.768,1.703-5.578,3.321-8.517,4.769  c-10.777,5.132-22.481,8.029-34.407,8.517l-34.663-20.27c-2.929-1.47-5.773-3.105-8.517-4.897L447.167,906.003h-0.298  l-21.036-11.753v722.384c0.328,48.196,39.653,87.006,87.849,86.7h1230.914c0.724,0,1.363-0.341,2.129-0.341  c10.18-0.651,20.216-2.745,29.808-6.217c4.145-1.756,8.146-3.835,11.966-6.217c2.853-1.618,7.75-5.152,7.75-5.152  c21.814-16.142,34.726-41.635,34.833-68.772V894.25C1831.068,908.067,1823.616,920.807,1811.58,927.593z"></path><path opacity="0.5" fill="#0A2767" enable-background="new    " d="M1797.017,891.397v44.287l-663.448,456.791L446.699,906.301  c0-0.235-0.191-0.426-0.426-0.426l0,0l-63.023-37.899v-31.938l25.976-0.426l54.932,31.512l1.277,0.426l4.684,2.981  c0,0,645.563,368.346,647.267,369.197l24.698,14.478c2.129-0.852,4.258-1.703,6.813-2.555  c1.278-0.852,640.879-360.681,640.879-360.681L1797.017,891.397z"></path><path fill="#1490DF" d="M1811.58,927.593l-0.809,0.468l-634.492,356.848c-2.768,1.703-5.578,3.321-8.517,4.769  c-24.641,12.038-53.457,12.038-78.098,0c-2.918-1.445-5.76-3.037-8.517-4.769L446.657,928.061l-0.766-0.468  c-12.25-6.642-19.93-19.409-20.057-33.343v722.384c0.305,48.188,39.616,87.004,87.803,86.7c0.001,0,0.002,0,0.004,0h1229.636  c48.188,0.307,87.5-38.509,87.807-86.696c0-0.001,0-0.002,0-0.004V894.25C1831.068,908.067,1823.616,920.807,1811.58,927.593z"></path><path opacity="0.1" enable-background="new    " d="M1185.52,1279.629l-9.496,5.323c-2.752,1.752-5.595,3.359-8.517,4.812  c-10.462,5.135-21.838,8.146-33.47,8.857l241.405,285.479l421.107,101.476c11.539-8.716,20.717-20.178,26.7-33.343L1185.52,1279.629  z"></path><path opacity="0.05" enable-background="new    " d="M1228.529,1255.442l-52.505,29.51c-2.752,1.752-5.595,3.359-8.517,4.812  c-10.462,5.135-21.838,8.146-33.47,8.857l113.101,311.838l549.538,74.989c21.649-16.254,34.394-41.743,34.407-68.815v-9.326  L1228.529,1255.442z"></path><path fill="#28A8EA" d="M514.833,1703.333h1228.316c18.901,0.096,37.335-5.874,52.59-17.033l-697.089-408.331  c-2.929-1.47-5.773-3.105-8.517-4.897L447.125,906.088h-0.298l-20.993-11.838v719.914  C425.786,1663.364,465.632,1703.286,514.833,1703.333C514.832,1703.333,514.832,1703.333,514.833,1703.333z"></path><path opacity="0.1" enable-background="new    " d="M1022,418.722v908.303c-0.076,31.846-19.44,60.471-48.971,72.392  c-9.148,3.931-19,5.96-28.957,5.962H425.833V383.25H511v-42.583h433.073C987.092,340.83,1021.907,375.702,1022,418.722z"></path><path opacity="0.2" enable-background="new    " d="M979.417,461.305v908.302c0.107,10.287-2.074,20.469-6.388,29.808  c-11.826,29.149-40.083,48.273-71.54,48.417H425.833V383.25h475.656c12.356-0.124,24.533,2.958,35.344,8.943  C962.937,405.344,979.407,432.076,979.417,461.305z"></path><path opacity="0.2" enable-background="new    " d="M979.417,461.305v823.136c-0.208,43-34.928,77.853-77.927,78.225H425.833V383.25  h475.656c12.356-0.124,24.533,2.958,35.344,8.943C962.937,405.344,979.407,432.076,979.417,461.305z"></path><path opacity="0.2" enable-background="new    " d="M936.833,461.305v823.136c-0.046,43.067-34.861,78.015-77.927,78.225H425.833  V383.25h433.072c43.062,0.023,77.951,34.951,77.927,78.013C936.833,461.277,936.833,461.291,936.833,461.305z"></path><linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="162.7469" y1="1383.0741" x2="774.0864" y2="324.2592" gradientTransform="matrix(1 0 0 -1 0 1705.3334)"><stop offset="0" style="stop-color:#1784D9;"></stop><stop offset="0.5" style="stop-color:#107AD5;"></stop><stop offset="1" style="stop-color:#0A63C9;"></stop></linearGradient><path fill="url(#SVGID_2_)" d="M78.055,383.25h780.723c43.109,0,78.055,34.947,78.055,78.055v780.723  c0,43.109-34.946,78.055-78.055,78.055H78.055c-43.109,0-78.055-34.947-78.055-78.055V461.305  C0,418.197,34.947,383.25,78.055,383.25z"></path><path fill="#FFFFFF" d="M243.96,710.631c19.238-40.988,50.29-75.289,89.17-98.495c43.057-24.651,92.081-36.94,141.675-35.515  c45.965-0.997,91.321,10.655,131.114,33.683c37.414,22.312,67.547,55.004,86.742,94.109c20.904,43.09,31.322,90.512,30.405,138.396  c1.013,50.043-9.706,99.628-31.299,144.783c-19.652,40.503-50.741,74.36-89.425,97.388c-41.327,23.734-88.367,35.692-136.011,34.578  c-46.947,1.133-93.303-10.651-134.01-34.067c-37.738-22.341-68.249-55.07-87.892-94.28c-21.028-42.467-31.57-89.355-30.745-136.735  C212.808,804.859,223.158,755.686,243.96,710.631z M339.006,941.858c10.257,25.912,27.651,48.385,50.163,64.812  c22.93,16.026,50.387,24.294,78.353,23.591c29.783,1.178,59.14-7.372,83.634-24.358c22.227-16.375,39.164-38.909,48.715-64.812  c10.677-28.928,15.946-59.572,15.543-90.404c0.33-31.127-4.623-62.084-14.649-91.554c-8.855-26.607-25.246-50.069-47.182-67.537  c-23.88-17.79-53.158-26.813-82.91-25.55c-28.572-0.74-56.644,7.593-80.184,23.804c-22.893,16.496-40.617,39.168-51.1,65.365  c-23.255,60.049-23.376,126.595-0.341,186.728L339.006,941.858z"></path><path fill="#50D9FF" d="M1362.667,255.5h383.25v383.25h-383.25V255.5z"></path>', 23)]))
        }
    },
    A = {
        viewBox: "0 0 127 127",
        xmlns: "http://www.w3.org/2000/svg"
    };
const L = {
    render: function(a, h) {
        return c(), t("svg", A, h[0] || (h[0] = [l("path", {
            d: "M27.2 80c0 7.3-5.9 13.2-13.2 13.2C6.7 93.2.8 87.3.8 80c0-7.3 5.9-13.2 13.2-13.2h13.2V80zm6.6 0c0-7.3 5.9-13.2 13.2-13.2 7.3 0 13.2 5.9 13.2 13.2v33c0 7.3-5.9 13.2-13.2 13.2-7.3 0-13.2-5.9-13.2-13.2V80z",
            fill: "#E01E5A"
        }, null, -1), l("path", {
            d: "M47 27c-7.3 0-13.2-5.9-13.2-13.2C33.8 6.5 39.7.6 47 .6c7.3 0 13.2 5.9 13.2 13.2V27H47zm0 6.7c7.3 0 13.2 5.9 13.2 13.2 0 7.3-5.9 13.2-13.2 13.2H13.9C6.6 60.1.7 54.2.7 46.9c0-7.3 5.9-13.2 13.2-13.2H47z",
            fill: "#36C5F0"
        }, null, -1), l("path", {
            d: "M99.9 46.9c0-7.3 5.9-13.2 13.2-13.2 7.3 0 13.2 5.9 13.2 13.2 0 7.3-5.9 13.2-13.2 13.2H99.9V46.9zm-6.6 0c0 7.3-5.9 13.2-13.2 13.2-7.3 0-13.2-5.9-13.2-13.2V13.8C66.9 6.5 72.8.6 80.1.6c7.3 0 13.2 5.9 13.2 13.2v33.1z",
            fill: "#2EB67D"
        }, null, -1), l("path", {
            d: "M80.1 99.8c7.3 0 13.2 5.9 13.2 13.2 0 7.3-5.9 13.2-13.2 13.2-7.3 0-13.2-5.9-13.2-13.2V99.8h13.2zm0-6.6c-7.3 0-13.2-5.9-13.2-13.2 0-7.3 5.9-13.2 13.2-13.2h33.1c7.3 0 13.2 5.9 13.2 13.2 0 7.3-5.9 13.2-13.2 13.2H80.1z",
            fill: "#ECB22E"
        }, null, -1)]))
    }
};
export {
    i as G, w as M, f as N, M as O, p as R, u as S, n as a, d as b, C as c, x as d, L as e
};