import {
    ak as o,
    at as e,
    al as r,
    J as n,
    X as t,
    b7 as a,
    aR as i,
    r as l,
    a3 as s,
    U as d,
    d3 as c,
    aT as u,
    am as b,
    aP as h,
    aO as p,
    aQ as v,
    d4 as f,
    b9 as x,
    i as y,
    c as g,
    Y as m,
    Z as w,
    d5 as C,
    aV as $,
    ap as z,
    aN as B,
    aq as S,
    aS as k,
    an as P
} from "./Cf0SOiw0.js";
import {
    i as T,
    u as H,
    a as R
} from "./MpDLC7up.js";
import {
    r as O,
    c as j,
    i as D
} from "./B7VeW_-d.js";

function E(o) {
    return o.replace(/#|\(|\)|,|\s|\./g, "_")
}
const {
    cubicBezierEaseInOut: F
} = e;
const _ = r("base-wave", "\n position: absolute;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n border-radius: inherit;\n"),
    I = n({
        name: "BaseWave",
        props: {
            clsPrefix: {
                type: String,
                required: !0
            }
        },
        setup(o) {
            a("-base-wave", _, i(o, "clsPrefix"));
            const e = l(null),
                r = l(!1);
            let n = null;
            return s((() => {
                null !== n && window.clearTimeout(n)
            })), {
                active: r,
                selfRef: e,
                play() {
                    null !== n && (window.clearTimeout(n), r.value = !1, n = null), d((() => {
                        var o;
                        null === (o = e.value) || void 0 === o || o.offsetHeight, r.value = !0, n = window.setTimeout((() => {
                            r.value = !1, n = null
                        }), 1e3)
                    }))
                }
            }
        },
        render() {
            const {
                clsPrefix: o
            } = this;
            return t("div", {
                ref: "selfRef",
                "aria-hidden": !0,
                class: [`${o}-base-wave`, this.active && `${o}-base-wave--active`]
            })
        }
    });

function q(o) {
    return c(o, [255, 255, 255, .16])
}

function K(o) {
    return c(o, [0, 0, 0, .12])
}
const N = u("n-button-group"),
    Q = o([r("button", "\n margin: 0;\n font-weight: var(--n-font-weight);\n line-height: 1;\n font-family: inherit;\n padding: var(--n-padding);\n height: var(--n-height);\n font-size: var(--n-font-size);\n border-radius: var(--n-border-radius);\n color: var(--n-text-color);\n background-color: var(--n-color);\n width: var(--n-width);\n white-space: nowrap;\n outline: none;\n position: relative;\n z-index: auto;\n border: none;\n display: inline-flex;\n flex-wrap: nowrap;\n flex-shrink: 0;\n align-items: center;\n justify-content: center;\n user-select: none;\n -webkit-user-select: none;\n text-align: center;\n cursor: pointer;\n text-decoration: none;\n transition:\n color .3s var(--n-bezier),\n background-color .3s var(--n-bezier),\n opacity .3s var(--n-bezier),\n border-color .3s var(--n-bezier);\n ", [b("color", [p("border", {
        borderColor: "var(--n-border-color)"
    }), b("disabled", [p("border", {
        borderColor: "var(--n-border-color-disabled)"
    })]), h("disabled", [o("&:focus", [p("state-border", {
        borderColor: "var(--n-border-color-focus)"
    })]), o("&:hover", [p("state-border", {
        borderColor: "var(--n-border-color-hover)"
    })]), o("&:active", [p("state-border", {
        borderColor: "var(--n-border-color-pressed)"
    })]), b("pressed", [p("state-border", {
        borderColor: "var(--n-border-color-pressed)"
    })])])]), b("disabled", {
        backgroundColor: "var(--n-color-disabled)",
        color: "var(--n-text-color-disabled)"
    }, [p("border", {
        border: "var(--n-border-disabled)"
    })]), h("disabled", [o("&:focus", {
        backgroundColor: "var(--n-color-focus)",
        color: "var(--n-text-color-focus)"
    }, [p("state-border", {
        border: "var(--n-border-focus)"
    })]), o("&:hover", {
        backgroundColor: "var(--n-color-hover)",
        color: "var(--n-text-color-hover)"
    }, [p("state-border", {
        border: "var(--n-border-hover)"
    })]), o("&:active", {
        backgroundColor: "var(--n-color-pressed)",
        color: "var(--n-text-color-pressed)"
    }, [p("state-border", {
        border: "var(--n-border-pressed)"
    })]), b("pressed", {
        backgroundColor: "var(--n-color-pressed)",
        color: "var(--n-text-color-pressed)"
    }, [p("state-border", {
        border: "var(--n-border-pressed)"
    })])]), b("loading", "cursor: wait;"), r("base-wave", "\n pointer-events: none;\n top: 0;\n right: 0;\n bottom: 0;\n left: 0;\n animation-iteration-count: 1;\n animation-duration: var(--n-ripple-duration);\n animation-timing-function: var(--n-bezier-ease-out), var(--n-bezier-ease-out);\n ", [b("active", {
        zIndex: 1,
        animationName: "button-wave-spread, button-wave-opacity"
    })]), T && "MozBoxSizing" in document.createElement("div").style ? o("&::moz-focus-inner", {
        border: 0
    }) : null, p("border, state-border", "\n position: absolute;\n left: 0;\n top: 0;\n right: 0;\n bottom: 0;\n border-radius: inherit;\n transition: border-color .3s var(--n-bezier);\n pointer-events: none;\n "), p("border", {
        border: "var(--n-border)"
    }), p("state-border", {
        border: "var(--n-border)",
        borderColor: "#0000",
        zIndex: 1
    }), p("icon", "\n margin: var(--n-icon-margin);\n margin-left: 0;\n height: var(--n-icon-size);\n width: var(--n-icon-size);\n max-width: var(--n-icon-size);\n font-size: var(--n-icon-size);\n position: relative;\n flex-shrink: 0;\n ", [r("icon-slot", "\n height: var(--n-icon-size);\n width: var(--n-icon-size);\n position: absolute;\n left: 0;\n top: 50%;\n transform: translateY(-50%);\n display: flex;\n align-items: center;\n justify-content: center;\n ", [v({
        top: "50%",
        originalTransform: "translateY(-50%)"
    })]), function({
        duration: e = ".2s",
        delay: r = ".1s"
    } = {}) {
        return [o("&.fade-in-width-expand-transition-leave-from, &.fade-in-width-expand-transition-enter-to", {
            opacity: 1
        }), o("&.fade-in-width-expand-transition-leave-to, &.fade-in-width-expand-transition-enter-from", "\n opacity: 0!important;\n margin-left: 0!important;\n margin-right: 0!important;\n "), o("&.fade-in-width-expand-transition-leave-active", `\n overflow: hidden;\n transition:\n opacity ${e} ${F},\n max-width ${e} ${F} ${r},\n margin-left ${e} ${F} ${r},\n margin-right ${e} ${F} ${r};\n `), o("&.fade-in-width-expand-transition-enter-active", `\n overflow: hidden;\n transition:\n opacity ${e} ${F} ${r},\n max-width ${e} ${F},\n margin-left ${e} ${F},\n margin-right ${e} ${F};\n `)]
    }()]), p("content", "\n display: flex;\n align-items: center;\n flex-wrap: nowrap;\n min-width: 0;\n ", [o("~", [p("icon", {
        margin: "var(--n-icon-margin)",
        marginRight: 0
    })])]), b("block", "\n display: flex;\n width: 100%;\n "), b("dashed", [p("border, state-border", {
        borderStyle: "dashed !important"
    })]), b("disabled", {
        cursor: "not-allowed",
        opacity: "var(--n-opacity-disabled)"
    })]), o("@keyframes button-wave-spread", {
        from: {
            boxShadow: "0 0 0.5px 0 var(--n-ripple-color)"
        },
        to: {
            boxShadow: "0 0 0.5px 4.5px var(--n-ripple-color)"
        }
    }), o("@keyframes button-wave-opacity", {
        from: {
            opacity: "var(--n-wave-opacity)"
        },
        to: {
            opacity: 0
        }
    })]),
    V = n({
        name: "Button",
        props: Object.assign(Object.assign({}, w.props), {
            color: String,
            textColor: String,
            text: Boolean,
            block: Boolean,
            loading: Boolean,
            disabled: Boolean,
            circle: Boolean,
            size: String,
            ghost: Boolean,
            round: Boolean,
            secondary: Boolean,
            tertiary: Boolean,
            quaternary: Boolean,
            strong: Boolean,
            focusable: {
                type: Boolean,
                default: !0
            },
            keyboard: {
                type: Boolean,
                default: !0
            },
            tag: {
                type: String,
                default: "button"
            },
            type: {
                type: String,
                default: "default"
            },
            dashed: Boolean,
            renderIcon: Function,
            iconPlacement: {
                type: String,
                default: "left"
            },
            attrType: {
                type: String,
                default: "button"
            },
            bordered: {
                type: Boolean,
                default: !0
            },
            onClick: [Function, Array],
            nativeFocusBehavior: {
                type: Boolean,
                default: !R
            }
        }),
        slots: Object,
        setup(o) {
            const e = l(null),
                r = l(null),
                n = l(!1),
                t = x((() => !o.quaternary && !o.tertiary && !o.secondary && !o.text && (!o.color || o.ghost || o.dashed) && o.bordered)),
                a = y(N, {}),
                {
                    mergedSizeRef: i
                } = H({}, {
                    defaultSize: "medium",
                    mergedSize: e => {
                        const {
                            size: r
                        } = o;
                        if (r) return r;
                        const {
                            size: n
                        } = a;
                        if (n) return n;
                        const {
                            mergedSize: t
                        } = e || {};
                        return t ? t.value : "medium"
                    }
                }),
                s = g((() => o.focusable && !o.disabled)),
                {
                    inlineThemeDisabled: d,
                    mergedClsPrefixRef: c,
                    mergedRtlRef: u
                } = m(o),
                b = w("Button", "-button", Q, C, o, c),
                h = $("Button", u, c),
                p = g((() => {
                    const e = b.value,
                        {
                            common: {
                                cubicBezierEaseInOut: r,
                                cubicBezierEaseOut: n
                            },
                            self: t
                        } = e,
                        {
                            rippleDuration: a,
                            opacityDisabled: l,
                            fontWeight: s,
                            fontWeightStrong: d
                        } = t,
                        c = i.value,
                        {
                            dashed: u,
                            type: h,
                            ghost: p,
                            text: v,
                            color: f,
                            round: x,
                            circle: y,
                            textColor: g,
                            secondary: m,
                            tertiary: w,
                            quaternary: C,
                            strong: $
                        } = o,
                        S = {
                            "--n-font-weight": $ ? d : s
                        };
                    let k = {
                        "--n-color": "initial",
                        "--n-color-hover": "initial",
                        "--n-color-pressed": "initial",
                        "--n-color-focus": "initial",
                        "--n-color-disabled": "initial",
                        "--n-ripple-color": "initial",
                        "--n-text-color": "initial",
                        "--n-text-color-hover": "initial",
                        "--n-text-color-pressed": "initial",
                        "--n-text-color-focus": "initial",
                        "--n-text-color-disabled": "initial"
                    };
                    const P = "tertiary" === h,
                        T = "default" === h,
                        H = P ? "default" : h;
                    if (v) {
                        const o = g || f;
                        k = {
                            "--n-color": "#0000",
                            "--n-color-hover": "#0000",
                            "--n-color-pressed": "#0000",
                            "--n-color-focus": "#0000",
                            "--n-color-disabled": "#0000",
                            "--n-ripple-color": "#0000",
                            "--n-text-color": o || t[z("textColorText", H)],
                            "--n-text-color-hover": o ? q(o) : t[z("textColorTextHover", H)],
                            "--n-text-color-pressed": o ? K(o) : t[z("textColorTextPressed", H)],
                            "--n-text-color-focus": o ? q(o) : t[z("textColorTextHover", H)],
                            "--n-text-color-disabled": o || t[z("textColorTextDisabled", H)]
                        }
                    } else if (p || u) {
                        const o = g || f;
                        k = {
                            "--n-color": "#0000",
                            "--n-color-hover": "#0000",
                            "--n-color-pressed": "#0000",
                            "--n-color-focus": "#0000",
                            "--n-color-disabled": "#0000",
                            "--n-ripple-color": f || t[z("rippleColor", H)],
                            "--n-text-color": o || t[z("textColorGhost", H)],
                            "--n-text-color-hover": o ? q(o) : t[z("textColorGhostHover", H)],
                            "--n-text-color-pressed": o ? K(o) : t[z("textColorGhostPressed", H)],
                            "--n-text-color-focus": o ? q(o) : t[z("textColorGhostHover", H)],
                            "--n-text-color-disabled": o || t[z("textColorGhostDisabled", H)]
                        }
                    } else if (m) {
                        const o = T ? t.textColor : P ? t.textColorTertiary : t[z("color", H)],
                            e = f || o,
                            r = "default" !== h && "tertiary" !== h;
                        k = {
                            "--n-color": r ? B(e, {
                                alpha: Number(t.colorOpacitySecondary)
                            }) : t.colorSecondary,
                            "--n-color-hover": r ? B(e, {
                                alpha: Number(t.colorOpacitySecondaryHover)
                            }) : t.colorSecondaryHover,
                            "--n-color-pressed": r ? B(e, {
                                alpha: Number(t.colorOpacitySecondaryPressed)
                            }) : t.colorSecondaryPressed,
                            "--n-color-focus": r ? B(e, {
                                alpha: Number(t.colorOpacitySecondaryHover)
                            }) : t.colorSecondaryHover,
                            "--n-color-disabled": t.colorSecondary,
                            "--n-ripple-color": "#0000",
                            "--n-text-color": e,
                            "--n-text-color-hover": e,
                            "--n-text-color-pressed": e,
                            "--n-text-color-focus": e,
                            "--n-text-color-disabled": e
                        }
                    } else if (w || C) {
                        const o = T ? t.textColor : P ? t.textColorTertiary : t[z("color", H)],
                            e = f || o;
                        w ? (k["--n-color"] = t.colorTertiary, k["--n-color-hover"] = t.colorTertiaryHover, k["--n-color-pressed"] = t.colorTertiaryPressed, k["--n-color-focus"] = t.colorSecondaryHover, k["--n-color-disabled"] = t.colorTertiary) : (k["--n-color"] = t.colorQuaternary, k["--n-color-hover"] = t.colorQuaternaryHover, k["--n-color-pressed"] = t.colorQuaternaryPressed, k["--n-color-focus"] = t.colorQuaternaryHover, k["--n-color-disabled"] = t.colorQuaternary), k["--n-ripple-color"] = "#0000", k["--n-text-color"] = e, k["--n-text-color-hover"] = e, k["--n-text-color-pressed"] = e, k["--n-text-color-focus"] = e, k["--n-text-color-disabled"] = e
                    } else k = {
                        "--n-color": f || t[z("color", H)],
                        "--n-color-hover": f ? q(f) : t[z("colorHover", H)],
                        "--n-color-pressed": f ? K(f) : t[z("colorPressed", H)],
                        "--n-color-focus": f ? q(f) : t[z("colorFocus", H)],
                        "--n-color-disabled": f || t[z("colorDisabled", H)],
                        "--n-ripple-color": f || t[z("rippleColor", H)],
                        "--n-text-color": g || (f ? t.textColorPrimary : P ? t.textColorTertiary : t[z("textColor", H)]),
                        "--n-text-color-hover": g || (f ? t.textColorHoverPrimary : t[z("textColorHover", H)]),
                        "--n-text-color-pressed": g || (f ? t.textColorPressedPrimary : t[z("textColorPressed", H)]),
                        "--n-text-color-focus": g || (f ? t.textColorFocusPrimary : t[z("textColorFocus", H)]),
                        "--n-text-color-disabled": g || (f ? t.textColorDisabledPrimary : t[z("textColorDisabled", H)])
                    };
                    let R = {
                        "--n-border": "initial",
                        "--n-border-hover": "initial",
                        "--n-border-pressed": "initial",
                        "--n-border-focus": "initial",
                        "--n-border-disabled": "initial"
                    };
                    R = v ? {
                        "--n-border": "none",
                        "--n-border-hover": "none",
                        "--n-border-pressed": "none",
                        "--n-border-focus": "none",
                        "--n-border-disabled": "none"
                    } : {
                        "--n-border": t[z("border", H)],
                        "--n-border-hover": t[z("borderHover", H)],
                        "--n-border-pressed": t[z("borderPressed", H)],
                        "--n-border-focus": t[z("borderFocus", H)],
                        "--n-border-disabled": t[z("borderDisabled", H)]
                    };
                    const {
                        [z("height", c)]: O, [z("fontSize", c)]: j, [z("padding", c)]: D, [z("paddingRound", c)]: E, [z("iconSize", c)]: F, [z("borderRadius", c)]: _, [z("iconMargin", c)]: I, waveOpacity: N
                    } = t, Q = {
                        "--n-width": y && !v ? O : "initial",
                        "--n-height": v ? "initial" : O,
                        "--n-font-size": j,
                        "--n-padding": y || v ? "initial" : x ? E : D,
                        "--n-icon-size": F,
                        "--n-icon-margin": I,
                        "--n-border-radius": v ? "initial" : y || x ? O : _
                    };
                    return Object.assign(Object.assign(Object.assign(Object.assign({
                        "--n-bezier": r,
                        "--n-bezier-ease-out": n,
                        "--n-ripple-duration": a,
                        "--n-opacity-disabled": l,
                        "--n-wave-opacity": N
                    }, S), k), R), Q)
                })),
                v = d ? S("button", g((() => {
                    let e = "";
                    const {
                        dashed: r,
                        type: n,
                        ghost: t,
                        text: a,
                        color: l,
                        round: s,
                        circle: d,
                        textColor: c,
                        secondary: u,
                        tertiary: b,
                        quaternary: h,
                        strong: p
                    } = o;
                    r && (e += "a"), t && (e += "b"), a && (e += "c"), s && (e += "d"), d && (e += "e"), u && (e += "f"), b && (e += "g"), h && (e += "h"), p && (e += "i"), l && (e += `j${E(l)}`), c && (e += `k${E(c)}`);
                    const {
                        value: v
                    } = i;
                    return e += `l${v[0]}`, e += `m${n[0]}`, e
                })), p, o) : void 0;
            return {
                selfElRef: e,
                waveElRef: r,
                mergedClsPrefix: c,
                mergedFocusable: s,
                mergedSize: i,
                showBorder: t,
                enterPressed: n,
                rtlEnabled: h,
                handleMousedown: r => {
                    var n;
                    s.value || r.preventDefault(), o.nativeFocusBehavior || (r.preventDefault(), o.disabled || s.value && (null === (n = e.value) || void 0 === n || n.focus({
                        preventScroll: !0
                    })))
                },
                handleKeydown: e => {
                    if ("Enter" === e.key) {
                        if (!o.keyboard || o.loading) return void e.preventDefault();
                        n.value = !0
                    }
                },
                handleBlur: () => {
                    n.value = !1
                },
                handleKeyup: e => {
                    if ("Enter" === e.key) {
                        if (!o.keyboard) return;
                        n.value = !1
                    }
                },
                handleClick: e => {
                    var n;
                    if (!o.disabled && !o.loading) {
                        const {
                            onClick: t
                        } = o;
                        t && j(t, e), o.text || null === (n = r.value) || void 0 === n || n.play()
                    }
                },
                customColorCssVars: g((() => {
                    const {
                        color: e
                    } = o;
                    if (!e) return null;
                    const r = q(e);
                    return {
                        "--n-border-color": e,
                        "--n-border-color-hover": r,
                        "--n-border-color-pressed": K(e),
                        "--n-border-color-focus": r,
                        "--n-border-color-disabled": e
                    }
                })),
                cssVars: d ? void 0 : p,
                themeClass: null == v ? void 0 : v.themeClass,
                onRender: null == v ? void 0 : v.onRender
            }
        },
        render() {
            const {
                mergedClsPrefix: o,
                tag: e,
                onRender: r
            } = this;
            null == r || r();
            const n = O(this.$slots.default, (e => e && t("span", {
                class: `${o}-button__content`
            }, e)));
            return t(e, {
                ref: "selfElRef",
                class: [this.themeClass, `${o}-button`, `${o}-button--${this.type}-type`, `${o}-button--${this.mergedSize}-type`, this.rtlEnabled && `${o}-button--rtl`, this.disabled && `${o}-button--disabled`, this.block && `${o}-button--block`, this.enterPressed && `${o}-button--pressed`, !this.text && this.dashed && `${o}-button--dashed`, this.color && `${o}-button--color`, this.secondary && `${o}-button--secondary`, this.loading && `${o}-button--loading`, this.ghost && `${o}-button--ghost`],
                tabindex: this.mergedFocusable ? 0 : -1,
                type: this.attrType,
                style: this.cssVars,
                disabled: this.disabled,
                onClick: this.handleClick,
                onBlur: this.handleBlur,
                onMousedown: this.handleMousedown,
                onKeyup: this.handleKeyup,
                onKeydown: this.handleKeydown
            }, "right" === this.iconPlacement && n, t(f, {
                width: !0
            }, {
                default: () => O(this.$slots.icon, (e => (this.loading || this.renderIcon || e) && t("span", {
                    class: `${o}-button__icon`,
                    style: {
                        margin: D(this.$slots.default) ? "0" : ""
                    }
                }, t(k, null, {
                    default: () => this.loading ? t(P, {
                        clsPrefix: o,
                        key: "loading",
                        class: `${o}-icon-slot`,
                        strokeWidth: 20
                    }) : t("div", {
                        key: "icon",
                        class: `${o}-icon-slot`,
                        role: "none"
                    }, this.renderIcon ? this.renderIcon() : e)
                }))))
            }), "left" === this.iconPlacement && n, this.text ? null : t(I, {
                ref: "waveElRef",
                clsPrefix: o
            }), this.showBorder ? t("div", {
                "aria-hidden": !0,
                class: `${o}-button__border`,
                style: this.customColorCssVars
            }) : null, this.showBorder ? t("div", {
                "aria-hidden": !0,
                class: `${o}-button__state-border`,
                style: this.customColorCssVars
            }) : null)
        }
    }),
    G = V;
export {
    V as B, G as X, E as c
};