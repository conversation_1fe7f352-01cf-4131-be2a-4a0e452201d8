import {
    d as n,
    b as e,
    c as t,
    N as o,
    e as i,
    u as r,
    f as a,
    g as s,
    h as l,
    m as c,
    i as d,
    j as u,
    k as h,
    l as f
} from "./BjWUbj3w.js";
import {
    i as v,
    M as p,
    J as g,
    X as m,
    n as b,
    r as y,
    d_ as C,
    F as x,
    K as S,
    bb as k,
    a1 as w,
    aT as P,
    aL as O,
    al as L,
    am as j,
    a4 as A,
    H as z,
    I as E,
    Y as F,
    c as B,
    Z as R,
    aq as $,
    U as M,
    aj as H,
    dp as T,
    ag as I,
    da as N,
    b6 as V,
    b0 as _,
    aV as D,
    ap as K,
    dv as q,
    dw as W,
    dx as U,
    dy as Z,
    h as X,
    ak as J,
    aO as Y,
    d$ as G,
    bx as Q,
    y as nn,
    e0 as en,
    au as tn,
    N as on,
    av as rn,
    e1 as an,
    d as sn,
    b as ln,
    o as cn,
    C as dn
} from "./Cf0SOiw0.js";
import {
    S as un
} from "./WZsIN7xM.js";
import {
    k as hn
} from "./BLWq1oPC.js";
import {
    f as fn,
    a as vn,
    p as pn
} from "./pB_XRIgB.js";
import {
    k as gn
} from "./9wLWmnxl.js";
import {
    u as mn
} from "./B6noBY_5.js";
import {
    i as bn
} from "./MpDLC7up.js";
import {
    d as yn
} from "./DOnko34f.js";
let Cn = !1;
const xn = Object.assign(Object.assign({}, i), {
        onAfterEnter: Function,
        onAfterLeave: Function,
        transformOrigin: String,
        blockScroll: {
            type: Boolean,
            default: !0
        },
        closeOnEsc: {
            type: Boolean,
            default: !0
        },
        onEsc: Function,
        autoFocus: {
            type: Boolean,
            default: !0
        },
        internalStyle: [String, Object],
        maskClosable: {
            type: Boolean,
            default: !0
        },
        onPositiveClick: Function,
        onNegativeClick: Function,
        onClose: Function,
        onMaskClick: Function,
        draggable: [Boolean, Object]
    }),
    Sn = g({
        name: "DialogEnvironment",
        props: Object.assign(Object.assign({}, xn), {
            internalKey: {
                type: String,
                required: !0
            },
            to: [String, Object],
            onInternalAfterLeave: {
                type: Function,
                required: !0
            }
        }),
        setup(n) {
            const e = y(!0);

            function t() {
                e.value = !1
            }
            return {
                show: e,
                hide: t,
                handleUpdateShow: function(n) {
                    e.value = n
                },
                handleAfterLeave: function() {
                    const {
                        onInternalAfterLeave: e,
                        internalKey: t,
                        onAfterLeave: o
                    } = n;
                    e && e(t), o && o()
                },
                handleCloseClick: function() {
                    const {
                        onClose: e
                    } = n;
                    e ? Promise.resolve(e()).then((n => {
                        !1 !== n && t()
                    })) : t()
                },
                handleNegativeClick: function(e) {
                    const {
                        onNegativeClick: o
                    } = n;
                    o ? Promise.resolve(o(e)).then((n => {
                        !1 !== n && t()
                    })) : t()
                },
                handlePositiveClick: function(e) {
                    const {
                        onPositiveClick: o
                    } = n;
                    o ? Promise.resolve(o(e)).then((n => {
                        !1 !== n && t()
                    })) : t()
                },
                handleMaskClick: function(e) {
                    const {
                        onMaskClick: o,
                        maskClosable: i
                    } = n;
                    o && (o(e), i && t())
                },
                handleEsc: function() {
                    const {
                        onEsc: e
                    } = n;
                    e && e()
                }
            }
        },
        render() {
            const {
                handlePositiveClick: n,
                handleUpdateShow: i,
                handleNegativeClick: r,
                handleCloseClick: a,
                handleAfterLeave: s,
                handleMaskClick: l,
                handleEsc: c,
                to: d,
                maskClosable: u,
                show: h
            } = this;
            return m(o, {
                show: h,
                onUpdateShow: i,
                onMaskClick: l,
                onEsc: c,
                to: d,
                maskClosable: u,
                onAfterEnter: this.onAfterEnter,
                onAfterLeave: s,
                closeOnEsc: this.closeOnEsc,
                blockScroll: this.blockScroll,
                autoFocus: this.autoFocus,
                transformOrigin: this.transformOrigin,
                draggable: this.draggable,
                internalAppear: !0,
                internalDialog: !0
            }, {
                default: ({
                    draggableClass: o
                }) => m(e, Object.assign({}, gn(this.$props, t), {
                    titleClass: b([this.titleClass, o]),
                    style: this.internalStyle,
                    onClose: a,
                    onNegativeClick: r,
                    onPositiveClick: n
                }))
            })
        }
    }),
    kn = g({
        name: "DialogProvider",
        props: {
            injectionKey: String,
            to: [String, Object]
        },
        setup() {
            const e = y([]),
                t = {};

            function o(n = {}) {
                const o = k(),
                    i = w(Object.assign(Object.assign({}, n), {
                        key: o,
                        destroy: () => {
                            var n;
                            null === (n = t[`n-dialog-${o}`]) || void 0 === n || n.hide()
                        }
                    }));
                return e.value.push(i), i
            }
            const i = ["info", "success", "warning", "error"].map((n => e => o(Object.assign(Object.assign({}, e), {
                type: n
            }))));
            const c = {
                create: o,
                destroyAll: function() {
                    Object.values(t).forEach((n => {
                        null == n || n.hide()
                    }))
                },
                info: i[0],
                success: i[1],
                warning: i[2],
                error: i[3]
            };
            return S(n, c), S(s, {
                clickedRef: a(64),
                clickedPositionRef: r()
            }), S(l, e), Object.assign(Object.assign({}, c), {
                dialogList: e,
                dialogInstRefs: t,
                handleAfterLeave: function(n) {
                    const {
                        value: t
                    } = e;
                    t.splice(t.findIndex((e => e.key === n)), 1)
                }
            })
        },
        render() {
            var n, e;
            return m(x, null, [this.dialogList.map((n => m(Sn, C(n, ["destroy", "style"], {
                internalStyle: n.style,
                to: this.to,
                ref: e => {
                    null === e ? delete this.dialogInstRefs[`n-dialog-${n.key}`] : this.dialogInstRefs[`n-dialog-${n.key}`] = e
                },
                internalKey: n.key,
                onInternalAfterLeave: this.handleAfterLeave
            })))), null === (e = (n = this.$slots).default) || void 0 === e ? void 0 : e.call(n)])
        }
    }),
    wn = P("n-loading-bar"),
    Pn = P("n-loading-bar-api");
const On = {
        common: O,
        self: function(n) {
            const {
                primaryColor: e,
                errorColor: t
            } = n;
            return {
                colorError: t,
                colorLoading: e,
                height: "2px"
            }
        }
    },
    Ln = L("loading-bar-container", "\n z-index: 5999;\n position: fixed;\n top: 0;\n left: 0;\n right: 0;\n height: 2px;\n", [fn({
        enterDuration: "0.3s",
        leaveDuration: "0.8s"
    }), L("loading-bar", "\n width: 100%;\n transition:\n max-width 4s linear,\n background .2s linear;\n height: var(--n-height);\n ", [j("starting", "\n background: var(--n-color-loading);\n "), j("finishing", "\n background: var(--n-color-loading);\n transition:\n max-width .2s linear,\n background .2s linear;\n "), j("error", "\n background: var(--n-color-error);\n transition:\n max-width .2s linear,\n background .2s linear;\n ")])]);
var jn = function(n, e, t, o) {
    return new(t || (t = Promise))((function(i, r) {
        function a(n) {
            try {
                l(o.next(n))
            } catch (e) {
                r(e)
            }
        }

        function s(n) {
            try {
                l(o.throw(n))
            } catch (e) {
                r(e)
            }
        }

        function l(n) {
            var e;
            n.done ? i(n.value) : (e = n.value, e instanceof t ? e : new t((function(n) {
                n(e)
            }))).then(a, s)
        }
        l((o = o.apply(n, e || [])).next())
    }))
};

function An(n, e) {
    return `${e}-loading-bar ${e}-loading-bar--${n}`
}
const zn = g({
        name: "LoadingBar",
        props: {
            containerClass: String,
            containerStyle: [String, Object]
        },
        setup() {
            const {
                inlineThemeDisabled: n
            } = F(), {
                props: e,
                mergedClsPrefixRef: t
            } = v(wn), o = y(null), i = y(!1), r = y(!1), a = y(!1), s = y(!1);
            let l = !1;
            const c = y(!1),
                d = B((() => {
                    const {
                        loadingBarStyle: n
                    } = e;
                    return n ? n[c.value ? "error" : "loading"] : ""
                }));

            function u() {
                return jn(this, void 0, void 0, (function*() {
                    i.value = !1, a.value = !1, l = !1, c.value = !1, s.value = !0, yield M(), s.value = !1
                }))
            }

            function h() {
                return jn(this, arguments, void 0, (function*(n = 0, e = 80, i = "starting") {
                    if (r.value = !0, yield u(), l) return;
                    a.value = !0, yield M();
                    const s = o.value;
                    s && (s.style.maxWidth = `${n}%`, s.style.transition = "none", s.offsetWidth, s.className = An(i, t.value), s.style.transition = "", s.style.maxWidth = `${e}%`)
                }))
            }
            const f = R("LoadingBar", "-loading-bar", Ln, On, e, t),
                p = B((() => {
                    const {
                        self: {
                            height: n,
                            colorError: e,
                            colorLoading: t
                        }
                    } = f.value;
                    return {
                        "--n-height": n,
                        "--n-color-loading": t,
                        "--n-color-error": e
                    }
                })),
                g = n ? $("loading-bar", void 0, p, e) : void 0;
            return {
                mergedClsPrefix: t,
                loadingBarRef: o,
                started: r,
                loading: a,
                entering: i,
                transitionDisabled: s,
                start: h,
                error: function() {
                    if (!l && !c.value)
                        if (a.value) {
                            c.value = !0;
                            const n = o.value;
                            if (!n) return;
                            n.className = An("error", t.value), n.style.maxWidth = "100%", n.offsetWidth, a.value = !1
                        } else h(100, 100, "error").then((() => {
                            c.value = !0;
                            const n = o.value;
                            n && (n.className = An("error", t.value), n.offsetWidth, a.value = !1)
                        }))
                },
                finish: function() {
                    return jn(this, void 0, void 0, (function*() {
                        if (l || c.value) return;
                        r.value && (yield M()), l = !0;
                        const n = o.value;
                        n && (n.className = An("finishing", t.value), n.style.maxWidth = "100%", n.offsetWidth, a.value = !1)
                    }))
                },
                handleEnter: function() {
                    i.value = !0
                },
                handleAfterEnter: function() {
                    i.value = !1
                },
                handleAfterLeave: function() {
                    return jn(this, void 0, void 0, (function*() {
                        yield u()
                    }))
                },
                mergedLoadingBarStyle: d,
                cssVars: n ? void 0 : p,
                themeClass: null == g ? void 0 : g.themeClass,
                onRender: null == g ? void 0 : g.onRender
            }
        },
        render() {
            if (!this.started) return null;
            const {
                mergedClsPrefix: n
            } = this;
            return m(A, {
                name: "fade-in-transition",
                appear: !0,
                onEnter: this.handleEnter,
                onAfterEnter: this.handleAfterEnter,
                onAfterLeave: this.handleAfterLeave,
                css: !this.transitionDisabled
            }, {
                default: () => {
                    var e;
                    return null === (e = this.onRender) || void 0 === e || e.call(this), z(m("div", {
                        class: [`${n}-loading-bar-container`, this.themeClass, this.containerClass],
                        style: this.containerStyle
                    }, m("div", {
                        ref: "loadingBarRef",
                        class: [`${n}-loading-bar`],
                        style: [this.cssVars, this.mergedLoadingBarStyle]
                    })), [
                        [E, this.loading || !this.loading && this.entering]
                    ])
                }
            })
        }
    }),
    En = g({
        name: "LoadingBarProvider",
        props: Object.assign(Object.assign({}, R.props), {
            to: {
                type: [String, Object, Boolean],
                default: void 0
            },
            containerClass: String,
            containerStyle: [String, Object],
            loadingBarStyle: {
                type: Object
            }
        }),
        setup(n) {
            const e = T(),
                t = y(null),
                o = {
                    start() {
                        var n;
                        e.value ? null === (n = t.value) || void 0 === n || n.start() : M((() => {
                            var n;
                            null === (n = t.value) || void 0 === n || n.start()
                        }))
                    },
                    error() {
                        var n;
                        e.value ? null === (n = t.value) || void 0 === n || n.error() : M((() => {
                            var n;
                            null === (n = t.value) || void 0 === n || n.error()
                        }))
                    },
                    finish() {
                        var n;
                        e.value ? null === (n = t.value) || void 0 === n || n.finish() : M((() => {
                            var n;
                            null === (n = t.value) || void 0 === n || n.finish()
                        }))
                    }
                },
                {
                    mergedClsPrefixRef: i
                } = F(n);
            return S(Pn, o), S(wn, {
                props: n,
                mergedClsPrefixRef: i
            }), Object.assign(o, {
                loadingBarRef: t
            })
        },
        render() {
            var n, e;
            return m(x, null, m(H, {
                disabled: !1 === this.to,
                to: this.to || "body"
            }, m(zn, {
                ref: "loadingBarRef",
                containerStyle: this.containerStyle,
                containerClass: this.containerClass
            })), null === (e = (n = this.$slots).default) || void 0 === e ? void 0 : e.call(n))
        }
    });
const Fn = g({
        name: "ModalEnvironment",
        props: Object.assign(Object.assign({}, c), {
            internalKey: {
                type: String,
                required: !0
            },
            onInternalAfterLeave: {
                type: Function,
                required: !0
            }
        }),
        setup(n) {
            const e = y(!0);

            function t() {
                e.value = !1
            }
            return {
                show: e,
                hide: t,
                handleUpdateShow: function(n) {
                    e.value = n
                },
                handleAfterLeave: function() {
                    const {
                        onInternalAfterLeave: e,
                        internalKey: t,
                        onAfterLeave: o
                    } = n;
                    e && e(t), o && o()
                },
                handleCloseClick: function() {
                    const {
                        onClose: e
                    } = n;
                    e ? Promise.resolve(e()).then((n => {
                        !1 !== n && t()
                    })) : t()
                },
                handleNegativeClick: function() {
                    const {
                        onNegativeClick: e
                    } = n;
                    e ? Promise.resolve(e()).then((n => {
                        !1 !== n && t()
                    })) : t()
                },
                handlePositiveClick: function() {
                    const {
                        onPositiveClick: e
                    } = n;
                    e ? Promise.resolve(e()).then((n => {
                        !1 !== n && t()
                    })) : t()
                },
                handleMaskClick: function(e) {
                    const {
                        onMaskClick: o,
                        maskClosable: i
                    } = n;
                    o && (o(e), i && t())
                },
                handleEsc: function() {
                    const {
                        onEsc: e
                    } = n;
                    e && e()
                }
            }
        },
        render() {
            const {
                handleUpdateShow: n,
                handleAfterLeave: e,
                handleMaskClick: t,
                handleEsc: i,
                show: r
            } = this;
            return m(o, Object.assign({}, this.$props, {
                show: r,
                onUpdateShow: n,
                onMaskClick: t,
                onEsc: i,
                onAfterLeave: e,
                internalAppear: !0,
                internalModal: !0
            }))
        }
    }),
    Bn = g({
        name: "ModalProvider",
        props: {
            to: [String, Object]
        },
        setup() {
            const n = y([]),
                e = {};
            const t = {
                create: function(t = {}) {
                    const o = k(),
                        i = w(Object.assign(Object.assign({}, t), {
                            key: o,
                            destroy: () => {
                                var n;
                                null === (n = e[`n-modal-${o}`]) || void 0 === n || n.hide()
                            }
                        }));
                    return n.value.push(i), i
                },
                destroyAll: function() {
                    Object.values(e).forEach((n => {
                        null == n || n.hide()
                    }))
                }
            };
            return S(u, t), S(d, {
                clickedRef: a(64),
                clickedPositionRef: r()
            }), S(h, n), Object.assign(Object.assign({}, t), {
                modalList: n,
                modalInstRefs: e,
                handleAfterLeave: function(e) {
                    const {
                        value: t
                    } = n;
                    t.splice(t.findIndex((n => n.key === e)), 1)
                }
            })
        },
        render() {
            var n, e;
            return m(x, null, [this.modalList.map((n => {
                var e;
                return m(Fn, C(n, ["destroy"], {
                    to: null !== (e = n.to) && void 0 !== e ? e : this.to,
                    ref: e => {
                        null === e ? delete this.modalInstRefs[`n-modal-${n.key}`] : this.modalInstRefs[`n-modal-${n.key}`] = e
                    },
                    internalKey: n.key,
                    onInternalAfterLeave: this.handleAfterLeave
                }))
            })), null === (e = (n = this.$slots).default) || void 0 === e ? void 0 : e.call(n)])
        }
    }),
    Rn = P("n-notification-provider"),
    $n = g({
        name: "NotificationContainer",
        props: {
            scrollable: {
                type: Boolean,
                required: !0
            },
            placement: {
                type: String,
                required: !0
            }
        },
        setup() {
            const {
                mergedThemeRef: n,
                mergedClsPrefixRef: e,
                wipTransitionCountRef: t
            } = v(Rn), o = y(null);
            return I((() => {
                var n, e;
                t.value > 0 ? null === (n = null == o ? void 0 : o.value) || void 0 === n || n.classList.add("transitioning") : null === (e = null == o ? void 0 : o.value) || void 0 === e || e.classList.remove("transitioning")
            })), {
                selfRef: o,
                mergedTheme: n,
                mergedClsPrefix: e,
                transitioning: t
            }
        },
        render() {
            const {
                $slots: n,
                scrollable: e,
                mergedClsPrefix: t,
                mergedTheme: o,
                placement: i
            } = this;
            return m("div", {
                ref: "selfRef",
                class: [`${t}-notification-container`, e && `${t}-notification-container--scrollable`, `${t}-notification-container--${i}`]
            }, e ? m(un, {
                theme: o.peers.Scrollbar,
                themeOverrides: o.peerOverrides.Scrollbar,
                contentStyle: {
                    overflow: "hidden"
                }
            }, n) : n)
        }
    }),
    Mn = {
        info: () => m(Z, null),
        success: () => m(U, null),
        warning: () => m(W, null),
        error: () => m(q, null),
        default: () => null
    },
    Hn = {
        closable: {
            type: Boolean,
            default: !0
        },
        type: {
            type: String,
            default: "default"
        },
        avatar: Function,
        title: [String, Function],
        description: [String, Function],
        content: [String, Function],
        meta: [String, Function],
        action: [String, Function],
        onClose: {
            type: Function,
            required: !0
        },
        keepAliveOnHover: Boolean,
        onMouseenter: Function,
        onMouseleave: Function
    },
    Tn = hn(Hn),
    In = g({
        name: "Notification",
        props: Hn,
        setup(n) {
            const {
                mergedClsPrefixRef: e,
                mergedThemeRef: t,
                props: o
            } = v(Rn), {
                inlineThemeDisabled: i,
                mergedRtlRef: r
            } = F(), a = D("Notification", r, e), s = B((() => {
                const {
                    type: e
                } = n, {
                    self: {
                        color: o,
                        textColor: i,
                        closeIconColor: r,
                        closeIconColorHover: a,
                        closeIconColorPressed: s,
                        headerTextColor: l,
                        descriptionTextColor: c,
                        actionTextColor: d,
                        borderRadius: u,
                        headerFontWeight: h,
                        boxShadow: f,
                        lineHeight: v,
                        fontSize: p,
                        closeMargin: g,
                        closeSize: m,
                        width: b,
                        padding: y,
                        closeIconSize: C,
                        closeBorderRadius: x,
                        closeColorHover: S,
                        closeColorPressed: k,
                        titleFontSize: w,
                        metaFontSize: P,
                        descriptionFontSize: O,
                        [K("iconColor", e)]: L
                    },
                    common: {
                        cubicBezierEaseOut: j,
                        cubicBezierEaseIn: A,
                        cubicBezierEaseInOut: z
                    }
                } = t.value, {
                    left: E,
                    right: F,
                    top: B,
                    bottom: R
                } = vn(y);
                return {
                    "--n-color": o,
                    "--n-font-size": p,
                    "--n-text-color": i,
                    "--n-description-text-color": c,
                    "--n-action-text-color": d,
                    "--n-title-text-color": l,
                    "--n-title-font-weight": h,
                    "--n-bezier": z,
                    "--n-bezier-ease-out": j,
                    "--n-bezier-ease-in": A,
                    "--n-border-radius": u,
                    "--n-box-shadow": f,
                    "--n-close-border-radius": x,
                    "--n-close-color-hover": S,
                    "--n-close-color-pressed": k,
                    "--n-close-icon-color": r,
                    "--n-close-icon-color-hover": a,
                    "--n-close-icon-color-pressed": s,
                    "--n-line-height": v,
                    "--n-icon-color": L,
                    "--n-close-margin": g,
                    "--n-close-size": m,
                    "--n-close-icon-size": C,
                    "--n-width": b,
                    "--n-padding-left": E,
                    "--n-padding-right": F,
                    "--n-padding-top": B,
                    "--n-padding-bottom": R,
                    "--n-title-font-size": w,
                    "--n-meta-font-size": P,
                    "--n-description-font-size": O
                }
            })), l = i ? $("notification", B((() => n.type[0])), s, o) : void 0;
            return {
                mergedClsPrefix: e,
                showAvatar: B((() => n.avatar || "default" !== n.type)),
                handleCloseClick() {
                    n.onClose()
                },
                rtlEnabled: a,
                cssVars: i ? void 0 : s,
                themeClass: null == l ? void 0 : l.themeClass,
                onRender: null == l ? void 0 : l.onRender
            }
        },
        render() {
            var n;
            const {
                mergedClsPrefix: e
            } = this;
            return null === (n = this.onRender) || void 0 === n || n.call(this), m("div", {
                class: [`${e}-notification-wrapper`, this.themeClass],
                onMouseenter: this.onMouseenter,
                onMouseleave: this.onMouseleave,
                style: this.cssVars
            }, m("div", {
                class: [`${e}-notification`, this.rtlEnabled && `${e}-notification--rtl`, this.themeClass, {
                    [`${e}-notification--closable`]: this.closable,
                    [`${e}-notification--show-avatar`]: this.showAvatar
                }],
                style: this.cssVars
            }, this.showAvatar ? m("div", {
                class: `${e}-notification__avatar`
            }, this.avatar ? N(this.avatar) : "default" !== this.type ? m(V, {
                clsPrefix: e
            }, {
                default: () => Mn[this.type]()
            }) : null) : null, this.closable ? m(_, {
                clsPrefix: e,
                class: `${e}-notification__close`,
                onClick: this.handleCloseClick
            }) : null, m("div", {
                ref: "bodyRef",
                class: `${e}-notification-main`
            }, this.title ? m("div", {
                class: `${e}-notification-main__header`
            }, N(this.title)) : null, this.description ? m("div", {
                class: `${e}-notification-main__description`
            }, N(this.description)) : null, this.content ? m("pre", {
                class: `${e}-notification-main__content`
            }, N(this.content)) : null, this.meta || this.action ? m("div", {
                class: `${e}-notification-main-footer`
            }, this.meta ? m("div", {
                class: `${e}-notification-main-footer__meta`
            }, N(this.meta)) : null, this.action ? m("div", {
                class: `${e}-notification-main-footer__action`
            }, N(this.action)) : null) : null)))
        }
    }),
    Nn = Object.assign(Object.assign({}, Hn), {
        duration: Number,
        onClose: Function,
        onLeave: Function,
        onAfterEnter: Function,
        onAfterLeave: Function,
        onHide: Function,
        onAfterShow: Function,
        onAfterHide: Function
    }),
    Vn = g({
        name: "NotificationEnvironment",
        props: Object.assign(Object.assign({}, Nn), {
            internalKey: {
                type: String,
                required: !0
            },
            onInternalAfterLeave: {
                type: Function,
                required: !0
            }
        }),
        setup(n) {
            const {
                wipTransitionCountRef: e
            } = v(Rn), t = y(!0);
            let o = null;

            function i() {
                t.value = !1, o && window.clearTimeout(o)
            }
            return X((() => {
                n.duration && (o = window.setTimeout(i, n.duration))
            })), {
                show: t,
                hide: i,
                handleClose: function() {
                    const {
                        onClose: e
                    } = n;
                    e ? Promise.resolve(e()).then((n => {
                        !1 !== n && i()
                    })) : i()
                },
                handleAfterLeave: function() {
                    e.value--;
                    const {
                        onAfterLeave: t,
                        onInternalAfterLeave: o,
                        onAfterHide: i,
                        internalKey: r
                    } = n;
                    t && t(), o(r), i && i()
                },
                handleLeave: function(e) {
                    const {
                        onHide: t
                    } = n;
                    t && t(), e.style.maxHeight = "0", e.offsetHeight
                },
                handleBeforeLeave: function(n) {
                    e.value++, n.style.maxHeight = `${n.offsetHeight}px`, n.style.height = `${n.offsetHeight}px`, n.offsetHeight
                },
                handleAfterEnter: function(t) {
                    e.value--, t.style.height = "", t.style.maxHeight = "";
                    const {
                        onAfterEnter: o,
                        onAfterShow: i
                    } = n;
                    o && o(), i && i()
                },
                handleBeforeEnter: function(n) {
                    e.value++, M((() => {
                        n.style.height = `${n.offsetHeight}px`, n.style.maxHeight = "0", n.style.transition = "none", n.offsetHeight, n.style.transition = "", n.style.maxHeight = n.style.height
                    }))
                },
                handleMouseenter: function(n) {
                    n.currentTarget === n.target && null !== o && (window.clearTimeout(o), o = null)
                },
                handleMouseleave: function(e) {
                    e.currentTarget === e.target && function() {
                        const {
                            duration: e
                        } = n;
                        e && (o = window.setTimeout(i, e))
                    }()
                }
            }
        },
        render() {
            return m(A, {
                name: "notification-transition",
                appear: !0,
                onBeforeEnter: this.handleBeforeEnter,
                onAfterEnter: this.handleAfterEnter,
                onBeforeLeave: this.handleBeforeLeave,
                onLeave: this.handleLeave,
                onAfterLeave: this.handleAfterLeave
            }, {
                default: () => this.show ? m(In, Object.assign({}, gn(this.$props, Tn), {
                    onClose: this.handleClose,
                    onMouseenter: this.duration && this.keepAliveOnHover ? this.handleMouseenter : void 0,
                    onMouseleave: this.duration && this.keepAliveOnHover ? this.handleMouseleave : void 0
                })) : null
            })
        }
    }),
    _n = J([L("notification-container", "\n z-index: 4000;\n position: fixed;\n overflow: visible;\n display: flex;\n flex-direction: column;\n align-items: flex-end;\n ", [J(">", [L("scrollbar", "\n width: initial;\n overflow: visible;\n height: -moz-fit-content !important;\n height: fit-content !important;\n max-height: 100vh !important;\n ", [J(">", [L("scrollbar-container", "\n height: -moz-fit-content !important;\n height: fit-content !important;\n max-height: 100vh !important;\n ", [L("scrollbar-content", "\n padding-top: 12px;\n padding-bottom: 33px;\n ")])])])]), j("top, top-right, top-left", "\n top: 12px;\n ", [J("&.transitioning >", [L("scrollbar", [J(">", [L("scrollbar-container", "\n min-height: 100vh !important;\n ")])])])]), j("bottom, bottom-right, bottom-left", "\n bottom: 12px;\n ", [J(">", [L("scrollbar", [J(">", [L("scrollbar-container", [L("scrollbar-content", "\n padding-bottom: 12px;\n ")])])])]), L("notification-wrapper", "\n display: flex;\n align-items: flex-end;\n margin-bottom: 0;\n margin-top: 12px;\n ")]), j("top, bottom", "\n left: 50%;\n transform: translateX(-50%);\n ", [L("notification-wrapper", [J("&.notification-transition-enter-from, &.notification-transition-leave-to", "\n transform: scale(0.85);\n "), J("&.notification-transition-leave-from, &.notification-transition-enter-to", "\n transform: scale(1);\n ")])]), j("top", [L("notification-wrapper", "\n transform-origin: top center;\n ")]), j("bottom", [L("notification-wrapper", "\n transform-origin: bottom center;\n ")]), j("top-right, bottom-right", [L("notification", "\n margin-left: 28px;\n margin-right: 16px;\n ")]), j("top-left, bottom-left", [L("notification", "\n margin-left: 16px;\n margin-right: 28px;\n ")]), j("top-right", "\n right: 0;\n ", [Dn("top-right")]), j("top-left", "\n left: 0;\n ", [Dn("top-left")]), j("bottom-right", "\n right: 0;\n ", [Dn("bottom-right")]), j("bottom-left", "\n left: 0;\n ", [Dn("bottom-left")]), j("scrollable", [j("top-right", "\n top: 0;\n "), j("top-left", "\n top: 0;\n "), j("bottom-right", "\n bottom: 0;\n "), j("bottom-left", "\n bottom: 0;\n ")]), L("notification-wrapper", "\n margin-bottom: 12px;\n ", [J("&.notification-transition-enter-from, &.notification-transition-leave-to", "\n opacity: 0;\n margin-top: 0 !important;\n margin-bottom: 0 !important;\n "), J("&.notification-transition-leave-from, &.notification-transition-enter-to", "\n opacity: 1;\n "), J("&.notification-transition-leave-active", "\n transition:\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier),\n opacity .3s var(--n-bezier),\n transform .3s var(--n-bezier-ease-in),\n max-height .3s var(--n-bezier),\n margin-top .3s linear,\n margin-bottom .3s linear,\n box-shadow .3s var(--n-bezier);\n "), J("&.notification-transition-enter-active", "\n transition:\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier),\n opacity .3s var(--n-bezier),\n transform .3s var(--n-bezier-ease-out),\n max-height .3s var(--n-bezier),\n margin-top .3s linear,\n margin-bottom .3s linear,\n box-shadow .3s var(--n-bezier);\n ")]), L("notification", "\n background-color: var(--n-color);\n color: var(--n-text-color);\n transition:\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier),\n opacity .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier);\n font-family: inherit;\n font-size: var(--n-font-size);\n font-weight: 400;\n position: relative;\n display: flex;\n overflow: hidden;\n flex-shrink: 0;\n padding-left: var(--n-padding-left);\n padding-right: var(--n-padding-right);\n width: var(--n-width);\n max-width: calc(100vw - 16px - 16px);\n border-radius: var(--n-border-radius);\n box-shadow: var(--n-box-shadow);\n box-sizing: border-box;\n opacity: 1;\n ", [Y("avatar", [L("icon", "\n color: var(--n-icon-color);\n "), L("base-icon", "\n color: var(--n-icon-color);\n ")]), j("show-avatar", [L("notification-main", "\n margin-left: 40px;\n width: calc(100% - 40px); \n ")]), j("closable", [L("notification-main", [J("> *:first-child", "\n padding-right: 20px;\n ")]), Y("close", "\n position: absolute;\n top: 0;\n right: 0;\n margin: var(--n-close-margin);\n transition:\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier);\n ")]), Y("avatar", "\n position: absolute;\n top: var(--n-padding-top);\n left: var(--n-padding-left);\n width: 28px;\n height: 28px;\n font-size: 28px;\n display: flex;\n align-items: center;\n justify-content: center;\n ", [L("icon", "transition: color .3s var(--n-bezier);")]), L("notification-main", "\n padding-top: var(--n-padding-top);\n padding-bottom: var(--n-padding-bottom);\n box-sizing: border-box;\n display: flex;\n flex-direction: column;\n margin-left: 8px;\n width: calc(100% - 8px);\n ", [L("notification-main-footer", "\n display: flex;\n align-items: center;\n justify-content: space-between;\n margin-top: 12px;\n ", [Y("meta", "\n font-size: var(--n-meta-font-size);\n transition: color .3s var(--n-bezier-ease-out);\n color: var(--n-description-text-color);\n "), Y("action", "\n cursor: pointer;\n transition: color .3s var(--n-bezier-ease-out);\n color: var(--n-action-text-color);\n ")]), Y("header", "\n font-weight: var(--n-title-font-weight);\n font-size: var(--n-title-font-size);\n transition: color .3s var(--n-bezier-ease-out);\n color: var(--n-title-text-color);\n "), Y("description", "\n margin-top: 8px;\n font-size: var(--n-description-font-size);\n white-space: pre-wrap;\n word-wrap: break-word;\n transition: color .3s var(--n-bezier-ease-out);\n color: var(--n-description-text-color);\n "), Y("content", "\n line-height: var(--n-line-height);\n margin: 12px 0 0 0;\n font-family: inherit;\n white-space: pre-wrap;\n word-wrap: break-word;\n transition: color .3s var(--n-bezier-ease-out);\n color: var(--n-text-color);\n ", [J("&:first-child", "margin: 0;")])])])])]);

function Dn(n) {
    const e = n.split("-")[1];
    return L("notification-wrapper", [J("&.notification-transition-enter-from, &.notification-transition-leave-to", `\n transform: translate(${"left"===e?"calc(-100%)":"calc(100%)"}, 0);\n `), J("&.notification-transition-leave-from, &.notification-transition-enter-to", "\n transform: translate(0, 0);\n ")])
}
const Kn = P("n-notification-api"),
    qn = g({
        name: "NotificationProvider",
        props: Object.assign(Object.assign({}, R.props), {
            containerClass: String,
            containerStyle: [String, Object],
            to: [String, Object],
            scrollable: {
                type: Boolean,
                default: !0
            },
            max: Number,
            placement: {
                type: String,
                default: "top-right"
            },
            keepAliveOnHover: Boolean
        }),
        setup(n) {
            const {
                mergedClsPrefixRef: e
            } = F(n), t = y([]), o = {}, i = new Set;

            function r(e) {
                const r = k(),
                    a = () => {
                        i.add(r), o[r] && o[r].hide()
                    },
                    s = w(Object.assign(Object.assign({}, e), {
                        key: r,
                        destroy: a,
                        hide: a,
                        deactivate: a
                    })),
                    {
                        max: l
                    } = n;
                if (l && t.value.length - i.size >= l) {
                    let n = !1,
                        e = 0;
                    for (const r of t.value) {
                        if (!i.has(r.key)) {
                            o[r.key] && (r.destroy(), n = !0);
                            break
                        }
                        e++
                    }
                    n || t.value.splice(e, 1)
                }
                return t.value.push(s), s
            }
            const a = ["info", "success", "warning", "error"].map((n => e => r(Object.assign(Object.assign({}, e), {
                type: n
            }))));
            const s = R("Notification", "-notification", _n, G, n, e),
                l = {
                    create: r,
                    info: a[0],
                    success: a[1],
                    warning: a[2],
                    error: a[3],
                    open: function(n) {
                        return r(n)
                    },
                    destroyAll: function() {
                        Object.values(t.value).forEach((n => {
                            n.hide()
                        }))
                    }
                },
                c = y(0);
            return S(Kn, l), S(Rn, {
                props: n,
                mergedClsPrefixRef: e,
                mergedThemeRef: s,
                wipTransitionCountRef: c
            }), Object.assign({
                mergedClsPrefix: e,
                notificationList: t,
                notificationRefs: o,
                handleAfterLeave: function(n) {
                    i.delete(n), t.value.splice(t.value.findIndex((e => e.key === n)), 1)
                }
            }, l)
        },
        render() {
            var n, e, t;
            const {
                placement: o
            } = this;
            return m(x, null, null === (e = (n = this.$slots).default) || void 0 === e ? void 0 : e.call(n), this.notificationList.length ? m(H, {
                to: null !== (t = this.to) && void 0 !== t ? t : "body"
            }, m($n, {
                class: this.containerClass,
                style: this.containerStyle,
                scrollable: this.scrollable && "top" !== o && "bottom" !== o,
                placement: o
            }, {
                default: () => this.notificationList.map((n => m(Vn, Object.assign({
                    ref: e => {
                        const t = n.key;
                        null === e ? delete this.notificationRefs[t] : this.notificationRefs[t] = e
                    }
                }, C(n, ["destroy", "hide", "deactivate"]), {
                    internalKey: n.key,
                    onInternalAfterLeave: this.handleAfterLeave,
                    keepAliveOnHover: void 0 === n.keepAliveOnHover ? this.keepAliveOnHover : n.keepAliveOnHover
                }))))
            })) : null)
        }
    });
const Wn = g({
        name: "InjectionExtractor",
        props: {
            onSetup: Function
        },
        setup(n, {
            slots: e
        }) {
            var t;
            return null === (t = n.onSetup) || void 0 === t || t.call(n), () => {
                var n;
                return null === (n = e.default) || void 0 === n ? void 0 : n.call(e)
            }
        }
    }),
    Un = {
        message: mn,
        notification: function() {
            const n = v(Kn, null);
            return null === n && p("use-notification", "No outer `n-notification-provider` found."), n
        },
        loadingBar: function() {
            const n = v(Pn, null);
            return null === n && p("use-loading-bar", "No outer <n-loading-bar-provider /> founded."), n
        },
        dialog: function() {
            const e = v(n, null);
            return null === e && p("use-dialog", "No outer <n-dialog-provider /> founded."), e
        },
        modal: f
    };

function Zn(n, {
    configProviderProps: e,
    messageProviderProps: t,
    dialogProviderProps: o,
    notificationProviderProps: i,
    loadingBarProviderProps: r,
    modalProviderProps: a
} = {}) {
    const s = [];
    n.forEach((n => {
        switch (n) {
            case "message":
                s.push({
                    type: n,
                    Provider: on,
                    props: t
                });
                break;
            case "notification":
                s.push({
                    type: n,
                    Provider: qn,
                    props: i
                });
                break;
            case "dialog":
                s.push({
                    type: n,
                    Provider: kn,
                    props: o
                });
                break;
            case "loadingBar":
                s.push({
                    type: n,
                    Provider: En,
                    props: r
                });
                break;
            case "modal":
                s.push({
                    type: n,
                    Provider: Bn,
                    props: a
                })
        }
    }));
    const l = function({
        providersAndProps: n,
        configProviderProps: e
    }) {
        let t = Q((function() {
            return m(en, nn(e), {
                default: () => n.map((({
                    type: n,
                    Provider: e,
                    props: t
                }) => m(e, nn(t), {
                    default: () => m(Wn, {
                        onSetup: () => o[n] = Un[n]()
                    })
                })))
            })
        }));
        const o = {
            app: t
        };
        let i;
        return bn && (i = document.createElement("div"), document.body.appendChild(i), t.mount(i)), Object.assign({
            unmount: () => {
                var n;
                null !== t && null !== i ? (t.unmount(), null === (n = i.parentNode) || void 0 === n || n.removeChild(i), i = null, t = null) : tn("discrete", "unmount call no need because discrete app has been unmounted")
            }
        }, o)
    }({
        providersAndProps: s,
        configProviderProps: e
    });
    return l
}
const Xn = {
        common: O,
        self: function(n) {
            const {
                heightSmall: e,
                heightMedium: t,
                heightLarge: o,
                borderRadius: i
            } = n;
            return {
                color: "#eee",
                colorEnd: "#ddd",
                borderRadius: i,
                heightSmall: e,
                heightMedium: t,
                heightLarge: o
            }
        }
    },
    Jn = J([L("skeleton", "\n height: 1em;\n width: 100%;\n transition:\n --n-color-start .3s var(--n-bezier),\n --n-color-end .3s var(--n-bezier),\n background-color .3s var(--n-bezier);\n animation: 2s skeleton-loading infinite cubic-bezier(0.36, 0, 0.64, 1);\n background-color: var(--n-color-start);\n "), J("@keyframes skeleton-loading", "\n 0% {\n background: var(--n-color-start);\n }\n 40% {\n background: var(--n-color-end);\n }\n 80% {\n background: var(--n-color-start);\n }\n 100% {\n background: var(--n-color-start);\n }\n ")]),
    Yn = g({
        name: "Skeleton",
        inheritAttrs: !1,
        props: Object.assign(Object.assign({}, R.props), {
            text: Boolean,
            round: Boolean,
            circle: Boolean,
            height: [String, Number],
            width: [String, Number],
            size: String,
            repeat: {
                type: Number,
                default: 1
            },
            animated: {
                type: Boolean,
                default: !0
            },
            sharp: {
                type: Boolean,
                default: !0
            }
        }),
        setup(n) {
            ! function() {
                if (bn && window.CSS && !Cn && (Cn = !0, "registerProperty" in (null === window || void 0 === window ? void 0 : window.CSS))) try {
                    CSS.registerProperty({
                        name: "--n-color-start",
                        syntax: "<color>",
                        inherits: !1,
                        initialValue: "#0000"
                    }), CSS.registerProperty({
                        name: "--n-color-end",
                        syntax: "<color>",
                        inherits: !1,
                        initialValue: "#0000"
                    })
                } catch (n) {}
            }();
            const {
                mergedClsPrefixRef: e
            } = F(n), t = R("Skeleton", "-skeleton", Jn, Xn, n, e);
            return {
                mergedClsPrefix: e,
                style: B((() => {
                    var e, o;
                    const i = t.value,
                        {
                            common: {
                                cubicBezierEaseInOut: r
                            }
                        } = i,
                        a = i.self,
                        {
                            color: s,
                            colorEnd: l,
                            borderRadius: c
                        } = a;
                    let d;
                    const {
                        circle: u,
                        sharp: h,
                        round: f,
                        width: v,
                        height: p,
                        size: g,
                        text: m,
                        animated: b
                    } = n;
                    void 0 !== g && (d = a[K("height", g)]);
                    const y = u ? null !== (e = null != v ? v : p) && void 0 !== e ? e : d : v,
                        C = null !== (o = u && null != v ? v : p) && void 0 !== o ? o : d;
                    return {
                        display: m ? "inline-block" : "",
                        verticalAlign: m ? "-0.125em" : "",
                        borderRadius: u ? "50%" : f ? "4096px" : h ? "" : c,
                        width: "number" == typeof y ? pn(y) : y,
                        height: "number" == typeof C ? pn(C) : C,
                        animation: b ? "" : "none",
                        "--n-bezier": r,
                        "--n-color-start": s,
                        "--n-color-end": l
                    }
                }))
            }
        },
        render() {
            const {
                repeat: n,
                style: e,
                mergedClsPrefix: t,
                $attrs: o
            } = this, i = m("div", rn({
                class: `${t}-skeleton`,
                style: e
            }, o));
            return n > 1 ? m(x, null, an(n, null).map((n => [i, "\n"]))) : i
        }
    }),
    Gn = {
        width: "16",
        height: "16",
        viewBox: "0 0 16 16",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Qn = {
    render: function(n, e) {
        return cn(), sn("svg", Gn, e[0] || (e[0] = [ln("path", {
            d: "M3 4.24997C3 4.13063 3.0439 4.01617 3.12204 3.93178C3.20018 3.84739 3.30616 3.79998 3.41667 3.79998C4.89611 3.79998 6.33778 3.23419 7.75 2.09C7.82212 2.03158 7.90985 2 8 2C8.09015 2 8.17788 2.03158 8.25 2.09C9.66222 3.23419 11.1039 3.79998 12.5833 3.79998C12.6938 3.79998 12.7998 3.84739 12.878 3.93178C12.9561 4.01617 13 4.13063 13 4.24997V7.39994C13 10.4005 11.3567 12.6055 8.15278 13.9687C8.0546 14.0104 7.9454 14.0104 7.84722 13.9687C4.64333 12.6055 3 10.3999 3 7.39994V4.24997Z",
            fill: "#0F7FFF"
        }, null, -1), ln("path", {
            d: "M8 10.5V7.5M8 5.5V5",
            stroke: "white",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }, null, -1)]))
    }
};
class ne {
    constructor() {
        this.scrollableParent = null, this.userScrolled = !0, this.isProgrammaticScroll = !1, this.onScrollFunction = this.onScroll.bind(this), this.onDebouncedScrollFunction = yn(this.onDebouncedScroll.bind(this), 100), this.lastScrollTop = 0, this.lastScrollHeight = 0, this.toBottomVisible = y(!1), this.onTouchMoveFunction = this.onTouchMove.bind(this), this.onSelectionChangeFunction = this.onSelectionChange.bind(this), this.hasSelection = !1, this.inputFocused = !1
    }
    setScrollableParent(n) {
        this.scrollableParent && this.stop(), this.scrollableParent = n, this.scrollableParent === document.documentElement ? (document.addEventListener("scroll", this.onScrollFunction), document.addEventListener("touchmove", this.onTouchMoveFunction), document.addEventListener("selectionchange", this.onSelectionChangeFunction)) : (this.scrollableParent.addEventListener("scroll", this.onScrollFunction), this.scrollableParent.addEventListener("touchmove", this.onTouchMoveFunction), document.addEventListener("selectionchange", this.onSelectionChangeFunction)), this.toBottomVisible.value = this.scrollableParent.scrollHeight - this.scrollableParent.clientHeight - this.scrollableParent.scrollTop > 10
    }
    onTouchMove(n) {
        this.userTouchMoved = !0
    }
    onDebouncedScroll(n) {
        this.scrollableParent && (this.lastScrollTop = this.scrollableParent.scrollTop, this.toBottomVisible.value = this.scrollableParent.scrollHeight - this.scrollableParent.clientHeight - this.scrollableParent.scrollTop > 100)
    }
    onScroll(n) {
        this.onDebouncedScrollFunction(n), this.userScrolled || (this.isProgrammaticScroll ? this.isProgrammaticScroll = !1 : this.lastScrollHeight === this.scrollableParent.scrollHeight ? this.userScrolled = !0 : this.lastScrollHeight = this.scrollableParent.scrollHeight)
    }
    autoScrollToBottom() {
        let n = this.userScrolled;
        dn.isMobile() && (n = n && this.userTouchMoved), this.hasSelection || dn.isMobile() && dn.isIos() && this.inputFocused || !n && this.scrollableParent && (this.isProgrammaticScroll = !0, this.scrollableParent.scrollTop = this.scrollableParent.scrollHeight - this.scrollableParent.clientHeight, this.toBottomVisible.value = !1)
    }
    start() {
        this.userScrolled = !1, this.userTouchMoved = !1, this.hasSelection = !1, this.inputFocused = !1, this.autoScrollToBottom()
    }
    setInputFocused(n) {
        this.inputFocused = n
    }
    stop() {
        this.scrollableParent && (this.scrollableParent === document.documentElement ? (document.removeEventListener("scroll", this.onScrollFunction), document.removeEventListener("touchmove", this.onTouchMoveFunction), document.removeEventListener("selectionchange", this.onSelectionChangeFunction)) : (this.scrollableParent.removeEventListener("scroll", this.onScrollFunction), this.scrollableParent.removeEventListener("touchmove", this.onTouchMoveFunction), document.removeEventListener("selectionchange", this.onSelectionChangeFunction))), this.scrollableParent = null
    }
    onSelectionChange(n) {
        const e = window.getSelection();
        e && e.toString().length > 0 && (this.hasSelection = !0)
    }
}
const ee = {
    width: "16",
    height: "16",
    viewBox: "0 0 16 16",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const te = {
    render: function(n, e) {
        return cn(), sn("svg", ee, e[0] || (e[0] = [ln("path", {
            d: "M5 7.50039C5 6.08621 5 5.37861 5.43956 4.93956C5.87861 4.5 6.58621 4.5 8.00039 4.5H9.50059C10.9148 4.5 11.6224 4.5 12.0614 4.93956C12.501 5.37861 12.501 6.08621 12.501 7.50039V10.0007C12.501 11.4149 12.501 12.1225 12.0614 12.5615C11.6224 13.0011 10.9148 13.0011 9.50059 13.0011H8.00039C6.58621 13.0011 5.87861 13.0011 5.43956 12.5615C5 12.1225 5 11.4149 5 10.0007V7.50039Z",
            stroke: "currentColor"
        }, null, -1), ln("path", {
            d: "M5.0002 11.5011C4.60232 11.5011 4.22074 11.3431 3.9394 11.0617C3.65806 10.7804 3.5 10.3988 3.5 10.0009V7.00052C3.5 5.11478 3.5 4.17165 4.08608 3.58608C4.67165 3 5.61478 3 7.50052 3H9.50078C9.89866 3 10.2802 3.15806 10.5616 3.4394C10.8429 3.72074 11.001 4.10232 11.001 4.5002",
            stroke: "currentColor"
        }, null, -1)]))
    }
};
export {
    ne as A, te as C, Yn as N, Qn as S, Zn as c
};