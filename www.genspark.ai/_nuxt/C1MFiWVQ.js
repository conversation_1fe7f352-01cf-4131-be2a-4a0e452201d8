import {
    c as e,
    a0 as t,
    a1 as n
} from "./Cf0SOiw0.js";
let i = null,
    a = null;
const r = n({
        isPlaying: !1,
        progress: 0,
        currentTime: 0,
        duration: 0,
        currentPodcast: null,
        isLoading: !1,
        isBuffering: !1,
        hasError: !1,
        volume: 1,
        isMuted: !1,
        playbackSpeed: 1,
        playlist: [],
        currentIndex: -1,
        isShuffled: !1,
        repeatMode: "none"
    }),
    s = new Set,
    l = () => {
        const n = () => {
                s.forEach((e => e(r)))
            },
            l = (e, t = 0, i = !1) => {
                if (r.playlist = [...e], r.currentIndex = Math.max(0, Math.min(t, e.length - 1)), e.length > 0 && r.currentIndex >= 0 && i) {
                    const t = e[r.currentIndex];
                    m(t)
                }
                n()
            },
            d = (e, t = !1) => {
                const i = r.playlist.findIndex((t => t.id === e.id)); - 1 === i ? (r.playlist.push(e), (1 === r.playlist.length || t) && (r.currentIndex = r.playlist.length - 1, m(e))) : t && (r.currentIndex = i, m(e)), n()
            },
            o = e => {
                if (e < 0 || e >= r.playlist.length) return;
                r.currentIndex = e;
                const t = r.playlist[e];
                m(t), i && i.play().catch((e => {
                    r.playlist.length > 1 && setTimeout((() => {
                        u()
                    }), 1e3)
                }))
            },
            u = () => {
                if (0 === r.playlist.length) return;
                let e = r.currentIndex + 1;
                if ("one" === r.repeatMode) e = r.currentIndex;
                else if (e >= r.playlist.length) {
                    if ("all" !== r.repeatMode) return void h();
                    e = 0
                }
                o(e)
            },
            c = () => {
                if (0 === r.playlist.length) return;
                let e = r.currentIndex - 1;
                "one" === r.repeatMode ? e = r.currentIndex : e < 0 && (e = "all" === r.repeatMode ? r.playlist.length - 1 : 0), o(e)
            },
            p = () => {
                r.playlist = [], r.currentIndex = -1, r.currentPodcast = null, v(), n()
            },
            g = () => {
                "mediaSession" in navigator && i && navigator.mediaSession.setPositionState({
                    duration: r.duration,
                    playbackRate: i.playbackRate || 1,
                    position: r.currentTime
                })
            },
            m = e => {
                var t;
                (null == (t = r.currentPodcast) ? void 0 : t.id) === e.id && i || (i && (i.pause(), i = null), r.isLoading = !0, r.isBuffering = !1, r.hasError = !1, r.currentPodcast = e, i = new Audio(e.audio_url), i.playbackRate = r.playbackSpeed, i.volume = r.isMuted ? 0 : r.volume, i.addEventListener("loadedmetadata", (() => {
                    i && (r.duration = i.duration, r.isLoading = !1, (e => {
                        "mediaSession" in navigator && i && (navigator.mediaSession.metadata = new MediaMetadata({
                            title: e.title || "AI Podcast",
                            artist: "Genspark",
                            album: "Genspark Podcast",
                            artwork: e.image ? [{
                                src: e.image,
                                sizes: "512x512",
                                type: "image/jpeg"
                            }] : []
                        }), navigator.mediaSession.setActionHandler("play", (() => {
                            i && !r.isPlaying && y()
                        })), navigator.mediaSession.setActionHandler("pause", (() => {
                            i && r.isPlaying && f()
                        })), navigator.mediaSession.setActionHandler("seekbackward", (() => {
                            if (i && r.duration) {
                                const e = Math.max(0, i.currentTime - 15);
                                i.currentTime = e, r.currentTime = e, r.progress = e / r.duration * 100, g(), n()
                            }
                        })), navigator.mediaSession.setActionHandler("seekforward", (() => {
                            if (i && r.duration) {
                                const e = Math.min(i.duration, i.currentTime + 15);
                                i.currentTime = e, r.currentTime = e, r.progress = e / r.duration * 100, g(), n()
                            }
                        })), navigator.mediaSession.setActionHandler("previoustrack", (() => {
                            c()
                        })), navigator.mediaSession.setActionHandler("nexttrack", (() => {
                            u()
                        })), navigator.mediaSession.setActionHandler("seekto", (e => {
                            if (i && r.duration && void 0 !== e.seekTime) {
                                const t = Math.max(0, Math.min(r.duration, e.seekTime));
                                i.currentTime = t, r.currentTime = t, r.progress = t / r.duration * 100, g(), n()
                            }
                        })))
                    })(e), n())
                })), i.addEventListener("timeupdate", (() => {
                    if (i) {
                        const e = i.currentTime / i.duration * 100;
                        r.progress = e || 0, r.currentTime = i.currentTime || 0, g(), n()
                    }
                })), i.addEventListener("ended", (() => {
                    if (r.isPlaying = !1, r.progress = 0, r.currentTime = 0, r.playlist.length > 0) {
                        i && i.duration > 0 && Math.abs(i.currentTime - i.duration) < 1 && setTimeout((() => {
                            u()
                        }), 500)
                    }
                    n()
                })), i.addEventListener("play", (() => {
                    r.isPlaying = !0, n()
                })), i.addEventListener("pause", (() => {
                    r.isPlaying = !1, n()
                })), i.addEventListener("error", (e => {
                    r.isPlaying = !1, r.isLoading = !1, r.isBuffering = !1, r.hasError = !0, r.playlist.length > 1 && setTimeout((() => {
                        u()
                    }), 1e3), n()
                })), i.addEventListener("loadstart", (() => {
                    r.isLoading = !0, n()
                })), i.addEventListener("canplay", (() => {
                    r.isLoading = !1, r.isBuffering = !1, n()
                })), i.addEventListener("waiting", (() => {
                    r.isBuffering = !0, n()
                })), i.addEventListener("canplaythrough", (() => {
                    r.isBuffering = !1, n()
                })), n())
            },
            y = async e => {
                if (e) {
                    const t = r.playlist.findIndex((t => t.id === e.id));
                    t >= 0 ? (r.isPlaying && r.currentIndex !== t && f(), r.currentIndex = t, m(e)) : (f(), d(e, !1), r.currentIndex = r.playlist.length - 1, m(e))
                }
                if (!i) {
                    if (!(r.playlist.length > 0 && r.currentIndex >= 0)) return;
                    if (m(r.playlist[r.currentIndex]), !i) return
                }
                try {
                    r.isPlaying ? (a && await a.catch((() => {})), i.pause(), a = null) : (a && await a.catch((() => {})), a = i.play(), await a, a = null)
                } catch (t) {
                    if (a = null, "AbortError" === t.name) return;
                    "NotAllowedError" === t.name || t.name, r.isPlaying = !1, n()
                }
            },
            f = () => {
                i && r.isPlaying && (i.pause(), r.isPlaying = !1)
            },
            h = () => {
                i && (i.pause(), i.currentTime = 0, r.progress = 0, r.currentTime = 0, n())
            },
            v = () => {
                i && (i.pause(), i = null), a = null, r.isPlaying = !1, r.progress = 0, r.currentTime = 0, r.duration = 0, r.currentPodcast = null, r.isLoading = !1, r.isBuffering = !1, r.hasError = !1, "mediaSession" in navigator && (navigator.mediaSession.metadata = null, navigator.mediaSession.setActionHandler("play", null), navigator.mediaSession.setActionHandler("pause", null), navigator.mediaSession.setActionHandler("seekbackward", null), navigator.mediaSession.setActionHandler("seekforward", null), navigator.mediaSession.setActionHandler("previoustrack", null), navigator.mediaSession.setActionHandler("nexttrack", null), navigator.mediaSession.setActionHandler("seekto", null)), n()
            };
        return {
            audioState: t(r),
            initAudio: m,
            togglePlay: y,
            pause: f,
            stop: h,
            seekTo: e => {
                if (!i || !r.duration) return;
                const t = e / 100 * r.duration;
                i.currentTime = t, r.currentTime = t, r.progress = e, n()
            },
            setVolume: e => {
                const t = Math.max(0, Math.min(1, e));
                r.volume = t, i && !r.isMuted && (i.volume = t), n()
            },
            toggleMute: () => {
                r.isMuted = !r.isMuted, i && (i.volume = r.isMuted ? 0 : r.volume), n()
            },
            setPlaybackSpeed: e => {
                const t = Math.max(.25, Math.min(4, e));
                r.playbackSpeed = t, i && (i.playbackRate = t), n()
            },
            rewind: (e = 15) => {
                if (!i || !r.duration || !Number.isFinite(r.duration)) return;
                const t = Math.max(0, i.currentTime - e);
                i.currentTime = t, r.currentTime = t, r.progress = t / r.duration * 100, g(), n()
            },
            forward: (e = 15) => {
                if (!i || !r.duration || !Number.isFinite(r.duration)) return;
                const t = Math.min(r.duration, i.currentTime + e);
                i.currentTime = t, r.currentTime = t, r.progress = t / r.duration * 100, g(), n()
            },
            formatTime: e => {
                if (!e || isNaN(e)) return "0:00";
                return `${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`
            },
            isCurrentPodcast: e => {
                var t;
                return (null == (t = r.currentPodcast) ? void 0 : t.id) === e
            },
            cleanup: v,
            addListener: e => (s.add(e), () => s.delete(e)),
            initPlaylist: l,
            addToPlaylist: d,
            removeFromPlaylist: e => {
                const t = r.playlist.findIndex((t => t.id === e)); - 1 !== t && (r.playlist.splice(t, 1), t < r.currentIndex ? r.currentIndex-- : t === r.currentIndex && (0 === r.playlist.length ? (r.currentIndex = -1, r.currentPodcast = null, v()) : (r.currentIndex >= r.playlist.length && (r.currentIndex = r.playlist.length - 1), r.currentIndex >= 0 && m(r.playlist[r.currentIndex]))), n())
            },
            playAtIndex: o,
            playNext: u,
            playPrevious: c,
            setRepeatMode: e => {
                r.repeatMode = e, n()
            },
            toggleShuffle: () => {
                r.isShuffled = !r.isShuffled, n()
            },
            clearPlaylist: p,
            getPlaylistIndex: e => r.playlist.findIndex((t => t.id === e)),
            hasNext: () => "all" === r.repeatMode || "one" === r.repeatMode ? r.playlist.length > 0 : r.currentIndex < r.playlist.length - 1,
            hasPrevious: () => "all" === r.repeatMode || "one" === r.repeatMode ? r.playlist.length > 0 : r.currentIndex > 0,
            isPlaying: e((() => r.isPlaying)),
            progress: e((() => r.progress)),
            currentTime: e((() => r.currentTime)),
            duration: e((() => r.duration)),
            currentPodcast: e((() => r.currentPodcast)),
            isLoading: e((() => r.isLoading)),
            isBuffering: e((() => r.isBuffering)),
            hasError: e((() => r.hasError)),
            volume: e((() => r.volume)),
            isMuted: e((() => r.isMuted)),
            playbackSpeed: e((() => r.playbackSpeed)),
            playlist: e((() => r.playlist)),
            currentIndex: e((() => r.currentIndex)),
            isShuffled: e((() => r.isShuffled)),
            repeatMode: e((() => r.repeatMode)),
            updatePlaylist: (e, t = !1) => {
                if (!e || 0 === e.length) return void p();
                if (t || 0 === r.playlist.length) return void l(e, 0, !1);
                const n = new Set(r.playlist.map((e => e.id))),
                    i = new Set(e.map((e => e.id)));
                if (new Set([...n].filter((e => i.has(e)))).size == r.playlist.length) {
                    e.filter((e => !n.has(e.id))).forEach((e => {
                        d(e, !1)
                    }))
                } else h(), l(e, 0, !1)
            }
        }
    };
export {
    l as u
};