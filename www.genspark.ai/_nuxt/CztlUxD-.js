import {
    d as l,
    b as e,
    o
} from "./Cf0SOiw0.js";
const r = {
    width: "32",
    height: "32",
    viewBox: "0 0 32 32",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const n = {
    render: function(n, t) {
        return o(), l("svg", r, t[0] || (t[0] = [e("path", {
            "fill-rule": "evenodd",
            "clip-rule": "evenodd",
            d: "M10.726 20.9458C10.3355 21.3364 9.70234 21.3364 9.31182 20.9458C6.57815 18.2122 6.57815 13.78 9.31182 11.0463C9.70234 10.6558 10.3355 10.6558 10.726 11.0463C11.1166 11.4369 11.1166 12.07 10.726 12.4606C8.77341 14.4132 8.77341 17.579 10.726 19.5316C11.1166 19.9222 11.1166 20.5553 10.726 20.9458Z",
            fill: "currentColor"
        }, null, -1), e("path", {
            "fill-rule": "evenodd",
            "clip-rule": "evenodd",
            d: "M21.3843 10.8003C21.7748 10.4097 22.3902 10.4266 22.7587 10.8379C25.3385 13.7173 25.2138 18.2675 22.4801 21.0012C22.0896 21.3917 21.4743 21.3748 21.1057 20.9635C20.7372 20.5521 20.755 19.9021 21.1455 19.5116C23.0981 17.559 23.1872 14.3088 21.3445 12.2521C20.976 11.8408 20.9938 11.1908 21.3843 10.8003Z",
            fill: "currentColor"
        }, null, -1)]))
    }
};
export {
    n as I
};