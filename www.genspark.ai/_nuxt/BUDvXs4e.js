import {
    _ as e
} from "./e-ES_T8J.js";
import {
    W as t,
    r as a,
    _ as l,
    S as s,
    d as n,
    f as o,
    y as i,
    o as r,
    b as c,
    H as d,
    e as u,
    C as p,
    t as v,
    n as _,
    F as m,
    k as g,
    I as f,
    D as h,
    ax as y,
    c as b,
    v as k,
    w as x,
    q as w,
    p as j,
    cB as C,
    Q as S,
    a as E,
    s as A,
    V as T,
    cg as M,
    h as $,
    x as I,
    L as R,
    R as L,
    d2 as P,
    i as q,
    a9 as O,
    J as B,
    T as U,
    U as F,
    a3 as D,
    l as N,
    ai as z,
    aF as H,
    aa as V,
    N as G,
    A as W,
    ag as K,
    j as J,
    B as Q,
    e2 as Y,
    e3 as Z,
    a4 as X,
    dP as ee,
    av as te,
    ay as ae,
    K as le
} from "./Cf0SOiw0.js";
import {
    S as se,
    P as ne,
    b as oe,
    u as ie,
    a as re,
    A as ce,
    M as de,
    _ as ue,
    c as pe,
    E as ve,
    d as _e,
    e as me
} from "./DWsxX4PV.js";
import {
    A as ge,
    a as fe,
    u as he
} from "./DmzoGJ5T.js";
import {
    _ as ye
} from "./DmWk8H2v.js";
import {
    C as be
} from "./BspXWmOn.js";
import {
    R as ke,
    N as xe
} from "./D0Ouax4K.js";
import {
    u as we,
    S as je
} from "./B0db5Fvl.js";
import {
    r as Ce,
    k as Se,
    s as Ee,
    n as Ae,
    I as Te,
    j as Me,
    M as $e,
    t as Ie,
    h as Re,
    v as Le,
    m as Pe,
    L as qe,
    w as Oe,
    x as Be,
    i as Ue,
    y as Fe,
    z as De,
    B as Ne,
    G as ze,
    H as He,
    J as Ve,
    K as Ge,
    Q as We,
    U as Ke,
    W as Je,
    X as Qe,
    Y as Ye,
    Z as Ze,
    $ as Xe,
    a0 as et,
    a1 as tt,
    a2 as at,
    a3 as lt,
    a4 as st,
    a5 as nt,
    a6 as ot,
    a7 as it,
    a8 as rt,
    a9 as ct,
    aa as dt,
    ab as ut,
    ac as pt,
    ad as vt,
    A as _t
} from "./DC84aLnd.js";
import {
    u as mt,
    d as gt,
    I as ft
} from "./BdlGQsae.js";
import {
    p as ht,
    g as yt
} from "./DT-NG54s.js";
import {
    N as bt
} from "./nuQnue4a.js";
import {
    N as kt
} from "./CW991W2w.js";
import {
    i as xt
} from "./P7LDOl0u.js";
import {
    L as wt
} from "./BUCk-Nnr.js";
import {
    S as jt
} from "./BUs-AQWo.js";
import {
    B as Ct
} from "./CAfqOhBF.js";
import {
    u as St
} from "./D5IxqnO4.js";
import {
    u as Et
} from "./BZiwhdnP.js";
import {
    C as At
} from "./Bm_HbXT2.js";
import {
    U as Tt,
    m as Mt,
    a as $t,
    C as It
} from "./Boc3hm_9.js";
import {
    h as Rt
} from "./CmF_-QGy.js";
import {
    m as Lt,
    e as Pt,
    D as qt
} from "./DZBrT1el.js";
import {
    u as Ot,
    I as Bt,
    _ as Ut,
    N as Ft,
    a as Dt,
    S as Nt
} from "./IgM9N0FT.js";
import {
    A as zt,
    C as Ht
} from "./tBofk-gQ.js";
import {
    _ as Vt
} from "./D6bQc9d9.js";
import {
    P as Gt,
    W as Wt,
    F as Kt
} from "./CIlzw36e.js";
import {
    E as Jt
} from "./D386eQgZ.js";
import {
    T as Qt
} from "./B5SiUF0y.js";
import {
    a as Yt
} from "./CKd5XOy1.js";
import {
    I as Zt
} from "./Dflnlfvw.js";
import {
    G as Xt
} from "./DW6cX6jm.js";
import {
    f as ea
} from "./Bl-gMEVt.js";
import {
    _ as ta
} from "./DKpDUEYb.js";
import {
    u as aa
} from "./gsZIXP6B.js";
import {
    c as la
} from "./Cp7w48vH.js";
import {
    u as sa
} from "./DJ-JsGJu.js";
import {
    u as na
} from "./B6noBY_5.js";
import {
    C as oa
} from "./CRmNre8Y.js";
import {
    M as ia
} from "./C1hkGl0A.js";
import {
    I as ra
} from "./CRDCtKfR.js";
import ca from "./BiRGpvqQ.js";
import {
    D as da
} from "./BThdTUgg.js";
import {
    B as ua
} from "./DAjjhrgi.js";
import {
    N as pa
} from "./DQpEsQQa.js";
import {
    s as va
} from "./CVrRKK4N.js";
import {
    N as _a
} from "./Dp4W8C_b.js";
import {
    M as ma,
    S as ga,
    u as fa
} from "./C1lFdfgL.js";
import {
    l as ha
} from "./CrbPJ6Kt.js";
import {
    _ as ya
} from "./CAmLbDGM.js";
import {
    r as ba
} from "./BXNOMSAZ.js";
import {
    R as ka
} from "./Cu_n4xpI.js";
import "./C1MFiWVQ.js";
import {
    d as xa
} from "./DOnko34f.js";
const wa = t("slideEditText", (() => {
        const e = a("");
        return {
            textContent: e,
            setTextContent: t => {
                e.value = t
            }
        }
    })),
    ja = {
        key: 0
    },
    Ca = {
        class: "spark_result"
    },
    Sa = {
        class: "markdown-viewer note_content"
    },
    Ea = {
        key: 0,
        class: "spark_result_card"
    },
    Aa = {
        class: "logo"
    },
    Ta = ["src"],
    Ma = {
        class: "markdown-viewer content"
    },
    $a = ["onClick"],
    Ia = ["src"],
    Ra = {
        key: 1,
        class: "gallery_load_wrapper"
    },
    La = ["src", "onLoad"],
    Pa = {
        class: "text"
    },
    qa = {
        class: "icon"
    },
    Oa = {
        key: 0,
        class: "gallery_load_wrapper"
    },
    Ba = ["src", "onLoad"],
    Ua = l({
        __name: "spark_result",
        props: {
            message: Object
        },
        setup(e) {
            const t = e,
                {
                    message: l
                } = s(t),
                h = a([]),
                y = e => "youtube" === e.type ? p.convertToYoutubeImageUrl(e.link) : e.url;
            return (e, t) => {
                var a, s, b, k, x, w, j, C, S, E, A, T, M, $, I, R, L, P, q, O;
                return (null == (a = i(l).session_state) ? void 0 : a.spark_result) ? (r(), n("div", ja, [c("div", Ca, [c("div", Sa, [u(ye, {
                    source: null == (b = null == (s = i(l).session_state) ? void 0 : s.spark_result) ? void 0 : b.note_content
                }, null, 8, ["source"])]), (null == (k = i(l).session_state) ? void 0 : k.spark_result) ? (r(), n("div", Ea, [c("div", Aa, [c("img", {
                    src: i(se)
                }, null, 8, Ta)]), c("div", {
                    class: "title",
                    onClick: t[0] || (t[0] = e => {
                        var t, a;
                        return i(p).windowopen(null == (a = null == (t = i(l).session_state) ? void 0 : t.spark_result) ? void 0 : a.url)
                    })
                }, v(null == (w = null == (x = i(l).session_state) ? void 0 : x.spark_result) ? void 0 : w.title), 1), c("div", Ma, [u(ye, {
                    source: null == (C = null == (j = i(l).session_state) ? void 0 : j.spark_result) ? void 0 : C.content
                }, null, 8, ["source"])]), (null == (E = null == (S = i(l).session_state) ? void 0 : S.spark_result) ? void 0 : E.gallery) && i(h).length >= 2 && i(h).length <= 4 ? (r(), n("div", {
                    key: 0,
                    class: _(["gallery", "gallery_" + i(h).length])
                }, [(r(!0), n(m, null, g(i(h), ((e, t) => (r(), n("div", {
                    class: _(["gallery__item", e.type]),
                    key: t
                }, [c("div", {
                    class: "image",
                    onClick: t => i(p).windowopen(e.link)
                }, [c("img", {
                    src: y(e)
                }, null, 8, Ia)], 8, $a)], 2)))), 128))], 2)) : o("", !0), (null == (T = null == (A = i(l).session_state) ? void 0 : A.spark_result) ? void 0 : T.gallery) ? d((r(), n("div", Ra, [(r(!0), n(m, null, g(null == ($ = null == (M = i(l).session_state) ? void 0 : M.spark_result) ? void 0 : $.gallery, ((e, a) => (r(), n("img", {
                    src: y(e),
                    onLoad: t => {
                        i(h).length < 4 && i(h).push(e)
                    },
                    onError: t[1] || (t[1] = e => {})
                }, null, 40, La)))), 256))], 512)), [
                    [f, !1]
                ]) : o("", !0), (null == (R = null == (I = i(l).session_state) ? void 0 : I.spark_result) ? void 0 : R.show_see_more) ? (r(), n("div", {
                    key: 2,
                    class: "see-more",
                    onClick: t[2] || (t[2] = e => {
                        var t, a;
                        return i(p).windowopen(null == (a = null == (t = i(l).session_state) ? void 0 : t.spark_result) ? void 0 : a.url)
                    })
                }, [c("div", Pa, v(e.$t("components.cross_check.see-more")), 1), c("div", qa, [u(i(ge))])])) : o("", !0)])) : o("", !0)]), (null == (P = null == (L = i(l).session_state) ? void 0 : L.spark_result) ? void 0 : P.gallery) ? d((r(), n("div", Oa, [(r(!0), n(m, null, g(null == (O = null == (q = i(l).session_state) ? void 0 : q.spark_result) ? void 0 : O.gallery, ((e, a) => (r(), n("img", {
                    src: y(e),
                    onLoad: t => {
                        i(h).length < 4 && i(h).push(e)
                    },
                    onError: t[3] || (t[3] = e => {})
                }, null, 40, Ba)))), 256))], 512)), [
                    [f, !1]
                ]) : o("", !0)])) : o("", !0)
            }
        }
    }, [
        ["__scopeId", "data-v-4ae48e24"]
    ]),
    Fa = e => {
        const t = document.querySelectorAll("iframe");
        return Array.from(t).find((t => {
            var a;
            return t.getAttribute("src") === (null == (a = null == e ? void 0 : e.payload) ? void 0 : a.iframeId)
        }))
    };
const Da = {
        class: "pt-0 slide-style-modify-model"
    },
    Na = {
        key: 0,
        class: "px-3 mb-[-22px]"
    },
    za = {
        class: "rounded-tl-xl rounded-tr-xl border border-[#DEC6C5]"
    },
    Ha = {
        key: 0,
        class: "flex flex-col gap-[10px] bg-[#FFFBFB] pt-[16px] pb-[24px] dark:bg-[#333]"
    },
    Va = {
        class: "text-black text-sm font-normal font-['Arial'] leading-[1.5em] dark:text-white"
    },
    Ga = {
        key: 1,
        class: "relative flex flex-col p-3 bg-[#F3F9FF] rounded-xl border border-[#0F7FFF] text-[#000] dark:bg-[#333] dark:border-[#333]"
    },
    Wa = {
        key: 0,
        class: "flex justify-between items-center"
    },
    Ka = {
        class: _(["inline-flex items-center gap-1 pr-[6px] pl-[12px] py-[3px] rounded-[6px] dark:text-white"])
    },
    Ja = {
        key: 0,
        class: "text-neutral-800 text-sm font-bold leading-none dark:text-white"
    },
    Qa = {
        key: 1,
        class: "text-neutral-800 text-sm font-bold leading-none dark:text-white"
    },
    Ya = {
        class: "text-neutral-800 text-sm font-bold leading-none dark:text-white"
    },
    Za = {
        class: "flex items-center gap-[12px]"
    },
    Xa = {
        class: "justify-start text-[#0F7FFF] font-bold text-xs font-['Helvetica_Neue'] leading-none"
    },
    el = {
        key: 0,
        class: "flex flex-col rounded-xl items-center justify-center text-center text-neutral-800 text-[14px] bg-[#F3F9FF]"
    },
    tl = {
        class: "py-3"
    },
    al = {
        key: 0,
        class: "w-full px-3 pb-3"
    },
    ll = ["src"],
    sl = l({
        __name: "SlideStyleModifyModel",
        props: {
            metaData: {
                type: Object,
                default: {},
                required: !1
            }
        },
        setup(e) {
            const t = e,
                l = a(""),
                s = a(!1),
                d = a(!1),
                p = a(!1),
                f = a(!1),
                h = mt(),
                b = we(),
                x = wa(),
                {
                    PLACEHOLDER_MAP: w
                } = b,
                {
                    editSlideStatus: E,
                    curEventData: R,
                    saveStatus: L
                } = y(h),
                {
                    textContent: O
                } = y(x),
                B = a(null),
                U = gt(),
                {
                    fileContentsUpdate: F
                } = h,
                {
                    domBase64: D
                } = y(U),
                {
                    t: N
                } = A();
            T(), q("currentUser"), a(!1), k(E, (e => {
                "normal" === e && Q();
                const t = document.querySelectorAll("iframe");
                for (let a of t) ht(a, {
                    type: S.SlideEditMode,
                    payload: {
                        isCanEditSlide: "edit" === e
                    }
                })
            })), M((() => {
                H()
            }));
            const z = a([{
                    id: 1,
                    name: N("components.slides.say_a_topic_get_complete_professional_slides")
                }, {
                    id: 2,
                    name: N("components.slides.auto_research_and_compile_findings_into_slides")
                }, {
                    id: 3,
                    name: N("components.slides.add_images_videos_sounds_using_ai_or_from_web")
                }, {
                    id: 4,
                    name: N("components.slides.change_styles_add_charts_and_visuals_with_ease")
                }, {
                    id: 5,
                    name: N("components.slides.import_any_document_and_convert_to_ai_slides")
                }, {
                    id: 6,
                    name: N("components.slides.activate_microphone_for_voice_command_control")
                }]),
                H = () => {
                    B.value && (B.value.focus(), setTimeout((() => {
                        B.value.style.height = "44px", B.value.style.height = B.value.scrollHeight + "px"
                    }), 1))
                },
                V = () => {
                    h.setSaveStatus(""), l.value = "", s.value = !1, x.setTextContent(""), d.value = !1, p.value = !1, b.setPlaceholder(w.ENTER_YOUR_SLIDES), h.setIsDisableInputPrompt(!1), f.value = !1
                },
                G = e => {
                    if (K(e), !P.PostMessageOrigins.includes(e.origin) || e.data.type !== S.SlideElementClicked) return;
                    ("container" !== e.data.payload.elementType && "img" !== e.data.payload.elementType && "text" !== e.data.payload.elementType || !e.data.payload.base64) && V();
                    const t = () => {
                        var t, a, l, s, n, o;
                        if (b.setPlaceholder(w.ASK_GENSPARK_TO_EDIT), (null == (a = null == (t = null == e ? void 0 : e.data) ? void 0 : t.payload) ? void 0 : a.domPath) && (p.value = !0, U.setDomPath(e.data.payload.domPath)), (null == (s = null == (l = null == e ? void 0 : e.data) ? void 0 : l.payload) ? void 0 : s.base64) && U.setDomBase64(e.data.payload.base64), null == (o = null == (n = null == e ? void 0 : e.data) ? void 0 : n.payload) ? void 0 : o.iframeId) {
                            const t = new URL(e.data.payload.iframeId).searchParams.get("pageIndex");
                            U.setPageNumber(+t)
                        }
                    };
                    "img" == e.data.payload.elementType && (t(), h.setIsDisableInputPrompt(!1)), "text" == e.data.payload.elementType && (t(), h.setIsDisableInputPrompt(!1)), "container" == e.data.payload.elementType && t(), h.setCurEventData(e.data), J()
                },
                W = () => {
                    f.value = !f.value
                },
                K = e => {
                    if (!P.PostMessageOrigins.includes(e.origin) || e.data.type !== S.SlideUpdateHtml) return;
                    let a = e.data.payload.content;
                    const l = e.data.payload.iframeId,
                        s = new URL(l),
                        n = s.origin + s.pathname;
                    F.set(n, a), h.handleSaveSlides(t.metaData)
                },
                J = () => {
                    const e = (() => {
                        const e = document.querySelectorAll("iframe");
                        return Array.from(e).filter((e => {
                            var t, a;
                            return e.getAttribute("src") !== (null == (a = null == (t = null == R ? void 0 : R.value) ? void 0 : t.payload) ? void 0 : a.iframeId)
                        }))
                    })();
                    for (let t of e) ht(t, {
                        type: S.SlideCancelEdit,
                        payload: C(R.value).payload
                    })
                },
                Q = () => {
                    V(), h.setEditSlideStatus("normal"), U.setDomPath(""), U.setDomBase64("");
                    const e = Fa(C(R.value));
                    e && ht(e, {
                        type: S.SlideCancelImageClicked,
                        payload: C(R.value).payload
                    })
                },
                Y = e => {
                    if ((e.ctrlKey || e.metaKey) && "z" === e.key) {
                        const e = Fa(C(R.value));
                        ht(e, {
                            type: S.SlideUndo
                        }), h.setIsShowPopover(!1)
                    }
                };
            return $((() => {
                b.setPlaceholder(w.ENTER_YOUR_SLIDES), window.addEventListener("message", G), window.addEventListener("keydown", Y)
            })), I((() => {
                window.removeEventListener("message", G), window.removeEventListener("keydown", Y)
            })), (e, t) => (r(), n("div", Da, ["normal" === i(E) ? (r(), n("div", Na, [c("div", za, [c("div", {
                class: _(["flex items-center justify-between bg-[#F6ECEC] px-[12px] h-[36px] rounded-tl-xl rounded-tr-xl text-pink-950 text-sm font-bold  leading-tight pb-[7px] cursor-pointer"]),
                onClick: W
            }, [c("div", null, v(e.$t("components.slides.ai_slides_mode")), 1), c("div", {
                class: _(["cursor-pointer w-[20px] h-[20px] flex items-center justify-center bg-[#EBE1E1] rounded-[30px] transition-all duration-300", f.value ? "rotate-0" : "rotate-180"])
            }, [u(i(fe))], 2)]), f.value ? (r(), n("div", Ha, [(r(!0), n(m, null, g(z.value, (e => (r(), n("div", {
                class: "flex items-center gap-[6px] justify-start pl-[14px]",
                key: e.id
            }, [u(i(ke), {
                class: "w-[18px] h-[18px] text-[#5CD4A1]"
            }), c("div", Va, v(e.name), 1)])))), 128))])) : o("", !0)])])) : o("", !0), "edit" == i(E) ? (r(), n("div", Ga, [d.value || s.value || i(O) || l.value ? (r(), n("div", Wa, [c("div", Ka, [d.value || i(O) ? (r(), n("div", Ja, v(e.$t("components.slides.edit_text")), 1)) : l.value ? (r(), n("div", Qa, v(e.$t("components.slides.edit_image_url")), 1)) : (r(), n(m, {
                key: 2
            }, [u(i(Ce)), c("div", Ya, v(e.$t("components.slides.select_to_edit")), 1)], 64))]), c("div", Za, [c("div", Xa, ["saving" === i(L) ? (r(), n(m, {
                key: 0
            }, [j(v(e.$t("components.slides.saving")), 1)], 64)) : "saved" === i(L) ? (r(), n(m, {
                key: 1
            }, [j(v(e.$t("components.slides.saved")), 1)], 64)) : "error" === i(L) ? (r(), n(m, {
                key: 2
            }, [j(v(e.$t("components.slides.failed_to_save")), 1)], 64)) : o("", !0)]), c("div", {
                class: "flex items-center p-[4px] bg-gray-200 rounded-full cursor-pointer dark:invert",
                onClick: Q
            }, [u(i(be), {
                class: "w-[12px] h-[12px] text-[#262626]"
            })])])])) : (r(), n("div", {
                key: 1,
                class: "absolute top-[5px] right-[5px] flex items-center p-[4px] bg-gray-200 rounded-full cursor-pointer dark:invert",
                onClick: Q
            }, [u(i(be), {
                class: "w-[12px] h-[12px] text-[#262626]"
            })])), "edit" == i(E) ? (r(), n(m, {
                key: 2
            }, [(p.value, r(), n("div", el, [c("div", tl, ["saving" === i(L) ? (r(), n(m, {
                key: 0
            }, [j(v(e.$t("components.slides.saving")), 1)], 64)) : "saved" === i(L) ? (r(), n(m, {
                key: 1
            }, [j(v(e.$t("components.slides.saved")), 1)], 64)) : "error" === i(L) ? (r(), n(m, {
                key: 2
            }, [j(v(e.$t("components.slides.failed_to_save")), 1)], 64)) : i(D) && i(D).trim() ? (r(), n(m, {
                key: 3
            }, [j(v(e.$t("components.slides.describe_your_desired_changes")), 1)], 64)) : (r(), n(m, {
                key: 4
            }, [j(v(e.$t("components.slides.ask_genspark_to_edit_selected_item")), 1)], 64))]), i(D) && i(D).trim() ? (r(), n("div", al, [c("img", {
                src: i(D),
                alt: "Selected element",
                class: "max-w-full h-auto max-h-[200px] rounded-lg border border-gray-200 shadow-sm object-contain mx-auto"
            }, null, 8, ll)])) : o("", !0)]))], 64)) : o("", !0)])) : o("", !0)]))
        }
    }, [
        ["__scopeId", "data-v-e2af6a1e"]
    ]),
    nl = {
        class: "flex items-center justify-center w-[74px] h-[50px] bg-[#F5F5F5] rounded-lg dark:bg-[#333] flex-shrink-0"
    },
    ol = {
        class: "flex-1 min-w-0"
    },
    il = {
        class: "text-neutral-800 text-sm font-bold leading-tight dark:text-white whitespace-nowrap overflow-hidden text-ellipsis"
    },
    rl = {
        class: "mt-[6px] text-neutral-400 text-[10px]"
    },
    cl = l({
        __name: "SlideMobileCanvasBar",
        props: {
            metaData: {
                type: Object,
                required: !0
            },
            isAsking: {
                type: Boolean,
                required: !1
            }
        },
        setup(e) {
            const t = gt(),
                a = () => {
                    t.setSlideCanvasVisible(!0)
                };
            return (t, l) => {
                var s;
                return r(), n("div", {
                    class: "flex items-center gap-[10px] p-[8px] mb-[16px] shadow-[0px_2px_4px_0px_rgba(0,0,0,0.05)] border border-gray-200 rounded-lg dark:border-[#e6e9eb40]",
                    onClick: a
                }, [c("div", nl, [e.isAsking ? (r(), E(i(Ct), {
                    key: 0,
                    class: "black-loading-animation"
                })) : (r(), E(i(jt), {
                    key: 1
                }))]), c("div", ol, [c("div", il, v((null == (s = e.metaData) ? void 0 : s.file_name) || t.$t("components.slides.untitled-slides")), 1), c("div", rl, v(e.isAsking ? t.$t("components.slides.generating") : t.$t("components.slides.click-to-open")), 1)])])
            }
        }
    }, [
        ["__scopeId", "data-v-068ece1c"]
    ]),
    dl = {
        key: 0,
        class: "html-canvas-bar"
    },
    ul = {
        class: "bar-content"
    },
    pl = {
        class: "first-row"
    },
    vl = {
        class: "selection-types"
    },
    _l = {
        key: 0,
        class: "selection-type"
    },
    ml = {
        class: "type-text"
    },
    gl = {
        key: 1,
        class: "selection-type"
    },
    fl = {
        key: 2,
        class: "selection-type"
    },
    hl = {
        class: "content-row"
    },
    yl = {
        key: 0,
        class: "image-edit-content"
    },
    bl = ["title"],
    kl = {
        class: "image-url-section"
    },
    xl = ["placeholder"],
    wl = {
        key: 1,
        class: "selection-content",
        style: {
            color: "#606366",
            display: "flex",
            "align-items": "center",
            "justify-content": "center",
            padding: "25px 0"
        }
    },
    jl = {
        key: 2,
        class: "selection-content"
    },
    Cl = {
        key: 3,
        class: "selection-content"
    },
    Sl = l({
        __name: "HtmlCanvasBar",
        setup(e) {
            const t = St(),
                l = Et(),
                s = we(),
                {
                    t: p
                } = A(),
                _ = a(null),
                m = a(""),
                g = b((() => {
                    var e, a;
                    return "img" === (null == (a = null == (e = t.selectedElement) ? void 0 : e.tagName) ? void 0 : a.toLowerCase())
                })),
                f = b((() => t.hasElementSelection || t.hasTextSelection || t.hasVditorSelection));
            k((() => t.selectedElement), (e => {
                var t;
                e && "img" === (null == (t = e.tagName) ? void 0 : t.toLowerCase()) ? m.value = e.src || "" : m.value = ""
            }), {
                immediate: !0
            }), k((() => f.value), (e => {
                e ? g.value ? s.setPlaceholder(p("components.html_canvas_bar.ask_genspark_modify_image")) : s.setPlaceholder(p("components.html_canvas_bar.ask_genspark_to_improve")) : s.setPlaceholder(p("components.html_canvas_bar.enter_doc_request"))
            }), {
                immediate: !0
            });
            const h = () => {
                    if (g.value && t.selectedElement) {
                        const e = t.selectedElement,
                            a = e.src,
                            s = m.value;
                        l.requestImageOperation({
                            element: e,
                            oldSrc: a,
                            newSrc: s,
                            type: "url-change",
                            description: p("components.html_canvas_bar.change_image_url")
                        })
                    }
                },
                y = () => {
                    if (g.value && t.selectedElement && m.value.trim()) {
                        const e = t.selectedElement,
                            a = e.src,
                            s = m.value.trim();
                        l.requestImageOperation({
                            element: e,
                            oldSrc: a,
                            newSrc: s,
                            type: "url-apply",
                            description: p("components.html_canvas_bar.apply_image_url")
                        })
                    }
                },
                x = () => {
                    Rt((e => {
                        if (g.value && t.selectedElement) {
                            const a = t.selectedElement,
                                s = a.src;
                            l.requestImageOperation({
                                element: a,
                                oldSrc: s,
                                newSrc: e,
                                type: "upload",
                                description: p("components.html_canvas_bar.upload_image")
                            }), m.value = e
                        }
                    }))
                };
            b((() => {
                var e;
                if (!t.selectedElement) return "";
                const a = t.selectedElement,
                    l = (null == (e = a.tagName) ? void 0 : e.toLowerCase()) || "",
                    s = a.id ? `#${a.id}` : "",
                    n = a.className ? `.${a.className.split(" ").join(".")}` : "";
                let o = l;
                s ? o += s : n && (o += n);
                let i = a.textContent || a.innerText || "";
                return i = i.trim().replace(/\s+/g, " "), i && (o += `: "${i}"`), o || "Unknown element"
            }));
            const w = b((() => {
                    var e;
                    if (!(null == (e = t.textSelection) ? void 0 : e.text)) return "";
                    let a = t.textSelection.text.trim();
                    return a = a.replace(/\s+/g, " "), a
                })),
                j = b((() => {
                    var e;
                    if (!(null == (e = t.vditorSelection) ? void 0 : e.text)) return "";
                    let a = t.vditorSelection.text.trim();
                    return a = a.replace(/\s+/g, " "), a
                })),
                C = () => {
                    t.clearAllSelections("html-canvas-bar")
                };
            return (e, a) => f.value ? (r(), n("div", dl, [c("div", ul, [c("div", pl, [c("div", vl, [i(t).hasElementSelection ? (r(), n("div", _l, [c("span", ml, v(g.value ? i(p)("components.html_canvas_bar.edit_image") : i(p)("components.html_canvas_bar.select_to_edit")), 1)])) : o("", !0), i(t).hasTextSelection ? (r(), n("div", gl, a[1] || (a[1] = [c("span", {
                class: "type-text"
            }, "AI Rewrite", -1)]))) : o("", !0), i(t).hasVditorSelection ? (r(), n("div", fl, a[2] || (a[2] = [c("span", {
                class: "type-text"
            }, "AI Rewrite", -1)]))) : o("", !0)]), c("button", {
                onClick: C,
                class: "close-button",
                title: "Clear Selection"
            }, [u(i(At))])]), c("div", hl, [g.value ? (r(), n("div", yl, [c("div", {
                class: "upload-section",
                onClick: x,
                title: i(p)("components.html_canvas_bar.click_to_upload_image")
            }, [u(i(Tt)), c("span", null, v(i(p)("components.html_canvas_bar.click_to_upload_image")), 1)], 8, bl), c("div", kl, [d(c("input", {
                ref_key: "imageUrlInputRef",
                ref: _,
                "onUpdate:modelValue": a[0] || (a[0] = e => m.value = e),
                type: "text",
                class: "image-url-input",
                placeholder: i(p)("components.html_canvas_bar.enter_image_url"),
                onInput: h,
                onKeyup: O(y, ["enter"])
            }, null, 40, xl), [
                [R, m.value]
            ])])])) : i(t).hasElementSelection ? (r(), n("div", wl, v(i(p)("components.html_canvas_bar.select_to_edit_or_ask_genspark_to_modify")), 1)) : o("", !0), i(t).hasTextSelection ? (r(), n("div", jl, v(w.value), 1)) : o("", !0), i(t).hasVditorSelection ? (r(), n("div", Cl, v(j.value), 1)) : o("", !0)])])])) : o("", !0)
        }
    }, [
        ["__scopeId", "data-v-bc65088d"]
    ]),
    El = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "12",
        height: "13",
        viewBox: "0 0 12 13",
        fill: "none"
    };
const Al = {
        render: function(e, t) {
            return r(), n("svg", El, t[0] || (t[0] = [c("path", {
                d: "M3 6.81818L5 9L9 5",
                stroke: "currentColor",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    Tl = {
        key: 0,
        class: "editor-switch-bar"
    },
    Ml = {
        class: "bar-content"
    },
    $l = {
        class: "switch-content"
    },
    Il = {
        class: "editor-info"
    },
    Rl = {
        class: "editor-type"
    },
    Ll = {
        class: "type-text"
    },
    Pl = {
        class: "editor-options"
    },
    ql = {
        class: "option-group"
    },
    Ol = ["onClick"],
    Bl = {
        class: "option-label"
    },
    Ul = ["title"],
    Fl = {
        key: 0,
        class: "format-description"
    },
    Dl = {
        class: "description-content"
    },
    Nl = {
        class: "description-list"
    },
    zl = {
        class: "description-text"
    },
    Hl = l({
        __name: "EditorSwitchBar",
        props: {
            project: {
                type: Object,
                required: !0
            }
        },
        emits: ["update:editorType"],
        setup(e, {
            emit: t
        }) {
            const l = e,
                s = t,
                d = St(),
                p = a(!1),
                {
                    t: f
                } = A(),
                h = [{
                    value: "html",
                    label: "Rich Text",
                    icon: "📝",
                    description: [f("components.editor_switch_bar.description1"), f("components.editor_switch_bar.description2"), f("components.editor_switch_bar.description3")]
                }, {
                    value: "markdown",
                    label: "Markdown",
                    icon: "Md",
                    description: [f("components.editor_switch_bar.description1"), f("components.editor_switch_bar.description2"), f("components.editor_switch_bar.description3")]
                }],
                y = b((() => d.userSelectEditorType)),
                x = b((() => h.find((e => e.value === y.value)) || h[0])),
                w = b((() => {
                    var e;
                    return !(d.hasElementSelection || d.hasTextSelection || d.hasVditorSelection) && "docs_agent" === (null == (e = l.project) ? void 0 : e.type)
                })),
                j = () => {
                    p.value = !p.value
                };
            return k((() => {
                var e;
                return null == (e = l.project) ? void 0 : e.id
            }), (() => {
                p.value = !1
            })), (e, t) => w.value ? (r(), n("div", Tl, [c("div", Ml, [c("div", $l, [c("div", Il, [c("div", Rl, [c("span", Ll, v(i(f)("pages.agents.ai_docs")), 1)]), c("div", Pl, [c("div", ql, [(r(), n(m, null, g(h, (e => c("button", {
                key: e.value,
                onClick: t => {
                    return a = e.value, d.setUserSelectEditorType(a), void s("update:editorType", a);
                    var a
                },
                class: _(["option-button", {
                    active: y.value === e.value
                }])
            }, [y.value === e.value ? (r(), E(i(Al), {
                key: 0,
                class: "check-icon"
            })) : o("", !0), c("span", Bl, v(e.label), 1)], 10, Ol))), 64))])])])]), c("div", {
                onClick: j,
                class: _(["toggle-button", {
                    rotated: p.value
                }]),
                title: p.value ? "Hide Format Description" : "Show Format Description"
            }, t[0] || (t[0] = [c("svg", {
                width: "10",
                height: "11",
                viewBox: "0 0 10 11",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg"
            }, [c("path", {
                d: "M1.25 4.03516L4.78553 7.56882L8.32107 4.03516",
                stroke: "#666666",
                "stroke-width": "0.833023",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            })], -1)]), 10, Ul)]), p.value ? (r(), n("div", Fl, [c("div", Dl, [o("", !0), c("div", Nl, [(r(!0), n(m, null, g(x.value.description, ((e, t) => (r(), n("div", {
                key: t,
                class: "description-item"
            }, [u(i(ke), {
                class: "description-icon"
            }), c("div", zl, v(e), 1)])))), 128))])])])) : o("", !0)])) : o("", !0)
        }
    }, [
        ["__scopeId", "data-v-9d8a3f95"]
    ]),
    Vl = ["onClick"],
    Gl = {
        class: "row"
    },
    Wl = {
        class: "left"
    },
    Kl = {
        key: 0,
        class: "absolute -top-4 left-2 px-2 py-0.5 bg-red-500 rounded-tl-lg rounded-tr-lg rounded-bl-sm rounded-br-lg inline-flex justify-center items-center gap-2.5 overflow-hidden text-white text-xs font-bold"
    },
    Jl = {
        class: "icon"
    },
    Ql = ["src"],
    Yl = {
        class: "text"
    },
    Zl = {
        class: "model-name"
    },
    Xl = {
        key: 0,
        class: "model-tags"
    },
    es = {
        class: "right"
    },
    ts = ["checked"],
    as = {
        key: 0,
        class: "description"
    },
    ls = {
        key: 1,
        class: "model-requirement-warning"
    },
    ss = {
        class: "requirement-label"
    },
    ns = {
        key: 2,
        class: "features"
    },
    os = {
        class: "feature-label"
    },
    is = {
        class: "feature-tags"
    },
    rs = {
        key: 0,
        class: "divider"
    },
    cs = l({
        __name: "ImageSelectModelNew",
        props: {
            promptImages: {
                type: Array,
                default: []
            },
            toggleModel: {
                type: Function,
                default: () => {}
            },
            modelsSelected: {
                type: String,
                default: ""
            },
            enableModelCheck: {
                type: Boolean,
                default: !0
            }
        },
        setup(e) {
            const t = e,
                {
                    t: a
                } = A(),
                l = q("currentUser"),
                s = b((() => (p.log("currentUser:", l.value), !p.isGensparkApp() && !(!l.value || !l.value.gk_dogfood)))),
                d = b((() => Mt.filter((e => {
                    const a = !t.enableModelCheck || !e.is_i2i || e.is_i2i && t.promptImages && t.promptImages.length > 0;
                    return "gemini-flash-2.0" === e.name ? s.value && a : a
                })))),
                u = e => e.description ? e.description(a) : "";
            return (t, s) => (r(), n("div", null, [(r(!0), n(m, null, g(i(l) && i(l).gk_dogfood ? d.value.filter((e => !e.deprecated)) : d.value.filter((e => !e.hidden)), (l => {
                var i, d, p, f, h;
                return r(), n("div", {
                    key: l.name,
                    class: "model-container"
                }, [c("div", {
                    class: "model",
                    onClick: t => e.toggleModel(l.name)
                }, [c("div", Gl, [c("div", Wl, [l.new ? (r(), n("div", Kl, " New ")) : o("", !0), c("div", Jl, [c("img", {
                    src: l.icon
                }, null, 8, Ql)]), c("div", Yl, [c("div", Zl, v((h = l, "function" == typeof h.label ? h.label(a) : h.label)), 1), l.price_level || l.speed || l.quality ? (r(), n("div", Xl, [l.price_level ? (r(), n("span", {
                    key: 0,
                    class: _(["price-tag", `price-${l.price_level}`])
                }, v((f = l.price_level, {
                    budget: a("components.generate_image_content.price_budget"),
                    moderate: a("components.generate_image_content.price_moderate"),
                    premium: a("components.generate_image_content.price_premium"),
                    expensive: a("components.generate_image_content.price_expensive")
                }[f] || f)), 3)) : o("", !0), l.speed ? (r(), n("span", {
                    key: 1,
                    class: _(["speed-tag", `speed-${null==(i=l.speed)?void 0:i.replace("_","-")}`])
                }, v((p = l.speed, {
                    very_fast: a("components.generate_image_content.speed_very_fast"),
                    fast: a("components.generate_image_content.speed_fast"),
                    medium: a("components.generate_image_content.speed_medium"),
                    slow: a("components.generate_image_content.speed_slow")
                }[null == p ? void 0 : p.replace("-", "_")] || p)), 3)) : o("", !0), l.quality ? (r(), n("span", {
                    key: 2,
                    class: _(["quality-tag", `quality-${l.quality}`])
                }, v((d = l.quality, {
                    excellent: a("components.generate_image_content.quality_excellent"),
                    high: a("components.generate_image_content.quality_high"),
                    medium: a("components.generate_image_content.quality_medium")
                }[d] || d)), 3)) : o("", !0)])) : o("", !0)])]), c("div", es, [c("input", {
                    type: "radio",
                    name: "model",
                    checked: e.modelsSelected === l.name
                }, null, 8, ts)])]), l.description ? (r(), n("div", as, v(u(l)), 1)) : o("", !0), l.is_i2i ? (r(), n("div", ls, [c("div", ss, [s[0] || (s[0] = c("span", {
                    class: "requirement-icon"
                }, "⚠️", -1)), j(" " + v(t.$t("components.generate_image_content.i2i_requirement")), 1)])])) : o("", !0), l.best_for && l.best_for.length > 0 ? (r(), n("div", ns, [c("div", os, v(t.$t("components.generate_image_content.best_for")) + ": ", 1), c("div", is, [(r(!0), n(m, null, g(l.best_for.slice(0, 3), (e => (r(), n("span", {
                    key: e,
                    class: "feature-tag"
                }, v((e => ({
                    image_editing: a("components.generate_image_content.feature_image_editing"),
                    complex_scenes: a("components.generate_image_content.feature_complex_scenes"),
                    images_with_text: a("components.generate_image_content.feature_images_with_text"),
                    quick_generation: a("components.generate_image_content.feature_quick_generation"),
                    artistic_styles: a("components.generate_image_content.feature_artistic_styles"),
                    creative_images: a("components.generate_image_content.feature_creative_images"),
                    reference_editing: a("components.generate_image_content.feature_reference_editing"),
                    style_transfer: a("components.generate_image_content.feature_style_transfer"),
                    quick_modifications: a("components.generate_image_content.feature_quick_modifications"),
                    high_quality_editing: a("components.generate_image_content.feature_high_quality_editing"),
                    professional_work: a("components.generate_image_content.feature_professional_work"),
                    detailed_control: a("components.generate_image_content.feature_detailed_control"),
                    latest_experience: a("components.generate_image_content.feature_latest_experience"),
                    realistic_images: a("components.generate_image_content.feature_realistic_images"),
                    high_quality_content: a("components.generate_image_content.feature_high_quality_content"),
                    photorealistic_style: a("components.generate_image_content.feature_photorealistic_style"),
                    safe_content: a("components.generate_image_content.feature_safe_content"),
                    natural_images: a("components.generate_image_content.feature_natural_images"),
                    realistic_photos: a("components.generate_image_content.feature_realistic_photos"),
                    commercial_images: a("components.generate_image_content.feature_commercial_images"),
                    product_images: a("components.generate_image_content.feature_product_images"),
                    high_resolution: a("components.generate_image_content.feature_high_resolution"),
                    chinese_content: a("components.generate_image_content.feature_chinese_content"),
                    general_purpose: a("components.generate_image_content.feature_general_purpose"),
                    reliable_results: a("components.generate_image_content.feature_reliable_results"),
                    everyday_use: a("components.generate_image_content.feature_everyday_use"),
                    quick_prototyping: a("components.generate_image_content.feature_quick_prototyping"),
                    fast_iterations: a("components.generate_image_content.feature_fast_iterations"),
                    speed_priority: a("components.generate_image_content.feature_speed_priority"),
                    logos: a("components.generate_image_content.feature_logos"),
                    text_in_images: a("components.generate_image_content.feature_text_in_images"),
                    creative_design: a("components.generate_image_content.feature_creative_design"),
                    text_heavy_images: a("components.generate_image_content.feature_text_heavy_images"),
                    improved_results: a("components.generate_image_content.feature_improved_results"),
                    design_work: a("components.generate_image_content.feature_design_work"),
                    artistic_content: a("components.generate_image_content.feature_artistic_content"),
                    unique_concepts: a("components.generate_image_content.feature_unique_concepts"),
                    experimental_features: a("components.generate_image_content.feature_experimental_features"),
                    quick_results: a("components.generate_image_content.feature_quick_results")
                }[e] || e))(e)), 1)))), 128))])])) : o("", !0)], 8, Vl), l.divider ? (r(), n("div", rs)) : o("", !0)])
            })), 128))]))
        }
    }, [
        ["__scopeId", "data-v-b1c443cb"]
    ]),
    ds = ["onClick"],
    us = {
        class: "row"
    },
    ps = {
        class: "left"
    },
    vs = {
        key: 0,
        class: "absolute -top-4 left-2 px-2 py-0.5 bg-red-500 rounded-tl-lg rounded-tr-lg rounded-bl-sm rounded-br-lg inline-flex justify-center items-center gap-2.5 overflow-hidden text-white text-xs font-bold"
    },
    _s = {
        class: "icon"
    },
    ms = ["src"],
    gs = {
        class: "text"
    },
    fs = {
        class: "model-name"
    },
    hs = {
        key: 0,
        class: "model-tags"
    },
    ys = {
        class: "right"
    },
    bs = ["checked", "disabled"],
    ks = {
        key: 0,
        class: "description"
    },
    xs = {
        key: 1,
        class: "model-requirement-warning"
    },
    ws = {
        key: 2,
        class: "features"
    },
    js = {
        class: "feature-label"
    },
    Cs = {
        class: "feature-tags"
    },
    Ss = {
        key: 3,
        class: "tip"
    },
    Es = {
        key: 0,
        class: "divider"
    },
    As = l({
        __name: "VideoSelectModelNew",
        props: {
            promptImages: {
                type: Array,
                default: []
            },
            modelsSelected: {
                type: String,
                default: ""
            },
            selectedAspectRatio: {
                type: String,
                default: ""
            },
            toggleModel: {
                type: Function,
                default: () => {}
            },
            enableModelCheck: {
                type: Boolean,
                default: !0
            }
        },
        emits: ["updateLastUsedModels", "updateModelsSelected"],
        setup(e, {
            emit: t
        }) {
            const a = e,
                l = q("currentUser"),
                {
                    t: s
                } = A(),
                d = e => {
                    var t;
                    if (!a.enableModelCheck) return !0;
                    const l = Lt.find((t => t.name === e));
                    return !!l && (!("auto" !== a.selectedAspectRatio && l.permitted_aspect_ratios && !l.permitted_aspect_ratios.includes(a.selectedAspectRatio)) && !((null == (t = a.promptImages) ? void 0 : t.length) > 0 && !l.support_i2v))
                },
                u = e => e.description ? e.description(s) : "",
                p = e => e.tip ? e.tip(s) : "",
                f = e => {
                    const t = [];
                    if (!1 === e.support_i2v && !1 !== e.support_t2v && t.push({
                            type: "t2v_only",
                            message: s("components.generate_video_content.t2v_only_requirement")
                        }), !1 === e.support_t2v && !1 !== e.support_i2v && t.push({
                            type: "i2v_only",
                            message: s("components.generate_video_content.i2v_only_requirement")
                        }), e.support_multi_images) {
                        const a = e.min_input_images || 1,
                            l = e.max_input_images || 4;
                        a === l ? t.push({
                            type: "exact_images",
                            message: s("components.generate_video_content.exact_images_requirement", {
                                count: a
                            })
                        }) : t.push({
                            type: "range_images",
                            message: s("components.generate_video_content.range_images_requirement", {
                                min: a,
                                max: l
                            })
                        })
                    }
                    return "vidu/start-end-to-video" === e.name && t.push({
                        type: "start_end_images",
                        message: s("components.generate_video_content.start_end_images_requirement")
                    }), t
                };
            return (t, a) => (r(), n("div", null, [(r(!0), n(m, null, g(i(l) && i(l).gk_realtime_dogfood ? i(Lt).filter((e => !e.deprecated)) : i(Lt).filter((e => !e.hidden)), (l => {
                var i, h, y, b, k;
                return r(), n("div", {
                    key: l.name,
                    class: "model-container"
                }, [c("div", {
                    class: _(["model", {
                        disabled: !d(l.name),
                        "model-auto": "auto" === l.name
                    }]),
                    onClick: t => d(l.name) ? e.toggleModel(l.name) : null
                }, [c("div", us, [c("div", ps, [l.new ? (r(), n("div", vs, " New ")) : o("", !0), c("div", _s, [c("img", {
                    src: l.icon
                }, null, 8, ms)]), c("div", gs, [c("div", fs, v((k = l, "function" == typeof k.label ? k.label(s) : k.label)), 1), l.price_level || l.speed || l.quality ? (r(), n("div", hs, [l.price_level ? (r(), n("span", {
                    key: 0,
                    class: _(["price-tag", `price-${l.price_level}`])
                }, v((b = l.price_level, {
                    budget: s("components.generate_video_content.price_budget"),
                    moderate: s("components.generate_video_content.price_moderate"),
                    premium: s("components.generate_video_content.price_premium"),
                    expensive: s("components.generate_video_content.price_expensive")
                }[b] || b)), 3)) : o("", !0), l.speed ? (r(), n("span", {
                    key: 1,
                    class: _(["speed-tag", `speed-${null==(i=l.speed)?void 0:i.replace("_","-")}`])
                }, v((y = l.speed, {
                    very_fast: s("components.generate_video_content.speed_very_fast"),
                    fast: s("components.generate_video_content.speed_fast"),
                    medium: s("components.generate_video_content.speed_medium"),
                    slow: s("components.generate_video_content.speed_slow")
                }[null == y ? void 0 : y.replace("-", "_")] || y)), 3)) : o("", !0), l.quality ? (r(), n("span", {
                    key: 2,
                    class: _(["quality-tag", `quality-${l.quality}`])
                }, v((h = l.quality, {
                    excellent: s("components.generate_video_content.quality_excellent"),
                    high: s("components.generate_video_content.quality_high"),
                    medium: s("components.generate_video_content.quality_medium")
                }[h] || h)), 3)) : o("", !0)])) : o("", !0)])]), c("div", ys, [c("input", {
                    type: "radio",
                    name: "model",
                    checked: e.modelsSelected === l.name,
                    disabled: !d(l.name)
                }, null, 8, bs)])]), l.description ? (r(), n("div", ks, v(u(l)), 1)) : o("", !0), f(l).length > 0 ? (r(), n("div", xs, [(r(!0), n(m, null, g(f(l), (e => (r(), n("div", {
                    key: e.type,
                    class: "requirement-label"
                }, [a[0] || (a[0] = c("span", {
                    class: "requirement-icon"
                }, "⚠️", -1)), j(" " + v(e.message), 1)])))), 128))])) : o("", !0), l.best_for && l.best_for.length > 0 ? (r(), n("div", ws, [c("div", js, v(t.$t("components.generate_video_content.best_for")) + ": ", 1), c("div", Cs, [(r(!0), n(m, null, g(l.best_for.slice(0, 3), (e => (r(), n("span", {
                    key: e,
                    class: "feature-tag"
                }, v((e => ({
                    premium_video_content: s("components.generate_video_content.feature_premium_video_content"),
                    videos_with_audio: s("components.generate_video_content.feature_videos_with_audio"),
                    highest_quality: s("components.generate_video_content.feature_highest_quality"),
                    quick_video_generation: s("components.generate_video_content.feature_quick_video_generation"),
                    cost_effective_quality: s("components.generate_video_content.feature_cost_effective_quality"),
                    text_to_video: s("components.generate_video_content.feature_text_to_video"),
                    image_animation: s("components.generate_video_content.feature_image_animation"),
                    single_image_to_video: s("components.generate_video_content.feature_single_image_to_video"),
                    high_quality_i2v: s("components.generate_video_content.feature_high_quality_i2v"),
                    professional_projects: s("components.generate_video_content.feature_professional_projects"),
                    general_video_creation: s("components.generate_video_content.feature_general_video_creation"),
                    image_to_video: s("components.generate_video_content.feature_image_to_video"),
                    versatile_ratios: s("components.generate_video_content.feature_versatile_ratios"),
                    standard_video_generation: s("components.generate_video_content.feature_standard_video_generation"),
                    chinese_content: s("components.generate_video_content.feature_chinese_content"),
                    fast_iterations: s("components.generate_video_content.feature_fast_iterations"),
                    cost_effective: s("components.generate_video_content.feature_cost_effective"),
                    multi_image_input: s("components.generate_video_content.feature_multi_image_input"),
                    basic_video_needs: s("components.generate_video_content.feature_basic_video_needs"),
                    good_quality: s("components.generate_video_content.feature_good_quality"),
                    balance_cost_quality: s("components.generate_video_content.feature_balance_cost_quality"),
                    high_quality_content: s("components.generate_video_content.feature_high_quality_content"),
                    premium_results: s("components.generate_video_content.feature_premium_results"),
                    standard_quality: s("components.generate_video_content.feature_standard_quality"),
                    detailed_generation: s("components.generate_video_content.feature_detailed_generation"),
                    multi_image_videos: s("components.generate_video_content.feature_multi_image_videos"),
                    complex_transitions: s("components.generate_video_content.feature_complex_transitions"),
                    professional_editing: s("components.generate_video_content.feature_professional_editing"),
                    lip_sync_videos: s("components.generate_video_content.feature_lip_sync_videos"),
                    talking_heads: s("components.generate_video_content.feature_talking_heads"),
                    audio_video_sync: s("components.generate_video_content.feature_audio_video_sync"),
                    quick_lipsync: s("components.generate_video_content.feature_quick_lipsync"),
                    cost_effective_sync: s("components.generate_video_content.feature_cost_effective_sync"),
                    live_generation: s("components.generate_video_content.feature_live_generation"),
                    real_time_content: s("components.generate_video_content.feature_real_time_content"),
                    interactive_video: s("components.generate_video_content.feature_interactive_video"),
                    subject_consistency: s("components.generate_video_content.feature_subject_consistency"),
                    character_videos: s("components.generate_video_content.feature_character_videos"),
                    portrait_videos: s("components.generate_video_content.feature_portrait_videos"),
                    basic_needs: s("components.generate_video_content.feature_basic_needs"),
                    natural_motion: s("components.generate_video_content.feature_natural_motion"),
                    general_content: s("components.generate_video_content.feature_general_content"),
                    fast_prototyping: s("components.generate_video_content.feature_fast_prototyping"),
                    high_quality_videos: s("components.generate_video_content.feature_high_quality_videos"),
                    premium_content: s("components.generate_video_content.feature_premium_content"),
                    google_quality: s("components.generate_video_content.feature_google_quality"),
                    general_video_generation: s("components.generate_video_content.feature_general_video_generation"),
                    versatile_content: s("components.generate_video_content.feature_versatile_content"),
                    tencent_quality: s("components.generate_video_content.feature_tencent_quality"),
                    versatile_generation: s("components.generate_video_content.feature_versatile_generation"),
                    start_end_videos: s("components.generate_video_content.feature_start_end_videos"),
                    transition_videos: s("components.generate_video_content.feature_transition_videos"),
                    specific_motions: s("components.generate_video_content.feature_specific_motions"),
                    creative_videos: s("components.generate_video_content.feature_creative_videos"),
                    artistic_content: s("components.generate_video_content.feature_artistic_content"),
                    professional_quality: s("components.generate_video_content.feature_professional_quality")
                }[e] || e))(e)), 1)))), 128))])])) : o("", !0), l.tip ? (r(), n("div", Ss, v(p(l)), 1)) : o("", !0)], 10, ds), l.divider ? (r(), n("div", Es)) : o("", !0)])
            })), 128))]))
        }
    }, [
        ["__scopeId", "data-v-6c36b9b2"]
    ]),
    Ts = {
        key: 0,
        class: "model-select-bar"
    },
    Ms = {
        class: "bar-content"
    },
    $s = {
        class: "model-info"
    },
    Is = {
        key: 0,
        class: "model-selector"
    },
    Rs = {
        class: "model-button"
    },
    Ls = {
        class: "model-selected"
    },
    Ps = {
        class: "icon"
    },
    qs = ["src"],
    Os = {
        class: "text"
    },
    Bs = {
        class: "models-list max-h-[45vh] overflow-y-auto"
    },
    Us = {
        key: 1,
        class: "model-selector"
    },
    Fs = {
        class: "model-button"
    },
    Ds = {
        class: "model-selected"
    },
    Ns = {
        class: "icon"
    },
    zs = ["src"],
    Hs = {
        class: "text"
    },
    Vs = {
        class: "models-list max-h-[45vh] overflow-y-auto"
    },
    Gs = {
        key: 2,
        class: "model-selector"
    },
    Ws = {
        class: "model-button"
    },
    Ks = {
        class: "model-selected"
    },
    Js = {
        class: "icon"
    },
    Qs = ["src"],
    Ys = {
        class: "text"
    },
    Zs = {
        class: "models-list max-h-[45vh] overflow-y-auto"
    },
    Xs = l({
        __name: "ModelSelectBar",
        props: {
            project: {
                type: Object,
                default: () => ({})
            }
        },
        setup(e) {
            const {
                t: t
            } = A(), l = q("chatAgentFunc", null), s = q("currentUser"), d = e, p = oe(), m = ie(), g = re(), {
                modelsSelected: f
            } = y(p), {
                modelsSelected: h
            } = y(m), {
                modelsSelected: w
            } = y(g);
            f.value = "auto", h.value = "auto", w.value = "fal-ai/bytedance/seedance/v1/lite", "video_generation_agent" === d.project.type && k(s, (e => {
                !e || w.value.startsWith("gemini") || "plus" !== e.plan && "pro" !== e.plan || (w.value = "gemini/veo3", l && l.setModelParams({
                    type: "video",
                    model: w.value
                }))
            }), {
                immediate: !0
            });
            const j = a(!1),
                C = a(!1),
                S = a(!1),
                E = b((() => Pt.find((e => e.name === f.value)))),
                T = b((() => Mt.find((e => e.name === h.value)))),
                M = b((() => Lt.find((e => e.name === w.value)))),
                I = b((() => {
                    var e;
                    return ["audio_generation_agent", "image_generation_agent", "video_generation_agent"].includes(null == (e = d.project) ? void 0 : e.type)
                })),
                R = e => {
                    p.setModelsSelected(e), l && l.setModelParams({
                        type: "audio",
                        model: e
                    }), j.value = !1
                },
                L = e => {
                    m.setModelsSelected(e), l && l.setModelParams({
                        type: "image",
                        model: e
                    }), C.value = !1
                },
                P = e => {
                    g.setModelsSelected(e), l && l.setModelParams({
                        type: "video",
                        model: e
                    }), S.value = !1
                };
            return $((() => {
                l && ("audio_generation_agent" === d.project.type && l.setModelParams({
                    type: "audio",
                    model: f.value
                }), "image_generation_agent" === d.project.type && l.setModelParams({
                    type: "image",
                    model: h.value
                }), "video_generation_agent" === d.project.type && l.setModelParams({
                    type: "video",
                    model: w.value
                }))
            })), (a, l) => I.value ? (r(), n("div", Ts, [c("div", Ms, [c("div", $s, ["audio_generation_agent" === e.project.type ? (r(), n("div", Is, [u(i(kt), {
                show: j.value,
                "onUpdate:show": l[0] || (l[0] = e => j.value = e),
                trigger: "click",
                "show-arrow": !1,
                placement: "top-start",
                class: "models-popover",
                style: {
                    padding: "0",
                    "border-radius": "12px",
                    "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                }
            }, {
                trigger: x((() => {
                    var e, a;
                    return [c("div", Rs, [c("div", Ls, [c("div", Ps, [c("img", {
                        src: null == (e = E.value) ? void 0 : e.icon
                    }, null, 8, qs)]), c("div", Os, v(E.value ? (a = E.value, "function" == typeof a.label ? a.label(t) : a.label) : ""), 1)]), c("div", {
                        class: _(["trigger-icon", {
                            active: j.value
                        }])
                    }, [u(i(ne))], 2)])]
                })),
                default: x((() => [c("div", Bs, [u(ce, {
                    "models-selected": i(f),
                    "toggle-model": R
                }, null, 8, ["models-selected"])])])),
                _: 1
            }, 8, ["show"])])) : o("", !0), "image_generation_agent" === e.project.type ? (r(), n("div", Us, [u(i(kt), {
                show: C.value,
                "onUpdate:show": l[1] || (l[1] = e => C.value = e),
                trigger: "click",
                "show-arrow": !1,
                placement: "top-start",
                class: "models-popover",
                style: {
                    padding: "0",
                    "border-radius": "12px",
                    "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                }
            }, {
                trigger: x((() => {
                    var e, a;
                    return [c("div", Fs, [c("div", Ds, [c("div", Ns, [c("img", {
                        src: null == (e = T.value) ? void 0 : e.icon
                    }, null, 8, zs)]), c("div", Hs, v(T.value ? (a = T.value, "function" == typeof a.label ? a.label(t) : a.label) : ""), 1)]), c("div", {
                        class: _(["trigger-icon", {
                            active: C.value
                        }])
                    }, [u(i(ne))], 2)])]
                })),
                default: x((() => [c("div", Vs, [u(cs, {
                    "models-selected": i(h),
                    "toggle-model": L,
                    "enable-model-check": !1
                }, null, 8, ["models-selected"])])])),
                _: 1
            }, 8, ["show"])])) : o("", !0), "video_generation_agent" === e.project.type ? (r(), n("div", Gs, [u(i(kt), {
                show: S.value,
                "onUpdate:show": l[2] || (l[2] = e => S.value = e),
                trigger: "click",
                "show-arrow": !1,
                placement: "top-start",
                class: "models-popover",
                style: {
                    padding: "0",
                    "border-radius": "12px",
                    "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                }
            }, {
                trigger: x((() => {
                    var e, a;
                    return [c("div", Ws, [c("div", Ks, [c("div", Js, [c("img", {
                        src: null == (e = M.value) ? void 0 : e.icon
                    }, null, 8, Qs)]), c("div", Ys, v(M.value ? (a = M.value, "function" == typeof a.label ? a.label(t) : a.label) : ""), 1)]), c("div", {
                        class: _(["trigger-icon", {
                            active: S.value
                        }])
                    }, [u(i(ne))], 2)])]
                })),
                default: x((() => [c("div", Zs, [u(As, {
                    "models-selected": i(w),
                    "toggle-model": P,
                    "enable-model-check": !1
                }, null, 8, ["models-selected"])])])),
                _: 1
            }, 8, ["show"])])) : o("", !0)])])])) : o("", !0)
        }
    }, [
        ["__scopeId", "data-v-e52b60af"]
    ]),
    en = {
        class: "slide-reference-card"
    },
    tn = {
        class: "slide-reference-header"
    },
    an = {
        class: "slide-reference-info"
    },
    ln = {
        key: 0,
        class: "page-badge"
    },
    sn = {
        class: "slide-reference-content"
    },
    nn = ["src"],
    on = {
        key: 1,
        class: "slide-placeholder"
    },
    rn = l({
        __name: "SlideReferenceCard",
        props: {
            data: {
                type: Object,
                required: !0,
                default: () => ({})
            }
        },
        setup: e => (t, a) => (r(), n("div", en, [c("div", tn, [c("div", an, [void 0 !== e.data.page_number ? (r(), n("span", ln, v(t.$t("components.slide_reference_card.page", {
            index: e.data.page_number + 1
        })), 1)) : o("", !0)])]), c("div", sn, [e.data.image_url ? (r(), n("img", {
            key: 0,
            src: e.data.image_url,
            alt: "Slide preview",
            class: "slide-image"
        }, null, 8, nn)) : (r(), n("div", on, a[0] || (a[0] = [h('<div class="placeholder-icon" data-v-03b19248><svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-v-03b19248><rect x="3" y="3" width="18" height="18" rx="2" ry="2" data-v-03b19248></rect><circle cx="8.5" cy="8.5" r="1.5" data-v-03b19248></circle><polyline points="21 15 16 10 5 21" data-v-03b19248></polyline></svg></div><div class="placeholder-text" data-v-03b19248>Screenshot</div>', 2)])))])]))
    }, [
        ["__scopeId", "data-v-03b19248"]
    ]),
    cn = {
        class: "template-reference-card"
    },
    dn = {
        class: "template-reference-header"
    },
    un = {
        class: "template-reference-info"
    },
    pn = {
        class: "template-badge"
    },
    vn = {
        key: 0,
        class: "template-name"
    },
    _n = {
        class: "template-reference-content"
    },
    mn = ["src", "alt"],
    gn = {
        key: 1,
        class: "template-placeholder"
    },
    fn = {
        class: "placeholder-text"
    },
    hn = {
        key: 2,
        class: "template-meta"
    },
    yn = {
        key: 0,
        class: "meta-item"
    },
    bn = {
        class: "meta-label"
    },
    kn = {
        class: "meta-value"
    },
    xn = l({
        __name: "TemplateReferenceCard",
        props: {
            data: {
                type: Object,
                required: !0,
                default: () => ({})
            },
            showActions: {
                type: Boolean,
                default: !0
            },
            showMeta: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["viewTemplate"],
        setup(e, {
            emit: t
        }) {
            const l = a(!1),
                s = () => {
                    l.value = !0
                };
            return (t, a) => (r(), n("div", cn, [c("div", dn, [c("div", un, [c("span", pn, v(t.$t("components.slides.use_template")), 1), e.data.template_name ? (r(), n("span", vn, v(e.data.template_name), 1)) : o("", !0)])]), c("div", _n, [e.data.template_screenshot_url ? (r(), n("img", {
                key: 0,
                src: e.data.template_screenshot_url,
                alt: e.data.template_name || t.$t("components.slides.template_preview_alt"),
                class: "template-image",
                onError: s
            }, null, 40, mn)) : (r(), n("div", gn, [a[0] || (a[0] = c("div", {
                class: "placeholder-icon"
            }, [c("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                width: "48",
                height: "48",
                viewBox: "0 0 24 24",
                fill: "none",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, [c("path", {
                d: "M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"
            }), c("circle", {
                cx: "12",
                cy: "13",
                r: "3"
            })])], -1)), c("div", fn, v(t.$t("components.slides.template_preview")), 1)])), e.showMeta ? (r(), n("div", hn, [e.data.template_id ? (r(), n("div", yn, [c("span", bn, v(t.$t("components.slides.template_id")), 1), c("span", kn, v(e.data.template_id), 1)])) : o("", !0)])) : o("", !0)])]))
        }
    }, [
        ["__scopeId", "data-v-dca6a30d"]
    ]),
    wn = {
        class: "pt-0"
    },
    jn = {
        class: "px-3 mb-[-22px]"
    },
    Cn = {
        class: "rounded-tl-xl rounded-tr-xl border border-[#DEC6C5]"
    },
    Sn = {
        key: 0,
        class: "flex flex-col gap-[10px] bg-[#FFFBFB] pt-[16px] pb-[24px] dark:bg-[#333]"
    },
    En = {
        class: "flex items-center gap-[6px] justify-start pl-[14px]"
    },
    An = {
        class: "text-black text-sm font-normal font-['Arial'] leading-[1.5em] dark:text-white"
    },
    Tn = B({
        __name: "AgentModeTip",
        props: {
            title: {
                type: String,
                required: !0
            },
            tips: {
                type: Array,
                required: !0
            }
        },
        setup(e) {
            const {
                t: t
            } = A(), l = a(!1);
            return (t, a) => (r(), n("div", wn, [c("div", jn, [c("div", Cn, [c("div", {
                class: _(["flex items-center justify-between bg-[#F6ECEC] px-[12px] h-[36px] rounded-tl-xl rounded-tr-xl text-pink-950 text-sm font-bold  leading-tight pb-[7px] cursor-pointer"]),
                onClick: a[0] || (a[0] = e => l.value = !l.value)
            }, [c("div", null, v(e.title), 1), c("div", {
                class: _(["cursor-pointer w-[20px] h-[20px] flex items-center justify-center bg-[#EBE1E1] rounded-[30px] transition-all duration-300", l.value ? "rotate-0" : "rotate-180"])
            }, [u(i(fe))], 2)]), l.value ? (r(), n("div", Sn, [(r(!0), n(m, null, g(e.tips, (e => (r(), n("div", En, [u(i(ke), {
                class: "w-[18px] h-[18px] text-[#5CD4A1]"
            }), c("div", An, v(e), 1)])))), 256))])) : o("", !0)])])]))
        }
    }),
    Mn = {
        __name: "agent",
        props: {
            agentType: {
                type: String,
                default: "default"
            },
            firstSearchQuery: {
                type: String,
                default: ""
            },
            not_login: {
                type: Boolean,
                default: !1
            },
            defaultMessageContent: {
                type: String,
                default: "thinking..."
            },
            getWritingContent: {
                type: Function,
                default: () => {}
            },
            onProjectEvent: {
                type: Function,
                default: () => {}
            },
            extra_data: {
                type: Object,
                default: {}
            },
            getClientContext: {
                type: Function,
                default: async () => {}
            },
            force_recaptcha: {
                type: Boolean,
                default: !1
            },
            noAutoScroll: {
                type: Boolean,
                default: !1
            },
            chat_session_id: {
                type: String,
                default: null
            },
            canvas_history_id: {
                type: String,
                default: null
            },
            is_private: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["triggerAction", "agentAskFinish", "agentAskStart"],
        setup(e, {
            expose: t,
            emit: l
        }) {
            const o = e,
                i = l,
                c = a(null),
                d = a(null),
                v = a("standard"),
                _ = na(),
                {
                    t: g
                } = A(),
                {
                    extra_data: f,
                    not_login: h,
                    agentType: x,
                    defaultMessageContent: w,
                    chat_session_id: j,
                    canvas_history_id: C,
                    is_private: S
                } = s(o),
                E = a(null),
                M = gt(),
                {
                    domBase64: I
                } = y(M);
            E.value = document.documentElement;
            const R = q("IS_CN"),
                L = () => "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function(e) {
                    const t = 16 * Math.random() | 0;
                    return ("x" == e ? t : 3 & t | 8).toString(16)
                })),
                P = e => {
                    if ("A" == e.target.tagName) {
                        let a = e.target.getAttribute("href");
                        if (!a || !a.startsWith("prompt://do_action")) return;
                        e.preventDefault();
                        try {
                            const t = new URL(e.target.href).searchParams.get("action"),
                                a = JSON.parse(t);
                            re(a)
                        } catch (t) {}
                    }
                },
                O = q("jsBridge"),
                B = a("");
            k(O, (e => {
                e && p.isGensparkAppIos() && O.value.callHandler("getPushToken", {}, (e => {
                    B.value = e
                }))
            }), {
                immediate: !0
            }), k((() => c.value), ((e, t) => {
                p.log("slotContent changed", e, t), t && t.removeEventListener("click", P), e && e.addEventListener("click", P)
            }));
            const D = a([]),
                N = a({}),
                z = a({}),
                H = a({}),
                V = a({}),
                G = () => ee(),
                W = (e = null) => e ? (N.value[e] || (N.value[e] = []), N.value[e]) : D.value,
                K = (e, t = null) => {
                    if (t) {
                        const a = W(t),
                            l = a.findIndex((t => t.id === e));
                        if (-1 !== l) return {
                            message: a[l],
                            index: l,
                            projectId: t
                        }
                    } else {
                        const t = D.value.findIndex((t => t.id === e));
                        if (-1 !== t) return {
                            message: D.value[t],
                            index: t,
                            projectId: null
                        };
                        for (const [a, l] of Object.entries(N.value)) {
                            const t = l.findIndex((t => t.id === e));
                            if (-1 !== t) return {
                                message: l[t],
                                index: t,
                                projectId: a
                            }
                        }
                    }
                    return null
                },
                J = (e, t = null) => {
                    if (t) {
                        const a = W(t);
                        return a.push(e), a[a.length - 1]
                    }
                    return D.value.push(e), D.value[D.value.length - 1]
                },
                Q = e => t => {
                    H.value[e] || (H.value[e] = {
                        id: e,
                        session_state: {},
                        ...t.project_data
                    }), ((e, t) => {
                        if ("project_start" === t.type && (e.id = t.id), "project_field" === t.type && (sa(e, t.field_name, (() => t.field_value)), "session_state.current_chat_session_id" === t.field_name && (e.session_state.current_chat_session_id = t.field_value, e.session_state.chat_session_history || (e.session_state.chat_session_history = []), e.session_state.chat_session_history = e.session_state.chat_session_history.filter((e => e.id !== t.field_value)), e.session_state.chat_session_history.unshift({
                                id: t.field_value,
                                name: t.field_value
                            })), "session_state.current_chat_session_name" === t.field_name)) {
                            const a = e.session_state.chat_session_history.find((t => t.id === e.session_state.current_chat_session_id));
                            a && (a.name = t.field_value)
                        }
                        "project_field_delta" === t.type && sa(e, t.field_name, (e => "string" == typeof e ? e + t.delta : e)), "project_data" === t.type && Object.assign(e, t.project_data), "project_field_append_item" === t.type && sa(e, t.field_name, (e => e ? [...e, t.field_value] : [t.field_value]))
                    })(H.value[e], t);
                    const a = V.value[e];
                    a && a.session_state && (a.session_state.sub_project = { ...H.value[e],
                        session_state: H.value[e].session_state
                    })
                },
                Y = e => e && z.value[e] || o.onProjectEvent,
                Z = (e, t = null) => {
                    e && (z.value[e] = t || Q(e))
                },
                X = () => D.value.length > 0 && D.value[D.value.length - 1].thinking,
                ee = () => {
                    const e = new URL(location.href);
                    if (e.searchParams.set("type", x.value), !e.searchParams.get("id") && e.pathname.match(/^\/spark\//)) {
                        const t = e.pathname.split("/");
                        e.searchParams.set("id", t[t.length - 1]), p.log("set id", t[t.length - 1])
                    }
                    return e.searchParams.get("id")
                },
                te = a(null),
                ae = b((() => null !== te.value)),
                {
                    setIsAsking: le
                } = aa();
            k(ae, (() => {
                le(ae.value)
            }), {
                immediate: !0
            });
            const se = new zt,
                ne = se.toBottomVisible;
            k((() => se.toBottomVisible.value), (() => {
                p.log("toBottomVisible changed", se.toBottomVisible.value), ne.value = se.toBottomVisible.value
            }));
            const oe = async (e = null, t = {}, a = null, l = !1) => {
                    window.gtag && gtag("event", "agent_ask", {
                        user_s_input: e,
                        agent_type: x.value
                    });
                    const s = o.noAutoScroll || !!t && !!t.no_auto_scroll;
                    if (h.value) {
                        if (!l) {
                            T();
                            const e = location.href,
                                t = "/login?redirect_url=" + encodeURIComponent(e);
                            D.value.push({
                                id: L(),
                                role: "assistant",
                                content: g("components.copilot.please-login-login_url-to-use-this-feature", [t]),
                                is_prompt: !0
                            })
                        }
                        return !1
                    }
                    if (X()) return l || _.info(g("components.copilot.thinking-please-wait")), !1;
                    te.value && te.value.abort(), te.value = new AbortController;
                    const n = te.value;
                    if (p.log("submit prompt:", e), !(t && t.force || e || !(D.value.length > 0 && "string" == typeof D.value[D.value.length - 1].content || 0 === D.value.length))) return p.log("user_s_input is empty"), !1;
                    let r;
                    if (a) return r = {
                        id: L(),
                        thinking: !0,
                        role: "assistant",
                        content: w.value,
                        is_prompt: !0
                    }, r.content = a, r.thinking = !1, r.is_prompt = !1, void D.value.push(r);
                    if (E.value && !s && (se.setScrollableParent(E.value), se.start()), o.getClientContext) {
                        const e = await o.getClientContext();
                        e && (f.value.client_context = e)
                    }
                    if (f.value && (f.value.writingContent = null), o.getWritingContent) {
                        const e = o.getWritingContent();
                        e && (f.value.writingContent = e)
                    }
                    let c = 0;
                    try {
                        const a = ee(),
                            n = D.value.filter((e => !e.is_prompt && !e.thinking && (!e.project_id || e.project_id === a))),
                            d = la(n);
                        d.forEach((e => {
                            "tool" != e.role || e.content || (e.content = "no result")
                        }));
                        let u = -1;
                        for (let e = d.length - 1; e >= 0; e--) {
                            const t = d[e];
                            if (t.session_state && !0 === t.session_state.is_compact_summary) {
                                u = e;
                                break
                            }
                        } - 1 !== u && d.splice(0, u);
                        const v = (e => {
                                let t = la(e);
                                for (; t.length > 0 && "assistant" === t[0].role;) t.shift();
                                const a = [];
                                for (const c of t) {
                                    const e = c.tool_calls;
                                    if (null != e) {
                                        for (const t of e) try {
                                            t.function && t.function.arguments && null != t.function.arguments && JSON.parse(t.function.arguments)
                                        } catch (r) {
                                            a.push(t.id)
                                        }
                                        c.tool_calls = e.filter((e => e.id && !a.includes(e.id)))
                                    }
                                }
                                t = t.filter((e => "tool" !== e.role || null != e.tool_call_id && !a.includes(e.tool_call_id)));
                                let l = 0;
                                for (; l < t.length;) {
                                    const e = t[l].tool_calls;
                                    if (null != e) {
                                        const a = e.length;
                                        let s = 0,
                                            n = l + 1;
                                        for (; n < t.length && "tool" === t[n].role;) s++, n++;
                                        if (s < a)
                                            for (let l = s; l < a; l++) t.splice(n, 0, {
                                                role: "tool",
                                                tool_call_id: e[l].id,
                                                content: "failed to call tool",
                                                id: "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function(e) {
                                                    const t = 16 * Math.random() | 0;
                                                    return ("x" === e ? t : 3 & t | 8).toString(16)
                                                }))
                                            })
                                    }
                                    l++
                                }
                                for (const c of t) "tool" !== c.role || null != c.content && "" !== c.content || (c.content = "no result");
                                const s = new Set;
                                for (const c of t)
                                    if (null != c.tool_calls)
                                        for (const e of c.tool_calls) e.id && s.add(e.id);
                                const n = [];
                                for (const c of t)("tool" !== c.role || null == c.tool_call_id || s.has(c.tool_call_id)) && n.push(c);
                                t = n;
                                const o = new Set,
                                    i = [];
                                for (const c of t) {
                                    if ("tool" === c.role && null != c.tool_call_id) {
                                        if (o.has(c.tool_call_id)) continue;
                                        o.add(c.tool_call_id)
                                    }
                                    i.push(c)
                                }
                                for (t = i; t.length > 0 && "assistant" === t[t.length - 1].role;) t.pop();
                                return t
                            })(d),
                            _ = R.value ? "" : await p.getRecaptchaToken("agent");
                        o.force_recaptcha && !_ && location.reload();
                        const m = { ...f.value,
                            ...t || {},
                            type: x.value,
                            project_id: ee(),
                            messages: v,
                            user_s_input: e,
                            g_recaptcha_token: _,
                            is_private: S.value,
                            push_token: B.value
                        };
                        j.value && (m.chat_session_id = j.value), C.value && (m.canvas_history_id = C.value);
                        let g = "/api/agent/ask_proxy";
                        l && (g = "/api/agent/ask_proxy_events"), i("agentAskStart", m), await ea(g, m, (async e => {
                            c++;
                            let t = r;
                            if (e.message_id) {
                                const a = K(e.message_id, e.project_id);
                                a && (t = a.message)
                            }
                            if (e.render_template && (t.render_template = e.render_template), "message_field_delta" !== e.type && p.log("message event", e.type, e), "message_field" === e.type && (sa(t, e.field_name, (() => e.field_value)), (e => {
                                    var t, a;
                                    if ("tool" === (null == e ? void 0 : e.role) && (null == (a = null == (t = null == e ? void 0 : e.session_state) ? void 0 : t.sub_project) ? void 0 : a.id)) {
                                        const t = e.session_state.sub_project.id;
                                        if (V.value[t]) return;
                                        V.value[t] = e, H.value[t] = { ...e.session_state.sub_project
                                        }, H.value[t].session_state || (H.value[t].session_state = {}), H.value[t].session_state.messages || (H.value[t].session_state.messages = W(t)), Z(t)
                                    }
                                })(t)), "message_field_delta" === e.type && sa(t, e.field_name, (t => (t || "") + e.delta)), "message_field_append_item" === e.type && sa(t, e.field_name, (t => [...t || [], e.field_value])), "message_result" == e.type && (Object.assign(t, e.message), e.project_id && t && (t.project_id = e.project_id), t.action && re(t.action)), "message_do_action" == e.type && e.action && re(e.action), "message_start" == e.type) {
                                if (K(e.message_id, e.project_id)) return void p.log("message_start already exists", e.message_id);
                                if (e.message_id) {
                                    r && (r.thinking = !1), r = {
                                        id: e.message_id,
                                        role: e.role,
                                        thinking: !0,
                                        content: ""
                                    }, e.tool_call_id && (r.tool_call_id = e.tool_call_id), e.project_id && (r.project_id = e.project_id);
                                    const t = G(),
                                        a = e.project_id && e.project_id !== t ? e.project_id : null;
                                    r = J(r, a), p.log("message_start added to project", a || "main", e.message_id)
                                }
                            }
                            if ("project_start" == e.type || "project_field" == e.type || "project_field_delta" == e.type || "project_field_append_item" == e.type || "project_data" == e.type) {
                                const t = Y(e.id);
                                t && t(e)
                            }
                            s || se.autoScrollToBottom()
                        }), te.value.signal), setTimeout((() => {
                            s || se.autoScrollToBottom()
                        }), 0)
                    } catch (d) {
                        if (!l)
                            if (r = {
                                    thinking: !1,
                                    role: "assistant",
                                    content: "",
                                    is_prompt: !0,
                                    error: !0,
                                    id: L()
                                }, r = J(r, null), n.signal.aborted) r.is_aborted = !0, r.content = g("components.copilot.request-aborted");
                            else if ("Unauthorized" == d.message) {
                            const e = location.href,
                                t = "/login?redirect_url=" + encodeURIComponent(e);
                            r.content = g("components.copilot.please-login-login_url-to-use-this-feature", [t]), r.is_prompt = !0
                        } else "network error" == d.message || "Load failed" == d.message ? (r.content = d.message, r.should_reload = !0, r._all_events_count = c, r.is_prompt = !0, window.$azureAppInsights && window.$azureAppInsights.trackException({
                            exception: d,
                            properties: {
                                eventType: "agent_network_retry",
                                agentType: x.value,
                                userInput: e,
                                errorMessage: d.message
                            }
                        })) : (r.content = d.message, r.should_retry = !0, r._all_events_count = c, r.is_prompt = !0, window.$azureAppInsights && window.$azureAppInsights.trackException({
                            exception: d,
                            properties: {
                                eventType: "agent_should_retry",
                                agentType: x.value,
                                userInput: e,
                                errorMessage: d.message
                            }
                        }))
                    } finally {
                        l || (r && (r.is_prompt = !1, (r.is_aborted || r.error) && (r.is_prompt = !0)), D.value.forEach((e => {
                            e.thinking = !1
                        })), r && (r.content || (r.should_retry = !0, r._all_events_count = c))), n === te.value && (te.value = null), de.value && de.value()
                    }
                    i("agentAskFinish", {
                        user_s_input: e,
                        message: r
                    }), gtag("event", "agent_ask_finish", {
                        user_s_input: e
                    })
                },
                ie = async (e, t = !0, a = {}) => {
                    const l = o.noAutoScroll || !!a && !!a.no_auto_scroll,
                        s = { ...e,
                            content: "",
                            thinking: !0
                        },
                        n = e.project_id && e.project_id !== G() ? e.project_id : null,
                        i = W(n);
                    i.push(s);
                    const r = i.length - 1,
                        c = e.content;
                    if (t)
                        for (let o = 0; o < c.length; o++) await new Promise((e => setTimeout(e, 20))), i[r].content += c[o];
                    else i[r].content = c;
                    i[r].thinking = !1, l || setTimeout((() => {
                        E.value && (E.value.scrollTop = E.value.scrollHeight)
                    }), 0), e.action && await re(e.action)
                },
                re = async e => {
                    if ("ACTION_ASK" == e.type && (!e.user_s_input || e.action_params && e.action_params.no_user_message || await ie({
                            id: L(),
                            role: "user",
                            content: e.user_s_input
                        }, !0, e.action_params), await oe(e.user_s_input, e.action_params)), "ACTION_CREDIT_EXHAUSTED" == e.type) v.value = "standard", F((() => {
                        d.value.trigger()
                    }));
                    else if ("ACTION_CREDIT_INSUFFICIENT" == e.type) v.value = "credit_insufficient", F((() => {
                        d.value.trigger()
                    }));
                    else if ("ACTION_CAPACITY_EXHAUSTED" == e.type) v.value = "capacity_exhausted", F((() => {
                        d.value.trigger()
                    }));
                    else if ("ACTION_RUN_INSTRUCTIONS" == e.type && e.client_task_action && (i("triggerAction", e.client_task_action), window && window.executeSpreadJSScript && "function" == typeof window.executeSpreadJSScript)) try {
                        window.executeSpreadJSScript(e.client_task_action)
                    } catch (t) {}
                    ce.value[e.type] && ce.value[e.type](e)
                };
            $((() => {
                if (o.firstSearchQuery) {
                    const e = {
                        type: "ACTION_ASK",
                        user_s_input: o.firstSearchQuery,
                        action_params: {
                            no_auto_scroll: !0
                        }
                    };
                    re(e)
                }
            }));
            const ce = a({}),
                de = a(null);
            const ue = a(null);
            return t({
                setExpandedToolCallResultMessage: e => {
                    ue.value = e
                },
                getExpandedToolCallResultMessage: () => ue.value,
                retry: async (e = {}) => {
                    e || (e = {});
                    const t = e.only_get_events || !1;
                    let a = -1;
                    for (let l = D.value.length - 1; l >= 0; l--)
                        if ("user" === D.value[l].role) {
                            a = l;
                            break
                        }
                    return -1 !== a && a < D.value.length - 1 && (D.value = D.value.slice(0, a + 1)), await oe("", {
                        force: !0
                    }, "", t)
                },
                toBottomVisible: ne,
                toBottom: () => {
                    E.value && (se.setScrollableParent(E.value), se.start())
                },
                setAskFinishCallback: e => {
                    de.value = e
                },
                setActionHandler: (e, t) => {
                    ce.value[e] = t
                },
                addMessage: ie,
                doAction: re,
                is_thinking: X,
                is_asking: ae,
                cancelAskAgent: async (e = {}) => {
                    if (te.value && (te.value.abort(), te.value = null), e && e.userAborted) {
                        const e = ee();
                        if (e) {
                            (await fetch(`/api/agent/ask_abort?project_id=${e}`)).ok
                        }
                    }
                },
                askAgent: (e = {}) => {
                    oe(e.user_s_input, e.action_params, e.answer, e.only_get_events)
                },
                createEmptyProject: async (e = {}) => {
                    e || (e = {}), await oe("", { ...e,
                        create_empty_project: !0,
                        force: !0
                    })
                },
                forceSubmit: async () => {
                    await oe("", {
                        force: !0
                    }, "")
                },
                setRecommendations: e => {
                    let t = D.value.filter((e => e.is_prompt)).length == D.value.length;
                    e && t && D.value.length > 0 && (D.value[D.value.length - 1].recommend_actions = e)
                },
                setNotLogin: e => {
                    h.value = e
                },
                submitPrompt: async (e = "", t = "", a = [], l = [], s = {}, n = {}, o = "", i = null, r = null) => {
                    if (a || (a = []), l || (l = []), !(e.trim() || a && 0 !== a.length || l && 0 !== l.length || t || i)) return void p.log("submitPrompt return");
                    const c = St(),
                        d = (e => {
                            if (!e.hasAnySelection) return null;
                            const t = e.selectionInfo;
                            return "vditor" === t.type && t.text ? {
                                type: "text",
                                text: {
                                    text: t.text
                                }
                            } : "text" === t.type && t.text ? {
                                type: "text",
                                text: {
                                    text: t.text,
                                    container_html: t.startContainer && t.startContainer.parentElement && t.startContainer.parentElement.outerHTML || ""
                                }
                            } : null
                        })(c);
                    let u = e;
                    if (e.startsWith("vision://")) {
                        u = `<img src="${e.slice(9)}" alt="Image" style="max-height: 100px;">`
                    }
                    const v = {
                        role: "user",
                        id: L(),
                        content: i || u
                    };
                    if (a.length > 0 || l.length > 0 || n && n.dom_path || r) {
                        if (v.content = [], a.forEach((e => {
                                v.content.push({
                                    type: "image_url",
                                    image_url: {
                                        url: e
                                    }
                                })
                            })), l.forEach((e => {
                                v.content.push({
                                    type: "private_file",
                                    private_file: e
                                })
                            })), n && n.dom_path) {
                            const e = n;
                            let t = "",
                                a = [];
                            if (null === e.page_number || void 0 === e.page_number) throw new Error("area.page_number is required but is null or undefined");
                            if (a.push(`User manually selected a DOM element on page ${e.page_number+1}.`), null != e.dom_path)
                                if ("string" == typeof e.dom_path) a.push(`The DOM path is: ${e.dom_path}`);
                                else if ("object" == typeof e.dom_path && e.dom_path.element) {
                                const t = e.dom_path.element;
                                let l = `The selected element is a <${t.tagName}>`;
                                t.id && (l += ` with ID "${t.id}"`), t.classes && t.classes.length > 0 && (l += ` with classes: ${t.classes.join(", ")}`), t.textContent && (l += ` containing text: "${t.textContent}"`), "img" === t.tagName && t.attributes.alt && (l += ` (image alt: "${t.attributes.alt}")`), "input" === t.tagName && t.attributes.type && (l += ` (${t.attributes.type} input`, t.attributes.value && (l += ` with value: "${t.attributes.value}"`), l += ")"), "a" === t.tagName && t.attributes.href && (l += ` linking to "${t.attributes.href}"`), l += ".", a.push(l), e.dom_path.path && "document" !== e.dom_path.path && a.push(`The element's location path is: ${e.dom_path.path}`), a.push("The user may want to ask questions or make modifications specifically about the content within this DOM element (rather than the entire page content. Adjust the viewport to this page to see the complete content of this slide and better understand what user'd like to do.)")
                            }
                            a.length > 0 && (t = a.join(" ")), I.value && (n.image_url = I.value), t && v.content.push({
                                type: "text",
                                text: t,
                                render_template: "slide_selected",
                                render_data: n
                            })
                        }
                        if (r) {
                            const e = `Use the template "${r.template_name}" with template id: ${r.template_id} to create a presentation.`;
                            v.content.push({
                                type: "text",
                                text: e,
                                render_template: "template_card",
                                render_data: {
                                    template_id: r.template_id,
                                    template_name: r.template_name,
                                    template_screenshot_url: r.template_screenshot_url,
                                    template_width: r.template_width || 1280,
                                    template_height: r.template_height || 720
                                }
                            })
                        }
                        e && v.content.push({
                            type: "text",
                            text: e
                        })
                    }
                    o && (v.session_state || (v.session_state = {}), v.session_state.extension_prompt = o), d && (v.session_state || (v.session_state = {}), v.session_state.html_editor_selection = d, c.clearAllSelections("message-submitted")), J(v, null);
                    const _ = { ...s || {}
                    };
                    await oe(e, _, t)
                },
                setContentElement: e => {
                    c.value = e
                },
                setScrollElement: e => {
                    e && E.value !== e && function(e) {
                        let t = 0;
                        e.addEventListener("touchstart", (function(e) {
                            p.log("touchstart!!!!!!"), t = e.touches ? e.touches[0].screenY : e.screenY
                        })), e.addEventListener("touchmove", (function(a) {
                            var l = a.touches ? a.touches[0].screenY - t : a.screenY - t,
                                s = e.scrollTop,
                                n = e.scrollHeight,
                                o = e.offsetHeight;
                            (0 === s && l > 0 || s + o >= n && l < 0) && a.preventDefault()
                        }))
                    }(e), p.log("setScrollElement", e), E.value = e, c.value || (c.value = e), o.noAutoScroll || (se.setScrollableParent(e), se.start())
                },
                setInputFocused: e => {
                    se.setInputFocused(e)
                },
                getMessagesRef: () => D,
                setMessages: e => {
                    D.value = e
                },
                abort: () => {
                    te.value && (te.value.abort(), te.value = null)
                },
                getProjectMessages: W,
                findMessageById: K,
                addMessageToProject: J,
                registerProjectEventHandler: Z,
                getProjectEventHandler: Y,
                getCurrentProjectId: G,
                getProjectMessagesRef: () => N,
                getProjectEventHandlersRef: () => z
            }), (e, t) => (r(), n(m, null, [U(e.$slots, "default", {
                messages: D.value,
                doAction: re,
                ref_key: "slotContent",
                ref: c
            }), u(ta, {
                ref_key: "quotaExceed",
                ref: d,
                "window-type": v.value
            }, null, 8, ["window-type"])], 64))
        }
    },
    $n = e => {
        const t = ie(),
            l = a("gpt-4o"),
            {
                modelsSelected: s,
                selectedAspectRatio: n,
                personalizeMode: o,
                hdEnabled: i,
                reflectionEnabled: r
            } = y(t),
            c = a({}),
            d = a([]),
            u = a("auto"),
            v = a([]),
            _ = ["flux", "flux-pro/ultra", "flux-speed", "recraft-v3"];
        k((() => d.value), (e => {
            const t = {};
            Object.entries(c.value).forEach((([a, l]) => {
                e.includes(a) && (t[a] = l)
            })), c.value = t
        }));
        const m = b((() => e.debug)),
            g = b((() => ({
                model_configs: ("auto" === s.value ? ((e, t, a) => {
                    let l = e.length > 0 ? [...e] : [...t];
                    if (p.log("getAutoModeModels starting with models", l, "; imageModelMap", a), Object.keys(a).length > 0) {
                        const e = Object.values(a).filter((e => e.model)).filter((e => {
                            const t = modelsConfig.find((t => t.name === e.model));
                            return t && !t.is_i2i
                        })).reduce(((e, t) => (e[t.model] = (e[t.model] || 0) + 1, e)), {});
                        if (p.log("modelUsageCount", e), Object.keys(e).length > 0) {
                            const a = Object.keys(e)[0],
                                s = [a, a, ...t.filter((e => e !== a))].slice(0, 4);
                            p.log("newModels after combining", s), l = s
                        }
                    }
                    const s = l.slice(0, 4);
                    return p.log("Final models returned:", s), s
                })(v.value, _, { ...c.value
                }) : [s.value]).map((e => ({
                    model: e,
                    aspect_ratio: n.value,
                    use_personalized_models: "none" !== o.value,
                    fashion_profile_id: "none" !== o.value ? o.value : null,
                    hd: i.value,
                    reflection_enabled: r.value,
                    style: u.value,
                    ...m.value && {
                        debug: !0
                    }
                }))),
                llm_model: l.value,
                imageModelMap: c.value
            })));
        return {
            imageModelMap: c,
            extra_data: g
        }
    },
    In = {
        class: "cooperation-canvas"
    },
    Rn = {
        class: "canvas-header"
    },
    Ln = {
        key: 0,
        class: "status-info"
    },
    Pn = {
        key: 0,
        class: "pending-count"
    },
    qn = {
        class: "canvas-content"
    },
    On = {
        key: 0,
        class: "task-status-section"
    },
    Bn = {
        class: "task-list"
    },
    Un = {
        class: "task-header"
    },
    Fn = {
        class: "task-id"
    },
    Dn = {
        class: "task-status"
    },
    Nn = {
        class: "task-timestamp"
    },
    zn = {
        key: 0,
        class: "task-details"
    },
    Hn = {
        class: "result-preview"
    },
    Vn = {
        key: 1,
        class: "task-details"
    },
    Gn = {
        class: "error-info"
    },
    Wn = {
        class: "file-system-section"
    },
    Kn = {
        class: "file-tree"
    },
    Jn = {
        class: "file-path"
    },
    Qn = {
        class: "file-type"
    },
    Yn = {
        key: 0,
        class: "empty-indicator"
    },
    Zn = l(B({
        __name: "CooperationCanvas",
        props: {
            project: {}
        },
        emits: ["ask"],
        setup(e, {
            emit: t
        }) {
            const l = e,
                s = t,
                {
                    isAsking: i
                } = aa(),
                d = a({}),
                u = a(!1),
                p = a(null),
                f = a([]),
                h = b((() => Object.values(d.value).filter((e => !["SUCCESS", "FAILURE", "ERROR", "REVOKED"].includes(e.status))).length)),
                y = (e, t = "") => {
                    const a = {};
                    if (!e) return a;
                    for (const [l, s] of Object.entries(e)) {
                        const e = t ? `${t}/${l}` : l;
                        a[e] = s, s && "object" == typeof s && "directory" === s.type && s.children && Object.assign(a, y(s.children, e))
                    }
                    return a
                },
                x = () => {
                    var e, t;
                    const a = null == (t = null == (e = l.project) ? void 0 : e.session_state) ? void 0 : t.file_tree_root_dir;
                    if (a && a.children) return y(a.children);
                    return y({
                        temp: {
                            type: "directory",
                            children: {}
                        },
                        workspace: {
                            type: "directory",
                            children: {}
                        }
                    })
                },
                w = e => "directory" === e.type && (!e.children || 0 === Object.keys(e.children).length),
                C = a([]),
                S = a(!1),
                E = async () => {
                    if (!S.value && 0 !== C.value.length) {
                        S.value = !0;
                        try {
                            await (async () => i.value ? new Promise((e => {
                                let t = !1;
                                const a = k(i, (l => {
                                    l || t || (t = !0, a(), e())
                                }), {
                                    immediate: !1
                                });
                                i.value || t || (t = !0, a(), e())
                            })) : Promise.resolve())();
                            const e = [...C.value];
                            if (C.value = [], e.length > 0) {
                                const t = e.flat();
                                s("ask", t)
                            }
                        } catch (e) {} finally {
                            S.value = !1
                        }
                    }
                },
                A = async e => {
                    C.value.push(e), await E()
                };

            function T() {
                f.value = [], p.value = null, u.value = !1
            }
            const M = async e => {
                    if (0 !== e.length) {
                        f.value = [...e], p.value && p.value.abort(), p.value = new AbortController, u.value = !0;
                        try {
                            await ea("/api/cooperation/async_tool_tasks_status", {
                                task_ids: f.value
                            }, (e => {
                                e.tasks && (Object.assign(d.value, e.tasks), P(e.tasks), R(e.tasks)), "All celery task status complete" === e.message && T()
                            }), p.value.signal)
                        } catch (t) {
                            "AbortError" === t.name || T()
                        }
                    }
                },
                R = e => {
                    for (const [t, a] of Object.entries(e)) "SUCCESS" !== a.status || a.processed || (L(t, a), a.processed = !0)
                },
                L = async (e, t) => {},
                P = e => {
                    var t, a;
                    const s = null == (a = null == (t = l.project) ? void 0 : t.session_state) ? void 0 : a.file_tree_root_dir;
                    if (!s || !s.waiting_for_created) return;
                    const n = [],
                        o = [];
                    if (l.project.session_state) {
                        for (const [t, a] of Object.entries(e))
                            if ("SUCCESS" === a.status) {
                                for (const [e, l] of Object.entries(s.waiting_for_created))
                                    if (l.task_id === t) {
                                        const l = e.split("/").filter(Boolean);
                                        let o = s.children;
                                        for (let e = 0; e < l.length - 1; e++) {
                                            const t = l[e];
                                            o[t] || (o[t] = {
                                                type: "directory",
                                                children: {}
                                            }), o = o[t].children
                                        }
                                        const i = l[l.length - 1],
                                            r = a.result || `File created by task ${t}`;
                                        o[i] = {
                                            type: "file",
                                            content: {
                                                text: r
                                            },
                                            task_id: t,
                                            created_by: "async_task"
                                        }, n.push(e), delete s.waiting_for_created[e]
                                    }
                            } else if ("FAILURE" === a.status)
                            for (const [e, l] of Object.entries(s.waiting_for_created)) l.task_id === t && (o.push(e), delete s.waiting_for_created[e]);
                        (n.length > 0 || o.length > 0) && (l.project.session_state = { ...l.project.session_state
                        })
                    }
                    if (n.length > 0) {
                        const e = [{
                            type: "text",
                            text: `Files have been created successfully: ${n.join(", ")}`,
                            hide_in_ui: !1
                        }];
                        A(e)
                    }
                    if (o.length > 0) {
                        const e = [{
                            type: "text",
                            text: `File creation failed for: ${o.join(", ")}.`,
                            hide_in_ui: !1
                        }];
                        A(e)
                    }
                },
                q = () => {
                    var e, t;
                    const a = null == (t = null == (e = l.project) ? void 0 : e.session_state) ? void 0 : t.file_tree_root_dir;
                    if (!a || !a.waiting_for_created) return [];
                    return Object.values(a.waiting_for_created).map((e => e.task_id)).filter(Boolean).filter((e => {
                        const t = d.value[e];
                        return !t || !["SUCCESS", "FAILURE", "ERROR", "REVOKED"].includes(t.status)
                    }))
                };
            return k((() => {
                var e;
                return null == (e = l.project) ? void 0 : e.session_state
            }), (() => {
                const e = q();
                let t = [],
                    a = !1;
                u.value ? (t = e.filter((e => !f.value.includes(e))), a = t.length > 0) : (a = !0, t = e), a && M(e)
            }), {
                deep: !0,
                flush: "post"
            }), $((() => {
                const e = q();
                e.length > 0 && M(e)
            })), I((() => {
                p.value && p.value.abort()
            })), (e, t) => (r(), n("div", In, [c("div", Rn, [t[0] || (t[0] = c("h2", null, "Cooperation Agent Canvas", -1)), d.value ? (r(), n("div", Ln, [j(" Active Tasks: " + v(Object.keys(d.value).length) + " ", 1), h.value > 0 ? (r(), n("span", Pn, " (" + v(h.value) + " pending) ", 1)) : o("", !0)])) : o("", !0)]), c("div", qn, [d.value && Object.keys(d.value).length > 0 ? (r(), n("div", On, [t[3] || (t[3] = c("h3", null, "Task Status", -1)), c("div", Bn, [(r(!0), n(m, null, g(d.value, ((e, a) => {
                return r(), n("div", {
                    key: a,
                    class: _(["task-item", (s = e.status, {
                        PENDING: "status-pending",
                        RUNNING: "status-running",
                        SUCCESS: "status-success",
                        FAILURE: "status-failure",
                        ERROR: "status-error",
                        REVOKED: "status-revoked"
                    }[s] || "status-unknown")])
                }, [c("div", Un, [c("span", Fn, v(a.substring(0, 8)) + "...", 1), c("span", Dn, v(e.status), 1), c("span", Nn, v((l = e.timestamp, new Date(l).toLocaleTimeString())), 1)]), "SUCCESS" === e.status && e.result_preview ? (r(), n("div", zn, [c("div", Hn, [t[1] || (t[1] = c("strong", null, "Result:", -1)), c("pre", null, v(e.result_preview), 1)])])) : o("", !0), "FAILURE" === e.status && e.error ? (r(), n("div", Vn, [c("div", Gn, [t[2] || (t[2] = c("strong", null, "Error:", -1)), c("pre", null, v(e.error), 1)])])) : o("", !0)], 2);
                var l, s
            })), 128))])])) : o("", !0), c("div", Wn, [t[4] || (t[4] = c("h3", null, "Workspace", -1)), c("div", Kn, [(r(!0), n(m, null, g(x(), ((e, t) => (r(), n("div", {
                key: t,
                class: _(["file-item", {
                    "empty-folder": "directory" === e.type && w(e)
                }])
            }, [c("span", Jn, v(t), 1), c("span", Qn, v(e.type), 1), "directory" === e.type && w(e) ? (r(), n("span", Yn, "(empty)")) : o("", !0)], 2)))), 128))])])])]))
        }
    }), [
        ["__scopeId", "data-v-45810d93"]
    ]),
    Xn = {
        class: "video-canvas-enhanced"
    },
    eo = l({
        __name: "video_canvas",
        props: {
            tool_call: Object,
            message: Object,
            inGlobalCanvas: Boolean,
            project: Object
        },
        emits: ["ready", "error", "export", "save"],
        setup(e, {
            expose: t,
            emit: l
        }) {
            const o = e,
                {
                    tool_call: c,
                    message: d,
                    inGlobalCanvas: p,
                    project: v
                } = s(o),
                _ = a(null),
                m = l;

            function g(e) {
                m("ready", e)
            }

            function f(e) {
                m("error", e)
            }
            async function h({
                blobs: e,
                options: t
            }) {
                if (e.length > 0) {
                    const t = e[0],
                        a = URL.createObjectURL(t),
                        l = document.createElement("a");
                    l.href = a, l.download = `video-${null==v?void 0:v.value.id}-${Date.now()}.mp4`, document.body.appendChild(l), l.click(), document.body.removeChild(l), URL.revokeObjectURL(a)
                }
                m("export", {
                    blobs: e,
                    options: t
                })
            }

            function y({
                projectId: e
            }) {
                m("save", {
                    projectId: e
                })
            }

            function b(e) {}

            function k(e) {}

            function x(e) {}
            return $((() => {
                window.addEventListener("exportProgress", b), window.addEventListener("exportComplete", k), window.addEventListener("exportError", x)
            })), D((() => {
                window.removeEventListener("exportProgress", b), window.removeEventListener("exportComplete", k), window.removeEventListener("exportError", x)
            })), t({
                getEditor: () => {
                    var e;
                    return null == (e = _.value) ? void 0 : e.getEditor()
                },
                save: () => {
                    var e;
                    return null == (e = _.value) ? void 0 : e.save()
                },
                export: () => {
                    var e;
                    return null == (e = _.value) ? void 0 : e.export()
                },
                getContextDetails: () => {
                    var e;
                    return null == (e = _.value) ? void 0 : e.getContextDetails()
                }
            }), (e, t) => (r(), n("div", Xn, [u(ca, {
                ref_key: "editorRef",
                ref: _,
                project: i(v),
                onReady: g,
                onError: f,
                onExport: h,
                onSave: y
            }, null, 8, ["project"])]))
        }
    }, [
        ["__scopeId", "data-v-e1607ee1"]
    ]),
    to = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "8",
        height: "9",
        viewBox: "0 0 8 9",
        fill: "none"
    };
const ao = {
        render: function(e, t) {
            return r(), n("svg", to, t[0] || (t[0] = [c("path", {
                d: "M7.75 4.33003L0.250001 8.66016L0.25 -9.53674e-05L7.75 4.33003Z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    lo = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none"
    };
const so = {
        render: function(e, t) {
            return r(), n("svg", lo, t[0] || (t[0] = [c("path", {
                d: "M2.99951 10.7647V15.353C2.99951 15.7897 3.18389 16.2087 3.51207 16.5176C3.84026 16.8265 4.28539 17 4.74951 17H15.2495C15.7136 17 16.1588 16.8265 16.487 16.5176C16.8151 16.2087 16.9995 15.7897 16.9995 15.353V10.7647M13.4995 10.4118L9.99951 13.7059M9.99951 13.7059L6.49951 10.4118M9.99951 13.7059V3",
                stroke: "currentColor",
                "stroke-width": "1.5",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    no = {
        class: "tab-content"
    },
    oo = {
        key: 0,
        class: "loading-container"
    },
    io = {
        key: 1,
        class: "error-container"
    },
    ro = {
        class: "error-message"
    },
    co = {
        key: 2,
        class: "file-browser"
    },
    uo = {
        key: 0,
        class: "file-tree-panel"
    },
    po = {
        class: "file-tree"
    },
    vo = ["onClick"],
    _o = {
        class: "directory-header"
    },
    mo = ["title"],
    go = {
        key: 0,
        class: "directory-children"
    },
    fo = ["onClick"],
    ho = {
        class: "directory-header"
    },
    yo = ["title"],
    bo = {
        key: 0,
        class: "directory-children"
    },
    ko = ["onClick"],
    xo = {
        class: "directory-header"
    },
    wo = ["title"],
    jo = {
        key: 0,
        class: "directory-children"
    },
    Co = ["onClick"],
    So = ["title"],
    Eo = ["onClick"],
    Ao = ["title"],
    To = ["onClick"],
    Mo = ["title"],
    $o = ["onClick"],
    Io = {
        class: "file-item-content"
    },
    Ro = ["title"],
    Lo = {
        key: 0,
        class: "empty-directory"
    },
    Po = {
        class: "empty-icon"
    },
    qo = {
        class: "download-section"
    },
    Oo = {
        class: "file-preview-panel"
    },
    Bo = {
        key: 0,
        class: "file-preview"
    },
    Uo = {
        class: "file-header"
    },
    Fo = {
        class: "file-header-left"
    },
    Do = {
        class: "file-title"
    },
    No = {
        class: "file-name"
    },
    zo = {
        class: "file-meta"
    },
    Ho = {
        class: "file-size"
    },
    Vo = {
        class: "file-date"
    },
    Go = {
        class: "file-actions"
    },
    Wo = {
        class: "file-content"
    },
    Ko = {
        key: 0,
        class: "text-content"
    },
    Jo = {
        key: 1,
        class: "image-content"
    },
    Qo = ["src", "alt"],
    Yo = {
        key: 2,
        class: "binary-content"
    },
    Zo = {
        class: "binary-message"
    },
    Xo = {
        key: 1,
        class: "no-preview"
    },
    ei = {
        class: "no-preview-content"
    },
    ti = l({
        __name: "CodeSandboxLightFilesTab",
        props: {
            loading: Boolean,
            error: String,
            files: Array,
            project: {
                type: Object,
                required: !1
            }
        },
        emits: ["refresh", "preview-file", "download-all"],
        setup(e, {
            emit: t
        }) {
            const l = e,
                s = na(),
                d = a({}),
                p = a(null),
                f = a(!1),
                h = b((() => {
                    if (!l.files || !Array.isArray(l.files)) return {};
                    const e = {},
                        t = (e, a, l) => {
                            if (1 === a.length) {
                                const t = a[0];
                                e[t] = { ...l,
                                    name: t,
                                    path: l.file_path,
                                    type: "file"
                                }
                            } else {
                                const s = a[0],
                                    n = a.slice(1);
                                e[s] || (e[s] = {
                                    type: "directory",
                                    children: {}
                                }), t(e[s].children, n, l)
                            }
                        };
                    return l.files.forEach((a => {
                        const l = a.file_path.split("/").filter(Boolean);
                        t(e, l, a)
                    })), e
                })),
                y = e => {
                    d.value[e] = !d.value[e]
                },
                k = e => {
                    "file" === e.type && (p.value = e)
                },
                w = async e => {
                    if (e && e.content) try {
                        const t = new Blob([e.content], {
                                type: A(e.name || e.file_path)
                            }),
                            a = window.URL.createObjectURL(t),
                            l = document.createElement("a");
                        l.style.display = "none", l.href = a, l.download = e.name || S(e.file_path), document.body.appendChild(l), l.click(), window.URL.revokeObjectURL(a), document.body.removeChild(l), s.success("File downloaded successfully")
                    } catch (t) {
                        s.error("Failed to download file. Please try again.")
                    } else s.error("No file content to download")
                },
                C = async () => {
                    var e, t;
                    if ((null == (e = l.project) ? void 0 : e.id) && l.files && 0 !== l.files.length) {
                        f.value = !0;
                        try {
                            const e = await fetch(`/api/code_sandbox_light/download/${l.project.id}`, {
                                method: "GET",
                                credentials: "include"
                            });
                            if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                            const a = e.headers.get("content-disposition"),
                                n = a ? null == (t = a.split("filename=")[1]) ? void 0 : t.replace(/"/g, "") : `project_${l.project.id.slice(0,8)}.zip`,
                                o = await e.blob(),
                                i = window.URL.createObjectURL(o),
                                r = document.createElement("a");
                            r.style.display = "none", r.href = i, r.download = n, document.body.appendChild(r), r.click(), window.URL.revokeObjectURL(i), document.body.removeChild(r), s.success("ZIP file downloaded successfully")
                        } catch (a) {
                            s.error("Failed to download ZIP file. Please try again.")
                        } finally {
                            f.value = !1
                        }
                    } else s.error("No project or files to download")
                },
                S = e => {
                    if (!e) return "untitled";
                    const t = e.split("/");
                    return t[t.length - 1] || e
                },
                A = e => {
                    var t;
                    if (!e) return "text/plain";
                    const a = (null == (t = e.split(".").pop()) ? void 0 : t.toLowerCase()) || "";
                    return ["txt", "md", "json", "js", "ts", "jsx", "tsx", "vue", "py", "java", "cpp", "c", "html", "css", "scss", "xml", "yaml", "yml", "log"].includes(a) ? "text/plain" : "jpg" === a || "jpeg" === a ? "image/jpeg" : "png" === a ? "image/png" : "gif" === a ? "image/gif" : "svg" === a ? "image/svg+xml" : "bmp" === a ? "image/bmp" : "webp" === a ? "image/webp" : "pdf" === a ? "application/pdf" : "json" === a ? "application/json" : "application/octet-stream"
                },
                T = e => {
                    var t;
                    if ("directory" === e.type) return Se;
                    const a = (null == (t = (e.name || S(e.file_path)).split(".").pop()) ? void 0 : t.toLowerCase()) || "";
                    return ["jpg", "jpeg", "png", "gif", "svg", "bmp", "webp"].includes(a) ? Te : ["js", "ts", "jsx", "tsx", "vue", "py", "java", "cpp", "c", "html", "css", "scss", "json", "xml", "yaml", "yml"].includes(a) ? Me : ["txt", "md", "pdf", "doc", "docx"].includes(a) ? da : Ae
                },
                M = e => {
                    var t;
                    if (!e) return !1;
                    const a = (null == (t = (e.name || S(e.file_path)).split(".").pop()) ? void 0 : t.toLowerCase()) || "";
                    return ["txt", "md", "json", "js", "ts", "jsx", "tsx", "vue", "py", "java", "cpp", "c", "html", "css", "scss", "xml", "yaml", "yml", "log"].includes(a)
                },
                $ = e => {
                    var t;
                    if (!e) return !1;
                    const a = (null == (t = (e.name || S(e.file_path)).split(".").pop()) ? void 0 : t.toLowerCase()) || "";
                    return ["jpg", "jpeg", "png", "gif", "svg", "bmp", "webp"].includes(a)
                },
                I = e => {
                    if (0 === e) return "0 B";
                    const t = Math.floor(Math.log(e) / Math.log(1024));
                    return parseFloat((e / Math.pow(1024, t)).toFixed(1)) + " " + ["B", "KB", "MB", "GB"][t]
                },
                R = e => {
                    if (!e) return "";
                    try {
                        const t = new Date(e);
                        return t.toLocaleDateString() + " " + t.toLocaleTimeString()
                    } catch {
                        return e
                    }
                };
            return (t, a) => {
                var s, b, S, A;
                return r(), n("div", no, [e.loading ? (r(), n("div", oo, [u(i(pa), {
                    size: "medium",
                    stroke: "#0f7fff"
                }), a[3] || (a[3] = c("div", {
                    class: "loading-text"
                }, "Loading files...", -1))])) : e.error ? (r(), n("div", io, [c("div", ro, v(e.error), 1), u(i(ua), {
                    onClick: a[0] || (a[0] = e => t.$emit("refresh")),
                    size: "small",
                    type: "primary"
                }, {
                    default: x((() => a[4] || (a[4] = [j(" Retry ")]))),
                    _: 1
                })])) : (r(), n("div", co, [h.value && Object.keys(h.value).length > 0 ? (r(), n("div", uo, [c("div", po, [(r(!0), n(m, null, g(h.value, ((e, t) => (r(), n("div", {
                    key: t,
                    class: "tree-item"
                }, ["directory" === e.type ? (r(), n("div", {
                    key: 0,
                    class: "directory-item",
                    onClick: e => y(t)
                }, [c("div", _o, [u(i(ao), {
                    class: _(["expand-icon", {
                        expanded: d.value[t]
                    }])
                }, null, 8, ["class"]), u(i(Se), {
                    class: "file-icon"
                }), c("span", {
                    class: "file-name",
                    title: t
                }, v(t), 9, mo)]), d.value[t] ? (r(), n("div", go, [(r(!0), n(m, null, g(e.children, ((e, a) => (r(), n("div", {
                    key: a,
                    class: "tree-item child-item"
                }, ["directory" === e.type ? (r(), n("div", {
                    key: 0,
                    class: "directory-item",
                    onClick: N((e => y(`${t}/${a}`)), ["stop"])
                }, [c("div", ho, [u(i(ao), {
                    class: _(["expand-icon", {
                        expanded: d.value[`${t}/${a}`]
                    }])
                }, null, 8, ["class"]), u(i(Se), {
                    class: "file-icon"
                }), c("span", {
                    class: "file-name",
                    title: a
                }, v(a), 9, yo)]), d.value[`${t}/${a}`] ? (r(), n("div", bo, [(r(!0), n(m, null, g(e.children, ((e, l) => (r(), n("div", {
                    key: l,
                    class: "tree-item child-item"
                }, ["directory" === e.type ? (r(), n("div", {
                    key: 0,
                    class: "directory-item",
                    onClick: N((e => y(`${t}/${a}/${l}`)), ["stop"])
                }, [c("div", xo, [u(i(ao), {
                    class: _(["expand-icon", {
                        expanded: d.value[`${t}/${a}/${l}`]
                    }])
                }, null, 8, ["class"]), u(i(Se), {
                    class: "file-icon"
                }), c("span", {
                    class: "file-name",
                    title: l
                }, v(l), 9, wo)]), d.value[`${t}/${a}/${l}`] ? (r(), n("div", jo, [(r(!0), n(m, null, g(e.children, (e => (r(), n("div", {
                    key: e.file_path || e.name,
                    class: "tree-item child-item"
                }, ["file" === e.type ? (r(), n("div", {
                    key: 0,
                    class: "file-item-content",
                    onClick: N((t => k(e)), ["stop"])
                }, [(r(), E(z(T(e)), {
                    class: "file-icon"
                })), c("span", {
                    class: "file-name",
                    title: e.name
                }, v(e.name), 9, So)], 8, Co)) : o("", !0)])))), 128))])) : o("", !0)], 8, ko)) : (r(), n("div", {
                    key: 1,
                    class: "file-item-content",
                    onClick: N((t => k(e)), ["stop"])
                }, [(r(), E(z(T(e)), {
                    class: "file-icon"
                })), c("span", {
                    class: "file-name",
                    title: e.name
                }, v(e.name), 9, Ao)], 8, Eo))])))), 128))])) : o("", !0)], 8, fo)) : (r(), n("div", {
                    key: 1,
                    class: "file-item-content",
                    onClick: N((t => k(e)), ["stop"])
                }, [(r(), E(z(T(e)), {
                    class: "file-icon"
                })), c("span", {
                    class: "file-name",
                    title: e.name
                }, v(e.name), 9, Mo)], 8, To))])))), 128))])) : o("", !0)], 8, vo)) : (r(), n("div", {
                    key: 1,
                    class: "tree-item file-item",
                    onClick: t => k(e)
                }, [c("div", Io, [(r(), E(z(T(e)), {
                    class: "file-icon"
                })), c("span", {
                    class: "file-name",
                    title: e.name
                }, v(e.name), 9, Ro)])], 8, $o))])))), 128)), h.value && 0 !== Object.keys(h.value).length ? o("", !0) : (r(), n("div", Lo, [c("div", Po, [u(Ee)]), a[5] || (a[5] = c("div", {
                    class: "empty-text"
                }, "No Data Available", -1))]))]), c("div", qo, [u(i(ua), {
                    type: "default",
                    class: "download-button",
                    loading: f.value,
                    disabled: f.value || !(null == (s = l.project) ? void 0 : s.id) || !l.files || 0 === l.files.length,
                    onClick: C
                }, {
                    default: x((() => [u(i(so), {
                        class: "download-icon"
                    }), j(" " + v(f.value ? "Preparing..." : "Download files"), 1)])),
                    _: 1
                }, 8, ["loading", "disabled"])])])) : o("", !0), c("div", Oo, [p.value ? (r(), n("div", Bo, [c("div", Uo, [c("div", Fo, [c("div", Do, [(r(), E(z(T(p.value)), {
                    class: "file-icon"
                })), c("span", No, v(p.value.name), 1)]), c("div", zo, [c("span", Ho, v(I(p.value.size || (null == (b = p.value.content) ? void 0 : b.length) || 0)), 1), c("span", Vo, v(R(p.value.modification_time || (null == (S = p.value.metadata) ? void 0 : S.updated_at))), 1)])]), c("div", Go, [u(i(ua), {
                    size: "small",
                    type: "primary",
                    onClick: a[1] || (a[1] = e => w(p.value))
                }, {
                    default: x((() => [u(i(so), {
                        class: "action-icon"
                    })])),
                    _: 1
                })])]), c("div", Wo, [M(p.value) ? (r(), n("div", Ko, [c("pre", null, [c("code", null, v(p.value.content || "Loading content..."), 1)])])) : $(p.value) ? (r(), n("div", Jo, [c("img", {
                    src: (A = p.value, A.url || `/api/files/${A.path}` || ""),
                    alt: p.value.name
                }, null, 8, Qo)])) : (r(), n("div", Yo, [c("div", Zo, [u(i(Ae), {
                    class: "binary-icon"
                }), a[7] || (a[7] = c("p", null, "Binary file cannot be previewed", -1)), u(i(ua), {
                    type: "primary",
                    onClick: a[2] || (a[2] = e => w(p.value))
                }, {
                    default: x((() => a[6] || (a[6] = [j(" Download to view ")]))),
                    _: 1
                })])]))])])) : (r(), n("div", Xo, [c("div", ei, [u(Ee, {
                    class: "preview-icon"
                }), a[8] || (a[8] = c("p", null, "No preview files yet", -1))])]))])]))])
            }
        }
    }, [
        ["__scopeId", "data-v-a45a3b5b"]
    ]),
    ai = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 20 20"
    };
const li = {
        render: function(e, t) {
            return r(), n("svg", ai, t[0] || (t[0] = [c("g", {
                fill: "none"
            }, [c("path", {
                d: "M5.845 4C5.364 4 5 4.374 5 4.804V6H4V4.804C4 3.794 4.84 3 5.845 3h10.31C17.16 3 18 3.794 18 4.804v7.392c0 1.01-.84 1.804-1.845 1.804H10v-1h6.155c.481 0 .845-.374.845-.804V4.804c0-.43-.364-.804-.845-.804H5.845zM10 12h2.5a.5.5 0 0 0 0-1H10v1zm-5 3a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1H5zM2 8.5A1.5 1.5 0 0 1 3.5 7h4A1.5 1.5 0 0 1 9 8.5v8A1.5 1.5 0 0 1 7.5 18h-4A1.5 1.5 0 0 1 2 16.5v-8zM3.5 8a.5.5 0 0 0-.5.5v8a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5v-8a.5.5 0 0 0-.5-.5h-4z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    si = {
        class: "preview-tab"
    },
    ni = {
        class: "preview-container"
    },
    oi = {
        key: 0,
        class: "empty-state"
    },
    ii = {
        key: 0,
        class: "carousel-container"
    },
    ri = {
        class: "carousel-wrapper"
    },
    ci = {
        key: 1,
        class: "empty-default"
    },
    di = {
        class: "empty-text"
    },
    ui = {
        key: 1,
        class: "preview-content"
    },
    pi = {
        class: "preview-header"
    },
    vi = {
        class: "preview-info"
    },
    _i = {
        class: "preview-controls"
    },
    mi = {
        class: "file-tabs"
    },
    gi = ["onClick"],
    fi = {
        class: "preview-actions"
    },
    hi = ["title"],
    yi = ["title"],
    bi = {
        class: "iframe-container"
    },
    ki = {
        key: 0,
        class: "iframe-loading"
    },
    xi = {
        class: "loading-text"
    },
    wi = ["src"],
    ji = l({
        __name: "CodeSandboxLightPreviewTab",
        props: {
            files: {
                type: Array,
                default: () => []
            },
            project: {
                type: Object,
                required: !0
            },
            isAsking: {
                type: Boolean,
                default: !1
            }
        },
        setup(e, {
            expose: t
        }) {
            const {
                t: l
            } = A(), s = e, d = a(null), p = a(null), f = a(!1), h = a("full"), y = e => e.split("/").map((e => encodeURIComponent(e))).join("/"), x = a(0), j = a(null), C = a("undefined" != typeof window ? window.innerWidth : 1024), S = b((() => C.value < 768 ? 96 : 64)), E = () => {
                "undefined" != typeof window && (C.value = window.innerWidth)
            };
            $((() => {
                "undefined" != typeof window && (C.value = window.innerWidth, window.addEventListener("resize", E))
            }));
            const T = [l("components.code_sandbox_light.preview_tab.dev_step_1"), l("components.code_sandbox_light.preview_tab.dev_step_2"), l("components.code_sandbox_light.preview_tab.dev_step_3"), l("components.code_sandbox_light.preview_tab.dev_step_4"), l("components.code_sandbox_light.preview_tab.dev_step_5"), l("components.code_sandbox_light.preview_tab.dev_step_6"), l("components.code_sandbox_light.preview_tab.dev_step_7"), l("components.code_sandbox_light.preview_tab.dev_step_8")],
                M = b((() => s.files.filter((e => {
                    const t = e.file_path.toLowerCase().split(".").pop();
                    return "html" === t || "htm" === t
                })).sort(((e, t) => "index.html" === e.file_path.toLowerCase() ? -1 : "index.html" === t.file_path.toLowerCase() ? 1 : e.file_path.localeCompare(t.file_path))))),
                I = b((() => {
                    const e = M.value.map((e => ({
                        label: e.file_path,
                        value: e.file_path
                    })));
                    return s.files.length > 0 && !M.value.some((e => "index.html" === e.file_path.toLowerCase())) && e.unshift({
                        label: l("components.code_sandbox_light.preview_tab.index_html_default"),
                        value: ""
                    }), e
                })),
                R = b((() => {
                    if (p.value && "" !== p.value) return M.value.find((e => e.file_path === p.value));
                    const e = M.value.find((e => "index.html" === e.file_path.toLowerCase()));
                    return e || (M.value[0] || null)
                }));
            k(M, (e => {
                var t;
                if (e.length > 0 && !p.value) {
                    const a = e.find((e => "index.html" === e.file_path.toLowerCase()));
                    if (a) p.value = a.file_path;
                    else {
                        const a = e.filter((e => !e.file_path.toLowerCase().includes("test")));
                        p.value = (null == (t = a[0] || e[0]) ? void 0 : t.file_path) || ""
                    }
                }
            }), {
                immediate: !0
            });
            const L = b((() => {
                var e, t, a;
                if (!(null == (e = s.project) ? void 0 : e.id)) return "";
                const l = new URLSearchParams,
                    n = null == (a = null == (t = s.project) ? void 0 : t.session_state) ? void 0 : a.canvas_history_id;
                n && l.set("canvas_history_id", n);
                const o = l.toString();
                if (R.value) {
                    const e = y(R.value.file_path);
                    return `/api/code_sandbox_light/preview/${s.project.id}/${e}${o?`?${o}`:""}`
                }
                return `/api/code_sandbox_light/preview/${s.project.id}${o?`?${o}`:""}`
            }));
            k((() => {
                var e, t, a;
                return [p.value, null == (e = R.value) ? void 0 : e.content, null == (a = null == (t = s.project) ? void 0 : t.session_state) ? void 0 : a.canvas_history_id]
            }), ((e, t) => {
                e.some(((e, a) => e !== (null == t ? void 0 : t[a]))) && P()
            }), {
                deep: !1
            }), k((() => s.files.map((e => ({
                path: e.file_path,
                content: e.content
            })))), (() => {
                setTimeout((() => {
                    P()
                }), 500)
            }), {
                deep: !0
            }), k(L, (e => {
                f.value = !!e
            }), {
                immediate: !0
            });
            const P = () => {
                    var e, t, a;
                    if (d.value && (null == (e = s.project) ? void 0 : e.id)) {
                        f.value = !0;
                        const e = new URLSearchParams;
                        e.set("_t", Date.now().toString());
                        const l = null == (a = null == (t = s.project) ? void 0 : t.session_state) ? void 0 : a.canvas_history_id;
                        l && e.set("canvas_history_id", l);
                        const n = e.toString(),
                            o = d.value;
                        if (R.value) {
                            const e = y(R.value.file_path);
                            o.src = `/api/code_sandbox_light/preview/${s.project.id}/${e}?${n}`
                        } else o.src = `/api/code_sandbox_light/preview/${s.project.id}?${n}`
                    }
                },
                q = () => {
                    var e, t, a;
                    if (null == (e = s.project) ? void 0 : e.id) {
                        const e = new URLSearchParams,
                            l = null == (a = null == (t = s.project) ? void 0 : t.session_state) ? void 0 : a.canvas_history_id;
                        l && e.set("canvas_history_id", l);
                        const n = e.toString();
                        let o;
                        if (R.value) {
                            const e = y(R.value.file_path);
                            o = `/api/code_sandbox_light/preview/${s.project.id}/${e}${n?`?${n}`:""}`
                        } else o = `/api/code_sandbox_light/preview/${s.project.id}${n?`?${n}`:""}`;
                        window.open(o, "_blank")
                    }
                },
                O = () => {
                    f.value = !1
                },
                B = () => {
                    const e = ["full", "tablet", "mobile"],
                        t = (e.indexOf(h.value) + 1) % e.length;
                    h.value = e[t]
                },
                U = () => {
                    switch (h.value) {
                        case "mobile":
                            return l("components.code_sandbox_light.preview_tab.mobile_width");
                        case "tablet":
                            return l("components.code_sandbox_light.preview_tab.tablet_width");
                        default:
                            return l("components.code_sandbox_light.preview_tab.full_width")
                    }
                },
                F = () => {
                    j.value || (j.value = va((() => {
                        x.value = (x.value + 1) % T.length
                    }), 2e3))
                },
                N = () => {
                    j.value && (clearInterval(j.value), j.value = null)
                };
            return k((() => s.isAsking), (e => {
                e && 0 === M.value.length ? F() : N()
            }), {
                immediate: !0
            }), k(M, (e => {
                e.length > 0 ? N() : s.isAsking && F()
            })), D((() => {
                N(), "undefined" != typeof window && window.removeEventListener("resize", E)
            })), t({
                reloadPreview: P
            }), (t, a) => (r(), n("div", si, [c("div", ni, [0 === M.value.length ? (r(), n("div", oi, [e.isAsking ? (r(), n("div", ii, [c("div", ri, [c("div", {
                class: "carousel-track",
                style: w({
                    transform: `translateY(${-x.value*S.value}px)`
                })
            }, [(r(!0), n(m, null, g([...T, ...T.slice(0, 3)], ((e, t) => (r(), n("div", {
                key: t,
                class: _(["carousel-item", {
                    active: t === x.value,
                    prev: t === x.value - 1 || 0 === x.value && t === T.length - 1,
                    next: t === x.value + 1 || x.value === T.length - 1 && 0 === t
                }])
            }, v(e), 3)))), 128))], 4)])])) : (r(), n("div", ci, [u(i($e), {
                class: "empty-icon"
            }), c("div", di, v(i(l)("components.code_sandbox_light.preview_tab.computer_setup_success")), 1)]))])) : (r(), n("div", ui, [c("div", pi, [c("div", vi, [c("div", _i, [c("div", mi, [(r(!0), n(m, null, g(I.value, (e => (r(), n("button", {
                key: e.value,
                class: _(["file-tab", {
                    "file-tab-active": p.value === e.value
                }]),
                onClick: t => p.value = e.value
            }, v(e.label), 11, gi)))), 128))])])]), c("div", fi, [c("div", {
                class: "action-button width-toggle",
                onClick: B,
                title: U()
            }, [u(i(li))], 8, hi), c("div", {
                class: "action-button",
                onClick: q,
                title: i(l)("components.code_sandbox_light.preview_tab.open_in_new_window")
            }, [u(i(Ie))], 8, yi)])]), c("div", bi, [f.value ? (r(), n("div", ki, [u(i(pa), {
                size: "medium",
                stroke: "#0f7fff"
            }), c("div", xi, v(i(l)("components.code_sandbox_light.preview_tab.loading_preview")), 1)])) : o("", !0), c("div", {
                class: _(["iframe-wrapper", `width-${h.value}`])
            }, [c("iframe", {
                ref_key: "previewIframe",
                ref: d,
                src: L.value,
                class: _(["preview-iframe", {
                    "iframe-hidden": f.value
                }]),
                onLoad: O
            }, null, 42, wi)], 2)])]))])]))
        }
    }, [
        ["__scopeId", "data-v-35c90f22"]
    ]);
var Ci, Si, Ei, Ai, Ti, Mi = function(e, t) {
    return Object.defineProperty ? Object.defineProperty(e, "raw", {
        value: t
    }) : e.raw = t, e
};
(Si = Ci || (Ci = {}))[Si.EOS = 0] = "EOS", Si[Si.Text = 1] = "Text", Si[Si.Incomplete = 2] = "Incomplete", Si[Si.ESC = 3] = "ESC", Si[Si.Unknown = 4] = "Unknown", Si[Si.SGR = 5] = "SGR", Si[Si.OSCURL = 6] = "OSCURL";
class $i {
    constructor() {
        this.VERSION = "6.0.6", this.setup_palettes(), this._use_classes = !1, this.bold = !1, this.faint = !1, this.italic = !1, this.underline = !1, this.fg = this.bg = null, this._buffer = "", this._url_allowlist = {
            http: 1,
            https: 1
        }, this._escape_html = !0, this.boldStyle = "font-weight:bold", this.faintStyle = "opacity:0.7", this.italicStyle = "font-style:italic", this.underlineStyle = "text-decoration:underline"
    }
    set use_classes(e) {
        this._use_classes = e
    }
    get use_classes() {
        return this._use_classes
    }
    set url_allowlist(e) {
        this._url_allowlist = e
    }
    get url_allowlist() {
        return this._url_allowlist
    }
    set escape_html(e) {
        this._escape_html = e
    }
    get escape_html() {
        return this._escape_html
    }
    set boldStyle(e) {
        this._boldStyle = e
    }
    get boldStyle() {
        return this._boldStyle
    }
    set faintStyle(e) {
        this._faintStyle = e
    }
    get faintStyle() {
        return this._faintStyle
    }
    set italicStyle(e) {
        this._italicStyle = e
    }
    get italicStyle() {
        return this._italicStyle
    }
    set underlineStyle(e) {
        this._underlineStyle = e
    }
    get underlineStyle() {
        return this._underlineStyle
    }
    setup_palettes() {
        this.ansi_colors = [
            [{
                rgb: [0, 0, 0],
                class_name: "ansi-black"
            }, {
                rgb: [187, 0, 0],
                class_name: "ansi-red"
            }, {
                rgb: [0, 187, 0],
                class_name: "ansi-green"
            }, {
                rgb: [187, 187, 0],
                class_name: "ansi-yellow"
            }, {
                rgb: [0, 0, 187],
                class_name: "ansi-blue"
            }, {
                rgb: [187, 0, 187],
                class_name: "ansi-magenta"
            }, {
                rgb: [0, 187, 187],
                class_name: "ansi-cyan"
            }, {
                rgb: [255, 255, 255],
                class_name: "ansi-white"
            }],
            [{
                rgb: [85, 85, 85],
                class_name: "ansi-bright-black"
            }, {
                rgb: [255, 85, 85],
                class_name: "ansi-bright-red"
            }, {
                rgb: [0, 255, 0],
                class_name: "ansi-bright-green"
            }, {
                rgb: [255, 255, 85],
                class_name: "ansi-bright-yellow"
            }, {
                rgb: [85, 85, 255],
                class_name: "ansi-bright-blue"
            }, {
                rgb: [255, 85, 255],
                class_name: "ansi-bright-magenta"
            }, {
                rgb: [85, 255, 255],
                class_name: "ansi-bright-cyan"
            }, {
                rgb: [255, 255, 255],
                class_name: "ansi-bright-white"
            }]
        ], this.palette_256 = [], this.ansi_colors.forEach((e => {
            e.forEach((e => {
                this.palette_256.push(e)
            }))
        }));
        let e = [0, 95, 135, 175, 215, 255];
        for (let a = 0; a < 6; ++a)
            for (let t = 0; t < 6; ++t)
                for (let l = 0; l < 6; ++l) {
                    let s = {
                        rgb: [e[a], e[t], e[l]],
                        class_name: "truecolor"
                    };
                    this.palette_256.push(s)
                }
        let t = 8;
        for (let a = 0; a < 24; ++a, t += 10) {
            let e = {
                rgb: [t, t, t],
                class_name: "truecolor"
            };
            this.palette_256.push(e)
        }
    }
    escape_txt_for_html(e) {
        return this._escape_html ? e.replace(/[&<>"']/gm, (e => "&" === e ? "&amp;" : "<" === e ? "&lt;" : ">" === e ? "&gt;" : '"' === e ? "&quot;" : "'" === e ? "&#x27;" : void 0)) : e
    }
    append_buffer(e) {
        var t = this._buffer + e;
        this._buffer = t
    }
    get_next_packet() {
        var e = {
                kind: Ci.EOS,
                text: "",
                url: ""
            },
            t = this._buffer.length;
        if (0 == t) return e;
        var a = this._buffer.indexOf("");
        if (-1 == a) return e.kind = Ci.Text, e.text = this._buffer, this._buffer = "", e;
        if (a > 0) return e.kind = Ci.Text, e.text = this._buffer.slice(0, a), this._buffer = this._buffer.slice(a), e;
        if (0 == a) {
            if (t < 3) return e.kind = Ci.Incomplete, e;
            var l = this._buffer.charAt(1);
            if ("[" != l && "]" != l && "(" != l) return e.kind = Ci.ESC, e.text = this._buffer.slice(0, 1), this._buffer = this._buffer.slice(1), e;
            if ("[" == l) {
                this._csi_regex || (this._csi_regex = Ii(Ei || (Ei = Mi(["\n                        ^                           # beginning of line\n                                                    #\n                                                    # First attempt\n                        (?:                         # legal sequence\n                          [                      # CSI\n                          ([<-?]?)              # private-mode char\n                          ([d;]*)                    # any digits or semicolons\n                          ([ -/]?               # an intermediate modifier\n                          [@-~])                # the command\n                        )\n                        |                           # alternate (second attempt)\n                        (?:                         # illegal sequence\n                          [                      # CSI\n                          [ -~]*                # anything legal\n                          ([\0-:])              # anything illegal\n                        )\n                    "], ["\n                        ^                           # beginning of line\n                                                    #\n                                                    # First attempt\n                        (?:                         # legal sequence\n                          \\x1b\\[                      # CSI\n                          ([\\x3c-\\x3f]?)              # private-mode char\n                          ([\\d;]*)                    # any digits or semicolons\n                          ([\\x20-\\x2f]?               # an intermediate modifier\n                          [\\x40-\\x7e])                # the command\n                        )\n                        |                           # alternate (second attempt)\n                        (?:                         # illegal sequence\n                          \\x1b\\[                      # CSI\n                          [\\x20-\\x7e]*                # anything legal\n                          ([\\x00-\\x1f:])              # anything illegal\n                        )\n                    "]))));
                let t = this._buffer.match(this._csi_regex);
                if (null === t) return e.kind = Ci.Incomplete, e;
                if (t[4]) return e.kind = Ci.ESC, e.text = this._buffer.slice(0, 1), this._buffer = this._buffer.slice(1), e;
                "" != t[1] || "m" != t[3] ? e.kind = Ci.Unknown : e.kind = Ci.SGR, e.text = t[2];
                var s = t[0].length;
                return this._buffer = this._buffer.slice(s), e
            }
            if ("]" == l) {
                if (t < 4) return e.kind = Ci.Incomplete, e;
                if ("8" != this._buffer.charAt(2) || ";" != this._buffer.charAt(3)) return e.kind = Ci.ESC, e.text = this._buffer.slice(0, 1), this._buffer = this._buffer.slice(1), e;
                this._osc_st || (this._osc_st = function(e) {
                    let t = e.raw[0],
                        a = /^\s+|\s+\n|\s*#[\s\S]*?\n|\n/gm,
                        l = t.replace(a, "");
                    return new RegExp(l, "g")
                }(Ai || (Ai = Mi(["\n                        (?:                         # legal sequence\n                          (\\)                    # ESC                           |                           # alternate\n                          ()                      # BEL (what xterm did)\n                        )\n                        |                           # alternate (second attempt)\n                        (                           # illegal sequence\n                          [\0-]                 # anything illegal\n                          |                           # alternate\n                          [\b-]                 # anything illegal\n                          |                           # alternate\n                          [-]                 # anything illegal\n                        )\n                    "], ["\n                        (?:                         # legal sequence\n                          (\\x1b\\\\)                    # ESC \\\n                          |                           # alternate\n                          (\\x07)                      # BEL (what xterm did)\n                        )\n                        |                           # alternate (second attempt)\n                        (                           # illegal sequence\n                          [\\x00-\\x06]                 # anything illegal\n                          |                           # alternate\n                          [\\x08-\\x1a]                 # anything illegal\n                          |                           # alternate\n                          [\\x1c-\\x1f]                 # anything illegal\n                        )\n                    "])))), this._osc_st.lastIndex = 0; {
                    let t = this._osc_st.exec(this._buffer);
                    if (null === t) return e.kind = Ci.Incomplete, e;
                    if (t[3]) return e.kind = Ci.ESC, e.text = this._buffer.slice(0, 1), this._buffer = this._buffer.slice(1), e
                } {
                    let t = this._osc_st.exec(this._buffer);
                    if (null === t) return e.kind = Ci.Incomplete, e;
                    if (t[3]) return e.kind = Ci.ESC, e.text = this._buffer.slice(0, 1), this._buffer = this._buffer.slice(1), e
                }
                this._osc_regex || (this._osc_regex = Ii(Ti || (Ti = Mi(["\n                        ^                           # beginning of line\n                                                    #\n                        ]8;                    # OSC Hyperlink\n                        [ -:<-~]*       # params (excluding ;)\n                        ;                           # end of params\n                        ([!-~]{0,512})        # URL capture\n                        (?:                         # ST\n                          (?:\\)                  # ESC                           |                           # alternate\n                          (?:)                    # BEL (what xterm did)\n                        )\n                        ([ -~]+)              # TEXT capture\n                        ]8;;                   # OSC Hyperlink End\n                        (?:                         # ST\n                          (?:\\)                  # ESC                           |                           # alternate\n                          (?:)                    # BEL (what xterm did)\n                        )\n                    "], ["\n                        ^                           # beginning of line\n                                                    #\n                        \\x1b\\]8;                    # OSC Hyperlink\n                        [\\x20-\\x3a\\x3c-\\x7e]*       # params (excluding ;)\n                        ;                           # end of params\n                        ([\\x21-\\x7e]{0,512})        # URL capture\n                        (?:                         # ST\n                          (?:\\x1b\\\\)                  # ESC \\\n                          |                           # alternate\n                          (?:\\x07)                    # BEL (what xterm did)\n                        )\n                        ([\\x20-\\x7e]+)              # TEXT capture\n                        \\x1b\\]8;;                   # OSC Hyperlink End\n                        (?:                         # ST\n                          (?:\\x1b\\\\)                  # ESC \\\n                          |                           # alternate\n                          (?:\\x07)                    # BEL (what xterm did)\n                        )\n                    "]))));
                let a = this._buffer.match(this._osc_regex);
                if (null === a) return e.kind = Ci.ESC, e.text = this._buffer.slice(0, 1), this._buffer = this._buffer.slice(1), e;
                e.kind = Ci.OSCURL, e.url = a[1], e.text = a[2];
                s = a[0].length;
                return this._buffer = this._buffer.slice(s), e
            }
            if ("(" == l) return e.kind = Ci.Unknown, this._buffer = this._buffer.slice(3), e
        }
    }
    ansi_to_html(e) {
        this.append_buffer(e);
        for (var t = [];;) {
            var a = this.get_next_packet();
            if (a.kind == Ci.EOS || a.kind == Ci.Incomplete) break;
            a.kind != Ci.ESC && a.kind != Ci.Unknown && (a.kind == Ci.Text ? t.push(this.transform_to_html(this.with_state(a))) : a.kind == Ci.SGR ? this.process_ansi(a) : a.kind == Ci.OSCURL && t.push(this.process_hyperlink(a)))
        }
        return t.join("")
    }
    with_state(e) {
        return {
            bold: this.bold,
            faint: this.faint,
            italic: this.italic,
            underline: this.underline,
            fg: this.fg,
            bg: this.bg,
            text: e.text
        }
    }
    process_ansi(e) {
        let t = e.text.split(";");
        for (; t.length > 0;) {
            let e = t.shift(),
                a = parseInt(e, 10);
            if (isNaN(a) || 0 === a) this.fg = null, this.bg = null, this.bold = !1, this.faint = !1, this.italic = !1, this.underline = !1;
            else if (1 === a) this.bold = !0;
            else if (2 === a) this.faint = !0;
            else if (3 === a) this.italic = !0;
            else if (4 === a) this.underline = !0;
            else if (21 === a) this.bold = !1;
            else if (22 === a) this.faint = !1, this.bold = !1;
            else if (23 === a) this.italic = !1;
            else if (24 === a) this.underline = !1;
            else if (39 === a) this.fg = null;
            else if (49 === a) this.bg = null;
            else if (a >= 30 && a < 38) this.fg = this.ansi_colors[0][a - 30];
            else if (a >= 40 && a < 48) this.bg = this.ansi_colors[0][a - 40];
            else if (a >= 90 && a < 98) this.fg = this.ansi_colors[1][a - 90];
            else if (a >= 100 && a < 108) this.bg = this.ansi_colors[1][a - 100];
            else if ((38 === a || 48 === a) && t.length > 0) {
                let e = 38 === a,
                    l = t.shift();
                if ("5" === l && t.length > 0) {
                    let a = parseInt(t.shift(), 10);
                    a >= 0 && a <= 255 && (e ? this.fg = this.palette_256[a] : this.bg = this.palette_256[a])
                }
                if ("2" === l && t.length > 2) {
                    let a = parseInt(t.shift(), 10),
                        l = parseInt(t.shift(), 10),
                        s = parseInt(t.shift(), 10);
                    if (a >= 0 && a <= 255 && l >= 0 && l <= 255 && s >= 0 && s <= 255) {
                        let t = {
                            rgb: [a, l, s],
                            class_name: "truecolor"
                        };
                        e ? this.fg = t : this.bg = t
                    }
                }
            }
        }
    }
    transform_to_html(e) {
        let t = e.text;
        if (0 === t.length) return t;
        if (t = this.escape_txt_for_html(t), !(e.bold || e.italic || e.faint || e.underline || null !== e.fg || null !== e.bg)) return t;
        let a = [],
            l = [],
            s = e.fg,
            n = e.bg;
        e.bold && a.push(this._boldStyle), e.faint && a.push(this._faintStyle), e.italic && a.push(this._italicStyle), e.underline && a.push(this._underlineStyle), this._use_classes ? (s && ("truecolor" !== s.class_name ? l.push(`${s.class_name}-fg`) : a.push(`color:rgb(${s.rgb.join(",")})`)), n && ("truecolor" !== n.class_name ? l.push(`${n.class_name}-bg`) : a.push(`background-color:rgb(${n.rgb.join(",")})`))) : (s && a.push(`color:rgb(${s.rgb.join(",")})`), n && a.push(`background-color:rgb(${n.rgb})`));
        let o = "",
            i = "";
        return l.length && (o = ` class="${l.join(" ")}"`), a.length && (i = ` style="${a.join(";")}"`), `<span${i}${o}>${t}</span>`
    }
    process_hyperlink(e) {
        let t = e.url.split(":");
        return t.length < 1 ? "" : this._url_allowlist[t[0]] ? `<a href="${this.escape_txt_for_html(e.url)}">${this.escape_txt_for_html(e.text)}</a>` : ""
    }
}

function Ii(e, ...t) {
    let a = e.raw[0].replace(/^\s+|\s+\n|\s*#[\s\S]*?\n|\n/gm, "");
    return new RegExp(a)
}
const Ri = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 24 24"
};
const Li = {
        render: function(e, t) {
            return r(), n("svg", Ri, t[0] || (t[0] = [c("g", {
                fill: "none"
            }, [c("path", {
                d: "M2.22 2.22a.75.75 0 0 0-.073.976l.073.084l4.034 4.035a9.986 9.986 0 0 0-3.955 5.75a.75.75 0 0 0 1.455.364a8.49 8.49 0 0 1 3.58-5.034l1.81 1.81A4 4 0 0 0 14.8 15.86l5.919 5.92a.75.75 0 0 0 1.133-.977l-.073-.084l-6.113-6.114l.001-.002l-1.2-1.198l-2.87-2.87h.002L8.719 7.658l.001-.002l-1.133-1.13L3.28 2.22a.75.75 0 0 0-1.06 0zm7.984 9.045l3.535 3.536a2.5 2.5 0 0 1-3.535-3.535zM12 5.5c-1 0-1.97.148-2.889.425l1.237 1.236a8.503 8.503 0 0 1 9.899 6.272a.75.75 0 0 0 1.455-.363A10.003 10.003 0 0 0 12 5.5zm.195 3.51l3.801 3.8a4.003 4.003 0 0 0-3.801-3.8z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    Pi = {
        class: "tab-content"
    },
    qi = {
        class: "deploy-container",
        style: {
            gap: "16px"
        }
    },
    Oi = {
        class: "card"
    },
    Bi = {
        class: "vertical-space"
    },
    Ui = {
        class: "deploy-section"
    },
    Fi = {
        class: "deploy-info"
    },
    Di = ["disabled"],
    Ni = {
        key: 0,
        class: "deployment-result"
    },
    zi = {
        class: "deployment-info"
    },
    Hi = {
        class: "deployment-url"
    },
    Vi = {
        class: "label"
    },
    Gi = {
        key: 1,
        class: "files-to-deploy"
    },
    Wi = {
        class: "files-list"
    },
    Ki = {
        class: "file-icon"
    },
    Ji = {
        class: "file-info"
    },
    Qi = {
        class: "file-name"
    },
    Yi = {
        class: "file-size"
    },
    Zi = {
        style: {
            "margin-top": "24px"
        }
    },
    Xi = {
        class: "card"
    },
    er = {
        key: 0
    },
    tr = {
        class: "loading-container"
    },
    ar = {
        class: "loading-text"
    },
    lr = {
        key: 1
    },
    sr = {
        class: "api-key-status-card"
    },
    nr = {
        class: "vertical-space"
    },
    or = {
        class: "masked-key-display"
    },
    ir = {
        class: "label"
    },
    rr = {
        class: "masked-key"
    },
    cr = {
        key: 0,
        class: "validation-message"
    },
    dr = {
        class: "label"
    },
    ur = ["disabled"],
    pr = {
        key: 2
    },
    vr = {
        class: "setup-instructions"
    },
    _r = {
        class: "form-label"
    },
    mr = {
        href: "https://dash.cloudflare.com/profile/api-tokens",
        target: "_blank",
        rel: "noopener noreferrer"
    },
    gr = {
        class: "form-group"
    },
    fr = {
        class: "form-label",
        for: "cloudflare-api-key"
    },
    hr = {
        class: "password-input-wrapper"
    },
    yr = ["type", "placeholder", "disabled"],
    br = ["disabled"],
    kr = {
        class: "form-group"
    },
    xr = ["disabled"],
    wr = {
        class: "form-group"
    },
    jr = {
        class: "form-label",
        for: "cloudflare-project-name"
    },
    Cr = ["disabled"],
    Sr = {
        class: "input-help"
    },
    Er = {
        class: "help-text"
    },
    Ar = {
        key: 0,
        class: "error-text"
    },
    Tr = {
        class: "form-group"
    },
    Mr = {
        class: "checkbox-group"
    },
    $r = ["disabled"],
    Ir = {
        class: "checkbox-label"
    },
    Rr = {
        class: "checkbox-description"
    },
    Lr = {
        class: "form-group"
    },
    Pr = {
        class: "checkbox-group"
    },
    qr = ["disabled"],
    Or = {
        class: "checkbox-label"
    },
    Br = {
        class: "checkbox-description"
    },
    Ur = {
        class: "form-group"
    },
    Fr = ["disabled"],
    Dr = {
        key: 0,
        class: "deployment-output"
    },
    Nr = ["innerHTML"],
    zr = {
        key: 1,
        class: "deployment-result"
    },
    Hr = {
        class: "deployment-info"
    },
    Vr = {
        class: "deployment-url"
    },
    Gr = {
        class: "label"
    },
    Wr = {
        class: "deployment-time"
    },
    Kr = {
        class: "label"
    },
    Jr = l({
        __name: "CodeSandboxLightDeployTab",
        props: {
            project: {
                type: Object,
                required: !0
            },
            isAsking: {
                type: Boolean,
                default: !1
            }
        },
        setup(e, {
            expose: t
        }) {
            var l, s;
            const p = e,
                {
                    t: f
                } = A(),
                h = na(),
                y = new $i,
                x = a(!1),
                w = a(""),
                C = a(null),
                S = a(""),
                T = a(!1),
                M = a(!1),
                I = a(!1),
                L = a(""),
                P = a("");
            a(!1);
            const q = a(!1),
                O = a((null == (s = null == (l = p.project) ? void 0 : l.session_state) ? void 0 : s.cloudflare_project_name) || ""),
                B = a(!1),
                U = a(!1),
                D = a(!1),
                z = a(""),
                G = a(null),
                W = b((() => {
                    var e, t;
                    return (null == (t = null == (e = p.project) ? void 0 : e.session_state) ? void 0 : t.files) || []
                })),
                K = b((() => W.value.length > 0)),
                J = b((() => {
                    if (!O.value.trim()) return !0;
                    return /^[a-z][a-z0-9-]*$/.test(O.value.trim())
                })),
                Q = b((() => ae.value ? y.ansi_to_html(ae.value) : "")),
                Y = a(null),
                Z = a(null),
                X = async () => {
                    if (S.value.trim()) {
                        T.value = !0;
                        try {
                            const e = await fetch("/api/code_sandbox/save_cloudflare_api_key", {
                                    method: "POST",
                                    headers: {
                                        "Content-Type": "application/json"
                                    },
                                    body: JSON.stringify({
                                        api_key: S.value.trim()
                                    })
                                }),
                                t = await e.json();
                            !e.ok || 0 !== t.status && "ok" !== t.status ? h.error(t.message || f("components.code_sandbox.api_key_save_failed")) : (h.success(f("components.code_sandbox.api_key_saved_successfully")), S.value = "", await te())
                        } catch (e) {
                            h.error("Failed to save API key. Please try again.")
                        } finally {
                            T.value = !1
                        }
                    } else h.error(f("components.code_sandbox.api_key_required"))
                },
                ee = async () => {
                    T.value = !0;
                    try {
                        const e = await fetch("/api/code_sandbox/remove_cloudflare_api_key", {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json"
                                }
                            }),
                            t = await e.json();
                        !e.ok || 0 !== t.status && "ok" !== t.status ? h.error(t.message || f("components.code_sandbox.api_key_remove_failed")) : (h.success(f("components.code_sandbox.api_key_removed_successfully")), S.value = "", await te())
                    } catch (e) {
                        h.error(f("components.code_sandbox.api_key_remove_error"))
                    } finally {
                        T.value = !1
                    }
                },
                te = async () => {
                    q.value = !0;
                    try {
                        const e = await fetch("/api/code_sandbox/get_cloudflare_api_key_info", {
                                method: "GET",
                                headers: {
                                    "Content-Type": "application/json"
                                }
                            }),
                            t = await e.json();
                        if (e.ok && (0 === t.status || "ok" === t.status)) {
                            const e = t.data;
                            I.value = (null == e ? void 0 : e.has_api_key) || !1, L.value = (null == e ? void 0 : e.masked_api_key) || "", P.value = (null == e ? void 0 : e.validation_message) || "", (null == e ? void 0 : e.has_api_key) && !S.value && (S.value = "")
                        }
                    } catch (e) {} finally {
                        q.value = !1
                    }
                },
                ae = a(""),
                le = async () => {
                    var e;
                    if ((null == (e = p.project) ? void 0 : e.id) && K.value)
                        if (O.value.trim())
                            if (J.value) {
                                D.value = !0, ae.value = "";
                                try {
                                    const e = await fetch("/api/code_sandbox_light/deploy_cloudflare", {
                                        method: "POST",
                                        headers: {
                                            "Content-Type": "application/json"
                                        },
                                        body: JSON.stringify({
                                            project_id: p.project.id,
                                            project_name: O.value.trim(),
                                            rebuild_db: B.value,
                                            recreate_project: U.value
                                        })
                                    });
                                    if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                                    const t = e.body.pipeThrough(new TextDecoderStream).getReader();
                                    let a = "";
                                    for (;;) {
                                        const {
                                            value: e,
                                            done: l
                                        } = await t.read();
                                        if (e && (a += e, ae.value = a), l) break
                                    }
                                    if (a.includes("✅ Deployment completed successfully!") || a.includes("✨ Complete deployment finished successfully!")) {
                                        const e = a.match(/https:\/\/[a-z0-9-]+\.pages\.dev/g);
                                        e && e.length > 0 && (z.value = e[e.length - 1]), G.value = new Date, h.success(f("components.code_sandbox_light.deploy_tab.deployment_success"))
                                    } else(a.includes("❌") || a.includes("Error:")) && h.error(f("components.code_sandbox_light.deploy_tab.deployment_failed"))
                                } catch (t) {
                                    h.error(f("components.code_sandbox_light.deploy_tab.deployment_error")), ae.value += `\n❌ Error: ${t.message}`
                                } finally {
                                    D.value = !1
                                }
                            } else h.error(f("components.code_sandbox_light.deploy_tab.project_name_help"));
                    else h.error(f("components.code_sandbox_light.deploy_tab.project_name_required"));
                    else h.error(f("components.code_sandbox_light.deploy_tab.no_project_or_files"))
                },
                se = () => {
                    z.value && window.open(z.value, "_blank")
                },
                ne = async () => {
                    var e, t;
                    if ((null == (e = p.project) ? void 0 : e.id) && K.value) {
                        x.value = !0;
                        try {
                            const e = await fetch("/api/code_sandbox_light/deploy", {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json"
                                },
                                body: JSON.stringify({
                                    project_id: p.project.id
                                })
                            });
                            if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                            const a = await e.json();
                            0 === a.status || "ok" === a.status ? (w.value = (null == (t = a.data) ? void 0 : t.url) || "", C.value = new Date, h.success(f("components.code_sandbox_light.deploy_tab.website_deployed"))) : h.error(a.message || "Deployment failed")
                        } catch (a) {
                            h.error(f("components.code_sandbox_light.deploy_tab.failed_to_deploy"))
                        } finally {
                            x.value = !1
                        }
                    } else h.error(f("components.code_sandbox_light.deploy_tab.no_project_or_files"))
                },
                oe = () => {
                    w.value && window.open(w.value, "_blank")
                },
                ie = e => {
                    if (!e) return "untitled";
                    const t = e.split("/");
                    return t[t.length - 1] || e
                },
                re = e => {
                    if (!e) return !1;
                    const t = e.toLowerCase().split(".").pop();
                    return ["png", "jpg", "jpeg", "gif", "svg", "webp", "bmp"].includes(t)
                },
                ce = e => {
                    if (!e || 0 === e) return "0 B";
                    const t = Math.floor(Math.log(e) / Math.log(1024));
                    return Math.round(e / Math.pow(1024, t) * 100) / 100 + " " + ["B", "KB", "MB", "GB"][t]
                },
                de = e => {
                    if (!e) return "";
                    const t = new Date - e,
                        a = Math.floor(t / 6e4),
                        l = Math.floor(t / 36e5);
                    return a < 1 ? f("components.code_sandbox_light.deploy_tab.just_now") : a < 60 ? `${a} minute${a>1?"s":""} ago` : l < 24 ? `${l} hour${l>1?"s":""} ago` : e.toLocaleString()
                };
            return k((() => {
                var e, t;
                return null == (t = null == (e = p.project) ? void 0 : e.session_state) ? void 0 : t.cloudflare_project_name
            }), (e => {
                e && !O.value && (O.value = e)
            }), {
                immediate: !0
            }), k(ae, (async (e, t) => {
                e && Z.value && (await F(), Z.value.scrollTop = Z.value.scrollHeight, !t && Y.value && Y.value.scrollIntoView({
                    behavior: "smooth",
                    block: "nearest"
                }))
            })), t({
                refreshApiKeyStatus: te
            }), $((() => {
                te()
            })), (t, a) => (r(), n("div", Pi, [c("div", qi, [c("h3", null, v(i(f)("components.code_sandbox_light.deploy_tab.share_with_friends")), 1), c("div", Oi, [c("div", Bi, [c("div", Ui, [c("div", Fi, [c("p", null, v(i(f)("components.code_sandbox_light.deploy_tab.publishing_description")), 1)]), c("button", {
                type: "button",
                class: "deploy-button",
                disabled: !K.value || e.isAsking || x.value,
                onClick: ne
            }, [x.value ? (r(), E(i(pa), {
                key: 0,
                size: "small",
                stroke: "#ffffff"
            })) : o("", !0), c("span", null, v(x.value ? i(f)("components.code_sandbox_light.deploy_tab.publishing") : i(f)("components.code_sandbox_light.deploy_tab.publish_website")), 1)], 8, Di)]), w.value ? (r(), n("div", Ni, [c("div", zi, [c("div", Hi, [c("div", null, [c("span", Vi, v(i(f)("components.code_sandbox_light.deploy_tab.url")) + ":", 1)]), c("div", null, [c("button", {
                type: "button",
                class: "deployment-link",
                onClick: oe
            }, [c("span", null, v(w.value), 1), u(i(Re), {
                class: "external-icon"
            })])])])])])) : o("", !0), K.value ? (r(), n("div", Gi, [c("h4", null, v(i(f)("components.code_sandbox_light.deploy_tab.files")) + " (" + v(W.value.length) + ") ", 1), c("div", Wi, [(r(!0), n(m, null, g(W.value, (e => {
                var t;
                return r(), n("div", {
                    key: e.file_path,
                    class: "file-item"
                }, [c("div", Ki, [re(e.file_path) ? (r(), E(i(Te), {
                    key: 1
                })) : (r(), E(i(Ae), {
                    key: 0
                }))]), c("div", Ji, [c("div", Qi, v(ie(e.file_path)), 1), c("div", Yi, v(ce((null == (t = e.content) ? void 0 : t.length) || 0)), 1)])])
            })), 128))])])) : o("", !0)])]), c("h3", Zi, v(i(f)("components.code_sandbox_light.deploy_tab.custom_domains")), 1), c("div", Xi, [q.value ? (r(), n("div", er, [c("div", tr, [u(i(pa), {
                size: "medium",
                stroke: "#0f7fff"
            }), c("div", ar, v(i(f)("components.code_sandbox_light.deploy_tab.loading_api_key_status")), 1)])])) : o("", !0), !q.value && I.value ? (r(), n("div", lr, [c("div", sr, [c("div", nr, [c("div", or, [c("span", ir, v(i(f)("components.code_sandbox.api_key_label")) + ":", 1), c("code", rr, v(L.value), 1)]), P.value ? (r(), n("div", cr, [c("span", dr, v(i(f)("components.code_sandbox.status")) + ":", 1), c("span", {
                class: _({
                    "success-text": P.value.includes("valid"),
                    "error-text": !P.value.includes("valid")
                })
            }, v(P.value), 3)])) : o("", !0), c("div", null, [c("button", {
                type: "button",
                class: "remove-api-key-button",
                disabled: T.value,
                onClick: ee
            }, [T.value ? (r(), E(i(pa), {
                key: 0,
                size: "small",
                stroke: "#ffffff"
            })) : o("", !0), c("span", null, v(T.value ? i(f)("components.code_sandbox_light.deploy_tab.removing") : i(f)("components.code_sandbox.remove_api_key")), 1)], 8, ur)])])])])) : o("", !0), q.value || I.value ? o("", !0) : (r(), n("div", pr, [c("div", vr, [c("h4", _r, v(i(f)("components.code_sandbox_light.deploy_tab.cloudflare_setup_instructions")), 1), c("ol", null, [c("li", null, v(i(f)("components.code_sandbox.instruction_1")), 1), c("li", null, v(i(f)("components.code_sandbox.instruction_2")), 1), c("li", null, v(i(f)("components.code_sandbox.instruction_3_cloudflare_workers")), 1), c("li", null, v(i(f)("components.code_sandbox.instruction_4_d1_permission")), 1), c("li", null, v(i(f)("components.code_sandbox.instruction_5_copy_key")), 1)]), c("p", null, [c("a", mr, v(i(f)("components.code_sandbox.open_cloudflare_dashboard")), 1)])]), c("form", {
                onSubmit: N(X, ["prevent"]),
                class: "api-key-form"
            }, [c("div", gr, [c("label", fr, v(i(f)("components.code_sandbox.cloudflare_api_key_label")), 1), c("div", hr, [d(c("input", {
                id: "cloudflare-api-key",
                type: M.value ? "text" : "password",
                class: "form-input password-input",
                "onUpdate:modelValue": a[0] || (a[0] = e => S.value = e),
                placeholder: i(f)("components.code_sandbox.cloudflare_api_key_placeholder"),
                disabled: T.value
            }, null, 8, yr), [
                [H, S.value]
            ]), c("button", {
                type: "button",
                class: "password-toggle",
                onClick: a[1] || (a[1] = e => M.value = !M.value),
                disabled: T.value
            }, [M.value ? (r(), E(i(Li), {
                key: 1,
                class: "eye-icon"
            })) : (r(), E(i(Le), {
                key: 0,
                class: "eye-icon"
            }))], 8, br)])]), c("div", kr, [c("button", {
                type: "submit",
                class: "api-key-save-button",
                disabled: !S.value.trim() || T.value
            }, [T.value ? (r(), E(i(pa), {
                key: 0,
                size: "small",
                stroke: "#ffffff"
            })) : o("", !0), c("span", null, v(T.value ? i(f)("components.code_sandbox_light.deploy_tab.saving") : i(f)("components.code_sandbox.save_api_key")), 1)], 8, xr)])], 32)])), I.value ? (r(), n(m, {
                key: 3
            }, [a[6] || (a[6] = c("div", {
                class: "border"
            }, null, -1)), c("form", {
                onSubmit: N(le, ["prevent"]),
                class: "cloudflare-form"
            }, [c("div", wr, [c("label", jr, [j(v(i(f)("components.code_sandbox_light.deploy_tab.project_name")) + " ", 1), a[5] || (a[5] = c("span", {
                class: "required"
            }, "*", -1))]), d(c("input", {
                id: "cloudflare-project-name",
                type: "text",
                class: _(["form-input", {
                    "input-error": !J.value
                }]),
                "onUpdate:modelValue": a[2] || (a[2] = e => O.value = e),
                placeholder: "my-website-project",
                disabled: D.value || !K.value || e.isAsking || q.value
            }, null, 10, Cr), [
                [R, O.value]
            ]), c("div", Sr, [c("div", Er, v(i(f)("components.code_sandbox_light.deploy_tab.project_name_help")), 1), O.value && !J.value ? (r(), n("div", Ar, v(i(f)("components.code_sandbox_light.deploy_tab.invalid_project_name")), 1)) : o("", !0)])]), c("div", Tr, [c("label", Mr, [d(c("input", {
                type: "checkbox",
                class: "checkbox-input",
                "onUpdate:modelValue": a[3] || (a[3] = e => B.value = e),
                disabled: D.value || e.isAsking || q.value
            }, null, 8, $r), [
                [V, B.value]
            ]), c("span", Ir, v(i(f)("components.code_sandbox_light.deploy_tab.rebuild_database")), 1)]), c("div", Rr, [c("small", null, v(i(f)("components.code_sandbox_light.deploy_tab.rebuild_database_description")), 1)])]), c("div", Lr, [c("label", Pr, [d(c("input", {
                type: "checkbox",
                class: "checkbox-input",
                "onUpdate:modelValue": a[4] || (a[4] = e => U.value = e),
                disabled: D.value || e.isAsking || q.value
            }, null, 8, qr), [
                [V, U.value]
            ]), c("span", Or, v(i(f)("components.code_sandbox_light.deploy_tab.recreate_project")), 1)]), c("div", Br, [c("small", null, v(i(f)("components.code_sandbox_light.deploy_tab.recreate_project_description")), 1)])]), c("div", Ur, [c("button", {
                type: "submit",
                class: "cloudflare-deploy-button",
                disabled: !K.value || !O.value.trim() || !J.value || e.isAsking || q.value || D.value
            }, [D.value ? (r(), E(i(pa), {
                key: 0,
                size: "small",
                stroke: "#ffffff"
            })) : o("", !0), c("span", null, v(D.value ? i(f)("components.code_sandbox_light.deploy_tab.deploying_to_cloudflare") : i(f)("components.code_sandbox_light.deploy_tab.deploy_to_cloudflare")), 1)], 8, Fr)])], 32), ae.value || D.value ? (r(), n("div", Dr, [c("h4", null, v(i(f)("components.code_sandbox_light.deploy_tab.deployment_output")), 1), c("div", {
                class: "output-container",
                ref_key: "outputContainer",
                ref: Y
            }, [c("pre", {
                ref_key: "outputPre",
                ref: Z
            }, [c("code", {
                innerHTML: Q.value || i(f)("components.code_sandbox_light.deploy_tab.starting_deployment")
            }, null, 8, Nr)], 512)], 512)])) : o("", !0), z.value ? (r(), n("div", zr, [c("h4", null, v(i(f)("components.code_sandbox_light.deploy_tab.cloudflare_pages_deployment")), 1), c("div", Hr, [c("div", Vr, [c("span", Gr, v(i(f)("components.code_sandbox_light.deploy_tab.url")) + ":", 1), c("button", {
                type: "button",
                class: "deployment-link",
                onClick: se
            }, [c("span", null, v(z.value), 1), u(i(Re), {
                class: "external-icon"
            })])]), c("div", Wr, [c("span", Kr, v(i(f)("components.code_sandbox_light.deploy_tab.deployed")) + ":", 1), c("span", null, v(de(G.value)), 1)])])])) : o("", !0)], 64)) : o("", !0)])])]))
        }
    }, [
        ["__scopeId", "data-v-101ea230"]
    ]),
    Qr = {
        class: "file-viewer"
    },
    Yr = {
        key: 0,
        class: "empty-state"
    },
    Zr = {
        class: "empty-icon"
    },
    Xr = {
        key: 1,
        class: "text-content"
    },
    ec = {
        class: "file-header"
    },
    tc = {
        class: "file-path"
    },
    ac = {
        class: "file-meta"
    },
    lc = {
        class: "file-size"
    },
    sc = {
        class: "file-date"
    },
    nc = {
        class: "code-content"
    },
    oc = {
        key: 2,
        class: "image-content"
    },
    ic = {
        class: "file-header"
    },
    rc = {
        class: "file-path"
    },
    cc = {
        class: "file-meta"
    },
    dc = {
        class: "file-size"
    },
    uc = {
        class: "file-date"
    },
    pc = {
        class: "image-container"
    },
    vc = ["src", "alt"],
    _c = {
        key: 1,
        class: "image-error"
    },
    mc = {
        key: 3,
        class: "binary-content"
    },
    gc = {
        class: "file-header"
    },
    fc = {
        class: "file-path"
    },
    hc = {
        class: "file-meta"
    },
    yc = {
        class: "file-size"
    },
    bc = {
        class: "file-date"
    },
    kc = {
        class: "binary-info"
    },
    xc = {
        class: "binary-icon"
    },
    wc = l({
        __name: "CodeSandboxLightFileViewer",
        props: {
            file: {
                type: Object,
                default: null
            }
        },
        setup(e) {
            const t = e,
                l = a(""),
                s = b((() => {
                    var e;
                    if (!(null == (e = t.file) ? void 0 : e.file_path)) return !1;
                    const a = t.file.file_path.toLowerCase().split(".").pop();
                    return ["txt", "md", "json", "js", "ts", "jsx", "tsx", "vue", "html", "css", "scss", "sass", "less", "xml", "yaml", "yml", "ini", "conf", "py", "java", "c", "cpp", "h", "hpp", "cs", "php", "rb", "go", "rs", "kt", "swift", "sh", "bash", "zsh", "fish", "ps1", "bat", "cmd", "sql", "r", "scala", "clj", "hs", "elm", "ex", "exs", "erl", "pl", "pm", "dockerfile", "gitignore", "gitattributes", "editorconfig", "prettierrc", "eslintrc", "babelrc", "log", "csv", "tsv", "properties", "env", "toml", "lock", "pem", "crt", "key"].includes(a) || !a
                })),
                o = b((() => {
                    var e;
                    if (!(null == (e = t.file) ? void 0 : e.file_path)) return !1;
                    const a = t.file.file_path.toLowerCase().split(".").pop();
                    return ["png", "jpg", "jpeg", "gif", "svg", "webp", "bmp"].includes(a)
                }));
            k((() => t.file), (e => {
                if (l.value = "", e && o.value && e.content) try {
                    if (e.content.startsWith("data:")) l.value = e.content;
                    else if (e.content.match(/^[A-Za-z0-9+/=]+$/)) {
                        const t = e.file_path.toLowerCase().split(".").pop(),
                            a = d(t);
                        l.value = `data:${a};base64,${e.content}`
                    }
                } catch (t) {}
            }), {
                immediate: !0
            });
            const d = e => ({
                    png: "image/png",
                    jpg: "image/jpeg",
                    jpeg: "image/jpeg",
                    gif: "image/gif",
                    svg: "image/svg+xml",
                    webp: "image/webp",
                    bmp: "image/bmp"
                }[e] || "image/png"),
                p = e => {
                    if (!e || 0 === e) return "0 B";
                    const t = Math.floor(Math.log(e) / Math.log(1024));
                    return Math.round(e / Math.pow(1024, t) * 100) / 100 + " " + ["B", "KB", "MB", "GB"][t]
                },
                _ = e => {
                    if (!e) return "";
                    const t = new Date(e),
                        a = new Date - t,
                        l = Math.floor(a / 6e4),
                        s = Math.floor(a / 36e5),
                        n = Math.floor(a / 864e5);
                    return l < 1 ? "just now" : l < 60 ? `${l}m ago` : s < 24 ? `${s}h ago` : n < 7 ? `${n}d ago` : t.toLocaleDateString()
                };
            return (t, a) => {
                var d, m, g, f, h, y;
                return r(), n("div", Qr, [e.file ? s.value ? (r(), n("div", Xr, [c("div", ec, [c("div", tc, v(e.file.file_path), 1), c("div", ac, [c("span", lc, v(p((null == (d = e.file.content) ? void 0 : d.length) || 0)), 1), c("span", sc, v(_(null == (m = e.file.metadata) ? void 0 : m.updated_at)), 1)])]), c("div", nc, [c("pre", null, [c("code", null, v(e.file.content || ""), 1)])])])) : o.value ? (r(), n("div", oc, [c("div", ic, [c("div", rc, v(e.file.file_path), 1), c("div", cc, [c("span", dc, v(p((null == (g = e.file.content) ? void 0 : g.length) || 0)), 1), c("span", uc, v(_(null == (f = e.file.metadata) ? void 0 : f.updated_at)), 1)])]), c("div", pc, [l.value ? (r(), n("img", {
                    key: 0,
                    src: l.value,
                    alt: e.file.file_path,
                    class: "preview-image"
                }, null, 8, vc)) : (r(), n("div", _c, [u(i(Te)), a[1] || (a[1] = c("span", null, "Cannot preview image", -1))]))])])) : (r(), n("div", mc, [c("div", gc, [c("div", fc, v(e.file.file_path), 1), c("div", hc, [c("span", yc, v(p((null == (h = e.file.content) ? void 0 : h.length) || 0)), 1), c("span", bc, v(_(null == (y = e.file.metadata) ? void 0 : y.updated_at)), 1)])]), c("div", kc, [c("div", xc, [u(i(Ae))]), a[2] || (a[2] = c("div", {
                    class: "binary-text"
                }, [c("div", {
                    class: "binary-title"
                }, "Binary file"), c("div", {
                    class: "binary-subtitle"
                }, "Cannot preview binary content")], -1))])])) : (r(), n("div", Yr, [c("div", Zr, [u(i(Ae))]), a[0] || (a[0] = c("div", {
                    class: "empty-text"
                }, "No file selected", -1))]))])
            }
        }
    }, [
        ["__scopeId", "data-v-5b3c95be"]
    ]),
    jc = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "100%",
        height: "100%",
        viewBox: "0 0 16 17",
        fill: "none",
        class: "database-icon"
    };
const Cc = l({}, [
        ["render", function(e, t) {
            return r(), n("svg", jc, t[0] || (t[0] = [c("path", {
                d: "M14.0014 4.5957C14.0014 4.5957 14.0013 12.1173 14.0013 12.6989C14.0013 13.6934 11.314 14.4996 7.99899 14.4996C4.68398 14.4996 1.99665 13.6934 1.99665 12.6989C1.99665 12.1413 1.99664 4.5957 1.99664 4.5957",
                stroke: "currentColor",
                "stroke-width": "1.35053",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), c("path", {
                d: "M14.0013 9.99805C14.0013 10.9925 11.314 11.7988 7.99899 11.7988C4.68398 11.7988 1.99664 10.9925 1.99664 9.99805",
                stroke: "currentColor",
                "stroke-width": "1.35053",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), c("path", {
                d: "M14.0013 7.29688C14.0013 8.29137 11.314 9.09758 7.99899 9.09758C4.68398 9.09758 1.99664 8.29137 1.99664 7.29688",
                stroke: "currentColor",
                "stroke-width": "1.35053",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), c("path", {
                d: "M7.99899 6.10141C11.314 6.10141 14.0013 5.2952 14.0013 4.3007C14.0013 3.3062 11.314 2.5 7.99899 2.5C4.68398 2.5 1.99664 3.3062 1.99664 4.3007C1.99664 5.2952 4.68398 6.10141 7.99899 6.10141Z",
                stroke: "currentColor",
                "stroke-width": "1.35053",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }],
        ["__scopeId", "data-v-b14ebf11"]
    ]),
    Sc = {
        class: "data-tab"
    },
    Ec = {
        class: "controls"
    },
    Ac = {
        class: "schema-selector"
    },
    Tc = {
        class: "schema-tabs"
    },
    Mc = ["onClick", "disabled"],
    $c = {
        key: 0,
        class: "data-content"
    },
    Ic = {
        class: "table-wrapper"
    },
    Rc = {
        key: 1,
        class: "loading-placeholder"
    },
    Lc = {
        key: 1,
        class: "empty-state-centered"
    },
    Pc = {
        class: "empty-icon"
    },
    qc = {
        class: "empty-title"
    },
    Oc = {
        class: "empty-description"
    },
    Bc = {
        key: 2,
        class: "empty-state-centered"
    },
    Uc = {
        class: "empty-icon"
    },
    Fc = {
        class: "empty-title"
    },
    Dc = {
        class: "empty-description"
    },
    Nc = {
        key: 3,
        class: "empty-state-centered"
    },
    zc = {
        class: "empty-icon"
    },
    Hc = {
        class: "empty-title"
    },
    Vc = {
        class: "empty-description"
    },
    Gc = l({
        __name: "CodeSandboxLightDataTab",
        props: {
            project: {
                type: Object,
                required: !0
            }
        },
        setup(e, {
            expose: t
        }) {
            const l = e,
                {
                    t: s
                } = A(),
                d = na(),
                f = a(!1),
                h = a(!1),
                y = a(!1),
                x = a(null),
                w = a(null),
                j = a([]),
                C = a(new Set),
                S = a(1),
                T = a(10),
                M = b((() => j.value.map((e => ({
                    label: e.name,
                    value: e.name
                }))))),
                I = b((() => {
                    var e, t;
                    return (null == (t = null == (e = w.value) ? void 0 : e.schema) ? void 0 : t.fields) ? w.value.schema.fields.map((e => {
                        let t = 150;
                        return "id" === e.name ? t = 120 : "datetime" === e.type ? t = 180 : "array" === e.type || "rich_text" === e.type ? t = 250 : "boolean" === e.type ? t = 100 : "number" !== e.type && "integer" !== e.type || (t = 120), {
                            key: e.name,
                            title: e.name,
                            width: t,
                            minWidth: t,
                            render: t => {
                                const a = t[e.name];
                                if (null == a) return "-";
                                switch (e.type) {
                                    case "datetime":
                                        return "number" == typeof a ? new Date(a).toLocaleString() : a;
                                    case "array":
                                        return Array.isArray(a) ? JSON.stringify(a) : a;
                                    case "rich_text":
                                        return a.length > 50 ? a.substring(0, 50) + "..." : a;
                                    default:
                                        return String(a)
                                }
                            }
                        }
                    })) : []
                })),
                R = b((() => {
                    var e;
                    return {
                        page: S.value,
                        pageSize: T.value,
                        showSizePicker: !0,
                        pageSizes: [10, 20, 50],
                        showQuickJumper: !0,
                        prefix: ({
                            itemCount: e
                        }) => `${s("components.code_sandbox_light.data_tab.total")}: ${e}`,
                        itemCount: (null == (e = w.value) ? void 0 : e.total) || 0,
                        onChange: e => {
                            S.value = e, x.value && P(x.value)
                        },
                        onUpdatePageSize: e => {
                            T.value = e, S.value = 1, x.value && P(x.value)
                        }
                    }
                })),
                L = () => {
                    var e;
                    try {
                        const t = (null == (e = l.project) ? void 0 : e.session_state) || {};
                        j.value = t.schema || []
                    } catch (t) {
                        j.value = []
                    }
                },
                P = async e => {
                    var t;
                    if (e && (null == (t = l.project) ? void 0 : t.id)) {
                        h.value = !0;
                        try {
                            const t = new URL(`/api/code_sandbox_light/preview/${l.project.id}/tables/${e}`, window.location.origin);
                            t.searchParams.set("page", S.value.toString()), t.searchParams.set("limit", T.value.toString());
                            const a = await fetch(t, {
                                method: "GET",
                                credentials: "include",
                                headers: {
                                    "Content-Type": "application/json"
                                }
                            });
                            if (!a.ok) throw new Error(`HTTP ${a.status}: ${a.statusText}`);
                            const s = await a.json();
                            w.value = s, C.value.add(e), p.log("Loaded table data:", s)
                        } catch (a) {
                            d.error(s("components.code_sandbox.load_data_error")), w.value = null
                        } finally {
                            h.value = !1, y.value = !1
                        }
                    }
                };
            return k((() => {
                var e, t;
                return null == (t = null == (e = l.project) ? void 0 : e.session_state) ? void 0 : t.schema
            }), (() => {
                if (L(), x.value) {
                    j.value.some((e => e.name === x.value)) || (x.value = null, w.value = null, C.value.delete(x.value))
                }
            }), {
                deep: !0,
                immediate: !0
            }), k(w, (() => {
                F((() => {
                    const e = document.querySelector(".data-tab");
                    if (!e) return;
                    const t = e.querySelector(".table-wrapper");
                    if (!t || t.classList.contains("table-scroll-init")) return;
                    t.style.position = "relative";
                    const a = document.createElement("div");
                    a.className = "table-scroll-button";
                    const l = window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches,
                        s = l ? "#eee" : "#232425",
                        n = l ? "#232425" : "#fff";
                    a.style.cssText = `\n        width: 56px;\n        height: 26px;\n        display: none;\n        align-items: center;\n        justify-content: center;\n        border-radius: 8px;\n        background: ${s};\n        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);\n        position: absolute;\n        top: 10px;\n        right: 8px;\n        z-index: 1000;\n        cursor: pointer;\n        color: ${n};\n      `;
                    const o = document.createElement("div");
                    o.className = "scroll-icon", o.style.cssText = `\n        width: 16px;\n        height: 16px;\n        color: ${n};\n        transform: translate(-2px, 0);\n      `, o.classList.add("scroll-icon-animate"), o.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">\n          <path d="M9.5556 3L12.2812 5.643C13.4271 6.75412 14 7.30962 14 8C14 8.69037 13.4271 9.24587 12.2812 10.357L9.5556 13M2 3L4.72564 5.643C5.87147 6.75412 6.4444 7.30962 6.4444 8C6.4444 8.69037 5.87147 9.24587 4.72565 10.357L2 13" stroke="currentColor" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>\n          </svg>', a.appendChild(o), t.appendChild(a);
                    const i = () => {
                        const e = t.querySelector(".n-scrollbar > .n-scrollbar-container"),
                            l = t.querySelector(".n-data-table-wrapper"),
                            s = null == l ? void 0 : l.querySelector(".n-data-table-table");
                        e && s && (s.scrollWidth > e.clientWidth ? a.style.display = "flex" : a.style.display = "none")
                    };
                    a.addEventListener("click", (() => {
                        const e = t.querySelector(".n-scrollbar > .n-scrollbar-container");
                        e && (e.scrollTo({
                            left: e.scrollLeft + e.clientWidth,
                            behavior: "smooth"
                        }), i(), a.style.display = "none")
                    }));
                    const r = t.querySelector(".n-scrollbar > .n-scrollbar-container");
                    r && r.addEventListener("scroll", (() => {
                        a.style.display = "none"
                    })), setTimeout(i, 500), window.addEventListener("resize", i), t.classList.add("table-scroll-init")
                }))
            }), {
                flush: "post"
            }), $((() => {
                L()
            })), t({
                refreshData: async () => {
                    x.value && await P(x.value)
                },
                loadSchemas: L
            }), (e, t) => (r(), n("div", Sc, [c("div", Ec, [c("div", Ac, [c("div", Tc, [(r(!0), n(m, null, g(M.value, (e => (r(), n("button", {
                key: e.value,
                class: _(["schema-tab", {
                    "schema-tab-active": x.value === e.value
                }]),
                onClick: t => (async e => {
                    if (!e) return x.value = null, w.value = null, void(S.value = 1);
                    x.value = e, C.value.has(e) || (y.value = !0), S.value = 1, await P(e)
                })(e.value),
                disabled: f.value
            }, v(e.label), 11, Mc)))), 128))])])]), x.value && (w.value || h.value || y.value) ? (r(), n("div", $c, [o("", !0), c("div", Ic, [w.value ? (r(), E(i(_a), {
                key: 0,
                columns: I.value,
                data: w.value.data || [],
                loading: h.value || y.value,
                pagination: R.value,
                remote: !0
            }, null, 8, ["columns", "data", "loading", "pagination"])) : (r(), n("div", Rc, [u(i(pa), {
                size: "large",
                stroke: "#0f7fff"
            }), c("p", null, v(i(s)("components.code_sandbox.loading_data")), 1)]))])])) : !x.value || w.value || h.value || y.value ? !x.value && j.value.length > 0 ? (r(), n("div", Bc, [c("div", Uc, [u(Cc)]), c("div", Fc, v(i(s)("components.code_sandbox_light.data_tab.data_storage_title")), 1), c("div", Dc, v(i(s)("components.code_sandbox_light.data_tab.data_storage_description")), 1)])) : j.value.length || f.value ? o("", !0) : (r(), n("div", Nc, [c("div", zc, [u(Cc)]), c("div", Hc, v(i(s)("components.code_sandbox_light.data_tab.data_storage_title")), 1), c("div", Vc, v(i(s)("components.code_sandbox_light.data_tab.data_storage_description")), 1)])) : (r(), n("div", Lc, [c("div", Pc, [u(Cc)]), c("div", qc, v(i(s)("components.code_sandbox_light.data_tab.data_storage_title")), 1), c("div", Oc, v(i(s)("components.code_sandbox_light.data_tab.data_storage_description")), 1)]))]))
        }
    }, [
        ["__scopeId", "data-v-03d1aebf"]
    ]);
const Wc = {
        class: "tutorial-tab"
    },
    Kc = {
        class: "tutorial-layout"
    },
    Jc = {
        key: 0,
        class: "toc-sidebar"
    },
    Qc = {
        class: "toc-header"
    },
    Yc = ["innerHTML"],
    Zc = {
        class: "tutorial-container"
    },
    Xc = {
        key: 0,
        class: "loading-container"
    },
    ed = l(Object.assign({
        name: "CodeSandboxLightTutorialTab"
    }, {
        __name: "CodeSandboxLightTutorialTab",
        setup(e) {
            const {
                locale: t
            } = A(), l = a(""), s = a(!0), d = a(""), p = a(!0);
            let v = [];
            const _ = e => {
                    l.value = e, F((() => {
                        g()
                    }))
                },
                m = e => e ? e = e.replace(/[ \t]*\[[0-9]+\]$/g, "") : "",
                g = () => {
                    const e = document.querySelector(".toc-content");
                    if (!e) return;
                    v.forEach((e => {
                        e.removeEventListener("click", f)
                    })), v = [];
                    e.querySelectorAll('a[href^="#"]').forEach((e => {
                        e.addEventListener("click", f), v.push(e)
                    }))
                },
                f = e => {
                    e.preventDefault();
                    const t = e.target.closest('a[href^="#"]');
                    if (!t) return;
                    const a = t.getAttribute("href");
                    if (!a || !a.startsWith("#")) return;
                    const l = a.substring(1),
                        s = document.getElementById(l);
                    s && s.scrollIntoView({
                        behavior: "smooth",
                        block: "start"
                    })
                };
            return $((async () => {
                await (async () => {
                    p.value = !0;
                    try {
                        const e = t.value || "en-US";
                        let a = await fetch(`/ai_developer/tutorial_${e}.md`);
                        a.ok || (a = await fetch("/ai_developer/tutorial_en-US.md")), a.ok ? d.value = await a.text() : d.value = "# Tutorial content not available\n\nPlease check back later."
                    } catch (e) {
                        d.value = "# Error loading tutorial\n\nPlease refresh the page to try again."
                    } finally {
                        p.value = !1
                    }
                })(), g()
            })), I((() => {
                v.forEach((e => {
                    e.removeEventListener("click", f)
                })), v = []
            })), (e, t) => (r(), n("div", Wc, [c("div", Kc, [l.value && s.value ? (r(), n("div", Jc, [c("div", Qc, [t[3] || (t[3] = j(" Table of Contents ")), c("button", {
                class: "toc-close-btn",
                onClick: t[0] || (t[0] = e => s.value = !1)
            }, t[2] || (t[2] = [c("svg", {
                width: "16",
                height: "16",
                viewBox: "0 0 16 16",
                fill: "currentColor"
            }, [c("path", {
                d: "M12.8 4.4L8.4 8.8l4.4 4.4-1.2 1.2L7.2 10l-4.4 4.4L1.6 13.2L6 8.8 1.6 4.4 2.8 3.2l4.4 4.4 4.4-4.4z"
            })], -1)]))]), c("div", {
                class: "toc-content",
                innerHTML: l.value
            }, null, 8, Yc)])) : o("", !0), l.value && !s.value ? (r(), n("div", {
                key: 1,
                class: "toc-toggle-btn",
                onClick: t[1] || (t[1] = e => s.value = !0)
            }, t[4] || (t[4] = [c("svg", {
                width: "20",
                height: "20",
                viewBox: "0 0 24 24",
                fill: "currentColor"
            }, [c("path", {
                d: "M3 9h14V7H3v2zm0 4h14v-2H3v2zm0 4h14v-2H3v2zm16 0h2v-2h-2v2zm0-10v2h2V7h-2zm0 6h2v-2h-2v2z"
            })], -1)]))) : o("", !0), c("div", Zc, [p.value ? (r(), n("div", Xc, [u(i(pa), {
                size: "large",
                stroke: "#0f7fff"
            }), t[5] || (t[5] = c("p", null, "Loading tutorial content...", -1))])) : (r(), E(ye, {
                key: 1,
                source: d.value,
                "toc-options": {
                    format: m,
                    level: [2, 3]
                },
                "toc-callback": _
            }, null, 8, ["source", "toc-options"]))])])]))
        }
    }), [
        ["__scopeId", "data-v-ede1da14"]
    ]),
    td = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 24 24"
    };
const ad = {
        render: function(e, t) {
            return r(), n("svg", td, t[0] || (t[0] = [c("g", {
                fill: "none"
            }, [c("path", {
                d: "M11 8.018a.75.75 0 0 1-.75.732c-.75 0-.75-.751-.75-.751V7.99a1.403 1.403 0 0 1 .008-.134a2.222 2.222 0 0 1 .42-1.067c.454-.613 1.27-1.062 2.585-1.039c.95.017 1.793.415 2.321 1.07c.537.667.718 1.57.362 2.459c-.362.905-1.181 1.265-1.652 1.471l-.05.023c-.28.123-.413.187-.493.251l-.001.001v.724a.75.75 0 0 1-1.5.001V11c0-.523.252-.897.563-1.147c.25-.2.565-.338.786-.436l.038-.017c.542-.239.8-.387.917-.679a.92.92 0 0 0-.138-.96c-.222-.275-.629-.502-1.179-.511c-.935-.016-1.245.285-1.353.432a.722.722 0 0 0-.134.33v.006zm1.25 7.482a1 1 0 1 0 0-2a1 1 0 0 0 0 2zM4 4.5A2.5 2.5 0 0 1 6.5 2H18a2.5 2.5 0 0 1 2.5 2.5v14.25a.75.75 0 0 1-.75.75H5.5a1 1 0 0 0 1 1h13.25a.75.75 0 0 1 0 1.5H6.5A2.5 2.5 0 0 1 4 19.5v-15zm1.5 0V18H19V4.5a1 1 0 0 0-1-1H6.5a1 1 0 0 0-1 1z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    ld = {
        class: "controls"
    },
    sd = {
        class: "canvas_header"
    },
    nd = {
        class: "current-canvas-header"
    },
    od = {
        class: "icon"
    },
    id = {
        class: "label"
    },
    rd = {
        class: "version-button flex items-center gap-[10px] bg-[#F5F5F5] dark:bg-[#333] px-[8px] py-[4px] rounded-lg whitespace-nowrap"
    },
    cd = {
        class: "version-button-label justify-start text-zinc-600 dark:text-zinc-300 text-sm leading-tight"
    },
    dd = {
        class: "flex flex-col gap-[12px] max-h-[300px] overflow-y-auto"
    },
    ud = ["onClick"],
    pd = {
        class: "flex justify-between items-center"
    },
    vd = ["title"],
    _d = {
        class: "action-buttons"
    },
    md = ["title"],
    gd = {
        class: "icon"
    },
    fd = {
        class: "tab-header"
    },
    hd = {
        class: "tab-header"
    },
    yd = {
        class: "tab-header file-tab-header"
    },
    bd = ["title"],
    kd = {
        class: "tab-header"
    },
    xd = {
        class: "tab-header"
    },
    wd = {
        class: "tab-header"
    },
    jd = {
        __name: "CodeSandboxLightCanvas",
        props: {
            project: {
                type: Object,
                required: !0
            },
            readOnly: {
                type: Boolean,
                default: !1
            },
            isAsking: {
                type: Boolean,
                default: !1
            },
            messages: {
                type: Object,
                default: null
            }
        },
        emits: ["close", "selectCanvasHistory"],
        setup(e, {
            emit: t
        }) {
            const l = e,
                {
                    project: d
                } = s(l),
                f = t,
                {
                    t: h
                } = A(),
                y = a(!1),
                w = a(""),
                j = a("preview"),
                C = a(null),
                S = a(null),
                T = a(!1),
                M = a(null),
                I = a(!1),
                R = a(!1),
                L = a({
                    filename: "",
                    file: null
                }),
                P = a(null),
                q = b((() => {
                    var e, t;
                    return (null == (t = null == (e = d.value) ? void 0 : e.session_state) ? void 0 : t.files) || []
                }));
            k((() => {
                var e, t, a, s;
                const n = null == (s = null == (e = l.messages) ? void 0 : e.value) ? void 0 : s[(null == (a = null == (t = l.messages) ? void 0 : t.value) ? void 0 : a.length) - 1];
                return null == n ? void 0 : n.tool_calls
            }), ((e, t) => {
                var a, l;
                const s = t && t.length > 0,
                    n = e && e.length > 0;
                if (s && !n) {
                    let e = !1;
                    if (t && L.value.filename)
                        for (const l of t)
                            if (null == (a = null == l ? void 0 : l.function) ? void 0 : a.name) {
                                let t = {};
                                try {
                                    t = JSON.parse(l.function.arguments || "{}")
                                } catch {
                                    t = {}
                                }
                                const a = t.file_path;
                                if (("CodeSandboxLightEdit" === l.function.name || "CodeSandboxLightWrite" === l.function.name) && a === L.value.filename) {
                                    e = !0;
                                    break
                                }
                            }
                    if ("fileviewer" === j.value && L.value.filename && e) {
                        const e = L.value.filename;
                        L.value.filename = "", L.value.file = null, setTimeout((() => {
                            const t = q.value.find((t => t.file_path === e));
                            t && (L.value.filename = e, L.value.file = t)
                        }), 10)
                    }
                    return
                }
                if (!e) return;
                const o = ["CodeSandboxLightRead", "CodeSandboxLightWrite", "CodeSandboxLightEdit", "CodeSandboxLightLS"];
                for (const r of e)
                    if ((null == (l = null == r ? void 0 : r.function) ? void 0 : l.name) && o.includes(r.function.name)) try {
                        const e = JSON.parse(r.function.arguments);
                        let t = null;
                        if (t = e.file_path || e.pattern, t) {
                            if (["CodeSandboxLightRead", "CodeSandboxLightEdit"].includes(r.function.name)) {
                                const e = q.value.find((e => e.file_path === t));
                                e && H(e)
                            } else j.value = "files"
                        }
                    } catch (i) {}
            }), {
                deep: !0
            });
            const O = () => {
                    p.log("Closing canvas"), d.value.session_state || (d.value.session_state = {}), d.value.session_state.canvas_mobile_visible = !1
                },
                B = () => {
                    var e;
                    "preview" === j.value ? (async () => {
                        const e = C.value;
                        if (!(null == e ? void 0 : e.reloadPreview)) return;
                        T.value = !0;
                        const t = new Promise((e => setTimeout(e, 1e3)));
                        e.reloadPreview(), await t, T.value = !1
                    })() : "data" === j.value ? U() : "deploy" === j.value ? (null == (e = S.value) ? void 0 : e.refreshApiKeyStatus) && S.value.refreshApiKeyStatus() : F()
                },
                U = async () => {
                    const e = M.value;
                    if (!(null == e ? void 0 : e.refreshData)) return;
                    y.value = !0;
                    const t = new Promise((e => setTimeout(e, 500)));
                    await e.refreshData(), await t, y.value = !1
                },
                F = async () => {
                    y.value = !0, w.value = "", await new Promise((e => setTimeout(e, 500))), y.value = !1
                },
                D = e => {
                    H(e)
                },
                z = e => {
                    H(e)
                },
                H = e => {
                    e && (L.value.filename = e.file_path, L.value.file = e, j.value = "fileviewer")
                },
                V = () => {
                    L.value.filename = "", L.value.file = null, j.value = "files"
                },
                W = e => {
                    if (!e) return "";
                    const t = e.split("/"),
                        a = t[t.length - 1];
                    return a.length > 12 ? a.substring(0, 12) + "..." : a
                },
                K = () => {
                    var e, t, a, l;
                    if (!(null == (t = null == (e = d.value) ? void 0 : e.session_state) ? void 0 : t.canvas_history) || !(null == (l = null == (a = d.value) ? void 0 : a.session_state) ? void 0 : l.canvas_history_id)) return 1;
                    const s = d.value.session_state.canvas_history.findIndex((e => e.id === d.value.session_state.canvas_history_id));
                    return -1 !== s ? d.value.session_state.canvas_history.length - s : 1
                },
                J = () => {
                    var e, t, a;
                    (null == (a = null == (t = null == (e = d.value) ? void 0 : e.session_state) ? void 0 : t.canvas_history) ? void 0 : a.length) > 1 && (R.value = !R.value)
                },
                Q = () => {
                    I.value = !I.value
                },
                Y = async e => {
                    d.value.session_state || (d.value.session_state = {}), d.value.session_state.canvas_history_id = e.id;
                    const t = await (async e => {
                        const t = await fetch(`/api/agent/get_canvas_history_item?project_id=${d.value.id}&canvas_history_id=${e.id}`);
                        if (!t.ok) return;
                        const a = await t.json();
                        return 0 === a.status ? a.data : void 0
                    })(e);
                    t && t.code_sandbox_light && (d.value.session_state.files = t.code_sandbox_light.files || [], d.value.session_state.schema = t.code_sandbox_light.schemas || [], f("selectCanvasHistory", e))
                };
            return $((() => {})), (t, a) => {
                var l, s;
                return r(), n("div", {
                    class: _(["wrapper", {
                        "mobile-visible": null == (s = null == (l = i(d)) ? void 0 : l.session_state) ? void 0 : s.canvas_mobile_visible
                    }])
                }, [u(i(G), null, {
                    default: x((() => {
                        var t, l, s;
                        return [c("div", ld, [c("div", sd, [c("div", nd, [c("div", od, [u(Fe)]), c("div", id, v(i(h)("components.code_sandbox_light.canvas.ai_developer")), 1)]), (null == (s = null == (l = null == (t = i(d)) ? void 0 : t.session_state) ? void 0 : l.canvas_history) ? void 0 : s.length) > 1 && !e.readOnly && !i(p).isMobile() ? (r(), E(i(kt), {
                            key: 0,
                            show: R.value,
                            placement: "bottom-start",
                            trigger: "hover",
                            "show-arrow": !1,
                            raw: "",
                            "onUpdate:show": J,
                            class: "p-[12px] rounded-[12px] bg-white dark:bg-[#222]"
                        }, {
                            trigger: x((() => [c("div", {
                                class: "cursor-pointer flex-shrink-0",
                                onClick: Q
                            }, [c("div", rd, [c("div", cd, v(i(h)("components.slides.save_point")) + "-" + v(K()), 1), u(i(fe), {
                                class: _([I.value && "rotate-180"])
                            }, null, 8, ["class"])])])])),
                            default: x((() => {
                                var e, t;
                                return [c("div", dd, [(r(!0), n(m, null, g(null == (t = null == (e = i(d)) ? void 0 : e.session_state) ? void 0 : t.canvas_history, ((e, t) => {
                                    var a, l, s, u, p, m;
                                    return r(), n("div", {
                                        key: t,
                                        class: _(["w-[257px] px-3 py-2 rounded-xl flex flex-col gap-1 cursor-pointer", e.id === (null == (l = null == (a = i(d)) ? void 0 : a.session_state) ? void 0 : l.canvas_history_id) ? "bg-[#0F7FFF] text-white" : "bg-neutral-100 hover:bg-[#E9E9E9] dark:bg-[#2a2a2a] dark:hover:bg-[#333] dark:text-white"]),
                                        onClick: t => (async e => {
                                            await Y(e), J()
                                        })(e)
                                    }, [c("div", pd, [c("div", null, v(i(h)("components.slides.save_point")) + "-" + v(i(d).session_state.canvas_history.length - t), 1), c("div", {
                                        class: _([e.id === (null == (u = null == (s = i(d)) ? void 0 : s.session_state) ? void 0 : u.canvas_history_id) ? "text-white" : "text-neutral-400"])
                                    }, v(new Date(e.ctime + "Z").toLocaleString()), 3)]), e.name ? (r(), n("div", {
                                        key: 0,
                                        class: _(["text-sm whitespace-nowrap overflow-hidden text-ellipsis", e.id === (null == (m = null == (p = i(d)) ? void 0 : p.session_state) ? void 0 : m.canvas_history_id) ? "text-white" : "text-neutral-400"]),
                                        title: e.name
                                    }, v(e.name), 11, vd)) : o("", !0)], 10, ud)
                                })), 128))])]
                            })),
                            _: 1
                        }, 8, ["show"])) : o("", !0)]), c("div", _d, [c("div", {
                            class: _(["action-button", {
                                loading: y.value || T.value
                            }]),
                            onClick: B,
                            title: i(h)("components.code_sandbox.refresh")
                        }, [u(De)], 10, md), c("div", {
                            class: "mobile-close-button",
                            onClick: O
                        }, [c("div", gd, [u(i(Ne))])])])]), u(i(ze), {
                            value: j.value,
                            "onUpdate:value": a[0] || (a[0] = e => j.value = e),
                            type: "line",
                            class: "sandbox-tabs"
                        }, {
                            default: x((() => [u(i(He), {
                                name: "preview",
                                tab: i(h)("components.code_sandbox.preview"),
                                "display-directive": "show:lazy"
                            }, {
                                tab: x((() => [c("div", fd, [u(Ve, {
                                    class: "tab-icon"
                                }), c("span", null, v(i(h)("components.code_sandbox.preview")), 1)])])),
                                default: x((() => [u(ji, {
                                    ref_key: "previewTab",
                                    ref: C,
                                    files: q.value,
                                    project: i(d),
                                    isAsking: e.isAsking
                                }, null, 8, ["files", "project", "isAsking"])])),
                                _: 1
                            }, 8, ["tab"]), u(i(He), {
                                name: "files",
                                tab: i(h)("components.code_sandbox.file_explorer"),
                                "display-directive": "show:lazy"
                            }, {
                                tab: x((() => [c("div", hd, [u(Ee, {
                                    class: "tab-icon"
                                }), c("span", null, v(i(h)("components.code_sandbox.file_explorer")), 1)])])),
                                default: x((() => [u(ti, {
                                    loading: y.value,
                                    error: w.value,
                                    files: q.value,
                                    project: i(d),
                                    onRefresh: F,
                                    onFileClick: D,
                                    onPreviewFile: z
                                }, null, 8, ["loading", "error", "files", "project"])])),
                                _: 1
                            }, 8, ["tab"]), L.value.filename ? (r(), E(i(He), {
                                key: 0,
                                "display-directive": "show:lazy",
                                name: "fileviewer",
                                tab: W(L.value.filename)
                            }, {
                                tab: x((() => [c("div", yd, [u(i(Ae), {
                                    class: "tab-icon"
                                }), c("span", {
                                    title: L.value.filename
                                }, v(W(L.value.filename)), 9, bd), u(i(Ne), {
                                    class: "close-icon",
                                    onClick: N(V, ["stop"])
                                })])])),
                                default: x((() => [u(wc, {
                                    ref_key: "fileViewerRef",
                                    ref: P,
                                    file: L.value.file
                                }, null, 8, ["file"])])),
                                _: 1
                            }, 8, ["tab"])) : o("", !0), u(i(He), {
                                name: "data",
                                tab: i(h)("components.code_sandbox_light.canvas.database"),
                                "display-directive": "show:lazy"
                            }, {
                                tab: x((() => [c("div", kd, [u(Cc, {
                                    class: "tab-icon"
                                }), c("span", null, v(i(h)("components.code_sandbox_light.canvas.database")), 1)])])),
                                default: x((() => [u(Gc, {
                                    ref_key: "dataTabRef",
                                    ref: M,
                                    project: i(d)
                                }, null, 8, ["project"])])),
                                _: 1
                            }, 8, ["tab"]), u(i(He), {
                                name: "deploy",
                                tab: i(h)("components.code_sandbox_light.canvas.publish"),
                                "display-directive": "show:lazy"
                            }, {
                                tab: x((() => [c("div", xd, [u(Ge, {
                                    class: "tab-icon"
                                }), c("span", null, v(i(h)("components.code_sandbox_light.canvas.publish")), 1)])])),
                                default: x((() => [u(Jr, {
                                    ref_key: "deployTab",
                                    ref: S,
                                    project: i(d)
                                }, null, 8, ["project"])])),
                                _: 1
                            }, 8, ["tab"]), u(i(He), {
                                name: "tutorial",
                                tab: i(h)("components.code_sandbox_light.canvas.tutorial"),
                                "display-directive": "show:lazy"
                            }, {
                                tab: x((() => [c("div", wd, [u(i(ad), {
                                    class: "tab-icon"
                                }), c("span", null, v(i(h)("components.code_sandbox_light.canvas.tutorial")), 1)])])),
                                default: x((() => [u(ed)])),
                                _: 1
                            }, 8, ["tab"]), o("", !0)])),
                            _: 1
                        }, 8, ["value"])]
                    })),
                    _: 1
                })], 2)
            }
        }
    },
    Cd = l(jd, [
        ["__scopeId", "data-v-11bd8f81"]
    ]),
    Sd = {
        class: "flex items-center justify-center w-[74px] h-[50px] bg-[#F5F5F5] rounded-lg dark:bg-[#333] flex-shrink-0"
    },
    Ed = {
        class: "icon"
    },
    Ad = {
        class: "flex-1 min-w-0"
    },
    Td = {
        class: "text-neutral-800 text-sm font-bold leading-tight dark:text-white whitespace-nowrap overflow-hidden text-ellipsis"
    },
    Md = {
        class: "mt-[6px] text-neutral-400 text-[10px]"
    },
    $d = l({
        __name: "MobileDocsCanvasEntry",
        props: {
            project: Object,
            isAsking: Boolean
        },
        setup(e) {
            const t = e,
                {
                    project: a,
                    isAsking: l
                } = s(t),
                o = b((() => {
                    var e;
                    const t = null == (e = a.value) ? void 0 : e.session_state;
                    if (!t) return "AI Doc";
                    const l = t.canvas_history,
                        s = t.canvas_history_id;
                    if (!l || !s) return "AI Doc";
                    const n = l.find((e => e.id === s));
                    return (null == n ? void 0 : n.name) || "AI Doc"
                })),
                d = () => {
                    a.value.session_state.canvas_mobile_visible = !0
                };
            return (e, t) => (r(), n("div", {
                class: "flex items-center gap-[10px] p-[8px] shadow-[0px_2px_4px_0px_rgba(0,0,0,0.05)] border border-gray-200 rounded-lg dark:border-[#e6e9eb40]",
                onClick: d
            }, [c("div", Sd, [c("div", Ed, [i(l) ? (r(), E(i(Ct), {
                key: 0,
                class: "black-loading-animation"
            })) : (r(), E(i(da), {
                key: 1
            }))])]), c("div", Ad, [c("div", Td, v(i(o)), 1), c("div", Md, v(i(l) ? "Generating..." : "Click to open"), 1)])]))
        }
    }, [
        ["__scopeId", "data-v-20bb0cce"]
    ]),
    Id = {
        class: "flex items-center justify-center w-[74px] h-[50px] bg-[#F5F5F5] rounded-lg dark:bg-[#333] flex-shrink-0"
    },
    Rd = {
        class: "icon"
    },
    Ld = {
        class: "flex-1 min-w-0"
    },
    Pd = {
        class: "text-neutral-800 text-sm font-bold leading-tight dark:text-white whitespace-nowrap overflow-hidden text-ellipsis"
    },
    qd = {
        class: "mt-[6px] text-neutral-400 text-[10px]"
    },
    Od = l({
        __name: "MobilePodcastsCanvasEntry",
        props: {
            project: Object,
            isAsking: Boolean
        },
        setup(e) {
            const t = e,
                {
                    project: a,
                    isAsking: l
                } = s(t),
                {
                    t: o
                } = A(),
                d = b((() => Ke(a.value, null))),
                u = b((() => Je(a.value, d.value, !0) || o("components.podcast.mobile_entry.ai_pods"))),
                p = b((() => Qe(d.value))),
                _ = b((() => Ye(a.value, d.value, !0))),
                m = b((() => l.value || p.value ? o("components.podcast.mobile_entry.generating") : _.value ? o("components.podcast.mobile_entry.click_to_open") : o("components.podcast.mobile_entry.ready_to_create"))),
                g = () => {
                    a.value.session_state.canvas_mobile_visible = !0
                };
            return (e, t) => (r(), n("div", {
                class: "flex mb-[16px] items-center gap-[10px] p-[8px] shadow-[0px_2px_4px_0px_rgba(0,0,0,0.05)] border border-gray-200 rounded-lg dark:border-[#e6e9eb40]",
                onClick: g
            }, [c("div", Id, [c("div", Rd, [i(l) ? (r(), E(i(Ct), {
                key: 0,
                class: "black-loading-animation"
            })) : (r(), E(i(Yt), {
                key: 1
            }))])]), c("div", Ld, [c("div", Pd, v(u.value), 1), c("div", qd, v(m.value), 1)])]))
        }
    }, [
        ["__scopeId", "data-v-0410dbe7"]
    ]),
    Bd = {
        class: "entry-content"
    },
    Ud = {
        class: "icon"
    },
    Fd = l({
        __name: "MobileCodeSandboxCanvasEntry",
        props: {
            project: {
                type: Object,
                required: !0
            }
        },
        emits: ["openCanvas"],
        setup(e, {
            emit: t
        }) {
            const a = e,
                l = t,
                s = () => {
                    var e;
                    (null == (e = a.project) ? void 0 : e.session_state) && (a.project.session_state.canvas_mobile_visible = !0), l("openCanvas")
                };
            return (e, t) => (r(), n("div", {
                class: "mobile-canvas-entry",
                onClick: s
            }, [c("div", Bd, [c("div", Ud, [u(i(Me))]), t[0] || (t[0] = c("div", {
                class: "text"
            }, "AI Developer", -1))])]))
        }
    }, [
        ["__scopeId", "data-v-4a5008de"]
    ]),
    Dd = {
        class: "flex items-center justify-center w-[74px] h-[50px] bg-[#F5F5F5] rounded-lg dark:bg-[#333] flex-shrink-0"
    },
    Nd = {
        class: "icon"
    },
    zd = {
        class: "flex-1 min-w-0"
    },
    Hd = {
        class: "text-neutral-800 text-sm font-bold leading-tight dark:text-white whitespace-nowrap overflow-hidden text-ellipsis"
    },
    Vd = {
        class: "mt-[6px] text-neutral-400 text-[10px]"
    },
    Gd = l({
        __name: "MobileGlobalCanvasEntry",
        props: {
            project: Object,
            isAsking: Boolean
        },
        setup(e) {
            const {
                t: t
            } = A(), a = e, {
                project: l
            } = s(a), o = he();
            return (t, a) => (r(), n("div", {
                class: "flex items-center gap-[10px] p-[8px] mb-[16px] shadow-[0px_2px_4px_0px_rgba(0,0,0,0.05)] border border-gray-200 rounded-lg dark:border-[#e6e9eb40]",
                onClick: a[0] || (a[0] = (...e) => i(o).markVisible && i(o).markVisible(...e))
            }, [c("div", Dd, [c("div", Nd, [e.isAsking ? (r(), E(i(Ct), {
                key: 0,
                class: "black-loading-animation"
            })) : (r(), E(i(Zt), {
                key: 1
            }))])]), c("div", zd, [c("div", Hd, v(i(l).name || t.$t("components.sheets_agent.untitled_sheets")), 1), c("div", Vd, v(e.isAsking ? t.$t("components.sheets_agent.generating") : t.$t("components.filedisplaycard.click-to-open")), 1)])]))
        }
    }, [
        ["__scopeId", "data-v-14ce59ec"]
    ]),
    Wd = {
        class: "notion-save-modal-dialog"
    },
    Kd = {
        class: "notion-save-modal-content"
    },
    Jd = {
        class: "notion-save-modal-header"
    },
    Qd = {
        class: "notion-save-modal-body"
    },
    Yd = {
        key: 0,
        class: "notion-save-modal-loading"
    },
    Zd = {
        key: 0,
        class: "notion-save-title"
    },
    Xd = {
        key: 1,
        class: "notion-save-modal-error"
    },
    eu = {
        class: "notion-save-modal-buttons"
    },
    tu = {
        key: 2,
        class: "page-selection-content"
    },
    au = {
        class: "custom-select-container"
    },
    lu = {
        class: "selected-option"
    },
    su = {
        class: "selected-option-icon"
    },
    nu = ["src"],
    ou = {
        key: 1
    },
    iu = {
        class: "selected-option-text"
    },
    ru = {
        key: 0,
        class: "custom-select-dropdown"
    },
    cu = ["onClick"],
    du = {
        class: "option-icon"
    },
    uu = ["src"],
    pu = {
        key: 1
    },
    vu = {
        class: "option-text"
    },
    _u = {
        class: "notion-save-modal-buttons"
    },
    mu = ["disabled"],
    gu = l({
        __name: "NotionSaveModal",
        props: {
            isVisible: {
                type: Boolean,
                default: !1
            },
            message: {
                type: Object,
                default: null
            },
            messageApi: {
                type: Object,
                default: null
            },
            attachments: {
                type: Array,
                default: () => []
            },
            autoSaveIfSinglePage: {
                type: Boolean,
                default: !0
            }
        },
        emits: ["close"],
        setup(e, {
            emit: t
        }) {
            const {
                t: l
            } = A(), s = e, i = t, d = a("loading"), u = a([]), p = a(null), f = a(!1), h = a(""), y = a(""), x = q("jsBridge"), w = a(!1);
            k((() => x.value), (e => {
                e && x.value.callHandler("support", {
                    api: "oauthLogin"
                }, (e => {
                    w.value = e
                }))
            }), {
                immediate: !0
            });
            const j = b((() => {
                    switch (d.value) {
                        case "loading":
                            return l("components.custom_tools.notion.connecting_to_notion");
                        case "error":
                            return l("components.custom_tools.notion.error");
                        case "page-selection":
                            return l("components.custom_tools.notion.save_to_notion_under");
                        default:
                            return l("components.custom_tools.notion.save_to_notion")
                    }
                })),
                C = e => "string" == typeof e && (e.startsWith("http") || e.startsWith("data:image/") || e.includes(".png") || e.includes(".jpg") || e.includes(".jpeg") || e.includes(".gif")),
                S = () => {
                    f.value = !1, i("close")
                },
                E = () => {
                    f.value = !f.value
                },
                T = async e => {
                    try {
                        const t = Ze(s.message),
                            a = (e => {
                                const t = [/^# (.+)$/m, /^## (.+)$/m, /^### (.+)$/m, /^#### (.+)$/m, /^##### (.+)$/m, /^###### (.+)$/m];
                                for (const l of t) {
                                    const t = e.match(l);
                                    if (t && t[1]) return t[1].trim().substring(0, 80)
                                }
                                const a = e.split("\n")[0].trim();
                                if (a) {
                                    const e = a.match(/^(.+?[.!?])\s/);
                                    return (e ? e[1] : a).substring(0, 80)
                                }
                                return l("components.custom_tools.notion.chat_with_genspark_assistant")
                            })(t);
                        d.value = "loading", y.value = a;
                        const n = "database" === e.type ? "database_id" : "page_id",
                            o = s.attachments.map((e => ({
                                name: e.name || "Unnamed file",
                                url: e.url,
                                type: e.type || "application/octet-stream"
                            })));
                        await Xe.saveContent(e.id, t, a, n, o), S(), s.messageApi && s.messageApi.success(l("components.custom_tools.notion.success_saved_to", {
                            title: e.title || "Notion"
                        }))
                    } catch (t) {
                        S(), s.messageApi && s.messageApi.error(t.message || l("components.custom_tools.notion.failed_to_save"))
                    }
                },
                M = async () => {
                    p.value && await T(p.value)
                },
                R = e => {
                    f.value && !e.target.closest(".custom-select-container") && (f.value = !1)
                },
                L = e => {
                    s.isVisible && ("Escape" === e.key ? S() : "Enter" === e.key && "page-selection" === d.value && M())
                };
            return k((() => s.isVisible), (e => {
                e ? F((() => {
                    (async () => {
                        if (s.isVisible && s.message) try {
                            if (d.value = "loading", !(await Xe.checkAuthStatus()).authenticated) return S(), void Xe.authenticate(window.location.href, w.value);
                            j.value = l("components.custom_tools.notion.loading_notion_pages");
                            const e = await Xe.fetchPages();
                            if (1001 === e.status_code) return s.messageApi && s.messageApi.info(e.message), S(), void Xe.authenticate(window.location.href, w.value);
                            if (e && e.error) return d.value = "error", h.value = e.message, void(s.messageApi && s.messageApi.error(l("components.custom_tools.notion.error_loading_pages", {
                                message: e.message
                            })));
                            if (!e || 0 === e.length) return d.value = "error", h.value = l("components.custom_tools.notion.no_pages_found"), void(s.messageApi && s.messageApi.error(l("components.custom_tools.notion.no_pages_found")));
                            u.value = e, p.value = e[0], 1 === e.length && s.autoSaveIfSinglePage ? await T(e[0]) : d.value = "page-selection"
                        } catch (e) {
                            d.value = "error", h.value = e.message || l("components.custom_tools.notion.failed_to_save"), s.messageApi && s.messageApi.error(e.message || l("components.custom_tools.notion.failed_to_save"))
                        }
                    })()
                })) : (d.value = "loading", u.value = [], p.value = null, f.value = !1, h.value = "", y.value = "")
            })), $((() => {
                document.addEventListener("click", R), document.addEventListener("keydown", L)
            })), I((() => {
                document.removeEventListener("click", R), document.removeEventListener("keydown", L)
            })), (t, a) => {
                var l, s;
                return e.isVisible ? (r(), n("div", {
                    key: 0,
                    class: "notion-save-modal-overlay",
                    onClick: N(S, ["self"])
                }, [c("div", Wd, [c("div", Kd, [c("div", Jd, [c("h3", null, v(j.value), 1), c("button", {
                    class: "close-button",
                    onClick: S
                }, "×")]), c("div", Qd, ["loading" === d.value ? (r(), n("div", Yd, [a[0] || (a[0] = c("div", {
                    class: "loading-spinner"
                }, null, -1)), y.value ? (r(), n("p", Zd, v(y.value), 1)) : o("", !0)])) : "error" === d.value ? (r(), n("div", Xd, [c("p", null, v(h.value), 1), c("div", eu, [c("button", {
                    class: "notion-save-modal-button notion-save-modal-button-primary",
                    onClick: S
                }, v(t.$t("components.custom_tools.notion.close")), 1)])])) : "page-selection" === d.value ? (r(), n("div", tu, [c("div", au, [c("div", {
                    class: _(["custom-select-header", {
                        active: f.value
                    }]),
                    onClick: E
                }, [c("div", lu, [c("span", su, [p.value && C(p.value.icon) ? (r(), n("img", {
                    key: 0,
                    src: p.value.icon,
                    alt: "Page Icon",
                    style: {
                        width: "24px",
                        height: "24px"
                    }
                }, null, 8, nu)) : (r(), n("span", ou, v((null == (l = p.value) ? void 0 : l.icon) || "📄"), 1))]), c("span", iu, v((null == (s = p.value) ? void 0 : s.title) || ""), 1)]), a[1] || (a[1] = c("div", {
                    class: "select-arrow"
                }, [c("svg", {
                    width: "14",
                    height: "8",
                    viewBox: "0 0 14 8",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg"
                }, [c("path", {
                    d: "M1 1.5L7 6.5L13 1.5",
                    stroke: "currentColor",
                    "stroke-width": "2",
                    "stroke-linecap": "round",
                    "stroke-linejoin": "round"
                })])], -1))], 2), f.value ? (r(), n("div", ru, [(r(!0), n(m, null, g(u.value, ((e, t) => {
                    var a;
                    return r(), n("div", {
                        key: e.id,
                        class: _(["custom-select-option", {
                            selected: (null == (a = p.value) ? void 0 : a.id) === e.id
                        }]),
                        onClick: t => (e => {
                            p.value = e, f.value = !1
                        })(e)
                    }, [c("span", du, [C(e.icon) ? (r(), n("img", {
                        key: 0,
                        src: e.icon,
                        alt: "Page Icon",
                        style: {
                            width: "24px",
                            height: "24px"
                        }
                    }, null, 8, uu)) : (r(), n("span", pu, v(e.icon || "📄"), 1))]), c("span", vu, v(e.title), 1)], 10, cu)
                })), 128))])) : o("", !0)]), c("div", _u, [c("button", {
                    class: "notion-save-modal-button notion-save-modal-button-cancel",
                    onClick: S
                }, v(t.$t("components.custom_tools.notion.cancel")), 1), c("button", {
                    class: "notion-save-modal-button notion-save-modal-button-primary",
                    onClick: M,
                    disabled: !p.value
                }, v(t.$t("components.custom_tools.notion.save")), 9, mu)])])) : o("", !0)])])])])) : o("", !0)
            }
        }
    }, [
        ["__scopeId", "data-v-dec65e19"]
    ]),
    fu = {
        key: 0,
        class: "chat-sessions-wrapper"
    },
    hu = {
        key: 0,
        class: "loading-chat-session"
    },
    yu = {
        key: 0,
        class: "chat-session conversation-content"
    },
    bu = {
        class: "chat-session-time"
    },
    ku = {
        class: "collapsed-project-content"
    },
    xu = ["onClick"],
    wu = ["onClick"],
    ju = {
        key: 0,
        class: "bubble"
    },
    Cu = {
        class: "desc"
    },
    Su = {
        class: "content"
    },
    Eu = ["message-content-id", "onPaste", "onBlur", "onKeydown"],
    Au = {
        class: "desc"
    },
    Tu = {
        class: "content"
    },
    Mu = {
        key: 1,
        class: "using-tool-call"
    },
    $u = {
        class: "label"
    },
    Iu = {
        class: "icon"
    },
    Ru = {
        class: "name"
    },
    Lu = {
        class: "arguments"
    },
    Pu = {
        key: 0,
        class: "view-tool-call-result"
    },
    qu = ["onClick"],
    Ou = {
        key: 0,
        class: "inline-component"
    },
    Bu = ["onClick"],
    Uu = {
        class: "compact-toggle-text"
    },
    Fu = {
        class: "icon"
    },
    Du = {
        key: 0,
        class: "compact-summary"
    },
    Nu = {
        class: "compact-summary-content"
    },
    zu = {
        key: 1
    },
    Hu = {
        key: 0,
        class: "buttons"
    },
    Vu = ["onClick"],
    Gu = {
        class: "icon"
    },
    Wu = {
        class: "label"
    },
    Ku = {
        class: "desc"
    },
    Ju = {
        class: "content"
    },
    Qu = {
        key: 0
    },
    Yu = {
        key: 1
    },
    Zu = {
        key: 2
    },
    Xu = {
        key: 3,
        class: "image_url_wrapper"
    },
    ep = ["src"],
    tp = {
        key: 4,
        class: "private-file-wrappers"
    },
    ap = {
        class: "file-wrapper"
    },
    lp = {
        class: "icon file-icon"
    },
    sp = {
        class: "file-info"
    },
    np = {
        class: "file-name"
    },
    op = {
        class: "file-size"
    },
    ip = {
        key: 0,
        class: "browser-extension-bar w-[calc(100%-24px)] min-h-[46px] flex items-center gap-[12px] py-2.5 px-[13px] rounded-[16px] bg-[#F5F5F5] dark:bg-[#232425]"
    },
    rp = ["src"],
    cp = {
        class: "text-neutral-800 text-sm leading-tight dark:border-[#ffffff20] break-all dark:text-neutral-200 line-clamp-4"
    },
    dp = {
        key: 1,
        class: "browser-extension-bar w-[calc(100%-24px)] min-h-[46px] flex items-center gap-[12px] py-2.5 px-[13px] rounded-[16px] bg-[#F5F5F5] dark:bg-[#1A1B1C]"
    },
    up = {
        class: "text-neutral-800 text-sm leading-tight dark:border-[#ffffff20] break-all dark:text-neutral-200 line-clamp-4"
    },
    pp = {
        key: 2,
        class: "browser-extension-bar w-[calc(100%-24px)] min-h-[46px] flex items-center gap-[12px] py-2.5 px-[13px] rounded-[16px] bg-[#F5F5F5] dark:bg-[#232425]"
    },
    vp = {
        class: "text-neutral-800 text-sm leading-tight dark:border-[#ffffff20] break-all dark:text-neutral-200 line-clamp-4"
    },
    _p = {
        class: "datasets-agent-tips"
    },
    mp = {
        class: "datasets-agent-tips"
    },
    gp = {
        class: "docs-agent-welcome-title"
    },
    fp = {
        class: "datasets-agent-tips"
    },
    hp = {
        class: "docs-agent-welcome-title"
    },
    yp = {
        class: "datasets-agent-tips"
    },
    bp = {
        class: "datasets-agent-tips"
    },
    kp = {
        key: 4,
        class: "loading-placeholder"
    },
    xp = {
        key: 0,
        class: "conversation-content"
    },
    wp = {
        key: 0,
        class: "chat-session-time"
    },
    jp = {
        class: "collapsed-project-content"
    },
    Cp = ["onClick"],
    Sp = ["onClick"],
    Ep = {
        key: 0,
        class: "bubble"
    },
    Ap = {
        class: "desc"
    },
    Tp = {
        class: "content"
    },
    Mp = ["message-content-id", "onPaste", "onBlur", "onKeydown"],
    $p = {
        class: "desc"
    },
    Ip = {
        class: "content"
    },
    Rp = {
        key: 1,
        class: "using-tool-call"
    },
    Lp = {
        class: "label"
    },
    Pp = {
        class: "icon"
    },
    qp = {
        class: "name"
    },
    Op = {
        class: "arguments"
    },
    Bp = {
        key: 0,
        class: "view-tool-call-result"
    },
    Up = ["onClick"],
    Fp = {
        key: 0,
        class: "inline-component"
    },
    Dp = ["onClick"],
    Np = {
        class: "compact-toggle-text"
    },
    zp = {
        class: "icon"
    },
    Hp = {
        key: 0,
        class: "compact-summary"
    },
    Vp = {
        class: "compact-summary-content"
    },
    Gp = {
        key: 0,
        class: "html-editor-selection"
    },
    Wp = {
        key: 0,
        class: "buttons border-add"
    },
    Kp = ["onClick"],
    Jp = {
        class: "icon"
    },
    Qp = {
        class: "label"
    },
    Yp = {
        class: "icon deep-research-icon"
    },
    Zp = {
        class: "label",
        style: {
            "margin-left": "4px"
        }
    },
    Xp = ["onClick"],
    ev = {
        class: "icon"
    },
    tv = {
        class: "label"
    },
    av = {
        class: "icon"
    },
    lv = {
        class: "label"
    },
    sv = {
        class: "desc"
    },
    nv = {
        class: "content"
    },
    ov = {
        key: 0
    },
    iv = {
        key: 1
    },
    rv = {
        key: 2
    },
    cv = {
        key: 3,
        class: "image_url_wrapper"
    },
    dv = ["src"],
    uv = {
        key: 4,
        class: "private-file-wrappers"
    },
    pv = {
        class: "file-wrapper"
    },
    vv = {
        class: "icon file-icon"
    },
    _v = {
        class: "file-info"
    },
    mv = {
        class: "file-name"
    },
    gv = {
        class: "file-size"
    },
    fv = {
        key: 3,
        class: "bubble context_length_exceeded"
    },
    hv = {
        key: 0,
        class: "new-chat-session"
    },
    yv = {
        class: "desc"
    },
    bv = {
        class: "icon"
    },
    kv = {
        class: "label"
    },
    xv = {
        class: "right"
    },
    wv = {
        key: 4,
        class: "bubble retry"
    },
    jv = {
        class: "left"
    },
    Cv = {
        class: "right"
    },
    Sv = {
        key: 5,
        class: "bubble retry"
    },
    Ev = {
        class: "left"
    },
    Av = {
        class: "right"
    },
    Tv = {
        key: 6,
        class: "w-[70%] bg-[#FAFAFA] py-[16px] px-[16px] rounded-[16px] break-words"
    },
    Mv = ["user-message-content-id", "onPaste", "onBlur", "onKeydown"],
    $v = {
        class: "p-0 m-0"
    },
    Iv = {
        class: "flex gap-[16px] justify-end"
    },
    Rv = ["onClick"],
    Lv = ["onClick"],
    Pv = ["onClick"],
    qv = {
        class: "message-action-icon icon p-[4px] hover:bg-[#F5F5F5] rounded-[6px]"
    },
    Ov = ["onClick"],
    Bv = {
        class: "message-action-icon icon p-[4px] hover:bg-[#F5F5F5] rounded-[6px]"
    },
    Uv = {
        key: 1,
        class: "sandbox_status"
    },
    Fv = {
        key: 2,
        class: "is_asking"
    },
    Dv = {
        class: "input-wrapper-wrapper-inner"
    },
    Nv = {
        key: 0,
        class: "recommend-queries"
    },
    zv = {
        class: "recommend-queries-inner"
    },
    Hv = ["onClick"],
    Vv = {
        key: 0,
        class: "icon"
    },
    Gv = {
        class: "text"
    },
    Wv = {
        key: 3,
        class: "mobile-canvas-entry"
    },
    Kv = {
        key: 7,
        class: "mobile-canvas-entry"
    },
    Jv = {
        key: 8,
        class: "mobile-canvas-entry"
    },
    Qv = {
        key: 9,
        class: "prompt-input-wrapper"
    },
    Yv = {
        key: 10,
        class: "realtime-input-wrapper"
    },
    Zv = {
        key: 0,
        class: "mobile-sidebar-page"
    },
    Xv = {
        key: 0,
        class: "tool-call-result-sidebar-mask"
    },
    e_ = {
        key: 0,
        class: "tool-call-result-sidebar j-tool-call-result-sidebar"
    },
    t_ = {
        class: "tool-call-result-sidebar-inner"
    },
    a_ = {
        class: "icon"
    },
    l_ = {
        class: "tool-call-result-sidebar-inner"
    },
    s_ = {
        class: "tool-call-result-sidebar-inner"
    },
    n_ = "moa-chat-modelsSelected",
    o_ = '<span class="cursor">█</span>',
    i_ = '<div class="thinking_prompt">Thinking...</div>',
    r_ = l({
        __name: "chat_agent",
        props: {
            project: {
                type: Object,
                required: !0
            },
            no_input: {
                type: Boolean,
                default: !1
            },
            initialQuery: {
                type: String,
                default: ""
            },
            initialText: {
                type: String,
                default: ""
            },
            readOnly: {
                type: Boolean,
                default: !1
            },
            externalRequestWebKnowledge: {
                type: Boolean,
                default: !1
            },
            externalModelsSelected: {
                type: String,
                default: "gpt-4.1"
            },
            searchMode: {
                type: Boolean,
                default: !1
            },
            inputPlaceHolder: {
                type: String,
                default: "Message"
            },
            styleClass: {
                type: String,
                default: "moa"
            },
            noAutoScroll: {
                type: Boolean,
                default: !1
            },
            realtimeMode: {
                type: Boolean,
                default: !1
            },
            showRealtimeToggleButton: {
                type: Boolean,
                default: void 0
            },
            is_replaying: {
                type: Boolean,
                default: !1
            },
            isPhoneCallCardButtonDisabled: {
                type: Boolean,
                default: !1
            },
            is_slide_agent: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["newSessionCreated", "requestCreateNewSession", "finish-loading-deep-dive-search-result", "phoneCallTaskCreated", "quotaExceed", "requestLoadProject", "triggerAction", "personalizationSettingsSaved", "messageUpdated", "agentAskStart", "agentAskFinish"],
        setup(t, {
            expose: l,
            emit: h
        }) {
            const w = {
                    mounted(e) {
                        F((() => {
                            if (e.focus(), "true" === e.contentEditable) {
                                const t = document.createRange(),
                                    a = window.getSelection();
                                t.selectNodeContents(e), t.collapse(!1), a.removeAllRanges(), a.addRange(t)
                            }
                        }))
                    }
                },
                C = t,
                T = h,
                M = W(),
                R = M.query.action,
                P = na(),
                {
                    t: O
                } = A(),
                {
                    project: B,
                    no_input: N,
                    readOnly: H,
                    searchMode: V,
                    inputPlaceHolder: G,
                    styleClass: se,
                    noAutoScroll: ne,
                    is_replaying: oe,
                    isPhoneCallCardButtonDisabled: ie,
                    is_slide_agent: ce
                } = s(C),
                ge = a(null),
                be = a(null),
                ke = a(null),
                Ce = a(null),
                Se = a(null),
                Ee = a(null),
                Ae = a(null),
                Te = a(!0),
                Me = a(!!M.query.use_browser_use_test_mode),
                $e = a(!!M.query.use_browser_use_test_mode),
                Ie = a(M.query.use_model),
                Re = a(!!M.query.use_terminal_use),
                Le = a(!!M.query.dataframe_enhanced),
                Pe = a(!!M.query.enable_jupyter),
                qe = a(M.query.ai_sheets_tools),
                Oe = a(!!M.query.use_python_workspace),
                Be = a(M.query.experiment_flag),
                Ue = a(C.realtimeMode),
                Fe = a(!1),
                De = a({}),
                Ne = a([]),
                ze = a(!1);
            a("duo");
            const He = a(!1),
                Ve = we(),
                {
                    prompt: Ge,
                    images: We,
                    files: Ke,
                    requestWebKnowledge: Je,
                    modelsSelected: Qe,
                    browserExtensionData: Ye,
                    agentSelectedModel: Ze
                } = y(Ve),
                {
                    setPrompt: Xe,
                    setImages: mt,
                    setFiles: ht
                } = Ve,
                {
                    setInputStatus: bt
                } = Ot(),
                {
                    imageModelMap: kt,
                    extra_data: xt
                } = $n(C),
                {
                    extra_data: wt
                } = (e => {
                    const t = re(),
                        {
                            modelsSelected: l,
                            selectedAspectRatio: s,
                            selectedDuration: n,
                            reflectionEnabled: o
                        } = y(t),
                        i = a([]),
                        r = a({}),
                        c = b((() => e.debug)),
                        d = e => {
                            var t;
                            const a = Lt.find((t => t.name === e));
                            return !(!a || "auto" !== s.value && a.permitted_aspect_ratios && !a.permitted_aspect_ratios.includes(s.value) || (null == (t = promptImages.value) ? void 0 : t.length) > 0 && !a.support_i2v)
                        },
                        u = b((() => {
                            const e = ["kling/v1.6/standard", "gemini/veo2", "pixverse/v3.5/turbo", "lumadream/ray-2-flash"].filter((e => d(e)));
                            return p.log("defaultAutoModels", e), e
                        }));
                    return {
                        extra_data: b((() => ({
                            model_configs: ("auto" === l.value ? getAutoModeModels(i.value, u.value, { ...r.value
                            }) : [l.value]).map((e => ({
                                model: e,
                                aspect_ratio: s.value,
                                duration: n.value,
                                reflection_enabled: o.value,
                                ...c.value && {
                                    debug: !0
                                }
                            }))),
                            imageModelMap: r.value
                        }))),
                        lastUsedModels: i,
                        isModelValid: d,
                        imageModelMap: r
                    }
                })(C),
                {
                    extra_data: Ct
                } = (e => {
                    const t = we(),
                        {
                            modelsSelected: a,
                            requestWebKnowledge: l
                        } = y(t);
                    return {
                        extra_data: b((() => ({
                            models: a.value.split(","),
                            run_with_another_model: e.value,
                            request_web_knowledge: l.value
                        })))
                    }
                })(Fe),
                St = gt(),
                {
                    slideCanvasVisible: Et
                } = y(St),
                At = he(),
                Tt = b((() => "slide_agent" === B.value.type || "slides_agent" === B.value.type)),
                Mt = e => {
                    if (!e) return null;
                    const t = e.match(/<source_project_type>(.*?)<\/source_project_type>/);
                    return t ? t[1] : null
                },
                Rt = a(!1),
                Pt = a(!1),
                zt = a(!1),
                ea = a(!1),
                ta = a({}),
                aa = a(null);
            let la = [];
            const ca = e => {
                    var t;
                    const a = null == (t = null == e ? void 0 : e.session_state) ? void 0 : t.error_message;
                    if (!a) return "internal service error";
                    const l = a.match(/The model returned the following errors: (.+?)(?="|\\"|$)/);
                    if (l && l[1]) {
                        return l[1].replace(/\\u([0-9a-fA-F]{4})/g, ((e, t) => String.fromCharCode(parseInt(t, 16))))
                    }
                    return a
                },
                da = e => {
                    if (!e || !e.function || !e.function.name) return null;
                    const t = la || [];
                    let a = t.find((t => t.name == e.function.name));
                    return a || (a = e.function.name.startsWith("mcp_") ? t.find((e => "mcp_xxxxxxxx" == e.name)) : t.find((e => "plain" == e.name))), a.check_can_be_opened_in_sidebar && (a.can_be_opened_in_sidebar = a.check_can_be_opened_in_sidebar(e)), a
                },
                ua = e => la.find((t => t.name == e));
            $((() => {
                la = aa.value.tool_call_configs
            }));
            const pa = b((() => Tt.value && (null == La ? void 0 : La.value) && !La.value.getExpandedToolCallResultMessage() && Et.value && !_a.value)),
                va = b((() => p.isMobile() && Tt.value)),
                _a = b((() => {
                    var e, t, a, l;
                    return "slides_agent" === (null == (e = B.value) ? void 0 : e.type) && !(null == (t = B.value) ? void 0 : t.id) && !(null == (l = null == (a = B.value) ? void 0 : a.session_state) ? void 0 : l.is_blank)
                })),
                wa = async (e, t) => {
                    const {
                        url: a,
                        width: l,
                        height: s = 720,
                        timeout: n = 30
                    } = t.payload || {};
                    let o = {
                            status: "completed"
                        },
                        i = {};
                    try {
                        if (!a || !l) throw new Error("URL and width are required parameters");
                        const e = document.createElement("div");
                        e.style.position = "fixed", e.style.top = "-10000px", e.style.left = "-10000px", e.style.width = `${l}px`, e.style.height = `${s}px`, e.style.overflow = "hidden", e.style.pointerEvents = "none", document.body.appendChild(e);
                        const t = document.createElement("iframe");
                        t.style.width = "100%", t.style.height = "100%", t.style.border = "none", t.src = a, e.appendChild(t);
                        const o = await new Promise(((a, l) => {
                            let o;
                            const i = () => {
                                o && clearTimeout(o), e.parentNode && document.body.removeChild(e)
                            };
                            t.onload = () => {
                                try {
                                    if (t.contentWindow) {
                                        t.contentWindow.postMessage({
                                            type: "PARENT_CALL_FUNCTION",
                                            payload: {
                                                functionName: "getHeight",
                                                args: [],
                                                callbackId: `height-${Date.now()}`
                                            }
                                        }, "*");
                                        const e = l => {
                                            if (l.source === t.contentWindow && "PARENT_CALL_FUNCTION_RESPONSE" === l.data.type)
                                                if (window.removeEventListener("message", e), i(), l.data.payload.success) a(l.data.payload.result);
                                                else try {
                                                    const e = t.contentDocument.documentElement.scrollHeight || t.contentDocument.body.scrollHeight || s;
                                                    a(e)
                                                } catch (n) {
                                                    a(s)
                                                }
                                        };
                                        window.addEventListener("message", e), setTimeout((() => {
                                            window.removeEventListener("message", e);
                                            try {
                                                const e = t.contentDocument.documentElement.scrollHeight || t.contentDocument.body.scrollHeight || s;
                                                i(), a(e)
                                            } catch (l) {
                                                i(), a(s)
                                            }
                                        }), 3e3)
                                    } else i(), a(s)
                                } catch (e) {
                                    i(), a(s)
                                }
                            }, t.onerror = () => {
                                i(), l(new Error("Failed to load URL"))
                            }, o = setTimeout((() => {
                                i(), l(new Error(`Timeout after ${n} seconds`))
                            }), 1e3 * n)
                        }));
                        i.url = {
                            success: !0,
                            height: o,
                            url: a,
                            width: l
                        }
                    } catch (r) {
                        o.status = "error", o.error_message = r.message, i.url = {
                            success: !1,
                            error: r.message,
                            url: a || "unknown"
                        }
                    }
                    if (e.callback_url) {
                        o.results = i;
                        try {
                            await fetch(e.callback_url, {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json"
                                },
                                body: JSON.stringify(o)
                            })
                        } catch (c) {}
                    }
                    return o
                },
                ja = e => {
                    if (T("triggerAction", e), ee.emit("triggerAction", e), e.instructions && Array.isArray(e.instructions))
                        for (const t of e.instructions)
                            if ("url.loadAndMeasureHeight" === t.action) return void wa(e, t);
                    ge.value ? ge.value.runAction(e) : be.value && be.value.runAction(e)
                },
                Ca = ({
                    callId: e,
                    name: t,
                    args: a
                }) => {
                    let l;
                    try {
                        if ("string" == typeof a) l = JSON.parse(a);
                        else {
                            if ("object" != typeof a || null === a) return;
                            l = a
                        }
                        p.log("Successfully parsed args:", l);
                        const e = null == l ? void 0 : l.query;
                        p.log("Extracted query:", e), e && (Fl.value = !0, Aa.value = "gpt-4o-mini", Ga(e))
                    } catch (s) {
                        return
                    }
                };
            k((() => C.realtimeMode), (e => {
                Ue.value = e
            }));
            const Sa = e => {
                    p.log("Showing search sidebar"), p.log("showSearchSideBar", e);
                    const t = {
                        searchResults: e.searchResults,
                        keywords: e.keywords,
                        jumpToUrl: e.jumpToUrl
                    };
                    p.log("showSearchSideBarData", t), Pl.value = t, Rl.value = !0
                },
                Ea = de.setup().modelsConfig,
                Aa = a(C.externalModelsSelected);
            if (!V.value) {
                const e = localStorage.getItem(n_);
                e && Ea.find((t => t.name == e)) && (Aa.value = e)
            }
            k(Aa, (() => {
                localStorage.setItem(n_, Aa.value)
            }));
            const Ta = () => {
                    {
                        let t = {},
                            a = [];
                        try {
                            const e = localStorage.getItem("installedCustomTools");
                            if (e) {
                                const a = JSON.parse(e);
                                "object" == typeof a && null !== a && (t = a)
                            }
                        } catch (e) {}
                        try {
                            const e = localStorage.getItem("unselectedCustomTools");
                            if (e) {
                                const t = JSON.parse(e);
                                Array.isArray(t) && (a = t.filter((e => "string" == typeof e)))
                            }
                        } catch (e) {}
                        const l = Object.keys(t),
                            s = l.filter((e => !a.includes(e)));
                        return p.log("getLocalStorageCustomTools calculated:", {
                            installed: l,
                            unselected: a,
                            actuallySelected: s
                        }), s
                    }
                },
                Ma = b((() => {
                    if (R === je.GENERATE_IMAGE) return xt.value;
                    if (R === je.GENERATE_VIDEO) return wt.value;
                    if (R === je.DEEP_RESEARCH) return Ct.value;
                    if (B.value && ("slide_agent" === B.value.type || "slides_agent" === B.value.type)) {
                        const e = gt();
                        return {
                            slides_agent_params: {
                                version_number: e.currentVersion.version_number,
                                page_number: e.pageNumber,
                                dom_path: e.domPath
                            },
                            ...De.value
                        }
                    }
                    return Fl.value && Aa.value == Ea[0].name && (Aa.value = Ea[0].search_model_name), Fl.value || Aa.value != Ea[0].search_model_name || (Aa.value = Ea[0].name), {
                        models: Aa.value.split(","),
                        run_with_another_model: Fe.value,
                        request_web_knowledge: Fl.value,
                        speed_mode: Ue.value,
                        use_webpage_capture_screen: Te.value,
                        use_browser_use: Me.value,
                        use_python_workspace: Oe.value,
                        use_browser_use_test_mode: $e.value,
                        use_terminal_use: Re.value,
                        dataframe_enhanced: Le.value,
                        enable_jupyter: Pe.value,
                        ai_sheets_tools: qe.value,
                        ...Ie.value || Ze.value ? {
                            use_model: Ie.value || Ze.value
                        } : {},
                        custom_tools: Ta(),
                        experiment_flag: Be.value,
                        ...De.value
                    }
                })),
                $a = a(null),
                Ia = a([]),
                Ra = a([]),
                La = a(null),
                Pa = () => {
                    Ue.value = !1
                },
                qa = () => {
                    B.value && B.value.session_state && B.value.session_state.messages && La.value && La.value.setMessages(B.value.session_state.messages)
                },
                Oa = a(!1),
                Ba = a([]),
                Fa = a({}),
                Da = () => {
                    B.value && B.value.session_state && (Ba.value = B.value.session_state.file_contents, Fa.value = B.value.session_state.meta_data, Oa.value = !0)
                };
            k((() => B.value && B.value.id), (() => {
                qa(), La.value && !B.value.id && La.value.cancelAskAgent(), us.value = []
            })), k((() => B.value), (() => {
                qa(), us.value = []
            })), k((() => {
                var e, t;
                return null == (t = null == (e = B.value) ? void 0 : e.session_state) ? void 0 : t.file_contents
            }), (() => {
                Da()
            }));
            const Na = b((() => {
                    const e = La.value.getMessagesRef().value;
                    if (e.length > 0) return e[e.length - 1]
                })),
                za = b((() => {
                    var e;
                    if (Ne.value.length > 0 && Ne.value[0].messages.length > 0) return Ne.value[0].messages[0];
                    const t = null == (e = La.value) ? void 0 : e.getMessagesRef().value;
                    return t && t.length > 0 ? t[0] : void 0
                })),
                Ha = a(null),
                Va = async () => {
                    if ("video_studio" == B.value.type && Ce.value) {
                        const e = await Ce.value.getContextDetails();
                        if (e && e.editorState) return {
                            video_studio: e.editorState
                        }
                    }
                    return null
                },
                Ga = (e, t, a, l) => {
                    var s, n, o, i, r, c, d;
                    if (La.value.is_asking) return void P.warning("Please wait for the previous message to finish");
                    De.value = { ...De.value
                    };
                    const u = La.value.getMessagesRef().value;
                    if (u.length > 0) {
                        const e = u[u.length - 1];
                        if (e.session_state && e.session_state.context_length_exceeded) return void P.warning("Context length exceeded, please open a new session")
                    }
                    Fe.value = !1;
                    let p = {};
                    Tt.value && (null !== St.domPath && (p.dom_path = St.domPath), null !== St.currentVersion.version_number && (p.version_number = St.currentVersion.version_number), null !== St.pageNumber && (p.page_number = St.pageNumber));
                    let v = null;
                    if (Tt.value && St.selectedTemplate) {
                        const e = St.selectedTemplate;
                        v = {
                            template_id: e.id,
                            template_name: e.name,
                            template_screenshot_url: e.pages && e.pages.length > 0 ? e.pages[0].screenshot_url : null,
                            template_width: e.width || 1280,
                            template_height: e.height || 720
                        }, St.setSelectedTemplate(null)
                    }
                    Se.value && (Se.value.onSubmitPrompt(e, t, a), !(null == (s = B.value) ? void 0 : s.id) && (null == (i = null == (o = null == (n = B.value) ? void 0 : n.session_state) ? void 0 : o.docs_agent) ? void 0 : i.type) && (e += `\n\n in ${null==(d=null==(c=null==(r=B.value)?void 0:r.session_state)?void 0:c.docs_agent)?void 0:d.type} format`)), Ee.value && Ee.value.onSubmitPrompt(e, t, a), La.value.submitPrompt(e, "", t, a, {}, p, Ye.value.prompt, l, v), Ve.setBrowserExtensionData({ ...Ye.value,
                        prompt: ""
                    }), Xe(""), mt([]), ht([]), Ia.value = [], Ra.value = []
                },
                Wa = q("jsBridge"),
                Ka = a(!1);
            p.isGensparkApp() ? (Ka.value = !0, setTimeout((() => {
                Ka.value && (Ka.value = !1)
            }), 3e3)) : Ka.value = !1;
            const Ja = a(!1),
                Qa = a(!1),
                Ya = a(!1),
                Za = a(!1),
                Xa = a(!1),
                el = a(!1),
                tl = a(!1),
                al = a(!1),
                ll = a(!1),
                nl = a(!1),
                ol = a(null),
                il = a(!1),
                rl = a(!1),
                dl = a(!1),
                ul = a(!1),
                pl = a(!1),
                vl = a(!1),
                _l = a(!1),
                ml = a(!1),
                gl = a(!1),
                fl = a(!1),
                hl = a(!1),
                yl = q("updateUserInfo"),
                bl = q("currentUser"),
                kl = () => {
                    B.value.session_state.messages = [], B.value.session_state.current_chat_session_id = null, Ne.value = [], qa()
                },
                xl = async e => {
                    const t = await fetch(`/api/agent/get_chat_session?project_id=${B.value.id}&chat_session_id=${e}`);
                    if (!t.ok) return;
                    const a = await t.json();
                    return 0 === a.status ? a.data : void 0
                },
                wl = async e => {
                    const t = await xl(e);
                    t && (B.value.session_state.messages = t.messages, B.value.session_state.current_chat_session_name = t.name, B.value.session_state.current_chat_session_id = t.id, qa())
                },
                jl = () => {
                    var e;
                    (null == (e = Ae.value) ? void 0 : e.switchTab) && (Ae.value.switchTab("github"), B.value && B.value.session_state && (B.value.session_state.canvas_mobile_visible = !0))
                };
            K((() => {
                ll.value = al.value && At.visible
            }));
            const Cl = b((() => p.isMobile() && al.value));
            K((() => {
                var e;
                Ja.value = "super_data_set" === B.value.type, Qa.value = B.value.type === ma || B.value.type === ga || "sheets_agent_new" === B.value.type, Ya.value = "podcasts_agent" === B.value.type, Za.value = "docs_agent" === B.value.type, Xa.value = "html_agent" === B.value.type, el.value = "code_sandbox" === B.value.type, tl.value = "code_sandbox_light" === B.value.type, al.value = (Ja.value || Qa.value) && ("sheets_agent_new" === B.value.type || !(La.value && La.value.getExpandedToolCallResultMessage())), il.value = "docs_agent" === B.value.type && !(La.value && La.value.getExpandedToolCallResultMessage()), rl.value = "markdown_agent" === B.value.type && !(La.value && La.value.getExpandedToolCallResultMessage()), dl.value = "html_agent" === B.value.type && !(La.value && La.value.getExpandedToolCallResultMessage()), ul.value = "poster_agent" === B.value.type && !(La.value && La.value.getExpandedToolCallResultMessage()), ml.value = "podcasts_agent" === B.value.type && !(La.value && La.value.getExpandedToolCallResultMessage()), hl.value = "video_studio" === B.value.type && (null == (e = null == bl ? void 0 : bl.value) ? void 0 : e.gk_dogfood) && !(La.value && La.value.getExpandedToolCallResultMessage()), pl.value = "image_agent" === B.value.type && !(La.value && La.value.getExpandedToolCallResultMessage()), vl.value = "test_cooperation_agent" === B.value.type && !(La.value && La.value.getExpandedToolCallResultMessage()), _l.value = "ai_inbox" === B.value.type && !(La.value && La.value.getExpandedToolCallResultMessage()), gl.value = "code_sandbox" === B.value.type && !(La.value && La.value.getExpandedToolCallResultMessage()), fl.value = "code_sandbox_light" === B.value.type && !(La.value && La.value.getExpandedToolCallResultMessage()), nl.value = pa.value || ll.value || il.value || rl.value || dl.value || ul.value || ml.value || hl.value || pl.value || vl.value || _l.value || gl.value || fl.value, Qa.value && !ol.value && La.value && (ol.value = fa(B, La))
            }));
            const El = e => {
                T("requestLoadProject", e, {
                    silent: !0
                })
            };
            $((() => {
                Da(), window.appInterface || (window.appInterface = {}), window.appInterface.submitPrompt = e => {
                    Ga(e.prompt, e.images, e.files)
                }, window.appInterface.newSession = () => {
                    T("requestCreateNewSession")
                }, window.appInterface.isAsking = () => {
                    var e;
                    return null == (e = La.value) ? void 0 : e.is_asking
                }, window.appInterface.stopAsking = () => {
                    var e;
                    null == (e = La.value) || e.cancelAskAgent({
                        userAborted: !0
                    })
                }, window.appInterface.loadProject = e => {
                    T("requestLoadProject", e)
                }, window.appInterface.refreshProject = () => {
                    B.value.id && T("requestLoadProject", B.value.id)
                }, window.appInterface.stopInitLoading = () => {
                    Ka.value = !1
                }, window.appInterface.setUnselectedCustomTools = e => {
                    window.appInterface.setUnselectedCustomTools = e => {
                        const t = JSON.stringify(e.unselected_tools);
                        p.log("unselectedString", t), localStorage.setItem("unselectedCustomTools", t)
                    }
                }, window.appInterface.showPersonalization = () => {
                    Pt.value = !1, zt.value = !0
                }, window.appInterface.chooseTools = () => {
                    Rt.value = !0
                }, k((() => {
                    var e;
                    return null == (e = La.value) ? void 0 : e.is_asking
                }), (e => {
                    var t;
                    null == (t = Wa.value) || t.callHandler("isAskingChanged", {
                        isAsking: e
                    })
                }), {
                    immediate: !0
                }), k((() => {
                    var e;
                    return null == (e = B.value) ? void 0 : e.id
                }), (e => {
                    var t;
                    null == (t = Wa.value) || t.callHandler("projectIdChanged", {
                        projectId: e
                    })
                }), {
                    immediate: !0
                }), document.addEventListener("click", _s)
            })), D((() => {
                window.appIterface = null, document.removeEventListener("click", _s)
            }));
            const Al = a(null);
            k((() => Al.value), (() => {
                Al.value && La.value.setContentElement(Al.value)
            }));
            const Tl = a(0),
                Ml = xa((() => {
                    var e, t;
                    Tl.value != window.innerWidth && (Tl.value = window.innerWidth, p.isMobile() || window.innerWidth <= 1220 ? null == (e = La.value) || e.setScrollElement(document.documentElement) : null == (t = La.value) || t.setScrollElement($a.value))
                }), 100);
            $((() => {
                var e, t;
                Ml(), window.addEventListener("resize", Ml), (null == (t = null == (e = La.value) ? void 0 : e.getMessagesRef()) ? void 0 : t.value) ? gs.value = La.value.getMessagesRef().value.length : gs.value = 0, p.isMobile() && window.addEventListener("resize", Dl), qa()
            })), D((() => {
                window.removeEventListener("resize", Ml)
            }));
            const $l = e => {
                    p.log("onProjectEvent", e), "project_start" == e.type && (B.value.id = e.id), "project_field" == e.type && (sa(B.value, e.field_name, (() => e.field_value)), "name" == e.field_name && T("newSessionCreated", B.value), "session_state.current_chat_session_id" == e.field_name && (B.value.session_state.current_chat_session_id = e.field_value, B.value.session_state.chat_session_history || (B.value.session_state.chat_session_history = []), B.value.session_state.chat_session_history = B.value.session_state.chat_session_history.filter((t => t.id != e.field_value)), B.value.session_state.chat_session_history.unshift({
                        id: e.field_value,
                        name: e.field_value
                    })), "session_state.current_chat_session_name" == e.field_name && (B.value.session_state.chat_session_history.find((e => e.id == B.value.session_state.current_chat_session_id)).name = e.field_value)), "project_field_delta" == e.type && sa(B.value, e.field_name, (t => "string" == typeof t ? t + e.delta : t)), "project_data" == e.type && (Object.assign(B.value, e.project_data), qa(), us.value = []), "project_field_append_item" == e.type && sa(B.value, e.field_name, (t => t ? [...t, e.field_value] : [e.field_value]))
                },
                Il = async e => {
                    const t = new URLSearchParams(window.location.href.split("?")[1]),
                        a = e || t.get("prompt"),
                        l = t.get("image"),
                        s = t.get("file"),
                        n = s && JSON.parse(s);
                    if (l) {
                        const e = await ba(J, "Image");
                        Ga(a || "", e, n || [])
                    } else Ga(a || "", [], n || [])
                };
            $((async () => {
                if (C.initialQuery || C.initialText) {
                    let e = [];
                    if (C.initialText && (e = await (async e => {
                            if (!e) return;
                            const t = new Blob([e], {
                                    type: "text/plain"
                                }),
                                a = new File([t], "content.txt", {
                                    type: "text/plain"
                                }),
                                l = "txt";
                            try {
                                const e = await fetch("/api/get_upload_personal_image_url");
                                if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                                const t = await e.json();
                                if (!t || 0 !== t.status) throw new Error(`API error! code: ${t.code}`);
                                const {
                                    upload_image_url: s,
                                    private_storage_url: n
                                } = t.data;
                                if (!(await fetch(s, {
                                        method: "PUT",
                                        headers: {
                                            "x-ms-blob-type": "BlockBlob"
                                        },
                                        body: a
                                    })).ok) throw new Error("Network response was not ok");
                                return [{
                                    name: a.name,
                                    type: a.type,
                                    ext: l,
                                    size: a.size,
                                    private_storage_url: n
                                }]
                            } catch (s) {
                                return null
                            }
                        })(C.initialText), !e)) return;
                    Ga(C.initialQuery, [], e)
                } else {
                    const t = new URLSearchParams(window.location.href.split("?")[1]),
                        a = t.get("prompt"),
                        l = t.get("image"),
                        s = t.get("file");
                    if (!a || l || s) {
                        if ("local" === l) try {
                            const e = await ha("currentUploadI2VImage", !1);
                            e && mt([e])
                        } catch (e) {}
                        if (s) {
                            const e = JSON.parse(s);
                            e.private_storage_url && ht([e])
                        }
                        if (M.query.from === J && l) {
                            const e = await ba(J, "Image");
                            mt([e])
                        }
                        if (M.query.from === Q.ExtensionNewTab) {
                            if (M.query.longPrompt) return void window.postMessage({
                                type: S.ChromeExtensionGetLongPrompt
                            }, "*");
                            Il()
                        }
                        M.query.from === J && t.get("action") === Q.AskByImage && window.parent.postMessage({
                            type: S.ChromeExtensionGetImageUrl
                        }, "*"), M.query.from === J && t.get("action") === Q.DownloadToAIDrive && window.parent.postMessage({
                            type: S.ChromeExtensionDownloadToAIDriveGetUrl
                        }, "*")
                    } else Ga(a);
                    if (a || s || l) {
                        t.delete("prompt"), t.delete("images"), t.delete("file");
                        const e = window.location.pathname + (t.toString() ? "?" + t.toString() : "");
                        history.replaceState(null, "", e)
                    } else !p.isMobile() && Ha.value && Ha.value.focus()
                }
            }));
            const Rl = a(!1),
                Ll = a(!1),
                Pl = a({}),
                ql = a(!1),
                Ol = b((() => {
                    var e;
                    return null == (e = p) ? void 0 : e.isGensparkAppAndroid()
                })),
                Bl = a(!1);
            $((() => {
                ql.value = window.innerWidth <= 768, Ll.value = window.innerWidth <= 768, window.addEventListener("resize", (() => {
                    ql.value = window.innerWidth <= 768, Ll.value = window.innerWidth <= 768
                })), R !== je.INIT_AGENT && R !== je.DEEP_RESEARCH && R !== je.GENERATE_SLIDE && "chat_now" !== R || Ul()
            }));
            const Ul = () => {
                let e = Ge.value;
                R === je.GENERAL_CHAT && (Aa.value = Qe.value, Fl.value = Je.value), R !== je.INIT_AGENT && bt(Bt.AGENT_SELECTED), Ga(e, We.value, Ke.value)
            };
            k(Rl, (e => {
                e && Ll.value ? document.body.style.overflow = "hidden" : document.body.style.overflow = ""
            })), D((() => {
                document.body.style.overflow = ""
            }));
            const Fl = a(C.externalRequestWebKnowledge);
            k((() => C.externalRequestWebKnowledge), (e => {
                Fl.value = e
            })), k((() => {
                var e, t;
                return null == (t = null == (e = La.value) ? void 0 : e.getMessagesRef()) ? void 0 : t.value
            }), (e => {
                if (e && La.value && Array.isArray(e)) {
                    const t = e.length;
                    t > gs.value && setTimeout((() => {
                        fs(e), hs(e)
                    }), 1e3), gs.value = t
                }
            }), {
                deep: !0
            });
            b((() => void 0 !== C.showRealtimeToggleButton ? C.showRealtimeToggleButton : !p.isGensparkApp() && !(!bl.value || !bl.value.gk_realtime_dogfood)));
            const Dl = () => {
                document.body.scrollTop = document.body.scrollHeight
            };
            D((() => {
                window.removeEventListener("resize", Dl)
            }));
            const Nl = e => {
                    M.query.from === J && window.parent.postMessage({
                        type: S.ChromeExtensionCopyContent,
                        payload: {
                            content: e
                        }
                    }, Q.ExtensionOrigin)
                },
                zl = e => {
                    if (e.session_state && "interactive_deep_dive_search" == e.session_state.render_template) try {
                        const t = JSON.parse(e.content),
                            a = t.detailAnswer || t.answer;
                        navigator.clipboard.writeText(a), P.success(O("pages.channel.copied")), Nl(a)
                    } catch (t) {
                        p.log("copyContent error", t)
                    } else e.content && (navigator.clipboard.writeText(e.content), P.success(O("pages.channel.copied")), Nl(e.content))
                },
                Vl = (e, t) => {
                    e.preventDefault();
                    const a = (e.clipboardData || window.clipboardData).getData("text");
                    document.execCommand("insertText", !1, a)
                },
                Gl = (e, t, a) => {
                    "Enter" !== e.key || e.shiftKey || (e.preventDefault(), Wl(t, a))
                },
                Wl = (e, t) => {
                    e.contentEditable = !1;
                    let a = document.querySelector(`[message-content-id="${t}"]`);
                    a && (a.contentEditable = !1), a = document.querySelector(`[user-message-content-id="${t}"]`), a && (a.contentEditable = !1), t >= 0 && a && a.innerText && a.innerText.trim() && La.value.getMessagesRef().value.length > t && (La.value.getMessagesRef().value = La.value.getMessagesRef().value.slice(0, t + 1), La.value.getMessagesRef().value[t].content = a.innerText, p.log("saveMessage", a.innerText), La.value.retry())
                },
                Kl = e => "string" != typeof e ? e : e.replace(/^[\n\t \r]+|[\n\t \r]+$/g, ""),
                Jl = a(new Map),
                Ql = a([0, 1, 2, 3]),
                Yl = e => {
                    if (!e || e === B.value.id) return 0;
                    if (Jl.value.has(e)) return Jl.value.get(e);
                    let t;
                    if (Ql.value.length > 0) t = Ql.value.shift();
                    else {
                        const e = Array.from(Jl.value.values()),
                            a = [0, 1, 2, 3].map((t => e.filter((e => e === t)).length));
                        t = a.indexOf(Math.min(...a))
                    }
                    return Jl.value.set(e, t), t
                };
            k((() => B.value.id), (() => {
                Jl.value.clear(), Ql.value = [0, 1, 2, 3]
            }));
            const Zl = a(new Set),
                Xl = (e, t, a) => {
                    if (!e.project_id || e.project_id === B.value.id) return !1;
                    if (0 === t) return !0;
                    return a[t - 1].project_id !== e.project_id
                },
                es = (e, t, a) => {
                    if (!e.project_id || e.project_id === B.value.id) return null;
                    let l = t;
                    for (; l > 0 && a[l - 1].project_id === e.project_id;) l--;
                    return `${e.project_id}-${l}`
                },
                ts = (e, t, a) => {
                    const l = es(e, t, a);
                    return l && Zl.value.has(l)
                },
                as = (e, t, a) => {
                    const l = es(e, t, a);
                    l && (Zl.value.has(l) ? Zl.value.delete(l) : Zl.value.add(l))
                },
                ls = p.filesizeString,
                ss = e => {
                    var t;
                    if (!e) return null;
                    if (!La.value || !La.value.getMessagesRef) return null;
                    let a = La.value.getMessagesRef().value;
                    if (!a) return null;
                    for (let l = 0; l < a.length; l++)
                        if (a[l].tool_calls && a[l].tool_calls.length > 0)
                            for (let s = 0; s < a[l].tool_calls.length; s++)
                                if ((null == (t = a[l].tool_calls[s]) ? void 0 : t.id) === e) return a[l].tool_calls[s];
                    return null
                };
            $((() => {
                ns(), window.appInterface || (window.appInterface = {}), window.appInterface.appEnterForeground = () => {
                    ns()
                }, window.appInterface.appEnterBackground = () => {}, window.appInterface.chatAbort = () => {
                    var e;
                    null == (e = La.value) || e.abort()
                }
            }));
            const ns = () => {
                    var e, t, a;
                    (null == (e = La.value) ? void 0 : e.is_asking) || (null == (t = B.value) ? void 0 : t.id) && (null == (a = La.value) || a.askAgent({
                        user_s_input: "",
                        action_params: {
                            force: !0
                        },
                        answer: null,
                        only_get_events: !0
                    }))
                },
                os = e => {
                    e && Ga(e)
                },
                is = e => {
                    var t;
                    e && e.url && (null == (t = Ha.value) ? void 0 : t.addImageUrl) && Ha.value.addImageUrl(e.url)
                },
                rs = () => {
                    Ga(O("components.research_me_modal.my_information_updated"))
                },
                cs = e => {
                    if (p.log("handleMessageUpdated called with data:", e), "gmail_draft" === e.component && "email_sent" === e.event) {
                        const t = e.data;
                        let a = "";
                        t.subject && (a += `${t.subject}\n`), t.to && (a += `To: ${t.to}\n`), t.cc && (a += `Cc: ${t.cc}\n`), t.bcc && (a += `Bcc: ${t.bcc}\n`), a += "\n\n has been sent.", p.log("handleMessageUpdated", a)
                    } else if ("offline_task_card" === e.component && "test_run" === e.event) {
                        const t = e.data;
                        p.log("handleMessageUpdated - offline task test run", t), t.prompt ? Ga(t.prompt) : P.warning("No prompt content found, unable to perform test run")
                    }
                },
                ds = e => {},
                us = a([]);
            k((() => La.value && La.value.is_asking), (async () => {
                La.value && !La.value.is_asking && await (async () => {
                    if (us.value = [], !La.value || !La.value.getMessagesRef) return;
                    const e = La.value.getMessagesRef().value;
                    if (!e || 0 === e.length) return;
                    const t = e[e.length - 1];
                    if (t && "assistant" === t.role) {
                        const e = t.content;
                        if ("string" == typeof e) {
                            const t = e.match(/<!-- follow-up-actions[^>]* -->/);
                            if (t) {
                                const e = t[0].match(/action_1="([^"]+)"/),
                                    a = t[0].match(/action_2="([^"]+)"/);
                                e && e[1] && (p.log("action_1:", e[1]), us.value = us.value || [], us.value.push({
                                    query: e[1],
                                    tool_call_type: null
                                })), a && a[1] && (p.log("action_2:", a[1]), us.value = us.value || [], us.value.push({
                                    query: a[1],
                                    tool_call_type: null
                                }))
                            } else p.log("no follow_up_actions")
                        }
                    }
                })()
            }));
            const ps = async e => {
                if ("html_badge_remove_badge" === e.data.type) try {
                    P.loading(O("components.html_badge.removing_badge"));
                    const t = e.data.payload.token,
                        a = "/api/html_badge/remove_badge",
                        l = await fetch(a, {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: JSON.stringify({
                                token: t
                            })
                        }),
                        s = await l.json();
                    if (p.log("remove_badge", s), 0 === s.status) {
                        const e = s.data.page_url;
                        document.querySelectorAll("iframe").forEach((t => {
                            t.src.split("?")[0] === e && (t.src = e + "?_t=" + Date.now())
                        }))
                    }
                } finally {
                    P.destroyAll()
                }
                if (e.data.type === S.ChromeExtensionRequestPageAnalysis && e.data.payload && (Ve.setBrowserExtensionData(e.data.payload), e.data.payload.actionType !== Q.ComparePrice && e.data.payload.actionType !== Q.ChatNow || !e.data.payload.userPrompt || Ga(e.data.payload.userPrompt)), e.data.type === S.ChromeExtensionUpdateUserInfo && yl(), e.data.type === S.ChromeExtensionPostImageUrl) {
                    const t = e.data.payload;
                    if (!t.url) return;
                    const a = decodeURIComponent(t.url);
                    if (a.includes("base64")) mt([a]);
                    else {
                        mt([a]);
                        const e = await yt(a);
                        mt([e])
                    }
                }
                if (e.data.type === S.ChromeExtensionPostLongPrompt) {
                    const t = e.data.payload.longPrompt;
                    t && Il(t)
                }
                if (e.data.type === S.ChromeExtensionDownloadToAIDrivePostUrl) {
                    const t = e.data.payload;
                    if (!t.url) return;
                    const a = decodeURIComponent(t.url);
                    a.includes("base64") ? Ga(O("components.aidrive.downloader.superagent_prompt"), [a]) : Ga(O("components.aidrive.downloader_dialog.superagent_prompt", {
                        url: a
                    }))
                }
            };
            $((() => {
                window.addEventListener("message", ps), window.parent.postMessage({
                    type: S.ChromeExtensionIndexInitSuccess
                }, Q.ExtensionOrigin), M.query.from === J && "serviceWorker" in navigator && (navigator.serviceWorker.ready.then((e => {
                    e.active.postMessage({
                        type: "GET_IS_REFRESH_PAGE"
                    })
                })), navigator.serviceWorker.addEventListener("message", (e => {
                    e.data && "REFRESH_PAGE" === e.data.type && e.data.payload.is_refresh_page && window.parent.postMessage({
                        type: "chrome_extension_refresh_page"
                    }, Q.ExtensionOrigin)
                })))
            })), I((() => {
                window.removeEventListener("message", ps)
            }));
            const vs = {
                isAsking: () => {
                    var e;
                    return null == (e = La.value) ? void 0 : e.is_asking
                },
                chat_now: () => {
                    Ul()
                },
                toBottom: () => {
                    La.value && La.value.toBottom()
                },
                appendClientContext: e => {
                    De.value = { ...De.value,
                        client_context: { ...e
                        }
                    }
                },
                submitAndClearPrompt: (e, t, a) => {
                    Ga(e, t, a)
                },
                setModelParams: e => {
                    De.value.model_params = e
                },
                agentRef: () => La,
                createNewChatSession: () => kl(),
                getToolCallConfig: da,
                handleChatSessionSelect: e => wl(e),
                setInputValue: e => {
                    Xe(e)
                },
                setInputValueAndSubmit: e => {
                    Xe(e), F((() => {
                        Ga(e, [], [], null)
                    }))
                },
                getInputValue: () => Ge.value,
                focusInput: () => {
                    Ha.value && Ha.value.focus && Ha.value.focus()
                }
            };
            le("chatAgentFunc", vs), l(vs);
            const _s = e => {
                    if (e.target && ("A" === e.target.tagName || e.target.closest && e.target.closest("a"))) {
                        const t = "A" === e.target.tagName ? e.target : e.target.closest("a"),
                            a = t.getAttribute("href");
                        if (a && a.startsWith("prompt://")) {
                            e.preventDefault();
                            const a = t.textContent;
                            Ga(a)
                        }
                    }
                },
                ms = e => {
                    var t;
                    if (!e || !e.id) return null;
                    if (!La.value || !La.value.getMessagesRef) return null;
                    let a = La.value.getMessagesRef().value;
                    if (!a) return null;
                    for (let l = 0; l < a.length; l++)
                        if (e.id == (null == (t = a[l]) ? void 0 : t.tool_call_id)) return a[l];
                    return null
                },
                gs = a(0),
                fs = e => {
                    var t, a;
                    if (!e || 0 === e.length || !La.value) return;
                    const l = null == (t = La.value) ? void 0 : t.getMessagesRef().value,
                        s = l[l.length - 1];
                    s && "tool" === s.role && !0 === (null == (a = null == s ? void 0 : s.session_state) ? void 0 : a.show_sidebar) && s.tool_call_id && !al.value && La.value.setExpandedToolCallResultMessage(s)
                },
                hs = e => {
                    if (!e || 0 === e.length) return;
                    const t = e[e.length - 1];
                    if (!t) return;
                    if (!t.project_id || t.project_id === B.value.id)
                        for (let a = e.length - 2; a >= 0; a--) {
                            const t = e[a];
                            if (t.project_id && t.project_id !== B.value.id && Xl(t, a, e)) {
                                const l = es(t, a, e);
                                if (l && !Zl.value.has(l)) {
                                    Zl.value.add(l);
                                    break
                                }
                            }
                        }
                },
                ys = b((() => {
                    var e, t, a, l, s, n;
                    if ((null == (t = null == (e = za.value) ? void 0 : e.session_state) ? void 0 : t.compact_chat_session_id) && !Ne.value.some((e => e.id === za.value.session_state.compact_chat_session_id))) return !0;
                    const o = (null == (l = null == (a = B.value) ? void 0 : a.session_state) ? void 0 : l.chat_session_history) || [],
                        i = new Set(Ne.value.map((e => e.id)));
                    return o.filter((e => i.has(e.id))).length + ((null == (n = null == (s = B.value) ? void 0 : s.session_state) ? void 0 : n.current_chat_session_id) ? 1 : 0) < o.length
                })),
                bs = b((() => {
                    var e, t, a, l, s, n;
                    if (Ne.value && Ne.value.length > 0) return !0;
                    return !!(null == (t = null == (e = za.value) ? void 0 : e.session_state) ? void 0 : t.compact_chat_session_id) || (null == (l = null == (a = B.value) ? void 0 : a.session_state) ? void 0 : l.chat_session_history) && (null == (n = null == (s = B.value) ? void 0 : s.session_state) ? void 0 : n.chat_session_history.length) > 0
                })),
                ks = async () => {
                    var e, t, a, l, s, n, o, i, r, c, d;
                    ze.value = !0;
                    try {
                        const u = null == (t = null == (e = za.value) ? void 0 : e.session_state) ? void 0 : t.compact_chat_session_id;
                        if (u && !Ne.value.some((e => e.id === u))) {
                            const e = await xl(u);
                            if (e) return void Ne.value.unshift(e)
                        }
                        let p = Ne.value.length;
                        if ((null == (l = null == (a = B.value) ? void 0 : a.session_state) ? void 0 : l.current_chat_session_id) && (p += 1), (null == (n = null == (s = B.value) ? void 0 : s.session_state) ? void 0 : n.chat_session_history) && p < (null == (r = null == (i = null == (o = B.value) ? void 0 : o.session_state) ? void 0 : i.chat_session_history) ? void 0 : r.length)) {
                            const e = null == (d = null == (c = B.value) ? void 0 : c.session_state) ? void 0 : d.chat_session_history[p],
                                t = await xl(e.id);
                            t && Ne.value.unshift(t)
                        }
                    } finally {
                        ze.value = !1
                    }
                },
                xs = e => {
                    if (!e) return "";
                    return new Date(e + "Z").toLocaleString()
                };
            $((() => {
                Y.on(Z.GMAIL_DRAFT_HANDOFF_SUPER_AGENT, ws)
            })), D((() => {
                Y.off(Z.GMAIL_DRAFT_HANDOFF_SUPER_AGENT, ws)
            }));
            const ws = e => {
                    let t = "EMAIL CONTEXT:\n";
                    t += `Email ID: ${e.emailId}\n`, e.subject && (t += `Subject: ${e.subject}\n`), e.to && (t += `To: ${e.to}\n`), e.cc && (t += `Cc: ${e.cc}\n`), e.selectedText && (t += `\nSelected content from email:\n"${e.selectedText}"\n`), e.prompt && (t += `\nUSER REQUEST: ${e.prompt}\n`), e.instruction && (t += `\nINSTRUCTION: ${e.instruction}\n\n`);
                    Ga(`${t}\nFulfill the request above, after that, please prepare a better email draft that is clear, professional, and effective. Improve the language, structure, and tone while maintaining the original intent.`)
                },
                js = e => {
                    Ga("", [], [], e)
                },
                Cs = e => {
                    Ga("", [], [], e)
                },
                Ss = e => {
                    if (La.value && ["email.new", "task_completed", "offline_task.new"].includes(e.type)) {
                        const a = `toolu_${Date.now().toString(36)}${Math.random().toString(36).substr(2,5)}`;
                        let l = "ai_inbox_notification",
                            s = {
                                type: e.type,
                                platform: e.platform || "system",
                                data: e.data
                            };
                        const n = {
                                role: "assistant",
                                content: "email.new" === e.type ? "收到一封新邮件" : "task_completed" === e.type ? "任务已完成" : "创建了新任务",
                                tool_calls: [{
                                    id: a,
                                    type: "function",
                                    function: {
                                        name: l,
                                        arguments: JSON.stringify(s)
                                    }
                                }]
                            },
                            o = {
                                role: "tool",
                                tool_call_id: a,
                                content: "",
                                session_state: {
                                    notification_type: e.type,
                                    notification_platform: e.platform || "system",
                                    notification_data: e.data,
                                    notification_timestamp: e.timestamp || new Date,
                                    notification: !0
                                }
                            };
                        try {
                            La.value.addMessage(n, !1), La.value.addMessage(o, !1)
                        } catch (t) {}
                    }
                };
            return (t, a) => {
                var l, s, h, y, b, k, C, S, A, $, I, R, q, F, D, W, K, Q, Y, Z, le, re, ce, de, he, we, je, Te, Me, $e, Ie, Re, Le, Pe, qe, Oe, Be, Fe, De, Ve, We, Ke, Je, Qe, Ze, Xe, mt, gt, ht, yt, bt, xt, wt;
                const Ct = e;
                return r(), n(m, null, [u(rt, {
                    ref_key: "toolCallConfigsRef",
                    ref: aa
                }, null, 512), c("div", {
                    class: _(["general-chat-wrapper j-general-chat-wrapper", {
                        "with-tool-call-result-sidebar": (null == (l = La.value) ? void 0 : l.getExpandedToolCallResultMessage()) && ss(null == (s = La.value) ? void 0 : s.getExpandedToolCallResultMessage().tool_call_id) || nl.value,
                        "with-infinite-canvas": pl.value,
                        "with-inbox-canvas": _l.value
                    }])
                }, [c("div", {
                    class: _(["chat-wrapper", Cl.value || va.value || Cl.value ? "chat-wrapper-padding-sheets" : ""]),
                    ref_key: "chatWrapper",
                    ref: $a
                }, [bs.value ? (r(), n("div", fu, [ze.value ? (r(), n("div", hu, [u(Vt)])) : o("", !0), !ze.value && ys.value ? (r(), n("div", {
                    key: 1,
                    class: "load-chat-session",
                    onClick: ks
                }, v(t.$t("components.chat_agent.load_earlier_messages")), 1)) : o("", !0), (r(!0), n(m, null, g(Ne.value, ((e, l) => (r(), n(m, {
                    key: l
                }, [e.messages && e.messages.length > 0 ? (r(), n("div", yu, [c("div", bu, v(xs(e.ctime)), 1), (r(!0), n(m, null, g(e.messages, ((l, s) => {
                    var d, p, f, h, y, b, k, w, j, C, S, A, M, $, I, R, L, P, q, O;
                    return r(), n(m, {
                        key: s
                    }, [Xl(l, s, e.messages) && ts(l, s, e.messages) ? (r(), n("div", {
                        key: 0,
                        class: _(["conversation-statement collapsed-project-group", {
                            [`project-color-${Yl(l.project_id)}`]: !0
                        }])
                    }, [c("div", ku, [a[40] || (a[40] = c("div", {
                        class: "collapsed-project-info"
                    }, [c("span", {
                        class: "collapsed-project-label"
                    }, "Character Agent Messages")], -1)), c("div", {
                        class: "collapsed-project-expand-btn",
                        onClick: t => as(l, s, e.messages)
                    }, a[39] || (a[39] = [c("svg", {
                        width: "16",
                        height: "16",
                        viewBox: "0 0 16 16",
                        fill: "currentColor"
                    }, [c("path", {
                        d: "M8 12l-4-4h8l-4 4z"
                    })], -1)]), 8, xu)])], 2)) : o("", !0), ts(l, s, e.messages) || "tool" == l.role && ss(l.tool_call_id) && (null == (d = da(ss(l.tool_call_id))) ? void 0 : d.can_be_opened_in_sidebar) ? o("", !0) : (r(), n("div", {
                        key: 1,
                        class: _(["conversation-statement", {
                            user: "user" == l.role,
                            assistant: "assistant" == l.role,
                            "plain-text": "string" == typeof l.content,
                            "different-project": l.project_id && l.project_id !== i(B).id,
                            [`project-color-${Yl(l.project_id)}`]: l.project_id && l.project_id !== i(B).id
                        }])
                    }, [Xl(l, s, e.messages) && l.project_id && l.project_id !== i(B).id ? (r(), n(m, {
                        key: 0
                    }, [c("div", {
                        class: "project-group-toggle",
                        onClick: t => as(l, s, e.messages)
                    }, [(r(), n("svg", {
                        width: "16",
                        height: "16",
                        viewBox: "0 0 16 16",
                        fill: "currentColor",
                        class: _({
                            rotated: Zl.value.has(es(l, s, e.messages))
                        })
                    }, a[41] || (a[41] = [c("path", {
                        d: "M8 12l-4-4h8l-4 4z"
                    }, null, -1)]), 2))], 8, wu), a[42] || (a[42] = c("span", {
                        class: "msg-label"
                    }, "Character Agent Messages", -1))], 64)) : o("", !0), c("div", {
                        class: _(["conversation-item-desc", "user" == l.role ? "user" : "assistant"])
                    }, [!l.content && (null == (p = null == l ? void 0 : l.session_state) ? void 0 : p.error) && (null == (f = null == l ? void 0 : l.session_state) ? void 0 : f.error_message) ? (r(), n("div", ju, [c("div", Cu, [c("div", Su, [c("pre", null, [c("code", null, "Error: " + v(ca(l)), 1)])])])])) : o("", !0), l.thinking || l.content && "string" == typeof l.content && ("tool" != l.role || "do_not_show_result" != (null == (h = l.session_state) ? void 0 : h.render_template)) || (null == (y = null == l ? void 0 : l.session_state) ? void 0 : y.answer) || "interactive_deep_dive_search" == (null == (b = null == l ? void 0 : l.session_state) ? void 0 : b.render_template) || (null == l ? void 0 : l.tool_calls) ? (r(), n("div", {
                        key: 1,
                        class: _(["bubble", {
                            thinking: l.thinking
                        }]),
                        "message-content-id": s,
                        onPaste: e => {
                            Vl(e)
                        },
                        onBlur: e => {},
                        onKeydown: e => {
                            Gl(e, l, s)
                        }
                    }, [c("div", Au, [c("div", Tu, ["assistant" == l.role ? (r(), n(m, {
                        key: 0
                    }, [l.thinking_blocks && 0 != l.thinking_blocks.length || !l.reasoning_content && !l.reasoning_delta ? o("", !0) : (r(), E(ye, {
                        key: 0,
                        source: (l.reasoning_content ? Kl(l.reasoning_content) : l.reasoning_delta) + (l.thinking ? o_ : ""),
                        "enable-complete-link": !0,
                        is_thinking: l.thinking
                    }, null, 8, ["source", "is_thinking"])), l.thinking_blocks ? (r(!0), n(m, {
                        key: 1
                    }, g(l.thinking_blocks, (e => (r(), n(m, null, [e.thinking ? (r(), E(ye, {
                        key: 0,
                        source: e.thinking,
                        "enable-complete-link": !0,
                        is_thinking: l.thinking
                    }, null, 8, ["source", "is_thinking"])) : o("", !0)], 64)))), 256)) : o("", !0), "google_map_local_widget" == (null == (k = null == l ? void 0 : l.session_state) ? void 0 : k.render_template) ? (r(), E(ct, {
                        key: 2,
                        "local-results": l.content
                    }, null, 8, ["local-results"])) : "image_search" == (null == (w = null == l ? void 0 : l.session_state) ? void 0 : w.render_template) ? (r(), E(ia, {
                        key: 3,
                        "initial-query": null == (j = null == l ? void 0 : l.session_state) ? void 0 : j.image_search_query,
                        "initial-items": null == (C = null == l ? void 0 : l.session_state) ? void 0 : C.flow_items,
                        integratedMode: !0,
                        "max-images-count": 20,
                        onAddImageUrl: a[0] || (a[0] = e => is(e))
                    }, null, 8, ["initial-query", "initial-items"])) : l.content || (null == (S = null == l ? void 0 : l.session_state) ? void 0 : S.answer) || (null == (A = null == l ? void 0 : l.session_state) ? void 0 : A.detailAnswer) || !l.content && !l.tool_calls ? (r(), E(ye, {
                        key: 4,
                        source: (l.content ? Kl(l.content) : (null == (M = null == l ? void 0 : l.session_state) ? void 0 : M.answer) || (null == ($ = null == l ? void 0 : l.session_state) ? void 0 : $.detailAnswer) || (l.thinking && !l.tool_calls ? i_ : "")) + (l.thinking ? o_ : ""),
                        linkify: !0,
                        "enable-complete-link": !0,
                        is_thinking: l.thinking
                    }, null, 8, ["source", "is_thinking"])) : o("", !0), (null == (I = l.session_state) ? void 0 : I.spark_result) ? (r(), E(Ua, {
                        key: 5,
                        message: l
                    }, null, 8, ["message"])) : o("", !0), l.tool_calls && l.tool_calls.length > 0 ? (r(!0), n(m, {
                        key: 6
                    }, g(l.tool_calls.filter((e => e && e.id)), ((e, s) => (r(), n(m, {
                        key: `${(null==l?void 0:l.id)||"msg"}-${s}-${(null==e?void 0:e.id)||"tool"}`
                    }, [(r(!0), n(m, null, g([da(e)], (d => (r(), n(m, {
                        key: `tcc-${(null==l?void 0:l.id)||"msg"}-${s}-${(null==e?void 0:e.id)||"tool"}`
                    }, [o("", !0), (null == d ? void 0 : d.hide_using_tool_bar) ? o("", !0) : (r(), n("div", Mu, [c("div", $u, v((null == d ? void 0 : d.agent_at_work) ? t.$t("components.chat_agent.using_agent") : t.$t("components.chat_agent.using_tool")), 1), a[44] || (a[44] = c("div", {
                        class: "separator"
                    }, "|", -1)), c("div", Iu, [(r(), E(z(null == d ? void 0 : d.icon)))]), c("div", Ru, v((null == d ? void 0 : d.get_label) ? null == d ? void 0 : d.get_label(e) : null == d ? void 0 : d.label), 1), c("div", Lu, ["function" == typeof(null == d ? void 0 : d.renderArguments) ? (r(), E(z(null == d ? void 0 : d.renderArguments(e)), {
                        key: 0
                    })) : o("", !0)]), (null == d ? void 0 : d.can_be_opened_in_sidebar) && !(null == d ? void 0 : d.hide_call_result_button) && ms(e) && !(null == d ? void 0 : d.do_not_show_result) ? (r(), n("div", Pu, [c("div", {
                        class: "view-tool-call-result-button",
                        onClick: () => {
                            La.value.setExpandedToolCallResultMessage(ms(e))
                        }
                    }, v(t.$t("components.chat_agent.view")), 9, qu)])) : o("", !0)])), (r(!0), n(m, null, g([ms(e)], (t => (r(), E(X, {
                        name: "inline-component-fade",
                        "enter-active-class": "inline-component-enter-active",
                        "leave-active-class": "inline-component-leave-active",
                        "enter-from-class": "inline-component-enter-from",
                        "leave-to-class": "inline-component-leave-to"
                    }, {
                        default: x((() => {
                            var a, l, s;
                            return [!(null == (a = null == t ? void 0 : t.session_state) ? void 0 : a.hide_inline_component) && (null == d ? void 0 : d.inline_component) ? (r(), n("div", Ou, [(r(), E(z(null == d ? void 0 : d.inline_component), te({
                                tool_call: e,
                                message: t,
                                ref_for: !0
                            }, { ...d.file_icon ? {
                                    "file-icon": d.file_icon
                                } : {},
                                ...d.need_asking ? {
                                    is_asking: null == (l = La.value) ? void 0 : l.is_asking
                                } : {},
                                ...d.need_project ? {
                                    project: i(B)
                                } : {},
                                ...t === La.value.getExpandedToolCallResultMessage() ? {
                                    expanding: !0
                                } : {},
                                ...d.inline_component && (null == (s = d.inline_component_supported_events) ? void 0 : s.includes("expand")) ? {
                                    onExpand: () => {
                                        La.value.setExpandedToolCallResultMessage(t)
                                    }
                                } : {}
                            }), null, 16, ["tool_call", "message"]))])) : o("", !0)]
                        })),
                        _: 2
                    }, 1024)))), 256))], 64)))), 128))], 64)))), 128)) : o("", !0)], 64)) : "user" == l.role ? (r(), n(m, {
                        key: 1
                    }, [(null == (R = l.session_state) ? void 0 : R.is_compact_summary) ? (r(), n(m, {
                        key: 0
                    }, [c("div", {
                        class: "compact-toggle",
                        onClick: () => {
                            var e;
                            l.session_state.is_compact_summary_expanded = !(null == (e = l.session_state) ? void 0 : e.is_compact_summary_expanded)
                        }
                    }, [c("div", Uu, v(t.$t("components.chat_agent.compact_summary")), 1), c("div", {
                        class: _(["compact-toggle-button", {
                            "compact-toggle-button-expanded": null == (L = l.session_state) ? void 0 : L.is_compact_summary_expanded
                        }])
                    }, [c("div", Fu, [u(i(fe))])], 2)], 8, Bu), (null == (P = l.session_state) ? void 0 : P.is_compact_summary_expanded) ? (r(), n("div", Du, [c("div", Nu, [u(ye, {
                        source: l.content,
                        "enable-complete-link": !0,
                        is_thinking: l.thinking
                    }, null, 8, ["source", "is_thinking"])])])) : o("", !0)], 64)) : (r(), n("pre", zu, [c("code", null, v(l.content), 1)]))], 64)) : "tool" == l.role ? (r(), E(z(null == (q = da(ss(l.tool_call_id))) ? void 0 : q.component), {
                        key: 2,
                        tool_call: ss(l.tool_call_id),
                        message: l,
                        "is-phone-call-card-button-disabled": i(ie),
                        onSelectQuestion: os,
                        onAddImageUrl: is,
                        onPhoneCallTaskCreated: a[1] || (a[1] = async e => {
                            T("phoneCallTaskCreated", e)
                        }),
                        onQuotaExceed: a[2] || (a[2] = e => {
                            T("quotaExceed", e)
                        }),
                        onPersonalizationSettingsSaved: rs,
                        onMessageUpdated: cs
                    }, null, 40, ["tool_call", "message", "is-phone-call-card-button-disabled", "onPersonalizationSettingsSaved"])) : o("", !0)])]), "assistant" != l.role || l.thinking || l.is_prompt || l.tool_calls || "interactive_deep_dive_search" == (null == (O = l.session_state) ? void 0 : O.render_template) ? o("", !0) : (r(), n("div", Hu, [c("div", {
                        class: "button",
                        onClick: e => zl(l)
                    }, [c("div", Gu, [u(i(Ht))]), c("div", Wu, v(t.$t("pages.channel.copy")), 1)], 8, Vu)]))], 42, Eu)) : o("", !0), l.content && Array.isArray(l.content) ? (r(!0), n(m, {
                        key: 2
                    }, g(l.content, (e => {
                        var t, a, s, d, p, g, f, h;
                        return r(), n(m, null, [e.hide_in_ui ? o("", !0) : (r(), n("div", {
                            key: 0,
                            class: _(["bubble", {
                                thinking: l.thinking,
                                image_url: "image_url" === e.type,
                                private_file: "private_file" === e.type,
                                slide_selected: "text" === e.type && "slide_selected" === e.render_template,
                                template_card: "text" === e.type && "template_card" === e.render_template
                            }])
                        }, [c("div", Ku, [c("div", Ju, ["text" === e.type && "slide_selected" === e.render_template ? (r(), n("div", Qu, [u(rn, {
                            data: e.render_data
                        }, null, 8, ["data"])])) : "text" === e.type && "template_card" === e.render_template ? (r(), n("div", Yu, [u(xn, {
                            data: e.render_data
                        }, null, 8, ["data"])])) : "text" === e.type ? (r(), n("div", Zu, v(null == e ? void 0 : e.text), 1)) : o("", !0), "image_url" === e.type ? (r(), n("div", Xu, [c("img", {
                            src: null == (t = null == e ? void 0 : e.image_url) ? void 0 : t.url
                        }, null, 8, ep)])) : o("", !0), "private_file" === e.type ? (r(), n("div", tp, [c("div", ap, [c("div", lp, ["pdf" == (null == (a = null == e ? void 0 : e.private_file) ? void 0 : a.ext) ? (r(), E(i(Gt), {
                            key: 0
                        })) : "doc" == (null == (s = null == e ? void 0 : e.private_file) ? void 0 : s.ext) || "docx" == (null == (d = null == e ? void 0 : e.private_file) ? void 0 : d.ext) ? (r(), E(i(Wt), {
                            key: 1
                        })) : "xls" == (null == (p = null == e ? void 0 : e.private_file) ? void 0 : p.ext) || "xlsx" == (null == (g = null == e ? void 0 : e.private_file) ? void 0 : g.ext) ? (r(), E(i(Jt), {
                            key: 2
                        })) : (r(), E(i(Kt), {
                            key: 3
                        }))]), c("div", sp, [c("div", np, v(null == (f = null == e ? void 0 : e.private_file) ? void 0 : f.name), 1), c("div", op, v(i(ls)(null == (h = null == e ? void 0 : e.private_file) ? void 0 : h.size)), 1)])])])) : o("", !0)])])], 2))], 64)
                    })), 256)) : o("", !0)], 2)], 2))], 64)
                })), 128))])) : o("", !0)], 64)))), 128))])) : o("", !0), u(Mn, {
                    agentType: i(B).type,
                    ref_key: "agentRef",
                    ref: La,
                    defaultMessageContent: "",
                    extra_data: Ma.value,
                    getClientContext: Va,
                    onProjectEvent: $l,
                    noAutoScroll: i(ne),
                    chat_session_id: null == (y = null == (h = i(B)) ? void 0 : h.session_state) ? void 0 : y.current_chat_session_id,
                    canvas_history_id: null == (k = null == (b = i(B)) ? void 0 : b.session_state) ? void 0 : k.canvas_history_id,
                    is_private: null == (C = i(B)) ? void 0 : C.is_private,
                    onTriggerAction: ja,
                    onAgentAskFinish: a[14] || (a[14] = e => {
                        (e => {
                            T("newSessionCreated", B.value), T("agentAskFinish", e)
                        })(e)
                    }),
                    onAgentAskStart: a[15] || (a[15] = e => {
                        (e => {
                            T("agentAskStart", e), ee.emit("agentAskStart", e)
                        })(e)
                    })
                }, {
                    default: x((({
                        messages: e,
                        doAction: l
                    }) => {
                        var s, p, f, h, y, b, k, C, S, A, $, I, R, L, q, F, D, N, H, G, W, K, Q, Y, Z, ee, le, se;
                        return [i(M).query.from === i(J) && i(Ye).title ? (r(), n("div", ip, [i(Ye).favicon ? (r(), n("img", {
                            key: 0,
                            class: "w-[24px] rounded-lg",
                            src: i(Ye).favicon
                        }, null, 8, rp)) : (r(), E(i(dt), {
                            key: 1
                        })), a[45] || (a[45] = c("div", {
                            class: "w-[1px] h-[36px] bg-[#00000015] dark:bg-[#ffffff15]"
                        }, null, -1)), c("div", cp, v(i(Ye).title), 1)])) : (null == e ? void 0 : e.length) > 0 && "user" === (null == (s = e[0]) ? void 0 : s.role) && (null == (f = null == (p = e[0]) ? void 0 : p.session_state) ? void 0 : f.extension_prompt) ? (r(), n("div", dp, [(r(), E(z((se = Mt(null == (y = null == (h = e[0]) ? void 0 : h.session_state) ? void 0 : y.extension_prompt), {
                            podcasts_agent: ae(Yt),
                            sheets_agent: ae(Zt),
                            slides_agent: ae(jt),
                            slide_agent: ae(jt),
                            docs_agent: ae(ve),
                            super_agent: ae(Xt)
                        }[se] || ae(pt))), {
                            class: "w-[24px] h-[24px] rounded-lg"
                        })), a[46] || (a[46] = c("div", {
                            class: "w-[1px] h-[36px] bg-[#00000015] dark:bg-[#ffffff15]"
                        }, null, -1)), c("div", up, " Created by " + v((Mt(null == (k = null == (b = e[0]) ? void 0 : b.session_state) ? void 0 : k.extension_prompt) || "unknown").replace("super_agent", "super").replace("podcasts", "pods")) + " agent ", 1)])) : (null == e ? void 0 : e.length) > ((null == (S = null == (C = i(B)) ? void 0 : C.session_state) ? void 0 : S.hidden_messages_count) || 0) && "user" === (null == (I = e[(null == ($ = null == (A = i(B)) ? void 0 : A.session_state) ? void 0 : $.hidden_messages_count) || 0]) ? void 0 : I.role) && (null == (F = null == (q = e[(null == (L = null == (R = i(B)) ? void 0 : R.session_state) ? void 0 : L.hidden_messages_count) || 0]) ? void 0 : q.session_state) ? void 0 : F.ai_inbox_summary) ? (r(), n("div", pp, [u(i(ft), {
                            class: "w-[36px] h-[36px] rounded-lg"
                        }), a[47] || (a[47] = c("div", {
                            class: "w-[1px] h-[36px] bg-[#00000015] dark:bg-[#ffffff15]"
                        }, null, -1)), c("div", vp, v(null == (G = null == (H = e[(null == (N = null == (D = i(B)) ? void 0 : D.session_state) ? void 0 : N.hidden_messages_count) || 0]) ? void 0 : H.session_state) ? void 0 : G.ai_inbox_summary_text), 1)])) : o("", !0), e && 0 != e.length || bs.value || i(V) || Ue.value || Ka.value ? o("", !0) : (r(), n("div", {
                            key: 3,
                            class: _(["empty-placeholder", Qa.value || Tt.value ? "sheets-agent-empty-placeholder" : ""])
                        }, [Tt.value ? (r(), n(m, {
                            key: 0
                        }, [j(v(t.$t("components.slides.welcome_title")) + " ", 1), c("ul", _p, [c("li", null, v(t.$t("components.slides.welcome_tip1")), 1), c("li", null, v(t.$t("components.slides.welcome_tip2")), 1), c("li", null, v(t.$t("components.slides.welcome_tip3")), 1), c("li", null, v(t.$t("components.slides.welcome_tip4")), 1)])], 64)) : Qa.value ? (r(), n(m, {
                            key: 1
                        }, [j(v(t.$t("components.sheets_agent.welcome_title")) + " ", 1), c("ul", mp, [c("li", null, v(t.$t("components.sheets_agent.welcome_tip1")), 1), c("li", null, v(t.$t("components.sheets_agent.welcome_tip2")), 1), c("li", null, v(t.$t("components.sheets_agent.welcome_tip3")), 1)])], 64)) : Za.value ? (r(), n(m, {
                            key: 2
                        }, [(null == (K = null == (W = i(B)) ? void 0 : W.session_state) ? void 0 : K.create_by_template) ? (r(), n(m, {
                            key: 0
                        }, [c("div", gp, v(t.$t("components.docs_agent.template_selected")), 1), c("ul", fp, [c("li", null, v(t.$t("components.docs_agent.template_selected_tip1")), 1), c("li", null, v(t.$t("components.docs_agent.template_selected_tip2")), 1), c("li", null, v(t.$t("components.docs_agent.template_selected_tip3")), 1)])], 64)) : (r(), n(m, {
                            key: 1
                        }, [c("div", hp, v(t.$t("components.docs_agent.welcome_title")), 1), c("ul", yp, [c("li", null, v(t.$t("components.editor_switch_bar.description1")), 1), c("li", null, v(t.$t("components.editor_switch_bar.description2")), 1), c("li", null, v(t.$t("components.editor_switch_bar.description3")), 1)])], 64))], 64)) : Ya.value ? (r(), n(m, {
                            key: 3
                        }, [j(v(t.$t("components.podcasts_agent.welcome_title")) + " ", 1), c("ul", bp, [c("li", null, v(t.$t("components.podcasts_agent.welcome_tip1")), 1), c("li", null, v(t.$t("components.podcasts_agent.welcome_tip2")), 1), c("li", null, v(t.$t("components.podcasts_agent.welcome_tip3")), 1)])], 64)) : (r(), n(m, {
                            key: 4
                        }, [j(v(t.$t("components.general_chat_content.hi_there_what_can_i_help_with")), 1)], 64))], 2)), e && 0 != e.length || i(V) || Ue.value || !Ka.value ? o("", !0) : (r(), n("div", kp, [u(Vt)])), e && e.length > 0 || bs.value ? (r(), n("div", {
                            key: 5,
                            class: "conversation-wrapper",
                            ref_key: "conversationContent",
                            ref: Al
                        }, [U(t.$slots, "conversation-content-inner-before", {}, void 0, !0), e && e.length > 0 ? (r(), n("div", xp, [Ne.value.length > 0 ? (r(), n("div", wp, " Latest ")) : o("", !0), (r(!0), n(m, null, g(e, ((l, s) => {
                            var p, f, h, y, b, k, j, C, S, A, M, $, I, R, L, q, U, F, D, N, H, V, G, W, K, J, Q, Y, Z, ee, ae, le, se, ne, oe, re, ce;
                            return r(), n(m, {
                                key: s
                            }, [s >= ((null == (f = null == (p = i(B)) ? void 0 : p.session_state) ? void 0 : f.hidden_messages_count) || 0) && Xl(l, s, e) && ts(l, s, e) ? (r(), n("div", {
                                key: 0,
                                class: _(["conversation-statement collapsed-project-group", {
                                    [`project-color-${Yl(l.project_id)}`]: !0
                                }])
                            }, [c("div", jp, [a[49] || (a[49] = c("div", {
                                class: "collapsed-project-info"
                            }, [c("span", {
                                class: "collapsed-project-label"
                            }, "Character Agent Messages")], -1)), c("div", {
                                class: "collapsed-project-expand-btn",
                                onClick: t => as(l, s, e)
                            }, a[48] || (a[48] = [c("svg", {
                                width: "16",
                                height: "16",
                                viewBox: "0 0 16 16",
                                fill: "currentColor"
                            }, [c("path", {
                                d: "M8 12l-4-4h8l-4 4z"
                            })], -1)]), 8, Cp)])], 2)) : o("", !0), !(s >= ((null == (y = null == (h = i(B)) ? void 0 : h.session_state) ? void 0 : y.hidden_messages_count) || 0)) || ts(l, s, e) || (null == (b = null == l ? void 0 : l.session_state) ? void 0 : b.ai_inbox_summary) || "tool" == l.role && ss(l.tool_call_id) && (null == (k = da(ss(l.tool_call_id))) ? void 0 : k.can_be_opened_in_sidebar) || (null == (j = da(ss(l.tool_call_id))) ? void 0 : j.do_not_show_conversation_statement) ? o("", !0) : (r(), n("div", {
                                key: 1,
                                class: _(["conversation-statement", {
                                    editing: l.contentEditable,
                                    user: "user" == l.role,
                                    assistant: "assistant" == l.role,
                                    "plain-text": "string" == typeof l.content,
                                    "different-project": l.project_id && l.project_id !== i(B).id,
                                    [`project-color-${Yl(l.project_id)}`]: l.project_id && l.project_id !== i(B).id
                                }])
                            }, [Xl(l, s, e) && l.project_id && l.project_id !== i(B).id ? (r(), n(m, {
                                key: 0
                            }, [c("div", {
                                class: "project-group-toggle",
                                onClick: t => as(l, s, e)
                            }, [(r(), n("svg", {
                                width: "16",
                                height: "16",
                                viewBox: "0 0 16 16",
                                fill: "currentColor",
                                class: _({
                                    rotated: Zl.value.has(es(l, s, e))
                                })
                            }, a[50] || (a[50] = [c("path", {
                                d: "M8 12l-4-4h8l-4 4z"
                            }, null, -1)]), 2))], 8, Sp), a[51] || (a[51] = c("span", {
                                class: "msg-label"
                            }, "Character Agent Messages", -1))], 64)) : o("", !0), c("div", {
                                class: _(["conversation-item-desc", ["user" == l.role ? "user" : "assistant", l.contentEditable ? "content-editable" : ""]])
                            }, [!l.content && (null == (C = null == l ? void 0 : l.session_state) ? void 0 : C.error) && (null == (S = null == l ? void 0 : l.session_state) ? void 0 : S.error_message) ? (r(), n("div", Ep, [c("div", Ap, [c("div", Tp, [c("pre", null, [c("code", null, "Error: " + v(ca(l)), 1)])])])])) : o("", !0), l.thinking || l.content && "string" == typeof l.content && ("tool" != l.role || "do_not_show_result" != (null == (A = l.session_state) ? void 0 : A.render_template)) || (null == (M = null == l ? void 0 : l.session_state) ? void 0 : M.answer) || "interactive_deep_dive_search" == (null == ($ = null == l ? void 0 : l.session_state) ? void 0 : $.render_template) || (null == l ? void 0 : l.tool_calls) ? (r(), n("div", {
                                key: 1,
                                class: _(["bubble", {
                                    thinking: l.thinking
                                }]),
                                "message-content-id": s,
                                onPaste: e => {
                                    Vl(e)
                                },
                                onBlur: e => {},
                                onKeydown: e => {
                                    Gl(e, l, s)
                                }
                            }, [c("div", $p, [c("div", Ip, ["assistant" == l.role ? (r(), n(m, {
                                key: 0
                            }, [l.thinking_blocks && 0 != l.thinking_blocks.length || !l.reasoning_content && !l.reasoning_delta ? o("", !0) : (r(), E(ye, {
                                key: 0,
                                source: (l.reasoning_content ? Kl(l.reasoning_content) : l.reasoning_delta) + (l.thinking ? o_ : ""),
                                "enable-complete-link": !0,
                                is_thinking: l.thinking
                            }, null, 8, ["source", "is_thinking"])), l.thinking_blocks ? (r(!0), n(m, {
                                key: 1
                            }, g(l.thinking_blocks, (e => (r(), n(m, null, [e.thinking ? (r(), E(ye, {
                                key: 0,
                                source: e.thinking,
                                "enable-complete-link": !0,
                                is_thinking: l.thinking
                            }, null, 8, ["source", "is_thinking"])) : o("", !0)], 64)))), 256)) : o("", !0), "google_map_local_widget" == (null == (I = null == l ? void 0 : l.session_state) ? void 0 : I.render_template) ? (r(), E(ct, {
                                key: 2,
                                "local-results": l.content
                            }, null, 8, ["local-results"])) : "image_search" == (null == (R = null == l ? void 0 : l.session_state) ? void 0 : R.render_template) ? (r(), E(ia, {
                                key: 3,
                                "initial-query": null == (L = null == l ? void 0 : l.session_state) ? void 0 : L.image_search_query,
                                "initial-items": null == (q = null == l ? void 0 : l.session_state) ? void 0 : q.flow_items,
                                integratedMode: !0,
                                "max-images-count": 20,
                                onAddImageUrl: a[3] || (a[3] = e => is(e))
                            }, null, 8, ["initial-query", "initial-items"])) : l.content || (null == (U = null == l ? void 0 : l.session_state) ? void 0 : U.answer) || (null == (F = null == l ? void 0 : l.session_state) ? void 0 : F.detailAnswer) || !l.content && !l.tool_calls ? (r(), E(ye, {
                                key: 4,
                                source: (l.content ? Kl(l.content) : (null == (D = null == l ? void 0 : l.session_state) ? void 0 : D.answer) || (null == (N = null == l ? void 0 : l.session_state) ? void 0 : N.detailAnswer) || (l.thinking && !l.tool_calls ? i_ : "")) + (l.thinking ? o_ : ""),
                                "enable-complete-link": !0,
                                linkify: !0,
                                is_thinking: l.thinking
                            }, null, 8, ["source", "is_thinking"])) : o("", !0), (null == (H = l.session_state) ? void 0 : H.spark_result) ? (r(), E(Ua, {
                                key: 5,
                                message: l
                            }, null, 8, ["message"])) : o("", !0), l.tool_calls && l.tool_calls.length > 0 ? (r(!0), n(m, {
                                key: 6
                            }, g(l.tool_calls.filter((e => e && e.id)), ((e, s) => (r(), n(m, {
                                key: `${(null==l?void 0:l.id)||"msg"}-${s}-${(null==e?void 0:e.id)||"tool"}`
                            }, [l.tool_calls && l.tool_calls.length > 0 ? (r(!0), n(m, {
                                key: 0
                            }, g([da(e)], (d => (r(), n(m, {
                                key: `tc-${(null==l?void 0:l.id)||"msg"}-${s}-${(null==e?void 0:e.id)||"tool"}`
                            }, [o("", !0), (null == d ? void 0 : d.hide_using_tool_bar) ? o("", !0) : (r(), n("div", Rp, [c("div", Lp, v((null == d ? void 0 : d.agent_at_work) ? t.$t("components.chat_agent.using_agent") : t.$t("components.chat_agent.using_tool")), 1), a[53] || (a[53] = c("div", {
                                class: "separator"
                            }, "|", -1)), c("div", Pp, [(r(), E(z(null == d ? void 0 : d.icon)))]), c("div", qp, v((null == d ? void 0 : d.get_label) ? null == d ? void 0 : d.get_label(e) : null == d ? void 0 : d.label), 1), c("div", Op, ["function" == typeof(null == d ? void 0 : d.renderArguments) ? (r(), E(z(null == d ? void 0 : d.renderArguments(e)), {
                                key: 0
                            })) : o("", !0)]), (null == d ? void 0 : d.can_be_opened_in_sidebar) && !(null == d ? void 0 : d.hide_call_result_button) && ms(e) && !(null == d ? void 0 : d.do_not_show_result) ? (r(), n("div", Bp, [c("div", {
                                class: "view-tool-call-result-button",
                                onClick: () => {
                                    La.value.setExpandedToolCallResultMessage(ms(e))
                                }
                            }, v(t.$t("components.chat_agent.view")), 9, Up)])) : o("", !0)])), (r(!0), n(m, null, g([ms(e)], (t => (r(), E(X, {
                                name: "inline-component-fade",
                                "enter-active-class": "inline-component-enter-active",
                                "leave-active-class": "inline-component-leave-active",
                                "enter-from-class": "inline-component-enter-from",
                                "leave-to-class": "inline-component-leave-to"
                            }, {
                                default: x((() => {
                                    var l, s, c;
                                    return [!(null == (l = null == t ? void 0 : t.session_state) ? void 0 : l.hide_inline_component) && (null == d ? void 0 : d.inline_component) ? (r(), n("div", Fp, [(r(), E(z(null == d ? void 0 : d.inline_component), te({
                                        tool_call: e,
                                        message: t,
                                        onMessageUpdated: a[4] || (a[4] = e => {
                                            cs(e)
                                        }),
                                        onSubmitPrompt: a[5] || (a[5] = e => Ga(e)),
                                        ref_for: !0
                                    }, { ...d.file_icon ? {
                                            "file-icon": d.file_icon
                                        } : {},
                                        ...d.need_asking ? {
                                            is_asking: null == (s = La.value) ? void 0 : s.is_asking
                                        } : {},
                                        ...d.need_project ? {
                                            project: i(B)
                                        } : {},
                                        ...t === La.value.getExpandedToolCallResultMessage() ? {
                                            expanding: !0
                                        } : {},
                                        ...d.inline_component && (null == (c = d.inline_component_supported_events) ? void 0 : c.includes("expand")) ? {
                                            onExpand: () => {
                                                La.value.setExpandedToolCallResultMessage(t)
                                            }
                                        } : {}
                                    }), null, 16, ["tool_call", "message"]))])) : o("", !0)]
                                })),
                                _: 2
                            }, 1024)))), 256))], 64)))), 128)) : o("", !0)], 64)))), 128)) : o("", !0)], 64)) : "user" == l.role ? (r(), n(m, {
                                key: 1
                            }, [(null == (V = l.session_state) ? void 0 : V.is_compact_summary) ? (r(), n(m, {
                                key: 0
                            }, [c("div", {
                                class: "compact-toggle",
                                onClick: () => {
                                    var e;
                                    l.session_state.is_compact_summary_expanded = !(null == (e = l.session_state) ? void 0 : e.is_compact_summary_expanded)
                                }
                            }, [c("div", Np, v(t.$t("components.chat_agent.compact_summary")), 1), c("div", {
                                class: _(["compact-toggle-button", {
                                    "compact-toggle-button-expanded": null == (G = l.session_state) ? void 0 : G.is_compact_summary_expanded
                                }])
                            }, [c("div", zp, [u(i(fe))])], 2)], 8, Dp), (null == (W = l.session_state) ? void 0 : W.is_compact_summary_expanded) ? (r(), n("div", Hp, [c("div", Vp, [u(ye, {
                                source: l.content,
                                "enable-complete-link": !0,
                                is_thinking: l.thinking
                            }, null, 8, ["source", "is_thinking"])])])) : o("", !0)], 64)) : (r(), n(m, {
                                key: 1
                            }, [(null == (K = l.session_state) ? void 0 : K.html_editor_selection) ? (r(), n("div", Gp, v((null == (Y = null == (Q = null == (J = l.session_state) ? void 0 : J.html_editor_selection) ? void 0 : Q.text) ? void 0 : Y.text) || (null == (ae = null == (ee = null == (Z = l.session_state) ? void 0 : Z.html_editor_selection) ? void 0 : ee.element) ? void 0 : ae.html)), 1)) : o("", !0), c("pre", null, [c("code", null, v(l.content), 1)])], 64))], 64)) : "tool" == l.role ? (r(), E(z(null == (le = da(ss(l.tool_call_id))) ? void 0 : le.component), {
                                key: 2,
                                tool_call: ss(l.tool_call_id),
                                message: l,
                                "is-phone-call-card-button-disabled": i(ie),
                                onSelectQuestion: os,
                                onAddImageUrl: is,
                                onPhoneCallTaskCreated: a[6] || (a[6] = async e => {
                                    T("phoneCallTaskCreated", e)
                                }),
                                onQuotaExceed: a[7] || (a[7] = e => {
                                    T("quotaExceed", e)
                                }),
                                onPersonalizationSettingsSaved: rs,
                                onMessageUpdated: a[8] || (a[8] = e => {
                                    cs(e)
                                })
                            }, null, 40, ["tool_call", "message", "is-phone-call-card-button-disabled", "onPersonalizationSettingsSaved"])) : o("", !0)])]), l.project_id && l.project_id != i(B).id || "assistant" != l.role || l.thinking || l.is_prompt || l.tool_calls || "interactive_deep_dive_search" == (null == (se = l.session_state) ? void 0 : se.render_template) ? o("", !0) : (r(), n("div", Wp, [c("div", {
                                class: "button",
                                onClick: e => zl(l)
                            }, [c("div", Jp, [u(i(Ht))]), c("div", Qp, v(t.$t("pages.channel.copy")), 1)], 8, Kp), "super_agent" == (null == (ne = i(B)) ? void 0 : ne.type) ? (r(), n("div", {
                                key: 0,
                                class: "button",
                                onClick: a[9] || (a[9] = e => {
                                    Ga(O("pages.channel.continue_deep_research"), [], [])
                                })
                            }, [c("div", Yp, [u(i(qt), {
                                style: {
                                    width: "16px",
                                    height: "16px"
                                }
                            })]), c("div", Zp, v(t.$t("pages.channel.deep_research")), 1)])) : (r(), n("div", {
                                key: 1,
                                class: "button",
                                onClick: e => (e => {
                                    vt(e, {
                                        messageApi: P,
                                        attachments: [],
                                        autoSaveIfSinglePage: !0,
                                        onShow: e => {
                                            ta.value = e, ea.value = !0
                                        }
                                    })
                                })(l)
                            }, [c("div", ev, [u(i(xe), {
                                style: {
                                    width: "16px",
                                    height: "16px",
                                    "margin-right": "4px"
                                }
                            })]), c("div", tv, v(t.$t("pages.channel.save_to_notion")), 1)], 8, Xp)), Ol.value ? (r(), n("div", {
                                key: 2,
                                class: "button",
                                onClick: a[10] || (a[10] = e => Bl.value = !0)
                            }, [c("div", av, [u(i($t), {
                                style: {
                                    width: "16px",
                                    height: "16px",
                                    "margin-right": "4px"
                                }
                            })]), c("div", lv, v(t.$t("pages.channel.feedback")), 1)])) : o("", !0)]))], 42, Mp)) : o("", !0), l.content && Array.isArray(l.content) ? (r(!0), n(m, {
                                key: 2
                            }, g(l.content, (e => {
                                var t, a, s, d, p, g, f, h;
                                return r(), n(m, null, [e.hide_in_ui ? o("", !0) : (r(), n("div", {
                                    key: 0,
                                    class: _(["bubble", {
                                        thinking: l.thinking,
                                        image_url: "image_url" === e.type,
                                        private_file: "private_file" === e.type,
                                        slide_selected: "text" === e.type && "slide_selected" === e.render_template,
                                        template_card: "text" === e.type && "template_card" === e.render_template
                                    }])
                                }, [c("div", sv, [c("div", nv, ["text" === e.type && "slide_selected" === e.render_template ? (r(), n("div", ov, [u(rn, {
                                    data: e.render_data
                                }, null, 8, ["data"])])) : "text" === e.type && "template_card" === e.render_template ? (r(), n("div", iv, [u(xn, {
                                    data: e.render_data
                                }, null, 8, ["data"])])) : "text" === e.type ? (r(), n("div", rv, v(null == e ? void 0 : e.text), 1)) : o("", !0), "image_url" === e.type ? (r(), n("div", cv, [c("img", {
                                    src: null == (t = null == e ? void 0 : e.image_url) ? void 0 : t.url
                                }, null, 8, dv)])) : o("", !0), "private_file" === e.type ? (r(), n("div", uv, [c("div", pv, [c("div", vv, ["pdf" == (null == (a = null == e ? void 0 : e.private_file) ? void 0 : a.ext) ? (r(), E(i(Gt), {
                                    key: 0
                                })) : "doc" == (null == (s = null == e ? void 0 : e.private_file) ? void 0 : s.ext) || "docx" == (null == (d = null == e ? void 0 : e.private_file) ? void 0 : d.ext) ? (r(), E(i(Wt), {
                                    key: 1
                                })) : "xls" == (null == (p = null == e ? void 0 : e.private_file) ? void 0 : p.ext) || "xlsx" == (null == (g = null == e ? void 0 : e.private_file) ? void 0 : g.ext) ? (r(), E(i(Jt), {
                                    key: 2
                                })) : (r(), E(i(Kt), {
                                    key: 3
                                }))]), c("div", _v, [c("div", mv, v(null == (f = null == e ? void 0 : e.private_file) ? void 0 : f.name), 1), c("div", gv, v(i(ls)(null == (h = null == e ? void 0 : e.private_file) ? void 0 : h.size)), 1)])])])) : o("", !0)])])], 2))], 64)
                            })), 256)) : o("", !0), l === e[e.length - 1] && "assistant" == l.role && (null == (oe = l.session_state) ? void 0 : oe.context_length_exceeded) ? (r(), n("div", fv, [Tt.value || Qa.value ? (r(), n("div", hv, [c("div", yv, v(t.$t("components.prompt_input_super.continue_chat_hint")), 1), c("div", {
                                class: "button",
                                onClick: kl
                            }, [c("div", bv, [u(i(Ft))]), c("div", kv, v(t.$t("components.prompt_input_super.continue_chat")), 1)])])) : (r(), n(m, {
                                key: 1
                            }, [a[54] || (a[54] = c("div", {
                                class: "left"
                            }, " Context Length Exceeded, Please open a new session ", -1)), c("div", xv, [c("div", {
                                class: "button",
                                onClick: a[11] || (a[11] = () => {
                                    Qa.value || el.value || tl.value || Za.value || Xa.value ? kl() : T("requestCreateNewSession")
                                })
                            }, " Create a new session ")])], 64))])) : l === e[e.length - 1] && "assistant" == l.role && l.should_retry ? (r(), n("div", wv, [c("div", jv, v(i(O)("components.copilot.error-generating-response")), 1), c("div", Cv, [c("div", {
                                class: "button",
                                onClick: a[12] || (a[12] = () => {
                                    (async () => {
                                        if (!La.value) return;
                                        if (await La.value.retry({
                                                only_get_events: !0
                                            }), !La.value.getMessagesRef) return;
                                        const e = La.value.getMessagesRef().value;
                                        if (!e || 0 === e.length) return;
                                        const t = e[e.length - 1];
                                        ("user" == (null == t ? void 0 : t.role) || "tool" == (null == t ? void 0 : t.role) && 0 == (null == t ? void 0 : t._all_events_count) && (null == t ? void 0 : t.should_retry)) && await La.value.retry({
                                            only_get_events: !1
                                        })
                                    })()
                                })
                            }, v(i(O)("components.copilot.retry")), 1)])])) : l === e[e.length - 1] && "assistant" == l.role && l.should_reload ? (r(), n("div", Sv, [c("div", Ev, v(i(O)("components.copilot.reload-required")), 1), c("div", Av, [c("div", {
                                class: "button",
                                onClick: a[13] || (a[13] = () => {
                                    window.location.reload()
                                })
                            }, v(i(O)("components.copilot.reload")), 1)])])) : o("", !0), "user" == l.role && !(null == (re = La.value) ? void 0 : re.is_asking) && l.contentEditable ? (r(), n("div", Tv, [d((r(), n("div", {
                                "user-message-content-id": s,
                                contenteditable: "true",
                                class: "outline-none text-[#232425] pb-[16px]",
                                onPaste: e => {
                                    Vl(e)
                                },
                                onBlur: e => {},
                                onKeydown: e => {
                                    Gl(e, l, s)
                                }
                            }, [c("pre", $v, [c("code", null, v(l.content), 1)])], 40, Mv)), [
                                [w]
                            ]), c("div", Iv, [c("div", {
                                onClick: () => {
                                    ((e, t) => {
                                        e.contentEditable = !1, "string" == typeof e.content && (e.content = "" + e.content, La.value.getMessagesRef().value[t].content = e.content);
                                        const a = document.querySelector(`[message-content-id="${t}"]`);
                                        a && (a.contentEditable = !1)
                                    })(l, s)
                                },
                                class: "bg-[#F5F5F5] w-[66px] py-[6px] rounded-[60px] text-center justify-start text-zinc-600 text-base font-normal font-['Arial'] leading-normal cursor-pointer hover:bg-[#E5E5E5]"
                            }, " Cancel ", 8, Rv), c("div", {
                                onClick: () => {
                                    Wl(l, s)
                                },
                                class: "bg-[#232425] w-[66px] py-[6px] rounded-[60px] text-center justify-start text-white text-base font-normal font-['Arial'] leading-normal cursor-pointer hover:bg-[#1A1A1A]"
                            }, " Save ", 8, Lv)])])) : o("", !0)], 2), "user" != l.role || (null == (ce = La.value) ? void 0 : ce.is_asking) ? o("", !0) : (r(), n("div", {
                                key: 1,
                                class: _(["message-actions-user", {
                                    "content-editable": l.contentEditable
                                }])
                            }, [l.contentEditable || Array.isArray(l.content) ? o("", !0) : (r(), n(m, {
                                key: 0
                            }, [c("div", {
                                class: "message-action",
                                onClick: e => (async e => {
                                    e && (await navigator.clipboard.writeText(e), He.value = !0, setTimeout((() => {
                                        He.value = !1
                                    }), 3e3))
                                })(l.content)
                            }, [c("div", qv, [He.value ? (r(), E(i(pe), {
                                key: 0,
                                class: "text-[#232425]"
                            })) : (r(), E(i(It), {
                                key: 1
                            }))])], 8, Pv), c("div", {
                                class: "message-action",
                                onClick: () => {
                                    ((e, t) => {
                                        const a = document.querySelector(`[message-content-id="${t}"]`);
                                        a && (a.contentEditable = !0, a.focus())
                                    })(0, s), l.contentEditable = !0
                                }
                            }, [c("div", Bv, [u(i(ve))])], 8, Ov)], 64))], 2))], 2))], 64)
                        })), 128)), (null == (Y = null == (Q = i(B)) ? void 0 : Q.session_state) ? void 0 : Y.sandbox_status) ? (r(), n("div", Uv, [j(v(null == (ee = null == (Z = i(B)) ? void 0 : Z.session_state) ? void 0 : ee.sandbox_status), 1), a[55] || (a[55] = c("span", {
                            class: "loading-dots"
                        }, null, -1))])) : o("", !0), (null == (le = La.value) ? void 0 : le.is_asking) || i(oe) ? (r(), n("div", Fv, [u(Vt)])) : o("", !0)])) : o("", !0)], 512)) : o("", !0)]
                    })),
                    _: 3
                }, 8, ["agentType", "extra_data", "noAutoScroll", "chat_session_id", "canvas_history_id", "is_private"])], 2), Ue.value ? (r(), E(me, {
                    key: 0,
                    speed: 5,
                    class: "realtime-cover-fixed"
                })) : o("", !0), i(N) || _l.value ? o("", !0) : (r(), n("div", {
                    key: 1,
                    class: _(["input-wrapper-wrapper j-input-wrapper-wrapper", [nl.value && !(null == (S = i(B)) ? void 0 : S.id)]])
                }, [(null == (A = La.value) ? void 0 : A.toBottomVisible) && !Ue.value ? (r(), n("div", {
                    key: 0,
                    class: "to-bottom-icon",
                    onClick: a[16] || (a[16] = (...e) => La.value.toBottom && La.value.toBottom(...e))
                }, [u(i(Qt))])) : o("", !0), c("div", Dv, [us.value && us.value.length > 0 && !(null == ($ = La.value) ? void 0 : $.is_asking) ? (r(), n("div", Nv, [c("div", zv, [(r(!0), n(m, null, g(us.value, (e => {
                    var t, a;
                    return r(), n("div", {
                        class: "recommend-query",
                        key: e.query,
                        onClick: () => {
                            Ga(e.query), us.value = null
                        }
                    }, [e.tool_call_type && (null == (t = ua(e.tool_call_type)) ? void 0 : t.icon) ? (r(), n("div", Vv, [(r(), E(z(null == (a = ua(e.tool_call_type)) ? void 0 : a.icon)))])) : o("", !0), c("div", Gv, v(e.query), 1)], 8, Hv)
                })), 128))])])) : o("", !0), i(H) ? o("", !0) : (r(), n(m, {
                    key: 1
                }, [(Za.value || Xa.value) && ((null == (I = i(B)) ? void 0 : I.id) || (null == (q = null == (R = i(B)) ? void 0 : R.session_state) ? void 0 : q.is_blank)) ? (r(), E(Sl, {
                    key: 0
                })) : o("", !0), va.value ? (r(), E(cl, {
                    key: 1,
                    metaData: Fa.value,
                    isAsking: null == (F = La.value) ? void 0 : F.is_asking
                }, null, 8, ["metaData", "isAsking"])) : o("", !0), Cl.value ? (r(), E(Gd, {
                    key: 2,
                    project: i(B),
                    isAsking: null == (D = La.value) ? void 0 : D.is_asking
                }, null, 8, ["project", "isAsking"])) : o("", !0), "podcasts_agent" == i(B).type ? (r(), n("div", Wv, [u(Od, {
                    project: i(B),
                    isAsking: null == (W = La.value) ? void 0 : W.is_asking
                }, null, 8, ["project", "isAsking"])])) : Tt.value ? (r(), E(sl, {
                    key: 4,
                    metaData: Fa.value
                }, null, 8, ["metaData"])) : o("", !0), Qa.value ? (r(), E(Tn, {
                    key: 5,
                    title: i(O)("components.sheets_agent.ai_sheets_mode"),
                    tips: [i(O)("components.sheets_agent.ai_sheets_mode_tip1"), i(O)("components.sheets_agent.ai_sheets_mode_tip2"), i(O)("components.sheets_agent.ai_sheets_mode_tip3"), i(O)("components.sheets_agent.ai_sheets_mode_tip4"), i(O)("components.sheets_agent.ai_sheets_mode_tip5")]
                }, null, 8, ["title", "tips"])) : o("", !0), Ya.value ? (r(), E(Tn, {
                    key: 6,
                    title: t.$t("components.podcasts_agent.ai_pods_mode"),
                    tips: [t.$t("components.podcasts_agent.welcome_tip1"), t.$t("components.podcasts_agent.welcome_tip2"), t.$t("components.podcasts_agent.welcome_tip3")]
                }, null, 8, ["title", "tips"])) : o("", !0), "docs_agent" == i(B).type || "markdown_agent" == i(B).type || "html_agent" == i(B).type ? (r(), n("div", Kv, [u($d, {
                    project: i(B)
                }, null, 8, ["project"])])) : o("", !0), "code_sandbox" == i(B).type || "code_sandbox_light" == i(B).type ? (r(), n("div", Jv, [u(Fd, {
                    project: i(B)
                }, null, 8, ["project"])])) : o("", !0), u(Xs, {
                    project: i(B)
                }, null, 8, ["project"]), u(Hl, {
                    project: i(B),
                    "onUpdate:editorType": ds
                }, null, 8, ["project"]), Ue.value ? o("", !0) : (r(), n("div", Qv, [U(t.$slots, "search-input-wrapper", {}, (() => [u(Nt, {
                    ref: "inputWrapper"
                }, {
                    default: x((() => {
                        var e, t;
                        return [u(Dt, {
                            ref_key: "promptInputRef",
                            ref: Ha,
                            modelValue: i(Ge),
                            "onUpdate:modelValue": a[17] || (a[17] = e => L(Ge) ? Ge.value = e : null),
                            onSubmitPrompt: a[18] || (a[18] = (e, t, a) => {
                                Ga(e, t, a), kt.value = {}
                            }),
                            onInputFocus: a[19] || (a[19] = () => {
                                var e;
                                null == (e = La.value) || e.setInputFocused(!0)
                            }),
                            onInputBlur: a[20] || (a[20] = () => {
                                var e;
                                null == (e = La.value) || e.setInputFocused(!1)
                            }),
                            onStopAsking: a[21] || (a[21] = () => {
                                var e;
                                null == (e = La.value) || e.cancelAskAgent({
                                    userAborted: !0
                                })
                            }),
                            showStopAsking: null == (e = La.value) ? void 0 : e.is_asking,
                            supportImages: !0,
                            onErrorMessage: a[22] || (a[22] = e => i(P).error(e)),
                            "onUpdate:images": a[23] || (a[23] = e => Ia.value = e),
                            "onUpdate:files": a[24] || (a[24] = e => Ra.value = e),
                            useSuggestion: !1,
                            placeholder: i(G),
                            styleClass: i(se),
                            isEditSlideMode: pa.value,
                            showPersonalizationButton: "super_agent" == i(B).type,
                            projectType: i(B).type,
                            project: i(B),
                            messages: null == (t = La.value) ? void 0 : t.getMessagesRef(),
                            onCreateNewChatSession: a[25] || (a[25] = () => {
                                kl()
                            }),
                            onSelectChatSession: a[26] || (a[26] = e => {
                                wl(e)
                            }),
                            onOpenGitHubTab: jl
                        }, null, 8, ["modelValue", "showStopAsking", "placeholder", "styleClass", "isEditSlideMode", "showPersonalizationButton", "projectType", "project", "messages"])]
                    })),
                    _: 1
                }, 512)]), !0), o("", !0)])), Ue.value ? (r(), n("div", Yv, [u(ue, {
                    onSwitchToKeyboard: Pa,
                    onFunctionCall: Ca,
                    onShowSearchSideBar: Sa,
                    searchStatusTopBarData: null == (Q = null == (K = Na.value) ? void 0 : K.session_state) ? void 0 : Q.search_status_top_bar_data
                }, null, 8, ["searchStatusTopBarData"])])) : o("", !0)], 64))])], 2)), Ol.value ? (r(), E(ya, {
                    key: 2,
                    show: Bl.value,
                    "onUpdate:show": a[27] || (a[27] = e => Bl.value = e)
                }, null, 8, ["show"])) : o("", !0), u(X, {
                    name: "slide-desktop"
                }, {
                    default: x((() => [Rl.value && !Ll.value ? (r(), E(_e, {
                        key: 0,
                        "search-results": Pl.value.searchResults,
                        keywords: Pl.value.keywords,
                        "jump-to-url": Pl.value.jumpToUrl,
                        "is-mobile": ql.value,
                        onCloseSearchSideBar: a[28] || (a[28] = e => Rl.value = !1),
                        class: "search-source-sidebar desktop"
                    }, null, 8, ["search-results", "keywords", "jump-to-url", "is-mobile"])) : o("", !0)])),
                    _: 1
                }), u(X, {
                    name: "slide"
                }, {
                    default: x((() => [Rl.value && Ll.value ? (r(), n("div", Zv, [u(_e, {
                        "search-results": Pl.value.searchResults,
                        keywords: Pl.value.keywords,
                        "is-mobile": ql.value,
                        "jump-to-url": Pl.value.jumpToUrl,
                        onCloseSearchSideBar: a[29] || (a[29] = e => Rl.value = !1)
                    }, null, 8, ["search-results", "keywords", "is-mobile", "jump-to-url"])])) : o("", !0)])),
                    _: 1
                })], 2), (null == (Y = La.value) ? void 0 : Y.getExpandedToolCallResultMessage()) && ss(null == (Z = La.value) ? void 0 : Z.getExpandedToolCallResultMessage().tool_call_id) ? (r(), n("div", Xv)) : o("", !0), u(X, {
                    name: pa.value ? "" : "slide-in-out"
                }, {
                    default: x((() => {
                        var e, t, l, s, d, p, v;
                        return [(null == (e = La.value) ? void 0 : e.getExpandedToolCallResultMessage()) && ss(null == (t = La.value) ? void 0 : t.getExpandedToolCallResultMessage().tool_call_id) ? (r(), n("div", e_, [c("div", t_, [c("div", {
                            class: "close-button",
                            onClick: a[30] || (a[30] = () => {
                                var e;
                                null == (e = La.value) || e.setExpandedToolCallResultMessage(null)
                            })
                        }, [c("div", a_, [u(i(oa))])]), (r(), E(z(null == (s = da(ss(null == (l = La.value) ? void 0 : l.getExpandedToolCallResultMessage().tool_call_id))) ? void 0 : s.component), {
                            key: null == (d = La.value) ? void 0 : d.getExpandedToolCallResultMessage().tool_call_id,
                            message: null == (p = La.value) ? void 0 : p.getExpandedToolCallResultMessage(),
                            tool_call: ss(null == (v = La.value) ? void 0 : v.getExpandedToolCallResultMessage().tool_call_id),
                            onClose: a[31] || (a[31] = () => {
                                var e;
                                null == (e = La.value) || e.setExpandedToolCallResultMessage(null)
                            })
                        }, null, 40, ["message", "tool_call"]))])])) : o("", !0)]
                    })),
                    _: 1
                }, 8, ["name"]), i(p).isMobile() && nl.value ? (r(), n("div", {
                    key: 1,
                    class: _(["global-canvas-mask", {
                        "mobile-visible": "docs_agent" != i(B).type && "code_sandbox" != i(B).type && "code_sandbox_light" != i(B).type && "markdown_agent" != i(B).type && "html_agent" != i(B).type && "podcasts_agent" != i(B).type || (null == (re = null == (le = i(B)) ? void 0 : le.session_state) ? void 0 : re.canvas_mobile_visible)
                    }])
                }, null, 2)) : o("", !0), nl.value && !hl.value ? (r(), n("div", {
                    key: 2,
                    class: _(["global-canvas j-global-canvas", {
                        "mobile-visible": "docs_agent" != i(B).type && "code_sandbox" != i(B).type && "code_sandbox_light" != i(B).type && "markdown_agent" != i(B).type && "html_agent" != i(B).type && "podcasts_agent" != i(B).type || (null == (de = null == (ce = i(B)) ? void 0 : ce.session_state) ? void 0 : de.canvas_mobile_visible),
                        "sheets-global-canvas": al.value,
                        "inbox-canvas-fullscreen": _l.value
                    }])
                }, [c("div", l_, [pa.value ? (r(), E(et, {
                    ref_key: "toolResultSlidesRenderRef",
                    ref: ge,
                    mode: "slide_agent",
                    projectFileContents: Ba.value,
                    metaData: Fa.value,
                    versionHistory: null == (we = null == (he = i(B)) ? void 0 : he.session_state) ? void 0 : we.version_history,
                    isAsking: null == (je = La.value) ? void 0 : je.is_asking,
                    onRequestLoadProject: El,
                    slidesUrl: null == (Me = null == (Te = i(B)) ? void 0 : Te.session_state) ? void 0 : Me.slides_url,
                    projectId: null == ($e = i(B)) ? void 0 : $e.id,
                    key: "slides_" + (null == (Ie = i(B)) ? void 0 : Ie.id)
                }, null, 8, ["projectFileContents", "metaData", "versionHistory", "isAsking", "slidesUrl", "projectId"])) : al.value && "sheets_agent_new" === (null == (Re = i(B)) ? void 0 : Re.type) ? (r(), E(Ct, {
                    key: 1
                }, {
                    default: x((() => {
                        var e, t;
                        return [(r(), E(ut, {
                            project: i(B),
                            conversation: null == (e = La.value) ? void 0 : e.getMessagesRef(),
                            readOnly: i(H),
                            inGlobalCanvas: !0,
                            key: "spreadsheet_" + (null == (t = i(B)) ? void 0 : t.id)
                        }, null, 8, ["project", "conversation", "readOnly"]))]
                    })),
                    _: 1
                })) : al.value ? (r(), E(tt, {
                    "in-global-canvas": !0,
                    project: i(B),
                    readOnly: i(H),
                    key: "datasets_" + (null == (Le = i(B)) ? void 0 : Le.id)
                }, null, 8, ["project", "readOnly"])) : il.value ? (r(), E(at, {
                    key: 3,
                    project: i(B),
                    messages: null == (Pe = La.value) ? void 0 : Pe.getMessagesRef(),
                    readOnly: i(H),
                    onCreateNewProject: a[32] || (a[32] = () => {
                        var e, t, a;
                        null == (a = La.value) || a.createEmptyProject({
                            session_state: {
                                docs_agent: null == (t = null == (e = i(B)) ? void 0 : e.session_state) ? void 0 : t.docs_agent
                            }
                        })
                    }),
                    ref_key: "docsCanvasRef",
                    ref: Se,
                    isAsking: null == (qe = La.value) ? void 0 : qe.is_asking
                }, null, 8, ["project", "messages", "readOnly", "isAsking"])) : rl.value ? (r(), E(lt, {
                    key: 4,
                    project: i(B),
                    ref_key: "markdownCanvasRef",
                    ref: Ee,
                    isAsking: null == (Oe = La.value) ? void 0 : Oe.is_asking
                }, null, 8, ["project", "isAsking"])) : dl.value ? (r(), E(st, {
                    key: 5,
                    project: i(B),
                    messages: null == (Be = La.value) ? void 0 : Be.getMessagesRef(),
                    readOnly: i(H),
                    onCreateNewProject: a[33] || (a[33] = () => {
                        var e, t, a;
                        null == (a = La.value) || a.createEmptyProject({
                            session_state: {
                                docs_agent: null == (t = null == (e = i(B)) ? void 0 : e.session_state) ? void 0 : t.docs_agent
                            }
                        })
                    }),
                    isAsking: null == (Fe = La.value) ? void 0 : Fe.is_asking,
                    ref: "htmlCanvasRef"
                }, null, 8, ["project", "messages", "readOnly", "isAsking"])) : ul.value ? (r(), E(nt, {
                    ref_key: "posterCanvasRef",
                    ref: be,
                    project: i(B),
                    key: "poster_" + (null == (De = i(B)) ? void 0 : De.type) + (null == (Ve = i(B)) ? void 0 : Ve.id)
                }, null, 8, ["project"])) : ml.value ? (r(), E(ot, {
                    ref_key: "podcastsCanvasRef",
                    ref: ke,
                    "in-global-canvas": !0,
                    project: i(B),
                    key: "podcasts_" + (null == (We = i(B)) ? void 0 : We.type) + (null == (Ke = i(B)) ? void 0 : Ke.id)
                }, null, 8, ["project"])) : pl.value ? (r(), E(ra, {
                    project: i(B),
                    key: "infinite_canvas_" + (null == (Je = i(B)) ? void 0 : Je.type) + (null == (Qe = i(B)) ? void 0 : Qe.id),
                    onAsk: js
                }, null, 8, ["project"])) : vl.value ? (r(), E(Zn, {
                    project: i(B),
                    key: "cooperation_canvas_" + (null == (Ze = i(B)) ? void 0 : Ze.type) + (null == (Xe = i(B)) ? void 0 : Xe.id),
                    onAsk: Cs
                }, null, 8, ["project"])) : _l.value ? (r(), E(_t, {
                    key: 10,
                    onNotification: Ss
                })) : gl.value ? (r(), E(it, {
                    key: 11,
                    ref_key: "codeSandboxCanvasRef",
                    ref: Ae,
                    project: i(B),
                    readOnly: i(H),
                    isAsking: null == (mt = La.value) ? void 0 : mt.is_asking,
                    messages: null == (gt = La.value) ? void 0 : gt.getMessagesRef(),
                    onSandbox_id_changed: a[34] || (a[34] = e => {
                        var t;
                        (null == (t = i(B)) ? void 0 : t.session_state) && (i(B).session_state.sandbox_id = e)
                    })
                }, null, 8, ["project", "readOnly", "isAsking", "messages"])) : fl.value ? (r(), E(Cd, {
                    key: 12,
                    project: i(B),
                    readOnly: i(H),
                    isAsking: null == (ht = La.value) ? void 0 : ht.is_asking,
                    messages: null == (yt = La.value) ? void 0 : yt.getMessagesRef()
                }, null, 8, ["project", "readOnly", "isAsking", "messages"])) : o("", !0)])], 2)) : o("", !0), "video_studio" == i(B).type ? d((r(), n("div", {
                    key: 3,
                    class: _(["global-canvas j-global-canvas", {
                        "mobile-visible": "docs_agent" != i(B).type && "code_sandbox" != i(B).type && "markdown_agent" != i(B).type && "html_agent" != i(B).type && "podcasts_agent" != i(B).type || (null == (xt = null == (bt = i(B)) ? void 0 : bt.session_state) ? void 0 : xt.canvas_mobile_visible),
                        "sheets-global-canvas": al.value,
                        "inbox-canvas-fullscreen": _l.value
                    }])
                }, [c("div", s_, [d((r(), E(eo, {
                    project: i(B),
                    ref_key: "videoStudioCanvasRef",
                    ref: Ce,
                    key: "video_studio_" + (null == (wt = i(B)) ? void 0 : wt.type)
                }, null, 8, ["project"])), [
                    [f, hl.value]
                ])])], 2)), [
                    [f, hl.value]
                ]) : o("", !0), Rt.value && i(p).isGensparkAppChatView() ? (r(), E(Ut, {
                    key: 4,
                    isModal: !0,
                    onClose: a[35] || (a[35] = e => Rt.value = !1)
                })) : o("", !0), i(p).isGensparkApp() ? (r(), E(ka, {
                    key: 5,
                    modelValue: Pt.value,
                    "onUpdate:modelValue": a[36] || (a[36] = e => Pt.value = e),
                    showPersonalizationDialog: zt.value,
                    "onUpdate:showPersonalizationDialog": a[37] || (a[37] = e => zt.value = e)
                }, null, 8, ["modelValue", "showPersonalizationDialog"])) : o("", !0), u(gu, {
                    "is-visible": ea.value,
                    message: ta.value.message,
                    "message-api": ta.value.messageApi,
                    attachments: ta.value.attachments,
                    "auto-save-if-single-page": ta.value.autoSaveIfSinglePage,
                    onClose: a[38] || (a[38] = e => ea.value = !1)
                }, null, 8, ["is-visible", "message", "message-api", "attachments", "auto-save-if-single-page"])], 64)
            }
        }
    }, [
        ["__scopeId", "data-v-7ef5b0ff"]
    ]);
export {
    Al as C, Gd as M, cl as S, r_ as _, $d as a, Fd as b, Od as c
};