import {
    d as e,
    o as t,
    b as a,
    r as i,
    U as s,
    _ as l,
    i as r,
    g as n,
    v as o,
    h as u,
    a3 as d,
    e as c,
    F as p,
    k as m,
    q as x,
    y as f,
    f as v,
    l as g,
    a as h,
    t as y
} from "./Cf0SOiw0.js";
import {
    P as k
} from "./CouGvJje.js";
import {
    u as w
} from "./C1MFiWVQ.js";
import {
    P as b
} from "./CIu9VtBu.js";
import {
    G as _
} from "./Btf03DwY.js";
import {
    F as L
} from "./O-S7dcNF.js";
import {
    d as j
} from "./DOnko34f.js";
const C = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 32 32"
};
const F = {
    render: function(i, s) {
        return t(), e("svg", C, s[0] || (s[0] = [a("path", {
            d: "M22.5 4c-2 0-3.9.8-5.3 2.2L16 7.4l-1.1-1.1c-2.9-3-7.7-3-10.6-.1l-.1.1c-3 3-3 7.8 0 10.8L16 29l11.8-11.9c3-3 3-7.8 0-10.8C26.4 4.8 24.5 4 22.5 4z",
            fill: "currentColor"
        }, null, -1)]))
    }
};

function z() {
    const e = i(226),
        t = i(1),
        a = i(null),
        l = a => {
            if (a)
                if (a <= 768) e.value = (a - 12) / 2, t.value = 2;
                else {
                    e.value = 226;
                    let i = a - 50;
                    const s = 12;
                    let l = Math.ceil(i / (e.value + s));
                    e.value = (i - s * (l - 1)) / l, t.value = l
                }
        },
        r = j((() => {
            a.value && l(a.value.clientWidth)
        }), 200);
    return {
        columnWidth: e,
        columnCount: t,
        containerRef: a,
        calculateLayout: l,
        onResize: r,
        initializeLayout: () => {
            a.value && s((() => {
                l(a.value.clientWidth)
            }))
        },
        setupResizeListener: () => {
            "undefined" != typeof window && window.addEventListener("resize", r)
        },
        cleanupResizeListener: () => {
            "undefined" != typeof window && window.removeEventListener("resize", r)
        }
    }
}
const P = ["onClick"],
    R = ["onClick"],
    W = {
        key: 0,
        class: "absolute bottom-0 flex items-center justify-center gap-[8px] left-0 right-0 h-[37px] bg-[#00000066]"
    },
    A = {
        class: "text-white text-[12px] leading-[16px]"
    },
    D = {
        class: "w-[50%] h-[6px] bg-[#FFFFFF33] rounded-[12px] transition-all duration-200 overflow-hidden"
    },
    I = {
        class: "text-white text-[12px] leading-[16px]"
    },
    $ = {
        class: "h-[40px] mt-[12px] text-[14px] font-normal leading-[20px] line-clamp-2 text-gray-800"
    },
    q = {
        class: "flex items-center justify-between gap-[10px]"
    },
    E = {
        class: "flex items-center gap-[6px] mt-[8px]"
    },
    S = ["src", "alt"],
    T = {
        class: "justify-start text-neutral-800 text-sm font-normal font-['Arial'] leading-tight truncate"
    },
    U = ["onClick"],
    B = {
        class: "text-xs text-[#606366]"
    },
    G = l({
        __name: "ResourceList",
        props: {
            dataList: {
                type: Array,
                default: () => [],
                required: !0
            }
        },
        emits: ["play-item", "like-updated"],
        setup(i, {
            emit: s
        }) {
            const l = i,
                j = s,
                C = r("currentUser"),
                G = n(),
                {
                    updatePlaylist: M,
                    togglePlay: O,
                    isCurrentPodcast: H,
                    audioState: J,
                    progress: K,
                    currentTime: N,
                    duration: Q,
                    formatTime: V
                } = w(),
                {
                    columnWidth: X,
                    containerRef: Y,
                    initializeLayout: Z,
                    setupResizeListener: ee,
                    cleanupResizeListener: te
                } = z();
            o((() => l.dataList), (() => {
                Z();
                const e = l.dataList.filter((e => e.audio_url)).map((e => ({
                    id: e.id || e.title,
                    audio_url: e.audio_url,
                    duration: e.duration,
                    title: e.name || "AI Podcast",
                    image: e.poster_url
                })));
                e.length > 0 ? M(e) : M([])
            }), {
                immediate: !0
            }), u((() => {
                Z(), ee()
            })), d((() => {
                te()
            }));
            const ae = e => {
                    const t = e.id || e.name || e.title;
                    return H(t) && J.isPlaying
                },
                ie = e => {
                    const t = e.id || e.name || e.title;
                    return H(t) && J.isPlaying && K.value || 0
                },
                se = e => {
                    const t = e.id || e.name || e.title;
                    return H(t) ? V(N.value) : "0:00"
                },
                le = e => {
                    const t = e.id || e.name || e.title;
                    return H(t) ? V(Q.value) : "0:00"
                },
                re = e => {
                    e.target.src = "/default-avatar.png"
                };
            return (s, l) => (t(), e(p, null, [a("div", {
                class: "flex flex-wrap gap-[12px] md:px-[25px] pb-[100px] box-border",
                ref_key: "publishResourceWrapper",
                ref: Y
            }, [(t(!0), e(p, null, m(i.dataList, ((i, s) => {
                var l, r, n, o;
                return t(), e("div", {
                    key: i.id || s,
                    class: "rounded-[12px] bg-[#fafafa] px-[12px] py-[16px] box-border hover:scale-105 transition-all duration-300 ease-in-out cursor-pointer",
                    onClick: e => (e => {
                        G.push(`/pods/detail?id=${e.id}`)
                    })(i),
                    style: x({
                        width: f(X) + "px"
                    })
                }, [a("div", {
                    style: x({
                        backgroundImage: `url(${i.poster_url})`
                    }),
                    class: "relative rounded-[12px] w-full aspect-square bg-cover bg-center bg-gray-200 flex justify-center items-center overflow-hidden"
                }, [a("div", {
                    class: "w-[42px] h-[42px] bg-[#0D1216B2] rounded-full flex items-center justify-center hover:bg-[#0D1216CC] transition-colors duration-200",
                    onClick: g((e => (e => {
                        if (e.audio_url) {
                            const t = {
                                id: e.id || e.name || e.title,
                                audio_url: e.audio_url,
                                duration: e.duration,
                                title: e.name || "AI Podcast",
                                image: e.poster_url
                            };
                            O(t)
                        }
                    })(i)), ["stop"])
                }, [ae(i) ? (t(), h(f(b), {
                    key: 1
                })) : (t(), h(f(k), {
                    key: 0
                }))], 8, R), ae(i) ? (t(), e("div", W, [a("div", A, y(se(i)), 1), a("div", D, [a("div", {
                    class: "h-full bg-white",
                    style: x({
                        width: ie(i) + "%"
                    })
                }, null, 4)]), a("div", I, y(le(i)), 1)])) : v("", !0)], 4), a("div", $, y(i.name), 1), a("div", q, [a("div", E, [i.avatar || (null == (l = f(C)) ? void 0 : l.avatar) ? (t(), e("img", {
                    key: 0,
                    class: "w-5 h-5 rounded-full bg-gray-200",
                    src: i.avatar || (null == (r = f(C)) ? void 0 : r.avatar),
                    alt: i.nickname || "User avatar",
                    onError: re
                }, null, 40, S)) : v("", !0), a("div", T, y(i.nickname || (null == (n = f(C)) ? void 0 : n.name)), 1)]), a("div", {
                    class: "flex items-center gap-[4px] mt-[6px] cursor-pointer hover:opacity-70 transition-opacity",
                    onClick: g((e => (async e => {
                        try {
                            const t = e.is_liked,
                                a = t ? "/api/publish/unlike" : "/api/publish/like",
                                i = await $fetch(a, {
                                    method: "POST",
                                    body: {
                                        resource_id: e.id
                                    }
                                });
                            0 === i.status && (e.is_liked = !t, i.data && "like_count" in i.data && (e.like_count = i.data.like_count), j("like-updated", {
                                item: e,
                                liked: !t
                            }))
                        } catch (t) {}
                    })(i)), ["stop"])
                }, [i.is_liked ? (t(), h(f(F), {
                    key: 1,
                    class: "w-[14px] h-[14px] text-[#FF3D3D] animate-heart-beat"
                })) : (t(), h(f(L), {
                    key: 0,
                    class: "w-[14px] h-[14px] transition-all duration-200"
                })), a("span", B, y((o = i.like_count, o && "like" !== o ? o >= 1e3 ? (o / 1e3).toFixed(1).replace(/\.0$/, "") + "k" : o.toString() : "like")), 1)], 8, U)])], 12, P)
            })), 128))], 512), c(_)], 64))
        }
    }, [
        ["__scopeId", "data-v-9f9b7d97"]
    ]);
export {
    G as R, z as u
};