import {
    d as o,
    b as n,
    o as r
} from "./Cf0SOiw0.js";
const t = {
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const e = {
        render: function(e, l) {
            return r(), o("svg", t, l[0] || (l[0] = [n("path", {
                d: "M10.5556 6.5H3.44444C3.19898 6.5 3 6.70351 3 6.95455V11.0455C3 11.2965 3.19898 11.5 3.44444 11.5H10.5556C10.801 11.5 11 11.2965 11 11.0455V6.95455C11 6.70351 10.801 6.5 10.5556 6.5Z",
                stroke: "currentColor",
                "stroke-linejoin": "round"
            }, null, -1), n("path", {
                d: "M5 6.5V4.72222C5 3.49492 5.89544 2.5 7 2.5C8.10456 2.5 9 3.49492 9 4.72222V6.5",
                stroke: "currentColor",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1), n("path", {
                d: "M7 8.5V9.5",
                stroke: "currentColor",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    l = {
        width: "6",
        height: "5",
        viewBox: "0 0 6 5",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const s = {
    render: function(t, e) {
        return r(), o("svg", l, e[0] || (e[0] = [n("path", {
            d: "M3 5L0.401924 0.5L5.59808 0.5L3 5Z",
            fill: "currentColor"
        }, null, -1)]))
    }
};
export {
    e as P, s as T
};