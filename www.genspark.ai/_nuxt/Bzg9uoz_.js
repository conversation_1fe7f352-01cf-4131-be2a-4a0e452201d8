import {
    d as t,
    b as i,
    o as e
} from "./Cf0SOiw0.js";
const o = {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const r = {
    render: function(r, n) {
        return e(), t("svg", o, n[0] || (n[0] = [i("g", {
            "clip-path": "url(#clip0_3477_569)"
        }, [i("path", {
            d: "M12.9297 17.1406L5.85862 10.0696L12.9297 2.99849",
            stroke: "white",
            "stroke-width": "2",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        })], -1), i("defs", null, [i("clipPath", {
            id: "clip0_3477_569"
        }, [i("rect", {
            width: "20",
            height: "20",
            fill: "white"
        })])], -1)]))
    }
};
export {
    r as L
};