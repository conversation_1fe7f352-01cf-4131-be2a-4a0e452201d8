import {
    r as e,
    a0 as t,
    v as n,
    aT as o,
    p as r,
    F as s,
    as as i,
    J as l,
    X as a,
    aj as u,
    c as d,
    aR as c,
    bb as f,
    h,
    a3 as p,
    au as v
} from "./Cf0SOiw0.js";
import {
    a as m,
    o as y,
    g as x
} from "./Jr9eiJio.js";
import {
    f as b
} from "./9wLWmnxl.js";

function g(o) {
    const r = e(!!o.value);
    if (r.value) return t(r);
    const s = n(o, (e => {
        e && (r.value = !0, s())
    }));
    return t(r)
}
const I = o("n-drawer-body"),
    S = o("n-modal-body"),
    E = o("n-modal-provider"),
    T = o("n-modal"),
    F = o("n-popover-body");

function w(e, t, n = "default") {
    const o = t[n];
    if (void 0 === o) throw new Error(`[vueuc/${e}]: slot[${n}] is empty.`);
    return o()
}

function z(e, t = !0, n = []) {
    return e.forEach((e => {
        if (null !== e)
            if ("object" == typeof e)
                if (Array.isArray(e)) z(e, t, n);
                else if (e.type === s) {
            if (null === e.children) return;
            Array.isArray(e.children) && z(e.children, t, n)
        } else e.type !== i && n.push(e);
        else "string" != typeof e && "number" != typeof e || n.push(r(String(e)))
    })), n
}

function Z(e, t, n = "default") {
    const o = t[n];
    if (void 0 === o) throw new Error(`[vueuc/${e}]: slot[${n}] is empty.`);
    const r = z(o());
    if (1 === r.length) return r[0];
    throw new Error(`[vueuc/${e}]: slot[${n}] should have exactly one child.`)
}
const $ = "@@coContext",
    N = {
        mounted(e, {
            value: t,
            modifiers: n
        }) {
            e[$] = {
                handler: void 0
            }, "function" == typeof t && (e[$].handler = t, y("clickoutside", e, t, {
                capture: n.capture
            }))
        },
        updated(e, {
            value: t,
            modifiers: n
        }) {
            const o = e[$];
            "function" == typeof t ? o.handler ? o.handler !== t && (m("clickoutside", e, o.handler, {
                capture: n.capture
            }), o.handler = t, y("clickoutside", e, t, {
                capture: n.capture
            })) : (e[$].handler = t, y("clickoutside", e, t, {
                capture: n.capture
            })) : o.handler && (m("clickoutside", e, o.handler, {
                capture: n.capture
            }), o.handler = void 0)
        },
        unmounted(e, {
            modifiers: t
        }) {
            const {
                handler: n
            } = e[$];
            n && m("clickoutside", e, n, {
                capture: t.capture
            }), e[$].handler = void 0
        }
    };
const A = new class {
        constructor() {
            this.elementZIndex = new Map, this.nextZIndex = 2e3
        }
        get elementCount() {
            return this.elementZIndex.size
        }
        ensureZIndex(e, t) {
            const {
                elementZIndex: n
            } = this;
            if (void 0 !== t) return e.style.zIndex = `${t}`, void n.delete(e);
            const {
                nextZIndex: o
            } = this;
            if (n.has(e)) {
                if (n.get(e) + 1 === this.nextZIndex) return
            }
            e.style.zIndex = `${o}`, n.set(e, o), this.nextZIndex = o + 1, this.squashState()
        }
        unregister(e, t) {
            const {
                elementZIndex: n
            } = this;
            n.has(e) && n.delete(e), this.squashState()
        }
        squashState() {
            const {
                elementCount: e
            } = this;
            e || (this.nextZIndex = 2e3), this.nextZIndex - e > 2500 && this.rearrange()
        }
        rearrange() {
            const e = Array.from(this.elementZIndex.entries());
            e.sort(((e, t) => e[1] - t[1])), this.nextZIndex = 2e3, e.forEach((e => {
                const t = e[0],
                    n = this.nextZIndex++;
                `${n}` !== t.style.zIndex && (t.style.zIndex = `${n}`)
            }))
        }
    },
    k = "@@ziContext",
    B = {
        mounted(e, t) {
            const {
                value: n = {}
            } = t, {
                zIndex: o,
                enabled: r
            } = n;
            e[k] = {
                enabled: !!r,
                initialized: !1
            }, r && (A.ensureZIndex(e, o), e[k].initialized = !0)
        },
        updated(e, t) {
            const {
                value: n = {}
            } = t, {
                zIndex: o,
                enabled: r
            } = n, s = e[k].enabled;
            r && !s && (A.ensureZIndex(e, o), e[k].initialized = !0), e[k].enabled = !!r
        },
        unmounted(e, t) {
            if (!e[k].initialized) return;
            const {
                value: n = {}
            } = t, {
                zIndex: o
            } = n;
            A.unregister(e, o)
        }
    };

function L(e) {
    return "string" == typeof e ? document.querySelector(e) : e()
}
const j = l({
    name: "LazyTeleport",
    props: {
        to: {
            type: [String, Object],
            default: void 0
        },
        disabled: Boolean,
        show: {
            type: Boolean,
            required: !0
        }
    },
    setup: e => ({
        showTeleport: g(c(e, "show")),
        mergedTo: d((() => {
            const {
                to: t
            } = e;
            return null != t ? t : "body"
        }))
    }),
    render() {
        return this.showTeleport ? this.disabled ? w("lazy-teleport", this.$slots) : a(u, {
            disabled: this.disabled,
            to: this.mergedTo
        }, w("lazy-teleport", this.$slots)) : null
    }
});

function R(e) {
    return e instanceof HTMLElement
}

function q(e) {
    for (let t = 0; t < e.childNodes.length; t++) {
        const n = e.childNodes[t];
        if (R(n) && (O(n) || q(n))) return !0
    }
    return !1
}

function C(e) {
    for (let t = e.childNodes.length - 1; t >= 0; t--) {
        const n = e.childNodes[t];
        if (R(n) && (O(n) || C(n))) return !0
    }
    return !1
}

function O(e) {
    if (! function(e) {
            if (e.tabIndex > 0 || 0 === e.tabIndex && null !== e.getAttribute("tabIndex")) return !0;
            if (e.getAttribute("disabled")) return !1;
            switch (e.nodeName) {
                case "A":
                    return !!e.href && "ignore" !== e.rel;
                case "INPUT":
                    return "hidden" !== e.type && "file" !== e.type;
                case "BUTTON":
                case "SELECT":
                case "TEXTAREA":
                    return !0;
                default:
                    return !1
            }
        }(e)) return !1;
    try {
        e.focus({
            preventScroll: !0
        })
    } catch (t) {}
    return document.activeElement === e
}
let V = [];
const D = l({
    name: "FocusTrap",
    props: {
        disabled: Boolean,
        active: Boolean,
        autoFocus: {
            type: Boolean,
            default: !0
        },
        onEsc: Function,
        initialFocusTo: String,
        finalFocusTo: String,
        returnFocusOnDeactivated: {
            type: Boolean,
            default: !0
        }
    },
    setup(t) {
        const o = f(),
            r = e(null),
            s = e(null);
        let i = !1,
            l = !1;
        const a = "undefined" == typeof document ? null : document.activeElement;

        function u() {
            return V[V.length - 1] === o
        }

        function d(e) {
            var n;
            "Escape" === e.code && u() && (null === (n = t.onEsc) || void 0 === n || n.call(t, e))
        }

        function c(e) {
            if (!l && u()) {
                const t = v();
                if (null === t) return;
                if (t.contains(x(e))) return;
                g("first")
            }
        }

        function v() {
            const e = r.value;
            if (null === e) return null;
            let t = e;
            for (; !(t = t.nextSibling, null === t || t instanceof Element && "DIV" === t.tagName););
            return t
        }

        function b() {
            var e;
            if (t.disabled) return;
            if (document.removeEventListener("focus", c, !0), V = V.filter((e => e !== o)), u()) return;
            const {
                finalFocusTo: n
            } = t;
            void 0 !== n ? null === (e = L(n)) || void 0 === e || e.focus({
                preventScroll: !0
            }) : t.returnFocusOnDeactivated && a instanceof HTMLElement && (l = !0, a.focus({
                preventScroll: !0
            }), l = !1)
        }

        function g(e) {
            if (u() && t.active) {
                const t = r.value,
                    n = s.value;
                if (null !== t && null !== n) {
                    const o = v();
                    if (null == o || o === n) return l = !0, t.focus({
                        preventScroll: !0
                    }), void(l = !1);
                    l = !0;
                    const r = "first" === e ? q(o) : C(o);
                    l = !1, r || (l = !0, t.focus({
                        preventScroll: !0
                    }), l = !1)
                }
            }
        }
        return h((() => {
            n((() => t.active), (e => {
                e ? (! function() {
                    var e;
                    if (t.disabled) return;
                    if (V.push(o), t.autoFocus) {
                        const {
                            initialFocusTo: n
                        } = t;
                        void 0 === n ? g("first") : null === (e = L(n)) || void 0 === e || e.focus({
                            preventScroll: !0
                        })
                    }
                    i = !0, document.addEventListener("focus", c, !0)
                }(), y("keydown", document, d)) : (m("keydown", document, d), i && b())
            }), {
                immediate: !0
            })
        })), p((() => {
            m("keydown", document, d), i && b()
        })), {
            focusableStartRef: r,
            focusableEndRef: s,
            focusableStyle: "position: absolute; height: 0; width: 0;",
            handleStartFocus: function(e) {
                if (l) return;
                const t = v();
                null !== t && (null !== e.relatedTarget && t.contains(e.relatedTarget) ? g("last") : g("first"))
            },
            handleEndFocus: function(e) {
                l || (null !== e.relatedTarget && e.relatedTarget === r.value ? g("last") : g("first"))
            }
        }
    },
    render() {
        const {
            default: e
        } = this.$slots;
        if (void 0 === e) return null;
        if (this.disabled) return e();
        const {
            active: t,
            focusableStyle: n
        } = this;
        return a(s, null, [a("div", {
            "aria-hidden": "true",
            tabindex: t ? "0" : "-1",
            ref: "focusableStartRef",
            style: n,
            onFocus: this.handleStartFocus
        }), e(), a("div", {
            "aria-hidden": "true",
            style: n,
            ref: "focusableEndRef",
            tabindex: t ? "0" : "-1",
            onFocus: this.handleEndFocus
        })])
    }
});

function M(e, t = "default", n = void 0) {
    const o = e[t];
    if (!o) return v("getFirstSlotVNode", `slot[${t}] is empty`), null;
    const r = b(o(n));
    return 1 === r.length ? r[0] : (v("getFirstSlotVNode", `slot[${t}] should have exactly one child`), null)
}

function H(e, t, n) {
    if (!t) return null;
    const o = b(t(n));
    return 1 === o.length ? o[0] : (v("getFirstSlotVNode", `slot[${e}] should have exactly one child`), null)
}
export {
    D as F, j as L, Z as a, M as b, N as c, I as d, H as e, T as f, w as g, E as h, S as m, F as p, B as z
};