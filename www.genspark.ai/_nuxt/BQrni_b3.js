import {
    aU as e,
    J as t,
    X as r,
    as as a,
    Y as n,
    Z as i,
    aV as s,
    c as o,
    ap as l
} from "./Cf0SOiw0.js";
import {
    i as p
} from "./MpDLC7up.js";
import {
    f as m
} from "./9wLWmnxl.js";
import {
    g as c
} from "./BjAOOWF7.js";
import {
    g as d,
    d as f
} from "./pB_XRIgB.js";
const u = {
    self: function() {
        return e
    }
};
let g;

function y() {
    if (!p) return !0;
    if (void 0 === g) {
        const e = document.createElement("div");
        e.style.display = "flex", e.style.flexDirection = "column", e.style.rowGap = "1px", e.appendChild(document.createElement("div")), e.appendChild(document.createElement("div")), document.body.appendChild(e);
        const t = 1 === e.scrollHeight;
        return document.body.removeChild(e), g = t
    }
    return g
}
const v = t({
    name: "Space",
    props: Object.assign(Object.assign({}, i.props), {
        align: String,
        justify: {
            type: String,
            default: "start"
        },
        inline: Boolean,
        vertical: Boolean,
        reverse: Boolean,
        size: {
            type: [String, Number, Array],
            default: "medium"
        },
        wrapItem: {
            type: Boolean,
            default: !0
        },
        itemClass: String,
        itemStyle: [String, Object],
        wrap: {
            type: Boolean,
            default: !0
        },
        internalUseGap: {
            type: Boolean,
            default: void 0
        }
    }),
    setup(e) {
        const {
            mergedClsPrefixRef: t,
            mergedRtlRef: r
        } = n(e), a = i("Space", "-space", void 0, u, e, t), p = s("Space", r, t);
        return {
            useGap: y(),
            rtlEnabled: p,
            mergedClsPrefix: t,
            margin: o((() => {
                const {
                    size: t
                } = e;
                if (Array.isArray(t)) return {
                    horizontal: t[0],
                    vertical: t[1]
                };
                if ("number" == typeof t) return {
                    horizontal: t,
                    vertical: t
                };
                const {
                    self: {
                        [l("gap", t)]: r
                    }
                } = a.value, {
                    row: n,
                    col: i
                } = d(r);
                return {
                    horizontal: f(i),
                    vertical: f(n)
                }
            }))
        }
    },
    render() {
        const {
            vertical: e,
            reverse: t,
            align: n,
            inline: i,
            justify: s,
            itemClass: o,
            itemStyle: l,
            margin: p,
            wrap: d,
            mergedClsPrefix: f,
            rtlEnabled: u,
            useGap: g,
            wrapItem: y,
            internalUseGap: v
        } = this, h = m(c(this), !1);
        if (!h.length) return null;
        const x = `${p.horizontal}px`,
            w = p.horizontal / 2 + "px",
            b = `${p.vertical}px`,
            j = p.vertical / 2 + "px",
            B = h.length - 1,
            C = s.startsWith("space-");
        return r("div", {
            role: "none",
            class: [`${f}-space`, u && `${f}-space--rtl`],
            style: {
                display: i ? "inline-flex" : "flex",
                flexDirection: e && !t ? "column" : e && t ? "column-reverse" : !e && t ? "row-reverse" : "row",
                justifyContent: ["start", "end"].includes(s) ? `flex-${s}` : s,
                flexWrap: !d || e ? "nowrap" : "wrap",
                marginTop: g || e ? "" : `-${j}`,
                marginBottom: g || e ? "" : `-${j}`,
                alignItems: n,
                gap: g ? `${p.vertical}px ${p.horizontal}px` : ""
            }
        }, y || !g && !v ? h.map(((t, n) => t.type === a ? t : r("div", {
            role: "none",
            class: o,
            style: [l, {
                maxWidth: "100%"
            }, g ? "" : e ? {
                marginBottom: n !== B ? b : ""
            } : u ? {
                marginLeft: C ? "space-between" === s && n === B ? "" : w : n !== B ? x : "",
                marginRight: C ? "space-between" === s && 0 === n ? "" : w : "",
                paddingTop: j,
                paddingBottom: j
            } : {
                marginRight: C ? "space-between" === s && n === B ? "" : w : n !== B ? x : "",
                marginLeft: C ? "space-between" === s && 0 === n ? "" : w : "",
                paddingTop: j,
                paddingBottom: j
            }]
        }, t))) : h)
    }
});
export {
    v as N
};