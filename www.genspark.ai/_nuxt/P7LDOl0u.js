import {
    c as e,
    d as a
} from "./DT-NG54s.js";
async function t(t, o = null, n = !1, l = !1, i = () => {}, r = () => {}) {
    const s = null == o ? void 0 : o.loading("image is uploading", {
            duration: 1e4
        }),
        c = document.createElement("input");
    c.type = "file", c.accept = "image/*,.heic,.heif", c.style.display = "none", document.body.appendChild(c);
    c.onchange = async () => {
        const {
            uploadImageUrl: o,
            path: d
        } = await (async () => {
            const e = await fetch(n ? "/api/get_upload_personal_image_url" : "/api/get_upload_image_url");
            if (!e.ok) throw null == s || s.destroy(), new Error(`HTTP error! status: ${e.status}`);
            const a = await e.json();
            if (!a || 0 !== a.status) throw null == s || s.destroy(), new Error(`API error! code: ${a.code}`);
            return {
                uploadImageUrl: a.data.upload_image_url,
                path: a.data.path
            }
        })();
        if (!o) return;
        const p = c.files;
        if (0 === p.length) return;
        let u = p[0];
        if (l) try {
            const t = await e(u, 1024, .95);
            u = await a(t, u.name)
        } catch (h) {
            const e = new FormData;
            e.append("file", u);
            const a = await fetch("/api/heic2jpeg", {
                    method: "POST",
                    body: e
                }),
                t = await a.blob();
            u = new window.File([t], u.name, {
                type: "image/jpeg"
            })
        }
        null == i || i(), fetch(o, {
            method: "PUT",
            headers: {
                "x-ms-blob-type": "BlockBlob"
            },
            body: u
        }).then((e => {
            if (!e.ok) throw new Error("Network response was not ok");
            t(n ? d : o.split("?")[0])
        })).catch((e => {})).finally((() => {
            c.value = "", null == s || s.destroy(), null == r || r()
        }))
    }, c && c.click()
}
export {
    t as i
};