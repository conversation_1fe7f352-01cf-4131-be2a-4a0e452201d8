class e extends Error {
    constructor(e = "Quota exceeded") {
        super(e), this.name = "QuotaExceededError"
    }
}
class t extends Error {
    constructor(e = "Recapture required") {
        super(e), this.name = "RecaptureError"
    }
}
const a = async e => new Promise((t => {
        const a = URL.createObjectURL(e),
            r = new Image;
        r.crossOrigin = "anonymous", r.src = a, r.onload = () => {
            URL.revokeObjectURL(a), t(e)
        }, r.onerror = async () => {
            URL.revokeObjectURL(a);
            const r = await (async e => {
                const t = new FormData;
                t.append("file", e);
                const a = await fetch("/api/heic2jpeg", {
                        method: "POST",
                        body: t
                    }),
                    r = await a.blob();
                return new window.File([r], e.name, {
                    type: "image/jpeg"
                })
            })(e);
            t(r)
        }
    })),
    r = async (e, t = 1024, a = .95, r = !1) => {
        const o = await n(e),
            s = document.createElement("canvas"),
            c = s.getContext("2d"),
            {
                width: d,
                height: w
            } = i(o.width, o.height, t);
        s.width = d, s.height = w, c.drawImage(o, 0, 0, d, w);
        return await new Promise((e => {
            s.toBlob(e, r ? "image/png" : "image/jpeg", a)
        }))
    },
    o = async (a, r, o = "", s = null) => {
        if (!r) throw new Error("Must provide operation parameters");
        const n = await fetch("/api/spark/edit_image", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                image_url: a,
                operation: r,
                prompt: o,
                params: s
            })
        });
        if (!n.ok) throw new Error(`HTTP error! status: ${n.status}`);
        const i = await n.json();
        if (-8 === i.status) throw new e;
        if (-11 === i.status) throw new t;
        if (0 !== i.status) throw new Error(`API error! code: ${i.status}`);
        return i.data
    },
    s = async () => {
        const e = await fetch("/api/get_upload_image_url");
        if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
        const t = await e.json();
        if (!t || 0 !== t.status) throw new Error(`API error! code: ${t.code}`);
        return t.data.upload_image_url
    },
    n = async e => {
        const t = new Image;
        return await new Promise(((a, r) => {
            t.onload = a, t.onerror = r, t.src = "string" == typeof e && e.startsWith("data:image/") ? e : URL.createObjectURL(e)
        })), t
    },
    i = (e, t, a) => ((e > a || t > a) && (e > t ? (t = t / e * a, e = a) : (e = e / t * a, t = a)), {
        width: Math.round(e),
        height: Math.round(t)
    }),
    c = async (e, t, a = null) => {
        const r = a || e.type || "application/octet-stream";
        if (!(await fetch(t, {
                method: "PUT",
                headers: {
                    "x-ms-blob-type": "BlockBlob",
                    "Content-Type": r
                },
                body: e
            })).ok) throw new Error("Upload failed");
        return t.split("?")[0]
    },
    d = async (e, t = 1024, a = .95, o = !1) => {
        const d = await s(),
            w = await r(e, t, a, o),
            g = await n(e),
            {
                width: u
            } = i(g.width, g.height, t);
        return {
            url: await c(w, d),
            scale: u / g.width
        }
    },
    w = async (e, t = 1024, a = .95) => {
        const r = await n(e);
        if (!("image/png" === e.type || "string" == typeof e && e.includes("data:image/png"))) {
            const e = r.width;
            return r.height, {
                maskUrl: null,
                scale: e / r.width
            }
        }
        const o = await s(),
            i = document.createElement("canvas"),
            d = i.getContext("2d"),
            w = r.width,
            g = r.height;
        i.width = w, i.height = g, d.drawImage(r, 0, 0, w, g);
        const u = d.getImageData(0, 0, w, g),
            l = u.data;
        for (let s = 0; s < l.length; s += 4) {
            const e = 0 === l[s + 3] ? 255 : 0;
            l[s] = e, l[s + 1] = e, l[s + 2] = e, l[s + 3] = 255
        }
        d.putImageData(u, 0, 0);
        const h = await new Promise((e => {
            i.toBlob(e, "image/jpeg", a)
        }));
        return {
            maskUrl: await c(h, o),
            scale: w / r.width
        }
    },
    g = async (e, t) => new Promise((async (a, o) => {
        try {
            const s = await r(e),
                n = indexedDB.open("ImageStorage", 1);
            n.onupgradeneeded = e => {
                const t = e.target.result;
                t.objectStoreNames.contains("images") || t.createObjectStore("images")
            }, n.onsuccess = e => {
                const r = e.target.result.transaction(["images"], "readwrite"),
                    n = r.objectStore("images").put(s, t);
                r.oncomplete = () => {
                    a(s)
                }, n.onerror = e => {
                    o(e)
                }
            }, n.onerror = e => {
                o(e.target.error)
            }
        } catch (s) {
            o(s)
        }
    })),
    u = async (e, t = !0) => new Promise(((a, r) => {
        const o = indexedDB.open("ImageStorage", 1);
        o.onsuccess = o => {
            const s = o.target.result,
                n = s.transaction(["images"], "readonly").objectStore("images").get(e);
            n.onsuccess = () => {
                if (n.result) {
                    const o = n.result,
                        i = new FileReader;
                    i.onload = () => {
                        const r = i.result;
                        if (t) {
                            s.transaction(["images"], "readwrite").objectStore("images").delete(e)
                        }
                        a(r)
                    }, i.onerror = () => {
                        r(new Error("Failed to convert blob to base64"))
                    }, i.readAsDataURL(o)
                } else r(new Error("No image found in IndexedDB"))
            }, n.onerror = () => {
                r(n.error)
            }
        }, o.onerror = () => {
            r(o.error)
        }
    })),
    l = async (e, t = 1024, a = .95, o = !1, s = !1) => {
        try {
            const n = await fetch(`${e}${s?"?_t=buster":""}`);
            if (!n.ok) throw new Error(`获取图片失败: ${n.status}`);
            const i = await n.blob(),
                c = await r(i, t, a, o);
            return new Promise(((e, t) => {
                const a = new FileReader;
                a.onload = () => e(a.result), a.onerror = t, a.readAsDataURL(c)
            }))
        } catch (n) {
            throw n
        }
    };
export {
    e as Q, t as R, w as a, d as b, l as c, o as e, s as g, u as l, g as s, a as t, c as u
};