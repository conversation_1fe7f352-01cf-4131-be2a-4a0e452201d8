import {
    _ as e
} from "./e-ES_T8J.js";
import {
    d as s,
    b as t,
    o as n,
    _ as o,
    r as i,
    S as a,
    v as l,
    f as r,
    F as d,
    k as u,
    E as c,
    n as p,
    e as v,
    a as _,
    t as m,
    l as h,
    D as g,
    s as w,
    c as f,
    y as k,
    ax as y,
    h as x,
    a3 as b,
    C,
    A as S,
    i as j,
    w as M,
    a4 as P,
    p as A,
    H as E,
    ai as T,
    U as B
} from "./Cf0SOiw0.js";
import {
    _ as $
} from "./D6bQc9d9.js";
import {
    b as I,
    _ as R,
    S as L
} from "./DdaMTYTP.js";
import {
    _ as F
} from "./DmWk8H2v.js";
import {
    M as q,
    _ as D,
    c as U,
    E as O,
    P as W,
    d as z,
    e as K
} from "./DWsxX4PV.js";
import {
    _ as V
} from "./BX7SPHBj.js";
import {
    _ as H
} from "./Cn9HXEST.js";
import {
    C as N
} from "./tBofk-gQ.js";
import {
    U as Z
} from "./IgM9N0FT.js";
import {
    P as G,
    W as J,
    F as Q
} from "./CIlzw36e.js";
import {
    E as X
} from "./D386eQgZ.js";
import {
    T as Y
} from "./B5SiUF0y.js";
import {
    u as ee
} from "./B0db5Fvl.js";
import {
    C as se
} from "./Boc3hm_9.js";
import {
    d as te
} from "./DOnko34f.js";
import {
    N as ne
} from "./CmeRl4Ak.js";
import {
    u as oe
} from "./DJ-JsGJu.js";
import {
    u as ie
} from "./B6noBY_5.js";
import {
    N as ae
} from "./CW991W2w.js";
import {
    T as le
} from "./BDUh8PoD.js";
import {
    S as re
} from "./DJt7CPhG.js";
const de = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 24 24",
    width: "24",
    height: "24"
};
const ue = {
        render: function(e, o) {
            return n(), s("svg", de, o[0] || (o[0] = [t("path", {
                d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5l1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    ce = {
        name: "MoaModelsResponse",
        components: {
            CheckIcon: ue,
            ExpandIcon: I,
            LoadingAnimation: $,
            MoaModelsConfig: q
        },
        emits: ["showSidebar"],
        props: {
            session_state: {
                type: Object,
                required: !1
            },
            modelsConfig: {
                type: Array,
                required: !1
            },
            thinking: {
                type: Boolean,
                required: !1
            }
        },
        setup(e, {
            emit: s
        }) {
            const t = i(e.modelsConfig),
                {
                    session_state: n,
                    thinking: o
                } = a(e);
            return l((() => e.modelsConfig), (() => {
                e.modelsConfig && (t.value = e.modelsConfig)
            })), t.value || (t.value = q.setup().modelsConfig), {
                session_state: n,
                modelsConfig: t,
                thinking: o,
                showSearchSideBar: e => {
                    s("showSidebar", e)
                }
            }
        }
    },
    pe = {
        key: 1,
        class: "moa-title"
    },
    ve = ["onClick"],
    _e = {
        class: "icon"
    },
    me = ["onClick"],
    he = {
        key: 0,
        class: "status"
    },
    ge = {
        key: 1,
        class: "icon finished"
    },
    we = {
        key: 1,
        class: "icon"
    },
    fe = ["src"],
    ke = {
        class: "text"
    },
    ye = {
        key: 0,
        class: "content"
    },
    xe = {
        key: 1,
        class: "content no-expand"
    },
    be = {
        key: 0
    },
    Ce = {
        key: 1
    },
    Se = {
        key: 2,
        class: "cursor"
    };
const je = o(ce, [
        ["render", function(e, o, i, a, l, g) {
            var w, f, k, y, x;
            const b = c("ExpandIcon"),
                C = $,
                S = c("CheckIcon"),
                j = R,
                M = F;
            return (null == (f = null == (w = a.session_state) ? void 0 : w.models) ? void 0 : f.length) > 1 ? (n(), s(d, {
                key: 0
            }, [r("", !0), (null == (y = null == (k = a.session_state) ? void 0 : k.models) ? void 0 : y.length) > 1 ? (n(), s("div", pe, " Mixture-of-Agents ")) : r("", !0), a.session_state ? (n(!0), s(d, {
                key: 2
            }, u(null == (x = a.session_state) ? void 0 : x.models, ((e, i) => {
                var l;
                return n(), s(d, null, [(n(!0), s(d, null, u(Array.from({
                    length: (null == (l = a.session_state) ? void 0 : l.layers) - 1
                }, ((e, s) => s)), (l => {
                    var d, u, c, g, w, f, k, y, x, P, A, E, T, B, $, I, R, L;
                    return n(), s("div", {
                        class: p([{
                            thinking: a.thinking && !(null == (d = a.session_state) ? void 0 : d[`layer_${l}_${i}_is_finished`]),
                            expanded: null == (u = a.session_state) ? void 0 : u[`layer_${l}_${i}_expanded`]
                        }, "bubble model-response-wrapper"])
                    }, [t("div", {
                        class: "expand-button",
                        onClick: () => {
                            a.session_state[`layer_${l}_${i}_expanded`] = !a.session_state[`layer_${l}_${i}_expanded`]
                        }
                    }, [t("div", _e, [v(b)])], 8, ve), (n(), s("div", {
                        class: "model-response",
                        key: i
                    }, [t("div", {
                        class: "title",
                        onClick: h((() => {
                            a.session_state[`layer_${l}_${i}_expanded`] = !a.session_state[`layer_${l}_${i}_expanded`]
                        }), ["stop"])
                    }, [a.thinking || (null == (c = a.session_state) ? void 0 : c[`layer_${l}_${i}_is_finished`]) ? (n(), s("div", he, [a.thinking && !(null == (g = a.session_state) ? void 0 : g[`layer_${l}_${i}_is_finished`]) ? (n(), _(C, {
                        key: 0
                    })) : (null == (w = a.session_state) ? void 0 : w[`layer_${l}_${i}_is_finished`]) ? (n(), s("div", ge, [v(S)])) : r("", !0)])) : r("", !0), a.modelsConfig.find((s => s.name == e)) ? (n(), s("div", we, [t("img", {
                        src: a.modelsConfig.find((s => s.name == e)).icon
                    }, null, 8, fe)])) : r("", !0), t("div", ke, m((null == (f = a.modelsConfig.find((s => s.name == e))) ? void 0 : f.label) || e), 1)], 8, me), a.session_state[`layer_${l}_${i}_expanded`] ? (n(), s("div", ye, ["interactive_deep_dive_search" == (null == (k = a.session_state) ? void 0 : k.render_template) ? (n(), _(j, {
                        key: 0,
                        "show-thinking": !1,
                        "show-search-status-top-bar": !1,
                        "show-search-source-top-bar": !1,
                        "show-followup-question": !1,
                        "show-try-moa": !1,
                        "show-loading-icon": !1,
                        "show-result-buttons": !1,
                        "external-streaming-detail-answer": null == (x = null == (y = a.session_state) ? void 0 : y[`layer_${l}_${i}`]) ? void 0 : x.detailAnswer,
                        "external-api-response": null == (A = null == (P = a.session_state) ? void 0 : P[`layer_${l}_${i}`]) ? void 0 : A.content,
                        "external-search-status-top-bar-data": null == (E = a.session_state) ? void 0 : E.search_status_top_bar_data,
                        onShowSidebar: o[0] || (o[0] = e => {
                            a.showSearchSideBar(e)
                        })
                    }, null, 8, ["external-streaming-detail-answer", "external-api-response", "external-search-status-top-bar-data"])) : (n(), _(M, {
                        key: 1,
                        source: null == (T = a.session_state) ? void 0 : T[`layer_${l}_${i}`]
                    }, null, 8, ["source"]))])) : (n(), s("div", xe, [t("div", null, ["interactive_deep_dive_search" == (null == (B = a.session_state) ? void 0 : B.render_template) ? (n(), s("div", be, m(null == (I = null == ($ = a.session_state) ? void 0 : $[`layer_${l}_${i}`]) ? void 0 : I.detailAnswer), 1)) : (n(), s("div", Ce, m(null == (R = a.session_state) ? void 0 : R[`layer_${l}_${i}`]), 1)), a.thinking && !(null == (L = a.session_state) ? void 0 : L[`layer_${l}_${i}_is_finished`]) ? (n(), s("span", Se, "█")) : r("", !0)])]))]))], 2)
                })), 256))], 64)
            })), 256)) : r("", !0)], 64)) : r("", !0)
        }],
        ["__scopeId", "data-v-bec29440"]
    ]),
    Me = {
        width: "16",
        height: "17",
        viewBox: "0 0 16 17",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Pe = {
        render: function(e, t) {
            return n(), s("svg", Me, t[0] || (t[0] = [g('<rect y="0.5" width="16" height="16" rx="8" fill="#EAEAEA"></rect><g clip-path="url(#clip0_9319_26881)"><path d="M9.3657 6.27154C9.70614 6.09791 10.0916 6 10.5 6C11.8807 6 13 7.11929 13 8.5C13 9.88071 11.8807 11 10.5 11C9.11929 11 8 9.88071 8 8.5C8 7.11929 6.88071 6 5.5 6C4.11929 6 3 7.11929 3 8.5C3 9.88071 4.11929 11 5.5 11C6.04373 11 6.54692 10.8264 6.95717 10.5317" stroke="black" stroke-linecap="round"></path></g><defs><clipPath id="clip0_9319_26881"><rect width="11.4286" height="11.4286" fill="white" transform="translate(2.28564 2.78516)"></rect></clipPath></defs>', 3)]))
        }
    },
    Ae = {
        width: "40",
        height: "40",
        viewBox: "0 0 40 40",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Ee = {
        render: function(e, t) {
            return n(), s("svg", Ae, t[0] || (t[0] = [g('<g clip-path="url(#clip0_145_3774)"><circle cx="20" cy="20" r="20" fill="currentColor"></circle><path d="M15 15.002L15 25.002" stroke="white" stroke-width="3.33333" stroke-linecap="round" stroke-linejoin="round"></path><path d="M20 11.668L20 28.3346" stroke="white" stroke-width="3.33333" stroke-linecap="round" stroke-linejoin="round"></path><path d="M25 15.002L25 25.002" stroke="white" stroke-width="3.33333" stroke-linecap="round" stroke-linejoin="round"></path><path d="M10 18.334L10 21.6673" stroke="white" stroke-width="3.33333" stroke-linecap="round" stroke-linejoin="round"></path><path d="M30 18.334L30 21.6673" stroke="white" stroke-width="3.33333" stroke-linecap="round" stroke-linejoin="round"></path></g><defs><clipPath id="clip0_145_3774"><rect width="40" height="40" fill="white"></rect></clipPath></defs>', 2)]))
        }
    },
    Te = {
        key: 0,
        class: "absolute top-0 left-0 right-0 bg-gradient-to-r from-[#F5F5FF] to-[#E5EDFB] py-[16px] md:py-[20px] flex justify-center items-center gap-[75px] px-[30px] mx-[12px] rounded-[16px] md:rounded-none md:mx-0"
    },
    Be = {
        class: "md:w-[760px] md:flex justify-between items-center text-center md:text-left gap-[65px]"
    },
    $e = {
        class: "justify-center text-[#232425] text-[14px] md:text-[16px] font-bold leading-[1.5]"
    },
    Ie = {
        class: "justify-center mt-[8px] text-[#232425] text-[13px] md:text-[14px] font-normal leading-[1.5]"
    },
    Re = {
        __name: "upgrade_prompt",
        props: {
            currentUser: {
                type: Object,
                default: null
            },
            isPlusPlan: {
                type: Boolean,
                default: !1
            },
            project: {
                type: Object,
                default: null
            },
            upgradePrompt: {
                type: Object,
                default: {}
            }
        },
        setup(e) {
            const {
                t: o
            } = w(), i = e, a = f((() => {
                var e, s, t, n;
                return (!1 === i.currentUser || "free" === (null == (e = i.currentUser) ? void 0 : e.plan)) && ((null == (n = null == (t = null == (s = i.project) ? void 0 : s.session_state) ? void 0 : t.messages) ? void 0 : n.length) || 0) < 1
            })), l = () => {
                window.open("/pricing", "_blank")
            };
            return (i, d) => k(a) ? (n(), s("div", Te, [t("div", Be, [t("div", null, [t("div", $e, m(e.upgradePrompt.title), 1), t("div", Ie, m(e.upgradePrompt.description), 1)]), t("div", {
                class: "inline-flex px-[20px] py-[6px] bg-[#0F7FFF] rounded-[31px] text-white text-[16px] font-bold leading-normal cursor-pointer mt-[12px] md:mt-0 whitespace-nowrap",
                onClick: l
            }, m(k(o)("components.upgrade_prompt.upgrade")), 1)])])) : r("", !0)
        }
    },
    Le = {
        class: "general-chat-wrapper"
    },
    Fe = {
        key: 0,
        class: "empty-placeholder"
    },
    qe = {
        key: 0,
        class: "conversation-content"
    },
    De = {
        key: 0,
        class: "assistant-message-divider"
    },
    Ue = {
        key: 1,
        class: "assistant-message-divider"
    },
    Oe = ["onClick"],
    We = {
        class: "icon"
    },
    ze = {
        class: "model-response"
    },
    Ke = ["onClick"],
    Ve = {
        key: 0,
        class: "status"
    },
    He = {
        key: 1,
        class: "icon finished"
    },
    Ne = {
        class: "icon"
    },
    Ze = {
        key: 0,
        class: "content"
    },
    Ge = {
        key: 1,
        class: "content no-expand"
    },
    Je = {
        key: 0,
        class: "cursor"
    },
    Qe = ["onClick"],
    Xe = {
        class: "icon"
    },
    Ye = {
        class: "model-response"
    },
    es = ["onClick"],
    ss = {
        key: 0,
        class: "status"
    },
    ts = {
        key: 1,
        class: "icon finished"
    },
    ns = {
        class: "text"
    },
    os = {
        key: 0,
        class: "content"
    },
    is = {
        key: 1,
        class: "content no-expand"
    },
    as = {
        key: 0,
        class: "cursor"
    },
    ls = ["message-content-id", "onPaste", "onBlur", "onKeydown"],
    rs = {
        class: "desc"
    },
    ds = {
        class: "content"
    },
    us = {
        key: 1
    },
    cs = {
        key: 0,
        class: "buttons"
    },
    ps = ["onClick"],
    vs = {
        class: "icon"
    },
    _s = {
        class: "label"
    },
    ms = {
        class: "desc"
    },
    hs = {
        class: "content"
    },
    gs = {
        key: 0
    },
    ws = {
        key: 1,
        class: "image_url_wrapper"
    },
    fs = ["src"],
    ks = {
        key: 2,
        class: "private-file-wrappers"
    },
    ys = {
        class: "file-wrapper"
    },
    xs = {
        class: "icon file-icon"
    },
    bs = {
        class: "file-info"
    },
    Cs = {
        class: "file-name"
    },
    Ss = {
        class: "file-size"
    },
    js = {
        key: 4,
        class: "bubble try_moa"
    },
    Ms = {
        class: "right"
    },
    Ps = ["onClick"],
    As = {
        key: 5,
        class: "bubble context_length_exceeded"
    },
    Es = {
        class: "right"
    },
    Ts = {
        key: 6,
        class: "bubble retry"
    },
    Bs = {
        class: "left"
    },
    $s = {
        class: "right"
    },
    Is = {
        key: 7,
        class: "w-[70%] bg-[#FAFAFA] py-[16px] px-[16px] rounded-[16px] break-words"
    },
    Rs = ["user-message-content-id", "onPaste", "onBlur", "onKeydown"],
    Ls = {
        class: "p-0 m-0"
    },
    Fs = {
        class: "flex gap-[16px] justify-end"
    },
    qs = ["onClick"],
    Ds = ["onClick"],
    Us = ["onClick"],
    Os = {
        class: "message-action-icon icon p-[4px] hover:bg-[#F5F5F5] rounded-[6px]"
    },
    Ws = ["onClick"],
    zs = {
        class: "message-action-icon icon p-[4px] hover:bg-[#F5F5F5] rounded-[6px]"
    },
    Ks = {
        key: 1,
        class: "input-wrapper-wrapper"
    },
    Vs = {
        class: "input-wrapper-wrapper-inner"
    },
    Hs = {
        key: 0,
        class: "controls"
    },
    Ns = {
        class: "models-wrapper"
    },
    Zs = {
        class: "models-selected"
    },
    Gs = {
        class: "model-selected"
    },
    Js = {
        class: "icon"
    },
    Qs = ["src"],
    Xs = {
        class: "text"
    },
    Ys = {
        class: "models-list"
    },
    et = ["onClick"],
    st = {
        class: "row"
    },
    tt = {
        class: "left"
    },
    nt = {
        class: "icon"
    },
    ot = ["src"],
    it = {
        class: "text"
    },
    at = {
        class: "right"
    },
    lt = ["checked"],
    rt = {
        key: 0,
        class: "description"
    },
    dt = {
        class: "text"
    },
    ut = {
        key: 0,
        class: "divider"
    },
    ct = {
        key: 0,
        class: "prompt-input-wrapper"
    },
    pt = {
        class: "input-wrapper",
        ref: "inputWrapper"
    },
    vt = {
        key: 1,
        class: "realtime-input-wrapper"
    },
    _t = {
        key: 0,
        class: "mobile-sidebar-page"
    },
    mt = "moa-chat-modelsSelected",
    ht = '<span class="cursor">█</span>',
    gt = '<div class="thinking_prompt">Thinking...</div>',
    wt = o({
        __name: "general_chat_content",
        props: {
            project: {
                type: Object,
                required: !0
            },
            no_input: {
                type: Boolean,
                default: !1
            },
            initialQuery: {
                type: String,
                default: ""
            },
            initialText: {
                type: String,
                default: ""
            },
            readOnly: {
                type: Boolean,
                default: !1
            },
            externalRequestWebKnowledge: {
                type: Boolean,
                default: !1
            },
            externalModelsSelected: {
                type: String,
                default: "gpt-4.1"
            },
            searchMode: {
                type: Boolean,
                default: !1
            },
            inputPlaceHolder: {
                type: String,
                default: "Message"
            },
            copilotType: {
                type: String,
                default: "COPILOT_MOA_CHAT"
            },
            noAutoScroll: {
                type: Boolean,
                default: !1
            },
            realtimeMode: {
                type: Boolean,
                default: !1
            },
            showRealtimeToggleButton: {
                type: Boolean,
                default: void 0
            }
        },
        emits: ["newSessionCreated", "requestCreateNewSession", "redoMoAsearch", "finish-loading-deep-dive-search-result", "requestLoadProject"],
        setup(o, {
            expose: c,
            emit: h
        }) {
            const g = o,
                le = h,
                re = S(),
                de = ie(),
                {
                    t: ce
                } = w(),
                {
                    project: pe,
                    no_input: ve,
                    readOnly: _e,
                    searchMode: me,
                    inputPlaceHolder: he,
                    copilotType: ge,
                    noAutoScroll: we
                } = a(g),
                fe = i(g.searchMode ? 1 : 0),
                ke = i(g.realtimeMode),
                ye = ee(),
                {
                    prompt: xe,
                    images: be,
                    files: Ce,
                    requestWebKnowledge: Se,
                    modelsSelected: Me
                } = y(ye),
                Ae = {
                    mounted(e) {
                        B((() => {
                            if (e.focus(), "true" === e.contentEditable) {
                                const s = document.createRange(),
                                    t = window.getSelection();
                                s.selectNodeContents(e), s.collapse(!1), t.removeAllRanges(), t.addRange(s)
                            }
                        }))
                    }
                },
                Te = ({
                    callId: e,
                    name: s,
                    args: t
                }) => {
                    let n;
                    try {
                        if ("string" == typeof t) n = JSON.parse(t);
                        else {
                            if ("object" != typeof t || null === t) return;
                            n = t
                        }
                        C.log("Successfully parsed args:", n);
                        const e = null == n ? void 0 : n.query;
                        C.log("Extracted query:", e), e && (Zt.value = !0, Ie.value = "gpt-4o-mini", Rt(e))
                    } catch (o) {
                        return
                    }
                };
            l((() => g.realtimeMode), (e => {
                ke.value = e
            }));
            const Be = e => {
                    C.log("Showing search sidebar"), C.log("showSearchSideBar", e);
                    const s = {
                        searchResults: e.searchResults,
                        keywords: e.keywords,
                        jumpToUrl: e.jumpToUrl
                    };
                    C.log("showSearchSideBarData", s), Ht.value = s, Kt.value = !0
                },
                $e = q.setup().modelsConfig,
                Ie = i(g.externalModelsSelected);
            if (!me.value) {
                const e = localStorage.getItem(mt);
                e && $e.find((s => s.name == e)) ? Ie.value = e : Ie.value = $e[0].name
            }
            l(Ie, (() => {
                localStorage.setItem(mt, Ie.value)
            }));
            const wt = i(!1),
                ft = i(!1),
                kt = f((() => (Zt.value && Ie.value == $e[0].name && (Ie.value = $e[0].search_model_name), Zt.value || Ie.value != $e[0].search_model_name || (Ie.value = $e[0].name), {
                    models: Ie.value.split(","),
                    run_with_another_model: ft.value,
                    request_web_knowledge: Zt.value,
                    speed_mode: ke.value
                }))),
                yt = i(null),
                xt = i([]),
                bt = i([]),
                Ct = i(null),
                St = j("jsBridge"),
                jt = i(!1),
                Mt = e => {
                    (null == e ? void 0 : e.user_s_input) && (Zt.value = !0, Rt(e.user_s_input))
                },
                Pt = () => {
                    le("finish-loading-deep-dive-search-result", pe.value.id)
                },
                At = () => {
                    ke.value = !1
                },
                Et = () => {
                    ke.value = !0
                },
                Tt = () => {
                    pe.value && pe.value.session_state && pe.value.session_state.messages && Ct.value && Ct.value.setMessages(pe.value.session_state.messages)
                };
            l((() => pe.value && pe.value.id), (() => {
                Tt()
            })), l((() => pe.value), (() => {
                Tt()
            })), l((() => Ie.value), (() => {
                var e;
                (null == (e = $e.find((e => e.name == Ie.value || e.search_model_name == Ie.value))) ? void 0 : e.disable_search_web) && (Zt.value = !1)
            }));
            const Bt = f((() => {
                    if (Ct.value && Ct.value.getMessagesRef) {
                        const e = Ct.value.getMessagesRef().value;
                        if (e.length > 0) return e[e.length - 1]
                    }
                    return null
                })),
                $t = i(""),
                It = i(null),
                Rt = (e, s, t) => {
                    if (Ct.value.is_asking) return void de.warning("Please wait for the previous message to finish");
                    const n = Ct.value.getMessagesRef().value;
                    if (n.length > 0) {
                        const e = n[n.length - 1];
                        if (e.session_state && e.session_state.context_length_exceeded) return void de.warning("Context length exceeded, please open a new session")
                    }
                    ft.value = !1, Ct.value.submitPrompt(e, "", s, t), $t.value = "", xt.value = [], bt.value = [], ye.setPrompt(""), ye.setImages([]), ye.setFiles([])
                },
                Lt = i(null);
            l((() => Lt.value), (() => {
                Lt.value && Ct.value.setContentElement(Lt.value)
            }));
            const Ft = i(0),
                qt = te((() => {
                    Ft.value != window.innerWidth && (Ft.value = window.innerWidth, C.isMobile() || window.innerWidth <= 1220 ? Ct.value.setScrollElement(document.documentElement) : Ct.value.setScrollElement(yt.value))
                }), 100);
            x((() => {
                qt(), window.addEventListener("resize", qt)
            })), b((() => {
                window.removeEventListener("resize", qt)
            }));
            const Dt = e => {
                C.log("onProjectEvent", e), "project_start" == e.type && (pe.value.id = e.id), "project_field" == e.type && oe(pe.value, e.field_name, (() => e.field_value))
            };
            x((() => {
                Tt(), Ut(), window.appInterface || (window.appInterface = {}), window.appInterface.appEnterForeground = () => {
                    Ut()
                }, window.appInterface.appEnterBackground = () => {}, window.appInterface.chatAbort = () => {
                    var e;
                    null == (e = Ct.value) || e.abort()
                }, window.appInterface.submitPrompt = e => {
                    Rt(e.prompt, e.images, e.files)
                }, window.appInterface.newSession = () => {
                    le("requestCreateNewSession")
                }, window.appInterface.isAsking = () => {
                    var e;
                    return null == (e = Ct.value) ? void 0 : e.is_asking
                }, window.appInterface.stopAsking = () => {
                    var e;
                    null == (e = Ct.value) || e.cancelAskCopilot({
                        userAborted: !0
                    })
                }, window.appInterface.loadProject = e => {
                    le("requestLoadProject", e)
                }, window.appInterface.refreshProject = () => {
                    pe.value && pe.value.id && le("requestLoadProject", pe.value.id)
                }, l((() => {
                    var e;
                    return null == (e = Ct.value) ? void 0 : e.is_asking
                }), (e => {
                    var s;
                    null == (s = St.value) || s.callHandler("isAskingChanged", {
                        isAsking: e
                    })
                }), {
                    immediate: !0
                }), l((() => {
                    var e;
                    return null == (e = pe.value) ? void 0 : e.id
                }), (e => {
                    var s;
                    null == (s = St.value) || s.callHandler("projectIdChanged", {
                        projectId: e
                    })
                }), {
                    immediate: !0
                })
            }));
            const Ut = () => {
                    var e, s, t;
                    (null == (e = Ct.value) ? void 0 : e.is_asking) || (null == (s = pe.value) ? void 0 : s.id) && (null == (t = Ct.value) || t.askCopilot({
                        user_s_input: "",
                        action_params: {
                            force: !0
                        },
                        answer: null,
                        only_get_events: !0
                    }))
                },
                Ot = () => {
                    It.value.selectFile()
                },
                Wt = e => {
                    ft.value = !0, Ie.value = $e[0].name, e && e.session_state && "interactive_deep_dive_search" == e.session_state.render_template && (Zt.value = !0, Ie.value = $e[0].search_model_name), setTimeout((() => {
                        if (Ct.value.forceSubmit(), me.value) {
                            const e = Ct.value.getMessagesRef().value.length;
                            fe.value = e - 1, le("redoMoASearch")
                        } else fe.value = 0
                    }), 0)
                },
                zt = () => Ct.value.getMessagesRef().value.length > 0 && Ct.value.getMessagesRef().value.some((e => "string" != typeof e.content && e.content.some((e => "text" != e.type))));
            x((async () => {
                if (g.initialQuery || g.initialText) {
                    let e = [];
                    if (g.initialText && (e = await (async e => {
                            if (!e) return;
                            const s = new Blob([e], {
                                    type: "text/plain"
                                }),
                                t = new File([s], "content.txt", {
                                    type: "text/plain"
                                }),
                                n = "txt";
                            try {
                                const e = await fetch("/api/get_upload_personal_image_url");
                                if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                                const s = await e.json();
                                if (!s || 0 !== s.status) throw new Error(`API error! code: ${s.code}`);
                                const {
                                    upload_image_url: o,
                                    private_storage_url: i
                                } = s.data;
                                if (!(await fetch(o, {
                                        method: "PUT",
                                        headers: {
                                            "x-ms-blob-type": "BlockBlob"
                                        },
                                        body: t
                                    })).ok) throw new Error("Network response was not ok");
                                return [{
                                    name: t.name,
                                    type: t.type,
                                    ext: n,
                                    size: t.size,
                                    private_storage_url: i
                                }]
                            } catch (o) {
                                return null
                            }
                        })(g.initialText), !e)) return;
                    Rt(g.initialQuery, [], e)
                } else {
                    const e = new URLSearchParams(window.location.href.split("?")[1]),
                        s = e.get("prompt");
                    if (e.get("from"), s) {
                        Rt(s), e.delete("prompt"), e.delete("from");
                        const t = window.location.pathname + (e.toString() ? "?" + e.toString() : "");
                        history.replaceState(null, "", t)
                    } else C.isMobile() || It.value.focus()
                }
            }));
            const Kt = i(!1),
                Vt = i(!1),
                Ht = i({}),
                Nt = i(!1);
            x((() => {
                Nt.value = window.innerWidth <= 768, Vt.value = window.innerWidth <= 768, window.addEventListener("resize", (() => {
                    Nt.value = window.innerWidth <= 768, Vt.value = window.innerWidth <= 768
                })), "chat_now" === re.query.action && ($t.value = xe.value, xt.value = be.value, bt.value = Ce.value, Zt.value = Se.value, Ie.value = Me.value, Rt(xe.value, be.value, Ce.value))
            })), l(Kt, (e => {
                e && Vt.value ? document.body.style.overflow = "hidden" : document.body.style.overflow = ""
            })), b((() => {
                document.body.style.overflow = ""
            }));
            const Zt = i(g.externalRequestWebKnowledge);
            l((() => g.externalRequestWebKnowledge), (e => {
                Zt.value = e
            }));
            const Gt = j("currentUser"),
                Jt = f((() => {
                    var e;
                    return "plus" === (null == (e = null == Gt ? void 0 : Gt.value) ? void 0 : e.plan)
                })),
                Qt = f((() => void 0 !== g.showRealtimeToggleButton ? g.showRealtimeToggleButton : !C.isGensparkApp() && !(!Gt.value || !Gt.value.gk_realtime_dogfood))),
                Xt = () => {
                    document.body.scrollTop = document.body.scrollHeight
                };
            x((() => {
                C.isMobile() && window.addEventListener("resize", Xt)
            })), b((() => {
                window.removeEventListener("resize", Xt)
            }));
            const Yt = (e, s) => {
                    e.preventDefault();
                    const t = (e.clipboardData || window.clipboardData).getData("text");
                    document.execCommand("insertText", !1, t)
                },
                en = (e, s, t) => {
                    "Enter" !== e.key || e.shiftKey || (e.preventDefault(), sn(s, t))
                },
                sn = (e, s) => {
                    e.contentEditable = !1;
                    let t = document.querySelector(`[message-content-id="${s}"]`);
                    t && (t.contentEditable = !1), t = document.querySelector(`[user-message-content-id="${s}"]`), t && (t.contentEditable = !1), s >= 0 && t && t.innerText && t.innerText.trim() && Ct.value.getMessagesRef().value.length > s && (Ct.value.getMessagesRef().value = Ct.value.getMessagesRef().value.slice(0, s + 1), Ct.value.getMessagesRef().value[s].content = t.innerText, C.log("saveMessage", t.innerText), Ct.value.retry())
                },
                tn = C.filesizeString,
                nn = e => "string" != typeof e ? e : e.replace(/^[\n\t \r]+|[\n\t \r]+$/g, "");
            return c({
                submitAndClearPrompt: Rt,
                selectImage: Ot,
                lastMessage: Bt
            }), (o, i) => {
                var a, l, c, h, g;
                const w = e;
                return n(), s("div", Le, [v(Re, {
                    currentUser: k(Gt),
                    isPlusPlan: Jt.value,
                    project: k(pe),
                    upgradePrompt: {
                        title: k(ce)("components.upgrade_prompt.title_plus"),
                        description: k(ce)("components.upgrade_prompt.description")
                    }
                }, null, 8, ["currentUser", "isPlusPlan", "project", "upgradePrompt"]), t("div", {
                    class: "chat-wrapper",
                    ref_key: "chatWrapper",
                    ref: yt
                }, [v(V, {
                    copilotType: k(ge),
                    ref_key: "copilotRef",
                    ref: Ct,
                    defaultMessageContent: "",
                    extra_data: kt.value,
                    onProjectEvent: Dt,
                    noAutoScroll: k(we),
                    onCopilotAskFinish: i[2] || (i[2] = e => {
                        le("newSessionCreated", pe.value)
                    })
                }, {
                    default: M((({
                        messages: e,
                        doAction: a
                    }) => [e && 0 != e.length || k(me) || ke.value ? r("", !0) : (n(), s("div", Fe, m(o.$t("components.general_chat_content.hi_there_what_can_i_help_with")), 1)), e && e.length > 0 ? (n(), s("div", {
                        key: 1,
                        class: "conversation-wrapper",
                        ref_key: "conversationContent",
                        ref: Lt
                    }, [e && e.length > 0 ? (n(), s("div", qe, [(n(!0), s(d, null, u(e, ((a, l) => {
                        var c, h, g, w, f, y, x, b, S, j, M, P, T, B, q, D, W, z, K, V, H, Z, Y, ee, te, ne, oe, ie, ae, re, pe, ve, he, ge, we, ke, ye, xe, be, Ce, Se, Me, Ee, Te, Ie, Re, Le, Fe, qe, Ks;
                        return n(), s(d, {
                            key: l
                        }, [!a.hide && l >= fe.value ? (n(), s(d, {
                            key: 0
                        }, [l > 0 && "assistant" == a.role && "assistant" == e[l - 1].role && !k(me) ? (n(), s("div", De)) : r("", !0), l > fe.value && "user" == a.role && k(me) ? (n(), s("div", Ue)) : r("", !0), (null == (c = a.session_state) ? void 0 : c.search_source_top_bar_data) ? (n(), _(L, {
                            key: 2,
                            "source-list": null == (h = a.session_state) ? void 0 : h.search_source_top_bar_data,
                            onShowSideBar: e => {
                                var s;
                                return (e => {
                                    const s = {
                                        searchResults: e.search_results,
                                        keywords: e.keywords,
                                        jumpToUrl: ""
                                    };
                                    Ht.value = s, Kt.value = !0
                                })(null == (s = a.session_state) ? void 0 : s.search_status_top_bar_data)
                            }
                        }, null, 8, ["source-list", "onShowSideBar"])) : r("", !0), t("div", {
                            class: p(["conversation-statement", {
                                editing: a.contentEditable,
                                user: "user" == a.role,
                                assistant: "assistant" == a.role,
                                "plain-text": "string" == typeof a.content
                            }])
                        }, [t("div", {
                            class: p(["conversation-item-desc", ["user" == a.role ? "user" : "assistant", a.contentEditable ? "content-editable" : ""]])
                        }, [v(je, {
                            session_state: null == a ? void 0 : a.session_state,
                            modelsConfig: k($e),
                            thinking: null == a ? void 0 : a.thinking,
                            onShowSidebar: Be
                        }, null, 8, ["session_state", "modelsConfig", "thinking"]), (null == (g = a.session_state) ? void 0 : g.reflection) ? (n(), s("div", {
                            key: 0,
                            class: p(["bubble model-response-wrapper", {
                                thinking: a.thinking,
                                expanded: null == (w = a.session_state) ? void 0 : w.reflection_expanded
                            }])
                        }, [t("div", {
                            class: "expand-button",
                            onClick: () => {
                                a.session_state.reflection_expanded = !a.session_state.reflection_expanded
                            }
                        }, [t("div", We, [v(k(I))])], 8, Oe), t("div", ze, [t("div", {
                            class: "title",
                            onClick: () => {
                                a.session_state.reflection_expanded = !a.session_state.reflection_expanded
                            }
                        }, [a.thinking || (null == (f = null == a ? void 0 : a.session_state) ? void 0 : f.reflection_is_finished) ? (n(), s("div", Ve, [a.thinking && !(null == (y = null == a ? void 0 : a.session_state) ? void 0 : y.reflection_is_finished) ? (n(), _($, {
                            key: 0
                        })) : (null == (x = null == a ? void 0 : a.session_state) ? void 0 : x.reflection_is_finished) ? (n(), s("div", He, [v(k(ue))])) : r("", !0)])) : r("", !0), t("div", Ne, [v(k(Pe))]), i[15] || (i[15] = t("div", {
                            class: "text"
                        }, "Reflection", -1))], 8, Ke), (null == (b = a.session_state) ? void 0 : b.reflection_expanded) ? (n(), s("div", Ze, [v(F, {
                            source: ((null == (S = a.session_state) ? void 0 : S.reflection) ? nn(null == (j = a.session_state) ? void 0 : j.reflection) : "") + (a.thinking && (null == (M = a.session_state) ? void 0 : M.reflection_is_started) && !(null == (P = a.session_state) ? void 0 : P.reflection_is_finished) ? ht : ""),
                            is_thinking: a.thinking
                        }, null, 8, ["source", "is_thinking"])])) : (n(), s("div", Ge, [t("div", null, [A(m(null == (T = null == a ? void 0 : a.session_state) ? void 0 : T.reflection) + " ", 1), a.thinking && !(null == (B = null == a ? void 0 : a.session_state) ? void 0 : B.reflection_is_finished) ? (n(), s("span", Je, "█")) : r("", !0)])]))])], 2)) : r("", !0), (null == (q = a.session_state) ? void 0 : q.answerthink) ? (n(), s("div", {
                            key: 1,
                            class: p(["bubble model-response-wrapper answerthink-box", {
                                thinking: a.thinking,
                                expanded: null == (D = a.session_state) ? void 0 : D.answerthink_expanded
                            }])
                        }, [t("div", {
                            class: "expand-button",
                            onClick: () => {
                                a.session_state.answerthink_expanded = !a.session_state.answerthink_expanded
                            }
                        }, [t("div", Xe, [v(k(I))])], 8, Qe), t("div", Ye, [t("div", {
                            class: "title",
                            onClick: () => {
                                a.session_state.answerthink_expanded = !a.session_state.answerthink_expanded
                            }
                        }, [a.thinking || (null == (W = null == a ? void 0 : a.session_state) ? void 0 : W.answerthink_is_finished) ? (n(), s("div", ss, [a.thinking && !(null == (z = null == a ? void 0 : a.session_state) ? void 0 : z.answerthink_is_finished) ? (n(), _($, {
                            key: 0
                        })) : (null == (K = null == a ? void 0 : a.session_state) ? void 0 : K.answerthink_is_finished) ? (n(), s("div", ts, [v(k(ue))])) : r("", !0)])) : r("", !0), r("", !0), t("div", ns, m(o.$t("pages.agents.thinking_process")), 1)], 8, es), (null == (V = a.session_state) ? void 0 : V.answerthink_expanded) ? (n(), s("div", os, [v(F, {
                            source: ((null == (H = a.session_state) ? void 0 : H.answerthink) ? nn(null == (Z = a.session_state) ? void 0 : Z.answerthink) : "") + (a.thinking && (null == (Y = a.session_state) ? void 0 : Y.answerthink_is_started) && !(null == (ee = a.session_state) ? void 0 : ee.answerthink_is_finished) ? ht : ""),
                            is_thinking: a.thinking
                        }, null, 8, ["source", "is_thinking"])])) : (n(), s("div", is, [t("div", null, [A(m(null == (te = null == a ? void 0 : a.session_state) ? void 0 : te.answerthink) + " ", 1), a.thinking && !(null == (ne = null == a ? void 0 : a.session_state) ? void 0 : ne.answerthink_is_finished) ? (n(), s("span", as, "█")) : r("", !0)])]))])], 2)) : r("", !0), a.thinking || a.content && "string" == typeof a.content || (null == (oe = null == a ? void 0 : a.session_state) ? void 0 : oe.answer) || "interactive_deep_dive_search" == (null == (ie = null == a ? void 0 : a.session_state) ? void 0 : ie.render_template) ? (n(), s("div", {
                            key: 2,
                            class: p(["bubble", {
                                thinking: a.thinking
                            }]),
                            "message-content-id": l,
                            onPaste: e => {
                                Yt(e)
                            },
                            onBlur: () => {
                                o.e
                            },
                            onKeydown: e => {
                                en(e, a, l)
                            }
                        }, [t("div", rs, [t("div", ds, ["assistant" == a.role ? (n(), s(d, {
                            key: 0
                        }, ["interactive_deep_dive_search" == (null == (ae = null == a ? void 0 : a.session_state) ? void 0 : ae.render_template) ? (n(), s(d, {
                            key: 0
                        }, [v(R, {
                            "search-query": null == (re = a.session_state) ? void 0 : re.search_query,
                            "external-stream-json-string": null == (pe = a.session_state) ? void 0 : pe.StreamJsonString,
                            "external-streaming-detail-answer": (null == (ve = a.session_state) ? void 0 : ve.detailAnswer) || (null == (he = null == a ? void 0 : a.session_state) ? void 0 : he.streaming_detail_answer),
                            "external-notify-realtime-answer": null == (ge = a.session_state) ? void 0 : ge.notify_realtime_answer,
                            "external-search-status-top-bar-data": null == (we = a.session_state) ? void 0 : we.search_status_top_bar_data,
                            "external-search-source-top-bar-data": null == (ke = a.session_state) ? void 0 : ke.search_source_top_bar_data,
                            "external-api-response": a.content,
                            "external-streaming-markmap": null == (ye = a.session_state) ? void 0 : ye.streaming_markmap,
                            "external-answer-divider": null == (xe = a.session_state) ? void 0 : xe.answer_divider,
                            "show-search-status-top-bar": !1,
                            "show-search-source-top-bar": !1,
                            "show-followup-question": !0,
                            "show-try-moa": (1 == (null == (Ce = null == (be = a.session_state) ? void 0 : be.models) ? void 0 : Ce.length) || !(null == (Se = a.session_state) ? void 0 : Se.models)) && a == e[e.length - 1],
                            "show-loading-icon": !1,
                            "show-thinking": k(me),
                            onShowSidebar: Be,
                            onTriggerAction: Mt,
                            onTryMoa: e => Wt(a),
                            onFinishLoading: Pt
                        }, null, 8, ["search-query", "external-stream-json-string", "external-streaming-detail-answer", "external-notify-realtime-answer", "external-search-status-top-bar-data", "external-search-source-top-bar-data", "external-api-response", "external-streaming-markmap", "external-answer-divider", "show-try-moa", "show-thinking", "onTryMoa"]), v(F, {
                            source: a.content ? "" : (k(me) || (null == (Me = a.session_state) ? void 0 : Me.detailAnswer) || (null == (Ee = null == a ? void 0 : a.session_state) ? void 0 : Ee.streaming_detail_answer) ? "" : gt) + (k(me) ? "" : ht),
                            is_thinking: a.thinking
                        }, null, 8, ["source", "is_thinking"])], 64)) : (n(), _(F, {
                            key: 1,
                            source: (a.content ? nn(a.content) : (null == (Te = null == a ? void 0 : a.session_state) ? void 0 : Te.answer) || (null == (Ie = null == a ? void 0 : a.session_state) ? void 0 : Ie.detailAnswer) || gt) + (a.thinking ? ht : ""),
                            is_thinking: a.thinking
                        }, null, 8, ["source", "is_thinking"]))], 64)) : (n(), s("pre", us, [t("code", null, m(a.content), 1)]))])]), "assistant" != a.role || a.thinking || a.is_prompt || "interactive_deep_dive_search" == (null == (Re = a.session_state) ? void 0 : Re.render_template) ? r("", !0) : (n(), s("div", cs, [t("div", {
                            class: "button",
                            onClick: e => (e => {
                                if (e.session_state && "interactive_deep_dive_search" == e.session_state.render_template) try {
                                    const s = JSON.parse(e.content),
                                        t = s.detailAnswer || s.answer;
                                    navigator.clipboard.writeText(t), de.success(ce("pages.channel.copied"))
                                } catch (s) {
                                    C.log("copyContent error", s)
                                } else e.content && (navigator.clipboard.writeText(e.content), de.success(ce("pages.channel.copied")))
                            })(a)
                        }, [t("div", vs, [v(k(N))]), t("div", _s, m(o.$t("pages.channel.copy")), 1)], 8, ps)]))], 42, ls)) : r("", !0), a.content && Array.isArray(a.content) ? (n(!0), s(d, {
                            key: 3
                        }, u(a.content, (e => {
                            var o, i, l, d, u, c, v, h;
                            return n(), s("div", {
                                class: p(["bubble", {
                                    thinking: a.thinking,
                                    image_url: "image_url" === e.type,
                                    private_file: "private_file" === e.type
                                }])
                            }, [t("div", ms, [t("div", hs, ["text" === e.type ? (n(), s("div", gs, m(null == e ? void 0 : e.text), 1)) : r("", !0), "image_url" === e.type ? (n(), s("div", ws, [t("img", {
                                src: null == (o = null == e ? void 0 : e.image_url) ? void 0 : o.url
                            }, null, 8, fs)])) : r("", !0), "private_file" === e.type ? (n(), s("div", ks, [t("div", ys, [t("div", xs, ["pdf" == (null == (i = null == e ? void 0 : e.private_file) ? void 0 : i.ext) ? (n(), _(k(G), {
                                key: 0
                            })) : "doc" == (null == (l = null == e ? void 0 : e.private_file) ? void 0 : l.ext) || "docx" == (null == (d = null == e ? void 0 : e.private_file) ? void 0 : d.ext) ? (n(), _(k(J), {
                                key: 1
                            })) : "xls" == (null == (u = null == e ? void 0 : e.private_file) ? void 0 : u.ext) || "xlsx" == (null == (c = null == e ? void 0 : e.private_file) ? void 0 : c.ext) ? (n(), _(k(X), {
                                key: 2
                            })) : (n(), _(k(Q), {
                                key: 3
                            }))]), t("div", bs, [t("div", Cs, m(null == (v = null == e ? void 0 : e.private_file) ? void 0 : v.name), 1), t("div", Ss, m(k(tn)(null == (h = null == e ? void 0 : e.private_file) ? void 0 : h.size)), 1)])])])) : r("", !0)])])], 2)
                        })), 256)) : r("", !0), k(_e) || "interactive_deep_dive_search" == (null == (Le = a.session_state) ? void 0 : Le.render_template) || a !== e[e.length - 1] || "assistant" != a.role || !a.content || a.is_aborted || a.should_retry || a.error || a.is_prompt || (null == (Fe = a.session_state) ? void 0 : Fe.context_length_exceeded) || (null == (qe = a.session_state) ? void 0 : qe.consume_usage_quota_exceeded) || a.session_state && a.session_state.models && 1 != a.session_state.models.length ? r("", !0) : (n(), s("div", js, [i[16] || (i[16] = t("div", {
                            class: "left"
                        }, "This answer isn't good enough?", -1)), t("div", Ms, [t("div", {
                            class: "button",
                            onClick: () => {
                                Wt(a)
                            }
                        }, " Try Mixture-of-Agents ", 8, Ps)])])), a === e[e.length - 1] && "assistant" == a.role && (null == (Ks = a.session_state) ? void 0 : Ks.context_length_exceeded) ? (n(), s("div", As, [i[17] || (i[17] = t("div", {
                            class: "left"
                        }, " Context Length Exceeded, Please open a new session ", -1)), t("div", Es, [t("div", {
                            class: "button",
                            onClick: i[0] || (i[0] = () => {
                                le("requestCreateNewSession")
                            })
                        }, " Create a new session ")])])) : r("", !0), a === e[e.length - 1] && "assistant" == a.role && a.should_retry ? (n(), s("div", Ts, [t("div", Bs, m(k(ce)("components.copilot.error-generating-response")), 1), t("div", $s, [t("div", {
                            class: "button",
                            onClick: i[1] || (i[1] = () => {
                                (async () => {
                                    if (!Ct.value) return;
                                    if (await Ct.value.retry({
                                            only_get_events: !0
                                        }), !Ct.value.getMessagesRef) return;
                                    const e = Ct.value.getMessagesRef().value;
                                    if (!e || 0 === e.length) return;
                                    const s = e[e.length - 1];
                                    s && 0 == s._all_events_count && s.should_retry && await Ct.value.retry({
                                        only_get_events: !1
                                    })
                                })()
                            })
                        }, m(k(ce)("components.copilot.retry")), 1)])])) : r("", !0), "user" == a.role && a.contentEditable ? (n(), s("div", Is, [E((n(), s("div", {
                            "user-message-content-id": l,
                            contenteditable: "true",
                            class: "outline-none text-[#232425] pb-[16px]",
                            onPaste: e => {
                                Yt(e)
                            },
                            onBlur: e => {},
                            onKeydown: e => {
                                en(e, a, l)
                            }
                        }, [t("pre", Ls, [t("code", null, m(a.content), 1)])], 40, Rs)), [
                            [Ae]
                        ]), t("div", Fs, [t("div", {
                            onClick: () => {
                                ((e, s) => {
                                    e.contentEditable = !1, "string" == typeof e.content && (e.content = "" + e.content, Ct.value.getMessagesRef().value[s].content = e.content);
                                    const t = document.querySelector(`[message-content-id="${s}"]`);
                                    t && (t.contentEditable = !1)
                                })(a, l)
                            },
                            class: "bg-[#F5F5F5] w-[66px] py-[6px] rounded-[60px] text-center justify-start text-zinc-600 text-base font-normal font-['Arial'] leading-normal cursor-pointer hover:bg-[#E5E5E5]"
                        }, " Cancel ", 8, qs), t("div", {
                            onClick: () => {
                                sn(a, l)
                            },
                            class: "bg-[#232425] w-[66px] py-[6px] rounded-[60px] text-center justify-start text-white text-base font-normal font-['Arial'] leading-normal cursor-pointer hover:bg-[#1A1A1A]"
                        }, " Save ", 8, Ds)])])) : r("", !0)], 2), "user" == a.role ? (n(), s("div", {
                            key: 0,
                            class: p(["message-actions-user", {
                                "content-editable": a.contentEditable
                            }])
                        }, [a.contentEditable || Array.isArray(a.content) ? r("", !0) : (n(), s(d, {
                            key: 0
                        }, [t("div", {
                            class: "message-action",
                            onClick: e => (async e => {
                                e && (await navigator.clipboard.writeText(e), jt.value = !0, setTimeout((() => {
                                    jt.value = !1
                                }), 3e3))
                            })(a.content)
                        }, [t("div", Os, [jt.value ? (n(), _(k(U), {
                            key: 0,
                            class: "text-[#232425]"
                        })) : (n(), _(k(se), {
                            key: 1
                        }))])], 8, Us), t("div", {
                            class: "message-action",
                            onClick: () => {
                                ((e, s) => {
                                    const t = document.querySelector(`[message-content-id="${s}"]`);
                                    t && (t.contentEditable = !0, t.focus())
                                })(0, l), a.contentEditable = !0
                            }
                        }, [t("div", zs, [v(k(O))])], 8, Ws)], 64))], 2)) : r("", !0)], 2)], 64)) : r("", !0)], 64)
                    })), 128))])) : r("", !0)], 512)) : r("", !0)])),
                    _: 1
                }, 8, ["copilotType", "extra_data", "noAutoScroll"])], 512), ke.value ? (n(), _(K, {
                    key: 0,
                    speed: 5,
                    class: "realtime-cover-fixed"
                })) : r("", !0), k(ve) ? r("", !0) : (n(), s("div", Ks, [(null == (a = Ct.value) ? void 0 : a.toBottomVisible) && !ke.value ? (n(), s("div", {
                    key: 0,
                    class: "to-bottom-icon",
                    onClick: i[3] || (i[3] = (...e) => Ct.value.toBottom && Ct.value.toBottom(...e))
                }, [v(k(Y))])) : r("", !0), t("div", Vs, [v(w, null, {
                    default: M((() => {
                        var e, o;
                        return [k(me) || ke.value ? r("", !0) : (n(), s("div", Hs, [t("div", {
                            class: p(["upload-attachments", {
                                disabled: "o1-preview" == Ie.value
                            }])
                        }, [t("div", {
                            class: "icon",
                            onClick: i[4] || (i[4] = () => {
                                "o1-preview" != Ie.value ? Ot() : k(de).info("O1 Preview does not support file upload.")
                            })
                        }, [v(k(Z))])], 2), t("div", Ns, [v(k(ae), {
                            trigger: "click",
                            "show-arrow": !1,
                            placement: "top-start",
                            class: "models-popover",
                            style: {
                                padding: "0",
                                "border-radius": "12px",
                                "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                            },
                            "onUpdate:show": i[5] || (i[5] = e => {
                                wt.value = e
                            })
                        }, {
                            trigger: M((() => {
                                var e, o, i, a;
                                return [t("div", Zs, [t("div", Gs, [t("div", Js, ["object" == typeof(null == (e = k($e).find((e => e.name == Ie.value || e.search_model_name == Ie.value))) ? void 0 : e.icon) ? (n(), _(T(null == (o = k($e).find((e => e.name == Ie.value || e.search_model_name == Ie.value))) ? void 0 : o.icon), {
                                    key: 0
                                })) : (n(), s("img", {
                                    key: 1,
                                    src: null == (i = k($e).find((e => e.name == Ie.value || e.search_model_name == Ie.value))) ? void 0 : i.icon
                                }, null, 8, Qs))]), t("div", Xs, m(null == (a = k($e).find((e => e.name == Ie.value || e.search_model_name == Ie.value))) ? void 0 : a.label), 1)]), t("div", {
                                    class: p(["icon", {
                                        active: wt.value
                                    }])
                                }, [v(k(W))], 2)])]
                            })),
                            default: M((() => [t("div", Ys, [(n(!0), s(d, null, u(k($e).filter((e => !e.hidden)), (e => (n(), s(d, null, [t("div", {
                                class: p(["model", {
                                    disabled: "o1-preview" == e.name && zt()
                                }]),
                                onClick: () => {
                                    "o1-preview" == e.name && zt() ? k(de).info("O1 model does not support attachments.") : Ie.value = e.name
                                }
                            }, [t("div", st, [t("div", tt, [t("div", nt, ["object" == typeof e.icon ? (n(), _(T(e.icon), {
                                key: 0
                            })) : (n(), s("img", {
                                key: 1,
                                src: e.icon
                            }, null, 8, ot))]), t("div", it, m(e.label), 1)]), t("div", at, [t("input", {
                                type: "radio",
                                name: "model",
                                checked: Ie.value == e.name || Ie.value == e.search_model_name
                            }, null, 8, lt)])]), e.description ? (n(), s("div", rt, [t("div", dt, m(e.description), 1)])) : r("", !0)], 10, et), e.description ? (n(), s("div", ut)) : r("", !0)], 64)))), 256))])])),
                            _: 1
                        })]), t("div", {
                            class: p(["web-knowledge-toggle", {
                                disabled: null == (e = k($e).find((e => e.name == Ie.value || e.search_model_name == Ie.value))) ? void 0 : e.disable_search_web
                            }])
                        }, [v(k(ne), {
                            checked: Zt.value,
                            "onUpdate:checked": i[6] || (i[6] = e => Zt.value = e),
                            disabled: null == (o = k($e).find((e => e.name == Ie.value || e.search_model_name == Ie.value))) ? void 0 : o.disable_search_web
                        }, {
                            default: M((() => [A(m(k(ce)("components.general_chat_content.search_web")), 1)])),
                            _: 1
                        }, 8, ["checked", "disabled"])], 2)]))]
                    })),
                    _: 1
                }), ke.value ? r("", !0) : (n(), s("div", ct, [t("div", pt, [v(H, {
                    ref_key: "promptInputRef",
                    ref: It,
                    modelValue: $t.value,
                    "onUpdate:modelValue": i[7] || (i[7] = e => $t.value = e),
                    onSubmitPrompt: i[8] || (i[8] = (e, s, t) => {
                        Rt(e, s, t)
                    }),
                    onStopAsking: i[9] || (i[9] = () => {
                        var e;
                        null == (e = Ct.value) || e.cancelAskCopilot({
                            userAborted: !0
                        })
                    }),
                    showStopAsking: null == (l = Ct.value) ? void 0 : l.is_asking,
                    supportImages: null == (c = k($e).find((e => e.name == Ie.value))) ? void 0 : c.support_images,
                    onErrorMessage: i[10] || (i[10] = e => k(de).error(e)),
                    images: xt.value,
                    "onUpdate:images": i[11] || (i[11] = e => xt.value = e),
                    files: bt.value,
                    "onUpdate:files": i[12] || (i[12] = e => bt.value = e),
                    useSuggestion: !1,
                    placeholder: k(he),
                    styleClass: "moa"
                }, null, 8, ["modelValue", "showStopAsking", "supportImages", "images", "files", "placeholder"])], 512), Qt.value ? (n(), _(k(Ee), {
                    key: 0,
                    onClick: Et,
                    style: {
                        cursor: "pointer"
                    }
                })) : r("", !0)])), ke.value ? (n(), s("div", vt, [v(D, {
                    onSwitchToKeyboard: At,
                    onFunctionCall: Te,
                    onShowSearchSideBar: Be,
                    searchStatusTopBarData: null == (g = null == (h = Bt.value) ? void 0 : h.session_state) ? void 0 : g.search_status_top_bar_data
                }, null, 8, ["searchStatusTopBarData"])])) : r("", !0)])])), v(P, {
                    name: "slide-desktop"
                }, {
                    default: M((() => [Kt.value && !Vt.value ? (n(), _(z, {
                        key: 0,
                        "search-results": Ht.value.searchResults,
                        keywords: Ht.value.keywords,
                        "jump-to-url": Ht.value.jumpToUrl,
                        "is-mobile": Nt.value,
                        onCloseSearchSideBar: i[13] || (i[13] = e => Kt.value = !1),
                        class: "search-source-sidebar desktop"
                    }, null, 8, ["search-results", "keywords", "jump-to-url", "is-mobile"])) : r("", !0)])),
                    _: 1
                }), v(P, {
                    name: "slide"
                }, {
                    default: M((() => [Kt.value && Vt.value ? (n(), s("div", _t, [v(z, {
                        "search-results": Ht.value.searchResults,
                        keywords: Ht.value.keywords,
                        "is-mobile": Nt.value,
                        "jump-to-url": Ht.value.jumpToUrl,
                        onCloseSearchSideBar: i[14] || (i[14] = e => Kt.value = !1)
                    }, null, 8, ["search-results", "keywords", "is-mobile", "jump-to-url"])])) : r("", !0)])),
                    _: 1
                })])
            }
        }
    }, [
        ["__scopeId", "data-v-253db5b8"]
    ]),
    ft = {
        width: "14",
        height: "14",
        viewBox: "0 0 14 14",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const kt = {
        render: function(e, t) {
            return n(), s("svg", ft, t[0] || (t[0] = [g('<g id="Group 1171275821"><g id="Group 1171275820"><circle id="Ellipse 1949" cx="7" cy="7" r="4.5" stroke="currentColor"></circle><path id="Ellipse 1950" d="M8.77202 7C8.77202 8.32614 8.52667 9.50252 8.15117 10.3286C7.75515 11.1999 7.31025 11.5 6.99929 11.5C6.68833 11.5 6.24343 11.1999 5.84741 10.3286C5.47191 9.50252 5.22656 8.32614 5.22656 7C5.22656 5.67386 5.47191 4.49748 5.84741 3.67137C6.24343 2.80012 6.68833 2.5 6.99929 2.5C7.31025 2.5 7.75515 2.80012 8.15117 3.67137C8.52667 4.49748 8.77202 5.67386 8.77202 7Z" stroke="currentColor"></path><line id="Line 47" x1="2.9082" y1="5.13281" x2="11.09" y2="5.13281" stroke="currentColor"></line><line id="Line 48" x1="2.9082" y1="8.77344" x2="11.09" y2="8.77344" stroke="currentColor"></line></g></g>', 1)]))
        }
    },
    yt = {
        width: "14",
        height: "14",
        viewBox: "0 0 14 14",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const xt = {
        render: function(e, o) {
            return n(), s("svg", yt, o[0] || (o[0] = [t("g", {
                id: "Frame"
            }, [t("path", {
                id: "Vector",
                d: "M10.5556 6.5H3.44444C3.19898 6.5 3 6.70351 3 6.95455V11.0455C3 11.2965 3.19898 11.5 3.44444 11.5H10.5556C10.801 11.5 11 11.2965 11 11.0455V6.95455C11 6.70351 10.801 6.5 10.5556 6.5Z",
                stroke: "currentColor",
                "stroke-linejoin": "round"
            }), t("path", {
                id: "Vector_2",
                d: "M5 6.5V4.72222C5 3.49492 5.89544 2.5 7 2.5C8.10456 2.5 9 3.49492 9 4.72222V6.5",
                stroke: "currentColor",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }), t("path", {
                id: "Vector_3",
                d: "M7 8.5V9.5",
                stroke: "currentColor",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            })], -1)]))
        }
    },
    bt = {
        name: "PermissionDropdown",
        components: {
            TriangleIconSvg: le,
            PublicIcon: kt,
            PrivateIcon: xt,
            SelectedIconSvg: re,
            NPopover: ae
        },
        props: {
            permission: {
                type: String,
                required: !0
            },
            styleClass: {
                type: String,
                default: "creative"
            },
            needPlusPlan: {
                type: Boolean,
                default: !1
            },
            disabled: {
                type: Boolean,
                default: !1
            },
            options: {
                type: Array,
                default: []
            }
        },
        emits: ["update:permission"],
        setup(e, {
            emit: s
        }) {
            const {
                disabled: t,
                needPlusPlan: n,
                styleClass: o
            } = a(e), {
                t: r
            } = w(), d = j("currentUser"), u = () => {
                v.value = !1
            };
            x((() => {
                document.body.addEventListener("click", u)
            })), b((() => {
                document.body.removeEventListener("click", u)
            }));
            let c = [{
                label: r("components.permission_dropdown.public_creative"),
                desc: r("components.permission_dropdown.public_creative_desc"),
                icon: kt,
                value: "public"
            }, {
                label: r("components.permission_dropdown.private_creative"),
                desc: r("components.permission_dropdown.private_creative_desc"),
                icon: xt,
                value: "private"
            }];
            "default" == e.styleClass && (c[0].label = r("components.permission_dropdown.public"), c[1].label = r("components.permission_dropdown.private")), e.options && 2 == e.options.length && (c[0].label = e.options[0].label, c[1].label = e.options[1].label, c[0].desc = e.options[0].desc, c[1].desc = e.options[1].desc);
            const p = i(e.permission);
            l((() => p.value), (e => {
                s("update:permission", e)
            }));
            const v = i(!1);
            return {
                utils: C,
                currentUser: d,
                needPlusPlan: n,
                styleClass: o,
                disabled: t,
                options: c,
                permission: p,
                permissionShowDropDownList: v
            }
        }
    },
    Ct = {
        class: "icon"
    },
    St = {
        class: "text"
    },
    jt = {
        class: "dropDownList"
    },
    Mt = ["onClick"],
    Pt = {
        class: "wrapper"
    },
    At = {
        class: "name"
    },
    Et = {
        class: "icon"
    },
    Tt = {
        class: "text"
    },
    Bt = {
        key: 0,
        class: "plan"
    },
    $t = {
        class: "desc"
    },
    It = {
        key: 0,
        class: "selected"
    };
const Rt = o(bt, [
        ["render", function(e, o, i, a, l, g) {
            const w = c("TriangleIconSvg"),
                f = c("SelectedIconSvg"),
                k = c("n-popover");
            return n(), _(k, {
                trigger: "manual",
                show: a.permissionShowDropDownList,
                placement: "default" == a.styleClass ? "bottom-end" : "bottom",
                "show-arrow": !1,
                disabled: a.disabled,
                style: {
                    padding: "0"
                },
                class: "permission-dropdown"
            }, {
                trigger: M((() => {
                    var e, s;
                    return [t("div", {
                        class: "dropdown",
                        onClick: o[0] || (o[0] = h((() => {
                            a.disabled || (a.permissionShowDropDownList = !a.permissionShowDropDownList)
                        }), ["stop"]))
                    }, [t("div", Ct, [(n(), _(T(null == (e = a.options.find((e => e.value == a.permission))) ? void 0 : e.icon)))]), t("div", St, m(null == (s = a.options.find((e => e.value == a.permission))) ? void 0 : s.label), 1), t("div", {
                        class: p(["triangle", a.permissionShowDropDownList ? "reverse" : ""])
                    }, [v(w)], 2)])]
                })),
                default: M((() => [t("ul", jt, [(n(!0), s(d, null, u(a.options, (e => (n(), s("li", {
                    key: e.value,
                    onClick: h((s => {
                        "private" == e.value && a.needPlusPlan && "free" == a.currentUser.plan ? a.utils.windowopen("/pricing") : (a.permission = e.value, a.permissionShowDropDownList = !1)
                    }), ["stop", "prevent"])
                }, [t("div", Pt, [t("div", At, [t("div", Et, [(n(), _(T(e.icon)))]), t("div", Tt, [t("span", null, m(e.label), 1), "private" == e.value && a.needPlusPlan && "free" == a.currentUser.plan ? (n(), s("div", Bt, " Plus ")) : r("", !0)])]), t("div", $t, m(e.desc), 1)]), a.permission == e.value ? (n(), s("div", It, [v(f)])) : r("", !0)], 8, Mt)))), 128))])])),
                _: 1
            }, 8, ["show", "placement", "disabled"])
        }],
        ["__scopeId", "data-v-5e751262"]
    ]),
    Lt = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Ft = {
    render: function(e, o) {
        return n(), s("svg", Lt, o[0] || (o[0] = [t("path", {
            d: "M2.5 12C2.5 7.52166 2.5 5.28249 3.89124 3.89124C5.28249 2.5 7.52166 2.5 12 2.5C16.4783 2.5 18.7175 2.5 20.1088 3.89124C21.5 5.28249 21.5 7.52166 21.5 12C21.5 16.4783 21.5 18.7175 20.1088 20.1088C18.7175 21.5 16.4783 21.5 12 21.5C7.52166 21.5 5.28249 21.5 3.89124 20.1088C2.5 18.7175 2.5 16.4783 2.5 12Z",
            stroke: "currentColor",
            "stroke-width": "1.5"
        }, null, -1), t("path", {
            d: "M16.5 9C17.3284 9 18 8.32843 18 7.5C18 6.67157 17.3284 6 16.5 6C15.6716 6 15 6.67157 15 7.5C15 8.32843 15.6716 9 16.5 9Z",
            stroke: "currentColor",
            "stroke-width": "1.5"
        }, null, -1), t("path", {
            d: "M16 22C15.3805 19.7749 13.9345 17.7821 11.8765 16.3342C9.65761 14.7729 6.87163 13.9466 4.01569 14.0027C3.67658 14.0019 3.33776 14.0127 3 14.0351",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linejoin": "round"
        }, null, -1), t("path", {
            d: "M13 18C14.7015 16.6733 16.5345 15.9928 18.3862 16.0001C19.4362 15.999 20.4812 16.2216 21.5 16.6617",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linejoin": "round"
        }, null, -1)]))
    }
};
export {
    ue as C, Ft as I, je as M, Ee as R, wt as _, Pe as a, Re as b, Rt as c
};