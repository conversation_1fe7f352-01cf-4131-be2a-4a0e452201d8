a[data-v-4cb0feb7],
abbr[data-v-4cb0feb7],
acronym[data-v-4cb0feb7],
address[data-v-4cb0feb7],
applet[data-v-4cb0feb7],
article[data-v-4cb0feb7],
aside[data-v-4cb0feb7],
audio[data-v-4cb0feb7],
b[data-v-4cb0feb7],
big[data-v-4cb0feb7],
blockquote[data-v-4cb0feb7],
body[data-v-4cb0feb7],
canvas[data-v-4cb0feb7],
caption[data-v-4cb0feb7],
center[data-v-4cb0feb7],
cite[data-v-4cb0feb7],
code[data-v-4cb0feb7],
dd[data-v-4cb0feb7],
del[data-v-4cb0feb7],
details[data-v-4cb0feb7],
dfn[data-v-4cb0feb7],
div[data-v-4cb0feb7],
dl[data-v-4cb0feb7],
dt[data-v-4cb0feb7],
em[data-v-4cb0feb7],
embed[data-v-4cb0feb7],
fieldset[data-v-4cb0feb7],
figcaption[data-v-4cb0feb7],
figure[data-v-4cb0feb7],
footer[data-v-4cb0feb7],
form[data-v-4cb0feb7],
h1[data-v-4cb0feb7],
h2[data-v-4cb0feb7],
h3[data-v-4cb0feb7],
h4[data-v-4cb0feb7],
h5[data-v-4cb0feb7],
h6[data-v-4cb0feb7],
header[data-v-4cb0feb7],
hgroup[data-v-4cb0feb7],
html[data-v-4cb0feb7],
i[data-v-4cb0feb7],
iframe[data-v-4cb0feb7],
img[data-v-4cb0feb7],
ins[data-v-4cb0feb7],
kbd[data-v-4cb0feb7],
label[data-v-4cb0feb7],
legend[data-v-4cb0feb7],
li[data-v-4cb0feb7],
mark[data-v-4cb0feb7],
menu[data-v-4cb0feb7],
nav[data-v-4cb0feb7],
object[data-v-4cb0feb7],
ol[data-v-4cb0feb7],
output[data-v-4cb0feb7],
p[data-v-4cb0feb7],
pre[data-v-4cb0feb7],
q[data-v-4cb0feb7],
ruby[data-v-4cb0feb7],
s[data-v-4cb0feb7],
samp[data-v-4cb0feb7],
section[data-v-4cb0feb7],
small[data-v-4cb0feb7],
span[data-v-4cb0feb7],
strike[data-v-4cb0feb7],
strong[data-v-4cb0feb7],
sub[data-v-4cb0feb7],
summary[data-v-4cb0feb7],
sup[data-v-4cb0feb7],
table[data-v-4cb0feb7],
tbody[data-v-4cb0feb7],
td[data-v-4cb0feb7],
tfoot[data-v-4cb0feb7],
th[data-v-4cb0feb7],
thead[data-v-4cb0feb7],
time[data-v-4cb0feb7],
tr[data-v-4cb0feb7],
tt[data-v-4cb0feb7],
u[data-v-4cb0feb7],
ul[data-v-4cb0feb7],
var[data-v-4cb0feb7],
video[data-v-4cb0feb7] {
    border: 0;
    box-sizing: border-box;
    font-size: 100%;
    margin: 0;
    padding: 0;
    vertical-align: baseline
}

[data-v-4cb0feb7]:after,
[data-v-4cb0feb7]:before {
    box-sizing: border-box
}

article[data-v-4cb0feb7],
aside[data-v-4cb0feb7],
details[data-v-4cb0feb7],
figcaption[data-v-4cb0feb7],
figure[data-v-4cb0feb7],
footer[data-v-4cb0feb7],
header[data-v-4cb0feb7],
hgroup[data-v-4cb0feb7],
menu[data-v-4cb0feb7],
nav[data-v-4cb0feb7],
section[data-v-4cb0feb7] {
    display: block
}

ol[data-v-4cb0feb7],
ul[data-v-4cb0feb7] {
    list-style: none
}

blockquote[data-v-4cb0feb7],
q[data-v-4cb0feb7] {
    quotes: none
}

blockquote[data-v-4cb0feb7]:after,
blockquote[data-v-4cb0feb7]:before,
q[data-v-4cb0feb7]:after,
q[data-v-4cb0feb7]:before {
    content: ""
}

table[data-v-4cb0feb7] {
    border-collapse: collapse;
    border-spacing: 0
}

a[data-v-4cb0feb7] {
    color: #232425;
    text-decoration: none
}

img[data-v-4cb0feb7] {
    border-style: none;
    vertical-align: middle
}

hr[data-v-4cb0feb7] {
    box-sizing: content-box;
    height: 0;
    overflow: visible
}

button[data-v-4cb0feb7],
input[data-v-4cb0feb7],
optgroup[data-v-4cb0feb7],
select[data-v-4cb0feb7],
textarea[data-v-4cb0feb7] {
    color: inherit
}

button[data-v-4cb0feb7],
input[data-v-4cb0feb7] {
    overflow: visible
}

button[data-v-4cb0feb7],
select[data-v-4cb0feb7] {
    text-transform: none
}

textarea[data-v-4cb0feb7] {
    overflow: auto;
    resize: vertical
}

[role=button][data-v-4cb0feb7],
a[data-v-4cb0feb7],
area[data-v-4cb0feb7],
button[data-v-4cb0feb7],
input[data-v-4cb0feb7]:not([type=range]),
label[data-v-4cb0feb7],
select[data-v-4cb0feb7],
summary[data-v-4cb0feb7],
textarea[data-v-4cb0feb7] {
    touch-action: manipulation
}

[data-v-4cb0feb7]::-webkit-scrollbar {
    background-color: transparent;
    height: 5px;
    width: 5px
}

[data-v-4cb0feb7]::-webkit-scrollbar-thumb {
    background-color: #e1e1e1;
    border-radius: 3px
}

li[data-v-4cb0feb7]::marker {
    color: var(--body-color)
}

@font-face {
    font-display: swap;
    font-family: lato;
    src: url(./Lato-Black.Duv_FuDB.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Lato Bold;
    src: url(./Lato-Bold.CS9L97ds.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Gelasio;
    src: url(./Gelasio-VariableFont_wght.DuIzQp6e.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Nunito Semi Bold;
    src: url(./Nunito-SemiBold.CF04qjHE.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Poppins Light;
    src: url(./Poppins-Light.BDLU8qmu.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Petrona Bold;
    src: url(./Petrona-Bold.B06SIiUm.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Inter;
    src: url(./Inter_24pt-Regular.DoVccHXZ.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Lora;
    src: url(./Lora-Regular.CREeHv_u.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Source Sans Pro;
    src: url(./SourceSans3-Regular.BQiNOCsP.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Barlow;
    src: url(./Barlow-Regular.Bre0Y4Y4.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Barlow Medium;
    src: url(./Barlow-Medium.DhDrUmMX.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Overpass Bold;
    src: url(./Overpass-Bold.BUzcjETU.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Overpass;
    src: url(./Overpass-Regular.T_2pbQkk.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Montserrat;
    src: url(./Montserrat-Regular.EjbTADAg.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Heebo Light;
    src: url(./Heebo-Light.oDItNtXb.woff2) format("woff2")
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 100;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0460-052f, u+1c80-1c8a, u+20b4, u+2de0-2dff, u+a640-a69f, u+fe2e-fe2f
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 100;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0301, u+0400-045f, u+0490-0491, u+04b0-04b1, u+2116
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 100;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+1f??
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 100;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0370-0377, u+037a-037f, u+0384-038a, u+038c, u+038e-03a1, u+03a3-03ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 100;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1, u+01af-01b0, u+0300-0301, u+0303-0304, u+0308-0309, u+0323, u+0329, u+1ea0-1ef9, u+20ab
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 100;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0100-02ba, u+02bd-02c5, u+02c7-02cc, u+02ce-02d7, u+02dd-02ff, u+0304, u+0308, u+0329, u+1d00-1dbf, u+1e00-1e9f, u+1ef2-1eff, u+2020, u+20a0-20ab, u+20ad-20c0, u+2113, u+2c60-2c7f, u+a720-a7ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 100;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2) format("woff2");
    unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+0304, u+0308, u+0329, u+2000-206f, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 200;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0460-052f, u+1c80-1c8a, u+20b4, u+2de0-2dff, u+a640-a69f, u+fe2e-fe2f
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 200;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0301, u+0400-045f, u+0490-0491, u+04b0-04b1, u+2116
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 200;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+1f??
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 200;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0370-0377, u+037a-037f, u+0384-038a, u+038c, u+038e-03a1, u+03a3-03ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 200;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1, u+01af-01b0, u+0300-0301, u+0303-0304, u+0308-0309, u+0323, u+0329, u+1ea0-1ef9, u+20ab
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 200;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0100-02ba, u+02bd-02c5, u+02c7-02cc, u+02ce-02d7, u+02dd-02ff, u+0304, u+0308, u+0329, u+1d00-1dbf, u+1e00-1e9f, u+1ef2-1eff, u+2020, u+20a0-20ab, u+20ad-20c0, u+2113, u+2c60-2c7f, u+a720-a7ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 200;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2) format("woff2");
    unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+0304, u+0308, u+0329, u+2000-206f, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 300;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0460-052f, u+1c80-1c8a, u+20b4, u+2de0-2dff, u+a640-a69f, u+fe2e-fe2f
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 300;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0301, u+0400-045f, u+0490-0491, u+04b0-04b1, u+2116
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 300;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+1f??
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 300;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0370-0377, u+037a-037f, u+0384-038a, u+038c, u+038e-03a1, u+03a3-03ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 300;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1, u+01af-01b0, u+0300-0301, u+0303-0304, u+0308-0309, u+0323, u+0329, u+1ea0-1ef9, u+20ab
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 300;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0100-02ba, u+02bd-02c5, u+02c7-02cc, u+02ce-02d7, u+02dd-02ff, u+0304, u+0308, u+0329, u+1d00-1dbf, u+1e00-1e9f, u+1ef2-1eff, u+2020, u+20a0-20ab, u+20ad-20c0, u+2113, u+2c60-2c7f, u+a720-a7ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 300;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2) format("woff2");
    unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+0304, u+0308, u+0329, u+2000-206f, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0460-052f, u+1c80-1c8a, u+20b4, u+2de0-2dff, u+a640-a69f, u+fe2e-fe2f
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0301, u+0400-045f, u+0490-0491, u+04b0-04b1, u+2116
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+1f??
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0370-0377, u+037a-037f, u+0384-038a, u+038c, u+038e-03a1, u+03a3-03ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1, u+01af-01b0, u+0300-0301, u+0303-0304, u+0308-0309, u+0323, u+0329, u+1ea0-1ef9, u+20ab
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0100-02ba, u+02bd-02c5, u+02c7-02cc, u+02ce-02d7, u+02dd-02ff, u+0304, u+0308, u+0329, u+1d00-1dbf, u+1e00-1e9f, u+1ef2-1eff, u+2020, u+20a0-20ab, u+20ad-20c0, u+2113, u+2c60-2c7f, u+a720-a7ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2) format("woff2");
    unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+0304, u+0308, u+0329, u+2000-206f, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 500;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0460-052f, u+1c80-1c8a, u+20b4, u+2de0-2dff, u+a640-a69f, u+fe2e-fe2f
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 500;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0301, u+0400-045f, u+0490-0491, u+04b0-04b1, u+2116
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 500;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+1f??
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 500;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0370-0377, u+037a-037f, u+0384-038a, u+038c, u+038e-03a1, u+03a3-03ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 500;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1, u+01af-01b0, u+0300-0301, u+0303-0304, u+0308-0309, u+0323, u+0329, u+1ea0-1ef9, u+20ab
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 500;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0100-02ba, u+02bd-02c5, u+02c7-02cc, u+02ce-02d7, u+02dd-02ff, u+0304, u+0308, u+0329, u+1d00-1dbf, u+1e00-1e9f, u+1ef2-1eff, u+2020, u+20a0-20ab, u+20ad-20c0, u+2113, u+2c60-2c7f, u+a720-a7ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 500;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2) format("woff2");
    unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+0304, u+0308, u+0329, u+2000-206f, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 600;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0460-052f, u+1c80-1c8a, u+20b4, u+2de0-2dff, u+a640-a69f, u+fe2e-fe2f
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 600;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0301, u+0400-045f, u+0490-0491, u+04b0-04b1, u+2116
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 600;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+1f??
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 600;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0370-0377, u+037a-037f, u+0384-038a, u+038c, u+038e-03a1, u+03a3-03ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 600;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1, u+01af-01b0, u+0300-0301, u+0303-0304, u+0308-0309, u+0323, u+0329, u+1ea0-1ef9, u+20ab
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 600;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0100-02ba, u+02bd-02c5, u+02c7-02cc, u+02ce-02d7, u+02dd-02ff, u+0304, u+0308, u+0329, u+1d00-1dbf, u+1e00-1e9f, u+1ef2-1eff, u+2020, u+20a0-20ab, u+20ad-20c0, u+2113, u+2c60-2c7f, u+a720-a7ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 600;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2) format("woff2");
    unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+0304, u+0308, u+0329, u+2000-206f, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 700;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0460-052f, u+1c80-1c8a, u+20b4, u+2de0-2dff, u+a640-a69f, u+fe2e-fe2f
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 700;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0301, u+0400-045f, u+0490-0491, u+04b0-04b1, u+2116
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 700;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+1f??
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 700;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0370-0377, u+037a-037f, u+0384-038a, u+038c, u+038e-03a1, u+03a3-03ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 700;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1, u+01af-01b0, u+0300-0301, u+0303-0304, u+0308-0309, u+0323, u+0329, u+1ea0-1ef9, u+20ab
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 700;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0100-02ba, u+02bd-02c5, u+02c7-02cc, u+02ce-02d7, u+02dd-02ff, u+0304, u+0308, u+0329, u+1d00-1dbf, u+1e00-1e9f, u+1ef2-1eff, u+2020, u+20a0-20ab, u+20ad-20c0, u+2113, u+2c60-2c7f, u+a720-a7ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 700;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2) format("woff2");
    unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+0304, u+0308, u+0329, u+2000-206f, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 800;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0460-052f, u+1c80-1c8a, u+20b4, u+2de0-2dff, u+a640-a69f, u+fe2e-fe2f
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 800;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0301, u+0400-045f, u+0490-0491, u+04b0-04b1, u+2116
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 800;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+1f??
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 800;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0370-0377, u+037a-037f, u+0384-038a, u+038c, u+038e-03a1, u+03a3-03ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 800;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1, u+01af-01b0, u+0300-0301, u+0303-0304, u+0308-0309, u+0323, u+0329, u+1ea0-1ef9, u+20ab
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 800;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0100-02ba, u+02bd-02c5, u+02c7-02cc, u+02ce-02d7, u+02dd-02ff, u+0304, u+0308, u+0329, u+1d00-1dbf, u+1e00-1e9f, u+1ef2-1eff, u+2020, u+20a0-20ab, u+20ad-20c0, u+2113, u+2c60-2c7f, u+a720-a7ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 800;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2) format("woff2");
    unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+0304, u+0308, u+0329, u+2000-206f, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 900;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0460-052f, u+1c80-1c8a, u+20b4, u+2de0-2dff, u+a640-a69f, u+fe2e-fe2f
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 900;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0301, u+0400-045f, u+0490-0491, u+04b0-04b1, u+2116
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 900;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+1f??
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 900;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0370-0377, u+037a-037f, u+0384-038a, u+038c, u+038e-03a1, u+03a3-03ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 900;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1, u+01af-01b0, u+0300-0301, u+0303-0304, u+0308-0309, u+0323, u+0329, u+1ea0-1ef9, u+20ab
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 900;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2) format("woff2");
    unicode-range: u+0100-02ba, u+02bd-02c5, u+02c7-02cc, u+02ce-02d7, u+02dd-02ff, u+0304, u+0308, u+0329, u+1d00-1dbf, u+1e00-1e9f, u+1ef2-1eff, u+2020, u+20a0-20ab, u+20ad-20c0, u+2113, u+2c60-2c7f, u+a720-a7ff
}

@font-face {
    font-display: swap;
    font-family: Inter;
    font-style: normal;
    font-weight: 900;
    src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2) format("woff2");
    unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+0304, u+0308, u+0329, u+2000-206f, u+20ac, u+2122, u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd
}

.message[data-v-4cb0feb7] {
    max-width: 600px
}

.message+.message[data-v-4cb0feb7] {
    margin-top: 15px
}

.message-container[data-v-4cb0feb7] {
    align-items: center;
    background: #fff;
    border-radius: 2px;
    box-shadow: 0 1px 8px #00000026;
    display: flex;
    font-size: 13px;
    min-width: 50px;
    overflow: hidden;
    padding: 10px;
    pointer-events: all;
    position: relative
}

.message-container .icons[data-v-4cb0feb7] {
    align-items: center;
    display: flex;
    margin-right: 10px
}

.message-container .title[data-v-4cb0feb7] {
    font-size: 14px;
    font-weight: 700;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.message-container .content[data-v-4cb0feb7] {
    width: 100%
}

.message-container .description[data-v-4cb0feb7] {
    color: #232425;
    line-height: 1.5
}

.message-container .title+.description[data-v-4cb0feb7] {
    margin-top: 5px
}

.message-container .control[data-v-4cb0feb7] {
    height: 100%;
    margin-left: 10px;
    position: relative
}

.message-container .close-btn[data-v-4cb0feb7] {
    align-items: center;
    color: #666;
    cursor: pointer;
    display: flex;
    font-size: 15px
}

.message-container .close-btn[data-v-4cb0feb7]:hover {
    color: #232425
}

.message-fade-enter-active[data-v-4cb0feb7] {
    animation: message-fade-in-down-4cb0feb7 .3s
}

.message-fade-leave-active[data-v-4cb0feb7] {
    animation: message-fade-out-4cb0feb7 .3s
}

@keyframes message-fade-in-down-4cb0feb7 {
    0% {
        opacity: 0;
        transform: translateY(-20px)
    }
    to {
        opacity: 1;
        transform: translateY(0)
    }
}

@keyframes message-fade-out-4cb0feb7 {
    0% {
        margin-top: 0;
        opacity: 1
    }
    to {
        margin-top: -45px;
        opacity: 0
    }
}