import {
    d as C,
    b as e,
    o as i
} from "./Cf0SOiw0.js";
const n = {
    width: "25",
    height: "24",
    viewBox: "0 0 25 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const o = {
    render: function(o, t) {
        return i(), C("svg", n, t[0] || (t[0] = [e("g", {
            id: "Frame"
        }, [e("path", {
            id: "Union",
            "fill-rule": "evenodd",
            "clip-rule": "evenodd",
            d: "M19.3182 6.0535C19.5424 5.96063 19.8005 6.01197 19.9721 6.18357L21.5126 7.7241C21.747 7.95841 21.747 8.33831 21.5126 8.57263L19.9721 10.1132C19.8005 10.2848 19.5424 10.3361 19.3182 10.2432C19.094 10.1504 18.9479 9.93157 18.9479 9.6889V8.99823C17.1623 9.25865 15.4776 10.0244 14.0996 11.2186L13.3701 11.8508L14.2406 12.6769C15.5463 13.9158 17.1908 14.7185 18.9479 14.9945V14.311C18.9479 14.0683 19.094 13.8495 19.3182 13.7566C19.5424 13.6638 19.8005 13.7151 19.9721 13.8867L21.5126 15.4272C21.747 15.6615 21.747 16.0414 21.5126 16.2758L19.9721 17.8163C19.8005 17.9879 19.5424 18.0392 19.3182 17.9464C19.094 17.8535 18.9479 17.6347 18.9479 17.392V16.5102C16.8049 16.2234 14.7932 15.269 13.2081 13.765L12.2312 12.8379L10.7193 14.1482C8.8936 15.7305 6.55858 16.6015 4.14258 16.6015C3.72836 16.6015 3.39258 16.2657 3.39258 15.8515C3.39258 15.4373 3.72836 15.1015 4.14258 15.1015C6.1977 15.1015 8.18392 14.3606 9.73695 13.0146L11.1379 11.8005L10.9361 11.609C9.10229 9.86891 6.6707 8.89885 4.14273 8.89885C3.72852 8.89885 3.39273 8.56306 3.39273 8.14885C3.39273 7.73463 3.72852 7.39885 4.14273 7.39885C7.05492 7.39885 9.85608 8.51634 11.9686 10.5209L12.2768 10.8134L13.1173 10.085C14.7684 8.654 16.8001 7.75432 18.9479 7.48455V6.60783C18.9479 6.36515 19.094 6.14637 19.3182 6.0535Z",
            fill: "white"
        })], -1)]))
    }
};
export {
    o as R
};