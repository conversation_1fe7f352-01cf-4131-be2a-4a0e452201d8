import {
    r as e,
    s as r,
    z as i,
    ad as t
} from "./Cf0SOiw0.js";
const a = () => {
        const a = "price_config",
            u = {
                paymentRegion: e(""),
                testmode: e(""),
                liveOrTestMode: e("")
            },
            n = {
                priceValue: e("0"),
                priceCurrency: e(""),
                priceInterval: e("")
            },
            l = {
                plusPriceMonthOnly: e(""),
                plusPriceMonthOnlyValue: e(""),
                plusPriceWithYear: e(""),
                plusPriceWithYearValue: e(""),
                plusPriceYearTotalValue: e(""),
                plusPriceCurrency: e(""),
                plusPriceInterval: e("")
            },
            o = {
                proPriceMonthOnly: e(""),
                proPriceMonthOnlyValue: e(""),
                proPriceWithYear: e(""),
                proPriceWithYearValue: e(""),
                proPriceYearTotalValue: e(""),
                proPriceCurrency: e(""),
                proPriceInterval: e("")
            },
            c = {
                creditPrice: e(""),
                creditPriceValue: e(""),
                creditPriceCurrency: e(""),
                credit2xPrice: e(""),
                credit2xPriceValue: e(""),
                credit2xPriceCurrency: e("")
            },
            s = {
                teamPlusPriceMonthOnly: e(""),
                teamPlusPriceMonthOnlyValue: e(""),
                teamPlusPriceCurrency: e(""),
                teamPlusPriceInterval: e("")
            },
            p = {
                teamCreditPriceId: e(""),
                teamCreditPriceValue: e(""),
                teamCreditPriceCurrency: e("")
            },
            {
                t: P
            } = r(),
            d = e => {
                if ("accountRegion" in e && (u.paymentRegion.value = e.accountRegion), "isTestMode" in e && (u.testmode.value = e.isTestMode.toLowerCase(), "false" === u.testmode.value ? u.liveOrTestMode.value = "live_mode" : u.liveOrTestMode.value = "test_mode"), "plusPriceMonth" in e && (l.plusPriceMonthOnly.value = e.plusPriceMonth), "plusPriceMonthValue" in e && (l.plusPriceMonthOnlyValue.value = e.plusPriceMonthValue), "plusPriceYear" in e && (l.plusPriceWithYear.value = e.plusPriceYear), "plusPriceYearValue" in e) {
                    l.plusPriceYearTotalValue.value = e.plusPriceYearValue;
                    const r = parseFloat(l.plusPriceYearTotalValue.value);
                    l.plusPriceWithYearValue.value = (Math.floor((r + 1e-4) / 12 * 100) / 100).toFixed(2).toString()
                }
                if ("proPriceMonth" in e && (o.proPriceMonthOnly.value = e.proPriceMonth), "proPriceMonthValue" in e && (o.proPriceMonthOnlyValue.value = e.proPriceMonthValue), "proPriceYear" in e && (o.proPriceWithYear.value = e.proPriceYear), "proPriceYearValue" in e) {
                    o.proPriceYearTotalValue.value = e.proPriceYearValue;
                    const r = parseFloat(o.proPriceYearTotalValue.value);
                    o.proPriceWithYearValue.value = (Math.floor((r + 1e-4) / 12 * 100) / 100).toFixed(2).toString()
                }
                if ("creditPrice" in e && (c.creditPrice.value = e.creditPrice), "creditPriceValue" in e && (c.creditPriceValue.value = e.creditPriceValue), "creditPriceCurrency" in e && (c.creditPriceCurrency.value = e.creditPriceCurrency), "credit2xPrice" in e && (c.credit2xPrice.value = e.credit2xPrice), "credit2xPriceValue" in e && (c.credit2xPriceValue.value = e.credit2xPriceValue), "credit2xPriceCurrency" in e && (c.credit2xPriceCurrency.value = e.credit2xPriceCurrency), "teamPriceMonth" in e && (s.teamPlusPriceMonthOnly.value = e.teamPriceMonth), "teamPriceMonthValue" in e && (s.teamPlusPriceMonthOnlyValue.value = e.teamPriceMonthValue), "teamCreditPrice" in e && (p.teamCreditPriceId.value = e.teamCreditPrice), "teamCreditPriceValue" in e && (p.teamCreditPriceValue.value = e.teamCreditPriceValue), "plusPriceMonthCurrency" in e) {
                    const r = e.plusPriceMonthCurrency;
                    l.plusPriceCurrency.value = "usd" === r ? P("pages.pricing.price-usd") : r, n.priceCurrency.value = l.plusPriceCurrency.value, o.proPriceCurrency.value = l.plusPriceCurrency.value, c.creditPriceCurrency.value = l.plusPriceCurrency.value, c.credit2xPriceCurrency.value = l.plusPriceCurrency.value, s.teamPlusPriceCurrency.value = l.plusPriceCurrency.value, p.teamCreditPriceCurrency.value = l.plusPriceCurrency.value
                }
                l.plusPriceInterval.value = P("pages.pricing.price-month"), n.priceInterval.value = l.plusPriceInterval.value, o.proPriceInterval.value = l.plusPriceInterval.value, s.teamPlusPriceInterval.value = l.plusPriceInterval.value
            },
            v = async e => new Promise((r => setTimeout(r, e)));
        return {
            getPriceConfigObject: () => ({
                paymentRegion: u.paymentRegion,
                liveOrTestMode: u.liveOrTestMode,
                freePriceValue: n.priceValue,
                freePriceCurrency: n.priceCurrency,
                freePriceInterval: n.priceInterval,
                testmode: u.testmode,
                plusPriceMonthOnly: l.plusPriceMonthOnly,
                plusPriceMonthOnlyValue: l.plusPriceMonthOnlyValue,
                plusPriceWithYear: l.plusPriceWithYear,
                plusPriceWithYearValue: l.plusPriceWithYearValue,
                plusPriceYearTotalValue: l.plusPriceYearTotalValue,
                plusPriceCurrency: l.plusPriceCurrency,
                plusPriceInterval: l.plusPriceInterval,
                proPriceMonthOnly: o.proPriceMonthOnly,
                proPriceMonthOnlyValue: o.proPriceMonthOnlyValue,
                proPriceWithYear: o.proPriceWithYear,
                proPriceWithYearValue: o.proPriceWithYearValue,
                proPriceYearTotalValue: o.proPriceYearTotalValue,
                proPriceCurrency: o.proPriceCurrency,
                proPriceInterval: o.proPriceInterval,
                creditPrice: c.creditPrice,
                creditPriceValue: c.creditPriceValue,
                creditPriceCurrency: c.creditPriceCurrency,
                credit2xPrice: c.credit2xPrice,
                credit2xPriceValue: c.credit2xPriceValue,
                credit2xPriceCurrency: c.credit2xPriceCurrency,
                teamPlusPriceMonthOnly: s.teamPlusPriceMonthOnly,
                teamPlusPriceMonthOnlyValue: s.teamPlusPriceMonthOnlyValue,
                teamPlusPriceCurrency: s.teamPlusPriceCurrency,
                teamPlusPriceInterval: s.teamPlusPriceInterval,
                teamCreditPriceId: p.teamCreditPriceId,
                teamCreditPriceValue: p.teamCreditPriceValue,
                teamCreditPriceCurrency: p.teamCreditPriceCurrency
            }),
            fetchPriceConfig: async (r, i) => {
                let u = "/api/payment/price_config";
                "testmode" in r.query && (u += `?testmode=${r.query.testmode}`);
                const n = e(null);
                for (let e = 0; e < 3; e++) try {
                    let e = {
                        key: a
                    };
                    null !== i && (e.headers = i);
                    const {
                        data: r
                    } = await t(u, e, "$rleUgFOogJ");
                    n.value = r.value;
                    break
                } catch (l) {
                    await v(200)
                }
                return n.value
            },
            processPriceConfig: async () => {
                const {
                    data: e
                } = i(a);
                null !== e.value && d(e.value)
            },
            parseDataValue: d
        }
    },
    u = () => {
        const r = "current_subscription",
            a = {
                currentPaidMembershipType: e(""),
                webPaidMembershipType: e(""),
                orgMembershipType: e(""),
                orgRole: e(""),
                orgId: e(""),
                orgSubscriptionId: e(""),
                orgSubscriptionStart: e(0),
                orgSubscriptionEnd: e(0),
                orgSubscriptionQuantity: e(0),
                orgSubscriptionMemberTotal: e(0),
                orgCreditsPackTotal: e(0),
                orgCreditsPackClaimed: e(0),
                applePaidMembershipType: e(""),
                appleProductInterval: e(""),
                inviteCreditStartDate: e(""),
                inviteCreditEndDate: e(""),
                vipMembershipStatus: e("")
            },
            u = {
                currentSubscriptionId: e(""),
                showFreePlan: e(!1),
                showPlusPlan: e(!1),
                showProPlan: e(!1),
                showCreditPlan: e(!1),
                plusShowManageSubscription: e(!1),
                plusPlanStatus: e(""),
                plusButtonDisabled: e(!1),
                proShowManageSubscription: e(!1),
                proPlanStatus: e(""),
                proButtonDisabled: e(!1),
                debugInfo: e("")
            },
            n = (e, r, i, t, n) => {
                const l = "userEmail" in e ? e.userEmail : "",
                    o = "userPhone" in e ? e.userPhone : "",
                    c = "stripeAccountEmail" in e ? e.stripeAccountEmail : "",
                    s = "stripeAccountPhone" in e ? e.stripeAccountPhone : "",
                    p = "stripeAccountName" in e ? e.stripeAccountName : "",
                    P = "customerId" in e ? e.customerId : "",
                    d = "memberId" in e ? e.memberId : "",
                    v = "membershipType" in e ? String(e.membershipType) : "",
                    b = "membershipStatus" in e && "string" == typeof e.membershipStatus ? e.membershipStatus : "";
                a.webPaidMembershipType.value = b && ["active", "canceled_but_active"].includes(b) && v || "free", u.currentSubscriptionId.value = "subId" in e && "string" == typeof e.subId ? e.subId : "";
                const m = "orgMembershipType" in e && "string" == typeof e.orgMembershipType ? e.orgMembershipType : "",
                    h = "orgMembershipStatus" in e && "string" == typeof e.orgMembershipStatus ? e.orgMembershipStatus : "";
                a.orgMembershipType.value = h && ["active", "canceled_but_active"].includes(h) && m || "free", a.orgRole.value = "orgRole" in e && "string" == typeof e.orgRole ? e.orgRole : "", a.orgId.value = "orgId" in e && "string" == typeof e.orgId ? e.orgId : "", a.orgSubscriptionId.value = "orgSubscriptionId" in e && "string" == typeof e.orgSubscriptionId ? e.orgSubscriptionId : "", a.orgSubscriptionStart.value = "orgSubscriptionStart" in e && "number" == typeof e.orgSubscriptionStart ? e.orgSubscriptionStart : 0, a.orgSubscriptionEnd.value = "orgSubscriptionEnd" in e && "number" == typeof e.orgSubscriptionEnd ? e.orgSubscriptionEnd : 0, a.orgSubscriptionQuantity.value = "orgSubscriptionQuantity" in e && "number" == typeof e.orgSubscriptionQuantity ? e.orgSubscriptionQuantity : 0, a.orgSubscriptionMemberTotal.value = "orgSubscriptionMemberTotal" in e && "number" == typeof e.orgSubscriptionMemberTotal ? e.orgSubscriptionMemberTotal : 0, a.orgCreditsPackTotal.value = "orgCreditsPackTotal" in e && "number" == typeof e.orgCreditsPackTotal ? e.orgCreditsPackTotal : 0, a.orgCreditsPackClaimed.value = "orgCreditsPackClaimed" in e && "number" == typeof e.orgCreditsPackClaimed ? e.orgCreditsPackClaimed : 0, u.showFreePlan.value = !1, u.showPlusPlan.value = !1, u.showProPlan.value = !1, u.showCreditPlan.value = !1, "inviteCreditStartDate" in e && "inviteCreditEndDate" in e && "number" == typeof e.inviteCreditStartDate && "number" == typeof e.inviteCreditEndDate && e.inviteCreditStartDate > 0 && e.inviteCreditEndDate > 0 && (a.inviteCreditStartDate.value = `${new Date(1e3*e.inviteCreditStartDate).toLocaleDateString()}`, a.inviteCreditEndDate.value = `${new Date(1e3*e.inviteCreditEndDate).toLocaleDateString()}`);
                let y = [];
                if ("subHistory" in e && e.subHistory && Array.isArray(e.subHistory))
                    for (const a of e.subHistory) y.push([`${new Date(1e3*a[0]).toLocaleDateString()}`, `${new Date(1e3*a[1]).toLocaleDateString()}`].join(" - "));
                "isVipMembership" in e && (a.vipMembershipStatus.value = !0 === e.isVipMembership ? "vip" : ""), "applePaidMembershipType" in e && "string" == typeof e.applePaidMembershipType && (a.applePaidMembershipType.value = e.applePaidMembershipType), "appleProductInterval" in e && "string" == typeof e.appleProductInterval && (a.appleProductInterval.value = e.appleProductInterval), a.currentPaidMembershipType.value = r || i ? "" != a.applePaidMembershipType.value ? a.applePaidMembershipType.value : "free" : a.webPaidMembershipType.value, "free" === a.currentPaidMembershipType.value ? ("" === a.inviteCreditStartDate.value && "" === a.inviteCreditEndDate.value ? u.showFreePlan.value = !0 : u.showCreditPlan.value = !0, u.showPlusPlan.value = !0, u.plusShowManageSubscription.value = !1, u.plusButtonDisabled.value = !1, u.showProPlan.value = !0, u.proShowManageSubscription.value = !1, u.proButtonDisabled.value = !1) : "plus" === a.currentPaidMembershipType.value ? (u.showFreePlan.value = !1, u.showPlusPlan.value = !0, r || i ? ("month" === a.appleProductInterval.value && !0 === t || "year" === a.appleProductInterval.value && !1 === t) && (u.plusButtonDisabled.value = !0) : (u.plusShowManageSubscription.value = !0, u.plusButtonDisabled.value = !0), u.showProPlan.value = !0, u.proShowManageSubscription.value = !1, u.proButtonDisabled.value = !1, u.showCreditPlan.value = !0, "" === a.inviteCreditStartDate.value && "" === a.inviteCreditEndDate.value && ("canceled_but_active" === b ? u.plusPlanStatus.value = n("pages.pricing.membership-status-reminder-canceled-but-active") : "expired" === b && (u.plusPlanStatus.value = n("pages.pricing.membership-status-reminder-expired")))) : "pro" === a.currentPaidMembershipType.value ? (u.showFreePlan.value = !1, u.showPlusPlan.value = !1, u.plusShowManageSubscription.value = !1, u.plusButtonDisabled.value = !1, u.showProPlan.value = !0, r || i ? ("month" === a.appleProductInterval.value && !0 === t || "year" === a.appleProductInterval.value && !1 === t) && (u.proButtonDisabled.value = !0) : (u.proShowManageSubscription.value = !0, u.proButtonDisabled.value = !0), u.showCreditPlan.value = !0, "canceled_but_active" === b ? u.proPlanStatus.value = n("pages.pricing.membership-status-reminder-canceled-but-active") : "expired" === b && (u.proPlanStatus.value = n("pages.pricing.membership-status-reminder-expired"))) : (u.plusPlanStatus.value = "", u.proPlanStatus.value = "");
                const g = [`user email [${l}], user phone [${o}], member [${d}],`, `customer [${P}], email [${c} <${p}>], phone [${s}],`, `membership: web_paid=[${a.webPaidMembershipType.value}], final=[${v}], ${b}],`, `currentPaidMembershipType=[${a.currentPaidMembershipType.value}], apple=[${a.applePaidMembershipType.value}, ${a.appleProductInterval.value}], vip=${a.vipMembershipStatus.value},`, `orgMembership: type=[${a.orgMembershipType.value}], status=[${h}], role=[${a.orgRole.value}], `, `subscriptionID [${a.orgSubscriptionId.value}], `, `org id [${a.orgId.value}], `, `org subscription quantity=[${a.orgSubscriptionQuantity.value}], start [${a.orgSubscriptionStart.value}], end [${a.orgSubscriptionEnd.value}],`, `subscriptionID [${u.currentSubscriptionId.value}], subscription interval [${e.subInterval}],`, `subscription_start [${e.subStart?new Date(1e3*e.subStart).toLocaleDateString():"N/A"}], subscription_end [${e.subEnd?new Date(1e3*e.subEnd).toLocaleDateString():"N/A"}],`, `invite credit [${e.creditRemains} out of ${e.creditTotal} months], ${a.inviteCreditStartDate.value} -- ${a.inviteCreditEndDate.value}`, `invite coupon [${e.couponId}, ${e.couponName}], ${e.couponStart?new Date(1e3*e.couponStart).toLocaleDateString():"N/A"} -- ${e.couponEnd?new Date(1e3*e.couponEnd).toLocaleDateString():"N/A"}`, `sub history [${y.join(", ")}]`].join("\n");
                u.debugInfo.value = void 0 !== c ? [`${g}`].join("\n") : [`DEBUG: ${g}`, `as a new paid user, you are going use ${l}`].join("\n")
            },
            l = async e => new Promise((r => setTimeout(r, e)));
        return {
            getCurrentSubscriptionObject: () => ({
                currentPaidMembershipType: a.currentPaidMembershipType,
                webPaidMembershipType: a.webPaidMembershipType,
                orgMembershipType: a.orgMembershipType,
                orgRole: a.orgRole,
                orgId: a.orgId,
                orgSubscriptionId: a.orgSubscriptionId,
                orgSubscriptionStart: a.orgSubscriptionStart,
                orgSubscriptionEnd: a.orgSubscriptionEnd,
                orgSubscriptionQuantity: a.orgSubscriptionQuantity,
                orgSubscriptionMemberTotal: a.orgSubscriptionMemberTotal,
                orgCreditsPackTotal: a.orgCreditsPackTotal,
                orgCreditsPackClaimed: a.orgCreditsPackClaimed,
                inviteCreditStartDate: a.inviteCreditStartDate,
                inviteCreditEndDate: a.inviteCreditEndDate,
                applePaidMembershipType: a.applePaidMembershipType,
                appleProductInterval: a.appleProductInterval,
                vipMembershipStatus: a.vipMembershipStatus,
                plusButtonDisabled: u.plusButtonDisabled,
                plusPlanStatus: u.plusPlanStatus,
                plusShowManageSubscription: u.plusShowManageSubscription,
                proButtonDisabled: u.proButtonDisabled,
                proPlanStatus: u.proPlanStatus,
                proShowManageSubscription: u.proShowManageSubscription,
                currentSubscriptionId: u.currentSubscriptionId,
                showFreePlan: u.showFreePlan,
                showPlusPlan: u.showPlusPlan,
                showProPlan: u.showProPlan,
                showCreditPlan: u.showCreditPlan,
                debugInfo: u.debugInfo
            }),
            fetchCurrentSubscription: async (i, a) => {
                let u = "/api/payment/current_subscriptions";
                "testmode" in i.query && (u += `?testmode=${i.query.testmode}`);
                const n = e(null);
                for (let e = 0; e < 3; e++) try {
                    let e = {
                        key: r
                    };
                    null !== a && (e.headers = a);
                    const {
                        data: i
                    } = await t(u, e, "$LUXcoZJn1J");
                    n.value = i.value;
                    break
                } catch (o) {
                    await l(200)
                }
                return n.value
            },
            processCurrentSubscription: async (e, t, a, u) => {
                const {
                    data: l
                } = i(r);
                null !== l.value && n(l.value, e, t, a, u)
            },
            parseCurrentSubscriptionData: n
        }
    };
export {
    u as a, a as u
};