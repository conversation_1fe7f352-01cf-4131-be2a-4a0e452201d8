import {
    d as n,
    b as o,
    o as r
} from "./Cf0SOiw0.js";
const t = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 32 32"
};
const s = {
    render: function(s, l) {
        return r(), n("svg", t, l[0] || (l[0] = [o("path", {
            d: "M17 15V8h-2v7H8v2h7v7h2v-7h7v-2z",
            fill: "currentColor"
        }, null, -1)]))
    }
};
export {
    s as A
};