import {
    d as t,
    b as C,
    o as e
} from "./Cf0SOiw0.js";
const i = {
    width: "12",
    height: "12",
    viewBox: "0 0 12 12",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const h = {
    render: function(h, l) {
        return e(), t("svg", i, l[0] || (l[0] = [C("rect", {
            x: "0.5",
            y: "0.5",
            width: "11",
            height: "11",
            rx: "5.5",
            stroke: "#CCCCCC"
        }, null, -1), C("rect", {
            x: "5.45312",
            y: "2.18359",
            width: "1.09091",
            height: "5.45455",
            fill: "#CCCCCC"
        }, null, -1), C("rect", {
            x: "5.45312",
            y: "8.72852",
            width: "1.09091",
            height: "1.09091",
            fill: "#CCCCCC"
        }, null, -1)]))
    }
};
export {
    h as W
};