import {
    d as e,
    D as o,
    o as t
} from "./Cf0SOiw0.js";
const r = {
    xmlns: "http://www.w3.org/2000/svg",
    width: "56",
    height: "56",
    viewBox: "0 0 56 56",
    fill: "none"
};
const i = {
    render: function(i, a) {
        return t(), e("svg", r, a[0] || (a[0] = [o('<g filter="url(#filter0_d_9656_28305)"><path d="M10 26C10 16.0589 18.0589 8 28 8C37.9411 8 46 16.0589 46 26C46 35.9411 37.9411 44 28 44C18.0589 44 10 35.9411 10 26Z" fill="white"></path></g><path d="M28 18.1348L28 32.1348" stroke="#232425" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M32.0668 28.9865L28.7262 32.1489C28.3251 32.5285 27.6749 32.5285 27.2738 32.1489L23.9332 28.9865" stroke="#232425" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><defs><filter id="filter0_d_9656_28305" x="0" y="0" width="56" height="56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix><feOffset dy="2"></feOffset><feGaussianBlur stdDeviation="5"></feGaussianBlur><feComposite in2="hardAlpha" operator="out"></feComposite><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"></feColorMatrix><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9656_28305"></feBlend><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9656_28305" result="shape"></feBlend></filter></defs>', 4)]))
    }
};
export {
    i as T
};