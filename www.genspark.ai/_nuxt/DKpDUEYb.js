import {
    _ as o
} from "./e-ES_T8J.js";
import {
    P as e
} from "./CAzLTKWw.js";
import {
    P as a,
    a as i
} from "./DPPm6Skg.js";
import {
    _ as s,
    s as n,
    r,
    v as t,
    i as d,
    C as l,
    a as w,
    w as u,
    A as p,
    j as c,
    Q as m,
    B as P,
    E as g,
    o as h,
    e as C
} from "./Cf0SOiw0.js";
import "./DY44xVYu.js";
import {
    N as v
} from "./BjWUbj3w.js";
const y = s({
    name: "QuotaExceed",
    components: {
        PricingWindowForPlusPlan: e,
        PricingWindowForCreditPack: i,
        PricingWindowForTeamCreditsClaim: a,
        NModal: v
    },
    props: {
        windowType: {
            type: String,
            default: "standard"
        }
    },
    setup(o, {
        expose: e
    }) {
        const {
            t: a
        } = n(), i = d("currentUser"), s = d("jsBridge"), w = r(!0), u = r(!1), g = r(!1), h = r(!1), C = r(!1), v = r(""), y = p();
        t((() => s.value), (o => {
            o && s.value.callHandler && s.value.callHandler("support", {
                api: "iap"
            }, (o => {
                u.value = o
            }))
        }), {
            immediate: !0
        });
        return e({
            trigger: async () => {
                (!l.isGensparkAppIos() || w.value && u.value) && (y.query.from !== c ? i.value && ("team_plus" === i.value.org_plan ? C.value = !0 : i.value.plan && "free" !== i.value.plan ? h.value = !0 : g.value = !0) : window.parent.postMessage({
                    type: m.ChromeExtensionQuotaExceed,
                    payload: {
                        redirectUrl: window.location.origin + "/pricing"
                    }
                }, P.ExtensionOrigin))
            }
        }), {
            utils: l,
            showPricingWindowForPlusPlan: g,
            showQuotaExceed: h,
            showPricingWindowForTeamCreditsClaim: C,
            quotaExceedMessage: v,
            currentUser: i
        }
    }
}, [
    ["render", function(e, a, i, s, n, r) {
        const t = g("PricingWindowForPlusPlan"),
            d = g("NModal"),
            l = g("PricingWindowForCreditPack"),
            p = g("PricingWindowForTeamCreditsClaim"),
            c = o;
        return h(), w(c, null, {
            default: u((() => [C(d, {
                show: s.showPricingWindowForPlusPlan,
                "onUpdate:show": a[1] || (a[1] = o => s.showPricingWindowForPlusPlan = o)
            }, {
                default: u((() => [C(t, {
                    "window-type": i.windowType,
                    onClose: a[0] || (a[0] = o => s.showPricingWindowForPlusPlan = !1)
                }, null, 8, ["window-type"])])),
                _: 1
            }, 8, ["show"]), C(d, {
                show: s.showQuotaExceed,
                "onUpdate:show": a[3] || (a[3] = o => s.showQuotaExceed = o)
            }, {
                default: u((() => [C(l, {
                    "window-type": i.windowType,
                    onClose: a[2] || (a[2] = o => s.showQuotaExceed = !1)
                }, null, 8, ["window-type"])])),
                _: 1
            }, 8, ["show"]), C(d, {
                show: s.showPricingWindowForTeamCreditsClaim,
                "onUpdate:show": a[5] || (a[5] = o => s.showPricingWindowForTeamCreditsClaim = o)
            }, {
                default: u((() => [C(p, {
                    "window-type": i.windowType,
                    onClose: a[4] || (a[4] = o => s.showPricingWindowForTeamCreditsClaim = !1)
                }, null, 8, ["window-type"])])),
                _: 1
            }, 8, ["show"])])),
            _: 1
        })
    }],
    ["__scopeId", "data-v-3dec1afb"]
]);
export {
    y as _
};