import {
    _ as e
} from "./e-ES_T8J.js";
import {
    _ as a
} from "./D6bQc9d9.js";
import {
    d as t,
    b as l,
    o as i,
    _ as r,
    s as o,
    r as s,
    c as n,
    h as c,
    a3 as u,
    v as d,
    e as m,
    f as g,
    w as h,
    q as p,
    F as v,
    k as y,
    t as f,
    C as w,
    g as I,
    E as C,
    n as _,
    H as k,
    a9 as O,
    L as b,
    a as E,
    p as S,
    l as x,
    ai as M
} from "./Cf0SOiw0.js";
import {
    N as B,
    U as A,
    _ as U
} from "./Boc3hm_9.js";
import {
    O as j
} from "./DnZj1005.js";
import {
    E as L
} from "./D5ao1EUl.js";
import {
    I as Q
} from "./CztlUxD-.js";
import {
    R
} from "./BUjMLq-a.js";
import {
    S as W
} from "./DxgY8w7w.js";
import {
    d as q
} from "./DOnko34f.js";
import {
    N as G
} from "./DXvAIxvL.js";
import {
    N
} from "./B-XpIQkh.js";
const $ = {
    width: "16",
    height: "16",
    viewBox: "0 0 16 16",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const P = {
        render: function(e, a) {
            return i(), t("svg", $, a[0] || (a[0] = [l("path", {
                d: "M10.2353 12.711C12.1183 10.919 12.9614 9.14242 13.2763 7.084C12.7472 7.33299 12.1474 7.41103 11.5676 7.30631C10.9878 7.20159 10.4596 6.91981 10.063 6.50363C9.66639 6.08745 9.42293 5.55952 9.3695 4.99978C9.31607 4.44004 9.45556 3.87896 9.76687 3.40148C10.0782 2.924 10.5444 2.55611 11.0948 2.35352C11.6453 2.15093 12.2501 2.12466 12.8176 2.27869C13.3851 2.43272 13.8845 2.75867 14.2402 3.20718C14.5958 3.65569 14.7884 4.20235 14.7886 4.7644C14.7873 8.1158 14.0718 10.8854 11.1951 13.6265C11.0677 13.7479 10.8948 13.8161 10.7146 13.8161C10.5343 13.8161 10.3614 13.7479 10.234 13.6265C10.1065 13.5051 10.0349 13.3404 10.0349 13.1687C10.0349 12.9971 10.1079 12.8324 10.2353 12.711ZM2.08989 12.711C3.97284 10.919 4.81726 9.14242 5.13086 7.084C4.60179 7.33299 4.00196 7.41103 3.42216 7.30631C2.84236 7.20159 2.31417 6.91981 1.91755 6.50363C1.52093 6.08745 1.27748 5.55952 1.22405 4.99978C1.17061 4.44004 1.31011 3.87896 1.62142 3.40148C1.93273 2.924 2.3989 2.55611 2.94936 2.35352C3.49983 2.15093 4.1046 2.12466 4.67214 2.27869C5.23968 2.43272 5.73907 2.75867 6.09472 3.20718C6.45037 3.65569 6.64291 4.20235 6.64319 4.7644C6.64184 8.1158 5.9264 10.8854 3.04969 13.6265C2.92223 13.7479 2.74936 13.8161 2.56911 13.8161C2.38886 13.8161 2.21599 13.7479 2.08853 13.6265C1.96107 13.5051 1.88947 13.3404 1.88947 13.1687C1.88947 12.9971 1.96243 12.8324 2.08989 12.711Z",
                fill: "white"
            }, null, -1)]))
        }
    },
    F = {
        name: "MediaGallery",
        components: {
            MOAImageDetail: U,
            NInput: N,
            NIcon: G,
            OutlinkIconSvg: j,
            LoadingAnimation: a,
            EnterIcon: L,
            InputLoading: Q,
            QuoteMarkIcon: P,
            RemixIcon: R,
            UploadIcon: A,
            SearchIcon: W,
            NoImageIcon: B
        },
        props: {
            emptyMessage: {
                type: String,
                default: ""
            },
            showFilters: {
                type: Boolean,
                default: !1
            },
            initialItems: {
                type: Array,
                default: () => []
            },
            initialQuery: {
                type: String,
                default: ""
            },
            showSource: {
                type: Boolean,
                default: !0
            },
            integratedMode: {
                type: Boolean,
                default: !1
            },
            useExternalSearch: {
                type: Boolean,
                default: !1
            },
            promptInputRef: {
                type: Object,
                default: null
            },
            showGallery: {
                type: Boolean,
                default: !1
            },
            hiddenQuery: {
                type: String,
                default: ""
            },
            crossOrigin: {
                type: String,
                default: ""
            },
            remixButtonText: {
                type: String,
                default: "Remix"
            },
            customIcon: {
                type: [Object, Function],
                default: null
            },
            maxImagesCount: {
                type: Number,
                default: 1e3
            }
        },
        emits: ["search", "imageClick", "add-image-url", "add-image-dataURL", "update-search", "loading-gallery", "update:showGallery", "update:items"],
        setup(e, {
            emit: a
        }) {
            const t = I(),
                {
                    t: l
                } = o(),
                i = s(e.initialQuery),
                r = s(e.hiddenQuery),
                m = s(e.initialItems),
                g = s(!1),
                h = s(!1),
                p = s(0),
                v = s(208),
                y = s(20),
                f = s(0),
                C = s(!0),
                _ = s(6),
                k = s(null),
                O = s(!1),
                b = s(null),
                E = n((() => !e.useExternalSearch && e.showFilters)),
                S = s(!1),
                x = s(!1),
                M = e.emptyMessage || l("components.generate_image_content.no_image_found"),
                B = n((() => e => {
                    const a = "_t=buster";
                    return e ? e.includes("?") ? `${e}&${a}` : `${e}?${a}` : ""
                })),
                A = e => new Promise((async (a, t) => {
                    try {
                        const t = document.querySelectorAll(`img[src="${e}"]`),
                            l = document.querySelectorAll(`img[src="${e.replace("width=1024","width=512")}"]`);
                        if (t.length > 0) {
                            const e = t[0].complete ? t[0] : l[0];
                            if (e.complete) {
                                const t = document.createElement("canvas");
                                t.width = e.naturalWidth, t.height = e.naturalHeight;
                                return t.getContext("2d").drawImage(e, 0, 0), void a(t.toDataURL("image/png"))
                            }
                        }
                    } catch (l) {
                        t(l)
                    }
                })),
                j = async () => {
                    f.value = 0, m.value = [], g.value = !0, w.log("loading-gallery:", g.value), a("loading-gallery", !0);
                    try {
                        const e = await $fetch("/api/copilot/get_flow_items", {
                            params: {
                                type: "COPILOT_IMAGES",
                                query: i.value || r.value,
                                page: f.value
                            }
                        });
                        0 === e.status && (m.value = e.data.flow_items, h.value = 0 === m.value.length, C.value = e.data.flow_items.length > 0, a("update-search", {
                            items: m.value,
                            query: i.value
                        }))
                    } catch (e) {} finally {
                        g.value = !1, w.log("loading-gallery:", g.value), a("loading-gallery", !1)
                    }
                },
                L = q((() => {
                    p.value = window.innerWidth
                }), 200);
            c((() => {
                L(), window.addEventListener("resize", L), e.initialItems.length || j();
                const a = new IntersectionObserver((e => {
                    e[0].isIntersecting && !g.value && C.value && Q()
                }), {
                    threshold: .1
                });
                b.value && a.observe(b.value)
            })), u((() => {
                window.removeEventListener("resize", L)
            })), d((() => m.value), (() => {
                L()
            }), {
                deep: !0
            });
            const Q = async () => {
                if (!g.value && C.value)
                    if (m.value.length >= e.maxImagesCount) C.value = !1;
                    else {
                        g.value = !0, f.value++;
                        try {
                            const t = await $fetch("/api/copilot/get_flow_items", {
                                params: {
                                    type: "COPILOT_IMAGES",
                                    query: i.value || r.value,
                                    page: f.value
                                }
                            });
                            if (0 === t.status) {
                                const l = e.maxImagesCount - m.value.length,
                                    i = t.data.flow_items.slice(0, l);
                                m.value = [...m.value, ...i], C.value = i.length > 0 && m.value.length < e.maxImagesCount, a("update:items", m.value)
                            }
                        } catch (t) {} finally {
                            g.value = !1
                        }
                    }
            };
            d((() => e.initialQuery), (a => {
                e.useExternalSearch && a !== i.value && (i.value = a, w.log("searchQuery:", i.value), j())
            })), d((() => e.hiddenQuery), (e => {
                e !== r.value && (r.value = e, i.value || j())
            }));
            return {
                getUrlWithCacheBuster: B,
                searchQuery: i,
                hiddenQuery: r,
                flow_items: m,
                loading: g,
                showEmptyPage: h,
                windowSize: p,
                columnWidth: v,
                columnGap: y,
                page: f,
                hasMore: C,
                columnNum: _,
                selectedImage: k,
                showImageDetail: O,
                getColumns: (e, a) => {
                    if (!e || 0 === a) return [];
                    const t = document.querySelector(".media-gallery").offsetWidth;
                    t <= 500 ? (_.value = 2, y.value = 10) : t <= 768 ? (_.value = 3, y.value = 20) : t <= 2048 ? (_.value = 4, y.value = 20) : (_.value = 6, y.value = 20), v.value = Math.floor((t - 40 - (_.value - 1) * y.value) / _.value);
                    const l = Array.from({
                        length: _.value
                    }, (() => ({
                        items: [],
                        height: 0
                    })));
                    return e.forEach((e => {
                        let a = l[0];
                        l.forEach((e => {
                            e.height < a.height && (a = e)
                        })), a.items.push(e);
                        const t = e.original_width / e.original_height || 1,
                            i = v.value / t;
                        a.height += i + 20
                    })), l.map((e => e.items))
                },
                handleSearch: j,
                handleImageClick: e => {
                    w.log("handleImageClick", e.id), k.value = e, O.value = !0, t.push({
                        path: "/gallery/detail",
                        query: {
                            id: e.id
                        }
                    }), a("imageClick", e)
                },
                handleLinkClick: (e, a) => {
                    e.preventDefault(), window.open(a, "_blank")
                },
                getHost: e => {
                    try {
                        return new URL(e).hostname.replace(/^www\./, "")
                    } catch (a) {
                        return e
                    }
                },
                MOAImageDetail: U,
                loadMore: Q,
                t: l,
                handleAddImageUrl: async l => {
                    S.value = !0;
                    const i = "string" == typeof l ? l : l.url;
                    if (e.integratedMode) {
                        if (a("add-image-url", {
                                url: i
                            }), e.crossOrigin) {
                            const e = await A(i.replace("width=512", "width=1024"));
                            a("add-image-dataURL", {
                                url: e
                            })
                        }
                    } else t.push({
                        path: "/agents",
                        query: {
                            type: "moa_generate_image",
                            remixImageUrl: i
                        }
                    });
                    a("update:showGallery", !1)
                },
                QuoteMarkIcon: P,
                loadMoreElem: b,
                showSearchBar: E,
                loadingAddImage: S,
                selectImage: async () => {
                    if (e.promptInputRef) try {
                        await e.promptInputRef.selectImage(), a("update:showGallery", !1)
                    } catch (t) {}
                },
                isSearchBarActive: x,
                emptyMessage: M,
                handleImageError: e => {
                    w.log("handleImageError called with flow_item:", e), e.loadError = !0
                },
                RemixIcon: R
            }
        }
    },
    T = {
        class: "media-gallery"
    },
    z = {
        key: 0,
        class: "input search-filters"
    },
    D = ["placeholder"],
    H = {
        class: "upload-icon"
    },
    K = {
        class: "upload-text"
    },
    Z = ["onClick"],
    V = ["src", "onError", "crossOrigin"],
    J = ["src", "crossOrigin"],
    X = ["onClick"],
    Y = {
        class: "remix-count"
    },
    ee = ["onClick"],
    ae = ["src", "onLoad", "onError", "crossOrigin"],
    te = ["src", "crossOrigin"],
    le = {
        key: 1,
        class: "remix-button hover-show"
    },
    ie = {
        key: 2,
        class: "empty_page"
    },
    re = {
        class: "title"
    },
    oe = {
        key: 3,
        class: "loading"
    },
    se = {
        class: "loading-wrapper"
    };
const ne = r(F, [
        ["render", function(r, o, s, n, c, u) {
            const d = C("SearchIcon"),
                w = C("UploadIcon"),
                I = e,
                B = a,
                A = C("NoImageIcon");
            return i(), t("div", T, [m(I, null, {
                default: h((() => [n.showSearchBar ? (i(), t("div", z, [l("div", {
                    class: _(["basic-search-bar", {
                        "is-active": n.isSearchBarActive
                    }]),
                    onClick: o[2] || (o[2] = e => n.isSearchBarActive = !0),
                    onFocusin: o[3] || (o[3] = e => n.isSearchBarActive = !0),
                    onFocusout: o[4] || (o[4] = e => n.isSearchBarActive = !1)
                }, [m(d, {
                    class: "search-icon"
                }), k(l("input", {
                    type: "text",
                    "onUpdate:modelValue": o[0] || (o[0] = e => n.searchQuery = e),
                    placeholder: n.t("pages.gallery.search-by-image-name"),
                    onKeyup: o[1] || (o[1] = O(((...e) => n.handleSearch && n.handleSearch(...e)), ["enter"]))
                }, null, 40, D), [
                    [b, n.searchQuery]
                ])], 34), l("div", {
                    class: "upload-button",
                    onClick: o[5] || (o[5] = (...e) => n.selectImage && n.selectImage(...e))
                }, [l("div", H, [m(w)]), l("div", K, f(n.t("components.generate_image_content.upload_image")), 1)])])) : g("", !0)])),
                _: 1
            }), n.flow_items && n.flow_items.length > 0 ? (i(), t("div", {
                key: 0,
                class: "images",
                style: p({
                    "--flow_item_column_width": n.columnWidth + "px",
                    "--flow_item_column_gap": n.columnGap + "px"
                })
            }, [(i(!0), t(v, null, y(n.getColumns(n.flow_items, n.windowSize), ((e, a) => (i(), t("div", {
                key: a,
                class: "image_column"
            }, [(i(!0), t(v, null, y(e, ((e, a) => (i(), t("div", {
                key: a,
                class: "flow_item_wrapper"
            }, [s.integratedMode ? (i(), t("div", {
                key: 1,
                class: "image_item",
                onClick: x((a => e.thumbnail && !e.loadError && n.handleAddImageUrl(s.crossOrigin ? n.getUrlWithCacheBuster(e.thumbnail) : e.thumbnail)), ["stop"])
            }, [e.loadError ? (i(), E(A, {
                key: 0,
                class: "no-image-icon"
            })) : g("", !0), l("div", {
                class: "skeleton-wrapper",
                style: p({
                    paddingBottom: e.original_height / e.original_width * 100 + "%"
                })
            }, [l("div", {
                class: _(["animate-skeleton", {
                    "is-error": e.loadError
                }])
            }, null, 2), l("img", {
                src: s.crossOrigin ? n.getUrlWithCacheBuster(e.thumbnail) : e.thumbnail,
                class: _(["actual-image", {
                    "is-error": e.loadError
                }]),
                onLoad: a => e.isLoaded = !0,
                onError: a => n.handleImageError(e),
                crossOrigin: s.crossOrigin || void 0
            }, null, 42, ae), l("img", {
                src: (s.crossOrigin ? n.getUrlWithCacheBuster(e.thumbnail) : e.thumbnail).replace("width=512", "width=1024"),
                crossOrigin: s.crossOrigin || void 0,
                style: {
                    display: "none"
                }
            }, null, 8, te)], 4), e.isLoaded ? (i(), t("div", le, [(i(), E(M(s.customIcon || n.RemixIcon), {
                class: "remix-icon"
            })), S(" " + f(s.remixButtonText), 1)])) : g("", !0)], 8, ee)) : (i(), t("div", {
                key: 0,
                class: "image_item",
                onClick: a => n.handleImageClick(e)
            }, [l("div", {
                class: "skeleton-wrapper",
                style: p({
                    paddingBottom: e.original_height / e.original_width * 100 + "%"
                })
            }, [o[7] || (o[7] = l("div", {
                class: "animate-skeleton"
            }, null, -1)), l("img", {
                src: s.crossOrigin ? n.getUrlWithCacheBuster(e.thumbnail) : e.thumbnail,
                class: _(["actual-image", {
                    "is-error": e.loadError
                }]),
                onError: a => n.handleImageError(e),
                crossOrigin: s.crossOrigin || void 0
            }, null, 42, V), l("img", {
                src: (s.crossOrigin ? n.getUrlWithCacheBuster(e.thumbnail) : e.thumbnail).replace("width=512", "width=1024"),
                crossOrigin: s.crossOrigin || void 0,
                style: {
                    display: "none"
                }
            }, null, 8, J)], 4), l("div", {
                class: "number-label hover-show",
                onClick: x((a => e.thumbnail && n.handleAddImageUrl(s.crossOrigin ? n.getUrlWithCacheBuster(e.thumbnail) : e.thumbnail)), ["stop"])
            }, [n.loadingAddImage ? n.loadingAddImage ? (i(), E(B, {
                key: 1
            })) : g("", !0) : (i(), t(v, {
                key: 0
            }, [S(f(s.remixButtonText), 1)], 64))], 8, X), l("div", Y, [(i(), E(M(s.customIcon || n.RemixIcon), {
                class: "remix-icon"
            })), l("p", null, f(Math.floor(100 * Math.random()) + 1), 1)])], 8, Z))])))), 128))])))), 128))], 4)) : g("", !0), n.showEmptyPage ? g("", !0) : (i(), t("div", {
                key: 1,
                class: "more",
                onClick: o[6] || (o[6] = (...e) => n.loadMore && n.loadMore(...e)),
                ref: "loadMoreElem"
            }, null, 512)), n.showEmptyPage ? (i(), t("div", ie, [l("div", re, f(n.emptyMessage), 1)])) : g("", !0), n.loading ? (i(), t("div", oe, [l("div", se, [m(B)])])) : g("", !0)])
        }],
        ["__scopeId", "data-v-e47f9881"]
    ]),
    ce = Object.freeze(Object.defineProperty({
        __proto__: null,
        default: ne
    }, Symbol.toStringTag, {
        value: "Module"
    }));
export {
    ne as M, P as Q, ce as a
};