import {
    d as a,
    b as n,
    o as l
} from "./Cf0SOiw0.js";
const h = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 20 20"
};
const v = {
        render: function(v, r) {
            return l(), a("svg", h, r[0] || (r[0] = [n("g", {
                fill: "none"
            }, [n("path", {
                d: "M3.5 16a.5.5 0 0 0 0 1h13a.5.5 0 0 0 0-1h-13zm0-13a.5.5 0 0 0 0 1h13a.5.5 0 0 0 0-1h-13zM3 11a2 2 0 0 0 2 2h2V7H5a2 2 0 0 0-2 2v2zm9 2H8V7h4v6zm1 0V7h2a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-2z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    r = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 20 20"
    };
const t = {
        render: function(h, v) {
            return l(), a("svg", r, v[0] || (v[0] = [n("g", {
                fill: "none"
            }, [n("path", {
                d: "M4 3.5a.5.5 0 0 0-1 0v13a.5.5 0 0 0 1 0v-13zm13 0a.5.5 0 0 0-1 0v13a.5.5 0 0 0 1 0v-13zM9 3a2 2 0 0 0-2 2v2h6V5a2 2 0 0 0-2-2H9zm-2 9V8h6v4H7zm0 1h6v2a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2v-2z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    w = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 20 20"
    };
const o = {
        render: function(h, v) {
            return l(), a("svg", w, v[0] || (v[0] = [n("g", {
                fill: "none"
            }, [n("path", {
                d: "M16.5 2a.5.5 0 0 1 .5.5V4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V2.5a.5.5 0 0 1 1 0V4a1 1 0 0 0 1 1h2V2.5a.5.5 0 0 1 1 0V5h4V2.5a.5.5 0 0 1 1 0V5h2a1 1 0 0 0 1-1V2.5a.5.5 0 0 1 .5-.5zm0 16a.5.5 0 0 0 .5-.5V16a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v1.5a.5.5 0 0 0 1 0V16a1 1 0 0 1 1-1h2v2.5a.5.5 0 0 0 1 0V15h4v2.5a.5.5 0 0 0 1 0V15h2a1 1 0 0 1 1 1v1.5a.5.5 0 0 0 .5.5zm1-7.5a.5.5 0 0 0 0-1h-4.887c-.106.125-.224.24-.342.353l-.143.14l.143.14c.122.119.245.237.353.367H17.5zm-15-1h4.887a5 5 0 0 0 .342.353l.143.14l-.143.14a4.833 4.833 0 0 0-.353.367H2.5a.5.5 0 0 1 0-1zm9.354 2.354a.5.5 0 0 0 0-.708L10.707 10l1.147-1.146a.5.5 0 0 0-.708-.708L10 9.293L8.854 8.146a.5.5 0 1 0-.708.708L9.293 10l-1.147 1.146a.5.5 0 0 0 .708.708L10 10.707l1.146 1.147a.5.5 0 0 0 .708 0z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    s = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 20 20"
    };
const m = {
        render: function(h, v) {
            return l(), a("svg", s, v[0] || (v[0] = [n("g", {
                fill: "none"
            }, [n("path", {
                d: "M10.5 2.5a.5.5 0 0 0-1 0v4.875c.127.107.244.228.36.348l.14.143l.14-.143c.116-.12.233-.24.36-.348V2.5zm-1 15v-4.888c.127-.107.244-.228.36-.348l.14-.143l.14.143c.116.12.233.24.36.348V17.5a.5.5 0 0 1-1 0zM2 3.5a.5.5 0 0 1 .5-.5H4a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H2.5a.5.5 0 0 1 0-1H4a1 1 0 0 0 1-1v-2H2.5a.5.5 0 0 1 0-1H5V8H2.5a.5.5 0 0 1 0-1H5V5a1 1 0 0 0-1-1H2.5a.5.5 0 0 1-.5-.5zm16 0a.5.5 0 0 0-.5-.5H16a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h1.5a.5.5 0 0 0 0-1H16a1 1 0 0 1-1-1v-2h2.5a.5.5 0 0 0 0-1H15V8h2.5a.5.5 0 0 0 0-1H15V5a1 1 0 0 1 1-1h1.5a.5.5 0 0 0 .5-.5zm-6.146 4.646a.5.5 0 0 0-.708 0L10 9.293L8.854 8.146a.5.5 0 1 0-.708.708L9.293 10l-1.147 1.146a.5.5 0 0 0 .708.708L10 10.707l1.146 1.147a.5.5 0 0 0 .708-.708L10.707 10l1.147-1.146a.5.5 0 0 0 0-.708z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    z = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 20 20"
    };
const e = {
        render: function(h, v) {
            return l(), a("svg", z, v[0] || (v[0] = [n("g", {
                fill: "none"
            }, [n("path", {
                d: "M17 16.5a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1 0-1h13a.5.5 0 0 1 .5.5zm0-5a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v6.5zM16 7V5a1 1 0 0 0-1-1h-2v3h3zM8 4v3h4V4H8zM7 4H5a1 1 0 0 0-1 1v2h3V4zm0 4H4v3h3V8zm1 3h4V8H8v3zm8 0V8h-3v3h3z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    i = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 20 20"
    };
const V = {
        render: function(h, v) {
            return l(), a("svg", i, v[0] || (v[0] = [n("g", {
                fill: "none"
            }, [n("path", {
                d: "M16.5 3a.5.5 0 0 1 .5.5v13a.5.5 0 0 1-1 0v-13a.5.5 0 0 1 .5-.5zm-5 0a.5.5 0 0 1 .5.5v13a.5.5 0 0 1-.5.5H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6.5zM7 4H5a1 1 0 0 0-1 1v2h3V4zm-3 8h3V8H4v4zm0 1v2a1 1 0 0 0 1 1h2v-3H4zm4 0v3h3v-3H8zm3-1V8H8v4h3zm0-8H8v3h3V4z",
                fill: "currentColor"
            })], -1)]))
        }
    },
    H = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 20 20"
    };
const x = {
    render: function(h, v) {
        return l(), a("svg", H, v[0] || (v[0] = [n("g", {
            fill: "none"
        }, [n("path", {
            d: "M3 3.5a.5.5 0 0 1 .5-.5h13a.5.5 0 0 1 0 1h-13a.5.5 0 0 1-.5-.5zm0 5a.5.5 0 0 1 .5-.5h13a.5.5 0 0 1 .5.5V15a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8.5zM4 13v2a1 1 0 0 0 1 1h2v-3H4zm8 3v-3H8v3h4zm1 0h2a1 1 0 0 0 1-1v-2h-3v3zm0-4h3V9h-3v3zm-1-3H8v3h4V9zM4 9v3h3V9H4z",
            fill: "currentColor"
        })], -1)]))
    }
};
export {
    e as A, m as D, t as M, v as a, o as b, V as c, x as d
};