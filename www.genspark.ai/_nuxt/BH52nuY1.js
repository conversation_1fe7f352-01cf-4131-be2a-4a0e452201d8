var t = {
    value: () => {}
};

function n() {
    for (var t, n = 0, r = arguments.length, i = {}; n < r; ++n) {
        if (!(t = arguments[n] + "") || t in i || /[\s.]/.test(t)) throw new Error("illegal type: " + t);
        i[t] = []
    }
    return new e(i)
}

function e(t) {
    this._ = t
}

function r(t, n) {
    for (var e, r = 0, i = t.length; r < i; ++r)
        if ((e = t[r]).name === n) return e.value
}

function i(n, e, r) {
    for (var i = 0, o = n.length; i < o; ++i)
        if (n[i].name === e) {
            n[i] = t, n = n.slice(0, i).concat(n.slice(i + 1));
            break
        }
    return null != r && n.push({
        name: e,
        value: r
    }), n
}
e.prototype = n.prototype = {
    constructor: e,
    on: function(t, n) {
        var e, o, u = this._,
            a = (o = u, (t + "").trim().split(/^|\s+/).map((function(t) {
                var n = "",
                    e = t.indexOf(".");
                if (e >= 0 && (n = t.slice(e + 1), t = t.slice(0, e)), t && !o.hasOwnProperty(t)) throw new Error("unknown type: " + t);
                return {
                    type: t,
                    name: n
                }
            }))),
            s = -1,
            l = a.length;
        if (!(arguments.length < 2)) {
            if (null != n && "function" != typeof n) throw new Error("invalid callback: " + n);
            for (; ++s < l;)
                if (e = (t = a[s]).type) u[e] = i(u[e], t.name, n);
                else if (null == n)
                for (e in u) u[e] = i(u[e], t.name, null);
            return this
        }
        for (; ++s < l;)
            if ((e = (t = a[s]).type) && (e = r(u[e], t.name))) return e
    },
    copy: function() {
        var t = {},
            n = this._;
        for (var r in n) t[r] = n[r].slice();
        return new e(t)
    },
    call: function(t, n) {
        if ((e = arguments.length - 2) > 0)
            for (var e, r, i = new Array(e), o = 0; o < e; ++o) i[o] = arguments[o + 2];
        if (!this._.hasOwnProperty(t)) throw new Error("unknown type: " + t);
        for (o = 0, e = (r = this._[t]).length; o < e; ++o) r[o].value.apply(n, i)
    },
    apply: function(t, n, e) {
        if (!this._.hasOwnProperty(t)) throw new Error("unknown type: " + t);
        for (var r = this._[t], i = 0, o = r.length; i < o; ++i) r[i].value.apply(n, e)
    }
};
var o = "http://www.w3.org/1999/xhtml";
const u = {
    svg: "http://www.w3.org/2000/svg",
    xhtml: o,
    xlink: "http://www.w3.org/1999/xlink",
    xml: "http://www.w3.org/XML/1998/namespace",
    xmlns: "http://www.w3.org/2000/xmlns/"
};

function a(t) {
    var n = t += "",
        e = n.indexOf(":");
    return e >= 0 && "xmlns" !== (n = t.slice(0, e)) && (t = t.slice(e + 1)), u.hasOwnProperty(n) ? {
        space: u[n],
        local: t
    } : t
}

function s(t) {
    return function() {
        var n = this.ownerDocument,
            e = this.namespaceURI;
        return e === o && n.documentElement.namespaceURI === o ? n.createElement(t) : n.createElementNS(e, t)
    }
}

function l(t) {
    return function() {
        return this.ownerDocument.createElementNS(t.space, t.local)
    }
}

function c(t) {
    var n = a(t);
    return (n.local ? l : s)(n)
}

function h() {}

function f(t) {
    return null == t ? h : function() {
        return this.querySelector(t)
    }
}

function p() {
    return []
}

function d(t) {
    return null == t ? p : function() {
        return this.querySelectorAll(t)
    }
}

function y(t) {
    return function() {
        return null == (n = t.apply(this, arguments)) ? [] : Array.isArray(n) ? n : Array.from(n);
        var n
    }
}

function _(t) {
    return function() {
        return this.matches(t)
    }
}

function v(t) {
    return function(n) {
        return n.matches(t)
    }
}
var g = Array.prototype.find;

function m() {
    return this.firstElementChild
}
var w = Array.prototype.filter;

function b() {
    return Array.from(this.children)
}

function x(t) {
    return new Array(t.length)
}

function k(t, n) {
    this.ownerDocument = t.ownerDocument, this.namespaceURI = t.namespaceURI, this._next = null, this._parent = t, this.__data__ = n
}

function A(t, n, e, r, i, o) {
    for (var u, a = 0, s = n.length, l = o.length; a < l; ++a)(u = n[a]) ? (u.__data__ = o[a], r[a] = u) : e[a] = new k(t, o[a]);
    for (; a < s; ++a)(u = n[a]) && (i[a] = u)
}

function N(t, n, e, r, i, o, u) {
    var a, s, l, c = new Map,
        h = n.length,
        f = o.length,
        p = new Array(h);
    for (a = 0; a < h; ++a)(s = n[a]) && (p[a] = l = u.call(s, s.__data__, a, n) + "", c.has(l) ? i[a] = s : c.set(l, s));
    for (a = 0; a < f; ++a) l = u.call(t, o[a], a, o) + "", (s = c.get(l)) ? (r[a] = s, s.__data__ = o[a], c.delete(l)) : e[a] = new k(t, o[a]);
    for (a = 0; a < h; ++a)(s = n[a]) && c.get(p[a]) === s && (i[a] = s)
}

function $(t) {
    return t.__data__
}

function E(t) {
    return "object" == typeof t && "length" in t ? t : Array.from(t)
}

function M(t, n) {
    return t < n ? -1 : t > n ? 1 : t >= n ? 0 : NaN
}

function S(t) {
    return function() {
        this.removeAttribute(t)
    }
}

function C(t) {
    return function() {
        this.removeAttributeNS(t.space, t.local)
    }
}

function q(t, n) {
    return function() {
        this.setAttribute(t, n)
    }
}

function X(t, n) {
    return function() {
        this.setAttributeNS(t.space, t.local, n)
    }
}

function P(t, n) {
    return function() {
        var e = n.apply(this, arguments);
        null == e ? this.removeAttribute(t) : this.setAttribute(t, e)
    }
}

function O(t, n) {
    return function() {
        var e = n.apply(this, arguments);
        null == e ? this.removeAttributeNS(t.space, t.local) : this.setAttributeNS(t.space, t.local, e)
    }
}

function T(t) {
    return t.ownerDocument && t.ownerDocument.defaultView || t.document && t || t.defaultView
}

function R(t) {
    return function() {
        this.style.removeProperty(t)
    }
}

function H(t, n, e) {
    return function() {
        this.style.setProperty(t, n, e)
    }
}

function j(t, n, e) {
    return function() {
        var r = n.apply(this, arguments);
        null == r ? this.style.removeProperty(t) : this.style.setProperty(t, r, e)
    }
}

function I(t, n) {
    return t.style.getPropertyValue(n) || T(t).getComputedStyle(t, null).getPropertyValue(n)
}

function Y(t) {
    return function() {
        delete this[t]
    }
}

function L(t, n) {
    return function() {
        this[t] = n
    }
}

function D(t, n) {
    return function() {
        var e = n.apply(this, arguments);
        null == e ? delete this[t] : this[t] = e
    }
}

function B(t) {
    return t.trim().split(/^|\s+/)
}

function z(t) {
    return t.classList || new V(t)
}

function V(t) {
    this._node = t, this._names = B(t.getAttribute("class") || "")
}

function U(t, n) {
    for (var e = z(t), r = -1, i = n.length; ++r < i;) e.add(n[r])
}

function F(t, n) {
    for (var e = z(t), r = -1, i = n.length; ++r < i;) e.remove(n[r])
}

function K(t) {
    return function() {
        U(this, t)
    }
}

function W(t) {
    return function() {
        F(this, t)
    }
}

function G(t, n) {
    return function() {
        (n.apply(this, arguments) ? U : F)(this, t)
    }
}

function J() {
    this.textContent = ""
}

function Q(t) {
    return function() {
        this.textContent = t
    }
}

function Z(t) {
    return function() {
        var n = t.apply(this, arguments);
        this.textContent = null == n ? "" : n
    }
}

function tt() {
    this.innerHTML = ""
}

function nt(t) {
    return function() {
        this.innerHTML = t
    }
}

function et(t) {
    return function() {
        var n = t.apply(this, arguments);
        this.innerHTML = null == n ? "" : n
    }
}

function rt() {
    this.nextSibling && this.parentNode.appendChild(this)
}

function it() {
    this.previousSibling && this.parentNode.insertBefore(this, this.parentNode.firstChild)
}

function ot() {
    return null
}

function ut() {
    var t = this.parentNode;
    t && t.removeChild(this)
}

function at() {
    var t = this.cloneNode(!1),
        n = this.parentNode;
    return n ? n.insertBefore(t, this.nextSibling) : t
}

function st() {
    var t = this.cloneNode(!0),
        n = this.parentNode;
    return n ? n.insertBefore(t, this.nextSibling) : t
}

function lt(t) {
    return function() {
        var n = this.__on;
        if (n) {
            for (var e, r = 0, i = -1, o = n.length; r < o; ++r) e = n[r], t.type && e.type !== t.type || e.name !== t.name ? n[++i] = e : this.removeEventListener(e.type, e.listener, e.options);
            ++i ? n.length = i : delete this.__on
        }
    }
}

function ct(t, n, e) {
    return function() {
        var r, i = this.__on,
            o = function(t) {
                return function(n) {
                    t.call(this, n, this.__data__)
                }
            }(n);
        if (i)
            for (var u = 0, a = i.length; u < a; ++u)
                if ((r = i[u]).type === t.type && r.name === t.name) return this.removeEventListener(r.type, r.listener, r.options), this.addEventListener(r.type, r.listener = o, r.options = e), void(r.value = n);
        this.addEventListener(t.type, o, e), r = {
            type: t.type,
            name: t.name,
            value: n,
            listener: o,
            options: e
        }, i ? i.push(r) : this.__on = [r]
    }
}

function ht(t, n, e) {
    var r = T(t),
        i = r.CustomEvent;
    "function" == typeof i ? i = new i(n, e) : (i = r.document.createEvent("Event"), e ? (i.initEvent(n, e.bubbles, e.cancelable), i.detail = e.detail) : i.initEvent(n, !1, !1)), t.dispatchEvent(i)
}

function ft(t, n) {
    return function() {
        return ht(this, t, n)
    }
}

function pt(t, n) {
    return function() {
        return ht(this, t, n.apply(this, arguments))
    }
}
k.prototype = {
    constructor: k,
    appendChild: function(t) {
        return this._parent.insertBefore(t, this._next)
    },
    insertBefore: function(t, n) {
        return this._parent.insertBefore(t, n)
    },
    querySelector: function(t) {
        return this._parent.querySelector(t)
    },
    querySelectorAll: function(t) {
        return this._parent.querySelectorAll(t)
    }
}, V.prototype = {
    add: function(t) {
        this._names.indexOf(t) < 0 && (this._names.push(t), this._node.setAttribute("class", this._names.join(" ")))
    },
    remove: function(t) {
        var n = this._names.indexOf(t);
        n >= 0 && (this._names.splice(n, 1), this._node.setAttribute("class", this._names.join(" ")))
    },
    contains: function(t) {
        return this._names.indexOf(t) >= 0
    }
};
var dt = [null];

function yt(t, n) {
    this._groups = t, this._parents = n
}

function _t() {
    return new yt([
        [document.documentElement]
    ], dt)
}

function vt(t) {
    return "string" == typeof t ? new yt([
        [document.querySelector(t)]
    ], [document.documentElement]) : new yt([
        [t]
    ], dt)
}

function gt(t, n, e) {
    t.prototype = n.prototype = e, e.constructor = t
}

function mt(t, n) {
    var e = Object.create(t.prototype);
    for (var r in n) e[r] = n[r];
    return e
}

function wt() {}
yt.prototype = _t.prototype = {
    constructor: yt,
    select: function(t) {
        "function" != typeof t && (t = f(t));
        for (var n = this._groups, e = n.length, r = new Array(e), i = 0; i < e; ++i)
            for (var o, u, a = n[i], s = a.length, l = r[i] = new Array(s), c = 0; c < s; ++c)(o = a[c]) && (u = t.call(o, o.__data__, c, a)) && ("__data__" in o && (u.__data__ = o.__data__), l[c] = u);
        return new yt(r, this._parents)
    },
    selectAll: function(t) {
        t = "function" == typeof t ? y(t) : d(t);
        for (var n = this._groups, e = n.length, r = [], i = [], o = 0; o < e; ++o)
            for (var u, a = n[o], s = a.length, l = 0; l < s; ++l)(u = a[l]) && (r.push(t.call(u, u.__data__, l, a)), i.push(u));
        return new yt(r, i)
    },
    selectChild: function(t) {
        return this.select(null == t ? m : function(t) {
            return function() {
                return g.call(this.children, t)
            }
        }("function" == typeof t ? t : v(t)))
    },
    selectChildren: function(t) {
        return this.selectAll(null == t ? b : function(t) {
            return function() {
                return w.call(this.children, t)
            }
        }("function" == typeof t ? t : v(t)))
    },
    filter: function(t) {
        "function" != typeof t && (t = _(t));
        for (var n = this._groups, e = n.length, r = new Array(e), i = 0; i < e; ++i)
            for (var o, u = n[i], a = u.length, s = r[i] = [], l = 0; l < a; ++l)(o = u[l]) && t.call(o, o.__data__, l, u) && s.push(o);
        return new yt(r, this._parents)
    },
    data: function(t, n) {
        if (!arguments.length) return Array.from(this, $);
        var e, r = n ? N : A,
            i = this._parents,
            o = this._groups;
        "function" != typeof t && (e = t, t = function() {
            return e
        });
        for (var u = o.length, a = new Array(u), s = new Array(u), l = new Array(u), c = 0; c < u; ++c) {
            var h = i[c],
                f = o[c],
                p = f.length,
                d = E(t.call(h, h && h.__data__, c, i)),
                y = d.length,
                _ = s[c] = new Array(y),
                v = a[c] = new Array(y);
            r(h, f, _, v, l[c] = new Array(p), d, n);
            for (var g, m, w = 0, b = 0; w < y; ++w)
                if (g = _[w]) {
                    for (w >= b && (b = w + 1); !(m = v[b]) && ++b < y;);
                    g._next = m || null
                }
        }
        return (a = new yt(a, i))._enter = s, a._exit = l, a
    },
    enter: function() {
        return new yt(this._enter || this._groups.map(x), this._parents)
    },
    exit: function() {
        return new yt(this._exit || this._groups.map(x), this._parents)
    },
    join: function(t, n, e) {
        var r = this.enter(),
            i = this,
            o = this.exit();
        return "function" == typeof t ? (r = t(r)) && (r = r.selection()) : r = r.append(t + ""), null != n && (i = n(i)) && (i = i.selection()), null == e ? o.remove() : e(o), r && i ? r.merge(i).order() : i
    },
    merge: function(t) {
        for (var n = t.selection ? t.selection() : t, e = this._groups, r = n._groups, i = e.length, o = r.length, u = Math.min(i, o), a = new Array(i), s = 0; s < u; ++s)
            for (var l, c = e[s], h = r[s], f = c.length, p = a[s] = new Array(f), d = 0; d < f; ++d)(l = c[d] || h[d]) && (p[d] = l);
        for (; s < i; ++s) a[s] = e[s];
        return new yt(a, this._parents)
    },
    selection: function() {
        return this
    },
    order: function() {
        for (var t = this._groups, n = -1, e = t.length; ++n < e;)
            for (var r, i = t[n], o = i.length - 1, u = i[o]; --o >= 0;)(r = i[o]) && (u && 4 ^ r.compareDocumentPosition(u) && u.parentNode.insertBefore(r, u), u = r);
        return this
    },
    sort: function(t) {
        function n(n, e) {
            return n && e ? t(n.__data__, e.__data__) : !n - !e
        }
        t || (t = M);
        for (var e = this._groups, r = e.length, i = new Array(r), o = 0; o < r; ++o) {
            for (var u, a = e[o], s = a.length, l = i[o] = new Array(s), c = 0; c < s; ++c)(u = a[c]) && (l[c] = u);
            l.sort(n)
        }
        return new yt(i, this._parents).order()
    },
    call: function() {
        var t = arguments[0];
        return arguments[0] = this, t.apply(null, arguments), this
    },
    nodes: function() {
        return Array.from(this)
    },
    node: function() {
        for (var t = this._groups, n = 0, e = t.length; n < e; ++n)
            for (var r = t[n], i = 0, o = r.length; i < o; ++i) {
                var u = r[i];
                if (u) return u
            }
        return null
    },
    size: function() {
        let t = 0;
        for (const n of this) ++t;
        return t
    },
    empty: function() {
        return !this.node()
    },
    each: function(t) {
        for (var n = this._groups, e = 0, r = n.length; e < r; ++e)
            for (var i, o = n[e], u = 0, a = o.length; u < a; ++u)(i = o[u]) && t.call(i, i.__data__, u, o);
        return this
    },
    attr: function(t, n) {
        var e = a(t);
        if (arguments.length < 2) {
            var r = this.node();
            return e.local ? r.getAttributeNS(e.space, e.local) : r.getAttribute(e)
        }
        return this.each((null == n ? e.local ? C : S : "function" == typeof n ? e.local ? O : P : e.local ? X : q)(e, n))
    },
    style: function(t, n, e) {
        return arguments.length > 1 ? this.each((null == n ? R : "function" == typeof n ? j : H)(t, n, null == e ? "" : e)) : I(this.node(), t)
    },
    property: function(t, n) {
        return arguments.length > 1 ? this.each((null == n ? Y : "function" == typeof n ? D : L)(t, n)) : this.node()[t]
    },
    classed: function(t, n) {
        var e = B(t + "");
        if (arguments.length < 2) {
            for (var r = z(this.node()), i = -1, o = e.length; ++i < o;)
                if (!r.contains(e[i])) return !1;
            return !0
        }
        return this.each(("function" == typeof n ? G : n ? K : W)(e, n))
    },
    text: function(t) {
        return arguments.length ? this.each(null == t ? J : ("function" == typeof t ? Z : Q)(t)) : this.node().textContent
    },
    html: function(t) {
        return arguments.length ? this.each(null == t ? tt : ("function" == typeof t ? et : nt)(t)) : this.node().innerHTML
    },
    raise: function() {
        return this.each(rt)
    },
    lower: function() {
        return this.each(it)
    },
    append: function(t) {
        var n = "function" == typeof t ? t : c(t);
        return this.select((function() {
            return this.appendChild(n.apply(this, arguments))
        }))
    },
    insert: function(t, n) {
        var e = "function" == typeof t ? t : c(t),
            r = null == n ? ot : "function" == typeof n ? n : f(n);
        return this.select((function() {
            return this.insertBefore(e.apply(this, arguments), r.apply(this, arguments) || null)
        }))
    },
    remove: function() {
        return this.each(ut)
    },
    clone: function(t) {
        return this.select(t ? st : at)
    },
    datum: function(t) {
        return arguments.length ? this.property("__data__", t) : this.node().__data__
    },
    on: function(t, n, e) {
        var r, i, o = function(t) {
                return t.trim().split(/^|\s+/).map((function(t) {
                    var n = "",
                        e = t.indexOf(".");
                    return e >= 0 && (n = t.slice(e + 1), t = t.slice(0, e)), {
                        type: t,
                        name: n
                    }
                }))
            }(t + ""),
            u = o.length;
        if (!(arguments.length < 2)) {
            for (a = n ? ct : lt, r = 0; r < u; ++r) this.each(a(o[r], n, e));
            return this
        }
        var a = this.node().__on;
        if (a)
            for (var s, l = 0, c = a.length; l < c; ++l)
                for (r = 0, s = a[l]; r < u; ++r)
                    if ((i = o[r]).type === s.type && i.name === s.name) return s.value
    },
    dispatch: function(t, n) {
        return this.each(("function" == typeof n ? pt : ft)(t, n))
    },
    [Symbol.iterator]: function*() {
        for (var t = this._groups, n = 0, e = t.length; n < e; ++n)
            for (var r, i = t[n], o = 0, u = i.length; o < u; ++o)(r = i[o]) && (yield r)
    }
};
var bt = .7,
    xt = 1 / bt,
    kt = "\\s*([+-]?\\d+)\\s*",
    At = "\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",
    Nt = "\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",
    $t = /^#([0-9a-f]{3,8})$/,
    Et = new RegExp(`^rgb\\(${kt},${kt},${kt}\\)$`),
    Mt = new RegExp(`^rgb\\(${Nt},${Nt},${Nt}\\)$`),
    St = new RegExp(`^rgba\\(${kt},${kt},${kt},${At}\\)$`),
    Ct = new RegExp(`^rgba\\(${Nt},${Nt},${Nt},${At}\\)$`),
    qt = new RegExp(`^hsl\\(${At},${Nt},${Nt}\\)$`),
    Xt = new RegExp(`^hsla\\(${At},${Nt},${Nt},${At}\\)$`),
    Pt = {
        aliceblue: 15792383,
        antiquewhite: 16444375,
        aqua: 65535,
        aquamarine: 8388564,
        azure: 15794175,
        beige: 16119260,
        bisque: 16770244,
        black: 0,
        blanchedalmond: 16772045,
        blue: 255,
        blueviolet: 9055202,
        brown: 10824234,
        burlywood: 14596231,
        cadetblue: 6266528,
        chartreuse: 8388352,
        chocolate: 13789470,
        coral: 16744272,
        cornflowerblue: 6591981,
        cornsilk: 16775388,
        crimson: 14423100,
        cyan: 65535,
        darkblue: 139,
        darkcyan: 35723,
        darkgoldenrod: 12092939,
        darkgray: 11119017,
        darkgreen: 25600,
        darkgrey: 11119017,
        darkkhaki: 12433259,
        darkmagenta: 9109643,
        darkolivegreen: 5597999,
        darkorange: 16747520,
        darkorchid: 10040012,
        darkred: 9109504,
        darksalmon: 15308410,
        darkseagreen: 9419919,
        darkslateblue: 4734347,
        darkslategray: 3100495,
        darkslategrey: 3100495,
        darkturquoise: 52945,
        darkviolet: 9699539,
        deeppink: 16716947,
        deepskyblue: 49151,
        dimgray: 6908265,
        dimgrey: 6908265,
        dodgerblue: 2003199,
        firebrick: 11674146,
        floralwhite: 16775920,
        forestgreen: 2263842,
        fuchsia: 16711935,
        gainsboro: 14474460,
        ghostwhite: 16316671,
        gold: 16766720,
        goldenrod: 14329120,
        gray: 8421504,
        green: 32768,
        greenyellow: 11403055,
        grey: 8421504,
        honeydew: 15794160,
        hotpink: 16738740,
        indianred: 13458524,
        indigo: 4915330,
        ivory: 16777200,
        khaki: 15787660,
        lavender: 15132410,
        lavenderblush: 16773365,
        lawngreen: 8190976,
        lemonchiffon: 16775885,
        lightblue: 11393254,
        lightcoral: 15761536,
        lightcyan: 14745599,
        lightgoldenrodyellow: 16448210,
        lightgray: 13882323,
        lightgreen: 9498256,
        lightgrey: 13882323,
        lightpink: 16758465,
        lightsalmon: 16752762,
        lightseagreen: 2142890,
        lightskyblue: 8900346,
        lightslategray: 7833753,
        lightslategrey: 7833753,
        lightsteelblue: 11584734,
        lightyellow: 16777184,
        lime: 65280,
        limegreen: 3329330,
        linen: 16445670,
        magenta: 16711935,
        maroon: 8388608,
        mediumaquamarine: 6737322,
        mediumblue: 205,
        mediumorchid: 12211667,
        mediumpurple: 9662683,
        mediumseagreen: 3978097,
        mediumslateblue: 8087790,
        mediumspringgreen: 64154,
        mediumturquoise: 4772300,
        mediumvioletred: 13047173,
        midnightblue: 1644912,
        mintcream: 16121850,
        mistyrose: 16770273,
        moccasin: 16770229,
        navajowhite: 16768685,
        navy: 128,
        oldlace: 16643558,
        olive: 8421376,
        olivedrab: 7048739,
        orange: 16753920,
        orangered: 16729344,
        orchid: 14315734,
        palegoldenrod: 15657130,
        palegreen: 10025880,
        paleturquoise: 11529966,
        palevioletred: 14381203,
        papayawhip: 16773077,
        peachpuff: 16767673,
        peru: 13468991,
        pink: 16761035,
        plum: 14524637,
        powderblue: 11591910,
        purple: 8388736,
        rebeccapurple: 6697881,
        red: 16711680,
        rosybrown: 12357519,
        royalblue: 4286945,
        saddlebrown: 9127187,
        salmon: 16416882,
        sandybrown: 16032864,
        seagreen: 3050327,
        seashell: 16774638,
        sienna: 10506797,
        silver: 12632256,
        skyblue: 8900331,
        slateblue: 6970061,
        slategray: 7372944,
        slategrey: 7372944,
        snow: 16775930,
        springgreen: 65407,
        steelblue: 4620980,
        tan: 13808780,
        teal: 32896,
        thistle: 14204888,
        tomato: 16737095,
        turquoise: 4251856,
        violet: 15631086,
        wheat: 16113331,
        white: 16777215,
        whitesmoke: 16119285,
        yellow: 16776960,
        yellowgreen: 10145074
    };

function Ot() {
    return this.rgb().formatHex()
}

function Tt() {
    return this.rgb().formatRgb()
}

function Rt(t) {
    var n, e;
    return t = (t + "").trim().toLowerCase(), (n = $t.exec(t)) ? (e = n[1].length, n = parseInt(n[1], 16), 6 === e ? Ht(n) : 3 === e ? new Lt(n >> 8 & 15 | n >> 4 & 240, n >> 4 & 15 | 240 & n, (15 & n) << 4 | 15 & n, 1) : 8 === e ? jt(n >> 24 & 255, n >> 16 & 255, n >> 8 & 255, (255 & n) / 255) : 4 === e ? jt(n >> 12 & 15 | n >> 8 & 240, n >> 8 & 15 | n >> 4 & 240, n >> 4 & 15 | 240 & n, ((15 & n) << 4 | 15 & n) / 255) : null) : (n = Et.exec(t)) ? new Lt(n[1], n[2], n[3], 1) : (n = Mt.exec(t)) ? new Lt(255 * n[1] / 100, 255 * n[2] / 100, 255 * n[3] / 100, 1) : (n = St.exec(t)) ? jt(n[1], n[2], n[3], n[4]) : (n = Ct.exec(t)) ? jt(255 * n[1] / 100, 255 * n[2] / 100, 255 * n[3] / 100, n[4]) : (n = qt.exec(t)) ? Ft(n[1], n[2] / 100, n[3] / 100, 1) : (n = Xt.exec(t)) ? Ft(n[1], n[2] / 100, n[3] / 100, n[4]) : Pt.hasOwnProperty(t) ? Ht(Pt[t]) : "transparent" === t ? new Lt(NaN, NaN, NaN, 0) : null
}

function Ht(t) {
    return new Lt(t >> 16 & 255, t >> 8 & 255, 255 & t, 1)
}

function jt(t, n, e, r) {
    return r <= 0 && (t = n = e = NaN), new Lt(t, n, e, r)
}

function It(t) {
    return t instanceof wt || (t = Rt(t)), t ? new Lt((t = t.rgb()).r, t.g, t.b, t.opacity) : new Lt
}

function Yt(t, n, e, r) {
    return 1 === arguments.length ? It(t) : new Lt(t, n, e, null == r ? 1 : r)
}

function Lt(t, n, e, r) {
    this.r = +t, this.g = +n, this.b = +e, this.opacity = +r
}

function Dt() {
    return `#${Ut(this.r)}${Ut(this.g)}${Ut(this.b)}`
}

function Bt() {
    const t = zt(this.opacity);
    return `${1===t?"rgb(":"rgba("}${Vt(this.r)}, ${Vt(this.g)}, ${Vt(this.b)}${1===t?")":`, ${t})`}`
}

function zt(t) {
    return isNaN(t) ? 1 : Math.max(0, Math.min(1, t))
}

function Vt(t) {
    return Math.max(0, Math.min(255, Math.round(t) || 0))
}

function Ut(t) {
    return ((t = Vt(t)) < 16 ? "0" : "") + t.toString(16)
}

function Ft(t, n, e, r) {
    return r <= 0 ? t = n = e = NaN : e <= 0 || e >= 1 ? t = n = NaN : n <= 0 && (t = NaN), new Wt(t, n, e, r)
}

function Kt(t) {
    if (t instanceof Wt) return new Wt(t.h, t.s, t.l, t.opacity);
    if (t instanceof wt || (t = Rt(t)), !t) return new Wt;
    if (t instanceof Wt) return t;
    var n = (t = t.rgb()).r / 255,
        e = t.g / 255,
        r = t.b / 255,
        i = Math.min(n, e, r),
        o = Math.max(n, e, r),
        u = NaN,
        a = o - i,
        s = (o + i) / 2;
    return a ? (u = n === o ? (e - r) / a + 6 * (e < r) : e === o ? (r - n) / a + 2 : (n - e) / a + 4, a /= s < .5 ? o + i : 2 - o - i, u *= 60) : a = s > 0 && s < 1 ? 0 : u, new Wt(u, a, s, t.opacity)
}

function Wt(t, n, e, r) {
    this.h = +t, this.s = +n, this.l = +e, this.opacity = +r
}

function Gt(t) {
    return (t = (t || 0) % 360) < 0 ? t + 360 : t
}

function Jt(t) {
    return Math.max(0, Math.min(1, t || 0))
}

function Qt(t, n, e) {
    return 255 * (t < 60 ? n + (e - n) * t / 60 : t < 180 ? e : t < 240 ? n + (e - n) * (240 - t) / 60 : n)
}
gt(wt, Rt, {
    copy(t) {
        return Object.assign(new this.constructor, this, t)
    },
    displayable() {
        return this.rgb().displayable()
    },
    hex: Ot,
    formatHex: Ot,
    formatHex8: function() {
        return this.rgb().formatHex8()
    },
    formatHsl: function() {
        return Kt(this).formatHsl()
    },
    formatRgb: Tt,
    toString: Tt
}), gt(Lt, Yt, mt(wt, {
    brighter(t) {
        return t = null == t ? xt : Math.pow(xt, t), new Lt(this.r * t, this.g * t, this.b * t, this.opacity)
    },
    darker(t) {
        return t = null == t ? bt : Math.pow(bt, t), new Lt(this.r * t, this.g * t, this.b * t, this.opacity)
    },
    rgb() {
        return this
    },
    clamp() {
        return new Lt(Vt(this.r), Vt(this.g), Vt(this.b), zt(this.opacity))
    },
    displayable() {
        return -.5 <= this.r && this.r < 255.5 && -.5 <= this.g && this.g < 255.5 && -.5 <= this.b && this.b < 255.5 && 0 <= this.opacity && this.opacity <= 1
    },
    hex: Dt,
    formatHex: Dt,
    formatHex8: function() {
        return `#${Ut(this.r)}${Ut(this.g)}${Ut(this.b)}${Ut(255*(isNaN(this.opacity)?1:this.opacity))}`
    },
    formatRgb: Bt,
    toString: Bt
})), gt(Wt, (function(t, n, e, r) {
    return 1 === arguments.length ? Kt(t) : new Wt(t, n, e, null == r ? 1 : r)
}), mt(wt, {
    brighter(t) {
        return t = null == t ? xt : Math.pow(xt, t), new Wt(this.h, this.s, this.l * t, this.opacity)
    },
    darker(t) {
        return t = null == t ? bt : Math.pow(bt, t), new Wt(this.h, this.s, this.l * t, this.opacity)
    },
    rgb() {
        var t = this.h % 360 + 360 * (this.h < 0),
            n = isNaN(t) || isNaN(this.s) ? 0 : this.s,
            e = this.l,
            r = e + (e < .5 ? e : 1 - e) * n,
            i = 2 * e - r;
        return new Lt(Qt(t >= 240 ? t - 240 : t + 120, i, r), Qt(t, i, r), Qt(t < 120 ? t + 240 : t - 120, i, r), this.opacity)
    },
    clamp() {
        return new Wt(Gt(this.h), Jt(this.s), Jt(this.l), zt(this.opacity))
    },
    displayable() {
        return (0 <= this.s && this.s <= 1 || isNaN(this.s)) && 0 <= this.l && this.l <= 1 && 0 <= this.opacity && this.opacity <= 1
    },
    formatHsl() {
        const t = zt(this.opacity);
        return `${1===t?"hsl(":"hsla("}${Gt(this.h)}, ${100*Jt(this.s)}%, ${100*Jt(this.l)}%${1===t?")":`, ${t})`}`
    }
}));
const Zt = t => () => t;

function tn(t, n) {
    return function(e) {
        return t + e * n
    }
}

function nn(t, n) {
    var e = n - t;
    return e ? tn(t, e > 180 || e < -180 ? e - 360 * Math.round(e / 360) : e) : Zt(isNaN(t) ? n : t)
}

function en(t) {
    return 1 == (t = +t) ? rn : function(n, e) {
        return e - n ? function(t, n, e) {
            return t = Math.pow(t, e), n = Math.pow(n, e) - t, e = 1 / e,
                function(r) {
                    return Math.pow(t + r * n, e)
                }
        }(n, e, t) : Zt(isNaN(n) ? e : n)
    }
}

function rn(t, n) {
    var e = n - t;
    return e ? tn(t, e) : Zt(isNaN(t) ? n : t)
}
const on = function t(n) {
    var e = en(n);

    function r(t, n) {
        var r = e((t = Yt(t)).r, (n = Yt(n)).r),
            i = e(t.g, n.g),
            o = e(t.b, n.b),
            u = rn(t.opacity, n.opacity);
        return function(n) {
            return t.r = r(n), t.g = i(n), t.b = o(n), t.opacity = u(n), t + ""
        }
    }
    return r.gamma = t, r
}(1);

function un(t, n) {
    return t = +t, n = +n,
        function(e) {
            return t * (1 - e) + n * e
        }
}
var an = /[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,
    sn = new RegExp(an.source, "g");

function ln(t, n) {
    var e, r, i, o = an.lastIndex = sn.lastIndex = 0,
        u = -1,
        a = [],
        s = [];
    for (t += "", n += "";
        (e = an.exec(t)) && (r = sn.exec(n));)(i = r.index) > o && (i = n.slice(o, i), a[u] ? a[u] += i : a[++u] = i), (e = e[0]) === (r = r[0]) ? a[u] ? a[u] += r : a[++u] = r : (a[++u] = null, s.push({
        i: u,
        x: un(e, r)
    })), o = sn.lastIndex;
    return o < n.length && (i = n.slice(o), a[u] ? a[u] += i : a[++u] = i), a.length < 2 ? s[0] ? function(t) {
        return function(n) {
            return t(n) + ""
        }
    }(s[0].x) : function(t) {
        return function() {
            return t
        }
    }(n) : (n = s.length, function(t) {
        for (var e, r = 0; r < n; ++r) a[(e = s[r]).i] = e.x(t);
        return a.join("")
    })
}
var cn, hn = 180 / Math.PI,
    fn = {
        translateX: 0,
        translateY: 0,
        rotate: 0,
        skewX: 0,
        scaleX: 1,
        scaleY: 1
    };

function pn(t, n, e, r, i, o) {
    var u, a, s;
    return (u = Math.sqrt(t * t + n * n)) && (t /= u, n /= u), (s = t * e + n * r) && (e -= t * s, r -= n * s), (a = Math.sqrt(e * e + r * r)) && (e /= a, r /= a, s /= a), t * r < n * e && (t = -t, n = -n, s = -s, u = -u), {
        translateX: i,
        translateY: o,
        rotate: Math.atan2(n, t) * hn,
        skewX: Math.atan(s) * hn,
        scaleX: u,
        scaleY: a
    }
}

function dn(t, n, e, r) {
    function i(t) {
        return t.length ? t.pop() + " " : ""
    }
    return function(o, u) {
        var a = [],
            s = [];
        return o = t(o), u = t(u),
            function(t, r, i, o, u, a) {
                if (t !== i || r !== o) {
                    var s = u.push("translate(", null, n, null, e);
                    a.push({
                        i: s - 4,
                        x: un(t, i)
                    }, {
                        i: s - 2,
                        x: un(r, o)
                    })
                } else(i || o) && u.push("translate(" + i + n + o + e)
            }(o.translateX, o.translateY, u.translateX, u.translateY, a, s),
            function(t, n, e, o) {
                t !== n ? (t - n > 180 ? n += 360 : n - t > 180 && (t += 360), o.push({
                    i: e.push(i(e) + "rotate(", null, r) - 2,
                    x: un(t, n)
                })) : n && e.push(i(e) + "rotate(" + n + r)
            }(o.rotate, u.rotate, a, s),
            function(t, n, e, o) {
                t !== n ? o.push({
                    i: e.push(i(e) + "skewX(", null, r) - 2,
                    x: un(t, n)
                }) : n && e.push(i(e) + "skewX(" + n + r)
            }(o.skewX, u.skewX, a, s),
            function(t, n, e, r, o, u) {
                if (t !== e || n !== r) {
                    var a = o.push(i(o) + "scale(", null, ",", null, ")");
                    u.push({
                        i: a - 4,
                        x: un(t, e)
                    }, {
                        i: a - 2,
                        x: un(n, r)
                    })
                } else 1 === e && 1 === r || o.push(i(o) + "scale(" + e + "," + r + ")")
            }(o.scaleX, o.scaleY, u.scaleX, u.scaleY, a, s), o = u = null,
            function(t) {
                for (var n, e = -1, r = s.length; ++e < r;) a[(n = s[e]).i] = n.x(t);
                return a.join("")
            }
    }
}
var yn, _n, vn = dn((function(t) {
        const n = new("function" == typeof DOMMatrix ? DOMMatrix : WebKitCSSMatrix)(t + "");
        return n.isIdentity ? fn : pn(n.a, n.b, n.c, n.d, n.e, n.f)
    }), "px, ", "px)", "deg)"),
    gn = dn((function(t) {
        return null == t ? fn : (cn || (cn = document.createElementNS("http://www.w3.org/2000/svg", "g")), cn.setAttribute("transform", t), (t = cn.transform.baseVal.consolidate()) ? pn((t = t.matrix).a, t.b, t.c, t.d, t.e, t.f) : fn)
    }), ", ", ")", ")"),
    mn = 0,
    wn = 0,
    bn = 0,
    xn = 0,
    kn = 0,
    An = 0,
    Nn = "object" == typeof performance && performance.now ? performance : Date,
    $n = "object" == typeof window && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function(t) {
        setTimeout(t, 17)
    };

function En() {
    return kn || ($n(Mn), kn = Nn.now() + An)
}

function Mn() {
    kn = 0
}

function Sn() {
    this._call = this._time = this._next = null
}

function Cn(t, n, e) {
    var r = new Sn;
    return r.restart(t, n, e), r
}

function qn() {
    kn = (xn = Nn.now()) + An, mn = wn = 0;
    try {
        ! function() {
            En(), ++mn;
            for (var t, n = yn; n;)(t = kn - n._time) >= 0 && n._call.call(void 0, t), n = n._next;
            --mn
        }()
    } finally {
        mn = 0,
            function() {
                var t, n, e = yn,
                    r = 1 / 0;
                for (; e;) e._call ? (r > e._time && (r = e._time), t = e, e = e._next) : (n = e._next, e._next = null, e = t ? t._next = n : yn = n);
                _n = t, Pn(r)
            }(), kn = 0
    }
}

function Xn() {
    var t = Nn.now(),
        n = t - xn;
    n > 1e3 && (An -= n, xn = t)
}

function Pn(t) {
    mn || (wn && (wn = clearTimeout(wn)), t - kn > 24 ? (t < 1 / 0 && (wn = setTimeout(qn, t - Nn.now() - An)), bn && (bn = clearInterval(bn))) : (bn || (xn = Nn.now(), bn = setInterval(Xn, 1e3)), mn = 1, $n(qn)))
}

function On(t, n, e) {
    var r = new Sn;
    return n = null == n ? 0 : +n, r.restart((e => {
        r.stop(), t(e + n)
    }), n, e), r
}
Sn.prototype = Cn.prototype = {
    constructor: Sn,
    restart: function(t, n, e) {
        if ("function" != typeof t) throw new TypeError("callback is not a function");
        e = (null == e ? En() : +e) + (null == n ? 0 : +n), this._next || _n === this || (_n ? _n._next = this : yn = this, _n = this), this._call = t, this._time = e, Pn()
    },
    stop: function() {
        this._call && (this._call = null, this._time = 1 / 0, Pn())
    }
};
var Tn = n("start", "end", "cancel", "interrupt"),
    Rn = [];

function Hn(t, n, e, r, i, o) {
    var u = t.__transition;
    if (u) {
        if (e in u) return
    } else t.__transition = {};
    ! function(t, n, e) {
        var r, i = t.__transition;

        function o(t) {
            e.state = 1, e.timer.restart(u, e.delay, e.time), e.delay <= t && u(t - e.delay)
        }

        function u(o) {
            var l, c, h, f;
            if (1 !== e.state) return s();
            for (l in i)
                if ((f = i[l]).name === e.name) {
                    if (3 === f.state) return On(u);
                    4 === f.state ? (f.state = 6, f.timer.stop(), f.on.call("interrupt", t, t.__data__, f.index, f.group), delete i[l]) : +l < n && (f.state = 6, f.timer.stop(), f.on.call("cancel", t, t.__data__, f.index, f.group), delete i[l])
                }
            if (On((function() {
                    3 === e.state && (e.state = 4, e.timer.restart(a, e.delay, e.time), a(o))
                })), e.state = 2, e.on.call("start", t, t.__data__, e.index, e.group), 2 === e.state) {
                for (e.state = 3, r = new Array(h = e.tween.length), l = 0, c = -1; l < h; ++l)(f = e.tween[l].value.call(t, t.__data__, e.index, e.group)) && (r[++c] = f);
                r.length = c + 1
            }
        }

        function a(n) {
            for (var i = n < e.duration ? e.ease.call(null, n / e.duration) : (e.timer.restart(s), e.state = 5, 1), o = -1, u = r.length; ++o < u;) r[o].call(t, i);
            5 === e.state && (e.on.call("end", t, t.__data__, e.index, e.group), s())
        }

        function s() {
            for (var r in e.state = 6, e.timer.stop(), delete i[n], i) return;
            delete t.__transition
        }
        i[n] = e, e.timer = Cn(o, 0, e.time)
    }(t, e, {
        name: n,
        index: r,
        group: i,
        on: Tn,
        tween: Rn,
        time: o.time,
        delay: o.delay,
        duration: o.duration,
        ease: o.ease,
        timer: null,
        state: 0
    })
}

function jn(t, n) {
    var e = Yn(t, n);
    if (e.state > 0) throw new Error("too late; already scheduled");
    return e
}

function In(t, n) {
    var e = Yn(t, n);
    if (e.state > 3) throw new Error("too late; already running");
    return e
}

function Yn(t, n) {
    var e = t.__transition;
    if (!e || !(e = e[n])) throw new Error("transition not found");
    return e
}

function Ln(t, n) {
    var e, r, i, o = t.__transition,
        u = !0;
    if (o) {
        for (i in n = null == n ? null : n + "", o)(e = o[i]).name === n ? (r = e.state > 2 && e.state < 5, e.state = 6, e.timer.stop(), e.on.call(r ? "interrupt" : "cancel", t, t.__data__, e.index, e.group), delete o[i]) : u = !1;
        u && delete t.__transition
    }
}

function Dn(t, n) {
    var e, r;
    return function() {
        var i = In(this, t),
            o = i.tween;
        if (o !== e)
            for (var u = 0, a = (r = e = o).length; u < a; ++u)
                if (r[u].name === n) {
                    (r = r.slice()).splice(u, 1);
                    break
                }
        i.tween = r
    }
}

function Bn(t, n, e) {
    var r, i;
    if ("function" != typeof e) throw new Error;
    return function() {
        var o = In(this, t),
            u = o.tween;
        if (u !== r) {
            i = (r = u).slice();
            for (var a = {
                    name: n,
                    value: e
                }, s = 0, l = i.length; s < l; ++s)
                if (i[s].name === n) {
                    i[s] = a;
                    break
                }
            s === l && i.push(a)
        }
        o.tween = i
    }
}

function zn(t, n, e) {
    var r = t._id;
    return t.each((function() {
            var t = In(this, r);
            (t.value || (t.value = {}))[n] = e.apply(this, arguments)
        })),
        function(t) {
            return Yn(t, r).value[n]
        }
}

function Vn(t, n) {
    var e;
    return ("number" == typeof n ? un : n instanceof Rt ? on : (e = Rt(n)) ? (n = e, on) : ln)(t, n)
}

function Un(t) {
    return function() {
        this.removeAttribute(t)
    }
}

function Fn(t) {
    return function() {
        this.removeAttributeNS(t.space, t.local)
    }
}

function Kn(t, n, e) {
    var r, i, o = e + "";
    return function() {
        var u = this.getAttribute(t);
        return u === o ? null : u === r ? i : i = n(r = u, e)
    }
}

function Wn(t, n, e) {
    var r, i, o = e + "";
    return function() {
        var u = this.getAttributeNS(t.space, t.local);
        return u === o ? null : u === r ? i : i = n(r = u, e)
    }
}

function Gn(t, n, e) {
    var r, i, o;
    return function() {
        var u, a, s = e(this);
        if (null != s) return (u = this.getAttribute(t)) === (a = s + "") ? null : u === r && a === i ? o : (i = a, o = n(r = u, s));
        this.removeAttribute(t)
    }
}

function Jn(t, n, e) {
    var r, i, o;
    return function() {
        var u, a, s = e(this);
        if (null != s) return (u = this.getAttributeNS(t.space, t.local)) === (a = s + "") ? null : u === r && a === i ? o : (i = a, o = n(r = u, s));
        this.removeAttributeNS(t.space, t.local)
    }
}

function Qn(t, n) {
    var e, r;

    function i() {
        var i = n.apply(this, arguments);
        return i !== r && (e = (r = i) && function(t, n) {
            return function(e) {
                this.setAttributeNS(t.space, t.local, n.call(this, e))
            }
        }(t, i)), e
    }
    return i._value = n, i
}

function Zn(t, n) {
    var e, r;

    function i() {
        var i = n.apply(this, arguments);
        return i !== r && (e = (r = i) && function(t, n) {
            return function(e) {
                this.setAttribute(t, n.call(this, e))
            }
        }(t, i)), e
    }
    return i._value = n, i
}

function te(t, n) {
    return function() {
        jn(this, t).delay = +n.apply(this, arguments)
    }
}

function ne(t, n) {
    return n = +n,
        function() {
            jn(this, t).delay = n
        }
}

function ee(t, n) {
    return function() {
        In(this, t).duration = +n.apply(this, arguments)
    }
}

function re(t, n) {
    return n = +n,
        function() {
            In(this, t).duration = n
        }
}
var ie = _t.prototype.constructor;

function oe(t) {
    return function() {
        this.style.removeProperty(t)
    }
}
var ue = 0;

function ae(t, n, e, r) {
    this._groups = t, this._parents = n, this._name = e, this._id = r
}

function se() {
    return ++ue
}
var le = _t.prototype;
ae.prototype = {
    constructor: ae,
    select: function(t) {
        var n = this._name,
            e = this._id;
        "function" != typeof t && (t = f(t));
        for (var r = this._groups, i = r.length, o = new Array(i), u = 0; u < i; ++u)
            for (var a, s, l = r[u], c = l.length, h = o[u] = new Array(c), p = 0; p < c; ++p)(a = l[p]) && (s = t.call(a, a.__data__, p, l)) && ("__data__" in a && (s.__data__ = a.__data__), h[p] = s, Hn(h[p], n, e, p, h, Yn(a, e)));
        return new ae(o, this._parents, n, e)
    },
    selectAll: function(t) {
        var n = this._name,
            e = this._id;
        "function" != typeof t && (t = d(t));
        for (var r = this._groups, i = r.length, o = [], u = [], a = 0; a < i; ++a)
            for (var s, l = r[a], c = l.length, h = 0; h < c; ++h)
                if (s = l[h]) {
                    for (var f, p = t.call(s, s.__data__, h, l), y = Yn(s, e), _ = 0, v = p.length; _ < v; ++_)(f = p[_]) && Hn(f, n, e, _, p, y);
                    o.push(p), u.push(s)
                }
        return new ae(o, u, n, e)
    },
    selectChild: le.selectChild,
    selectChildren: le.selectChildren,
    filter: function(t) {
        "function" != typeof t && (t = _(t));
        for (var n = this._groups, e = n.length, r = new Array(e), i = 0; i < e; ++i)
            for (var o, u = n[i], a = u.length, s = r[i] = [], l = 0; l < a; ++l)(o = u[l]) && t.call(o, o.__data__, l, u) && s.push(o);
        return new ae(r, this._parents, this._name, this._id)
    },
    merge: function(t) {
        if (t._id !== this._id) throw new Error;
        for (var n = this._groups, e = t._groups, r = n.length, i = e.length, o = Math.min(r, i), u = new Array(r), a = 0; a < o; ++a)
            for (var s, l = n[a], c = e[a], h = l.length, f = u[a] = new Array(h), p = 0; p < h; ++p)(s = l[p] || c[p]) && (f[p] = s);
        for (; a < r; ++a) u[a] = n[a];
        return new ae(u, this._parents, this._name, this._id)
    },
    selection: function() {
        return new ie(this._groups, this._parents)
    },
    transition: function() {
        for (var t = this._name, n = this._id, e = se(), r = this._groups, i = r.length, o = 0; o < i; ++o)
            for (var u, a = r[o], s = a.length, l = 0; l < s; ++l)
                if (u = a[l]) {
                    var c = Yn(u, n);
                    Hn(u, t, e, l, a, {
                        time: c.time + c.delay + c.duration,
                        delay: 0,
                        duration: c.duration,
                        ease: c.ease
                    })
                }
        return new ae(r, this._parents, t, e)
    },
    call: le.call,
    nodes: le.nodes,
    node: le.node,
    size: le.size,
    empty: le.empty,
    each: le.each,
    on: function(t, n) {
        var e = this._id;
        return arguments.length < 2 ? Yn(this.node(), e).on.on(t) : this.each(function(t, n, e) {
            var r, i, o = function(t) {
                return (t + "").trim().split(/^|\s+/).every((function(t) {
                    var n = t.indexOf(".");
                    return n >= 0 && (t = t.slice(0, n)), !t || "start" === t
                }))
            }(n) ? jn : In;
            return function() {
                var u = o(this, t),
                    a = u.on;
                a !== r && (i = (r = a).copy()).on(n, e), u.on = i
            }
        }(e, t, n))
    },
    attr: function(t, n) {
        var e = a(t),
            r = "transform" === e ? gn : Vn;
        return this.attrTween(t, "function" == typeof n ? (e.local ? Jn : Gn)(e, r, zn(this, "attr." + t, n)) : null == n ? (e.local ? Fn : Un)(e) : (e.local ? Wn : Kn)(e, r, n))
    },
    attrTween: function(t, n) {
        var e = "attr." + t;
        if (arguments.length < 2) return (e = this.tween(e)) && e._value;
        if (null == n) return this.tween(e, null);
        if ("function" != typeof n) throw new Error;
        var r = a(t);
        return this.tween(e, (r.local ? Qn : Zn)(r, n))
    },
    style: function(t, n, e) {
        var r = "transform" == (t += "") ? vn : Vn;
        return null == n ? this.styleTween(t, function(t, n) {
            var e, r, i;
            return function() {
                var o = I(this, t),
                    u = (this.style.removeProperty(t), I(this, t));
                return o === u ? null : o === e && u === r ? i : i = n(e = o, r = u)
            }
        }(t, r)).on("end.style." + t, oe(t)) : "function" == typeof n ? this.styleTween(t, function(t, n, e) {
            var r, i, o;
            return function() {
                var u = I(this, t),
                    a = e(this),
                    s = a + "";
                return null == a && (this.style.removeProperty(t), s = a = I(this, t)), u === s ? null : u === r && s === i ? o : (i = s, o = n(r = u, a))
            }
        }(t, r, zn(this, "style." + t, n))).each(function(t, n) {
            var e, r, i, o, u = "style." + n,
                a = "end." + u;
            return function() {
                var s = In(this, t),
                    l = s.on,
                    c = null == s.value[u] ? o || (o = oe(n)) : void 0;
                l === e && i === c || (r = (e = l).copy()).on(a, i = c), s.on = r
            }
        }(this._id, t)) : this.styleTween(t, function(t, n, e) {
            var r, i, o = e + "";
            return function() {
                var u = I(this, t);
                return u === o ? null : u === r ? i : i = n(r = u, e)
            }
        }(t, r, n), e).on("end.style." + t, null)
    },
    styleTween: function(t, n, e) {
        var r = "style." + (t += "");
        if (arguments.length < 2) return (r = this.tween(r)) && r._value;
        if (null == n) return this.tween(r, null);
        if ("function" != typeof n) throw new Error;
        return this.tween(r, function(t, n, e) {
            var r, i;

            function o() {
                var o = n.apply(this, arguments);
                return o !== i && (r = (i = o) && function(t, n, e) {
                    return function(r) {
                        this.style.setProperty(t, n.call(this, r), e)
                    }
                }(t, o, e)), r
            }
            return o._value = n, o
        }(t, n, null == e ? "" : e))
    },
    text: function(t) {
        return this.tween("text", "function" == typeof t ? function(t) {
            return function() {
                var n = t(this);
                this.textContent = null == n ? "" : n
            }
        }(zn(this, "text", t)) : function(t) {
            return function() {
                this.textContent = t
            }
        }(null == t ? "" : t + ""))
    },
    textTween: function(t) {
        var n = "text";
        if (arguments.length < 1) return (n = this.tween(n)) && n._value;
        if (null == t) return this.tween(n, null);
        if ("function" != typeof t) throw new Error;
        return this.tween(n, function(t) {
            var n, e;

            function r() {
                var r = t.apply(this, arguments);
                return r !== e && (n = (e = r) && function(t) {
                    return function(n) {
                        this.textContent = t.call(this, n)
                    }
                }(r)), n
            }
            return r._value = t, r
        }(t))
    },
    remove: function() {
        return this.on("end.remove", (t = this._id, function() {
            var n = this.parentNode;
            for (var e in this.__transition)
                if (+e !== t) return;
            n && n.removeChild(this)
        }));
        var t
    },
    tween: function(t, n) {
        var e = this._id;
        if (t += "", arguments.length < 2) {
            for (var r, i = Yn(this.node(), e).tween, o = 0, u = i.length; o < u; ++o)
                if ((r = i[o]).name === t) return r.value;
            return null
        }
        return this.each((null == n ? Dn : Bn)(e, t, n))
    },
    delay: function(t) {
        var n = this._id;
        return arguments.length ? this.each(("function" == typeof t ? te : ne)(n, t)) : Yn(this.node(), n).delay
    },
    duration: function(t) {
        var n = this._id;
        return arguments.length ? this.each(("function" == typeof t ? ee : re)(n, t)) : Yn(this.node(), n).duration
    },
    ease: function(t) {
        var n = this._id;
        return arguments.length ? this.each(function(t, n) {
            if ("function" != typeof n) throw new Error;
            return function() {
                In(this, t).ease = n
            }
        }(n, t)) : Yn(this.node(), n).ease
    },
    easeVarying: function(t) {
        if ("function" != typeof t) throw new Error;
        return this.each(function(t, n) {
            return function() {
                var e = n.apply(this, arguments);
                if ("function" != typeof e) throw new Error;
                In(this, t).ease = e
            }
        }(this._id, t))
    },
    end: function() {
        var t, n, e = this,
            r = e._id,
            i = e.size();
        return new Promise((function(o, u) {
            var a = {
                    value: u
                },
                s = {
                    value: function() {
                        0 == --i && o()
                    }
                };
            e.each((function() {
                var e = In(this, r),
                    i = e.on;
                i !== t && ((n = (t = i).copy())._.cancel.push(a), n._.interrupt.push(a), n._.end.push(s)), e.on = n
            })), 0 === i && o()
        }))
    },
    [Symbol.iterator]: le[Symbol.iterator]
};
var ce = {
    time: null,
    delay: 0,
    duration: 250,
    ease: function(t) {
        return ((t *= 2) <= 1 ? t * t * t : (t -= 2) * t * t + 2) / 2
    }
};

function he(t, n) {
    for (var e; !(e = t.__transition) || !(e = e[n]);)
        if (!(t = t.parentNode)) throw new Error(`transition ${n} not found`);
    return e
}

function fe(t, n, e) {
    this.k = t, this.x = n, this.y = e
}
_t.prototype.interrupt = function(t) {
    return this.each((function() {
        Ln(this, t)
    }))
}, _t.prototype.transition = function(t) {
    var n, e;
    t instanceof ae ? (n = t._id, t = t._name) : (n = se(), (e = ce).time = En(), t = null == t ? null : t + "");
    for (var r = this._groups, i = r.length, o = 0; o < i; ++o)
        for (var u, a = r[o], s = a.length, l = 0; l < s; ++l)(u = a[l]) && Hn(u, t, n, l, a, e || he(u, n));
    return new ae(r, this._parents, t, n)
}, fe.prototype = {
    constructor: fe,
    scale: function(t) {
        return 1 === t ? this : new fe(this.k * t, this.x, this.y)
    },
    translate: function(t, n) {
        return 0 === t & 0 === n ? this : new fe(this.k, this.x + this.k * t, this.y + this.k * n)
    },
    apply: function(t) {
        return [t[0] * this.k + this.x, t[1] * this.k + this.y]
    },
    applyX: function(t) {
        return t * this.k + this.x
    },
    applyY: function(t) {
        return t * this.k + this.y
    },
    invert: function(t) {
        return [(t[0] - this.x) / this.k, (t[1] - this.y) / this.k]
    },
    invertX: function(t) {
        return (t - this.x) / this.k
    },
    invertY: function(t) {
        return (t - this.y) / this.k
    },
    rescaleX: function(t) {
        return t.copy().domain(t.range().map(this.invertX, this).map(t.invert, t))
    },
    rescaleY: function(t) {
        return t.copy().domain(t.range().map(this.invertY, this).map(t.invert, t))
    },
    toString: function() {
        return "translate(" + this.x + "," + this.y + ") scale(" + this.k + ")"
    }
};
var pe = new fe(1, 0, 0);

function de(t) {
    for (; !t.__zoom;)
        if (!(t = t.parentNode)) return pe;
    return t.__zoom
}
de.prototype = fe.prototype;
export {
    wt as C, Lt as R, fe as T, Zt as a, on as b, Rt as c, gt as d, mt as e, ln as f, n as g, nn as h, un as i, pe as j, Ln as k, rn as n, It as r, vt as s, de as t
};