.search-sidebar[data-v-f6a230d6] {
    position: relative;
    width: 340px
}

.sidebar-content[data-v-f6a230d6] {
    background-color: #fff;
    border: 1px solid #efefef;
    border-radius: 12px;
    height: 800px;
    overflow-y: auto;
    padding: 20px
}

.execution-steps[data-v-f6a230d6] {
    margin-bottom: 30px
}

.header[data-v-f6a230d6] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 0;
    position: relative
}

.header h3[data-v-f6a230d6] {
    font-family: Arial;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    margin: 0;
    padding-left: 0;
    text-align: left
}

.header h3.mobile-title[data-v-f6a230d6] {
    flex: 1;
    padding: 0 16px;
    text-align: center
}

.close-icon-wrapper[data-v-f6a230d6] {
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    height: 20px;
    justify-content: center;
    position: absolute;
    right: 6px;
    top: 6px;
    width: 20px;
    z-index: 1
}

.close-icon[data-v-f6a230d6] {
    cursor: pointer;
    height: 16px;
    width: 16px
}

.step[data-v-f6a230d6] {
    align-items: center;
    color: #666;
    display: flex;
    margin: 10px 0
}

.step.completed[data-v-f6a230d6] {
    color: #333
}

.check-icon[data-v-f6a230d6] {
    color: #4caf50;
    margin-right: 10px
}

.search-results .result-item[data-v-f6a230d6] {
    padding: 15px 0
}

.result-link[data-v-f6a230d6] {
    color: inherit;
    display: block;
    text-decoration: none
}

.result-title[data-v-f6a230d6]:hover {
    text-decoration: underline
}

.source-info[data-v-f6a230d6] {
    align-items: center;
    display: flex;
    gap: 10px;
    margin-bottom: 8px
}

.source-icon[data-v-f6a230d6] {
    border-radius: 4px;
    height: 20px;
    width: 20px
}

.date[data-v-f6a230d6],
.source-name[data-v-f6a230d6] {
    font-family: Arial;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px
}

.date[data-v-f6a230d6] {
    color: #666
}

.result-title[data-v-f6a230d6] {
    color: #0c66cc;
    margin: 5px 0
}

.result-excerpt[data-v-f6a230d6],
.result-title[data-v-f6a230d6] {
    font-family: Arial;
    font-size: 14px;
    font-weight: 400
}

.result-excerpt[data-v-f6a230d6] {
    color: #666;
    line-height: 21px
}

.keywords-container[data-v-f6a230d6] {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin: 0
}

.keyword-tag[data-v-f6a230d6] {
    background-color: #f5f5f5;
    border-radius: 8px;
    color: #666;
    font-size: 14px;
    line-height: 1.5em;
    padding: 8px 12px;
    width: -moz-fit-content;
    width: fit-content
}

.section-divider[data-v-f6a230d6] {
    background-color: #efefef;
    height: 1px;
    margin: 20px 0
}

.search-results[data-v-f6a230d6] {
    position: relative
}

.result-item[data-v-f6a230d6] {
    min-height: 120px
}

.step[data-v-f6a230d6] svg {
    height: 12px;
    margin-right: 8px;
    width: 12px
}

.step[data-v-f6a230d6] svg circle {
    fill: #5cd4a1
}

.step[data-v-f6a230d6] svg path {
    stroke: #fff
}

.header[data-v-f6a230d6] svg {
    height: 20px;
    margin-right: 8px;
    width: 20px
}

.back-button[data-v-f6a230d6] {
    align-items: center;
    cursor: pointer;
    display: flex;
    left: 0;
    position: absolute
}

.back-button[data-v-f6a230d6] svg path {
    stroke: #333
}

.result-link.highlighted[data-v-f6a230d6] {
    background-color: #f0f7ff;
    border-radius: 8px;
    margin: -12px;
    padding: 12px
}

.close-icon-wrapper[data-v-f6a230d6] svg path {
    stroke: #232425
}

@media (max-width:1220px) {
    .search-sidebar[data-v-f6a230d6] {
        border: none;
        height: 100%;
        width: 100%
    }
    .sidebar-content[data-v-f6a230d6] {
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        border-radius: 0;
        height: 100%
    }
}

@media (prefers-color-scheme:dark) {
    .search-sidebar[data-v-f6a230d6],
    .sidebar-content[data-v-f6a230d6] {
        background-color: #232425;
        border-color: #333
    }
    .header h3[data-v-f6a230d6] {
        color: #fff
    }
    .step[data-v-f6a230d6] {
        color: #999
    }
    .close-icon[data-v-f6a230d6],
    .step.completed[data-v-f6a230d6] {
        color: #fff
    }
    .source-name[data-v-f6a230d6] {
        color: #ccc
    }
    .result-title[data-v-f6a230d6] {
        color: #5b9eff
    }
    .date[data-v-f6a230d6],
    .result-excerpt[data-v-f6a230d6] {
        color: #999
    }
    .keyword-tag[data-v-f6a230d6] {
        background-color: #2a2b2c;
        color: #999
    }
    .section-divider[data-v-f6a230d6] {
        background-color: #333
    }
    .result-link.highlighted[data-v-f6a230d6] {
        background-color: #1a2634
    }
    .back-button[data-v-f6a230d6] svg path {
        stroke: #fff
    }
    .result-link:hover .result-title[data-v-f6a230d6] {
        color: #7cb3ff
    }
    .close-icon-wrapper[data-v-f6a230d6],
    .keyword-tag[data-v-f6a230d6]:hover {
        background-color: #333435
    }
    .close-icon[data-v-f6a230d6] svg path {
        stroke: #fff
    }
    .close-icon-wrapper[data-v-f6a230d6] svg path {
        stroke: #fff !important
    }
}

.input-container[data-v-2011914b] {
    background-color: #fff;
    border-radius: 24px;
    box-shadow: 0 2px 4px #0000001a;
    height: 100%;
    justify-content: space-between;
    width: 100%
}

.icon-button[data-v-2011914b],
.input-container[data-v-2011914b] {
    align-items: center;
    display: flex
}

.icon-button[data-v-2011914b] {
    background-color: #f0f0f0;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    height: 48px;
    justify-content: center;
    margin: 16px;
    width: 48px
}

.icon-keyboard[data-v-2011914b]:before {
    content: "⌨️";
    font-size: 24px
}

.icon-microphone[data-v-2011914b]:before {
    content: "🎤";
    font-size: 24px
}

.input-field[data-v-2011914b] {
    align-items: center;
    display: flex;
    flex-grow: 1;
    justify-content: center;
    text-align: center
}

.dots-container[data-v-2011914b] {
    align-items: flex-end;
    display: flex;
    gap: 8px;
    height: 32px
}

.dots-container.mic-disabled[data-v-2011914b] {
    align-items: center
}

.dot[data-v-2011914b] {
    background-color: #4285f4;
    border-radius: 41.48px;
    transition: height .1s ease;
    width: 4.74px
}

.dot.remote-active[data-v-2011914b] {
    background-color: #34a853
}

.dot.mic-disabled[data-v-2011914b] {
    align-self: flex-center;
    background-color: #ea4335;
    height: 4.74px !important
}

.dots-container.connecting .dot[data-v-2011914b] {
    background-color: #9aa0a6
}

.dots-container.connecting .dot.active-dot[data-v-2011914b] {
    background-color: #4285f4
}

.mic-button[data-v-2011914b] {
    opacity: .5;
    transition: all .3s ease
}

.mic-button.mic-enabled[data-v-2011914b] {
    opacity: 1
}

.muted.dots-container[data-v-2011914b] {
    align-items: center
}

.settings-button[data-v-2011914b] {
    background-color: #f0f0f0;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    height: 48px;
    right: 16px;
    top: 16px;
    width: 48px;
    z-index: 100
}

.settings-button[data-v-2011914b],
.settings-modal[data-v-2011914b] {
    align-items: center;
    display: flex;
    justify-content: center;
    position: fixed
}

.settings-modal[data-v-2011914b] {
    background-color: #00000080;
    height: 100%;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 1001
}

.settings-modal-content[data-v-2011914b] {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px #00000026;
    max-width: 90%;
    padding: 24px;
    width: 320px
}

.settings-modal h3[data-v-2011914b] {
    color: #202124;
    margin-bottom: 16px;
    margin-top: 0
}

.settings-modal h4[data-v-2011914b] {
    color: #5f6368;
    margin-bottom: 8px;
    margin-top: 0
}

.voice-options[data-v-2011914b] {
    margin-bottom: 20px
}

.voice-option[data-v-2011914b] {
    align-items: center;
    display: flex;
    margin-bottom: 8px
}

.voice-option input[type=radio][data-v-2011914b] {
    margin-right: 8px
}

.instant-mode-option[data-v-2011914b] {
    align-items: center;
    display: flex;
    margin-bottom: 24px
}

.instant-mode-option input[type=checkbox][data-v-2011914b] {
    margin-right: 8px
}

.settings-buttons[data-v-2011914b] {
    display: flex;
    gap: 12px;
    justify-content: flex-end
}

.settings-buttons button[data-v-2011914b] {
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    padding: 8px 16px
}

.settings-buttons button[data-v-2011914b]:first-child {
    background-color: #4285f4;
    color: #fff
}

.settings-buttons button[data-v-2011914b]:last-child {
    background-color: #f1f3f4;
    color: #5f6368
}

.search-status-top-bar[data-v-2011914b] {
    width: 100%
}

.realtime-cover[data-v-7353c748] {
    background-color: #fff;
    overflow: hidden
}

.particle-canvas[data-v-7353c748],
.realtime-cover[data-v-7353c748] {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.particle-canvas[data-v-7353c748] {
    z-index: 0
}

.media-container[data-v-7353c748] {
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    background-color: #fff9;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    transition: opacity .5s ease;
    width: 100%;
    z-index: 1
}

.media-container[data-v-7353c748],
.media-wrapper[data-v-7353c748] {
    align-items: center;
    display: flex;
    justify-content: center
}

.media-wrapper[data-v-7353c748] {
    border-radius: 12px;
    height: 75%;
    overflow: hidden;
    position: relative;
    width: 75%
}

.media-wrapper[data-v-7353c748]:before {
    border-radius: 12px;
    bottom: 0;
    box-shadow: inset 0 0 80px 40px #fffffff2;
    content: "";
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 2
}

.media-content[data-v-7353c748] {
    border-radius: 8px;
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, .15)) blur(1px);
    max-height: 90%;
    max-width: 90%;
    -o-object-fit: contain;
    object-fit: contain
}

@keyframes pulse-7353c748 {
    0% {
        box-shadow: inset 0 0 80px 40px #fffffff2
    }
    50% {
        box-shadow: inset 0 0 100px 60px #fffffffa
    }
    to {
        box-shadow: inset 0 0 80px 40px #fffffff2
    }
}

.media-wrapper[data-v-7353c748]:before {
    animation: pulse-7353c748 6s ease-in-out infinite
}

.user .content[data-v-3bbf2a2c] pre {
    background: none;
    margin: 0;
    padding: 0
}

.user .content[data-v-3bbf2a2c] pre code {
    font-family: sans-serif;
    white-space: pre-wrap
}

.conversation-content[data-v-3bbf2a2c] {
    background: transparent;
    color: #666;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding-bottom: 180px;
    position: relative
}

.conversation .role[data-v-3bbf2a2c] {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start
}

.conversation .avatar[data-v-3bbf2a2c] {
    height: 36px;
    width: 36px
}

.conversation .avatar img[data-v-3bbf2a2c] {
    width: 100%
}

.conversation .name[data-v-3bbf2a2c] {
    color: #252525;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal
}

.conversation-item-desc .bubble[data-v-3bbf2a2c] {
    align-items: flex-start;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    gap: 10px;
    justify-content: flex-start;
    line-height: 20px;
    padding: 12px 16px
}

.conversation-item-desc .bubble code[data-v-3bbf2a2c],
.conversation-item-desc .bubble pre[data-v-3bbf2a2c] {
    font-size: 16px
}

.conversation-item-desc .button[data-v-3bbf2a2c] {
    align-items: center;
    border: 1px solid #efefef;
    border-radius: 8px;
    color: #606366;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 2px;
    line-height: normal;
    padding: 4px 8px
}

@media (prefers-color-scheme:dark) {
    .conversation-item-desc .button[data-v-3bbf2a2c] {
        border-color: #252525;
        color: #ddd
    }
}

.conversation-item-desc .button .icon[data-v-3bbf2a2c] {
    display: flex
}

@keyframes blink-animation-3bbf2a2c {
    0%,
    to {
        color: #000
    }
    50% {
        color: transparent
    }
}

@media (prefers-color-scheme:dark) {
    @keyframes blink-animation-3bbf2a2c {
        0%,
        to {
            color: #fff
        }
        50% {
            color: transparent
        }
    }
}

.conversation-item-desc.assistant .bubble[data-v-3bbf2a2c] .cursor {
    animation: blink-animation-3bbf2a2c .5s infinite;
    color: #606366;
    font-size: 12px
}

.conversation-item-desc[data-v-3bbf2a2c] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-width: 260px;
    width: 100%
}

.conversation-item-desc.user[data-v-3bbf2a2c] {
    align-items: flex-end;
    margin-left: 30px
}

.conversation-item-desc.assistant .bubble[data-v-3bbf2a2c] {
    border: 1px solid transparent;
    border-radius: 16px;
    box-sizing: border-box;
    min-width: 260px;
    padding-left: 0;
    padding-right: 0;
    width: 100%
}

.conversation-item-desc.assistant .bubble.retry[data-v-3bbf2a2c],
.conversation-item-desc.assistant .bubble.try_moa[data-v-3bbf2a2c] {
    align-items: center;
    background: #fafafa;
    border-radius: 16px;
    color: #232425;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 18px 16px
}

.conversation-item-desc .retry .button[data-v-3bbf2a2c],
.conversation-item-desc .try_moa .button[data-v-3bbf2a2c] {
    align-items: center;
    background: #232425;
    border-radius: 24px;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    flex-shrink: 0;
    font-size: 14px;
    gap: 8px;
    justify-content: center;
    padding: 8px 16px
}

@media (max-width:1220px) {
    .conversation-item-desc.assistant .bubble.retry[data-v-3bbf2a2c],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-3bbf2a2c] {
        flex-direction: column
    }
}

.models-list[data-v-3bbf2a2c] {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px 8px
}

.models-list[data-v-3bbf2a2c],
.models-list .model[data-v-3bbf2a2c] {
    display: flex;
    flex-direction: column
}

.models-list .model[data-v-3bbf2a2c] {
    color: #232425;
    cursor: pointer;
    margin-top: 4px;
    padding: 10px 6px 10px 10px
}

.models-list .model .row[data-v-3bbf2a2c] {
    border-radius: 8px;
    color: #232425;
    gap: 4px;
    justify-content: space-between
}

.models-list .model .row[data-v-3bbf2a2c],
.models-selected[data-v-3bbf2a2c] {
    display: flex;
    flex-direction: row;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.models-selected[data-v-3bbf2a2c] {
    align-items: center;
    background-color: #f4f4f4;
    border-radius: 16px;
    color: #606366;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    gap: 8px;
    line-height: 150%;
    padding: 6px 12px
}

@media (prefers-color-scheme:dark) {
    .models-list[data-v-3bbf2a2c] {
        background-color: #252525
    }
    .models-list .model .row[data-v-3bbf2a2c] {
        color: #ddd
    }
    .conversation-item-desc.assistant .bubble[data-v-3bbf2a2c] {
        color: #fff
    }
    .conversation-item-desc.assistant .bubble.retry[data-v-3bbf2a2c],
    .conversation-item-desc.assistant .bubble.try_moa[data-v-3bbf2a2c] {
        background: #111;
        color: #fff
    }
    .conversation-item-desc .retry .button[data-v-3bbf2a2c],
    .conversation-item-desc .try_moa .button[data-v-3bbf2a2c] {
        background: #fafafa;
        color: #232425
    }
}

.controls[data-v-3bbf2a2c] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden
}

.controls[data-v-3bbf2a2c]::-webkit-scrollbar {
    display: none
}

.model-container[data-v-3bbf2a2c] {
    display: contents
}

.model[data-v-3bbf2a2c] {
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 12px 10px;
    position: relative;
    transition: background-color .2s ease
}

.model[data-v-3bbf2a2c]:first-child {
    margin-top: 0
}

.model.disabled[data-v-3bbf2a2c] {
    cursor: not-allowed;
    opacity: .5
}

.model.disabled[data-v-3bbf2a2c]:hover {
    background-color: transparent
}

@media (hover:hover) {
    .model[data-v-3bbf2a2c]:hover:not(.disabled) {
        background-color: #f5f5f5
    }
}

.divider[data-v-3bbf2a2c] {
    background-color: #efefef;
    height: 1px;
    margin: 6px 0;
    width: 100%
}

.model .row[data-v-3bbf2a2c] {
    box-sizing: border-box;
    color: #606366;
    justify-content: space-between;
    min-height: 24px;
    width: 100%
}

.model .left[data-v-3bbf2a2c],
.model .row[data-v-3bbf2a2c] {
    align-items: flex-start;
    display: flex
}

.model .left[data-v-3bbf2a2c] {
    flex: 1;
    flex-direction: row;
    gap: 12px;
    min-width: 0;
    position: relative
}

.model .left .icon[data-v-3bbf2a2c] {
    align-items: center;
    background-color: transparent;
    border-radius: 50%;
    display: flex;
    flex-shrink: 0;
    height: 32px;
    justify-content: center;
    margin-top: 1px;
    overflow: hidden;
    width: 32px
}

.model .left .icon img[data-v-3bbf2a2c] {
    height: 32px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 32px
}

.model .left .text[data-v-3bbf2a2c] {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 6px;
    min-width: 0
}

.model-name[data-v-3bbf2a2c] {
    color: #606366;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2
}

.model-tags[data-v-3bbf2a2c] {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    gap: 4px
}

.price-tag[data-v-3bbf2a2c],
.quality-tag[data-v-3bbf2a2c],
.speed-tag[data-v-3bbf2a2c] {
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    line-height: 1.4;
    min-width: -moz-fit-content;
    min-width: fit-content;
    padding: 2px 8px;
    text-align: center;
    white-space: nowrap
}

.price-tag.price-budget[data-v-3bbf2a2c] {
    background-color: #e8f5e9;
    color: #2e7d32
}

.price-tag.price-moderate[data-v-3bbf2a2c] {
    background-color: #fff3e0;
    color: #f57c00
}

.price-tag.price-expensive[data-v-3bbf2a2c],
.price-tag.price-premium[data-v-3bbf2a2c] {
    background-color: #fce4ec;
    color: #c2185b
}

.speed-tag.speed-very-fast[data-v-3bbf2a2c] {
    background-color: #e3f2fd;
    color: #1976d2
}

.speed-tag.speed-fast[data-v-3bbf2a2c] {
    background-color: #e8f5e9;
    color: #388e3c
}

.speed-tag.speed-medium[data-v-3bbf2a2c] {
    background-color: #fff3e0;
    color: #f57c00
}

.speed-tag.speed-slow[data-v-3bbf2a2c] {
    background-color: #fce4ec;
    color: #c2185b
}

.quality-tag.quality-excellent[data-v-3bbf2a2c] {
    background-color: #e8f5e9;
    color: #2e7d32
}

.quality-tag.quality-high[data-v-3bbf2a2c] {
    background-color: #e3f2fd;
    color: #1976d2
}

.quality-tag.quality-medium[data-v-3bbf2a2c] {
    background-color: #fff3e0;
    color: #f57c00
}

.model .description[data-v-3bbf2a2c] {
    color: #909499;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 1.4;
    margin-top: 6px
}

.features[data-v-3bbf2a2c] {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-top: 8px
}

.feature-label[data-v-3bbf2a2c] {
    color: #909499;
    font-size: 11px;
    font-weight: 500;
    line-height: 1.2;
    margin: 0
}

.feature-tags[data-v-3bbf2a2c] {
    align-items: flex-start;
    display: flex;
    flex-wrap: wrap;
    gap: 4px
}

.feature-tag[data-v-3bbf2a2c] {
    background-color: #f5f5f5;
    border-radius: 12px;
    color: #666;
    font-size: 10px;
    line-height: 1.2;
    padding: 3px 8px;
    text-align: center;
    white-space: nowrap
}

.model .right[data-v-3bbf2a2c] {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    margin-left: 12px;
    margin-top: 3px
}

.model .right input[type=radio][data-v-3bbf2a2c] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    cursor: pointer;
    height: 16px;
    margin: 0;
    position: relative;
    width: 16px
}

.model .right input[type=radio][data-v-3bbf2a2c]:checked {
    background-color: #fff;
    border-color: #0f7fff
}

.model .right input[type=radio][data-v-3bbf2a2c]:checked:after {
    background: #0f7fff;
    border-radius: 50%;
    content: "";
    height: 10px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 10px
}

@media (prefers-color-scheme:dark) {
    .model[data-v-3bbf2a2c]:hover:not(.disabled) {
        background-color: #333
    }
    .model .row[data-v-3bbf2a2c],
    .model-name[data-v-3bbf2a2c] {
        color: #ddd
    }
    .model .description[data-v-3bbf2a2c] {
        color: #909499
    }
    .divider[data-v-3bbf2a2c] {
        background-color: #333
    }
    .model .left .icon[data-v-3bbf2a2c] {
        background-color: #efefef
    }
    .model .right input[type=radio][data-v-3bbf2a2c] {
        background-color: #fff;
        border-color: #666
    }
    .model .right input[type=radio][data-v-3bbf2a2c]:checked {
        background-color: #fff;
        border-color: #0f7fff
    }
    .feature-label[data-v-3bbf2a2c] {
        color: #999
    }
    .feature-tag[data-v-3bbf2a2c] {
        background-color: #333;
        color: #ccc
    }
    .price-tag.price-budget[data-v-3bbf2a2c] {
        background-color: #1b4332;
        color: #52c41a
    }
    .price-tag.price-moderate[data-v-3bbf2a2c] {
        background-color: #3d2914;
        color: #fa8c16
    }
    .price-tag.price-expensive[data-v-3bbf2a2c],
    .price-tag.price-premium[data-v-3bbf2a2c] {
        background-color: #3d1a27;
        color: #f759ab
    }
    .speed-tag.speed-very-fast[data-v-3bbf2a2c] {
        background-color: #0d2a4d;
        color: #40a9ff
    }
    .speed-tag.speed-fast[data-v-3bbf2a2c] {
        background-color: #1b4332;
        color: #52c41a
    }
    .speed-tag.speed-medium[data-v-3bbf2a2c] {
        background-color: #3d2914;
        color: #fa8c16
    }
    .speed-tag.speed-slow[data-v-3bbf2a2c] {
        background-color: #3d1a27;
        color: #f759ab
    }
    .quality-tag.quality-excellent[data-v-3bbf2a2c] {
        background-color: #1b4332;
        color: #52c41a
    }
    .quality-tag.quality-high[data-v-3bbf2a2c] {
        background-color: #0d2a4d;
        color: #40a9ff
    }
    .quality-tag.quality-medium[data-v-3bbf2a2c] {
        background-color: #3d2914;
        color: #fa8c16
    }
}

@media (max-width:320px) {
    .price-tag[data-v-3bbf2a2c],
    .quality-tag[data-v-3bbf2a2c],
    .speed-tag[data-v-3bbf2a2c] {
        font-size: 8px;
        padding: 1px 4px
    }
}