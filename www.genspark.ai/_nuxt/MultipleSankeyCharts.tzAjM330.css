.multiple-sankey-charts[data-v-d5ec5171] {
    overflow: hidden;
    position: relative;
    transition: transform .3s ease
}

.multiple-sankey-charts[data-v-d5ec5171]:hover {
    transform: scale(1.01)
}

.chart-container[data-v-d5ec5171] {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    position: relative;
    width: 100%
}

.chart-title[data-v-d5ec5171] {
    background-color: #fffc;
    border-radius: 5px;
    box-sizing: border-box;
    left: 50%;
    margin: 0 0 20px;
    padding: 5px 10px;
    position: relative;
    text-align: center;
    top: 20px;
    transform: translate(-50%);
    width: 100%;
    z-index: 3
}

.chart-scaler[data-v-d5ec5171] {
    align-items: center;
    display: flex;
    justify-content: center
}

.arrow[data-v-d5ec5171] {
    cursor: pointer;
    opacity: 0;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    transition: opacity .3s ease;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 60px;
    z-index: 2
}

.arrow.left[data-v-d5ec5171] {
    left: 10px;
    transform: translateY(-50%) rotate(180deg)
}

.arrow.right[data-v-d5ec5171] {
    right: 10px
}

.arrow img[data-v-d5ec5171] {
    width: 100%
}

.multiple-sankey-charts:hover .arrow[data-v-d5ec5171] {
    opacity: 1
}

.footer[data-v-d5ec5171] {
    align-items: flex-end;
    bottom: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 20px;
    position: absolute;
    width: 100%
}

.chart-tips[data-v-d5ec5171] {
    color: #0003;
    font-size: 10px;
    text-align: center;
    width: 100%
}

.logo[data-v-d5ec5171] {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    height: 20px;
    left: 20px;
    position: absolute;
    top: 20px;
    width: auto
}

.fullscreen-button[data-v-d5ec5171] {
    background: #f0f0f0;
    border-radius: 6px;
    box-sizing: border-box;
    cursor: pointer;
    height: 32px;
    opacity: .6;
    padding: 4px;
    position: absolute;
    right: 20px;
    top: 14px;
    transition: transform .3s ease;
    width: 32px;
    z-index: 1000
}

.fullscreen-button[data-v-d5ec5171]:hover {
    opacity: 1;
    transform: scale(1.1)
}

.fullscreen-mode[data-v-d5ec5171] {
    align-items: center;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: #fffc;
    box-sizing: border-box;
    display: flex;
    height: 100vh !important;
    justify-content: center;
    left: 0;
    margin: 0;
    padding: 0;
    position: fixed;
    top: 0;
    width: 100vw !important;
    z-index: 9999
}

.fullscreen-mode .chart-container[data-v-d5ec5171] {
    height: 90%;
    width: 90%
}

.fullscreen-mode .fullscreen-button[data-v-d5ec5171] {
    position: fixed;
    right: 20px;
    top: 20px
}

.fullscreen-mode .logo[data-v-d5ec5171] {
    height: 40px;
    left: 40px;
    top: 40px
}

.fullscreen-mode .chart-tips[data-v-d5ec5171] {
    bottom: 40px;
    font-size: 18px;
    left: 50%;
    padding: 0 40px;
    position: fixed;
    transform: translate(-50%);
    width: auto
}

.fullscreen-mode .footer[data-v-d5ec5171] {
    bottom: 40px
}

.fullscreen-mode .fullscreen-button[data-v-d5ec5171] {
    height: 32px;
    right: 40px;
    top: 40px;
    width: 32px
}

.multiple-sankey-charts.fullscreen-mode[data-v-d5ec5171]:hover {
    transform: none
}

.pointer-events-auto[data-v-d5ec5171] {
    pointer-events: auto
}

.fullscreen-button[data-v-d5ec5171] svg {
    color: #1980fa;
    fill: #1980fa
}

@media (prefers-color-scheme:dark) {
    .fullscreen-mode[data-v-d5ec5171] {
        background: #1a1a1a
    }
    .fullscreen-button[data-v-d5ec5171] {
        background: #232425
    }
    .chart-tips[data-v-d5ec5171] {
        color: #888
    }
}