.toolbar-group[data-v-df9dcab9] {
    display: contents
}

.toolbar-button[data-v-b3f57df3] {
    align-items: center;
    background-color: #fff;
    border-radius: 4px;
    box-sizing: border-box;
    color: #232425;
    cursor: pointer;
    display: inline-flex;
    font-size: 14px;
    height: 28px;
    justify-content: center;
    line-height: 1;
    min-width: 28px;
    transition: all .2s ease
}

.toolbar-button.active[data-v-b3f57df3] {
    background-color: #e6f4ff;
    border-color: #1677ff;
    color: #1677ff
}

.toolbar-button.disabled[data-v-b3f57df3] {
    cursor: not-allowed;
    opacity: .5;
    pointer-events: none
}

@media (hover:hover) {
    .toolbar-button[data-v-b3f57df3]:hover:not(.disabled) {
        background-color: #f5f5f5;
        border-color: #ddd;
        color: #232425
    }
    .toolbar-button:hover:not(.disabled) .icon[data-v-b3f57df3] {
        color: #232425
    }
}

.toolbar-button[data-v-b3f57df3]:active:not(.disabled) {
    background-color: #e6f4ff;
    border-color: #1677ff;
    color: #1677ff
}

.icon[data-v-b3f57df3] {
    align-items: center;
    color: #232425;
    display: inline-flex;
    height: 16px;
    justify-content: center;
    line-height: 1;
    width: 16px
}

.icon[data-v-b3f57df3] svg {
    height: 100%;
    width: 100%
}

.toolbar-button.inspect-mode[data-v-b3f57df3] {
    transition: all .3s ease
}

@media screen and (max-width:768px) {
    .toolbar-button.inspect-mode[data-v-b3f57df3] {
        display: none
    }
}

.toolbar-button.inspect-mode[data-v-b3f57df3]:active:not(.disabled) {
    background-color: #d9d9d9;
    border-color: transparent;
    color: #232425;
    transform: scale(.95)
}

.toolbar-button.inspect-mode-active[data-v-b3f57df3] {
    background-color: #eff6ff;
    border-color: #eff6ff
}

.toolbar-button.inspect-mode-active .icon[data-v-b3f57df3] {
    color: #0f7fff
}

.toolbar-button.inspect-mode .icon[data-v-b3f57df3] {
    height: 16px;
    transition: all .3s ease;
    width: 16px
}

.toolbar-button.inspect-mode-active .icon[data-v-b3f57df3] {
    transform: scale(1.1)
}

.toolbar-divider[data-v-974669dc] {
    background-color: #ddd;
    margin: 0 4px;
    width: 1px
}

.heading-selector-wrapper[data-v-b6b77fa6] {
    display: inline-block;
    flex-shrink: 0
}

.heading-selector[data-v-b6b77fa6] {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-sizing: border-box;
    color: #232425;
    cursor: pointer;
    display: inline-flex;
    font-size: 12px;
    height: 28px;
    line-height: 1.5;
    min-width: 80px;
    padding: 0 8px;
    text-align: left;
    transition: all .2s ease
}

.heading-selector[data-v-b6b77fa6],
.selector-content[data-v-b6b77fa6] {
    align-items: center;
    justify-content: space-between
}

.selector-content[data-v-b6b77fa6] {
    display: flex;
    width: 100%
}

@media (hover:hover) {
    .heading-selector[data-v-b6b77fa6]:hover {
        background-color: #f5f5f5;
        border-color: #ddd;
        color: #232425
    }
    .heading-selector:hover .icon[data-v-b6b77fa6] {
        color: #232425
    }
}

.heading-selector[data-v-b6b77fa6]:active {
    background-color: #e6f4ff;
    border-color: #1677ff;
    color: #1677ff
}

.icon[data-v-b6b77fa6] {
    align-items: center;
    color: #232425;
    display: inline-flex;
    height: 12px;
    justify-content: center;
    line-height: 1;
    width: 12px
}

.icon svg[data-v-b6b77fa6] {
    height: 100%;
    width: 100%
}

.font-size-input-wrapper[data-v-90505933] {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    height: 28px;
    position: relative
}

.font-size-input[data-v-90505933] {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-sizing: border-box;
    color: #232425;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    height: 28px;
    outline: none;
    padding: 0 8px;
    text-align: center;
    transition: all .2s ease;
    width: 70px
}

@media (hover:hover) {
    .font-size-input[data-v-90505933]:hover {
        background-color: #f5f5f5;
        border-color: #ddd;
        color: #232425
    }
}

.font-size-input[data-v-90505933]:active {
    background-color: #e6f4ff;
    border-color: #1677ff;
    color: #1677ff
}

.font-size-input[data-v-90505933]:focus {
    cursor: text
}

.font-size-input.font-size-changed[data-v-90505933],
.font-size-input[data-v-90505933]:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px #1890ff33
}

.font-size-options[data-v-90505933] {
    background: #fff;
    border-radius: 12px;
    min-width: 200px;
    overflow: hidden;
    padding: 8px
}

.options-header[data-v-90505933] {
    border-bottom: 1px solid #eee;
    color: #666;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
    padding-bottom: 4px
}

.options-grid[data-v-90505933] {
    display: grid;
    gap: 4px;
    grid-template-columns: repeat(4, 1fr)
}

.size-option[data-v-90505933] {
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    padding: 6px 8px;
    text-align: center;
    transition: all .2s ease
}

.size-option[data-v-90505933]:hover {
    background: #f5f5f5;
    border-color: #ddd
}

.size-option.active[data-v-90505933] {
    background: #e6f3ff;
    border-color: #1890ff;
    color: #1890ff;
    font-weight: 500
}

.simple-color-picker-wrapper[data-v-4542108a] {
    box-sizing: border-box;
    flex-shrink: 0;
    height: 28px;
    position: relative
}

.simple-color-picker-wrapper.filter-mode[data-v-4542108a] {
    height: 100%
}

.picker-button[data-v-4542108a] {
    align-items: center;
    background-color: transparent;
    border-radius: 5px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    gap: 2px;
    height: 28px;
    justify-content: center;
    padding: 2px;
    transition: background-color .2s;
    width: 38px
}

.picker-button[data-v-4542108a]:hover {
    background-color: #f5f5f5
}

.icon[data-v-4542108a] {
    align-items: center;
    color: #232425;
    display: flex;
    flex-shrink: 0;
    height: 16px;
    justify-content: center;
    line-height: 1;
    width: 16px
}

.icon svg[data-v-4542108a] {
    height: 100%;
    width: 100%
}

.color-square[data-v-4542108a] {
    border: 1px solid #e0e0e0;
    border-radius: 2px;
    box-sizing: border-box;
    flex-shrink: 0;
    height: 12px;
    width: 12px
}

.clear-button[data-v-4542108a] {
    background-color: #f5f5f5;
    border: none;
    border-radius: 12px;
    color: #232425;
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    padding: 10px 0;
    transition: background-color .2s;
    width: 100%
}

.clear-button[data-v-4542108a]:hover {
    background-color: #e8e8e8
}

.filter-mode-trigger[data-v-4542108a] {
    height: 100%;
    pointer-events: none;
    width: 100%
}

.spacing-adjuster-wrapper[data-v-2d474f50] {
    position: relative
}

.spacing-adjuster-wrapper[data-v-2d474f50] .n-popover {
    --n-color: #fff;
    --n-border-radius: 6px;
    --n-box-shadow: 0 4px 16px rgba(0, 0, 0, .08);
    --n-border: 1px solid #e6f4ff
}

.spacing-button[data-v-2d474f50] {
    align-items: center;
    background-color: #fff;
    border: none;
    border-radius: 4px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    height: 28px;
    justify-content: center;
    transition: all .2s ease;
    width: 28px
}

.spacing-button[data-v-2d474f50]:hover {
    background-color: #f5f5f5;
    border-color: #ddd;
    color: #232425
}

.spacing-button[data-v-2d474f50]:active {
    background-color: #e6f4ff;
    border-color: #1677ff;
    color: #1677ff
}

.icon[data-v-2d474f50] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    transition: color .2s ease;
    width: 16px
}

.icon[data-v-2d474f50],
.spacing-button:hover .icon[data-v-2d474f50] {
    color: #232425
}

.spacing-button:active .icon[data-v-2d474f50] {
    color: #1677ff
}

.icon svg[data-v-2d474f50] {
    height: 100%;
    width: 100%
}

.spacing-panel[data-v-2d474f50] {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 12px;
    box-shadow: 0 4px 16px #00000014;
    min-width: 280px;
    padding: 16px
}

.spacing-item[data-v-2d474f50] {
    align-items: center;
    display: flex;
    gap: 12px;
    margin-bottom: 16px
}

.spacing-item[data-v-2d474f50]:last-child {
    margin-bottom: 0
}

.spacing-label[data-v-2d474f50] {
    color: #232425;
    flex-shrink: 0;
    font-size: 14px;
    width: 60px
}

.spacing-value[data-v-2d474f50] {
    background: #f0f8ff;
    border: 1px solid #e6f4ff;
    border-radius: 4px;
    color: #232425;
    cursor: pointer;
    flex-shrink: 0;
    font-size: 12px;
    padding: 2px 4px;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 40px
}

.spacing-value[data-v-2d474f50]:hover {
    background: #e6f4ff;
    border-color: #4096ff
}

.spacing-input[data-v-2d474f50] {
    background: #fff;
    border: 1px solid #1677ff;
    border-radius: 4px;
    box-shadow: 0 0 0 2px #1677ff1a;
    color: #232425;
    flex-shrink: 0;
    font-size: 12px;
    outline: none;
    padding: 2px 4px;
    text-align: center;
    width: 40px
}

.spacing-input[data-v-2d474f50]:focus {
    border-color: #1677ff;
    box-shadow: 0 0 0 2px #1677ff33
}

.slider-wrapper[data-v-2d474f50] {
    position: relative;
    width: 100%;
    z-index: 1
}

.slider-wrapper[data-v-2d474f50] .n-slider {
    position: relative;
    z-index: 2
}

.slider-wrapper[data-v-2d474f50] .n-slider-handle {
    border-color: #1677ff;
    cursor: grab
}

.slider-wrapper[data-v-2d474f50] .n-slider-handle:active {
    border-color: #1677ff;
    cursor: grabbing
}

.slider-wrapper[data-v-2d474f50] .n-slider-handle:hover {
    border-color: #4096ff
}

.slider-wrapper[data-v-2d474f50] .n-slider-fill {
    background-color: #1677ff
}

.slider-wrapper[data-v-2d474f50] .n-slider-rail {
    background-color: #e6f4ff;
    cursor: pointer;
    --n-dot-border-active: 2px solid #0f7fff;
    --n-fill-color: #0f7fff;
    --n-fill-color-hover: #0f7fff
}

.margin-padding-adjuster-wrapper[data-v-4f721c12] {
    position: relative
}

.margin-padding-adjuster-wrapper[data-v-4f721c12] .n-popover {
    --n-color: #fff;
    --n-border-radius: 6px;
    --n-box-shadow: 0 4px 16px rgba(0, 0, 0, .08);
    --n-border: 1px solid #e6f4ff
}

.margin-padding-button[data-v-4f721c12] {
    align-items: center;
    background-color: #fff;
    border: none;
    border-radius: 4px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    height: 28px;
    justify-content: center;
    transition: all .2s ease;
    width: 28px
}

.margin-padding-button[data-v-4f721c12]:hover {
    background-color: #f5f5f5;
    border-color: #ddd;
    color: #232425
}

.margin-padding-button[data-v-4f721c12]:active {
    background-color: #e6f4ff;
    border-color: #1677ff;
    color: #1677ff
}

.icon[data-v-4f721c12] {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    transition: color .2s ease;
    width: 16px
}

.icon[data-v-4f721c12],
.margin-padding-button:hover .icon[data-v-4f721c12] {
    color: #232425
}

.margin-padding-button:active .icon[data-v-4f721c12] {
    color: #1677ff
}

.icon svg[data-v-4f721c12] {
    height: 100%;
    width: 100%
}

.margin-padding-panel[data-v-4f721c12] {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 12px;
    box-shadow: 0 4px 16px #00000014;
    min-width: 350px;
    padding: 16px
}

.panel-header[data-v-4f721c12] {
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;
    padding-bottom: 8px
}

.element-info[data-v-4f721c12] {
    color: #666;
    font-size: 12px;
    font-weight: 500
}

.body-element-notice[data-v-4f721c12] {
    background: #f0f8ff;
    border: 1px solid #e6f4ff;
    border-radius: 3px;
    color: #1677ff;
    font-size: 11px;
    margin-top: 4px;
    padding: 2px 6px
}

.section[data-v-4f721c12] {
    margin-bottom: 20px
}

.section[data-v-4f721c12]:last-of-type {
    margin-bottom: 16px
}

.section-title[data-v-4f721c12] {
    color: #232425;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px
}

.spacing-grid[data-v-4f721c12] {
    display: grid;
    gap: 12px;
    grid-template-columns: 1fr 1fr
}

.spacing-item[data-v-4f721c12] {
    align-items: center;
    display: flex;
    gap: 8px
}

.spacing-label[data-v-4f721c12] {
    width: 50px
}

.spacing-label[data-v-4f721c12],
.spacing-value[data-v-4f721c12] {
    color: #232425;
    flex-shrink: 0;
    font-size: 12px
}

.spacing-value[data-v-4f721c12] {
    background: #f0f8ff;
    border: 1px solid #e6f4ff;
    border-radius: 4px;
    cursor: pointer;
    padding: 2px 4px;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 35px
}

.spacing-value[data-v-4f721c12]:hover {
    background: #e6f4ff;
    border-color: #4096ff
}

.spacing-input[data-v-4f721c12] {
    background: #fff;
    border: 1px solid #1677ff;
    border-radius: 4px;
    box-shadow: 0 0 0 2px #1677ff1a;
    color: #232425;
    flex-shrink: 0;
    font-size: 12px;
    outline: none;
    padding: 2px 4px;
    text-align: center;
    width: 35px
}

.spacing-input[data-v-4f721c12]:focus {
    border-color: #1677ff;
    box-shadow: 0 0 0 2px #1677ff33
}

.slider-wrapper[data-v-4f721c12] {
    flex: 1;
    position: relative;
    z-index: 1
}

.slider-wrapper[data-v-4f721c12] .n-slider {
    position: relative;
    z-index: 2
}

.slider-wrapper[data-v-4f721c12] .n-slider-handle {
    border-color: #1677ff;
    cursor: grab
}

.slider-wrapper[data-v-4f721c12] .n-slider-handle:active {
    border-color: #1677ff;
    cursor: grabbing
}

.slider-wrapper[data-v-4f721c12] .n-slider-handle:hover {
    border-color: #4096ff
}

.slider-wrapper[data-v-4f721c12] .n-slider-fill {
    background-color: #1677ff
}

.slider-wrapper[data-v-4f721c12] .n-slider-rail {
    background-color: #e6f4ff;
    cursor: pointer;
    --n-dot-border-active: 2px solid #0f7fff;
    --n-fill-color: #0f7fff;
    --n-fill-color-hover: #0f7fff
}

.margin-padding-panel[data-v-4f721c12] .n-button {
    --n-ripple-color: #0f7fff;
    --n-text-color: #767c82;
    --n-text-color-hover: #0f7fff;
    --n-text-color-pressed: #0f7fff;
    --n-text-color-focus: #0f7fff;
    --n-border-hover: 1px solid #0f7fff;
    --n-border-pressed: 1px solid #0f7fff;
    --n-border-focus: 1px solid #0f7fff
}

.panel-footer[data-v-4f721c12] {
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: center;
    padding-top: 8px
}

.toolbar-upload-image-button[data-v-f0946f44] {
    display: inline-block;
    position: relative
}

.popover-trigger[data-v-f0946f44] {
    height: 1px;
    pointer-events: none;
    position: absolute;
    top: 100%;
    width: 1px
}

@media (max-width:767px) {
    .popover-trigger[data-v-f0946f44] {
        left: 0;
        transform: translate(0)
    }
    [data-v-f0946f44] .v-binder-follower-content {
        left: 16px !important;
        margin-left: 0 !important;
        margin-top: 40px;
        position: fixed !important;
        right: 16px !important;
        transform: none !important;
        width: -moz-fit-content;
        width: fit-content
    }
    .image-url-dialog[data-v-f0946f44],
    .insert-image-popover[data-v-f0946f44] {
        max-width: none;
        min-width: 0;
        width: 100%
    }
}

.insert-image-popover[data-v-f0946f44] {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 12px;
    box-shadow: 0 4px 12px #00000026;
    min-width: 200px;
    padding: 12px
}

.insert-image-popover[data-v-f0946f44] .n-button {
    --n-ripple-color: #0f7fff;
    --n-text-color: #767c82;
    --n-text-color-hover: #0f7fff;
    --n-text-color-pressed: #0f7fff;
    --n-text-color-focus: #0f7fff;
    --n-border-hover: 1px solid #0f7fff;
    --n-border-pressed: 1px solid #0f7fff;
    --n-border-focus: 1px solid #0f7fff
}

.popover-title[data-v-f0946f44] {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px
}

.popover-buttons[data-v-f0946f44] {
    align-items: center;
    display: flex;
    justify-content: flex-start
}

.image-url-dialog[data-v-f0946f44] {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 12px;
    box-shadow: 0 4px 12px #00000026;
    max-width: 100%;
    padding: 16px;
    width: 100%
}

.image-url-dialog[data-v-f0946f44] .n-button {
    --n-ripple-color: #0f7fff;
    --n-text-color: #767c82;
    --n-text-color-hover: #0f7fff;
    --n-text-color-pressed: #0f7fff;
    --n-text-color-focus: #0f7fff;
    --n-border: 1px solid #0f7fff;
    --n-border-hover: 1px solid #0f7fff;
    --n-border-pressed: 1px solid #0f7fff;
    --n-border-focus: 1px solid #0f7fff;
    --n-border-disabled: 1px solid #0f7fff
}

.image-url-dialog[data-v-f0946f44] .n-button.n-button--primary-type {
    --n-color: #0f7fff;
    --n-color-hover: #0f7fffaf;
    --n-text-color: #fff;
    --n-text-color-hover: #fff;
    --n-text-color-pressed: #fff;
    --n-text-color-focus: #fff;
    --n-color-pressed: #0f7fff;
    --n-color-focus: #0f7fff;
    --n-color-disabled: #0f7fff
}

.image-url-dialog[data-v-f0946f44] .n-input {
    --n-caret-color: #0f7fff;
    --n-loading-color: #0f7fff;
    --n-border-hover: 1px solid #0f7fff;
    --n-border-pressed: 1px solid #0f7fff;
    --n-border-focus: 1px solid #0f7fff;
    --n-border-disabled: 1px solid #0f7fff;
    --n-box-shadow-focus: 0 0 0 2px #0f7fff1f
}

.dialog-title[data-v-f0946f44] {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px
}

.dialog-content[data-v-f0946f44] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.dialog-buttons[data-v-f0946f44] {
    align-items: center;
    display: flex;
    justify-content: flex-end
}

.toolbar-replace-image-button[data-v-a95c52b4] {
    display: inline-block;
    position: relative
}

.popover-trigger[data-v-a95c52b4] {
    height: 1px;
    pointer-events: none;
    position: absolute;
    top: 100%;
    width: 1px
}

@media (max-width:767px) {
    .popover-trigger[data-v-a95c52b4] {
        left: 0;
        transform: translate(0)
    }
    [data-v-a95c52b4] .v-binder-follower-content {
        left: 16px !important;
        margin-left: 0 !important;
        margin-top: 40px;
        position: fixed !important;
        right: 16px !important;
        transform: none !important;
        width: -moz-fit-content;
        width: fit-content
    }
    .image-url-dialog[data-v-a95c52b4],
    .replace-image-popover[data-v-a95c52b4] {
        max-width: none;
        min-width: 0;
        width: 100%
    }
}

.replace-image-popover[data-v-a95c52b4] {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 4px 12px #00000026;
    min-width: 200px;
    padding: 12px
}

.popover-title[data-v-a95c52b4] {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px
}

.popover-buttons[data-v-a95c52b4] {
    align-items: center;
    display: flex;
    justify-content: flex-start
}

.image-url-dialog[data-v-a95c52b4] {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 4px 12px #00000026;
    max-width: 100%;
    padding: 16px;
    width: 100%
}

.dialog-title[data-v-a95c52b4] {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px
}

.dialog-content[data-v-a95c52b4] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.dialog-buttons[data-v-a95c52b4] {
    align-items: center;
    display: flex;
    justify-content: flex-end
}

.toolbar-insert-link-button[data-v-3a094b11] {
    display: inline-block;
    position: relative
}

.popover-trigger[data-v-3a094b11] {
    height: 1px;
    pointer-events: none;
    position: absolute;
    top: 100%;
    width: 1px
}

@media (max-width:767px) {
    .popover-trigger[data-v-3a094b11] {
        left: 0;
        transform: translate(0)
    }
    [data-v-3a094b11] .v-binder-follower-content {
        left: 16px !important;
        margin-left: 0 !important;
        margin-top: 40px;
        position: fixed !important;
        right: 16px !important;
        transform: none !important;
        width: -moz-fit-content;
        width: fit-content
    }
    .insert-link-popover[data-v-3a094b11] {
        max-width: none;
        min-width: 0;
        width: 100%
    }
}

.insert-link-popover[data-v-3a094b11] {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 4px 12px #00000026;
    min-width: 240px;
    padding: 12px
}

.popover-title[data-v-3a094b11] {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px
}

.popover-buttons[data-v-3a094b11] {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-start
}

@media (max-width:767px) {
    .popover-buttons[data-v-3a094b11] {
        justify-content: space-between
    }
    .popover-buttons .n-button[data-v-3a094b11] {
        flex: 1;
        min-width: 0
    }
}

.link-input-row[data-v-3a094b11] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.toolbar-insert-table-button[data-v-b5f449ba] {
    display: inline-block;
    position: relative
}

.popover-trigger[data-v-b5f449ba] {
    height: 1px;
    pointer-events: none;
    position: absolute;
    top: 100%;
    width: 1px
}

@media (max-width:767px) {
    .popover-trigger[data-v-b5f449ba] {
        left: 0;
        transform: translate(0)
    }
    [data-v-b5f449ba] .v-binder-follower-content {
        left: 16px !important;
        margin-left: 0 !important;
        margin-top: 40px;
        position: fixed !important;
        right: 16px !important;
        transform: none !important;
        width: -moz-fit-content;
        width: fit-content
    }
    .insert-table-popover[data-v-b5f449ba] {
        max-width: none;
        min-width: 0;
        width: 100%
    }
}

.insert-table-popover[data-v-b5f449ba] {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 12px;
    box-shadow: 0 4px 12px #00000026;
    min-width: 240px;
    padding: 12px
}

.insert-table-popover[data-v-b5f449ba] .n-input {
    --n-loading-color: #0f7fff;
    --n-border-hover: 1px solid #0f7fff7f;
    --n-border-focus: 1px solid #0f7fff7f
}

.insert-table-popover[data-v-b5f449ba] .n-button {
    --n-ripple-color: #0f7fff;
    --n-text-color-hover: #0f7fff;
    --n-text-color-pressed: #0f7fff;
    --n-text-color-focus: #0f7fff;
    --n-border-disabled: 1px solid #0f7fff;
    --n-border-hover: 1px solid #0f7fff7f;
    --n-border-pressed: 1px solid #0f7fff7f;
    --n-border-focus: 1px solid #0f7fff7f
}

.insert-table-popover[data-v-b5f449ba] .n-button--primary-type {
    --n-color: #0f7fff;
    --n-color-hover: #0f7fff;
    --n-color-pressed: #0f7fff;
    --n-color-focus: #0f7fff;
    --n-color-disabled: #0f7fff;
    color: #fff
}

.insert-table-popover[data-v-b5f449ba] .n-button--primary-type:hover {
    opacity: .8
}

.popover-title[data-v-b5f449ba] {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px
}

.table-input-section[data-v-b5f449ba] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.input-row[data-v-b5f449ba] {
    align-items: center;
    display: flex;
    gap: 8px;
    justify-content: space-between
}

.input-label[data-v-b5f449ba] {
    color: #666;
    font-size: 13px;
    min-width: 80px
}

.popover-buttons[data-v-b5f449ba] {
    align-items: center;
    display: flex;
    justify-content: flex-end;
    margin-top: 8px
}

.toolbar-icon[data-v-575e9a83] {
    align-items: center;
    display: flex;
    font-size: 12px;
    font-weight: 700;
    justify-content: center
}

.toolbar-icon[data-v-575e9a83],
.toolbar-icon svg[data-v-575e9a83] {
    height: 16px;
    width: 16px
}

.rotate-180[data-v-575e9a83] {
    transform: rotate(180deg)
}

.editor-toolbar-wrapper[data-v-8ac83de1] {
    box-sizing: border-box;
    position: sticky;
    top: 20px;
    width: 100%;
    z-index: 1004
}

.editor-toolbar-container[data-v-8ac83de1] {
    width: 100%
}

.editor-toolbar[data-v-8ac83de1] {
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 12px;
    box-shadow: 0 2px 8px #0000001a;
    box-sizing: border-box;
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
    gap: 8px;
    margin: 0 auto;
    max-width: 1060px;
    padding: 12px 16px;
    z-index: 10
}

.color-buttons-group[data-v-8ac83de1] {
    display: contents
}

.expand-icon[data-v-8ac83de1] {
    transform: rotate(0);
    transform-origin: center;
    transition: transform .3s ease
}

.expand-icon.is-rotated[data-v-8ac83de1] {
    transform: rotate(180deg) !important
}

.icon[data-v-2634786b] {
    align-items: center;
    color: #232425;
    display: inline-flex;
    height: 14px;
    justify-content: center;
    margin-right: 0;
    width: 14px
}

.icon svg[data-v-2634786b] {
    height: 100%;
    width: 100%
}

.button[data-v-2634786b] {
    align-items: center;
    background: #fff;
    color: #232425;
    cursor: pointer;
    display: inline-flex;
    font-size: 12px;
    height: 16px;
    justify-content: center;
    padding: 0;
    transition: all .2s ease;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 16px
}

@media (hover:hover) {
    .button[data-v-2634786b]:hover {
        background: #f5f5f5;
        border-radius: 4px;
        transform: scale(1.1)
    }
}

.button-active[data-v-2634786b] {
    background-color: #eff6ff !important;
    border-radius: 4px;
    transform: scale(1.05)
}

.button-active[data-v-2634786b],
.button-active .icon[data-v-2634786b] {
    color: #1677ff !important
}

@media (hover:hover) {
    .button-active[data-v-2634786b]:hover {
        background-color: #dbeafe !important;
        transform: scale(1.1)
    }
}

.button-disabled[data-v-2634786b] {
    background: #f5f5f5 !important;
    cursor: not-allowed !important;
    opacity: .5;
    pointer-events: none
}

.button-disabled[data-v-2634786b],
.button-disabled .icon[data-v-2634786b] {
    color: #bfbfbf !important
}

@media (hover:hover) {
    .button-disabled[data-v-2634786b]:hover {
        background: #f5f5f5 !important;
        transform: none !important
    }
}

.popover-title[data-v-2634786b] {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px
}

.popover-buttons[data-v-2634786b] {
    align-items: center;
    display: flex;
    gap: 8px;
    justify-content: flex-start
}

.popover-button[data-v-2634786b] {
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    color: #333;
    cursor: pointer;
    font-size: 12px;
    height: 32px;
    outline: none;
    padding: 0 12px;
    transition: all .2s;
    white-space: nowrap
}

.popover-button[data-v-2634786b]:hover {
    background: #f5f5f5;
    border-color: #1890ff;
    color: #1890ff
}

.dialog-title[data-v-2634786b] {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px
}

.dialog-content[data-v-2634786b] {
    display: flex;
    flex-direction: column;
    gap: 12px
}

.url-input[data-v-2634786b] {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    height: 32px;
    outline: none;
    padding: 0 8px;
    transition: border-color .2s;
    width: 200px
}

.url-input[data-v-2634786b]:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px #1890ff33
}

.dialog-buttons[data-v-2634786b] {
    align-items: center;
    display: flex;
    gap: 8px;
    justify-content: center
}

.dialog-button[data-v-2634786b] {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    height: 32px;
    outline: none;
    padding: 0 12px;
    transition: all .2s;
    white-space: nowrap
}

.dialog-button.cancel[data-v-2634786b] {
    background: #fff;
    color: #333
}

.dialog-button.cancel[data-v-2634786b]:hover {
    background: #f5f5f5;
    border-color: #bfbfbf
}

.dialog-button.confirm[data-v-2634786b] {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff
}

.dialog-button.confirm[data-v-2634786b]:hover:not(:disabled) {
    background: #40a9ff;
    border-color: #40a9ff
}

.dialog-button.confirm[data-v-2634786b]:disabled {
    background: #f5f5f5;
    border-color: #d9d9d9;
    color: #bfbfbf;
    cursor: not-allowed
}

.popover-trigger[data-v-2634786b] {
    height: 1px;
    left: 50%;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    top: 100%;
    width: 1px
}

.icon[data-v-2e3c866b] {
    align-items: center;
    color: #232425;
    display: inline-flex;
    height: 14px;
    justify-content: center;
    margin-right: 0;
    width: 14px
}

.icon svg[data-v-2e3c866b] {
    height: 100%;
    width: 100%
}

.image-focus-popover[data-v-2e3c866b] {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px #00000026;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 16px
}

.popover-header[data-v-2e3c866b] {
    align-items: center;
    display: flex;
    justify-content: space-between
}

.popover-title[data-v-2e3c866b] {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px
}

.reset-focus-button[data-v-2e3c866b] {
    align-items: center;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    color: #666;
    cursor: pointer;
    display: inline-flex;
    height: 24px;
    justify-content: center;
    outline: none;
    padding: 0;
    transition: all .2s ease;
    width: 24px
}

.reset-focus-button[data-v-2e3c866b]:hover {
    background: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff
}

.reset-focus-button[data-v-2e3c866b]:active {
    background: #d6f0ff;
    border-color: #0d7dd6;
    color: #0d7dd6
}

.reset-focus-button .icon[data-v-2e3c866b] {
    align-items: center;
    display: flex;
    height: 14px;
    justify-content: center;
    width: 14px
}

.reset-focus-button .icon svg[data-v-2e3c866b] {
    height: 100%;
    width: 100%
}

.image-focus-container[data-v-2e3c866b] {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.image-focus-preview[data-v-2e3c866b] {
    background: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: crosshair;
    height: 300px;
    max-width: 300px;
    overflow: hidden;
    position: relative;
    width: 100%
}

.focus-preview-image[data-v-2e3c866b] {
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 100%
}

.background-focus-preview[data-v-2e3c866b] {
    background: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: crosshair;
    height: 300px;
    max-width: 300px;
    overflow: hidden;
    position: relative;
    width: 100%
}

.preview-reset-button[data-v-2e3c866b] {
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    background: #ffffffe6 !important;
    box-shadow: 0 2px 8px #00000026;
    cursor: pointer !important;
    pointer-events: auto !important;
    position: absolute !important;
    right: 8px;
    top: 8px;
    z-index: 2
}

.preview-reset-button[data-v-2e3c866b]:hover {
    background: #e6f7fff2 !important;
    transform: scale(1.05)
}

.focus-dot[data-v-2e3c866b] {
    background: #1890ff;
    border: 2px solid #fff;
    border-radius: 50%;
    box-shadow: 0 2px 4px #0003;
    height: 8px;
    pointer-events: none;
    width: 8px;
    z-index: 1
}

.focus-dot[data-v-2e3c866b],
.focus-dot[data-v-2e3c866b]:before {
    position: absolute;
    transform: translate(-50%, -50%)
}

.focus-dot[data-v-2e3c866b]:before {
    border: 1px solid #1890ff;
    border-radius: 50%;
    content: "";
    height: 20px;
    left: 50%;
    opacity: .3;
    top: 50%;
    width: 20px
}

.image-focus-popover-independent[data-v-2e3c866b] {
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    position: fixed;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    z-index: 1003
}

@media (max-width:768px) {
    .image-focus-popover-independent .image-focus-popover[data-v-2e3c866b] {
        max-width: 280px;
        min-width: 280px
    }
    .image-focus-popover-independent .image-focus-preview[data-v-2e3c866b] {
        height: 250px;
        max-width: 250px
    }
}

.float-menu[data-v-8109ab17] {
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 10px #00000026;
    padding: 8px;
    position: fixed;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: -moz-max-content;
    width: max-content;
    z-index: 1002
}

.menu-buttons[data-v-8109ab17] {
    display: flex;
    gap: 12px
}

.icon[data-v-8109ab17],
.menu-buttons[data-v-8109ab17] {
    align-items: center;
    justify-content: center
}

.icon[data-v-8109ab17] {
    color: #232425;
    display: inline-flex;
    height: 14px;
    margin-right: 0;
    width: 14px
}

.icon svg[data-v-8109ab17] {
    height: 100%;
    width: 100%
}

.button[data-v-8109ab17] {
    align-items: center;
    background: #fff;
    color: #232425;
    cursor: pointer;
    display: inline-flex;
    font-size: 12px;
    height: 16px;
    justify-content: center;
    padding: 0;
    transition: all .2s ease;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 16px
}

@media (hover:hover) {
    .button[data-v-8109ab17]:hover {
        background: #f5f5f5;
        border-radius: 4px;
        transform: scale(1.1)
    }
}

.button-active[data-v-8109ab17] {
    background-color: #eff6ff !important;
    border-radius: 4px;
    transform: scale(1.05)
}

.button-active[data-v-8109ab17],
.button-active .icon[data-v-8109ab17] {
    color: #1677ff !important
}

@media (hover:hover) {
    .button-active[data-v-8109ab17]:hover {
        background-color: #dbeafe !important;
        transform: scale(1.1)
    }
}

.button-disabled[data-v-8109ab17] {
    background: #f5f5f5 !important;
    cursor: not-allowed !important;
    opacity: .5;
    pointer-events: none
}

.button-disabled[data-v-8109ab17],
.button-disabled .icon[data-v-8109ab17] {
    color: #bfbfbf !important
}

@media (hover:hover) {
    .button-disabled[data-v-8109ab17]:hover {
        background: #f5f5f5 !important;
        transform: none !important
    }
}

@media (max-width:768px) {
    .float-menu[data-v-8109ab17] {
        padding: 6px
    }
    .menu-buttons[data-v-8109ab17] {
        gap: 10px
    }
    .button[data-v-8109ab17] {
        font-size: 11px;
        height: 14px;
        padding: 0;
        width: 14px
    }
    .icon[data-v-8109ab17] {
        height: 12px;
        margin-right: 0;
        width: 12px
    }
}

.link-input-container[data-v-8109ab17] {
    min-width: 250px;
    padding: 8px
}

.link-input-row[data-v-8109ab17] {
    align-items: center;
    display: flex;
    gap: 8px
}

.link-input[data-v-8109ab17] {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    flex: 1;
    font-size: 14px;
    height: 32px;
    outline: none;
    padding: 0 8px;
    transition: border-color .2s
}

.link-input[data-v-8109ab17]:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px #1890ff33
}

.link-button[data-v-8109ab17] {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    height: 32px;
    outline: none;
    padding: 0 12px;
    transition: all .2s;
    white-space: nowrap
}

.link-button.confirm[data-v-8109ab17] {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff
}

.link-button.confirm[data-v-8109ab17]:hover:not(:disabled) {
    background: #40a9ff;
    border-color: #40a9ff
}

.link-button.confirm[data-v-8109ab17]:disabled {
    background: #f5f5f5;
    border-color: #d9d9d9;
    color: #bfbfbf;
    cursor: not-allowed
}

.element-border[data-v-da9f51ec] {
    border-radius: 2px;
    pointer-events: none;
    position: fixed;
    transition: all .1s ease;
    z-index: 1000
}

.border-hover[data-v-da9f51ec] {
    border: 2px dashed rgba(24, 144, 255, .8)
}

.border-hover[data-v-da9f51ec],
.border-selected[data-v-da9f51ec] {
    background-color: transparent;
    height: calc(100% + 4px);
    transform: translate(-2px, -2px);
    width: calc(100% + 4px)
}

.border-selected[data-v-da9f51ec] {
    border: 2px solid #0f7fff
}

.move-handle[data-v-da9f51ec] {
    align-items: center;
    background-color: #1890ff;
    border: 2px solid #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px #00000026;
    cursor: move;
    display: flex;
    height: 12px;
    justify-content: center;
    left: 50%;
    pointer-events: auto;
    position: absolute;
    top: -6px;
    transform: translate(-50%);
    transition: all .2s ease;
    width: 20px;
    z-index: 1002
}

.move-handle[data-v-da9f51ec]:before {
    background-color: #fff;
    border-radius: 1px;
    content: "";
    display: block;
    height: 2px;
    width: 8px
}

.move-handle[data-v-da9f51ec]:hover {
    background-color: #40a9ff;
    box-shadow: 0 4px 12px #00000040;
    transform: translate(-50%) scale(1.1)
}

.move-handle[data-v-da9f51ec]:active {
    background-color: #096dd9;
    transform: translate(-50%) scale(1.05)
}

.move-preview[data-v-da9f51ec] {
    background-color: #52c41a1a;
    border: 2px dashed #52c41a;
    border-radius: 2px;
    pointer-events: none;
    position: fixed;
    z-index: 1001
}

.move-overlay[data-v-da9f51ec] {
    background-color: transparent;
    cursor: move;
    height: 100vh;
    left: 0;
    position: fixed;
    top: 0;
    width: 100vw;
    z-index: 1004
}

.element-label[data-v-da9f51ec],
.move-overlay[data-v-da9f51ec] {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.element-label[data-v-da9f51ec] {
    border-radius: 2px;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, system-ui, sans-serif;
    font-size: 11px;
    font-weight: 500;
    line-height: 1.2;
    padding: 2px 6px;
    position: absolute;
    white-space: nowrap
}

.label-hover[data-v-da9f51ec] {
    background-color: #1890ffe6;
    color: #fff;
    left: 0;
    top: -20px
}

.label-selected[data-v-da9f51ec] {
    background-color: #1890ff;
    color: #fff;
    font-weight: 600;
    left: 0;
    top: -22px
}

.element-border[data-label-below] .element-label[data-v-da9f51ec] {
    margin-top: 2px;
    top: 100%
}

.border-active[data-v-da9f51ec] {
    animation: borderAppear-da9f51ec .2s ease
}

@keyframes borderAppear-da9f51ec {
    0% {
        opacity: 0;
        transform: scale(.95)
    }
    to {
        opacity: 1;
        transform: scale(1)
    }
}

@media (max-width:768px) {
    .element-label[data-v-da9f51ec] {
        font-size: 10px;
        padding: 1px 4px
    }
    .label-hover[data-v-da9f51ec] {
        top: -18px
    }
    .label-selected[data-v-da9f51ec] {
        top: -20px
    }
    .move-handle[data-v-da9f51ec] {
        height: 14px;
        top: -7px;
        width: 22px
    }
}

.resize-border[data-v-4a340028] {
    background-color: transparent;
    border: 2px solid #1890ff;
    border-radius: 2px;
    box-shadow: 0 0 0 1px #fffc;
    pointer-events: none;
    position: fixed;
    z-index: 1000
}

.resize-handle[data-v-4a340028] {
    background-color: #1890ff;
    border: 1px solid #fff;
    border-radius: 1px;
    box-shadow: 0 1px 4px #0000004d;
    height: 8px;
    pointer-events: auto;
    position: fixed;
    transition: all .2s ease;
    width: 8px;
    z-index: 1002
}

.resize-handle[data-v-4a340028]:hover {
    background-color: #40a9ff;
    box-shadow: 0 2px 8px #0006;
    transform: scale(1.2)
}

.resize-handle[data-v-4a340028]:active {
    background-color: #096dd9;
    transform: scale(1.1)
}

.resize-handle-nw[data-v-4a340028] {
    cursor: nw-resize
}

.resize-handle-ne[data-v-4a340028] {
    cursor: ne-resize
}

.resize-handle-sw[data-v-4a340028] {
    cursor: sw-resize
}

.resize-handle-se[data-v-4a340028] {
    cursor: se-resize
}

.resize-preview[data-v-4a340028] {
    background-color: #1890ff1a;
    border: 2px dashed #1890ff;
    border-radius: 2px;
    pointer-events: none;
    position: fixed;
    z-index: 1001
}

.drag-overlay[data-v-4a340028] {
    background-color: transparent;
    height: 100vh;
    left: 0;
    position: fixed;
    top: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 100vw;
    z-index: 1004
}

@media (max-width:768px) {
    .resize-handle[data-v-4a340028] {
        height: 10px;
        width: 10px
    }
}

.ai-rewrite-panel[data-v-c3c92521] {
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 8px 24px #0000001f, 0 4px 8px #00000014;
    max-height: 500px;
    position: fixed;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    z-index: 1002
}

.ai-rewrite-container[data-v-c3c92521],
.ai-rewrite-panel[data-v-c3c92521] {
    display: flex;
    flex-direction: column;
    overflow: hidden
}

.ai-rewrite-container[data-v-c3c92521] {
    flex: 1;
    gap: 12px;
    padding: 16px
}

.ai-rewrite-header[data-v-c3c92521] {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px
}

.ai-rewrite-title[data-v-c3c92521] {
    align-items: center;
    color: #232425;
    display: flex;
    font-size: 16px;
    font-weight: 600;
    gap: 8px
}

.icon[data-v-c3c92521] {
    align-items: center;
    display: inline-flex;
    height: 18px;
    justify-content: center;
    width: 18px
}

.icon svg[data-v-c3c92521] {
    height: 100%;
    width: 100%
}

.close-button[data-v-c3c92521] {
    background: none;
    border: none;
    border-radius: 4px;
    color: #8c8c8c;
    cursor: pointer;
    font-size: 20px;
    line-height: 1;
    padding: 4px;
    transition: all .2s
}

.close-button[data-v-c3c92521]:hover {
    background: #f5f5f5;
    color: #232425
}

.ai-rewrite-input-row[data-v-c3c92521] {
    align-items: center;
    display: flex;
    gap: 8px
}

.ai-rewrite-input[data-v-c3c92521] {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    flex: 1;
    font-size: 14px;
    height: 36px;
    outline: none;
    padding: 0 12px;
    transition: border-color .2s
}

.ai-rewrite-input[data-v-c3c92521]:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px #1890ff33
}

.ai-rewrite-input[data-v-c3c92521]:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed
}

.ai-rewrite-button[data-v-c3c92521] {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    height: 36px;
    outline: none;
    padding: 0 16px;
    transition: all .2s;
    white-space: nowrap
}

.ai-rewrite-button.confirm[data-v-c3c92521] {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff
}

.ai-rewrite-button.confirm[data-v-c3c92521]:hover:not(:disabled) {
    background: #40a9ff;
    border-color: #40a9ff
}

.ai-rewrite-button.confirm[data-v-c3c92521]:disabled {
    background: #f5f5f5;
    border-color: #d9d9d9;
    color: #bfbfbf;
    cursor: not-allowed
}

.ai-rewrite-button.replace[data-v-c3c92521] {
    background: #52c41a;
    border-color: #52c41a;
    color: #fff
}

.ai-rewrite-button.replace[data-v-c3c92521]:hover {
    background: #73d13d;
    border-color: #73d13d
}

.ai-rewrite-button.cancel[data-v-c3c92521] {
    background: #fff;
    border-color: #d9d9d9;
    color: #595959
}

.ai-rewrite-button.cancel[data-v-c3c92521]:hover {
    background: #f5f5f5;
    border-color: #bfbfbf
}

.ai-rewrite-response[data-v-c3c92521] {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 8px;
    overflow: hidden
}

.response-label[data-v-c3c92521] {
    color: #8c8c8c;
    font-size: 12px;
    font-weight: 500
}

.response-content[data-v-c3c92521] {
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    flex: 1;
    font-size: 14px;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
    padding: 12px
}

.response-actions[data-v-c3c92521] {
    display: flex;
    gap: 8px;
    justify-content: flex-end
}

.message-item[data-v-c3c92521] {
    margin-bottom: 8px
}

.message-item[data-v-c3c92521]:last-child {
    margin-bottom: 0
}

.response-loading[data-v-c3c92521] {
    align-items: center;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    padding: 12px
}

.tool-calls-display[data-v-c3c92521] {
    color: #8c8c8c;
    font-size: 12px;
    margin-top: 4px
}

.tool-calls-label[data-v-c3c92521] {
    font-weight: 700
}

.tool-call-name[data-v-c3c92521] {
    margin-left: 4px
}

.original-content-reference[data-v-c3c92521] {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-left: 3px solid #1890ff;
    border-radius: 6px;
    padding: 8px 12px
}

.reference-label[data-v-c3c92521] {
    color: #6c757d;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 4px
}

.reference-content[data-v-c3c92521] {
    color: #495057;
    font-size: 13px;
    font-style: italic;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.placeholder-overlay[data-v-89dd18ae] {
    align-items: flex-start;
    box-sizing: border-box;
    cursor: text;
    display: flex;
    justify-content: flex-start;
    padding: 40px 80px;
    pointer-events: auto;
    position: absolute;
    z-index: 1000
}

.placeholder-content[data-v-89dd18ae] {
    height: auto;
    width: 100%
}

.placeholder-overlay.small-width[data-v-89dd18ae] {
    padding: 24px 30px
}

.placeholder-title[data-v-89dd18ae] {
    color: #9ca3af;
    font-size: 24px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 8px
}

.placeholder-subtitle[data-v-89dd18ae],
.placeholder-title[data-v-89dd18ae] {
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif
}

.placeholder-subtitle[data-v-89dd18ae] {
    color: #d1d5db;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5
}

.placeholder-overlay[data-v-89dd18ae] {
    transition: opacity .2s ease-in-out
}

.slide-height-overflow-indicator[data-v-b65fdcae] {
    opacity: .9;
    z-index: 1002
}

.overflow-warning[data-v-b65fdcae],
.slide-height-overflow-indicator[data-v-b65fdcae] {
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0
}

.overflow-warning[data-v-b65fdcae] {
    bottom: calc(100% + 10px);
    box-sizing: border-box;
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    height: auto;
    padding: 8px 12px;
    text-align: center;
    z-index: 1003
}

.overflow-warning[data-v-b65fdcae],
.overflow-warning-content[data-v-b65fdcae] {
    align-items: center;
    display: flex;
    justify-content: center
}

.overflow-warning-content[data-v-b65fdcae] {
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
    background: #000000bf;
    border-radius: 6px;
    flex-wrap: wrap;
    gap: 8px;
    padding: 6px 12px
}

.overflow-icon[data-v-b65fdcae] {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, .2));
    font-size: 14px
}

.overflow-text[data-v-b65fdcae] {
    font-weight: 700;
    text-shadow: 0 1px 3px rgba(0, 0, 0, .8);
    white-space: nowrap
}

.overflow-suggestion[data-v-b65fdcae] {
    font-size: 11px;
    font-style: italic;
    opacity: .95;
    text-shadow: 0 1px 2px rgba(0, 0, 0, .6)
}

.overflow-striped-pattern[data-v-b65fdcae] {
    background-image: repeating-linear-gradient(45deg, #ff6b6b1a, #ff6b6b1a 10px, #ff6b6b33 0, #ff6b6b33 20px);
    border: 2px dashed #ff6b6b;
    border-radius: 0 0 8px 8px;
    border-top: none;
    bottom: 0;
    box-sizing: border-box;
    left: 0;
    position: absolute;
    right: 0;
    top: 0
}

@keyframes pulseWarning-b65fdcae {
    0%,
    to {
        opacity: .9;
        transform: scale(1)
    }
    50% {
        opacity: 1;
        transform: scale(1.02)
    }
}

@media (max-width:768px) {
    .overflow-warning-content[data-v-b65fdcae] {
        flex-direction: column;
        gap: 4px
    }
    .overflow-suggestion[data-v-b65fdcae],
    .overflow-text[data-v-b65fdcae] {
        font-size: 11px
    }
}

.warning-fade-enter-active[data-v-b65fdcae] {
    transition: all .3s ease-out
}

.warning-fade-leave-active[data-v-b65fdcae] {
    transition: all .6s ease-in
}

.warning-fade-enter-from[data-v-b65fdcae] {
    opacity: 0;
    transform: translateY(-10px) scale(.95)
}

.warning-fade-leave-to[data-v-b65fdcae] {
    opacity: 0;
    transform: translateY(-5px) scale(.98)
}

.warning-fade-enter-to[data-v-b65fdcae],
.warning-fade-leave-from[data-v-b65fdcae] {
    opacity: 1;
    transform: translateY(0) scale(1)
}

.editor-wrapper[data-v-323f4d3d] {
    background-color: #fafafa;
    box-sizing: border-box;
    color: #232425;
    height: 100%;
    overflow: auto;
    padding: 0 20px;
    position: relative;
    width: 100%
}

.editor-wrapper.disabled[data-v-323f4d3d] {
    overflow: hidden;
    pointer-events: none
}

.slide-editor.editor-wrapper[data-v-323f4d3d] {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 20px;
    justify-content: center;
    margin-left: var(--43168733);
    --standard-easing: cubic-bezier(.4, 0, .2, 1);
    --radius-md: 8px;
    transition: all .25s var(--standard-easing)
}

.editor-disabled-overlay[data-v-323f4d3d] {
    background-color: #00000080;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1005
}

.editor-container[data-v-323f4d3d] {
    border: 1px solid #eaeaea;
    border-radius: 12px;
    min-height: max(calc(100vh - 300px), 400px);
    overflow: hidden;
    position: relative
}

.docs_agent .editor-container[data-v-323f4d3d] {
    box-shadow: 0 4px 15px #0000001a;
    margin: 40px auto 120px;
    max-width: 880px
}

.slide-editor .editor-container[data-v-323f4d3d] {
    background-color: var(--37ab4669);
    border-radius: var(--3ee3bd0a);
    box-shadow: var(--34dc67b2);
    height: var(--5716d944);
    min-height: var(--2038a25e);
    min-width: var(--967e591e);
    overflow: var(--6e372f64);
    transition: var(--1ddfdd89);
    width: var(--536d27cb)
}

.editor-iframe[data-v-323f4d3d] {
    background-color: #fff;
    border: none;
    display: block;
    overflow: hidden;
    width: 100%
}

.slide-editor .editor-iframe[data-v-323f4d3d] {
    border: var(--e02d6cb4);
    border-radius: var(--cc3dc402);
    transform: var(--5ae8e47d);
    transform-origin: var(--dfa4ce74);
    width: var(--5a03058a)
}

.hover-tip[data-v-323f4d3d] {
    animation: fadeIn-323f4d3d .2s ease-out;
    pointer-events: none;
    position: fixed;
    z-index: 1003
}

.hover-tip-content[data-v-323f4d3d] {
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    background: #000000d9;
    border-radius: 6px;
    box-shadow: 0 4px 12px #00000026;
    color: #fff;
    font-size: 12px;
    line-height: 1.4;
    max-width: 200px;
    padding: 8px 12px
}

.hover-tip-title[data-v-323f4d3d] {
    color: #fff;
    font-weight: 600;
    margin-bottom: 4px
}

.hover-tip-actions[data-v-323f4d3d] {
    display: flex;
    flex-direction: column;
    gap: 2px
}

.hover-tip-action[data-v-323f4d3d] {
    align-items: center;
    display: flex;
    font-size: 11px;
    gap: 6px;
    opacity: .9
}

.hover-tip-action kbd[data-v-323f4d3d] {
    background: #fff3;
    border: 1px solid hsla(0, 0%, 100%, .3);
    border-radius: 3px;
    color: #fff;
    font-family: inherit;
    font-size: 10px;
    min-width: 16px;
    padding: 1px 4px;
    text-align: center
}

@keyframes fadeIn-323f4d3d {
    0% {
        opacity: 0;
        transform: translateY(-5px)
    }
    to {
        opacity: 1;
        transform: translateY(0)
    }
}

@media (max-width:500px) {
    .editor-wrapper[data-v-323f4d3d] {
        overflow-x: hidden;
        padding: 0
    }
    .editor-container[data-v-323f4d3d] {
        transform: none;
        transform-origin: top left;
        width: 100%
    }
    .docs_agent .editor-container[data-v-323f4d3d] {
        padding-bottom: 300px;
        position: absolute;
        transform: scale(.5);
        transform-origin: top left;
        width: 200%
    }
}