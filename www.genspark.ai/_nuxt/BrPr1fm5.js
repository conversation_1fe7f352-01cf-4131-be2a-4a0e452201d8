import {
    aL as o,
    a$ as e,
    aN as r,
    al as l,
    am as n,
    aO as a,
    aP as c,
    ak as s,
    J as t,
    X as i,
    b0 as d,
    r as h,
    Y as g,
    Z as b,
    K as C,
    aR as v,
    aV as p,
    c as u,
    ap as k,
    aq as f,
    aT as m
} from "./Cf0SOiw0.js";
import {
    r as x,
    c as P
} from "./B7VeW_-d.js";
import {
    a as y
} from "./pB_XRIgB.js";
import {
    c as I
} from "./DAjjhrgi.js";
const z = {
        common: o,
        self: function(o) {
            const {
                textColor2: l,
                primaryColorHover: n,
                primaryColorPressed: a,
                primaryColor: c,
                infoColor: s,
                successColor: t,
                warningColor: i,
                errorColor: d,
                baseColor: h,
                borderColor: g,
                opacityDisabled: b,
                tagColor: C,
                closeIconColor: v,
                closeIconColorHover: p,
                closeIconColorPressed: u,
                borderRadiusSmall: k,
                fontSizeMini: f,
                fontSizeTiny: m,
                fontSizeSmall: x,
                fontSizeMedium: P,
                heightMini: y,
                heightTiny: I,
                heightSmall: z,
                heightMedium: S,
                closeColorHover: $,
                closeColorPressed: B,
                buttonColor2Hover: H,
                buttonColor2Pressed: R,
                fontWeightStrong: j
            } = o;
            return Object.assign(Object.assign({}, e), {
                closeBorderRadius: k,
                heightTiny: y,
                heightSmall: I,
                heightMedium: z,
                heightLarge: S,
                borderRadius: k,
                opacityDisabled: b,
                fontSizeTiny: f,
                fontSizeSmall: m,
                fontSizeMedium: x,
                fontSizeLarge: P,
                fontWeightStrong: j,
                textColorCheckable: l,
                textColorHoverCheckable: l,
                textColorPressedCheckable: l,
                textColorChecked: h,
                colorCheckable: "#0000",
                colorHoverCheckable: H,
                colorPressedCheckable: R,
                colorChecked: c,
                colorCheckedHover: n,
                colorCheckedPressed: a,
                border: `1px solid ${g}`,
                textColor: l,
                color: C,
                colorBordered: "rgb(250, 250, 252)",
                closeIconColor: v,
                closeIconColorHover: p,
                closeIconColorPressed: u,
                closeColorHover: $,
                closeColorPressed: B,
                borderPrimary: `1px solid ${r(c,{alpha:.3})}`,
                textColorPrimary: c,
                colorPrimary: r(c, {
                    alpha: .12
                }),
                colorBorderedPrimary: r(c, {
                    alpha: .1
                }),
                closeIconColorPrimary: c,
                closeIconColorHoverPrimary: c,
                closeIconColorPressedPrimary: c,
                closeColorHoverPrimary: r(c, {
                    alpha: .12
                }),
                closeColorPressedPrimary: r(c, {
                    alpha: .18
                }),
                borderInfo: `1px solid ${r(s,{alpha:.3})}`,
                textColorInfo: s,
                colorInfo: r(s, {
                    alpha: .12
                }),
                colorBorderedInfo: r(s, {
                    alpha: .1
                }),
                closeIconColorInfo: s,
                closeIconColorHoverInfo: s,
                closeIconColorPressedInfo: s,
                closeColorHoverInfo: r(s, {
                    alpha: .12
                }),
                closeColorPressedInfo: r(s, {
                    alpha: .18
                }),
                borderSuccess: `1px solid ${r(t,{alpha:.3})}`,
                textColorSuccess: t,
                colorSuccess: r(t, {
                    alpha: .12
                }),
                colorBorderedSuccess: r(t, {
                    alpha: .1
                }),
                closeIconColorSuccess: t,
                closeIconColorHoverSuccess: t,
                closeIconColorPressedSuccess: t,
                closeColorHoverSuccess: r(t, {
                    alpha: .12
                }),
                closeColorPressedSuccess: r(t, {
                    alpha: .18
                }),
                borderWarning: `1px solid ${r(i,{alpha:.35})}`,
                textColorWarning: i,
                colorWarning: r(i, {
                    alpha: .15
                }),
                colorBorderedWarning: r(i, {
                    alpha: .12
                }),
                closeIconColorWarning: i,
                closeIconColorHoverWarning: i,
                closeIconColorPressedWarning: i,
                closeColorHoverWarning: r(i, {
                    alpha: .12
                }),
                closeColorPressedWarning: r(i, {
                    alpha: .18
                }),
                borderError: `1px solid ${r(d,{alpha:.23})}`,
                textColorError: d,
                colorError: r(d, {
                    alpha: .1
                }),
                colorBorderedError: r(d, {
                    alpha: .08
                }),
                closeIconColorError: d,
                closeIconColorHoverError: d,
                closeIconColorPressedError: d,
                closeColorHoverError: r(d, {
                    alpha: .12
                }),
                closeColorPressedError: r(d, {
                    alpha: .18
                })
            })
        }
    },
    S = {
        color: Object,
        type: {
            type: String,
            default: "default"
        },
        round: Boolean,
        size: {
            type: String,
            default: "medium"
        },
        closable: Boolean,
        disabled: {
            type: Boolean,
            default: void 0
        }
    },
    $ = l("tag", "\n --n-close-margin: var(--n-close-margin-top) var(--n-close-margin-right) var(--n-close-margin-bottom) var(--n-close-margin-left);\n white-space: nowrap;\n position: relative;\n box-sizing: border-box;\n cursor: default;\n display: inline-flex;\n align-items: center;\n flex-wrap: nowrap;\n padding: var(--n-padding);\n border-radius: var(--n-border-radius);\n color: var(--n-text-color);\n background-color: var(--n-color);\n transition: \n border-color .3s var(--n-bezier),\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier),\n box-shadow .3s var(--n-bezier),\n opacity .3s var(--n-bezier);\n line-height: 1;\n height: var(--n-height);\n font-size: var(--n-font-size);\n", [n("strong", "\n font-weight: var(--n-font-weight-strong);\n "), a("border", "\n pointer-events: none;\n position: absolute;\n left: 0;\n right: 0;\n top: 0;\n bottom: 0;\n border-radius: inherit;\n border: var(--n-border);\n transition: border-color .3s var(--n-bezier);\n "), a("icon", "\n display: flex;\n margin: 0 4px 0 0;\n color: var(--n-text-color);\n transition: color .3s var(--n-bezier);\n font-size: var(--n-avatar-size-override);\n "), a("avatar", "\n display: flex;\n margin: 0 6px 0 0;\n "), a("close", "\n margin: var(--n-close-margin);\n transition:\n background-color .3s var(--n-bezier),\n color .3s var(--n-bezier);\n "), n("round", "\n padding: 0 calc(var(--n-height) / 3);\n border-radius: calc(var(--n-height) / 2);\n ", [a("icon", "\n margin: 0 4px 0 calc((var(--n-height) - 8px) / -2);\n "), a("avatar", "\n margin: 0 6px 0 calc((var(--n-height) - 8px) / -2);\n "), n("closable", "\n padding: 0 calc(var(--n-height) / 4) 0 calc(var(--n-height) / 3);\n ")]), n("icon, avatar", [n("round", "\n padding: 0 calc(var(--n-height) / 3) 0 calc(var(--n-height) / 2);\n ")]), n("disabled", "\n cursor: not-allowed !important;\n opacity: var(--n-opacity-disabled);\n "), n("checkable", "\n cursor: pointer;\n box-shadow: none;\n color: var(--n-text-color-checkable);\n background-color: var(--n-color-checkable);\n ", [c("disabled", [s("&:hover", "background-color: var(--n-color-hover-checkable);", [c("checked", "color: var(--n-text-color-hover-checkable);")]), s("&:active", "background-color: var(--n-color-pressed-checkable);", [c("checked", "color: var(--n-text-color-pressed-checkable);")])]), n("checked", "\n color: var(--n-text-color-checked);\n background-color: var(--n-color-checked);\n ", [c("disabled", [s("&:hover", "background-color: var(--n-color-checked-hover);"), s("&:active", "background-color: var(--n-color-checked-pressed);")])])])]),
    B = Object.assign(Object.assign(Object.assign({}, b.props), S), {
        bordered: {
            type: Boolean,
            default: void 0
        },
        checked: Boolean,
        checkable: Boolean,
        strong: Boolean,
        triggerClickOnClose: Boolean,
        onClose: [Array, Function],
        onMouseenter: Function,
        onMouseleave: Function,
        "onUpdate:checked": Function,
        onUpdateChecked: Function,
        internalCloseFocusable: {
            type: Boolean,
            default: !0
        },
        internalCloseIsButtonTag: {
            type: Boolean,
            default: !0
        },
        onCheckedChange: Function
    }),
    H = m("n-tag"),
    R = t({
        name: "Tag",
        props: B,
        slots: Object,
        setup(o) {
            const e = h(null),
                {
                    mergedBorderedRef: r,
                    mergedClsPrefixRef: l,
                    inlineThemeDisabled: n,
                    mergedRtlRef: a
                } = g(o),
                c = b("Tag", "-tag", $, z, o, l);
            C(H, {
                roundRef: v(o, "round")
            });
            const s = {
                    setTextContent(o) {
                        const {
                            value: r
                        } = e;
                        r && (r.textContent = o)
                    }
                },
                t = p("Tag", a, l),
                i = u((() => {
                    const {
                        type: e,
                        size: l,
                        color: {
                            color: n,
                            textColor: a
                        } = {}
                    } = o, {
                        common: {
                            cubicBezierEaseInOut: s
                        },
                        self: {
                            padding: t,
                            closeMargin: i,
                            borderRadius: d,
                            opacityDisabled: h,
                            textColorCheckable: g,
                            textColorHoverCheckable: b,
                            textColorPressedCheckable: C,
                            textColorChecked: v,
                            colorCheckable: p,
                            colorHoverCheckable: u,
                            colorPressedCheckable: f,
                            colorChecked: m,
                            colorCheckedHover: x,
                            colorCheckedPressed: P,
                            closeBorderRadius: I,
                            fontWeightStrong: z,
                            [k("colorBordered", e)]: S,
                            [k("closeSize", l)]: $,
                            [k("closeIconSize", l)]: B,
                            [k("fontSize", l)]: H,
                            [k("height", l)]: R,
                            [k("color", e)]: j,
                            [k("textColor", e)]: M,
                            [k("border", e)]: O,
                            [k("closeIconColor", e)]: T,
                            [k("closeIconColorHover", e)]: E,
                            [k("closeIconColorPressed", e)]: W,
                            [k("closeColorHover", e)]: w,
                            [k("closeColorPressed", e)]: _
                        }
                    } = c.value, F = y(i);
                    return {
                        "--n-font-weight-strong": z,
                        "--n-avatar-size-override": `calc(${R} - 8px)`,
                        "--n-bezier": s,
                        "--n-border-radius": d,
                        "--n-border": O,
                        "--n-close-icon-size": B,
                        "--n-close-color-pressed": _,
                        "--n-close-color-hover": w,
                        "--n-close-border-radius": I,
                        "--n-close-icon-color": T,
                        "--n-close-icon-color-hover": E,
                        "--n-close-icon-color-pressed": W,
                        "--n-close-icon-color-disabled": T,
                        "--n-close-margin-top": F.top,
                        "--n-close-margin-right": F.right,
                        "--n-close-margin-bottom": F.bottom,
                        "--n-close-margin-left": F.left,
                        "--n-close-size": $,
                        "--n-color": n || (r.value ? S : j),
                        "--n-color-checkable": p,
                        "--n-color-checked": m,
                        "--n-color-checked-hover": x,
                        "--n-color-checked-pressed": P,
                        "--n-color-hover-checkable": u,
                        "--n-color-pressed-checkable": f,
                        "--n-font-size": H,
                        "--n-height": R,
                        "--n-opacity-disabled": h,
                        "--n-padding": t,
                        "--n-text-color": a || M,
                        "--n-text-color-checkable": g,
                        "--n-text-color-checked": v,
                        "--n-text-color-hover-checkable": b,
                        "--n-text-color-pressed-checkable": C
                    }
                })),
                d = n ? f("tag", u((() => {
                    let e = "";
                    const {
                        type: l,
                        size: n,
                        color: {
                            color: a,
                            textColor: c
                        } = {}
                    } = o;
                    return e += l[0], e += n[0], a && (e += `a${I(a)}`), c && (e += `b${I(c)}`), r.value && (e += "c"), e
                })), i, o) : void 0;
            return Object.assign(Object.assign({}, s), {
                rtlEnabled: t,
                mergedClsPrefix: l,
                contentRef: e,
                mergedBordered: r,
                handleClick: function() {
                    if (!o.disabled && o.checkable) {
                        const {
                            checked: e,
                            onCheckedChange: r,
                            onUpdateChecked: l,
                            "onUpdate:checked": n
                        } = o;
                        l && l(!e), n && n(!e), r && r(!e)
                    }
                },
                handleCloseClick: function(e) {
                    if (o.triggerClickOnClose || e.stopPropagation(), !o.disabled) {
                        const {
                            onClose: r
                        } = o;
                        r && P(r, e)
                    }
                },
                cssVars: n ? void 0 : i,
                themeClass: null == d ? void 0 : d.themeClass,
                onRender: null == d ? void 0 : d.onRender
            })
        },
        render() {
            var o, e;
            const {
                mergedClsPrefix: r,
                rtlEnabled: l,
                closable: n,
                color: {
                    borderColor: a
                } = {},
                round: c,
                onRender: s,
                $slots: t
            } = this;
            null == s || s();
            const h = x(t.avatar, (o => o && i("div", {
                    class: `${r}-tag__avatar`
                }, o))),
                g = x(t.icon, (o => o && i("div", {
                    class: `${r}-tag__icon`
                }, o)));
            return i("div", {
                class: [`${r}-tag`, this.themeClass, {
                    [`${r}-tag--rtl`]: l,
                    [`${r}-tag--strong`]: this.strong,
                    [`${r}-tag--disabled`]: this.disabled,
                    [`${r}-tag--checkable`]: this.checkable,
                    [`${r}-tag--checked`]: this.checkable && this.checked,
                    [`${r}-tag--round`]: c,
                    [`${r}-tag--avatar`]: h,
                    [`${r}-tag--icon`]: g,
                    [`${r}-tag--closable`]: n
                }],
                style: this.cssVars,
                onClick: this.handleClick,
                onMouseenter: this.onMouseenter,
                onMouseleave: this.onMouseleave
            }, g || h, i("span", {
                class: `${r}-tag__content`,
                ref: "contentRef"
            }, null === (e = (o = this.$slots).default) || void 0 === e ? void 0 : e.call(o)), !this.checkable && n ? i(d, {
                clsPrefix: r,
                class: `${r}-tag__close`,
                disabled: this.disabled,
                onClick: this.handleCloseClick,
                focusable: this.internalCloseFocusable,
                round: c,
                isButtonTag: this.internalCloseIsButtonTag,
                absolute: !0
            }) : null, !this.checkable && this.mergedBordered ? i("div", {
                class: `${r}-tag__border`,
                style: {
                    borderColor: a
                }
            }) : null)
        }
    });
export {
    R as N
};