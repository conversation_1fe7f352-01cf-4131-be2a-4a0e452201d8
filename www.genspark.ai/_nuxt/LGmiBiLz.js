import {
    e as t,
    f as l,
    i as e,
    h as i,
    j as C,
    k as n,
    l as h,
    m as w,
    d as r,
    n as s
} from "./zH1ZpJ79.js";
import {
    d as o,
    o as L,
    b as f,
    J as u,
    c as d,
    n as g,
    a as x,
    y as c,
    _ as a
} from "./Cf0SOiw0.js";
const p = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const v = {
        render: function(t, l) {
            return L(), o("svg", p, l[0] || (l[0] = [f("path", {
                d: "M20 6H11L9 4H4C2.9 4 2 4.9 2 6V9.14286H22V8C22 6.9 21.1 6 20 6Z",
                fill: "#232425"
            }, null, -1), f("path", {
                d: "M4 6.85742H20C20.6266 6.85742 21.1426 7.37339 21.1426 8V18C21.1426 18.6266 20.6266 19.1426 20 19.1426H4C3.37339 19.1426 2.85742 18.6266 2.85742 18V8C2.85742 7.37339 3.37339 6.85742 4 6.85742Z",
                stroke: "#232425",
                "stroke-width": "1.71429"
            }, null, -1)]))
        }
    },
    H = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const V = {
        render: function(t, l) {
            return L(), o("svg", H, l[0] || (l[0] = [f("rect", {
                x: "2",
                y: "2",
                width: "20",
                height: "20",
                rx: "4",
                fill: "#3B82F6"
            }, null, -1), f("path", {
                d: "M8.41463 9.68C8.41463 8.756 7.64634 8 6.70732 8C5.72561 8 5 8.756 5 9.68C5 10.646 5.72561 11.36 6.70732 11.36C7.64634 11.36 8.41463 10.646 8.41463 9.68Z",
                fill: "white"
            }, null, -1), f("path", {
                d: "M5.34146 14.384V16.4H19V13.04L15.2439 9.386C15.0305 9.176 14.7317 9.176 14.5183 9.386L9.43902 14.384L7.73171 12.746C7.51829 12.536 7.21951 12.536 7.0061 12.746L5.34146 14.384Z",
                fill: "white"
            }, null, -1)]))
        }
    },
    y = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const m = {
        render: function(t, l) {
            return L(), o("svg", y, l[0] || (l[0] = [f("rect", {
                x: "2",
                y: "2",
                width: "20",
                height: "20",
                rx: "4",
                fill: "#3ECD8C"
            }, null, -1), f("path", {
                d: "M17.25 9.42188C17.25 8.71875 16.4062 8.39062 15.9375 8.85938L13.5 11.3438V9.5625C13.5 9.04688 13.0781 8.625 12.5625 8.625H7.6875C7.125 8.625 6.75 9.04688 6.75 9.5625V14.4375C6.75 15 7.125 15.375 7.6875 15.375H12.5625C13.0781 15.375 13.5 15 13.5 14.4375V12.7031L15.9375 15.1875C16.4062 15.6562 17.25 15.3281 17.25 14.625V9.42188Z",
                fill: "white"
            }, null, -1)]))
        }
    },
    M = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Z = {
        render: function(t, l) {
            return L(), o("svg", M, l[0] || (l[0] = [f("rect", {
                x: "2",
                y: "2",
                width: "20",
                height: "20",
                rx: "4",
                fill: "#EF4444"
            }, null, -1), f("path", {
                d: "M10.5595 7.25096C10.8113 6.34566 12.522 6.2452 12.7236 7.55272C12.975 8.30743 12.6225 9.56616 12.4716 10.3711C12.8741 11.4273 13.428 12.1825 14.2831 12.6855C15.1889 12.5849 16.9499 12.4336 17.5536 13.0371C18.0563 13.5404 17.9547 14.9488 16.6474 14.9492C15.8928 14.949 14.7856 14.6469 13.83 14.0937C12.7737 14.295 11.5157 14.748 10.4091 15.1504C7.89345 19.4773 6.43395 17.515 6.58489 16.7099C6.78625 15.7038 8.14509 14.8986 9.15129 14.3955C9.65442 13.4395 10.409 11.8294 10.912 10.6221C10.5599 9.21343 10.3583 8.05592 10.5595 7.25096ZM9.10051 15.2031C8.79856 15.4547 7.84344 16.2596 7.59172 16.9639C7.60931 16.9556 8.16007 16.688 9.10051 15.2031ZM11.7167 11.4765C11.3646 12.3821 10.962 13.3889 10.4589 14.2441C11.2638 13.892 12.1701 13.4888 13.2265 13.2373C12.6229 12.8348 12.1191 12.231 11.7167 11.4765ZM17.0497 13.9941C17.2509 13.8432 16.9491 13.4409 15.1884 13.5918C16.7839 14.2898 17.0452 13.9994 17.0497 13.9941ZM11.6669 7.3535C11.5664 7.35456 11.5664 8.71179 11.7675 9.416C12.0188 8.96292 12.0687 7.35495 11.6669 7.3535Z",
                fill: "white"
            }, null, -1)]))
        }
    },
    B = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const k = {
        render: function(t, l) {
            return L(), o("svg", B, l[0] || (l[0] = [f("rect", {
                x: "2",
                y: "2",
                width: "20",
                height: "20",
                rx: "4",
                fill: "#3276CD"
            }, null, -1), f("path", {
                d: "M17.2811 8H16.4106C16.0624 8 15.7722 8.2 15.7142 8.5C14.4955 13.4 14.5535 13.25 14.4955 13.65C14.4955 13.6 14.4955 13.55 14.4374 13.45C14.3794 13.2 14.4955 13.45 13.1026 8.5C12.9866 8.2 12.7544 8 12.4062 8H11.6518C11.3036 8 11.0134 8.2 10.9554 8.5C9.56257 13.45 9.56257 13.3 9.50454 13.65V13.45C9.4465 13.2 8.69206 9.8 8.40189 8.5C8.28582 8.2 8.05369 8 7.70548 8H6.7189C6.25463 8 5.90643 8.4 6.0225 8.75C6.48677 10.4 7.58941 14.25 7.93762 15.55C8.05369 15.85 8.34386 16 8.63403 16H10.0849C10.4331 16 10.7233 15.85 10.7813 15.55L11.8259 12L12 11.5L12.1741 12C12.1741 12 12.8705 14.5 13.2187 15.55C13.2767 15.8 13.5669 16 13.8571 16H15.3079C15.5981 16 15.8883 15.85 16.0043 15.55C17.165 11.45 17.7454 9.6 17.9775 8.75C18.0936 8.4 17.7454 8 17.2811 8Z",
                fill: "white"
            }, null, -1)]))
        }
    },
    F = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const _ = {
        render: function(t, l) {
            return L(), o("svg", F, l[0] || (l[0] = [f("rect", {
                x: "2",
                y: "2",
                width: "20",
                height: "20",
                rx: "4",
                fill: "#4FA16B"
            }, null, -1), f("path", {
                d: "M10.032 7.15039C10.2126 7.15044 10.3645 7.26333 10.4441 7.42969L10.447 7.43457C11.5404 9.5132 11.1898 8.88326 11.8884 10.3965L12.0369 10.7197L12.1648 10.3867C12.4608 9.61295 12.6567 9.14902 13.5515 7.43359L13.4187 7.36426L13.5525 7.43359L13.5544 7.42969C13.6339 7.26343 13.7861 7.15068 13.9665 7.15039H15.4109C15.716 7.15039 15.9439 7.49159 15.8122 7.7998L15.781 7.86035L15.7781 7.86523C15.3804 8.59284 14.8081 9.59476 14.3347 10.4219C14.0983 10.8349 13.8859 11.2049 13.7331 11.4746C13.6569 11.6092 13.5954 11.7203 13.5525 11.7988C13.5313 11.8376 13.5138 11.8697 13.5017 11.8936C13.4958 11.9052 13.4895 11.9169 13.4851 11.9268C13.483 11.9315 13.4808 11.9383 13.4783 11.9453C13.477 11.9488 13.4751 11.9544 13.4734 11.9609C13.4722 11.9658 13.4685 11.9809 13.4685 12V12.0391L13.488 12.0732L15.779 16.1357L15.781 16.1396C15.9733 16.4618 15.7362 16.8496 15.4109 16.8496H13.9665C13.7879 16.8494 13.6595 16.7868 13.5837 16.6738L13.5544 16.623L13.5515 16.6182L12.9519 15.4561C12.5647 14.6831 12.6881 14.8257 12.1619 13.6074L12.0261 13.292L11.8874 13.6064C11.6626 14.1159 11.5789 14.3863 11.0906 15.3662L10.447 16.6172L10.4441 16.623C10.3746 16.768 10.2362 16.8496 10.032 16.8496H8.63744C8.25204 16.8494 8.03153 16.4527 8.2185 16.1396L8.21948 16.1357L10.5115 12.0732L10.5525 12L10.5115 11.9268L8.22338 7.87207C8.14546 7.70812 8.15687 7.52472 8.23315 7.38477C8.30806 7.24769 8.44678 7.15052 8.63744 7.15039H10.032Z",
                fill: "white"
            }, null, -1)]))
        }
    },
    D = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const E = {
        render: function(t, l) {
            return L(), o("svg", D, l[0] || (l[0] = [f("rect", {
                x: "2",
                y: "2",
                width: "20",
                height: "20",
                rx: "4",
                fill: "#8B5CF6"
            }, null, -1), f("path", {
                d: "M12.875 8C14.7083 8 15.917 9.3125 15.917 11.1582C15.917 14.8496 12.2083 14.3164 11.25 14.3164V16.6543C11.25 16.9414 11.0417 17.1465 10.75 17.1465H9.5C9.20833 17.1465 9 16.9414 9 16.6543V8.49219C9 8.20508 9.20833 8 9.5 8H12.875ZM11.25 12.3887H12.375C12.75 12.3887 13.0417 12.2656 13.25 12.0605C13.6666 11.5684 13.6669 10.7071 13.292 10.2559C13.0837 10.0098 12.7918 9.92778 12.417 9.92773H11.25V12.3887Z",
                fill: "white"
            }, null, -1)]))
        }
    },
    j = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const z = {
        render: function(t, l) {
            return L(), o("svg", j, l[0] || (l[0] = [f("rect", {
                x: "2",
                y: "2",
                width: "20",
                height: "20",
                rx: "4",
                fill: "#8B5CF6"
            }, null, -1), f("path", {
                d: "M10.3594 13.6404C10.4766 13.4841 10.4766 13.2888 10.3203 13.1716V13.1326L9.03125 11.9998L10.3203 10.906C10.4766 10.7498 10.4766 10.5544 10.3594 10.3982L9.65625 9.65601C9.53906 9.53882 9.30469 9.53882 9.1875 9.65601L6.92188 11.7654C6.80469 11.9216 6.80469 12.1169 6.92188 12.2732L9.1875 14.3826C9.26562 14.4216 9.34375 14.4607 9.42188 14.4607C9.5 14.4607 9.61719 14.4216 9.65625 14.3826L10.3594 13.6404Z",
                fill: "white"
            }, null, -1), f("path", {
                d: "M13.6406 8.13257L12.6641 7.85913C12.4688 7.82007 12.3125 7.93726 12.2344 8.09351L10.125 15.4763C10.0469 15.6326 10.1641 15.8279 10.3594 15.8669L11.2969 16.1794C11.4922 16.2185 11.6875 16.1013 11.7266 15.9451L13.875 8.56226C13.9141 8.40601 13.7969 8.2107 13.6406 8.13257Z",
                fill: "white"
            }, null, -1), f("path", {
                d: "M13.6016 10.3982C13.4844 10.5154 13.4844 10.7498 13.6406 10.8669V10.906L14.9297 11.9998L13.6406 13.1326C13.4844 13.2888 13.4844 13.4841 13.6016 13.6404L14.3047 14.3826C14.4219 14.4998 14.6562 14.4998 14.7734 14.3826L17.0391 12.2732C17.1562 12.1169 17.1562 11.8826 17.0391 11.7654L14.7734 9.65601C14.6562 9.53882 14.4219 9.53882 14.3047 9.65601L13.6016 10.3982Z",
                fill: "white"
            }, null, -1)]))
        }
    },
    I = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const b = {
        render: function(t, l) {
            return L(), o("svg", I, l[0] || (l[0] = [f("rect", {
                x: "2",
                y: "2",
                width: "20",
                height: "20",
                rx: "4",
                fill: "#EFCD47"
            }, null, -1), f("path", {
                d: "M14.3369 6.68131C14.7642 6.56908 15.225 6.76853 15.4287 7.17936L15.4678 7.2692C15.6248 7.68963 15.4644 8.16596 15.084 8.40494L14.999 8.45182L13.417 9.23307V13.7204L13.418 13.7311V13.7546L13.4033 14.0495C13.2652 15.4065 12.1862 16.4856 10.8291 16.6237L10.5342 16.6383C9.04095 16.6383 7.81295 15.5036 7.66504 14.0495L7.65039 13.7546C7.65075 12.162 8.94149 10.8708 10.5342 10.8708L10.7412 10.8776C11.0126 10.8971 11.2734 10.955 11.5186 11.0446V8.61979L11.5244 8.5358L11.5273 8.51139C11.571 8.20439 11.7634 7.93298 12.0469 7.79264L14.1562 6.74869L14.1904 6.73405L14.3047 6.69205L14.3369 6.68131ZM10.5342 12.7692C9.99092 12.7692 9.54917 13.2112 9.54883 13.7546L9.55469 13.8551C9.60535 14.3511 10.0246 14.7389 10.5342 14.7389L10.6348 14.7331C11.1307 14.6825 11.5183 14.2638 11.5186 13.7546L11.5127 13.654C11.4654 13.1909 11.0974 12.8223 10.6348 12.7751L10.5342 12.7692Z",
                fill: "white"
            }, null, -1)]))
        }
    },
    A = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const $ = {
        render: function(t, l) {
            return L(), o("svg", A, l[0] || (l[0] = [f("path", {
                d: "M14.2222 17.5556H12V15.3333H9.77778V13.1111H12V15.3333H14.2222M14.2222 8.66667H12V10.8889H14.2222V13.1111H12V10.8889H9.77778V8.66667H12V6.44444H9.77778V4.22222H12V6.44444H14.2222M19.7778 2H4.22222C2.98889 2 2 2.98889 2 4.22222V19.7778C2 20.3671 2.23413 20.9324 2.65087 21.3491C3.06762 21.7659 3.63285 22 4.22222 22H19.7778C20.3671 22 20.9324 21.7659 21.3491 21.3491C21.7659 20.9324 22 20.3671 22 19.7778V4.22222C22 3.63285 21.7659 3.06762 21.3491 2.65087C20.9324 2.23413 20.3671 2 19.7778 2Z",
                fill: "#EF9A44"
            }, null, -1)]))
        }
    },
    J = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const T = {
        render: function(t, l) {
            return L(), o("svg", J, l[0] || (l[0] = [f("rect", {
                x: "2",
                y: "2",
                width: "20",
                height: "20",
                rx: "4",
                fill: "#6B7280"
            }, null, -1), f("rect", {
                x: "7",
                y: "7",
                width: "10",
                height: "2",
                rx: "0.5",
                fill: "white"
            }, null, -1), f("rect", {
                x: "7",
                y: "11",
                width: "10",
                height: "2",
                rx: "0.5",
                fill: "white"
            }, null, -1), f("rect", {
                x: "7",
                y: "15",
                width: "7",
                height: "2",
                rx: "0.5",
                fill: "white"
            }, null, -1)]))
        }
    },
    q = a(u({
        __name: "FileTypeIcon",
        props: {
            file: {},
            size: {
                default: "medium"
            },
            customClass: {}
        },
        setup(f) {
            const u = f,
                a = d((() => {
                    const l = [`file-type-${t(u.file)}`, `size-${u.size}`];
                    return u.customClass && l.push(u.customClass), l
                }));
            return (t, f) => (L(), o("div", {
                class: g(["file-type-icon", a.value])
            }, [t.file.isDir ? (L(), x(c(v), {
                key: 0
            })) : c(l)(t.file) ? (L(), x(c(V), {
                key: 1
            })) : c(e)(t.file) ? (L(), x(c(m), {
                key: 2
            })) : c(i)(t.file) ? (L(), x(c(Z), {
                key: 3
            })) : c(C)(t.file) ? (L(), x(c(k), {
                key: 4
            })) : c(n)(t.file) ? (L(), x(c(_), {
                key: 5
            })) : c(h)(t.file) ? (L(), x(c(E), {
                key: 6
            })) : c(w)(t.file) ? (L(), x(c(z), {
                key: 7
            })) : c(r)(t.file) ? (L(), x(c(b), {
                key: 8
            })) : c(s)(t.file) ? (L(), x(c($), {
                key: 9
            })) : (L(), x(c(T), {
                key: 10
            }))], 2))
        }
    }), [
        ["__scopeId", "data-v-bee86d57"]
    ]);
export {
    q as F, v as I
};