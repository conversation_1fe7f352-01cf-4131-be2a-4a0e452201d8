const __vite__mapDeps = (i, m = __vite__mapDeps, d = (m.f || (m.f = ["./DK9KCCqA.js", "./D6bQc9d9.js", "./Cf0SOiw0.js", "./entry.CjsqieIi.css", "./LoadingAnimation.B2xv_2PZ.css", "./DmWk8H2v.js", "./B56nEZrv.js", "./BXgTVD7d.js", "./BYvs8isC.js", "./0tM3vo_n.js", "./Db8eFYek.js", "./CsPFbezH.js", "./DnZj1005.js", "./prompt-link-plugin.BsDk_Kem.css", "./Jx3-I-D7.js", "./Cp7w48vH.js", "./C-H3edso.js", "./BPQGB51Y.js", "./DJ-JsGJu.js", "./CSefR0kE.js", "./DyMB-pVc.js", "./Bzg9uoz_.js", "./CQ2glRxo.js", "./DAjjhrgi.js", "./MpDLC7up.js", "./B7VeW_-d.js", "./YoutubeWidget.CYLc38aB.css", "./BH52nuY1.js", "./BuZX8s8J.js", "./CRmNre8Y.js", "./Bl-gMEVt.js", "./CVrRKK4N.js", "./DOnko34f.js", "./B6noBY_5.js", "./BjWUbj3w.js", "./pB_XRIgB.js", "./BLWq1oPC.js", "./DpMvtoun.js", "./Jr9eiJio.js", "./9wLWmnxl.js", "./WZsIN7xM.js", "./mindmap_in_markdown.DgMUazV_.css", "./CQjXacSG.js", "./F4fwMVvr.js", "./qk0HrepY.js", "./wD492QF4.js", "./CqNssBtC.js", "./BqHcVhvy.js", "./DLUhQFIq.js", "./C_QvFyDp.js", "./DR_b14-4.js", "./MarkdownWithPlugins.B6XS6AW6.css", "./github.C1DJlbbM.css", "./DmzoGJ5T.js", "./Dflnlfvw.js", "./CW991W2w.js", "./BuhfKjCJ.js", "./BGK9k_mT.js", "./BUCk-Nnr.js", "./CAfqOhBF.js", "./LoadingIcon.DBGzfwQJ.css", "./WhweajiO.js", "./DOWa3jpG.js", "./4s4Iy95q.js", "./DKpDUEYb.js", "./e-ES_T8J.js", "./CAzLTKWw.js", "./CJmWj3ri.js", "./PlanPlusDesc.C8oWeb3m.css", "./9KCDKcmx.js", "./PlanProDesc.DOSRRpAW.css", "./0XKHBXCr.js", "./PricingWindowForPlusPlan.CbG0vZqH.css", "./DPPm6Skg.js", "./lJbCVLd4.js", "./PlanCreditDesc.xiOfDrvw.css", "./PricingWindowForTeamCreditsClaim.SgSSHRIB.css", "./DY44xVYu.js", "./QuotaExceed.DgKuFXXK.css", "./LGmiBiLz.js", "./zH1ZpJ79.js", "./FileTypeIcon.BQIzFgr3.css", "./nuQnue4a.js", "./NG4ombpb.js", "./DT-NG54s.js", "./gsZIXP6B.js", "./DxgY8w7w.js", "./BwHsXuPr.js", "./B0db5Fvl.js", "./AGJ7ifDi.js", "./GlobalDialog.c6DzFmQH.css", "./TableSheet.B9X5OYDV.css", "./BZ8pMrhH.js", "./CouGvJje.js", "./CIu9VtBu.js", "./Dwl0GcWq.js", "./DdaMTYTP.js", "./BLjKtaC-.js", "./mJse5lCL.js", "./D5LV4gfS.js", "./SankeyChart.DP1dwR3-.css", "./CHwVUzYg.js", "./CRIx66FB.js", "./DqWfLcpp.js", "./Bm_HbXT2.js", "./MultipleSankeyCharts.tzAjM330.css", "./tBofk-gQ.js", "./DkAysl9I.js", "./Cy7E5O2b.js", "./EchartsWidget.CvqjPENX.css", "./D5ao1EUl.js", "./DcBgjX7B.js", "./Dnth285N.js", "./DW6cX6jm.js", "./C_XD2eP3.js", "./DHyvnBfI.js", "./BDUh8PoD.js", "./DwpGavSW.js", "./BAZ5Sqbz.js", "./BSM9O9PP.js", "./ViewCompanyDetail.6HVPlPkA.css", "./StockPriceChart.D4iedbzM.css", "./x7yzXAMO.js", "./L54g9xmZ.js", "./V-H-Vsd5.js", "./ProductTemplate.BZ06eJEQ.css", "./CAmLbDGM.js", "./DQpEsQQa.js", "./feedback_dialog.DMSfPXgH.css", "./DXvAIxvL.js", "./B-XpIQkh.js", "./DGJMLFjI.js", "./By6xEfKc.js", "./C38RzRfR.js", "./BrPr1fm5.js", "./BihyrXkC.js", "./LatestReports.BMgQzIXP.css", "./SankeyReport.gKf9EE1u.css", "./DYrQUwU9.js", "./CvZgRYhs.js", "./Dc8Bac8D.js", "./BrcpPT-Q.js", "./CaEkZ53E.js", "./DeepDiveSearchVerticalResult.JZpcnYLz.css", "./DWsxX4PV.js", "./C1lFdfgL.js", "./BdlGQsae.js", "./BThdTUgg.js", "./BN-NNxvY.js", "./D9ll07Bp.js", "./BUs-AQWo.js", "./CKd5XOy1.js", "./CrbPJ6Kt.js", "./Di7Ot5aL.js", "./UVj2ej2A.js", "./CVKRwtBu.js", "./LoginGuidance.Dm3Ueo8u.css", "./CbfxwEmT.js", "./Ug8thHSu.js", "./BV6guSOS.js", "./data.Du5y1n5K.css", "./CfHz9NLm.js", "./ImageGenerating.CYegq0bI.css", "./Cl89jLsD.js", "./DbJ6Dt9m.js", "./FCN43o2W.js", "./image_editor.CgOynLPR.css", "./BXNOMSAZ.js", "./agents_configs.pCdHKEuw.css", "./Boc3hm_9.js", "./BUjMLq-a.js", "./NoImage.C266N_4k.css", "./Dd3cVSEE.js", "./DZBrT1el.js", "./AudioSelectModel.D1T2KOLW.css", "./BX7SPHBj.js", "./B5SiUF0y.js", "./Cn9HXEST.js", "./BffV2qdL.js", "./CIlzw36e.js", "./D386eQgZ.js", "./PromptInput.DkEDh9JB.css", "./IgM9N0FT.js", "./D0Ouax4K.js", "./DnWGHCrg.js", "./BspXWmOn.js", "./Cu_n4xpI.js", "./ResearchMeModal.CIL8KOGg.css", "./BJGgZAPd.js", "./Chtxu0jj.js", "./D5IxqnO4.js", "./SearchInputWrapper.PldMiM8q.css", "./CmeRl4Ak.js", "./DJt7CPhG.js", "./ImageGallery_icon.DzV6an_d.css", "./DC84aLnd.js", "./nw2_yPEg.js", "./DdNe12v6.js", "./zge8xy1K.js", "./HR06imN1.js", "./ID2-UV3f.js", "./C1MFiWVQ.js", "./PodcastPlayer.AE8F-F9L.css", "./CaQJ23Tc.js", "./DsPg3ryP.js", "./vue-tel-input.PECflDwb.css", "./DWCxnUK7.js", "./m6pNMthJ.js", "./DrzlY6u5.js", "./D_mTlMH9.js", "./BZiwhdnP.js", "./CmF_-QGy.js", "./8u4bVPF0.js", "./FontSelector.CzpyUOlP.css", "./C7XKdzt5.js", "./4oYWEiiU.js", "./HtmlEditor.CiHPWAwT.css", "./zB2AApOf.js", "./ExportActions.BDF2JlfK.css", "./CRDCtKfR.js", "./DKfgo1Ia.js", "./InfiniteCanvas.B5YWGL2B.css", "./BQrni_b3.js", "./BjAOOWF7.js", "./AiInboxCanvas.ruyHI9k8.css", "./Dp4W8C_b.js", "./3_kYp8w9.js", "./VideoSelectModel.BNvBs73l.css", "./C1hkGl0A.js", "./CztlUxD-.js", "./MediaGallery.DycJgvo4.css", "./DHMy_TLW.js", "./BpWej-le.js", "./PhoneCallTaskReplayContent.xB-TOc7P.css", "./BtBJxh_l.js", "./hnuIrtL7.js", "./91WddEU0.js", "./usePhoneCallStep.CvscoHqz.css", "./PhoneCallTaskContent.B-u36doh.css"]))) => i.map(i => d[i]);
import {
    d as e,
    b as t,
    o as a,
    _ as l,
    r as s,
    S as n,
    c as o,
    v as i,
    h as r,
    x as c,
    a as d,
    f as u,
    F as p,
    e as v,
    w as _,
    a4 as m,
    t as g,
    k as h,
    X as f,
    E as y,
    C as k,
    n as b,
    l as w,
    s as x,
    q as C,
    H as S,
    I,
    P,
    dQ as j,
    i as R,
    ai as A,
    bx as M,
    aJ as T,
    m as D,
    p as L,
    a3 as E,
    L as $,
    a9 as O,
    ax as U,
    A as F,
    g as N,
    D as B,
    ah as G
} from "./Cf0SOiw0.js";
import {
    M as q,
    a as V,
    E as H,
    P as z,
    _ as W,
    b as Q,
    d as K,
    c as J
} from "./DdaMTYTP.js";
import {
    C as Z
} from "./DW6cX6jm.js";
import {
    S as Y
} from "./DJt7CPhG.js";
import {
    P as X,
    T as ee
} from "./BDUh8PoD.js";
import {
    P as te
} from "./DHyvnBfI.js";
import {
    D as ae
} from "./D9ll07Bp.js";
import {
    t as le,
    Q as se,
    M as ne,
    B as oe
} from "./Jx3-I-D7.js";
import {
    D as ie
} from "./BThdTUgg.js";
import {
    _ as re
} from "./D6bQc9d9.js";
import {
    M as ce
} from "./B56nEZrv.js";
import {
    u as de
} from "./B6noBY_5.js";
import {
    _ as ue
} from "./DmWk8H2v.js";
import {
    _ as pe
} from "./BX7SPHBj.js";
import {
    _ as ve
} from "./e-ES_T8J.js";
import {
    _ as _e
} from "./Cn9HXEST.js";
import {
    C as me
} from "./tBofk-gQ.js";
import {
    D as ge,
    O as he,
    C as fe
} from "./C1lFdfgL.js";
import {
    G as ye
} from "./BN-NNxvY.js";
import {
    S as ke,
    P as be,
    u as we,
    a as xe,
    d as Ce,
    D as Se
} from "./DWsxX4PV.js";
import {
    U as Ie
} from "./IgM9N0FT.js";
import {
    M as Pe,
    C as je,
    a as Re,
    b as Ae,
    I as Me,
    c as Te
} from "./Dwl0GcWq.js";
import {
    M as De,
    _ as Le,
    s as Ee,
    m as $e,
    F as Oe
} from "./Boc3hm_9.js";
import {
    T as Ue
} from "./B5SiUF0y.js";
import {
    d as Fe
} from "./DOnko34f.js";
import {
    u as Ne
} from "./DJ-JsGJu.js";
import {
    N as Be
} from "./CW991W2w.js";
import {
    D as Ge,
    p as qe,
    P as Ve,
    C as He,
    _ as ze,
    q as We,
    R as Qe
} from "./DC84aLnd.js";
import {
    I as Ke,
    c as Je,
    a as Ze,
    b as Ye,
    d as Xe,
    R as et,
    D as tt,
    U as at,
    V as lt,
    f as st
} from "./3_kYp8w9.js";
import {
    p as nt
} from "./BV6guSOS.js";
import {
    Q as ot,
    M as it
} from "./C1hkGl0A.js";
import {
    R as rt,
    a as ct,
    b as dt,
    m as ut,
    d as pt,
    c as vt
} from "./DZBrT1el.js";
import {
    I as _t
} from "./DHMy_TLW.js";
import {
    a as mt,
    o as gt
} from "./CaQJ23Tc.js";
import {
    R as ht
} from "./BUjMLq-a.js";
import {
    u as ft
} from "./B0db5Fvl.js";
import {
    N as yt
} from "./BQrni_b3.js";
import {
    N as kt,
    b as bt,
    a as wt
} from "./Dp4W8C_b.js";
import {
    N as xt
} from "./CmeRl4Ak.js";
import {
    N as Ct
} from "./nuQnue4a.js";
import {
    N as St
} from "./C38RzRfR.js";
import {
    S as It
} from "./DxgY8w7w.js";
import {
    G as Pt
} from "./BdlGQsae.js";
import {
    l as jt
} from "./CrbPJ6Kt.js";
import {
    F as Rt,
    W as At,
    P as Mt
} from "./CIlzw36e.js";
import {
    E as Tt
} from "./D386eQgZ.js";
import {
    E as Dt
} from "./Cy7E5O2b.js";
import {
    S as Lt
} from "./BpWej-le.js";
import {
    N as Et
} from "./BjWUbj3w.js";
import {
    A as $t,
    P as Ot
} from "./DmzoGJ5T.js";
import {
    m as Ut,
    C as Ft,
    a as Nt,
    S as Bt,
    H as Gt,
    P as qt,
    b as Vt
} from "./BZ8pMrhH.js";
import {
    P as Ht
} from "./CouGvJje.js";
import {
    P as zt
} from "./CIu9VtBu.js";
import {
    f as Wt
} from "./Bl-gMEVt.js";
const Qt = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 512 512"
};
const Kt = {
        render: function(l, s) {
            return a(), e("svg", Qt, s[0] || (s[0] = [t("path", {
                fill: "none",
                stroke: "currentColor",
                "stroke-linecap": "round",
                "stroke-linejoin": "round",
                "stroke-width": "32",
                d: "M464 128L240 384l-96-96"
            }, null, -1), t("path", {
                fill: "none",
                stroke: "currentColor",
                "stroke-linecap": "round",
                "stroke-linejoin": "round",
                "stroke-width": "32",
                d: "M144 384l-96-96"
            }, null, -1), t("path", {
                fill: "none",
                stroke: "currentColor",
                "stroke-linecap": "round",
                "stroke-linejoin": "round",
                "stroke-width": "32",
                d: "M368 128L232 284"
            }, null, -1)]))
        }
    },
    Jt = {
        width: "100%",
        height: "100%",
        viewBox: "0 0 12 12",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Zt = {
        render: function(l, s) {
            return a(), e("svg", Jt, s[0] || (s[0] = [t("circle", {
                cx: "6",
                cy: "6",
                r: "6",
                fill: "currentColor"
            }, null, -1), t("path", {
                d: "M3 6H9",
                stroke: "white",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, null, -1)]))
        }
    },
    Yt = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 32 32"
    };
const Xt = {
        name: "TableCellDetails",
        props: {
            step: Object,
            runningProject: Boolean
        },
        components: {
            NDataTable: kt,
            NModal: Et,
            MarkdownWithPlugins: ue,
            EchartsWidget: Dt,
            CopyIcon: {
                render: function(l, s) {
                    return a(), e("svg", Yt, s[0] || (s[0] = [t("path", {
                        d: "M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2z",
                        fill: "currentColor"
                    }, null, -1), t("path", {
                        d: "M4 18H2V4a2 2 0 0 1 2-2h14v2H4z",
                        fill: "currentColor"
                    }, null, -1)]))
                }
            },
            FullScreenIcon: qe,
            PartnerData: z,
            ExpandLessIcon: H,
            ExpandMoreIcon: V,
            MagnifierAnimation: q,
            LoadingAnimation: re,
            DownloadIcon: Ge,
            ScrollIcon: Lt
        },
        setup(e, {
            emit: t,
            expose: a
        }) {
            const l = s(null),
                d = de(),
                {
                    step: u,
                    runningProject: p
                } = n(e),
                v = s(null),
                _ = s(!1),
                m = s(null),
                g = s(!1),
                h = s(null),
                y = s(null),
                b = e => {
                    if (!e) return [];
                    const t = [...new Set(e.map((e => e.cell_column)))],
                        a = [...new Set(e.map((e => e.cell_row)))],
                        l = [];
                    return a.forEach((a => {
                        const s = {
                            __row: a
                        };
                        t.forEach((t => {
                            const l = e.find((e => e.cell_row === a && e.cell_column === t));
                            l && l.cell_value_ele_array && l.cell_value_ele_array.length > 0 ? s[t] = l.cell_value_ele_array[0].value : l && l.cell_value ? s[t] = l.cell_value : s[t] = "-"
                        })), l.push(s)
                    })), l
                },
                w = e => "string" == typeof e ? parseFloat(e.replace(",", "")) : e,
                x = o((() => {
                    const e = b(u.value.table_cell_details);
                    if (h.value) {
                        const t = h.value.key;
                        h.value.sorter;
                        const a = h.value.order,
                            l = u.value.columns.find((e => e.key === t));
                        l && "number" === l.type && e.sort(((e, l) => "ascend" === a ? w(e[t]) - w(l[t]) : w(l[t]) - w(e[t])))
                    }
                    return e
                })),
                C = e => ({
                    title: e,
                    key: "__row",
                    fixed: "left",
                    sorter: "default"
                }),
                S = s(!1),
                I = () => {
                    if (!y.value) return;
                    const e = y.value.querySelector(".n-scrollbar-container"),
                        t = y.value.querySelector(".n-scrollbar-content");
                    e && t && (S.value = e.clientWidth < t.scrollWidth, k.log("canScroll", S.value, e, t))
                };
            i((() => y.value), ((e, t) => {
                I()
            }));
            const P = (e, t, a) => {
                    if (a) {
                        const l = [C(t)].concat(a);
                        return l.forEach((e => {
                            "number" == e.type ? e.sorter = (t, a) => w(t[e.key]) - w(a[e.key]) : e.sorter = "default"
                        })), j(l, e), l
                    }
                    const l = [...new Set(e.map((e => e.cell_column)))].filter((e => e !== t)).map((e => ({
                        title: e,
                        key: e,
                        sorter: "default"
                    })));
                    return t || (t = "-"), l.unshift(C(t)), j(l, e), l
                },
                j = (e, t) => {
                    const a = b(t);
                    return e.forEach((e => {
                        const l = a.reduce(((a, l) => {
                            const s = t.find((t => t.cell_row === l.__row && t.cell_column === e.key));
                            return Math.max(a, ((n = s) && n.cell_value_ele_array ? n.cell_value_ele_array.reduce(((e, t) => Math.max(e, t.value.length + t.description.length)), 1) : n && n.cell_value ? n.cell_value.length : 1) || 0);
                            var n
                        }), 0);
                        e.width = Math.min(Math.max(3 * l, 80), 450)
                    })), e.reduce(((e, t) => e + t.width), 0)
                },
                R = parseInt(1e5 * Math.random()),
                A = s(0),
                M = s(0),
                T = le((() => {
                    A.value = window.innerHeight, M.value = window.innerWidth, I()
                }), 100);
            r((() => {
                T(), window.addEventListener("resize", T)
            })), c((() => {
                window.removeEventListener("resize", T)
            }));
            const D = e => {
                const t = e => {
                        try {
                            return new URL(e).hostname.replace(/^www\./, "")
                        } catch (t) {
                            return e
                        }
                    },
                    a = "object" == typeof e ? e.url : e;
                let l = "object" == typeof e ? e.site_name : t(a);
                return l || (l = t(a)), f("a", {
                    href: a,
                    target: "_blank"
                }, l)
            };
            return {
                scrollToRight: () => {
                    if (!y.value) return;
                    const e = y.value.querySelector(".n-scrollbar-container");
                    e.scrollTo({
                        left: e.scrollWidth,
                        behavior: "smooth"
                    }), S.value = !1
                },
                canScroll: S,
                tableWrapperRef: y,
                getRefLink: D,
                tableRef: l,
                exportSorterAndFilterCsv: () => {
                    var e;
                    return null == (e = l.value) ? void 0 : e.downloadCsv({
                        fileName: "data.csv",
                        keepOriginalData: !1
                    })
                },
                tableData: x,
                dataTableSortState: h,
                messageApi: d,
                windowHeight: A,
                windowWidth: M,
                random_id: R,
                runningProject: p,
                calcTableData: b,
                calcTableColumns: P,
                copyTableData: e => {
                    const t = b(e.table_cell_details),
                        a = P(e.table_cell_details, e.header_of_rows, e.columns);
                    let l = a.map((e => e.title)).join("\t") + "\n";
                    t.forEach((e => {
                        l += a.map((t => e[t.key] || "")).join("\t") + "\n"
                    })), navigator.clipboard.writeText(l).then((() => {})).catch((e => {}))
                },
                renderCell: () => (e, t, a) => {
                    if (!u.value || !u.value.table_cell_details) return e;
                    const l = u.value.table_cell_details.find((e => e.cell_row === t.__row && e.cell_column === a.key)),
                        s = () => {
                            const e = document.querySelector(`[ref-key="${R}|||${t.__row}|||${a.key}"]`);
                            if (e) {
                                const t = function(e) {
                                    let t = e.parentNode;
                                    for (; t;) {
                                        if (t === document.documentElement) return window;
                                        const e = window.getComputedStyle(t).overflowY;
                                        if ("auto" === e || "scroll" === e) return t;
                                        t = t.parentNode
                                    }
                                    return null
                                }(e);
                                let a;
                                const s = () => {
                                    clearTimeout(a), a = setTimeout((function() {
                                        t.removeEventListener("scroll", s), e.classList.add("highlight"), requestAnimationFrame((() => {
                                            e.classList.remove("highlight")
                                        }))
                                    }), 100)
                                };
                                t.addEventListener("scroll", s), e.scrollIntoView({
                                    block: "start",
                                    behavior: "smooth"
                                }), l.expand_resource = !0
                            }
                        };
                    if (l && l.cell_value_ele_array && l.cell_value_ele_array.length > 0) {
                        const e = l.cell_value_ele_array;
                        return f("div", {
                            class: "cell_value_ele_array",
                            _value: e[0].value,
                            onClick: () => {
                                s()
                            }
                        }, e.map((e => f("div", {
                            class: "cell_value_ele"
                        }, [f("span", {
                            class: "value"
                        }, e.value), e.description ? f("span", {
                            class: "desc"
                        }, `(${e.description})`) : null, f("span", {
                            class: "ref_urls"
                        }, e.ref_urls.map((e => D(e))))].filter((e => e))))))
                    }
                    return f("div", {
                        "data-key": R + "|||" + t.__row + "|||" + a.key,
                        onClick: () => {
                            s()
                        }
                    }, e)
                },
                calcChartData: e => {
                    if (e.chart_data) return e.chart_data.legend && (e.chart_data.legend.bottom = "20"), e.chart_data.grid = {}, e.chart_data;
                    const t = P(e.table_cell_details, e.header_of_rows, e.columns).filter((e => "__row" !== e.key)).filter((e => "number" === e.type || !e.type)),
                        a = b(e.table_cell_details).map((e => {
                            const a = t.map((t => {
                                let a = e[t.key];
                                return "string" == typeof a && (a = a.replace(/[,]/g, "")), "" === a ? null : parseFloat(a)
                            }));
                            return {
                                type: "line",
                                name: e.__row,
                                data: a
                            }
                        }));
                    return {
                        xAxis: {
                            type: "category",
                            data: t.map((e => e.title))
                        },
                        yAxis: {
                            type: "value"
                        },
                        series: a,
                        legend: {
                            bottom: "20",
                            data: a.map((e => e.name))
                        }
                    }
                },
                step: u,
                fullScreenTableData: v,
                fullScreenTableDataShow: _,
                fullScreenChartData: m,
                fullScreenChartDataShow: g
            }
        }
    },
    ea = {
        class: "block"
    },
    ta = {
        class: "table_wrapper",
        ref: "tableWrapperRef"
    },
    aa = {
        class: "icon"
    },
    la = {
        class: "controls"
    },
    sa = {
        class: "icon"
    },
    na = {
        class: "text"
    },
    oa = {
        class: "icon"
    },
    ia = {
        class: "text"
    },
    ra = {
        class: "icon"
    },
    ca = {
        class: "text"
    },
    da = {
        key: 0,
        class: "chart_wrapper"
    },
    ua = {
        class: "controls"
    },
    pa = {
        class: "icon"
    },
    va = {
        class: "text"
    },
    _a = {
        class: "table_cell_details"
    },
    ma = ["ref-key"],
    ga = ["onClick"],
    ha = {
        class: "resource_title"
    },
    fa = {
        key: 0,
        class: "icon"
    },
    ya = {
        class: "text"
    },
    ka = {
        class: "icon more_icon"
    },
    ba = {
        key: 1,
        class: "sub-title"
    },
    wa = ["src"],
    xa = {
        key: 2,
        class: "markdown-viewer"
    };
const Ca = l(Xt, [
    ["render", function(l, s, n, o, i, r) {
        var c, f, k, x, C, S, I;
        const P = y("ScrollIcon"),
            j = y("NDataTable"),
            R = y("CopyIcon"),
            A = y("FullScreenIcon"),
            M = y("DownloadIcon"),
            T = Dt,
            D = re,
            L = y("ExpandLessIcon"),
            E = y("ExpandMoreIcon"),
            $ = ue,
            O = y("n-modal");
        return a(), e(p, null, [t("div", ea, [o.step.expand ? (a(), e(p, {
            key: 0
        }, [t("div", ta, [v(m, {
            name: "fade"
        }, {
            default: _((() => [o.canScroll ? (a(), e("div", {
                key: 0,
                class: "scroll_icon",
                onClick: s[0] || (s[0] = (...e) => o.scrollToRight && o.scrollToRight(...e))
            }, [t("div", aa, [v(P)])])) : u("", !0)])),
            _: 1
        }), v(j, {
            ref: "tableRef",
            data: o.tableData,
            columns: o.calcTableColumns(null == (c = o.step) ? void 0 : c.table_cell_details, null == (f = o.step) ? void 0 : f.header_of_rows, null == (k = o.step) ? void 0 : k.columns),
            "scroll-x": o.calcTableColumns(null == (x = o.step) ? void 0 : x.table_cell_details, null == (C = o.step) ? void 0 : C.header_of_rows, null == (S = o.step) ? void 0 : S.columns).reduce(((e, t) => e + t.width), 0),
            bordered: !0,
            loading: !o.step.is_finish,
            "render-cell": o.renderCell(),
            "onUpdate:sorter": s[1] || (s[1] = e => {
                o.dataTableSortState = e
            })
        }, null, 8, ["data", "columns", "scroll-x", "loading", "render-cell"]), t("div", la, [t("div", {
            class: "button",
            onClick: s[2] || (s[2] = () => {
                o.copyTableData(o.step), o.messageApi.success("Table data copied to clipboard")
            })
        }, [t("div", sa, [v(R)]), t("div", na, g(l.$t("components.TableCellDetails.copy")), 1)]), t("div", {
            class: "button",
            onClick: s[3] || (s[3] = () => {
                var e, t, a, l, s, n, i;
                o.fullScreenTableDataShow = !0, o.fullScreenTableData = {
                    data: o.tableData,
                    columns: o.calcTableColumns(null == (e = o.step) ? void 0 : e.table_cell_details, null == (t = o.step) ? void 0 : t.header_of_rows, null == (a = o.step) ? void 0 : a.columns),
                    scroll_x: 100 * o.calcTableColumns(null == (l = o.step) ? void 0 : l.table_cell_details, null == (s = o.step) ? void 0 : s.header_of_rows, null == (n = o.step) ? void 0 : n.columns).length,
                    is_finish: null == (i = o.step) ? void 0 : i.is_finish
                }
            })
        }, [t("div", oa, [v(A)]), t("div", ia, g(l.$t("components.TableCellDetails.full-screen")), 1)]), t("div", {
            class: "button",
            onClick: s[4] || (s[4] = () => {
                o.exportSorterAndFilterCsv()
            })
        }, [t("div", ra, [v(M)]), t("div", ca, g(l.$t("components.TableCellDetails.export-csv")), 1)])])], 512), !1 !== o.step.chart_data ? (a(), e("div", da, [v(T, {
            chartData: o.calcChartData(o.step)
        }, null, 8, ["chartData"]), t("div", ua, [t("div", {
            class: "button",
            onClick: s[5] || (s[5] = () => {
                o.fullScreenChartData = o.calcChartData(o.step), o.fullScreenChartDataShow = !0
            })
        }, [t("div", pa, [v(A)]), t("div", va, g(l.$t("components.TableCellDetails.full-screen")), 1)])])])) : u("", !0), t("div", _a, [o.step.expand ? (a(!0), e(p, {
            key: 0
        }, h(null == (I = o.step) ? void 0 : I.table_cell_details, ((l, s) => {
            var n, i;
            return a(), e("div", {
                key: s,
                "ref-key": o.random_id + "|||" + l.cell_row + "|||" + l.cell_column,
                class: "resource"
            }, [t("div", {
                class: b(["resource_header", {
                    expand: l.expand_resource
                }]),
                onClick: e => l.expand_resource = !l.expand_resource
            }, [t("div", ha, [o.runningProject && l.running ? (a(), e("div", fa, [o.runningProject && l.running ? (a(), e(p, {
                key: 0
            }, [u("", !0), v(D)], 64)) : u("", !0)])) : u("", !0), t("div", ya, g(l.cell_title), 1)]), t("div", ka, [l.expand_resource ? (a(), d(L, {
                key: 0
            })) : (a(), d(E, {
                key: 1
            }))])], 10, ga), u("", !0), !l.expand_resource && (null == (n = null == l ? void 0 : l.ref_links) ? void 0 : n.length) > 0 && (null == (i = null == l ? void 0 : l.ref_links) ? void 0 : i.some((e => e.favicon))) ? (a(), e("div", ba, [(a(!0), e(p, null, h(null == l ? void 0 : l.ref_links, ((l, s) => (a(), e("div", {
                key: s,
                class: "favicon"
            }, [t("img", {
                src: l.favicon
            }, null, 8, wa)])))), 128))])) : u("", !0), l.expand_resource ? (a(), e("div", xa, [v($, {
                source: l.cell_detail_markdown
            }, null, 8, ["source"])])) : u("", !0)], 8, ma)
        })), 128)) : u("", !0)])], 64)) : u("", !0)]), o.fullScreenTableData ? (a(), d(O, {
            key: 0,
            show: o.fullScreenTableDataShow,
            "onUpdate:show": s[9] || (s[9] = e => o.fullScreenTableDataShow = e)
        }, {
            default: _((() => {
                var e, a, l, n;
                return [t("div", {
                    class: "full_screen_table_wrapper",
                    onClick: s[8] || (s[8] = e => o.fullScreenTableDataShow = !1)
                }, [t("div", {
                    class: "full_screen_table",
                    onClick: s[7] || (s[7] = w((() => {}), ["stop"]))
                }, [v(j, {
                    data: null == (e = o.fullScreenTableData) ? void 0 : e.data,
                    columns: null == (a = o.fullScreenTableData) ? void 0 : a.columns,
                    "scroll-x": null == (l = o.fullScreenTableData) ? void 0 : l.columns.reduce(((e, t) => e + t.width), 0),
                    "max-height": o.windowHeight - 150,
                    bordered: !0,
                    loading: !(null == (n = o.fullScreenTableData) ? void 0 : n.is_finish),
                    "render-cell": o.renderCell(),
                    "onUpdate:sorter": s[6] || (s[6] = e => {
                        o.dataTableSortState = e
                    })
                }, null, 8, ["data", "columns", "scroll-x", "max-height", "loading", "render-cell"])])])]
            })),
            _: 1
        }, 8, ["show"])) : u("", !0), o.fullScreenChartData ? (a(), d(O, {
            key: 1,
            show: o.fullScreenChartDataShow,
            "onUpdate:show": s[12] || (s[12] = e => o.fullScreenChartDataShow = e)
        }, {
            default: _((() => [t("div", {
                class: "full_screen_chart_wrapper",
                onClick: s[11] || (s[11] = e => o.fullScreenChartDataShow = !1)
            }, [t("div", {
                class: "full_screen_chart",
                onClick: s[10] || (s[10] = w((() => {}), ["stop"]))
            }, [v(T, {
                chartData: o.fullScreenChartData,
                height: Math.max(o.windowHeight - 150, 500)
            }, null, 8, ["chartData", "height"])])])])),
            _: 1
        }, 8, ["show"])) : u("", !0)], 64)
    }],
    ["__scopeId", "data-v-967c3835"]
]);
const Sa = {
        class: "call-information"
    },
    Ia = {
        class: "call-information__container"
    },
    Pa = {
        key: 0,
        class: "call-information__title"
    },
    ja = {
        class: "call-information__body"
    },
    Ra = {
        class: "call-information__contact"
    },
    Aa = {
        class: "call-information__contact-details"
    },
    Ma = {
        key: 0,
        class: "call-information__contact-name"
    },
    Ta = {
        key: 1,
        class: "call-information__contact-address"
    },
    Da = {
        key: 2,
        class: "call-information__contact-phone"
    },
    La = ["innerHTML"],
    Ea = ["src"],
    $a = {
        key: 0,
        class: "call-information__purpose"
    },
    Oa = {
        key: 0,
        class: "call-information__thinking"
    },
    Ua = {
        class: "call-information__thinking-title"
    },
    Fa = {
        class: "call-information__thinking-content"
    };
const Na = l({
        name: "CallInformation",
        props: {
            step: {
                type: Object,
                required: !0
            }
        },
        setup: () => ({
            callInfo: {
                maskPhoneNumber: Ut
            }
        })
    }, [
        ["render", function(l, s, n, o, i, r) {
            var c, d, p, _, m, h, f, y, k, b, w, x, C, S, I, P, j, R, A, M, T, D, L;
            const E = ue;
            return a(), e("div", Sa, [t("div", Ia, [(null == (c = n.step) ? void 0 : c.title) ? (a(), e("div", Pa, g(null == (d = n.step) ? void 0 : d.title), 1)) : u("", !0), t("div", ja, [t("div", Ra, [t("div", Aa, [(null == (_ = null == (p = n.step) ? void 0 : p.business) ? void 0 : _.name) ? (a(), e("div", Ma, g(null == (h = null == (m = n.step) ? void 0 : m.business) ? void 0 : h.name), 1)) : u("", !0), (null == (y = null == (f = n.step) ? void 0 : f.business) ? void 0 : y.address) ? (a(), e("div", Ta, g(null == (b = null == (k = n.step) ? void 0 : k.business) ? void 0 : b.address), 1)) : u("", !0), (null == (x = null == (w = n.step) ? void 0 : w.business) ? void 0 : x.phone_number) ? (a(), e("div", Da, [t("span", {
                innerHTML: o.callInfo.maskPhoneNumber(null == (S = null == (C = n.step) ? void 0 : C.business) ? void 0 : S.phone_number)
            }, null, 8, La)])) : u("", !0)]), (null == (P = null == (I = n.step) ? void 0 : I.business) ? void 0 : P.google_maps_embed_url) ? (a(), e("iframe", {
                key: 0,
                class: "call-information__contact-map",
                loading: "lazy",
                allowfullscreen: "",
                referrerpolicy: "no-referrer-when-downgrade",
                src: null == (R = null == (j = n.step) ? void 0 : j.business) ? void 0 : R.google_maps_embed_url
            }, null, 8, Ea)) : u("", !0)]), s[0] || (s[0] = t("div", {
                class: "call-information__divider"
            }, null, -1)), (null == (A = n.step) ? void 0 : A.purpose) ? (a(), e("div", $a, g(null == (M = n.step) ? void 0 : M.purpose), 1)) : u("", !0)])]), (null == (T = n.step) ? void 0 : T.thinking) ? (a(), e("div", Oa, [t("div", Ua, g(null == (D = n.step) ? void 0 : D.thinking_title), 1), t("div", Fa, [v(E, {
                source: null == (L = n.step) ? void 0 : L.thinking
            }, null, 8, ["source"])])])) : u("", !0)])
        }],
        ["__scopeId", "data-v-a3ae3df8"]
    ]),
    Ba = {
        name: "AsyncTaskContent",
        components: {
            CheckIcon: je,
            ExpandMoreIcon: V,
            ExpandLessIcon: H,
            ChecksCircleIcon: Z,
            ChecksIcon: Kt,
            MagnifierAnimation: q,
            SelectedIconSvg: Y,
            TriangleIconSvg: ee,
            PublicIcon: te,
            PrivateIcon: X,
            StopIcon: Zt,
            DataIcon: ae,
            QuoteIcon: se,
            PhoneIcon: Ot,
            PhoneCallIcon: Vt,
            PhoneErrorIcon: qt,
            HangUpIcon: Gt,
            StartNewCallIcon: Bt,
            CallAgainIcon: Nt,
            PlayIcon: Ht,
            PauseIcon: zt,
            CircleIconFrame: Ft,
            ArrowRightIcon: $t,
            DocumentIcon: ie,
            CreateFormPhoneCall: He,
            PhoneCallSetting: Ve,
            CallInformation: Na,
            MoaModelsResponse: Pe,
            NDataTable: kt
        },
        props: {
            message: {
                type: Object,
                required: !0
            },
            hide_intermediate_steps: {
                type: Boolean,
                default: !1
            },
            isRunning: {
                type: Boolean,
                default: !1
            },
            hide_phone_call_buttons: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["phoneCallTaskCreated", "quotaExceed"],
        setup(e) {
            const {
                message: t,
                hide_intermediate_steps: a,
                isRunning: l,
                hide_phone_call_buttons: o
            } = n(e), {
                t: r,
                locale: c
            } = x(), d = s([]), u = s(null), p = s("unknown"), v = s(!0), _ = s(0), m = () => {
                let e = 0;
                if (t.value && t.value.session_state && t.value.session_state.steps) {
                    for (let a = t.value.session_state.steps.length - 1; a >= 0 && t.value.session_state.steps[a].expand; a--) e++;
                    return t.value.session_state.steps.length - e
                }
                return 0
            };
            _.value = m(), i((() => t.value), (() => {
                _.value = m()
            }));
            const g = s(null),
                h = s(!1),
                f = s("0:00"),
                y = s("-:--"),
                b = s(0),
                w = s(null),
                C = s(!1),
                S = s(!1),
                I = s(null),
                P = e => `${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`,
                j = s(!1),
                R = e => {
                    if (!j.value) return;
                    if (!(g.value && Array.isArray(g.value) && g.value.length && g.value[0])) return;
                    if (!(w.value && Array.isArray(w.value) && w.value.length && w.value[0])) return;
                    const t = w.value[0].getBoundingClientRect(),
                        a = Math.max(0, Math.min(e.clientX - t.left, t.width)) / t.width * 100;
                    if (b.value = a, C.value) {
                        const e = a / 100 * g.value[0].duration;
                        f.value = P(e), g.value[0].currentTime = e
                    }
                },
                A = () => {
                    j.value = !1, document.removeEventListener("mousemove", R), document.removeEventListener("mouseup", A)
                };
            return {
                t: r,
                locale: c,
                initialContact: u,
                initialPurpose: p,
                SparkPageIcon: ke,
                getImageUrl: e => "youtube" === e.type ? k.convertToYoutubeImageUrl(e.link) : e.url,
                hide_intermediate_steps: a,
                hide_intermediate_steps_switch: v,
                hide_intermediate_steps_index: _,
                create_phone_call_form_show: S,
                phoneCallSetting: I,
                hide_phone_call_buttons: o,
                isFinished: () => !!t.value && ["SUCCESS", "FINISHED"].indexOf(t.value.status) >= 0,
                message: t,
                isRunning: l,
                loadedGallery: d,
                utils: k,
                audioPlayer: g,
                isPlaying: h,
                currentTime: f,
                duration: y,
                progress: b,
                formatTime: P,
                togglePlay: () => {
                    g.value && Array.isArray(g.value) && g.value.length && g.value[0] && (C.value && (h.value ? g.value[0].pause() : g.value[0].play()), h.value = !h.value)
                },
                updateProgress: () => {
                    if (!(g.value && Array.isArray(g.value) && g.value.length && g.value[0])) return;
                    const e = g.value[0];
                    f.value = P(e.currentTime), b.value = e.currentTime / e.duration * 100
                },
                onAudioLoaded: () => {
                    if (!(g.value && Array.isArray(g.value) && g.value.length && g.value[0])) return;
                    y.value = P(g.value[0].duration);
                    const e = b.value / 100 * g.value[0].duration;
                    f.value = P(e), g.value[0].currentTime = e, h.value && g.value[0].play(), C.value = !0
                },
                onAudioEnded: () => {
                    h.value = !1, b.value = 0, f.value = "0:00"
                },
                isDragging: j,
                startDrag: e => {
                    j.value = !0, document.addEventListener("mousemove", R), document.addEventListener("mouseup", A)
                },
                stopDrag: A,
                progressBarRef: w,
                seekAudio: e => {
                    if (!(g.value && Array.isArray(g.value) && g.value.length && g.value[0])) return;
                    if (!(w.value && Array.isArray(w.value) && w.value.length && w.value[0])) return;
                    const t = w.value[0].getBoundingClientRect(),
                        a = Math.max(0, Math.min(e.clientX - t.left, t.width)) / t.width * 100;
                    if (b.value = a, C.value) {
                        const e = a / 100 * g.value[0].duration;
                        f.value = P(e), g.value[0].currentTime = e
                    }
                },
                setAudioTimeAndPlay: e => {
                    if (void 0 === e || e <= 0) return;
                    if (!(g.value && Array.isArray(g.value) && g.value.length && g.value[0])) return;
                    if (!C.value) return;
                    g.value[0].currentTime = e / 1e3, h.value = !0;
                    const t = e / 1e3 / g.value[0].duration;
                    b.value = t;
                    const a = t / 100 * g.value[0].duration;
                    f.value = P(a), g.value[0].pause(), g.value[0].play()
                }
            }
        },
        methods: {
            getStatusDisplayText(e) {
                const {
                    t: t,
                    locale: a
                } = x();
                return "queued" === e ? t("pages.call_assistant.status_queued") : "initiated" === e ? t("pages.call_assistant.status_initiated") : "ringing" === e ? t("pages.call_assistant.status_ringing") : "connected" == e ? t("pages.call_assistant.status_connected") : "in-progress" == e ? t("pages.call_assistant.status_in_progress") : "canceled" === e ? t("pages.call_assistant.status_canceled") : "busy" === e ? t("pages.call_assistant.status_busy") : "no-answer" === e ? t("pages.call_assistant.status_no_answer") : "failed" === e ? t("pages.call_assistant.status_failed") : "completed" === e ? t("pages.call_assistant.status_completed") : "disconnected" === e ? t("pages.call_assistant.status_disconnected") : e
            },
            formatDateTime(e) {
                if (!e) return "";
                const t = new Date(1e3 * e);
                return `${t.getFullYear().toString().padStart(2,"0").slice(-2)}/${(t.getMonth()+1).toString().padStart(2,"0")}/${t.getDate().toString().padStart(2,"0")} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`
            },
            formatTimeFromMs(e) {
                if (null == e) return "0:00";
                const t = Math.floor(e / 1e3) % 60,
                    a = Math.floor(e / 6e4) % 60;
                return Math.floor(e / 36e5) > 0 || a >= 10 ? "9:59" : `${a}:${t<10?"0":""}${t}`
            },
            maskPhoneNumber(e) {
                if (!e) return "";
                let t = !1;
                try {
                    window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches && (t = !0)
                } catch (a) {}
                return e.replace(/(\*[\s\*]*\*)+/g, (e => {
                    const a = e.replace(/\s/g, "").replace(/\*/g, "5");
                    return `<span style="display:inline-block; filter:blur(8px); background-color:${t?"rgba(100,100,100,0.3)":"rgba(200,200,200,0.2)"}; border-radius:3px; color:inherit; font-weight:bold; user-select:none; position:relative; padding:0 2px; margin:0 1px; transform:scale(1.05); opacity:0.95;">${a}</span>`
                }))
            }
        }
    },
    Ga = {
        key: 0,
        class: "bubble assistant full_width hide-intermediate-steps-switch"
    },
    qa = {
        class: "bubble-inner"
    },
    Va = {
        class: "text"
    },
    Ha = {
        class: "text"
    },
    za = {
        class: "icon"
    },
    Wa = {
        key: 1
    },
    Qa = {
        class: "bubble assistant statements"
    },
    Ka = {
        class: "bubble-inner"
    },
    Ja = {
        key: 0,
        class: "block"
    },
    Za = ["onClick"],
    Ya = {
        class: "title"
    },
    Xa = {
        key: 0,
        class: "icon"
    },
    el = {
        key: 0,
        class: "stop_icon"
    },
    tl = {
        class: "text"
    },
    al = {
        key: 0,
        class: "icon"
    },
    ll = {
        key: 0,
        class: "block"
    },
    sl = {
        key: 0
    },
    nl = {
        key: 0,
        class: "block"
    },
    ol = {
        class: "phone_call_group"
    },
    il = {
        class: "phone_call"
    },
    rl = {
        class: "phone_call_header"
    },
    cl = {
        class: "phone_call_title"
    },
    dl = {
        class: "icon-container"
    },
    ul = {
        class: "phone_call_title_container"
    },
    pl = {
        key: 0,
        class: "phone_call_title"
    },
    vl = {
        key: 1,
        class: "phone_call_subtitle"
    },
    _l = ["innerHTML"],
    ml = {
        key: 0,
        class: "divider"
    },
    gl = {
        key: 1,
        class: "realtime_conversation_items"
    },
    hl = {
        key: 0,
        class: "realtime_conversation_item"
    },
    fl = ["onClick"],
    yl = {
        class: "realtime_conversation_item_content"
    },
    kl = {
        class: "role"
    },
    bl = {
        class: "content"
    },
    wl = {
        key: 2,
        class: "divider"
    },
    xl = {
        key: 3,
        class: "call_ended"
    },
    Cl = {
        key: 1,
        class: "summary_card"
    },
    Sl = {
        class: "title"
    },
    Il = {
        class: "content"
    },
    Pl = {
        key: 2,
        class: "audio_player"
    },
    jl = ["src"],
    Rl = {
        class: "time_display"
    },
    Al = {
        key: 0,
        class: "bottom_buttons_container"
    },
    Ml = ["onClick"],
    Tl = {
        class: "phone_call_button_text"
    },
    Dl = ["onClick"],
    Ll = {
        class: "phone_call_button_text"
    },
    El = {
        class: "phone_call_button_text"
    },
    $l = {
        key: 0,
        class: "block"
    },
    Ol = {
        class: "simple_table"
    },
    Ul = {
        key: 1,
        class: "block"
    },
    Fl = {
        class: "search_queries"
    },
    Nl = {
        key: 7,
        class: "block"
    },
    Bl = {
        key: 0,
        class: "verify_results"
    },
    Gl = ["onClick"],
    ql = {
        class: "resource_title"
    },
    Vl = {
        key: 0,
        class: "icon"
    },
    Hl = {
        class: "icon"
    },
    zl = ["src"],
    Wl = {
        class: "text"
    },
    Ql = ["href", "onClick"],
    Kl = {
        class: "icon more_icon"
    },
    Jl = {
        key: 2,
        class: "resource_content"
    },
    Zl = {
        class: "text-content"
    },
    Yl = {
        class: "quote-left"
    },
    Xl = {
        class: "quote-right"
    },
    es = {
        key: 0,
        class: "evidence_str"
    },
    ts = {
        key: 1,
        class: "evidence_str"
    },
    as = {
        key: 2,
        class: "evidence_str"
    },
    ls = {
        key: 0,
        class: "link"
    },
    ss = ["href", "onClick"],
    ns = {
        key: 1,
        class: "loading-anim"
    },
    os = {
        key: 1,
        class: "verify_results not_expand"
    },
    is = {
        class: "sub-title"
    },
    rs = ["src"],
    cs = {
        key: 8,
        class: "block"
    },
    ds = {
        key: 0,
        class: "block"
    },
    us = {
        class: "spark_result"
    },
    ps = {
        class: "markdown-viewer note_content"
    },
    vs = {
        key: 0,
        class: "spark_result_card"
    },
    _s = {
        class: "logo"
    },
    ms = ["src"],
    gs = ["onClick"],
    hs = {
        class: "markdown-viewer content"
    },
    fs = ["onClick"],
    ys = ["src"],
    ks = {
        key: 1,
        class: "gallery_load_wrapper"
    },
    bs = ["src", "onLoad"],
    ws = ["onClick"],
    xs = {
        class: "text"
    },
    Cs = {
        class: "icon"
    };
const Ss = l(Ba, [
        ["render", function(l, s, n, o, i, r) {
            var c, _, m, f, k, x;
            const P = y("ExpandMoreIcon"),
                j = re,
                R = y("StopIcon"),
                A = y("CheckIcon"),
                M = y("ExpandLessIcon"),
                T = W,
                D = y("CallInformation"),
                L = y("MoaModelsResponse"),
                E = ue,
                $ = y("CircleIconFrame"),
                O = y("PhoneIcon"),
                U = y("PhoneCallIcon"),
                F = y("PhoneErrorIcon"),
                N = y("PlayIcon"),
                B = y("PauseIcon"),
                G = y("HangUpIcon"),
                q = y("CallAgainIcon"),
                V = y("StartNewCallIcon"),
                H = y("NDataTable"),
                z = y("QuoteIcon"),
                Q = Ca,
                K = y("ArrowRightIcon"),
                J = y("CreateFormPhoneCall"),
                Z = y("PhoneCallSetting");
            return a(), e(p, null, [o.isFinished() && o.hide_intermediate_steps && o.hide_intermediate_steps_switch && o.hide_intermediate_steps_index > 0 ? (a(), e("div", Ga, [s[15] || (s[15] = t("div", {
                class: "bubble-inner card-place-holder1"
            }, null, -1)), s[16] || (s[16] = t("div", {
                class: "bubble-inner card-place-holder2"
            }, null, -1)), t("div", qa, [t("div", Va, g(l.$t("components.cross_check.autopilotagent-process")), 1), t("div", {
                class: "see-more",
                onClick: s[0] || (s[0] = e => o.hide_intermediate_steps_switch = !1)
            }, [t("div", Ha, g(l.$t("components.cross_check.see-more")), 1), t("div", za, [v(P)])])])])) : u("", !0), (null == (_ = null == (c = o.message) ? void 0 : c.session_state) ? void 0 : _.steps) && 0 != (null == (f = null == (m = o.message) ? void 0 : m.session_state) ? void 0 : f.steps.length) || !o.isRunning ? u("", !0) : (a(), e("div", Wa, [t("div", Qa, [t("div", Ka, [v(j)])])])), (a(!0), e(p, null, h(null == (x = null == (k = o.message) ? void 0 : k.session_state) ? void 0 : x.steps, ((n, i) => {
                var c, _, m, f, y, k, x, W, J, Z, Y, X;
                return a(), e(p, null, [!o.isFinished() || !o.hide_intermediate_steps || !o.hide_intermediate_steps_switch || i >= o.hide_intermediate_steps_index ? (a(), e("div", {
                    key: 0,
                    class: b(["bubble assistant statements", {
                        full_width: "business_call_information" == (null == n ? void 0 : n.type) || "phone_call" == (null == n ? void 0 : n.type) || "markdown" == (null == n ? void 0 : n.type) || "spark_result" == (null == n ? void 0 : n.type) || "table_cell_details" == (null == n ? void 0 : n.type) || "search_queries" == (null == n ? void 0 : n.type) || "verify_results" == (null == n ? void 0 : n.type) || "vue_component" == (null == n ? void 0 : n.type)
                    }])
                }, [t("div", {
                    class: b({
                        "bubble-inner": !["business_call_information", "phone_call"].includes(null == n ? void 0 : n.type),
                        "bubble-no-wrapper": ["business_call_information", "phone_call"].includes(null == n ? void 0 : n.type),
                        notice: null == n ? void 0 : n.is_notice
                    })
                }, ["markdown" == (null == n ? void 0 : n.type) || "spark_result" == (null == n ? void 0 : n.type) || "table_cell_details" == (null == n ? void 0 : n.type) || "search_queries" == (null == n ? void 0 : n.type) || "verify_results" == (null == n ? void 0 : n.type) || "vue_component" == (null == n ? void 0 : n.type) ? (a(), e("div", Ja, [t("div", {
                    class: "expand",
                    onClick: () => {
                        (null == n ? void 0 : n.is_notice) || (n.expand = !n.expand)
                    }
                }, [t("div", Ya, [(null == n ? void 0 : n.is_notice) ? u("", !0) : (a(), e("div", Xa, [o.isRunning || n.is_finish ? o.isRunning && !n.is_finish ? (a(), e(p, {
                    key: 1
                }, [u("", !0), v(j)], 64)) : (a(), e(p, {
                    key: 2
                }, [v(A), u("", !0)], 64)) : (a(), e("div", el, [v(R)]))])), t("div", tl, g(n.title), 1)]), !o.isRunning && !n.is_finish || n.is_notice ? u("", !0) : (a(), e("div", al, [n.expand ? (a(), d(M, {
                    key: 1
                })) : (a(), d(P, {
                    key: 0
                }))]))], 8, Za)])) : u("", !0), "vue_component" == (null == n ? void 0 : n.type) ? (a(), e(p, {
                    key: 1
                }, [n.expand ? (a(), e("div", ll, ["deep_dive_search" == n.component_type ? (a(), e("div", sl, [v(T, {
                    "external-stream-json-string": n.streaming_summary || "",
                    "external-thinking": n.thinking || "",
                    "external-api-response": n.component_content || "",
                    showCrossCheck: !1,
                    showFollowupQuestion: !1,
                    showCharts: !1
                }, null, 8, ["external-stream-json-string", "external-thinking", "external-api-response"])])) : u("", !0)])) : u("", !0)], 64)) : u("", !0), "business_call_information" == (null == n ? void 0 : n.type) ? (a(), d(D, {
                    key: 2,
                    step: n
                }, null, 8, ["step"])) : u("", !0), (null == (c = null == n ? void 0 : n.models) ? void 0 : c.length) > 1 && n.expand ? (a(), d(L, {
                    key: 3,
                    session_state: n,
                    thinking: o.isRunning
                }, null, 8, ["session_state", "thinking"])) : u("", !0), (null == n ? void 0 : n.markdown) ? (a(), e(p, {
                    key: 4
                }, [n.expand && n.markdown || n.is_notice ? (a(), e("div", nl, [t("div", {
                    class: b(["markdown-viewer", {
                        "markdown-viewer-loading": !n.is_finish && o.isRunning
                    }])
                }, [v(E, {
                    source: n.markdown,
                    supportCitationLink: !0
                }, null, 8, ["source"])], 2)])) : u("", !0)], 64)) : u("", !0), "phone_call" == (null == n ? void 0 : n.type) ? (a(), e(p, {
                    key: 5
                }, [t("div", ol, [t("div", il, [t("div", rl, [t("div", cl, [t("div", dl, [v($), "completed" == (null == n ? void 0 : n.status) ? (a(), d(O, {
                    key: 0,
                    class: "phone-icon"
                })) : u("", !0), "queued" == (null == n ? void 0 : n.status) || "initiated" == (null == n ? void 0 : n.status) || "ringing" == (null == n ? void 0 : n.status) || "connected" == (null == n ? void 0 : n.status) || "in-progress" == (null == n ? void 0 : n.status) || "canceled" == (null == n ? void 0 : n.status) ? (a(), d(U, {
                    key: 1,
                    class: "phone-icon"
                })) : u("", !0), "busy" == (null == n ? void 0 : n.status) || "no-answer" == (null == n ? void 0 : n.status) || "failed" == (null == n ? void 0 : n.status) || "disconnected" == (null == n ? void 0 : n.status) ? (a(), d(F, {
                    key: 2,
                    class: "phone-icon"
                })) : u("", !0)]), t("div", ul, [(null == n ? void 0 : n.title) ? (a(), e("div", pl, g(null == n ? void 0 : n.title), 1)) : u("", !0), (null == n ? void 0 : n.phone_number) ? (a(), e("div", vl, [t("span", {
                    innerHTML: r.maskPhoneNumber(null == n ? void 0 : n.phone_number)
                }, null, 8, _l)])) : u("", !0)])]), "canceled" == (null == n ? void 0 : n.status) || "busy" == (null == n ? void 0 : n.status) || "no-answer" == (null == n ? void 0 : n.status) || "failed" == (null == n ? void 0 : n.status) || !(null == n ? void 0 : n.completed_at) && "disconnected" == (null == n ? void 0 : n.status) ? u("", !0) : (a(), e("div", {
                    key: 0,
                    class: b(["phone_call_status", {
                        before_call: "queued" == (null == n ? void 0 : n.status) || "initiated" == (null == n ? void 0 : n.status) || "ringing" == (null == n ? void 0 : n.status) || "connected" == (null == n ? void 0 : n.status),
                        during_call: "in-progress" == (null == n ? void 0 : n.status)
                    }])
                }, g((null == n ? void 0 : n.completed_at) ? r.formatDateTime(null == n ? void 0 : n.completed_at) : r.getStatusDisplayText(null == n ? void 0 : n.status)), 3))]), (null == n ? void 0 : n.realtime_conversation_items) && (null == n ? void 0 : n.realtime_conversation_items.length) > 0 ? (a(), e("div", ml)) : u("", !0), (null == n ? void 0 : n.realtime_conversation_items) && (null == n ? void 0 : n.realtime_conversation_items.length) > 0 ? (a(), e("div", gl, [(a(!0), e(p, null, h(null == n ? void 0 : n.realtime_conversation_items, ((s, n) => (a(), e("div", {
                    key: n
                }, [(null == s ? void 0 : s.type) && ("message" != (null == s ? void 0 : s.type) || "message" == (null == s ? void 0 : s.type) && (null == s ? void 0 : s.content) && (0 !== (null == s ? void 0 : s.audio_start_ms) || 0 !== (null == s ? void 0 : s.audio_end_ms))) ? (a(), e("div", hl, [t("div", {
                    onClick: e => o.setAudioTimeAndPlay(null == s ? void 0 : s.audio_start_ms),
                    class: b({
                        realtime_conversation_item_time: !!(0 !== (null == s ? void 0 : s.audio_start_ms) && (null == s ? void 0 : s.audio_start_ms) || 0 !== (null == s ? void 0 : s.audio_end_ms) && (null == s ? void 0 : s.audio_end_ms)),
                        realtime_conversation_item_time_invisible: !(0 !== (null == s ? void 0 : s.audio_start_ms) && (null == s ? void 0 : s.audio_start_ms) || 0 !== (null == s ? void 0 : s.audio_end_ms) && (null == s ? void 0 : s.audio_end_ms))
                    })
                }, g(r.formatTimeFromMs(null == s ? void 0 : s.audio_start_ms)), 11, fl), t("div", yl, [t("div", kl, g("message" == (null == s ? void 0 : s.type) ? null == s ? void 0 : s.role : l.$t("pages.call_assistant.key_pressed")) + ": ", 1), t("div", bl, g("message" == (null == s ? void 0 : s.type) ? (null == s ? void 0 : s.content) || "..." : null == s ? void 0 : s.key), 1)])])) : u("", !0)])))), 128))])) : u("", !0), (null == n ? void 0 : n.call_ended) && "canceled" != (null == n ? void 0 : n.status) && "busy" != (null == n ? void 0 : n.status) && "no-answer" != (null == n ? void 0 : n.status) && "failed" != (null == n ? void 0 : n.status) ? (a(), e("div", wl)) : u("", !0), (null == n ? void 0 : n.call_ended) && "canceled" != (null == n ? void 0 : n.status) && "busy" != (null == n ? void 0 : n.status) && "no-answer" != (null == n ? void 0 : n.status) && "failed" != (null == n ? void 0 : n.status) ? (a(), e("div", xl, g("<**user**>" == (null == n ? void 0 : n.call_ended) ? l.$t("pages.call_assistant.call_ended_by_you") : l.$t("pages.call_assistant.call_ended_by", [null == n ? void 0 : n.call_ended])), 1)) : u("", !0)]), "canceled" == (null == n ? void 0 : n.status) || "busy" == (null == n ? void 0 : n.status) || "no-answer" == (null == n ? void 0 : n.status) || "failed" == (null == n ? void 0 : n.status) || "disconnected" == (null == n ? void 0 : n.status) ? (a(), e("div", {
                    key: 0,
                    class: b(["phone_call_error", {
                        error_type_canceled: "canceled" == (null == n ? void 0 : n.status)
                    }])
                }, g(r.getStatusDisplayText(null == n ? void 0 : n.status)), 3)) : u("", !0), (null == n ? void 0 : n.summary_card) && "canceled" != (null == n ? void 0 : n.status) && "busy" != (null == n ? void 0 : n.status) && "no-answer" != (null == n ? void 0 : n.status) && "failed" != (null == n ? void 0 : n.status) ? (a(), e("div", Cl, [t("div", Sl, g(null == (_ = null == n ? void 0 : n.summary_card) ? void 0 : _.title), 1), t("div", Il, [v(E, {
                    source: null == (m = null == n ? void 0 : n.summary_card) ? void 0 : m.content.join("\n")
                }, null, 8, ["source"])])])) : u("", !0), (null == n ? void 0 : n.audio_player) && "canceled" != (null == n ? void 0 : n.status) && "busy" != (null == n ? void 0 : n.status) && "no-answer" != (null == n ? void 0 : n.status) && "failed" != (null == n ? void 0 : n.status) ? (a(), e("div", Pl, [t("audio", {
                    ref_for: !0,
                    ref: "audioPlayer",
                    src: null == (f = null == n ? void 0 : n.audio_player) ? void 0 : f.url,
                    preload: "metadata",
                    onTimeupdate: s[1] || (s[1] = (...e) => o.updateProgress && o.updateProgress(...e)),
                    onLoadedmetadata: s[2] || (s[2] = (...e) => o.onAudioLoaded && o.onAudioLoaded(...e)),
                    onEnded: s[3] || (s[3] = (...e) => o.onAudioEnded && o.onAudioEnded(...e))
                }, null, 40, jl), t("button", {
                    class: "play_button",
                    onClick: s[4] || (s[4] = (...e) => o.togglePlay && o.togglePlay(...e))
                }, [o.isPlaying ? (a(), d(B, {
                    key: 1,
                    class: "pause_icon"
                })) : (a(), d(N, {
                    key: 0,
                    class: "play_icon"
                }))]), t("div", Rl, g(o.currentTime || "0:00") + "/" + g(o.duration || "-:--"), 1), t("div", {
                    ref_for: !0,
                    ref: "progressBarRef",
                    class: "progress_bar",
                    onClick: s[6] || (s[6] = (...e) => o.seekAudio && o.seekAudio(...e)),
                    onMousedown: s[7] || (s[7] = (...e) => o.startDrag && o.startDrag(...e))
                }, [t("div", {
                    class: "progress_bar_filled",
                    style: C({
                        width: o.progress + "%"
                    })
                }, [t("div", {
                    class: "progress_handle",
                    onMousedown: s[5] || (s[5] = w(((...e) => o.startDrag && o.startDrag(...e)), ["stop"]))
                }, null, 32)], 4)], 544)])) : u("", !0)]), !o.hide_phone_call_buttons && ((null == n ? void 0 : n.hang_up_button) || (null == n ? void 0 : n.call_again_button_text) || (null == n ? void 0 : n.start_new_call_button_text)) ? (a(), e("div", Al, [(null == n ? void 0 : n.hang_up_button) ? (a(), e("div", {
                    key: 0,
                    class: "phone_call_button hang_up",
                    onClick: e => {
                        var t;
                        return o.utils.postRequest(null == (t = null == n ? void 0 : n.hang_up_button) ? void 0 : t.on_click_callback_url)
                    }
                }, [v(G, {
                    class: "phone_call_button_icon"
                }), t("div", Tl, g(null == (y = null == n ? void 0 : n.hang_up_button) ? void 0 : y.text), 1)], 8, Ml)) : u("", !0), (null == n ? void 0 : n.call_again_button_text) ? (a(), e("div", {
                    key: 1,
                    class: "phone_call_button call_again",
                    onClick: () => {
                        var e, t, a, l, s, i, r, c, d;
                        (null == n ? void 0 : n.place_id) || (null == (t = null == (e = o.message) ? void 0 : e.session_state) ? void 0 : t.personal_mode) ? (o.initialContact = {
                            type: (null == (l = null == (a = o.message) ? void 0 : a.session_state) ? void 0 : l.personal_mode) ? "personal" : "business",
                            place_id: null == n ? void 0 : n.place_id,
                            title: null == n ? void 0 : n.called_party_name,
                            address: null == n ? void 0 : n.called_party_address,
                            phone: null == n ? void 0 : n.phone_number,
                            country: null == (i = null == (s = o.message) ? void 0 : s.session_state) ? void 0 : i.personal_country,
                            countryCode: null == (c = null == (r = o.message) ? void 0 : r.session_state) ? void 0 : c.personal_country_code
                        }, o.initialPurpose = null == n ? void 0 : n.purpose, o.create_phone_call_form_show = !0) : null == (d = o.phoneCallSetting) || d.showLastStep()
                    }
                }, [v(q, {
                    class: "phone_call_button_icon"
                }), t("div", Ll, g(null == n ? void 0 : n.call_again_button_text), 1)], 8, Dl)) : u("", !0), (null == n ? void 0 : n.start_new_call_button_text) ? (a(), e("div", {
                    key: 2,
                    class: "phone_call_button start_new_call",
                    onClick: s[8] || (s[8] = () => {
                        o.initialContact = null, o.initialPurpose = "", o.create_phone_call_form_show = !0
                    })
                }, [v(V, {
                    class: "phone_call_button_icon"
                }), t("div", El, g(null == n ? void 0 : n.start_new_call_button_text), 1)])) : u("", !0)])) : u("", !0)], 64)) : u("", !0), "search_queries" == (null == n ? void 0 : n.type) ? (a(), e(p, {
                    key: 6
                }, [n.expand && (null == n ? void 0 : n.simple_table) ? (a(), e("div", $l, [t("div", Ol, [v(H, {
                    data: null == (k = null == n ? void 0 : n.simple_table) ? void 0 : k.data,
                    columns: null == (x = null == n ? void 0 : n.simple_table) ? void 0 : x.columns,
                    "scroll-x": 100 * (null == (W = null == n ? void 0 : n.simple_table) ? void 0 : W.columns),
                    bordered: !0,
                    loading: !n.is_finish
                }, null, 8, ["data", "columns", "scroll-x", "loading"])])])) : u("", !0), n.expand ? (a(), e("div", Ul, [t("div", Fl, [(a(!0), e(p, null, h(null == n ? void 0 : n.search_queries, ((t, l) => (a(), e("div", {
                    key: l,
                    class: "search_query"
                }, g(t[0]), 1)))), 128))])])) : u("", !0)], 64)) : u("", !0), "verify_results" == (null == n ? void 0 : n.type) ? (a(), e("div", Nl, [(null == n ? void 0 : n.expand) ? (a(), e("div", Bl, [(null == n ? void 0 : n.expand) ? (a(!0), e(p, {
                    key: 0
                }, h(null == n ? void 0 : n.verify_results, ((n, i) => (a(), e("div", {
                    key: i,
                    class: "resource"
                }, [n ? (a(), e("div", {
                    key: 0,
                    class: b(["resource_header", {
                        expand: null == n ? void 0 : n.expand_resource
                    }]),
                    onClick: () => {
                        n.expand_resource = !n.expand_resource
                    }
                }, [t("div", ql, [o.isRunning && (null == n ? void 0 : n.running) ? (a(), e("div", Vl, [o.isRunning && n.running ? (a(), e(p, {
                    key: 0
                }, [u("", !0), v(j)], 64)) : u("", !0)])) : u("", !0), t("div", Hl, [t("img", {
                    src: n.favicon
                }, null, 8, zl)]), t("div", Wl, [t("a", {
                    target: "_blank",
                    href: n.url,
                    onClick: e => {
                        n.expand_resource && (o.utils.windowopen(n.url), e.stopPropagation()), e.preventDefault()
                    }
                }, g(n.title ? n.title : n.url), 9, Ql)])]), t("div", Kl, [n.expand_resource ? (a(), d(M, {
                    key: 0
                })) : (a(), d(P, {
                    key: 1
                }))])], 10, Gl)) : u("", !0), u("", !0), n && n.expand_resource ? (a(), e("div", Jl, [s[17] || (s[17] = t("div", {
                    class: "divider"
                }, null, -1)), t("div", Zl, [t("div", Yl, [v(z)]), t("div", Xl, [v(z)]), (null == n ? void 0 : n.evidence_str) && (null == n ? void 0 : n.evidence_str.length) > 0 ? (a(), e("div", es, [(a(!0), e(p, null, h(null == n ? void 0 : n.evidence_str, (t => (a(), e("p", null, g(t), 1)))), 256))])) : (null == n ? void 0 : n.evaluation) ? (a(), e("div", ts, [t("p", null, g(null == n ? void 0 : n.evaluation), 1)])) : (a(), e("div", as, [t("p", null, g(l.$t("components.cross_check.no_related_information")), 1)])), u("", !0)]), s[18] || (s[18] = t("div", {
                    class: "divider"
                }, null, -1)), (null == n ? void 0 : n.url) ? (a(), e("div", ls, [t("a", {
                    target: "_blank",
                    href: n.url,
                    onClick: e => {
                        o.utils.windowopen(n.url)
                    }
                }, g(n.url), 9, ss)])) : u("", !0)])) : u("", !0)])))), 128)) : u("", !0), o.isRunning && !n.is_finish ? (a(), e("div", ns, [v(j)])) : u("", !0)])) : (a(), e("div", os, [t("div", is, [u("", !0), (a(!0), e(p, null, h(null == n ? void 0 : n.verify_results, ((l, s) => (a(), e("div", {
                    key: s,
                    class: "favicon"
                }, [t("img", {
                    src: l.favicon
                }, null, 8, rs)])))), 128))])]))])) : u("", !0), "table_cell_details" == (null == n ? void 0 : n.type) ? (a(), e("div", cs, [v(Q, {
                    step: n,
                    runningProject: o.isRunning
                }, null, 8, ["step", "runningProject"])])) : u("", !0), "spark_result" == (null == n ? void 0 : n.type) ? (a(), e(p, {
                    key: 9
                }, [n.expand ? (a(), e("div", ds, [t("div", us, [t("div", ps, [v(E, {
                    source: null == (J = null == n ? void 0 : n.spark_result) ? void 0 : J.note_content
                }, null, 8, ["source"])]), n.spark_result ? (a(), e("div", vs, [t("div", _s, [t("img", {
                    src: o.SparkPageIcon
                }, null, 8, ms)]), t("div", {
                    class: "title",
                    onClick: e => o.utils.windowopen(n.spark_result.url)
                }, g(n.spark_result.title), 9, gs), t("div", hs, [v(E, {
                    source: null == (Z = null == n ? void 0 : n.spark_result) ? void 0 : Z.content
                }, null, 8, ["source"])]), (null == (Y = null == n ? void 0 : n.spark_result) ? void 0 : Y.gallery) && o.loadedGallery.length >= 2 && o.loadedGallery.length <= 4 ? (a(), e("div", {
                    key: 0,
                    class: b(["gallery", "gallery_" + o.loadedGallery.length])
                }, [(a(!0), e(p, null, h(o.loadedGallery, ((l, s) => (a(), e("div", {
                    class: b(["gallery__item", l.type]),
                    key: s
                }, [t("div", {
                    class: "image",
                    onClick: e => o.utils.windowopen(l.link)
                }, [t("img", {
                    src: o.getImageUrl(l)
                }, null, 8, ys)], 8, fs)], 2)))), 128))], 2)) : u("", !0), (null == (X = null == n ? void 0 : n.spark_result) ? void 0 : X.gallery) ? S((a(), e("div", ks, [(a(!0), e(p, null, h(n.spark_result.gallery, ((t, l) => (a(), e("img", {
                    src: o.getImageUrl(t),
                    onLoad: e => {
                        o.loadedGallery.length < 4 && o.loadedGallery.push(t)
                    },
                    onError: s[9] || (s[9] = e => {})
                }, null, 40, bs)))), 256))], 512)), [
                    [I, !1]
                ]) : u("", !0), n.spark_result.show_see_more ? (a(), e("div", {
                    key: 2,
                    class: "see-more",
                    onClick: e => o.utils.windowopen(n.spark_result.url)
                }, [t("div", xs, g(l.$t("components.cross_check.see-more")), 1), t("div", Cs, [v(K)])], 8, ws)) : u("", !0)])) : u("", !0)])])) : u("", !0)], 64)) : u("", !0)], 2)], 2)) : u("", !0)], 64)
            })), 256)), v(J, {
                show: o.create_phone_call_form_show,
                "onUpdate:show": s[10] || (s[10] = e => o.create_phone_call_form_show = e),
                agentConfig: {
                    id: "phone_call",
                    name: o.t("pages.article_verification.phone_call"),
                    agent_type: "phone_call",
                    new_label: o.t("pages.article_verification.new-phone-call"),
                    button_label: o.t("pages.article_verification.phone_call")
                },
                "initial-contact": o.initialContact,
                "initial-purpose": o.initialPurpose,
                onCreated: s[11] || (s[11] = async e => {
                    o.create_phone_call_form_show = !1, l.$emit("phoneCallTaskCreated", e)
                }),
                onQuotaExceed: s[12] || (s[12] = () => {
                    l.$emit("quotaExceed")
                }),
                ref: "createFormPhoneCall"
            }, null, 8, ["show", "agentConfig", "initial-contact", "initial-purpose"]), v(Z, {
                ref: "phoneCallSetting",
                onCreated: s[13] || (s[13] = async e => {
                    var t;
                    null == (t = o.phoneCallSetting) || t.hide(), l.$emit("phoneCallTaskCreated", e)
                }),
                onQuotaExceed: s[14] || (s[14] = () => {
                    l.$emit("quotaExceed")
                })
            }, null, 512)], 64)
        }],
        ["__scopeId", "data-v-ccdbbb95"]
    ]),
    Is = {
        name: "CrossCheckContent",
        components: {
            AsyncTaskContent: Ss,
            TableCellDetails: Ca,
            PartnerData: z,
            SelectedIconSvg: Y,
            TriangleIconSvg: ee,
            PublicIcon: te,
            PrivateIcon: X,
            StopIcon: Zt,
            DataIcon: ae,
            QuoteIcon: se,
            LoadingAnimation: re,
            Markdown: ce,
            DocumentIcon: ie,
            ChecksCircleIcon: Z,
            ChecksIcon: Kt,
            MagnifierAnimation: q,
            ExpandMoreIcon: V,
            ExpandLessIcon: H,
            DeepDiveSearchVerticalResult: W
        },
        props: {
            project: Object,
            runningProject: Boolean,
            loadingProjectId: String,
            no_header: {
                type: Boolean,
                default: !1
            },
            hide_intermediate_steps: {
                type: Boolean,
                default: !1
            },
            hide_phone_call_buttons: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["runProject", "updateProject", "phoneCallTaskCreated", "quotaExceed"],
        setup(e, {
            emit: t,
            expose: a
        }) {
            const l = R("currentUser"),
                o = de(),
                d = P(),
                u = j({
                    locale: d.$i18n.locale.value,
                    messages: d.$i18n.messages.value,
                    fallbackLocale: d.$i18n.fallbackLocale.value
                }),
                p = s([]),
                {
                    t: v
                } = x(),
                _ = {
                    article_verification: {
                        name: v("components.cross_check.cross-check"),
                        icon: Z
                    },
                    generate_sparkpage_gan: {
                        name: v("components.cross_check.generate-sparkpage-gan"),
                        icon: Z
                    },
                    generate_sparkpage_table: {
                        name: v("pages.article_verification.data-search"),
                        icon: ae
                    }
                },
                m = s(null),
                {
                    project: g,
                    runningProject: h,
                    loadingProjectId: f,
                    no_header: y,
                    hide_intermediate_steps: b,
                    hide_phone_call_buttons: w
                } = n(e),
                C = s("");
            i((() => m.value ? m.value.length : 0), (async e => {
                if (!e) return;
                await new Promise((e => {
                    setTimeout((() => {
                        e()
                    }), 0)
                }));
                [{
                    class: "-md-ext-gallery",
                    func: (e, t, a) => {
                        t.value && t.value.list || k.log("data.list is null");
                        const l = M(ne, {
                            data: t,
                            ...a
                        });
                        return l.use(VueTouchEvents), l
                    }
                }, {
                    class: "-md-ext-block-quote",
                    func: (e, t, a) => M(oe, {
                        data: t,
                        ...a
                    })
                }].forEach((e => {
                    m.value.map((t => Array.prototype.slice.call(t.querySelectorAll(`.${e.class}`)))).flat().forEach((t => {
                        try {
                            if (t.classList.contains("-md-ext-initialized")) return;
                            const a = s(JSON.parse(t.textContent)),
                                l = e.func(t, a, {});
                            l.use(u), p.value.push({
                                app: l,
                                div: t,
                                data: a,
                                exposedDict: {}
                            }), l.provide("mdExtExpose", (e => t => {
                                const a = p.value.findIndex((t => t.div === e));
                                a >= 0 && (p.value[a].exposedDict = t)
                            })(t)), l.mount(t), t.classList.add("-md-ext-initialized")
                        } catch (a) {
                            k.log("init markdown component error"), k.log(`source: ${t.outerHTML}`)
                        }
                    }))
                }))
            })), a({});
            const S = s(!1),
                I = () => {
                    S.value = !1
                };
            c((() => {
                document.removeEventListener("click", I)
            })), r((async () => {
                document.addEventListener("click", I)
            }));
            const A = s(null),
                L = s(null);
            return i([() => {
                var e, t;
                return null == (t = null == (e = g.value) ? void 0 : e.session_state) ? void 0 : t.is_replay
            }, () => {
                var t;
                return null == (t = e.project) ? void 0 : t.type
            }], (async ([e, t]) => {
                "phone_call" === t && (e ? A.value = T((() => D((() =>
                        import ("./DK9KCCqA.js")), __vite__mapDeps([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233]),
                    import.meta.url))) : L.value = T((() => D((() =>
                        import ("./BtBJxh_l.js")), __vite__mapDeps([234, 1, 2, 3, 4, 195, 31, 146, 32, 20, 6, 8, 9, 7, 10, 14, 15, 16, 17, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 38, 39, 40, 41, 5, 11, 12, 13, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 173, 169, 61, 141, 140, 170, 148, 126, 104, 127, 57, 128, 82, 55, 56, 171, 147, 196, 197, 59, 84, 88, 87, 150, 62, 53, 54, 58, 60, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 83, 85, 86, 89, 90, 91, 158, 139, 108, 109, 142, 129, 135, 106, 183, 198, 96, 97, 98, 99, 100, 101, 102, 103, 105, 107, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 130, 131, 132, 133, 134, 136, 137, 138, 143, 199, 157, 159, 160, 161, 162, 179, 200, 201, 202, 151, 163, 93, 164, 152, 203, 204, 205, 206, 207, 192, 186, 187, 208, 180, 209, 210, 190, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 184, 224, 92, 235, 94, 236, 237, 193, 149, 175, 176, 177, 178, 181, 145, 153, 154, 155, 156, 165, 166, 167, 168, 144, 172, 174, 182, 185, 188, 189, 191, 95, 194, 226, 227, 228, 229, 230, 231, 225, 232, 238]),
                    import.meta.url))))
            }), {
                immediate: !0
            }), {
                taskTypeConfig: _,
                projectIsFinished: () => !!g.value && ["SUCCESS", "FINISHED"].indexOf(g.value.status) >= 0,
                messageApi: o,
                currentUser: l,
                no_header: y,
                hide_intermediate_steps: b,
                hide_phone_call_buttons: w,
                utils: k,
                updateProject: () => {
                    t("updateProject", g.value)
                },
                markdownElementRefs: m,
                projectPermissionShowDropDownList: S,
                begin_cross_check: () => {
                    g.value.session_state.begin_cross_check = !0;
                    const e = [];
                    g.value.session_state.statements.forEach(((t, a) => {
                        e.push({
                            i: a,
                            statement: t.statement,
                            checked: t.checked
                        })
                    })), t("runProject", {
                        begin_cross_check: !0,
                        statements: e
                    })
                },
                add_statement_text: C,
                add_statement_input_keydown: e => {
                    e.isComposing || "Enter" === e.key && (g.value.session_state.begin_cross_check || (g.value.session_state.statements.push({
                        i: g.value.session_state.statements.length,
                        statement: C.value,
                        checked: !0
                    }), C.value = ""), e.preventDefault(), e.stopPropagation())
                },
                project: g,
                runningProject: h,
                loadingProjectId: f,
                PhoneCallTaskReplayComponent: A,
                PhoneCallTaskContentComponent: L
            }
        }
    },
    Ps = {
        key: 0,
        class: "main-header-wrapper"
    },
    js = {
        class: "main-header"
    },
    Rs = {
        class: "main-header-left"
    },
    As = {
        key: 0,
        class: "title"
    },
    Ms = {
        class: "icon"
    },
    Ts = {
        class: "label"
    },
    Ds = {
        class: "main-header-right"
    },
    Ls = {
        key: 0,
        class: "dropdown"
    },
    Es = {
        class: "icon"
    },
    $s = {
        class: "text"
    },
    Os = {
        key: 1,
        class: "dropdown"
    },
    Us = {
        class: "icon"
    },
    Fs = {
        class: "text"
    },
    Ns = {
        key: 2,
        class: "dropDownList"
    },
    Bs = {
        class: "wrapper"
    },
    Gs = {
        class: "name"
    },
    qs = {
        class: "icon"
    },
    Vs = {
        class: "text"
    },
    Hs = {
        class: "desc"
    },
    zs = {
        key: 0,
        class: "selected"
    },
    Ws = {
        class: "wrapper"
    },
    Qs = {
        class: "name"
    },
    Ks = {
        class: "icon"
    },
    Js = {
        class: "text"
    },
    Zs = {
        class: "desc"
    },
    Ys = {
        key: 0,
        class: "selected"
    },
    Xs = {
        class: "main-inner-inner-wrapper"
    },
    en = {
        class: "main-inner-inner"
    },
    tn = {
        key: 0,
        class: "error",
        style: {
            color: "red"
        }
    },
    an = {
        key: 1,
        class: "bubble user article-content"
    },
    ln = {
        class: "bubble-inner"
    },
    sn = {
        class: "text"
    };
const nn = l(Is, [
        ["render", function(l, s, n, o, i, r) {
            var c, _, m, h, f, k, w, x, C, S, I, P, j, R;
            const M = y("PublicIcon"),
                T = y("TriangleIconSvg"),
                D = y("PrivateIcon"),
                L = y("SelectedIconSvg"),
                E = y("Markdown"),
                $ = y("AsyncTaskContent");
            return a(), e(p, null, [o.project && !o.no_header && "phone_call" != o.project.type ? (a(), e("div", Ps, [t("div", js, [t("div", Rs, [o.taskTypeConfig[o.project.type] ? (a(), e("div", As, [t("div", Ms, [(a(), d(A(o.taskTypeConfig[o.project.type].icon)))]), t("div", Ts, g(o.taskTypeConfig[o.project.type].name), 1)])) : u("", !0)]), t("div", Ds, [(null == (c = o.project) ? void 0 : c.cogen_id) === (null == (_ = o.currentUser) ? void 0 : _.id) && "phone_call" != o.project.type ? (a(), e("div", {
                key: 0,
                class: "permission",
                onClick: s[2] || (s[2] = e => {
                    o.runningProject || o.loadingProjectId ? o.projectPermissionShowDropDownList = !1 : (o.projectPermissionShowDropDownList = !o.projectPermissionShowDropDownList, e.stopPropagation(), e.preventDefault())
                })
            }, [o.project.is_private ? o.project.is_private ? (a(), e("div", Os, [t("div", Us, [v(D)]), t("div", Fs, g(l.$t("components.cross_check.private")), 1), t("div", {
                class: b(["triangle", o.projectPermissionShowDropDownList ? "reverse" : ""])
            }, [v(T)], 2)])) : u("", !0) : (a(), e("div", Ls, [t("div", Es, [v(M)]), t("div", $s, g(l.$t("components.cross_check.public")), 1), t("div", {
                class: b(["triangle", o.projectPermissionShowDropDownList ? "reverse" : ""])
            }, [v(T)], 2)])), o.projectPermissionShowDropDownList ? (a(), e("ul", Ns, [t("li", {
                onClick: s[0] || (s[0] = e => {
                    e.stopPropagation(), e.preventDefault(), o.project.is_private = !1, o.projectPermissionShowDropDownList = !1, o.updateProject()
                })
            }, [t("div", Bs, [t("div", Gs, [t("div", qs, [v(M)]), t("div", Vs, [t("span", null, g(l.$t("components.cross_check.public")), 1)])]), t("div", Hs, g(l.$t("components.cross_check.anyone-with-the-link-can-view")), 1)]), o.project.is_private ? u("", !0) : (a(), e("div", zs, [v(L)]))]), t("li", {
                onClick: s[1] || (s[1] = e => {
                    e.stopPropagation(), e.preventDefault(), o.project.is_private = !0, o.projectPermissionShowDropDownList = !1, o.updateProject()
                })
            }, [t("div", Ws, [t("div", Qs, [t("div", Ks, [v(D)]), t("div", Js, [t("span", null, g(l.$t("components.cross_check.private")), 1)])]), t("div", Zs, g(l.$t("components.cross_check.only-you-can-view-this")), 1)]), o.project.is_private ? (a(), e("div", Ys, [v(L)])) : u("", !0)])])) : u("", !0)])) : u("", !0)])])])) : u("", !0), t("div", Xs, [t("div", en, [(null == (h = null == (m = o.project) ? void 0 : m.session_state) ? void 0 : h.error) ? (a(), e("div", tn, g(null == (k = null == (f = o.project) ? void 0 : f.session_state) ? void 0 : k.error), 1)) : u("", !0), (null == (x = null == (w = o.project) ? void 0 : w.session_state) ? void 0 : x.article) ? (a(), e("div", an, [t("div", ln, [t("div", sn, [v(E, {
                source: null == (S = null == (C = o.project) ? void 0 : C.session_state) ? void 0 : S.article
            }, null, 8, ["source"])])])])) : u("", !0), u("", !0), "phone_call" !== o.project.type ? (a(), d($, {
                key: 3,
                message: {
                    session_state: o.project.session_state,
                    status: o.project.status
                },
                isRunning: o.runningProject,
                hide_intermediate_steps: o.hide_intermediate_steps,
                hide_phone_call_buttons: o.hide_phone_call_buttons,
                onPhoneCallTaskCreated: s[3] || (s[3] = async e => {
                    l.$emit("phoneCallTaskCreated", e)
                }),
                onQuotaExceed: s[4] || (s[4] = e => {
                    l.$emit("quotaExceed", e)
                })
            }, null, 8, ["message", "isRunning", "hide_intermediate_steps", "hide_phone_call_buttons"])) : u("", !0), "phone_call" === o.project.type && !(null == (P = null == (I = o.project) ? void 0 : I.session_state) ? void 0 : P.is_replay) && o.PhoneCallTaskContentComponent ? (a(), d(A(o.PhoneCallTaskContentComponent), {
                key: 4,
                message: {
                    session_state: o.project.session_state,
                    status: o.project.status
                },
                isRunning: o.runningProject,
                hide_intermediate_steps: o.hide_intermediate_steps,
                hide_phone_call_buttons: o.hide_phone_call_buttons,
                onPhoneCallTaskCreated: s[5] || (s[5] = async e => {
                    l.$emit("phoneCallTaskCreated", e)
                }),
                onQuotaExceed: s[6] || (s[6] = e => {
                    l.$emit("quotaExceed", e)
                })
            }, null, 40, ["message", "isRunning", "hide_intermediate_steps", "hide_phone_call_buttons"])) : u("", !0), "phone_call" === o.project.type && (null == (R = null == (j = o.project) ? void 0 : j.session_state) ? void 0 : R.is_replay) && o.PhoneCallTaskReplayComponent ? (a(), d(A(o.PhoneCallTaskReplayComponent), {
                key: 5,
                message: {
                    session_state: o.project.session_state,
                    status: o.project.status
                },
                hide_intermediate_steps: o.hide_intermediate_steps,
                onPhoneCallTaskCreated: s[7] || (s[7] = async e => {
                    l.$emit("phoneCallTaskCreated", e)
                }),
                onQuotaExceed: s[8] || (s[8] = e => {
                    l.$emit("quotaExceed", e)
                })
            }, null, 40, ["message", "hide_intermediate_steps"])) : u("", !0)])])], 64)
        }],
        ["__scopeId", "data-v-c42d874d"]
    ]),
    on = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 24 24"
    };
const rn = {
        render: function(l, s) {
            return a(), e("svg", on, s[0] || (s[0] = [t("path", {
                d: "M16.01 11H4v2h12.01v3L20 12l-3.99-4z",
                fill: "currentColor"
            }, null, -1)]))
        }
    },
    cn = {
        name: "MoaTranslatorContent",
        components: {
            ToBottomIcon: Ue,
            ReflectionIcon: Re,
            ExpandIcon: Q,
            Copilot: pe,
            PromptInput: _e,
            CopyIcon: me,
            MarkdownWithPlugins: ue,
            LoadingAnimation: re,
            PolygonIcon: be,
            UploadIcon: Ie,
            MoreIcon: V,
            ArrowRightAltFilled: rn,
            CheckIcon: je,
            NPopover: Be
        },
        props: {
            project: {
                type: Object,
                required: !0
            },
            no_input: {
                type: Boolean,
                default: !1
            },
            readOnly: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["newSessionCreated"],
        setup(e, {
            emit: t
        }) {
            const {
                t: a
            } = x(), {
                project: l,
                no_input: c,
                readOnly: d
            } = n(e), u = () => {
                l.value && l.value.session_state && l.value.session_state.messages && D.value && D.value.setMessages(l.value.session_state.messages)
            };
            i((() => l.value && l.value.id), (() => {
                l.value = e.project, u()
            }));
            const p = "COPILOT_MOA_TRANSLATOR",
                v = s(!1),
                _ = s(null),
                m = [{
                    name: "google-translate,deepl,gpt-4.1,claude-4-sonnet",
                    icon: De,
                    label: "Mixture-of-Agents",
                    full_label: "Mixture-of-Agents",
                    description: "Auto-mixes best AI models for your task."
                }, {
                    name: "google-translate",
                    icon: ye,
                    label: "Google Translate",
                    full_label: "Google Translate"
                }, {
                    name: "deepl",
                    icon: ge,
                    label: "DeepL",
                    full_label: "DeepL"
                }, {
                    name: "gpt-4.1",
                    icon: he,
                    label: "GPT-4.1",
                    full_label: "Open AI GPT-4.1"
                }, {
                    name: "claude-4-sonnet",
                    icon: fe,
                    label: "Claude 4 Sonnet",
                    full_label: "Anthropic Claude 4 Sonnet"
                }],
                g = {
                    Arabic: a("components.moa_translator.source_language_labels.Arabic"),
                    Bulgarian: a("components.moa_translator.source_language_labels.Bulgarian"),
                    Czech: a("components.moa_translator.source_language_labels.Czech"),
                    Danish: a("components.moa_translator.source_language_labels.Danish"),
                    German: a("components.moa_translator.source_language_labels.German"),
                    Greek: a("components.moa_translator.source_language_labels.Greek"),
                    English: a("components.moa_translator.source_language_labels.English"),
                    Spanish: a("components.moa_translator.source_language_labels.Spanish"),
                    Estonian: a("components.moa_translator.source_language_labels.Estonian"),
                    Finnish: a("components.moa_translator.source_language_labels.Finnish"),
                    French: a("components.moa_translator.source_language_labels.French"),
                    Hungarian: a("components.moa_translator.source_language_labels.Hungarian"),
                    Indonesian: a("components.moa_translator.source_language_labels.Indonesian"),
                    Italian: a("components.moa_translator.source_language_labels.Italian"),
                    Japanese: a("components.moa_translator.source_language_labels.Japanese"),
                    Korean: a("components.moa_translator.source_language_labels.Korean"),
                    Lithuanian: a("components.moa_translator.source_language_labels.Lithuanian"),
                    Latvian: a("components.moa_translator.source_language_labels.Latvian"),
                    Norwegian: a("components.moa_translator.source_language_labels.Norwegian"),
                    Dutch: a("components.moa_translator.source_language_labels.Dutch"),
                    Polish: a("components.moa_translator.source_language_labels.Polish"),
                    Portuguese: a("components.moa_translator.source_language_labels.Portuguese"),
                    Romanian: a("components.moa_translator.source_language_labels.Romanian"),
                    Russian: a("components.moa_translator.source_language_labels.Russian"),
                    Slovak: a("components.moa_translator.source_language_labels.Slovak"),
                    Slovenian: a("components.moa_translator.source_language_labels.Slovenian"),
                    Swedish: a("components.moa_translator.source_language_labels.Swedish"),
                    Turkish: a("components.moa_translator.source_language_labels.Turkish"),
                    Ukrainian: a("components.moa_translator.source_language_labels.Ukrainian"),
                    Chinese: a("components.moa_translator.source_language_labels.Chinese"),
                    Urdu: a("components.moa_translator.source_language_labels.Urdu"),
                    Vietnamese: a("components.moa_translator.source_language_labels.Vietnamese"),
                    Telugu: a("components.moa_translator.source_language_labels.Telugu"),
                    Thai: a("components.moa_translator.source_language_labels.Thai"),
                    Tamil: a("components.moa_translator.source_language_labels.Tamil"),
                    Serbian: a("components.moa_translator.source_language_labels.Serbian"),
                    Punjabi: a("components.moa_translator.source_language_labels.Punjabi"),
                    Persian: a("components.moa_translator.source_language_labels.Persian"),
                    Malay: a("components.moa_translator.source_language_labels.Malay"),
                    Malayalam: a("components.moa_translator.source_language_labels.Malayalam"),
                    Marathi: a("components.moa_translator.source_language_labels.Marathi"),
                    Kannada: a("components.moa_translator.source_language_labels.Kannada"),
                    Gujarati: a("components.moa_translator.source_language_labels.Gujarati"),
                    Hebrew: a("components.moa_translator.source_language_labels.Hebrew"),
                    Hindi: a("components.moa_translator.source_language_labels.Hindi"),
                    Bengali: a("components.moa_translator.source_language_labels.Bengali")
                },
                h = {
                    ar: a("components.moa_translator.target_language_labels.Arabic"),
                    bg: a("components.moa_translator.target_language_labels.Bulgarian"),
                    cs: a("components.moa_translator.target_language_labels.Czech"),
                    da: a("components.moa_translator.target_language_labels.Danish"),
                    de: a("components.moa_translator.target_language_labels.German"),
                    el: a("components.moa_translator.target_language_labels.Greek"),
                    "en-us": a("components.moa_translator.target_language_labels.English_US"),
                    "en-gb": a("components.moa_translator.target_language_labels.English_GB"),
                    es: a("components.moa_translator.target_language_labels.Spanish"),
                    et: a("components.moa_translator.target_language_labels.Estonian"),
                    fi: a("components.moa_translator.target_language_labels.Finnish"),
                    fr: a("components.moa_translator.target_language_labels.French"),
                    hu: a("components.moa_translator.target_language_labels.Hungarian"),
                    id: a("components.moa_translator.target_language_labels.Indonesian"),
                    it: a("components.moa_translator.target_language_labels.Italian"),
                    ja: a("components.moa_translator.target_language_labels.Japanese"),
                    ko: a("components.moa_translator.target_language_labels.Korean"),
                    lt: a("components.moa_translator.target_language_labels.Lithuanian"),
                    lv: a("components.moa_translator.target_language_labels.Latvian"),
                    no: a("components.moa_translator.target_language_labels.Norwegian"),
                    nl: a("components.moa_translator.target_language_labels.Dutch"),
                    pl: a("components.moa_translator.target_language_labels.Polish"),
                    "pt-br": a("components.moa_translator.target_language_labels.Portuguese_BR"),
                    "pt-pt": a("components.moa_translator.target_language_labels.Portuguese_PT"),
                    ro: a("components.moa_translator.target_language_labels.Romanian"),
                    ru: a("components.moa_translator.target_language_labels.Russian"),
                    sk: a("components.moa_translator.target_language_labels.Slovak"),
                    sl: a("components.moa_translator.target_language_labels.Slovenian"),
                    sv: a("components.moa_translator.target_language_labels.Swedish"),
                    tr: a("components.moa_translator.target_language_labels.Turkish"),
                    uk: a("components.moa_translator.target_language_labels.Ukrainian"),
                    "zh-hans": a("components.moa_translator.target_language_labels.Chinese_Hans"),
                    "zh-hant": a("components.moa_translator.target_language_labels.Chinese_Hant")
                },
                f = s("gpt-4.1"),
                y = "moa-translator-modelsSelected"; {
                const e = localStorage.getItem(y);
                e && m.find((t => t.name == e)) && (f.value = e), i((() => f.value), (() => {
                    localStorage.setItem(y, f.value)
                }))
            }
            const b = s("en-us"),
                w = "moa-translator-default-target-language-code"; {
                const e = localStorage.getItem(w);
                e && (b.value = e)
            }
            i(b, (() => {
                localStorage.setItem(w, b.value)
            }));
            const C = s(!1),
                S = s(b.value),
                I = s(null),
                P = o((() => {
                    if (l.value && l.value.session_state && l.value.session_state.messages) {
                        const e = l.value.session_state.messages[l.value.session_state.messages.length - 1];
                        if (e && e.session_state && e.session_state.detected_source_lang) return {
                            detected_source_lang: e.session_state.detected_source_lang,
                            detected_source_lang_target_language_code: e.session_state.detected_source_lang_target_language_code
                        }
                    }
                    return null
                })),
                j = s(!1),
                R = o((() => ({
                    run_with_another_model: j.value,
                    models: f.value.split(","),
                    target_lang_code: S.value
                }))),
                A = () => {
                    if (l.value && l.value.session_state && l.value.session_state.messages && l.value.session_state.messages.length > 0) {
                        let e = l.value.session_state.messages[l.value.session_state.messages.length - 1];
                        "assistant" === e.role && e.session_state && e.session_state.target_lang_code && (S.value = e.session_state.target_lang_code)
                    }
                };
            A();
            const M = s(null),
                T = s([]),
                D = s(null),
                L = s(""),
                $ = s(null),
                O = s(null);
            r((() => {
                u()
            }));
            const U = Fe((() => {
                k.isMobile() || window.innerWidth <= 1220 ? D.value.setScrollElement(document.documentElement) : D.value.setScrollElement(M.value)
            }), 100);
            r((() => {
                U(), window.addEventListener("resize", U)
            })), E((() => {
                window.removeEventListener("resize", U)
            })), r((() => {
                k.isMobile() || $.value.focus()
            }));
            const F = s([]),
                N = s([]);
            return (async () => {
                const e = await fetch(`/api/copilot?type=${p}`),
                    t = await e.json();
                F.value = t.data.metadata.source_languages, N.value = t.data.metadata.target_languages
            })(), {
                readOnly: d,
                setDefaultTargetLanguage: e => {
                    b.value = e
                },
                no_input: c,
                runWithAnotherModel: j,
                tryMoa: () => {
                    j.value = !0, f.value = m[0].name, setTimeout((() => {
                        D.value.forceSubmit()
                    }), 0)
                },
                swapedTargetLanguageCode: I,
                swapSourceAndTargetLanguage: () => {
                    if (I.value) return S.value = I.value, void(I.value = "");
                    if (P.value && P.value.detected_source_lang_target_language_code) {
                        let e = S.value;
                        S.value = P.value.detected_source_lang_target_language_code, I.value = e
                    }
                },
                targetLanguagePopoverShow: v,
                targetLanguagePopover: _,
                targetLanguages: N,
                sourceLanguages: F,
                targetLanguageCode: S,
                sourceLanguage: P,
                COPILOT_TYPE: p,
                sourceLanguageLabels: g,
                targetLanguageLabels: h,
                chatWrapper: M,
                promptInputRef: $,
                selectImage: () => {
                    $.value.selectImage()
                },
                promptImages: T,
                modelsPopoverShow: C,
                modelsConfig: m,
                modelsSelected: f,
                copilotAskFinish: e => {
                    A(), t("newSessionCreated", l.value)
                },
                onProjectEvent: e => {
                    k.log("onProjectEvent", e), "project_start" == e.type && (l.value.id = e.id), "project_field" == e.type && Ne(l.value, e.field_name, (() => e.field_value))
                },
                LoadingAnim: '<span class="cursor">█</span>',
                extra_data: R,
                conversationContent: O,
                prompt: L,
                submitAndClearPrompt: (e, t) => {
                    I.value = null, D.value.submitPrompt(e, "", t), L.value = "", T.value = []
                },
                project: l,
                copilotRef: D,
                safePath: e => e.replace(/\./g, "_").replace(/-/g, "_"),
                strip: e => "string" != typeof e ? e : e.replace(/^[\n\t \r]+|[\n\t \r]+$/g, "")
            }
        }
    },
    dn = {
        class: "general-chat-wrapper"
    },
    un = {
        class: "chat-wrapper",
        ref: "chatWrapper"
    },
    pn = {
        key: 0,
        class: "empty-placeholder"
    },
    vn = {
        key: 1,
        class: "conversation-wrapper",
        ref: "conversationContent"
    },
    _n = {
        key: 0,
        class: "conversation-content"
    },
    mn = {
        key: 0,
        class: "assistant-message-divider"
    },
    gn = {
        key: 1,
        class: "moa-title"
    },
    hn = ["onClick"],
    fn = {
        class: "icon"
    },
    yn = {
        class: "model-response"
    },
    kn = ["onClick"],
    bn = {
        key: 0,
        class: "status"
    },
    wn = {
        key: 1,
        class: "icon finished"
    },
    xn = {
        class: "icon"
    },
    Cn = ["src"],
    Sn = {
        class: "text"
    },
    In = {
        key: 0,
        class: "content"
    },
    Pn = {
        key: 1,
        class: "content no-expand"
    },
    jn = {
        key: 0,
        class: "cursor"
    },
    Rn = ["onClick"],
    An = {
        class: "icon"
    },
    Mn = {
        class: "model-response"
    },
    Tn = ["onClick"],
    Dn = {
        class: "status"
    },
    Ln = {
        key: 1,
        class: "icon finished"
    },
    En = {
        class: "icon"
    },
    $n = {
        key: 0,
        class: "content"
    },
    On = {
        key: 1,
        class: "content no-expand"
    },
    Un = {
        key: 0,
        class: "cursor"
    },
    Fn = {
        class: "desc"
    },
    Nn = {
        key: 0,
        class: "content"
    },
    Bn = {
        key: 1
    },
    Gn = {
        class: "content"
    },
    qn = {
        key: 0
    },
    Vn = {
        key: 1
    },
    Hn = ["src"],
    zn = {
        key: 2,
        class: "bubble try_moa"
    },
    Wn = {
        class: "right"
    },
    Qn = {
        key: 0,
        class: "input-wrapper-wrapper"
    },
    Kn = {
        class: "input-wrapper-wrapper-inner"
    },
    Jn = {
        class: "controls"
    },
    Zn = {
        class: "models-wrapper"
    },
    Yn = {
        class: "models-selected"
    },
    Xn = {
        class: "model-selected"
    },
    eo = {
        class: "icon"
    },
    to = ["src"],
    ao = {
        class: "text"
    },
    lo = {
        class: "models-list"
    },
    so = ["onClick"],
    no = {
        class: "row"
    },
    oo = {
        class: "left"
    },
    io = {
        class: "icon"
    },
    ro = ["src"],
    co = {
        class: "text"
    },
    uo = {
        class: "right"
    },
    po = ["checked"],
    vo = {
        key: 0,
        class: "description"
    },
    _o = {
        class: "text"
    },
    mo = {
        key: 0,
        class: "divider"
    },
    go = {
        class: "source-language-wrapper"
    },
    ho = {
        key: 0,
        class: "source-language"
    },
    fo = {
        key: 1,
        class: "source-language"
    },
    yo = {
        class: "text"
    },
    ko = {
        class: "icon"
    },
    bo = {
        class: "target-language-menu"
    },
    wo = ["onClick"],
    xo = {
        class: "input-wrapper",
        ref: "inputWrapper"
    };
const Co = l(cn, [
        ["render", function(l, s, n, o, i, r) {
            var c;
            const m = y("ExpandIcon"),
                f = re,
                k = y("CheckIcon"),
                x = ue,
                C = y("ReflectionIcon"),
                S = (y("CopyIcon"), pe),
                I = y("ToBottomIcon"),
                P = (y("UploadIcon"), y("PolygonIcon")),
                j = y("NPopover"),
                R = y("ArrowRightAltFilled"),
                A = y("MoreIcon"),
                M = ve,
                T = _e;
            return a(), e("div", dn, [t("div", un, [v(S, {
                copilotType: o.COPILOT_TYPE,
                ref: "copilotRef",
                defaultMessageContent: "",
                extra_data: o.extra_data,
                onProjectEvent: o.onProjectEvent,
                onCopilotAskFinish: s[1] || (s[1] = e => {
                    o.copilotAskFinish(e)
                })
            }, {
                default: _((({
                    messages: l,
                    doAction: n
                }) => [l && 0 != l.length ? u("", !0) : (a(), e("div", pn, " Hi there, what can I help with? ")), l && l.length > 0 ? (a(), e("div", vn, [l && l.length > 0 ? (a(), e("div", _n, [(a(!0), e(p, null, h(l, ((n, i) => {
                    var r, c, _, y, S, I, P, j, R, A, M, T, D, E, $, O, U;
                    return a(), e(p, {
                        key: i
                    }, [i > 0 && "assistant" == n.role && "assistant" == l[i - 1].role ? (a(), e("div", mn)) : u("", !0), (null == (c = null == (r = n.session_state) ? void 0 : r.models) ? void 0 : c.length) > 1 ? (a(), e("div", gn, " Mixture-of-Agents ")) : u("", !0), t("div", {
                        class: b(["conversation-statement", "user" == n.role ? "user" : "assistant"])
                    }, [t("div", {
                        class: b(["conversation-item-desc", "user" == n.role ? "user" : "assistant"])
                    }, [(null == (y = null == (_ = null == n ? void 0 : n.session_state) ? void 0 : _.models) ? void 0 : y.length) > 1 ? (a(), e(p, {
                        key: 0
                    }, [u("", !0), (a(!0), e(p, null, h(null == (S = null == n ? void 0 : n.session_state) ? void 0 : S.models, (l => {
                        var s, i, r, c, p, _, h, y, C;
                        return a(), e("div", {
                            class: b([{
                                thinking: n.thinking && !(null == (s = null == n ? void 0 : n.session_state) ? void 0 : s[`${o.safePath(l)}_is_finished`]),
                                expanded: null == (i = null == n ? void 0 : n.session_state) ? void 0 : i[`${o.safePath(l)}_expanded`]
                            }, "bubble model-response-wrapper"])
                        }, [t("div", {
                            class: "expand-button",
                            onClick: () => {
                                n.session_state[`${o.safePath(l)}_expanded`] = !n.session_state[`${o.safePath(l)}_expanded`]
                            }
                        }, [t("div", fn, [v(m)])], 8, hn), t("div", yn, [t("div", {
                            class: "title",
                            onClick: w((() => {
                                n.session_state[`${o.safePath(l)}_expanded`] = !n.session_state[`${o.safePath(l)}_expanded`]
                            }), ["stop"])
                        }, [n.thinking || (null == (r = null == n ? void 0 : n.session_state) ? void 0 : r[`${o.safePath(l)}_is_finished`]) ? (a(), e("div", bn, [n.thinking && !(null == (c = null == n ? void 0 : n.session_state) ? void 0 : c[`${o.safePath(l)}_is_finished`]) ? (a(), d(f, {
                            key: 0
                        })) : (null == (p = null == n ? void 0 : n.session_state) ? void 0 : p[`${o.safePath(l)}_is_finished`]) ? (a(), e("div", wn, [v(k)])) : u("", !0)])) : u("", !0), t("div", xn, [t("img", {
                            src: o.modelsConfig.find((e => e.name == l)).icon
                        }, null, 8, Cn)]), t("div", Sn, g((null == (_ = o.modelsConfig.find((e => e.name == l))) ? void 0 : _.label) || l), 1)], 8, kn), n.session_state[`${o.safePath(l)}_expanded`] ? (a(), e("div", In, [v(x, {
                            source: null == (h = null == n ? void 0 : n.session_state) ? void 0 : h[`${o.safePath(l)}_translated_text`]
                        }, null, 8, ["source"])])) : (a(), e("div", Pn, [t("div", null, [L(g(null == (y = null == n ? void 0 : n.session_state) ? void 0 : y[`${o.safePath(l)}_translated_text`]) + " ", 1), n.thinking && !(null == (C = null == n ? void 0 : n.session_state) ? void 0 : C[`${o.safePath(l)}_is_finished`]) ? (a(), e("span", jn, "█")) : u("", !0)])]))])], 2)
                    })), 256))], 64)) : u("", !0), (null == (I = n.session_state) ? void 0 : I.reflection) ? (a(), e("div", {
                        key: 1,
                        class: b(["bubble model-response-wrapper", {
                            thinking: n.thinking,
                            expanded: null == (P = n.session_state) ? void 0 : P.reflection_expanded
                        }])
                    }, [t("div", {
                        class: "expand-button",
                        onClick: () => {
                            n.session_state.reflection_expanded = !n.session_state.reflection_expanded
                        }
                    }, [t("div", An, [v(m)])], 8, Rn), t("div", Mn, [t("div", {
                        class: "title",
                        onClick: w((() => {
                            n.session_state.reflection_expanded = !n.session_state.reflection_expanded
                        }), ["stop"])
                    }, [t("div", Dn, [n.thinking && !(null == (j = null == n ? void 0 : n.session_state) ? void 0 : j.reflection_is_finished) ? (a(), d(f, {
                        key: 0
                    })) : (null == (R = null == n ? void 0 : n.session_state) ? void 0 : R.reflection_is_finished) ? (a(), e("div", Ln, [v(k)])) : u("", !0)]), t("div", En, [v(C)]), s[11] || (s[11] = t("div", {
                        class: "text"
                    }, "Reflection", -1))], 8, Tn), (null == (A = n.session_state) ? void 0 : A.reflection_expanded) ? (a(), e("div", $n, [v(x, {
                        source: ((null == (M = n.session_state) ? void 0 : M.reflection) ? o.strip(null == (T = n.session_state) ? void 0 : T.reflection) : "") + (n.thinking && (null == (D = n.session_state) ? void 0 : D.reflection_is_started) && !(null == (E = n.session_state) ? void 0 : E.reflection_is_finished) ? o.LoadingAnim : "")
                    }, null, 8, ["source"])])) : (a(), e("div", On, [t("div", null, [L(g(null == ($ = null == n ? void 0 : n.session_state) ? void 0 : $.reflection) + " ", 1), n.thinking && !(null == (O = null == n ? void 0 : n.session_state) ? void 0 : O.reflection_is_finished) ? (a(), e("span", Un, "█")) : u("", !0)])]))]), u("", !0)], 2)) : u("", !0), t("div", {
                        class: b(["bubble", {
                            thinking: n.thinking
                        }])
                    }, [t("div", Fn, ["string" == typeof n.content ? (a(), e("div", Nn, ["assistant" == n.role ? (a(), d(x, {
                        key: 0,
                        source: (n.content ? o.strip(n.content) : (null == (U = null == n ? void 0 : n.session_state) ? void 0 : U.translated_text) || "") + (n.thinking ? o.LoadingAnim : "")
                    }, null, 8, ["source"])) : (a(), e("pre", Bn, [t("code", null, g(n.content), 1)]))])) : (a(!0), e(p, {
                        key: 1
                    }, h(n.content, (l => {
                        var s;
                        return a(), e("div", Gn, ["text" === l.type ? (a(), e("div", qn, g(null == l ? void 0 : l.text), 1)) : u("", !0), "image_url" === l.type ? (a(), e("div", Vn, [t("img", {
                            src: null == (s = null == l ? void 0 : l.image_url) ? void 0 : s.url
                        }, null, 8, Hn)])) : u("", !0)])
                    })), 256))]), u("", !0)], 2), o.readOnly || n !== l[l.length - 1] || "assistant" != n.role || n.is_prompt || n.session_state && n.session_state.models && 1 != n.session_state.models.length ? u("", !0) : (a(), e("div", zn, [s[12] || (s[12] = t("div", {
                        class: "left"
                    }, "This answer isn't good enough?", -1)), t("div", Wn, [t("div", {
                        class: "button",
                        onClick: s[0] || (s[0] = () => {
                            o.tryMoa()
                        })
                    }, " Try Mixture-of-Agents ")])]))], 2)], 2)], 64)
                })), 128))])) : u("", !0)], 512)) : u("", !0)])),
                _: 1
            }, 8, ["copilotType", "extra_data", "onProjectEvent"])], 512), o.no_input ? u("", !0) : (a(), e("div", Qn, [(null == (c = o.copilotRef) ? void 0 : c.toBottomVisible) ? (a(), e("div", {
                key: 0,
                class: "to-bottom-icon",
                onClick: s[2] || (s[2] = (...e) => o.copilotRef.toBottom && o.copilotRef.toBottom(...e))
            }, [v(I)])) : u("", !0), t("div", Kn, [v(M, null, {
                default: _((() => [t("div", Jn, [u("", !0), t("div", Zn, [v(j, {
                    trigger: "click",
                    "show-arrow": !1,
                    placement: "top-start",
                    class: "models-popover",
                    style: {
                        padding: "0",
                        "border-radius": "12px",
                        "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                    },
                    "onUpdate:show": s[4] || (s[4] = e => {
                        o.modelsPopoverShow = e
                    })
                }, {
                    trigger: _((() => [t("div", Yn, [t("div", Xn, [t("div", eo, [t("img", {
                        src: o.modelsConfig.find((e => e.name == o.modelsSelected)).icon
                    }, null, 8, to)]), t("div", ao, g(o.modelsConfig.find((e => e.name == o.modelsSelected)).label), 1)]), t("div", {
                        class: b(["icon", {
                            active: o.modelsPopoverShow
                        }])
                    }, [v(P)], 2)])])),
                    default: _((() => [t("div", lo, [(a(!0), e(p, null, h(o.modelsConfig, (l => (a(), e(p, null, [t("div", {
                        class: "model",
                        onClick: () => {
                            o.modelsSelected = l.name
                        }
                    }, [t("div", no, [t("div", oo, [t("div", io, [t("img", {
                        src: l.icon
                    }, null, 8, ro)]), t("div", co, g(l.label), 1)]), t("div", uo, [t("input", {
                        type: "radio",
                        checked: o.modelsSelected == l.name
                    }, null, 8, po)])]), l.description ? (a(), e("div", vo, [t("div", _o, g(l.description), 1)])) : u("", !0)], 8, so), l.description ? (a(), e("div", mo)) : u("", !0)], 64)))), 256))])])),
                    _: 1
                })]), t("div", go, [o.sourceLanguage && o.sourceLanguage.detected_source_lang && null === o.swapedTargetLanguageCode ? (a(), e("div", ho, g(o.sourceLanguageLabels[o.sourceLanguage.detected_source_lang] || o.sourceLanguage.detected_source_lang), 1)) : (a(), e("div", fo, g(l.$t("components.moa_translator.auto_detect")), 1))]), t("div", {
                    class: "arrow-right-icon icon",
                    onClick: s[5] || (s[5] = () => {
                        (o.sourceLanguage || o.swapedTargetLanguageCode) && o.swapSourceAndTargetLanguage()
                    })
                }, [v(R)]), v(j, {
                    "onUpdate:show": s[7] || (s[7] = e => {
                        o.targetLanguagePopoverShow = e
                    }),
                    trigger: "click",
                    "show-arrow": !1,
                    placement: "top-start",
                    style: {
                        padding: "0",
                        "border-radius": "12px",
                        "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                    },
                    ref: "targetLanguagePopover"
                }, {
                    trigger: _((() => [t("div", {
                        class: b(["target-language", {
                            active: o.targetLanguagePopoverShow
                        }]),
                        onClick: s[6] || (s[6] = () => {
                            o.targetLanguagePopoverShow = !o.targetLanguagePopoverShow
                        })
                    }, [t("div", yo, g(o.targetLanguageLabels[o.targetLanguageCode] || o.targetLanguageCode), 1), t("div", ko, [v(A)])], 2)])),
                    default: _((() => [t("div", bo, [(a(!0), e(p, null, h(o.targetLanguages, (t => (a(), e("div", {
                        class: "target-language-menu-item",
                        onClick: () => {
                            o.targetLanguageCode = t.code, o.setDefaultTargetLanguage(t.code), o.targetLanguagePopover.setShow(!1)
                        }
                    }, g(t.name), 9, wo)))), 256))])])),
                    _: 1
                }, 512)])])),
                _: 1
            }), t("div", xo, [v(T, {
                ref: "promptInputRef",
                modelValue: o.prompt,
                "onUpdate:modelValue": s[8] || (s[8] = e => o.prompt = e),
                onSubmitPrompt: s[9] || (s[9] = (e, t) => {
                    o.submitAndClearPrompt(e, t)
                }),
                supportImages: !1,
                images: o.promptImages,
                "onUpdate:images": s[10] || (s[10] = e => o.promptImages = e),
                useSuggestion: !1,
                placeholder: "Message",
                styleClass: "moa"
            }, null, 8, ["modelValue", "images"])], 512)])]))])
        }],
        ["__scopeId", "data-v-1ea34e58"]
    ]),
    So = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 32 32"
    };
const Io = {
        name: "MOAImageSidePanel",
        components: {
            PolygonIcon: be,
            PreviewIcon: K,
            DownloadIcon: Ge,
            ImageIcon: {
                render: function(l, s) {
                    return a(), e("svg", So, s[0] || (s[0] = [t("path", {
                        d: "M19 14a3 3 0 1 0-3-3a3 3 0 0 0 3 3zm0-4a1 1 0 1 1-1 1a1 1 0 0 1 1-1z",
                        fill: "currentColor"
                    }, null, -1), t("path", {
                        d: "M26 4H6a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2zm0 22H6v-6l5-5l5.59 5.59a2 2 0 0 0 2.82 0L21 19l5 5zm0-4.83l-3.59-3.59a2 2 0 0 0-2.82 0L18 19.17l-5.59-5.59a2 2 0 0 0-2.82 0L6 17.17V6h20z",
                        fill: "currentColor"
                    }, null, -1)]))
                }
            },
            LoadingAnimation: re,
            SearchIcon: It
        },
        props: {
            items: {
                type: Array,
                default: () => []
            },
            loading: {
                type: Boolean,
                default: !1
            },
            columnWidth: {
                type: Number,
                default: 140
            },
            columnGap: {
                type: Number,
                default: 8
            },
            emptyText: {
                type: String,
                default: "No generated images yet"
            }
        },
        emits: ["itemClick", "expandChange", "preview", "download", "search"],
        setup(e, {
            emit: t
        }) {
            const a = s(""),
                l = s([]),
                n = s(!1),
                c = async (e = "") => {
                    var t;
                    try {
                        n.value = !0, l.value = [];
                        const a = new URLSearchParams;
                        e && a.set("query", e);
                        const s = await fetch(`/api/copilot/get_flow_items?${a.toString()}`, {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json"
                                },
                                body: JSON.stringify({
                                    type: "COPILOT_MOA_IMAGE"
                                })
                            }),
                            o = await s.json();
                        0 === o.status && (null == (t = o.data) ? void 0 : t.flow_items) && (l.value = o.data.flow_items.map((e => ({ ...e,
                            thumbnail: e.url || e.thumbnail,
                            original_width: e.width || 800,
                            original_height: e.height || 800
                        }))))
                    } catch (a) {
                        l.value = []
                    } finally {
                        n.value = !1
                    }
                },
                d = o((() => n.value || e.loading));
            i((() => e.items), (e => {
                (null == e ? void 0 : e.length) && (l.value = e)
            }), {
                immediate: !0
            }), r((() => {
                var t, a;
                (null == (t = e.items) ? void 0 : t.length) || (null == (a = l.value) ? void 0 : a.length) || c()
            }));
            const u = o((() => {
                    var e;
                    if (!(null == (e = l.value) ? void 0 : e.length)) return [];
                    const t = Array.from({
                        length: 3
                    }, (() => []));
                    return l.value.forEach((e => {
                        t.reduce(((e, t) => p(e) <= p(t) ? e : t)).push(e)
                    })), t
                })),
                p = e => e.reduce(((e, t) => e + 1), 0);
            return {
                columns: u,
                searchQuery: a,
                handleSearch: () => {
                    a.value.trim() && c(a.value)
                },
                flowItems: l,
                isLoadingFlowItems: n,
                showLoading: d
            }
        }
    },
    Po = {
        class: "moa-side-panel"
    },
    jo = {
        class: "panel-content"
    },
    Ro = {
        key: 0,
        class: "images-container"
    },
    Ao = ["onClick"],
    Mo = ["src", "alt"],
    To = {
        class: "image-overlay"
    },
    Do = {
        class: "image-actions"
    },
    Lo = ["onClick"],
    Eo = ["onClick"],
    $o = {
        key: 1,
        class: "empty-state"
    },
    Oo = {
        class: "empty-icon"
    },
    Uo = {
        key: 2,
        class: "loading-overlay"
    },
    Fo = {
        class: "search-box"
    },
    No = {
        class: "input moa"
    },
    Bo = {
        class: "textarea-wrapper"
    },
    Go = {
        class: "icon-group"
    };
const qo = l(Io, [
        ["render", function(l, s, n, o, i, r) {
            var c;
            const d = y("PreviewIcon"),
                _ = y("DownloadIcon"),
                m = y("ImageIcon"),
                f = re,
                k = y("SearchIcon");
            return a(), e("div", Po, [t("div", jo, [s[3] || (s[3] = t("div", {
                class: "panel-header"
            }, [t("h3", null, "Generated Images")], -1)), (null == (c = o.flowItems) ? void 0 : c.length) ? (a(), e("div", Ro, [(a(!0), e(p, null, h(o.columns, ((s, n) => (a(), e("div", {
                key: n,
                class: "image-column"
            }, [(a(!0), e(p, null, h(s, ((s, n) => (a(), e("div", {
                key: n,
                class: "image-item",
                onClick: e => l.$emit("itemClick", s)
            }, [t("img", {
                src: s.thumbnail,
                alt: s.title
            }, null, 8, Mo), t("div", To, [t("div", Do, [t("button", {
                class: "action-btn",
                onClick: w((e => l.$emit("preview", s)), ["stop"])
            }, [v(d)], 8, Lo), t("button", {
                class: "action-btn",
                onClick: w((e => l.$emit("download", s)), ["stop"])
            }, [v(_)], 8, Eo)])])], 8, Ao)))), 128))])))), 128))])) : (a(), e("div", $o, [t("div", Oo, [v(m)]), t("p", null, g(n.emptyText), 1)])), o.showLoading ? (a(), e("div", Uo, [v(f)])) : u("", !0), t("div", Fo, [t("div", No, [t("div", Bo, [S(t("input", {
                type: "text",
                "onUpdate:modelValue": s[0] || (s[0] = e => o.searchQuery = e),
                onKeyup: s[1] || (s[1] = O(((...e) => o.handleSearch && o.handleSearch(...e)), ["enter"])),
                placeholder: "Search images...",
                class: "search-input"
            }, null, 544), [
                [$, o.searchQuery]
            ]), t("div", Go, [t("div", {
                class: "input-icon",
                onClick: s[2] || (s[2] = (...e) => o.handleSearch && o.handleSearch(...e))
            }, [v(k)])])])])])])])
        }],
        ["__scopeId", "data-v-be7e023b"]
    ]),
    Vo = {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    };
const Ho = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const zo = {
        name: "GenerateImageContent",
        components: {
            PermissionDropdown: Te,
            ExpandIcon: Q,
            Copilot: pe,
            PromptInput: _e,
            CopyIcon: me,
            MarkdownWithPlugins: ue,
            LoadingAnimation: re,
            PolygonIcon: be,
            MOAImageResult: We,
            MOAVideoResult: ze,
            NSelect: St,
            UploadIcon: Ie,
            NRadioGroup: wt,
            NRadio: bt,
            NSpace: yt,
            MOAImageSidePanel: qo,
            MediaGallery: it,
            GalleryIcon: Me,
            UnifiedSelect: at,
            NTooltip: Ct,
            DropdownIcon: tt,
            CheckIcon: je,
            ReflectionIcon: Re,
            RatioAutoIcon: et,
            Ratio21Icon: dt,
            Ratio169Icon: {
                render: function(l, s) {
                    return a(), e("svg", Vo, s[0] || (s[0] = [t("rect", {
                        width: "10",
                        height: "16",
                        rx: "3",
                        transform: "matrix(1.39071e-07 1 1 -1.39071e-07 4 7)",
                        stroke: "#232425",
                        "stroke-width": "1.75"
                    }, null, -1)]))
                }
            },
            Ratio32Icon: Xe,
            Ratio11Icon: ct,
            Ratio34Icon: Ye,
            Ratio23Icon: Ze,
            Ratio916Icon: {
                render: function(l, s) {
                    return a(), e("svg", Ho, s[0] || (s[0] = [t("rect", {
                        width: "10",
                        height: "16",
                        rx: "3",
                        transform: "matrix(-1 0 0 1 17 4)",
                        stroke: "#232425",
                        "stroke-width": "1.75"
                    }, null, -1)]))
                }
            },
            Ratio12Icon: rt,
            Ratio43Icon: Je,
            MOAImageDetail: Le,
            InfoIcon: _t,
            QuoteMarkIcon: ot,
            RemixIcon: ht,
            NPopover: Be,
            NCheckbox: xt,
            ImageSelectModel: Ke,
            UpgradePrompt: Ae
        },
        props: {
            project: {
                type: Object,
                required: !0
            },
            debug: {
                type: Boolean,
                default: !1
            },
            no_input: {
                type: Boolean,
                default: !1
            },
            readOnly: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["newSessionCreated"],
        setup(e, {
            emit: t
        }) {
            const {
                t: a
            } = x(), l = s(!1), c = s("auto"), d = s("none"), u = s([]), p = R("currentUser"), v = N(), _ = F();
            de();
            const m = [{
                    name: "auto",
                    value: "auto",
                    icon: "RatioAutoIcon",
                    label: a("components.generate_image_content.ratio_auto"),
                    divider: !0
                }, {
                    name: "9:16",
                    value: "9:16",
                    icon: "Ratio12Icon",
                    label: "9:16"
                }, {
                    name: "2:3",
                    value: "2:3",
                    icon: "Ratio23Icon",
                    label: "2:3"
                }, {
                    name: "3:4",
                    value: "3:4",
                    icon: "Ratio34Icon",
                    label: "3:4",
                    divider: !0
                }, {
                    name: "1:1",
                    value: "1:1",
                    icon: "Ratio11Icon",
                    label: "1:1",
                    divider: !0
                }, {
                    name: "4:3",
                    value: "4:3",
                    icon: "Ratio43Icon",
                    label: "4:3"
                }, {
                    name: "3:2",
                    value: "3:2",
                    icon: "Ratio32Icon",
                    label: "3:2"
                }, {
                    name: "16:9",
                    value: "16:9",
                    icon: "Ratio21Icon",
                    label: "16:9"
                }],
                g = s("auto"),
                h = s(!1),
                f = s(!1),
                y = "moa-image-reflectionEnabled",
                b = ft(),
                {
                    prompt: w,
                    images: C
                } = U(b),
                S = we(),
                {
                    modelsSelected: I,
                    selectedAspectRatio: P,
                    personalizeMode: j,
                    hdEnabled: A,
                    reflectionEnabled: M,
                    imageModelMap: T,
                    selectedStyle: D
                } = U(S),
                L = o((() => {
                    var e;
                    return "plus" === (null == (e = null == p ? void 0 : p.value) ? void 0 : e.plan)
                })); {
                const e = localStorage.getItem(y);
                null !== e && (f.value = "true" === e)
            }
            i(f, (() => {
                localStorage.setItem(y, f.value.toString())
            }));
            const $ = s([]),
                O = ["flux", "flux-pro/ultra", "flux-speed", "recraft-v3"],
                B = o((() => ({
                    model_configs: ("auto" === c.value ? ((e, t, a) => {
                        let l = e.length > 0 ? [...e] : [...t];
                        if (k.log("getAutoModeModels starting with models", l, "; imageModelMap", a), Object.keys(a).length > 0) {
                            const e = Object.values(a).filter((e => e.model)).filter((e => {
                                const t = $e.find((t => t.name === e.model));
                                return t && !t.is_i2i
                            })).reduce(((e, t) => (e[t.model] = (e[t.model] || 0) + 1, e)), {});
                            if (k.log("modelUsageCount", e), Object.keys(e).length > 0) {
                                const a = Object.keys(e)[0],
                                    s = [a, a, ...t.filter((e => e !== a))].slice(0, 4);
                                k.log("newModels after combining", s), l = s
                            }
                        }
                        const s = l.slice(0, 4);
                        return k.log("Final models returned:", s), s
                    })($.value, O, { ...fe.value
                    }) : [c.value]).map((e => ({
                        model: e,
                        aspect_ratio: g.value,
                        use_personalized_models: "none" !== d.value,
                        fashion_profile_id: "none" !== d.value ? d.value : null,
                        hd: h.value,
                        reflection_enabled: f.value,
                        style: ke.value,
                        ..._e.value && {
                            debug: !0
                        }
                    }))),
                    llm_model: he.value,
                    imageModelMap: fe.value
                }))),
                G = s(null),
                {
                    project: q,
                    no_input: V,
                    readOnly: H
                } = n(e),
                z = s(q.value.not_recommendable ? "private" : "public");
            i((() => z.value), (() => {
                fetch("/api/project/update", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        id: q.value.id,
                        not_recommendable: "private" === z.value
                    })
                })
            }));
            const W = s(""),
                Q = s(null),
                K = s([]),
                J = s([]),
                Z = s(null),
                Y = (e, t, a) => {
                    oe.value = !1, W.value = "", K.value = [], J.value = [], G.value.submitPrompt(e, "", t);
                    const l = B.value.model_configs.filter((e => {
                        const t = $e.find((t => t.name === e.model));
                        return t && !t.is_i2i
                    })).map((e => e.model));
                    k.log("actual sending models", l), $.value = l
                },
                X = s(null);
            i((() => q.value ? q.value.id : ""), (e => {
                const t = new URL(window.location.href).searchParams;
                e && "undefined" !== e && t.set("id", e), history.replaceState(null, "", `${window.location.pathname}?${t.toString()}`), G.value.setMessages(q.value.session_state.messages)
            }));
            r((() => {
                k.isMobile() || Q.value.focus()
            })), r((() => {
                q.value && q.value.session_state.messages && G.value.setMessages(q.value.session_state.messages), te(), ne(), (() => {
                    const e = v.currentRoute.value.query.remixImageUrl;
                    e && (K.value.push(e), v.replace({
                        path: v.currentRoute.value.path,
                        query: { ...v.currentRoute.value.query,
                            remixImageUrl: void 0
                        }
                    }))
                })(), window.addEventListener("keydown", Ce); {
                    const e = document.querySelector(".moa-image-detail");
                    if (e) {
                        const {
                            direction: t
                        } = mt(e, {
                            threshold: 50,
                            onSwipeEnd(e) {
                                Z.value && ("left" === t.value ? xe("next") : "right" === t.value && xe("prev"))
                            }
                        })
                    }
                }
                "chat_now" === _.query.action && (W.value = w.value, c.value = I.value, g.value = P.value, d.value = j.value, h.value = A.value, f.value = M.value, K.value = C.value, fe.value = T.value, ke.value = D.value, Y(W.value, K.value, J.value), fe.value = {})
            })), E((() => {
                document.body.style.overflow = "", window.removeEventListener("keydown", Ce)
            }));
            const ee = e => {
                    K.value || (K.value = []), K.value.push(e.url), fe.value[e.url] = {
                        url: e.url,
                        model: e.model
                    }, k.log("Image added from model:", e)
                },
                te = async () => {
                    try {
                        const e = await nt();
                        u.value = e
                    } catch (e) {
                        u.value = []
                    }
                },
                ae = o((() => {
                    if ("none" === d.value) return "Personalized: None";
                    const e = u.value.find((e => e.id === d.value));
                    return e ? `Personalized: ${e.name}` : "Style: None"
                })),
                le = s([]),
                se = s(!1),
                ne = async () => {
                    var e;
                    try {
                        se.value = !0;
                        const t = await fetch("/api/copilot/get_flow_items", {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json"
                                },
                                body: JSON.stringify({
                                    type: "COPILOT_MOA_IMAGE",
                                    update_flow_data: {}
                                })
                            }),
                            a = await t.json();
                        0 === a.status && (null == (e = a.data) ? void 0 : e.flow_items) && (le.value = a.data.flow_items.map((e => ({ ...e,
                            thumbnail: e.url || e.thumbnail,
                            original_width: e.width || 800,
                            original_height: e.height || 800
                        }))))
                    } catch (t) {} finally {
                        se.value = !1
                    }
                },
                oe = s(!1),
                ie = s([]),
                re = s(""),
                ce = s(["best wallpaper", "beautiful image", "best image"][Math.floor(3 * Math.random())]),
                ue = o((() => {
                    const e = u.value.map((e => {
                        var t;
                        return {
                            id: e.id,
                            label: e.name,
                            value: e.id,
                            icon: null == (t = e.id_image_urls) ? void 0 : t[0],
                            extra: e
                        }
                    }));
                    return e.unshift({
                        id: "none",
                        label: "None",
                        value: "none"
                    }), e
                })),
                pe = s(!1);
            i(d, (e => {
                if (e && "none" !== e) {
                    const t = u.value.find((t => t.id === e));
                    t && k.log("Selected profile:", t.name)
                }
            }));
            const ve = o((() => (k.log("currentUser:", p.value), !k.isGensparkApp() && !(!p.value || !p.value.gk_realtime_dogfood)))),
                _e = o((() => e.debug)),
                me = s(new Map),
                ge = s(new Map),
                he = s("gpt-4o"),
                fe = s({});
            i((() => K.value), (e => {
                const t = {};
                Object.entries(fe.value).forEach((([a, l]) => {
                    e.includes(a) && (t[a] = l)
                })), fe.value = t
            }));
            const ye = s(!1),
                ke = s("auto"),
                be = o((() => Ee(a)));
            i(Z, (e => {
                document.body.style.overflow = e ? "hidden" : ""
            }));
            const xe = e => {
                    var t, a, l;
                    if (!Z.value) return;
                    const s = [];
                    if (((null == (l = null == (a = null == (t = G.value) ? void 0 : t.getMessagesRef) ? void 0 : a.call(t)) ? void 0 : l.value) || []).forEach((e => {
                            try {
                                const t = "string" == typeof e.content ? JSON.parse(e.content) : e.content;
                                (null == t ? void 0 : t.generated_images) && t.generated_images.forEach((e => {
                                    (e.url || e.url_nowatermark) && s.push({ ...e,
                                        url: e.url || e.url_nowatermark
                                    })
                                }))
                            } catch (t) {}
                        })), s.length <= 1) return;
                    const n = s.findIndex((e => {
                        const t = Z.value.url || Z.value.url_nowatermark;
                        return e.url === t || e.url_nowatermark === t
                    }));
                    if (-1 === n) return;
                    let o;
                    o = "next" === e ? n + 1 >= s.length ? 0 : n + 1 : n - 1 < 0 ? s.length - 1 : n - 1, Z.value = { ...s[o],
                        transition: "next" === e ? "slide-left" : "slide-right"
                    }
                },
                Ce = e => {
                    Z.value && ("ArrowLeft" === e.key ? xe("prev") : "ArrowRight" === e.key ? xe("next") : "Escape" === e.key && (Z.value = null))
                },
                Se = o((() => ({
                    touchAction: "pan-y pinch-zoom",
                    userSelect: "none",
                    width: "100%",
                    height: "100%",
                    position: "relative"
                }))),
                Ie = o((() => K.value.length > 0 ? a("components.generate_image_content.remix_image_message") : a("components.generate_image_content.create_image_message"))),
                Pe = s(null),
                je = o((() => window.innerWidth <= 768 ? .95 * (window.innerHeight - 200) : 500)),
                Re = o((() => $e.filter((e => {
                    const t = !e.is_i2i || e.is_i2i && K.value && K.value.length > 0;
                    return "gemini-flash-2.0" === e.name ? ve.value && t : t
                }))));
            gt(Pe, (e => {
                if (oe.value) {
                    e.target.closest(".remix-selector") || (oe.value = !1)
                }
            }));
            return {
                readOnly: H,
                no_input: V,
                t: a,
                modelsPopoverShow: l,
                modelsConfig: $e,
                modelsSelected: c,
                copilotAskFinish: e => {
                    t("newSessionCreated", q.value)
                },
                copilotAskStart: e => {
                    (() => {
                        const e = $e.find((e => e.name === c.value));
                        if ((null == e ? void 0 : e.is_i2i) && (!K.value || 0 === K.value.length)) {
                            const e = $e.find((e => !e.is_i2i));
                            e && (c.value = e.name)
                        }
                    })()
                },
                onProjectEvent: e => {
                    k.log("onProjectEvent", e), "project_start" == e.type && (q.value.id = e.id), "project_field" == e.type && Ne(q.value, e.field_name, (() => e.field_value))
                },
                LoadingAnim: '<span class="cursor">█</span>',
                extra_data: B,
                conversationContent: X,
                prompt: W,
                submitAndClearPrompt: Y,
                project: q,
                copilotRef: G,
                strip: e => e.replace(/^[\n\t \r]+|[\n\t \r]+$/g, ""),
                personalizeMode: d,
                fashionProfiles: u,
                toggleModel: e => {
                    c.value = e, l.value = !1, "auto" === e && ($.value = [])
                },
                aspectRatioOptions: m,
                selectedAspectRatio: g,
                selectAspectRatio: e => {
                    g.value = e, pe.value = !1
                },
                promptInputRef: Q,
                promptImages: K,
                selectImage: () => {
                    Q.value.selectImage()
                },
                addImageUrl: ee,
                getSelectedProfileName: ae,
                flowItems: le,
                isLoadingFlowItems: se,
                handleFlowItemClick: e => {
                    Q.value && ee(e.thumbnail)
                },
                handlePreview: e => {
                    imageViewer && imageViewer.openImageViewer(e, le.value)
                },
                handleDownload: async e => {
                    try {
                        const t = await fetch(e.url),
                            a = await t.blob();
                        Oe.saveAs(a, `image-${Date.now()}.png`)
                    } catch (t) {}
                },
                showGallery: oe,
                toggleSidePanel: () => {
                    oe.value = !oe.value
                },
                hdEnabled: h,
                personalizeOptions: ue,
                aspectRatioPopoverShow: pe,
                personalizeConfig: {
                    type: "personalize",
                    placeholder: "Select Style"
                },
                promptFiles: J,
                showDebugFeatures: _e,
                promptExpandedStates: me,
                getMessagePrompts: e => (null == e ? void 0 : e.session_state) ? Object.entries(e.session_state).filter((([e]) => e.startsWith("prompt_"))).map((([t, a]) => ({
                    key: t,
                    content: a,
                    stateKey: `${e.id||e.timestamp||Date.now()}_${t}`
                }))).filter((e => e.content)) : [],
                togglePrompt: e => {
                    if (e) {
                        const t = me.value.get(e.stateKey) || !1;
                        me.value.set(e.stateKey, !t)
                    }
                },
                isPromptExpanded: e => me.value.get(e.stateKey) || !1,
                reflectionEnabled: f,
                tryReflection: () => {
                    f.value = !0, setTimeout((() => {
                        G.value.forceSubmit()
                    }), 0)
                },
                messageTaskStatus: ge,
                handleTaskComplete: ({
                    index: e,
                    result: t
                }, a) => {
                    const l = a.id || a.timestamp || Date.now(),
                        s = new Map(ge.value);
                    s.has(l) || s.set(l, []);
                    const n = [...s.get(l) || []];
                    n[e] = {
                        status: t.status,
                        url: "SUCCESS" === t.status ? t.image_urls[0] : null,
                        timestamp: Date.now()
                    }, s.set(l, n), ge.value = s
                },
                isMessageComplete: e => {
                    var t;
                    const a = e.id || e.timestamp || Date.now(),
                        l = ge.value.get(a);
                    if (!l) return !1;
                    const s = "string" == typeof e.content ? JSON.parse(e.content) : e.content,
                        n = (null == (t = null == s ? void 0 : s.generated_images) ? void 0 : t.length) || 0,
                        o = l.filter((e => "SUCCESS" === (null == e ? void 0 : e.status))).length;
                    return n > 0 && o === n
                },
                handleImageSelect: e => {
                    Z.value = e
                },
                selectedImage: Z,
                permission: z,
                llmModel: he,
                imageModelMap: fe,
                lastUsedModels: $,
                stylePopoverShow: ye,
                selectedStyle: ke,
                selectStyle: e => {
                    ke.value = e, ye.value = !1
                },
                getLabel: e => "function" == typeof e.label ? e.label(a) : e.label,
                getDescription: e => e.description ? e.description(a) : "",
                styleOptions: be,
                currentUser: p,
                handleImageNavigation: xe,
                imageDetailStyles: Se,
                cachedItems: ie,
                cachedQuery: re,
                updateCachedItems: e => {
                    ie.value = e.items, re.value = e.query
                },
                QuoteMarkIcon: ot,
                featureEnabled: ve,
                toggleRemixDropdown: () => {
                    oe.value = !oe.value
                },
                closeRemixDropdown: () => {
                    oe.value = !1
                },
                placeholderMessage: Ie,
                handlePopoverShow: e => {
                    e && (oe.value = !1)
                },
                galleryRef: Pe,
                hiddenQuery: ce,
                openImageInNewWindow: e => {
                    window.open(e, "_blank")
                },
                galleryHeight: je,
                filteredModelsConfig: Re,
                isPlusPlan: L
            }
        }
    },
    Wo = {
        class: "general-chat-wrapper"
    },
    Qo = {
        class: "chat-wrapper"
    },
    Ko = {
        class: "chat-content-wrapper"
    },
    Jo = {
        key: 0,
        class: "permission-wrapper"
    },
    Zo = {
        key: 0,
        class: "empty-placeholder"
    },
    Yo = {
        class: "conversation-wrapper",
        ref: "conversationContent"
    },
    Xo = {
        key: 0,
        class: "conversation-content"
    },
    ei = {
        key: 0,
        class: "assistant-message-divider"
    },
    ti = {
        key: 1,
        class: "conversation-item-desc assistant"
    },
    ai = {
        class: "bubble desc text"
    },
    li = {
        key: 2,
        class: "moa-title"
    },
    si = ["onClick"],
    ni = {
        class: "icon"
    },
    oi = {
        class: "model-response"
    },
    ii = ["onClick"],
    ri = {
        key: 0,
        class: "status"
    },
    ci = {
        key: 1,
        class: "icon finished"
    },
    di = {
        class: "icon"
    },
    ui = {
        class: "text"
    },
    pi = {
        key: 0,
        class: "content"
    },
    vi = {
        key: 0,
        class: "task-content"
    },
    _i = {
        key: 2,
        class: "prompt-box"
    },
    mi = {
        class: "prompt-box-title"
    },
    gi = {
        class: "prompt-box-content"
    },
    hi = {
        class: "desc"
    },
    fi = {
        class: "content"
    },
    yi = {
        key: 1,
        class: "creating-image-tasks"
    },
    ki = {
        class: "text"
    },
    bi = ["src", "alt"],
    wi = {
        class: "content"
    },
    xi = {
        class: "text-content"
    },
    Ci = {
        key: 1
    },
    Si = {
        key: 4,
        class: "array-content-wrapper"
    },
    Ii = {
        key: 0,
        class: "images-row"
    },
    Pi = ["src", "onClick"],
    ji = ["onClick"],
    Ri = {
        class: "desc"
    },
    Ai = {
        class: "content"
    },
    Mi = {
        class: "text-content"
    },
    Ti = {
        key: 5,
        class: "bubble try_moa"
    },
    Di = {
        class: "left"
    },
    Li = {
        class: "main-text"
    },
    Ei = {
        class: "sub-text"
    },
    $i = {
        class: "right"
    },
    Oi = {
        key: 1,
        class: "input-wrapper-wrapper"
    },
    Ui = {
        class: "input-wrapper-wrapper-inner"
    },
    Fi = {
        class: "controls"
    },
    Ni = {
        class: "models-wrapper"
    },
    Bi = {
        class: "models-selected"
    },
    Gi = {
        class: "model-selected"
    },
    qi = {
        class: "icon"
    },
    Vi = ["src"],
    Hi = {
        class: "text"
    },
    zi = {
        class: "models-list list-scroll"
    },
    Wi = {
        class: "options-wrapper"
    },
    Qi = {
        class: "remix-dropdown-container"
    },
    Ki = {
        class: "model-selected"
    },
    Ji = {
        class: "icon"
    },
    Zi = {
        class: "text"
    },
    Yi = {
        class: "reflection-toggle"
    },
    Xi = {
        class: "models-selected style-selector"
    },
    er = {
        class: "model-selected"
    },
    tr = {
        class: "text"
    },
    ar = {
        class: "style-cards"
    },
    lr = ["onClick"],
    sr = {
        class: "style-image"
    },
    nr = ["src", "alt"],
    or = {
        key: 1,
        class: "placeholder-image"
    },
    ir = {
        class: "style-label"
    },
    rr = {
        key: 0,
        class: "personalize-options"
    },
    cr = {
        class: "models-selected aspect-ratio-selector"
    },
    dr = {
        class: "model-selected"
    },
    ur = {
        class: "icon"
    },
    pr = {
        class: "text"
    },
    vr = {
        class: "models-list"
    },
    _r = ["onClick"],
    mr = {
        class: "row"
    },
    gr = {
        class: "left"
    },
    hr = {
        class: "icon"
    },
    fr = {
        class: "text"
    },
    yr = {
        class: "right"
    },
    kr = ["checked"],
    br = {
        key: 0,
        class: "divider"
    },
    wr = {
        key: 1,
        class: "hd-toggle"
    },
    xr = {
        key: 2,
        class: "llm-model-toggle"
    },
    Cr = {
        class: "input-wrapper",
        ref: "inputWrapper"
    };
const Sr = l(zo, [
        ["render", function(l, s, n, o, i, r) {
            const c = Le,
                m = Ae,
                f = Te,
                k = y("ExpandIcon"),
                w = re,
                x = y("CheckIcon"),
                S = y("ReflectionIcon"),
                I = ue,
                P = We,
                j = ze,
                R = y("RemixIcon"),
                M = (y("CopyIcon"), pe),
                T = y("MediaGallery"),
                D = y("PolygonIcon"),
                E = y("ImageSelectModel"),
                $ = y("NPopover"),
                O = y("DropdownIcon"),
                U = y("NCheckbox"),
                F = at,
                N = y("NSelect"),
                B = ve,
                G = _e;
            return a(), e("div", Wo, [o.selectedImage && o.selectedImage.url ? (a(), d(c, {
                key: 0,
                imageData: o.selectedImage,
                onClose: s[0] || (s[0] = e => o.selectedImage = null),
                onAddImageUrl: o.addImageUrl,
                onNavigate: o.handleImageNavigation,
                style: C(o.imageDetailStyles)
            }, null, 8, ["imageData", "onAddImageUrl", "onNavigate", "style"])) : u("", !0), v(m, {
                currentUser: o.currentUser,
                isPlusPlan: o.isPlusPlan,
                project: o.project,
                upgradePrompt: {
                    title: o.t("components.upgrade_prompt.title"),
                    description: o.t("components.upgrade_prompt.description_image")
                }
            }, null, 8, ["currentUser", "isPlusPlan", "project", "upgradePrompt"]), t("div", Qo, [t("div", Ko, [o.project && o.project.id && !o.no_input ? (a(), e("div", Jo, [v(f, {
                permission: o.permission,
                "onUpdate:permission": s[1] || (s[1] = e => o.permission = e),
                needPlusPlan: !0
            }, null, 8, ["permission"])])) : u("", !0), v(M, {
                copilotType: "COPILOT_MOA_IMAGE",
                ref: "copilotRef",
                defaultMessageContent: "",
                force_recaptcha: !0,
                extra_data: o.extra_data,
                onProjectEvent: o.onProjectEvent,
                onCopilotAskFinish: s[4] || (s[4] = e => {
                    o.copilotAskFinish(e)
                }),
                onCopilotAskStart: s[5] || (s[5] = e => {
                    o.copilotAskStart(e)
                })
            }, {
                default: _((({
                    messages: l,
                    doAction: n
                }) => [l && 0 != l.length ? u("", !0) : (a(), e("div", Zo, g(o.t("components.generate_image_content.greeting")), 1)), t("div", Yo, [l && l.length > 0 ? (a(), e("div", Xo, [(a(!0), e(p, null, h(l, ((n, i) => {
                    var r, c, _, m, f, y, C, A, M, T, D, E, $, O, U, F, N, B, G;
                    return a(), e("div", {
                        key: i,
                        class: b(["conversation-statement", "user" == n.role ? "user" : "assistant"])
                    }, [i > 0 && "assistant" == n.role && "assistant" == l[i - 1].role ? (a(), e("div", ei)) : u("", !0), (null == (r = n.session_state) ? void 0 : r.preferred_model) ? (a(), e("div", ti, [t("div", ai, g(o.t("components.generate_image_content.preferred_model_tip", {
                        model: o.getLabel(o.modelsConfig.find((e => {
                            var t;
                            return e.name === (null == (t = n.session_state) ? void 0 : t.preferred_model)
                        })))
                    })), 1)])) : u("", !0), (null == (c = n.session_state) ? void 0 : c.reflection) ? (a(), e("div", li, g(o.t("components.generate_image_content.auto_prompt")), 1)) : u("", !0), t("div", {
                        class: b(["conversation-item-desc", "user" == n.role ? "user" : "assistant"])
                    }, [(null == (_ = n.session_state) ? void 0 : _.reflection) ? (a(), e("div", {
                        key: 0,
                        class: b(["bubble model-response-wrapper", {
                            thinking: n.thinking,
                            expanded: !1 !== (null == (m = null == n ? void 0 : n.session_state) ? void 0 : m.reflection_expanded)
                        }])
                    }, [t("div", {
                        class: "expand-button",
                        onClick: () => {
                            n.session_state.reflection_expanded = !n.session_state.reflection_expanded
                        }
                    }, [t("div", ni, [v(k)])], 8, si), t("div", oi, [t("div", {
                        class: "title",
                        onClick: () => {
                            n.session_state.reflection_expanded = !n.session_state.reflection_expanded
                        }
                    }, [n.thinking || (null == (f = null == n ? void 0 : n.session_state) ? void 0 : f.reflection_is_finished) ? (a(), e("div", ri, [n.thinking && !(null == (y = null == n ? void 0 : n.session_state) ? void 0 : y.reflection_is_finished) ? (a(), d(w, {
                        key: 0
                    })) : (null == (C = null == n ? void 0 : n.session_state) ? void 0 : C.reflection_is_finished) ? (a(), e("div", ci, [v(x)])) : u("", !0)])) : u("", !0), t("div", di, [v(S)]), t("div", ui, g(o.t("components.generate_image_content.reflection")), 1)], 8, ii), (null == (A = n.session_state) ? void 0 : A.reflection_expanded) ? (a(), e("div", pi, [v(I, {
                        source: ((null == (M = n.session_state) ? void 0 : M.reflection) ? o.strip(null == (T = n.session_state) ? void 0 : T.reflection) : "") + (n.thinking && (null == (D = n.session_state) ? void 0 : D.reflection_is_started) && !(null == (E = n.session_state) ? void 0 : E.reflection_is_finished) ? o.LoadingAnim : "")
                    }, null, 8, ["source"]), (a(), e(p, null, h(4, (l => {
                        var i, r, c, v, _, m, h, f, y, k, b, w, x, C;
                        return a(), e(p, {
                            key: l
                        }, [(null == (r = null == (i = n.session_state) ? void 0 : i["task_" + (l - 1)]) ? void 0 : r.original_prompt) ? (a(), e("div", vi, [s[24] || (s[24] = t("div", {
                            class: "task-box-divider"
                        }, null, -1)), (null == (v = null == (c = n.session_state) ? void 0 : c["task_" + (l - 1)]) ? void 0 : v.original_prompt) ? (a(), d(I, {
                            key: 0,
                            source: `### ${l}. ${null==(m=null==(_=n.session_state)?void 0:_["task_"+(l-1)])?void 0:m.original_prompt}`
                        }, null, 8, ["source"])) : u("", !0), (null == (f = null == (h = n.session_state) ? void 0 : h["task_" + (l - 1)]) ? void 0 : f.reflection) ? (a(), d(I, {
                            key: 1,
                            source: null == (k = null == (y = n.session_state) ? void 0 : y["task_" + (l - 1)]) ? void 0 : k.reflection
                        }, null, 8, ["source"])) : u("", !0), (null == (w = null == (b = n.session_state) ? void 0 : b["task_" + (l - 1)]) ? void 0 : w.prompt) ? (a(), e("div", _i, [t("div", mi, g(o.t("components.generate_image_content.auto_prompt")), 1), t("div", gi, g(null == (C = null == (x = n.session_state) ? void 0 : x["task_" + (l - 1)]) ? void 0 : C.prompt), 1)])) : u("", !0)])) : u("", !0)], 64)
                    })), 64))])) : u("", !0)])], 2)) : u("", !0), "moa_image_result" == n.render_template ? (a(), e("div", {
                        key: 1,
                        class: b(["bubble", {
                            thinking: n.thinking
                        }])
                    }, [t("div", hi, [t("div", fi, [(null == ($ = n.content) ? void 0 : $.preparing) && (null == (O = n.content) ? void 0 : O.has_aspect_ratio) || !(null == (U = n.content) ? void 0 : U.preparing) ? (a(), d(P, {
                        key: 0,
                        data: n.content,
                        onAddImageUrl: s[2] || (s[2] = e => o.addImageUrl(e)),
                        onTaskComplete: e => o.handleTaskComplete(e, n),
                        onSelectImage: o.handleImageSelect
                    }, null, 8, ["data", "onTaskComplete", "onSelectImage"])) : (null == (F = n.content) ? void 0 : F.preparing) && !(null == (N = n.content) ? void 0 : N.has_aspect_ratio) ? (a(), e("div", yi, [v(w), t("div", ki, g(o.t("components.generate_image_content.creating_image_tasks")), 1), (a(!0), e(p, null, h(null == (B = n.content) ? void 0 : B.generated_images, (l => {
                        var s;
                        return a(), e("div", {
                            key: l.model,
                            class: "model-icon-wrapper"
                        }, [t("img", {
                            src: null == (s = o.modelsConfig.find((e => e.name === l.model))) ? void 0 : s.icon,
                            alt: l.model,
                            class: "model-icon"
                        }, null, 8, bi)])
                    })), 128))])) : u("", !0)])])], 2)) : "moa_video_result" == n.render_template ? (a(), e("div", {
                        key: 2,
                        class: b(["bubble", {
                            thinking: n.thinking
                        }])
                    }, [v(j, {
                        data: n.content
                    }, null, 8, ["data"])], 2)) : "string" == typeof n.content ? (a(), e("div", {
                        key: 3,
                        class: b(["bubble desc", {
                            thinking: n.thinking
                        }])
                    }, [t("div", wi, [t("div", xi, ["assistant" == n.role ? (a(), d(I, {
                        key: 0,
                        source: (n.content ? o.strip(n.content) : (null == (G = null == n ? void 0 : n.session_state) ? void 0 : G.answer) || "") + (n.thinking ? o.LoadingAnim : "")
                    }, null, 8, ["source"])) : (a(), e("pre", Ci, [t("code", null, g(n.content), 1)]))])])], 2)) : Array.isArray(n.content) ? (a(), e("div", Si, [n.content.some((e => "image_url" === e.type)) ? (a(), e("div", Ii, [(a(!0), e(p, null, h(n.content.filter((e => "image_url" === e.type)), ((l, s) => {
                        var n;
                        return a(), e("div", {
                            key: "img-" + s,
                            class: "image-item"
                        }, [t("img", {
                            src: null == (n = null == l ? void 0 : l.image_url) ? void 0 : n.url,
                            onClick: e => {
                                var t;
                                return o.openImageInNewWindow(null == (t = null == l ? void 0 : l.image_url) ? void 0 : t.url)
                            }
                        }, null, 8, Pi), t("div", {
                            class: "remix-button hover-show",
                            onClick: e => o.addImageUrl(l.image_url)
                        }, [v(R), L(g(o.t("components.generate_image_content.remix")), 1)], 8, ji)])
                    })), 128))])) : u("", !0), (a(!0), e(p, null, h(n.content.filter((e => "text" === e.type)), ((l, s) => (a(), e("div", {
                        key: "text-" + s,
                        class: "bubble"
                    }, [t("div", Ri, [t("div", Ai, [t("div", Mi, g(null == l ? void 0 : l.text), 1)])])])))), 128))])) : u("", !0), !o.readOnly && n === l[l.length - 1] && "assistant" == n.role && !n.is_prompt && n.session_state && void 0 !== n.session_state.reflection_enabled && !1 === n.session_state.reflection_enabled && o.isMessageComplete(n) ? (a(), e("div", Ti, [t("div", Di, [t("div", Li, g(o.t("components.generate_image_content.not_satisfied")), 1), t("div", Ei, g(o.t("components.generate_image_content.auto_prompt_description")), 1)]), t("div", $i, [t("div", {
                        class: "button",
                        onClick: s[3] || (s[3] = () => {
                            o.tryReflection()
                        })
                    }, g(o.t("components.generate_image_content.try_auto_prompt")), 1)])])) : u("", !0), u("", !0)], 2)], 2)
                })), 128))])) : u("", !0)], 512)])),
                _: 1
            }, 8, ["extra_data", "onProjectEvent"])])]), o.no_input ? u("", !0) : (a(), e("div", Oi, [t("div", Ui, [o.showGallery ? (a(), e("div", {
                key: 0,
                class: "media-gallery-container",
                ref: "galleryRef",
                style: C({
                    top: "-" + (o.galleryHeight + 10) + "px",
                    height: o.galleryHeight + "px"
                })
            }, [v(T, {
                remixButtonText: o.t("components.generate_image_content.remix"),
                showGallery: o.showGallery,
                "onUpdate:showGallery": s[6] || (s[6] = e => o.showGallery = e),
                showFilters: !0,
                initialItems: o.cachedItems,
                initialQuery: o.cachedQuery,
                showSource: !1,
                integratedMode: !0,
                promptInputRef: o.promptInputRef,
                hiddenQuery: o.hiddenQuery,
                onAddImageUrl: s[7] || (s[7] = e => o.addImageUrl(e)),
                onUpdateSearch: s[8] || (s[8] = e => o.updateCachedItems(e))
            }, null, 8, ["remixButtonText", "showGallery", "initialItems", "initialQuery", "promptInputRef", "hiddenQuery"])], 4)) : u("", !0), v(B, null, {
                default: _((() => [t("div", Fi, [t("div", Ni, [v($, {
                    trigger: "click",
                    show: o.modelsPopoverShow,
                    "onUpdate:show": [s[9] || (s[9] = e => o.modelsPopoverShow = e), s[10] || (s[10] = e => {
                        o.handlePopoverShow(e), o.modelsPopoverShow = e
                    })],
                    "show-arrow": !1,
                    placement: "top-start",
                    class: "models-popover",
                    style: {
                        padding: "0",
                        "border-radius": "12px",
                        "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                    }
                }, {
                    trigger: _((() => [t("div", Bi, [t("div", Gi, [t("div", qi, [t("img", {
                        src: o.modelsConfig.find((e => e.name == o.modelsSelected)).icon
                    }, null, 8, Vi)]), t("div", Hi, g(o.getLabel(o.modelsConfig.find((e => e.name == o.modelsSelected)))), 1)]), t("div", {
                        class: b(["trigger-icon", {
                            active: o.modelsPopoverShow
                        }])
                    }, [v(D)], 2)])])),
                    default: _((() => [t("div", zi, [v(E, {
                        promptImages: o.promptImages,
                        modelsSelected: o.modelsSelected,
                        toggleModel: o.toggleModel
                    }, null, 8, ["promptImages", "modelsSelected", "toggleModel"])])])),
                    _: 1
                }, 8, ["show"])]), t("div", Wi, [t("div", Qi, [t("div", {
                    class: "models-selected remix-selector",
                    onClick: s[11] || (s[11] = (...e) => o.toggleRemixDropdown && o.toggleRemixDropdown(...e))
                }, [t("div", Ki, [t("div", Ji, [v(R)]), t("div", Zi, g(o.t("components.generate_image_content.remix")), 1)]), t("div", {
                    class: b(["trigger-icon", {
                        active: o.showGallery
                    }])
                }, [v(O)], 2)])]), t("div", Yi, [v(U, {
                    checked: o.reflectionEnabled,
                    "onUpdate:checked": s[12] || (s[12] = e => o.reflectionEnabled = e)
                }, {
                    default: _((() => [L(g(o.t("components.generate_image_content.auto_prompt")), 1)])),
                    _: 1
                }, 8, ["checked"])]), v($, {
                    trigger: "click",
                    show: o.stylePopoverShow,
                    "onUpdate:show": [s[13] || (s[13] = e => o.stylePopoverShow = e), s[14] || (s[14] = e => {
                        o.handlePopoverShow(e), o.stylePopoverShow = e
                    })],
                    "show-arrow": !1,
                    placement: "top-start",
                    class: "models-popover style-popover",
                    style: {
                        padding: "0",
                        "border-radius": "12px",
                        "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                    }
                }, {
                    trigger: _((() => {
                        var e;
                        return [t("div", Xi, [t("div", er, [t("div", tr, g(null == (e = o.styleOptions.find((e => e.value === o.selectedStyle))) ? void 0 : e.label), 1)]), t("div", {
                            class: b(["trigger-icon", {
                                active: o.stylePopoverShow
                            }])
                        }, [v(O)], 2)])]
                    })),
                    default: _((() => [t("div", ar, [(a(!0), e(p, null, h(o.styleOptions, (l => (a(), e("div", {
                        class: b(["style-card", {
                            active: o.selectedStyle === l.value
                        }]),
                        onClick: e => o.selectStyle(l.value)
                    }, [t("div", sr, [l.preview ? (a(), e("img", {
                        key: 0,
                        src: l.preview,
                        alt: l.label
                    }, null, 8, nr)) : (a(), e("div", or, [(a(), d(A(l.icon || "div")))]))]), t("div", ir, g(l.label), 1)], 10, lr)))), 256))])])),
                    _: 1
                }, 8, ["show"]), o.showDebugFeatures ? (a(), e("div", rr, [v(F, {
                    modelValue: o.personalizeMode,
                    "onUpdate:modelValue": s[15] || (s[15] = e => o.personalizeMode = e),
                    options: o.personalizeOptions,
                    config: o.personalizeConfig,
                    size: "small"
                }, null, 8, ["modelValue", "options", "config"])])) : u("", !0), v($, {
                    trigger: "click",
                    show: o.aspectRatioPopoverShow,
                    "onUpdate:show": [s[16] || (s[16] = e => o.aspectRatioPopoverShow = e), s[17] || (s[17] = e => {
                        o.handlePopoverShow(e), o.aspectRatioPopoverShow = e
                    })],
                    "show-arrow": !1,
                    placement: "top-start",
                    class: "models-popover",
                    style: {
                        padding: "0",
                        "border-radius": "12px",
                        "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                    }
                }, {
                    trigger: _((() => {
                        var e, l;
                        return [t("div", cr, [t("div", dr, [t("div", ur, [(a(), d(A(null == (e = o.aspectRatioOptions.find((e => e.value === o.selectedAspectRatio))) ? void 0 : e.icon)))]), t("div", pr, g(null == (l = o.aspectRatioOptions.find((e => e.value === o.selectedAspectRatio))) ? void 0 : l.label), 1)]), t("div", {
                            class: b(["trigger-icon", {
                                active: o.aspectRatioPopoverShow
                            }])
                        }, [v(O)], 2)])]
                    })),
                    default: _((() => [t("div", vr, [(a(!0), e(p, null, h(o.aspectRatioOptions, (l => (a(), e(p, null, [t("div", {
                        class: "model",
                        onClick: e => o.selectAspectRatio(l.value)
                    }, [t("div", mr, [t("div", gr, [t("div", hr, [(a(), d(A(l.icon)))]), t("div", fr, g(l.label), 1)]), t("div", yr, [t("input", {
                        type: "radio",
                        name: "aspectRatio",
                        checked: o.selectedAspectRatio === l.value
                    }, null, 8, kr)])])], 8, _r), l.divider ? (a(), e("div", br)) : u("", !0)], 64)))), 256))])])),
                    _: 1
                }, 8, ["show"]), o.showDebugFeatures ? (a(), e("div", wr, [v(U, {
                    checked: o.hdEnabled,
                    "onUpdate:checked": s[18] || (s[18] = e => o.hdEnabled = e)
                }, {
                    default: _((() => s[25] || (s[25] = [L("HD")]))),
                    _: 1
                }, 8, ["checked"])])) : u("", !0), o.showDebugFeatures ? (a(), e("div", xr, [v(N, {
                    value: o.llmModel,
                    "onUpdate:value": s[19] || (s[19] = e => o.llmModel = e),
                    options: [{
                        label: "GPT-4o",
                        value: "gpt-4o"
                    }, {
                        label: "Claude 3.5",
                        value: "claude-3-5-sonnet-20241022"
                    }],
                    size: "small",
                    style: {
                        width: "120px"
                    }
                }, null, 8, ["value"])])) : u("", !0)])])])),
                _: 1
            }), t("div", Cr, [v(G, {
                ref: "promptInputRef",
                modelValue: o.prompt,
                "onUpdate:modelValue": s[20] || (s[20] = e => o.prompt = e),
                onSubmitPrompt: s[21] || (s[21] = (e, t, a) => {
                    o.submitAndClearPrompt(e, t, a), o.imageModelMap = {}
                }),
                supportImages: !0,
                images: o.promptImages,
                "onUpdate:images": s[22] || (s[22] = e => o.promptImages = e),
                files: o.promptFiles,
                "onUpdate:files": s[23] || (s[23] = e => o.promptFiles = e),
                useSuggestion: !1,
                placeholder: o.placeholderMessage,
                styleClass: "moa"
            }, null, 8, ["modelValue", "images", "files", "placeholder"])], 512)])]))])
        }],
        ["__scopeId", "data-v-4b702e6b"]
    ]),
    Ir = {
        name: "GenerateVideoContent",
        components: {
            RemixIcon: Pt,
            NPopover: Be,
            NCheckbox: xt,
            PermissionDropdown: Te,
            ExpandIcon: Q,
            Copilot: pe,
            PromptInput: _e,
            CopyIcon: me,
            MarkdownWithPlugins: ue,
            LoadingAnimation: re,
            PolygonIcon: be,
            MOAImageResult: We,
            MOAVideoResult: ze,
            NSelect: St,
            UploadIcon: Ie,
            NRadioGroup: wt,
            NRadio: bt,
            NSpace: yt,
            MOAImageSidePanel: qo,
            MediaGallery: it,
            GalleryIcon: It,
            UnifiedSelect: at,
            NTooltip: Ct,
            DropdownIcon: tt,
            CheckIcon: je,
            ReflectionIcon: Re,
            MOAImageDetail: Le,
            InfoIcon: _t,
            QuoteMarkIcon: ot,
            Cropper: st,
            VideoSelectModel: lt,
            UpgradePrompt: Ae
        },
        props: {
            project: {
                type: Object,
                required: !0
            },
            debug: {
                type: Boolean,
                default: !1
            },
            no_input: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["newSessionCreated"],
        setup(e, {
            emit: t
        }) {
            const {
                t: a
            } = x(), l = F(), c = s(!1), d = R("currentUser"), u = o((() => {
                var e;
                return "plus" === (null == (e = null == d ? void 0 : d.value) ? void 0 : e.plan)
            })), p = s(!d.value || "plus" !== d.value.plan && "pro" !== d.value.plan ? "fal-ai/bytedance/seedance/v1/lite" : "gemini/veo3");
            i(d, (e => {
                !e || p.value.startsWith("gemini") || "plus" !== e.plan && "pro" !== e.plan || (p.value = "gemini/veo3")
            }), {
                immediate: !0
            });
            const v = s("16:9"),
                _ = s("5"),
                m = s(!1),
                g = s(!0),
                h = ft(),
                f = xe(),
                {
                    prompt: y,
                    images: b
                } = U(h),
                {
                    modelsSelected: w,
                    selectedAspectRatio: C,
                    selectedDuration: S,
                    reflectionEnabled: I
                } = U(f),
                P = o((() => q.value.length > 0 ? a("components.generate_image_content.video-description-based-on-this-image") : a("components.generate_image_content.what-video-do-you-want-to-create"))),
                j = e => {
                    var t;
                    const a = ut.find((t => t.name === e));
                    return !!a && (!("auto" !== v.value && a.permitted_aspect_ratios && !a.permitted_aspect_ratios.includes(v.value)) && !((null == (t = q.value) ? void 0 : t.length) > 0 && !a.support_i2v))
                },
                A = e => {
                    if ("auto" === e) return !0;
                    if ("auto" === p.value) return ut.some((t => !t.permitted_aspect_ratios || t.permitted_aspect_ratios.includes(e)));
                    const t = ut.find((e => e.name === p.value));
                    return !!t && (!t.permitted_aspect_ratios || t.permitted_aspect_ratios.includes(e))
                },
                M = s([]),
                T = o((() => {
                    const e = ["kling/v1.6/pro", "fal-ai/bytedance/seedance/v1/lite", "minimax/hailuo-02/standard", "gemini/veo3"].filter((e => j(e)));
                    return k.log("defaultAutoModels", e), e
                })),
                D = o((() => ({
                    model_configs: ("auto" === p.value ? ((e, t, a) => {
                        let l = e.length > 0 ? [...e, ...t] : [...t];
                        if (l = [...new Set(l)], l.length < 4 && (l = ["fal-ai/bytedance/seedance/v1/lite", ...l]), k.log("getAutoModeModels starting with models", l, "; imageModelMap", a), Object.keys(a).length > 0) {
                            const e = Object.values(a).filter((e => e.model)).reduce(((e, t) => (e[t.model] = (e[t.model] || 0) + 1, e)), {});
                            if (k.log("modelUsageCount", e), Object.keys(e).length > 0) {
                                const a = Object.keys(e)[0],
                                    s = [a, a, ...t.filter((e => e !== a))];
                                k.log("newModels after combining", s), l = s
                            }
                        }
                        const s = l.filter(j).slice(0, 4);
                        return k.log("Final models returned:", s), s
                    })(M.value, T.value, { ...de.value
                    }) : [p.value]).map((e => ({
                        model: e,
                        aspect_ratio: v.value,
                        duration: _.value,
                        reflection_enabled: g.value,
                        ...ie.value && {
                            debug: !0
                        }
                    }))),
                    imageModelMap: de.value
                }))),
                L = s(null),
                {
                    project: $,
                    no_input: O
                } = n(e),
                N = s($.value.not_recommendable ? "private" : "public");
            i((() => N.value), (() => {
                fetch("/api/project/update", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        id: $.value.id,
                        not_recommendable: "private" === N.value
                    })
                })
            }));
            const B = s(""),
                G = s(null),
                q = s([]),
                V = s([]),
                H = s(null),
                z = (e, t, a) => {
                    ae.value = !1, L.value.submitPrompt(e, "", t);
                    const l = D.value.model_configs.map((e => e.model));
                    k.log("actual sending models", l), M.value = l
                },
                W = s(null);
            i((() => $.value ? $.value.id : ""), (e => {
                const t = new URL(window.location.href).searchParams;
                e && "undefined" !== e && t.set("id", e), history.replaceState(null, "", `${window.location.pathname}?${t.toString()}`), L.value.setMessages($.value.session_state.messages)
            }));
            const Q = e => {
                const t = new URL(window.location.href).searchParams;
                t.delete(e), history.replaceState(null, "", `${window.location.pathname}?${t.toString()}`)
            };
            r((async () => {
                if ($.value && $.value.session_state.messages && L.value.setMessages($.value.session_state.messages), window.addEventListener("keydown", me), "clipboard" === l.query.from) navigator.clipboard.readText().then((e => {
                    if (e && e.trim()) {
                        const t = e.trim();
                        (t.startsWith("http") || t.startsWith("data:image/")) && (K({
                            url: t
                        }, !1), ye(t), Q("from"))
                    }
                })).catch((e => {}));
                else if ("local2" === l.query.from) {
                    const e = localStorage.getItem("imageUrlToShare");
                    e && (K({
                        url: e
                    }, !1), ye(e), Q("from"))
                } else if ("i2v" === l.query.from) ae.value = !0;
                else if ("local" === l.query.from) try {
                    const e = await jt("currentUploadI2VImage");
                    K({
                        url: e
                    }, !1), ye(e), Q("from")
                } catch (e) {} {
                    const e = document.querySelector(".moa-image-detail");
                    if (e) {
                        const {
                            direction: t
                        } = mt(e, {
                            threshold: 50,
                            onSwipeEnd(e) {
                                H.value && ("left" === t.value ? _e("next") : "right" === t.value && _e("prev"))
                            }
                        })
                    }
                }
                "chat_now" === l.query.action && (B.value = y.value, q.value = b.value, p.value = w.value, v.value = C.value, _.value = S.value, g.value = I.value, z(B.value, q.value, V.value))
            })), E((() => {
                document.body.style.overflow = "", window.removeEventListener("keydown", me)
            }));
            const K = (e, t = !0) => {
                    var a;
                    q.value || (q.value = []), t ? te(e.url, e.model) : ((null == (a = ut.find((e => e.name === p.value))) ? void 0 : a.multiple_image) ? q.value.push(e.url) : q.value = [e.url], de.value[e.url] = {
                        url: e.url,
                        model: e.model
                    })
                },
                J = s(!1),
                Z = s(""),
                Y = s(null),
                X = s(null),
                ee = s(null);
            i([v, Z], (async ([e, t]) => {
                let a = e;
                "auto" === a && t && (a = await fe(t)), ee.value = "16:9" === a ? 16 / 9 : "9:16" === a ? 9 / 16 : "1:1" === a ? 1 : "4:3" === a ? 4 / 3 : "3:4" === a ? 3 / 4 : null
            }), {
                immediate: !0
            });
            const te = (e, t) => {
                Z.value = e, Y.value = t, J.value = !0
            };
            o((() => {
                if ("none" === personalizeMode.value) return "Personalized: None";
                const e = fashionProfiles.value.find((e => e.id === personalizeMode.value));
                return e ? `Personalized: ${e.name}` : "Style: None"
            }));
            const ae = s(!1),
                le = s([]),
                se = s(""),
                ne = s(["beautiful image", "lovely cats", "cute dogs"][Math.floor(3 * Math.random())]),
                oe = s(!1),
                ie = o((() => e.debug)),
                re = s(new Map),
                ce = s(new Map),
                de = s({});
            i((() => q.value), (e => {
                const t = {};
                Object.entries(de.value).forEach((([a, l]) => {
                    e.includes(a) && (t[a] = l)
                })), de.value = t
            }));
            const ue = s(!1),
                pe = s("auto"),
                ve = o((() => styleOptions(a)));
            i(H, (e => {
                document.body.style.overflow = e ? "hidden" : ""
            }));
            const _e = e => {
                    var t, a, l;
                    if (!H.value) return;
                    const s = [];
                    if (((null == (l = null == (a = null == (t = L.value) ? void 0 : t.getMessagesRef) ? void 0 : a.call(t)) ? void 0 : l.value) || []).forEach((e => {
                            try {
                                const t = "string" == typeof e.content ? JSON.parse(e.content) : e.content;
                                (null == t ? void 0 : t.generated_images) && t.generated_images.forEach((e => {
                                    (e.url || e.url_nowatermark) && s.push({ ...e,
                                        url: e.url || e.url_nowatermark
                                    })
                                }))
                            } catch (t) {}
                        })), s.length <= 1) return;
                    const n = s.findIndex((e => {
                        const t = H.value.url || H.value.url_nowatermark;
                        return e.url === t || e.url_nowatermark === t
                    }));
                    if (-1 === n) return;
                    let o;
                    o = "next" === e ? n + 1 >= s.length ? 0 : n + 1 : n - 1 < 0 ? s.length - 1 : n - 1, H.value = { ...s[o],
                        transition: "next" === e ? "slide-left" : "slide-right"
                    }
                },
                me = e => {
                    H.value && ("ArrowLeft" === e.key ? _e("prev") : "ArrowRight" === e.key ? _e("next") : "Escape" === e.key && (H.value = null))
                },
                ge = o((() => ({
                    touchAction: "pan-y pinch-zoom",
                    userSelect: "none",
                    width: "100%",
                    height: "100%",
                    position: "relative"
                }))),
                he = o((() => ut.filter((e => j(e.name)))));
            i([v, () => q.value], (() => {
                he.value.some((e => e.name === p.value)) || (p.value = he.value[0].name)
            })), i(p, (e => {
                if ("auto" !== v.value && !A(v.value)) {
                    const t = ut.find((t => t.name === e));
                    t && t.permitted_aspect_ratios && t.permitted_aspect_ratios.length > 0 ? t.permitted_aspect_ratios.includes("16:9") ? v.value = "16:9" : t.permitted_aspect_ratios.includes("9:16") ? v.value = "9:16" : v.value = t.permitted_aspect_ratios[0] : v.value = "9:16"
                }
            }));
            const fe = async e => new Promise((t => {
                    const a = new Image;
                    a.onload = () => {
                        const e = a.width / a.height;
                        e >= 1.4 ? t("16:9") : e >= 1.2 && e < 1.4 ? t("4:3") : e > .8 && e < 1.2 ? t("1:1") : e > .7 && e <= .8 ? t("3:4") : e <= .6 && t("9:16")
                    }, a.onerror = () => {
                        t(null)
                    }, a.src = e
                })),
                ye = async e => {
                    const t = await fe(e);
                    t && (v.value = t)
                },
                ke = s(null),
                be = s(null),
                we = o((() => window.innerWidth <= 768 ? .95 * (window.innerHeight - 200) : 500));
            return gt(ke, (e => {
                if (ae.value) {
                    e.target.closest(".remix-selector") || (ae.value = !1)
                }
            })), gt(be, (e => {
                J.value && (J.value = !1)
            })), {
                no_input: O,
                t: a,
                modelsPopoverShow: c,
                modelsConfig: ut,
                modelsSelected: p,
                copilotAskFinish: e => {
                    t("newSessionCreated", $.value)
                },
                copilotAskStart: e => {
                    B.value = "", q.value = [], V.value = []
                },
                onProjectEvent: e => {
                    k.log("onProjectEvent", e), "project_start" == e.type && ($.value.id = e.id), "project_field" == e.type && Ne($.value, e.field_name, (() => e.field_value))
                },
                LoadingAnim: '<span class="cursor">█</span>',
                extra_data: D,
                conversationContent: W,
                prompt: B,
                submitAndClearPrompt: z,
                project: $,
                copilotRef: L,
                strip: e => e.replace(/^[\n\t \r]+|[\n\t \r]+$/g, ""),
                toggleModel: e => {
                    p.value = e, c.value = !1, "auto" === e && (M.value = [])
                },
                aspectRatioOptions: vt,
                durationOptions: pt,
                selectedAspectRatio: v,
                selectAspectRatio: e => {
                    v.value = e, oe.value = !1
                },
                selectedDuration: _,
                selectDuration: e => {
                    if (!e.free && d.value && "free" === d.value.plan) return m.value = !1, void window.open("/pricing", "_blank");
                    _.value = e.value, m.value = !1
                },
                promptInputRef: G,
                promptImages: q,
                selectImage: () => {
                    G.value.selectImage()
                },
                addImageUrl: K,
                handleFlowItemClick: e => {
                    G.value && K(e.thumbnail)
                },
                handlePreview: e => {
                    imageViewer && imageViewer.openImageViewer(e, flowItems.value)
                },
                handleDownload: async e => {
                    try {
                        const t = await fetch(e.url),
                            a = await t.blob();
                        Oe.saveAs(a, `video-${Date.now()}.mp4`)
                    } catch (t) {}
                },
                showGallery: ae,
                toggleRemixDropdown: () => {
                    ae.value = !ae.value
                },
                aspectRatioPopoverShow: oe,
                durationPopoverShow: m,
                personalizeConfig: {
                    type: "personalize",
                    placeholder: "Select Style"
                },
                promptFiles: V,
                showDebugFeatures: ie,
                promptExpandedStates: re,
                getMessagePrompts: e => (null == e ? void 0 : e.session_state) ? Object.entries(e.session_state).filter((([e]) => e.startsWith("prompt_"))).map((([t, a]) => ({
                    key: t,
                    content: a,
                    stateKey: `${e.id||e.timestamp||Date.now()}_${t}`
                }))).filter((e => e.content)) : [],
                togglePrompt: e => {
                    if (e) {
                        const t = re.value.get(e.stateKey) || !1;
                        re.value.set(e.stateKey, !t)
                    }
                },
                isPromptExpanded: e => re.value.get(e.stateKey) || !1,
                reflectionEnabled: g,
                tryReflection: () => {
                    g.value = !0, setTimeout((() => {
                        L.value.forceSubmit()
                    }), 0)
                },
                messageTaskStatus: ce,
                handleTaskComplete: ({
                    index: e,
                    result: t
                }, a) => {
                    const l = a.id || a.timestamp || Date.now(),
                        s = new Map(ce.value);
                    s.has(l) || s.set(l, []);
                    const n = [...s.get(l) || []];
                    n[e] = {
                        status: t.status,
                        url: "SUCCESS" === t.status ? t.image_urls[0] : null,
                        timestamp: Date.now()
                    }, s.set(l, n), ce.value = s
                },
                isMessageComplete: e => {
                    var t;
                    const a = e.id || e.timestamp || Date.now(),
                        l = ce.value.get(a);
                    if (!l) return !1;
                    const s = "string" == typeof e.content ? JSON.parse(e.content) : e.content,
                        n = (null == (t = null == s ? void 0 : s.generated_images) ? void 0 : t.length) || 0,
                        o = l.filter((e => "SUCCESS" === (null == e ? void 0 : e.status))).length;
                    return n > 0 && o === n
                },
                handleImageSelect: e => {
                    H.value = e
                },
                selectedImage: H,
                permission: N,
                imageModelMap: de,
                lastUsedModels: M,
                stylePopoverShow: ue,
                selectedStyle: pe,
                selectStyle: e => {
                    pe.value = e, ue.value = !1
                },
                getLabel: e => "function" == typeof e.label ? e.label(a) : e.label,
                getDescription: e => e.description ? e.description(a) : "",
                getTip: e => e.tip ? e.tip(a) : "",
                styleOptions: ve,
                currentUser: d,
                isPlusPlan: u,
                handleImageNavigation: _e,
                imageDetailStyles: ge,
                cachedItems: le,
                cachedQuery: se,
                updateCachedItems: e => {
                    le.value = e.items, se.value = e.query
                },
                QuoteMarkIcon: ot,
                filteredModels: he,
                hiddenQuery: ne,
                galleryRef: ke,
                galleryHeight: we,
                isCropDialogVisible: J,
                currentCropImage: Z,
                currentImageModel: Y,
                cropResult: X,
                cropRatio: ee,
                showImageCropDialog: te,
                handleCropComplete: () => {
                    if (!X.value) return;
                    const {
                        coordinates: e,
                        canvas: t
                    } = X.value, {
                        width: a,
                        height: l
                    } = e, s = document.createElement("canvas"), n = s.getContext("2d");
                    s.width = a, s.height = l;
                    const o = new Image;
                    o.onload = () => {
                        var t;
                        n.drawImage(o, e.left, e.top, a, l, 0, 0, a, l);
                        const i = s.toDataURL("image/jpeg", 1);
                        (null == (t = ut.find((e => e.name === p.value))) ? void 0 : t.multiple_image) ? q.value.push(i): q.value = [i], de.value[i] = {
                            url: i,
                            model: Y.value
                        }, k.log("Image added from model (cropped):", {
                            url: i,
                            model: Y.value
                        }), J.value = !1, X.value = null
                    }, o.src = Z.value
                },
                cancelCrop: () => {
                    q.value.push(Z.value), de.value[Z.value] = {
                        url: Z.value,
                        model: Y.value
                    }, k.log("Image added from model (original):", {
                        url: Z.value,
                        model: Y.value
                    }), J.value = !1, X.value = null
                },
                updateCropResult: e => {
                    X.value = e
                },
                cropperDialogRef: be,
                isModelValid: j,
                isAspectRatioValid: A,
                placeholderMessage: P,
                RemixIcon: Pt
            }
        }
    },
    Pr = {
        class: "general-chat-wrapper"
    },
    jr = {
        class: "chat-wrapper"
    },
    Rr = {
        class: "chat-content-wrapper"
    },
    Ar = {
        key: 0,
        class: "permission-wrapper"
    },
    Mr = {
        key: 0,
        class: "empty-placeholder"
    },
    Tr = {
        class: "conversation-wrapper",
        ref: "conversationContent"
    },
    Dr = {
        key: 0,
        class: "conversation-content"
    },
    Lr = {
        key: 0,
        class: "assistant-message-divider"
    },
    Er = {
        key: 1,
        class: "conversation-item-desc assistant"
    },
    $r = {
        class: "bubble desc text"
    },
    Or = {
        key: 2,
        class: "moa-title"
    },
    Ur = ["onClick"],
    Fr = {
        class: "icon"
    },
    Nr = {
        class: "model-response"
    },
    Br = ["onClick"],
    Gr = {
        key: 0,
        class: "status"
    },
    qr = {
        key: 1,
        class: "icon finished"
    },
    Vr = {
        class: "icon"
    },
    Hr = {
        class: "text"
    },
    zr = {
        key: 0,
        class: "content"
    },
    Wr = {
        key: 0,
        class: "task-content"
    },
    Qr = {
        key: 2,
        class: "prompt-box"
    },
    Kr = {
        class: "prompt-box-title"
    },
    Jr = {
        class: "prompt-box-content"
    },
    Zr = {
        class: "desc"
    },
    Yr = {
        class: "content"
    },
    Xr = {
        key: 1,
        class: "creating-image-tasks"
    },
    ec = {
        class: "text"
    },
    tc = ["src", "alt"],
    ac = {
        class: "content"
    },
    lc = {
        class: "text-content"
    },
    sc = {
        key: 1
    },
    nc = {
        key: 4,
        class: "array-content-wrapper"
    },
    oc = {
        key: 0,
        class: "images-row"
    },
    ic = ["onClick"],
    rc = ["src"],
    cc = {
        class: "desc"
    },
    dc = {
        class: "content"
    },
    uc = {
        class: "text-content"
    },
    pc = {
        key: 5,
        class: "bubble try_moa"
    },
    vc = {
        class: "left"
    },
    _c = {
        class: "main-text"
    },
    mc = {
        class: "sub-text"
    },
    gc = {
        class: "right"
    },
    hc = {
        key: 1,
        class: "input-wrapper-wrapper"
    },
    fc = {
        class: "input-wrapper-wrapper-inner"
    },
    yc = {
        class: "image-cropper-container"
    },
    kc = {
        class: "crop-footer"
    },
    bc = {
        class: "controls"
    },
    wc = {
        class: "models-wrapper"
    },
    xc = {
        class: "models-selected"
    },
    Cc = {
        class: "model-selected"
    },
    Sc = {
        class: "icon"
    },
    Ic = ["src"],
    Pc = {
        class: "text"
    },
    jc = {
        class: "models-list list-scroll"
    },
    Rc = {
        class: "options-wrapper"
    },
    Ac = {
        class: "remix-dropdown-container"
    },
    Mc = {
        class: "model-selected"
    },
    Tc = {
        class: "icon"
    },
    Dc = {
        class: "text"
    },
    Lc = {
        class: "models-selected aspect-ratio-selector"
    },
    Ec = {
        class: "model-selected"
    },
    $c = {
        class: "icon"
    },
    Oc = {
        class: "text"
    },
    Uc = {
        class: "models-list"
    },
    Fc = ["onClick"],
    Nc = {
        class: "row"
    },
    Bc = {
        class: "left"
    },
    Gc = {
        class: "icon"
    },
    qc = {
        class: "text"
    },
    Vc = {
        class: "right"
    },
    Hc = ["checked", "disabled"],
    zc = {
        key: 0,
        class: "divider"
    },
    Wc = {
        class: "models-selected aspect-ratio-selector"
    },
    Qc = {
        class: "model-selected"
    },
    Kc = {
        class: "text"
    },
    Jc = {
        class: "models-list"
    },
    Zc = ["onClick"],
    Yc = {
        class: "row"
    },
    Xc = {
        class: "left"
    },
    ed = {
        class: "text"
    },
    td = {
        class: "right"
    },
    ad = ["checked"],
    ld = {
        class: "reflection-toggle"
    },
    sd = {
        class: "input-wrapper",
        ref: "inputWrapper"
    };
const nd = l(Ir, [
        ["render", function(l, s, n, o, i, r) {
            const c = Le,
                m = Ae,
                f = Te,
                k = y("ExpandIcon"),
                w = re,
                x = y("CheckIcon"),
                S = y("ReflectionIcon"),
                I = ue,
                P = We,
                j = ze,
                R = (y("CopyIcon"), pe),
                M = y("MediaGallery"),
                T = y("Cropper"),
                D = y("PolygonIcon"),
                E = y("VideoSelectModel"),
                $ = y("NPopover"),
                O = y("RemixIcon"),
                U = y("DropdownIcon"),
                F = y("NCheckbox"),
                N = ve,
                B = _e;
            return a(), e("div", Pr, [o.selectedImage && o.selectedImage.url ? (a(), d(c, {
                key: 0,
                imageData: o.selectedImage,
                onClose: s[0] || (s[0] = e => o.selectedImage = null),
                onAddImageUrl: o.addImageUrl,
                onNavigate: o.handleImageNavigation,
                style: C(o.imageDetailStyles)
            }, null, 8, ["imageData", "onAddImageUrl", "onNavigate", "style"])) : u("", !0), v(m, {
                currentUser: o.currentUser,
                isPlusPlan: o.isPlusPlan,
                project: o.project,
                upgradePrompt: {
                    title: o.t("components.upgrade_prompt.title"),
                    description: o.t("components.upgrade_prompt.description_video")
                }
            }, null, 8, ["currentUser", "isPlusPlan", "project", "upgradePrompt"]), t("div", jr, [t("div", Rr, [o.project && o.project.id && !o.no_input ? (a(), e("div", Ar, [v(f, {
                permission: o.permission,
                "onUpdate:permission": s[1] || (s[1] = e => o.permission = e),
                needPlusPlan: !0
            }, null, 8, ["permission"])])) : u("", !0), v(R, {
                copilotType: "COPILOT_MOA_VIDEO",
                ref: "copilotRef",
                defaultMessageContent: "",
                extra_data: o.extra_data,
                onProjectEvent: o.onProjectEvent,
                onCopilotAskFinish: s[4] || (s[4] = e => {
                    o.copilotAskFinish(e)
                }),
                onCopilotAskStart: s[5] || (s[5] = e => {
                    o.copilotAskStart(e)
                })
            }, {
                default: _((({
                    messages: l,
                    doAction: n
                }) => [l && 0 != l.length ? u("", !0) : (a(), e("div", Mr, g(o.t("components.generate_image_content.greeting")), 1)), t("div", Tr, [l && l.length > 0 ? (a(), e("div", Dr, [(a(!0), e(p, null, h(l, ((n, i) => {
                    var r, c, _, m, f, y, C, R, A, M, T, D, L, E, $, O, U, F, N;
                    return a(), e("div", {
                        key: i,
                        class: b(["conversation-statement", "user" == n.role ? "user" : "assistant"])
                    }, [i > 0 && "assistant" == n.role && "assistant" == l[i - 1].role ? (a(), e("div", Lr)) : u("", !0), (null == (r = n.session_state) ? void 0 : r.preferred_model) ? (a(), e("div", Er, [t("div", $r, g(o.t("components.generate_image_content.preferred_model_tip", {
                        model: o.getLabel(o.modelsConfig.find((e => {
                            var t;
                            return e.name === (null == (t = n.session_state) ? void 0 : t.preferred_model)
                        })))
                    })), 1)])) : u("", !0), (null == (c = n.session_state) ? void 0 : c.reflection) ? (a(), e("div", Or, g(o.t("components.generate_image_content.auto_prompt")), 1)) : u("", !0), t("div", {
                        class: b(["conversation-item-desc", "user" == n.role ? "user" : "assistant"])
                    }, [(null == (_ = n.session_state) ? void 0 : _.reflection) ? (a(), e("div", {
                        key: 0,
                        class: b(["bubble model-response-wrapper", {
                            thinking: n.thinking,
                            expanded: !1 !== (null == (m = null == n ? void 0 : n.session_state) ? void 0 : m.reflection_expanded)
                        }])
                    }, [t("div", {
                        class: "expand-button",
                        onClick: () => {
                            n.session_state.reflection_expanded = !n.session_state.reflection_expanded
                        }
                    }, [t("div", Fr, [v(k)])], 8, Ur), t("div", Nr, [t("div", {
                        class: "title",
                        onClick: () => {
                            n.session_state.reflection_expanded = !n.session_state.reflection_expanded
                        }
                    }, [n.thinking || (null == (f = null == n ? void 0 : n.session_state) ? void 0 : f.reflection_is_finished) ? (a(), e("div", Gr, [n.thinking && !(null == (y = null == n ? void 0 : n.session_state) ? void 0 : y.reflection_is_finished) ? (a(), d(w, {
                        key: 0
                    })) : (null == (C = null == n ? void 0 : n.session_state) ? void 0 : C.reflection_is_finished) ? (a(), e("div", qr, [v(x)])) : u("", !0)])) : u("", !0), t("div", Vr, [v(S)]), t("div", Hr, g(o.t("components.generate_image_content.reflection")), 1)], 8, Br), (null == (R = n.session_state) ? void 0 : R.reflection_expanded) ? (a(), e("div", zr, [v(I, {
                        source: ((null == (A = n.session_state) ? void 0 : A.reflection) ? o.strip(null == (M = n.session_state) ? void 0 : M.reflection) : "") + (n.thinking && (null == (T = n.session_state) ? void 0 : T.reflection_is_started) && !(null == (D = n.session_state) ? void 0 : D.reflection_is_finished) ? o.LoadingAnim : "")
                    }, null, 8, ["source"]), (a(), e(p, null, h(4, (l => {
                        var i, r, c, v, _, m, h, f, y, k, b, w, x, C;
                        return a(), e(p, {
                            key: l
                        }, [(null == (r = null == (i = n.session_state) ? void 0 : i["task_" + (l - 1)]) ? void 0 : r.original_prompt) ? (a(), e("div", Wr, [s[22] || (s[22] = t("div", {
                            class: "task-box-divider"
                        }, null, -1)), (null == (v = null == (c = n.session_state) ? void 0 : c["task_" + (l - 1)]) ? void 0 : v.original_prompt) ? (a(), d(I, {
                            key: 0,
                            source: `### ${l}. ${null==(m=null==(_=n.session_state)?void 0:_["task_"+(l-1)])?void 0:m.original_prompt}`
                        }, null, 8, ["source"])) : u("", !0), (null == (f = null == (h = n.session_state) ? void 0 : h["task_" + (l - 1)]) ? void 0 : f.reflection) ? (a(), d(I, {
                            key: 1,
                            source: null == (k = null == (y = n.session_state) ? void 0 : y["task_" + (l - 1)]) ? void 0 : k.reflection
                        }, null, 8, ["source"])) : u("", !0), (null == (w = null == (b = n.session_state) ? void 0 : b["task_" + (l - 1)]) ? void 0 : w.prompt) ? (a(), e("div", Qr, [t("div", Kr, g(o.t("components.generate_image_content.auto_prompt")), 1), t("div", Jr, g(null == (C = null == (x = n.session_state) ? void 0 : x["task_" + (l - 1)]) ? void 0 : C.prompt), 1)])) : u("", !0)])) : u("", !0)], 64)
                    })), 64))])) : u("", !0)])], 2)) : u("", !0), "moa_image_result" == n.render_template ? (a(), e("div", {
                        key: 1,
                        class: b(["bubble", {
                            thinking: n.thinking
                        }])
                    }, [t("div", Zr, [t("div", Yr, [(null == (L = n.content) ? void 0 : L.preparing) && (null == (E = n.content) ? void 0 : E.has_aspect_ratio) || !(null == ($ = n.content) ? void 0 : $.preparing) ? (a(), d(P, {
                        key: 0,
                        data: n.content,
                        onAddImageUrl: s[2] || (s[2] = e => o.addImageUrl(e)),
                        onTaskComplete: e => o.handleTaskComplete(e, n),
                        onSelectImage: o.handleImageSelect
                    }, null, 8, ["data", "onTaskComplete", "onSelectImage"])) : (null == (O = n.content) ? void 0 : O.preparing) && !(null == (U = n.content) ? void 0 : U.has_aspect_ratio) ? (a(), e("div", Xr, [v(w), t("div", ec, g(o.t("components.generate_image_content.creating_image_tasks")), 1), (a(!0), e(p, null, h(null == (F = n.content) ? void 0 : F.generated_videos, (l => {
                        var s;
                        return a(), e("div", {
                            key: l.model,
                            class: "model-icon-wrapper"
                        }, [t("img", {
                            src: null == (s = o.modelsConfig.find((e => e.name === l.model))) ? void 0 : s.icon,
                            alt: l.model,
                            class: "model-icon"
                        }, null, 8, tc)])
                    })), 128))])) : u("", !0)])])], 2)) : "moa_video_result" == n.render_template ? (a(), e("div", {
                        key: 2,
                        class: b(["bubble", {
                            thinking: n.thinking
                        }])
                    }, [v(j, {
                        data: n.content
                    }, null, 8, ["data"])], 2)) : "string" == typeof n.content ? (a(), e("div", {
                        key: 3,
                        class: b(["bubble desc", {
                            thinking: n.thinking
                        }])
                    }, [t("div", ac, [t("div", lc, ["assistant" == n.role ? (a(), d(I, {
                        key: 0,
                        source: (n.content ? o.strip(n.content) : (null == (N = null == n ? void 0 : n.session_state) ? void 0 : N.answer) || "") + (n.thinking ? o.LoadingAnim : "")
                    }, null, 8, ["source"])) : (a(), e("pre", sc, [t("code", null, g(n.content), 1)]))])])], 2)) : Array.isArray(n.content) ? (a(), e("div", nc, [n.content.some((e => "image_url" === e.type)) ? (a(), e("div", oc, [(a(!0), e(p, null, h(n.content.filter((e => "image_url" === e.type)), ((l, s) => {
                        var n;
                        return a(), e("div", {
                            key: "img-" + s,
                            class: "image-item",
                            onClick: e => o.addImageUrl(l.image_url)
                        }, [t("img", {
                            src: null == (n = null == l ? void 0 : l.image_url) ? void 0 : n.url
                        }, null, 8, rc)], 8, ic)
                    })), 128))])) : u("", !0), (a(!0), e(p, null, h(n.content.filter((e => "text" === e.type)), ((l, s) => (a(), e("div", {
                        key: "text-" + s,
                        class: "bubble"
                    }, [t("div", cc, [t("div", dc, [t("div", uc, g(null == l ? void 0 : l.text), 1)])])])))), 128))])) : u("", !0), n === l[l.length - 1] && "assistant" == n.role && !n.is_prompt && n.session_state && void 0 !== n.session_state.reflection_enabled && !1 === n.session_state.reflection_enabled && o.isMessageComplete(n) ? (a(), e("div", pc, [t("div", vc, [t("div", _c, g(o.t("components.generate_image_content.not_satisfied")), 1), t("div", mc, g(o.t("components.generate_image_content.auto_prompt_description")), 1)]), t("div", gc, [t("div", {
                        class: "button",
                        onClick: s[3] || (s[3] = () => {
                            o.tryReflection()
                        })
                    }, g(o.t("components.generate_image_content.try_auto_prompt")), 1)])])) : u("", !0), u("", !0)], 2)], 2)
                })), 128))])) : u("", !0)], 512)])),
                _: 1
            }, 8, ["extra_data", "onProjectEvent"])])]), o.no_input ? u("", !0) : (a(), e("div", hc, [t("div", fc, [o.showGallery ? (a(), e("div", {
                key: 0,
                class: "media-gallery-container",
                ref: "galleryRef",
                style: C({
                    top: "-" + (o.galleryHeight + 10) + "px",
                    height: o.galleryHeight + "px"
                })
            }, [v(M, {
                showGallery: o.showGallery,
                "onUpdate:showGallery": s[6] || (s[6] = e => o.showGallery = e),
                remixButtonText: o.t("components.generate_image_content.image-to-video"),
                "custom-icon": o.RemixIcon,
                showFilters: !0,
                initialItems: o.cachedItems,
                initialQuery: o.cachedQuery,
                showSource: !1,
                integratedMode: !0,
                promptInputRef: o.promptInputRef,
                hiddenQuery: o.hiddenQuery,
                crossOrigin: "anonymous",
                onAddImageDataURL: s[7] || (s[7] = e => o.addImageUrl(e)),
                onUpdateSearch: s[8] || (s[8] = e => o.updateCachedItems(e))
            }, null, 8, ["showGallery", "remixButtonText", "custom-icon", "initialItems", "initialQuery", "promptInputRef", "hiddenQuery"])], 4)) : u("", !0), o.isCropDialogVisible ? (a(), e("div", {
                key: 1,
                class: "cropper-dialog",
                ref: "cropperDialogRef",
                style: C({
                    top: "-" + (o.galleryHeight + 10) + "px",
                    height: o.galleryHeight + "px"
                })
            }, [t("div", yc, [o.isCropDialogVisible && o.currentCropImage ? (a(), d(T, {
                key: 0,
                class: "cropper",
                backgroundClass: "cropper-background",
                src: o.currentCropImage,
                "stencil-props": {
                    aspectRatio: o.cropRatio
                },
                canvas: {
                    backgroundColor: "#f0f0f0"
                },
                onChange: o.updateCropResult
            }, null, 8, ["src", "stencil-props", "onChange"])) : u("", !0)]), t("div", kc, [t("button", {
                class: "crop-button",
                onClick: s[9] || (s[9] = (...e) => o.handleCropComplete && o.handleCropComplete(...e))
            }, g(l.$t("components.generate_image_content.crop-image")), 1)])], 4)) : u("", !0), v(N, null, {
                default: _((() => [t("div", bc, [t("div", wc, [v($, {
                    show: o.modelsPopoverShow,
                    "onUpdate:show": [s[10] || (s[10] = e => o.modelsPopoverShow = e), s[11] || (s[11] = e => {
                        o.modelsPopoverShow = e
                    })],
                    trigger: "click",
                    "show-arrow": !1,
                    placement: "top-start",
                    class: "models-popover",
                    style: {
                        padding: "0",
                        "border-radius": "12px",
                        "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                    }
                }, {
                    trigger: _((() => [t("div", xc, [t("div", Cc, [t("div", Sc, [t("img", {
                        src: o.modelsConfig.find((e => e.name == o.modelsSelected)).icon
                    }, null, 8, Ic)]), t("div", Pc, g(o.getLabel(o.modelsConfig.find((e => e.name == o.modelsSelected)))), 1)]), t("div", {
                        class: b(["trigger-icon", {
                            active: o.modelsPopoverShow
                        }])
                    }, [v(D)], 2)])])),
                    default: _((() => [t("div", jc, [v(E, {
                        promptImages: o.promptImages,
                        modelsSelected: o.modelsSelected,
                        selectedAspectRatio: o.selectedAspectRatio,
                        toggleModel: o.toggleModel
                    }, null, 8, ["promptImages", "modelsSelected", "selectedAspectRatio", "toggleModel"])])])),
                    _: 1
                }, 8, ["show"])]), t("div", Rc, [t("div", Ac, [t("div", {
                    class: "models-selected remix-selector",
                    onClick: s[12] || (s[12] = (...e) => o.toggleRemixDropdown && o.toggleRemixDropdown(...e))
                }, [t("div", Mc, [t("div", Tc, [v(O)]), t("div", Dc, g(l.$t("components.generate_image_content.image-to-video")), 1)]), t("div", {
                    class: b(["trigger-icon", {
                        active: o.showGallery
                    }])
                }, [v(U)], 2)])]), v($, {
                    trigger: "click",
                    show: o.aspectRatioPopoverShow,
                    "onUpdate:show": [s[13] || (s[13] = e => o.aspectRatioPopoverShow = e), s[14] || (s[14] = e => {
                        o.aspectRatioPopoverShow = e
                    })],
                    "show-arrow": !1,
                    placement: "top-start",
                    class: "models-popover",
                    style: {
                        padding: "0",
                        "border-radius": "12px",
                        "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                    }
                }, {
                    trigger: _((() => {
                        var e, l;
                        return [t("div", Lc, [t("div", Ec, [t("div", $c, [(a(), d(A(null == (e = o.aspectRatioOptions.find((e => e.value === o.selectedAspectRatio))) ? void 0 : e.icon)))]), t("div", Oc, g(null == (l = o.aspectRatioOptions.find((e => e.value === o.selectedAspectRatio))) ? void 0 : l.label), 1)]), t("div", {
                            class: b(["trigger-icon", {
                                active: o.aspectRatioPopoverShow
                            }])
                        }, [v(U)], 2)])]
                    })),
                    default: _((() => [t("div", Uc, [(a(!0), e(p, null, h(o.aspectRatioOptions, (l => (a(), e(p, null, [t("div", {
                        class: b(["model", {
                            disabled: !o.isAspectRatioValid(l.value)
                        }]),
                        onClick: e => o.isAspectRatioValid(l.value) ? o.selectAspectRatio(l.value) : null
                    }, [t("div", Nc, [t("div", Bc, [t("div", Gc, [(a(), d(A(l.icon)))]), t("div", qc, g(l.label), 1)]), t("div", Vc, [t("input", {
                        type: "radio",
                        name: "aspectRatio",
                        checked: o.selectedAspectRatio === l.value,
                        disabled: !o.isAspectRatioValid(l.value)
                    }, null, 8, Hc)])])], 10, Fc), l.divider ? (a(), e("div", zc)) : u("", !0)], 64)))), 256))])])),
                    _: 1
                }, 8, ["show"]), v($, {
                    trigger: "click",
                    show: o.durationPopoverShow,
                    "onUpdate:show": [s[15] || (s[15] = e => o.durationPopoverShow = e), s[16] || (s[16] = e => {
                        o.durationPopoverShow = e
                    })],
                    "show-arrow": !1,
                    placement: "top-start",
                    class: "models-popover",
                    style: {
                        padding: "0",
                        "border-radius": "12px",
                        "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                    }
                }, {
                    trigger: _((() => [t("div", Wc, [t("div", Qc, [t("div", Kc, g(o.durationOptions.find((e => e.value === o.selectedDuration)).label), 1)]), t("div", {
                        class: b(["trigger-icon", {
                            active: o.durationPopoverShow
                        }])
                    }, [v(U)], 2)])])),
                    default: _((() => [t("div", Jc, [(a(!0), e(p, null, h(o.durationOptions, (l => (a(), e("div", {
                        class: "model",
                        onClick: e => o.selectDuration(l)
                    }, [t("div", Yc, [t("div", Xc, [t("div", ed, [t("span", {
                        class: b(["plus-icon", {
                            "plus-icon-paid": !l.free,
                            "plus-icon-hidden": o.currentUser && ("plus" === o.currentUser.plan || "pro" === o.currentUser.plan)
                        }])
                    }, " Plus ", 2), t("span", null, g(l.label), 1)])]), t("div", td, [t("input", {
                        type: "radio",
                        name: "duration",
                        checked: o.selectedDuration === l.value
                    }, null, 8, ad)])])], 8, Zc)))), 256))])])),
                    _: 1
                }, 8, ["show"]), t("div", ld, [v(F, {
                    checked: o.reflectionEnabled,
                    "onUpdate:checked": s[17] || (s[17] = e => o.reflectionEnabled = e)
                }, {
                    default: _((() => [L(g(o.t("components.generate_image_content.auto_prompt")), 1)])),
                    _: 1
                }, 8, ["checked"])])])])])),
                _: 1
            }), t("div", sd, [v(B, {
                ref: "promptInputRef",
                modelValue: o.prompt,
                "onUpdate:modelValue": s[18] || (s[18] = e => o.prompt = e),
                onSubmitPrompt: s[19] || (s[19] = (e, t, a) => {
                    o.submitAndClearPrompt(e, t, a), o.imageModelMap = {}
                }),
                supportImages: !0,
                images: o.promptImages,
                "onUpdate:images": s[20] || (s[20] = e => o.promptImages = e),
                files: o.promptFiles,
                "onUpdate:files": s[21] || (s[21] = e => o.promptFiles = e),
                useSuggestion: !1,
                placeholder: o.placeholderMessage,
                styleClass: "moa"
            }, null, 8, ["modelValue", "images", "files", "placeholder"])], 512)])]))])
        }],
        ["__scopeId", "data-v-57261172"]
    ]),
    od = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "100%",
        height: "100%",
        viewBox: "0 0 50 48",
        fill: "none"
    };
const id = {
        render: function(t, l) {
            return a(), e("svg", od, l[0] || (l[0] = [B('<path d="M24.3982 14.0342L22.8994 7.80532L25.897 7.80532L24.3982 14.0342Z" fill="#FF820F"></path><path d="M32.1812 17.729L37.3058 11.4605L39.5291 14.3283L32.1812 17.729Z" fill="#FD3434"></path><path d="M30.5628 12.7983L31.4315 10.3694L32.4051 10.9927L30.5628 12.7983Z" fill="#FD3434"></path><path d="M19.0471 12.9356L18.9548 10.3577L17.8381 10.6569L19.0471 12.9356Z" fill="#0F7FFF"></path><path d="M30.5628 33.0825L31.4315 35.5115L32.4051 34.8882L30.5628 33.0825Z" fill="#FD3434"></path><path d="M19.0471 32.9452L18.9548 35.5232L17.8381 35.2239L19.0471 32.9452Z" fill="#0F7FFF"></path><path d="M15.8232 16.7452L9.97148 12.979L12.0571 10.8934L15.8232 16.7452Z" fill="#79E27D"></path><path d="M24.3982 33.3496L22.8994 39.5785L25.897 39.5785L24.3982 33.3496Z" fill="#FF0F82"></path><path d="M32.1812 29.6548L37.3058 35.9233L39.5291 33.0555L32.1812 29.6548Z" fill="#34D8FD"></path><path d="M15.8232 30.6377L9.97148 34.4038L12.0571 36.4894L15.8232 30.6377Z" fill="#0F7FFF"></path>', 10)]))
        }
    },
    rd = {
        xmlns: "http://www.w3.org/2000/svg",
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none"
    };
const cd = {
        components: {
            StepFinishedIcon: {
                render: function(l, s) {
                    return a(), e("svg", rd, s[0] || (s[0] = [t("circle", {
                        cx: "10",
                        cy: "10",
                        r: "10",
                        fill: "#0F7FFF"
                    }, null, -1), t("path", {
                        d: "M6 10L9 13L14 8",
                        stroke: "white",
                        "stroke-width": "1.5",
                        "stroke-linecap": "round",
                        "stroke-linejoin": "round"
                    }, null, -1)]))
                }
            }
        },
        props: {
            steps: {
                type: Array,
                required: !0
            },
            currentStep: {
                type: Number,
                required: !0
            },
            isDoing: {
                type: Boolean,
                required: !0
            }
        },
        setup(e) {
            const {
                steps: t,
                currentStep: a,
                isDoing: l
            } = n(e), {
                t: s
            } = x();
            return {
                steps: t,
                currentStep: a,
                isDoing: l,
                t: s
            }
        }
    },
    dd = {
        class: "chat-progress-bar"
    },
    ud = {
        class: "icon-wrapper"
    },
    pd = {
        key: 0,
        class: "icon finished"
    },
    vd = {
        key: 1,
        class: "icon"
    },
    _d = {
        class: "chat-progress-bar-step-label"
    };
const md = {
        name: "DeepResearch",
        components: {
            PdfIcon: Mt,
            WordIcon: At,
            ExcelIcon: Tt,
            FileIcon: Rt,
            ToBottomIcon: Ue,
            CheckIcon: je,
            ReflectionIcon: Re,
            ExpandIcon: Q,
            Copilot: pe,
            PromptInput: _e,
            CopyIcon: me,
            MarkdownWithPlugins: ue,
            LoadingAnimation: re,
            PolygonIcon: be,
            UploadIcon: Ie,
            DeepDiveSearchVerticalResult: W,
            SearchStatusTopBar: J,
            SearchSourceSideBar: Ce,
            AsyncTaskContent: Ss,
            ChatProgressBar: l(cd, [
                ["render", function(l, s, n, o, i, r) {
                    const c = y("StepFinishedIcon");
                    return a(), e("div", dd, [s[0] || (s[0] = t("div", {
                        class: "chat-progress-bar-line"
                    }, null, -1)), (a(!0), e(p, null, h(o.steps, ((l, s) => (a(), e("div", {
                        key: s,
                        class: b(["chat-progress-bar-step", {
                            doing: s == o.currentStep - 1 && o.isDoing,
                            finished: s < o.currentStep - 1 || s == o.currentStep - 1 && !o.isDoing
                        }])
                    }, [t("div", ud, [s < o.currentStep - 1 || s == o.currentStep - 1 && !o.isDoing ? (a(), e("div", pd, [v(c)])) : (a(), e("div", vd, g(s + 1), 1))]), t("div", _d, g(l.label), 1)], 2)))), 128))])
                }],
                ["__scopeId", "data-v-c02f45a1"]
            ]),
            RefreshIcon: Qe,
            NPopover: Be,
            NCheckbox: xt
        },
        props: {
            project: {
                type: Object,
                required: !0
            },
            no_input: {
                type: Boolean,
                default: !1
            },
            initialQuery: {
                type: String,
                default: ""
            },
            initialText: {
                type: String,
                default: ""
            },
            readOnly: {
                type: Boolean,
                default: !1
            }
        },
        emits: ["newSessionCreated", "requestCreateNewSession", "requestRefreshProject", "update:isRunningBackground"],
        setup(e, {
            emit: t,
            expose: a
        }) {
            F();
            const l = de(),
                d = s(!1),
                u = R("currentUser"),
                {
                    t: p
                } = x(),
                v = [{
                    name: "gpt-4o,claude-3-5-sonnet,gemini-1.5-pro",
                    icon: De,
                    label: "Mixture-of-Agents",
                    full_label: "Mixture-of-Agents",
                    description: p("components.general_chat_content.auto_mixes_best_ai_models_for_your_task")
                }, {
                    name: "gpt-4o",
                    icon: he,
                    label: "GPT-4o",
                    full_label: "Open AI GPT-4o",
                    disabled: !0
                }, {
                    name: "gpt-4o-mini",
                    icon: he,
                    label: "GPT-4o Mini",
                    full_label: "Open AI GPT-4o Mini",
                    disabled: !0
                }, {
                    name: "o1",
                    icon: he,
                    label: "o1",
                    full_label: "Open AI o1",
                    disabled: !0
                }, {
                    name: "o3-mini-high",
                    icon: he,
                    label: "o3-mini-high",
                    full_label: "Open AI o3-mini-high",
                    disabled: !0
                }, {
                    name: "claude-3-5-sonnet",
                    icon: fe,
                    label: "Claude 3.5 Sonnet",
                    full_label: "Anthropic Claude 3.5 Sonnet",
                    disabled: !0
                }, {
                    name: "claude-3-5-haiku",
                    icon: fe,
                    label: "Claude 3.5 Haiku",
                    full_label: "Anthropic Claude 3.5 Haiku",
                    disabled: !0
                }, {
                    name: "gemini-2.0-flash",
                    icon: ye,
                    label: "Gemini 2.0 Flash",
                    full_label: "Google Gemini 2.0 Flash",
                    disabled: !0
                }, {
                    name: "deep-seek-v3",
                    icon: Se,
                    label: "DeepSeek V3",
                    full_label: "DeepSeek V3",
                    disabled: !0
                }, {
                    name: "deep-seek-r1",
                    icon: Se,
                    label: "DeepSeek R1",
                    full_label: "DeepSeek R1",
                    disabled: !0
                }],
                _ = s(v[0].name),
                m = [{
                    id: "moa_deep_research",
                    copilotType: "COPILOT_MOA_DEEP_RESEARCH",
                    inputPlaceholder: p("pages.chat_agent.moa_deep_research_input_placeholder")
                }, {
                    id: "phone_call",
                    copilotType: "COPILOT_PHONE_CALL",
                    inputPlaceholder: p("pages.chat_agent.phone_call_input_placeholder")
                }, {
                    id: "moa_deep_research_v2",
                    copilotType: "COPILOT_MOA_DEEP_RESEARCH_V2",
                    inputPlaceholder: p("pages.chat_agent.moa_deep_research_input_placeholder")
                }, {
                    id: "moa_deep_research_v3",
                    copilotType: "COPILOT_MOA_DEEP_RESEARCH_V3",
                    inputPlaceholder: p("pages.chat_agent.moa_deep_research_input_placeholder")
                }],
                g = s(!1),
                h = s(!1),
                f = o((() => ({
                    models: _.value.split(","),
                    run_with_another_model: h.value,
                    request_web_knowledge: d.value
                }))),
                y = s(null),
                b = s([]),
                w = s([]),
                C = s(null),
                {
                    project: S,
                    no_input: I,
                    readOnly: P
                } = n(e),
                j = s(S.value.not_recommendable ? "private" : "public");
            i((() => j.value), (() => {
                fetch("/api/project/update", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        id: S.value.id,
                        not_recommendable: "private" === j.value
                    })
                })
            }));
            const A = o((() => (k.log("showWebKnowledge", u.value), !(!u.value || !u.value.gk_realtime_dogfood)))),
                M = () => {
                    S.value && S.value.session_state && S.value.session_state.messages && C.value && C.value.setMessages(S.value.session_state.messages)
                };
            i((() => {
                var e;
                return null == (e = S.value) ? void 0 : e.id
            }), (() => {
                M()
            })), i((() => S.value), (() => {
                M()
            }));
            const T = o((() => S.value && S.value.type ? m.find((e => e.id == S.value.type)) : null)),
                D = s(""),
                L = s(null),
                $ = (e, t, a, s) => {
                    if (C.value.is_asking) return void l.warning("Please wait for the previous message to finish");
                    const n = C.value.getMessagesRef().value;
                    if (n.length > 0) {
                        const e = n[n.length - 1];
                        if (e.session_state && e.session_state.context_length_exceeded) return void l.warning("Context length exceeded, please open a new session")
                    }
                    h.value = !1, C.value.submitPrompt(e, "", t, a, s), D.value = "", b.value = [], w.value = []
                },
                O = s(null);
            i((() => O.value), (() => {
                O.value && C.value.setContentElement(O.value)
            }));
            const U = s(0),
                N = Fe((() => {
                    U.value != window.innerWidth && (U.value = window.innerWidth, k.isMobile() || window.innerWidth <= 1220 ? C.value.setScrollElement(document.documentElement) : C.value.setScrollElement(y.value))
                }), 100);
            r((() => {
                N(), window.addEventListener("resize", N)
            })), E((() => {
                window.removeEventListener("resize", N)
            }));
            const B = e => {
                k.log("onProjectEvent", e), "project_start" == e.type && (S.value.id = e.id), "project_field" == e.type && Ne(S.value, e.field_name, (() => e.field_value))
            };
            r((() => {
                M()
            }));
            const q = s(!1);
            let V = null,
                H = s(0);
            c((() => {
                V && V.abort()
            }));
            const z = o((() => {
                    var e, t, a, l, s, n, o;
                    const i = null == (l = null == (e = C.value) ? void 0 : e.getMessagesRef()) ? void 0 : l.value[(null == (a = null == (t = C.value) ? void 0 : t.getMessagesRef()) ? void 0 : a.value.length) - 1];
                    return !i || (!!(null == (s = null == i ? void 0 : i.session_state) ? void 0 : s.can_chat) || !((null == (n = null == i ? void 0 : i.session_state) ? void 0 : n.run_project) || (null == (o = null == i ? void 0 : i.session_state) ? void 0 : o.steps)))
                })),
                W = () => {
                    var e, a, l;
                    const s = C.value.getMessagesRef().value[C.value.getMessagesRef().value.length - 1];
                    ((null == (e = null == s ? void 0 : s.session_state) ? void 0 : e.run_project) || (null == (a = null == s ? void 0 : s.session_state) ? void 0 : a.steps)) && "FINISHED" != (null == (l = S.value) ? void 0 : l.status) && (async e => {
                        if (!q.value) try {
                            if (q.value = !0, !S.value || !S.value.id) return;
                            if (H.value++, H.value > 4) return;
                            V && V.abort(), V = new AbortController, await Wt("/api/project/run", {
                                project_id: S.value.id
                            }, (t => {
                                e && !e.session_state && (e.session_state = {}), "SESSION_STATE_FIELD" === t.type && ("" == t.field_name ? G(e.session_state, (() => t.field_value)) : Ne(e.session_state, t.field_name, (() => t.field_value))), "SESSION_STATE_FIELD_DELTA" === t.type && Ne(e.session_state, t.field_name, (e => (e || "") + t.delta)), "SESSION_STATE_FIELD_APPEND_ITEM" === t.type && Ne(e.session_state, t.field_name, (e => [...e || [], t.field_value])), "project_start" !== t.type && "project_field" !== t.type || B(t)
                            }), V.signal), t("requestRefreshProject")
                        } finally {
                            q.value = !1
                        }
                    })(s)
                };
            r((() => {
                W()
            }));
            r((async () => {
                if (e.initialQuery || e.initialText) {
                    let t = [];
                    if (e.initialText && (t = await (async e => {
                            if (!e) return;
                            const t = new Blob([e], {
                                    type: "text/plain"
                                }),
                                a = new File([t], "content.txt", {
                                    type: "text/plain"
                                }),
                                l = "txt";
                            try {
                                const e = await fetch("/api/get_upload_personal_image_url");
                                if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
                                const t = await e.json();
                                if (!t || 0 !== t.status) throw new Error(`API error! code: ${t.code}`);
                                const {
                                    upload_image_url: s,
                                    private_storage_url: n
                                } = t.data;
                                if (!(await fetch(s, {
                                        method: "PUT",
                                        headers: {
                                            "x-ms-blob-type": "BlockBlob"
                                        },
                                        body: a
                                    })).ok) throw new Error("Network response was not ok");
                                return [{
                                    name: a.name,
                                    type: a.type,
                                    ext: l,
                                    size: a.size,
                                    private_storage_url: n
                                }]
                            } catch (s) {
                                return null
                            }
                        })(e.initialText), !t)) return;
                    $(e.initialQuery, [], t)
                } else {
                    const e = new URLSearchParams(window.location.href.split("?")[1]),
                        t = e.get("prompt");
                    if (t) {
                        $(t), e.delete("prompt");
                        const a = window.location.pathname + (e.toString() ? "?" + e.toString() : "");
                        history.replaceState(null, "", a)
                    } else k.isMobile() || L.value.focus()
                }
            }));
            const Q = s(!1),
                K = s(!1),
                J = s({}),
                Z = s(!1);
            r((() => {
                Z.value = window.innerWidth <= 768, K.value = window.innerWidth <= 768, window.addEventListener("resize", (() => {
                    Z.value = window.innerWidth <= 768, K.value = window.innerWidth <= 768
                }))
            })), i(Q, (e => {
                e && K.value ? document.body.style.overflow = "hidden" : document.body.style.overflow = ""
            })), E((() => {
                document.body.style.overflow = ""
            }));
            const Y = s(0);
            i((() => {
                var e, t, a, l, s, n;
                return "" + (null == (t = null == (e = S.value) ? void 0 : e.session_state) ? void 0 : t.process_build_final_report_done) + (null == (l = null == (a = S.value) ? void 0 : a.session_state) ? void 0 : l.process_deep_research_done) + (null == (n = null == (s = S.value) ? void 0 : s.session_state) ? void 0 : n.process_create_plan_done)
            }), (() => {
                var e, t, a, l, s, n, o;
                (null == (e = S.value) ? void 0 : e.session_state) ? "boolean" == typeof(null == (a = null == (t = S.value) ? void 0 : t.session_state) ? void 0 : a.process_build_final_report_done) ? Y.value = 3: "boolean" == typeof(null == (s = null == (l = S.value) ? void 0 : l.session_state) ? void 0 : s.process_deep_research_done) ? Y.value = 2 : "boolean" == typeof(null == (o = null == (n = S.value) ? void 0 : n.session_state) ? void 0 : o.process_create_plan_done) ? Y.value = 1 : Y.value = 0: Y.value = 0
            }), {
                immediate: !0
            });
            const X = o((() => !(!S.value || !S.value.session_state) && (0 != Y.value && (1 == Y.value ? !1 === S.value.session_state.process_create_plan_done : 2 == Y.value ? !1 === S.value.session_state.process_deep_research_done : 3 == Y.value && !1 === S.value.session_state.process_build_final_report_done))));
            i((() => q.value), (e => {
                k.log("update:isRunningBackground", e), t("update:isRunningBackground", e)
            }), {
                immediate: !0
            });
            return {
                refresh: () => {
                    location.reload()
                },
                permission: j,
                t: p,
                projectType: T,
                currentStep: Y,
                isCurrentStepDoing: X,
                readOnly: P,
                projectCanChat: z,
                projectIsRunning: q,
                thinking_prompt: '<div class="thinking_prompt">Thinking...</div>',
                no_input: I,
                requestCreateNewSession: () => {
                    t("requestCreateNewSession")
                },
                haveNonTextContent: () => C.value.getMessagesRef().value.length > 0 && C.value.getMessagesRef().value.some((e => "string" != typeof e.content && e.content.some((e => "text" != e.type)))),
                messageApi: l,
                filesizeString: k.filesizeString,
                runWithAnotherModel: h,
                tryMoa: e => {
                    e && e.session_state && "interactive_deep_dive_search" == e.session_state.render_template && (d.value = !0), h.value = !0, _.value = v[0].name, setTimeout((() => {
                        C.value.forceSubmit()
                    }), 0)
                },
                chatWrapper: y,
                promptInputRef: L,
                selectImage: () => {
                    L.value.selectFile()
                },
                promptImages: b,
                promptFiles: w,
                modelsPopoverShow: g,
                modelsConfig: v,
                modelsSelected: _,
                copilotAskFinish: e => {
                    t("newSessionCreated", S.value), W()
                },
                onProjectEvent: B,
                LoadingAnim: '<span class="cursor">█</span>',
                extra_data: f,
                conversationContent: O,
                prompt: D,
                submitAndClearPrompt: $,
                project: S,
                copilotRef: C,
                strip: e => "string" != typeof e ? e : e.replace(/^[\n\t \r]+|[\n\t \r]+$/g, ""),
                requestWebKnowledge: d,
                showSidebar: Q,
                sidebarData: J,
                handleToggleSidebar: e => {
                    e && (J.value = e), Q.value = !Q.value
                },
                handleShowSidebar: e => {
                    e && (J.value = e), Q.value = !0
                },
                isMobile: Z,
                showFullScreenSidebar: K,
                handleFollowupDeepDiveSearch: e => {
                    (null == e ? void 0 : e.user_s_input) && (d.value = !0, $(e.user_s_input))
                },
                showWebKnowledge: A,
                showSearchSideBar: e => {
                    k.log("Showing search sidebar"), k.log("showSearchSideBar", e);
                    const t = {
                        searchResults: e.searchResults,
                        keywords: e.keywords,
                        jumpToUrl: e.jumpToUrl
                    };
                    k.log("showSearchSideBarData", t), J.value = t, Q.value = !0
                }
            }
        }
    },
    gd = {
        class: "general-chat-wrapper"
    },
    hd = {
        class: "chat-wrapper",
        ref: "chatWrapper"
    },
    fd = {
        key: 0,
        class: "progress-bar-wrapper"
    },
    yd = {
        key: 0,
        class: "refresh_button_wrapper"
    },
    kd = {
        class: "text"
    },
    bd = {
        class: "icon"
    },
    wd = {
        key: 1,
        class: "conversation-wrapper",
        ref: "conversationContent"
    },
    xd = {
        key: 0,
        class: "conversation-content"
    },
    Cd = {
        key: 0,
        class: "assistant-message-divider"
    },
    Sd = {
        key: 0,
        class: "research_plan_title"
    },
    Id = {
        key: 1,
        class: "moa-title"
    },
    Pd = ["onClick"],
    jd = {
        class: "icon"
    },
    Rd = ["onClick"],
    Ad = {
        key: 0,
        class: "status"
    },
    Md = {
        key: 1,
        class: "icon finished"
    },
    Td = {
        key: 1,
        class: "icon"
    },
    Dd = ["src"],
    Ld = {
        class: "text"
    },
    Ed = {
        key: 0,
        class: "content"
    },
    $d = {
        key: 1,
        class: "content no-expand"
    },
    Od = {
        key: 0
    },
    Ud = {
        key: 1
    },
    Fd = {
        key: 2,
        class: "cursor"
    },
    Nd = ["onClick"],
    Bd = {
        class: "icon"
    },
    Gd = {
        class: "model-response"
    },
    qd = ["onClick"],
    Vd = {
        key: 0,
        class: "status"
    },
    Hd = {
        key: 1,
        class: "icon finished"
    },
    zd = {
        class: "icon"
    },
    Wd = {
        key: 0,
        class: "content"
    },
    Qd = {
        key: 1,
        class: "content no-expand"
    },
    Kd = {
        key: 0,
        class: "cursor"
    },
    Jd = ["onClick"],
    Zd = {
        class: "icon"
    },
    Yd = {
        class: "model-response"
    },
    Xd = ["onClick"],
    eu = {
        key: 0,
        class: "status"
    },
    tu = {
        key: 1,
        class: "icon finished"
    },
    au = {
        class: "text"
    },
    lu = {
        key: 0,
        class: "content"
    },
    su = {
        key: 1,
        class: "content no-expand"
    },
    nu = {
        key: 0,
        class: "cursor"
    },
    ou = {
        class: "desc"
    },
    iu = {
        class: "content"
    },
    ru = {
        key: 1
    },
    cu = {
        class: "desc"
    },
    du = {
        class: "content"
    },
    uu = {
        key: 0
    },
    pu = {
        key: 1,
        class: "image_url_wrapper"
    },
    vu = ["src"],
    _u = {
        key: 2,
        class: "private-file-wrappers"
    },
    mu = {
        class: "file-wrapper"
    },
    gu = {
        class: "icon file-icon"
    },
    hu = {
        class: "file-info"
    },
    fu = {
        class: "file-name"
    },
    yu = {
        class: "file-size"
    },
    ku = {
        key: 6,
        class: "bubble context_length_exceeded"
    },
    bu = {
        class: "right"
    },
    wu = {
        key: 7,
        class: "bubble"
    },
    xu = {
        key: 8,
        class: "bubble"
    },
    Cu = {
        key: 2,
        class: "plan_bottom_wrapper"
    },
    Su = {
        key: 0,
        class: "permission-wrapper"
    },
    Iu = {
        key: 1,
        class: "start_buttons"
    },
    Pu = {
        key: 0,
        class: "input-wrapper-wrapper"
    },
    ju = {
        class: "input-wrapper-wrapper-inner"
    },
    Ru = {
        class: "controls"
    },
    Au = {
        class: "models-wrapper"
    },
    Mu = {
        class: "models-selected"
    },
    Tu = {
        class: "model-selected"
    },
    Du = {
        class: "icon"
    },
    Lu = ["src"],
    Eu = {
        class: "text"
    },
    $u = {
        class: "models-list"
    },
    Ou = ["onClick"],
    Uu = {
        class: "row"
    },
    Fu = {
        class: "left"
    },
    Nu = {
        class: "icon"
    },
    Bu = ["src"],
    Gu = {
        class: "text"
    },
    qu = {
        class: "right"
    },
    Vu = ["checked"],
    Hu = {
        key: 0,
        class: "description"
    },
    zu = {
        class: "text"
    },
    Wu = {
        key: 0,
        class: "divider"
    },
    Qu = {
        class: "input-wrapper",
        ref: "inputWrapper"
    },
    Ku = {
        key: 0,
        class: "mobile-sidebar-page"
    };
const Ju = l(md, [
    ["render", function(l, s, n, o, i, r) {
        var c, f, k, x, C, S;
        const I = y("ChatProgressBar"),
            P = y("RefreshIcon"),
            j = y("SearchStatusTopBar"),
            R = y("ExpandIcon"),
            A = re,
            M = y("CheckIcon"),
            T = W,
            D = ue,
            E = y("ReflectionIcon"),
            $ = (y("CopyIcon"), y("PdfIcon")),
            O = y("WordIcon"),
            U = y("ExcelIcon"),
            F = y("FileIcon"),
            N = y("AsyncTaskContent"),
            B = Te,
            G = pe,
            q = y("ToBottomIcon"),
            V = (y("UploadIcon"), y("PolygonIcon")),
            H = y("NPopover"),
            z = (y("NCheckbox"), ve),
            Q = _e,
            K = y("SearchSourceSideBar");
        return a(), e("div", gd, [t("div", hd, ["moa_deep_research" == (null == (c = o.project) ? void 0 : c.type) || "moa_deep_research_v2" == (null == (f = o.project) ? void 0 : f.type) || "moa_deep_research_v3" == (null == (k = o.project) ? void 0 : k.type) ? (a(), e("div", fd, [v(I, {
            steps: [{
                label: o.t("pages.chat_agent.create_plan")
            }, {
                label: o.t("pages.chat_agent.deep_research")
            }, {
                label: o.t("pages.chat_agent.final_report")
            }],
            currentStep: o.currentStep,
            isDoing: o.isCurrentStepDoing
        }, null, 8, ["steps", "currentStep", "isDoing"]), v(m, {
            name: "fade"
        }, {
            default: _((() => {
                var n;
                return [!o.projectIsRunning && !(null == (n = o.copilotRef) ? void 0 : n.is_asking) && o.project && ["STARTED", "PENDING", "PROGRESS"].indexOf(o.project.status) >= 0 ? (a(), e("div", yd, [t("div", {
                    class: "refresh_button",
                    onClick: s[0] || (s[0] = e => {
                        o.refresh()
                    })
                }, [t("div", kd, g(l.$t("pages.article_verification.refresh")), 1), t("div", bd, [v(P)])])])) : u("", !0)]
            })),
            _: 1
        })])) : u("", !0), v(G, {
            copilotType: o.projectType.copilotType,
            ref: "copilotRef",
            defaultMessageContent: "",
            extra_data: o.extra_data,
            onProjectEvent: o.onProjectEvent,
            onCopilotAskFinish: s[9] || (s[9] = e => {
                o.copilotAskFinish(e)
            })
        }, {
            default: _((({
                messages: n,
                doAction: i
            }) => [u("", !0), n && n.length > 0 ? (a(), e("div", wd, [n && n.length > 0 ? (a(), e("div", xd, [(a(!0), e(p, null, h(n, ((i, r) => {
                var c, _, m, f, y, k, x, C, S, I, P, G, q, V, H, z, W, Q, K, J, Z, Y, X, ee, te, ae, le, se, ne, oe, ie, re, ce, de, ue, pe, ve, _e, me, ge, he, fe, ye, ke, be, we, xe, Ce, Se;
                return a(), e(p, {
                    key: r
                }, [r > 0 && "assistant" == i.role && "assistant" == n[r - 1].role ? (a(), e("div", Cd)) : u("", !0), (null == (c = i.session_state) ? void 0 : c.search_status_top_bar_data) ? (a(), d(j, {
                    key: 1,
                    ref_for: !0,
                    ref: "searchStatusTopBarRef",
                    data: null == (_ = i.session_state) ? void 0 : _.search_status_top_bar_data,
                    onClick: s[1] || (s[1] = e => {
                        o.showSearchSideBar(e)
                    })
                }, null, 8, ["data"])) : u("", !0), t("div", {
                    class: b({
                        research_plan: null == (m = null == i ? void 0 : i.session_state) ? void 0 : m.solution_data
                    })
                }, [(null == (f = i.session_state) ? void 0 : f.solution_data) ? (a(), e("div", Sd, g(l.$t("pages.chat_agent.research_plan")), 1)) : u("", !0), (null == (k = null == (y = i.session_state) ? void 0 : y.models) ? void 0 : k.length) > 1 ? (a(), e("div", Id, " Mixture-of-Agents ")) : u("", !0), t("div", {
                    class: b(["conversation-statement", "user" == i.role ? "user" : "assistant"])
                }, [t("div", {
                    class: b(["conversation-item-desc", "user" == i.role ? "user" : "assistant"])
                }, [(null == (C = null == (x = null == i ? void 0 : i.session_state) ? void 0 : x.models) ? void 0 : C.length) > 1 ? (a(), e(p, {
                    key: 0
                }, [u("", !0), (a(!0), e(p, null, h(null == (S = null == i ? void 0 : i.session_state) ? void 0 : S.models, ((l, n) => {
                    var r;
                    return a(), e(p, null, [(a(!0), e(p, null, h(Array.from({
                        length: (null == (r = null == i ? void 0 : i.session_state) ? void 0 : r.layers) - 1
                    }, ((e, t) => t)), (r => {
                        var c, p, _, m, h, f, y, k, x, C, S, I, P, j, L, E, $, O;
                        return a(), e("div", {
                            class: b([{
                                thinking: i.thinking && !(null == (c = null == i ? void 0 : i.session_state) ? void 0 : c[`layer_${r}_${n}_is_finished`]),
                                expanded: null == (p = null == i ? void 0 : i.session_state) ? void 0 : p[`layer_${r}_${n}_expanded`]
                            }, "bubble model-response-wrapper"])
                        }, [t("div", {
                            class: "expand-button",
                            onClick: () => {
                                i.session_state[`layer_${r}_${n}_expanded`] = !i.session_state[`layer_${r}_${n}_expanded`]
                            }
                        }, [t("div", jd, [v(R)])], 8, Pd), (a(), e("div", {
                            class: "model-response",
                            key: n
                        }, [t("div", {
                            class: "title",
                            onClick: w((() => {
                                i.session_state[`layer_${r}_${n}_expanded`] = !i.session_state[`layer_${r}_${n}_expanded`]
                            }), ["stop"])
                        }, [i.thinking || (null == (_ = null == i ? void 0 : i.session_state) ? void 0 : _[`layer_${r}_${n}_is_finished`]) ? (a(), e("div", Ad, [i.thinking && !(null == (m = null == i ? void 0 : i.session_state) ? void 0 : m[`layer_${r}_${n}_is_finished`]) ? (a(), d(A, {
                            key: 0
                        })) : (null == (h = null == i ? void 0 : i.session_state) ? void 0 : h[`layer_${r}_${n}_is_finished`]) ? (a(), e("div", Md, [v(M)])) : u("", !0)])) : u("", !0), o.modelsConfig.find((e => e.name == l)) ? (a(), e("div", Td, [t("img", {
                            src: o.modelsConfig.find((e => e.name == l)).icon
                        }, null, 8, Dd)])) : u("", !0), t("div", Ld, g((null == (f = o.modelsConfig.find((e => e.name == l))) ? void 0 : f.label) || l), 1)], 8, Rd), i.session_state[`layer_${r}_${n}_expanded`] ? (a(), e("div", Ed, ["interactive_deep_dive_search" == (null == (y = i.session_state) ? void 0 : y.render_template) ? (a(), d(T, {
                            key: 0,
                            "show-thinking": !1,
                            "show-search-status-top-bar": !1,
                            "show-followup-question": !1,
                            "show-try-moa": !1,
                            "show-loading-icon": !1,
                            "show-result-buttons": !1,
                            "external-streaming-detail-answer": null == (x = null == (k = i.session_state) ? void 0 : k[`layer_${r}_${n}`]) ? void 0 : x.detailAnswer,
                            "external-api-response": null == (S = null == (C = i.session_state) ? void 0 : C[`layer_${r}_${n}`]) ? void 0 : S.content,
                            "external-search-status-top-bar-data": null == (I = i.session_state) ? void 0 : I.search_status_top_bar_data,
                            onShowSidebar: s[2] || (s[2] = e => {
                                o.showSearchSideBar(e)
                            })
                        }, null, 8, ["external-streaming-detail-answer", "external-api-response", "external-search-status-top-bar-data"])) : (a(), d(D, {
                            key: 1,
                            source: null == (P = null == i ? void 0 : i.session_state) ? void 0 : P[`layer_${r}_${n}`]
                        }, null, 8, ["source"]))])) : (a(), e("div", $d, [t("div", null, ["interactive_deep_dive_search" == (null == (j = null == i ? void 0 : i.session_state) ? void 0 : j.render_template) ? (a(), e("div", Od, g(null == (E = null == (L = i.session_state) ? void 0 : L[`layer_${r}_${n}`]) ? void 0 : E.detailAnswer), 1)) : (a(), e("div", Ud, g(null == ($ = null == i ? void 0 : i.session_state) ? void 0 : $[`layer_${r}_${n}`]), 1)), i.thinking && !(null == (O = null == i ? void 0 : i.session_state) ? void 0 : O[`layer_${r}_${n}_is_finished`]) ? (a(), e("span", Fd, "█")) : u("", !0)])]))]))], 2)
                    })), 256))], 64)
                })), 256))], 64)) : u("", !0), (null == (I = i.session_state) ? void 0 : I.reflection) ? (a(), e("div", {
                    key: 1,
                    class: b(["bubble model-response-wrapper", {
                        thinking: i.thinking,
                        expanded: null == (P = i.session_state) ? void 0 : P.reflection_expanded
                    }])
                }, [t("div", {
                    class: "expand-button",
                    onClick: () => {
                        i.session_state.reflection_expanded = !i.session_state.reflection_expanded
                    }
                }, [t("div", Bd, [v(R)])], 8, Nd), t("div", Gd, [t("div", {
                    class: "title",
                    onClick: () => {
                        i.session_state.reflection_expanded = !i.session_state.reflection_expanded
                    }
                }, [i.thinking || (null == (G = null == i ? void 0 : i.session_state) ? void 0 : G.reflection_is_finished) ? (a(), e("div", Vd, [i.thinking && !(null == (q = null == i ? void 0 : i.session_state) ? void 0 : q.reflection_is_finished) ? (a(), d(A, {
                    key: 0
                })) : (null == (V = null == i ? void 0 : i.session_state) ? void 0 : V.reflection_is_finished) ? (a(), e("div", Hd, [v(M)])) : u("", !0)])) : u("", !0), t("div", zd, [v(E)]), s[21] || (s[21] = t("div", {
                    class: "text"
                }, "Reflection", -1))], 8, qd), (null == (H = i.session_state) ? void 0 : H.reflection_expanded) ? (a(), e("div", Wd, [v(D, {
                    source: ((null == (z = i.session_state) ? void 0 : z.reflection) ? o.strip(null == (W = i.session_state) ? void 0 : W.reflection) : "") + (i.thinking && (null == (Q = i.session_state) ? void 0 : Q.reflection_is_started) && !(null == (K = i.session_state) ? void 0 : K.reflection_is_finished) ? o.LoadingAnim : "")
                }, null, 8, ["source"])])) : (a(), e("div", Qd, [t("div", null, [L(g(null == (J = null == i ? void 0 : i.session_state) ? void 0 : J.reflection) + " ", 1), i.thinking && !(null == (Z = null == i ? void 0 : i.session_state) ? void 0 : Z.reflection_is_finished) ? (a(), e("span", Kd, "█")) : u("", !0)])]))]), u("", !0)], 2)) : u("", !0), (null == (Y = i.session_state) ? void 0 : Y.aggregatorthink) ? (a(), e("div", {
                    key: 2,
                    class: b(["bubble model-response-wrapper", {
                        thinking: i.thinking,
                        expanded: null == (X = i.session_state) ? void 0 : X.aggregatorthink_expanded
                    }])
                }, [t("div", {
                    class: "expand-button",
                    onClick: () => {
                        i.session_state.aggregatorthink_expanded = !i.session_state.aggregatorthink_expanded
                    }
                }, [t("div", Zd, [v(R)])], 8, Jd), t("div", Yd, [t("div", {
                    class: "title",
                    onClick: () => {
                        i.session_state.aggregatorthink_expanded = !i.session_state.aggregatorthink_expanded
                    }
                }, [i.thinking || (null == (ee = null == i ? void 0 : i.session_state) ? void 0 : ee.aggregatorthink_is_finished) ? (a(), e("div", eu, [i.thinking && !(null == (te = null == i ? void 0 : i.session_state) ? void 0 : te.aggregatorthink_is_finished) ? (a(), d(A, {
                    key: 0
                })) : (null == (ae = null == i ? void 0 : i.session_state) ? void 0 : ae.aggregatorthink_is_finished) ? (a(), e("div", tu, [v(M)])) : u("", !0)])) : u("", !0), u("", !0), t("div", au, g(l.$t("pages.agents.thinking_process")), 1)], 8, Xd), (null == (le = i.session_state) ? void 0 : le.aggregatorthink_expanded) ? (a(), e("div", lu, [v(D, {
                    source: ((null == (se = i.session_state) ? void 0 : se.aggregatorthink) ? o.strip(null == (ne = i.session_state) ? void 0 : ne.aggregatorthink) : "") + (i.thinking && (null == (oe = i.session_state) ? void 0 : oe.aggregatorthink_is_started) && !(null == (ie = i.session_state) ? void 0 : ie.aggregatorthink_is_finished) ? o.LoadingAnim : "")
                }, null, 8, ["source"])])) : (a(), e("div", su, [t("div", null, [L(g(null == (re = null == i ? void 0 : i.session_state) ? void 0 : re.aggregatorthink) + " ", 1), i.thinking && !(null == (ce = null == i ? void 0 : i.session_state) ? void 0 : ce.aggregatorthink_is_finished) ? (a(), e("span", nu, "█")) : u("", !0)])]))])], 2)) : u("", !0), i.thinking || i.content && "string" == typeof i.content || (null == (de = null == i ? void 0 : i.session_state) ? void 0 : de.answer) || "interactive_deep_dive_search" == (null == (ue = null == i ? void 0 : i.session_state) ? void 0 : ue.render_template) ? (a(), e("div", {
                    key: 3,
                    class: b(["bubble", {
                        thinking: i.thinking
                    }])
                }, [t("div", ou, [t("div", iu, ["assistant" == i.role ? (a(), e(p, {
                    key: 0
                }, ["interactive_deep_dive_search" == (null == (pe = null == i ? void 0 : i.session_state) ? void 0 : pe.render_template) ? (a(), e(p, {
                    key: 0
                }, [v(T, {
                    "external-streaming-detail-answer": (null == (ve = i.session_state) ? void 0 : ve.detailAnswer) || (null == (_e = i.session_state) ? void 0 : _e.streaming_detail_answer),
                    "external-search-status-top-bar-data": null == (me = i.session_state) ? void 0 : me.search_status_top_bar_data,
                    "external-api-response": i.content,
                    "show-search-status-top-bar": !1,
                    "show-followup-question": !0,
                    "show-try-moa": !1,
                    "show-loading-icon": !1,
                    "show-thinking": !1,
                    onShowSidebar: s[3] || (s[3] = e => {
                        o.showSearchSideBar(e)
                    }),
                    onTriggerAction: s[4] || (s[4] = e => {
                        o.requestWebKnowledge = !0, o.submitAndClearPrompt(e.user_s_input)
                    })
                }, null, 8, ["external-streaming-detail-answer", "external-search-status-top-bar-data", "external-api-response"]), v(D, {
                    source: i.content ? "" : ((null == (ge = i.session_state) ? void 0 : ge.detailAnswer) || (null == (he = null == i ? void 0 : i.session_state) ? void 0 : he.streaming_detail_answer) ? "" : o.thinking_prompt) + o.LoadingAnim
                }, null, 8, ["source"])], 64)) : (a(), d(D, {
                    key: 1,
                    source: (i.content ? o.strip(i.content) : (null == (fe = null == i ? void 0 : i.session_state) ? void 0 : fe.answer) || (null == (ye = null == i ? void 0 : i.session_state) ? void 0 : ye.detailAnswer) || o.thinking_prompt) + (i.thinking ? o.LoadingAnim : "")
                }, null, 8, ["source"]))], 64)) : (a(), e("pre", ru, [t("code", null, g(i.content), 1)]))])]), u("", !0)], 2)) : u("", !0), i.content && Array.isArray(i.content) ? (a(!0), e(p, {
                    key: 4
                }, h(i.content, (l => {
                    var s, n, r, c, p, v, _, m;
                    return a(), e("div", {
                        class: b(["bubble", {
                            thinking: i.thinking,
                            image_url: "image_url" === l.type,
                            private_file: "private_file" === l.type
                        }])
                    }, [t("div", cu, [t("div", du, ["text" === l.type ? (a(), e("div", uu, g(null == l ? void 0 : l.text), 1)) : u("", !0), "image_url" === l.type ? (a(), e("div", pu, [t("img", {
                        src: null == (s = null == l ? void 0 : l.image_url) ? void 0 : s.url
                    }, null, 8, vu)])) : u("", !0), "private_file" === l.type ? (a(), e("div", _u, [t("div", mu, [t("div", gu, ["pdf" == (null == (n = null == l ? void 0 : l.private_file) ? void 0 : n.ext) ? (a(), d($, {
                        key: 0
                    })) : "doc" == (null == (r = null == l ? void 0 : l.private_file) ? void 0 : r.ext) || "docx" == (null == (c = null == l ? void 0 : l.private_file) ? void 0 : c.ext) ? (a(), d(O, {
                        key: 1
                    })) : "xls" == (null == (p = null == l ? void 0 : l.private_file) ? void 0 : p.ext) || "xlsx" == (null == (v = null == l ? void 0 : l.private_file) ? void 0 : v.ext) ? (a(), d(U, {
                        key: 2
                    })) : (a(), d(F, {
                        key: 3
                    }))]), t("div", hu, [t("div", fu, g(null == (_ = null == l ? void 0 : l.private_file) ? void 0 : _.name), 1), t("div", yu, g(o.filesizeString(null == (m = null == l ? void 0 : l.private_file) ? void 0 : m.size)), 1)])])])) : u("", !0)])])], 2)
                })), 256)) : u("", !0), u("", !0), i === n[n.length - 1] && "assistant" == i.role && (null == (ke = i.session_state) ? void 0 : ke.context_length_exceeded) ? (a(), e("div", ku, [s[23] || (s[23] = t("div", {
                    class: "left"
                }, " Context Length Exceeded, Please open a new session ", -1)), t("div", bu, [t("div", {
                    class: "button",
                    onClick: s[5] || (s[5] = () => {
                        o.requestCreateNewSession()
                    })
                }, " Create a new session ")])])) : u("", !0), (null == (be = i.session_state) ? void 0 : be.steps) ? (a(), e("div", wu, [v(N, {
                    message: i,
                    hide_intermediate_steps: !1,
                    isRunning: o.projectIsRunning
                }, null, 8, ["message", "isRunning"])])) : u("", !0), o.projectIsRunning && i === n[n.length - 1] ? (a(), e("div", xu, [v(A)])) : u("", !0)], 2)], 2), (null == (we = i.session_state) ? void 0 : we.solution_data) && i === n[n.length - 1] && !(null == (xe = o.copilotRef) ? void 0 : xe.is_asking) ? (a(), e("div", Cu, [o.project && o.project.id && !o.no_input ? (a(), e("div", Su, [v(B, {
                    permission: o.permission,
                    "onUpdate:permission": s[6] || (s[6] = e => o.permission = e),
                    needPlusPlan: !1,
                    disabled: (null == (Ce = o.copilotRef) ? void 0 : Ce.is_asking) || o.projectIsRunning,
                    options: [{
                        label: "Public Research",
                        desc: "Your research is okay to share with our community and inspire others."
                    }, {
                        label: "Private Research",
                        desc: "Keep your research private."
                    }]
                }, null, 8, ["permission", "disabled"])])) : u("", !0), (null == (Se = null == i ? void 0 : i.session_state) ? void 0 : Se.disable_start_buttons) ? u("", !0) : (a(), e("div", Iu, [t("div", {
                    class: "edit_button",
                    onClick: s[7] || (s[7] = w((() => o.submitAndClearPrompt(l.$t("pages.chat_agent.edit_plan"), null, null, {
                        button_label: "pages.chat_agent.edit_plan"
                    })), ["stop"]))
                }, g(l.$t("pages.chat_agent.edit_plan")), 1), t("div", {
                    class: "start_button",
                    onClick: s[8] || (s[8] = w((() => o.submitAndClearPrompt(l.$t("pages.chat_agent.start_research"), null, null, {
                        button_label: "pages.chat_agent.start_research"
                    })), ["stop"]))
                }, g(l.$t("pages.chat_agent.start_research")), 1)]))])) : u("", !0)], 2)], 64)
            })), 128))])) : u("", !0)], 512)) : u("", !0)])),
            _: 1
        }, 8, ["copilotType", "extra_data", "onProjectEvent"])], 512), !o.no_input && o.projectCanChat ? (a(), e("div", Pu, [(null == (x = o.copilotRef) ? void 0 : x.toBottomVisible) ? (a(), e("div", {
            key: 0,
            class: "to-bottom-icon",
            onClick: s[10] || (s[10] = (...e) => o.copilotRef.toBottom && o.copilotRef.toBottom(...e))
        }, [v(q)])) : u("", !0), t("div", ju, [v(z, null, {
            default: _((() => [t("div", Ru, [u("", !0), t("div", Au, [v(H, {
                trigger: "click",
                "show-arrow": !1,
                placement: "top-start",
                class: "models-popover",
                style: {
                    padding: "0",
                    "border-radius": "12px",
                    "--n-box-shadow": "0 4px 10px rgba(0, 0, 0, 0.15)"
                },
                "onUpdate:show": s[12] || (s[12] = e => {
                    o.modelsPopoverShow = e
                })
            }, {
                trigger: _((() => [t("div", Mu, [t("div", Tu, [t("div", Du, [t("img", {
                    src: o.modelsConfig.find((e => e.name == o.modelsSelected)).icon
                }, null, 8, Lu)]), t("div", Eu, g(o.modelsConfig.find((e => e.name == o.modelsSelected)).label), 1)]), t("div", {
                    class: b(["icon", {
                        active: o.modelsPopoverShow
                    }])
                }, [v(V)], 2)])])),
                default: _((() => [t("div", $u, [(a(!0), e(p, null, h(o.modelsConfig, (l => (a(), e(p, null, [t("div", {
                    class: b(["model", {
                        disabled: "o1-preview" == l.name && o.haveNonTextContent() || l.disabled
                    }]),
                    onClick: () => {
                        "o1-preview" == l.name && o.haveNonTextContent() ? o.messageApi.info("O1 model does not support attachments.") : o.modelsSelected = l.name
                    }
                }, [t("div", Uu, [t("div", Fu, [t("div", Nu, [t("img", {
                    src: l.icon
                }, null, 8, Bu)]), t("div", Gu, g(l.label), 1)]), t("div", qu, [t("input", {
                    type: "radio",
                    name: "model",
                    checked: o.modelsSelected == l.name
                }, null, 8, Vu)])]), l.description ? (a(), e("div", Hu, [t("div", zu, g(l.description), 1)])) : u("", !0)], 10, Ou), l.description ? (a(), e("div", Wu)) : u("", !0)], 64)))), 256))])])),
                _: 1
            })]), u("", !0)])])),
            _: 1
        }), t("div", Qu, [v(Q, {
            ref: "promptInputRef",
            modelValue: o.prompt,
            "onUpdate:modelValue": s[14] || (s[14] = e => o.prompt = e),
            onSubmitPrompt: s[15] || (s[15] = (e, t, a) => {
                o.submitAndClearPrompt(e, t, a)
            }),
            onStopAsking: s[16] || (s[16] = () => {
                var e;
                null == (e = o.copilotRef) || e.cancelAskCopilot()
            }),
            showStopAsking: null == (C = o.copilotRef) ? void 0 : C.is_asking,
            supportImages: "o1-preview" != o.modelsSelected,
            images: o.promptImages,
            "onUpdate:images": s[17] || (s[17] = e => o.promptImages = e),
            files: o.promptFiles,
            "onUpdate:files": s[18] || (s[18] = e => o.promptFiles = e),
            useSuggestion: !1,
            placeholder: (null == (S = o.projectType) ? void 0 : S.inputPlaceholder) || "Message",
            styleClass: "moa"
        }, null, 8, ["modelValue", "showStopAsking", "supportImages", "images", "files", "placeholder"])], 512)])])) : u("", !0), v(m, {
            name: "slide-desktop"
        }, {
            default: _((() => [o.showSidebar && !o.showFullScreenSidebar ? (a(), d(K, {
                key: 0,
                "search-results": o.sidebarData.searchResults,
                keywords: o.sidebarData.keywords,
                "jump-to-url": o.sidebarData.jumpToUrl,
                "is-mobile": o.isMobile,
                onCloseSearchSideBar: s[19] || (s[19] = e => o.showSidebar = !1),
                class: "search-source-sidebar desktop"
            }, null, 8, ["search-results", "keywords", "jump-to-url", "is-mobile"])) : u("", !0)])),
            _: 1
        }), v(m, {
            name: "slide"
        }, {
            default: _((() => [o.showSidebar && o.showFullScreenSidebar ? (a(), e("div", Ku, [v(K, {
                "search-results": o.sidebarData.searchResults,
                keywords: o.sidebarData.keywords,
                "is-mobile": o.isMobile,
                "jump-to-url": o.sidebarData.jumpToUrl,
                onCloseSearchSideBar: s[20] || (s[20] = e => o.showSidebar = !1)
            }, null, 8, ["search-results", "keywords", "is-mobile", "jump-to-url"])])) : u("", !0)])),
            _: 1
        })])
    }],
    ["__scopeId", "data-v-15fa5e36"]
]);
export {
    Na as C, Ju as D, Sr as G, rn as H, Co as M, Zt as S, Kt as a, nn as b, id as c, nd as d
};