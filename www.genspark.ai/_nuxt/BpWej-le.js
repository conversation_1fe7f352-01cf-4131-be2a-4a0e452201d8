import {
    d as o,
    b as r,
    o as t
} from "./Cf0SOiw0.js";
const n = {
    xmlns: "http://www.w3.org/2000/svg",
    width: "16",
    height: "16",
    viewBox: "0 0 16 16",
    fill: "none"
};
const e = {
    render: function(e, s) {
        return t(), o("svg", n, s[0] || (s[0] = [r("path", {
            d: "M9.5556 3L12.2812 5.643C13.4271 6.75412 14 7.30962 14 8C14 8.69037 13.4271 9.24587 12.2812 10.357L9.5556 13M2 3L4.72564 5.643C5.87147 6.75412 6.4444 7.30962 6.4444 8C6.4444 8.69037 5.87147 9.24587 4.72565 10.357L2 13",
            stroke: "currentColor",
            "stroke-width": "1.33333",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }, null, -1)]))
    }
};
export {
    e as S
};