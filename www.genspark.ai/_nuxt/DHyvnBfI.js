import {
    d as o,
    b as r,
    o as n
} from "./Cf0SOiw0.js";
const t = {
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const e = {
    render: function(e, s) {
        return n(), o("svg", t, s[0] || (s[0] = [r("path", {
            d: "M6.99984 10.5C10.2215 10.5 12.8332 7 12.8332 7C12.8332 7 10.2215 3.5 6.99984 3.5C3.77817 3.5 1.1665 7 1.1665 7C1.1665 7 3.77817 10.5 6.99984 10.5Z",
            stroke: "currentColor",
            "stroke-linejoin": "round"
        }, null, -1), r("path", {
            d: "M6.99984 8.45768C7.80525 8.45768 8.45817 7.80476 8.45817 6.99935C8.45817 6.19394 7.80525 5.54102 6.99984 5.54102C6.19443 5.54102 5.5415 6.19394 5.5415 6.99935C5.5415 7.80476 6.19443 8.45768 6.99984 8.45768Z",
            stroke: "currentColor",
            "stroke-linejoin": "round"
        }, null, -1)]))
    }
};
export {
    e as P
};