import {
    d as l,
    b as n,
    o
} from "./Cf0SOiw0.js";
const r = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 32 32"
};
const t = {
    render: function(t, s) {
        return o(), l("svg", r, s[0] || (s[0] = [n("path", {
            d: "M24 9.4L22.6 8L16 14.6L9.4 8L8 9.4l6.6 6.6L8 22.6L9.4 24l6.6-6.6l6.6 6.6l1.4-1.4l-6.6-6.6L24 9.4z",
            fill: "currentColor"
        }, null, -1)]))
    }
};
export {
    t as C
};