import {
    ak as n,
    al as e,
    am as s,
    J as t,
    X as i,
    an as o,
    a4 as a,
    Y as r,
    Z as l,
    ao as c,
    c as p,
    ap as d,
    aq as m,
    r as u,
    ag as f
} from "./Cf0SOiw0.js";
import {
    f as v,
    p as h
} from "./pB_XRIgB.js";
import {
    u as y
} from "./BGK9k_mT.js";
const g = n([n("@keyframes spin-rotate", "\n from {\n transform: rotate(0);\n }\n to {\n transform: rotate(360deg);\n }\n "), e("spin-container", "\n position: relative;\n ", [e("spin-body", "\n position: absolute;\n top: 50%;\n left: 50%;\n transform: translateX(-50%) translateY(-50%);\n ", [v()])]), e("spin-body", "\n display: inline-flex;\n align-items: center;\n justify-content: center;\n flex-direction: column;\n "), e("spin", "\n display: inline-flex;\n height: var(--n-size);\n width: var(--n-size);\n font-size: var(--n-size);\n color: var(--n-color);\n ", [s("rotate", "\n animation: spin-rotate 2s linear infinite;\n ")]), e("spin-description", "\n display: inline-block;\n font-size: var(--n-font-size);\n color: var(--n-text-color);\n transition: color .3s var(--n-bezier);\n margin-top: 8px;\n "), e("spin-content", "\n opacity: 1;\n transition: opacity .3s var(--n-bezier);\n pointer-events: all;\n ", [s("spinning", "\n user-select: none;\n -webkit-user-select: none;\n pointer-events: none;\n opacity: var(--n-opacity-spinning);\n ")])]),
    b = {
        small: 20,
        medium: 18,
        large: 16
    },
    z = t({
        name: "Spin",
        props: Object.assign(Object.assign({}, l.props), {
            contentClass: String,
            contentStyle: [Object, String],
            description: String,
            stroke: String,
            size: {
                type: [String, Number],
                default: "medium"
            },
            show: {
                type: Boolean,
                default: !0
            },
            strokeWidth: Number,
            rotate: {
                type: Boolean,
                default: !0
            },
            spinning: {
                type: Boolean,
                validator: () => !0,
                default: void 0
            },
            delay: Number
        }),
        slots: Object,
        setup(n) {
            const {
                mergedClsPrefixRef: e,
                inlineThemeDisabled: s
            } = r(n), t = l("Spin", "-spin", g, c, n, e), i = p((() => {
                const {
                    size: e
                } = n, {
                    common: {
                        cubicBezierEaseInOut: s
                    },
                    self: i
                } = t.value, {
                    opacitySpinning: o,
                    color: a,
                    textColor: r
                } = i;
                return {
                    "--n-bezier": s,
                    "--n-opacity-spinning": o,
                    "--n-size": "number" == typeof e ? h(e) : i[d("size", e)],
                    "--n-color": a,
                    "--n-text-color": r
                }
            })), o = s ? m("spin", p((() => {
                const {
                    size: e
                } = n;
                return "number" == typeof e ? String(e) : e[0]
            })), i, n) : void 0, a = y(n, ["spinning", "show"]), v = u(!1);
            return f((e => {
                let s;
                if (a.value) {
                    const {
                        delay: t
                    } = n;
                    if (t) return s = window.setTimeout((() => {
                        v.value = !0
                    }), t), void e((() => {
                        clearTimeout(s)
                    }))
                }
                v.value = a.value
            })), {
                mergedClsPrefix: e,
                active: v,
                mergedStrokeWidth: p((() => {
                    const {
                        strokeWidth: e
                    } = n;
                    if (void 0 !== e) return e;
                    const {
                        size: s
                    } = n;
                    return b["number" == typeof s ? "medium" : s]
                })),
                cssVars: s ? void 0 : i,
                themeClass: null == o ? void 0 : o.themeClass,
                onRender: null == o ? void 0 : o.onRender
            }
        },
        render() {
            var n, e;
            const {
                $slots: s,
                mergedClsPrefix: t,
                description: r
            } = this, l = s.icon && this.rotate, c = (r || s.description) && i("div", {
                class: `${t}-spin-description`
            }, r || (null === (n = s.description) || void 0 === n ? void 0 : n.call(s))), p = s.icon ? i("div", {
                class: [`${t}-spin-body`, this.themeClass]
            }, i("div", {
                class: [`${t}-spin`, l && `${t}-spin--rotate`],
                style: s.default ? "" : this.cssVars
            }, s.icon()), c) : i("div", {
                class: [`${t}-spin-body`, this.themeClass]
            }, i(o, {
                clsPrefix: t,
                style: s.default ? "" : this.cssVars,
                stroke: this.stroke,
                "stroke-width": this.mergedStrokeWidth,
                class: `${t}-spin`
            }), c);
            return null === (e = this.onRender) || void 0 === e || e.call(this), s.default ? i("div", {
                class: [`${t}-spin-container`, this.themeClass],
                style: this.cssVars
            }, i("div", {
                class: [`${t}-spin-content`, this.active && `${t}-spin-content--spinning`, this.contentClass],
                style: this.contentStyle
            }, s), i(a, {
                name: "fade-in-transition"
            }, {
                default: () => this.active ? p : null
            })) : p
        }
    });
export {
    z as N
};