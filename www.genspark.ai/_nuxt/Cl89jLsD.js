import {
    d as r,
    o,
    b as t
} from "./Cf0SOiw0.js";
const e = {
    width: "28",
    height: "28",
    viewBox: "0 0 28 28",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const n = {
    render: function(n, s) {
        return o(), r("svg", e, s[0] || (s[0] = [t("path", {
            d: "M22.8 6.33333H12.9L10.7 4H5.2C3.99 4 3 5.05 3 6.33333V9H25V8.66667C25 7.38333 24.01 6.33333 22.8 6.33333Z",
            fill: "currentcolor",
            stroke: "currentcolor",
            "stroke-width": "1.75"
        }, null, -1), t("path", {
            d: "M17.4 23H14H5.2C3.99 23 3 21.9714 3 20.7143V9.28571C3 8.02857 3.99 7 5.2 7H22.8C24.01 7 25 8.02857 25 9.28571V15V15.8571",
            stroke: "currentcolor",
            "stroke-width": "1.75",
            "stroke-linecap": "round"
        }, null, -1), t("path", {
            d: "M22.5 17L22.7579 17.697C23.0961 18.611 23.2652 19.068 23.5986 19.4014C23.932 19.7348 24.389 19.9039 25.303 20.2421L26 20.5L25.303 20.7579C24.389 21.0961 23.932 21.2652 23.5986 21.5986C23.2652 21.932 23.0961 22.389 22.7579 23.303L22.5 24L22.2421 23.303C21.9039 22.389 21.7348 21.932 21.4014 21.5986C21.068 21.2652 20.611 21.0961 19.697 20.7579L19 20.5L19.697 20.2421C20.611 19.9039 21.068 19.7348 21.4014 19.4014C21.7348 19.068 21.9039 18.611 22.2421 17.697L22.5 17Z",
            stroke: "currentcolor",
            "stroke-width": "1.75",
            "stroke-linejoin": "round"
        }, null, -1)]))
    }
};
export {
    n as A
};