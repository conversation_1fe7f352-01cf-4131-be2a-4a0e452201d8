import {
    _ as e,
    r as a,
    a as t,
    f as s,
    y as n,
    o,
    w as l,
    b as c,
    t as i,
    H as m,
    R as u,
    L as d
} from "./Cf0SOiw0.js";
import {
    N as p
} from "./BjWUbj3w.js";
const _ = {
        class: "rename-dialog"
    },
    r = {
        class: "title"
    },
    v = {
        class: "content"
    },
    f = {
        class: "buttons"
    },
    j = e({
        __name: "edit_project_name_modal",
        emits: ["finishEditProjectName"],
        setup(e, {
            expose: j,
            emit: b
        }) {
            const h = a(null),
                g = a(!1),
                w = b,
                E = a(""),
                k = () => {
                    h.value.name = E.value, w("finishEditProjectName", h.value), g.value = !1
                };
            return j({
                startEdit: e => {
                    h.value = e, g.value = !0, E.value = e.name
                }
            }), (e, a) => n(h) ? (o(), t(n(p), {
                key: 0,
                show: n(g),
                "onUpdate:show": a[2] || (a[2] = e => u(g) ? g.value = e : null)
            }, {
                default: l((() => [c("div", _, [c("div", r, i(e.$t("components.agents.edit_project_name_modal.rename")), 1), c("div", v, [m(c("input", {
                    type: "text",
                    "onUpdate:modelValue": a[0] || (a[0] = e => u(E) ? E.value = e : null),
                    class: "input-field"
                }, null, 512), [
                    [d, n(E)]
                ])]), c("div", f, [c("button", {
                    class: "cancel-button",
                    onClick: a[1] || (a[1] = e => g.value = !1)
                }, i(e.$t("components.agents.edit_project_name_modal.cancel")), 1), c("button", {
                    class: "save-button",
                    onClick: k
                }, i(e.$t("components.agents.edit_project_name_modal.save")), 1)])])])),
                _: 1
            }, 8, ["show"])) : s("", !0)
        }
    }, [
        ["__scopeId", "data-v-ce8c9fd8"]
    ]);
export {
    j as E
};