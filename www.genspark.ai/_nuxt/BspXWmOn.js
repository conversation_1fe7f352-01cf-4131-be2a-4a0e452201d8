import {
    d as o,
    o as r,
    b as t
} from "./Cf0SOiw0.js";
const n = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const e = {
    render: function(e, s) {
        return r(), o("svg", n, s[0] || (s[0] = [t("path", {
            d: "M19.001 5L5.00098 19M5.00098 5L19.001 19",
            stroke: "currentColor",
            "stroke-width": "1.5",
            "stroke-linecap": "round",
            "stroke-linejoin": "round"
        }, null, -1)]))
    }
};
export {
    e as C
};