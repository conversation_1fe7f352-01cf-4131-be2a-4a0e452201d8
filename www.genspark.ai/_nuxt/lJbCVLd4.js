import {
    _ as e,
    d as a,
    F as s,
    k as t,
    o as l,
    b as n,
    t as i,
    n as r
} from "./Cf0SOiw0.js";
const o = {
        class: "theme row5"
    },
    d = {
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
    },
    p = ["fill"];
const c = e({
    name: "PlanCreditDesc",
    props: {
        packType: {
            type: String,
            default: "standard",
            validator: e => ["standard", "2x"].includes(e)
        }
    },
    setup: e => ({
        features: "2x" === e.packType ? [{
            translationKey: "pages.pricing.credit-2x-plan-feature1",
            isSideNote: !1
        }] : [{
            translationKey: "pages.pricing.credit-plan-feature1",
            isSideNote: !1
        }]
    })
}, [
    ["render", function(e, c, u, f, v, w) {
        return l(), a("div", o, [(l(!0), a(s, null, t(f.features, ((s, t) => (l(), a("div", {
            key: t,
            class: "row5_line"
        }, [n("div", {
            class: r(s.isSideNote ? "row5_line_bullet_sub" : "row5_line_bullet")
        }, [(l(), a("svg", d, [n("path", {
            "fill-rule": "evenodd",
            "clip-rule": "evenodd",
            d: "M18.0633 5.67387C18.5196 5.98499 18.6374 6.60712 18.3262 7.06343L10.8262 18.0634C10.6585 18.3095 10.3898 18.4679 10.0934 18.4957C9.79688 18.5235 9.50345 18.4178 9.29289 18.2072L4.79289 13.7072C4.40237 13.3167 4.40237 12.6835 4.79289 12.293C5.18342 11.9025 5.81658 11.9025 6.20711 12.293L9.85368 15.9396L16.6738 5.93676C16.9849 5.48045 17.607 5.36275 18.0633 5.67387Z",
            fill: s.isSideNote ? "none" : "currentColor"
        }, null, 8, p)])), n("span", null, i(e.$t(s.translationKey)), 1)], 2)])))), 128))])
    }],
    ["__scopeId", "data-v-1bc0a756"]
]);
export {
    c as P
};