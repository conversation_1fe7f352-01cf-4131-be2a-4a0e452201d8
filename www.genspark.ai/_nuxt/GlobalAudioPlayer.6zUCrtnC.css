.global-audio-player[data-v-672e600c] {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background: #e8eaf6;
    border-radius: 34px;
    bottom: 60px;
    box-shadow: 0 4px 12px #0000001a;
    padding: 6px;
    position: fixed;
    right: 40px;
    z-index: 1000
}

.player-content[data-v-672e600c] {
    gap: 12px
}

.play-btn[data-v-672e600c],
.player-content[data-v-672e600c] {
    align-items: center;
    display: flex
}

.play-btn[data-v-672e600c] {
    background: #5c6bc0;
    border: none;
    border-radius: 50%;
    color: #fff;
    cursor: pointer;
    flex-shrink: 0;
    height: 30px;
    justify-content: center;
    transition: all .2s ease;
    width: 30px
}

.play-btn[data-v-672e600c]:hover:not(:disabled) {
    background: #3f51b5;
    transform: scale(1.05)
}

.play-btn[data-v-672e600c]:disabled {
    cursor: not-allowed;
    opacity: .6
}

.loading-spinner[data-v-672e600c] {
    animation: spin-672e600c 1s linear infinite;
    border: 2px solid hsla(0, 0%, 100%, .3);
    border-radius: 50%;
    border-top-color: #fff;
    height: 20px;
    width: 20px
}

@keyframes spin-672e600c {
    0% {
        transform: rotate(0)
    }
    to {
        transform: rotate(1turn)
    }
}

.podcast-time[data-v-672e600c] {
    color: #5c6bc0;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif;
    font-size: 16px;
    font-weight: 500;
    min-width: 80px;
    text-align: center
}

.close-btn[data-v-672e600c] {
    align-items: center;
    background: transparent;
    border: none;
    border-radius: 50%;
    color: #9e9e9e;
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    height: 32px;
    justify-content: center;
    transition: all .2s ease;
    width: 32px
}

.close-btn[data-v-672e600c]:hover {
    background: #9e9e9e1a;
    color: #757575
}

.slide-up-enter-active[data-v-672e600c],
.slide-up-leave-active[data-v-672e600c] {
    transition: transform .3s ease, opacity .3s ease
}

.slide-up-enter-from[data-v-672e600c],
.slide-up-leave-to[data-v-672e600c] {
    opacity: 0;
    transform: translate(-50%, 100%)
}

@media (max-width:768px) {
    .global-audio-player[data-v-672e600c] {
        bottom: 16px;
        padding: 6px 12px
    }
    .player-content[data-v-672e600c] {
        gap: 10px
    }
    .play-btn[data-v-672e600c] {
        height: 44px;
        width: 44px
    }
    .podcast-time[data-v-672e600c] {
        font-size: 14px;
        min-width: 70px
    }
    .close-btn[data-v-672e600c] {
        height: 28px;
        width: 28px
    }
}

@media (max-width:480px) {
    .global-audio-player[data-v-672e600c] {
        bottom: 12px;
        padding: 4px 8px
    }
    .player-content[data-v-672e600c] {
        gap: 8px
    }
    .play-btn[data-v-672e600c] {
        height: 40px;
        width: 40px
    }
    .podcast-time[data-v-672e600c] {
        font-size: 12px;
        min-width: 60px
    }
}

@media (prefers-color-scheme:dark) {
    .global-audio-player[data-v-672e600c] {
        background: #2c2c2e
    }
    .play-btn[data-v-672e600c] {
        background: #5c6bc0
    }
    .close-btn[data-v-672e600c],
    .podcast-time[data-v-672e600c] {
        color: #8e8e93
    }
    .close-btn[data-v-672e600c]:hover {
        background: #8f8f941a;
        color: #aeaeb2
    }
}