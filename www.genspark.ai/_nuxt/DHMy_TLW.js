import {
    d as t,
    b as e,
    o as i
} from "./Cf0SOiw0.js";
const h = {
    width: "11",
    height: "11",
    viewBox: "0 0 11 11",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const l = {
    render: function(l, r) {
        return i(), t("svg", h, r[0] || (r[0] = [e("rect", {
            x: "0.5",
            y: "0.5",
            width: "10",
            height: "10",
            rx: "5",
            stroke: "#BDBDBD"
        }, null, -1), e("rect", {
            x: "5",
            y: "2",
            width: "1",
            height: "5",
            fill: "#BDBDBD"
        }, null, -1), e("rect", {
            x: "5",
            y: "8",
            width: "1",
            height: "1",
            fill: "#BDBDBD"
        }, null, -1)]))
    }
};
export {
    l as I
};