import {
    r as e,
    a0 as t,
    y as n,
    aB as o,
    aC as a,
    c as r,
    a1 as i,
    ag as u,
    v as c,
    h as l,
    aD as s
} from "./Cf0SOiw0.js";

function d(e) {
    return !!o() && (a(e), !0)
}

function v(e) {
    return "function" == typeof e ? e() : n(e)
}
const p = "undefined" != typeof window && "undefined" != typeof document;
"undefined" != typeof WorkerGlobalScope && (globalThis, WorkerGlobalScope);
const f = Object.prototype.toString,
    m = () => {},
    h = g();

function g() {
    var e, t;
    return p && (null == (e = null == window ? void 0 : window.navigator) ? void 0 : e.userAgent) && (/iP(?:ad|hone|od)/.test(window.navigator.userAgent) || (null == (t = null == window ? void 0 : window.navigator) ? void 0 : t.maxTouchPoints) > 2 && /iPad|Macintosh/.test(null == window ? void 0 : window.navigator.userAgent))
}

function y(e) {
    var t;
    const n = v(e);
    return null != (t = null == n ? void 0 : n.$el) ? t : n
}
const w = p ? window : void 0,
    E = p ? window.navigator : void 0;

function b(...e) {
    let t, n, o, a;
    if ("string" == typeof e[0] || Array.isArray(e[0]) ? ([n, o, a] = e, t = w) : [t, n, o, a] = e, !t) return m;
    Array.isArray(n) || (n = [n]), Array.isArray(o) || (o = [o]);
    const r = [],
        i = () => {
            r.forEach((e => e())), r.length = 0
        },
        u = c((() => [y(t), v(a)]), (([e, t]) => {
            if (i(), !e) return;
            const a = (u = t, "[object Object]" === f.call(u) ? { ...t
            } : t);
            var u;
            r.push(...n.flatMap((t => o.map((n => ((e, t, n, o) => (e.addEventListener(t, n, o), () => e.removeEventListener(t, n, o)))(e, t, n, a))))))
        }), {
            immediate: !0,
            flush: "post"
        }),
        l = () => {
            u(), i()
        };
    return d(l), l
}
let x = !1;

function S(e, t, n = {}) {
    const {
        window: o = w,
        ignore: a = [],
        capture: r = !0,
        detectIframe: i = !1
    } = n;
    if (!o) return m;
    h && !x && (x = !0, Array.from(o.document.body.children).forEach((e => e.addEventListener("click", m))), o.document.documentElement.addEventListener("click", m));
    let u = !0;
    const c = e => a.some((t => {
            if ("string" == typeof t) return Array.from(o.document.querySelectorAll(t)).some((t => t === e.target || e.composedPath().includes(t))); {
                const n = y(t);
                return n && (e.target === n || e.composedPath().includes(n))
            }
        })),
        l = [b(o, "click", (n => {
            const o = y(e);
            o && o !== n.target && !n.composedPath().includes(o) && (0 === n.detail && (u = !c(n)), u ? t(n) : u = !0)
        }), {
            passive: !0,
            capture: r
        }), b(o, "pointerdown", (t => {
            const n = y(e);
            u = !c(t) && !(!n || t.composedPath().includes(n))
        }), {
            passive: !0
        }), i && b(o, "blur", (n => {
            setTimeout((() => {
                var a;
                const r = y(e);
                "IFRAME" !== (null == (a = o.document.activeElement) ? void 0 : a.tagName) || (null == r ? void 0 : r.contains(o.document.activeElement)) || t(n)
            }), 0)
        }))].filter(Boolean);
    return () => l.forEach((e => e()))
}

function A(t) {
    const n = function() {
        const t = e(!1),
            n = s();
        return n && l((() => {
            t.value = !0
        }), n), t
    }();
    return r((() => (n.value, Boolean(t()))))
}

function L(t, n = {}) {
    const {
        window: o = w
    } = n, a = A((() => o && "matchMedia" in o && "function" == typeof o.matchMedia));
    let r;
    const i = e(!1),
        c = e => {
            i.value = e.matches
        },
        l = () => {
            r && ("removeEventListener" in r ? r.removeEventListener("change", c) : r.removeListener(c))
        },
        s = u((() => {
            a.value && (l(), r = o.matchMedia(v(t)), "addEventListener" in r ? r.addEventListener("change", c) : r.addListener(c), i.value = r.matches)
        }));
    return d((() => {
        s(), l(), r = void 0
    })), i
}

function P(t, n = {}) {
    const {
        controls: o = !1,
        navigator: a = E
    } = n, r = A((() => a && "permissions" in a));
    let i;
    const u = "string" == typeof t ? {
            name: t
        } : t,
        c = e(),
        l = () => {
            i && (c.value = i.state)
        },
        s = function(e) {
            let t;

            function n() {
                return t || (t = e()), t
            }
            return n.reset = async () => {
                const e = t;
                t = void 0, e && await e
            }, n
        }((async () => {
            if (r.value) {
                if (!i) try {
                    i = await a.permissions.query(u), b(i, "change", l), l()
                } catch (e) {
                    c.value = "prompt"
                }
                return i
            }
        }));
    return s(), o ? {
        state: c,
        isSupported: r,
        query: s
    } : c
}

function k(n = {}) {
    const {
        navigator: o = E,
        read: a = !1,
        source: i,
        copiedDuring: u = 1500,
        legacy: c = !1
    } = n, l = A((() => o && "clipboard" in o)), s = P("clipboard-read"), f = P("clipboard-write"), m = r((() => l.value || c)), h = e(""), g = e(!1), y = function(n, o, a = {}) {
        const {
            immediate: r = !0
        } = a, i = e(!1);
        let u = null;

        function c() {
            u && (clearTimeout(u), u = null)
        }

        function l() {
            i.value = !1, c()
        }

        function s(...e) {
            c(), i.value = !0, u = setTimeout((() => {
                i.value = !1, u = null, n(...e)
            }), v(o))
        }
        return r && (i.value = !0, p && s()), d(l), {
            isPending: t(i),
            start: s,
            stop: l
        }
    }((() => g.value = !1), u);

    function w(e) {
        return "granted" === e || "prompt" === e
    }
    return m.value && a && b(["copy", "cut"], (function() {
        var e, t, n;
        l.value && w(s.value) ? o.clipboard.readText().then((e => {
            h.value = e
        })) : h.value = null != (n = null == (t = null == (e = null == document ? void 0 : document.getSelection) ? void 0 : e.call(document)) ? void 0 : t.toString()) ? n : ""
    })), {
        isSupported: m,
        text: h,
        copied: g,
        copy: async function(e = v(i)) {
            m.value && null != e && (l.value && w(f.value) ? await o.clipboard.writeText(e) : function(e) {
                const t = document.createElement("textarea");
                t.value = null != e ? e : "", t.style.position = "absolute", t.style.opacity = "0", document.body.appendChild(t), t.select(), document.execCommand("copy"), t.remove()
            }(e), h.value = e, g.value = !0, y.start())
        }
    }
}

function M(e) {
    const t = L("(prefers-color-scheme: light)", e),
        n = L("(prefers-color-scheme: dark)", e);
    return r((() => n.value ? "dark" : t.value ? "light" : "no-preference"))
}

function T(t, n = {}) {
    const {
        threshold: o = 50,
        onSwipe: a,
        onSwipeEnd: u,
        onSwipeStart: c,
        passive: l = !0,
        window: s = w
    } = n, d = i({
        x: 0,
        y: 0
    }), v = i({
        x: 0,
        y: 0
    }), p = r((() => d.x - v.x)), f = r((() => d.y - v.y)), {
        max: h,
        abs: g
    } = Math, y = r((() => h(g(p.value), g(f.value)) >= o)), E = e(!1), x = r((() => y.value ? g(p.value) > g(f.value) ? p.value > 0 ? "left" : "right" : f.value > 0 ? "up" : "down" : "none")), S = e => [e.touches[0].clientX, e.touches[0].clientY], A = (e, t) => {
        v.x = e, v.y = t
    };
    let L;
    const P = function(e) {
        if (!e) return !1;
        let t = !1;
        const n = {
            get passive() {
                return t = !0, !1
            }
        };
        return e.addEventListener("x", m, n), e.removeEventListener("x", m), t
    }(null == s ? void 0 : s.document);
    L = l ? P ? {
        passive: !0
    } : {
        capture: !1
    } : P ? {
        passive: !1,
        capture: !0
    } : {
        capture: !0
    };
    const k = [b(t, "touchstart", (e => {
        if (1 !== e.touches.length) return;
        L.capture && !L.passive && e.preventDefault();
        const [t, n] = S(e);
        ((e, t) => {
            d.x = e, d.y = t
        })(t, n), A(t, n), null == c || c(e)
    }), L), b(t, "touchmove", (e => {
        if (1 !== e.touches.length) return;
        const [t, n] = S(e);
        A(t, n), !E.value && y.value && (E.value = !0), E.value && (null == a || a(e))
    }), L), b(t, ["touchend", "touchcancel"], (e => {
        E.value && (null == u || u(e, x.value)), E.value = !1
    }), L)];
    return {
        isPassiveEventSupported: P,
        isSwiping: E,
        direction: x,
        coordsStart: d,
        coordsEnd: v,
        lengthX: p,
        lengthY: f,
        stop: () => k.forEach((e => e()))
    }
}
export {
    T as a, M as b, S as o, k as u
};