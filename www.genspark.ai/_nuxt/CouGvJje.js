import {
    d as t,
    b as o,
    o as e
} from "./Cf0SOiw0.js";
const i = {
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const r = {
    render: function(r, s) {
        return e(), t("svg", i, s[0] || (s[0] = [o("g", {
            id: "Frame"
        }, [o("path", {
            id: "Vector",
            d: "M6 12V5.78956C6 5.00724 6.85783 4.52789 7.5241 4.93791L12.5 8L17.6161 11.1483C18.2506 11.5388 18.2506 12.4612 17.6161 12.8517L12.5 16L7.5241 19.0621C6.85783 19.4721 6 18.9928 6 18.2104V12Z",
            fill: "white"
        })], -1)]))
    }
};
export {
    r as P
};