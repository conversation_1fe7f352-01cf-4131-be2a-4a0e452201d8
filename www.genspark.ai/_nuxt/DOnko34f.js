import {
    az as t,
    aA as n
} from "./Cf0SOiw0.js";
import {
    t as i
} from "./DyMB-pVc.js";
var r = function() {
        return t.Date.now()
    },
    o = Math.max,
    e = Math.min;

function u(t, u, a) {
    var f, c, v, m, d, s, l = 0,
        p = !1,
        T = !1,
        h = !0;
    if ("function" != typeof t) throw new TypeError("Expected a function");

    function x(n) {
        var i = f,
            r = c;
        return f = c = void 0, l = n, m = t.apply(r, i)
    }

    function g(t) {
        var n = t - s;
        return void 0 === s || n >= u || n < 0 || T && t - l >= v
    }

    function w() {
        var t = r();
        if (g(t)) return y(t);
        d = setTimeout(w, function(t) {
            var n = u - (t - s);
            return T ? e(n, v - (t - l)) : n
        }(t))
    }

    function y(t) {
        return d = void 0, h && f ? x(t) : (f = c = void 0, m)
    }

    function j() {
        var t = r(),
            n = g(t);
        if (f = arguments, c = this, s = t, n) {
            if (void 0 === d) return function(t) {
                return l = t, d = setTimeout(w, u), p ? x(t) : m
            }(s);
            if (T) return clearTimeout(d), d = setTimeout(w, u), x(s)
        }
        return void 0 === d && (d = setTimeout(w, u)), m
    }
    return u = i(u) || 0, n(a) && (p = !!a.leading, v = (T = "maxWait" in a) ? o(i(a.maxWait) || 0, u) : v, h = "trailing" in a ? !!a.trailing : h), j.cancel = function() {
        void 0 !== d && clearTimeout(d), l = 0, f = s = c = d = void 0
    }, j.flush = function() {
        return void 0 === d ? m : y(r())
    }, j
}
export {
    u as d, r as n
};