import {
    d as r,
    o,
    b as t
} from "./Cf0SOiw0.js";
const c = {
    width: "12",
    height: "2",
    viewBox: "0 0 12 2",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
};
const e = {
    render: function(e, l) {
        return o(), r("svg", c, l[0] || (l[0] = [t("g", {
            id: "Group 1171276049"
        }, [t("circle", {
            cx: "10.9999",
            cy: "1",
            r: "1",
            transform: "rotate(90 10.9999 1)",
            fill: "currentColor"
        }), t("circle", {
            cx: "6.00006",
            cy: "1",
            r: "1",
            transform: "rotate(90 6.00006 1)",
            fill: "currentColor"
        }), t("circle", {
            cx: "0.999146",
            cy: "1",
            r: "1",
            transform: "rotate(90 0.999146 1)",
            fill: "currentColor"
        })], -1)]))
    }
};
export {
    e as O
};