import {
    d as a,
    b as n,
    o
} from "./Cf0SOiw0.js";
const h = {
    xmlns: "http://www.w3.org/2000/svg",
    "xmlns:xlink": "http://www.w3.org/1999/xlink",
    viewBox: "0 0 20 20"
};
const r = {
    render: function(r, t) {
        return o(), a("svg", h, t[0] || (t[0] = [n("g", {
            fill: "none"
        }, [n("path", {
            d: "M8 6a.5.5 0 0 1 .09.992L8 7H6a3 3 0 0 0-.197 5.994L6 13h2a.5.5 0 0 1 .09.992L8 14H6a4 4 0 0 1-.22-7.994L6 6h2zm6 0a4 4 0 0 1 .22 7.994L14 14h-2a.5.5 0 0 1-.09-.992L12 13h2a3 3 0 0 0 .197-5.994L14 7h-2a.5.5 0 0 1-.09-.992L12 6h2zM6 9.5h8a.5.5 0 0 1 .09.992L14 10.5H6a.5.5 0 0 1-.09-.992L6 9.5h8h-8z",
            fill: "currentColor"
        })], -1)]))
    }
};
export {
    r as L
};