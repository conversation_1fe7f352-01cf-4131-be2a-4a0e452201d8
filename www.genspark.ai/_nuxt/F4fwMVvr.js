import {
    cZ as t
} from "./Cf0SOiw0.js";
import {
    r as n
} from "./qk0HrepY.js";
var r, e = {
    exports: {}
};

function i() {
    return r || (r = 1, e.exports = (i = i || function(r, e) {
        var i;
        if ("undefined" != typeof window && window.crypto && (i = window.crypto), "undefined" != typeof self && self.crypto && (i = self.crypto), "undefined" != typeof globalThis && globalThis.crypto && (i = globalThis.crypto), !i && "undefined" != typeof window && window.msCrypto && (i = window.msCrypto), !i && void 0 !== t && t.crypto && (i = t.crypto), !i) try {
            i = n
        } catch (w) {}
        var o = function() {
                if (i) {
                    if ("function" == typeof i.getRandomValues) try {
                        return i.getRandomValues(new Uint32Array(1))[0]
                    } catch (w) {}
                    if ("function" == typeof i.randomBytes) try {
                        return i.randomBytes(4).readInt32LE()
                    } catch (w) {}
                }
                throw new Error("Native crypto module could not be used to get secure random number.")
            },
            s = Object.create || function() {
                function t() {}
                return function(n) {
                    var r;
                    return t.prototype = n, r = new t, t.prototype = null, r
                }
            }(),
            a = {},
            c = a.lib = {},
            u = c.Base = function() {
                return {
                    extend: function(t) {
                        var n = s(this);
                        return t && n.mixIn(t), n.hasOwnProperty("init") && this.init !== n.init || (n.init = function() {
                            n.$super.init.apply(this, arguments)
                        }), n.init.prototype = n, n.$super = this, n
                    },
                    create: function() {
                        var t = this.extend();
                        return t.init.apply(t, arguments), t
                    },
                    init: function() {},
                    mixIn: function(t) {
                        for (var n in t) t.hasOwnProperty(n) && (this[n] = t[n]);
                        t.hasOwnProperty("toString") && (this.toString = t.toString)
                    },
                    clone: function() {
                        return this.init.prototype.extend(this)
                    }
                }
            }(),
            f = c.WordArray = u.extend({
                init: function(t, n) {
                    t = this.words = t || [], this.sigBytes = n != e ? n : 4 * t.length
                },
                toString: function(t) {
                    return (t || p).stringify(this)
                },
                concat: function(t) {
                    var n = this.words,
                        r = t.words,
                        e = this.sigBytes,
                        i = t.sigBytes;
                    if (this.clamp(), e % 4)
                        for (var o = 0; o < i; o++) {
                            var s = r[o >>> 2] >>> 24 - o % 4 * 8 & 255;
                            n[e + o >>> 2] |= s << 24 - (e + o) % 4 * 8
                        } else
                            for (var a = 0; a < i; a += 4) n[e + a >>> 2] = r[a >>> 2];
                    return this.sigBytes += i, this
                },
                clamp: function() {
                    var t = this.words,
                        n = this.sigBytes;
                    t[n >>> 2] &= 4294967295 << 32 - n % 4 * 8, t.length = r.ceil(n / 4)
                },
                clone: function() {
                    var t = u.clone.call(this);
                    return t.words = this.words.slice(0), t
                },
                random: function(t) {
                    for (var n = [], r = 0; r < t; r += 4) n.push(o());
                    return new f.init(n, t)
                }
            }),
            h = a.enc = {},
            p = h.Hex = {
                stringify: function(t) {
                    for (var n = t.words, r = t.sigBytes, e = [], i = 0; i < r; i++) {
                        var o = n[i >>> 2] >>> 24 - i % 4 * 8 & 255;
                        e.push((o >>> 4).toString(16)), e.push((15 & o).toString(16))
                    }
                    return e.join("")
                },
                parse: function(t) {
                    for (var n = t.length, r = [], e = 0; e < n; e += 2) r[e >>> 3] |= parseInt(t.substr(e, 2), 16) << 24 - e % 8 * 4;
                    return new f.init(r, n / 2)
                }
            },
            d = h.Latin1 = {
                stringify: function(t) {
                    for (var n = t.words, r = t.sigBytes, e = [], i = 0; i < r; i++) {
                        var o = n[i >>> 2] >>> 24 - i % 4 * 8 & 255;
                        e.push(String.fromCharCode(o))
                    }
                    return e.join("")
                },
                parse: function(t) {
                    for (var n = t.length, r = [], e = 0; e < n; e++) r[e >>> 2] |= (255 & t.charCodeAt(e)) << 24 - e % 4 * 8;
                    return new f.init(r, n)
                }
            },
            l = h.Utf8 = {
                stringify: function(t) {
                    try {
                        return decodeURIComponent(escape(d.stringify(t)))
                    } catch (n) {
                        throw new Error("Malformed UTF-8 data")
                    }
                },
                parse: function(t) {
                    return d.parse(unescape(encodeURIComponent(t)))
                }
            },
            y = c.BufferedBlockAlgorithm = u.extend({
                reset: function() {
                    this._data = new f.init, this._nDataBytes = 0
                },
                _append: function(t) {
                    "string" == typeof t && (t = l.parse(t)), this._data.concat(t), this._nDataBytes += t.sigBytes
                },
                _process: function(t) {
                    var n, e = this._data,
                        i = e.words,
                        o = e.sigBytes,
                        s = this.blockSize,
                        a = o / (4 * s),
                        c = (a = t ? r.ceil(a) : r.max((0 | a) - this._minBufferSize, 0)) * s,
                        u = r.min(4 * c, o);
                    if (c) {
                        for (var h = 0; h < c; h += s) this._doProcessBlock(i, h);
                        n = i.splice(0, c), e.sigBytes -= u
                    }
                    return new f.init(n, u)
                },
                clone: function() {
                    var t = u.clone.call(this);
                    return t._data = this._data.clone(), t
                },
                _minBufferSize: 0
            });
        c.Hasher = y.extend({
            cfg: u.extend(),
            init: function(t) {
                this.cfg = this.cfg.extend(t), this.reset()
            },
            reset: function() {
                y.reset.call(this), this._doReset()
            },
            update: function(t) {
                return this._append(t), this._process(), this
            },
            finalize: function(t) {
                return t && this._append(t), this._doFinalize()
            },
            blockSize: 16,
            _createHelper: function(t) {
                return function(n, r) {
                    return new t.init(r).finalize(n)
                }
            },
            _createHmacHelper: function(t) {
                return function(n, r) {
                    return new g.HMAC.init(t, r).finalize(n)
                }
            }
        });
        var g = a.algo = {};
        return a
    }(Math), i)), e.exports;
    var i
}
var o, s = {
    exports: {}
};

function a() {
    return o ? s.exports : (o = 1, s.exports = (t = i(), function(n) {
        var r = t,
            e = r.lib,
            i = e.WordArray,
            o = e.Hasher,
            s = r.algo,
            a = [],
            c = [];
        ! function() {
            function t(t) {
                for (var r = n.sqrt(t), e = 2; e <= r; e++)
                    if (!(t % e)) return !1;
                return !0
            }

            function r(t) {
                return 4294967296 * (t - (0 | t)) | 0
            }
            for (var e = 2, i = 0; i < 64;) t(e) && (i < 8 && (a[i] = r(n.pow(e, .5))), c[i] = r(n.pow(e, 1 / 3)), i++), e++
        }();
        var u = [],
            f = s.SHA256 = o.extend({
                _doReset: function() {
                    this._hash = new i.init(a.slice(0))
                },
                _doProcessBlock: function(t, n) {
                    for (var r = this._hash.words, e = r[0], i = r[1], o = r[2], s = r[3], a = r[4], f = r[5], h = r[6], p = r[7], d = 0; d < 64; d++) {
                        if (d < 16) u[d] = 0 | t[n + d];
                        else {
                            var l = u[d - 15],
                                y = (l << 25 | l >>> 7) ^ (l << 14 | l >>> 18) ^ l >>> 3,
                                g = u[d - 2],
                                w = (g << 15 | g >>> 17) ^ (g << 13 | g >>> 19) ^ g >>> 10;
                            u[d] = y + u[d - 7] + w + u[d - 16]
                        }
                        var v = e & i ^ e & o ^ i & o,
                            _ = (e << 30 | e >>> 2) ^ (e << 19 | e >>> 13) ^ (e << 10 | e >>> 22),
                            m = p + ((a << 26 | a >>> 6) ^ (a << 21 | a >>> 11) ^ (a << 7 | a >>> 25)) + (a & f ^ ~a & h) + c[d] + u[d];
                        p = h, h = f, f = a, a = s + m | 0, s = o, o = i, i = e, e = m + (_ + v) | 0
                    }
                    r[0] = r[0] + e | 0, r[1] = r[1] + i | 0, r[2] = r[2] + o | 0, r[3] = r[3] + s | 0, r[4] = r[4] + a | 0, r[5] = r[5] + f | 0, r[6] = r[6] + h | 0, r[7] = r[7] + p | 0
                },
                _doFinalize: function() {
                    var t = this._data,
                        r = t.words,
                        e = 8 * this._nDataBytes,
                        i = 8 * t.sigBytes;
                    return r[i >>> 5] |= 128 << 24 - i % 32, r[14 + (i + 64 >>> 9 << 4)] = n.floor(e / 4294967296), r[15 + (i + 64 >>> 9 << 4)] = e, t.sigBytes = 4 * r.length, this._process(), this._hash
                },
                clone: function() {
                    var t = o.clone.call(this);
                    return t._hash = this._hash.clone(), t
                }
            });
        r.SHA256 = o._createHelper(f), r.HmacSHA256 = o._createHmacHelper(f)
    }(Math), t.SHA256));
    var t
}
export {
    i as a, a as r
};