<!DOCTYPE html>
<html lang="zh-CN" data-capo="">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Genspark Agents</title>
    <script src="https://www.googletagmanager.com/gtag/js?id=G-VL3SBJ0Y0F" async data-hid="92ebc66"></script>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/entry.CjsqieIi.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/agents.CwFTCoH2.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/github.C1DJlbbM.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/LoadingAnimation.B2xv_2PZ.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/ImageGallery_icon.DzV6an_d.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/DeepDiveSearchVerticalResult.JZpcnYLz.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/MultipleSankeyCharts.tzAjM330.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/SankeyChart.DP1dwR3-.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/YoutubeWidget.CYLc38aB.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/prompt-link-plugin.BsDk_Kem.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/mindmap_in_markdown.DgMUazV_.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/EchartsWidget.CvqjPENX.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/StockPriceChart.D4iedbzM.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/ViewCompanyDetail.6HVPlPkA.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/SankeyReport.gKf9EE1u.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/LatestReports.BMgQzIXP.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/ProductTemplate.BZ06eJEQ.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/feedback_dialog.DMSfPXgH.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/MarkdownWithPlugins.B6XS6AW6.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/AudioSelectModel.D1T2KOLW.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/agents_configs.pCdHKEuw.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/image_editor.CgOynLPR.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/QuotaExceed.DgKuFXXK.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/PricingWindowForPlusPlan.CbG0vZqH.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/PlanPlusDesc.C8oWeb3m.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/PlanProDesc.DOSRRpAW.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/PricingWindowForTeamCreditsClaim.SgSSHRIB.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/PlanCreditDesc.xiOfDrvw.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/LoginGuidance.Dm3Ueo8u.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/ImageGenerating.CYegq0bI.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/LoadingIcon.DBGzfwQJ.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/data.Du5y1n5K.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/NoImage.C266N_4k.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/PromptInput.DkEDh9JB.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/SearchInputWrapper.PldMiM8q.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/ResearchMeModal.CIL8KOGg.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/FileTypeIcon.BQIzFgr3.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/agent_task_list.Dbv5-9sy.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/AiInboxCanvas.ruyHI9k8.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/TableSheet.B9X5OYDV.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/GlobalDialog.c6DzFmQH.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/PodcastPlayer.AE8F-F9L.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/vue-tel-input.PECflDwb.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/HtmlEditor.CiHPWAwT.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/FontSelector.CzpyUOlP.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/ExportActions.BDF2JlfK.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/InfiniteCanvas.B5YWGL2B.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/index_layout.FoCKiQ9w.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/menu.BFjHCDBG.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/autopilotagent_menu.DTFZNCDA.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/deep_research.CwIvacMk.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/VideoSelectModel.BNvBs73l.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/MediaGallery.DycJgvo4.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/gear.D4UpHB0m.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/chat_agent.y35-912K.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/VideoEditor.D_uDoj10.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/edit_project_name_modal.2oRmLvRk.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/AiDownloaderDialog.BUKWhE8G.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/PermissionPopover.61OJLDKj.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/ResourceList.DALYwr-7.css" crossorigin>
    <link rel="stylesheet" href="https://www.genspark.ai/_nuxt/GlobalAudioPlayer.6zUCrtnC.css" crossorigin>
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Cf0SOiw0.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Gl-llTHe.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/D6bQc9d9.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/e-ES_T8J.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Dwl0GcWq.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DdaMTYTP.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BLjKtaC-.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/mJse5lCL.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BH52nuY1.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/D5LV4gfS.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CHwVUzYg.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CRIx66FB.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DqWfLcpp.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Bm_HbXT2.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CQ2glRxo.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Bzg9uoz_.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DAjjhrgi.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/MpDLC7up.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/B7VeW_-d.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CsPFbezH.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DnZj1005.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/B56nEZrv.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/tBofk-gQ.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BjWUbj3w.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/pB_XRIgB.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BLWq1oPC.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DpMvtoun.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Jr9eiJio.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/9wLWmnxl.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/WZsIN7xM.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/B6noBY_5.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DOnko34f.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DyMB-pVc.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Jx3-I-D7.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Cp7w48vH.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/C-H3edso.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BPQGB51Y.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DJ-JsGJu.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CSefR0kE.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BYvs8isC.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/0tM3vo_n.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BuZX8s8J.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CRmNre8Y.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Bl-gMEVt.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CVrRKK4N.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DkAysl9I.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Cy7E5O2b.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/D5ao1EUl.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CW991W2w.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BuhfKjCJ.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BGK9k_mT.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DcBgjX7B.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Dnth285N.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DW6cX6jm.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/C_XD2eP3.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DHyvnBfI.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BDUh8PoD.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DwpGavSW.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BAZ5Sqbz.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BSM9O9PP.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/x7yzXAMO.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/L54g9xmZ.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/V-H-Vsd5.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CAmLbDGM.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DQpEsQQa.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DXvAIxvL.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/B-XpIQkh.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DGJMLFjI.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/By6xEfKc.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/C38RzRfR.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BrPr1fm5.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BihyrXkC.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DYrQUwU9.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CvZgRYhs.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Dc8Bac8D.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BrcpPT-Q.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/WhweajiO.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CaEkZ53E.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DmWk8H2v.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BXgTVD7d.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Db8eFYek.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CQjXacSG.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/F4fwMVvr.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/qk0HrepY.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/wD492QF4.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CqNssBtC.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BqHcVhvy.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DLUhQFIq.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/C_QvFyDp.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DR_b14-4.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DWsxX4PV.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/C1lFdfgL.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BdlGQsae.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BThdTUgg.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BN-NNxvY.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/D9ll07Bp.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BUs-AQWo.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CKd5XOy1.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Dflnlfvw.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CrbPJ6Kt.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Di7Ot5aL.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DKpDUEYb.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CAzLTKWw.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CJmWj3ri.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/9KCDKcmx.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/0XKHBXCr.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DPPm6Skg.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/lJbCVLd4.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DY44xVYu.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/UVj2ej2A.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CVKRwtBu.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CbfxwEmT.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Ug8thHSu.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BUCk-Nnr.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CAfqOhBF.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BV6guSOS.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DT-NG54s.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/nuQnue4a.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CfHz9NLm.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Cl89jLsD.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DbJ6Dt9m.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/FCN43o2W.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BXNOMSAZ.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Boc3hm_9.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BUjMLq-a.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Dd3cVSEE.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DZBrT1el.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BX7SPHBj.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/B5SiUF0y.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Cn9HXEST.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BffV2qdL.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CIlzw36e.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/D386eQgZ.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/IgM9N0FT.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/D0Ouax4K.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DnWGHCrg.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/B0db5Fvl.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BspXWmOn.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Cu_n4xpI.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BJGgZAPd.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Chtxu0jj.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/zH1ZpJ79.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/LGmiBiLz.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/D5IxqnO4.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CmeRl4Ak.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DJt7CPhG.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DeFGaANp.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DZ51nUum.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BMP7zQoC.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DxgY8w7w.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DC84aLnd.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/nw2_yPEg.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DdNe12v6.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BwHsXuPr.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DOWa3jpG.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DmzoGJ5T.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/4s4Iy95q.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/NG4ombpb.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/gsZIXP6B.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/AGJ7ifDi.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/zge8xy1K.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/HR06imN1.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/ID2-UV3f.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/C1MFiWVQ.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CouGvJje.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CaQJ23Tc.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DsPg3ryP.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DWCxnUK7.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/m6pNMthJ.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DrzlY6u5.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/D_mTlMH9.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BZiwhdnP.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CmF_-QGy.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/8u4bVPF0.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/C7XKdzt5.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/4oYWEiiU.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/zB2AApOf.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CRDCtKfR.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DKfgo1Ia.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BQrni_b3.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BjAOOWF7.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CfpQPauw.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/HCr483ve.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DjawIWev.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/W5AxVKvJ.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/P7LDOl0u.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/COYh4g7i.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/k2Gnd_E0.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/QQkd3sR0.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/3_kYp8w9.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/C1hkGl0A.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CztlUxD-.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DHMy_TLW.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Dp4W8C_b.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BpWej-le.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BZ8pMrhH.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CIu9VtBu.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DCkkKq1S.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/JllO9TFG.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BUDvXs4e.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BiRGpvqQ.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CsEkj3AT.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DmCciZvb.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CEhuGuk0.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BWQxP2bF.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Bju5W73y.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CMNqFBX1.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BL3u8nKF.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/Btf03DwY.js">
    <link rel="modulepreload" as="script" crossorigin href="https://www.genspark.ai/_nuxt/O-S7dcNF.js">
    <link rel="prefetch" as="style" crossorigin href="https://www.genspark.ai/_nuxt/index_layout_9528.pwW86pSx.css">
    <link rel="prefetch" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DCfyDaJW.js">
    <link rel="prefetch" as="script" crossorigin href="https://www.genspark.ai/_nuxt/BRFRQggj.js">
    <link rel="prefetch" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CIVDLvzL.js">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/image-viewer-arrow.D269mdfs.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/deepseek.DcMKAzsn.png">
    <link rel="prefetch" as="audio" href="https://www.genspark.ai/_nuxt/connected.sc9CHxRT.mp3">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/spark_page.DjIr8NF0.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/kimi.BnD7Gu-O.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/kling_icon.B9Oegjgb.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/pixverse_icon.dA9T4Slk.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/luma_icon.DbkLlrXJ.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/hunyuan_icon.BXIfEhw_.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/google.0ZkpJID5.png">
    <link rel="prefetch" as="image" type="image/jpeg" href="https://www.genspark.ai/_nuxt/bria-icon.BHasLJ0-.jpg">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/meta-icon.B91jfMkt.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/iclight-icon.XQPHnTpm.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/moa_image_preview_cartoon.BDJEz38H.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/moa_image_preview_watercolor.Bgke2qmq.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/moa_image_preview_anime.BkBL6SS8.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/moa_image_preview_oil_painting.BSDFCb2c.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/moa_image_preview_3D.DbOpejUw.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/moa_image_preview_minimalist.8hLQaCaA.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/moa_image_preview_pop_art.D1vhYOSD.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/moa_image_preview_realistic.CdA7Sgyl.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/moa_image_preview_auto.DRZHPGWi.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/byteplus.DpZM8Rzz.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/minimax_icon.pN19_fx0.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/wan_icon.BmJ2phk-.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/vidu_icon.QdqdcFj8.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/cassetteai_icon.D5jM5Jm2.png">
    <link rel="prefetch" as="image" type="image/webp" href="https://www.genspark.ai/_nuxt/unreal_speech_icon.DkTduWug.webp">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/deepwiki_icon.BiOEdBz-.png">
    <link rel="prefetch" as="image" type="image/png" href="https://www.genspark.ai/_nuxt/logo.DopCppMG.png">
    <link rel="prefetch" as="script" crossorigin href="https://www.genspark.ai/_nuxt/CnGkyhum.js">
    <link rel="prefetch" as="script" crossorigin href="https://www.genspark.ai/_nuxt/C5JlNeVW.js">
    <link rel="prefetch" as="script" crossorigin href="https://www.genspark.ai/_nuxt/DfSgAeDq.js">
    <link rel="prefetch" as="script" crossorigin href="https://www.genspark.ai/_nuxt/kOuJAkkE.js">
    <meta name="referrer" content="no-referrer">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon.ico">
    <script type="text/javascript" data-hid="79266a8">
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());
        gtag('config', 'G-VL3SBJ0Y0F');
    </script>
    <script type="text/javascript" data-hid="7fbaa41">
        window.addEventListener('error', function(event) {
            if (event.target.tagName === 'SCRIPT') {
                window.parent.postMessage({
                    type: 'chrome_extension_refresh_page',
                }, 'chrome-extension://gefpcelbgaofbnehfglgibacfejejflp')
            }
        }, true);
    </script>
    <script type="text/javascript" data-hid="ecd759b">
        ! function(f, b, e, v, n, t, s) {
            if (f.fbq) return;
            n = f.fbq = function() {
                n.callMethod ?
                    n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };
            if (!f._fbq) f._fbq = n;
            n.push = n;
            n.loaded = !0;
            n.version = '2.0';
            n.queue = [];
            t = b.createElement(e);
            t.async = !0;
            t.src = v;
            s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s)
        }(window, document, 'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '984530900272144');
        fbq('track', 'PageView');
    </script>
    <script type="text/javascript" data-hid="8368497">
        window.genspark_page_id = 'version_1755176383123';
    </script>
    <noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=984530900272144&ev=PageView&noscript=1" /></noscript>
    <meta name="theme-color" content="#fff">
    <link rel="manifest" href="/manifest.webmanifest">
    <script type="module" src="https://www.genspark.ai/_nuxt/Cf0SOiw0.js" crossorigin></script>
    <script id="unhead:payload" type="application/json">
        {
            "title": "Genspark"
        }
    </script>
    <link rel="preload" as="fetch" fetchpriority="low" crossorigin="anonymous" href="https://www.genspark.ai/_nuxt/builds/meta/c09678d4-c724-4243-8492-9fd994d8a1ba.json">
</head>

<body>
    <!--teleport start anchor-->
    <!---->
    <!--teleport anchor-->
    <div id="__nuxt">
        <!--[-->
        <!---->
        <div class="n-config-provider" style="height:100%;" data-v-3e366fdb>
            <div style="height:100%;" data-v-3e366fdb>
                <!--[-->
                <!--[-->
                <!--[-->
                <div style="height:100%;" data-v-19e33e89>
                    <!--[-->
                    <div class="index-layout" data-v-62861aa6><span data-v-62861aa6></span>
                        <div class="loading index-layout-content" data-v-62861aa6>
                            <!--[-->
                            <div class="" data-v-19e33e89><span data-v-19e33e89></span></div>
                            <!--]-->
                        </div>
                        <!---->
                    </div>
                    <!---->
                    <!--]--><span data-v-19e33e89></span>
                    <div data-v-19e33e89 data-v-3f4eb59b>
                        <!---->
                        <!---->
                    </div>
                </div><span data-v-19e33e89 data-v-3dec1afb></span><span data-v-19e33e89 data-v-3dec1afb></span><span data-v-19e33e89></span>
                <!---->
                <!---->
                <div data-v-19e33e89>
                    <!----><span data-v-3dec1afb></span></div>
                <!--teleport start-->
                <!--teleport end-->
                <!--]-->
                <!--]-->
                <!---->
                <!--]-->
            </div>
            <!---->
        </div>
        <!--]-->
    </div>
    <div id="teleports"></div>
    <script type="application/json" data-nuxt-data="nuxt-app" data-ssr="true" id="__NUXT_DATA__">
        [
            ["ShallowReactive", 1], {
                "data": 2,
                "state": 14,
                "once": 17,
                "_errors": 18,
                "serverRendered": 10,
                "path": 21
            },
            ["ShallowReactive", 3], {
                "is_login": 4,
                "IS_CN": 11,
                "IS_JP": 10,
                "gensparkBrowserId": -1,
                "headersValue": 12
            }, {
                "status": 5,
                "message": 6,
                "data": 7
            },
            0, "ok", {
                "cogen_id": 8,
                "cogen_name": 9,
                "is_login": 10
            }, "7ab46097-ad25-4bc5-a02d-5baf80f4788e", "hs*********@op****.edu.kg", true, false, ["null", "user-agent", 13], "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", ["Reactive", 15], {
                "$snuxt-i18n-meta": 16
            }, {},
            ["Set"],
            ["ShallowReactive", 19], {
                "is_login": 20,
                "headersValue": 20,
                "IS_CN": 20,
                "IS_JP": 20,
                "gensparkBrowserId": 20
            },
            null, "/agents?type=moa_chat"
        ]
    </script>
    <script>
        window.__NUXT__ = {};
        window.__NUXT__.config = {
            public: {
                APPLICATIONINSIGHTS_CONNECTION_STRING: "InstrumentationKey=5c09cc03-8ac4-449e-a543-7415b6801d7f;IngestionEndpoint=https://westus2-2.in.applicationinsights.azure.com/;LiveEndpoint=https://westus2.livediagnostics.monitor.azure.com/",
                GOOGLE_CAPTCHA_SITE_KEY: "6Leq7KYqAAAAAGdd1NaUBJF9dHTPAKP7DcnaRc66",
                i18n: {
                    baseUrl: "",
                    defaultLocale: "en-US",
                    defaultDirection: "ltr",
                    strategy: "no_prefix",
                    lazy: true,
                    rootRedirect: "",
                    routesNameSeparator: "___",
                    defaultLocaleRouteNameSuffix: "default",
                    skipSettingLocaleOnNavigate: false,
                    differentDomains: false,
                    trailingSlash: false,
                    configLocales: [{
                        code: "en-US",
                        language: "en-US",
                        name: "English (English)",
                        files: ["/app/frontend-nuxt/locales/en-US.json"]
                    }, {
                        code: "de-DE",
                        language: "de-DE",
                        name: "German (Deutsch)",
                        files: ["/app/frontend-nuxt/locales/de-DE.json"]
                    }, {
                        code: "es-ES",
                        language: "es-ES",
                        name: "Spanish (Español)",
                        files: ["/app/frontend-nuxt/locales/es-ES.json"]
                    }, {
                        code: "fr-FR",
                        language: "fr-FR",
                        name: "French (Français)",
                        files: ["/app/frontend-nuxt/locales/fr-FR.json"]
                    }, {
                        code: "hi-IN",
                        language: "hi-IN",
                        name: "Hindi (हिंदी)",
                        files: ["/app/frontend-nuxt/locales/hi-IN.json"]
                    }, {
                        code: "it-IT",
                        language: "it-IT",
                        name: "Italian (Italiano)",
                        files: ["/app/frontend-nuxt/locales/it-IT.json"]
                    }, {
                        code: "ja-JP",
                        language: "ja-JP",
                        name: "Japanese (日本語)",
                        files: ["/app/frontend-nuxt/locales/ja-JP.json"]
                    }, {
                        code: "ko-KR",
                        language: "ko-KR",
                        name: "Korean (한국어)",
                        files: ["/app/frontend-nuxt/locales/ko-KR.json"]
                    }, {
                        code: "pt-BR",
                        language: "pt-BR",
                        name: "Portuguese (Português)",
                        files: ["/app/frontend-nuxt/locales/pt-BR.json"]
                    }, {
                        code: "zh-CN",
                        language: "zh-CN",
                        name: "Simplified Chinese (简体中文)",
                        files: ["/app/frontend-nuxt/locales/zh-CN.json"]
                    }, {
                        code: "zh-TW",
                        language: "zh-TW",
                        name: "Traditional Chinese (繁體中文)",
                        files: ["/app/frontend-nuxt/locales/zh-TW.json"]
                    }],
                    locales: {
                        "en-US": {
                            domain: ""
                        },
                        "de-DE": {
                            domain: ""
                        },
                        "es-ES": {
                            domain: ""
                        },
                        "fr-FR": {
                            domain: ""
                        },
                        "hi-IN": {
                            domain: ""
                        },
                        "it-IT": {
                            domain: ""
                        },
                        "ja-JP": {
                            domain: ""
                        },
                        "ko-KR": {
                            domain: ""
                        },
                        "pt-BR": {
                            domain: ""
                        },
                        "zh-CN": {
                            domain: ""
                        },
                        "zh-TW": {
                            domain: ""
                        }
                    },
                    detectBrowserLanguage: {
                        alwaysRedirect: false,
                        cookieCrossOrigin: false,
                        cookieDomain: "",
                        cookieKey: "i18n_set",
                        cookieSecure: false,
                        fallbackLocale: "en-US",
                        redirectOn: "root",
                        useCookie: true
                    },
                    experimental: {
                        localeDetector: "",
                        switchLocalePathLinkSSR: false,
                        autoImportTranslationFunctions: false
                    },
                    multiDomainLocales: false
                }
            },
            app: {
                baseURL: "/",
                buildId: "c09678d4-c724-4243-8492-9fd994d8a1ba",
                buildAssetsDir: "/_nuxt/",
                cdnURL: "https://www.genspark.ai/"
            }
        }
    </script>
    <script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"96f0c0345d49038b","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"version":"2025.8.0","token":"4edd5f8ec12a48cfa682ab8261b80a79"}'
        crossorigin="anonymous"></script>
</body>

</html>