if (!self.define) {
    let e, s = {};
    const n = (n, t) => (n = new URL(n + ".js", t).href, s[n] || new Promise((s => {
        if ("document" in self) {
            const e = document.createElement("script");
            e.src = n, e.onload = s, document.head.appendChild(e)
        } else e = n, importScripts(n), s()
    })).then((() => {
        let e = s[n];
        if (!e) throw new Error(`Module ${n} didn’t register its module`);
        return e
    })));
    self.define = (t, i) => {
        const r = e || ("document" in self ? document.currentScript.src : "") || location.href;
        if (s[r]) return;
        let c = {};
        const o = e => n(e, r),
            a = {
                module: {
                    uri: r
                },
                exports: c,
                require: o
            };
        s[r] = Promise.all(t.map((e => a[e] || o(e)))).then((e => (i(...e), c)))
    }
}
define(["./workbox-29ea817b"], (function(e) {
    "use strict";
    importScripts("/service-worker.js?v=1755104662700"), self.addEventListener("message", (e => {
        e.data && "SKIP_WAITING" === e.data.type && self.skipWaiting()
    })), e.precacheAndRoute([{
        url: "_nuxt/builds/latest.json",
        revision: "d599d19f2ed66cfd1d9ce238adc75cdf"
    }, {
        url: "_nuxt/builds/meta/63a3dba8-9227-4f04-8a64-31cd626625f6.json",
        revision: null
    }, {
        url: "manifest.webmanifest",
        revision: "b5ea00a56199c239f77083101f0ea82f"
    }], {}), e.cleanupOutdatedCaches(), e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("/"))), e.registerRoute((({
        request: e
    }) => "document" === e.destination), new e.StaleWhileRevalidate({
        cacheName: "html-cache",
        plugins: [new e.ExpirationPlugin({
            maxEntries: 10,
            maxAgeSeconds: 86400
        })]
    }), "GET"), e.registerRoute((({
        request: e
    }) => "script" === e.destination || "style" === e.destination), new e.NetworkFirst({
        cacheName: "static-resources",
        plugins: [new e.ExpirationPlugin({
            maxEntries: 20,
            maxAgeSeconds: 604800
        })]
    }), "GET"), e.registerRoute((({
        request: e
    }) => "image" === e.destination), new e.CacheFirst({
        cacheName: "image-cache",
        plugins: [new e.ExpirationPlugin({
            maxEntries: 50,
            maxAgeSeconds: 2592e3
        })]
    }), "GET")
}));